package controle.vendasonline;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoAgrupamentoEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaCadastroClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.FiltrosEnum;
import negocio.comuns.basico.enumerador.TipoLinkCampanhaEnum;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.oamd.CampanhaCupomDesconto;
import negocio.facade.jdbc.oamd.CampanhaCupomDescontoJSON;
import negocio.facade.jdbc.vendas.AgendaVendasOnlineVO;
import negocio.facade.jdbc.vendas.AulasVendasOnline;
import negocio.facade.jdbc.vendas.CampoAdicionalObrigatorioVO;
import negocio.facade.jdbc.vendas.CaptacaoLeadsVendasOnlineVO;
import negocio.facade.jdbc.vendas.ConfigModalidadeCarrosselVendasOnlineVO;
import negocio.facade.jdbc.vendas.ContatoRodapeVendasOnlineVO;
import negocio.facade.jdbc.vendas.FotoFachadaVendasOnlineVO;
import negocio.facade.jdbc.vendas.ImagensAcademiaVendasVO;
import negocio.facade.jdbc.vendas.MenuVendasOnlineVO;
import negocio.facade.jdbc.vendas.ModalidadeCarrouselVendasOnlineVO;
import negocio.facade.jdbc.vendas.MultiEmpresaConfigsVendasOnlineVO;
import negocio.facade.jdbc.vendas.PaginaInicialVendasOnlineVO;
import negocio.facade.jdbc.vendas.PlanosSiteVendasOnlineVO;
import negocio.facade.jdbc.vendas.TurmaEHorarioVendasOnlineVO;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import negocio.facade.jdbc.vendas.VendasOnlineCampanhaVO;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioTentativaVO;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioVO;
import negocio.oamd.CupomDescontoVO;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.oamd.OAMDService;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;
import java.util.logging.Logger;

public class GestaoVendasOnlineControle extends SuperControleRelatorio {

    private Integer plano;
    private Integer categoria;
    private Integer produto;
    private String titulo;
    private String urlHotsite;
    private Boolean exibirBtnPublicar;
    private Boolean msgPublicado;
    private String url = PropsService.getPropertyValue(PropsService.urlVendasOnline);
    private Integer vendas = 0;
    private Integer vendasNaoFinalizadas = 0;
    private List<SelectItem> planos;
    private List<SelectItem> planosSite;
    private List<SelectItem> categorias;
    private List<SelectItem> produtos;
    private List<ItemRelatorioTO> lista;
    private List<SelectItem> convenios;
    private List<SelectItem> camposAdicionaisItens;
    private Integer categoriaDeProdutosSelecionada;
    private List<SelectItem> categoriasDeProdutos;

    private List<CampoAdicionalObrigatorioVO> camposAdicionaisItensPlanoProduto;
    private Integer quantidadeCamposAdicionaisItensPlanoProduto;
    private VendasConfigVO config;
    private JSONArray arrayGrafico;
    private List<String> camposAdicionais;
    private List<String> camposAdicionaisProduto;
    private List<String> camposAdicionaisPlanoFlow;
    private List<String> camposAdicionaisProdutoFlow;
    private List<ImagensAcademiaVendasVO> imagens;
    private List<SelectItem> selectItemListaEmpresas = new ArrayList<SelectItem>();
    private EmpresaVO empresaVO;
    private Date dataInicial;
    private Date dataFinal;
    private boolean abaBasico = true;
    private boolean abaAvancado = false;
    private boolean abaConvenioCobranca = false;
    private boolean abaFormaPagamento = false;
    private boolean abaIntegracoes = false;
    private boolean sairModoAvancado = true;
    protected List listaSelectItemConsultor;
    private boolean apresentarTipoParcelamentoStone = false;
    private String numeroCupomDesconto = "";
    private String paramCupom = "";
    private Integer qtdProduto;
    private Integer tipoLinkPlano;
    private Integer tipoLinkProduto;
    private VendasOnlineConvenioVO vendasOnlineConvenioVO;
    private VendasOnlineConvenioVO vendasOnlineFormPagVO;
    private List<VendasOnlineConvenioVO> listaVendasOnlineConvenioEmpresa;
    private List<VendasOnlineConvenioVO> listaVendasOnlineFormaPagamentoEmpresa;
    private List<PlanoVO> listaPlanoVO;
    private List<ProdutoVO> listaProdutoVO;
    private List<ConvenioCobrancaVO> listaConvenioCobrancaVO;
    private List<ConvenioCobrancaVO> listaConvenioCobrancaGeralVO;
    private String onComplete;
    private boolean configuracoes = false;
    private List<VendasOnlineConvenioTentativaVO> listaVendasOnlineConvenioTentativa;
    private VendasOnlineConvenioTentativaVO vendasOnlineConvenioTentativaVO;
    private boolean utilizarRetentativaConvenio = false;
    private Map<Integer, Boolean> planosCredito = new HashMap<>();
    private Map<Integer, Boolean> produtosDiaria = new HashMap<>();
    private String menuSelecionado = "GESTAO";
    private boolean abaCores = false;
    private boolean abaMenu = false;
    private boolean abaPagInicial = false;
    private boolean abaFotoFachada = false;
    private boolean abaModaCarrosel = false;
    private boolean abaModaBanner = false;
    private boolean abaPlanos = false;
    private boolean abaCapLeads = false;
    private boolean abaAgenda = false;
    private boolean abaContato = false;
    private boolean abaPublicar = false;
    private boolean abaIntegracoesHotsite = false;
    private ConfigModalidadeCarrosselVendasOnlineVO configModalidadeCarrosselVendasOnlineVO;
    private ModalidadeCarrouselVendasOnlineVO modalidadeCarrouselVendasOnlineVO;
    private ModalidadeCarrouselVendasOnlineVO modalidadeCarrouselVendasOnlineVO2;
    private ModalidadeCarrouselVendasOnlineVO modalidadeCarrouselVendasOnlineVO3;
    private String fotoKeyExcluirModaCarrousel = "";
    private PlanosSiteVendasOnlineVO planosSiteVendasOnlineVO;
    private PaginaInicialVendasOnlineVO paginaInicialVendasOnlineVO;
    private PaginaInicialVendasOnlineVO paginaInicialVendasOnlineVO2;
    private PaginaInicialVendasOnlineVO paginaInicialVendasOnlineVO3;
    private String paginaInicialSelecionada = "";
    private FotoFachadaVendasOnlineVO fotoFachadaVendasOnlineVO;
    private String fotoKeyExcluirPagInicialVendasOnline = "";
    private String fotoKeyExcluirPagInicialVendasOnline2 = "";
    private String fotoKeyExcluirPagInicialVendasOnline3 = "";
    private String fotoKeyExcluirCapLeadsVendasOnline = "";
    private String fotoKeyExcluirFotoFachadaVendasOnline = "";
    private MenuVendasOnlineVO menuVendasOnlineVO;
    private String fotoKeyExcluirMenuVendasOnline = "";
    private String fotoKeyExcluirMultiConfigsVendasOnline = "";
    private CaptacaoLeadsVendasOnlineVO captacaoLeadsVendasOnlineVO;
    private ContatoRodapeVendasOnlineVO contatoRodapeVendasOnlineVO;
    private boolean multiEmpresaConfig = false;
    private MultiEmpresaConfigsVendasOnlineVO multiEmpresaConfigsVendasOnlineVO;
    private AgendaVendasOnlineVO agendaVendasOnlineVO;
    private boolean mostrarConfirmacaoExclusao = false;
    private String protocoloHttps;
    private String dominioHotsite;
    private String ipServidorHotsite;
    boolean publicado;
    boolean erroConsultarStatus;
    private boolean dominioProprioHotsite = false;
    private Integer codigoEventoPlano;
    private Integer codigoEventoProduto;
    private Integer codigoEventoVisitante;
    private List<SelectItem> listaEventos;
    private List<VendasOnlineCampanhaVO>listaLinksCampanhaPlano;
    private List<VendasOnlineCampanhaVO>listaLinksCampanhaProduto;
    private List<VendasOnlineCampanhaVO>listaLinksCampanhaVisitante;
    private List<UsuarioVO> usuariosLink;
    private Integer paramUsuario;
    private boolean habilitarempresahotsite;
    private boolean exibirConfigContratoConcomitanteComParcelaEmAberto = false;
    private boolean abaCamposAdicionais = false;
    protected List listaSelectItemCampanhaCupomDesconto;
    private AgendaVendasOnlineVO agendaLinkVisitanteVendasOnlineVO;

    private String beneficioParaAdicionar;
    private List<String> lstBeneficiosDoPlano;

    private Integer freepass;

    public boolean informarValorDesejadoMensal;
    public Double valorDesejadoMensal;

    public GestaoVendasOnlineControle() {
        init();
    }

    public void init() {
        try {
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);
            setMostrarConfirmacaoExclusao(Boolean.FALSE);
            setMsgAlert("");
            setMensagemID("");
            setMensagemDetalhada("", "");
            setTipoLinkPlano(1);
            setTipoLinkProduto(1);
            setVendasOnlineConvenioTentativaVO(new VendasOnlineConvenioTentativaVO());
            validarPeriodoPesquisa();
            setModalidadeCarrouselVendasOnlineVO(new ModalidadeCarrouselVendasOnlineVO());
            setPlanosSiteVendasOnlineVO(new PlanosSiteVendasOnlineVO());
            setBeneficioParaAdicionar("");
            setLstBeneficiosDoPlano(new ArrayList<String>());
            montarPlanosSite();
            listaLinksCampanhaPlano  = getFacade().getVendasOnlineCampanha().consultarTipoEEmpresa(TipoLinkCampanhaEnum.PLANO.getCodigo(), empresaVO.getCodigo());
            listaLinksCampanhaProduto  = getFacade().getVendasOnlineCampanha().consultar(TipoLinkCampanhaEnum.PRODUTO.getCodigo());
            listaLinksCampanhaVisitante  = getFacade().getVendasOnlineCampanha().consultar(TipoLinkCampanhaEnum.VISITANTE.getCodigo());
            config = getFacade().getVendasOnline().config(empresaVO.getCodigo());
            if(getFacade().getVendasOnline().consultarEmpresasHotsite(empresaVO.getCodigo()).size() > 0){
                boolean empativa = getFacade().getVendasOnline().consultarEmpresasHotsite(empresaVO.getCodigo()).get(0).isAtivo() ;
                this.setHabilitarempresahotsite(empativa);
            }
            montarListaSelectItemConsultor(empresaVO);
            montarListaSelectItemCampanhaCupomDesconto(empresaVO);
            if (!UteisValidacao.emptyString(config.getUrlvenda())) {
                url = config.getUrlvenda();
            }
            camposAdicionais = Arrays.asList(config.getCamposAdicionais().split(";"));
            camposAdicionaisProduto = Arrays.asList(config.getCamposAdicionaisProduto().split(";"));
            camposAdicionaisPlanoFlow = Arrays.asList(config.getCamposAdicionaisPlanoFlow().split(";"));
            camposAdicionaisProdutoFlow = Arrays.asList(config.getCamposAdicionaisProdutoFlow().split(";"));

            camposAdicionaisItens = new ArrayList<>();
            camposAdicionaisItensPlanoProduto = new ArrayList<>();

            for (CamposAdicionaisVendasOnlineEnum c : CamposAdicionaisVendasOnlineEnum.values()) {
                camposAdicionaisItens.add(new SelectItem(c.name(), c.getDescricao()));

                if(camposAdicionais.contains(c.name())){
                    camposAdicionaisItensPlanoProduto.add(new CampoAdicionalObrigatorioVO(c,c.getDescricao(), true, false, c.isHabilitaPlano(), c.isHabilitaProduto(), c.getHint()));
                }else{
                    camposAdicionaisItensPlanoProduto.add(new CampoAdicionalObrigatorioVO(c, c.getDescricao(), false, false, c.isHabilitaPlano(), c.isHabilitaProduto(), c.getHint()));
                }
            }
            int index = 0;
            for(CampoAdicionalObrigatorioVO campo : camposAdicionaisItensPlanoProduto){
                if(camposAdicionaisProduto.contains(camposAdicionaisItensPlanoProduto.get(index).getObj().name())){
                    camposAdicionaisItensPlanoProduto.get(index).setObrigatorioProduto(true);
                }
                if(camposAdicionaisPlanoFlow.contains(camposAdicionaisItensPlanoProduto.get(index).getObj().name())){
                    camposAdicionaisItensPlanoProduto.get(index).setObrigatorioPlanoFlow(true);
                }
                if(camposAdicionaisProdutoFlow.contains(camposAdicionaisItensPlanoProduto.get(index).getObj().name())){
                    camposAdicionaisItensPlanoProduto.get(index).setObrigatorioProdutoFlow(true);
                }
                index++;
            }

            quantidadeCamposAdicionaisItensPlanoProduto = camposAdicionaisItensPlanoProduto.size();
            montarPlanos();
            montarEventos();
            montarListaSelectItemPlanoCategoria();
            montarProdutos();
            totalizar();
            setarGrafico();
            montarConvenios();
            montarListaUsuarios();
            montarListaEmpresas();
            carregarListaVendasOnlineConvenio(empresaVO);
            carregarListaVendasOnlineConvenioTentativa();
            setConfigModalidadeCarrosselVendasOnlineVO(getFacade().getVendasOnline().consultarConfigModalidadesCarrosselVendasOnline(config.getCodigo()));
            setPaginaInicialVendasOnlineVO(getFacade().getVendasOnline().consultarPaginaInicialVendasOnline(config.getCodigo(), 1));
            setPaginaInicialVendasOnlineVO2(getFacade().getVendasOnline().consultarPaginaInicialVendasOnline(config.getCodigo(), 2));
            setPaginaInicialVendasOnlineVO3(getFacade().getVendasOnline().consultarPaginaInicialVendasOnline(config.getCodigo(), 3));
            setMenuVendasOnlineVO(getFacade().getVendasOnline().consultarMenuVendasOnline(config.getCodigo()));
            setFotoFachadaVendasOnlineVO(getFacade().getVendasOnline().consultarFotoFachadaVendasOnline(config.getCodigo()));
            setCaptacaoLeadsVendasOnline(getFacade().getVendasOnline().consultarCapLeadsVendasOnline(config.getCodigo()));
            setContatoRodapeVendasOnlineVO(getFacade().getVendasOnline().consultarContatoRodapeVendasOnline(config.getCodigo()));
            setMultiEmpresaConfigsVendasOnlineVO(getFacade().getVendasOnline().consultarMultiEmpresaCongifsVendasOnline());
            setAgendaVendasOnlineVO(new AgendaVendasOnlineVO());
            setAgendaLinkVisitanteVendasOnlineVO(new AgendaVendasOnlineVO());
            montarListaDeCategoriasDeProdutos();

            //Agenda do hotsite
            carregarTurmaHorarioVendasOnlineAulas();
            carregarAulasVendasOnline();

            //link de visitante
            carregarAulasVendasOnlineLinkVisitanteDisponiveis();
            carregarAulasVendasOnlineLinkVisitanteJaExistentes();

            setExibirConfigContratoConcomitanteComParcelaEmAberto(false);
            if (empresaVO.getPermiteContratosConcomintante()) {
                setExibirConfigContratoConcomitanteComParcelaEmAberto(true);
            }
            try {
                validarApresentacaoTipoParcelamentoStone();
            } catch (Exception ignored) {
            }
            montarSucessoDadosGravados();
            setMsgAlert(getMensagemNotificar());
            setMostrarConfirmacaoExclusao(Boolean.FALSE);

            informarValorDesejadoMensal = false;
            valorDesejadoMensal = new Double(0);

        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            Uteis.logar(e, GestaoVendasOnlineControle.class);
        }
    }

    private void carregarTurmaHorarioVendasOnlineAulas() throws Exception {
        List<TurmaEHorarioVendasOnlineVO> list =
                                    getFacade()
                                        .getVendasOnline()
                                        .consultarTurmaEHorarioVendasOnlinePorEmpresa(getEmpresaVO().getCodigo());
        getAgendaVendasOnlineVO().setTurmaEHorario(list);
    }

    private void carregarAulasVendasOnline() throws Exception {
        List<AulasVendasOnline> list =
                                getFacade()
                                        .getVendasOnline()
                                        .consultarAulasVendasOnlinePorEmpresa(getEmpresaVO().getCodigo());
        getAgendaVendasOnlineVO().setAulasVendasOnline(list);
    }

    private void carregarAulasVendasOnlineLinkVisitanteDisponiveis() throws Exception {
        List<TurmaEHorarioVendasOnlineVO> list =
                getFacade()
                        .getVendasOnline()
                        .consultarTurmaEHorarioVendasOnlineLinkVisitantePorEmpresa(getEmpresaVO().getCodigo());
        getAgendaLinkVisitanteVendasOnlineVO().setListTurmaHorarioLinkVisitante(list);
    }

    private List<TurmaEHorarioVendasOnlineVO> obterAulasVendasOnlineLinkVisitanteDisponiveisBanco() throws Exception {
        List<TurmaEHorarioVendasOnlineVO> list =
                getFacade()
                        .getVendasOnline()
                        .consultarTurmaEHorarioVendasOnlineLinkVisitantePorEmpresa(getEmpresaVO().getCodigo());
        return list;
    }

    private void carregarAulasVendasOnlineLinkVisitanteJaExistentes() throws Exception {
        List<AulasVendasOnline> list =
                getFacade()
                        .getVendasOnline()
                        .consultarAulasVendasOnlineLinkVisitantePorEmpresa(getEmpresaVO().getCodigo(), config.getCodigo());
        getAgendaLinkVisitanteVendasOnlineVO().setAulasVendasOnlineLinkVisitante(list);
        getAgendaLinkVisitanteVendasOnlineVO().setTipoAulasLinkVisitante(getFacade().getVendasOnline().obterTipoAulasLinkVisitante(config.getCodigo()));
    }

    public void carregarProdutosDeVendasOnline(ValueChangeEvent event) throws Exception {
        List<ProdutoVO> list =
                getFacade()
                        .getVendasOnline()
                        .consultarProdutosVendasOnlinePorTurma(Integer.parseInt(String.valueOf(event.getNewValue())));

        getAgendaVendasOnlineVO().setProdutoVOS(list);
    }

    private void validarPeriodoPesquisa() throws Exception {
        long dias = Uteis.nrDiasEntreDatas(getDataInicial(), getDataFinal());
        dias = dias + 1;
        if (dias > 31) {
            setDataInicial(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            setDataFinal(Uteis.obterUltimoDiaMes(Calendario.hoje()));
            throw new ConsistirException("Não foi possível atualizar os dados para o período de datas informado. Intervalo de dias superior a 31 dias.");
        }
    }

    public void setarGrafico() throws Exception {
        arrayGrafico = new JSONArray();
        Map<String, Integer> map = getFacade().getContrato().vendasOnlinePlano(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
        for (SelectItem s : planos) {
            JSONObject obj = new JSONObject();
            obj.put("cor", config.getCor());
            obj.put("plano", s.getLabel());
            obj.put("sigla", s.getLabel().length() > 7 ? s.getLabel().substring(0, 8) : s.getLabel());
            obj.put("valor", map.get(s.getLabel()) == null ? 0 : map.get(s.getLabel()));
            arrayGrafico.put(obj);
        }
    }

    @Override
    public void gravarFiltro(FiltrosEnum filtro, String valor) throws Exception {
        super.gravarFiltro(filtro, valor);
    }

    public void gravarConfigModalidade() throws Exception {

        configModalidadeCarrosselVendasOnlineVO.setCodVendasOnlineConfig(config.getCodigo());
        getFacade().getVendasOnline().gravarConfigModalidadeCarrossel(configModalidadeCarrosselVendasOnlineVO);

        //LOG - INICIO
        try {
            registrarLogObjetoVO(config, config.getCodigo(), config.getClass().getSimpleName(), 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(config.getClass().getSimpleName(), config.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DAS CONFIGURAÇÕES DO VENDAS ONLINE ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
        init();
        montarSucessoDadosGravados();
    }
    public void gravar() {
        try {
            limparMsg();
            validarCampos();
            config.setNovoObj(false);
            config.setObjetoVOAntesAlteracao(getFacade().getVendasOnline().config(getEmpresaVO().getCodigo()));
            setMsgAlert("");
            config.setCamposAdicionais("");
            config.setCamposAdicionaisProduto("");
            config.setCamposAdicionaisProdutoFlow("");
            config.setCamposAdicionaisPlanoFlow("");

            validacoesLinksGooglePlayEAppleStore();

            //Se a configuração estiver marcada e não tiver configurados um convênio Pix e Cartão, deve ser false a configuração
            if (config.isPrimeiraCobrancaPixEGuardarCartao() && (UteisValidacao.emptyNumber(config.getConvenioCobrancaPixVO().getCodigo()) ||
                UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo()))) {
                config.setPrimeiraCobrancaPixEGuardarCartao(false);
            } //Se a configuração estiver marcada e a lista de Convênio de Cobrança por Plano ou Produto também estiver marcada, deve limpara a lista, pois não foi tratado esse cenário.
            else if (config.isPrimeiraCobrancaPixEGuardarCartao() && !UteisValidacao.emptyList(listaVendasOnlineConvenioEmpresa)) {
                config.setUsarConvenioPlanoProduto(false);
                List<VendasOnlineConvenioVO> listaVazia = new ArrayList<>();
                listaVendasOnlineConvenioEmpresa = listaVazia;
            }

            for(CampoAdicionalObrigatorioVO campo : camposAdicionaisItensPlanoProduto){
                if(campo.isObrigatorioPlano()){
                    config.setCamposAdicionais(config.getCamposAdicionais() + ";" + campo.getObj());
                }
                if(campo.isObrigatorioProduto()){
                    config.setCamposAdicionaisProduto(config.getCamposAdicionaisProduto() + ";" + campo.getObj());
                }
                if(campo.isObrigatorioPlanoFlow()) {
                    config.setCamposAdicionaisPlanoFlow(config.getCamposAdicionaisPlanoFlow() + ";" + campo.getObj());
                }
                if(campo.isObrigatorioProdutoFlow()) {
                    config.setCamposAdicionaisProdutoFlow(config.getCamposAdicionaisProdutoFlow() + ";" + campo.getObj());
                }
            }

            config.setCamposAdicionais(config.getCamposAdicionais().replaceFirst(";", ""));
            config.setCamposAdicionaisProduto(config.getCamposAdicionaisProduto().replaceFirst(";", ""));
            config.setCamposAdicionaisPlanoFlow(config.getCamposAdicionaisPlanoFlow().replaceFirst(";", ""));
            config.setCamposAdicionaisProdutoFlow(config.getCamposAdicionaisProdutoFlow().replaceFirst(";", ""));

            validacoesFacilitePayEPagoLivre();

            if(isAbaModaCarrosel() || isAbaModaBanner()) {
                if((getListaModalidadeCarrouselVendasOnline().size()) < 15) {
                    String mensagem = validarModalidadeVendasOnline();
                    if(mensagem != null && !mensagem.isEmpty()){
                        throw new Exception(mensagem);
                    }else{
                        //Inicializa se está null
                        if(listaModalidadeInclusao == null){
                            listaModalidadeInclusao = new ArrayList<>();
                        }
                        //Insere o codigo da configuracao na modalidade carrousel
                        modalidadeCarrouselVendasOnlineVO.setCodVendasOnlineConfig(config.getCodigo());
                        modalidadeCarrouselVendasOnlineVO2.setCodVendasOnlineConfig(config.getCodigo());
                        modalidadeCarrouselVendasOnlineVO3.setCodVendasOnlineConfig(config.getCodigo());

                        //Verifica se é a ABA da MODAL BANNER
                        if(isAbaModaBanner()){
                            modalidadeCarrouselVendasOnlineVO.setBanner(true);
                            modalidadeCarrouselVendasOnlineVO2.setBanner(true);
                            modalidadeCarrouselVendasOnlineVO3.setBanner(true);
                        }


                        if(getModalidadeCarrouselVendasOnlineVO().getCodigo() == null) {
                            //Verifica o Tipo de Agrupamento
                            TipoAgrupamentoEnum tipo = verificarAgrupamento();
                            getModalidadeCarrouselVendasOnlineVO().setTipoAgrupamento(tipo);
                            getModalidadeCarrouselVendasOnlineVO2().setTipoAgrupamento(tipo);
                            getModalidadeCarrouselVendasOnlineVO3().setTipoAgrupamento(tipo);
                        }
                        //Grava a Modalidade
                        getFacade().getVendasOnline().gravarModalidadeCarrousel(modalidadeCarrouselVendasOnlineVO);
                        getFacade().getVendasOnline().gravarModalidadeCarrousel(modalidadeCarrouselVendasOnlineVO2);
                        getFacade().getVendasOnline().gravarModalidadeCarrousel(modalidadeCarrouselVendasOnlineVO3);

                        //Com o ID da Modalidade sobe o Arquivo e atualiza o objeto
                        gravarArquivoModalidade(modalidadeCarrouselVendasOnlineVO);
                        gravarArquivoModalidade(modalidadeCarrouselVendasOnlineVO2);
                        gravarArquivoModalidade(modalidadeCarrouselVendasOnlineVO3);

                        //Adiciona na Lista de Exibição
                        getListaModalidadeInclusao().add(modalidadeCarrouselVendasOnlineVO);
                        getListaModalidadeInclusao().add(modalidadeCarrouselVendasOnlineVO2);
                        getListaModalidadeInclusao().add(modalidadeCarrouselVendasOnlineVO3);

                        //Zera as variaveis do Controle para reiniciar
                        setModalidadeCarrouselVendasOnlineVO(new ModalidadeCarrouselVendasOnlineVO());
                        setModalidadeCarrouselVendasOnlineVO2(new ModalidadeCarrouselVendasOnlineVO());
                        setModalidadeCarrouselVendasOnlineVO3(new ModalidadeCarrouselVendasOnlineVO());
                    }
                }else{
                    if(isAbaModaCarrosel() ){
                        throw new Exception("A lista de modalidades Carrossel está cheia. Máximo permitido: 15.");
                    }else{
                        throw new Exception("A lista de modalidades Banner está cheia. Máximo permitido: 15.");
                    }
                }
            }
            if(isAbaPublicar()){

            }
            if(isAbaIntegracoes() && config.isIntegracaoBotConversa() && UteisValidacao.emptyString(config.getEnderecoEnviarAcoesBotConversa())){
                throw new Exception("Você habilitou a integração com o BotConversa, porém não informou o Endereço da API");
            }
            if(!UteisValidacao.emptyString(getFotoKeyExcluirModaCarrousel())){
                removerFotoKeyModaCarrouselServidor(getFotoKeyExcluirModaCarrousel());
                setFotoKeyExcluirModaCarrousel("");
            }
            if(isAbaPlanos()){
                if(getListaPlanosSiteVendasOnline() != null
                        && getListaPlanosSiteVendasOnline().size() >= 10
                        && getPlanosSiteVendasOnlineVO().getCodigo() == null){
                    throw new Exception("A lista de Planos está cheia. Máximo permitido: 10.");
                }else{
                    getPlanosSiteVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
                    getPlanosSiteVendasOnlineVO().setBeneficios(formatarBeneficiosPlanoParaString(getLstBeneficiosDoPlano()));
                    getFacade().getVendasOnline().gravarPlanoSiteVendasOnline(getPlanosSiteVendasOnlineVO());

                    if(isInformarValorDesejadoMensal()) {
                        PlanoDuracaoVO planoDuracaoVO = getFacade().getPlanoDuracao().consultarPorPlanoRecorrencia(planosSiteVendasOnlineVO.getCodPlano(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        if(planoDuracaoVO != null && !UteisValidacao.emptyNumber(planoDuracaoVO.getCodigo())){
                            planoDuracaoVO.setValorDesejadoMensal(valorDesejadoMensal);
                        }

                        getFacade().getPlanoDuracao().alterar(planoDuracaoVO);

                    }

                }
            }
            if(isAbaPagInicial()){
                getPaginaInicialVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
                getFacade().getVendasOnline().gravarPaginaInicial(getPaginaInicialVendasOnlineVO(),1);

                getPaginaInicialVendasOnlineVO2().setCodVendasOnlineConfig(config.getCodigo());
                getFacade().getVendasOnline().gravarPaginaInicial(getPaginaInicialVendasOnlineVO2(),2);

                getPaginaInicialVendasOnlineVO3().setCodVendasOnlineConfig(config.getCodigo());
                getFacade().getVendasOnline().gravarPaginaInicial(getPaginaInicialVendasOnlineVO3(),3);
            }
            if(!UteisValidacao.emptyString(getFotoKeyExcluirPagInicialVendasOnline())){
                removerImagemPagInicialServidor(getFotoKeyExcluirPagInicialVendasOnline());
                setFotoKeyExcluirPagInicialVendasOnline("");
            }
            if(isAbaFotoFachada()){
                getFotoFachadaVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
                getFacade().getVendasOnline().gravarFotoFachada(getFotoFachadaVendasOnlineVO());
            }
            if(!UteisValidacao.emptyString(getFotoKeyExcluirFotoFachadaVendasOnline())){
                removerImagemPagInicialServidor(getFotoKeyExcluirFotoFachadaVendasOnline());
                setFotoKeyExcluirFotoFachadaVendasOnline("");
            }
            if(isAbaMenu()){
                getMenuVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
                getFacade().getVendasOnline().gravarMenu(getMenuVendasOnlineVO());
            }
            if(!UteisValidacao.emptyString(getFotoKeyExcluirMenuVendasOnline())){
                removerImagemMenuServidor(getFotoKeyExcluirMenuVendasOnline());
                setFotoKeyExcluirMenuVendasOnline("");
            }
            if(isAbaCapLeads()){
                getCaptacaoLeadsVendasOnline().setCodVendasOnlineConfig(config.getCodigo());
                getFacade().getVendasOnline().gravarCaptacaoLeads(getCaptacaoLeadsVendasOnline());
            }
            if(!UteisValidacao.emptyString(getFotoKeyExcluirCapLeadsVendasOnline())){
                removerImagemCapLeadsServidor(getFotoKeyExcluirCapLeadsVendasOnline());
                setFotoKeyExcluirCapLeadsVendasOnline("");
            }
            if(isAbaContato()){
                getContatoRodapeVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
                getFacade().getVendasOnline().gravarContatoRodape(getContatoRodapeVendasOnlineVO());
            }
            if(isAbaIntegracoesHotsite()){
                getFacade().getVendasOnline().gravarIntegracoesHotsite(config);
            }
            if(isMultiEmpresaConfig()){
                if(getMultiEmpresaConfigsVendasOnlineVO() != null
                        && UteisValidacao.emptyString(getMultiEmpresaConfigsVendasOnlineVO().getFotoKey())){
                    throw new Exception("A Logo da página MultiEmpresa é Obrigatória.");
                }else{
                    getFacade().getVendasOnline().gravarMultiEmpresaConfig(getMultiEmpresaConfigsVendasOnlineVO());
                }
            }
            if(!UteisValidacao.emptyString(getFotoKeyExcluirMultiConfigsVendasOnline())){
                removerImagemMultiConfigServidor(getFotoKeyExcluirMenuVendasOnline());
                setFotoKeyExcluirMultiConfigsVendasOnline("");
            }

            getFacade().getVendasOnline().gravarConfig(config);

            if (config.isHabilitarAgendamentoAulaExperimentalLinkVisitante()) {
                getFacade().getVendasOnline().gravarAgendaVendasOnlineLinkVisitante(getAgendaLinkVisitanteVendasOnlineVO(), getUsuario(), config.getCodigo(), config.getEmpresa());
            }

            //configuração plano produto
            getFacade().getVendasOnlineConvenio().gravar(config, getUsuarioLogado(), getListaVendasOnlineConvenioEmpresa(), false);

            //configuração forma pagamento plano produto
            getFacade().getVendasOnlineConvenio().gravar(config, getUsuarioLogado(), getListaVendasOnlineFormaPagamentoEmpresa(), true);

            //configuração convenio
            getFacade().getVendasOnlineConvenioTentativa().gravar(config, getUsuarioLogado(), getListaVendasOnlineConvenioTentativa());

            //LOG - INICIO
            try {
                registrarLogObjetoVO(config, config.getCodigo(), config.getClass().getSimpleName(), 0);
            } catch (Exception e) {
                registrarLogErroObjetoVO(config.getClass().getSimpleName(), config.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DAS CONFIGURAÇÕES DO VENDAS ONLINE ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            //LOG - FIM
            init();
            montarSucessoDadosGravados();
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    private void validacoesLinksGooglePlayEAppleStore() throws Exception {
        if (getConfig().isAtivarLinksGooglePlayEAppleStore()) {
            if (UteisValidacao.emptyString(getConfig().getUrlLinkGooglePlay()) && UteisValidacao.emptyString(getConfig().getUrlLinkAppleStore())) {
                throw new Exception("Pelo menos uma url para Google Play ou Apple Store deve ser informada.");
            }
            if (!UteisValidacao.emptyString(getConfig().getUrlLinkGooglePlay()) && !getConfig().getUrlLinkGooglePlay().contains("https://play.google.com/")) {
                throw new Exception("Url para Google Play deve ser informada iniciando por \"https://play.google.com/\".");
            }
            if (!UteisValidacao.emptyString(getConfig().getUrlLinkAppleStore()) && !getConfig().getUrlLinkAppleStore().contains("https://apps.apple.com/")) {
                throw new Exception("Url para Apple Store deve ser informada iniciando por \"https://apps.apple.com/\".");
            }
        } else {
            getConfig().setUrlLinkGooglePlay("");
            getConfig().setUrlLinkAppleStore("");
        }
    }

    public void validacoesFacilitePayEPagoLivre() throws Exception {

        boolean existeConvenioFacilitePay = existeConvenioFacilitePay();
        boolean existeConvenioPagoLivre = existeConvenioPagoLivre();

        if (existeConvenioFacilitePay || existeConvenioPagoLivre) {
            String descConvExibir = existeConvenioFacilitePay ? "FYPAY" : "PAGOLIVRE";

            List<String> camposAdicionais = Arrays.asList(config.getCamposAdicionais().split(";"));
            List<String> camposAdicionaisProduto = Arrays.asList(config.getCamposAdicionaisProduto().split(";"));

            boolean contem;
            List<String> obrigatorios = new ArrayList<>();
            obrigatorios.add("TELEFONE");
            for (String obrigatorio : obrigatorios) {
                contem = false;
                for (String camposObrigatorios : camposAdicionais) {
                    if (obrigatorio.equalsIgnoreCase(camposObrigatorios)) {
                        contem = true;
                    }
                }
                if (!contem) {
                    throw new Exception("O campo adicional \"" + obrigatorio + "\", na aba \"Campos de Cadastro\" é obrigatório para transações através do convênio de cobrança "
                            + descConvExibir + " configurado. Configure o campo telefone para Plano e também para Produtos / Visitante");
                }
            }

            for (String obrigatorio : obrigatorios) {
                contem = false;
                for (String camposObrigatorios : camposAdicionaisProduto) {
                    if (obrigatorio.equalsIgnoreCase(camposObrigatorios)) {
                        contem = true;
                    }
                }
                if (!contem) {
                    throw new Exception("O campo adicional \"" + obrigatorio + "\", na aba \"Campos de Cadastro\" é obrigatório para transações através do convênio de cobrança "
                            + descConvExibir + " configurado. Configure o campo telefone para Plano e também para Produtos / Visitante");
                }
            }
        }
    }

    private TipoAgrupamentoEnum verificarAgrupamento() throws Exception {
        TipoAgrupamentoEnum tipo = null;
        List<ModalidadeCarrouselVendasOnlineVO> listaModaExistente = getListaModalidadeCarrouselVendasOnline();
        Map<Integer, TipoAgrupamentoEnum> mapExistente = new HashMap<>();
        if(listaModaExistente != null
                && listaModaExistente.size() > 0){
            for (ModalidadeCarrouselVendasOnlineVO modalidade :listaModaExistente) {
                mapExistente.put(modalidade.getTipoAgrupamento().getId(), modalidade.getTipoAgrupamento());
            }
            for (TipoAgrupamentoEnum agrupamento :TipoAgrupamentoEnum.values()) {
                if(!mapExistente.containsKey(agrupamento.getId())){
                    tipo = agrupamento;
                    break;
                }
            }
        }else{
            tipo = TipoAgrupamentoEnum.TIPO_AGRUPAMENTO_1;
        }
        return tipo;
    }

    private void gravarArquivoModalidade(ModalidadeCarrouselVendasOnlineVO modalidade) throws Exception {
        String key = null;
        try {
            if(modalidade.getCodigo() != null
                    && modalidade.getBytesArquivo() != null) {
                key = MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                        MidiaEntidadeEnum.IMAGENS_MODALIDADE_CORROUSEL_VENDAS,
                        String.format("%s_%s", Calendario.hoje().getTime(),
                                modalidade.getCodigo()), modalidade.getBytesArquivo());
                modalidade.setFotoKey(key);
                getFacade().getVendasOnline().gravarModalidadeCarrousel(modalidade);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    private String validarModalidadeVendasOnline() {
        StringBuffer str = new StringBuffer("");
        if(modalidadeCarrouselVendasOnlineVO.getBytesArquivo() == null && modalidadeCarrouselVendasOnlineVO.getUrlFoto() == null){
            if(isAbaModaBanner()){
                str.append("A Imagem do Banner da Modalidade 1 é Obrigatório.");
            }else{
                str.append("A Imagem da Modalidade 1 é Obrigatório. ");
            }
        }
        if(UteisValidacao.emptyString(modalidadeCarrouselVendasOnlineVO.getTituloModalidadeVendas())){
            str.append("O Título da Modalidade 1 é Obrigatório.");
        }
        if(UteisValidacao.emptyString(modalidadeCarrouselVendasOnlineVO.getDescricaoModalidadeVendas())){
            str.append("A Descrição da Modalidade 1 é Obrigatório. ");
        }
        if(modalidadeCarrouselVendasOnlineVO2.getBytesArquivo() == null && modalidadeCarrouselVendasOnlineVO.getUrlFoto() == null){
            if(isAbaModaBanner()){
                str.append("A Imagem do Banner da Modalidade 2 é Obrigatório.");
            }else{
                str.append("A Imagem da Modalidade 2 é Obrigatório.");
            }
        }
        if(UteisValidacao.emptyString(modalidadeCarrouselVendasOnlineVO2.getTituloModalidadeVendas())){
            str.append("O Título da Modalidade 2 é Obrigatório. ");
        }
        if(UteisValidacao.emptyString(modalidadeCarrouselVendasOnlineVO2.getDescricaoModalidadeVendas())){
            str.append("A Descrição da Modalidade 2 é Obrigatório. ");
        }
        if(modalidadeCarrouselVendasOnlineVO3.getBytesArquivo() == null && modalidadeCarrouselVendasOnlineVO.getUrlFoto() == null){
            if(isAbaModaBanner()){
                str.append("A Imagem do Banner da Modalidade 3 é Obrigatório. ");
            }else{
                str.append("A Imagem da Modalidade 3 é Obrigatório. ");
            }
        }
        if(UteisValidacao.emptyString(modalidadeCarrouselVendasOnlineVO3.getTituloModalidadeVendas())){
            str.append("O Título da Modalidade 3 é Obrigatório. ");
        }
        if(UteisValidacao.emptyString(modalidadeCarrouselVendasOnlineVO3.getDescricaoModalidadeVendas())){
            str.append("A Descrição da Modalidade 3 é Obrigatório. ");
        }
        return UteisValidacao.emptyString(str.toString())?null:str.toString();
    }

    public void realizarConsultaLogObjetoSelecionadoVendasOnlineConfigs() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "VENDASCONFIG";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), getConfig().getCodigo(), 0);
        titulo = "Controle de log - Vendas online";
    }

    private void validarCampos() throws Exception {
        if (UteisValidacao.emptyString(config.getTituloCheckout())) {
            throw new Exception("O campo \"título da tela de checkout\", não pode ficar vazio.");
        }

        if (isUtilizarRetentativaConvenio()) {

            if (getListaVendasOnlineConvenioTentativa().size() < 2) {
                throw new Exception("Selecione pelo menos dois convênios de cobrança.");
            } else {

                boolean existeConvenioGetNetOnline = false;
                for (VendasOnlineConvenioTentativaVO vendasVO : getListaVendasOnlineConvenioTentativa()) {
                    ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobrancaVO(vendasVO.getConvenioCobrancaVO().getCodigo());
                    if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                        existeConvenioGetNetOnline = true;
                        break;
                    }
                }

                if (existeConvenioGetNetOnline) {
                    boolean contem;
                    List<String> obrigatorios = new ArrayList<>();
                    obrigatorios.add("TELEFONE");
                    obrigatorios.add("CEP");
                    obrigatorios.add("ENDERECO");
                    obrigatorios.add("NUMERO");
                    obrigatorios.add("BAIRRO");
                    obrigatorios.add("COMPLEMENTO");
                    obrigatorios.add("DT_NASCIMENTO");
                    for (String obrigatorio : obrigatorios) {
                        contem = false;
                        for (String camposObrigatorios : getCamposAdicionais()) {
                            if (obrigatorio.equalsIgnoreCase(camposObrigatorios)) {
                                contem = true;
                            }
                        }
                        if (!contem)
                            throw new Exception("O campo adicional \"" + obrigatorio + "\", é obrigatório para transações através do convênio \"" + TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.getDescricao() + "\"");
                    }
                }
            }

        } else {

            setListaVendasOnlineConvenioTentativa(new ArrayList<>());
            if (!UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                getVendasOnlineConvenioTentativaVO().setOrdem(1);
                getVendasOnlineConvenioTentativaVO().getEmpresaVO().setCodigo(config.getEmpresa());
                getListaVendasOnlineConvenioTentativa().add(getVendasOnlineConvenioTentativaVO());
            }
        }
    }

    public boolean existeConvenioFacilitePay() throws Exception {
        for (VendasOnlineConvenioTentativaVO vendasVO : getListaVendasOnlineConvenioTentativa()) {
            ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobrancaVO(vendasVO.getConvenioCobrancaVO().getCodigo());
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                return true;
            }
        }
        return false;
    }

    public boolean existeConvenioPagoLivre() throws Exception {
        for (VendasOnlineConvenioTentativaVO vendasVO : getListaVendasOnlineConvenioTentativa()) {
            ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobrancaVO(vendasVO.getConvenioCobrancaVO().getCodigo());
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE)) {
                return true;
            }
        }
        return false;
    }

    public void verImagens() {
        try {
            imagens = getFacade().getVendasOnline().imagens(getEmpresaVO().getCodigo());
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void montarListaEmpresas() throws Exception {
        if (request().getParameter("recarregarVendasControle") == null) {
            boolean naoPovoado = true;
            setSelectItemListaEmpresas(new ArrayList<>());
            List<SelectItem> selectItemListaEmpresasFake = new ArrayList<>();
            for (Object usuarioPerfilAcessoVO : getUsuario().getUsuarioPerfilAcessoVOs()) {
                if (((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO).getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                    if (((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO).getPerfilAcesso().consultarObjPermissaoVO("ConsultarInfoTodasEmpresas") != null) {
                        if (((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO).getPerfilAcesso().consultarObjPermissaoVO("VendasOnline") != null) {
                            naoPovoado = false;
                            List<EmpresaVO> empresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_MINIMOS);
                            for (EmpresaVO emp : empresas) {
                                selectItemListaEmpresasFake.add(new SelectItem(emp.getCodigo(), emp.getNome()));
                            }
                        }
                    }
                }
            }

            if (naoPovoado == false) {
                getSelectItemListaEmpresas().add(new SelectItem(getEmpresaVO().getCodigo(), getEmpresaVO().getNome()));
                for (Object usuarioPerfilAcessoVO2 : getUsuario().getUsuarioPerfilAcessoVOs()) {
                    if (!((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO2).getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                        if (((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO2).getPerfilAcesso().consultarObjPermissaoVO("VendasOnline") != null) {
                            for (SelectItem item : selectItemListaEmpresasFake) {
                                if (item.getValue().equals(((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO2).getEmpresa().getCodigo())) {
                                    getSelectItemListaEmpresas().add(item);
                                }
                            }
                        }
                    }
                }
            }

            if (naoPovoado)
                getSelectItemListaEmpresas().add(new SelectItem(getEmpresaVO().getCodigo(), getEmpresaVO().getNome()));
        }
    }

    public byte[] uploadImagemPaginaInicial(UploadEvent upload, PaginaInicialVendasOnlineVO paginaInicialUpload, Integer posicao) throws Exception {

        setMsgAlert("");

        // CARREGA A IMAGEM
        UploadItem item = upload.getUploadItem();
        File imagemCaminho = item.getFile();

        boolean erroAoLerArquivo = true;
        try {

            // VALIDAÇÃO
            validarCampos();
            if (!validarResolucao(item, 1920, 1080)) {
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }
            if (UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione o convênio de cobrança padrão indo na aba gestão em seguida engrenagem de configuração.");
            }

            // CONVERTE A IMAGEM
            BufferedImage bufferedImage = ImageIO.read(imagemCaminho);
            erroAoLerArquivo = false;
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "png", baos);

            // CONFIGURAÇÕES FINAIS
            paginaInicialUpload.setCodVendasOnlineConfig(config.getCodigo());
            getFacade().getVendasOnline().gravarPaginaInicial(paginaInicialUpload,posicao);
            String key = MidiaService.getInstance().uploadObjectFromByteArray(
                    getKey(),
                    MidiaEntidadeEnum.IMAGENS_PAGINA_INICIAL_VENDAS,
                    String.format("%s_%s", Calendario.hoje().getTime(), paginaInicialUpload.getCodigo()),
                    baos.toByteArray());
            paginaInicialUpload.setFotoKey(key);
            limparMsg();
            setMsgAlert(getMensagemNotificar());
            return baos.toByteArray();
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, imagemCaminho, "uploadImagem");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public byte[] uploadImagemPaginaInicial1(UploadEvent upload) throws Exception {
        return uploadImagemPaginaInicial(upload, getPaginaInicialVendasOnlineVO(), 1);
    }

    public byte[] uploadImagemPaginaInicial2(UploadEvent upload) throws Exception {
        return uploadImagemPaginaInicial(upload, getPaginaInicialVendasOnlineVO2(), 2);
    }

    public byte[] uploadImagemPaginaInicial3(UploadEvent upload) throws Exception {
        return uploadImagemPaginaInicial(upload, getPaginaInicialVendasOnlineVO3(), 3);
    }

    public byte[] uploadImagemCaptacaoLeads(UploadEvent upload) throws Exception {

        setMsgAlert("");

        // CARREGA A IMAGEM
        UploadItem item = upload.getUploadItem();
        File imagemCaminho = item.getFile();

        boolean erroAoLerArquivo = true;
        try {

            // VALIDAÇÃO
            validarCampos();
            if (!validarResolucao(item, 1920, 1080)) {
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }
            if (UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione o convênio de cobrança padrão indo na aba gestão em seguida engrenagem de configuração.");
            }

            // CONVERTE A IMAGEM
            BufferedImage bufferedImage = ImageIO.read(imagemCaminho);
            erroAoLerArquivo = false;
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "png", baos);

            // CONFIGURAÇÕES FINAIS
            getCaptacaoLeadsVendasOnline().setCodVendasOnlineConfig(config.getCodigo());
            getFacade().getVendasOnline().gravarCaptacaoLeads(getCaptacaoLeadsVendasOnline());
            String key = MidiaService.getInstance().uploadObjectFromByteArray(
                    getKey(),
                    MidiaEntidadeEnum.IMAGENS_CAPTACAO_LEADS_VENDAS,
                    String.format("%s_%s", Calendario.hoje().getTime(), getCaptacaoLeadsVendasOnline().getCodigo()),
                    baos.toByteArray());
            getCaptacaoLeadsVendasOnline().setFotoKey(key);
            limparMsg();
            setMsgAlert(getMensagemNotificar());
            return baos.toByteArray();
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, imagemCaminho, "uploadImagem");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public byte[] uploadImagemFotoFachada(UploadEvent upload) throws Exception {
        setMsgAlert("");
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            if (!validarResolucao(item, 1920, 1080)) {
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[1000000];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();
            if (UteisValidacao.emptyNumber(config.getCodigo())) {
                throw new Exception("Nenhuma configuração de vendas online existente para prosseguir. Primeiro você precisa gravar as configurações do vendas online (Gestão -> Configurações -> Gravar configurações)");
            }
            getFotoFachadaVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
            getFacade().getVendasOnline().gravarFotoFachada(getFotoFachadaVendasOnlineVO());
            String key = MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                    MidiaEntidadeEnum.IMAGEM_FOTO_FACHADA_VENDAS,
                    String.format("%s_%s", Calendario.hoje().getTime(),
                            getFotoFachadaVendasOnlineVO().getCodigo()), arrayOutputStream.toByteArray());
            getFotoFachadaVendasOnlineVO().setFotoKey(key);
            montarSucesso("msg_operacao_sucesso");
            setMsgAlert(getMensagemNotificar());
            return foto;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagemModalidadeCarrousel");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public byte[] uploadImagemMultiEmpresaConfig(UploadEvent upload) throws Exception {
        setMsgAlert("");
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            if (!validarResolucao(item, 100, 100)) {
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }
            if (UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione o convênio de cobrança padrão indo na aba gestão em seguida engrenagem de configuração.");
            }
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[1000000];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();
            getFacade().getVendasOnline().gravarMultiEmpresaConfig(getMultiEmpresaConfigsVendasOnlineVO());
            String key = MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                    MidiaEntidadeEnum.IMAGENS_BANNER_MULTI_CONFIG_VENDAS,
                    String.format("%s_%s", Calendario.hoje().getTime(),
                            getMultiEmpresaConfigsVendasOnlineVO().getCodigo()), arrayOutputStream.toByteArray());
            getMultiEmpresaConfigsVendasOnlineVO().setFotoKey(key);
            getFacade().getVendasOnline().gravarMultiEmpresaConfig(getMultiEmpresaConfigsVendasOnlineVO());
            montarSucesso("msg_upload_arquivo");
            setMsgAlert(getMensagemNotificar());
            return foto;
        } catch (Exception e) {
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagem");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public byte[] uploadImagemMenu(UploadEvent upload) throws Exception {
        setMsgAlert("");
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            if (!validarResolucao(item, 150, 71)) {
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }

            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[1000000];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();
            if(isAbaModaBanner()){
                getModalidadeCarrouselVendasOnlineVO().setBanner(true);
            }
            getMenuVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
            getFacade().getVendasOnline().gravarMenu(getMenuVendasOnlineVO());
            String key = MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                    MidiaEntidadeEnum.IMAGENS_MODALIDADE_CORROUSEL_VENDAS,
                    String.format("%s_%s", Calendario.hoje().getTime(),
                            getMenuVendasOnlineVO().getCodigo()), arrayOutputStream.toByteArray());
            getMenuVendasOnlineVO().setFotoKey(key);
            setMsgAlert(getMensagemNotificar());
            return foto;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagemModalidadeCarrousel");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    private Boolean validarResolucao(UploadItem item, int largura, int altura) throws IOException {
        BufferedImage bimg = ImageIO.read(item.getFile());
        if (bimg.getWidth()==largura && bimg.getHeight()==altura){
            return true;
        }
        return false;
    }

    public byte[] uploadImagemModalidadeCarrousel(UploadEvent upload) throws Exception {
        limparMsg();
        UploadItem item = upload.getUploadItem();
        boolean erroAoLerArquivo = true;
        File item1 = item.getFile();

        try {
            if (!validarResolucao(item, 960, 600)
                && !validarResolucao(item, 378, 236)){
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }
            if (UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione o convênio de cobrança padrão indo na aba gestão em seguida engrenagem de configuração.");
            }
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[1000000];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();
            if(isAbaModaBanner()){
                getModalidadeCarrouselVendasOnlineVO().setBanner(true);
            }
            getModalidadeCarrouselVendasOnlineVO().setBytesArquivo(arrayOutputStream.toByteArray());
            getModalidadeCarrouselVendasOnlineVO().setNomeArquivo(item.getFileName());
            getModalidadeCarrouselVendasOnlineVO().setFotoKey(null);
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Arquivo anexado com sucesso.");
            setMsgAlert(getMensagemNotificar());
            return foto;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagemModalidadeCarrousel");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public byte[] uploadImagemModalidadeCarrousel2(UploadEvent upload) throws Exception {
        limparMsg();
        UploadItem item = upload.getUploadItem();
        boolean erroAoLerArquivo = true;
        File item1 = item.getFile();

        try {
            if (!validarResolucao(item, 960, 600)
                && !validarResolucao(item, 378, 236)) {
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }
            if (UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione o convênio de cobrança padrão indo na aba gestão em seguida engrenagem de configuração.");
            }
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[1000000];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();
            if(isAbaModaBanner()){
                getModalidadeCarrouselVendasOnlineVO2().setBanner(true);
            }
            getModalidadeCarrouselVendasOnlineVO2().setBytesArquivo(arrayOutputStream.toByteArray());
            getModalidadeCarrouselVendasOnlineVO2().setNomeArquivo(item.getFileName());
            getModalidadeCarrouselVendasOnlineVO2().setFotoKey(null);
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Arquivo anexado com sucesso.");
            setMsgAlert(getMensagemNotificar());
            return foto;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagemModalidadeCarrousel");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public byte[] uploadImagemModalidadeCarrousel3(UploadEvent upload) throws Exception {
        limparMsg();
        UploadItem item = upload.getUploadItem();
        boolean erroAoLerArquivo = true;
        File item1 = item.getFile();

        try {
            if (!validarResolucao(item, 960, 600)
                && !validarResolucao(item, 378, 236)) {
                throw new Exception("Arquivo não Enviado. Resolução é inferior à solicitada");
            }
            if (item.getFile().length() > 1000000) {
                throw new Exception("Arquivo não Enviado. Tamanho Máximo Permitido 1 MB");
            }
            if (UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione o convênio de cobrança padrão indo na aba gestão em seguida engrenagem de configuração.");
            }
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[1000000];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();
            if(isAbaModaBanner()){
                getModalidadeCarrouselVendasOnlineVO3().setBanner(true);
            }
            getModalidadeCarrouselVendasOnlineVO3().setBytesArquivo(arrayOutputStream.toByteArray());
            getModalidadeCarrouselVendasOnlineVO3().setNomeArquivo(item.getFileName());
            getModalidadeCarrouselVendasOnlineVO3().setFotoKey(null);
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Arquivo anexado com sucesso.");
            setMsgAlert(getMensagemNotificar());
            return foto;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagemModalidadeCarrousel");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public byte[] uploadImagem(UploadEvent upload) throws Exception {
        setMsgAlert("");
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        boolean erroAoLerArquivo = true;
        try {
            BufferedImage outImage = ImageIO.read(item1);
            erroAoLerArquivo = false;
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] foto = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
            fi.close();

            String key = MidiaService.getInstance().uploadObjectFromByteArray(getKey(),
                    MidiaEntidadeEnum.IMAGENS_ACADEMIA_VENDAS,
                    String.format("%s_%s", Calendario.hoje().getTime(),
                            getEmpresaVO().getCodigo()), arrayOutputStream.toByteArray());

            ImagensAcademiaVendasVO imagem = new ImagensAcademiaVendasVO();
            imagem.setEmpresa(getEmpresaVO().getCodigo());
            imagem.setFotoKey(key);

            getFacade().getVendasOnline().gravarImagem(imagem);
            verImagens();
            setMsgAlert(getMensagemNotificar());
            return foto;
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            tratarErroAoCarregarImagem(e, erroAoLerArquivo, item1, "uploadImagem");
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            return null;
        }
    }

    public void removerImagem() {
        try {
            ImagensAcademiaVendasVO imagem = (ImagensAcademiaVendasVO) context().getExternalContext().getRequestMap().get("imagem");
            MidiaService.getInstance().deleteObject(getKey(),
                    MidiaEntidadeEnum.IMAGENS_ACADEMIA_VENDAS,
                    imagem.getFotoKey());
            getFacade().getVendasOnline().excluirImagem(imagem);
            verImagens();
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void abrirModalExclusao(){
        limparMsg();
        ModalidadeCarrouselVendasOnlineVO modaCarrousel = (ModalidadeCarrouselVendasOnlineVO) context().getExternalContext().getRequestMap().get("item");
        setModalidadeCarrouselVendasOnlineVO(modaCarrousel);
        setMostrarConfirmacaoExclusao(Boolean.TRUE);
    }

    public void removerModaCarrousel() {
        try {
            limparMsg();
            boolean banner = isAbaModaBanner()?true:false;
            List<ModalidadeCarrouselVendasOnlineVO> modalidades =
                    getFacade().getVendasOnline().consultarModalidadesCarrouselVendasOnlinePorAgrupamento(getModalidadeCarrouselVendasOnlineVO().getTipoAgrupamento().getId(), banner);
            if(modalidades != null){
                for (ModalidadeCarrouselVendasOnlineVO mod :modalidades) {
                    getFacade().getVendasOnline().excluirModaCarrousel(mod.getCodigo());
                    getListaModalidadeCarrouselVendasOnline().remove(mod);
                }
            }
            montarSucessoDadosGravados();
            init();
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerPlanoSite() {
        try {
            limparMsg();
            PlanosSiteVendasOnlineVO planoSite = (PlanosSiteVendasOnlineVO) context().getExternalContext().getRequestMap().get("planoSite");
            getFacade().getVendasOnline().excluirPlanoSite(planoSite.getCodigo());
            montarSucessoDadosGravados();
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void editarModaCarrouselVendasOnline() {
        try {
            boolean banner = isAbaModaBanner()?true:false;
            ModalidadeCarrouselVendasOnlineVO modalidade = (ModalidadeCarrouselVendasOnlineVO) context().getExternalContext().getRequestMap().get("item");
            List<ModalidadeCarrouselVendasOnlineVO> modalidades =
                    getFacade().getVendasOnline().consultarModalidadesCarrouselVendasOnlinePorAgrupamento(modalidade.getTipoAgrupamento().getId(), banner);
            if(modalidades != null){
                setModalidadeCarrouselVendasOnlineVO(modalidades.get(0));
                setModalidadeCarrouselVendasOnlineVO2(modalidades.get(1));
                setModalidadeCarrouselVendasOnlineVO3(modalidades.get(2));
            }
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void editarPlanoSiteVendasOnline() {
        try {
            limparMsg();
            setPlanosSiteVendasOnlineVO((PlanosSiteVendasOnlineVO) context().getExternalContext().getRequestMap().get("planoSite"));
            if(planosSiteVendasOnlineVO != null && !UteisValidacao.emptyString(planosSiteVendasOnlineVO.getBeneficios())) {
                setLstBeneficiosDoPlano(formatarBeneficiosPlanoParaLista(planosSiteVendasOnlineVO.getBeneficios()));
            }else{
                setLstBeneficiosDoPlano(new ArrayList<String>());
                setBeneficioParaAdicionar("");
            }
            verificarPlanoSelecionado(null);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerFotoKeyModaCarrouselServidor(String fotoKey) {
        try {
            MidiaService.getInstance().deleteObject(getKey(), MidiaEntidadeEnum.IMAGENS_MODALIDADE_CORROUSEL_VENDAS,
                    fotoKey);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemModaCarrousel() {
        if(!UteisValidacao.emptyString(getModalidadeCarrouselVendasOnlineVO().getNomeArquivo())) {
            getModalidadeCarrouselVendasOnlineVO().setFotoKey("");
            getModalidadeCarrouselVendasOnlineVO().setNomeArquivo(null);
            getModalidadeCarrouselVendasOnlineVO().setBytesArquivo(null);
        }
    }

    public void removerImagemModaCarrousel2() {
        if(!UteisValidacao.emptyString(getModalidadeCarrouselVendasOnlineVO2().getNomeArquivo())) {
            getModalidadeCarrouselVendasOnlineVO2().setFotoKey("");
            getModalidadeCarrouselVendasOnlineVO2().setNomeArquivo(null);
            getModalidadeCarrouselVendasOnlineVO2().setBytesArquivo(null);
        }
    }

    public void removerImagemModaCarrousel3() {
        if(!UteisValidacao.emptyString(getModalidadeCarrouselVendasOnlineVO3().getNomeArquivo())) {
            getModalidadeCarrouselVendasOnlineVO3().setFotoKey("");
            getModalidadeCarrouselVendasOnlineVO3().setNomeArquivo(null);
            getModalidadeCarrouselVendasOnlineVO3().setBytesArquivo(null);
        }
    }
    public void removerImagemPagInicialServidor(String fotoKey) {
        try {
            MidiaService.getInstance().deleteObject(getKey(),
                    MidiaEntidadeEnum.IMAGENS_PAGINA_INICIAL_VENDAS,
                    fotoKey);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemCapLeadsServidor(String fotoKey) {
        try {
            MidiaService.getInstance().deleteObject(getKey(),
                    MidiaEntidadeEnum.IMAGENS_CAPTACAO_LEADS_VENDAS,
                    fotoKey);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemMenuServidor(String fotoKey) {
        try {
            MidiaService.getInstance().deleteObject(getKey(),
                    MidiaEntidadeEnum.IMAGENS_MENU_VENDAS,
                    fotoKey);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemMultiConfigServidor(String fotoKey) {
        try {
            MidiaService.getInstance().deleteObject(getKey(),
                    MidiaEntidadeEnum.IMAGENS_BANNER_MULTI_CONFIG_VENDAS,
                    fotoKey);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemPagInicial() {
        try {
            setFotoKeyExcluirPagInicialVendasOnline(getPaginaInicialVendasOnlineVO().getFotoKey());
            getPaginaInicialVendasOnlineVO().setFotoKey("");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemPagInicial2() {
        try {
            setFotoKeyExcluirPagInicialVendasOnline2(getPaginaInicialVendasOnlineVO2().getFotoKey());
            getPaginaInicialVendasOnlineVO2().setFotoKey("");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemPagInicial3() {
        try {
            setFotoKeyExcluirPagInicialVendasOnline3(getPaginaInicialVendasOnlineVO3().getFotoKey());
            getPaginaInicialVendasOnlineVO3().setFotoKey("");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }


    public void removerImagemCapLeads() {
        try {
            setFotoKeyExcluirCapLeadsVendasOnline(getCaptacaoLeadsVendasOnline().getFotoKey());
            getCaptacaoLeadsVendasOnline().setFotoKey("");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerFotoFachada() {
        try {
            setFotoKeyExcluirFotoFachadaVendasOnline(getFotoFachadaVendasOnlineVO().getFotoKey());
            getFotoFachadaVendasOnlineVO().setFotoKey("");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }


    public void removerImagemMenu() {
        try {
            setFotoKeyExcluirMenuVendasOnline(getMenuVendasOnlineVO().getFotoKey());
            getMenuVendasOnlineVO().setFotoKey("");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void removerImagemMultiConfigs() {
        try {
            setFotoKeyExcluirMultiConfigsVendasOnline(getMultiEmpresaConfigsVendasOnlineVO().getFotoKey());
            getMultiEmpresaConfigsVendasOnlineVO().setFotoKey("");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    private void tratarErroAoCarregarImagem(Exception e, boolean erroAoLerArquivo, File file, String nomeMetodo) {
        if ((erroAoLerArquivo) || (!isCMYK(file))) {
            montarErro("Existe um erro não identificado no arquivo de imagem. Edite a imagem, crie outro arquivo e repita a operação.");
        }
        Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CARREGAR IMAGEM EMPRESA. método:" + nomeMetodo + ". Erro: " + e.getMessage());
    }

    public void montarConvenios() throws Exception {
        setListaConvenioCobrancaGeralVO(new ArrayList<>());
        setListaConvenioCobrancaGeralVO(getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(getEmpresaVO().getCodigo(), false,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, SituacaoConvenioCobranca.ATIVO));

        setListaConvenioCobrancaVO(new ArrayList<>());
        setListaConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorTipoCobranca(TipoCobrancaEnum.ONLINE,
                getEmpresaVO().getCodigo(), SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_TODOS));
        convenios = new ArrayList<SelectItem>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            convenios.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(convenios, "label");
        convenios.add(0, new SelectItem(0, ""));
    }

    public void montarPlanosSite() throws Exception {
        List<PlanoVO> objs = getFacade().getPlano().consultarVigentes(getEmpresaVO().getCodigo(),
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA,
                true,
                false, true);
        planosSite = new ArrayList<SelectItem>();
        planosSite.add(new SelectItem(0, ""));
        if (!UteisValidacao.emptyList(objs)) {
            for (PlanoVO o : objs) {
                planosSite.add(new SelectItem(o.getCodigo(), o.getDescricao()));
            }
        }
    }

    public void montarPlanos() throws Exception {
        List<PlanoVO> objs = getFacade().getPlano().consultarVigentes(getEmpresaVO().getCodigo(),
                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA,
                true,
                false, false);
        planos = new ArrayList<SelectItem>();
        planosCredito = new HashMap<>();
        if (!UteisValidacao.emptyList(objs)) {
            plano = objs.get(0).getCodigo();
            for (PlanoVO o : objs) {
                planosCredito.put(o.getCodigo(), o.isVendaCreditoTreino());
                planos.add(new SelectItem(o.getCodigo(), o.getDescricao()));
            }
        }
    }

    public void montarEventos()throws Exception{
        List<EventoVO> lista = getFacade().getEvento().consultarTodosEventosAtivos(false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        this.listaEventos = new ArrayList<>();
        this.listaEventos.add(new SelectItem(0,""));
        for (EventoVO eventoVO: lista){
            this.listaEventos.add(new SelectItem(eventoVO.getCodigo(),eventoVO.getDescricao()));
        }
    }

    public void montarListaSelectItemPlanoCategoria() {
        try {
            List resultadoConsulta = getFacade().getCategoria().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            List<SelectItem> objs = new ArrayList<>();
            while (i.hasNext()) {
                CategoriaVO obj = (CategoriaVO) i.next();
                categoria = categoria == null ? obj.getCodigo() : categoria;
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            setCategorias(objs);
        } catch (Exception e){
            montarErro(e);
        }
    }

    public boolean getMontarLinkCredito(){
        return plano == null || planosCredito.get(plano) == null ? false : planosCredito.get(plano);
    }

    public boolean getMontarLinkDiaria(){
        return produto == null || produtosDiaria.get(produto) == null ? false : produtosDiaria.get(produto);
    }

    public void montarProdutos() throws Exception {
        List<ProdutoVO> objs = getFacade().getProduto().consultarParaVendasOnline(0, 0, getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        produtosDiaria = new HashMap<>();
        produtos = new ArrayList<>();
        if (!UteisValidacao.emptyList(objs)) {
            produto = objs.get(0).getCodigo();
            for (ProdutoVO o : objs) {
                produtosDiaria.put(o.getCodigo(), o.getTipoProduto().equals(TipoProduto.DIARIA.getCodigo()));
                produtos.add(new SelectItem(o.getCodigo(), o.getDescricao()));
            }
        }
    }

    public void totalizar() throws Exception {
        setVendas(0);

        Integer vendasPlano = getFacade().getContrato().vendasOnlinePlanoTotal(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
        Integer vendasProduto = getFacade().getContrato().vendasOnlineProdutoTotal(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
        Integer vendasDiarias = getFacade().getAulaAvulsaDiaria().vendasOnlineDiariaTotal(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
        setVendas(vendasPlano + vendasProduto + vendasDiarias);

        setVendasNaoFinalizadas(getFacade().getLog().vendasNaoRealizadasTotal(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal()));
    }

    public void realizadas() {
        try {
            setLista(new ArrayList<>());
            titulo = "Vendas realizadas";

            List<ItemRelatorioTO> realizadas = new ArrayList<>();
            List<ItemRelatorioTO> planos = getFacade().getContrato().vendasOnlinePlanoLista(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
            List<ItemRelatorioTO> produtos = getFacade().getContrato().vendasOnlineProdutoLista(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
            List<ItemRelatorioTO> diarias = getFacade().getAulaAvulsaDiaria().vendasOnlineDiariaLista(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
            realizadas.addAll(planos);
            realizadas.addAll(produtos);
            realizadas.addAll(diarias);
            Ordenacao.ordenarListaReverse(realizadas, "dataLancamento");
            setLista(realizadas);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void naorealizadas() {
        try {
            setLista(new ArrayList<>());
            titulo = "Vendas não finalizadas";
            lista = getFacade().getLog().vendasNaoRealizadasLista(getEmpresaVO().getCodigo(), getDataInicial(), getDataFinal());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getLinkPlanos() {
        return url + "/planos?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + addParamUsuario();
    }

    public String getLinkProdutos() {
        return url + "/produtos?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + addParamUsuario();
    }

    public String getLinkLoja() {
        return url + "/loja?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + addParamUsuario();
    }

    public String getLinkCadastro() throws Exception {
        return url + "/cadastro?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + (UteisValidacao.emptyString(addParamUsuario()) && !UteisValidacao.emptyString(addParamFreepass()) ? "&us=" + getUsuarioLogado().getCodigo():  addParamUsuario()) + addParamFreepass();
    }

    public void atualizarlinkPlanoCupom() {
        setMsgAlert("");
        if (UteisValidacao.emptyString(getParamCupom())) {
            setNumeroCupomDesconto("");
            setParamCupom("");
        } else {
            try {
                CupomDescontoVO cupomDescontoVO = new OAMDService().consultarPorNumeroCupom(getParamCupom(), getEmpresaLogado().getCodEmpresaFinanceiro());
                if ((cupomDescontoVO == null) || (cupomDescontoVO.getId() == null)){
                    throw new Exception("Cupom inválido.");
                }
                setNumeroCupomDesconto(URLEncoder.encode(getParamCupom()));
                montarSucessoGrowl("Cupom desconto encontrado, o link para direcionamento ao plano foi atualizado.");
                setMsgAlert(getMensagemNotificar());
            } catch (Exception e) {
                setParamCupom("");
                setNumeroCupomDesconto("");
                montarErro("Cupom desconto não encontrado, informe outro cupom.");
                setMsgAlert(getMensagemNotificar());
            }
        }
    }

    public void gerarLinkPlanoAbaCampanha(){
      try {
          limparMsg();
          setMsgAlert("");
          if (!UteisValidacao.emptyString(getParamCupom())) {
             atualizarlinkPlanoCupom();
          }
          if ((codigoEventoPlano == null) || (codigoEventoPlano == 0)){
              throw new ConsistirException("O campo evento deve ser informado.");
          }
          String link = "";
          String linkAgenda = null;
          if (tipoLinkPlano == 1){
              this.plano = null;
              this.categoria = null;
              link = getLinkPlanos();
          }else if (tipoLinkPlano == 2){
              if ((this.plano == null) || (this.plano == 0)){
                  throw new ConsistirException("O campo plano deve ser informado.");
              }
              this.categoria = null;
              link = getLinkPlanoEspecifico();
          }else if (tipoLinkPlano == 3){
              this.plano = null;
              if ((this.categoria == null) || (this.categoria == 0)){
                  throw new ConsistirException("O campo categoria deve ser informado.");
              }
              link = getLinkPlanoCategoriaEspecifico();
          }
          link = link + "&evento="+codigoEventoPlano;;
          if (getMontarLinkCredito()){
              linkAgenda = getLinkPlanoEspecificoAgenda() + "&evento="+codigoEventoPlano;
          }
          gerarLink(TipoLinkCampanhaEnum.PLANO,this.codigoEventoPlano,this.plano,this.categoria, getParamCupom(),null,null,link,linkAgenda);
          listaLinksCampanhaPlano  = getFacade().getVendasOnlineCampanha().consultar(TipoLinkCampanhaEnum.PLANO.getCodigo());
        }catch (Exception e) {
          montarErro(e);
      }
    }


    public void gerarLinkProdutoAbaCampanha(){
        try {
            limparMsg();
            setMsgAlert("");
            if ((codigoEventoProduto == null) || (codigoEventoProduto == 0)){
                throw new ConsistirException("O campo evento deve ser informado.");
            }
            String link = null;
            String linkAgenda = "";
            Integer quantidadeProduto = null;
            if (tipoLinkProduto == 1){
                this.produto = null;
                quantidadeProduto = null;
                link = getLinkProdutos();
            }else if (tipoLinkProduto == 2){
                quantidadeProduto = getQtdProduto();
                if ((this.produto == null) || (this.produto == 0)){
                    throw new ConsistirException("O campo produto deve ser informado.");
                }
                link = getLinkProdutoEspecifico();
            }
            if (UteisValidacao.emptyNumber(getQtdProduto())) {
                throw new Exception("A quantidade do produto não pode ser 0");
            }
            link = link + "&evento="+codigoEventoProduto;
            if (getMontarLinkDiaria()){
                linkAgenda = getLinkProdutoEspecificoAgenda() + "&evento="+codigoEventoProduto;
            }
            gerarLink(TipoLinkCampanhaEnum.PRODUTO,this.codigoEventoProduto,null,null, null,this.produto,quantidadeProduto, link,linkAgenda);
            listaLinksCampanhaProduto  = getFacade().getVendasOnlineCampanha().consultar(TipoLinkCampanhaEnum.PRODUTO.getCodigo());
        }catch (Exception e) {
            montarErro(e);
        }
    }

    public void gerarLinkVisitanteAbaCampanha(){
        try {
            limparMsg();
            setMsgAlert("");
            if ((codigoEventoVisitante == null) || (codigoEventoVisitante == 0)){
                throw new ConsistirException("O campo evento deve ser informado.");
            }
            String link = getLinkCadastro() + "&evento="+codigoEventoVisitante;
            gerarLink(TipoLinkCampanhaEnum.VISITANTE,this.codigoEventoVisitante,null,null, null,null,null, link,null);
            listaLinksCampanhaVisitante  = getFacade().getVendasOnlineCampanha().consultar(TipoLinkCampanhaEnum.VISITANTE.getCodigo());
        }catch (Exception e) {
            montarErro(e);
        }
    }

    private void gerarLink(TipoLinkCampanhaEnum tipoLinkCampanhaEnum, Integer codigoEvento, Integer codigoPlano, Integer codigoCategoria, String cupomDesconto, Integer codigoProduto, Integer qtdeProduto, String link,String linkAgenda)throws Exception{
        VendasOnlineCampanhaVO vendasOnlineCampanhaVO = new VendasOnlineCampanhaVO();
        vendasOnlineCampanhaVO.setTipoLink(tipoLinkCampanhaEnum.getCodigo());
        vendasOnlineCampanhaVO.setEmpresaVO(this.empresaVO);
        vendasOnlineCampanhaVO.setEventoVO(new EventoVO());
        vendasOnlineCampanhaVO.getEventoVO().setCodigo(codigoEvento);
        vendasOnlineCampanhaVO.setLink(link);
        vendasOnlineCampanhaVO.setLinkAgenda(linkAgenda);
        vendasOnlineCampanhaVO.setCupomDesconto(cupomDesconto);
        vendasOnlineCampanhaVO.setQuantidadeProduto(qtdeProduto);
        if (codigoPlano != null){
            vendasOnlineCampanhaVO.setPlanoVO(new PlanoVO());
            vendasOnlineCampanhaVO.getPlanoVO().setCodigo(codigoPlano);
        }
        if (codigoCategoria != null){
            vendasOnlineCampanhaVO.setCategoriaVO(new CategoriaVO());
            vendasOnlineCampanhaVO.getCategoriaVO().setCodigo(codigoCategoria);
        }
        if (codigoProduto != null){
            vendasOnlineCampanhaVO.setProdutoVO(new ProdutoVO());
            vendasOnlineCampanhaVO.getProdutoVO().setCodigo(codigoProduto);
        }
        getFacade().getVendasOnlineCampanha().incluir(vendasOnlineCampanhaVO);
        montarSucessoGrowl("");
        montarSucessoDadosGravados();
    }

    public void limparAtualizarlinkPlanoCupom() {
        setParamCupom("");
        setNumeroCupomDesconto("");
    }

    public String getLinkPlanoCategoriaEspecifico() throws Exception {
        if (categoria != null && categoria > 0) {
            return url + "/planos?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + "&ct=" + categoria + addParamUsuario();
        }
        return "";
    }

    public String getLinkPlanoEspecifico() throws Exception {
        if (plano != null && plano > 0) {
            return url + "/checkout?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + "&pl=" + plano +
                    (UteisValidacao.emptyString(getParamCupom()) ? numeroCupomDesconto : "&cupom=" + numeroCupomDesconto) +
                    addParamUsuario();
        }
        return "";
    }

    public String getLinkPlanoEspecificoAgenda() throws Exception {
        if (plano != null && plano > 0) {
            return url + "/agenda-aulas?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + "&pl=" + plano +
                    (UteisValidacao.emptyString(getParamCupom()) ? numeroCupomDesconto : "&cupom=" + numeroCupomDesconto) +
                    addParamUsuario();
        }
        return "";
    }

    public String getLinkProdutoEspecificoAgenda() throws Exception {
        if (produto != null && produto > 0) {
            return url + "/agenda-aulas?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + "&pr=" + produto +
                    (UteisValidacao.emptyString(getParamCupom()) ? numeroCupomDesconto : "&cupom=" + numeroCupomDesconto) +
                    addParamUsuario();
        }
        return "";
    }

    public String getLinkProdutoEspecifico() throws Exception {
        if (produto != null && produto > 0) {
            return url + "/checkout?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + "&pr=" + produto +
                    (getQtdProduto() > 1 ? "&qtd=" + getQtdProduto() : "") + addParamUsuario();
        }
        return "";
    }

    public void irParaTelaCliente() {
        ItemRelatorioTO obj = (ItemRelatorioTO) context().getExternalContext().getRequestMap().get("item");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarListaSelectItemConsultor(EmpresaVO empresa) throws Exception {
        List resultadoConsulta = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT", empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO) i.next();
            boolean temUsuario = getFacade().getColaborador().verificarColaboradorTemUsuario(obj.getCodigo());
            try {
                if (!temUsuario) {
                    List<UsuarioVO> usuarios = getFacade().getUsuario().consultarPorNome(obj.getPessoa().getNome(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    temUsuario = !UteisValidacao.emptyList(usuarios);
                }
            } catch (Exception e) {
                Uteis.logar(e, GestaoVendasOnlineControle.class);
            }
            if (temUsuario) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
            }
        }
        setListaSelectItemConsultor(objs);
    }

    public void montarListaSelectItemCampanhaCupomDesconto(EmpresaVO empresa) throws Exception {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");

            Integer idFavorecido = empresa.getCodEmpresaFinanceiro();
            RedeEmpresaVO redeEmpresaVO = loginControle.getRedeEmpresaVO();
            String chaveRede = "";
            if (redeEmpresaVO != null) {
                chaveRede = redeEmpresaVO.getChaverede();
            }

            CampanhaCupomDesconto campanhaCupomDescontoDAO = new CampanhaCupomDesconto();
            List<CampanhaCupomDescontoJSON> campanhaCupomDescontoJSONS = campanhaCupomDescontoDAO.consultarCampanhaCupomDescontoJSONOAMD(chaveRede, idFavorecido, true);
            List<SelectItem> objs = new ArrayList<>();
            objs.add(new SelectItem(0, ""));
            Iterator i = campanhaCupomDescontoJSONS.iterator();
            while (i.hasNext()) {
                CampanhaCupomDescontoJSON obj = (CampanhaCupomDescontoJSON) i.next();
                objs.add(new SelectItem(obj.getId(), obj.getDescricaoCampanha()));
            }
            setListaSelectItemCampanhaCupomDesconto(objs);
        } catch (Exception e) {
            Uteis.logar(e, GestaoVendasOnlineControle.class);
        }
    }

    public void validarApresentacaoTipoParcelamentoStone() throws Exception {
        if (!UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
            ConvenioCobrancaVO convenio = getFacade().getConvenioCobranca().consultarPorChavePrimaria(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                setApresentarTipoParcelamentoStone(true);
            } else {
                setApresentarTipoParcelamentoStone(false);
                getVendasOnlineConvenioTentativaVO().setTipoParcelamentoStone("");
            }
        } else {
            setApresentarTipoParcelamentoStone(false);
            getVendasOnlineConvenioTentativaVO().setTipoParcelamentoStone("");
        }
    }

    public void tratarApresentarValorAnuidade() {
        if (getConfig().getDetalharParcelaTelaCheckout()) {
            getConfig().setApresentarValorAnuidade(true);
        } else {
            getConfig().setApresentarValorAnuidade(false);
        }
    }

    public String getLinkEspelhoDeAulas() {
        return url + "/agenda-aulas?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() +"&le=1";
    }

    public String getLinkApi() {
        return PropsService.getPropertyValue(PropsService.urlDocumentacaoApi);
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public Integer getCategoria() {
        return categoria;
    }

    public void setCategoria(Integer categoria) {
        this.categoria = categoria;
    }

    public List<SelectItem> getPlanos() {
        return planos;
    }

    public void setPlanos(List<SelectItem> planos) {
        this.planos = planos;
    }

    public List<SelectItem> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<SelectItem> categorias) {
        this.categorias = categorias;
    }

    public Integer getVendas() {
        return vendas;
    }

    public void setVendas(Integer vendas) {
        this.vendas = vendas;
    }

    public Integer getVendasNaoFinalizadas() {
        return vendasNaoFinalizadas;
    }

    public void setVendasNaoFinalizadas(Integer vendasNaoFinalizadas) {
        this.vendasNaoFinalizadas = vendasNaoFinalizadas;
    }

    public List<SelectItem> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<SelectItem> convenios) {
        this.convenios = convenios;
    }

    public VendasConfigVO getConfig() {
        return config;
    }

    public void setConfig(VendasConfigVO config) {
        this.config = config;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<ItemRelatorioTO> getLista() {
        return lista;
    }

    public void setLista(List<ItemRelatorioTO> lista) {
        this.lista = lista;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getGrafico() {
        try {
            return arrayGrafico.toString();
        } catch (Exception e) {
            return "[]";
        }
    }

    public JSONArray getArrayGrafico() {
        return arrayGrafico;
    }

    public void setArrayGrafico(JSONArray arrayGrafico) {
        this.arrayGrafico = arrayGrafico;
    }

    public List<String> getCamposAdicionais() {
        return camposAdicionais;
    }

    public void setCamposAdicionais(List<String> camposAdicionais) {
        this.camposAdicionais = camposAdicionais;
    }

    public List<SelectItem> getCamposAdicionaisItens() {
        return camposAdicionaisItens;
    }

    public void setCamposAdicionaisItens(List<SelectItem> camposAdicionaisItens) {
        this.camposAdicionaisItens = camposAdicionaisItens;
    }

    public List<ImagensAcademiaVendasVO> getImagens() {
        return imagens;
    }

    public void setImagens(List<ImagensAcademiaVendasVO> imagens) {
        this.imagens = imagens;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        try {
            this.empresaVO = (EmpresaVO) empresaVO.getClone(false);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
    }

    public EmpresaVO getEmpresaVO() {
        try {
            if (empresaVO == null) {
                empresaVO = (EmpresaVO) getEmpresaLogado().getClone(false);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return this.empresaVO;
    }

    public Date getDataInicial() throws Exception {
        if (dataInicial == null) {
            dataInicial = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
        }
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        if (dataFinal == null) {
            dataFinal = Calendario.hoje();
        }
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<SelectItem> getSelectItemListaEmpresas() {
        return selectItemListaEmpresas;
    }

    public void setSelectItemListaEmpresas(List<SelectItem> selectItemListaEmpresas) {
        this.selectItemListaEmpresas = selectItemListaEmpresas;
    }

    public void desmarcarCobrarProdutosAtoCompra() {
        if (getConfig().getCobrarPrimeiraParcelaCompra()) {
            getConfig().setCobrarProdutosJuntoAdesao(false);
        }
    }

    public void desmarcarCobrarPrimeiraParcelaCompraRenovacao() {
        if (!getConfig().isCobrarPrimeiraParcelaCompraRenovacao()) {
            getConfig().setCobrarParcelasMesSeguinteRenovacao(false);
        }
    }

    public boolean isSairModoAvancado() {
        return sairModoAvancado;
    }

    public void setSairModoAvancado(boolean sairModoAvancado) {
        this.sairModoAvancado = sairModoAvancado;
    }

    public List getListaSelectItemConsultor() {
        return listaSelectItemConsultor;
    }

    public void setListaSelectItemConsultor(List listaSelectItemConsultor) {
        this.listaSelectItemConsultor = listaSelectItemConsultor;
    }

    public boolean isApresentarTipoParcelamentoStone() {
        return apresentarTipoParcelamentoStone;
    }

    public void setApresentarTipoParcelamentoStone(boolean apresentarTipoParcelamentoStone) {
        this.apresentarTipoParcelamentoStone = apresentarTipoParcelamentoStone;
    }

    public String getNumeroCupomDesconto() {
        return numeroCupomDesconto;
    }

    public void setNumeroCupomDesconto(String numeroCupomDesconto) {
        this.numeroCupomDesconto = numeroCupomDesconto;
    }

    public String getParamCupom() {
        return paramCupom;
    }

    public void setParamCupom(String paramCupom) {
        this.paramCupom = paramCupom;
    }

    public List<SelectItem> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<SelectItem> produtos) {
        this.produtos = produtos;
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getQtdProduto() {
        if (qtdProduto == null) {
            qtdProduto = 1;
        }
        return qtdProduto;
    }

    public void setQtdProduto(Integer qtdProduto) {
        this.qtdProduto = qtdProduto;
    }

    public void atualizarLinkProdutoQtd() {
        try {
            limparMsg();
            setMsgAlert("");
            if (UteisValidacao.emptyNumber(getQtdProduto())) {
                throw new Exception("A quantidade não pode ser 0");
            }
            montarSucessoGrowl("O link para direcionamento ao produto foi atualizado.");
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
        setMsgAlert(getMensagemNotificar());
    }

    public List<SelectItem> getListaTipoLinkPlano() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(1, "Link com todos os planos"));
        lista.add(new SelectItem(2, "Link para um plano específico"));
        lista.add(new SelectItem(3, "Link para planos de uma determinada categoria"));
        return lista;
    }


    public List<SelectItem> getListaFreepass() throws Exception {

        ArrayList<ProdutoVO> listProduto =  (ArrayList<ProdutoVO>) getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo("", "FR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, ""));
        for (ProdutoVO produto : listProduto) {
            lista.add(new SelectItem(produto.getCodigo(), produto.getDescricao()));
        }
        return lista;
    }

    public List<SelectItem> getListaTipoLinkAgenda() throws Exception {

        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(1, "Permite agendar aula"));
        lista.add(new SelectItem(2, "Não permite agendar aula"));
        return lista;
    }

    public List<SelectItem> getListaTipoLinkProduto() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(1, "Link com todos os produtos"));
        lista.add(new SelectItem(2, "Link para um produto específico"));
        lista.add(new SelectItem(3, "Link para produtos de uma categoria específica"));
        return lista;
    }

    public Integer getTipoLinkPlano() {
        if (tipoLinkPlano == null) {
            tipoLinkPlano = 1;
        }
        return tipoLinkPlano;
    }

    public void setTipoLinkPlano(Integer tipoLinkPlano) {
        this.tipoLinkPlano = tipoLinkPlano;
    }

    public Integer getTipoLinkProduto() {
        if (tipoLinkProduto == null) {
            tipoLinkProduto = 1;
        }
        return tipoLinkProduto;
    }

    public void setTipoLinkProduto(Integer tipoLinkProduto) {
        this.tipoLinkProduto = tipoLinkProduto;
    }

    public void abrirConfiguracoes() {
        this.menuSelecionado = "";
        abrirBasico();
        setConfiguracoes(true);
        carregarListaVendasOnlineConvenio(null);
        carregarListaVendasOnlineConvenioTentativa();
        try {
            validarApresentacaoTipoParcelamentoStone();
        } catch (Exception ignored) {
        }
    }

    public void abrirConfiguracoesMultiEmpresa() {
        fecharAbas();
        setConfiguracoes(false);
        setMultiEmpresaConfig(true);
    }

    public void sairConfiguracoes() {
        setConfiguracoes(false);
    }

    public void carregarListaVendasOnlineConvenio(EmpresaVO empresaVO) {
        try {
            limparMsg();
            montarConvenios();
            setVendasOnlineConvenioVO(new VendasOnlineConvenioVO());
            setVendasOnlineFormPagVO(new VendasOnlineConvenioVO());
            setListaVendasOnlineConvenioEmpresa(new ArrayList<>());
            setListaVendasOnlineFormaPagamentoEmpresa(new ArrayList<>());
            if (empresaVO == null) {
             empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(config.getEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            setListaVendasOnlineConvenioEmpresa(getFacade().getVendasOnlineConvenio().consultarPorEmpresa(empresaVO.getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, false));
            setListaVendasOnlineFormaPagamentoEmpresa(getFacade().getVendasOnlineConvenio().consultarPorEmpresa(empresaVO.getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, true));
            getVendasOnlineConvenioVO().setEmpresaVO(empresaVO);
            getVendasOnlineFormPagVO().setEmpresaVO(empresaVO);
            carregarPlanos(getVendasOnlineConvenioVO().getEmpresaVO().getCodigo());
            carregarProdutos(getVendasOnlineConvenioVO().getEmpresaVO().getCodigo());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void carregarListaVendasOnlineConvenioTentativa() {
        try {
            limparMsg();
            montarConvenios();
            setVendasOnlineConvenioTentativaVO(new VendasOnlineConvenioTentativaVO());
            setListaVendasOnlineConvenioTentativa(new ArrayList<>());
            List<VendasOnlineConvenioTentativaVO> listaAtual = getFacade().getVendasOnlineConvenioTentativa().consultarPorEmpresa(config.getEmpresa(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            for (VendasOnlineConvenioTentativaVO obj : listaAtual) {
                obj.setObjetoVOAntesAlteracao(obj.getClone(true));
            }
            setListaVendasOnlineConvenioTentativa(listaAtual);
            verificarUtilizarRetentativaConvenio();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void selecionouPlano() {
        try {
            limparMsg();
            setOnComplete("");
            getVendasOnlineConvenioVO().setProdutoVO(null);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void selecionouPlanoFormpag() {
        try {
            limparMsg();
            setOnComplete("");
            getVendasOnlineFormPagVO().setProdutoVO(null);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void selecionouProduto() {
        try {
            limparMsg();
            setOnComplete("");
            getVendasOnlineConvenioVO().setPlanoVO(null);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void selecionouProdutoFormPag() {
        try {
            limparMsg();
            setOnComplete("");
            getVendasOnlineFormPagVO().setPlanoVO(null);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void carregarPlanos(Integer empresa) throws Exception {
        setListaPlanoVO(new ArrayList<>());
        if (!UteisValidacao.emptyNumber(empresa)) {
            setListaPlanoVO(getFacade().getPlano().consultarVigentesTodasEmpresas(Uteis.NIVELMONTARDADOS_DADOSBASICOS, true));
        } else {
            setListaPlanoVO(getFacade().getPlano().consultarVigentes(empresa, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, true, true, false));
        }
    }

    private void carregarProdutos(Integer empresa) throws Exception {
        setListaProdutoVO(new ArrayList<>());
        setListaProdutoVO(getFacade().getProduto().consultarParaVendasOnline(0, 0, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    public List<SelectItem> getListaSelectItemPlano() {
        List<SelectItem> lista = new ArrayList<>();
        for (PlanoVO planoVO : getListaPlanoVO()) {
            boolean existe = false;
            for (VendasOnlineConvenioVO obj : getListaVendasOnlineConvenioEmpresa()) {
                if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo()) && planoVO.getCodigo().equals(obj.getPlanoVO().getCodigo())) {
                    existe = true;
                    break;
                }
            }
            if (!existe) {
                lista.add(new SelectItem(planoVO.getCodigo(), planoVO.getCodigo()+" - "+planoVO.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemPlanoFormPag() {
        List<SelectItem> lista = new ArrayList<>();
        for (PlanoVO planoVO : getListaPlanoVO()) {
            lista.add(new SelectItem(planoVO.getCodigo(), planoVO.getCodigo()+" - "+planoVO.getDescricao()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    private List<ModalidadeCarrouselVendasOnlineVO> listaModalidadeInclusao;

    public List<ModalidadeCarrouselVendasOnlineVO> getListaModalidadeCarrouselVendasOnline() throws Exception {
        List<ModalidadeCarrouselVendasOnlineVO> list = getFacade().getVendasOnline().consultarModalidadesCarrouselVendasOnline(config.getCodigo(), isAbaModaBanner());
        if(!list.isEmpty()){
            Ordenacao.ordenarLista(list, "codigo");
        }
        return list;
    }

    public List<PlanosSiteVendasOnlineVO> getListaPlanosSiteVendasOnline() throws Exception {
        List<PlanosSiteVendasOnlineVO> list = getFacade().getVendasOnline().consultarPlanosSiteVendasOnline(config.getCodigo());
        if(!list.isEmpty()){
            Ordenacao.ordenarLista(list, "codigo");
        }
        return list;
    }

    public List<SelectItem> getListaSelectItemProduto() {
        List<SelectItem> lista = new ArrayList<>();
        for (ProdutoVO produtoVO : getListaProdutoVO()) {
            boolean existe = false;
            for (VendasOnlineConvenioVO obj : getListaVendasOnlineConvenioEmpresa()) {
                if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo()) && produtoVO.getCodigo().equals(obj.getProdutoVO().getCodigo())) {
                    existe = true;
                    break;
                }
            }
            if (!existe) {
                lista.add(new SelectItem(produtoVO.getCodigo(), produtoVO.getCodigo()+" - "+produtoVO.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getListaSelectItemProdutoFormPag() {
        List<SelectItem> lista = new ArrayList<>();
        for (ProdutoVO produtoVO : getListaProdutoVO()) {
            lista.add(new SelectItem(produtoVO.getCodigo(), produtoVO.getCodigo()+" - "+produtoVO.getDescricao()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public void adicionarVendasOnlineConvenio() {
        adicionarVendasOnline(false);
    }

    public void adicionarVendasOnlineFormaPagamento() {
        adicionarVendasOnline(true);
    }

    public void adicionarVendasOnline(boolean addFormaPagamento) {
        try {
            limparMsg();
            setOnComplete("");

            if (!addFormaPagamento && UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getEmpresaVO().getCodigo())) {
                throw new Exception("Empresa não informada.");
            }

            if (addFormaPagamento && UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getEmpresaVO().getCodigo())) {
                throw new Exception("Empresa não informada.");
            }

            if (!addFormaPagamento && UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione um convênio de cobrança.");
            }

            if (addFormaPagamento && UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getFormaPagamento())) {
                throw new Exception("Selecione uma forma de pagamento.");
            }

            if (!addFormaPagamento && UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getProdutoVO().getCodigo()) &&
                    UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getPlanoVO().getCodigo())) {
                throw new Exception("Selecione um plano ou produto.");
            }

            if (addFormaPagamento && UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getProdutoVO().getCodigo()) &&
                    UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getPlanoVO().getCodigo())) {
                throw new Exception("Selecione um plano ou produto.");
            }

            if (!addFormaPagamento && !UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getProdutoVO().getCodigo()) &&
                    !UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getPlanoVO().getCodigo())) {
                throw new Exception("Selecione somente um item (Plano ou Produto).");
            }

            if (addFormaPagamento && !UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getProdutoVO().getCodigo()) &&
                    !UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getPlanoVO().getCodigo())) {
                throw new Exception("Selecione somente um item (Plano ou Produto).");
            }

            getVendasOnlineConvenioVO().setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getVendasOnlineConvenioVO().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            getVendasOnlineFormPagVO().setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getVendasOnlineFormPagVO().getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            if (addFormaPagamento) {
                getVendasOnlineFormPagVO().setConvenioCobrancaVO(null);
            } else {
                getVendasOnlineConvenioVO().setFormaPagamento(null);
                getVendasOnlineConvenioVO().setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorChavePrimaria(getVendasOnlineConvenioVO().getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            //caso não seja Stone então limpar a configuração de parcelamento
            if (!getVendasOnlineConvenioVO().getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                getVendasOnlineConvenioVO().setTipoParcelamentoStone("");
            }

            if (!addFormaPagamento && !UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getProdutoVO().getCodigo())) {
                getVendasOnlineConvenioVO().setProdutoVO(getFacade().getProduto().consultarPorChavePrimaria(getVendasOnlineConvenioVO().getProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            if (!addFormaPagamento && !UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getPlanoVO().getCodigo())) {
                getVendasOnlineConvenioVO().setPlanoVO(getFacade().getPlano().consultarPorChavePrimaria(getVendasOnlineConvenioVO().getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }

            if (addFormaPagamento && !UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getProdutoVO().getCodigo())) {
                getVendasOnlineFormPagVO().setProdutoVO(getFacade().getProduto().consultarPorChavePrimaria(getVendasOnlineFormPagVO().getProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            if (addFormaPagamento && !UteisValidacao.emptyNumber(getVendasOnlineFormPagVO().getPlanoVO().getCodigo())) {
                getVendasOnlineFormPagVO().setPlanoVO(getFacade().getPlano().consultarPorChavePrimaria(getVendasOnlineFormPagVO().getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }

            if(addFormaPagamento){
                getListaVendasOnlineFormaPagamentoEmpresa().add(getVendasOnlineFormPagVO());
            }else{
                getListaVendasOnlineConvenioEmpresa().add(getVendasOnlineConvenioVO());
            }

            ConvenioCobrancaVO convenioCobrancaVO = (ConvenioCobrancaVO) getVendasOnlineConvenioVO().getConvenioCobrancaVO().getClone(true);
            novoVendasOnlineConvenio(convenioCobrancaVO);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void novoVendasOnlineConvenio(ConvenioCobrancaVO convenioCobrancaVO) {
        try {
            setVendasOnlineConvenioVO(new VendasOnlineConvenioVO());
            setVendasOnlineFormPagVO(new VendasOnlineConvenioVO());
            getVendasOnlineConvenioVO().setEmpresaVO(getEmpresaVO());
            getVendasOnlineFormPagVO().setEmpresaVO(getEmpresaVO());
            getVendasOnlineConvenioVO().setConvenioCobrancaVO(convenioCobrancaVO);
            carregarPlanos(getVendasOnlineConvenioVO().getEmpresaVO().getCodigo());
            carregarProdutos(getVendasOnlineConvenioVO().getEmpresaVO().getCodigo());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirConvenioCobrancaPlano() {
        try {
            limparMsg();
            setOnComplete("");

            VendasOnlineConvenioVO excluir = (VendasOnlineConvenioVO) request().getAttribute("itemPlano");
            if (excluir == null) {
                throw new Exception("Erro ao obter item");
            }

            List<VendasOnlineConvenioVO> lista = new ArrayList<>();
            for (VendasOnlineConvenioVO rateioVO : getListaVendasOnlineConvenioEmpresa()) {
                if (rateioVO.hashCode() != excluir.hashCode()) {
                    lista.add(rateioVO);
                }
            }
            setListaVendasOnlineConvenioEmpresa(lista);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirFormaPagamentoPlano() {
        try {
            limparMsg();
            setOnComplete("");

            VendasOnlineConvenioVO excluir = (VendasOnlineConvenioVO) request().getAttribute("itemPlano2");
            if (excluir == null) {
                throw new Exception("Erro ao obter item");
            }

            List<VendasOnlineConvenioVO> lista = new ArrayList<>();
            for (VendasOnlineConvenioVO rateioVO : getListaVendasOnlineFormaPagamentoEmpresa()) {
                if (rateioVO.hashCode() != excluir.hashCode()) {
                    lista.add(rateioVO);
                }
            }
            setListaVendasOnlineFormaPagamentoEmpresa(lista);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirConvenioCobrancaProduto() {
        try {
            limparMsg();
            setOnComplete("");

            VendasOnlineConvenioVO excluir = (VendasOnlineConvenioVO) request().getAttribute("itemProduto");
            if (excluir == null) {
                throw new Exception("Erro ao obter item");
            }

            List<VendasOnlineConvenioVO> lista = new ArrayList<>();
            for (VendasOnlineConvenioVO rateioVO : getListaVendasOnlineConvenioEmpresa()) {
                if (rateioVO.hashCode() != excluir.hashCode()) {
                    lista.add(rateioVO);
                }
            }
            setListaVendasOnlineConvenioEmpresa(lista);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirFormaPagamentoProduto() {
        try {
            limparMsg();
            setOnComplete("");

            VendasOnlineConvenioVO excluir = (VendasOnlineConvenioVO) request().getAttribute("itemProduto2");
            if (excluir == null) {
                throw new Exception("Erro ao obter item");
            }

            List<VendasOnlineConvenioVO> lista = new ArrayList<>();
            for (VendasOnlineConvenioVO rateioVO : getListaVendasOnlineFormaPagamentoEmpresa()) {
                if (rateioVO.hashCode() != excluir.hashCode()) {
                    lista.add(rateioVO);
                }
            }
            setListaVendasOnlineFormaPagamentoEmpresa(lista);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isAbaBasico() {
        return abaBasico;
    }

    public void setAbaBasico(boolean abaBasico) {
        this.abaBasico = abaBasico;
    }

    public boolean isAbaIntegracoes() {
        return abaIntegracoes;
    }

    public void setAbaIntegracoes(boolean abaIntegracoes) {
        this.abaIntegracoes = abaIntegracoes;
    }

    public boolean isAbaAvancado() {
        return abaAvancado;
    }

    public void setAbaAvancado(boolean abaAvancado) {
        this.abaAvancado = abaAvancado;
    }

    public boolean isAbaConvenioCobranca() {
        return abaConvenioCobranca;
    }

    public void setAbaConvenioCobranca(boolean abaConvenioCobranca) {
        this.abaConvenioCobranca = abaConvenioCobranca;
    }

    public boolean isAbaFormaPagamento() {
        return abaFormaPagamento;
    }

    public void setAbaFormaPagamento(boolean abaFormaPagamento) {
        this.abaFormaPagamento = abaFormaPagamento;
    }

    private void fecharAbas() {
        setAbaBasico(false);
        setAbaAvancado(false);
        setAbaConvenioCobranca(false);
        setAbaFormaPagamento(false);
        setAbaIntegracoes(false);
        setAbaCores(false);
        setAbaMenu(false);
        setAbaPagInicial(false);
        setAbaFotoFachada(false);
        setAbaModaCarrosel(false);
        setAbaModaBanner(false);
        setAbaPlanos(false);
        setAbaCapLeads(false);
        setAbaAgenda(false);
        setAbaContato(false);
        setAbaIntegracoesHotsite(false);
        setAbaPublicar(false);
        setExibirBtnPublicar(false);
        setMsgPublicado(Boolean.FALSE);
        listaModalidadeInclusao = new ArrayList<>();
        setModalidadeCarrouselVendasOnlineVO(new ModalidadeCarrouselVendasOnlineVO());
        setModalidadeCarrouselVendasOnlineVO2(new ModalidadeCarrouselVendasOnlineVO());
        setModalidadeCarrouselVendasOnlineVO3(new ModalidadeCarrouselVendasOnlineVO());
        setMultiEmpresaConfig(false);
        setAbaCamposAdicionais(false);
    }

    public void abrirBasico() {
        fecharAbas();
        setAbaBasico(true);
    }

    public void abrirAvancado() {
        fecharAbas();
        setAbaAvancado(true);
    }

    public void abrirConvenioCobranca() {
        fecharAbas();
        setAbaConvenioCobranca(true);
    }

    public void abrirFormaPagamento() {
        fecharAbas();
        setAbaFormaPagamento(true);
    }

    public void abrirIntegracoes() {
        fecharAbas();
        setAbaIntegracoes(true);
    }

    public void abrirCores() {
        fecharAbas();
        setAbaCores(true);
    }

    public void abrirMenu() {
        fecharAbas();
        setAbaMenu(true);
    }

    public void abrirPagInicial() {
        fecharAbas();
        setAbaPagInicial(true);
    }
    public void abrirFotoFachada() {
        fecharAbas();
        setAbaFotoFachada(true);
    }

    public void abrirModaCarrosel() {
        fecharAbas();
        setAbaModaCarrosel(true);
    }

    public void abrirModaBanner() {
        fecharAbas();
        setAbaModaBanner(true);
    }

    public void abrirPlanos() {
        fecharAbas();
        setAbaPlanos(true);
    }

    public void abrirCapLeads() {
        fecharAbas();
        setAbaCapLeads(true);
    }

    public void abrirAgenda() {
        fecharAbas();
        setAbaAgenda(true);
    }

    public void abrirContato() {
        fecharAbas();
        setAbaContato(true);
    }

    public void abrirIntegracoesHotsite() throws Exception {
        fecharAbas();
        setAbaIntegracoesHotsite(true);
    }

    public void abrirPublicar() throws Exception {
        try {
            setMsgPublicado(false);
            setMsgAlert("");
            limparMsg();
            setErroConsultarStatus(false);
            setPublicado(false);
            fecharAbas();
            setAbaPublicar(true);
            setDominioProprioHotsite(getFacade().getVendasOnline().consultarDominioProprioHotsite(getConfig().getEmpresa()));
            if(getFacade().getVendasOnline().consultarEmpresasHotsite(empresaVO.getCodigo()).size() > 0){
                boolean empativa = getFacade().getVendasOnline().consultarEmpresasHotsite(empresaVO.getCodigo()).get(0).isAtivo() ;
                this.setHabilitarempresahotsite(empativa);
            }
            try {
                getUrlByChaveAPI();
            } catch (Exception e) {
                setErroConsultarStatus(true);
                setPublicado(false);
                montarErro("Erro ao consultar o endereço do site!");
            }
            getDominioHotsite();
            getProtocoloHttps();
            if (isPublicado()) {
                montarSucessoGrowl("Configurações consultadas com sucesso!");
            }
        } catch (Exception e) {
            throw e;
        }
    }
    public void abrirCamposAdicionais() {
        fecharAbas();
        setAbaCamposAdicionais(true);
    }

    public List<ProdutoVO> getListaProdutoVO() {
        if (listaProdutoVO == null) {
            listaProdutoVO = new ArrayList<>();
        }
        return listaProdutoVO;
    }

    public void setListaProdutoVO(List<ProdutoVO> listaProdutoVO) {
        this.listaProdutoVO = listaProdutoVO;
    }

    public List<PlanoVO> getListaPlanoVO() {
        if (listaPlanoVO == null) {
            listaPlanoVO = new ArrayList<>();
        }
        return listaPlanoVO;
    }

    public void setListaPlanoVO(List<PlanoVO> listaPlanoVO) {
        this.listaPlanoVO = listaPlanoVO;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<VendasOnlineConvenioVO> getListaConfigProdutos() {
        List<VendasOnlineConvenioVO> lista = new ArrayList<>();
        for (VendasOnlineConvenioVO obj : getListaVendasOnlineConvenioEmpresa()) {
            if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo()) && UteisValidacao.emptyNumber(obj.getFormaPagamento())) {
                lista.add(obj);
            }
        }
        return lista;
    }

    public List<VendasOnlineConvenioVO> getListaConfigProdutosFormaPag() {
        List<VendasOnlineConvenioVO> lista = new ArrayList<>();
        for (VendasOnlineConvenioVO obj : getListaVendasOnlineFormaPagamentoEmpresa()) {
            if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo()) && !UteisValidacao.emptyNumber(obj.getFormaPagamento())) {
                lista.add(obj);
            }
        }
        return lista;
    }

    public List<VendasOnlineConvenioVO> getListaConfigPlanos() {
        List<VendasOnlineConvenioVO> lista = new ArrayList<>();
        for (VendasOnlineConvenioVO obj : getListaVendasOnlineConvenioEmpresa()) {
            if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo()) && UteisValidacao.emptyNumber(obj.getFormaPagamento())) {
                lista.add(obj);
            }
        }
        return lista;
    }

    public List<VendasOnlineConvenioVO> getListaConfigPlanosFormaPag() {
        List<VendasOnlineConvenioVO> lista = new ArrayList<>();
        for (VendasOnlineConvenioVO obj : getListaVendasOnlineFormaPagamentoEmpresa()) {
            if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo()) && !UteisValidacao.emptyNumber(obj.getFormaPagamento())) {
                lista.add(obj);
            }
        }
        return lista;
    }

    private ConvenioCobrancaVO obterConvenioCobrancaVO(Integer codigo) throws Exception {
        for (ConvenioCobrancaVO conv : getListaConvenioCobrancaVO()) {
            if (conv.getCodigo().equals(codigo)) {
                return conv;
            }
        }
        throw new Exception("Não foi possível obter o convênio de cobranca com código " + codigo);
    }

    public List<ConvenioCobrancaVO> getListaConvenioCobrancaVO() {
        return listaConvenioCobrancaVO;
    }

    public void setListaConvenioCobrancaVO(List<ConvenioCobrancaVO> listaConvenioCobrancaVO) {
        this.listaConvenioCobrancaVO = listaConvenioCobrancaVO;
    }

    public VendasOnlineConvenioVO getVendasOnlineConvenioVO() {
        if (vendasOnlineConvenioVO == null) {
            vendasOnlineConvenioVO = new VendasOnlineConvenioVO();
        }
        return vendasOnlineConvenioVO;
    }

    public void setVendasOnlineConvenioVO(VendasOnlineConvenioVO vendasOnlineConvenioVO) {
        this.vendasOnlineConvenioVO = vendasOnlineConvenioVO;
    }

    public List<VendasOnlineConvenioVO> getListaVendasOnlineConvenioEmpresa() {
        if (listaVendasOnlineConvenioEmpresa == null) {
            listaVendasOnlineConvenioEmpresa = new ArrayList<>();
        }
        return listaVendasOnlineConvenioEmpresa;
    }

    public void setListaVendasOnlineConvenioEmpresa(List<VendasOnlineConvenioVO> listaVendasOnlineConvenioEmpresa) {
        this.listaVendasOnlineConvenioEmpresa = listaVendasOnlineConvenioEmpresa;
    }

    public boolean isConfiguracoes() {
        return configuracoes;
    }

    public void setConfiguracoes(boolean configuracoes) {
        this.configuracoes = configuracoes;
    }

    public List<VendasOnlineConvenioTentativaVO> getListaVendasOnlineConvenioTentativa() {
        if (listaVendasOnlineConvenioTentativa == null) {
            listaVendasOnlineConvenioTentativa = new ArrayList<>();
        }
        return listaVendasOnlineConvenioTentativa;
    }

    public void setListaVendasOnlineConvenioTentativa(List<VendasOnlineConvenioTentativaVO> listaVendasOnlineConvenioTentativa) {
        this.listaVendasOnlineConvenioTentativa = listaVendasOnlineConvenioTentativa;
    }

    public VendasOnlineConvenioTentativaVO getVendasOnlineConvenioTentativaVO() {
        if (vendasOnlineConvenioTentativaVO == null) {
            vendasOnlineConvenioTentativaVO = new VendasOnlineConvenioTentativaVO();
        }
        return vendasOnlineConvenioTentativaVO;
    }

    public void setVendasOnlineConvenioTentativaVO(VendasOnlineConvenioTentativaVO vendasOnlineConvenioTentativaVO) {
        this.vendasOnlineConvenioTentativaVO = vendasOnlineConvenioTentativaVO;
    }

    private ConvenioCobrancaVO obterConvenioCobranca(Integer codigo) {
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {
            if (obj.getCodigo().equals(codigo)) {
                return obj;
            }
        }
        return null;
    }

    public List<SelectItem> getListaSelecItemConvenioTentativa() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaVO()) {

            boolean jaAdicionado = false;

            if (isUtilizarRetentativaConvenio()) {
                for (VendasOnlineConvenioTentativaVO tentaVO : getListaVendasOnlineConvenioTentativa()) {
                    if (tentaVO.getConvenioCobrancaVO().getCodigo().equals(obj.getCodigo())) {
                        jaAdicionado = true;
                        break;
                    }
                }
            }

            if (!jaAdicionado) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public void adicionarConvenio() {
        try {
            limparMsg();


            if (UteisValidacao.emptyNumber(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo())) {
                throw new Exception("Selecione um convênio de cobrança.");
            }

            ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobrancaVO(getVendasOnlineConvenioTentativaVO().getConvenioCobrancaVO().getCodigo());
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                if (UteisValidacao.emptyString(getVendasOnlineConvenioTentativaVO().getTipoParcelamentoStone())) {
                    throw new Exception("O campo \"Tipo de parcelamento\" não pode ficar vazio.");
                }
            }

            getVendasOnlineConvenioTentativaVO().setConvenioCobrancaVO(convenioCobrancaVO);
            getVendasOnlineConvenioTentativaVO().getEmpresaVO().setCodigo(config.getEmpresa());
            getVendasOnlineConvenioTentativaVO().setOrdem(getListaVendasOnlineConvenioTentativa().size() + 1);
            getListaVendasOnlineConvenioTentativa().add(getVendasOnlineConvenioTentativaVO());
            ordenarListaVendasOnlineConvenioTentativa();
            setVendasOnlineConvenioTentativaVO(new VendasOnlineConvenioTentativaVO());
            validarApresentacaoTipoParcelamentoStone();
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void adicionarAulaLinkVisitante() {
        try {
            limparMsg();

            if (UteisValidacao.emptyNumber(getAgendaLinkVisitanteVendasOnlineVO().getAulaLinkVisitanteEscolhida())) {
                throw new Exception("Selecione uma aula.");
            }

            for (TurmaEHorarioVendasOnlineVO turma : getAgendaLinkVisitanteVendasOnlineVO().getListTurmaHorarioLinkVisitante()) {
                if (Objects.equals(getAgendaLinkVisitanteVendasOnlineVO().getAulaLinkVisitanteEscolhida(), turma.getCodigoTurma())) {
                    AulasVendasOnline aula = new AulasVendasOnline();
                    aula.setCodigoTurma(turma.getCodigoTurma());
                    aula.setTurma(turma.getCodigoTurma() + " - " + turma.getDescricao());
                    aula.setModalidade(turma.getCodigoModalidade() + " - " + turma.getNomeModalidade());
                    aula.setCodigoModalidade(turma.getCodigoModalidade());
                    boolean podeAdicionar = true;
                    for(AulasVendasOnline a : getAgendaLinkVisitanteVendasOnlineVO().getAulasVendasOnlineLinkVisitante()){
                        if(a.getCodigoTurma().equals(turma.getCodigoTurma()) && a.getCodigoModalidade().equals(turma.getCodigoModalidade())){
                            podeAdicionar = false;
                        }
                    }
                    if (podeAdicionar) {
                        getAgendaLinkVisitanteVendasOnlineVO().getAulasVendasOnlineLinkVisitante().add(aula);
                        getAgendaLinkVisitanteVendasOnlineVO().setAulaLinkVisitanteEscolhida(0);

                        //depois de adicionar a aula na lista, remover da combo pra não deixar selecionar de novo
                        removerAulaParaNaoPermitirSelecao(turma);
                        setSucesso(true);
                        setErro(false);
                        montarSucessoGrowl("Aula adicionada com sucesso!");
                    } else {
                        throw new Exception("Esta aula já foi adicionada.");
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void ordenarListaVendasOnlineConvenioTentativa() {
        int posicao = 1;
        Ordenacao.ordenarLista(getListaVendasOnlineConvenioTentativa(), "ordem");
        for (VendasOnlineConvenioTentativaVO obj : getListaVendasOnlineConvenioTentativa()) {
            obj.setOrdem(posicao++);
        }
        Ordenacao.ordenarLista(getListaVendasOnlineConvenioTentativa(), "ordem");
    }

    public void removerConvenio() {
        try {
            limparMsg();

            List<VendasOnlineConvenioTentativaVO> novaLista = new ArrayList<>();
            VendasOnlineConvenioTentativaVO obj = (VendasOnlineConvenioTentativaVO) context().getExternalContext().getRequestMap().get("conv");
            for (VendasOnlineConvenioTentativaVO fase : getListaVendasOnlineConvenioTentativa()) {
                if (!fase.getOrdem().equals(obj.getOrdem())) {
                    novaLista.add(fase);
                }
            }
            setListaVendasOnlineConvenioTentativa(novaLista);
            ordenarListaVendasOnlineConvenioTentativa();
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void moverParaCimaConvenio() {
        try {
            limparMsg();

            VendasOnlineConvenioTentativaVO obj = (VendasOnlineConvenioTentativaVO) context().getExternalContext().getRequestMap().get("conv");
            for (VendasOnlineConvenioTentativaVO fase : getListaVendasOnlineConvenioTentativa()) {
                if (obj.getOrdem() == fase.getOrdem() + 1) {
                    fase.setOrdem(fase.getOrdem() + 1);
                    obj.setOrdem(obj.getOrdem() - 1);
                    break;
                }
            }
            setListaVendasOnlineConvenioTentativa(Ordenacao.ordenarLista(getListaVendasOnlineConvenioTentativa(), "ordem"));
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void moverParaBaixoConvenio() {
        try {
            limparMsg();

            VendasOnlineConvenioTentativaVO obj = (VendasOnlineConvenioTentativaVO) context().getExternalContext().getRequestMap().get("conv");
            for (VendasOnlineConvenioTentativaVO fase : getListaVendasOnlineConvenioTentativa()) {
                if (fase.getOrdem() == obj.getOrdem() + 1) {
                    fase.setOrdem(fase.getOrdem() - 1);
                    obj.setOrdem(obj.getOrdem() + 1);
                    break;
                }
            }
            setListaVendasOnlineConvenioTentativa(Ordenacao.ordenarLista(getListaVendasOnlineConvenioTentativa(), "ordem"));

        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void verificarUtilizarRetentativaConvenio() {
        if (getListaVendasOnlineConvenioTentativa().size() > 1) {
            setUtilizarRetentativaConvenio(true);
        } else {
            if (!UteisValidacao.emptyList(getListaVendasOnlineConvenioTentativa())) {
                setVendasOnlineConvenioTentativaVO(getListaVendasOnlineConvenioTentativa().get(0));
            }
            setUtilizarRetentativaConvenio(false);
        }
    }

    public void acaoUtilizarRetentativaConvenio() throws Exception {
        if (!isUtilizarRetentativaConvenio()) {
            validarApresentacaoTipoParcelamentoStone();
            setListaVendasOnlineConvenioTentativa(new ArrayList<>());
        } else {
            setVendasOnlineConvenioTentativaVO(new VendasOnlineConvenioTentativaVO());
        }
    }

    public boolean isUtilizarRetentativaConvenio() {
        return utilizarRetentativaConvenio;
    }

    public void setUtilizarRetentativaConvenio(boolean utilizarRetentativaConvenio) {
        this.utilizarRetentativaConvenio = utilizarRetentativaConvenio;
    }

    public boolean isApresentacaoTipoParcelamentoStoneVendasOnlineConvenio() {
        try {
            if (!UteisValidacao.emptyNumber(getVendasOnlineConvenioVO().getConvenioCobrancaVO().getCodigo())) {
                for (ConvenioCobrancaVO convenioCobrancaVO : getListaConvenioCobrancaVO()) {
                    if (convenioCobrancaVO.getCodigo().equals(getVendasOnlineConvenioVO().getConvenioCobrancaVO().getCodigo())) {
                        if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                            return true;
                        } else {
                            getVendasOnlineConvenioVO().setTipoParcelamentoStone("");
                        }
                    }
                }
            }
            return false;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void selecionarGestao(){
        this.menuSelecionado = "GESTAO";
        this.configuracoes = false;
    }

    public void selecionarLinks(){
        this.menuSelecionado = "LINKS";
        this.configuracoes = false;
    }

    public void selecionarLinksCampanha(){
        this.menuSelecionado = "LINKS_CAMPANHA";
        this.configuracoes = false;
    }


    public void selecionarSite(){
        this.menuSelecionado = "SITE";
        this.configuracoes = false;
        fecharAbas();
        setAbaCores(true);
    }
    public void ativaOuInativaEmpresaHotsite() throws Exception {
        getFacade().getVendasOnline().incluirAtualiza(getEmpresaVO().getCodigo());
        if(getFacade().getVendasOnline().consultarEmpresasHotsite(getEmpresaVO().getCodigo()).size() > 0) {
            boolean empativa = getFacade().getVendasOnline().consultarEmpresasHotsite(getEmpresaVO().getCodigo()).get(0).isAtivo();
            this.setHabilitarempresahotsite(empativa);
            if (this.isHabilitarempresahotsite()) {
                montarSucessoGrowl("Empresa habilitada com sucesso!");
            } else {
                montarSucessoGrowl("Empresa desabilitada com sucesso!");
            }
        }
    }
    public void temaClaro() {
        if(getConfig() != null) {
            getConfig().setTemaEscuro(false);
        }
    }

    public void temaEscuro() {
        if(getConfig() != null) {
            getConfig().setTemaClaro(false);
        }
    }

    public boolean getSiteSelecionado(){
        return "SITE".equals(this.menuSelecionado);
    }

    public boolean getGestaoSelecionado(){
        return "GESTAO".equals(this.menuSelecionado);
    }

    public boolean getLinksSelecionado(){
        return "LINKS".equals(this.menuSelecionado);
    }

    public boolean getLinksCampanhaSelecionado(){
        return "LINKS_CAMPANHA".equals(this.menuSelecionado);
    }


    public boolean isAbaCores() {
        return abaCores;
    }

    public void setAbaCores(boolean abaCores) {
        this.abaCores = abaCores;
    }

    public boolean isAbaMenu() {
        return abaMenu;
    }

    public void setAbaMenu(boolean abaMenu) {
        this.abaMenu = abaMenu;
    }

    public boolean isAbaPagInicial() {
        return abaPagInicial;
    }

    public void setAbaPagInicial(boolean abaPagInicial) {
        this.abaPagInicial = abaPagInicial;
    }

    public boolean isAbaModaCarrosel() {
        return abaModaCarrosel;
    }

    public void setAbaModaCarrosel(boolean abaModaCarrosel) {
        this.abaModaCarrosel = abaModaCarrosel;
    }

    public boolean isAbaModaBanner() {
        return abaModaBanner;
    }

    public void setAbaFotoFachada(boolean abaFotoFachada) {
        this.abaFotoFachada = abaFotoFachada;
    }

    public boolean isAbaFotoFachada() {
        return abaFotoFachada;
    }

    public void setAbaModaBanner(boolean abaModaBanner) {
        this.abaModaBanner = abaModaBanner;
    }

    public boolean isAbaPlanos() {
        return abaPlanos;
    }

    public void setAbaPlanos(boolean abaPlanos) {
        this.abaPlanos = abaPlanos;
    }

    public boolean isAbaCapLeads() {
        return abaCapLeads;
    }

    public void setAbaCapLeads(boolean abaCapLeads) {
        this.abaCapLeads = abaCapLeads;
    }

    public boolean isAbaAgenda() {
        return abaAgenda;
    }

    public void setAbaAgenda(boolean abaAgenda) {
        this.abaAgenda = abaAgenda;
    }

    public boolean isAbaContato() {
        return abaContato;
    }

    public void setAbaContato(boolean abaContato) {
        this.abaContato = abaContato;
    }

    public ConfigModalidadeCarrosselVendasOnlineVO getConfigModalidadeCarrosselVendasOnlineVO() {
        if(this.configModalidadeCarrosselVendasOnlineVO == null){
            this.configModalidadeCarrosselVendasOnlineVO = new ConfigModalidadeCarrosselVendasOnlineVO();
        }
        return configModalidadeCarrosselVendasOnlineVO;
    }

    public void setConfigModalidadeCarrosselVendasOnlineVO(ConfigModalidadeCarrosselVendasOnlineVO configModalidadeCarrosselVendasOnlineVO) {
        this.configModalidadeCarrosselVendasOnlineVO = configModalidadeCarrosselVendasOnlineVO;
    }

    public ModalidadeCarrouselVendasOnlineVO getModalidadeCarrouselVendasOnlineVO() {
        if(this.modalidadeCarrouselVendasOnlineVO == null){
            this.modalidadeCarrouselVendasOnlineVO = new ModalidadeCarrouselVendasOnlineVO();
        }
        return modalidadeCarrouselVendasOnlineVO;
    }

    public void setModalidadeCarrouselVendasOnlineVO(
            ModalidadeCarrouselVendasOnlineVO modalidadeCarrouselVendasOnlineVO) {
        this.modalidadeCarrouselVendasOnlineVO = modalidadeCarrouselVendasOnlineVO;
    }

    public String getFotoKeyExcluirModaCarrousel() {
        return fotoKeyExcluirModaCarrousel;
    }

    public void setFotoKeyExcluirModaCarrousel(String fotoKeyExcluirModaCarrousel) {
        this.fotoKeyExcluirModaCarrousel = fotoKeyExcluirModaCarrousel;
    }

    public PlanosSiteVendasOnlineVO getPlanosSiteVendasOnlineVO() {
        if(this.planosSiteVendasOnlineVO == null){
            this.planosSiteVendasOnlineVO = new PlanosSiteVendasOnlineVO();
        }
        return planosSiteVendasOnlineVO;
    }

    public void setPlanosSiteVendasOnlineVO(PlanosSiteVendasOnlineVO planosSiteVendasOnlineVO) {
        this.planosSiteVendasOnlineVO = planosSiteVendasOnlineVO;
    }

    public List<SelectItem> getPlanosSite() {
        return planosSite;
    }

    public void setPlanosSite(List<SelectItem> planosSite) {
        this.planosSite = planosSite;
    }

    public PaginaInicialVendasOnlineVO getPaginaInicialVendasOnlineVO() {
        if(this.paginaInicialVendasOnlineVO == null){
            this.paginaInicialVendasOnlineVO = new PaginaInicialVendasOnlineVO();
        }
        return paginaInicialVendasOnlineVO;
    }

    public void setPaginaInicialVendasOnlineVO(PaginaInicialVendasOnlineVO paginaInicialVendasOnlineVO) {
        this.paginaInicialVendasOnlineVO = paginaInicialVendasOnlineVO;
    }

    public PaginaInicialVendasOnlineVO getPaginaInicialVendasOnlineVO2() {
        if(this.paginaInicialVendasOnlineVO2 == null){
            this.paginaInicialVendasOnlineVO2 = new PaginaInicialVendasOnlineVO();
        }
        return paginaInicialVendasOnlineVO2;
    }

    public void setPaginaInicialVendasOnlineVO2(PaginaInicialVendasOnlineVO paginaInicialVendasOnlineVO2) {
        this.paginaInicialVendasOnlineVO2 = paginaInicialVendasOnlineVO2;
    }

    public PaginaInicialVendasOnlineVO getPaginaInicialVendasOnlineVO3() {
        if(this.paginaInicialVendasOnlineVO3 == null){
            this.paginaInicialVendasOnlineVO3 = new PaginaInicialVendasOnlineVO();
        }
        return paginaInicialVendasOnlineVO3;
    }

    public void setPaginaInicialVendasOnlineVO3(PaginaInicialVendasOnlineVO paginaInicialVendasOnlineVO3) {
        this.paginaInicialVendasOnlineVO3 = paginaInicialVendasOnlineVO3;
    }

    public String getPaginaInicialSelecionada() {
        return paginaInicialSelecionada;
    }
    public void setPaginaInicialSelecionada(String paginaInicialSelecionada) {
        this.paginaInicialSelecionada = paginaInicialSelecionada;
    }

    public String getFotoKeyExcluirPagInicialVendasOnline() {
        return fotoKeyExcluirPagInicialVendasOnline;
    }

    public void setFotoKeyExcluirPagInicialVendasOnline(String fotoKeyExcluirPagInicialVendasOnline) {
        this.fotoKeyExcluirPagInicialVendasOnline = fotoKeyExcluirPagInicialVendasOnline;
    }

    public void setFotoKeyExcluirPagInicialVendasOnline2(String fotoKeyExcluirPagInicialVendasOnline) {
        this.fotoKeyExcluirPagInicialVendasOnline2 = fotoKeyExcluirPagInicialVendasOnline2;
    }

    public void setFotoKeyExcluirPagInicialVendasOnline3(String fotoKeyExcluirPagInicialVendasOnline) {
        this.fotoKeyExcluirPagInicialVendasOnline3 = fotoKeyExcluirPagInicialVendasOnline2;
    }

    public String getFotoKeyExcluirCapLeadsVendasOnline() {
        return fotoKeyExcluirCapLeadsVendasOnline;
    }

    public void setFotoKeyExcluirCapLeadsVendasOnline(String fotoKeyExcluirCapLeadsVendasOnline) {
        this.fotoKeyExcluirCapLeadsVendasOnline = fotoKeyExcluirCapLeadsVendasOnline;
    }

    public MenuVendasOnlineVO getMenuVendasOnlineVO() {
        return menuVendasOnlineVO;
    }

    public void setMenuVendasOnlineVO(MenuVendasOnlineVO menuVendasOnlineVO) {
        this.menuVendasOnlineVO = menuVendasOnlineVO;
    }
    public FotoFachadaVendasOnlineVO getFotoFachadaVendasOnlineVO() {
        return fotoFachadaVendasOnlineVO;
    }
    public void setFotoFachadaVendasOnlineVO(FotoFachadaVendasOnlineVO fotoFachadaVendasOnlineVO) {
        this.fotoFachadaVendasOnlineVO = fotoFachadaVendasOnlineVO;
    }

    public void setFotoKeyExcluirFotoFachadaVendasOnline(String fotoKeyExcluirFotoFachadaVendasOnline) {
        this.fotoKeyExcluirFotoFachadaVendasOnline = fotoKeyExcluirFotoFachadaVendasOnline;
    }
    public String getFotoKeyExcluirFotoFachadaVendasOnline() {
        return fotoKeyExcluirFotoFachadaVendasOnline;
    }

    public String getFotoKeyExcluirMenuVendasOnline() {
        return fotoKeyExcluirMenuVendasOnline;
    }

    public void setFotoKeyExcluirMenuVendasOnline(String fotoKeyExcluirMenuVendasOnline) {
        this.fotoKeyExcluirMenuVendasOnline = fotoKeyExcluirMenuVendasOnline;
    }

    public String getFotoKeyExcluirMultiConfigsVendasOnline() {
        return fotoKeyExcluirMultiConfigsVendasOnline;
    }

    public void setFotoKeyExcluirMultiConfigsVendasOnline(String fotoKeyExcluirMultiConfigsVendasOnline) {
        this.fotoKeyExcluirMultiConfigsVendasOnline = fotoKeyExcluirMultiConfigsVendasOnline;
    }

    public CaptacaoLeadsVendasOnlineVO getCaptacaoLeadsVendasOnline() {
        if(this.captacaoLeadsVendasOnlineVO == null){
            this.captacaoLeadsVendasOnlineVO = new CaptacaoLeadsVendasOnlineVO();
        }
        return captacaoLeadsVendasOnlineVO;
    }

    public void setCaptacaoLeadsVendasOnline(CaptacaoLeadsVendasOnlineVO captacaoLeadsVendasOnlineVO) {
        this.captacaoLeadsVendasOnlineVO = captacaoLeadsVendasOnlineVO;
    }

    public ContatoRodapeVendasOnlineVO getContatoRodapeVendasOnlineVO() {
        if(this.contatoRodapeVendasOnlineVO == null){
            this.contatoRodapeVendasOnlineVO = new ContatoRodapeVendasOnlineVO();
        }
        return contatoRodapeVendasOnlineVO;
    }

    public void setContatoRodapeVendasOnlineVO(ContatoRodapeVendasOnlineVO contatoRodapeVendasOnlineVO) {
        this.contatoRodapeVendasOnlineVO = contatoRodapeVendasOnlineVO;
    }


    public List<ModalidadeCarrouselVendasOnlineVO> getListaModalidadeInclusao() {
        return listaModalidadeInclusao;
    }

    public void setListaModalidadeInclusao(List<ModalidadeCarrouselVendasOnlineVO> listaModalidadeInclusao) {
        this.listaModalidadeInclusao = listaModalidadeInclusao;
    }
    public AgendaVendasOnlineVO getAgendaVendasOnlineVO() {
        return agendaVendasOnlineVO;
    }

    public void setAgendaVendasOnlineVO(AgendaVendasOnlineVO agendaVendasOnlineVO) {
        if(getConfig() != null) {
            getConfig().setAgenda(agendaVendasOnlineVO);
        }
        this.agendaVendasOnlineVO = agendaVendasOnlineVO;
    }

    public void alterarStatusProduto() throws Exception {
        AulasVendasOnline obj = (AulasVendasOnline) context().getExternalContext().getRequestMap().get("aulaVendasOnline");
        obj.setAtivo(!obj.isAtivo());
        getFacade().getVendasOnline().mudarStatusAulaVendasOnline(obj.getCodigoProduto());
    }

    public AgendaVendasOnlineVO editarProduto() throws Exception {
        AulasVendasOnline obj = (AulasVendasOnline) context().getExternalContext().getRequestMap().get("aulaVendasOnline");
        AgendaVendasOnlineVO vo = getAgendaVendasOnlineVO();
        vo.setValorProduto(obj.getValor());
        vo.setDescricaoProduto(obj.getDescricaoProduto());
        vo.setAulaEscolhida(obj.getCodigoTurma());
        vo.setEstaEditando(true);
        setAgendaVendasOnlineVO(vo);

        return vo;
    }

    public void excluirProduto() throws Exception {
        AulasVendasOnline obj = (AulasVendasOnline) context().getExternalContext().getRequestMap().get("aulaVendasOnline");
        getFacade().getVendasOnline().excluirAulaVendasOnline(config, getUsuarioLogado(), obj.getCodigoProduto(), false, null);
        carregarAulasVendasOnline();
    }

    public void excluirAulaLinkVisitante() throws Exception {
        AulasVendasOnline obj = (AulasVendasOnline) context().getExternalContext().getRequestMap().get("aulaVendasOnlineLinkVisitante");
        if (obj != null && !UteisValidacao.emptyNumber(obj.getCodigo())) {
            getFacade().getVendasOnline().excluirAulaVendasOnline(config, getUsuarioLogado(), obj.getCodigo(), true, obj.getCodigoTurma());
            carregarAulasVendasOnlineLinkVisitanteJaExistentes();
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Aula excluída com sucesso!");
        } else if (obj != null && !UteisValidacao.emptyNumber(obj.getCodigoTurma())) {
            for (AulasVendasOnline aulaRemover : getAgendaLinkVisitanteVendasOnlineVO().getAulasVendasOnlineLinkVisitante()) {
                if (aulaRemover.getCodigoTurma().equals(obj.getCodigoTurma())) {
                    getAgendaLinkVisitanteVendasOnlineVO().getAulasVendasOnlineLinkVisitante().remove(aulaRemover);
                    //depois de remover a aula na lista, voltar ela pra combo pra deixar selecionar de novo
                    disponibilizarAulaNovamenteParaSelecao(obj);
                    break;
                }
            }
            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("Aula excluída com sucesso!");
        }
    }

    public void disponibilizarAulaNovamenteParaSelecao(AulasVendasOnline aula) throws Exception {
        TurmaEHorarioVendasOnlineVO turma = new TurmaEHorarioVendasOnlineVO();
        turma.setCodigoTurma(aula.getCodigoTurma());
        String[] nomeTurmaTratado = aula.getTurma().split(" - ");
        turma.setDescricao(nomeTurmaTratado[1]);

        String[] nomeModalidadeTratado = aula.getModalidade().split(" - ");
        turma.setNomeModalidade(nomeModalidadeTratado[1]);
        turma.setCodigoModalidade(aula.getCodigoModalidade());
        getAgendaLinkVisitanteVendasOnlineVO().getListTurmaHorarioLinkVisitante().add(turma);
    }

    public void removerAulaParaNaoPermitirSelecao(TurmaEHorarioVendasOnlineVO turma) {
        //depois de adicionar a aula na lista, remover da combo pra não deixar selecionar de novo
        List<TurmaEHorarioVendasOnlineVO> listaAulasDisponiveisAinda = new ArrayList<>();
        for (TurmaEHorarioVendasOnlineVO item : getAgendaLinkVisitanteVendasOnlineVO().getListTurmaHorarioLinkVisitante()) {
            if (!item.getCodigoTurma().equals(turma.getCodigoTurma())) {
                listaAulasDisponiveisAinda.add(item);
            }
        }
        getAgendaLinkVisitanteVendasOnlineVO().setListTurmaHorarioLinkVisitante(listaAulasDisponiveisAinda);
    }



    public boolean isMultiEmpresaConfig() {
        return multiEmpresaConfig;
    }

    public void setMultiEmpresaConfig(boolean multiEmpresaConfig) {
        this.multiEmpresaConfig = multiEmpresaConfig;
    }

    public MultiEmpresaConfigsVendasOnlineVO getMultiEmpresaConfigsVendasOnlineVO() {
        if(this.multiEmpresaConfigsVendasOnlineVO == null){
            this.multiEmpresaConfigsVendasOnlineVO = new MultiEmpresaConfigsVendasOnlineVO();
        }
        return multiEmpresaConfigsVendasOnlineVO;
    }

    public void setMultiEmpresaConfigsVendasOnlineVO(MultiEmpresaConfigsVendasOnlineVO multiEmpresaConfigsVendasOnlineVO) {
        this.multiEmpresaConfigsVendasOnlineVO = multiEmpresaConfigsVendasOnlineVO;
    }

    public ModalidadeCarrouselVendasOnlineVO getModalidadeCarrouselVendasOnlineVO2() {
        return modalidadeCarrouselVendasOnlineVO2;
    }

    public void setModalidadeCarrouselVendasOnlineVO2(ModalidadeCarrouselVendasOnlineVO modalidadeCarrouselVendasOnlineVO2) {
        this.modalidadeCarrouselVendasOnlineVO2 = modalidadeCarrouselVendasOnlineVO2;
    }

    public ModalidadeCarrouselVendasOnlineVO getModalidadeCarrouselVendasOnlineVO3() {
        return modalidadeCarrouselVendasOnlineVO3;
    }

    public void setModalidadeCarrouselVendasOnlineVO3(ModalidadeCarrouselVendasOnlineVO modalidadeCarrouselVendasOnlineVO3) {
        this.modalidadeCarrouselVendasOnlineVO3 = modalidadeCarrouselVendasOnlineVO3;
    }

    public boolean isAbaPublicar() {
        return abaPublicar;
    }

    public void setAbaPublicar(boolean abaPublicar) {
        this.abaPublicar = abaPublicar;
    }

    public void validarUrlAPI() throws Exception {
        try {
            limparMsg();
            if(!Util.isEmptyString(urlHotsite)){
                String retorno = validateUrl(getKey(), getProtocoloHttps() + urlHotsite + PropsService.getPropertyValue(PropsService.dominioHotsite));
                if(retorno.contains("sucesso")){
                    setExibirBtnPublicar(true);
                }else{
                    setExibirBtnPublicar(false);
                    throw new Exception("Não foi possível validar a URL: " + retorno);
                }
            }else{
                throw new Exception("O Domínio é Obrigatório.");
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void validarUrlAPIDominioProprio() throws Exception {
        try {
            limparMsg();
            if (UteisValidacao.emptyString(urlHotsite)) {
                throw new Exception("O Domínio é Obrigatório.");
            }

            urlHotsite = Uteis.removerEspacosInicioFimString(urlHotsite);

            if (urlHotsite.contains("https://")) {
                throw new Exception("Não é necessário digitar o \"https://\"! Remova e deixe somente o domínio e tente novamente.");
            } else if (urlHotsite.contains("http://")) {
                throw new Exception("Só é aceito protocolo seguro \"https:\"!");
            }
            if (urlHotsite.substring(urlHotsite.length() - 1).equals("/")) {
                throw new Exception("Não é necessário digitar a \"/\" no final! Remova e tente novamente.");
            }

            int timeout = 10000;

            //verifica se o endereço está acessível com um timeout.
            if (isAddressReachable(urlHotsite, timeout)) {
                InetAddress address;
                try {
                    address = InetAddress.getByName(urlHotsite);
                } catch (Exception ex) {
                    throw new Exception("Consegui acessar o site, mas não consegui obter o endereço DNS do servidor");
                }

                if (address == null) {
                    throw new Exception("Erro ao instanciar o endereço. Entre em contato com a Pacto!");
                }

                if (!UteisValidacao.emptyString(address.getHostAddress())) {
                    if (address.getHostAddress().equals(getIpServidorHotsite())) {
                        String retorno = validateUrl(getKey(), getProtocoloHttps() + urlHotsite);
                        if (retorno.contains("sucesso")) {
                            setExibirBtnPublicar(true);
                        } else {
                            setExibirBtnPublicar(false);
                            montarErro(new Exception("Não foi possível validar a URL: " + retorno));
                        }
                    } else {
                        throw new Exception("Consegui acessar o site, mas o endereço DNS do servidor não está configurado corretamente de acordo com o nosso!");
                    }
                } else {
                    throw new Exception("Consegui acessar o site, mas não encontrei o endereço DNS do servidor!");
                }
            } else {
                throw new Exception("Não foi possível acessar a url informada. Verifique se url informada está correta. Atualmente só aceitamos protocolo seguro 'https', caso o seu site seja somente 'http' não irá funcionar também!");
            }
            montarSucessoGrowl("URL validada com sucesso! Clique em 'Publicar' para continuar.");
        } catch (Exception e) {
            montarErro(e.getMessage());
        }

    }

    public static boolean isAddressReachable(String url, int timeout) {
        try {
            URL test = new URL("https://" + url);
            HttpURLConnection.setFollowRedirects(false);
            HttpURLConnection urlConnect = (HttpURLConnection) test.openConnection();
            System.setProperty("http.agent", "");
            urlConnect.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534.30 (KHTML, like Gecko) Chrome/12.0.742.100 Safari/534.30");
            urlConnect.setRequestMethod("HEAD");
            urlConnect.setConnectTimeout(timeout);
            return (urlConnect.getResponseCode() == HttpURLConnection.HTTP_OK);
        } catch (Exception e) {
            return false;
        }
    }

    public void publicarUrlAPI() throws Exception{
        limparMsg();
        String retorno = null;
        try {
            String erro = validarCamposVendas();
            if(Util.isEmptyString(erro)) {
                retorno = bindUrl(getKey(), getProtocoloHttps()+ urlHotsite + PropsService.getPropertyValue(PropsService.dominioHotsite));
                if (!UteisValidacao.emptyString(retorno)) {
                    setPublicado(true);
                    setErroConsultarStatus(false);
                    setExibirBtnPublicar(false);
                    montarSucessoGrowl("Site publicado com sucesso!");
                    getFacade().getVendasOnline().alterarDominioProprioHotsite(getConfig().getEmpresa(), false);
                } else {
                    throw new Exception("Não foi possível publicar o site!");
                }

            } else {
                montarErro(new Exception(erro));
            }
        } catch (Exception e) {
            montarErro(new Exception("Não foi possível publicar a URL: "+retorno));
        }
    }

    public void publicarUrlAPIDominioProprio() throws Exception {
        limparMsg();
        String retorno = null;
        try {
            String erro = validarCamposVendas();
            if (Util.isEmptyString(erro)) {
                retorno = bindUrl(getKey(), getProtocoloHttps() + urlHotsite);
                if (!UteisValidacao.emptyString(retorno)) {
                    setPublicado(true);
                    setErroConsultarStatus(false);
                    setExibirBtnPublicar(false);
                    montarSucessoGrowl("Site publicado com sucesso!");
                    getFacade().getVendasOnline().alterarDominioProprioHotsite(getConfig().getEmpresa(), true);
                } else {
                    throw new Exception("Não foi possível publicar o site!");
                }

            } else {
                montarErro(new Exception(erro));
            }

        } catch (Exception e) {
            montarErro(new Exception("Não foi possível publicar a URL: " + retorno));
        }
    }

    public void getUrlByChaveAPI() throws Exception {
        limparMsg();
        if(!Util.isEmptyString(getKey())){
            String retorno = getUrlByChave(getKey());
            if(retorno != null && !retorno.isEmpty()){
                setUrlHotsite(retorno.replace(getProtocoloHttps(), "").replace(getDominioHotsite(),""));
                if (!UteisValidacao.emptyString(getUrlHotsite())) {
                    setPublicado(true);
                    setErroConsultarStatus(false);
                }
            }else{
                setUrlHotsite("");
                setPublicado(false);
                setErroConsultarStatus(false);
            }
        }else{
            montarErro(new Exception("Chave da empresa é Obrigatório."));
        }
    }

    private String validarCamposVendas() throws Exception {
        StringBuilder str = new StringBuilder();
        List<PlanosSiteVendasOnlineVO> listaPlano = getFacade().getVendasOnline().consultarPlanosSiteVendasOnline(config.getCodigo());
        if (listaPlano == null || listaPlano.size() < 1){
            str.append("É obrigatório ter um Plano configurado. ");
        }
        carregarAulasVendasOnline();
        FotoFachadaVendasOnlineVO fotoFachadaVendasOnlineVO = getFacade().getVendasOnline().consultarFotoFachadaVendasOnline(config.getCodigo());
        if (UteisValidacao.emptyString(fotoFachadaVendasOnlineVO.getFotoKey())) {
            str.append("É obrigatório ter foto da fachada configurada. ");
        }

        return str.toString();
    }

    public String validateUrl(String chave, String url) throws Exception {
        org.json.JSONObject body = new org.json.JSONObject();
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(obterURLAPI(chave) + "/validateUrl?url="+url,
                headers, params, body.toString(), MetodoHttpEnum.POST);
        JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
        if (jsonRetorno.has("string") && jsonRetorno.getString("string") != null) {
            return jsonRetorno.optString("string");
        }else{
            return jsonRetorno.has("erro")?jsonRetorno.getString("erro"):"";
        }
    }

    public String getUrlByChave(String chave) throws Exception {
        org.json.JSONObject body = new org.json.JSONObject();
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(obterURLAPISemChave(chave) + "getUrlByChave?chave="+chave,
                headers, params, body.toString(), MetodoHttpEnum.GET);
        JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
        if (jsonRetorno.has("chave") && jsonRetorno.getString("chave") != null) {
            return jsonRetorno.optString("chave");
        }else {
            return "";
        }
    }

    public String getTitleLinkCadastroVisitante() {
        StringBuilder sb = new StringBuilder();
        sb.append("Este link direciona o usuário para a tela de cadastro de visitantes.</br>");
        sb.append("<b>Novo cadastro:</b> será cadastrado na base de dados.</br>");
        sb.append("<b>Cadastro existente:</b> terá seus dados atualizados.</br></br>");
        sb.append("Se uma opção de <b>FreePass</b> for selecionada.</br>");
        sb.append("<b>Cadastro de Visitante (novo ou antigo):</b> se não tiver nenhum FreePass registrado em seu histórico, terá o FreePass lançado.</br>");
        sb.append("<b>Cadastro diferente de Visitante:</b> apenas terá seus dados atualizados.</br>");
        return sb.toString();
    }

    public String getTitleEspelhoAulas() {
        StringBuilder sb = new StringBuilder();
        sb.append("Este é um link genérico com o espelho da agenda de todas as suas aulas cadastradas, ou seja, é a sua grade horária apenas. não permitindo qualquer tipo de agendamento.</br>");
        sb.append("<b>Obs:</b> Caso você queira o link que permita agendamentos, é necessário que você gere um link por produto específico ou por plano específico.</br>");
        sb.append("Assim que selecionar o produto ou plano específico, irá exibir o link de agendamento para você utilizar.</br>");
        return sb.toString();
    }

    public String getTitleLinkUserResponsavel() {
        StringBuilder sb = new StringBuilder();
        sb.append("Informando um usuário responsável pelos links, os alunos que receberem esses links ficarão vinculados com o usuário informado aqui, independente do tipo de link gerado (link de cadastro de visitante, link da loja, plano, produto, etc).</br>");
        sb.append("Este campo aqui é opcional e caso você não informe, os alunos que vierem dos links ficarão vinculados com o Consultor Site padrão definido lá na aba 'Avançado' nas configurações do Vendas Online.</br>");
        return sb.toString();
    }

    public String getTitleCamposExibirVendasOnline() {
        StringBuilder sb = new StringBuilder();
        sb.append("Essas são as configurações de personalização de quais campos serão exibidos ou não aos alunos.</br>");
        sb.append("Note que existem configurações distintas para links de venda de Plano e Produto/Visitante, configure de acordo com o desejado.</br>");
        return sb.toString();
    }

    public String getTitleContratoConcomitanteParcelaEmAberto() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>desmarcada</b>, ao lançar um contrato concomitante, se o aluno digitar o cpf ou email e usar o cadastro já existente dele,</br>");
        sb.append("ao lançar o plano o sistema irá verificar se esse aluno tem alguma parcela vencida e caso encontre alguma não permitirá a venda.</br></br>");
        sb.append("Com essa configuração <b>marcada</b>, o sistema não irá validar se o aluno possui parcela em aberto antes de lançar um contrato concomitante.</br></br>");
        sb.append("<b>Obs:</b> Essa configuração aqui só aparece quando a empresa utiliza contratos concomitantes.");
        return sb.toString();
    }

    public String getTitleVendaProdutoAlunoOutraUnidade() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>marcada</b>, o sistema irá permitir o aluno da unidade 1, comprar um produto usando o link da unidade 2 por exemplo.</br>");
        sb.append("Com essa configuração <b>desmarcada</b>, o sistema não irá permitir a venda de produtos para alunos de outras unidades.</br></br>");
        sb.append("<b>Obs:</b> Configuração válida quando o aluno já se encontra cadastrado no sistema e ao digitar o CPF no vendas online, utiliza o cadastro já existente para prosseguir com a venda.</br>");
        sb.append("Com essa configuração <b>marcada</b>, o aluno não será transferido de unidade no ato da venda, independente se possui contrato ou se está como visitante");
        return sb.toString();
    }

    public String getTitleExibirTipoDocumentoTelaVendasOnline() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>marcada</b>, ao acessar a tela do Vendas Online para registrar os dados do aluno, irá exibir um Checkbox para escolher entre CPF ou CNPJ.</br>");
        sb.append("Esse tipo irá definir o lançamento do cadastro do aluno no Sistema Pacto Adm.</br></br>");
        sb.append("<b>Obs:</b> Essa configuração não intefere na exibição dos outros campos como: Data de Nascimento, Sexo, Responsável e etc.");
        //Foi definido pelo time que a configuração não iria interferir nos outros campos, conforme Obs acima. Se for para mudar, precisa discutir com o time e gerar um novo ticket de Evolução.
        return sb.toString();
    }

    public String getTitleNaoPodeProsseguirVisitanteMesmoCPF() {
        StringBuilder sb = new StringBuilder();
        sb.append("Essa configuração é válida somente para o link de cadastro de visitantes</br>");
        sb.append("Com essa configuração <b>desmarcada</b>, caso o usuário digite um cpf que já exista </br>");
        sb.append("cadastrado no sistema, então será exibido uma mensagem que não pode prosseguir e pedirá para procurar a recepção da academia.");
        return sb.toString();
    }

    public String getTitleExibirEnvioEmailUsuarioMovel() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>marcada</b>, ao término de uma venda com sucesso será enviado automaticamente um email para o aluno com o usuário e senha dele do APP Treino.</br>");
        sb.append("Com essa configuração <b>desmarcada</b>, Será incluído o usuário móvel, porém não será enviado o email ao aluno automaticamente.</br>");
        return sb.toString();
    }

    public String getTitleHabilitarAgendamentoAulaExperimentalLinkVisitanteVendasOnline() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>marcada</b>, ao acessar o link de cadastro de visitante concluindo o preenchimento do formulário,</br>");
        sb.append("o cliente será direcionado para uma tela de agendamento de aula experimental, onde irá conseguir agendar uma aula.</br></br>");
        sb.append("<b>Obs:</b> O cliente que se cadastrar através do link com esta configuração habilitada, só conseguirá agendar aula experimental através do mesmo uma única vez.");
        //Foi definido pelo time que a configuração não iria interferir nos outros campos, conforme Obs acima. Se for para mudar, precisa discutir com o time e gerar um novo ticket de Evolução.
        return sb.toString();
    }

    public String getTitleModalidadesIniciarSelecionadasContratoTurma() {
        StringBuilder sb = new StringBuilder();
        sb.append("Esta configuração afeta apenas contratos com turma, na tela de modalidades. </br>");
        sb.append("Com a configuração <b>marcada</b>, as modalidades estarão todas selecionadas por padrão. </br> ");
        sb.append("Com a configuração <b>desmarcada</b>, as modalidades não estarão selecionadas inicialmente. </br> ");
        sb.append("Em ambos os casos, o aluno poderá selecionar ou desmarcar as modalidades conforme desejar.");
        return sb.toString();
    }

    public String getTitleAtivarLinksGooglePlayEAppleStore() {
        StringBuilder sb = new StringBuilder();
        sb.append("Com essa configuração <b>marcada</b>, ao finalizar uma venda com sucesso, o usuário será redirecionado para uma tela onde serão exibidos os links para download do APP Treino.</br>");
        sb.append("Para isso, será necessário configurar nos campos abaixo, qual a url o sistema deve exibir. </br></br>");
        sb.append("<b>IMPORTANTE:</b> Se o cliente utilizar esse recurso, o recurso de 'Endereço para direcionar o aluno após a venda' não irá funcionar, </br>");
        sb.append("pois precisa direcionar para a nossa tela, para exibir a opções dos links.");
        return sb.toString();
    }

    public String getTitleGoogleTagManagerVendasOnline() {
        StringBuilder sb = new StringBuilder();
        sb.append("Adicione seu código do Google Tag Manager para ativar ferramentas de marketing, análise e rastreamento de forma simples no vendas online.</br>");
        sb.append("Já temos o script embutido nas páginas do vendas online, você só precisa informar o ID do seu Tag Manager.</br>");
        sb.append("Informe aqui somente o ID do TagManager desejado. EX: <b>GTM-XXXXXXXX</b>");
        return sb.toString();
    }

    public String getTitleGoogleTagManagerHotsite() {
        StringBuilder sb = new StringBuilder();
        sb.append("Adicione seu código do Google Tag Manager para ativar ferramentas de marketing, análise e rastreamento de forma simples no hotsite.</br>");
        sb.append("Já temos o script embutido nas páginas do hotsite, você só precisa informar o ID do seu Tag Manager.</br>");
        sb.append("Informe aqui somente o ID do TagManager desejado. EX: <b>GTM-XXXXXXXX</b>");
        return sb.toString();
    }

    public void limparCamposUrlLinksGooglePlayEAppleStore() {
        getConfig().setUrlLinkGooglePlay("");
        getConfig().setUrlLinkAppleStore("");
    }

    public String bindUrl(String chave, String url) throws Exception {
        org.json.JSONObject body = new org.json.JSONObject();
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(obterURLAPI(chave) + "/bindUrl?url="+url,
                headers, params, body.toString(), MetodoHttpEnum.POST);
        JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
        if (jsonRetorno.getString("return").isEmpty()) {
            throw new Exception(jsonRetorno.optString("return"));
        }else{
            setMsgPublicado(Boolean.TRUE);
            return jsonRetorno.optString("return");
        }
    }

    public String deleteUrl(String chave, String url) throws Exception {
        org.json.JSONObject body = new org.json.JSONObject();
        Map<String, String> params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(obterURLAPI(chave) + "/deleteUrl?url="+url,
                headers, params, body.toString(), MetodoHttpEnum.POST);
        JSONObject jsonRetorno = new JSONObject(respostaHttpDTO.getResponse());
        if (jsonRetorno.getString("return").isEmpty()) {
            throw new Exception(jsonRetorno.optString("return"));
        }else{
            setMsgPublicado(Boolean.TRUE);
            return jsonRetorno.optString("return");
        }
    }
    private String obterURLAPI(final String chave) {
        return  PropsService.getPropertyValue(chave, PropsService.urlZWAPI)+"/prest/v1/hotsite/"+chave;
    }

    private String obterURLAPISemChave(final String chave) {
        return  PropsService.getPropertyValue(chave, PropsService.urlZWAPI)+"/prest/v1/hotsite/";
    }

    public String getTitleExpDominioProprio() {
        return "Você pode definir um domínio próprio, PORÉM para o perfeito funcionamento do seu Hotsite, você deve nos enviar o certificado SSL/TLS 1.2 do seu site</br> ou, se ainda não tem, será necessário comprar de um fornecedor reconhecido. Por exemplo: GoDaddy, ActiveWeb";
    }

    public String getTitleHabilitarEmpresa() {
        StringBuilder sb = new StringBuilder();
        sb.append("Marque esta opção caso deseje que essa unidade que você está configurando seja apresentada na lista de unidades disponíveis para venda.</br>");
        sb.append("No zw adm quando se usa <b>banco multiempresa (várias empresas na mesma chave)</b> ou então <b>rede de empresas para chaves diferentes</b>, pode ser que você queira que somente uma unidade ou outra</br>");
        sb.append("seja exibida na lista de unidades disponíveis para compra.</br>");
        sb.append("Com essa configuração <b>habilitada</b> o sistema irá exibir essa unidade na página principal junto com as outras e com a configuração <b>desabilitada</b> não irá exibir.</br></br>");
        sb.append("<b>Obs:</b> Configuração válida somente para <b>banco multiempresa (várias empresas na mesma chave)</b> ou então <b>rede de empresas para chaves diferentes</b>.</br>");
        return sb.toString();
    }

    public String getTitleTipoParcelamentoStone() throws Exception {
        StringBuilder title = new StringBuilder();
        title.append("Na Stone existem dois tipos de parcelamentos e ambos possuem juros. A diferença é de quem os juros serão cobrados: <br>");
        title.append("<b>a) do lojista:</b> Nesta modalidade os juros serão cobramos da loja e o valor repassado ao portador do cliente é <b> 'sem juros'.</b><br>");
        title.append("<b>b) do portador do cartão:</b> Aqui os juros não são pagos pelo lojista e são repassados diretamente ao portador do cartão <b>'com juros'.</b></br>");
        title.append("Dentro do nosso sistema, a transação parcelada ficará um valor menor ao parcelado real cobrado no cartão do cliente pois quem controla os juros neste caso é a Stone.");

        return title.toString();
    }

    public String getUrlHotsite() {
        return urlHotsite;
    }

    public void setUrlHotsite(String urlHotsite) {
        this.urlHotsite = urlHotsite;
    }

    public Boolean getExibirBtnPublicar() {
        return exibirBtnPublicar;
    }

    public void setExibirBtnPublicar(Boolean exibirBtnPublicar) {
        this.exibirBtnPublicar = exibirBtnPublicar;
    }

    public Boolean getMsgPublicado() {
        return msgPublicado;
    }

    public void setMsgPublicado(Boolean msgPublicado) {
        this.msgPublicado = msgPublicado;
    }

    public String getProtocoloHttps() {
        if(protocoloHttps == null || UteisValidacao.emptyString(protocoloHttps)){
            protocoloHttps = PropsService.getPropertyValue(PropsService.protocoloHttps);
        }
        return protocoloHttps;
    }

    public void setProtocoloHttps(String protocoloHttps) {
        this.protocoloHttps = protocoloHttps;
    }

    public String getDominioHotsite() {
        if(dominioHotsite == null || UteisValidacao.emptyString(dominioHotsite)){
            dominioHotsite = PropsService.getPropertyValue(PropsService.dominioHotsite);
        }
        return dominioHotsite;
    }

    public String getIpServidorHotsite() {
        if(ipServidorHotsite == null || UteisValidacao.emptyString(ipServidorHotsite)){
            ipServidorHotsite = PropsService.getPropertyValue(PropsService.ipServidorHotsite);
        }
        return ipServidorHotsite;
    }

    public void setDominioHotsite(String dominioHotsite) {
        this.dominioHotsite = dominioHotsite;
    }

    public boolean isMostrarConfirmacaoExclusao() {
        return mostrarConfirmacaoExclusao;
    }

    public void setMostrarConfirmacaoExclusao(boolean mostrarConfirmacaoExclusao) {
        this.mostrarConfirmacaoExclusao = mostrarConfirmacaoExclusao;
    }

    public String getClassesPeloMenuAtivo() {
        if (getMenuZwUi()) {
            return "container-box zw_ui especial container-conteudo-central";
        }
        return "";
    }

    public List<ConvenioCobrancaVO> getListaConvenioCobrancaGeralVO() {
        if (listaConvenioCobrancaGeralVO == null) {
            listaConvenioCobrancaGeralVO = new ArrayList<>();
        }
        return listaConvenioCobrancaGeralVO;
    }

    public void setListaConvenioCobrancaGeralVO(List<ConvenioCobrancaVO> listaConvenioCobrancaGeralVO) {
        this.listaConvenioCobrancaGeralVO = listaConvenioCobrancaGeralVO;
    }

    public Integer getCodigoEventoPlano() {
        return codigoEventoPlano;
    }

    public void setCodigoEventoPlano(Integer codigoEventoPlano) {
        this.codigoEventoPlano = codigoEventoPlano;
    }

    public Integer getCodigoEventoProduto() {
        return codigoEventoProduto;
    }

    public void setCodigoEventoProduto(Integer codigoEventoProduto) {
        this.codigoEventoProduto = codigoEventoProduto;
    }

    public List<SelectItem> getListaEventos() {
        return listaEventos;
    }

    public void setListaEventos(List<SelectItem> listaEventos) {
        this.listaEventos = listaEventos;
    }

    public List<SelectItem> getConveniosPix() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaGeralVO()) {
            if (obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PIX)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<SelectItem> getConveniosBoleto() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaGeralVO()) {
            if (obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) &&
                    !obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<VendasOnlineCampanhaVO> getListaLinksCampanhaPlano() {
        return listaLinksCampanhaPlano;
    }

    public void setListaLinksCampanhaPlano(List<VendasOnlineCampanhaVO> listaLinksCampanhaPlano) {
        this.listaLinksCampanhaPlano = listaLinksCampanhaPlano;
    }

    public String getStyleComboEventoPlano(){
        if (tipoLinkPlano == 1){
            return "margin-left: 12.2vh;";
        }else if (tipoLinkPlano == 2){
            return "margin-left: 8vh;";
        }else if (tipoLinkPlano == 3){
            return "margin-left: 11vh;";
        }
        return "";
    }

    public String getStyleComboEventoProduto(){
        if (tipoLinkProduto == 1){
            return "margin-left: 12.2vh;";
        }else if (tipoLinkProduto == 2){
            return "margin-left: 4.4vh;";
        }
        return "";
    }

    public List<VendasOnlineCampanhaVO> getListaLinksCampanhaProduto() {
        return listaLinksCampanhaProduto;
    }

    public void setListaLinksCampanhaProduto(List<VendasOnlineCampanhaVO> listaLinksCampanhaProduto) {
        this.listaLinksCampanhaProduto = listaLinksCampanhaProduto;
    }

    public Integer getCodigoEventoVisitante() {
        return codigoEventoVisitante;
    }

    public void setCodigoEventoVisitante(Integer codigoEventoVisitante) {
        this.codigoEventoVisitante = codigoEventoVisitante;
    }

    public List<VendasOnlineCampanhaVO> getListaLinksCampanhaVisitante() {
        return listaLinksCampanhaVisitante;
    }

    public void setListaLinksCampanhaVisitante(List<VendasOnlineCampanhaVO> listaLinksCampanhaVisitante) {
        this.listaLinksCampanhaVisitante = listaLinksCampanhaVisitante;
    }

    private void montarListaUsuarios() {
        try {
            this.setUsuariosLink(getFacade().getUsuario().consultarTodosAtivosSemAdministrador(Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void desaabilitarHotsite() throws Exception {
        deleteUrl(getKey(), getProtocoloHttps()+ urlHotsite + PropsService.getPropertyValue(PropsService.dominioHotsite));            setPublicado(true);
        setErroConsultarStatus(false);
        setExibirBtnPublicar(false);
        setPublicado(false);
        limparCamposHotsite();
        this.setMsgPublicado(false);
        montarSucessoGrowl("Site removido com sucesso!");
    }

    public void limparCamposHotsite() {
        setUrlHotsite("");
        setExibirBtnPublicar(false);
    }

    public List<UsuarioVO> getUsuariosLink() {
        if (usuariosLink == null) {
            usuariosLink = new ArrayList<>();
        }
        return usuariosLink;
    }

    public void setUsuariosLink(List<UsuarioVO> usuariosLink) {
        this.usuariosLink = usuariosLink;
    }

    public List<SelectItem> getSelectItemUsuariosLink() {
        List<SelectItem> lista = new ArrayList<>();
        for (UsuarioVO usuarioVO : getUsuariosLink()) {
            lista.add(new SelectItem(usuarioVO.getCodigo(), usuarioVO.getNome()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0 , ""));
        return lista;
    }

    public Integer getParamUsuario() {
        if (paramUsuario == null) {
            paramUsuario = 0;
        }
        return paramUsuario;
    }

    public void setParamUsuario(Integer paramUsuario) {
        this.paramUsuario = paramUsuario;
    }

    private String addParamUsuario() {
        return (UteisValidacao.emptyNumber(getParamUsuario()) ? "" : "&us=" + getParamUsuario()) ;
    }

    private String addParamFreepass(){
        return (UteisValidacao.emptyNumber(getFreepass()) ?  "" :  "&fp=" + getFreepass());
    }
    public boolean isDominioProprioHotsite() {
        return dominioProprioHotsite;
    }

    public void setDominioProprioHotsite(boolean dominioProprioHotsite) {
        this.dominioProprioHotsite = dominioProprioHotsite;
    }

    public void setIpServidorHotsite(String ipServidorHotsite) {
        this.ipServidorHotsite = ipServidorHotsite;
    }

    public boolean isPublicado() {
        return publicado;
    }

    public void setPublicado(boolean publicado) {
        this.publicado = publicado;
    }

    public boolean isErroConsultarStatus() {
        return erroConsultarStatus;
    }

    public void setErroConsultarStatus(boolean erroConsultarStatus) {
        this.erroConsultarStatus = erroConsultarStatus;
    }

    public boolean isHabilitarempresahotsite() {
        return habilitarempresahotsite;
    }

    public void setHabilitarempresahotsite(boolean habilitarempresahotsite) {
        this.habilitarempresahotsite = habilitarempresahotsite;
    }

    public boolean isExibirConfigContratoConcomitanteComParcelaEmAberto() {
        return exibirConfigContratoConcomitanteComParcelaEmAberto;
    }

    public void setExibirConfigContratoConcomitanteComParcelaEmAberto(boolean exibirConfigContratoConcomitanteComParcelaEmAberto) {
        this.exibirConfigContratoConcomitanteComParcelaEmAberto = exibirConfigContratoConcomitanteComParcelaEmAberto;
    }

    public List getListaSelectItemCampanhaCupomDesconto() {
        if (listaSelectItemCampanhaCupomDesconto == null) {
            listaSelectItemCampanhaCupomDesconto = new ArrayList<SelectItem>();
        }
        return listaSelectItemCampanhaCupomDesconto;
    }

    public void setListaSelectItemCampanhaCupomDesconto(List listaSelectItemCampanhaCupomDesconto) {
        this.listaSelectItemCampanhaCupomDesconto = listaSelectItemCampanhaCupomDesconto;
    }

    public Integer getFreepass() {
        return freepass;
    }

    public void setFreepass(Integer freepass) {
        this.freepass = freepass;
    }

    public boolean isAbaCamposAdicionais() {
        return abaCamposAdicionais;
    }

    public void setAbaCamposAdicionais(boolean abaCamposAdicionais) {
        this.abaCamposAdicionais = abaCamposAdicionais;
    }

    public List<CampoAdicionalObrigatorioVO> getCamposAdicionaisItensPlanoProduto() {
        return camposAdicionaisItensPlanoProduto;
    }

    public void setCamposAdicionaisItensPlanoProduto(List<CampoAdicionalObrigatorioVO> camposAdicionaisItensPlanoProduto) {
        this.camposAdicionaisItensPlanoProduto = camposAdicionaisItensPlanoProduto;
    }

    public Integer getQuantidadeCamposAdicionaisItensPlanoProduto() {
        return quantidadeCamposAdicionaisItensPlanoProduto;
    }

    public void setQuantidadeCamposAdicionaisItensPlanoProduto(Integer quantidadeCamposAdicionaisItensPlanoProduto) {
        this.quantidadeCamposAdicionaisItensPlanoProduto = quantidadeCamposAdicionaisItensPlanoProduto;
    }

    public AgendaVendasOnlineVO getAgendaLinkVisitanteVendasOnlineVO() {
        return agendaLinkVisitanteVendasOnlineVO;
    }

    public void setAgendaLinkVisitanteVendasOnlineVO(AgendaVendasOnlineVO agendaLinkVisitanteVendasOnlineVO) {
        this.agendaLinkVisitanteVendasOnlineVO = agendaLinkVisitanteVendasOnlineVO;
    }

    public List<VendasOnlineConvenioVO> getListaVendasOnlineFormaPagamentoEmpresa() {
        return listaVendasOnlineFormaPagamentoEmpresa;
    }

    public void setListaVendasOnlineFormaPagamentoEmpresa(List<VendasOnlineConvenioVO> listaVendasOnlineFormaPagamentoEmpresa) {
        this.listaVendasOnlineFormaPagamentoEmpresa = listaVendasOnlineFormaPagamentoEmpresa;
    }

    public VendasOnlineConvenioVO getVendasOnlineFormPagVO() {
        return vendasOnlineFormPagVO;
    }

    public void setVendasOnlineFormPagVO(VendasOnlineConvenioVO vendasOnlineFormPagVO) {
        this.vendasOnlineFormPagVO = vendasOnlineFormPagVO;
    }

    public void incluirItemListaAulasVendasOnline() {
        try {
            config.setNovoObj(!getAgendaVendasOnlineVO().getEstaEditando());
            getContatoRodapeVendasOnlineVO().setCodVendasOnlineConfig(config.getCodigo());
            getFacade().getVendasOnline().gravarAgendaVendasOnline(getAgendaVendasOnlineVO());
            getAgendaVendasOnlineVO().setEstaEditando(false);
            carregarAulasVendasOnline();
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public String getBeneficioParaAdicionar() {
        if(UteisValidacao.emptyString(beneficioParaAdicionar)) {
            beneficioParaAdicionar = "";
        }
        return beneficioParaAdicionar;
    }

    public void setBeneficioParaAdicionar(String beneficioParaAdicionar) {
        this.beneficioParaAdicionar = beneficioParaAdicionar;
    }

    public List<String> getLstBeneficiosDoPlano() {
        if(UteisValidacao.emptyList(lstBeneficiosDoPlano)) {
            lstBeneficiosDoPlano = new ArrayList<String>();
        }
        return lstBeneficiosDoPlano;
    }

    public void setLstBeneficiosDoPlano(List<String> lstBeneficiosDoPlano) {
        this.lstBeneficiosDoPlano = lstBeneficiosDoPlano;
    }

    public void adicionarBeneficioNaLista() {
        try {
            limparMsg();

            if(UteisValidacao.emptyString(beneficioParaAdicionar)) {
                throw new Exception("Informe a descrição do benefício para adicioná-lo!");
            }

            for(String obj : lstBeneficiosDoPlano) {
                if(!UteisValidacao.emptyString(obj) && obj.equalsIgnoreCase(beneficioParaAdicionar)){
                    throw new Exception("Este benefício já foi informado!");
                }
            }

            lstBeneficiosDoPlano.add(beneficioParaAdicionar);

            beneficioParaAdicionar = "";

        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void removerBeneficioDaLista(String beneficioRemover) {
        try {
            limparMsg();

            if(!UteisValidacao.emptyString(beneficioRemover)) {
                int indexRemocao = this.lstBeneficiosDoPlano.indexOf(beneficioRemover);

                if(indexRemocao >= 0) {
                    lstBeneficiosDoPlano.remove(indexRemocao);
                }else{
                    throw new Exception("Não foi possível obter o indície para remover o item!");
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    private String formatarBeneficiosPlanoParaString(List<String> lstBeneficios) throws Exception {
        try {

            String beneficios = "";

            if(UteisValidacao.emptyList(lstBeneficios)) {
                return beneficios;
            }else{
                for(String st : lstBeneficios) {
                    if(UteisValidacao.emptyString(beneficios)) {
                        beneficios += st;
                    }else{
                        beneficios += "#" + st;
                    }
                }
            }

            return beneficios;
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    private List<String> formatarBeneficiosPlanoParaLista(String beneficios) throws Exception {
        try {

            List<String> lstBeneficios = new ArrayList<String>();

            if(UteisValidacao.emptyString(beneficios)) {
                return lstBeneficios;
            }else{
                String[] arrayBeneficios = beneficios.split("#");

                if(arrayBeneficios != null) {

                    for(String st : arrayBeneficios) {
                        if(!UteisValidacao.emptyString(st)){
                            lstBeneficios.add(st);
                        }
                    }

                }
                return lstBeneficios;
            }

        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    public void verificarPlanoSelecionado(ActionEvent event) {

        setInformarValorDesejadoMensal(false);

        if(planosSiteVendasOnlineVO != null && !UteisValidacao.emptyNumber(planosSiteVendasOnlineVO.getCodPlano())) {
            try {
                PlanoVO planoSelecionado = getFacade().getPlano().consultarPorChavePrimaria(planosSiteVendasOnlineVO.getCodPlano(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if(planoSelecionado != null && planoSelecionado.isVendaCreditoTreino()) {
                    setInformarValorDesejadoMensal(true);
                }

            } catch (Exception e) {

            }
        }

    }

    public boolean isInformarValorDesejadoMensal() {
        return informarValorDesejadoMensal;
    }

    public void setInformarValorDesejadoMensal(boolean informarValorDesejadoMensal) {
        this.informarValorDesejadoMensal = informarValorDesejadoMensal;
    }

    public Double getValorDesejadoMensal() {
        if(valorDesejadoMensal == null) {
            valorDesejadoMensal = new Double(0);
        }
        return valorDesejadoMensal;
    }

    public void setValorDesejadoMensal(Double valorDesejadoMensal) {
        this.valorDesejadoMensal = valorDesejadoMensal;
    }

    public boolean isAbaIntegracoesHotsite() {
        return abaIntegracoesHotsite;
    }

    public void setAbaIntegracoesHotsite(boolean abaIntegracoesHotsite) {
        this.abaIntegracoesHotsite = abaIntegracoesHotsite;
    }

    public String getLinkCategoriaDeProdutosEspecifica() throws Exception {
        if (categoriaDeProdutosSelecionada != null && categoriaDeProdutosSelecionada > 0) {
            return url + "/produtos?un=" + getEmpresaVO().getCodigo() + "&k=" + getKey() + "&ct=" +
                    categoriaDeProdutosSelecionada + addParamUsuario();
        }
        return "";
    }

    public Integer getCategoriaDeProdutosSelecionada() {return categoriaDeProdutosSelecionada;}

    public void setCategoriaDeProdutosSelecionada(Integer categoriaDeProdutosSelecionada) {
        this.categoriaDeProdutosSelecionada = categoriaDeProdutosSelecionada;
    }

    public List<SelectItem> getCategoriasDeProdutos() {
        return categoriasDeProdutos;
    }

    public void setCategoriasDeProdutos(List<SelectItem> categoriasDeProdutos) {
        this.categoriasDeProdutos = categoriasDeProdutos;
    }

    public void montarListaDeCategoriasDeProdutos() {
        try {
            List resultadoConsulta = getFacade().getCategoriaProduto().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            List<SelectItem> objs = new ArrayList<>();
            while (i.hasNext()) {
                CategoriaProdutoVO obj = (CategoriaProdutoVO) i.next();
                categoriaDeProdutosSelecionada = categoriaDeProdutosSelecionada == null ? obj.getCodigo() : categoriaDeProdutosSelecionada;
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            setCategoriasDeProdutos(objs);
        } catch (Exception e){
            montarErro(e);
        }
    }
}
