package controle.tef;


import controle.arquitetura.SuperControle;
import java.util.HashSet;
import java.util.Set;

import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.tef.TefFiservService;

public class TefValidarPagamentosPendentesFiservController extends SuperControle {

    private String stringRetornoVerificacaoTransacoesPendentesFiserv = "TESTE";
    private String onCompletePinpad;

    public void confirmarRetornoVerificacaoTransacoesPendentesPinpadFiserv() {
        String retorno = getStringRetornoVerificacaoTransacoesPendentesFiserv();

        if (!UteisValidacao.emptyString(retorno)) {
            // Encontrar o valor para TipoCampo 210 - Quantidade de Transações Pendentes
            int index210 = (retorno.indexOf("{\\\"TipoCampo\\\": 210, \\\"Valor\\\": \\\"") + 34);
            int end210 = retorno.indexOf("\\\"", index210);
            String quantidade = retorno.substring(index210, end210).trim();

            if (quantidade.equals("0")) {
                //Se a quantidade = 0, não tem tratamento e fecha o modal
                setOnCompletePinpad("Richfaces.hideModalPanel('modalVerificacaoPagamentosPendentesFiserv');");
            } else {
                // Encontrar o valor para TipoCampo 160 - Cupom Fiscal da Transação Pendente
                int index160 = (retorno.indexOf("{\\\"TipoCampo\\\": 160, \\\"Valor\\\": \\\"") + 34);
                int end160 = retorno.indexOf("\\\"", index160);
                String cupomFiscal = retorno.substring(index160, end160).trim();

                try {
                    getFacade().getPinPadPedido().alterarStatusPedidoPinpadFiserv(cupomFiscal, StatusPinpadEnum.CANCELADO);
                } catch (Exception e) {
                    montarErro("Falha para atualizar o status do CupomFiscal Fiserv: " + cupomFiscal);
                }
                setSucesso(true); //Essa validação é importante para o botão Fechar Modal funcionar após o usuário ler a mensagem
            }
        } else {
            setOnCompletePinpad("Richfaces.hideModalPanel('modalVerificacaoPagamentosPendentesFiserv');");
        }
    }

    public void iniciarVerificacaoPagamentosPendentesFiserv() {
        // Verificar se tem Transações Pendentes na maquininha para o Pinpad Caixa/Fiserv, caso o já não tenha verificado
        try {
            setOnCompletePinpad(""); //Apagar para não correr risco de lixo de processos anteriores
            setMsgAlert(""); //Apagar para não correr risco de lixo de processos anteriores
            setStringRetornoVerificacaoTransacoesPendentesFiserv(""); //Apagar para não correr risco de lixo de processos anteriores

            // Busca apenas se tiver pelo menos uma Forma de Pagamento Pinpad Caixa/Fiserv
            Set<String> codigosEmpresasFiserv = new HashSet<>();
            codigosEmpresasFiserv = TefFiservService.obterListEmpresasFiserv(Conexao.getFromSession(), getEmpresaLogado().getCodigo());

            // A pedido da Fiserv, só deve verificar uma vez por usuário logado
            if (!codigosEmpresasFiserv.isEmpty() && !getUsuarioLogado().isVerificouTransacoesPendentesFiserv()) {
                for (String codigoEmpresa : codigosEmpresasFiserv) {
                    if (!UteisValidacao.emptyString(codigoEmpresa)) {
                        abrirPinpadFiserv(codigoEmpresa);
                    }
                }
                getUsuarioLogado().setVerificouTransacoesPendentesFiserv(true); //Pedido da Fiserv, para não verificar novamente, se o usuário continuar logado.
            }
        } catch (Exception e) {
            Uteis.logarDebug("Erro para iniciarVerificacaoPagamentosPendentesFiserv - " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void abrirPinpadFiserv(String codigoEmpresaFiserv) {
        try {
            JSONObject jsonObject = TefFiservService.montarObjetoVerificacaoCobrancaFiserv(codigoEmpresaFiserv, getUsuarioLogado());
            String json = jsonObject.toString();
            System.out.println("json: " + json);

            setMsgAlert("Richfaces.showModalPanel('modalVerificacaoPagamentosPendentesFiserv');verificarPendentes(" + json + ", true);"); //verificarPendentes(json, true); é a função JavaScript que chama o pinpad no arquivo include_pinpad_fiserv.jsp
        } catch (Exception ex) {
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public String getStringRetornoVerificacaoTransacoesPendentesFiserv() {
        return stringRetornoVerificacaoTransacoesPendentesFiserv;
    }

    public void setStringRetornoVerificacaoTransacoesPendentesFiserv(String stringRetornoVerificacaoTransacoesPendentesFiserv) {
        this.stringRetornoVerificacaoTransacoesPendentesFiserv = stringRetornoVerificacaoTransacoesPendentesFiserv;
    }

    public String getOnCompletePinpad() {
        if (onCompletePinpad == null) {
            onCompletePinpad = "";
        }
        return onCompletePinpad;
    }

    public void setOnCompletePinpad(String onCompletePinpad) {
        this.onCompletePinpad = onCompletePinpad;
    }

}
