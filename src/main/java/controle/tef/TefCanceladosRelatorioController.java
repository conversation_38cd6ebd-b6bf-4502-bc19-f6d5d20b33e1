package controle.tef;


import com.sun.xml.ws.util.StringUtils;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.TefCancelamentosTO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.List;

public class TefCanceladosRelatorioController extends SuperControle {

    private TefCancelamentosTO cancelTOSelecionado;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();
    private boolean exibirModalParametros = false;
    private boolean exibirPix = false;
    private String dadosConcatenados;

    public TefCancelamentosTO getCancelTOSelecionado() {
        return cancelTOSelecionado;
    }

    public void setCancelTOSelecionado(TefCancelamentosTO cancelTOSelecionado) {
        this.cancelTOSelecionado = cancelTOSelecionado;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public boolean isExibirModalParametros() {
        return exibirModalParametros;
    }

    public void setExibirModalParametros(boolean exibirModalParametros) {
        this.exibirModalParametros = exibirModalParametros;
    }
    public void fecharPanelDadosParametros() {
        this.setExibirModalParametros(false);
        this.setExibirPix(false);
    }

    public boolean isExibirPix() {
        return exibirPix;
    }

    public void setExibirPix(boolean exibirPix) {
        this.exibirPix = exibirPix;
    }

    public void exibirParams(ActionEvent evt) {
        String params = (String) evt.getComponent().getAttributes().get("params");
        TefCancelamentosTO tefCanceladoTo = (TefCancelamentosTO) evt.getComponent().getAttributes().get("atributosItemTefCancelado");
        cancelTOSelecionado = null;
        listaParametrosSelecionado = null;
        if (params != null && tefCanceladoTo != null) {
            cancelTOSelecionado = tefCanceladoTo;
            setExibirModalParametros(true);
            setMensagemDetalhada("");
            setMensagem("");
            if (params.equals("envio")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(cancelTOSelecionado.getParamsenvio());
                } catch (Exception e) {
                    setMensagem("Erro ao obter detalhes do envio!");
                    setMensagemDetalhada(e.getMessage());
                }
            } else if (params.equals("resposta")) {
                try {
                    if (cancelTOSelecionado.getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.GETCARD)) {
                        listaParametrosSelecionado = Uteis.obterListaParametrosValores(cancelTOSelecionado.getParamsrespcancel());
                    } else if (cancelTOSelecionado.getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.FISERV)) {
                        listaParametrosSelecionado = obterListaParametrosValoresFiserv(cancelTOSelecionado.getParamsrespcancel());
                    }
                }catch (Exception e) {
                    setMensagem("Erro ao obter resposta da administradora, necessário reenviar esta transação!");
                    setMensagemDetalhada(e.getMessage());
                }
            }
        }
    }

    public void tratarRecibo(ActionEvent evt) {
        limparMsg();
        setMsgAlert("");
        String params = (String) evt.getComponent().getAttributes().get("params");

        TefCancelamentosTO tefCanceladosTo = (TefCancelamentosTO) evt.getComponent().getAttributes().get("atributosItemTefCancelado");
        cancelTOSelecionado = tefCanceladosTo;

        if (params != null && tefCanceladosTo != null) {
            if (tefCanceladosTo.getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.FISERV)) {
                try {
                    String comprovanteCancelamentoFiserv = obterComprovanteCancelamentoFiserv(tefCanceladosTo.getParamsrespcancel());
                    dadosConcatenados = comprovanteCancelamentoFiserv;
                } catch (Exception e) {
                    montarErro("Erro ao obter recibo!");
                }
            } else if (tefCanceladosTo.getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.GETCARD)) {
                try {
                    JSONObject jsonObject = new JSONObject(tefCanceladosTo.getParamsrespcancel());
                    dadosConcatenados = jsonObject.getJSONObject("response").getString("CupomCliente");
                } catch (Exception e) {
                    montarErro("Erro ao obter recibo!");
                }
            }

            if (params.equals("enviarEmail")) {
                try {
                    UteisEmail uteisEmail = new UteisEmail();
                    PessoaVO pessoa = getFacade().getPessoa().consultarPorChavePrimaria(tefCanceladosTo.getPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    String nomeEmpresa = StringUtils.capitalize(getEmpresaLogado().getNome().toLowerCase());

                    uteisEmail.novo(("Comp. Cancelamento " + getNomeAdquirente() + " - " + pessoa.getNome() + " - " + nomeEmpresa).toUpperCase(), getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS));
                    uteisEmail.setRemetente(getUsuarioLogado());

                    List<EmailVO> emails = getFacade().getEmail().consultarEmails(tefCanceladosTo.getPessoa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (emails != null && !emails.isEmpty()) {
                        emails.forEach(email -> {
                            try {
                                uteisEmail.enviarEmail(email.getEmail(), pessoa.getNome(), getHtmlEmail(), nomeEmpresa.toUpperCase());
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
                        montarSucessoGrowl("E-mail Enviado com Sucesso!");
                    }
                } catch (Exception ex) {
                    montarErro("Erro ao enviar email!");
                }
            }
        }
    }

    public String getHtmlComprovante() {
        return dadosConcatenados;
    }

    public String getNomeAdquirente() {
        if (cancelTOSelecionado != null) {
            return cancelTOSelecionado.getOpcoesPinpadEnum().getNome();
        }
        return "";
    }

    public String getHtmlEmail() {
        return "<html>\n" +
                "<head>\n" +
                "    <title>Comprovante de Cancelamento " + getNomeAdquirente() + "</title>\n" +
                "    <style>\n" +
                "\n" +
                "        body {\n" +
                "            font-family: Arial, sans-serif;\n" +
                "        }\n" +
                "\n" +
                "        .comprovante {\n" +
                "            border: 1px solid #ccc;\n" +
                "            padding: 20px;\n" +
                "            width: 400px;\n" +
                "            margin: auto;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "<div class='comprovante'><h2 style=\"text-align: center\">Comprovante de Cancelamento " + getNomeAdquirente() + "</h2>\n" +
                "    <pre style=\"margin-left: 65px\">"+dadosConcatenados+"</pre>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>";
    }

    private List<ObjetoGenerico> obterListaParametrosValoresFiserv(String jsonStr) throws Exception {
        List<ObjetoGenerico> listaRetornar = new ArrayList<ObjetoGenerico>();

        // 1. Remover aspas duplas do início e do fim
        if (jsonStr.startsWith("\"") && jsonStr.endsWith("\"")) {
            jsonStr = jsonStr.substring(1, jsonStr.length() - 1);
        }
        // 2. Substituir ocorrências de \" por " (isso converte as aspas escapadas para aspas normais)
        jsonStr = jsonStr.replace("\\\"", "\"");
        // 3. Converter a String para JSONArray
        JSONArray jsonArray = new JSONArray(jsonStr);
        // 4. Iterar sobre o JSONArray e converter cada elemento em ObjetoGenerico
        for (int i = 0; i < jsonArray.length(); i++) {
            listaRetornar.addAll(Uteis.obterListaParametrosValores(jsonArray.get(i).toString()));
        }

        return listaRetornar;
    }

    private String obterComprovanteCancelamentoFiserv(String retorno) {
        String canhotoAdquirente = "";

        // Encontrar o valor para TipoCampo 121
        // Comprovante de pagamento da Adquirente (Canhoto que geralmetne imprime no equipamento)
        // Setar esse valor no campo Observação do Recibo, sendo uma exigência da Fiserv
        int index121 = (retorno.indexOf("{\\\"TipoCampo\\\": 121, \\\"Valor\\\": \\\"") + 34);
        int end121 = retorno.indexOf("\\\"", index121);
        canhotoAdquirente = retorno.substring(index121, end121).trim();
        canhotoAdquirente = canhotoAdquirente.replace("\\\\n", "\n");

        return canhotoAdquirente;
    }

}
