/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.basico;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.plano.PlanoTextoPadrao;
import negocio.facade.jdbc.plano.Produto;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/*
 * <AUTHOR> Felipe
 */
public class EnvioEmailContratoReciboControle extends SuperControle {

    private ContratoVO contratoVO;
    private VendaAvulsaVO vendaAvulsaVO;
    private List<MovPagamentoVO> pagamentoVOs;
    private List<EmailVO> listaEmailsEnviar;
    private String novoEmailEnviar;
    private boolean envioDeContrato = false;
    private boolean envioDeContratoProduto = false;
    private boolean reciboColaborador = false;
    private boolean reciboPagamento = false;
    private OrcamentoVO orcamentoVO;
    private AulaAvulsaDiariaVO aulaAvulsaDiariaVO;

    private Boolean enviarContratoParaAssinatura = false;

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public OrcamentoVO getOrcamentoVO() {
        if(orcamentoVO == null){
            orcamentoVO = new OrcamentoVO();
        }
        return orcamentoVO;
    }

    public void setOrcamentoVO(OrcamentoVO orcamentoVO) {
        this.orcamentoVO = orcamentoVO;
    }


    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public List<MovPagamentoVO> getPagamentoVOs() {
        if (pagamentoVOs == null) {
            pagamentoVOs = new ArrayList<MovPagamentoVO>();
        }
        return pagamentoVOs;
    }

    public void setPagamentoVOs(List<MovPagamentoVO> pagamentoVOs) {
        this.pagamentoVOs = pagamentoVOs;
    }

    public void adicionarEmailEnviar() throws Exception {
        try {
            if (!UteisValidacao.validaEmail(getNovoEmailEnviar())) {
                throw new Exception("Informe o e-mail corretamente!");
            }
            EmailVO novoEmail = new EmailVO();
            novoEmail.setEmailCorrespondencia(true);
            novoEmail.setEmail(getNovoEmailEnviar());
            novoEmail.setPermiteRemover(true);
            getListaEmailsEnviar().add(novoEmail);

            setNovoEmailEnviar("");
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public String enviarOrcamento() {
        String retorno = "";
        try {
            ModeloOrcamentoVO modeloOrcamento = getFacade().getModeloOrcamento().consultarPorChavePrimaria(getOrcamentoVO().getModeloOrcamento(), Uteis.NIVELMONTARDADOS_TODOS);
            ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(getOrcamentoVO().getCliente(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();

            if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
                throw new Exception("Configure o servidor de envio de e-mail!");
            }

            if (UteisValidacao.emptyList(getListaEmailsEnviar())) {
                throw new Exception("Adicione um e-mail para enviar o orçamento!");
            }

            StringBuilder email = new StringBuilder();
            List<EmailVO> emailVOList = getListaEmailsEnviar();
            if (emailVOList.size() > 0) {
                int qtdEmails = emailVOList.size();
                for (int i = 0; i < qtdEmails; i++) {
                    Object obj = emailVOList.get(i);
                    if (obj instanceof EmailVO) {
                        EmailVO emailV = (EmailVO) obj;
                        if (email.toString().isEmpty()) {
                            email.append(emailV.getEmail());
                        } else {
                            email.append(";").append(emailV.getEmail());
                        }
                    }
                }
            }
            String[] emails = email.toString().split(";");

            enviarOrcamento(getOrcamentoVO(), emails, modeloOrcamento, cliente, configuracaoSistemaCRMVO);
            setSucesso(true);
            setErro(false);
            retorno = "Orçamento enviado com sucesso para os e-mail(s): " + email.toString();
            HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
            historicoContatoVO.setDia(Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            historicoContatoVO.setObservacao(retorno);
            historicoContatoVO.setResponsavelCadastro(getUsuarioLogado());
            historicoContatoVO.setResultado("Envio de E-mail do orçamento "+ getOrcamentoVO().getCodigo());
            getContratoVO().setCliente(getFacade().getCliente().consultarPorCodigoPessoa(cliente.getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            historicoContatoVO.setClienteVO(cliente);
            historicoContatoVO.setContatoAvulso(false);
            historicoContatoVO.setTipoContato("EM");
            getFacade().getHistoricoContato().incluir(historicoContatoVO);
            setMensagemDetalhada(retorno);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            retorno = e.getMessage();
            setMensagem(retorno);
        }
        return retorno;
    }


    public void removerEmailEnviar() throws Exception {
        EmailVO obj = (EmailVO) context().getExternalContext().getRequestMap().get("emailsEnviar");
        int index = 0;
        Iterator i = getListaEmailsEnviar().iterator();
        while (i.hasNext()) {
            EmailVO objExistente = (EmailVO) i.next();
            if (objExistente.getEmail().equals(obj.getEmail())) {
                getListaEmailsEnviar().remove(index);
                return;
            }
            index++;
        }
        setSucesso(true);
        setErro(false);
        setMensagemID("msg_dados_excluidos");
    }

    public String enviarContrato() throws Exception {
        return enviarContrato(false, false, null, getUsuarioLogado(), null);
    }

    public String enviarContrato(boolean enviarParaAssinar, boolean exception,
                                 HttpServletRequest request, UsuarioVO usuarioVO, Integer aditivo) throws Exception {
        String retorno = "";
        try {
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();

            if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
                throw new Exception("Configure o servidor de envio de e-mail!");
            }

            if (UteisValidacao.emptyList(getListaEmailsEnviar())) {
                throw new Exception("Adicione um e-mail para enviar o contrato!");
            }

            StringBuilder email = new StringBuilder();
            List<EmailVO> emailVOList = getListaEmailsEnviar();
            if (emailVOList.size() > 0) {
                int qtdEmails = emailVOList.size();
                for (int i = 0; i < qtdEmails; i++) {
                    Object obj = emailVOList.get(i);
                    if (obj instanceof EmailVO) {
                        EmailVO emailV = (EmailVO) obj;
                        if (email.toString().isEmpty()) {
                            email.append(emailV.getEmail());
                        } else {
                            email.append(";").append(emailV.getEmail());
                        }
                    }
                }
            }
            String[] emails = email.toString().split(";");

            enviarContrato(emails, getContratoVO(), getPagamentoVOs(), configuracaoSistemaCRMVO , enviarParaAssinar, request, usuarioVO, aditivo);
            setSucesso(true);
            setErro(false);
            retorno = "Contrato enviado com sucesso para os e-mail(s): " + email.toString();

            HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
            historicoContatoVO.setDia(Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            historicoContatoVO.setObservacao(retorno);
            historicoContatoVO.setResponsavelCadastro(usuarioVO);
            historicoContatoVO.setResultado("Envio de E-mail do contrato "+ getContratoVO().getCodigo());
            getContratoVO().setCliente(getFacade().getCliente().consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            historicoContatoVO.setClienteVO(contratoVO.getCliente());
            historicoContatoVO.setContatoAvulso(false);
            historicoContatoVO.setTipoContato("EM");
            getFacade().getHistoricoContato().incluir(historicoContatoVO);
            setMensagemDetalhada(retorno);
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            retorno = e.getMessage();
            setMensagem(retorno);
            if (exception) {
                throw e;
            }
        }
        return retorno;
    }

    public String enviarContratoProduto(boolean exception,
                                 HttpServletRequest request, UsuarioVO usuarioVO) throws Exception {
        String retorno = "";
        try {
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();

            if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
                throw new Exception("Configure o servidor de envio de e-mail!");
            }

            if (UteisValidacao.emptyList(getListaEmailsEnviar())) {
                throw new Exception("Adicione um e-mail para enviar o contrato!");
            }

            StringBuilder email = new StringBuilder();
            List<EmailVO> emailVOList = getListaEmailsEnviar();
            if (emailVOList.size() > 0) {
                int qtdEmails = emailVOList.size();
                for (int i = 0; i < qtdEmails; i++) {
                    Object obj = emailVOList.get(i);
                    if (obj instanceof EmailVO) {
                        EmailVO emailV = (EmailVO) obj;
                        if (email.toString().isEmpty()) {
                            email.append(emailV.getEmail());
                        } else {
                            email.append(";").append(emailV.getEmail());
                        }
                    }
                }
            }
            String[] emails = email.toString().split(";");

            enviarContratoProduto(emails, getVendaAvulsaVO(), configuracaoSistemaCRMVO , request, usuarioVO);
            setSucesso(true);
            setErro(false);
            retorno = "Contrato de produto enviado com sucesso para os e-mail(s): " + email;
            HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
            historicoContatoVO.setDia(Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            historicoContatoVO.setObservacao(retorno);
            historicoContatoVO.setResponsavelCadastro(usuarioVO);
            historicoContatoVO.setResultado("Envio de E-mail do contrato "+ getContratoVO().getCodigo());
            getContratoVO().setCliente(getFacade().getCliente().consultarPorCodigoPessoa(vendaAvulsaVO.getParcela().getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            historicoContatoVO.setClienteVO(vendaAvulsaVO.getCliente());
            historicoContatoVO.setContatoAvulso(false);
            historicoContatoVO.setTipoContato("EM");
            getFacade().getHistoricoContato().incluir(historicoContatoVO);
            setMensagemDetalhada(retorno);
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            retorno = e.getMessage();
            setMensagem(retorno);
            if (exception) {
                throw e;
            }
        }
        return retorno;
    }

    public void prepararContratoEPagamento(ContratoVO obj) throws Exception {
        ContratoVO c = getFacade().getContrato().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
        setContratoVO(c);
        // setar atributos do contrato
        getContratoVO().setMovParcelaVOs(getFacade().getMovParcela().consultarPorContratoNaoRenegociadaNegociada(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getContratoVO().setContratoTextoPadrao(getFacade().getContratoTextoPadrao().consultarPorCodigoContrato(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getContratoVO().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getContratoVO().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getContratoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getContratoVO().setContratoDuracao(getFacade().getContratoDuracao().consultarContratoDuracoes(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (obj.isVendaCreditoTreino()){
            getContratoVO().getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(getContratoVO().getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        getContratoVO().setContratoHorario(getFacade().getContratoHorario().consultarContratoHorarios(getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getContratoVO().setContratoModalidadeVOs(getFacade().getContratoModalidade().consultarContratoModalidades(getContratoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        getContratoVO().setResponsavelContrato(getFacade().getUsuario().consultarPorChavePrimaria(getContratoVO().getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getContratoVO().getPlano().setPlanoTextoPadrao(getContratoVO().getContratoTextoPadrao().getPlanoTextoPadrao());
        // pesquisar pagamentos já efetuados para informar no contrato.
        setPagamentoVOs(getFacade().getMovPagamento().consultarPagamentoDeUmContrato(getContratoVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TODOS));
    }

    public void prepararContratoProdutoEPagamento(String chave, Integer codProduto, Connection con, HttpServletRequest request) throws Exception {
        getVendaAvulsaVO().setParcela(UteisValidacao.emptyList(getVendaAvulsaVO().getMovParcelaVOs()) ? new MovParcelaVO() : getVendaAvulsaVO().getMovParcelaVOs().get(0));
        ProdutoVO prod = getFacade().getProduto().consultarPorChavePrimaria(codProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getVendaAvulsaVO().setTextoPadrao(getFacade().getPlanoTextoPadrao().consultarPorChavePrimaria(prod.getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_TODOS));
        String contratoEpoca = getFacade().getVendaAvulsa().obterContratoProdutoTextoPadrao(getVendaAvulsaVO().getCodigo(), prod.getCodigo());
        if (!UteisValidacao.emptyString(contratoEpoca)) {
            getVendaAvulsaVO().getTextoPadrao().setTexto(contratoEpoca);
        }
        getVendaAvulsaVO().getParcela().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(
                getVendaAvulsaVO().getParcela().getPessoa().getCodigo(),
                Uteis.NIVELMONTARDADOS_TODOS));
        getVendaAvulsaVO().setMovProdutoVOs(getFacade().getMovProduto().consultarPorCodigoParcela(getVendaAvulsaVO().getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getVendaAvulsaVO().setEmpresa(
                getFacade().getEmpresa().consultarPorChavePrimaria(
                        getVendaAvulsaVO().getEmpresa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS));
        getVendaAvulsaVO().setResponsavel(
                getFacade().getUsuario().consultarPorChavePrimaria(
                        getVendaAvulsaVO().getResponsavel().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        List<MovPagamentoVO> pagamentos = getFacade().getMovPagamento().consultarPagamentoDeUmaParcela(
                getVendaAvulsaVO().getParcela().getCodigo().intValue(), false,
                Uteis.NIVELMONTARDADOS_TODOS);

        getVendaAvulsaVO().getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(chave, con, getVendaAvulsaVO(), null, pagamentos, getVendaAvulsaVO().getEmpresa().getDescMoeda(), request, true, false, prod.getCodigo());
    }

    public void prepararContratoProdutoEPagamentoDiaria(String chave, Integer codProduto, Connection con, HttpServletRequest request) throws Exception {
        MovParcela movParcelaDAO;
        Produto produtoDAO;
        PlanoTextoPadrao planoTextoPadraoDAO;
        AulaAvulsaDiaria aulaAvulsaDiariaDAO;
        Pessoa pessoaDAO;
        MovProduto movProdutoDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        MovPagamento movPagamentoDAO;

        try {
            movParcelaDAO = new MovParcela(con);
            produtoDAO = new Produto(con);
            planoTextoPadraoDAO = new PlanoTextoPadrao(con);
            aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
            pessoaDAO = new Pessoa(con);
            movProdutoDAO = new MovProduto(con);
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            movPagamentoDAO = new MovPagamento(con);

            getAulaAvulsaDiariaVO().setMovParcelaVOs(movParcelaDAO.consultarPorCodigoAulaAvulsaDiariaLista(getAulaAvulsaDiariaVO().getCodigo(), null, false, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getAulaAvulsaDiariaVO().setParcela(UteisValidacao.emptyList(getAulaAvulsaDiariaVO().getMovParcelaVOs()) ? new MovParcelaVO() : getAulaAvulsaDiariaVO().getMovParcelaVOs().get(0));
            ProdutoVO prod = produtoDAO.consultarPorChavePrimaria(codProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getAulaAvulsaDiariaVO().setTextoPadrao(planoTextoPadraoDAO.consultarPorChavePrimaria(prod.getContratoTextoPadrao(), Uteis.NIVELMONTARDADOS_TODOS));
            String contratoEpoca = aulaAvulsaDiariaDAO.obterContratoProdutoTextoPadraoDiaria(getAulaAvulsaDiariaVO().getCodigo(), prod.getCodigo());
            if (!UteisValidacao.emptyString(contratoEpoca)) {
                getAulaAvulsaDiariaVO().getTextoPadrao().setTexto(contratoEpoca);
            }
            getAulaAvulsaDiariaVO().getParcela().setPessoa(pessoaDAO.consultarPorChavePrimaria(
                    getAulaAvulsaDiariaVO().getParcela().getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_TODOS));
            getAulaAvulsaDiariaVO().setMovProdutoVOs(movProdutoDAO.consultarPorCodigoParcela(getAulaAvulsaDiariaVO().getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getAulaAvulsaDiariaVO().setEmpresa(
                    empresaDAO.consultarPorChavePrimaria(
                            getAulaAvulsaDiariaVO().getEmpresa().getCodigo(),
                            Uteis.NIVELMONTARDADOS_TODOS));
            getAulaAvulsaDiariaVO().setResponsavel(
                    usuarioDAO.consultarPorChavePrimaria(
                            getAulaAvulsaDiariaVO().getResponsavel().getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            List<MovPagamentoVO> pagamentos = movPagamentoDAO.consultarPagamentoDeUmaParcela(
                    getAulaAvulsaDiariaVO().getParcela().getCodigo().intValue(), false,
                    Uteis.NIVELMONTARDADOS_TODOS);

            getAulaAvulsaDiariaVO().getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(chave, con, null, getAulaAvulsaDiariaVO(), pagamentos, getAulaAvulsaDiariaVO().getEmpresa().getDescMoeda(), request, true, false, prod.getCodigo());
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            movParcelaDAO = null;
            produtoDAO = null;
            planoTextoPadraoDAO = null;
            aulaAvulsaDiariaDAO = null;
            pessoaDAO = null;
            movProdutoDAO = null;
            empresaDAO = null;
            usuarioDAO = null;
            movPagamentoDAO = null;
        }
    }

    public void prepararListaEmail(Integer pessoa, boolean envioDeContrato) throws Exception {
        try {
            setNovoEmailEnviar("");
            setMensagem("");
            setSucesso(false);
            setErro(false);
            setEnvioDeContrato(envioDeContrato);
            setListaEmailsEnviar(getFacade().getEmail().consultarEmails(pessoa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            setMensagem(e.getMessage());
        }
    }

    public String getNovoEmailEnviar() {
        if (novoEmailEnviar == null) {
            novoEmailEnviar = "";
        }
        return novoEmailEnviar;
    }

    public void setNovoEmailEnviar(String novoEmailEnviar) {
        this.novoEmailEnviar = novoEmailEnviar;
    }

    public List<EmailVO> getListaEmailsEnviar() {
        if (listaEmailsEnviar == null) {
            listaEmailsEnviar = new ArrayList<EmailVO>();
        }
        return listaEmailsEnviar;
    }

    public void setListaEmailsEnviar(List<EmailVO> listaEmailsEnviar) {
        this.listaEmailsEnviar = listaEmailsEnviar;
    }

    public boolean isEnvioDeContrato() {
        return envioDeContrato;
    }

    public void setEnvioDeContrato(boolean envioDeContrato) {
        this.envioDeContrato = envioDeContrato;
    }

    public boolean isReciboColaborador() {
        return reciboColaborador;
    }

    public void setReciboColaborador(boolean reciboColaborador) {
        this.reciboColaborador = reciboColaborador;
    }

    public boolean isReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(boolean reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public Boolean getEnviarContratoParaAssinatura() {
        return enviarContratoParaAssinatura;
    }

    public void setEnviarContratoParaAssinatura(Boolean enviarContratoParaAssinatura) {
        this.enviarContratoParaAssinatura = enviarContratoParaAssinatura;
    }

    public VendaAvulsaVO getVendaAvulsaVO() {
        return vendaAvulsaVO;
    }

    public void setVendaAvulsaVO(VendaAvulsaVO vendaAvulsaVO) {
        this.vendaAvulsaVO = vendaAvulsaVO;
    }

    public boolean isEnvioDeContratoProduto() {
        return envioDeContratoProduto;
    }

    public void setEnvioDeContratoProduto(boolean envioDeContratoProduto) {
        this.envioDeContratoProduto = envioDeContratoProduto;
    }

    public AulaAvulsaDiariaVO getAulaAvulsaDiariaVO() {
        return aulaAvulsaDiariaVO;
    }

    public void setAulaAvulsaDiariaVO(AulaAvulsaDiariaVO aulaAvulsaDiariaVO) {
        this.aulaAvulsaDiariaVO = aulaAvulsaDiariaVO;
    }
}
