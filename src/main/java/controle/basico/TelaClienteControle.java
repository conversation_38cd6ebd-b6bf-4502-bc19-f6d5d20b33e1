/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoContratoTO;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estudio.controle.ClienteEstudioControle;
import br.com.pactosolucoes.integracao.conciliadora.LogConciliadoraVO;
import br.com.pactosolucoes.linhatempocontrato.controle.LinhaTempoContratoControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.totalpass.ApiTotalPass;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.contrato.CancelamentoContratoControle;
import controle.contrato.ContratoControle;
import controle.contrato.MovProdutoControle;
import controle.crm.HistoricoContatoControle;
import controle.crm.MetaCRMControle;
import controle.financeiro.BoletoBancarioControle;
import controle.financeiro.GestaoRemessasControle;
import controle.financeiro.MovParcelaControle;
import controle.plano.OrcamentoControle;
import negocio.armario.AluguelArmarioVO;
import negocio.comuns.arquitetura.LogTotalPassVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.OpcoesNomenclaturaVendaCreditoEnum;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.AtestadoVO;
import negocio.comuns.contrato.CancelamentoContratoVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.contrato.UtilizacaoAvaliacaoFisicaVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;
import negocio.comuns.crm.*;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.pactoprint.CarteirinhaClienteVO;
import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.controle.basico.HistoricoPontosParceiroFidelidadeControle;
import relatorio.controle.basico.ListaAcessoControleRel;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.boleto.asaas.BoletoAsaasService;
import servicos.impl.boleto.asaas.CobrancaAsaasRetornoDTO;
import servicos.integracao.botconversa.BotConversaController;
import servicos.integracao.enumerador.IntegracoesEnum;
import servicos.integracao.gymbotpro.GymbotProController;
import servicos.integracao.impl.conciliadora.ConciliadoraServiceImpl;
import servicos.integracao.impl.parceirofidelidade.dotz.ParceiroFidelidadeAPIDotzImpl;
import servicos.integracao.impl.parceirofidelidade.to.RetornoParceiroTO;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.integracao.sms.Message;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.pix.PixAsaas;
import servicos.pix.PixPagamentoService;
import servicos.pix.PixStatusEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servicos.vendasonline.VendasOnlineService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class TelaClienteControle extends ClienteControle {
    private static final String LISTA_COMPRAS = "LISTA_COMPRAS";
    private static final String LISTA_PARCELAS = "LISTA_PARCELAS";
    private static final String LISTA_PAGAMENTOS = "LISTA_PAGAMENTOS";
    private static final String LISTA_BOLETOS = "LISTA_BOLETOS";
    private static final String LISTA_REMESSA = "LISTA_REMESSA";
    private static final String LISTA_TRANSACOES = "LISTA_TRANSACOES";
    private static final String LISTA_TRANSACOES_VERIFICACAO = "LISTA_TRANSACOES_VERIFICACAO";
    private static final String LISTA_OBSERVACAO = "LISTA_OBSERVACAO";
    private static final String LISTA_PIX = "LISTA_PIX";
    private static final String LISTA_GETCARD = "LISTA_GETCARD";
    private static final String LISTA_BOLETO_GERAL = "LISTA_BOLETO_GERAL";
    private static final String LISTA_MOV_CONTA = "LISTA_MOV_CONTA";
    private static final Integer LISTA_PAGINADA_LIMIT = 6;
    private static final Integer LISTA_PAGINADA_OBSERVACAO_LIMIT = 4;
    private static final String AUTORIZADO_TOTALPASS = "204";
    private static final String BLOQUEADO_TOTALPASS_CHECKIN = "422";

    private ClienteVO cliente;
    private ClienteVO clienteTitular;
    private List<ContratoVO> listaContratosCliente;
    private boolean carregarFinanceiro = true;
    private boolean carregarRelacionamento = true;
    private boolean carregarCentral = true;
    private boolean carregarEstudio = true;
    private boolean carregarNotasFiscais = true;
    private List<MovProdutoVO> listaHistoricoProduto;
    private List<MovParcelaVO> listaHistoricoParcelas;
    private List<MovPagamentoVO> listaHistoricoPagamentos;
    private List<HistoricoContatoVO> listaHistoricoContato;
    private List<NotasFiscaisTO> listaNotasFiscais;
    private List<ClienteMensagemVO> avisos = new ArrayList<>();
    private List<ClienteMensagemVO> avisosModal = new ArrayList<>();
    private Boolean permitirAlteracaoMensagemObservacao;
    private ContratoVO contratoSelecionado = null;
    private ContratoVO contratoImpressao = null;
    private List<ChequeVO> cheques;
    private boolean graficosAcessoCarregado = false;
    private MalaDireta malaDiretaDAO;
    private boolean financeiroContrato = false;
    private String mensagemParq;
    private String retornoLiberacaoDeVaga;
    private List<ConviteAulaExperimentalVO> convites = new ArrayList<>();
    private List<SelectItem> tiposConvites = new ArrayList<>();
    private Integer codigoTipoConvite;
    private List<MovProdutoVO> listaProdutosComValidade;
    private Map<String, Boolean> mapaMostrar = new HashMap<>();
    private List<HistoricoContratoVO> listaHistoricoContrato;
    private List<NegociacaoEventoContratoTO> eventos;
    private List<ContratoOperacaoVO> listaContratoOperacao;
    private Date dataUltimoAcesso;
    private boolean mostrarColunaCupomDesconto = false;
    private boolean debitoContaCorrente;
    private boolean permitirTransferenciaCredito;
    private boolean trocaPlano = false;
    private boolean apresentarTopo = true;
    private ControleCreditoTreinoVO controleCreditoTreinoVO;
    private ConfiguracaoSistemaVO configuracaoPlanoCreditoTreino = null;
    private boolean temContratoNaEmpresaAtual = false;
    private boolean apresentarEmpresa = false;
    private Integer totalPresencaContratoCredito = 0;
    private Integer totalFaltaContratoCredito = 0;
    private List<SelectItem> listaOperacaoCreditoTreino;
    private Integer codigoOperacaoCreditoSelecionado = TipoOperacaoCreditoTreinoEnum.CODIGO_TODOS;
    private boolean aulasMarcadasComCreditoExtra = false;
    private boolean aplicouFiltroExtratoCreditoTreino = false;
    private Date dataInicioOperacaoExtratoCredito;
    private Date dataFimOperacaoExtratoCredito;
    private boolean apresentarAtualisarBV = false;
    private boolean apresentaMudancaPlano = true;
    private boolean contratoVigenteMudancaPlano = true;
    private boolean contratoRenovadoMudancaPlano = false;
    private ListaPaginadaTO listaCompras;
    private ListaPaginadaTO listaParcelas;
    private ListaPaginadaTO listaPagamentos;
    private ListaPaginadaTO listaRemessa;
    private ListaPaginadaTO listaBoleto;
    private ListaPaginadaTO listaTrasacoes;
    private ListaPaginadaTO listaTransacoesVerificacao;
    private ListaPaginadaTO listaPix;
    private ListaPaginadaTO listaCancelamentoGetCard;
    private ListaPaginadaTO listaMovConta;
    private ListaPaginadaTO listaBoletoGeral;
    private ListaPaginadaTO listaClienteObservacao;
    private boolean apresentarRenovavelAutomaticamente = false;
    private boolean contratoRenovarAutomaticamente = false;
    private ContratoAssinaturaDigitalVO docs = new ContratoAssinaturaDigitalVO();
    private boolean apresentarBotaoGrvControleCredito = false;
    private boolean alterarClienteObersacao = false;
    private boolean apresentarAlteracao = false;
    private ClienteObservacaoVO clienteObervacaoParametroExclusao = new ClienteObservacaoVO();
    private String retornoListaModal = "";
    private String retornoOncompleteListaObservacao = "";
    private Boolean exibirConvites = false;
    private Integer convitesDireito = 0;

    private Map<String, String> statusProtheus;

    private ClienteObservacaoVO oservacaoParaVisualizacao = new ClienteObservacaoVO();
    private String onCompleteVisualizar;
    private Integer valorPontuacao;

    private File arquivoCliente;

    private String extensaoArquivoCliente;

    private boolean mostrarBotaoGravarAnexo = false;

    private String onCompleteAnexo;

    private String nomeAnexoCliente;

    private AtestadoVO ultimoAtestadoCliente;
    private NotasFiscaisTO notasFiscaisTOSelecionada;
    private String onCompleteNota;

    private String nomeClienteObjecao = "";
    private String objecao = "";
    private boolean apresentarEstornoCancelamento = false;
    private int diasVigenciaDeContratosAtivos;
    private PlanoVO planoParaTransferencia;
    private ContratoVO novoContratoParaTransferenciaDePlano;
    private Integer diaVencimentoParcelasAlterado;
    private List<SelectItem> listaDiaVencimentoParcelas = new ArrayList<>();
    private ContratoVO contratoParaMudancaDiaVencimentoParcela;
    private boolean permiteAlterarDataVencimentoParcelas = false;
    private boolean permiteLiberarCobrancaDataVencimentoParcelas = false;
    private boolean liberarValorProRataVencimentoParcelas = false;
    private String mensagemAlteracaoDatavencimentoParcelas = "";
    private Double valorProRataAlteracaoDataVencimentoParcelas = 0.0;

    private int diferencaDiasNovoVencimento = 0;

    private boolean alterarMesProximaParcelaEmAberto = false;
    private List<LogConciliadoraVO> listaLogConciliadora;
    private int diaVencimentoCartao;
    private Boolean compartilharLink = false;
    private ModeloOrcamentoVO modeloOrcamento;

    //INTEGRAÇÃO GYMPASS
    private String onCompleteGymPass;
    private List<PeriodoAcessoClienteVO> listaGymPass;
    private String tokemGYMPASS = "";
    private List<LogTotalPassVO> listaTotalPass;
    private String onCompleteBloqueioCobrancas;
    private boolean apresentarBloqueioParcelas = false;
    private String onCompleteGenerico;
    private String tokemTOTALPASS = "";
    private String onCompleteTotalPass;
    private RemessaItemVO remessaItemVORemover;
    private boolean cobrarParcelasEmAberto = true;
    private boolean cadastrarCartaoOnline = false;
    private List<IndicadoVO> listaIndicacoesAmigoFit = new ArrayList<>();
    private List<UtilizacaoAvaliacaoFisicaVO> utilizacoes;
    private boolean exibirTodasModalidades = false;
    private boolean exibBotConversa = false;
    private List<String> modalidadesContrato = new ArrayList<>();
    private int totalModalidadesContrato = 0;
    private List<PixVO> listaPixVo;
    private List<CancelamentoGetCardTO> listaCancelamentoGetCardVo;
    private List<BoletoVO> listaBoletoGeralVo;
    private List<MovContaVO> listaMovContaVo;
    private boolean apresentarDiaPlus = false;
    private boolean apresentarDayUse = false;
    private String onCompleteBoleto;
    private String onCompletePix;
    private BoletoVO boletoVOSelecionado;
    private boolean boletoCancelarSelecionados = false;
    //MGB
    private String nivelMGB = null;
    private boolean marcarTodosBoleto = false;

    private String fileNameNotaSesi;
    private Integer saldoCreditosProcessado = 0;
    private Integer marcacoesFuturas = 0;
    private String hintSPC;

    private boolean possuiPermissaoVerCacBiLtv = false;
    private String dataMatriculaPorExtenso;

    private String dataMatriculaPorExtensoAteFimContato;
    private CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTODetalhar;
    private boolean exibirModalParametros = false;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();

    private MovProdutoVO movProdutoVOValidade;

    private boolean exibirEstornoContrato;
    private boolean novaNegociacaoPadrao = false;
    private boolean novaTelaAlunoPadrao = false;
    private boolean novaTelaVendaAvulsa = false;
    private boolean novaTelaAlunoPadraoEmpresa = false;
    private boolean novaVendaAvulsaPadraoEmpresa = false;
    private List<CarteirinhaClienteVO> listaCarteirinhas = new ArrayList<>();

    //Gymbot Pro
    private boolean exibeGymbotPro = false;

    public boolean isExibeGymbotPro() {
        return exibeGymbotPro;
    }

    public void setExibeGymbotPro(boolean exibeGymbotPro) {
        this.exibeGymbotPro = exibeGymbotPro;
    }


    public boolean isExibBotConversa() {
        return exibBotConversa;
    }

    public void setExibBotConversa(boolean exibBotConversa) {
        this.exibBotConversa = exibBotConversa;
    }

    public TelaClienteControle() throws Exception {
        super(true);

        try {
            String matricula = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("matricula");
            String codContrato = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("codContrato");
            preencherCompartilharLink();

            if (matricula == null || matricula.trim().isEmpty()) {
                pegarClienteControle();
                apresentarTopo = false;
            } else {
                prepararTelaCliente(matricula);
            }

            if (!UteisValidacao.emptyString(codContrato)) {
                selecionarContrato(Integer.parseInt(codContrato), false);
            }

            avaliarDiaPlusAndDayUse();

            preencherApresentarEmpresa();
            povoarListaOperacaoCreditoTreino();
            inicializarPaginacao();
            validarApresentarAtualizarBV();
            validarEdicaoCliente();
            consultarObservacoesDoCliente();
            if (!getClienteObservacoes().isEmpty()) {
                setApresentarAlteracao(true);
            }
            obterPontuacao();
            montarUrlCompletaAnexo();

            setExibBotConversa(false);
            String urlFluxoGymbot = getFacade().getMalaDireta().findbyFluxoUrlTelaAluno(getEmpresaLogado().getCodigo(),null);
            if (urlFluxoGymbot != null && !urlFluxoGymbot.isEmpty()) {
                setExibBotConversa(true);
            }

            setExibeGymbotPro(false);
            String fluxoGymbot = getFacade().getMalaDireta().findbyFluxoGymbotProTelaAluno(getEmpresaLogado().getCodigo(),null);
            if (fluxoGymbot != null && !fluxoGymbot.isEmpty()) {
                setExibeGymbotPro(true);
            }

            FuncionalidadeControle funcionalidadeControle = (FuncionalidadeControle) context().getExternalContext().getSessionMap().get("FuncionalidadeControle");
            if (funcionalidadeControle.getAbrirFuncCliente()) {
                funcionalidadeControle.validarFuncionalidadeCliente();
            }

            if (getConfiguracaoSistema().isCancelarContratoNaUnidadeOrigemAoTransferirAluno() &&
                    permissao("PermissaoTransferirClienteEmpresa")) {

                // Porque este recurso só deve ser aplicado a contratos de recorrencia.
                // Transferir contratos não recorrentes podem gerar problemas como deste ticket: #16553
                int quantidadeContratosNaoRecorrentesVigentes = getFacade()
                        .getContrato()
                        .quantidadeContratosNaoRecorrentesVigentes(cliente.getPessoa().getCodigo());
                setPermiteTransferirContrato(quantidadeContratosNaoRecorrentesVigentes == 0);
            }

            Optional<ContratoVO> contratoRecorrenciaAtivo = listaContratosCliente.stream().filter(c -> c.getContratoRecorrenciaVO().getCodigo() > 0 && c.getSituacao().equals("AT")).findFirst();
            setPermiteTransferirPlano(permissao("MudarPlanoContratoAtivo") && contratoRecorrenciaAtivo.isPresent());
            getFacade().getPessoa().obterInformacoesDeBloqueioCobrancaAutomatica(getCliente().getPessoa());
            boolean temAutorizacao = getFacade().getAutorizacaoCobrancaCliente().clienteTemAutorizacaoCobrancaAtiva(getCliente().getCodigo());
            if (temAutorizacao || getCliente().getPessoa().getDataBloqueioCobrancaAutomatica() != null) {
                setApresentarBloqueioParcelas(true);
            }
            montarListaAutorizacaoCliente();
            consultarModalidadesContrato();
            validaPermissaoVisuaalizarBotao();

            possuiPermissaoVerCacBiLtv = permissao("BiLtv");
            preencherDataMatricula();
            //validar se o usuario marcou que vai usar a tela nova
            setMsgAlert("");
            processarRecursoPadrao();
            notificarRecursoEmpresa(RecursoSistema.TELA_ALUNO_CARREGOU);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void processarRecursoPadrao() {

        try {
            this.novaTelaAlunoPadraoEmpresa = getFacade().getUsuario().recursoPadraoEmpresa(TipoInfoMigracaoEnum.TELA_ALUNO, getClienteVO().getEmpresa().getCodigo());
        }catch (Exception e){
            Uteis.logar(e, ContratoControle.class);
        }
        try {
            this.novaVendaAvulsaPadraoEmpresa = getFacade().getUsuario().recursoPadraoEmpresa(TipoInfoMigracaoEnum.VENDA_AVULSA, getClienteVO().getEmpresa().getCodigo());
        }catch (Exception e){
            Uteis.logar(e, ContratoControle.class);
        }


        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            novaNegociacaoPadrao = (getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.NEGOCIACAO, getUsuarioLogado().getCodigo(), getClienteVO().getEmpresa().getCodigo()) &&
                    !getFacade().getEmpresa().temPlanoPacote(getClienteVO().getEmpresa().getCodigo()) &&
                    (loginControle.getPermissaoAcessoMenuVO().getVendaRapidaTelaPadraoLancarContrato() == null || !loginControle.getPermissaoAcessoMenuVO().getVendaRapidaTelaPadraoLancarContrato()));
        }catch (Exception e){
            Uteis.logar(e, TelaClienteControle.class);
        }
        try {
            this.novaTelaAlunoPadrao = (this.novaTelaAlunoPadraoEmpresa ||
                    (getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.TELA_ALUNO,
                    getUsuarioLogado().getCodigo(), getClienteVO().getEmpresa().getCodigo())));
        }catch (Exception e){
            Uteis.logar(e, TelaClienteControle.class);
        }
        try {
            this.novaTelaVendaAvulsa = (this.novaVendaAvulsaPadraoEmpresa ||
                    (getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.VENDA_AVULSA,
                    getUsuarioLogado().getCodigo(), getClienteVO().getEmpresa().getCodigo())));
        }catch (Exception e){
            Uteis.logar(e, TelaClienteControle.class);
        }
    }

    private void validaPermissaoVisuaalizarBotao() throws Exception {

        if (!validarPermisaoUsuario("EstornoContrato_Autorizar",
                "3.19 - Estorno de Contrato - Autorizar")) {
            setExibirEstornoContrato(false);
        }
        else {
            setExibirEstornoContrato(true);
        }
    }
    private void preencherDataMatricula() {
        Date dataMatricula = getClienteVO().getSituacaoClienteSinteticoVO().getDataMatricula();
        if (dataMatricula == null) {
            setDataMatriculaPorExtenso("");
            return;
        }

        Integer codigoCliente = getClienteVO().getCodigo();

        String textWithCacPermissionToFormat = "Cliente <i>desde %s</i>. Vinculo atual de %s, nesse tempo, gerou R$ %s de receita, uma média de R$ %s mensal.";
        final String textToFormat = "<i>Aluno desde %s</i>. Vinculo Atual de %s";
        final String dataMatriculaText = Calendario.getDataAplicandoFormatacao(dataMatricula, "dd.MM.yyyy");
        String result = null;
        try {
            Date inicioVinculoAtual = getFacade().getFinanceiro().getPlanoConta().consultarDataInicioVinculoAtual(codigoCliente, null);
            if (Calendario.igual(inicioVinculoAtual, Calendario.hoje())) {
                setDataMatriculaPorExtenso("Se matriculou hoje.");
                return;
            }

            Date vigenciaUltimoContrato = getFacade().getFinanceiro().getPlanoConta().consultarDataContratoVigenciaAte(codigoCliente, null);
            Date dataAvaliarFim = Calendario.hoje();
            if(Calendario.menor(vigenciaUltimoContrato, dataAvaliarFim)){
                dataAvaliarFim = vigenciaUltimoContrato;
                textWithCacPermissionToFormat = "Cliente <i>desde %s</i>. Último vinculo de %s, nesse tempo, gerou R$ %s de receita, uma média de R$ %s mensal.";
            } else {
                setDataMatriculaPorExtensoAteFimContato("*Até o final do vinculo é de " + Uteis.obterDiferencaEntreDatasPorExtenso(inicioVinculoAtual, vigenciaUltimoContrato));
            }
            result = String.format(textToFormat,dataMatriculaText,
                    Uteis.obterDiferencaEntreDatasPorExtenso(inicioVinculoAtual, dataAvaliarFim));

            if (possuiPermissaoVerCacBiLtv) {
                double valorReceitaCliente = getFacade().getFinanceiro().getPlanoConta().consultarValorRateioPorAluno(codigoCliente);


                double quantidadeDeMeses = ((vigenciaUltimoContrato.getTime() - inicioVinculoAtual.getTime()) / (1000 * 60 * 60 * 24) / 30);

                result = String.format(textWithCacPermissionToFormat,dataMatriculaText,
                        Uteis.obterDiferencaEntreDatasPorExtenso(inicioVinculoAtual, dataAvaliarFim),
                        Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(valorReceitaCliente),
                        Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(valorReceitaCliente / (quantidadeDeMeses < 1 ? 1 : quantidadeDeMeses))                        );
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        setDataMatriculaPorExtenso(result);
    }

    private void avaliarDiaPlusAndDayUse() {
        try {
            EmpresaVO empresa = getCliente().getEmpresa();

            boolean possuiContratoVigente = false;
            boolean planoPermiteDiaPlus = false;
            boolean produtoDayUsePreenchido = (empresa.getProdutoDayUse() != null && empresa.getProdutoDayUse().getCodigo() > 0);
            boolean modalidadeDayUsePreenchido = (empresa.getModalidadeDayUse() != null && empresa.getModalidadeDayUse().getCodigo() > 0);
            boolean produtoDiaPlusPreenchido = (empresa.getProdutoDiaPlus() != null && empresa.getProdutoDiaPlus().getCodigo() > 0);
            boolean modalidadeDiaPlusPreenchido = (empresa.getModalidadeDiaPlus() != null && empresa.getModalidadeDiaPlus().getCodigo() > 0);

            for (ContratoVO contratoVO : listaContratosCliente) {
                if (contratoVO.getSituacao().equals("AT")) {
                    if (Calendario.entre(Calendario.hoje(), contratoVO.getVigenciaDe(), contratoVO.getVigenciaAteAjustada())) {
                        possuiContratoVigente = true;
                    }
                    if (contratoVO.getPlano().getPlanoTipo().getCodigo().equals(empresa.getTipoPlanoDiaPlus().getCodigo())) {
                        planoPermiteDiaPlus = true;
                    }
                }
            }

            setApresentarDiaPlus(possuiContratoVigente && planoPermiteDiaPlus && produtoDiaPlusPreenchido && modalidadeDiaPlusPreenchido);
            setApresentarDayUse(!possuiContratoVigente && produtoDayUsePreenchido && modalidadeDayUsePreenchido);
        } catch (Exception ignored) {

        }
    }

    private void preencherCompartilharLink() {
        try {
            String share = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("shr");
            compartilharLink = share != null && share.equals("t");
        } catch (Exception e) {
        }
    }

    public void prepararClienteTransferenciaEmpresa() {
        try {
            prepararTelaCliente(cliente, true);
            setDiasVigenciaDeContratosAtivos(getFacade().getContrato().consultarDiasVigenciaContratoAtivo(cliente));
        } catch (Exception e) {
            Uteis.logar(e, TelaClienteControle.class);
        } finally {
            notificarRecursoEmpresa(RecursoSistema.TRANSFERENCIA_DE_EMPRESA_PERFIL_ALUNO);
        }
    }

    public boolean verificarDebitoClienteEmpresa() throws Exception {
        if (getFacade().getMovParcela().temParcelasAberto(cliente.getCodigo())) {
            setMensagemDetalhada("msg_erro", "O cliente possui parcela(s) vencida(s) em aberto");
            setSucesso(false);
            setErro(true);
            return false;
        } else {
            return true;
        }
    }

    public void transferirClienteEmpresa() throws Exception {
        if (getEmpresaTransferenciaCliente() == null || getEmpresaTransferenciaCliente().getCodigo() == 0) {
            setMensagemDetalhada("msg_erro", "Você precisa escolher uma empresa para realizar a transferência");
            setSucesso(false);
            setErro(true);
        } else if (verificarDebitoClienteEmpresa()) {
            setCopiaClienteVO(getClienteVO());
            try {
                String retorno = gravarClienteTrocandoEmpresa();
                if (!retorno.equals("erro")) {
                    setEmpresaTransferenciaCliente(null);
                    redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getMatricula());
                    limparMsg();
                    notificarRecursoEmpresa(RecursoSistema.TRANSFERENCIA_DE_EMPRESA_PERFIL_ALUNO_SUCESSO);
                }
                resetTransferenciaClienteEmpresa();
            } catch (Exception ex) {
                setMensagemDetalhada("msg_erro", ex.getMessage());
                setSucesso(false);
                setErro(true);
            }
        }
    }

    public boolean getColunaCupomDesconto() {
        for (MovProdutoVO prod : this.listaHistoricoProduto) {
            if (!UteisValidacao.emptyString(prod.getNumeroCupomDesconto()))
                return true;
        }
        return false;
    }

    public Boolean isTrocaPlano() {
        return trocaPlano;
    }

    public void setTrocaPlano(Boolean trocaPlano) {
        this.trocaPlano = trocaPlano;
    }

    public void transferirPlanoDoCliente() throws Exception {
        transferirPlanoDoClienteGeral(false,null, getKey());
    }

    public void transferirPlanoDoClienteGeral(boolean servlet, ClienteControle clienteControle, String chave) throws Exception {
        if (getPlanoParaTransferencia() == null) {
            setMensagemDetalhada("msg_erro", "Você precisa escolher um plano");
            setSucesso(false);
            setErro(true);
        } else {
            try {
                // MAPEIA VALUE OBJECTS COM AS INFOS NECESSARIAS
                boolean trocaPlano = isTrocaPlano();
                Date ontem = Calendario.ontem();
                UsuarioVO usuarioResponsavel;
                PlanoVO planoOrigem = getContratoParaTransferenciaDePlano().getPlano();
                PlanoVO planoDestino = getPlanoParaTransferencia();
                ContratoVO contrato = getContratoParaTransferenciaDePlano();
                int empresa = getEmpresaLogado().getCodigo();
                usuarioResponsavel = getUsuarioLogado();
                if (servlet) {
                    usuarioResponsavel = this.getUsuario();
                    empresa = this.getEmpresa().getCodigo();
                }
                if(Calendario.maiorOuIgual(contrato.getVigenciaDe(), Calendario.hoje())){
                    throw new Exception("A operação de mudança de plano só pode ser feita após o primeiro dia do contrato.");
                }


                //CRIA O CANCELAMENTO DO CONTRATO ATUAL
                CancelamentoContratoVO cancelamento = new CancelamentoContratoVO();
                cancelamento.setNomePlanoOrigem(planoOrigem.getDescricao());
                cancelamento.setNomePlanoDestino(planoDestino.getDescricao());
                cancelamento.setResponsavelCancelamento(usuarioResponsavel);
                cancelamento.setDataCancelamento(ontem);
                JustificativaOperacaoVO justificativa = getFacade()
                        .getJustificativaOperacao()
                        .obterJustificativaCancelamentoMudancaPlano(empresa);

                cancelamento.setTipoJustificativaOperacao(justificativa.getCodigo());
                cancelamento.setTipoDevolucaoCancelamento("");
                cancelamento.setTipoParcelaCancelamento(TipoParcelaCancelamento.CANCELAMENTO_POR_MUDANCA_PLANO);
                cancelamento.setTransferenciaEmpresaClientePlano(true);
                cancelamento.setListaParcelas(getFacade().getMovParcela().consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                cancelamento.setDataFinalAcessoCancelamentoAntecipado(cancelamento.getDataCancelamento());
                cancelamento.setEmpresaDestinoTransferencia(empresa);
                MovParcelaVO proximaParcelaAnuidadeEmAberto = getFacade().getMovParcela().consultarProximaParcelaAnuidadeEmAberto(contrato);
                CancelamentoContratoControle cancelamentoContratoControle = new CancelamentoContratoControle();
                if (!servlet) {
                    clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
                }
                clienteControle.setContratoVO(contrato);
                cancelamentoContratoControle.setContratoVO(contrato);

                if (servlet) {
                    cancelamentoContratoControle.novoGeral(servlet, clienteControle, usuarioResponsavel);
                } else {
                    cancelamentoContratoControle.novo();
                }

                cancelamentoContratoControle.setCancelamentoContratoVO(cancelamento);

                if (servlet) {
                    cancelamentoContratoControle.calculoCancelamentoClienteGeral(servlet, clienteControle);
                } else {
                    cancelamentoContratoControle.calculoCancelamentoCliente();
                }
                cancelamento.setTipoDevolucaoCancelamento("TR");

                cancelamentoContratoControle.obterValoresCancelamento();

                if (servlet) {
                    cancelamentoContratoControle.validarConfirmacaoCancelamento();
                } else {
                    cancelamentoContratoControle.listarPagamento();
                }

                cancelamentoContratoControle.getCancelamentoContratoVO().setTipoTranferenciaCancelamento("DE");
                cancelamentoContratoControle.obterTipoTransferencia();
                cancelamentoContratoControle.validarTransferenciaCliente();

                //Verificando se aluno utilizou mais dias do que pagou
                Double valorTotalPagoPeloCliente = cancelamentoContratoControle.getCancelamentoContratoVO().getValorTotalPagoPeloCliente();
                Double valorTotalSomaProdutoContratos = cancelamentoContratoControle.getCancelamentoContratoVO().getValorTotalSomaProdutoContratos();
                Double valorUtilizadoClienteBase = cancelamentoContratoControle.getCancelamentoContratoVO().getValorUtilizadoPeloClienteBase();
                if ((valorTotalPagoPeloCliente - valorTotalSomaProdutoContratos) > valorUtilizadoClienteBase) {
                    if (servlet) {
                        cancelamentoContratoControle.gravarGeral(true, this.getEmpresa(), this.getUsuario());
                    } else {
                        cancelamentoContratoControle.gravar();
                    }
                }else {
                    throw new Exception("Não é possível realizar a transferência quando o valor utilizado do contrato é maior que o valor pago. Aluno utilizou mais dias do que pagou, lembrando que produtos comprados junto ao contrato(serviços, matricula, anuidade, etc), entram com valor total no cálculo do valor utilizado.");
                }

                //PROCESSA MULTA SE CONFIGURAÇAO ESTIVER ATIVA E SE VALOR DO CONTRATO ORIGINAL FOR MAIOR QUE O VALOR DO CONTRATO NOVO
                boolean configAtivaAplicarMulta = getContratoParaTransferenciaDePlano().getEmpresa().isAplicarMultaMudancaPlano();

                Double valorOrigem = getContratoParaTransferenciaDePlano().getValorFinal();
                Double valorFinal = getNovoContratoParaTransferenciaDePlano().getValorFinal();

                if (configAtivaAplicarMulta && valorOrigem > valorFinal) {
                    ZillyonWebFacade zw = new ZillyonWebFacade();
                    UsuarioVO usuario;
                    if (servlet) {
                        usuario = this.getUsuario();
                    } else {
                        usuario = this.getUsuarioLogado();
                    }
                    Date dataVencimentoMulta = getNovoContratoParaTransferenciaDePlano().getDataPrimeiraParcela();
                    Double valorMulta = calcularValorMultaMudancaPlano();

                    zw.processaMultaMudancaPlano(usuario, valorMulta, contrato, dataVencimentoMulta, empresa);
                }

                //processa novo contrato
                int diaPrimeiraParcela = getDiaVencimentoCartaoTransferenciaDePlano();

                Date dataVencimentoAnuidade = proximaParcelaAnuidadeEmAberto != null ? proximaParcelaAnuidadeEmAberto.getDataVencimento() : null;


                UsuarioVO usuarioVO;
                EmpresaVO empresaVO;
                ClienteVO clienteVO1;
                if (servlet) {
                    usuarioVO = this.getUsuario();
                    empresaVO = this.getEmpresa();
                    clienteVO1 = this.getCliente();
                } else {
                    usuarioVO = this.getUsuarioLogado();
                    empresaVO = this.getEmpresaLogado();
                    clienteVO1 = getClienteVO();
                }

                ContratoVO novoContrato = getFacade().getContrato().gravarContratoSite(
                        servlet ? chave : getKey(),
                        getPlanoParaTransferencia().getCodigo(),
                        clienteVO1.getCodigo(),
                        getContratoParaTransferenciaDePlano().getNrParcelasAdesao(),
                        getContratoParaTransferenciaDePlano().getNrVezesParcelarProduto(),
                        "",
                        true,
                        true,
                        false,
                        getContratoParaTransferenciaDePlano().getNrParcelasPagamento(),
                        false,
                        0,
                        empresaVO.getCodigo(),
                        true,
                        usuarioVO,
                        diaPrimeiraParcela,
                        null,
                        false,
                        contrato.getCodigo(), dataVencimentoAnuidade, null, contrato, trocaPlano, null, null,null, false);
                notificarRecursoEmpresaMudancaUpDown();

                getFacade().getHistoricoContrato().excluirHistoricoContratos(novoContrato.getCodigo());

                //INCREMENTA HISTORICO DO CONTRATO COM O CANCELAMENTO
                HistoricoContratoVO historico = new HistoricoContratoVO();
                historico.setDescricao("GERADO A PARTIR DA MUDANÇA DO PLANO " + planoOrigem.getDescricao() + ", PARA " + planoDestino.getDescricao());
                historico.setResponsavelRegistro(usuarioResponsavel);
                historico.setDataRegistro(Calendario.hoje());

                historico.setDataInicioSituacao(novoContrato.getVigenciaDe());
                historico.setDataFinalSituacao(novoContrato.getVigenciaAteAjustada());
                historico.setTipoHistorico("TE");
                historico.setContrato(novoContrato.getCodigo());
                getFacade().getHistoricoContrato().incluirSemCommit(historico, false);


                if (!servlet) {
                    redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + getClienteVO().getMatricula());
                }
            } catch (Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
                setSucesso(false);
                setErro(true);
                montarErro(e);
                Uteis.logar(e, TelaClienteControle.class);
                if (servlet) {
                    e.printStackTrace();
                    throw e;
                }
            }
        }
    }

    public Double calcularValorMultaMudancaPlano() {
        Double valorContratoOriginal = this.getContratoParaTransferenciaDePlano().getValorFinal();
        Double porcentagemMulta = this.getContratoParaTransferenciaDePlano().getPlano().getPercentualMultaCancelamento();
        return valorContratoOriginal * porcentagemMulta / 100;
    };

    private void setarModuloZW() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if (loginControle != null) {
            loginControle.setModuloAberto(ModuloAberto.ZILLYONWEB);
        }
    }

    private void inicializarPaginacao() {
        listaCompras = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaParcelas = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaPagamentos = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaRemessa = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaBoleto = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaTrasacoes = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaTransacoesVerificacao = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaPix = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaCancelamentoGetCard = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaBoletoGeral = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaClienteObservacao = new ListaPaginadaTO(LISTA_PAGINADA_OBSERVACAO_LIMIT);
        listaMovConta = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
    }

    public void obterPontuacao() throws Exception {
        setValorPontuacao(getFacade().getHistoricoPontos().obterPontosTotalPorCliente(cliente.getCodigo()));
    }

    private void preencherApresentarEmpresa() throws Exception {
        Boolean apresentarEmpresaTelaCliente = (Boolean) JSFUtilities.getFromSession("apresentarEmpresaTelaCliente");
        if (apresentarEmpresaTelaCliente == null) {
            List<EmpresaVO> empresas = getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            apresentarEmpresaTelaCliente = empresas.size() > 1;
            JSFUtilities.storeOnSession("apresentarEmpresaTelaCliente", apresentarEmpresaTelaCliente);
        }
        setApresentarEmpresa(apresentarEmpresaTelaCliente);
    }

    private void povoarListaOperacaoCreditoTreino() throws Exception {
        this.listaOperacaoCreditoTreino = new ArrayList<SelectItem>();
        this.listaOperacaoCreditoTreino.add(new SelectItem(TipoOperacaoCreditoTreinoEnum.CODIGO_TODOS, TipoOperacaoCreditoTreinoEnum.OPERACAO_TODOS));
        for (TipoOperacaoCreditoTreinoEnum obj : TipoOperacaoCreditoTreinoEnum.values()) {
            if (obj == TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL) {
                this.listaOperacaoCreditoTreino.add(new SelectItem(obj.getCodigo(), TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL_POSITIVO));
                this.listaOperacaoCreditoTreino.add(new SelectItem(TipoOperacaoCreditoTreinoEnum.CODIGO_AJUSTE_MANUAL_NEGATIVO, TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL_NEGATIVO));
            } else {
                this.listaOperacaoCreditoTreino.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
    }

    public void pegarClienteControle() {
        ClienteControle telaControle = (ClienteControle) JSFUtilities.getManagedBean(ClienteControle.class.getSimpleName());
        if (telaControle != null && telaControle.getClienteVO() != null && !UteisValidacao.emptyNumber(telaControle.getClienteVO().getCodigo())) {
            prepararTelaCliente(telaControle.getClienteVO().getMatricula());
        }

    }

    public void verTodosAvisos() {
        try {
            avisosModal = getFacade().getClienteMensagem().consultarTelaCliente(getCliente().getCodigo(), null);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void upload(UploadEvent upload) {
        setOnCompleteAnexo("");
        try {
            UploadItem item = upload.getUploadItem();
            if (item.getFile().length() > 20971520) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("Arquivo tem tamanho superior a 20 Megabytes");
                throw new ConsistirException("Tamanho Superior a 20 Megabytes");
            }

            setArquivoCliente(item.getFile());
            setNomeAnexoCliente(item.getFileName());
            String[] partes = item.getFileName().split("\\.");
            setExtensaoArquivoCliente("." + partes[partes.length - 1]);

            montarSucessoGrowl("Arquivo enviado com sucesso!");
            setOnCompleteAnexo(getMensagemNotificar());
        } catch (Exception e) {
            setOnCompleteAnexo(getMensagemNotificar());
            montarErro(e);
        }
    }

    public void gravarAnexoCliente() {
        try {
            String chaveArquivo = "";
            if (getArquivoCliente() != null) {
                if (!cliente.getAnexo().isEmpty()) {
                    excluirAnexo();
                }
                chaveArquivo = MidiaService.getInstance().uploadObjectWithExtension(getKey(),
                        MidiaEntidadeEnum.ANEXO_CLIENTE,
                        cliente.getCodigo().toString(),
                        getArquivoCliente(),
                        getExtensaoArquivoCliente());

                cliente.setAnexo(chaveArquivo + "?time=" + getTimeStamp());
                cliente.setNomeAnexo(getNomeAnexoCliente());
                cliente.setDataCadastroAnexo(Calendario.hoje());

                getFacade().getCliente().gravarChaveAnexo(cliente);
                inclurLogAnexo();
                montarSucessoDadosGravados();
                setArquivoCliente(null);
                setMostrarBotaoGravarAnexo(false);
                setOnCompleteAnexo("");
                setNomeAnexoCliente("");
                montarUrlCompletaAnexo();
            } else {
                throw new ConsistirException("Seleciona o arquivo e ser gravado.");
            }
        } catch (Exception ex) {
            montarErro(ex);
            setOnCompleteAnexo("");
        }
    }

    public void inclurLogAnexo() throws Exception {
        try {
            LogVO log = new LogVO();
            log.setChavePrimaria(cliente.getCodigo().toString());
            log.setNomeEntidade("Cliente");
            log.setNomeEntidadeDescricao("Cliente");
            log.setOperacao("Inclusão Anexo Cliente");
            log.setNomeCampo("Anexo:");
            log.setValorCampoAlterado(cliente.getNomeAnexo());
            log.setResponsavelAlteracao(getUsuarioLogado().getNome());
            log.setDataAlteracao(Calendario.hoje());
            log.setUserOAMD(this.getUsuarioLogado().getUserOamd());
            registrarLogObjetoVO(log, cliente.getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("Cliente", cliente.getCodigo(), "ERRO AO GERAR LOG DO CLIENTE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
        }
    }

    public void excluirAnexo() {
        try {
            getFacade().getCliente().excluirAnexo(cliente, this.getUsuarioLogado());
            montarSucessoGrowl("Dados excluídos com sucesso");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void cancelarOperacaoCadastroAnexo() {
        setArquivoCliente(null);
        setMostrarBotaoGravarAnexo(false);
        setOnCompleteAnexo("");
    }

    public void montarUrlCompletaAnexo() {
        cliente.setUrlCompletaAnexo(getPaintFotoDaNuvem(cliente.getAnexo()));
    }

    public void mostrarBotaoGravarAnexo() {
        mostrarBotaoGravarAnexo = true;
    }

    public void limparAnexo() {
        setArquivoCliente(null);
    }

    @Override
    public void obterSaldoContaCorrenteCliente() throws Exception {
        MovimentoContaCorrenteClienteVO obj = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(
                getCliente().getPessoa().getCodigo(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (obj == null) {
            setDebitoContaCorrente(false);
            setPermitirTransferenciaCredito(false);
        } else {
            getCliente().setSaldoContaCorrente(obj.getSaldoAtual());
            setDebitoContaCorrente(getCliente().getSaldoContaCorrente() < 0);
            setPermitirTransferenciaCredito(getCliente().getSaldoContaCorrente() > 0);
        }
    }

    public void selecionarContrato() {
        selecionarContrato(null);
    }

    public void selecionarContrato(Integer codigoContrato) {
        selecionarContrato(codigoContrato, true);
    }

    public void selecionarContrato(Integer codigoContrato, Boolean refreshPage) {
        try {
            if (contratoSelecionado != null && refreshPage) {
                voltarContratos();
                return;
            }
            if (codigoContrato != null) {
                contratoSelecionado = new ContratoVO();
                contratoSelecionado.setCodigo(codigoContrato);
            } else {
                contratoSelecionado = (ContratoVO) context().getExternalContext().getRequestMap().get("contratoTbl");
            }
            carregarDadosContrato(contratoSelecionado.getCodigo());
            limparMsg();
            marcacoesFuturas = 0;
            if (contratoSelecionado != null && contratoSelecionado.getContratoRecorrenciaVO().isCancelamentoProporcional()) {
                ContratoOperacaoVO contratoOperacaoVO = getFacade().getContratoOperacao().consultarPorTipoOperacaoCodigoContrato(contratoSelecionado.getCodigo(), "CA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (contratoOperacaoVO != null && Calendario.menor(Calendario.hoje(), contratoOperacaoVO.getDataInicioEfetivacaoOperacao())) {
                    setApresentarEstornoCancelamento(true);
                }
            }
            if(contratoSelecionado.isVendaCreditoTreino()){
                try {
                    Integer saldoVirtualAluno = getFacade().getControleCreditoTreino().saldoVirtualAluno(getCliente().getCodigoMatricula(),
                            contratoSelecionado.getCodigo());
                    saldoCreditosProcessado = getFacade().getControleCreditoTreino().consultarSaldoCredito(contratoSelecionado.getCodigo());
                    marcacoesFuturas = saldoCreditosProcessado - saldoVirtualAluno;

                }catch (Exception e){
                    Uteis.logar(e, TelaClienteControle.class);
                }
            }
        } catch (Exception e) {
            voltarContratos();
            Uteis.logar(e, TelaClienteControle.class);
            montarErro(e);
        }

    }

    public void carregarDadosContrato(int codigoContrato) throws Exception {
        contratoSelecionado = getFacade().getContrato().consultarContratoDetalhe(codigoContrato);

        if (context() != null) {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            clienteControle.setContratoVO(getContratoSelecionado());
            clienteControle.setClienteVO(getClienteVO());
        }

        if (!UteisValidacao.emptyNumber(contratoSelecionado.getContratoRecorrenciaVO().getCodigo())) {
            setApresentarRenovavelAutomaticamente(contratoSelecionado.getPlano().getPlanoRecorrencia().getRenovavelAutomaticamente() || contratoSelecionado.getContratoRecorrenciaVO().getRenovavelAutomaticamente());
        } else {
            setApresentarRenovavelAutomaticamente(contratoSelecionado.getPlano().getRenovavelAutomaticamente() || contratoSelecionado.getRenovavelAutomaticamente());
        }
        setApresentarDataAlteracaoManual(contratoSelecionado.getDataAlteracaoManual() != null);
        setListaContratos(new ArrayList<>());
        getListaContratos().add(contratoSelecionado);
        LinhaTempoContratoControle linhaControle = (LinhaTempoContratoControle) context().getExternalContext().getRequestMap().get(LinhaTempoContratoControle.class.getSimpleName());
        if (linhaControle == null) {
            linhaControle = new LinhaTempoContratoControle();
        }
        linhaControle.iniciarLinhaContrato(contratoSelecionado.getCodigo(), cliente.getCodigoMatricula());
        carregarFinanceiro = true;
        abrirFinanceiro();
        cheques = getFacade().getCheque().consultarTelaCliente(contratoSelecionado.getCodigo());
        carregarFinanceiro = true;
        apresentarOperacaoNoMenuCliente();
        setListaHistoricoContrato(getFacade().getHistoricoContrato().consultarPorContratoSimplificado(contratoSelecionado.getCodigo()));
        consultarContratoOperacaoHistoricoContratoPeriodoAcessoCliente();

        if (!isContratoTransferido() || isPessoaDonaContrato()) {
            mapaMostrar.put("ESTORNO", validarPermissaoParaEstornarContrato());
        }
        consultarControleCreditoTreino();
        contratoSelecionado.registrarObjetoVOAntesDaAlteracao();
        if (contratoSelecionado.getGrupo() != null && contratoSelecionado.getGrupo().getCodigo() > 0) {
            contratoSelecionado.setGrupo(getFacade().getGrupo().consultarPorChavePrimaria(contratoSelecionado.getGrupo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
    }

    public void selecionarPrimeiroContrato() throws Exception {
        if (contratoSelecionado != null) {
            voltarContratos();
            return;
        }
        contratoSelecionado = getListaContratos().get(0);
        contratoSelecionado = getFacade().getContrato().consultarContratoDetalhe(contratoSelecionado.getCodigo());
        setListaContratos(new ArrayList<>());
        getListaContratos().add(contratoSelecionado);
        LinhaTempoContratoControle linhaControle = (LinhaTempoContratoControle) context().getExternalContext().getRequestMap().get(LinhaTempoContratoControle.class.getSimpleName());
        linhaControle.iniciarLinhaContrato(contratoSelecionado.getCodigo(), cliente.getCodigoMatricula());
        carregarFinanceiro = true;
        abrirFinanceiro();
        cheques = getFacade().getCheque().consultarTelaCliente(contratoSelecionado.getCodigo());
        carregarFinanceiro = true;
        apresentarOperacaoNoMenuCliente();
        setListaHistoricoContrato(getFacade().getHistoricoContrato().consultarPorContratoSimplificado(contratoSelecionado.getCodigo()));
        consultarContratoOperacaoHistoricoContratoPeriodoAcessoCliente();
        if (!isContratoTransferido() || isPessoaDonaContrato()) {
            mapaMostrar.put("ESTORNO", validarPermissaoParaEstornarContrato());
        }
    }

    public void consultarContratoOperacaoHistoricoContratoPeriodoAcessoCliente() throws Exception {
        setListaContratoOperacao(new ArrayList());
        List<ContratoOperacaoVO> objsOperacao = getFacade().getContratoOperacao().consultarPorContrato(contratoSelecionado.getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
        List<ContratoOperacaoVO> operacoesPorCodigo = new ArrayList<ContratoOperacaoVO>(objsOperacao);
        Ordenacao.ordenarListaReverse(operacoesPorCodigo, "codigo");
        if (contratoSelecionado.getSituacao().equals("AT") || contratoSelecionado.getSituacao().equals("TR")) {
            for (ContratoOperacaoVO operacao : operacoesPorCodigo) {
                if (!operacao.getTipoOperacao().equals("AH") && !operacao.getTipoOperacao().equals("BC") && !operacao.getTipoOperacao().equals("TV") && !operacao.getTipoOperacao().equals("RT")) {
                    if (((operacao.getTipoOperacao().equals("CR")
                            || operacao.getTipoOperacao().equals("AT")
                            || operacao.getTipoOperacao().equals("BA")
                            || operacao.getTipoOperacao().equals("BR")
                            || operacao.getTipoOperacao().equals("TR")) && contratoSelecionado.getSituacao().equals("AT"))
                            || (operacao.getTipoOperacao().equals("TR") && contratoSelecionado.getSituacao().equals("TR"))) {
                        operacao.setPermiteEstorno(true);
                        break;
                    } else {  // operações de manutenção de modalidade, que devem impedir estorno de operações anteriores, para evitar erros nas matrículas nas turmas
                        break;
                    }
                }
            }
        } else if (contratoSelecionado.getSituacao().equals("IN")) {
            for (ContratoOperacaoVO operacao : operacoesPorCodigo) {
                if ((operacao.getTipoOperacao().equals("BA") || operacao.getTipoOperacao().equals("AT")) && Calendario.maior(operacao.getDataOperacao(), contratoSelecionado.getVigenciaAteAjustada())) {
                    operacao.setPermiteEstorno(true);
                }
            }
        }
        setListaContratoOperacao(objsOperacao);
        for (ContratoOperacaoVO contratoOperacaoVO1 : getListaContratoOperacao()) {
            if (!contratoOperacaoVO1.getChaveArquivo().isEmpty()) {
                contratoOperacaoVO1.setUrlCompletaArquivoAtestado(getPaintFotoDaNuvem(contratoOperacaoVO1.getChaveArquivo()));
            }
        }
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
    }

    public void voltarContratos() {
        try {
            contratoSelecionado = null;
            montarListaContrato();
        } catch (Exception e) {
            Uteis.logar(e, TelaClienteControle.class);
            montarErro(e);
        }
    }

    @Override
    public void obterListaProdutoComValidadeCliente() {
        try {
            setListaProdutosComValidade(getFacade().getMovProduto().consultarProdutoComValidadePorCodigoPessoa(
                    cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_VENDA));
            ((MovProdutoControle) getControlador(MovProdutoControle.class)).fecharModalEditarVigenciaFinalProduto();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void abrirCentral() {
        try {
            if (carregarCentral) {
                setEventos(getFacade().getPessoa().consultarEventosPessoa(cliente.getPessoa().getCodigo(), null));
                carregarCentral = false;
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void obterStatusProtheus() {
        try {
            statusProtheus = new HashMap<String, String>();
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            String url = PropsService.getPropertyValue(PropsService.urlIntegracaoProtheus) + "/recibo/status?id=" + obj.getInfoExtrato().getCodigoMovproduto();
            String resposta = ExecuteRequestHttpService.executeRequest(url, new HashMap<String, String>());
            JSONObject json = new JSONObject(resposta).getJSONObject("return");
            Iterator keys = json.keys();
            try {
                statusProtheus.put("codPacto", obj.getInfoExtrato().getCodigoMovproduto().toString());
            } catch (Exception e) {
            }

            statusProtheus.put("status", obj.getInfoExtrato().getStatusProtheus());
            while (keys.hasNext()) {
                String k = (String) keys.next();
                statusProtheus.put(k, json.optString(k));
            }
            setMsgAlert("Richfaces.showModalPanel('statusProtheus')");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }

    }

    public void abrirFinanceiro() {
        try {
            if (carregarFinanceiro) {
                inicializarPaginacao();
                obterListaProdutoComValidadeCliente();
                listaHistoricoProduto = getFacade().getMovProduto().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), LISTA_PAGINADA_LIMIT, 0);
                if (getIntegraProtheus()) {
                    getListaParcelas().setLimit(10);
                    listaHistoricoParcelas = getFacade().getMovParcela().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), getListaParcelas().getLimit(), 0, "codigo", true);
                    getFacade().getMovParcela().montarInformacoesExtratoAluno(listaHistoricoParcelas, "codigo", true);
                    Ordenacao.ordenarListaReverse(listaHistoricoParcelas, "dataVencimento");
                } else {
                    listaHistoricoParcelas = getFacade().getMovParcela().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), LISTA_PAGINADA_LIMIT, 0, "codigo", true);
                }
                listaHistoricoPagamentos = getFacade().getMovPagamento().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), LISTA_PAGINADA_LIMIT, 0);
                preencherItensCobranca();
                carregarFinanceiro = false;
                validarOpcoesProdutos();
            }
            listaCompras.setCount(getFacade().getMovProduto().obterCountProdutosCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo()));
            listaPagamentos.setCount(getFacade().getMovPagamento().obterCountPagamentosCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo()));
            listaParcelas.setCount(getFacade().getMovParcela().obterTotalParcelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo()));
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarOpcoesProdutos() throws Exception {
        setApresentarOpcaoEstornoProduto(false);
        setApresentarOpcaoCancelarSessao(false);
        setApresentarValorParcialmentePago(false);

        boolean permissao = getFacade().getControleAcesso().verificarPermissaoFuncionalidade("CancelarSessao");
        for (MovProdutoVO mov : getListaHistoricoProduto()) {

            if (!getApresentarOpcaoEstornoProduto()) {
                if ((getUsuarioLogado().getAdministrador() || mov.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo()))
                        && (mov.getProduto().getTipoProduto().equals("PE")
                        || mov.getProduto().getTipoProduto().equals("SE")
                        || mov.getProduto().getTipoProduto().equals("DI")
                        || mov.getProduto().getTipoProduto().equals("AA")
                        || mov.getProduto().getTipoProduto().equals("SS")
                        || mov.getProduto().getTipoProduto().equals(TipoProduto.DESAFIO.getCodigo())
                        || (mov.getProduto().getTipoProduto().equals("AC") && mov.getSituacao().equals("EA"))
                        || (mov.getProduto().getTipoProduto().equals("CC") && mov.getSituacao().equals("EA"))
                        || (mov.getProduto().getTipoProduto().equals(TipoProduto.ARMARIO.getCodigo()) && mov.getSituacao().equals("EA"))
                        || mov.getProduto().getTipoProduto().equals(TipoProduto.HOMEFIT.getCodigo())
                        || mov.getProduto().getTipoProduto().equals(TipoProduto.BIO_TOTEM.getCodigo())
                        || mov.getProduto().getTipoProduto().equals(TipoProduto.CONSULTA_NUTRICIONAL.getCodigo()))) {
                    setApresentarOpcaoEstornoProduto(true);
                }
            }

            if (!getApresentarOpcaoCancelarSessao()) {
                if (mov.getProduto().getTipoProduto().equals("SS") && !mov.getSituacao().equals("CA") && permissao) {
                    setApresentarOpcaoCancelarSessao(true);
                }
            }

            if (!getApresentarValorParcialmentePago()) {
                if (mov.getValorParcialmentePago() != null && mov.getValorParcialmentePago() > 0.0) {
                    this.setApresentarValorParcialmentePago(Boolean.TRUE);
                }
            }

            if (mov.getSituacao().equalsIgnoreCase("EA")) {
                mov.setMudarCorSituacaoEmAberto("red");
            }
        }
    }

    public void abrirRelacionamento() {
        try {
            if (carregarRelacionamento) {
                listaHistoricoContato = getFacade().getHistoricoContato().consultarTelaCliente(cliente.getCodigo(), 10);
                if (!UteisValidacao.emptyString(cliente.getEmpresa().getNomeUsuarioAmigoFit()) && cliente.getEmpresa().isIntegracaoAmigoFitHabilitada() == true) {
                    try {
                        listaIndicacoesAmigoFit = getFacade().getZWFacade().consultarIndicacoesClienteAmigoFit(cliente);
                    } catch (Exception e) {
                        setMensagemDetalhada("msg_erro", e.getMessage());
                    }
                }
                carregarRelacionamento = false;
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void verMaisHistoricoContato() {
        try {
            listaHistoricoContato = getFacade().getHistoricoContato().consultarTelaCliente(cliente.getCodigo(), listaHistoricoContato.size() + 5);
            carregarRelacionamento = false;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void abrirNotasFiscais() {
        notificarRecursoEmpresa(RecursoSistema.ENVIO_NFSE_CLIENTE);
        try {

            if (carregarNotasFiscais) {
                if(getConfiguracaoSistema().isUtilizarServicoSesiSC()){
                    listaNotasFiscais = consultarNotasFiscaisSesi(10);
                } else {
                    listaNotasFiscais = consultarNotasFiscais(10);
                }
                carregarNotasFiscais = false;
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }



    private List<NotasFiscaisTO> consultarNotasFiscais(Integer limit) throws Exception {
        List<NotasFiscaisTO> listaRetornar = new ArrayList<NotasFiscaisTO>();
        try {
            /* Notas do e-notas */
            List<NotaFiscalVO> listaNotas = getFacade().getNotaFiscal().consultarNotas(getCliente().getPessoa().getCodigo(), limit, 0, "dataemissao DESC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (NotaFiscalVO notaFiscalVO : listaNotas) {
                NotasFiscaisTO novo = new NotasFiscaisTO();
                novo.setIdRPS(notaFiscalVO.getCodigo().toString());
                novo.setIdNFCe(notaFiscalVO.getCodigo().toString());
                novo.setSerie(notaFiscalVO.getSerie());
                novo.setNrRPS(notaFiscalVO.getRps());
                novo.setNrNota(notaFiscalVO.getNumeroNota());
                novo.setValor(notaFiscalVO.getValorApresentar());
                novo.setDataEmissao(notaFiscalVO.getDataEmissaoApresentar());
                if (!UteisValidacao.emptyString(notaFiscalVO.getDataAutorizacaoApresentar())) {
                    novo.setDataProcessamento(notaFiscalVO.getDataAutorizacaoApresentar());
                } else {
                    novo.setDataProcessamento(notaFiscalVO.getDataRegistroApresentar());
                }
                novo.setRazaoSocial(notaFiscalVO.getRazaoSocial());
                novo.setTipoNota(notaFiscalVO.getTipo().getCodigo());
                novo.setEmail(notaFiscalVO.getClienteEmail());
                novo.setCodNotaFiscal(notaFiscalVO.getCodigo());
                novo.setStatus(notaFiscalVO.getStatusNotaApresentar());
                novo.setEnotas(true);
                novo.setLinkPDF(notaFiscalVO.getLinkPDF());
                listaRetornar.add(novo);
            }

//            Na linha 1075, é feita uma consulta no antigo serviço de notas fiscais da pacto. Após ser feita a migração para o eNotas, esse serviço foi
//            desabilitado. Comentei essa parte do código, pois a consulta no serviço inativo estava causando erro na exibição de notas na tela do aluno.

//            String listaRPS = getFacade().getNFSeEmitida().consultarPorPessoaParaTela(getCliente().getPessoa().getCodigo(), limit);
//            String listaNFCe = getFacade().getNotaFiscalConsumidorEletronica().consultarPorPessoaParaTela(getCliente().getPessoa().getCodigo(), limit);
//
//            if (!UteisValidacao.emptyString(listaRPS) || !UteisValidacao.emptyString(listaNFCe)) {
//
//                Map<String, String> headers = new HashMap<String, String>();
//                headers.put("Content-Type", "application/x-www-form-urlencoded");
//
//                Map<String, String> corpo = new HashMap<String, String>();
//                corpo.put("consulta", "true");
//                if (!UteisValidacao.emptyString(listaRPS)) {
//                    corpo.put("rps", listaRPS);
//                }
//                if (!UteisValidacao.emptyString(listaNFCe)) {
//                    corpo.put("nfce", listaNFCe);
//                }
//
//                String urlConsultar = PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nota";
//                try {
//                    String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");
//                    JSONArray listaRetornoJsonArray = new JSONArray(executeRequest);
//                    List<NotasFiscaisTO> listaRetornouWS = JSONMapper.getList(listaRetornoJsonArray, NotasFiscaisTO.class);
//                    if (limit != null && listaRetornouWS.size() > limit) {
//                        listaRetornar.addAll(listaRetornouWS.subList(0, limit));
//                    } else {
//                        listaRetornar.addAll(listaRetornouWS);
//                    }
//                } catch (Exception ex) {
//                    listaRetornar = new ArrayList<NotasFiscaisTO>();
//                }
//            }

            return listaRetornar;
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
            throw new Exception("Erro na consulta de notas fiscais, verificar no suporte!");
        }
    }

    private List<NotasFiscaisTO> consultarNotasFiscaisSesi(Integer limit) throws Exception {
        List<NotasFiscaisTO> listaRetornar = new ArrayList<NotasFiscaisTO>();
        if(!getConfiguracaoSistema().isUtilizarServicoSesiSC()){
            return listaRetornar;
        }
        try {
            return getFacade().getIntegracaoSesi().consultarNotas(getCliente().getPessoa().getCodigo(), limit, 0, "codigo DESC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
            throw new Exception("Erro na consulta de notas fiscais, verificar no suporte!");
        }
    }

    public void verMaisNotasFiscais() {
        try {
            if(getConfiguracaoSistema().isUtilizarServicoSesiSC()){
                listaNotasFiscais = consultarNotasFiscaisSesi(listaNotasFiscais.size() + 10);
            } else {
                listaNotasFiscais = consultarNotasFiscais(listaNotasFiscais.size() + 10);
            }
            carregarNotasFiscais = false;
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirEstudio() {
        try {
            if (carregarEstudio) {
                ClienteEstudioControle clienteEstudioControle = (ClienteEstudioControle) JSFUtilities.getManagedBean("clienteEstudioControle");
                clienteEstudioControle.setVerTodosModalAgenda(Boolean.FALSE);
                clienteEstudioControle.atualizar();
                clienteEstudioControle.setCodigoClienteAgendar(0);
                clienteEstudioControle.montarListaAgendar();
                carregarEstudio = false;
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarConfiguracaoSistema() {
        setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
    }

    @Override
    public void montarAmariosAlugados() {
        try {
            cliente.setArmariosAlugados(getFacade().getArmario().obterAluguelPorCliente(cliente.getCodigo(), cliente.getEmpresa().getCodigo(), getConfiguracaoSistema().getHabilitarGestaoArmarios()));
        } catch (Exception erro) {
            montarErro(erro);
        }
    }

    public void montarOrcamentosCliente() {
        try {
            cliente.setOrcamentos(getFacade().getOrcamento().consultarPorCodCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } catch (Exception erro) {
            montarErro(erro);
        }
    }

    public void prepararAtualizarStatusChaveArmarioDevolvida(ActionEvent actionEvent) {
        AluguelArmarioVO aluguel = (AluguelArmarioVO) context().getExternalContext().getRequestMap().get("aluguel");
        setAluguelSelecionado(aluguel);
    }

    public void atualizarStatusChaveArmarioDevolvida() {
        notificarRecursoEmpresa(RecursoSistema.HISTORICO_ARMARIO_CLIENTE);
        try {
            notificarRecursoEmpresa(RecursoSistema.CHAVE_DEVOLVIDA_ALUGUEL_ARMARIO);
            getFacade().getCliente().alterarStatusChaveDevolvidaAluguelArmario(getAluguelSelecionado().getCodigo(), getAluguelSelecionado().getChaveDevolvida(),
                    cliente.getCodigo(), cliente.getEmpresa().getCodigo(), getConfiguracaoSistema().getHabilitarGestaoArmarios());
            setMsgAlert("fireElement('form:btnAtualizaCliente');");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void prepararImprimirOrcamento(ActionEvent actionEvent) {
        OrcamentoVO orcaments = (OrcamentoVO) context().getExternalContext().getRequestMap().get("orcamento");
        setOrcamentoVO(orcaments);
    }

    public void imprimirOrcamento() {
        try {
            setMsgAlert("");
            if (getEmpresaLogado() == null || getEmpresaLogado().getCodigo() == 0) {
                throw new Exception("Empresa não encontrada. Selecione a empresa ou entre novamente no sistema.");
            }
            setModeloOrcamento(getFacade().getModeloOrcamento().consultarPorChavePrimaria(getOrcamentoVO().getModeloOrcamento(), Uteis.NIVELMONTARDADOS_TODOS));
            getModeloOrcamento().substituirTagsTextoEmBranco(getOrcamentoVO(), getEmpresaLogado(), Conexao.getFromSession(), false);
            setSucesso(true);
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public String getAbrirPDF() {
        if (getSucesso()) {
            return "abrirPopup('VisualizarContrato', 'ContratoEmBranco', 730, 545);";
        } else {
            return "";
        }
    }

    public String editarOrcamentoCliente() {
        OrcamentoVO obj = (OrcamentoVO) context().getExternalContext().getRequestMap().get("orcamento");
        obj.registrarObjetoVOAntesDaAlteracao();

        if (prepararTelaEdicaoOrcamento(obj)) {
            return "editarOrcamentoCliente";
        } else {
            return "";
        }
    }

    private boolean prepararTelaEdicaoOrcamento(OrcamentoVO obj) {
        boolean podeEditar = false;
        try {
            OrcamentoControle orcamentoControle = (OrcamentoControle) context().getExternalContext().getSessionMap().get("OrcamentoControle");
            orcamentoControle.setNomeCliente(cliente.getPessoa().getNomeAbreviado());
            orcamentoControle.setClienteSelecionado(obj.getCliente());
            orcamentoControle.setConsultorSelecionado(obj.getConsultor());
            orcamentoControle.setModeloOrcamentoSelecionado(obj.getModeloOrcamento());
            orcamentoControle.setPeriodoSelecionado(obj.getPeriodo());
            orcamentoControle.setSituacaoSelecionada(obj.getSituacao());
            orcamentoControle.setTipoTurmaSelecionada(obj.getTipoTurma());
            orcamentoControle.setOrcamentoVO(obj);
            podeEditar = true;
        } catch (Exception e) {
            e.getStackTrace();
        }
        return podeEditar;
    }

    public void montarAvisos() throws Exception {
        avisos = getFacade().getClienteMensagem().consultarTelaCliente(cliente.getCodigo(), 2);
        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
        clienteControle.limparMensagem();
        clienteControle.setErro(false);
        clienteControle.setSucesso(false);
        clienteControle.setClienteVO(getCliente());
        if (!UteisValidacao.emptyList(avisos)) {
            List<ClienteMensagemVO> todasMensagens = getFacade().getClienteMensagem().consultarTelaCliente(cliente.getCodigo(), null);
            getCliente().setListaClienteMensagem(todasMensagens);
            clienteControle.inicializarUsuarioLogado();
            clienteControle.abrirPanelMensagem();
        } else {
            clienteControle.setApresentarPanelLancaMensagem(false);
            clienteControle.limparPanelMensagem();
        }
    }

    public void irParaTitular() {
        try {
            ClienteVO clienteTitular = new ClienteVO();
            clienteTitular.setCodigo(cliente.getTitularPlanoCompartilhado());
            prepararTelaCliente(clienteTitular, true);
            redirect("/faces/clienteNav.jsp?page=cliente&matricula=" + super.getClienteVO().getMatricula());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void prepararTelaCliente(String matricula) {
        try {

            cliente = getFacade().getCliente().consultarPorMatricula(matricula, true, Uteis.NIVELMONTARDADOS_TODOS);
            if (!UteisValidacao.emptyNumber(cliente.getPessoa().getCodigo())) {
                cliente.getPessoa().setAssinaturaBiometriaFacialB(getFacade().getPessoa().verificaAssinaturaBiometriaFacial(cliente.getPessoa().getCodigo()));
                cliente.getPessoa().setAssinaturaBiometriaDigitalB(getFacade().getPessoa().verificaAssinaturaBiometriaDigital(cliente.getPessoa().getCodigo()));
            }

            if (!UteisValidacao.emptyNumber(cliente.getTitularPlanoCompartilhado())) {
                clienteTitular = getFacade().getCliente().consultarPorCodigo(cliente.getTitularPlanoCompartilhado(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            dataUltimoAcesso = getFacade().getAcessoCliente().obterUltimoAcesso(cliente.getCodigo());
            convites = getFacade().getConviteAulaExperimental().consultarConvitesRecebidos(cliente.getCodigo());
            montarAvisos();
            montarListaContrato();
            consultarObservacoesDoCliente();
            consultarObsercaoParqPositivo();
            consultarObjecaoDefinitiva();
            inicializarConfiguracaoSistema();
            montarAmariosAlugados();
            montarOrcamentosCliente();
            obterSaldoContaCorrenteCliente();
            limparEnderecosTelefonesClienteControle();
            obterPontuacao();
            consultarUltimoAtestadoCliente();
            setPermiteAlterarDataVencimentoParcelas(permissao("MudarDataVencimentoParcelas"));
            setPermiteLiberarCobrancaDataVencimentoParcelas(permissao("AlterarCobrancaProRataDiaRecorrente"));
            consultarUsuarioMovel();
            verificarMGBIntegracao();
            if (cliente.getDataInclusaoSPC() != null) {
                List<MovParcelaVO> parcelasSPC = getFacade().getMovParcela().consultarPorPessoaAndIncluidaSPC(cliente.getPessoa().getCodigo(), true);
                if (!parcelasSPC.isEmpty()) {
                StringBuilder hintSB = new StringBuilder();
                hintSB.append("Negativado em ").append(cliente.getDataInclusaoSPCApresentar()).append("<br/>");

                String infoParcela = "%s - %s - %s";

                for (MovParcelaVO parcela : parcelasSPC) {
                    hintSB.append(String.format(infoParcela,
                                    parcela.getCodigo(),
                                    parcela.getDescricao(),
                                    parcela.getValorParcela_Apresentar_Moeda()))
                            .append("<br/>");
                }

                hintSPC = hintSB.toString();
                }
                else{
                    cliente.setDataInclusaoSPC(null);
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            Uteis.logar(e, TelaClienteControle.class);
        }
    }

    private void consultarUsuarioMovel() throws Exception {
        getCliente().setUsuarioMovelVO(getFacade().getUsuarioMovel().consultarPorCliente(getCliente(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    private void consultarUltimoAtestadoCliente() {
        try {
            ultimoAtestadoCliente = getFacade().getAtestado().consultarUltimoAtestado(cliente.getPessoa());
            if (ultimoAtestadoCliente != null && ultimoAtestadoCliente.getCodigo() > 0) {
                Integer codigoMovProduto = ultimoAtestadoCliente.getMovProduto().getCodigo();
                ultimoAtestadoCliente.setMovProduto(getFacade().getMovProduto().consultarPorChavePrimaria(codigoMovProduto, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            } else {
                ultimoAtestadoCliente = null;
            }
        } catch (Exception e) {
            montarErro(e);
            Uteis.logar(e, this.getClass());
        }
    }

    private void consultarObsercaoParqPositivo() throws Exception {
        if (getCliente().isParqPositivo()) {
            AtestadoVO atestado = getFacade().getAtestado().consultarUltimoAtestado(getCliente().getPessoa());
            setMensagemParq(atestado.getObservacao());
        }
    }

    private void consultarObservacoesDoCliente() throws Exception {
        setClienteObservacoes(getFacade().getClienteObservacao().consultarObservacaoPaginado(getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, LISTA_PAGINADA_LIMIT, 0));
    }

    public void montarListaContrato() throws Exception {
        montarListaContrato(3);
    }

    public void verMenosContratos() throws Exception {
        if (getListaContratos().size() > 3) {
            setListaContratos(getListaContratos().subList(0, 3));
        }
    }

    public void verMaisContratos() throws Exception {
        montarListaContrato(null);
    }

    public void verMenosPagamentos() throws Exception {
        if (getListaHistoricoPagamentos().size() > 3) {
            setListaHistoricoPagamentos(getListaHistoricoPagamentos().subList(0, 3));
        }
    }

    public void verMaisPagamentos() throws Exception {
        listaHistoricoPagamentos = getFacade().getMovPagamento().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), 0, 0);
    }

    public void verMenosContatos() throws Exception {
        if (getListaHistoricoContato().size() > 3) {
            setListaHistoricoContato(getListaHistoricoContato().subList(0, 10));
        }
    }

    public void verMaisProdutos() throws Exception {
        listaHistoricoProduto = getFacade().getMovProduto().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), 0, 0);
        validarOpcoesProdutos();
    }

    public void verMaisContatos() throws Exception {
        listaHistoricoContato = getFacade().getHistoricoContato().consultarTelaCliente(cliente.getCodigo(), null);
    }

    public void verMenosProdutos() throws Exception {
        if (getListaHistoricoProduto().size() > 3) {
            setListaHistoricoProduto(getListaHistoricoProduto().subList(0, 3));
        }
        setApresentarOpcaoEstornoProduto(false);
        setApresentarOpcaoCancelarSessao(false);
        setApresentarValorParcialmentePago(false);
        validarOpcoesProdutos();
    }

    public void verMaisDadosContrato() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.MOSTRAR_DETALHES_CONTRATO_CLIENTE);
        financeiroContrato = true;
    }

    public void verMenosDadosContrato() throws Exception {
        financeiroContrato = false;
    }

    public void verMaisItensRemessa() {
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.nrPaginasItensRemessaEstorno", 9999);
    }

    public void verMaisItensRemessaBoleto() {
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.nrPaginasItensRemessaBoleto", 9999);
    }

    public void verMenosItensRemessa() {
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.nrPaginasItensRemessaEstorno", 10);
    }

    public void verMenosItensRemessaBoleto() {
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.nrPaginasItensRemessaBoleto", 10);
    }

    public void montarListaAutorizacaoCliente() throws Exception {
        setAutosCobCliente(getFacade().getAutorizacaoCobrancaCliente().consultarPorCliente(
                this.cliente.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    @Override
    public void montarListaContrato(Integer limit) throws Exception {
        setConvitesDireito(0);
        listaContratosCliente = getFacade().getContrato().consultarListaTela(cliente.getPessoa().getCodigo(), limit);
        setListaContratos(listaContratosCliente);
        setListaContratosDependentes(getFacade().getContratoDependente().findAllByCliente(cliente, limit));

        if (!UteisValidacao.emptyList(listaContratosCliente) || !UteisValidacao.emptyList(getListaContratosDependentes())) {
            List<ContratoVO> contratos = new ArrayList<>();
            if (!UteisValidacao.emptyList(getListaContratosDependentes())) {
                for (ContratoDependenteVO contratoDep : getListaContratosDependentes()) {
                    contratos.add(contratoDep.getContrato());
                }
            }
            contratos.addAll(listaContratosCliente);
            for (ContratoVO contratoVO : contratos) {
                convitesDireito += getFacade().getConvite().convitesDireito(contratoVO.getCodigo());
            }
            exibirConvites = getFacade().getConvite().jaConvidou(getCliente().getCodigo());
        }
        boolean primeiro = true;
        for (ContratoVO contrato : getListaContratos()) {
            if (primeiro) {// validacao é feita apenas uma vez na lista, no primeiro contrato
                contrato.verificarQualBotaoReferenteASituacaoContratoSeraApresentado(
                        getCliente(),
                        getListaContratos());
                primeiro = false;
            }
            if (contrato.getContratoInativo()
                    && contrato.getDataRenovarRealizada() != null) {
                contrato.setSituacao("RN");
                contrato.getContratoInativo();
                contrato.setApresentarBotaoRenovarContrato(true);

            } else if (contrato.getContratoInativo()) {
                contrato.setSituacao("IN");
                contrato.getContratoInativo();

            }
            if (getEmpresaLogado() != null
                    && getEmpresaLogado().getCodigo().intValue() != 0
                    && getEmpresaLogado().getCodigo().intValue() != contrato.getEmpresa().getCodigo().intValue()) {
                contrato.setRenovarContrato(false);
                contrato.setRematricularContrato(false);

            }
        }
    }

    public ClienteVO getCliente() {
        return cliente;
    }
    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ClienteVO getClienteTitular() {
        return clienteTitular;
    }

    public void setClienteTitular(ClienteVO clienteTitular) {
        this.clienteTitular = clienteTitular;
    }

    public String getPaintFotoDaNuvem() {
        try {
            return getPaintFotoDaNuvem(getCliente().getPessoa().getFotoKey());
        } catch (Exception e) {
            return String.format("%s/%s",
                    PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                    "fotoPadrao.jpg");
        }

    }

    public void atualizarFotos() {
        try {
            ClientesMarcadosControle controlMarc = (ClientesMarcadosControle) JSFUtilities.getManagedBean(ClientesMarcadosControle.class.getSimpleName());
            controlMarc.atualizarClientes();
        } catch (Exception ignored) {
        }

    }

    public void recarregarFotoEAtualizarVersaoTreino() throws Exception {
        recarregarFoto(true, getCliente(), false);
    }

    @Override
    public String getIdadeCliente() {
        try {
            Integer idade = Uteis.calcularIdadePessoa(Calendario.hoje(), getCliente().getPessoa().getDataNasc());
            return String.valueOf(idade);
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public void permitirAlterarConsultorContrato() throws InstantiationException, IllegalAccessException {
        notificarRecursoEmpresa(RecursoSistema.CONSULTOR_RESPONSAVEL_CONTRATO_CLIENTE);
        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
        clienteControle.setAlterandoConsultor(true);
        getContratoSelecionado().registrarObjetoVOAntesDaAlteracao();
    }

    @Override
    public void alterarConsultorContrato() {
        try {
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
            clienteControle.setClienteVO(getCliente());
            clienteControle.setContratoVO(getContratoSelecionado());
            clienteControle.alterarConsultorContrato();
            montarConsultorSelecionado();
        } catch (Exception ignored) {
        }
    }

    public void montarConsultorSelecionado() throws Exception {
        contratoSelecionado.setConsultor(getFacade().getColaborador().consultarPorChavePrimaria(contratoSelecionado.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    public boolean isCarregarFinanceiro() {
        return carregarFinanceiro;
    }

    public void setCarregarFinanceiro(boolean carregarFinanceiro) {
        this.carregarFinanceiro = carregarFinanceiro;
    }

    public boolean isCarregarRelacionamento() {
        return carregarRelacionamento;
    }

    public void setCarregarRelacionamento(boolean carregarRelacionamento) {
        this.carregarRelacionamento = carregarRelacionamento;
    }

    public List<MovProdutoVO> getListaHistoricoProduto() {
        if (listaHistoricoProduto == null) {
            listaHistoricoProduto = new ArrayList<MovProdutoVO>();
        }
        return listaHistoricoProduto;
    }

    public void setListaHistoricoProduto(List<MovProdutoVO> listaHistoricoProduto) {
        this.listaHistoricoProduto = listaHistoricoProduto;
    }

    public List<MovParcelaVO> getListaHistoricoParcelas() {
        return listaHistoricoParcelas;
    }

    public void setListaHistoricoParcelas(List<MovParcelaVO> listaHistoricoParcelas) {
        this.listaHistoricoParcelas = listaHistoricoParcelas;
    }

    public List<MovPagamentoVO> getListaHistoricoPagamentos() {
        return listaHistoricoPagamentos;
    }

    public void setListaHistoricoPagamentos(List<MovPagamentoVO> listaHistoricoPagamentos) {
        this.listaHistoricoPagamentos = listaHistoricoPagamentos;
    }

    public List<HistoricoContatoVO> getListaHistoricoContato() {
        return listaHistoricoContato;
    }

    public void setListaHistoricoContato(List<HistoricoContatoVO> listaHistoricoContato) {
        this.listaHistoricoContato = listaHistoricoContato;
    }

    public String abreTela() throws Exception {
        ClienteMensagemVO msg = (ClienteMensagemVO) context().getExternalContext().getRequestMap().get("avisoCliente");

        if (msg.getTipomensagem().getNavegacao().trim().isEmpty()) {
            return "";
        } else {
            if (msg.getTipomensagem().equals(TiposMensagensEnum.BOLETIM)) {
                QuestionarioClienteControle qcc = (QuestionarioClienteControle) context().getExternalContext().getSessionMap().get(
                        "QuestionarioClienteControle");

                if (qcc == null) {
                    qcc = new QuestionarioClienteControle();
                }
                qcc.inicializarCliente();
                QuestionarioClienteVO qc = getFacade().getQuestionarioCliente().consultarPorChavePrimaria(
                        msg.getQuestionarioCliente().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS);
                qc.setNovoObj(false);
                qc.registrarObjetoVOAntesDaAlteracao();
                qcc.setQuestionarioClienteVO(qc);

                if ((qc.getConsultor() != null)
                        && (qc.getConsultor().getSituacao().equals("NA"))) {
                    boolean deveAdicionar = true;
                    // Isto impede que outra pessoa mude o consultor do BV
                    // pendente caso o consultor original esteja inativo
                    qcc.getListaSelectConsultor().clear();
                    qcc.setApresentarBotaoAtualizar(false);

                    for (Object o : qcc.getListaSelectConsultor()) {
                        SelectItem object = (SelectItem) o;
                        Integer codigo = Integer.valueOf(object.getValue().toString());

                        if (codigo.intValue() == qc.getConsultor().getCodigo().intValue()) {
                            deveAdicionar = false;
                            break;
                        }
                    }
                    if (deveAdicionar) {
                        ((ArrayList) qcc.getListaSelectConsultor()).add(new SelectItem(qc.getConsultor().getCodigo(), qc.getConsultor().getPessoa().getNome()
                                + " (inativo)"));
                    }
                }
                context().getExternalContext().getSessionMap().put(
                        "QuestionarioClienteControle", qcc);
            }
            if (msg.getTipomensagem().equals(TiposMensagensEnum.DADOS_INCOMPLETOS)) {
                ClienteControle cc = (ClienteControle) context().getExternalContext().getSessionMap().get(
                        "ClienteControle");
                ActionEvent evt = JSFUtilities.createEventParameter("aba", "dadosPessoais");
                cc.abrirAba(evt);
            }
            if (msg.getTipomensagem().equals(TiposMensagensEnum.PARCELA_ATRASO)) {
                ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
                if (contratoControle != null) {
                    contratoControle.liberarBackingBeanMemoria("ContratoControle");
                }
                caixa();
            }
            if (msg.getTipomensagem().equals(TiposMensagensEnum.RISCO)) {
                MetaCRMControle hist = (MetaCRMControle) context().getExternalContext().getSessionMap().get(
                        "MetaCRMControle");

                if (hist == null) {
                    hist = new MetaCRMControle();
                }

                hist.inicializarContatoAvulso();
                hist.getHistoricoContatoVO().setFase(FasesCRMEnum.GRUPO_RISCO.getSigla());
                context().getExternalContext().getSessionMap().put(
                        "MetaCRMControle", hist);

            }
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
            if (clienteControle == null) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("msg_erro", "Cliente Não Inicializado.");
            }
            clienteControle.pegarClienteTelaCliente();
            clienteControle.setClienteMensagemSelecionada(msg);
            ClienteVO cliente = clienteControle.getClienteVO();
            HistoricoContatoControle historico = (HistoricoContatoControle) JSFUtilities.getManagedBean("HistoricoContatoControle");
            historico.setClienteVO(cliente);
            return msg.getTipomensagem().getNavegacao();
        }
    }

    public String caixa() throws Exception {
        return irParaCaixaEmAberto(null, null);
    }

    public void permisaoClienteMensagemObservacaoGeral() {
        try {
            if (getMensagemObservacaoCliente().getMensagem().contains("Untitled document")) {
                getMensagemObservacaoCliente().setMensagem(
                        getMensagemObservacaoCliente().getMensagem().replaceAll(
                                "(?i)</?(body|html|head|title)\\b[^>]*>", ""));
                getMensagemObservacaoCliente().setMensagem(
                        getMensagemObservacaoCliente().getMensagem().replaceAll(
                                "Untitled document", "<html><body>") + "</body></html>");
            }

            gravarClienteObservacaoComLog();
            consultarObservacoesDoCliente();
            setApresentarPanelObservacao(false);
            setApresentarAlteracao(true);

            getMensagemObservacaoCliente().setMensagem("");
            limparMsg();
            montarSucessoGrowl("Observação incluída com Sucesso!");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void gravarClienteObservacaoComLog() throws Exception {
        Connection con = getFacade().getClienteObservacao().getCon();
        try {
            ClienteObservacaoVO clienteObservacaoVO = new ClienteObservacaoVO();
            clienteObservacaoVO.setClienteVO(getCliente());
            clienteObservacaoVO.setDataCadastro(Calendario.hoje());
            clienteObservacaoVO.setObservacao(getMensagemObservacaoCliente().getMensagem());
            clienteObservacaoVO.setUsuarioVO(getUsuarioLogado());

            con.setAutoCommit(false);
            getFacade().getClienteObservacao().incluir(clienteObservacaoVO);
            incluirLogClienteObservacao(con, null, clienteObservacaoVO, false);
            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void prepararObservacaoExclussao() {
        ClienteObservacaoVO clienteObervacaoParametro = (ClienteObservacaoVO) context().getExternalContext().getRequestMap().get("clienteMensagem");
        clienteObervacaoParametroExclusao = clienteObervacaoParametro;
    }

    public void excluirObservacao() {
        try {
            excluirObservacaoComLog();
            listaClienteObservacao.setCount(getFacade().getClienteObservacao().consultarQuantidadeListaObservacao(getCliente().getCodigo()));
            setClienteObservacoes(getFacade().getClienteObservacao().consultarObservacaoPaginado(getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, LISTA_PAGINADA_OBSERVACAO_LIMIT, 0));
            if (getClienteObservacoes().isEmpty()) {
                setApresentarAlteracao(false);
                setRetornoOncompleteListaObservacao("Richfaces.hideModalPanel('modalObservacaoConfirmarcao');Richfaces.hideModalPanel('mdlObservacoes')");
            } else {
                setRetornoOncompleteListaObservacao("Richfaces.hideModalPanel('modalObservacaoConfirmarcao')");
            }
            limparMsg();
            prepararAbrirObservacao();
            montarSucessoGrowl("Observação excluida com Sucesso!");
        } catch (Exception e) {
            montarErro(e);
            setRetornoOncompleteListaObservacao("Richfaces.hideModalPanel('modalObservacaoConfirmarcao')");
        }
    }

    public void excluirObservacaoComLog() throws Exception {
        Connection con = getFacade().getClienteObservacao().getCon();
        try {
            ClienteObservacaoVO clienteObervacaoNovo = getFacade().getClienteObservacao().consultarChavePrimaria(getClienteObervacaoParametroExclusao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            con.setAutoCommit(false);
            getFacade().getClienteObservacao().excluir(clienteObervacaoNovo);
            incluirLogClienteObservacao(con, null, clienteObervacaoNovo, true);
            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void selecionarObservacao() {
        try {
            ClienteObservacaoVO clienteObervacaoParametro = (ClienteObservacaoVO) context().getExternalContext().getRequestMap().get("clienteMensagem");
            for (int i = 0; i < getClienteObservacoes().size(); i++) {
                if (getClienteObservacoes().get(i).getCodigo() == clienteObervacaoParametro.getCodigo()) {
                    if (getClienteObservacoes().get(i).isAlterarObservacao()) {
                        getClienteObservacoes().get(i).setAlterarObservacao(false);
                        setAlterarClienteObersacao(false);
                    } else {
                        getClienteObservacoes().get(i).setAlterarObservacao(true);
                        setAlterarClienteObersacao(true);
                    }
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void selecionarObservacaoParaVisualizacao() {
        try {
            ClienteObservacaoVO clienteObervacaoParametro = (ClienteObservacaoVO) context().getExternalContext().getRequestMap().get("clienteMensagem");
            setOservacaoParaVisualizacao(clienteObervacaoParametro);
            setOnCompleteVisualizar("Richfaces.showModalPanel('mdlObservacoesVizualizar');");
        } catch (Exception e) {
            montarErro(e);
            setOnCompleteVisualizar("");
        }
    }

    public void alterarObservacao() {
        try {
            alterarObservacaoComLog();
            listaClienteObservacao.setCount(getFacade().getClienteObservacao().consultarQuantidadeListaObservacao(getCliente().getCodigo()));
            setClienteObservacoes(getFacade().getClienteObservacao().consultarObservacaoPaginado(getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, LISTA_PAGINADA_OBSERVACAO_LIMIT, 0));
            setAlterarClienteObersacao(false);
            limparMsg();
            montarSucessoGrowl("Observação alterada com Sucesso!");
        } catch (Exception e) {
            montarErro(e);
            setAlterarClienteObersacao(false);
        }

    }

    public void alterarObservacaoComLog() throws Exception {
        Connection con = getFacade().getClienteObservacao().getCon();
        try {
            ClienteObservacaoVO clienteObervacaoParametro = (ClienteObservacaoVO) context().getExternalContext().getRequestMap().get("clienteMensagem");
            ClienteObservacaoVO clienteSemAlteracao = getFacade().getClienteObservacao().consultarChavePrimaria(clienteObervacaoParametro.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            con.setAutoCommit(false);
            getFacade().getClienteObservacao().alterar(clienteObervacaoParametro);
            incluirLogClienteObservacao(con, clienteSemAlteracao, clienteObervacaoParametro, false);
            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Boolean validarPermisaoUsuario(String entidade, String nomeEntidade)
            throws Exception {
        return validarPermisaoUsuario(entidade, nomeEntidade, getCliente());
    }

    public String irParaCaixaEmAberto(Date dataInicio, Date dataFim) throws Exception {
        MovParcelaControle mpc = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        if (mpc == null) {
            mpc = new MovParcelaControle(getCliente().getPessoa().getNome());
        }

        mpc.setValorConsulta(getCliente().getPessoa().getNome());
        mpc.setMatricula(getCliente().getMatricula());
        mpc.setIncluirParcelasRecorrencia(Boolean.TRUE);
        mpc.setDataInicio(dataInicio);
        mpc.setDataTermino(dataFim);
        if (dataFim != null) {
            mpc.setNaoApresentarVencimentosDeMesesFuturos(false);
        }
        mpc.novo(false, false);

        context().getExternalContext().getSessionMap().put("MovParcelaControle", mpc);

        return "tela8";
    }

    public Boolean getPermitirAlteracaoMensagemObservacao() {
        return permitirAlteracaoMensagemObservacao;
    }

    public void setPermitirAlteracaoMensagemObservacao(Boolean permitirAlteracaoMensagemObservacao) {
        this.permitirAlteracaoMensagemObservacao = permitirAlteracaoMensagemObservacao;
    }

    public void inicializarUsuarioLogado() throws Exception {
        cliente.setUsuarioVO(new UsuarioVO());
        cliente.getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
        cliente.getUsuarioVO().setUsername(getUsuarioLogado().getUsername());
        cliente.getUsuarioVO().setUserOamd(getUsuarioLogado().getUserOamd());
        setResponsavelFreePass(new UsuarioVO());
        getResponsavelFreePass().setCodigo(getUsuarioLogado().getCodigo());
        getResponsavelFreePass().setUsername(getUsuarioLogado().getUsername());
        getResponsavelFreePass().setUserOamd(getUsuarioLogado().getUserOamd());
        setResponsavelVinculo(new UsuarioVO());
        getResponsavelVinculo().setCodigo(getUsuarioLogado().getCodigo());
        getResponsavelVinculo().setUsername(getUsuarioLogado().getUsername());
        getResponsavelVinculo().setUserOamd(getUsuarioLogado().getUserOamd());
        setResponsavelEdicaoPagamento(new UsuarioVO());
        getResponsavelEdicaoPagamento().setCodigo(getUsuarioLogado().getCodigo());
        getResponsavelEdicaoPagamento().setUsername(getUsuarioLogado().getUsername());
        getResponsavelEdicaoPagamento().setUserOamd(getUsuarioLogado().getUserOamd());
    }

    public void gravarClienteMensagemObservacaoClienteGeralJaValidado() {
        try {
            if (getMensagemObservacaoCliente().isNovoObj()) {
                getFacade().getClienteMensagem().incluir(
                        getMensagemObservacaoCliente());
                // LOG - INICIO

                try {
                    getMensagemObservacaoCliente().setObjetoVOAntesAlteracao(new ClienteMensagemVO());
                    getMensagemObservacaoCliente().setNovoObj(true);
                    registrarLogObjetoVO(getMensagemObservacaoCliente(),
                            getMensagemObservacaoCliente().getCodigo(),
                            "MENSAGEMOBSERVACAO", getMensagemObservacaoCliente().getCliente().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO(
                            "MENSAGEMOBSERVACAO",
                            getMensagemObservacaoCliente().getCliente().getPessoa().getCodigo(),
                            "ERRO AO GERAR LOG DE INCLUSÃO DE MENSAGEM OBSERVACAO",
                            this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                // LOG - FIM
            } else {
                ClienteMensagemVO clienteMSGVO = getFacade().getClienteMensagem().consultarPorChavePrimaria(
                        getMensagemObservacaoCliente().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getFacade().getClienteMensagem().alterar(
                        getMensagemObservacaoCliente());
                // LOG - INICIO

                try {
                    if (clienteMSGVO != null) {
                        getMensagemObservacaoCliente().setObjetoVOAntesAlteracao(clienteMSGVO);
                        registrarLogObjetoVO(getMensagemObservacaoCliente(),
                                getMensagemObservacaoCliente().getCodigo(), "MENSAGEMCONSULTOR", this.getCliente().getPessoa().getCodigo());
                    }
                } catch (Exception e) {
                    registrarLogErroObjetoVO(
                            "MENSAGEMOBSERVACAO",
                            getMensagemObservacaoCliente().getCliente().getPessoa().getCodigo(),
                            "ERRO AO GERAR LOG DE ALTERAÇÃO DE MENSAGEM OBSERVACAO",
                            this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                // LOG - FIM
            }

            getCliente().adicionarObjClienteMensagemVOs(
                    getMensagemObservacaoCliente());
            setMensagemID("msg_dados_gravados");
            setAbrirRichModalPanelUsuarioSenha(false);
            setSucesso(true);
            setErro(false);
            inicializarUsuarioLogado();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRichModalPanelUsuarioSenha(true);
            setSucesso(false);
            setErro(true);
        }
    }

    public void validarPermisaoUsuarioGravando(String entidade,
                                               String nomeEntidade) throws Exception {
        UsuarioVO usuario = getFacade().getControleAcesso().verificarLoginUsuario(
                cliente.getUsuarioVO().getCodigo(),
                cliente.getUsuarioVO().getSenha().toUpperCase());
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                return;
            }
            throw new Exception(
                    "O usuário informado não tem nenhum perfil acesso informado.");

        }
        for (Object o : usuario.getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;

            if (cliente.getEmpresa().getCodigo().equals(
                    usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(
                        getFacade().getPermissao().consultarPermissaos(
                                usuarioPerfilAcesso.getPerfilAcesso().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(
                        usuarioPerfilAcesso.getPerfilAcesso(),
                        usuario, entidade, nomeEntidade);

            }
        }
    }

    public void gravarClienteMensagemObservacaoClienteGeral() {
        try {
            // obterUsuarioLogado();
            if (!validarPermisaoUsuario("ObservacaoCliente",
                    "2.36 - Lançar observação para o cliente")) {
                validarPermisaoUsuarioGravando("ObservacaoCliente",
                        "2.36 - Lançar observação para o cliente");
            }
            if (getMensagemObservacaoCliente().isNovoObj()) {
                getFacade().getClienteMensagem().incluir(
                        getMensagemObservacaoCliente());
            } else {
                getFacade().getClienteMensagem().alterar(
                        getMensagemObservacaoCliente());
            }
            getCliente().adicionarObjClienteMensagemVOs(
                    getMensagemObservacaoCliente());
            setMensagemID("msg_dados_gravados");
            setAbrirRichModalPanelUsuarioSenha(false);
            setSucesso(true);
            setErro(false);
            inicializarUsuarioLogado();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRichModalPanelUsuarioSenha(true);
            setSucesso(false);
            setErro(true);
        }
    }

    public void carregarGraficosAcessos() {
        graficosAcessoCarregado = true;
        try {
            setListaSelectItemPeriodoAcessoCliente(getFacade().getPeriodoAcessoCliente().consultarPorPessoa(getCliente().getPessoa().getCodigo(),
                    true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
        }

    }

    public boolean isCarregarEstudio() {
        return carregarEstudio;
    }

    public void setCarregarEstudio(boolean carregarEstudio) {
        this.carregarEstudio = carregarEstudio;
    }

    public ContratoVO getContratoSelecionado() {
        return contratoSelecionado;
    }

    public void setContratoSelecionado(ContratoVO contrato) {
        this.contratoSelecionado = contrato;
    }

    public List<ChequeVO> getCheques() {
        return cheques;
    }

    public void setCheques(List<ChequeVO> cheques) {
        this.cheques = cheques;
    }

    public boolean isGraficosAcessoCarregado() {
        return graficosAcessoCarregado;
    }

    public void setGraficosAcessoCarregado(boolean graficosAcessoCarregado) {
        this.graficosAcessoCarregado = graficosAcessoCarregado;
    }

    public List<ClienteMensagemVO> getAvisos() {
        return avisos;
    }

    public void setAvisos(List<ClienteMensagemVO> avisos) {
        this.avisos = avisos;
    }

    public List<ClienteMensagemVO> getAvisosModal() {
        return avisosModal;
    }

    public void setAvisosModal(List<ClienteMensagemVO> avisosModal) {
        this.avisosModal = avisosModal;
    }

    public String getMensagemParq() {
        return mensagemParq;
    }

    public void setMensagemParq(String mensagemParq) {
        this.mensagemParq = mensagemParq;
    }

    public void validarUsuarioELiberarVaga() {
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                if (existeOperacaoDeRetornoPendente()) {
                    retornoLiberacaoDeVaga = "Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');Richfaces.showModalPanel('panelRetornoPendente');";
                    return;
                }

                List<MatriculaAlunoHorarioTurmaVO> matriculas = getFacade().getMatriculaAlunoHorarioTurma()
                        .consultarMatriculaAtivaPorContrato(contratoSelecionado.getCodigo(),
                                contratoSelecionado.getEmpresa().getCodigo(),
                                Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS);

                Date dataFimAtualMatricula;
                Integer codigoPessoa = getFacade().getPessoa().consultarCodigoPessoaPorCodigoContrato(contratoSelecionado.getCodigo());
                for (MatriculaAlunoHorarioTurmaVO matricula : matriculas) {
                    dataFimAtualMatricula = matricula.getDataFim();
                    if (Calendario.maior(Uteis.somarDias(Calendario.hoje(), -1), contratoSelecionado.getVigenciaAteAjustada())) {
                        matricula.setDataFim(Uteis.somarDias(Calendario.hoje(), -1));
                    } else {
                        matricula.setDataFim(contratoSelecionado.getVigenciaAteAjustada());
                    }
                    if (Calendario.maior(matricula.getDataInicio(), matricula.getDataFim())) {
                        getFacade().getMatriculaAlunoHorarioTurma().excluir(matricula);
                    } else {
                        getFacade().getMatriculaAlunoHorarioTurma().alterar(matricula);
                    }
                    registraLogs(codigoPessoa, dataFimAtualMatricula, matricula);
                }
                registrarContratoOperacaoParaLiberacaoDeVaga();
                if (retornoLiberacaoDeVaga == null) {
                    setMsgAlert(retornoLiberacaoDeVaga);
                }
                setMsgAlert("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');Richfaces.showModalPanel('panelConfirmaLiberacaoDeVaga');");
                montarSucesso("Aula liberada com sucesso");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMsgAlert("");
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        retornoLiberacaoDeVaga = null;
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.autorizar("Liberar vaga na turma de aluno inativo", "LiberarVagaNaTurma",
                "Você precisa da permissão: 9.34 - Liberar vaga na turma de aluno inativo",
                "form:idlistacontratos", listener);
    }

    public void registrarClickEquipeControle() {
        // Rastreamento de click - Equipe Controle
        notificarRecursoEmpresa(RecursoSistema.HISTORICO_DE_FALTAS_ALUNO_TURMA);
    }

    // Invocado via refelxao pela classe AutorizacaoFuncionalidadeControle
    public void liberarVaga() {
        try {
            List<MatriculaAlunoHorarioTurmaVO> matriculas = getFacade().getMatriculaAlunoHorarioTurma()
                    .consultarMatriculaAtivaPorContrato(contratoSelecionado.getCodigo(),
                            contratoSelecionado.getEmpresa().getCodigo(),
                            Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS);

            Date dataFimAtualMatricula;
            Integer codigoPessoa = getFacade().getPessoa().consultarCodigoPessoaPorCodigoContrato(contratoSelecionado.getCodigo());
            for (MatriculaAlunoHorarioTurmaVO matricula : matriculas) {
                dataFimAtualMatricula = matricula.getDataFim();
                matricula.setDataFim(contratoSelecionado.getVigenciaAteAjustada());
                getFacade().getMatriculaAlunoHorarioTurma().alterar(matricula);
                registraLogs(codigoPessoa, dataFimAtualMatricula, matricula);
            }

            registrarContratoOperacaoParaLiberacaoDeVaga();
            mapaMostrar.put("ALTERACAO_CONTRATO", Boolean.FALSE);
            if (retornoLiberacaoDeVaga == null) {
                setMsgAlert(retornoLiberacaoDeVaga);
            }
            setMsgAlert("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');Richfaces.showModalPanel('panelConfirmaLiberacaoDeVaga');");
            montarSucesso("Aula liberada com sucesso");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert("");
        }
    }

    private void registraLogs(Integer codigoPessoa, Date dataFimAtual, MatriculaAlunoHorarioTurmaVO mat) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao("ALTERAÇÃO");
        log.setNomeEntidade("MatriculaHorarioTurma");
        log.setChavePrimaria(mat.getCodigo().toString());
        log.setPessoa(codigoPessoa);
        log.setNomeCampo("Liberar Vaga - DataFim");
        log.setValorCampoAnterior(Calendario.getData(dataFimAtual, "dd/MM/yyyy"));
        log.setValorCampoAlterado(Calendario.getData(mat.getDataFim(), "dd/MM/yyyy"));
        log.setDescricao("O contrato " + contratoSelecionado.getCodigo()
                + " estava inativo e ainda ocupando vagas em turmas do sistema. "
                + " O usuário '" + getUsuarioLogado().getNome() + "' decidiu então liberar as vagas.");
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        getFacade().getLog().incluir(log);
    }

    private boolean existeOperacaoDeRetornoPendente() throws Exception {
        return getFacade().getContratoOperacao().existeOperacaoPendenteDeRetorno(contratoSelecionado.getCodigo(), Calendario.hoje());
    }

    private void registrarContratoOperacaoParaLiberacaoDeVaga() throws Exception {
        ContratoOperacaoVO contratoOperacaoVO = getFacade().getContratoOperacao().novo();
        contratoOperacaoVO.setContrato(contratoSelecionado.getCodigo());
        contratoOperacaoVO.setTipoOperacao(TipoOperacaoContratoEnum.LIBERAR_VAGA.getSigla());
        contratoOperacaoVO.setJustificativa("Permitir que vagas de alunos inativos possam ser preenchidas.");
        contratoOperacaoVO.setResponsavel(getUsuarioLogado());
        contratoOperacaoVO.setObservacao("Usuário liberou vaga de aluno inativo.");
        getFacade().getContratoOperacao().incluir(contratoOperacaoVO);
    }

    public List<ConviteAulaExperimentalVO> getConvites() {
        return convites;
    }

    public void setConvites(List<ConviteAulaExperimentalVO> convites) {
        this.convites = convites;
    }

    public void abrirModalEnviarConvite() {
        try {
            if (tiposConvites == null || tiposConvites.isEmpty()) {
                tiposConvites = new ArrayList<SelectItem>();
                List<TipoConviteAulaExperimentalVO> todosTipos = getFacade().getTipoConviteAulaExperimental().consultarTodos(getEmpresaLogado().getCodigo());
                todosTipos = Ordenacao.ordenarLista(todosTipos, "descricao");
                for (TipoConviteAulaExperimentalVO t : todosTipos) {
                    tiposConvites.add(new SelectItem(t.getCodigo(), t.getDescricao()));
                }
            }

            if (cliente.getSituacao().equals(SituacaoClienteEnum.VISITANTE.getCodigo())
                    || cliente.getSituacao().equals(SituacaoClienteEnum.CANCELADO.getCodigo())
                    || cliente.getSituacao().equals(SituacaoClienteEnum.INATIVO.getCodigo())
                    || cliente.getSituacao().equals(SituacaoClienteEnum.VENCIDO.getCodigo())) {
                setMsgAlert("Richfaces.showModalPanel('modalLancarConvite');");
            } else {
                throw new Exception("Você não pode enviar um convite de Aula Experimental para um cliente com contrato vigente.");
            }
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void enviarConvite() {
        try {
            getFacade().getConviteAulaExperimentalService().gerarConviteViaWeb(OrigemSistemaEnum.ZW, getUsuarioLogado().getCodigo(),
                    codigoTipoConvite, null, null, cliente.getCodigo(), getUsuarioLogado().getColaboradorVO().getCodigo());
            setMsgAlert("Richfaces.hideModalPanel('modalLancarConvite');");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public String getRetornoLiberacaoDeVaga() {
        return retornoLiberacaoDeVaga;
    }

    public List<MovProdutoVO> getListaProdutosComValidade() {
        if (listaProdutosComValidade == null) {
            listaProdutosComValidade = new ArrayList<MovProdutoVO>();
        }
        return listaProdutosComValidade;
    }

    public void setListaProdutosComValidade(List<MovProdutoVO> listaProdutosComValidade) {
        this.listaProdutosComValidade = listaProdutosComValidade;
    }

    public void setRetornoLiberacaoDeVaga(String retornoLiberacaoDeVaga) {
        this.retornoLiberacaoDeVaga = retornoLiberacaoDeVaga;
    }

    public List<SelectItem> getTiposConvites() {
        return tiposConvites;
    }

    public void setTiposConvites(List<SelectItem> tiposConvites) {
        this.tiposConvites = tiposConvites;
    }

    public Integer getCodigoTipoConvite() {
        return codigoTipoConvite;
    }

    public void setCodigoTipoConvite(Integer codigoTipoConvite) {
        this.codigoTipoConvite = codigoTipoConvite;
    }

    public void visualizarDocumentos() {
        notificarRecursoEmpresa(RecursoSistema.DOCUMENTOS_CONTRATOS_CLIENTE);
        setMsgAlert("");
        try {
            ContratoVO obj = (ContratoVO) context().getExternalContext().getRequestMap().get("contratoTbl");
            docs = getFacade().getContratoAssinaturaDigital().consultarPorContrato(obj.getCodigo());
            if (docs.getAssinatura() == null || docs.getAssinatura().isEmpty()) {
                LoginControle loginControle = (LoginControle) JSFUtilities.getManagedBean(LoginControle.class.getSimpleName());
                loginControle.abrirModalDadosAppAssinatura();
            }
            docs.initImagens();
            setMsgAlert("Richfaces.showModalPanel('visualizacaoDocs');");
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void imprimirContrato() {
        notificarRecursoEmpresa(RecursoSistema.IMPRESSAO_CONTRATO_CLIENTE);
        setMsgAlert("");
        setMensagemDetalhada("");
        try {
            ContratoVO obj = (ContratoVO) context().getExternalContext().getRequestMap().get("contratoTbl");
            setContratoImpressao(obj);
            if (getContratoImpressao().getCodigo() != 0) {
                JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", getContratoImpressao().getEmpresa());
                getFacade().getContratoTextoPadrao().consultarHtmlContrato(getContratoImpressao().getCodigo(),true, true);
            } else {
                throw new Exception("Não foi possível emitir o contrato. Dados não encontrados!");
            }
            notificarRecursoEmpresa(RecursoSistema.IMPRESSAO_CONTRATO_CLIENTE);
            setMsgAlert("abrirPopup('VisualizarContrato', 'RelatorioContrato', 730, 545);");
        } catch (Exception e) {
            montarErro(e.getMessage());

        }
    }

    public List<MovPagamentoVO> prepararContratoEPagamento() throws Exception {
        setContratoImpressao(new ContratoVO());
        // obter contrato
        ContratoVO obj = (ContratoVO) context().getExternalContext().getRequestMap().get("contratoTbl");
        ContratoVO c = getFacade().getContrato().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
        setContratoImpressao(c);
        // setar atributos do contrato
        //getContratoImpressao().setMovParcelaVOs(getFacade().getMovParcela().consultarPorContrato(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getContratoImpressao().setMovParcelaVOs(getFacade().getMovParcela().consultarPorContratoNaoRenegociadaNegociada(c.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getContratoImpressao().setContratoTextoPadrao(getFacade().getContratoTextoPadrao().consultarPorCodigoContrato(getContratoImpressao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getContratoImpressao().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(getContratoImpressao().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getContratoImpressao().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getContratoImpressao().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getContratoImpressao().setContratoDuracao(getFacade().getContratoDuracao().consultarContratoDuracoes(getContratoImpressao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        if (getContratoImpressao().isVendaCreditoTreino()) {
            getContratoImpressao().getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(getContratoImpressao().getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        getContratoImpressao().setContratoHorario(getFacade().getContratoHorario().consultarContratoHorarios(getContratoImpressao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getContratoImpressao().setContratoModalidadeVOs(getFacade().getContratoModalidade().consultarContratoModalidades(getContratoImpressao().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        getContratoImpressao().setResponsavelContrato(getFacade().getUsuario().consultarPorChavePrimaria(getContratoImpressao().getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getContratoImpressao().getPlano().setPlanoTextoPadrao(getContratoImpressao().getContratoTextoPadrao().getPlanoTextoPadrao());
        // pesquisar pagamentos já efetuados para informar no contrato.
        return (List<MovPagamentoVO>) getFacade().getMovPagamento().consultarPagamentoDeUmContrato(getContratoImpressao().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public ContratoVO getContratoImpressao() {
        return contratoImpressao;
    }

    public void setContratoImpressao(ContratoVO contratoImpressao) {
        this.contratoImpressao = contratoImpressao;
    }

    public void apresentarOperacaoNoMenuCliente() throws Exception {
        this.mapaMostrar = getFacade().getContrato().apresentarOperacaoNoMenuCliente(contratoSelecionado, getClienteVO(), getEmpresaLogado(), getUsuarioLogado(), listaContratosCliente);
    }

    public Map<String, Boolean> getMapaMostrar() {
        return mapaMostrar;
    }

    public void setMapaMostrar(Map<String, Boolean> mapaMostrar) {
        this.mapaMostrar = mapaMostrar;
    }

    public List<HistoricoContratoVO> getListaHistoricoContrato() {
        return listaHistoricoContrato;
    }

    public void setListaHistoricoContrato(List<HistoricoContratoVO> listaHistoricoContrato) {
        this.listaHistoricoContrato = listaHistoricoContrato;
    }

    public List<ContratoOperacaoVO> getListaContratoOperacao() {
        return listaContratoOperacao;
    }

    public void setListaContratoOperacao(List<ContratoOperacaoVO> listaContratoOperacao) {
        this.listaContratoOperacao = listaContratoOperacao;
    }

    public boolean isFinanceiroContrato() {
        return financeiroContrato;
    }

    public void setFinanceiroContrato(boolean financeiroContrato) {
        this.financeiroContrato = financeiroContrato;
    }

    public void preencherItensCobranca() throws Exception {

        TipoCobrancaEnum[] tiposRemessa = {TipoCobrancaEnum.EDI_DCC,TipoCobrancaEnum.EDI_DCO};
        List<RemessaItemVO> itensRemessa = getFacade().getZWFacade().getRemessaItem().
                consultarTelaCliente(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0, tiposRemessa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0);
        listaRemessa.setCount(getFacade().getZWFacade().getRemessaItem().obterCountRemessaCliente(cliente.getPessoa().getCodigo(), tiposRemessa));


        TipoCobrancaEnum[] tiposBoleto = {TipoCobrancaEnum.BOLETO};
        List<RemessaItemVO> itensBoleto = getFacade().getZWFacade().getRemessaItem().
                consultarTelaCliente(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0, tiposBoleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0);
        listaBoleto.setCount(getFacade().getZWFacade().getRemessaItem().obterCountRemessaCliente(cliente.getPessoa().getCodigo(), tiposBoleto));

        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaEstorno", itensRemessa);
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.nrPaginasItensRemessaEstorno", 10);
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaBoleto", itensBoleto);
        GestaoRemessasControle control = (GestaoRemessasControle) getControlador(GestaoRemessasControle.class);
        control.validarApresentacaoDaColunaRetornoManual();

        //lista de transações normais
        List<TransacaoVO> transacoes = getFacade().getTransacao().consultarTelaCliente(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0);
        listaTrasacoes.setCount(getFacade().getTransacao().obterCountTransacaoCliente(cliente.getPessoa().getCodigo()));
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoes", transacoes);

        //lista de transações de verificação do cartão
        List<TransacaoVO> transacoesVerificacao = getFacade().getTransacao().consultarTelaClienteTransacaoVerificacao(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0);
        listaTransacoesVerificacao.setCount(getFacade().getTransacao().obterCountTransacaoVerificacaoCliente(cliente.getPessoa().getCodigo()));
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoesVerificacao", transacoesVerificacao);

        listaPixVo = getFacade().getPix().consultarPorPessoaTelaCliente(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0);
        listaPix.setCount(getFacade().getPix().quantidadePorPessoa(cliente.getPessoa().getCodigo()));

        listaCancelamentoGetCardVo = getFacade().getTransacao().consultarPorPessoaTelaCliente(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0);
        listaCancelamentoGetCard.setCount(getFacade().getTransacao().quantidadePorPessoa(cliente.getPessoa().getCodigo()));

        listaBoletoGeralVo = getFacade().getBoleto().consultarTelaCliente(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0);
        listaBoletoGeral.setCount(getFacade().getBoleto().consultarTelaClienteQtd(cliente.getPessoa().getCodigo()));

        listaMovContaVo = getFacade().getMovConta().consultarTelaCliente(cliente.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0);
        listaMovConta.setCount(getFacade().getMovConta().quantidadePorPessoaTelaCliente(cliente.getPessoa().getCodigo()));
    }

    public void verMaisTransacoes() throws Exception {
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.numeroTransacoesExibir", 0);
    }

    public void verMenosTransacoes() throws Exception {
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.numeroTransacoesExibir", 10);
    }

    private boolean validarPermissaoParaEstornarContrato() throws Exception {
        return getFacade().getContrato().validarPermissaoParaEstornarContrato(getContratoSelecionado(), getClienteVO(), getEmpresaLogado(), getUsuarioLogado());
    }

    public void abrirHistoricoVinculo() {
        try {
            notificarRecursoEmpresa(RecursoSistema.VER_HISTORICO_VINCULO_ALUNO);
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean(ClienteControle.class.getSimpleName());
            clienteControle.pegarClienteTelaCliente();
            HistoricoVinculoControle historicoControle = (HistoricoVinculoControle) JSFUtilities.getManagedBean(HistoricoVinculoControle.class.getSimpleName());
            historicoControle.historicoCliente();
        } catch (Exception ex) {

        }
    }

    public void paintFoto(OutputStream out, Object data) throws Exception {
        if (getCliente().getPessoa().getFoto() == null || getCliente().getPessoa().getFoto().length == 0) {
            recarregarFoto(false, getCliente(), false);
        }
        SuperControle.paintFoto(out, getCliente().getPessoa().getFoto());
    }

    public void abrirHistoricoContato() {
        try {
            HistoricoContatoControle contatoControle = (HistoricoContatoControle) JSFUtilities.getManagedBean("HistoricoContatoControle");
            contatoControle.setClienteVO(getCliente());
            contatoControle.inicializarHistoricoContato();
        } catch (Exception ignored) {

        }
    }

    public void abrirHistoricoDotz() {
        try {
            HistoricoPontosParceiroFidelidadeControle controle = (HistoricoPontosParceiroFidelidadeControle) JSFUtilities.getManagedBean("HistoricoPontosParceiroFidelidadeControle");
            controle.setDataInicio(null);
            controle.setDataTermino(null);
            controle.setPessoa(getCliente().getPessoa());
            controle.setApresentarVoltar(false);
            controle.consultar();
        } catch (Exception ignored) {

        }
    }

    public void consultarControleCreditoTreino() throws Exception {
        if (this.contratoSelecionado.isVendaCreditoTreino()) {
            this.contratoSelecionado.setListaControleCreditoTreino(getFacade().getControleCreditoTreino().consultar(this.contratoSelecionado.getCodigo()));
            this.totalFaltaContratoCredito = getFacade().getControleCreditoTreino().consultarTotalOperacao(this.contratoSelecionado.getCodigo(), TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO);
            this.totalPresencaContratoCredito = getFacade().getControleCreditoTreino().consultarTotalOperacao(this.contratoSelecionado.getCodigo(), TipoOperacaoCreditoTreinoEnum.UTILIZACAO);
            try {
                configuracaoPlanoCreditoTreino = getFacade().getConfiguracaoSistema().buscarPorCodigo(1, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception ex) {
                montarErro(ex);
            }
        }
    }

    public String getConfigNomenclaturaVendaCredito() {
        return configuracaoPlanoCreditoTreino != null ? OpcoesNomenclaturaVendaCreditoEnum.getDescricao(configuracaoPlanoCreditoTreino.getNomenclaturaVendaCredito()) : "Crédito de Treino";
    }

    public void novoAjusteManualControleCreditoTreino() {
        notificarRecursoEmpresa(RecursoSistema.CONTROLE_CREDITO_CLIENTE);
        try {
            setMsgAlert("");
            this.controleCreditoTreinoVO = new ControleCreditoTreinoVO();
            controleCreditoTreinoVO.setTipoOperacaoCreditoTreinoEnum(TipoOperacaoCreditoTreinoEnum.AJUSTE_MANUAL);
            controleCreditoTreinoVO.setUsuarioVO(getUsuarioLogado());
            controleCreditoTreinoVO.setContratoVO(this.contratoSelecionado);
            setApresentarBotaoGrvControleCredito(true);
            setMsgAlert("Richfaces.showModalPanel('modalControleCreditoTreino');");
            montarSucesso("msg_entre_dados");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert("");
        }
    }

    public void validarPermisaoAjusteManualCredito() {
        try {
            limparMsg();
            ControleCreditoTreinoVO.validarDadosInclusao(controleCreditoTreinoVO);
            AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    getFacade().getControleCreditoTreino().incluir(controleCreditoTreinoVO, getCliente().getCodigo(), getFacade().getSituacaoClienteSinteticoDW());
                    setApresentarBotaoGrvControleCredito(false);
                    getFacade().getSituacaoClienteSinteticoDW().atualizarBaseOffLineZillyonAcesso((String) JSFUtilities.getFromSession(JSFUtilities.KEY), getCliente().getPessoa().getCodigo());
                    contratoSelecionado.setListaControleCreditoTreino(getFacade().getControleCreditoTreino().consultar(contratoSelecionado.getCodigo()));
                    contratoSelecionado.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(contratoSelecionado.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    setSucesso(true);
                    setMensagem("Dados gravados com sucesso!");
                    setMsgAlert("document.getElementById('form:btnAtualizaCliente').click();");
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    setMsgAlert(getMensagemNotificar());
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete() + ";document.getElementById('form:btnAtualizaCliente').click();";
                }
            };
            auto.autorizar("Autorização Ajuste Manual Crédito de Treino", "NOVOAJUSTEMANUALCREDITOTREINO",
                    "Você precisa da permissão \"Lançar ajuste manual de crédito de treino\"",
                    "form:listaHistoricoCreditoTreino, form:mensagemControleCredito, form:pgInfoCreditoTreinoContrato, form:scControleCreditoTreino, form:tabelaContratoModalidade,formControleCreditoTreino:mensagemControleCredito,formControleCreditoTreino:botoesControleCredito", listener);

        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void gravarAjusteManualControleCreditoTreino() {
        try {
            getFacade().getControleCreditoTreino().incluir(controleCreditoTreinoVO, getCliente().getCodigo(), getFacade().getSituacaoClienteSinteticoDW());
            setApresentarBotaoGrvControleCredito(false);
            getFacade().getSituacaoClienteSinteticoDW().atualizarBaseOffLineZillyonAcesso((String) JSFUtilities.getFromSession(JSFUtilities.KEY), getCliente().getPessoa().getCodigo());
            this.contratoSelecionado.setListaControleCreditoTreino(getFacade().getControleCreditoTreino().consultar(this.contratoSelecionado.getCodigo()));
            this.contratoSelecionado.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(this.contratoSelecionado.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            setSucesso(true);
            setMensagem("Dados gravados com sucesso!");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public Boolean getMenuContrato() throws Exception {
        // Regra de necogio um cliente uma vez matriculado com a configuracão da
        // empresa nao permitir contrato concomitantes nao podera
        // realizar outro contrato de matricula somente rematricula ou renovacao
        // dia 05/05/2010 MAX.
        if (!UtilReflection.objetoNulo(this.cliente, "getSituacaoClienteSinteticoVO().getSituacao()") && this.cliente.getSituacaoClienteSinteticoVO().getSituacao().equals("VI") && UteisValidacao.emptyList(getListaContratos())) {
            return true;
        }
        List<ContratoVO> lista = getListaContratos();
        temContratoNaEmpresaAtual = false;
        // se a empresa atual do cliente é diferente de alguma empresa dos seus
        // contratos
        // deve permitir uma nova linha de contratos da empresa do usuário que
        // está logado.

        for (ContratoVO ct : lista) {
            if (ct.getEmpresa().getCodigo().equals(cliente.getEmpresa().getCodigo())
                && ((UtilReflection.objetoMaiorQueZero(ct ,"getPessoaOriginal().getCodigo()")
                    && (!ct.getPessoaOriginal().getCodigo().equals(cliente.getPessoa().getCodigo()) && ct.getSituacao().equals("AT") // cliente que recebeu direto de uso e contrato está ativo não pode lançar contrato se empresa não permitir concomitancia
                        || (ct.getPessoaOriginal().getCodigo().equals(cliente.getPessoa().getCodigo()) && (ct.getSituacao().equals("IN") ||ct.getSituacao().equals("CA") ))))  // cliente que cedeu direto de uso e contrato não está ativo não pode lançar contrato se empresa não permitir concomitancia, deve renovar/rematricular
                    || (!UtilReflection.objetoMaiorQueZero(ct ,"getPessoaOriginal().getCodigo()") && UteisValidacao.emptyNumber(ct.getContratoResponsavelRenovacaoMatricula()) && UteisValidacao.emptyNumber(ct.getContratoResponsavelRematriculaMatricula())))) { // nesse caso, um contrato anterior pertence a pessoa, mas a renovação/rematircula foi  transferida. Então essa linha de contratos não pode impedir de lançar um contrato para esse cliente
                temContratoNaEmpresaAtual = true;
            }
        }
        // montarListaContrato();
        try {
            if (cliente.getEmpresa().getPermiteContratosConcomintante() || !temContratoNaEmpresaAtual) {
                return true;
            } else {
                return false;
            }
        } catch (NullPointerException e) {
            redirect("/faces/clientes.jsp");
            e.printStackTrace();
            return false;
        }
    }

    private void carregarListaPaginacao(ListaPaginadaTO paginacao, String codigo) throws Exception {
        if (codigo.equals(LISTA_COMPRAS)) {
            consultarListaProdutos(paginacao);
        } else if (codigo.equals(LISTA_PARCELAS)) {
            consultarListaParcelas(paginacao);
        } else if (codigo.equals(LISTA_PAGAMENTOS)) {
            consultarPagamentos(paginacao);
        } else if (codigo.equals(LISTA_BOLETOS)) {
            consultarBoleto(paginacao);
        } else if (codigo.equals(LISTA_REMESSA)) {
            consultarRemessa(paginacao);
        } else if (codigo.equals(LISTA_TRANSACOES)) {
            consultarTransacoes(paginacao);
        } else if (codigo.equals(LISTA_TRANSACOES_VERIFICACAO)) {
            consultarTransacoesVerificacao(paginacao);
        } else if (codigo.equals(LISTA_OBSERVACAO)) {
            consultarClienteObservacao(paginacao);
        } else if (codigo.equals(LISTA_PIX)) {
            consultarPix(paginacao);
        } else if (codigo.equals(LISTA_GETCARD)) {
            consultarGetCard(paginacao);
        } else if (codigo.equals(LISTA_BOLETO_GERAL)) {
            consultarBoletoGeral(paginacao);
            acaoMarcarTodosBoleto();
        } else if (codigo.equals(LISTA_MOV_CONTA)) {
            consultarContas(paginacao);
        }
    }

    private void consultarListaProdutos(ListaPaginadaTO paginacao) throws Exception {
        listaHistoricoProduto = getFacade().getMovProduto().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), paginacao.getLimit(), paginacao.getOffset());
        validarOpcoesProdutos();
    }

    private void consultarListaParcelas(ListaPaginadaTO paginacao) throws Exception {
        listaHistoricoParcelas = getFacade().getMovParcela().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), paginacao.getLimit(), paginacao.getOffset(), paginacao.getOrderBy(), paginacao.isOrderByDesc());
        if (getIntegraProtheus()) {
            getFacade().getMovParcela().montarInformacoesExtratoAluno(listaHistoricoParcelas, paginacao.getOrderBy(), paginacao.isOrderByDesc());
        }
    }

    private void consultarPagamentos(ListaPaginadaTO paginacao) throws Exception {
        listaHistoricoPagamentos = getFacade().getMovPagamento().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), paginacao.getLimit(), paginacao.getOffset());
    }

    private void consultarRemessa(ListaPaginadaTO paginacao) throws Exception {
        TipoCobrancaEnum[] tiposCobranca = {TipoCobrancaEnum.EDI_DCC,TipoCobrancaEnum.EDI_DCO};
        List<RemessaItemVO> itensRemessa = getFacade().getZWFacade().getRemessaItem().
                consultarTelaCliente(cliente.getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset(), tiposCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0);
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaEstorno", itensRemessa);
    }

    private void consultarBoleto(ListaPaginadaTO paginacao) throws Exception {
        TipoCobrancaEnum[] tiposCobranca = {TipoCobrancaEnum.BOLETO};
        List<RemessaItemVO> itensBoleto = getFacade().getZWFacade().getRemessaItem().
                consultarTelaCliente(cliente.getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset(), tiposCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0);
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaBoleto", itensBoleto);
    }

    private void consultarTransacoes(ListaPaginadaTO paginacao) throws Exception {
        List<TransacaoVO> transacoes = getFacade().getTransacao().consultarTelaCliente(cliente.getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset());
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoes", transacoes);
    }

    private void consultarTransacoesVerificacao(ListaPaginadaTO paginacao) throws Exception {
        List<TransacaoVO> transacoesverificacao = getFacade().getTransacao().consultarTelaClienteTransacaoVerificacao(cliente.getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset());
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoesVerificacao", transacoesverificacao);
    }

    private void consultarClienteObservacao(ListaPaginadaTO paginacao) throws Exception {
        setClienteObservacoes(getFacade().getClienteObservacao().consultarObservacaoPaginado(getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, paginacao.getLimit(), paginacao.getOffset()));
    }

    private void consultarPix(ListaPaginadaTO paginacao) throws Exception {
        setListaPixVo(getFacade().getPix().consultarPorPessoaTelaCliente(getCliente().getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset()));
    }

    private void consultarGetCard(ListaPaginadaTO paginacao) throws Exception {
        setListaCancelamentoGetCardVo(getFacade().getTransacao().consultarPorPessoaTelaCliente(getCliente().getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset()));
    }

    private void consultarContas(ListaPaginadaTO paginacao) throws Exception {
        setListaMovContaVo(getFacade().getMovConta().consultarTelaCliente(getCliente().getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset()));
    }

    private void consultarBoletoGeral(ListaPaginadaTO paginacao) throws Exception {
        setListaBoletoGeralVo(getFacade().getBoleto().consultarTelaCliente(getCliente().getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset()));
    }

    private ListaPaginadaTO obterPaginacaoPorCodigo(String codigo) throws Exception {
        if (codigo.equals(LISTA_COMPRAS)) {
            return listaCompras;
        } else if (codigo.equals(LISTA_PARCELAS)) {
            return listaParcelas;
        } else if (codigo.equals(LISTA_PAGAMENTOS)) {
            return listaPagamentos;
        } else if (codigo.equals(LISTA_BOLETOS)) {
            return listaBoleto;
        } else if (codigo.equals(LISTA_REMESSA)) {
            return listaRemessa;
        } else if (codigo.equals(LISTA_TRANSACOES)) {
            return listaTrasacoes;
        } else if (codigo.equals(LISTA_TRANSACOES_VERIFICACAO)) {
            return listaTransacoesVerificacao;
        } else if (codigo.equals(LISTA_OBSERVACAO)) {
            return listaClienteObservacao;
        } else if (codigo.equals(LISTA_PIX)) {
            return listaPix;
        } else if (codigo.equals(LISTA_BOLETO_GERAL)) {
            return listaBoletoGeral;
        } else if (codigo.equals(LISTA_MOV_CONTA)) {
            return listaMovConta;
        }
        return null;
    }

    public void proximaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.proximaPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void paginaAnterior(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.paginaAnterior();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void ultimaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.ultimaPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void primeiraPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.primeiraPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOffset(0);
        carregarListaPaginacao(paginacao, codigo);
    }

    public void exportarControleCreditoTreino(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = ((ExportadorListaControle) getControlador(ExportadorListaControle.class));
        StringBuilder filtro = new StringBuilder();
        //filtro.append("Cliente: ").append(this.clienteVO.getPessoa().getNome());
        filtro.append("  Contrato: ").append(this.contratoSelecionado.getCodigo());
        exportadorListaControle.exportar(evt, this.contratoSelecionado.getListaControleCreditoTreino(), filtro.toString(), null);
    }

    public void visualizarControleCreditoTreino() {
        setMsgAlert("Richfaces.showModalPanel('modalControleCreditoTreino');");
    }

    private void validarApresentarAtualizarBV() throws Exception {
        setClienteVO(getCliente());
        obterQuestionario();
        if (this.clienteVO.getSituacao().equals("VI")
                || this.clienteVO.getSituacao().equals("IN")) {
            String retorno = questionarioSerRespondidoPeloCliente();
            if (retorno.equals("questionario")) {
                setApresentarAtualisarBV(true);
            }
        }
    }

    public String abrirQuestionarioBV() {
        try {
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
            clienteControle.setClienteVO(getCliente());
            clienteControle.setPessoaVO(getCliente().getPessoa());
            clienteControle.obterQuestionario();
            clienteControle.setQuestionarioSemCliente(true);
            String retorno = "";
            if (this.cliente.getSituacao().equals("VI")
                    || this.cliente.getSituacao().equals("IN")) {
                retorno = clienteControle.questionarioSerRespondidoPeloCliente();
                if (retorno.equals("questionario")) {
                    setApresentarAtualisarBV(true);
                    clienteControle.getQuestionarioClienteVO().setData(Calendario.hoje());
                }
            }
            return retorno;
        } catch (Exception err) {
            montarErro(err);
        }
        return "";
    }

    public ConfiguracaoSistemaVO getConfiguracaoPlanoCreditoTreino() {
        return configuracaoPlanoCreditoTreino;
    }

    public void setConfiguracaoPlanoCreditoTreino(ConfiguracaoSistemaVO configuracaoPlanoCreditoTreino) {
        this.configuracaoPlanoCreditoTreino = configuracaoPlanoCreditoTreino;
    }

    public Date getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public boolean isDebitoContaCorrente() {
        return debitoContaCorrente;
    }

    public void setDebitoContaCorrente(boolean debitoContaCorrente) {
        this.debitoContaCorrente = debitoContaCorrente;
    }

    public boolean isPermitirTransferenciaCredito() {
        return permitirTransferenciaCredito;
    }

    public void setPermitirTransferenciaCredito(boolean permitirTransferenciaCredito) {
        this.permitirTransferenciaCredito = permitirTransferenciaCredito;
    }

    public boolean isApresentarTopo() {
        return apresentarTopo;
    }

    public void setApresentarTopo(boolean apresentarTopo) {
        this.apresentarTopo = apresentarTopo;
    }

    public ControleCreditoTreinoVO getControleCreditoTreinoVO() {
        return controleCreditoTreinoVO;
    }

    public void setControleCreditoTreinoVO(ControleCreditoTreinoVO controleCreditoTreinoVO) {
        this.controleCreditoTreinoVO = controleCreditoTreinoVO;
    }

    public List<NegociacaoEventoContratoTO> getEventos() {
        return eventos;
    }

    public void setEventos(List<NegociacaoEventoContratoTO> eventos) {
        this.eventos = eventos;
    }

    public boolean isCarregarCentral() {
        return carregarCentral;
    }

    public void setCarregarCentral(boolean carregarCentral) {
        this.carregarCentral = carregarCentral;
    }

    public boolean isApresentarEmpresa() {
        return apresentarEmpresa;
    }

    public void setApresentarEmpresa(boolean apresentarEmpresa) {
        this.apresentarEmpresa = apresentarEmpresa;
    }

    public void verificarCliente() {
        try {
            getFacade().getCliente().verificarCliente(cliente, getUsuarioLogado());
            montarSucesso("Cliente verificado com sucesso.");
        } catch (Exception ex) {
            cliente.setVerificadoEm(null);
            montarErro(ex);
        }
    }

    public void desverificarCliente() {
        try {
            getFacade().getCliente().desverificarCliente(cliente, getUsuarioLogado());
            cliente.setVerificadoEm(null);
            montarSucesso("Cliente verificado com sucesso.");
        } catch (Exception ex) {
            cliente.setVerificadoEm(null);
            montarErro(ex);
        }
    }

    public void prepararVisualizacaoUtilizacaoAvaliacao(ActionEvent actionEvent) throws Exception {
        setSucesso(false);
        setErro(false);
        setMensagem("");
        setMensagemDetalhada("", "");

        MovProdutoVO movProduto = (MovProdutoVO) actionEvent.getComponent().getAttributes().get("movProdutoVO");
        setUtilizacoes(getFacade().getUtilizacaoAvaliacaoFisica().findByMovproduto(movProduto.getCodigo()));
    }

    private void consultarObjecaoDefinitiva() throws Exception {
        if (!UteisValidacao.emptyNumber(getCliente().getObjecao().getCodigo())) {
            getCliente().setObjecao(getFacade().getObjecao().consultarPorChavePrimaria(getCliente().getObjecao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
    }

    public void confirmarAlterarContatroRenovarAutomaticamente() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.RENOVAVEL_AUTOMATICAMENTE_CLIENTE);
        setMsgAlert("");
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        limparMsg();
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                alterarPermiteRenovacaoAutomatica(getContratoSelecionado().getPermiteRenovacaoAutomatica(), auto.getUsuario());
                montarSucessoGrowl("Renovação automática alterada com sucesso!");
                auto.setRenderComponents("panelAutorizacaoFuncionalidade,containerAlterarContratoRenovacao");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada(e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("2.66 - Alterar renovação automática do contrato", "adicionarRemoverContratoRenovacaoAutomatica",
                "Alterar renovação automática do contrato",
                "panelAutorizacaoFuncionalidade", listener);

    }

    public String getOnComplete() {
        return getMensagemNotificar();
    }

    public void alterarPermiteRenovacaoAutomatica(boolean contratoRenovarAutomaticamente, UsuarioVO usuario) throws Exception {
        getFacade().getContrato().alterarPermiteRenovacaoAutomatica(getContratoSelecionado(), contratoRenovarAutomaticamente, usuario, getCliente());
        getContratoSelecionado().registrarObjetoVOAntesDaAlteracao();
    }

    public Integer getTotalPresencaContratoCredito() {
        return totalPresencaContratoCredito;
    }

    public void setTotalPresencaContratoCredito(Integer totalPresencaContratoCredito) {
        this.totalPresencaContratoCredito = totalPresencaContratoCredito;
    }

    public Integer getTotalFaltaContratoCredito() {
        return totalFaltaContratoCredito;
    }

    public void setTotalFaltaContratoCredito(Integer totalFaltaContratoCredito) {
        this.totalFaltaContratoCredito = totalFaltaContratoCredito;
    }


    public List<SelectItem> getListaOperacaoCreditoTreino() {
        return listaOperacaoCreditoTreino;
    }

    public void setListaOperacaoCreditoTreino(List<SelectItem> listaOperacaoCreditoTreino) {
        this.listaOperacaoCreditoTreino = listaOperacaoCreditoTreino;
    }

    public Integer getCodigoOperacaoCreditoSelecionado() {
        return codigoOperacaoCreditoSelecionado;
    }

    public void setCodigoOperacaoCreditoSelecionado(Integer codigoOperacaoCreditoSelecionado) {
        this.codigoOperacaoCreditoSelecionado = codigoOperacaoCreditoSelecionado;
    }

    public boolean isAulasMarcadasComCreditoExtra() {
        return aulasMarcadasComCreditoExtra;
    }

    public void setAulasMarcadasComCreditoExtra(boolean aulasMarcadasComCreditoExtra) {
        this.aulasMarcadasComCreditoExtra = aulasMarcadasComCreditoExtra;
    }

    public void aplicarFiltroExtratoCreditoTreino() {
        notificarRecursoEmpresa(RecursoSistema.CONTROLE_CREDITO_LOG_CLIENTE);
        try {
            aplicouFiltroExtratoCreditoTreino = true;
            this.contratoSelecionado.setListaControleCreditoTreino(getFacade().getControleCreditoTreino().consultarPorOperacao(this.contratoSelecionado.getCodigo(), codigoOperacaoCreditoSelecionado, this.aulasMarcadasComCreditoExtra, this.dataInicioOperacaoExtratoCredito, this.dataFimOperacaoExtratoCredito));
            montarSucesso("");
            setMsgAlert("Richfaces.hideModalPanel('modalFiltroCredito');");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert("");
        }
    }

    public void limparFiltroDataExtratoCredito() {
        this.dataInicioOperacaoExtratoCredito = null;
        this.dataFimOperacaoExtratoCredito = null;
    }

    public void limparFiltroExtratoCreditoTreino() {
        notificarRecursoEmpresa(RecursoSistema.CONTROLE_CREDITO_LOG_CLIENTE);
        try {
            limparFiltroDataExtratoCredito();
            this.contratoSelecionado.setListaControleCreditoTreino(getFacade().getControleCreditoTreino().consultar(this.contratoSelecionado.getCodigo()));
            aplicouFiltroExtratoCreditoTreino = false;
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public boolean isAplicouFiltroExtratoCreditoTreino() {
        return aplicouFiltroExtratoCreditoTreino;
    }

    public void setAplicouFiltroExtratoCreditoTreino(boolean aplicouFiltroExtratoCreditoTreino) {
        this.aplicouFiltroExtratoCreditoTreino = aplicouFiltroExtratoCreditoTreino;
    }

    public String getAtributosImprimirExtratoCreditoTreino() {
        if (this.aplicouFiltroExtratoCreditoTreino) {
            return "dataOperacao=Data,operacao=Operaçao,usuario_Apresentar=Usuario,observacao=Observação,aulaDesmarcada_Apresentar=Aula Desmarcada,aulaMarcada_Apresentar=Aula Marcada,quantidade=Quantidade";
        } else {
            return "dataOperacao=Data,operacao=Operaçao,usuario_Apresentar=Usuario,observacao=Observação,aulaDesmarcada_Apresentar=Aula Desmarcada,aulaMarcada_Apresentar=Aula Marcada,quantidade=Quantidade,saldo=Saldo";
        }

    }

    public Date getDataInicioOperacaoExtratoCredito() {
        return dataInicioOperacaoExtratoCredito;
    }

    public void setDataInicioOperacaoExtratoCredito(Date dataInicioOperacaoExtratoCredito) {
        this.dataInicioOperacaoExtratoCredito = dataInicioOperacaoExtratoCredito;
    }

    public Date getDataFimOperacaoExtratoCredito() {
        return dataFimOperacaoExtratoCredito;
    }

    public void setDataFimOperacaoExtratoCredito(Date dataFimOperacaoExtratoCredito) {
        this.dataFimOperacaoExtratoCredito = dataFimOperacaoExtratoCredito;
    }

    private void limparEnderecosTelefonesClienteControle() {
        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
        if (clienteControle != null) {
            clienteControle.limparEnderecosTelefones();
        }
    }

    public boolean isApresentarAtualisarBV() {
        return apresentarAtualisarBV;
    }

    public void setApresentarAtualisarBV(boolean apresentarAtualisarBV) {
        this.apresentarAtualisarBV = apresentarAtualisarBV;
    }

    public void prepararAbrirObservacao() {
        try {
            getClienteVO().setUsuarioVO(getUsuarioLogado());
            if (getClienteObservacoes().isEmpty()) {
                setRetornoListaModal("Não há observações cadastradas");
            } else if (!getClienteObservacoes().isEmpty()) {
                setRetornoListaModal("");
                setClienteObservacoes(getFacade().getClienteObservacao().consultarObservacaoPaginado(getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, LISTA_PAGINADA_OBSERVACAO_LIMIT, 0));
                listaClienteObservacao.setCount(getFacade().getClienteObservacao().consultarQuantidadeListaObservacao(getCliente().getCodigo()));
            }
            setMensagem("");
            setMensagemDetalhada("", "");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararAbrirObservacaoAdicionar() {
        notificarRecursoEmpresa(RecursoSistema.OBSERVACAO_CLIENTE);
        try {
            getClienteVO().setUsuarioVO(getUsuarioLogado());
            setMensagem("");
            setMensagemDetalhada("", "");
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public ListaPaginadaTO getListaCompras() {
        return listaCompras;
    }

    public void setListaCompras(ListaPaginadaTO listaCompras) {
        this.listaCompras = listaCompras;
    }

    public ListaPaginadaTO getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(ListaPaginadaTO listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public ListaPaginadaTO getListaPagamentos() {
        return listaPagamentos;
    }

    public void setListaPagamentos(ListaPaginadaTO listaPagamentos) {
        this.listaPagamentos = listaPagamentos;
    }

    public ListaPaginadaTO getListaRemessa() {
        return listaRemessa;
    }

    public void setListaRemessa(ListaPaginadaTO listaRemessa) {
        this.listaRemessa = listaRemessa;
    }

    public ListaPaginadaTO getListaBoleto() {
        return listaBoleto;
    }

    public void setListaBoleto(ListaPaginadaTO listaBoleto) {
        this.listaBoleto = listaBoleto;
    }

    public ListaPaginadaTO getListaTrasacoes() {
        return listaTrasacoes;
    }

    public void setListaTrasacoes(ListaPaginadaTO listaTrasacoes) {
        this.listaTrasacoes = listaTrasacoes;
    }

    public ListaPaginadaTO getListaTransacoesVerificacao() {
        return listaTransacoesVerificacao;
    }

    public void setListaTransacoesVerificacao(ListaPaginadaTO listaTransacoesVerificacao) {
        this.listaTransacoesVerificacao = listaTransacoesVerificacao;
    }

    private void validarEdicaoCliente() {
        Object obj = JSFUtilities.getFromSession(ClienteVO.ULTIMO_CLIENTE_EDITADO);
        if (obj != null) { // permite que mais de uma edição seja feita para o mesmo popup de um cliente
            if (cliente != null && !UteisValidacao.emptyNumber(cliente.getCodigo()) && obj.equals(cliente.getCodigo().toString())) {
                JSFUtilities.storeOnSession(ClienteVO.FLAG_PERMITE_GRAVAR_CLIENTE, Boolean.TRUE);
            } else {
                JSFUtilities.removeFromSession(ClienteVO.ULTIMO_CLIENTE_EDITADO);
            }
        }
    }

    public boolean isApresentarRenovavelAutomaticamente() {
        return apresentarRenovavelAutomaticamente;
    }

    public void setApresentarRenovavelAutomaticamente(boolean apresentarRenovavelAutomaticamente) {
        this.apresentarRenovavelAutomaticamente = apresentarRenovavelAutomaticamente;
    }

    public boolean isContratoRenovarAutomaticamente() {
        return contratoRenovarAutomaticamente;
    }

    public void setContratoRenovarAutomaticamente(boolean contratoRenovarAutomaticamente) {
        this.contratoRenovarAutomaticamente = contratoRenovarAutomaticamente;
    }

    public ContratoAssinaturaDigitalVO getDocs() {
        return docs;
    }

    public void setDocs(ContratoAssinaturaDigitalVO docs) {
        this.docs = docs;
    }

    public boolean isApresentarBotaoGrvControleCredito() {
        return apresentarBotaoGrvControleCredito;
    }

    public void setApresentarBotaoGrvControleCredito(boolean apresentarBotaoGrvControleCredito) {
        this.apresentarBotaoGrvControleCredito = apresentarBotaoGrvControleCredito;
    }

    public void fecharModalControleCreditoTreino() {
        setMsgAlert("Richfaces.hideModalPanel('modalControleCreditoTreino');document.getElementById('form:btnAtualizaCliente').click();");
    }

    public String getAbrirPopUpListaAcessosRel() throws Exception {
        try {
            FuncionalidadeControle fc = (FuncionalidadeControle) JSFUtilities.getManagedBean("FuncionalidadeControle");
            ListaAcessoControleRel listaAcesso = (ListaAcessoControleRel) JSFUtilities.getManagedBean("ListaAcessoControleRel");

            listaAcesso.getListaAcessoRel().setClienteVO(getCliente());
            return fc.getAbrirPopUp();
        } catch (Exception e) {
            Uteis.logar(e, TelaClienteControle.class);
            montarErro(e);
        }
        return "";
    }


    public void abrirTelaRegistrarAcesso() {
        RegistrarAcessoAvulsoControle registrarAcessoAvulsoControle = (RegistrarAcessoAvulsoControle) JSFUtilities.getManagedBean("RegistrarAcessoAvulsoControle");
        try {
            registrarAcessoAvulsoControle.inicializarDados();
            registrarAcessoAvulsoControle.setClienteVO(getClienteVO());
            registrarAcessoAvulsoControle.setTelaCliente(true);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }


    public void setAlterarClienteObersacao(boolean alterarClienteObersacao) {
        this.alterarClienteObersacao = alterarClienteObersacao;
    }

    public boolean isAlterarClienteObersacao() {
        return alterarClienteObersacao;
    }

    public void limparCamposEditaveis() {
        setAlterarClienteObersacao(false);
    }

    public void setApresentarAlteracao(boolean apresentarAlteracao) {
        this.apresentarAlteracao = apresentarAlteracao;
    }

    public boolean isApresentarAlteracao() {
        return apresentarAlteracao;
    }

    public void setClienteObervacaoParametroExclusao(ClienteObservacaoVO clienteObervacaoParametroExclusao) {
        this.clienteObervacaoParametroExclusao = clienteObervacaoParametroExclusao;
    }

    public ClienteObservacaoVO getClienteObervacaoParametroExclusao() {
        return clienteObervacaoParametroExclusao;
    }

    public void setRetornoListaModal(String retornoListaModal) {
        this.retornoListaModal = retornoListaModal;
    }

    public String getRetornoListaModal() {
        return retornoListaModal;
    }

    public void setListaClienteObservacao(ListaPaginadaTO listaClienteObservacao) {
        this.listaClienteObservacao = listaClienteObservacao;
    }

    public ListaPaginadaTO getListaClienteObservacao() {
        return listaClienteObservacao;
    }

    public void setRetornoOncompleteListaObservacao(String retornoOncompleteListaObservacao) {
        this.retornoOncompleteListaObservacao = retornoOncompleteListaObservacao;
    }

    public String getRetornoOncompleteListaObservacao() {
        return retornoOncompleteListaObservacao;
    }


    public ClienteObservacaoVO getOservacaoParaVisualizacao() {
        return oservacaoParaVisualizacao;
    }

    public void setOservacaoParaVisualizacao(ClienteObservacaoVO oservacaoParaVisualizacao) {
        this.oservacaoParaVisualizacao = oservacaoParaVisualizacao;
    }

    public String getOnCompleteVisualizar() {
        if (onCompleteVisualizar == null) {
            onCompleteVisualizar = "";
        }
        return onCompleteVisualizar;
    }

    public void setOnCompleteVisualizar(String onCompleteVisualizar) {
        this.onCompleteVisualizar = onCompleteVisualizar;
    }

    public void irParaTelaLancarBrinde() throws Exception {
        LancarBrindeClienteControle lancarBrindeClienteControle = (LancarBrindeClienteControle) JSFUtilities.getManagedBean("LancarBrindeClienteControle");
        lancarBrindeClienteControle.limparDados();
        lancarBrindeClienteControle.setClienteSelecionado(getClienteVO());
    }

    public void irParaTelaAjustePontuacao() {
        LancarBrindeClienteControle lancarBrindeClienteControle = (LancarBrindeClienteControle) JSFUtilities.getManagedBean("LancarBrindeClienteControle");
        lancarBrindeClienteControle.limparDados();
        lancarBrindeClienteControle.setClienteSelecionadoAjuste(getClienteVO());
    }

    public void irParaTelaHistoricoPontos() {
        HistoricoPontosControle historicoPontosControle = (HistoricoPontosControle) JSFUtilities.getManagedBean("HistoricoPontosControle");
        try {
            historicoPontosControle.montarListaHistoricoExterno(getClienteVO());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean getApresentarBotaoLancarBrinde() {
        boolean retorno = false;

        if (getClienteVO().getSituacao().equals("AT") || getClienteVO().getSituacao().equals("IN")) {
            retorno = true;
        }

        return retorno;
    }

    public Integer getValorPontuacao() {
        if (valorPontuacao == null) {
            valorPontuacao = 0;
        }
        return valorPontuacao;
    }

    public void setValorPontuacao(Integer valorPontuacao) {
        this.valorPontuacao = valorPontuacao;
    }

    public String getLabelPontos() {
        if (valorPontuacao > 1) {
            return "pontos";
        } else {
            return "ponto";
        }
    }

    public File getArquivoCliente() {
        return arquivoCliente;
    }

    public void setArquivoCliente(File arquivoCliente) {
        this.arquivoCliente = arquivoCliente;
    }

    public String getExtensaoArquivoCliente() {
        return extensaoArquivoCliente;
    }

    public void setExtensaoArquivoCliente(String extensaoArquivoCliente) {
        this.extensaoArquivoCliente = extensaoArquivoCliente;
    }

    public boolean isMostrarBotaoGravarAnexo() {
        return mostrarBotaoGravarAnexo;
    }

    public void setMostrarBotaoGravarAnexo(boolean mostrarBotaoGravarAnexo) {
        this.mostrarBotaoGravarAnexo = mostrarBotaoGravarAnexo;
    }

    public String getOnCompleteAnexo() {
        if (onCompleteAnexo == null) {
            onCompleteAnexo = "";
        }
        return onCompleteAnexo;
    }

    public void setOnCompleteAnexo(String onCompleteAnexo) {
        this.onCompleteAnexo = onCompleteAnexo;
    }

    public String getNomeAnexoCliente() {
        return nomeAnexoCliente;
    }

    public void setNomeAnexoCliente(String nomeAnexoCliente) {
        this.nomeAnexoCliente = nomeAnexoCliente;
    }

    public AtestadoVO getUltimoAtestadoCliente() {
        return ultimoAtestadoCliente;
    }

    public boolean isCarregarNotasFiscais() {
        return carregarNotasFiscais;
    }

    public void setCarregarNotasFiscais(boolean carregarNotasFiscais) {
        this.carregarNotasFiscais = carregarNotasFiscais;
    }

    public List<NotasFiscaisTO> getListaNotasFiscais() {
        if (listaNotasFiscais == null) {
            listaNotasFiscais = new ArrayList<NotasFiscaisTO>();
        }
        return listaNotasFiscais;
    }

    public void setListaNotasFiscais(List<NotasFiscaisTO> listaNotasFiscais) {
        this.listaNotasFiscais = listaNotasFiscais;
    }

    public NotasFiscaisTO getNotasFiscaisTOSelecionada() {
        if (notasFiscaisTOSelecionada == null) {
            notasFiscaisTOSelecionada = new NotasFiscaisTO();
        }
        return notasFiscaisTOSelecionada;
    }

    public void setNotasFiscaisTOSelecionada(NotasFiscaisTO notasFiscaisTOSelecionada) {
        this.notasFiscaisTOSelecionada = notasFiscaisTOSelecionada;
    }

    public void prepararEnvioEmailNota() {
        try {
            setOnCompleteNota("");
            setNotasFiscaisTOSelecionada(new NotasFiscaisTO());
            NotasFiscaisTO nota = (NotasFiscaisTO) context().getExternalContext().getRequestMap().get("nota");
            if (nota != null) {
                setNotasFiscaisTOSelecionada(nota);
                if (!UteisValidacao.emptyString(getCliente().getPessoa().getTodosEmails())) {
                    getNotasFiscaisTOSelecionada().setEmail(getCliente().getPessoa().getTodosEmails());
                }
                setOnCompleteNota("Richfaces.showModalPanel('modalEnviarNotaEmail');");
            }
        } catch (Exception ignored) {
        }
    }
    public void enviarFluxo(){
        try {
            setMsgAlert("");
            Connection con;

            con = Conexao.getFromSession();
            String key = DAO.resolveKeyFromConnection(con);

            List<Message> messageList = new ArrayList<>();
            Message m = new Message();
            m.setNumero(getCliente().getPessoa().getTelefones());
            m.setNome(getCliente().getPessoa().getNome());
            m.setMsg("ND");
            messageList.add(m);

            ConfiguracaoIntegracaoBotConversaVO fluxoGymbot = getFacade().getMalaDireta().buscarFluxoPeloCodigoEmpresa(getEmpresaLogado().getCodigo(), con);
            BotConversaController controller = new BotConversaController(key, con);
            String ret = controller.sendMessage(key, getCliente().getEmpresa().getCodigo(), fluxoGymbot.getUrlwebhoobotconversa(), messageList);
            String msgBotConversa = "\nDisparo de fluxo GymBot, \"" + fluxoGymbot.getDescricao() + "\", retorno: " + ret;

            MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
            HistoricoContato historicoContatoDAO = new HistoricoContato(con);

            malaDiretaVO.setMeioDeEnvio(MeioEnvio.GYMBOT.getCodigo());
            malaDiretaVO.setRemetente(getUsuarioLogado());
            malaDiretaVO.setContatoAvulso(true);
            historicoContatoDAO.executarGravacaoVindoGymbot(malaDiretaVO,0,getCliente().getCodigo(),msgBotConversa, fluxoGymbot.getCodigo());

            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("GymBot disparado com sucesso!");
            setMsgAlert(getMensagemNotificar());
        }
           catch (Exception e){
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void enviarFluxoGymbotPro(){
        try {
            setMsgAlert("");
            Connection con;

            con = Conexao.getFromSession();
            String key = DAO.resolveKeyFromConnection(con);

            List<Message> messageList = new ArrayList<>();
            Message m = new Message();
            m.setNumero(getCliente().getPessoa().getTelefones());
            m.setNome(getCliente().getPessoa().getNome());
            m.setMsg("ND");
            messageList.add(m);

            ConfiguracaoIntegracaoGymbotProVO fluxoGymbot = getFacade().getMalaDireta().buscarFluxoGymbotProPeloCodigoEmpresa(getEmpresaLogado().getCodigo(), con);
            GymbotProController controller = new GymbotProController(key, con);
            String ret = controller.sendMessage(key, getCliente().getEmpresa().getCodigo(), fluxoGymbot.getToken(), fluxoGymbot.getIdFluxo(), messageList);

            //Grava o historico do contato
            String msgBotConversa = "\nDisparo GymBot Pro, \"" + fluxoGymbot.getDescricao() + "\", retorno: " + ret;
            MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
            HistoricoContato historicoContatoDAO = new HistoricoContato(con);
            malaDiretaVO.setMeioDeEnvio(MeioEnvio.GYMBOT_PRO.getCodigo());
            malaDiretaVO.setRemetente(getUsuarioLogado());
            malaDiretaVO.setContatoAvulso(true);
            malaDiretaVO.setConfigGymbotPro(fluxoGymbot);

            historicoContatoDAO.executarGravacaoVindoGymbot(malaDiretaVO,0,getCliente().getCodigo(),msgBotConversa, fluxoGymbot.getCodigo());

            setSucesso(true);
            setErro(false);
            montarSucessoGrowl("GymBot disparado com sucesso!");
            setMsgAlert(getMensagemNotificar());
        }
        catch (Exception e){
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void solicitarEnvioEmailNota() {
        try {

            if (UteisValidacao.emptyString(getNotasFiscaisTOSelecionada().getEmail())) {
                throw new Exception("Informe o email para enviar a nota");
            }

            if (getNotasFiscaisTOSelecionada().getCodNotaFiscal() > 0) {
                /* Notas do e-notas */
                NotaFiscalVO nota = getFacade().getNotaFiscal().consultarPorChavePrimaria(getNotasFiscaisTOSelecionada().getCodNotaFiscal(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String retorno = getFacade().getNotaFiscal().enviarEmailNotaFiscal(getNotasFiscaisTOSelecionada().getEmail(), nota, getUsuarioLogado(), "");
                montarSucessoGrowl(retorno);
            } else {
                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Content-Type", "application/x-www-form-urlencoded");

                Map<String, String> corpo = new HashMap<String, String>();
                corpo.put("rps", getNotasFiscaisTOSelecionada().getIdReferencia());
                corpo.put("enviarEmail", getNotasFiscaisTOSelecionada().getEmail());
                corpo.put("usuario", getUsuarioLogado().getUsername().toUpperCase());

                String urlConsultar = PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nota";
                String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");
                JSONObject retornoJSON = new JSONObject(executeRequest);
                montarSucessoGrowl(retornoJSON.getString("retorno") + " Em instantes você receberá o email.");
            }
            setOnCompleteNota("Richfaces.hideModalPanel('modalEnviarNotaEmail');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getOnCompleteNota() {
        if (onCompleteNota == null) {
            onCompleteNota = "";
        }
        return onCompleteNota;
    }

    public void setOnCompleteNota(String onCompleteNota) {
        this.onCompleteNota = onCompleteNota;
    }


    public String getNomeClienteObjecao() {
        return nomeClienteObjecao;
    }

    public void setNomeClienteObjecao(String nomeClienteObjecao) {
        this.nomeClienteObjecao = nomeClienteObjecao;
    }

    public String getObjecao() {
        return objecao;
    }

    public void setObjecao(String objecao) {
        this.objecao = objecao;
    }

    public void prepararRemoverObjecao() {
        try {
            setNomeClienteObjecao(getClienteVO().getNome_Apresentar());

            if (getClienteVO().getObjecao().getCodigo() > 0) {
                getClienteVO().setObjecao(getFacade().getObjecao().consultarPorChavePrimaria(getClienteVO().getObjecao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            setObjecao(getClienteVO().getObjecao().getDescricao());
            limparMensagem();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararAlterarMatricula() {
        try {
            limparMensagem();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isApresentarEstornoCancelamento() {
        return apresentarEstornoCancelamento;
    }

    public void setApresentarEstornoCancelamento(boolean apresentarEstornoCancelamento) {
        this.apresentarEstornoCancelamento = apresentarEstornoCancelamento;
    }

    public void confirmarDesfazerCancelamentoProporcional() {
        setMsgAlert("");
        setContratoOperacaoVO(new ContratoOperacaoVO());
        ContratoOperacaoVO obj = (ContratoOperacaoVO) context().getExternalContext().getRequestMap().get("contratoOperacao");
        setContratoOperacaoVO(obj);
        setMsgAlert("Richfaces.showModalPanel('modalDesfazerCancelamento')");
    }

    public void desfazerCancelamentoProporcional() {
        try {
            setMsgAlert("");
            if (UteisValidacao.emptyString(getContratoOperacaoVO().getInformacoesDesfazer())) {
                throw new ConsistirException("Este contrato não foi cancelado pelo método de \"Cancelamento verificando próxima parcela em aberto\", portanto, não é possível desfazer essa operação.");
            }
            getFacade().getZWFacade().desfazerCancelamentoProporcional(getContratoOperacaoVO(), getUsuarioLogado());
            gerarLogAlterarDesfazerCancelamento(getUsuarioLogado());
            setMsgAlert("Richfaces.hideModalPanel('modalDesfazerCancelamento');document.getElementById('form:btnAtualizaCliente').click()");
            montarSucessoGrowl("Cancelamento revertido com sucesso!");
        } catch (Exception ex) {
            setMsgAlert("Richfaces.hideModalPanel('modalDesfazerCancelamento');");
            montarErro(ex);
        }
    }

    public void gerarLogAlterarDesfazerCancelamento(UsuarioVO usuario) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("CONTRATO");
        log.setDescricao("DESFEITO O CANCELAMENTO DO CONTRATO: " + getContratoSelecionado().getCodigo() + " -----------");
        log.setChavePrimaria(getContratoSelecionado().getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setUsuarioVO(usuario);
        log.setResponsavelAlteracao(usuario.getNomeAbreviado());
        log.setOperacao("EXCLUSÃO");
        log.setNomeCampo("CANCELAMENTO DO CONTRATO: " + getContratoSelecionado().getCodigo() + " -----------");
        log.setUserOAMD(usuario.getUserOamd());
        log.setPessoa(getCliente().getPessoa().getCodigo());
        log.setValorCampoAnterior(" ");
        log.setValorCampoAlterado("DESFEITO O CANCELAMENTO DO CONTRATO - " + getContratoSelecionado().getCodigo());
        getFacade().getLog().incluir(log);
    }

    public void ordenarLista(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        String orderBy = (String) evt.getComponent().getAttributes().get("orderBy");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOrderBy(orderBy);
        paginacao.setOrderByDesc(!paginacao.isOrderByDesc());
        carregarListaPaginacao(paginacao, codigo);
    }

    public Map<String, String> getStatusProtheus() {
        return statusProtheus;
    }

    public void setStatusProtheus(Map<String, String> statusProtheus) {
        this.statusProtheus = statusProtheus;
    }

    public void selecionarEmpresaTransferencia() {
        setEmpresaTransferenciaCliente((EmpresaVO) context().getExternalContext().getRequestMap().get("empresa"));
    }

    public void selecionarContratoParaTransferenciaDePlano(ActionEvent evt) throws Exception {
        Integer codigoContrato = (Integer) evt.getComponent().getAttributes().get("codigoContrato");
        selecionarContratoParaTransferenciaDePlanoGeral(codigoContrato);
    }

    public void selecionarContratoParaTransferenciaDePlanoGeral(Integer codigoContrato) throws Exception {
        ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
        contratoVigenteMudancaPlano = true;
        contratoRenovadoMudancaPlano = false;
        if (Calendario.menorOuIgual(Calendario.hoje(), contrato.getVigenciaDe())) {
            apresentaMudancaPlano = false;
            contratoVigenteMudancaPlano = false;
        } else if(contrato.getContratoResponsavelRenovacaoMatricula() > 0){
            apresentaMudancaPlano = false;
            contratoRenovadoMudancaPlano = true;
        } else {
            apresentaMudancaPlano = true;
        }
        setPlanoParaTransferencia(null);
        this.setContratoParaTransferenciaDePlano(contrato);
    }

    public void selecionarPlanoParaTransferencia() {
        selecionarPlanoParaTransferenciaGeral((PlanoVO) context().getExternalContext().getRequestMap().get("plano"), getKey());
    }

    public void selecionarPlanoParaTransferenciaGeral(PlanoVO planoVO, String chave) {
        setPlanoParaTransferencia(planoVO);
        ContratoVO contrato = getContratoParaTransferenciaDePlano();
        try {
            if(Calendario.igual(contrato.getVigenciaDe(), Calendario.hoje())){
                throw new Exception("A operação de mudança de plano não pode ser feita no primeiro dia do contrato.");
            }
            setDiaVencimentoCartaoTransferenciaDePlano(getPlanoParaTransferencia().getPlanoRecorrencia().isGerarParcelasValorDiferente() || getPlanoParaTransferencia().getPlanoRecorrencia().isGerarParcelasValorDiferenteRenovacao() ?  Uteis.getDiaMesData(Calendario.hoje()) :  getContratoParaTransferenciaDePlano().getContratoRecorrenciaVO().getDiaVencimentoCartao());
            setNovoContratoParaTransferenciaDePlano(getFacade().getContrato().gravarContratoSite(
                    chave,
                    getPlanoParaTransferencia().getCodigo(),
                    getClienteVO().getCodigo(),
                    getContratoParaTransferenciaDePlano().getNrParcelasAdesao(),
                    getContratoParaTransferenciaDePlano().getNrVezesParcelarProduto(),
                    "",
                    true,
                    true,
                    false,
                    getContratoParaTransferenciaDePlano().getNrParcelasPagamento(),
                    true,
                    0,
                    getEmpresaLogado().getCodigo(),
                    true,
                    getUsuarioLogado(), getDiaVencimentoCartaoTransferenciaDePlano(), null, false, 0, null, null, contrato,
                    isTrocaPlano(), null,null,null, false));
        } catch (Exception e) {
            Uteis.logar(e, TelaClienteControle.class);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            Uteis.logar(e, TelaClienteControle.class);
        }
    }

    public int getDiasVigenciaDeContratosAtivos() {
        return diasVigenciaDeContratosAtivos;
    }

    public void setDiasVigenciaDeContratosAtivos(int diasVigenciaDeContratosAtivos) {
        this.diasVigenciaDeContratosAtivos = diasVigenciaDeContratosAtivos;
    }

    public int getDiaVencimentoCartao() {
        return diaVencimentoCartao;
    }

    public void setDiaVencimentoCartao(int diaVencimentoCartao) {
        this.diaVencimentoCartao = diaVencimentoCartao;
    }

    public PlanoVO getPlanoParaTransferencia() {
        if (planoParaTransferencia == null) {
            return new PlanoVO();
        }
        return planoParaTransferencia;
    }

    public void setPlanoParaTransferencia(PlanoVO plano) {
        this.planoParaTransferencia = plano;
    }

    public void setNovoContratoParaTransferenciaDePlano(ContratoVO contratoParaTransferenciaDePlano) {
        this.novoContratoParaTransferenciaDePlano = contratoParaTransferenciaDePlano;
    }

    public ContratoVO getNovoContratoParaTransferenciaDePlano() {
        return novoContratoParaTransferenciaDePlano;
    }

    public String getValorFinalContrato() throws Exception {
        return Uteis.formatarValorEmReal(getNovoContratoParaTransferenciaDePlano().getValorFinal());
    }

    public String getDataFinalContrato() {
        String data = Uteis.getData(getNovoContratoParaTransferenciaDePlano().getVigenciaAteAjustada());
        if (UteisValidacao.emptyString(data)) {
            return " - ";
        }
        return data;
    }

    public void resetTransferenciaClienteEmpresa() {
        setEmpresaTransferenciaCliente(null);
    }

    public Integer getConvitesDireito() {
        return convitesDireito;
    }

    public void setConvitesDireito(Integer convitesDireito) {
        this.convitesDireito = convitesDireito;
    }

    public boolean isPlanoSelecionadoParaTransferenciaIgualAtual() {
        return (getPlanoParaTransferencia() != null &&
                getContratoParaTransferenciaDePlano() != null &&
                getPlanoParaTransferencia().getCodigo() != 0 &&
                getContratoParaTransferenciaDePlano().getPlano().getCodigo() != 0 &&
                getPlanoParaTransferencia().getCodigo() == getContratoParaTransferenciaDePlano().getPlano().getCodigo());
    }

    public void resetarDadosTansferenciaPlano() {
        setPlanoParaTransferencia(null);
        setContratoParaTransferenciaDePlano(null);
        setNovoContratoParaTransferenciaDePlano(null);
    }

    public Boolean getExibirConvites() {
        return exibirConvites;
    }

    public void setExibirConvites(Boolean exibirConvites) {
        this.exibirConvites = exibirConvites;
    }

    public Integer getDiaVencimentoParcelasAlterado() {
        if (diaVencimentoParcelasAlterado == null) {
            diaVencimentoParcelasAlterado = diaVencimentoCartao;
        }
        return diaVencimentoParcelasAlterado;
    }

    public void setDiaVencimentoParcelasAlterado(Integer dia) {
        this.diaVencimentoParcelasAlterado = dia;
    }

    public List<SelectItem> getListaDiaVencimentoParcelas() {
        return listaDiaVencimentoParcelas;
    }

    public void setListaDiaVencimentoParcelas(List<SelectItem> listaDiaVencimentoParcelas) {
        this.listaDiaVencimentoParcelas = listaDiaVencimentoParcelas;
    }

    public void selecionarContratoParaMudancaDiaVencimentoParcela(ActionEvent actionEvent) throws Exception {
        Integer codigoContrato = (Integer) actionEvent.getComponent().getAttributes().get("codigoContrato");
        Integer diaVencimentoCartao = (Integer) actionEvent.getComponent().getAttributes().get("diaVencimentoCartao");
        ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_TODOS);
        this.setContratoParaMudancaDiaVencimentoParcela(contrato);
        this.setDiaVencimentoCartao(diaVencimentoCartao);
        montarListaDiaVencimentoParcelas();
        notificarRecursoEmpresa(RecursoSistema.MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO);
    }

    public void montarListaDiaVencimentoParcelas() {
        List<SelectItem> dias = new ArrayList<SelectItem>();

        for (String dia : getContratoParaMudancaDiaVencimentoParcela().getPlano().getListaDiasVencimento()) {
            if (dia.length() > 0) {
                dias.add(new SelectItem(dia, dia));
            }
        }

        setListaDiaVencimentoParcelas(dias);
    }

    public void setContratoParaMudancaDiaVencimentoParcela(ContratoVO contrato) {
        this.contratoParaMudancaDiaVencimentoParcela = contrato;
    }

    public ContratoVO getContratoParaMudancaDiaVencimentoParcela() {
        return contratoParaMudancaDiaVencimentoParcela;
    }

    public void alterarDataVencimentoParcela() {
        if (getContratoParaMudancaDiaVencimentoParcela() != null) {
            try {

                List<MovParcelaVO> parcelasAlteradas = getFacade().getContratoRecorrencia().alterarDataVencimentoParcelasMensalidade(
                        getContratoParaMudancaDiaVencimentoParcela(),
                        getDiaVencimentoParcelasAlterado(),
                        getUsuarioLogado(), getValorProRataAlteracaoDataVencimentoParcelas(), getDiferencaDiasNovoVencimento(), isLiberarValorProRataVencimentoParcelas(), isAlterarMesProximaParcelaEmAberto());

                getFacade().getContrato().acrescentarDiasContrato(getContratoParaMudancaDiaVencimentoParcela(), getDiferencaDiasNovoVencimento());
                getFacade().getZWFacade().atualizarSintetico(getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                montarListaContrato();
                setCarregarFinanceiro(true);
                abrirFinanceiro();
                montarSucessoGrowl("Os vencimentos das " + parcelasAlteradas.size() + " parcelas em aberto foram alterados");
                setSucesso(true);
                setErro(false);

                notificarRecursoEmpresa(RecursoSistema.MUDANCA_DE_VENCIMENTO_PERFIL_ALUNO_SUCESSO);
            } catch (Exception e) {
                montarErroComLog(e);
            }
        }
    }

    public boolean getPermiteAlterarDataVencimentoParcelas() {
        return permiteAlterarDataVencimentoParcelas;
    }

    public void setPermiteAlterarDataVencimentoParcelas(boolean permiteAlterarDataVencimentoParcelas) {
        this.permiteAlterarDataVencimentoParcelas = permiteAlterarDataVencimentoParcelas;
    }

    public String getMensagemAlteracaoDatavencimentoParcelas() {
        return mensagemAlteracaoDatavencimentoParcelas;
    }

    public void setMensagemAlteracaoDatavencimentoParcelas(String mensagemAlteracaoDatavencimentoParcelas) {
        this.mensagemAlteracaoDatavencimentoParcelas = mensagemAlteracaoDatavencimentoParcelas;
    }

    public void calcularProRataDeAlteracaoVencimentoParcelas() {
        try {
//            if (getContratoParaMudancaDiaVencimentoParcela() != null && getDiaVencimentoParcelasAlterado() != null) {
//                Date dataVencimentoAPartir = Calendario.somarDias(Calendario.hoje(), 1);
//                MovParcelaVO proximaParcelaEmAberto = getFacade()
//                        .getMovParcela()
//                        .consultarProximaEmAbertoPorContrato(getContratoParaMudancaDiaVencimentoParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, dataVencimentoAPartir);
//
//                if (proximaParcelaEmAberto != null) {
//
//                    int diaVencimentoParcelaAtual = proximaParcelaEmAberto.getDiaVencimento();
//                    int novoDiaVencimento = getDiaVencimentoParcelasAlterado();
//                    Date dataAtual = Calendario.hoje();
//                    Date novoVencimentoProximaParcela = Calendario.setDiaMes(proximaParcelaEmAberto.getDataVencimento(), novoDiaVencimento);
//
//                    // Significa que ele ta marcando para que o sistema jogue a proxima parcela para o mês posterior
//                    if (Calendario.maior(dataAtual, novoVencimentoProximaParcela)) {
//                        setAlterarMesProximaParcelaEmAberto(true);
//                        Date aux = Calendario.somarMeses(novoVencimentoProximaParcela, 1);
//                        setDiferencaDiasNovoVencimento(Calendario.diferencaEmDias(dataAtual, aux));
//                    } else {
//                        setAlterarMesProximaParcelaEmAberto(false);
//                        setDiferencaDiasNovoVencimento(novoDiaVencimento - diaVencimentoParcelaAtual > 0 ? novoDiaVencimento - diaVencimentoParcelaAtual : 0);
//                    }
//                    Double valorProRata = 0.0;
//                    if (getDiferencaDiasNovoVencimento() > 0 && getCliente().getEmpresa().getToleranciaProrata() >= getDiferencaDiasNovoVencimento()) {
//                        valorProRata = 0.0;
//                        setPermiteLiberarCobrancaDataVencimentoParcelas(false);
//                    } else {
//                        valorProRata = Uteis.arredondarForcando2CasasDecimais((getContratoParaMudancaDiaVencimentoParcela()
//                                .getContratoRecorrenciaVO()
//                                .getValorMensal() / 30) * getDiferencaDiasNovoVencimento());
//                        setPermiteLiberarCobrancaDataVencimentoParcelas(permissao("AlterarCobrancaProRataDiaRecorrente"));
//                    }
//
//                    setValorProRataAlteracaoDataVencimentoParcelas(valorProRata);
//                }
//            }

            JSONObject jsonResp = getFacade().getContrato().calcularProRataDeAlteracaoVencimentoParcelas(getCliente(), getContratoParaMudancaDiaVencimentoParcela(),
                    getDiaVencimentoParcelasAlterado(), getEmpresaLogado(), getUsuarioLogado());
            setAlterarMesProximaParcelaEmAberto(jsonResp.getBoolean("alterarMesProximaParcelaEmAberto"));
            setPermiteLiberarCobrancaDataVencimentoParcelas(jsonResp.getBoolean("permiteLiberarCobrancaDataVencimentoParcelas"));
            setValorProRataAlteracaoDataVencimentoParcelas(jsonResp.getDouble("valorProRataAlteracaoDataVencimentoParcelas"));
            setDiferencaDiasNovoVencimento(jsonResp.getInt("diferencaDiasNovoVencimento"));
        } catch (Exception e) {
            montarErroComLog(e);
        }

    }

    public String getValorProRataAlteracaoDataVencimentoParcelas_Apresentar(){
        return Formatador.formatarValorMonetario(this.valorProRataAlteracaoDataVencimentoParcelas);
    }

    public Double getValorProRataAlteracaoDataVencimentoParcelas() {
        return valorProRataAlteracaoDataVencimentoParcelas;
    }

    public void setValorProRataAlteracaoDataVencimentoParcelas(Double valorProRataAlteracaoDataVencimentoParcelas) {
        this.valorProRataAlteracaoDataVencimentoParcelas = valorProRataAlteracaoDataVencimentoParcelas;
    }

    public int getDiferencaDiasNovoVencimento() {
        return diferencaDiasNovoVencimento;
    }

    public void setDiferencaDiasNovoVencimento(int diferencaDiasNovoVencimento) {
        this.diferencaDiasNovoVencimento = diferencaDiasNovoVencimento;
    }

    public boolean isAlterarMesProximaParcelaEmAberto() {
        return alterarMesProximaParcelaEmAberto;
    }

    public void setAlterarMesProximaParcelaEmAberto(boolean alterarMesProximaParcelaEmAberto) {
        this.alterarMesProximaParcelaEmAberto = alterarMesProximaParcelaEmAberto;
    }

    public String getUrlAbrirClienteTreinoWeb() {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getManagedBean(LoginControle.class.getSimpleName());
            return loginControle.getAbrirNovaPlataforma("NTR", getCliente().getMatricula());
        } catch (Exception ex) {
            return "";
        }
    }

    public boolean getApresentarLinkAlunoTreino() {
        try {
            return getEmpresaLogado().isAddAutoClienteTreinoWeb() || !UteisValidacao.emptyNumber(getCliente().getUsuarioMovelVO().getCodigo());
        } catch (Exception ex) {
            return false;
        }
    }

    public void consultarParceiroFidelidade() {
        try {

            String cpf = getCliente().getPessoa().getCfp();

            if (getCliente().getEmpresa().isUsarParceiroFidelidade() && !UteisValidacao.emptyString(cpf)) {

                cpf = Uteis.tirarCaracteres(cpf, true);

                if (!SuperVO.verificaCPF(cpf)) {
                    return;
                }

                ParceiroFidelidadeVO parceiroFidelidadeVO = getFacade().getParceiroFidelidade().consultarPorEmpresaETipo(getCliente().getEmpresa().getCodigo(), TipoParceiroEnum.DOTZ, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ParceiroFidelidadeAPIDotzImpl parceiroFidelidadeAPIDotz = new ParceiroFidelidadeAPIDotzImpl(parceiroFidelidadeVO);
                RetornoParceiroTO retornoParceiroTO = parceiroFidelidadeAPIDotz.consultarSaldo(cpf, false);
                getCliente().setSaldoParceiroFidelidade(retornoParceiroTO.getMensagem());
            }
        } catch (Exception ignored) {
            getCliente().setSaldoParceiroFidelidade(null);
        }
    }

    public void obterStatusConciliadora() {
        try {
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("historicoPagamentos");
            ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(getFacade().getContrato().getCon());
            setListaLogConciliadora(conciliadoraService.obterLogConciliadora(0, obj.getCodigo()));
            setMsgAlert("Richfaces.showModalPanel('modalStatusConciliadora')");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public boolean isApresentarStatusConciliadora() {
        try {
            return getEmpresaLogado().isUsarConciliadora();
        } catch (Exception e) {
            return false;
        }
    }

    public List<LogConciliadoraVO> getListaLogConciliadora() {
        if (listaLogConciliadora == null) {
            listaLogConciliadora = new ArrayList<LogConciliadoraVO>();
        }
        return listaLogConciliadora;
    }

    public void setListaLogConciliadora(List<LogConciliadoraVO> listaLogConciliadora) {
        this.listaLogConciliadora = listaLogConciliadora;
    }

    public void notificarRecursoClickTelaCliente() {
        notificarRecursoEmpresa(RecursoSistema.REGISTRAR_NOVA_VISITA);
    }

    public void notificarRecursoEmpresaMudanca() {
        notificarRecursoEmpresa(RecursoSistema.MUDANCA_DE_PLANO);
        setTrocaPlano(true);
    }

    public void notificarRecursoEmpresaTreinoZW() {
        notificarRecursoEmpresa(RecursoSistema.ABRIR_CLIENTE_TREINO_WEB_ATRAVES_ZW);
    }

    public void notificarRecursoEmpresaMudancaUpDown() {
        if (getContratoParaTransferenciaDePlano().getPlano().getPlanoRecorrencia().getValorMensal() >
                getNovoContratoParaTransferenciaDePlano().getPlano().getPlanoRecorrencia().getValorMensal()) {
            notificarRecursoEmpresa(RecursoSistema.DOWNGRADE_PLANO_SUCESSO);
        } else {
            notificarRecursoEmpresa(RecursoSistema.UPGRADE_PLANO_SUCESSO);
        }
    }

    public boolean isPermiteLiberarCobrancaDataVencimentoParcelas() {
        return permiteLiberarCobrancaDataVencimentoParcelas;
    }

    public void setPermiteLiberarCobrancaDataVencimentoParcelas(boolean permiteLiberarCobrancaDataVencimentoParcelas) {
        this.permiteLiberarCobrancaDataVencimentoParcelas = permiteLiberarCobrancaDataVencimentoParcelas;
    }

    public boolean isLiberarValorProRataVencimentoParcelas() {
        return liberarValorProRataVencimentoParcelas;
    }

    public void setLiberarValorProRataVencimentoParcelas(boolean liberarValorProRataVencimentoParcelas) {
        this.liberarValorProRataVencimentoParcelas = liberarValorProRataVencimentoParcelas;
    }

    public Boolean getCompartilharLink() {
        return compartilharLink;
    }

    public void setCompartilharLink(Boolean compartilharLink) {
        this.compartilharLink = compartilharLink;
    }

    public void validarLinkCartao() {
        notificarRecursoEmpresa(RecursoSistema.LINK_CADASTRO_CARTAO_ONLINE);
        setCobrarParcelasEmAberto(false);
        setCadastrarCartaoOnline(true);
        validarCompartilharLink(true);
    }


    public void validarLinkPagamento() {
        notificarRecursoEmpresa(RecursoSistema.LINK_PAGAMENTO_ONLINE);
        setCobrarParcelasEmAberto(true);
        setCadastrarCartaoOnline(false);
        validarCompartilharLink(false);
    }

    public void validarCompartilharLink(boolean cadastrarCartao) {
        try {
            setMsgAlert("");
            getFacade().getCliente().validarCompartilharLink(getCliente(), cadastrarCartao, getEmpresaLogado(), getUsuarioLogado(), null, null);
            setMsgAlert("Richfaces.showModalPanel('modalCompartilharPagamento')");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void linkCompartilhar() {
        try {
            setMsgAlert("");
            VendasOnlineService vendasOnlineService = new VendasOnlineService(null, Conexao.getFromSession());
            String link = vendasOnlineService.obterLinkPagamentoVendasOnline(getKey(), getCliente(), getCliente().getEmpresa().getCodigo(), isCobrarParcelasEmAberto(),
                    isCobrarParcelasEmAberto() ? OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO : OrigemCobrancaEnum.VENDAS_ONLINE_LINK_CADASTRAR, null,
                    getUsuarioLogado(), null, null, null, 1);
            setMsgAlert("copiar('" + link + "')");
            incluirLogResponsavelGerarLink(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void linkCompartilharWhatsapp() {
        try {
            setMsgAlert("");
            VendasOnlineService vendasOnlineService = new VendasOnlineService(null, Conexao.getFromSession());
            String link = vendasOnlineService.obterLinkPagamentoVendasOnline(getKey(), getCliente(), getCliente().getEmpresa().getCodigo(), isCobrarParcelasEmAberto(),
                    isCobrarParcelasEmAberto() ? OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO : OrigemCobrancaEnum.VENDAS_ONLINE_LINK_CADASTRAR, null,
                    getUsuarioLogado(), null, null, null, 1);

            if (!UteisValidacao.emptyList(getCliente().getPessoa().getTelefoneVOs())) {
                String conteudo = "*Seu link para cadastrar o cartão de crédito. TAG_EMPRESA*\n" +
                        "URL_LINK_PAGAMENTO";
                conteudo = conteudo.replaceAll("TAG_EMPRESA", getEmpresaLogado().getNome());
                conteudo = conteudo.replaceAll("URL_LINK_PAGAMENTO", link);

                String telaWhatsApp = "https://web.whatsapp.com/send?phone=55"
                        + Uteis.removerMascara(getCliente().getPessoa().getTelefoneVOs().get(0).getNumero()
                                .replaceAll(" ", ""))
                        .replaceAll("\\(", "").replaceAll("\\)", "")
                        + "&text="
                        + URLEncoder.encode(conteudo, "UTF-8")
                        .replaceAll("\\+", "%20");
                setMsgAlert("abrirPopup('" + telaWhatsApp + "','WhatsApp', 1000, 650);");

                incluirLogResponsavelGerarLink(true);
            }
        } catch (Exception e) {
        }
    }

    public void incluirLogResponsavelGerarLink(boolean whatsapp) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(getClienteVO().getPessoa().getCodigo().toString());
            obj.setNomeEntidade("LINK DE COMPARTILHAMENTO");
            obj.setNomeEntidadeDescricao("LINK DE COMPARTILHAMENTO");
            if (isCadastrarCartaoOnline()) {
                obj.setOperacao("GEROU LINK PARA CADASTRAR CARTÃO");
            } else {
                obj.setOperacao("GEROU LINK PARA PAGAMENTO");
            }
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("Compartilhou via");
            obj.setValorCampoAnterior("");
            if (whatsapp) {
                obj.setValorCampoAlterado("botão WhatsApp");
            } else {
                obj.setValorCampoAlterado("botão Copiar");
            }

            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, getClienteVO().getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("GEROU LINK DE COMPARTILHAMENTO", getClienteVO().getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG "
                    + " GEROU LINK DE COMPARTILHAMENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public boolean isApresentaMudancaPlano() {
        return apresentaMudancaPlano;
    }

    public void setApresentaMudancaPlano(boolean apresentaMudancaPlano) {
        this.apresentaMudancaPlano = apresentaMudancaPlano;
    }

    public ModeloOrcamentoVO getModeloOrcamento() {
        return modeloOrcamento;
    }

    public void setModeloOrcamento(ModeloOrcamentoVO modeloOrcamento) {
        this.modeloOrcamento = modeloOrcamento;
    }
    public void setOnCompleteTotalPass(String onCompleteTotalPass) {
        this.onCompleteTotalPass = onCompleteTotalPass;
    }

    public String getOnCompleteGymPass() {
        if (onCompleteGymPass == null) {
            onCompleteGymPass = "";
        }
        return onCompleteGymPass;
    }

    public void registrarTotalPass() {
        if (getClienteVO().getPessoa().getCfp() != null  && !getClienteVO().getPessoa().getCfp().isEmpty()) {
            tokemTOTALPASS =  Uteis.removerMascara(getClienteVO().getPessoa().getCfp().trim());
            notificarRecursoEmpresa(RecursoSistema.TOTALPASS);
            limparMensagem();
        }
    }

    public void setOnCompleteGymPass(String onCompleteGymPass) {
        this.onCompleteGymPass = onCompleteGymPass;
    }

    public List<PeriodoAcessoClienteVO> getListaGymPass() {
        if (listaGymPass == null) {
            listaGymPass = new ArrayList<PeriodoAcessoClienteVO>();
        }
        return listaGymPass;
    }

    public void setListaGymPass(List<PeriodoAcessoClienteVO> listaGymPass) {
        this.listaGymPass = listaGymPass;
    }

    public List<LogTotalPassVO> getListaTotalPass() {
        if (listaTotalPass == null) {
            listaTotalPass = new ArrayList<LogTotalPassVO>();
        }
        return listaTotalPass;
    }

    public void setListaTotalPass(List<LogTotalPassVO> listaTotalPass) {
        this.listaTotalPass = listaTotalPass;
    }

    public void novoGymPass() {
        if (getClienteVO().getGympasUniqueToken() != null && !getClienteVO().getGympasUniqueToken().isEmpty()) {
            tokemGYMPASS = getClienteVO().getGympasUniqueToken().trim();
            notificarRecursoEmpresa(RecursoSistema.GYMPASS);
            limparMensagem();
        }
    }

    public void excluirTokenGymPass() {
        try {
            validarPermissao("ExcluirTokemGympass", "9.63 - Excluir Tokem GymPass", getUsuarioLogado());
            getFacade().getCliente().excluirTokenGymPassCliente(getClienteVO(), getUsuarioLogado());
            getClienteVO().setGympasUniqueToken("");
            montarSucessoGrowl("Token excluído com sucesso!");
        } catch (Exception e) {
            montarErro("Não foi possível excluir o Token! " + e.getMessage());
        }
    }

    public void salvarTokenGymPass() {
        try {
            getClienteVO().setGympasUniqueToken(getClienteVO().getGympasUniqueToken().trim());
            validarInformacoesTokenGymPass();
            String tokem = getClienteVO().getGympasUniqueToken();

            String matricula = getFacade().getCliente().nomeMatricuaClienteGymPassUniqueToken(getClienteVO().getGympasUniqueToken());
            if (!matricula.isEmpty() && !matricula.trim().contains(getClienteVO().getMatricula())) {
                getClienteVO().setGympasUniqueToken(tokemGYMPASS);
                throw new Exception("O Token (" + tokem + ") já está sendo utilizado por " + matricula);
            }

            getFacade().getCliente().alterarGymPassUniqueToken(getClienteVO(), getUsuarioLogado());
            setOnCompleteGymPass("");
            limparMsg();
            montarSucessoGrowl("Token salvo com sucesso!");
            tokemGYMPASS = getClienteVO().getGympasUniqueToken().trim();
        } catch (Exception ex) {
            if (ex.getMessage().toUpperCase().contains("GYMPASS"))
                montarAviso(ex.getMessage());
            else
                montarErro(ex);
        }
    }

    public void validarTotalPass() throws Exception {
        try {
            limparMsg();
            getFacade().getCliente().validarTotalPass(getClienteVO(), getEmpresaLogado(), getUsuarioLogado());
            montarSucessoGrowl("TotalPass lançado com sucesso!");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * Método responsável por desvincular um aluno do TotalPass.
     * Chama o método do facade que busca o aluno e remove a vinculação.
     */
    public void desvincularAlunoTotalpass() throws Exception {
        try {
            limparMsg();
            String nomeAluno = getFacade().getCliente().desvincularAlunoTotalpass(
                getClienteVO().getPessoa().getCodigo().longValue(),
                getUsuarioLogado()
            );
            montarSucessoGrowl("TotalPass desvinculado com sucesso para o aluno: " + nomeAluno);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void validarTotalPassANTIGO() throws Exception {
        try {
            List<TotalPassVO> listaTotalPass = getFacade().getTotalPass().consultarPorEmpresa(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            Boolean jaPossuiAcesso = getFacade().getPeriodoAcessoCliente().existePeriodoAcessoTotalpassVigente(cliente.getPessoa().getCodigo());

            String msg = "";

            msg = validarInformacoesTokenTotalPass(jaPossuiAcesso, listaTotalPass);

            if(msg.isEmpty()) {
                TotalPassVO item = listaTotalPass.get(0);
                ApiTotalPass apiTotalPass = new ApiTotalPass();
                LogTotalPassVO logTotalPassVO = apiTotalPass.consumirAPI("Manual",getClienteVO().getPessoa().getCodigo(), getEmpresaLogado().getCodigo(), item.getApiKey().trim(), Uteis.removerMascara(getClienteVO().getPessoa().getCfp()), item.getCodigoTotalpass().trim(), false);
                getFacade().getLogTotalPass().incluir(logTotalPassVO);
                Boolean naoTem = true;
                if (logTotalPassVO.getResposta().equals(AUTORIZADO_TOTALPASS)) {
                    try {
                        Integer freepass = getFacade().getCliente().jaTemFreePass(Calendario.hoje(), getClienteVO().getCodigo());
                        naoTem = UteisValidacao.emptyNumber(freepass);
                    } catch (Exception e) {
                        Uteis.logar(e, TelaClienteControle.class);
                    }
                    if (naoTem && !jaPossuiAcesso) {
                        getClienteVO().setFreePass(getFacade().getProduto().criarOuConsultarProdutoTotalPass());
                        getClienteVO().setResponsavelFreePass(0);
                        getFacade().getCliente().incluirFreePassTotalPass(getClienteVO(), Calendario.hoje(), true);
                    }
                    //REGISTRAR LOG DA ALTERAÇAO DE MATRÍCULA
                    registrarLogTotalPass(TotalPassVO.TOTALPASS_OPERACOES_VALIDACAO, "");
                    limparMsg();
                    montarSucessoGrowl("TotalPass lançado com sucesso!");

                } else if (logTotalPassVO.getResposta().equals(BLOQUEADO_TOTALPASS_CHECKIN)) {
                    limparMsg();
                    try {
                        JSONObject json = new JSONObject(logTotalPassVO.getRespostaApi());
                        montarErro(json.getJSONArray("errors").getJSONObject(0).getJSONArray("source").getJSONObject(0).getString("message"));
                        return;
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                    montarErro(" Por favor realize o check-in na TotalPass.");
                }else{
                    notificarRecursoEmpresa(RecursoSistema.TOTAL_PASS_ERRO_VALIDACAO_EXISTE_NO_DIA);
                    limparMsg();
                    montarErro("TotalPass não validou o acesso.");
                }
            }else{
                montarErro(msg);
            }
        }catch(Exception e ){
            montarErro(e);
        }
    }

    public void validarTokenGymPass() {
        try {
            validarInformacoesTokenGymPass();
            getFacade().getCliente().validarTokenGymPass(getKey(), getClienteVO(), getUsuarioLogado(), getEmpresaLogado(), true);
            limparMsg();
            montarSucessoGrowl("GymPass lançado com sucesso!");
        } catch (Exception ex) {
            if (ex.getMessage().toUpperCase().contains("GYMPASS"))
                montarAviso(ex.getMessage());
            else
                montarErro(ex);
        }
    }

    public void validarTokenGymPassAntigo() {
        try {
            validarInformacoesTokenGymPass();
            //salvar no cliente as infos passadas na modal
            getFacade().getCliente().alterarGymPassUniqueToken(getClienteVO(), getUsuarioLogado());
            Map<String, String> headers = new HashMap<String, String>();
            final String urlMsValidate = String.format("%s/gymid/validate-user/%s/%s",
                    PropsService.getPropertyValue(PropsService.urlGymPassMs),
                    getEmpresaLogado().getCodigoGymPass(),
                    getClienteVO().getGympasUniqueToken()
            );
            Uteis.logar(null, "GYMPASS MS Requisição | " + urlMsValidate);
            String retorno = "";
            JSONObject jsonObject = new JSONObject();
            JSONObject content = new JSONObject();
            try {
                retorno = ExecuteRequestHttpService.executeHttpRequestGETEncode(urlMsValidate, headers, "UTF-8");
                jsonObject = new JSONObject(retorno);
                content = new JSONObject(jsonObject.get("content").toString());
            } catch (Exception e) {
                Uteis.logar(e, TelaClienteControle.class);
            }
            Uteis.logar(null, "GYMPASS Retorno | " + retorno);

            if (content.getInt("code") == 200 || (content.getInt("code") == 400 && content.getString("key").equals("checkin.already.validated"))) {
                notificarRecursoEmpresa(RecursoSistema.GYM_PASS_SUCESSO);
                getClienteVO().setResponsavelFreePass(getUsuarioLogado().getCodigo());
                getClienteVO().setFreePass(getFacade().getProduto().criarOuConsultarProdutoGymPass());
                Boolean naoTem = true;
                try {
                    Integer freepass = getFacade().getCliente().jaTemFreePass(Calendario.hoje(), getClienteVO().getCodigo());
                    naoTem = UteisValidacao.emptyNumber(freepass);
                }catch (Exception e){
                    Uteis.logar(e, TelaClienteControle.class);
                }
                if(naoTem){
                    PeriodoAcessoClienteVO periodoAcessoClienteVO = getFacade().getCliente().incluirFreePass(getClienteVO(), Calendario.hoje(), getClienteVO().getGympasUniqueToken(), getClienteVO().getGympassTypeNumber(), null);
                    if (periodoAcessoClienteVO != null && content.getInt("code") == 200 &&
                            content.has("produtoGymPass") && !UteisValidacao.emptyString(content.getString("produtoGymPass"))) {
                        getFacade().getPeriodoAcessoCliente().gravarInfoCheckin(getClienteVO(),
                                new EmpresaVO(getClienteVO().getEmpresa().getCodigo()),
                                getClienteVO().getGympasUniqueToken(),
                                periodoAcessoClienteVO,
                                IntegracoesEnum.GYMPASS,
                                content.getString("produtoGymPass")
                        );
                    }
                }
                //REGISTRAR LOG DA ALTERAÇAO DE MATRÍCULA
                registrarLogGynPass(GympassVO.GYMPASS_OPERACOES_VALIDACAO, "");
                limparMsg();
                montarSucessoGrowl("GymPass lançado com sucesso!");
            } else {
                validarMensagemErroAutorizarTokenGymPass(content);
            }
        } catch (Exception ex) {
            if (ex.getMessage().toUpperCase().contains("GYMPASS"))
                montarAviso(ex.getMessage());
            else
                montarErro(ex);
        }
    }

    private void validarMensagemErroAutorizarTokenGymPass(JSONObject content) {
        notificarRecursoEmpresa(RecursoSistema.GYM_PASS_ERRO_VALIDACAO_EXISTE_NO_DIA);
        limparMsg();
        switch (content.getInt("code")) {
            case 404:
                montarErro("Não foi encontrado na Gympass o check-in do aluno. Por favor, peça ao aluno para realizar o check-in e tente novamente.");
                break;
            case 400:
                switch (content.getString("key")) {
                    case "checkin.validation.cancelled":
                        montarErro("O aluno cancelou o checkin e seu sistema tentou validar. Por favor, peça ao aluno para realizar o check-in e tente novamente.");
                        break;
                    case "checkin.validation.expired":
                        montarErro("O check-in do aluno expirou. Por favor, peça ao aluno para realizar o check-in e tente novamente.");
                        break;
                    case "checkin.already.validated":
                        montarErro("O check-in do aluno já foi validado.");
                        break;
                    default:
                        montarErro("Erro não definido!");
                        break;
                }
                break;
            case 403:
                montarErro("Problema na integração na Gympass, Por favor, entre em contato com a Gympass e solicite para verificar seus dados e integração!");
                break;
            default:
                montarErro(content.getString("key"));
                break;
        }
    }

    private void registrarLogTotalPass(String oep, String detalhes) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(getClienteVO().getPessoa().getCodigo().toString());
            obj.setNomeEntidade("CLIENTE - TOTALPASS");
            obj.setNomeEntidadeDescricao("Cliente - TotalPass");
            obj.setOperacao(oep + " TOTALPASS");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("TOTALPASS-Cliente");
            obj.setValorCampoAnterior("TOKEN TOTALPASS: " + getClienteVO().getPessoa().getCfp());
            obj.setValorCampoAlterado(detalhes);
            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, getClienteVO().getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLIENTE - TOTALPASS", getClienteVO().getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG " + oep
                    + " TOTALPASS", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }


    private String validarInformacoesTokenTotalPass( Boolean jaPossuiAcesso, List<TotalPassVO> listaTotalPass) throws Exception {
        limparMsg();
        limparMensagem();
        setOnCompleteGymPass("");
        TotalPassVO itens = new TotalPassVO();

        if (listaTotalPass.isEmpty()) {
            return "Informe as configurações para TotalPass na tela de integrações.";
        }else{
            itens = listaTotalPass.get(0);
        }

        if (UteisValidacao.emptyString(getClienteVO().getPessoa().getCfp())) {
            return "Cadastre o cpf para realizar validação com TotalPass.";
        }
        if (getEmpresaLogado() != null && UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
            return "Nenhuma empresa logada.";
        }
        if (UteisValidacao.emptyString(itens.getApiKey()) || UteisValidacao.emptyString(itens.getCodigoTotalpass()) || itens.getInativo()) {
            return "Informe as configurações para TotalPass na tela de integrações.";
        }
        if(jaPossuiAcesso){
            return "A validação nesta empresa já foi realizada hoje";
        }
        return "";
    }

    private void registrarLogGynPass(String oep, String detalhes) throws Exception {
        try {
            getFacade().getCliente().registrarLogGynPass(getClienteVO(), oep, detalhes, getUsuarioLogado());
        } catch (Exception e) {
            registrarLogErroObjetoVO("CLIENTE - GYMPASS", getClienteVO().getPessoa().getCodigo(), "ERRO AO REGISTRAR LOG " + oep
                    + " GYMPASS", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void validarInformacoesTokenGymPass() throws Exception {
        limparMsg();
        limparMensagem();
        setOnCompleteGymPass("");

        if (UteisValidacao.emptyString(getClienteVO().getGympasUniqueToken())) {
            throw new Exception("Informe o Token GymPass.");
        }
        if (getEmpresaLogado() != null && UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
            throw new Exception("Nenhuma empresa logada.");
        }
        if (UteisValidacao.emptyString(getEmpresaLogado().getCodigoGymPass())) {
            throw new Exception("Informe o Código da Academia na GymPass nas configurações da empresa.");
        }
    }

    public void abrirHistoricoGymPass() {
        try {
            setListaGymPass(new ArrayList<>());
            setListaGymPass(getFacade().getPeriodoAcessoCliente().consultarPorPessoaGymPass(getClienteVO().getPessoa().getCodigo()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirHistoricoTotalPass() {
        try {
            setListaTotalPass(new ArrayList<>());
            setListaTotalPass(getFacade().getLogTotalPass().consultarPorPessoaLogTotalPass(getClienteVO().getPessoa().getCodigo()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<SelectItem> getListaTipoGymPass() {
        List<SelectItem> listaTiposGymPass = new ArrayList<>();
        listaTiposGymPass.add(new SelectItem("1", "Tipo 1"));
        listaTiposGymPass.add(new SelectItem("2", "Tipo 2"));
        listaTiposGymPass.add(new SelectItem("3", "Tipo 3"));
        listaTiposGymPass.add(new SelectItem("4", "Tipo 4"));
        listaTiposGymPass.add(new SelectItem("5", "Tipo 5"));
        return listaTiposGymPass;
    }

    public String getOnCompleteBloqueioCobrancas() {
        if (onCompleteBloqueioCobrancas == null) {
            onCompleteBloqueioCobrancas = "";
        }
        return onCompleteBloqueioCobrancas;
    }

    public void setOnCompleteBloqueioCobrancas(String onCompleteBloqueioCobrancas) {
        this.onCompleteBloqueioCobrancas = onCompleteBloqueioCobrancas;
    }

    public void abrirBloquearCobrancasAutomatica() {
        try {
            limparMsg();
            setOnCompleteBloqueioCobrancas("");

            this.getCliente().getPessoa().setDataBloqueioCobrancaAutomatica(null);
            this.getCliente().getPessoa().setTipoBloqueioCobrancaAutomatica(null);

            setOnCompleteBloqueioCobrancas("Richfaces.showModalPanel('modalConfirmarBloquearCobrancas');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void confirmarBloquearCobrancasAutomatica() {
        try {
            limparMsg();
            setOnCompleteBloqueioCobrancas("");

            if (this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica() == null) {
                throw new Exception("Selecione o tipo de bloqueio");
            }

            if (this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS) &&
                    this.getCliente().getPessoa().getDataBloqueioCobrancaAutomatica() == null) {
                throw new Exception("Selecione uma data de bloqueio");
            }

            if (this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS)) {
                this.getCliente().getPessoa().setDataBloqueioCobrancaAutomatica(Calendario.hoje());
            }

            getFacade().getPessoa().alterarDataBloqueioCobrancaAutomatica(this.getCliente().getPessoa().getDataBloqueioCobrancaAutomatica(),
                    this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica(), this.getCliente().getPessoa().getCodigo(), getUsuarioLogado(), true, "");

            notificarRecursoEmpresa(RecursoSistema.COBRANCA_BLOQUEADA_BLOQUEAR);

            setOnCompleteBloqueioCobrancas("Richfaces.hideModalPanel('modalConfirmarBloquearCobrancas');document.getElementById('form:btnAtualizaCliente').click()");

            String msg = "Todas as cobranças automáticas do aluno foram bloqueadas.";
            if (this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS)) {
                msg = "Todas as cobranças com data superior à " + this.getCliente().getPessoa().getDataBloqueioCobrancaAutomaticaApresentar() + ", foram bloqueadas.";
            }
            montarSucessoGrowl(msg);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void confirmarDesbloquearCobrancasAutomatica() {
        try {
            limparMsg();
            setOnCompleteBloqueioCobrancas("");

            getFacade().getPessoa().alterarDataBloqueioCobrancaAutomatica(null, null,
                    this.getCliente().getPessoa().getCodigo(), getUsuarioLogado(), true, "");

            notificarRecursoEmpresa(RecursoSistema.COBRANCA_BLOQUEADA_DESBLOQUEAR);
            setOnCompleteBloqueioCobrancas("Richfaces.hideModalPanel('modalDesbloquearBloquearCobrancas');document.getElementById('form:btnAtualizaCliente').click()");
            montarSucessoGrowl("Parcelas liberadas para cobrança automática.");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isSelecionouParcelasFuturas() {
        return this.getCliente() != null &&
                this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica() != null &&
                this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS);
    }

    public boolean isSelecionouTodasParcelas() {
        return this.getCliente() != null &&
                this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica() != null &&
                this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS);
    }

    public List<SelectItem> getListSelectItemTipoBloqueioCobrancaEnum() {
        return TipoBloqueioCobrancaEnum.getSelectListTipoBloqueioCobrancaEnum();
    }

    public String getBloqueioCobrancaApresentar() {
        boolean temPermissao;
        try {
            temPermissao = permissao("PermiteBloquearDesbloquearClienteCobrancaAutomatica");
        } catch (Exception ex) {
            temPermissao = false;
        }

        String msgPermissao = "<br/><br/>Necessário permissão: <b>\"9.64 - Permite bloquear ou desbloquear cobrança automática do cliente\"</b><br/>";

        StringBuilder msg = new StringBuilder();
        if (this.getCliente().getPessoa().getDataBloqueioCobrancaAutomatica() == null) {
            msg.append("A cobrança automática está liberada.");
            if (!temPermissao) {
                msg.append(msgPermissao);
            }
        } else {

            if (this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.TODAS_PARCELAS)) {
                msg.append("<b>TODAS</b> as parcelas estão bloqueadas para cobrança automática.");
            } else if (this.getCliente().getPessoa().getTipoBloqueioCobrancaAutomatica().equals(TipoBloqueioCobrancaEnum.PARCELAS_FUTURAS)) {
                msg.append("<b>TODAS</b> as parcelas com vencimento superior ao dia <b>").append(this.getCliente().getPessoa().getDataBloqueioCobrancaAutomaticaApresentar());
                msg.append("</b> estão bloqueadas para cobrança automática.");
            } else {
                return "";
            }

            if (!temPermissao) {
                msg.append(msgPermissao);
            } else {
                msg.append("<br/><br/><b>CLIQUE PARA DESBLOQUEAR</b>");
            }
        }
        return msg.toString();
    }

    public boolean isApresentarBloqueioParcelas() {
        return apresentarBloqueioParcelas;
    }

    public void setApresentarBloqueioParcelas(boolean apresentarBloqueioParcelas) {
        this.apresentarBloqueioParcelas = apresentarBloqueioParcelas;
    }

    private void verificarMGBIntegracao(){
        try {
            nivelMGB = getFacade().getMgbService().nivelAluno(getEmpresaLogado().getCodigo(), cliente.getMatricula());
        }catch (Exception e){
            Uteis.logar(e, TelaClienteControle.class);
            nivelMGB = null;
        }
    }

    public String getNivelMGB() {
        return nivelMGB;
    }

    public void setNivelMGB(String nivelMGB) {
        this.nivelMGB = nivelMGB;
    }

    public void abrirModalRemoverParcelaBoleto() {
        try {
            limparMsg();
            setOnCompleteGenerico("");

            RemessaItemVO obj = (RemessaItemVO) context().getExternalContext().getRequestMap().get("item");
            if (obj == null) {
                throw new Exception("Remessa item não encontrado.");
            }

            setRemessaItemVORemover(obj);
            setOnCompleteGenerico("Richfaces.showModalPanel('modalRemoverParcelaBoleto')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean getButtonAcessoConvidado() {
        try {
            return cliente.getSituacao().equals("VI") && getFacade().getConvite().faltaLancarAcessoConvidado(cliente);
        } catch (Exception ignore) {
            return false;
        }
    }

    public void ativarAcessoConvidado() {
        try {
            cliente.setResponsavelFreePass(getUsuarioLogado().getCodigo());
            cliente.setFreePass(getFacade().getProduto().criarOuConsultarProdutoPorTipoNrDiasVigencia(TipoProduto.FREEPASS.getCodigo(), 1, "CONVITE", Uteis.NIVELMONTARDADOS_MINIMOS));
            getFacade().getCliente().incluirFreePass(cliente, Calendario.hoje(), null, null, null);
            getFacade().getConvite().atualizarFaltaAcessoConvidado(cliente);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void confirmarRemoverParcelaBoleto() {
        try {
            limparMsg();
            setOnCompleteGenerico("");

            if (UteisValidacao.emptyNumber(getRemessaItemVORemover().getCodigo())) {
                throw new Exception("Remessa item não encontrado.");
            }

            getFacade().getRemessaItemMovParcela().removerParcelaBoleto(getClienteVO(), getRemessaItemVORemover(), getUsuarioLogado());

            setOnCompleteGenerico("Richfaces.hideModalPanel('modalRemoverParcelaBoleto')");
            montarSucessoGrowl("Parcela foi removida");
        } catch (Exception ex) {
            setErro(true);
            setMensagem(ex.getMessage());
            montarErro(ex);
        }
    }

    public String getTextoRemoverItemRemessa() {
        if (getRemessaItemVORemover() != null && getRemessaItemVORemover().getRemessa() != null && getRemessaItemVORemover().getRemessa().getDataFechamento() == null) {
            return "A remessa desta parcela ainda não foi fechada. A parcela será removida da remessa e não irá gerar nenhuma cobrança. Deseja prosseguir?";
        } else {
            return "A remessa desta parcela já foi fechada. Ao remover essa parcela do registro de remessa, ela poderá ser cobrada também automaticamente através de outro convênio (caso seja incluído por exemplo uma autorização de cartão de crédito)" +
                    " para este aluno, mesmo que ele ainda tenha o convênio de boleto. Caso ocorra o pagamento do boleto e o processo de cobrança automático também cobre automaticamente no cartão de crédito, a " +
                    "diferença será incluída como crédito na conta corrente desse aluno.";
        }
    }

    public String getOnCompleteGenerico() {
        if (onCompleteGenerico == null) {
            onCompleteGenerico = "";
        }
        return onCompleteGenerico;
    }

    public void setOnCompleteGenerico(String onCompleteGenerico) {
        this.onCompleteGenerico = onCompleteGenerico;
    }

    public RemessaItemVO getRemessaItemVORemover() {
        if (remessaItemVORemover == null) {
            remessaItemVORemover = new RemessaItemVO();
        }
        return remessaItemVORemover;
    }

    public void setRemessaItemVORemover(RemessaItemVO remessaItemVORemover) {
        this.remessaItemVORemover = remessaItemVORemover;
    }

    public boolean isCobrarParcelasEmAberto() {
        return cobrarParcelasEmAberto;
    }

    public void setCobrarParcelasEmAberto(boolean cobrarParcelasEmAberto) {
        this.cobrarParcelasEmAberto = cobrarParcelasEmAberto;
    }

    public boolean isCadastrarCartaoOnline() {
        return cadastrarCartaoOnline;
    }

    public void setCadastrarCartaoOnline(boolean cadastrarCartaoOnline) {
        this.cadastrarCartaoOnline = cadastrarCartaoOnline;
    }

    public List<IndicadoVO> getListaIndicacoesAmigoFit() {
        return listaIndicacoesAmigoFit;
    }

    public void setListaIndicacoesAmigoFit(List<IndicadoVO> listaIndicacoesAmigoFit) {
        this.listaIndicacoesAmigoFit = listaIndicacoesAmigoFit;
    }

    public List<UtilizacaoAvaliacaoFisicaVO> getUtilizacoes() {
        if (utilizacoes == null) {
            utilizacoes = new ArrayList<>();
        }
        return utilizacoes;
    }

    public void setUtilizacoes(List<UtilizacaoAvaliacaoFisicaVO> utilizacoes) {
        this.utilizacoes = utilizacoes;
    }

    public void mostrarMaisModalidades() throws Exception {
        consultarModalidadesContrato();
    }

    public void consultarModalidadesContrato() throws Exception {
        modalidadesContrato = getFacade().getContrato().getContratoModalidades(cliente.getPessoa().getCodigo(), exibirTodasModalidades);
        totalModalidadesContrato = getFacade().getContrato().countModalidadesContrato(cliente.getPessoa().getCodigo());
    }

    public List<String> getModalidadesContrato() {
        return modalidadesContrato;
    }

    public int getTotalModalidadesContrato() {
        return totalModalidadesContrato;
    }

    public boolean getExibirTodasModalidades() {
        return exibirTodasModalidades;
    }

    public void setExibirTodasModalidades(boolean exibirTodasModalidades) {
        this.exibirTodasModalidades = exibirTodasModalidades;
    }

    public ListaPaginadaTO getListaPix() {
        return listaPix;
    }

    public void setListaPix(ListaPaginadaTO listaPix) {
        this.listaPix = listaPix;
    }

    public ListaPaginadaTO getListaCancelamentoGetCard() {
        return listaCancelamentoGetCard;
    }

    public void setListaCancelamentoGetCard(ListaPaginadaTO listaCancelamentoGetCard) {
        this.listaCancelamentoGetCard = listaCancelamentoGetCard;
    }

    public List<CancelamentoGetCardTO> getListaCancelamentoGetCardVo() {
        return listaCancelamentoGetCardVo;
    }

    public void setListaCancelamentoGetCardVo(List<CancelamentoGetCardTO> listaCancelamentoGetCardVo) {
        this.listaCancelamentoGetCardVo = listaCancelamentoGetCardVo;
    }

    public List<PixVO> getListaPixVo() {
        return listaPixVo;
    }

    public void setListaPixVo(List<PixVO> listaPixVo) {
        this.listaPixVo = listaPixVo;
    }

    public int getDiasProRata() {
        int diferencaDiasNovoVencimento = 0;

        try {
            Date dataVencimentoAPartir = Calendario.hoje();
            int diaAmanha = Calendario.getDia(dataVencimentoAPartir);
            if (diaAmanha > getContratoParaTransferenciaDePlano().getDiaVencimentoProrata()) {
                diferencaDiasNovoVencimento = 30 + (diaAmanha - getContratoParaTransferenciaDePlano().getDiaVencimentoProrata());
                dataVencimentoAPartir = Uteis.somarDias(dataVencimentoAPartir, diferencaDiasNovoVencimento);
                diferencaDiasNovoVencimento = Calendario.diferencaEmDias(dataVencimentoAPartir, Calendario.hoje());
            } else {
                diferencaDiasNovoVencimento = getContratoParaTransferenciaDePlano().getDiaVencimentoProrata() - diaAmanha;
            }

            if (diferencaDiasNovoVencimento < 0) {
                diferencaDiasNovoVencimento = 0;
            }

        } catch (Exception e) {
            montarErroComLog(e);
        }
        return diferencaDiasNovoVencimento;
    }

    public Double getValorPago() throws Exception {
        if(contratoSelecionado != null) {
            Double sum = getFacade().getContrato().consultarValorPagoContrato(contratoSelecionado.getCodigo());
            return sum;
        } else {
            return 0.0;
        }
    }

    public boolean getApresentarDayUse() {
        return apresentarDayUse;
    }

    public void setApresentarDayUse(boolean apresentarDayUse) {
        this.apresentarDayUse = apresentarDayUse;
    }

    public boolean getApresentarDiaPlus() {
        return apresentarDiaPlus;
    }

    public void setApresentarDiaPlus(boolean apresentarDiaPlus) {
        this.apresentarDiaPlus = apresentarDiaPlus;
    }

    public ListaPaginadaTO getListaBoletoGeral() {
        return listaBoletoGeral;
    }

    public void setListaBoletoGeral(ListaPaginadaTO listaBoletoGeral) {
        this.listaBoletoGeral = listaBoletoGeral;
    }

    public List<BoletoVO> getListaBoletoGeralVo() {
        return listaBoletoGeralVo;
    }

    public void setListaBoletoGeralVo(List<BoletoVO> listaBoletoGeralVo) {
        this.listaBoletoGeralVo = listaBoletoGeralVo;
    }

    public String getOnCompleteBoleto() {
        if (onCompleteBoleto == null) {
            onCompleteBoleto = "";
        }
        return onCompleteBoleto;
    }

    public void setOnCompleteBoleto(String onCompleteBoleto) {
        this.onCompleteBoleto = onCompleteBoleto;
    }

    public String getOnCompletePix() {
        if (UteisValidacao.emptyString(onCompletePix)) {
            return "";
        }
        return onCompletePix;
    }

    public void setOnCompletePix(String onCompletePix) {
        this.onCompletePix = onCompletePix;
    }

    public void detalheBoleto(ActionEvent evt) {
        try {
            limparMsg();
            setOnCompleteBoleto("");
            BoletoVO boletoVO = (BoletoVO) JSFUtilities.getFromActionEvent("boletoDetalhe", evt);
            boletoVO.setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorCodigo(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0));
            if (boletoVO.getConvenioCobrancaVO().isBoletoAsaas()) {
                detalheBoletoAsaas(boletoVO);
            } else if (boletoVO.getConvenioCobrancaVO().isBoletoPjBank()) {
                detalheBoletoPjBank(boletoVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void detalheBoletoPjBank(BoletoVO boletoVO) {
        try {
            String detalhesBoletoPJBank = getFacade().getBoleto().obterDetalheBoletoPJBank(boletoVO.getCodigo());
            setOnCompleteBoleto("window.open('" + detalhesBoletoPJBank + "', '_blank');");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void detalheBoletoAsaas(BoletoVO boletoVO) {
        BoletoAsaasService boletoAsaasService;
        setCobrancaAsaasRetornoDTODetalhar(null);
        setExibirModalParametros(false);
        listaParametrosSelecionado = null;
        try {
            if (boletoVO != null && boletoVO.getEmpresaVO() != null && boletoVO.getConvenioCobrancaVO() != null &&
                    !UteisValidacao.emptyNumber(boletoVO.getEmpresaVO().getCodigo()) && !UteisValidacao.emptyNumber(boletoVO.getConvenioCobrancaVO().getCodigo()) &&
                    !UteisValidacao.emptyString(boletoVO.getIdExterno())) {
                boletoAsaasService = new BoletoAsaasService(Conexao.getFromSession(), boletoVO.getEmpresaVO().getCodigo(), boletoVO.getConvenioCobrancaVO().getCodigo());
                setCobrancaAsaasRetornoDTODetalhar(boletoAsaasService.obterDadosCobranca(boletoVO.getIdExterno()));
                listaParametrosSelecionado = Uteis.obterListaParametrosValores(getCobrancaAsaasRetornoDTODetalhar().toString());
                setExibirModalParametros(true);
                setOnCompleteBoleto("Richfaces.showModalPanel('panelDadosParametrosAsaas');");
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        } finally {
            boletoAsaasService = null;
        }
    }

    public void detalhePix(ActionEvent evt) {
        try {
            limparMsg();
            setOnCompletePix("");
            PixVO pixVO = (PixVO) JSFUtilities.getFromActionEvent("pixDetalhe", evt);
            if (pixVO.getConveniocobranca().isPixAsaas()) {
                detalhePixAsaas(pixVO);
            } else if (pixVO.getConveniocobranca().isPixPjBank()) {
                detalhePixPjBank(pixVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void detalhePixPjBank(PixVO pixVO) {
        try {
            String detalhesPixPJBank = getFacade().getPix().obterDetalhePixPJBank(pixVO.getCodigo());
            setOnCompletePix("window.open('" + detalhesPixPJBank + "', '_blank');");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void detalhePixAsaas(PixVO pixVO) {
        PixAsaas service;
        setCobrancaAsaasRetornoDTODetalhar(null);
        setExibirModalParametros(false);
        listaParametrosSelecionado = null;
        try {
            if (pixVO != null && pixVO.getConveniocobranca() != null &&
                    !UteisValidacao.emptyNumber(pixVO.getEmpresa()) && !UteisValidacao.emptyNumber(pixVO.getConveniocobranca().getCodigo()) &&
                    !UteisValidacao.emptyString(pixVO.getTxid())) {
                service = new PixAsaas(Conexao.getFromSession(), pixVO.getConveniocobranca());
                setCobrancaAsaasRetornoDTODetalhar(service.obterDadosCobranca(pixVO.getTxid()));
                listaParametrosSelecionado = Uteis.obterListaParametrosValores(getCobrancaAsaasRetornoDTODetalhar().toString());
                setExibirModalParametros(true);
                setOnCompleteBoleto("Richfaces.showModalPanel('panelDadosParametrosAsaas');");
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        } finally {
            service = null;
        }
    }

    public void enviarEmailBoleto(ActionEvent evt) {
        try {
            limparMsg();
            setOnCompleteBoleto("");

            BoletoVO obj = (BoletoVO) JSFUtilities.getFromActionEvent("boletoDetalhe", evt);

            List<EmailVO> emailsEnviar = getFacade().getEmail().consultarEmails(obj.getPessoaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(emailsEnviar)) {
                throw new Exception(obj.getPessoaVO().getNome() + " não tem e-mail cadastrado.");
            }

            String[] emails = new String[emailsEnviar.size()];
            int i = 0;
            for (Object objEmail : emailsEnviar) {
                EmailVO emailVO = (EmailVO) objEmail;
                emails[i] = emailVO.getEmail();
                i++;
            }

            getFacade().getBoleto().enviarEmailBoleto(getKey(), obj, emails, true, false);
            montarSucessoGrowl("Email enviado com sucesso, para " + Arrays.toString(emails));
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void enviarEmailClienteBoletoBancoBrasil(BoletoVO boletoVO) {
        BoletoBancarioControle boletoBancarioControle = (BoletoBancarioControle) context().getExternalContext().getSessionMap().get("BoletoBancarioControle");
        boolean emailEnviadoSucesso = boletoBancarioControle.enviarEmailClienteBoletoBancoBrasil(boletoVO);
        if (emailEnviadoSucesso) {
            montarSucessoGrowl("Email enviado com sucesso!");
        } else {
            montarErro("Falha ao tentar enviar email!");
        }
    }

    public void enviarEmailBoletoSelecionados() {
        try {
            limparMsg();
            setOnCompleteBoleto("");

            Map<Integer, List<BoletoVO>> mapaConvenioBoleto = new HashMap<>();
            for (BoletoVO boletoVO : getListaBoletoGeralVo()) {
                if (boletoVO.isSelecionado() && boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                    List<BoletoVO> listaBoleto = mapaConvenioBoleto.get(boletoVO.getConvenioCobrancaVO().getCodigo());
                    if (listaBoleto == null) {
                        listaBoleto = new ArrayList<>();
                    }
                    listaBoleto.add(boletoVO);
                    mapaConvenioBoleto.put(boletoVO.getConvenioCobrancaVO().getCodigo(), listaBoleto);
                }
            }

            if (mapaConvenioBoleto.isEmpty()) {
                throw new Exception("Nenhum boleto selecionado");
            }

            for (Integer conv : mapaConvenioBoleto.keySet()) {
                List<BoletoVO> boletosSelecionados = mapaConvenioBoleto.get(conv);
                List<EmailVO> emailsEnviar = getFacade().getEmail().consultarEmails(boletosSelecionados.get(0).getPessoaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyList(emailsEnviar)) {
                    throw new Exception(boletosSelecionados.get(0).getPessoaVO().getNome() + " não tem e-mail cadastrado.");
                }

                String[] emails = new String[emailsEnviar.size()];
                int i = 0;
                for (Object objEmail : emailsEnviar) {
                    EmailVO emailVO = (EmailVO) objEmail;
                    emails[i] = emailVO.getEmail();
                    i++;
                }

                if (boletosSelecionados.size() == 1) {
                    getFacade().getBoleto().enviarEmailBoleto(getKey(),
                            boletosSelecionados.get(0), emails, true, false);
                } else {
                    ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(conv, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(boletosSelecionados);
                    BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
                    String linkBoleto = boletosManager.getByIds(pedidos);
                    getFacade().getBoleto().enviarEmailBoletoLink(getKey(), linkBoleto, boletosSelecionados.get(0).getEmpresaVO().getCodigo(),
                            boletosSelecionados.get(0).getPessoaVO().getCodigo(), emails);
                }
                montarSucessoGrowl("E-mail enviado com sucesso, para " + Arrays.toString(emails));
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public BoletoVO getBoletoVOSelecionado() {
        return boletoVOSelecionado;
    }

    public void setBoletoVOSelecionado(BoletoVO boletoVOSelecionado) {
        this.boletoVOSelecionado = boletoVOSelecionado;
    }

    public void selecionarBoletoSincronizar(ActionEvent evt) {
        selecionarBoleto("modalSincronizarBoleto", evt);
    }

    public void selecionarBoletoCancelar(ActionEvent evt) {
        selecionarBoleto("modalCancelarBoleto", evt);
    }

    public void selecionarBoleto(String modalAbrir, ActionEvent evt) {
        try {
            limparMsg();
            setOnCompleteBoleto("");
            setBoletoVOSelecionado(null);
            setBoletoCancelarSelecionados(false);

            BoletoVO obj = (BoletoVO) JSFUtilities.getFromActionEvent("boletoDetalhe", evt);
            if (obj == null) {
                throw new Exception("Boleto não encontrado.");
            }
            setBoletoVOSelecionado(obj);
            if (!UteisValidacao.emptyString(modalAbrir)) {
                setOnCompleteBoleto("Richfaces.showModalPanel('"+modalAbrir+"')");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void sincronizarBoleto() {
        try {
            limparMsg();
            setOnCompleteBoleto("");

            if (UteisValidacao.emptyNumber(getBoletoVOSelecionado().getCodigo())) {
                throw new Exception("Boleto não encontrado.");
            }

            boolean jaCancelado = false;
            String retorno = "";

            try{
                retorno = getFacade().getBoleto().sincronizarBoleto(getBoletoVOSelecionado(), getUsuarioLogado(), "SincronizarBoleto - Tela Cliente");
            } catch (Exception e){
                if (e.getMessage().contains("Não foi encontrado boleto com o Id Único")){
                    jaCancelado = true;
                } else {
                    throw e;
                }
            }

            atualizarBoletoNaLista(getBoletoVOSelecionado());

            if (jaCancelado){
                retorno = "O Boleto foi atualizado para a situação CANCELADO";
            }

            montarSucessoGrowl(retorno);
            setOnCompleteBoleto("Richfaces.hideModalPanel('modalSincronizarBoleto')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void atualizarBoletoNaLista(BoletoVO boletoVONova) {
        try {
            boletoVONova = getFacade().getBoleto().consultarChavePrimariaTelaCliente(boletoVONova.getCodigo());
        } catch (Exception ignored) {
        }

        BoletoVO boletoVOAnterior = null;
        for (BoletoVO obj : getListaBoletoGeralVo()) {
            if (boletoVONova.getCodigo().equals(obj.getCodigo())) {
                boletoVOAnterior = obj;
                break;
            }
        }

        if (boletoVOAnterior != null) {
            int posicao = getListaBoletoGeralVo().indexOf(boletoVOAnterior);
            getListaBoletoGeralVo().remove(boletoVOAnterior);
            getListaBoletoGeralVo().add(posicao, boletoVONova);
        }
    }

    public String getMensagemCancelarBoleto() {
        if (getBoletoVOSelecionado() != null &&
                getBoletoVOSelecionado().getTipo().equals(TipoBoletoEnum.ITAU)) {
            return "O Banco Itaú não disponibiliza o cancelamento online. Será alterado a situação do boleto para CANCELADO porém no banco o boleto não sofrerá alterações." +
                    " Caso o aluno pague, o sistema irá dar baixa na parcela se ela estiver em aberto ou gerar saldo na Conta Corrente se ela não estiver disponível.";
        } else if (isBoletoCancelarSelecionados()) {
            return "Será cancelado todos os boletos selecionados e que podem ser cancelados. " +
                    "Esta operação não poderá ser desfeita!";
        } else {
            return "Ao cancelar este boleto, mesmo que o aluno já o tenha impresso, não será mais possível realizar o pagamento do mesmo. " +
                    "Esta operação não poderá ser desfeita!";
        }
    }

    public void cancelarBoleto() {
        try {
            limparMsg();
            setOnCompleteBoleto("");

            if (UteisValidacao.emptyNumber(getBoletoVOSelecionado().getCodigo())) {
                throw new Exception("Boleto não encontrado.");
            }

            getFacade().getBoleto().cancelarBoleto(getBoletoVOSelecionado(), getUsuarioLogado(), "CancelarBoleto - Tela Cliente");
            atualizarBoletoNaLista(getBoletoVOSelecionado());

            montarSucessoGrowl("Boleto cancelado");
            setOnCompleteBoleto("Richfaces.hideModalPanel('modalCancelarBoleto')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void sincronizarTodosBoletos() {
        try {
            limparMsg();
            setOnCompleteBoleto("");

            List<BoletoVO> lista = new ArrayList<>(getListaBoletoGeralVo());
            for (BoletoVO obj : lista) {
                try {
                    if (obj.isPodeSincronizar()) {
                        getFacade().getBoleto().sincronizarBoleto(obj, getUsuarioLogado(), "sincronizarBoleto - SincronizarTodos");
                        atualizarBoletoNaLista(obj);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            montarSucessoGrowl("Boletos sincronizados.");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public boolean isMarcarTodosBoleto() {
        return marcarTodosBoleto;
    }

    public void setMarcarTodosBoleto(boolean marcarTodosBoleto) {
        this.marcarTodosBoleto = marcarTodosBoleto;
    }

    public void acaoMarcarTodosBoleto() {
        for (BoletoVO boletoVO : getListaBoletoGeralVo()) {
            boletoVO.setSelecionado(isMarcarTodosBoleto());
        }
    }

    public boolean isApresentarCancelarBoletos() {
        for (BoletoVO boletoVO : getListaBoletoGeralVo()) {
            if (boletoVO.isSelecionado() &&
                    boletoVO.isPodeCancelar()) {
                return true;
            }
        }
        return false;
    }

    public boolean isApresentarImprimirBoletos() {
        Set<Integer> convenioCobranca = new HashSet<>();
        for (BoletoVO boletoVO : getListaBoletoGeralVo()) {
            if (boletoVO.isSelecionado() && !boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                return false;
            }
            if (boletoVO.isSelecionado() && boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                convenioCobranca.add(boletoVO.getConvenioCobrancaVO().getCodigo());
            }
        }
        return (convenioCobranca.size() == 1);
    }

    public void imprimirBoletosSelecionados() {
        try {
            limparMsg();
            setOnCompleteBoleto("");

            List<BoletoVO> boletosSelecionados = new ArrayList<>();
            for (BoletoVO boletoVO : getListaBoletoGeralVo()) {
                if (boletoVO.isSelecionado() && boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                    boletosSelecionados.add(boletoVO);
                }
            }

            if (UteisValidacao.emptyList(boletosSelecionados)) {
                throw new Exception("Nenhum boleto selecionado");
            }

            ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(boletosSelecionados.get(0).getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(boletosSelecionados);
            BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
            String linkBoleto = boletosManager.getByIds(pedidos);
            setOnCompleteBoleto("window.open('" + linkBoleto + "', '_blank');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public boolean isContratoVigenteMudancaPlano() {
        return contratoVigenteMudancaPlano;
    }

    public void setContratoVigenteMudancaPlano(boolean contratoVigenteMudancaPlano) {
        this.contratoVigenteMudancaPlano = contratoVigenteMudancaPlano;
    }

    public boolean isContratoRenovadoMudancaPlano() {
        return contratoRenovadoMudancaPlano;
    }

    public void setContratoRenovadoMudancaPlano(boolean contratoRenovadoMudancaPlano) {
        this.contratoRenovadoMudancaPlano = contratoRenovadoMudancaPlano;
    }

    public void imprimirNotaSesc() {
        try {
            String pathRelatorio = this.getServletContext().getRealPath("relatorio");
            String  nomeArquivo = String.format("%s-%s-%s.pdf", new Object[]{
                    "notaSesi",
                    getKey(),
                    new Date().getTime()
            });
            setOnCompleteNota("");
            setNotasFiscaisTOSelecionada(new NotasFiscaisTO());
            NotasFiscaisTO nota = (NotasFiscaisTO) context().getExternalContext().getRequestMap().get("nota");
            if (nota != null) {
                String notaBase64 = getFacade().getZWFacade().startThreadIntegracaoFiescImprimirNota(0,nota.getCodNotaFiscal());
                File file = new File(pathRelatorio + File.separator +  nomeArquivo);
                try (FileOutputStream fos = new FileOutputStream(file); ) {
                    // To be short I use a corrupted PDF string, so make sure to use a valid one if you want to preview the PDF file
                    byte[] decoder = Base64.getDecoder().decode(notaBase64);

                    fos.write(decoder);
                    fileNameNotaSesi = nomeArquivo;
                }
            }
        } catch (Exception ignored) {
        }
    }

    public String getFileNameNotaSesi() {
        return fileNameNotaSesi;
    }

    public void setFileNameNotaSesi(String fileNameNotaSesi) {
        this.fileNameNotaSesi = fileNameNotaSesi;
    }

    public boolean isBoletoCancelarSelecionados() {
        return boletoCancelarSelecionados;
    }

    public void setBoletoCancelarSelecionados(boolean boletoCancelarSelecionados) {
        this.boletoCancelarSelecionados = boletoCancelarSelecionados;
    }

    public void prepararCancelarBoletosSelecionados() {
        try {
            limparMsg();
            setOnCompleteBoleto("");
            setBoletoVOSelecionado(null);
            setBoletoCancelarSelecionados(true);
            setOnCompleteBoleto("Richfaces.showModalPanel('modalCancelarBoleto')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void acaoCancelarBoletosSelecionados() {
        try {
            limparMsg();
            setOnCompleteBoleto("");

            List<BoletoVO> lista = new ArrayList<>(getListaBoletoGeralVo());
            Integer qtdCancelado = 0;
            for (BoletoVO obj : lista) {
                try {
                    if (obj.isSelecionado() &&
                            obj.isPodeCancelar()) {
                        getFacade().getBoleto().cancelarBoleto(obj, getUsuarioLogado(), "CancelarBoletosSelecionados - Tela Cliente");
                        qtdCancelado++;
                        atualizarBoletoNaLista(obj);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyNumber(qtdCancelado)) {
                throw new Exception("Nenhum boleto cancelado.");
            }
            montarSucessoGrowl("Foi cancelado " + qtdCancelado + " boletos.");
            setOnCompleteBoleto("Richfaces.hideModalPanel('modalCancelarBoleto')");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private boolean isContratoTransferido() {
        return !UteisValidacao.emptyNumber(contratoSelecionado.getPessoaOriginal().getCodigo());
    }

    private boolean isPessoaDonaContrato() {
        return UteisValidacao.emptyNumber(contratoSelecionado.getPessoaOriginal().getCodigo()) ||
                getClienteVO().getPessoa().getCodigo().equals(contratoSelecionado.getPessoaOriginal().getCodigo());
    }

    public Integer getMarcacoesFuturas() {
        return marcacoesFuturas;
    }

    public Integer getSaldoCreditosProcessado() {
        return saldoCreditosProcessado;
    }

    public String getHintSPC() {
        return hintSPC;
    }

    public void setHintSPC(String hintSPC) {
        this.hintSPC = hintSPC;
    }

    public boolean isPossuiPermissaoVerCacBiLtv() {
        return possuiPermissaoVerCacBiLtv;
    }

    public void setPossuiPermissaoVerCacBiLtv(boolean possuiPermissaoVerCacBiLtv) {
        this.possuiPermissaoVerCacBiLtv = possuiPermissaoVerCacBiLtv;
    }

    public String getDataMatriculaPorExtenso() {
        if (dataMatriculaPorExtenso == null) {
            dataMatriculaPorExtenso = "";
        }
        return dataMatriculaPorExtenso;
    }

    public void setDataMatriculaPorExtenso(String dataMatriculaPorExtenso) {
        this.dataMatriculaPorExtenso = dataMatriculaPorExtenso;
    }

    public List<MovContaVO> getListaMovContaVo() {
        if (listaMovContaVo == null) {
            listaMovContaVo = new ArrayList<>();
        }
        return listaMovContaVo;
    }

    public void setListaMovContaVo(List<MovContaVO> listaMovContaVo) {
        this.listaMovContaVo = listaMovContaVo;
    }

    public ListaPaginadaTO getListaMovConta() {
        return listaMovConta;
    }

    public void setListaMovConta(ListaPaginadaTO listaMovConta) {
        this.listaMovConta = listaMovConta;
    }

    public CobrancaAsaasRetornoDTO getCobrancaAsaasRetornoDTODetalhar() {
        if (cobrancaAsaasRetornoDTODetalhar == null) {
            return new CobrancaAsaasRetornoDTO();
        }
        return cobrancaAsaasRetornoDTODetalhar;
    }

    public void setCobrancaAsaasRetornoDTODetalhar(CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTODetalhar) {
        this.cobrancaAsaasRetornoDTODetalhar = cobrancaAsaasRetornoDTODetalhar;
    }

    public boolean isExibirModalParametros() {
        return exibirModalParametros;
    }

    public void setExibirModalParametros(boolean exibirModalParametros) {
        this.exibirModalParametros = exibirModalParametros;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public void selecionarProdutoComValidade(ActionEvent evt) throws Exception {
        setMsgAlert("");
        setMovProdutoVOValidade((MovProdutoVO) evt.getComponent().getAttributes().get("movProdutoVO"));
    }

    public void confirmarAlterarMovprodutoRenovarAutomaticamente() throws Exception {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        limparMsg();
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                alterarPermiteRenovacaoAutomaticaProduto(auto.getUsuario());
                montarSucessoGrowl("Renovação automática do produto alterada com sucesso!");
                auto.setRenderComponents("panelAutorizacaoFuncionalidade,conteinerProdutoValidade");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada(e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("2.83 - Alterar renovação automática do produto.", "adicionarRemoverProdutoRenovacaoAutomatica",
                "Alterar renovação automática do produto",
                "panelAutorizacaoFuncionalidade", listener);

    }

    public void alterarPermiteRenovacaoAutomaticaProduto(UsuarioVO usuario) throws Exception {
        getFacade().getMovProduto().alterarPermiteRenovacaoAutomaticaMovProduto(getMovProdutoVOValidade(), !getMovProdutoVOValidade().getRenovavelAutomaticamente(), usuario);
        getMovProdutoVOValidade().setRenovavelAutomaticamente(!getMovProdutoVOValidade().getRenovavelAutomaticamente());
    }

    public MovProdutoVO getMovProdutoVOValidade() {
        return movProdutoVOValidade;
    }

    public void setMovProdutoVOValidade(MovProdutoVO movProdutoVOValidade) {
        this.movProdutoVOValidade = movProdutoVOValidade;
    }

    public boolean isExibirEstornoContrato() {
        return exibirEstornoContrato;
    }

    public void setExibirEstornoContrato(boolean exibirEstornoContrato) {
        this.exibirEstornoContrato = exibirEstornoContrato;
    }

    public String getDataMatriculaPorExtensoAteFimContato() {
        if(dataMatriculaPorExtensoAteFimContato == null) {
            dataMatriculaPorExtensoAteFimContato = "";
        }
        return dataMatriculaPorExtensoAteFimContato;
    }

    public void setDataMatriculaPorExtensoAteFimContato(String dataMatriculaPorExtensoAteFimContato) {
        this.dataMatriculaPorExtensoAteFimContato = dataMatriculaPorExtensoAteFimContato;
    }

    public void selecionarPixSincronizar(ActionEvent evt) {
        try {
            limparMsg();
            PixVO pixVOTela = (PixVO) JSFUtilities.getFromActionEvent("pix", evt);

            if (pixVOTela == null || UteisValidacao.emptyNumber(pixVOTela.getCodigo())) {
                throw new Exception("Não foi possível obter o pix para tentar sincronizar, entre em contato com a Pacto");
            }

            //montar objeto completo agora, com base no anterior
            PixVO pixVO = getFacade().getPix().consultarPorCodigo(pixVOTela.getCodigo(), true);

            Connection con;
            PixPagamentoService pixPagamentoService;
            try {
                con = Conexao.getFromSession();
                pixPagamentoService = new PixPagamentoService(con);

                //PROCESSAR O PIX
                PixVO pixVOProcessado = pixPagamentoService.processarPixControlandoTransacao(pixVO);

                //Se mudou o status, então notificar
                if (!pixVOProcessado.getStatusAnterior().equals(pixVOProcessado.getStatus())) {
                    //alterar objeto da tela
                    for (PixVO pix : getListaPixVo()) {
                        if (pix.getCodigo().equals(pixVO.getCodigo())) {
                            pix.setStatus(pixVO.getStatus());
                            pix.setReciboPagamento(pixVO.getReciboPagamento());
                            break;
                        }
                    }
                    String statusApresentar = pixVOProcessado.getStatus().equals(PixStatusEnum.CONCLUIDA.getDescricao()) ? "PAGO" : pixVOProcessado.getStatus();
                    montarSucessoGrowl("Pix teve seu status alterado para: " + statusApresentar + "!");
                } else {
                    montarSucessoGrowl("Não houve alterações no status do pix!");
                }
            } catch (Exception ex) {
                montarErro(ex.getMessage());
            } finally {
                con = null;
                pixPagamentoService = null;
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isNovaNegociacaoPadrao() {
        return novaNegociacaoPadrao;
    }

    public void setNovaNegociacaoPadrao(boolean novaNegociacaoPadrao) {
        this.novaNegociacaoPadrao = novaNegociacaoPadrao;
    }

    public boolean isNovaTelaAlunoPadrao() {
        return novaTelaAlunoPadrao;
    }

    public void setNovaTelaAlunoPadrao(boolean novaTelaAlunoPadrao) {
        this.novaTelaAlunoPadrao = novaTelaAlunoPadrao;
    }

    public void abrirNovaTelaClientePadrao() {
        abrirNovaTelaCliente(true);
    }

    public void abrirNovaTelaClienteExperimente() {
        abrirNovaTelaCliente(false);
    }

    public void verificarAbrirNovaTelaCliente() {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            if (this.isNovaTelaAlunoPadrao() &&
                    loginControle != null &&
                    loginControle.isApresentarModuloNovoTreino()) {
                this.abrirNovaTelaClientePadrao();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void abrirNovaTelaCliente(boolean padrao) {
        try {
            limparMsg();
            setMsgAlert("");
            if (padrao) {
                getFacade().getUsuario().gravarRecurso(TipoInfoMigracaoEnum.TELA_ALUNO, getUsuarioLogado().getCodigo(), "true", getUsuarioLogado());
            }
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String openWindow = "window.open('" + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_TREINO.getSiglaModulo()) + "&redirect=/pessoas/perfil-v2/"+this.getClienteVO().getCodigoMatricula()+"/contratos', '_self')";
            if (padrao) {
                notificarRecursoEmpresa(RecursoSistema.PADRAO_NOVA_TELA_ALUNO);
            } else {
                notificarRecursoEmpresa(RecursoSistema.EXPERIMENTE_TELA_ALUNO);
            }
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void abrirNovNegociacao() {
        try {
            limparMsg();
            setMsgAlert("");
            getFacade().getUsuario().gravarRecurso(TipoInfoMigracaoEnum.NEGOCIACAO, getUsuarioLogado().getCodigo(), "true", getUsuarioLogado());
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (getEmpresaLogado().isUtilizaGestaoClientesComRestricoes() && clienteControle.clientePossuiRestricao()) {
                throw new Exception(clienteControle.getMensagemClienteRestricao());
            }
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String matricula = String.valueOf(((this.clienteVO != null && !UteisValidacao.emptyNumber(this.clienteVO.getCodigoMatricula())) ? this.clienteVO.getCodigoMatricula() :
                    clienteControle.getClienteVO().getCodigoMatricula()));
            String openWindow = "window.open('"
                    + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_ZW.getSiglaModulo())
                    + "&redirect=/adm/negociacao/contrato/"+matricula+"', '_self');";
            notificarRecursoEmpresa(RecursoSistema.NEGOCIACAO);
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public boolean isHabilitarRecursoPadraoTelaCliente() {
        return Uteis.isHabilitarRecursoPadraoTelaCliente();
    }

    public boolean isNovaTelaVendaAvulsa() {
        return novaTelaVendaAvulsa;
    }

    public void setNovaTelaVendaAvulsa(boolean novaTelaVendaAvulsa) {
        this.novaTelaVendaAvulsa = novaTelaVendaAvulsa;
    }

    public void abrirNovoVendaAvulsa() {
        try {
            limparMsg();
            setMsgAlert("");
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            String matricula = String.valueOf(((this.clienteVO != null && !UteisValidacao.emptyNumber(this.clienteVO.getCodigoMatricula())) ? this.clienteVO.getCodigoMatricula() :
                    clienteControle.getClienteVO().getCodigoMatricula()));
            String openWindow = "window.open('"
                    + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_ZW.getSiglaModulo())
                    + "&redirect=/adm/venda-avulsa/" + matricula + "', '_self')";
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void incluirLogClienteObservacao(Connection con, ClienteObservacaoVO clienteObservacaoAnterior, ClienteObservacaoVO clienteObservacaoAlterado, boolean isExcluir) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setChavePrimaria(clienteObservacaoAlterado.getCodigo().toString());
            log.setNomeEntidade("OBSERVAÇÃO CLIENTE");
            log.setNomeEntidadeDescricao("Observação - Cliente");
            log.setValorCampoAnterior(gerarLogObservacao(clienteObservacaoAnterior));
            log.setValorCampoAlterado(gerarLogObservacao(clienteObservacaoAlterado));
            if (isExcluir) {
                log.setOperacao("EXCLUSÃO OBSERVAÇÃO");
            } else if(clienteObservacaoAnterior == null) {
                log.setOperacao("INCLUSÃO OBSERVAÇÃO");
            } else {
                log.setOperacao("ALTERAÇÃO OBSERVAÇÃO");
                log.setValorCampoAnterior(clienteObservacaoAnterior.getObservacao());
                log.setValorCampoAlterado(clienteObservacaoAlterado.getObservacao());
            }
            log.setNomeCampo("Campo(s)");
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao(getUsuarioLogado().getUsername());
            log.setChavePrimariaEntidadeSubordinada("");
            PessoaVO pessoa = clienteObservacaoAnterior != null ? clienteObservacaoAnterior.getClienteVO().getPessoa() : clienteObservacaoAlterado.getClienteVO().getPessoa();
            log.setPessoa(pessoa.getCodigo());
            if (isExcluir || !log.getValorCampoAlterado().equals(log.getValorCampoAnterior())) {
                Log logDao = new Log(con);
                logDao.incluirSemCommit(log);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String gerarLogObservacao(ClienteObservacaoVO clienteObservacao) {
        if (clienteObservacao == null) {
            return "";
        }
        String valores = "codigo=" + clienteObservacao.getCodigo() + "\n";
        valores += "Observação=" + clienteObservacao.getObservacao() + "\n";
        SimpleDateFormat smd = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        valores += "Data Cadastro=" + smd.format(clienteObservacao.getDataCadastro()) + "\n";
        return valores;
    }

    public boolean isNovaTelaAlunoPadraoEmpresa() {
        return novaTelaAlunoPadraoEmpresa;
    }

    public void setNovaTelaAlunoPadraoEmpresa(boolean novaTelaAlunoPadraoEmpresa) {
        this.novaTelaAlunoPadraoEmpresa = novaTelaAlunoPadraoEmpresa;
    }

    public boolean isNovaVendaAvulsaPadraoEmpresa() {
        return novaVendaAvulsaPadraoEmpresa;
    }

    public void setNovaVendaAvulsaPadraoEmpresa(boolean novaVendaAvulsaPadraoEmpresa) {
        this.novaVendaAvulsaPadraoEmpresa = novaVendaAvulsaPadraoEmpresa;
    }

    public List<CarteirinhaClienteVO> getListaCarteirinhas() {
        return listaCarteirinhas;
    }

    public void setListaCarteirinhas(List<CarteirinhaClienteVO> listaCarteirinhas) {
        this.listaCarteirinhas = listaCarteirinhas;
    }

    public void abrirHistoricoCarteirinha() {
        try {
            setListaCarteirinhas(new ArrayList<>());
            setListaCarteirinhas(getFacade().getCarteirinhaCliente().consultarCarteirinhas(getClienteVO(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            montarErro(e);
        }
    }
}
