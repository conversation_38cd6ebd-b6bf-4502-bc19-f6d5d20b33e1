package controle.basico;

import br.com.pactosolucoes.ce.controle.AgendaVisitaControle;
import br.com.pactosolucoes.ce.controle.CadastroInicialControle;
import br.com.pactosolucoes.ce.controle.ConversaControle;
import br.com.pactosolucoes.ce.controle.FornecedorControle;
import br.com.pactosolucoes.ce.controle.OrcamentoDetalhadoControle;
import br.com.pactosolucoes.ce.controle.basico.NavegacaoControle;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.estudio.controle.AgendaAmbienteColaboradorControle;
import br.com.pactosolucoes.estudio.controle.AgendaIndividualControle;
import br.com.pactosolucoes.estudio.controle.AgendaMensalControle;
import br.com.pactosolucoes.estudio.controle.ConfiguracaoEstudioControle;
import br.com.pactosolucoes.estudio.controle.DisponibilidadeControle;
import br.com.pactosolucoes.estudio.controle.PacoteControle;
import br.com.pactosolucoes.estudio.controle.RelatorioClientesInativosControle;
import br.com.pactosolucoes.estudio.controle.RelatorioComissaoControle;
import br.com.pactosolucoes.estudio.controle.RelatorioFechamentoDiarioControle;
import br.com.pactosolucoes.estudio.controle.RelatorioGeralAgendamentosControle;
import controle.acesso.AutorizacaoAcessoGrupoEmpresarialControle;
import controle.acesso.IntegracaoAcessoGrupoEmpresarialControle;
import controle.arquitetura.FuncionalidadeClienteEnum;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.FuncionalidadeSistemaEnumAux;
import controle.arquitetura.GrupoFuncionalidadeSistemaEnum;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.UsuarioControle;
import controle.clubevantagens.ClubeVantagensControle;
import controle.clubevantagens.ItemCampanhaControle;
import controle.contrato.AtestadoControle;
import controle.crm.AgendaControle;
import controle.crm.BusinessIntelligenceCRMControle;
import controle.crm.ConfiguracaoSistemaCRMControle;
import controle.crm.HistoricoContatoControle;
import controle.crm.IndicacaoControle;
import controle.crm.MalaDiretaControle;
import controle.crm.MensagemBuilderControle;
import controle.crm.MetaCRMControle;
import controle.crm.PassivoControle;
import controle.estoque.BalancoControle;
import controle.estoque.CardexControle;
import controle.estoque.CompraControle;
import controle.estoque.ProdutoEstoqueControle;
import controle.estoque.RelatorioEstoqueProdutoControle;
import controle.financeiro.AulaAvulsaDiariaControle;
import controle.financeiro.BIFinanceiroControle;
import controle.financeiro.CaixaControle;
import controle.financeiro.CentroCustosControle;
import controle.financeiro.ConfiguracaoFinanceiroControle;
import controle.financeiro.ContaContabilControle;
import controle.financeiro.ContaControle;
import controle.financeiro.ConvenioCobrancaControle;
import controle.financeiro.DREControle;
import controle.financeiro.DemonstrativoFinanceiroControle;
import controle.financeiro.FechamentoCaixaPlanoContaControle;
import controle.financeiro.FluxoCaixaControle;
import controle.financeiro.FreePassControle;
import controle.financeiro.GerenciadorContaControle;
import controle.financeiro.GestaoLotesControle;
import controle.financeiro.GestaoNFCeControle;
import controle.financeiro.GestaoNotasControle;
import controle.financeiro.GestaoPersonalControle;
import controle.financeiro.GestaoRecebiveisControle;
import controle.financeiro.MetaFinanceiroControle;
import controle.financeiro.MovContaControle;
import controle.financeiro.MovParcelaControle;
import controle.financeiro.PinPadControle;
import controle.financeiro.PlanoContasControle;
import controle.financeiro.RateioIntegracaoControle;
import controle.financeiro.RecebivelAvulsoControle;
import controle.financeiro.ReciboControle;
import controle.financeiro.RelatorioDevolucaoChequeControle;
import controle.financeiro.RelatorioMovimentacaoFinanceiraControle;
import controle.financeiro.RelatorioOrcamentarioControle;
import controle.financeiro.RelatorioRepasseControle;
import controle.financeiro.TipoContaControle;
import controle.financeiro.TipoDocumentoControle;
import controle.financeiro.VendaAvulsaControle;
import controle.plano.ConsultarTurmaControle;
import controle.plano.GestaoTurmaControle;
import controle.plano.OrcamentoControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.enumerador.ClienteFuncionalidadeTO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UrlRedirectNZWEnum;
import negocio.comuns.utilitarias.UrlRedirectPAYEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.basico.BIControle;
import relatorio.controle.basico.ComissaoControle;
import relatorio.controle.basico.DescontoOcupacaoControleRel;
import relatorio.controle.basico.FechamentoAcessosControleRel;
import relatorio.controle.basico.FrequenciaOcupacaoControleRel;
import relatorio.controle.basico.HistoricoPontosParceiroFidelidadeControle;
import relatorio.controle.basico.InclusaoVendaRapidaControle;
import relatorio.controle.basico.ListaAcessoControleRel;
import relatorio.controle.basico.RelatorioBVsControle;
import relatorio.controle.basico.RelatorioClientesControle;
import relatorio.controle.financeiro.CaixaPorOperadorRelControleRel;
import relatorio.controle.financeiro.CompetenciaSinteticoControleRel;
import relatorio.controle.financeiro.FaturamentoSinteticoControleRel;
import relatorio.controle.financeiro.ParcelaEmAbertoControleRel;
import relatorio.controle.financeiro.ReceitaPorPeriodoSinteticoRelControleRel;
import relatorio.controle.financeiro.SaldoContaCorrenteControleRel;
import relatorio.controle.sad.BIConviteAulaExperimentalControle;
import relatorio.controle.sad.RenovacaoSinteticoControle;
import servicos.notificacao.IdLocalicazaoMenuEnum;
import servicos.notificacao.NotificacaoMsService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.servlet.http.HttpServletRequest;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * Created by Rafael on 01/09/2015.
 */
public class FuncionalidadeControle extends SuperControle {

    ClienteFuncionalidadeTO funcSelecionada;
    HashMap<Integer, byte[]> clientesFoto = new HashMap<>();
    HashMap<Integer,String> clientesFotoKey = new HashMap<>();
    private String rotulo;
    private ClienteVO clienteEditar;
    private Boolean abrirFuncCliente = false;
    private Boolean mostrarModalContratosRenovar = false;
    private boolean podeConsultarEmTodasEmpresas = false;
    private boolean bancoMultiEmpresa = true;
    private String funcionalidadeNome;
    private Map<String, Boolean> funcHabilitadas = new HashMap<>();
    private Map<FuncionalidadeSistemaEnum, Boolean> funcionalidadesNovasLiberadas = null;
    private String funcionalidadeAberta = null;
    private String abrirPopUp = "";
    private boolean perfilAcessoUnificado = false;

    public FuncionalidadeControle() {
        try {
            this.setBancoMultiEmpresa(getFacade().getEmpresa().quantidadeEmpresas(true) > 1);
            this.setPerfilAcessoUnificado(getFacade().getPerfilAcesso().temPerfilUnificado());
        } catch (Exception ex) {
            this.setBancoMultiEmpresa(true);
            ex.printStackTrace();
        }
    }

    public void setarFuncionalidade() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        this.funcionalidadeAberta = request.getParameter("funcionalidadeAberta");
    }


    //Caso retorne nulo para navegação
    //A aplicação irá executar 'oncomplete' com o valor url do Enumerator
    public String preparaFuncionalidade() throws Exception {
        setarFuncionalidade();
        try {
            if (funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum() != null) {
                LoginControle loginControle = JSFUtilities.getManagedBean(LoginControle.class);
                notificarRecursoEmpresa(RecursoSistema.fromDescricao(funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().toString()));
                if(funcSelecionada.getIdLocalicazaoMenuEnum() == null){
                    Uteis.logarDebug("Não foi possível identificar a origem de clique da funcionalidade. O registro de acesso ao recurso não foi realizado.");
                }else{
                    NotificacaoMsService.notificarAcessoMenuRecurso(funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum(), funcSelecionada.getIdLocalicazaoMenuEnum());
                }
                if(!funcSelecionada.getFuncionalidadeSistemaEnumAux().getModulo().isZwUi() ){
                    loginControle.setModuloAberto(funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().getModulo());
                }
                MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
                switch (funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum()) {
                    case ABRIR_CAIXA:
                        CaixaControle caixaca = JSFUtilities.getManagedBean(CaixaControle.class);
                        caixaca.abrirCaixa();
                        break;
                    case FECHAR_CAIXA:
                        CaixaControle caixacf = JSFUtilities.getManagedBean(CaixaControle.class);
                        List<CaixaVO> caixasEmAberto = CaixaControle.getFacade().getFinanceiro().getCaixa().consultarCaixasEmAberto(getUsuarioLogado().getCodigo(),
                                getEmpresaLogado().getCodigo(),Uteis.NIVELMONTARDADOS_TODOS);
                        if (caixasEmAberto.size() == 1) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl("abrirPopup('" + request().getContextPath() + "/faces/pages/finan/telaFechamentoCaixa.jsp', 'FechamentoCaixa', 800, 530);Richfaces.hideModalPanel('modalFecharCaixas');");
                        }
                        caixacf.fecharCaixa();
                        break;
                    case CONSULTAR_CAIXA:
                        CaixaControle caixac = JSFUtilities.getManagedBean(CaixaControle.class);
                        caixac.abrirModalHistoricoCaixa();
                        return caixac.getAbrirModalConsultarCaixa();
                    case CAIXA_EM_ABERTO:
                        MovParcelaControle mpc = JSFUtilities.getManagedBean(MovParcelaControle.class);
                        return mpc.novo();
                    case ATUALIZAR_BV:
                        TelaClienteControle ts2 = JSFUtilities.getManagedBean(TelaClienteControle.class);
                        return ts2.abrirQuestionarioBV();
                    case VENDA_AVULSA:
                        VendaAvulsaControle vac = JSFUtilities.getManagedBean(VendaAvulsaControle.class);
                        return vac.novoSemComprador();
                    case DIARIA:
                        AulaAvulsaDiariaControle aac = JSFUtilities.getManagedBean(AulaAvulsaDiariaControle.class);
                        return aac.novoDiaria();
                    case FREE_PASS:
                        FreePassControle fpc = JSFUtilities.getManagedBean(FreePassControle.class);
                        return fpc.novo();
                    case ORCAMENTO:
                        OrcamentoControle orc = JSFUtilities.getManagedBean(OrcamentoControle.class);
                        return orc.abrirRealizarOrcamento();
                    case GESTAO_DE_TURMA:
                        GestaoTurmaControle gtc = JSFUtilities.getManagedBean(GestaoTurmaControle.class);
                        return gtc.abrirGestaoTurma();
                    case GESTAO_PERSONAL:
                        GestaoPersonalControle gpc = JSFUtilities.getManagedBean(GestaoPersonalControle.class);
                        return gpc.novo();
                    case RELATORIO_DE_PERSONAL:
                        GestaoPersonalControle gpcR = JSFUtilities.getManagedBean(GestaoPersonalControle.class);
                        return gpcR.novoRelatorio();
                    case GESTAO_DE_TRANSACOES:
                        NavegacaoControle nc = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return nc.abrirGestaoTransacoes();
                    case GESTAO_DE_REMESSAS:
                        NavegacaoControle gr = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return gr.abrirGestaoRemessas();
                    case GESTAO_DE_COMISSAO:
                        NavegacaoControle gestaoComissao = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return gestaoComissao.abrirGestaoComissao();
                    case RELATORIO_FREQUENCIA_TURMAS:
                        ConsultarTurmaControle consultaTurmasFrequencia = JSFUtilities.getManagedBean(ConsultarTurmaControle.class);
                        consultaTurmasFrequencia.setExibeRelatorioFrequenciaTurmas(true);
                        consultaTurmasFrequencia.setMostrarEmpresa(true);
                        consultaTurmasFrequencia.novo();
                        break;
                    case CONSULTA_DE_TURMAS:
                        ConsultarTurmaControle consultaTurmas = JSFUtilities.getManagedBean(ConsultarTurmaControle.class);
                        consultaTurmas.novo();
                        break;
                    case MAPA_TURMAS:
                        ConsultarTurmaControle mapaTurmas = JSFUtilities.getManagedBean(ConsultarTurmaControle.class);
                        mapaTurmas.novo();
                        break;
    //                case GESTAO_FAMILIA:
    //                    BIFamiliaControle bifamilia = JSFUtilities.getManagedBean(BIFamiliaControle.class);
    //                    bifamilia.abrirSugestoes();
    //                    break;
                    case COMISSAO_VARIAVEL:
                        ComissaoControle comissaoVariavel = JSFUtilities.getManagedBean(ComissaoControle.class);
                        comissaoVariavel.novo();
                        break;
                    case CONTROLE_LOG:
                        AutorizacaoAcessoGrupoEmpresarialControle controleLog = JSFUtilities.getManagedBean(AutorizacaoAcessoGrupoEmpresarialControle.class);
                        controleLog.abrirPopup();
                        break;
                    case BALANCO:
                        BalancoControle balancoControle = JSFUtilities.getManagedBean(BalancoControle.class);
                        balancoControle.abrirTelaBalanco();
                        break;
                    case COMPRA:
                        CompraControle compraControle = JSFUtilities.getManagedBean(CompraControle.class);
                        compraControle.abrirTelaCompra();
                        break;
                    case CARDEX:
                        CardexControle cardexControle = JSFUtilities.getManagedBean(CardexControle.class);
                        return cardexControle.abrirTelaCardex();
                    case CONFIGURAR_PRODUTO_ESTOQUE:
                        ProdutoEstoqueControle prodEstoque = JSFUtilities.getManagedBean(ProdutoEstoqueControle.class);
                        prodEstoque.abrirTelaProdutoEstoque();
                        break;
                    case POSICAO_DO_ESTOQUE:
                        RelatorioEstoqueProdutoControle posEstoque = JSFUtilities.getManagedBean(RelatorioEstoqueProdutoControle.class);
                        return posEstoque.abrirTelaPosicaoEstoque();
                    case METAS_FINANCEIRO_VENDA:
                        MetaFinanceiroControle metaFinan = JSFUtilities.getManagedBean(MetaFinanceiroControle.class);
                        metaFinan.abrirTelaMetas();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(metaFinan.getMsgAlert());
                        break;
                    case INTEGRACAO_ACESSO:
                        IntegracaoAcessoGrupoEmpresarialControle integracaoAcesso = JSFUtilities.getManagedBean(IntegracaoAcessoGrupoEmpresarialControle.class);
                        integracaoAcesso.abrirPopup();
                        break;
                    case AUTORIZACAO_ACESSO:
                        AutorizacaoAcessoGrupoEmpresarialControle autorizacaoAcesso = JSFUtilities.getManagedBean(AutorizacaoAcessoGrupoEmpresarialControle.class);
                        autorizacaoAcesso.abrirPopup();
                        break;
                    case IMPRIME_RECIBO_BANCO:
                        ReciboControle reciboControle = JSFUtilities.getManagedBean(ReciboControle.class);
                        reciboControle.escolherAcao();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(reciboControle.getIrPara());
                        break;
                    case GERAL_CLIENTES:
                        ListasERelatoriosControle geralCliente = JSFUtilities.getManagedBean(ListasERelatoriosControle.class);
                        return geralCliente.carregarFiltros();
                    case LISTA_ACESSOS:
                        ListaAcessoControleRel listaAcesso = JSFUtilities.getManagedBean(ListaAcessoControleRel.class);
                        listaAcesso.abrirRelatorio();
                        break;
                    case FECHAMENTO_ACESSOS:
                        FechamentoAcessosControleRel fechamentoAcesso = JSFUtilities.getManagedBean(FechamentoAcessosControleRel.class);
                        fechamentoAcesso.abrirRelatorio();
                        break;
                    case LISTA_CLIENTES_SIMPLIFICADA:
                        NavegacaoControle clientesSimplificado = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return clientesSimplificado.abrirListaClientesSimplificada();
                    case FREQUENCIA_OCUPACAO_TURMAS:
                        FrequenciaOcupacaoControleRel frequenciaOcupacao = JSFUtilities.getManagedBean(FrequenciaOcupacaoControleRel.class);
                        frequenciaOcupacao.novo();
                        break;
                    case DESCONTO_OCUPACAO_TURMAS:
                        DescontoOcupacaoControleRel descontoOcupacao = JSFUtilities.getManagedBean(DescontoOcupacaoControleRel.class);
                        descontoOcupacao.novo();
                        break;
                    case FECHAMENTO_CAIXA_OPERADOR:
                        CaixaPorOperadorRelControleRel fechamentoCaixaOperador = JSFUtilities.getManagedBean(CaixaPorOperadorRelControleRel.class);
                        fechamentoCaixaOperador.novo();
                        break;
                    case COMPETENCIA_MENSAL:
                        CompetenciaSinteticoControleRel competenciaMensal = JSFUtilities.getManagedBean(CompetenciaSinteticoControleRel.class);
                        competenciaMensal.novo();
                        break;
                    case CONVITES_AULAS:
                        BIConviteAulaExperimentalControle biconvite = JSFUtilities.getManagedBean(BIConviteAulaExperimentalControle.class);
                        biconvite.abrirBI();
                        break;
                    case FATURAMENTO_PERIODO:
                        FaturamentoSinteticoControleRel faturamentoPeriodo = JSFUtilities.getManagedBean(FaturamentoSinteticoControleRel.class);
                        faturamentoPeriodo.inicializarDadosFaturamento();
                        break;
                    case CRM_META_EXTA:
                        MalaDiretaControle malaDiretaControle = JSFUtilities.getManagedBean(MalaDiretaControle.class);
                        malaDiretaControle.setMensagem("");
                        break;
                    case FATURAMENTO_RECEBIDO_PERIODO:
                        FaturamentoSinteticoControleRel faturamentoRecebidoPeriodo = JSFUtilities.getManagedBean(FaturamentoSinteticoControleRel.class);
                        faturamentoRecebidoPeriodo.inicializarDadosFaturamentoRecebido();
                        break;
                    case RECEITA_PERIODO:
                        ReceitaPorPeriodoSinteticoRelControleRel receitaPeriodo = JSFUtilities.getManagedBean(ReceitaPorPeriodoSinteticoRelControleRel.class);
                        receitaPeriodo.novo();
                        break;
                    case GESTAO_NEGATIVACOES:
                    case RELATORIO_PARCELAS:
                        ParcelaEmAbertoControleRel parcelaEmAberto = JSFUtilities.getManagedBean(ParcelaEmAbertoControleRel.class);
                        parcelaEmAberto.novo();
                        break;
                    case RELATORIO_PRODUTOS:
                        ParcelaEmAbertoControleRel relatorioProduto = JSFUtilities.getManagedBean(ParcelaEmAbertoControleRel.class);
                        relatorioProduto.novo();
                        break;
                    case SALDO_CONTA_CORRENTE:
                        SaldoContaCorrenteControleRel saldoCC = JSFUtilities.getManagedBean(SaldoContaCorrenteControleRel.class);
                        saldoCC.novo();
                        break;
                    case PREVISAO_RENOVACAO_CONTRATO:
                        RenovacaoSinteticoControle previsaoRenovacao = JSFUtilities.getManagedBean(RenovacaoSinteticoControle.class);
                        previsaoRenovacao.novo();
                        break;
                    case RELATORIO_CLIENTES:
                        RelatorioClientesControle relatorioCliente = JSFUtilities.getManagedBean(RelatorioClientesControle.class);
                        relatorioCliente.inicializar();
                        break;
                    case RELATORIO_REPASSE:
                        RelatorioRepasseControle relatorioRepasse = JSFUtilities.getManagedBean(RelatorioRepasseControle.class);
                        relatorioRepasse.montarFiltros();
                        break;
                    case MODELO_MENSAGEM:
                    case MAILING:
                        MensagemBuilderControle mailing = JSFUtilities.getManagedBean(MensagemBuilderControle.class);
                        return mailing.init();
                    case GESTAO_NOTAS:
                        GestaoNotasControle gestaoNotasControle = JSFUtilities.getManagedBean(GestaoNotasControle.class);
                        gestaoNotasControle.prepararTela();
                        break;

                    case GESTAO_NFCE:
                        GestaoNFCeControle gestaoNFCeControle = JSFUtilities.getManagedBean(GestaoNFCeControle.class);
                        gestaoNFCeControle.prepararTela();
                        break;

                    case CONSULTA_HISTORICO_CONTATO:
                        HistoricoContatoControle historicoContato = JSFUtilities.getManagedBean(HistoricoContatoControle.class);
                        historicoContato.inicializarDadosRealizarContato();
                        break;
                    case PASSIVO:
                        PassivoControle passivo = JSFUtilities.getManagedBean(PassivoControle.class);
                        passivo.realizarlimpezaCamposMensagem();
                        break;
                    case INDICACAO:
                        IndicacaoControle indicacao = JSFUtilities.getManagedBean(IndicacaoControle.class);
                        indicacao.realizarlimpezaCamposMensagem();
                        break;
                    case LANCAMENTOS:
                        MovContaControle lancamento = JSFUtilities.getManagedBean(MovContaControle.class);
                        String navLancamento = lancamento.abrirTelaLancamentos();
                        if (navLancamento.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(lancamento.getMsgAlert());
                            break;
                        }
                        return navLancamento;
                    case LANCAMENTO_CONTA_RAPIDO:
                        MovContaControle lancamentoRapidoControle = JSFUtilities.getManagedBean(MovContaControle.class);
                        String navLancamentoRapido = lancamentoRapidoControle.abrirTelaLancamentoRapido();
                        if (navLancamentoRapido.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(lancamentoRapidoControle.getMsgAlert());
                            break;
                        }
                        return navLancamentoRapido;
                    case ULTIMOS_LANCAMENTOS:
                        MovContaControle ultimosLancamentos = JSFUtilities.getManagedBean(MovContaControle.class);
                        String navUltLancamento = ultimosLancamentos.abrirTelaLancamentos();
                        if (navUltLancamento.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(ultimosLancamentos.getMsgAlert());
                            break;
                        }
                        return navUltLancamento;
    //                case CONTAS_PAGAR:
                    case CONTAS_A_PAGAR:
                        MovContaControle contasPagar = JSFUtilities.getManagedBean(MovContaControle.class);
                        String navContasPagar = contasPagar.abrirTelaLancamentos();
                        if (navContasPagar.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(contasPagar.getMsgAlert());
                            break;
                        }
                        if(menuControle != null){
                            menuControle.prepararMenuExibir("FIN-INICIO");
                        }
                        return navContasPagar;
                    case NOVA_CONTA_PAGAR:
                        MovContaControle novaContasPagar = JSFUtilities.getManagedBean(MovContaControle.class);
                        String navPagar = novaContasPagar.novaContaPagarInit();
                        if (navPagar.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(novaContasPagar.getMsgAlert());
                            break;
                        }
                        return navPagar;
    //                case CONTAS_RECEBER:
                    case CONTAS_A_RECEBER:
                        MovContaControle contasReceber = JSFUtilities.getManagedBean(MovContaControle.class);
                        String navCReceber = contasReceber.abrirTelaLancamentos();
                        if (navCReceber.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(contasReceber.getMsgAlert());
                            break;
                        }
                        if(menuControle != null){
                            menuControle.prepararMenuExibir("FIN-INICIO");
                        }
                        return navCReceber;
                    case NOVA_CONTAS_RECEBER:
                        MovContaControle novaContasReceber = JSFUtilities.getManagedBean(MovContaControle.class);
                        String navContas = novaContasReceber.novaContaReceberInit();
                        if (navContas.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(novaContasReceber.getMsgAlert());
                            break;
                        }
                        return navContas;
                    case RECEBIVEIS:
                        GestaoRecebiveisControle recebiveis = JSFUtilities.getManagedBean(GestaoRecebiveisControle.class);
                        String navegacaoR = recebiveis.abrirTelaRecebiveis();
                        if (navegacaoR.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(recebiveis.getMsgAlert());
                            break;
                        }
                        return navegacaoR;
                    case LOTES:
                        GestaoLotesControle lotes = JSFUtilities.getManagedBean(GestaoLotesControle.class);
                        String navegacaoL = lotes.abrirTelaConsultaLotes();
                        if (navegacaoL.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(lotes.getMsgAlert());
                            break;
                        }
                        return navegacaoL;
                    case BLOQUEIO_CAIXA:
                        try {
                            autorizar("BloqueioCaixa", "9.35 - Adicionar data de bloqueio de caixa");
                            return "bloqueioCaixa";
                        } catch (Exception e) {
                            montarMsgAlert(e.getMessage());
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(getMsgAlert());
                            return "";
                        }
                    case CAIXA_ADIMISTRATIVO:
                        CaixaControle caixaAdm = JSFUtilities.getManagedBean(CaixaControle.class);
                        String navCaixa = caixaAdm.abrirTelaCaixaAdm();
                        if (navCaixa.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(caixaAdm.getMsgAlert());
                            break;
                        }
                        return navCaixa;
                    case RESUMO_CONTAS:
                        GerenciadorContaControle resumoContas = JSFUtilities.getManagedBean(GerenciadorContaControle.class);
                        String navResumoC = resumoContas.resumoContas();
                        if (navResumoC.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(resumoContas.getMsgAlert());
                            break;
                        }
                        return navResumoC;
                    case DEMONSTRATIVO_FINAN:
                        DemonstrativoFinanceiroControle demonstrativo = JSFUtilities.getManagedBean(DemonstrativoFinanceiroControle.class);
                        return demonstrativo.abrirDemonstrativoFinanceiro();
                    case FECHAMENTO_CAIXA_PLANO_CONTAS:
                        FechamentoCaixaPlanoContaControle fechamento = JSFUtilities.getManagedBean(FechamentoCaixaPlanoContaControle.class);
                        return fechamento.abrirDemonstrativoFinanceiro();
                    case DRE:
                        DREControle dre = JSFUtilities.getManagedBean(DREControle.class);
                        return dre.abrirDRE();
                    case FLUXO_CAIXA_FINAN:
                        FluxoCaixaControle fluxo = JSFUtilities.getManagedBean(FluxoCaixaControle.class);
                        String naFluxo =  fluxo.abrirFluxoDeCaixa();
                        if (naFluxo.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(fluxo.getMsgAlert());
                            break;
                        }
                         return naFluxo;
                    case MOVIMENTACOES_FINAN:
                        RelatorioMovimentacaoFinanceiraControle movFinan = JSFUtilities.getManagedBean(RelatorioMovimentacaoFinanceiraControle.class);
                        movFinan.abrirFechamento();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(movFinan.getMsgAlert());
                        break;
                    case RELATORIO_DEVOLUCAO_CHEQUE:
                        RelatorioDevolucaoChequeControle relDevolucao = JSFUtilities.getManagedBean(RelatorioDevolucaoChequeControle.class);
                        relDevolucao.abrirTelaRelatorioDevolucaoCheque();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(relDevolucao.getMsgAlert());
                        break;
                    case RELATORIO_ORCAMENTARIO:
                        RelatorioOrcamentarioControle relOrcamentario = JSFUtilities.getManagedBean(RelatorioOrcamentarioControle.class);
                        relOrcamentario.abrirTelaRelatorioOrcamentario();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(relOrcamentario.getMsgAlert());
                        break;
                    case GESTAO_DE_BOLETOS_ONLINE:
                        NavegacaoControle navegacaoControle = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        String urlRedirecionamento = navegacaoControle.abrirGestaoBoletosOnline();
                        return urlRedirecionamento;
                    case RATEIO_INTEGRACAO:
                        RateioIntegracaoControle rateio = JSFUtilities.getManagedBean(RateioIntegracaoControle.class);
                        String navRateio = rateio.abrirTelaRateioIntegracao();
                        if (navRateio.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(rateio.getMsgAlert());
                            break;
                        }
                        return navRateio;
                    case PLANO_CONTAS:
                        PlanoContasControle planoContas = JSFUtilities.getManagedBean(PlanoContasControle.class);
                        String navPlanoConta = planoContas.abrirPlanoConta();
                        if (navPlanoConta.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(planoContas.getMsgAlert());
                            break;
                        }
                        return navPlanoConta;
                    case CENTRO_CUSTOS:
                        CentroCustosControle centroCustos = JSFUtilities.getManagedBean(CentroCustosControle.class);
                        String navCentroCusto = centroCustos.abrirCentroCusto();
                        if (navCentroCusto.equals("")) {
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(centroCustos.getMsgAlert());
                            break;
                        }
                        return navCentroCusto;
                    case CHEQUES_CARTOES_AVULSOS:
                        RecebivelAvulsoControle chequesCartoes = (RecebivelAvulsoControle) JSFUtilities.getManagedBean("RecebivelAvulsoControle");
                        chequesCartoes.abrirPopUp();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(chequesCartoes.getMsgAlert());
                        break;
                    case FORNCEDOR:
                        FornecedorControle fornecedor = (FornecedorControle) JSFUtilities.getManagedBean("FornecedorControle");
                        fornecedor.abrirFornecedor();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(fornecedor.getMsgAlert());
                        break;
                    case CADASTRO_INICIAL_CE:
                        CadastroInicialControle cadastroInicial = (CadastroInicialControle) JSFUtilities.getManagedBean("CadastroInicialControle");
                        return cadastroInicial.abrirCadastroInicialNovo();
                    case SIMULAR_ORCAMENTO:
                        OrcamentoDetalhadoControle orcamento = (OrcamentoDetalhadoControle) JSFUtilities.getManagedBean("OrcamentoDetalhadoControle");
                        return orcamento.abrirOrcamentoFicticio();
                    case LISTA_PROSPECTS:
                        NavegacaoControle prospects = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return prospects.abrirTelaProspects();
                    case AGENDA_VISISTA:
                        AgendaVisitaControle agendaVisista = (AgendaVisitaControle) JSFUtilities.getManagedBean("AgendaVisitaControle");
                        return agendaVisista.abrirAgendaVisitaMenuLateral();
                    case CONVERSAS:
                        ConversaControle conversa = (ConversaControle) JSFUtilities.getManagedBean("ConversaControle");
                        return conversa.abrirTelaConsultaConversas();
                    case AGENDA_EVENTOS:
                        NavegacaoControle eventos = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return eventos.abrirTelaInicial();
                    case CAIXA_EM_ABERTO_CE:
                        MovParcelaControle caixaAbertoCE = (MovParcelaControle) JSFUtilities.getManagedBean("MovParcelaControle");
                        return caixaAbertoCE.abrirCE();
                    case PESQUISA_GERAL:
                        NavegacaoControle pesquisaGeral = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return pesquisaGeral.abrirPesquisaGeral();
                    case GESTAO_CREDITO:
                        NavegacaoControle gestaoCredito = JSFUtilities.getManagedBean(NavegacaoControle.class);
                        return gestaoCredito.abrirGestaoCredito();
                    case COMISSAO_EST:
                        RelatorioComissaoControle comissaoEst = (RelatorioComissaoControle) JSFUtilities.getManagedBean("RelatorioComissaoControle");
                        return comissaoEst.acaoEntrar();
                    case DIARIO:
                        RelatorioFechamentoDiarioControle diario = (RelatorioFechamentoDiarioControle) JSFUtilities.getManagedBean("RelatorioFechamentoDiarioControle");
                        return diario.acaoEntrar();
                    case CLIENTES_SEM_SESSAO:
                        RelatorioClientesInativosControle clienteSemSessao = (RelatorioClientesInativosControle) JSFUtilities.getManagedBean("RelatorioClientesInativosControle");
                        return clienteSemSessao.novo();
                    case AGENDAMENTOS:
                        RelatorioGeralAgendamentosControle agendamentos = (RelatorioGeralAgendamentosControle) JSFUtilities.getManagedBean("RelatorioGeralAgendamentosControle");
                        return agendamentos.acaoEntrar();
                    case DISPONIBILIDADE_EST:
                        DisponibilidadeControle disponibilidade = (DisponibilidadeControle) JSFUtilities.getManagedBean("disponibilidadeControle");
                        return disponibilidade.acaoEntrarELimpar();
                    case VENDA_AVULSA_EST:
                        VendaAvulsaControle vendaAvulsaEst = JSFUtilities.getManagedBean(VendaAvulsaControle.class);
                        return vendaAvulsaEst.novoSemComprador();
                    case CAIXA_ABERTO_EST:
                        MovParcelaControle caixaAbertoEst = (MovParcelaControle) JSFUtilities.getManagedBean("MovParcelaControle");
                        return caixaAbertoEst.novo();
                    case PACOTE_EST:
                        PacoteControle pacoteControle = (PacoteControle) JSFUtilities.getManagedBean("pacoteControle");
                        if (pacoteControle == null) {
                            pacoteControle = new PacoteControle();
                            JSFUtilities.storeOnSession("pacoteControle", pacoteControle);
                            pacoteControle = (PacoteControle) JSFUtilities.getManagedBean("pacoteControle");
                        }
                        return pacoteControle.acaoEntrar();
                    case AGENDA_MENSAL_EST:
                        AgendaMensalControle agendaMensalController = (AgendaMensalControle) JSFUtilities.getManagedBean("agendaMensalController");
                        return agendaMensalController.acaoEntrar();
                    case AMBIENTE_EST:
                        AgendaAmbienteColaboradorControle ambienteEst = (AgendaAmbienteColaboradorControle) JSFUtilities.getManagedBean("AgendaAmbienteColaboradorControle");
                        ambienteEst.setParTipoFiltro("ambiente");
                        return ambienteEst.verPreferencias();
                    case PROFISSIONAL_EST:
                        AgendaAmbienteColaboradorControle profissional = (AgendaAmbienteColaboradorControle) JSFUtilities.getManagedBean("AgendaAmbienteColaboradorControle");
                        profissional.setParTipoFiltro("colaborador");
                        return profissional.verPreferencias();
                    case INDIVIDUAL_EST:
                        AgendaIndividualControle agendaIndividual = (AgendaIndividualControle) JSFUtilities.getManagedBean("agendaIndividualControle");
                        agendaIndividual.setRecarregarDados(true);
                        return agendaIndividual.acaoEntrar();
                    case CARTEIRAS:
                        LoginControle carteiras = JSFUtilities.getManagedBean(LoginControle.class);
                        return carteiras.abrirCarteirasCRM();
                    case CRM_META_DIARIA:
                        MetaCRMControle metaCRMControle = (MetaCRMControle) JSFUtilities.getManagedBean("MetaCRMControle");
                        return metaCRMControle.consultarMetas();
                    case CONFIG_ZW:
                        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getManagedBean("ConfiguracaoSistemaControle");
                        config.novo();
                        break;
                    case GOOG_CALENDAR:
                        ConfiguracaoSistemaControle calendar = (ConfiguracaoSistemaControle) JSFUtilities.getManagedBean("ConfiguracaoSistemaControle");
                        calendar.novo();
                        break;
                    case CONFIG_CRM:
                        ConfiguracaoSistemaCRMControle configCrm = (ConfiguracaoSistemaCRMControle) JSFUtilities.getManagedBean("ConfiguracaoSistemaCRMControle");
                        configCrm.iniciar();
                        break;
                    case CONFIG_FIN:
                        ConfiguracaoFinanceiroControle configFinan = (ConfiguracaoFinanceiroControle) JSFUtilities.getManagedBean("ConfiguracaoFinanceiroControle");
                        configFinan.preparaEdicao();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(configFinan.getMsgAlert());
                        break;
                    case DOC_VELOCIDADE:
                        int h = getHeightScreenClient();
                        int w = getWidthScreenClient();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl("abrirPopup('https://pactosolucoes.com.br/ajuda/conhecimento/doc-velocidade/', 'Doc. Velocimetro', " + w + "," + h + ");");
                        break;
                    case VELOCIMETRO:
                        int hc = getHeightScreenClient();
                        int wc = getWidthScreenClient();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl("abrirPopup('http://pactosolucoes.speedtestcustom.com/', 'Velocimetro', " + wc + "," + hc + ");");
                        break;
                    case CONFIG_ESTUDIO:
                        ConfiguracaoEstudioControle confEstudio = (ConfiguracaoEstudioControle) JSFUtilities.getManagedBean("configuracaoEstudioControle");
                        confEstudio.acaoEntrar();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(confEstudio.getMsgAlert());
                        break;
                    case TIPO_CONTA:
                        TipoContaControle tipoC = (TipoContaControle) JSFUtilities.getManagedBean("TipoContaControle");
                        tipoC.abrirTipoConta();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(tipoC.getMsgAlert());
                        break;
                    case CONTA_CONTABIL:
                        ContaContabilControle controle = (ContaContabilControle) JSFUtilities.getManagedBean("ContaContabilControle");
                        controle.abrirContaContabil();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(controle.getMsgAlert());
                        break;
                    case TIPO_DOCUMENTO:
                        TipoDocumentoControle tipoD = (TipoDocumentoControle) JSFUtilities.getManagedBean("TipoDocumentoControle");
                        tipoD.abrirTipoDocumento();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(tipoD.getMsgAlert());
                        break;
                    case SORTEIO:
                        SorteioControle sorteioControle = (SorteioControle) JSFUtilities.getManagedBean("SorteioControle");
                        sorteioControle.abrirPopup();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(sorteioControle.getMsgAlert());
                        break;
                    case OPERACOES_COLETIVAS:
                        OperacaoColetivaControle operacaoColetivaControle = (OperacaoColetivaControle) JSFUtilities.getManagedBean("OperacaoColetivaControle");
                        operacaoColetivaControle.abrirPopup();
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(operacaoColetivaControle.getMsgAlert());
                        break;
                    case DADOS_USUSARIO:
                        UsuarioControle acu = (UsuarioControle) JSFUtilities.getManagedBean("UsuarioControle");
                        acu.novoMeusDados();
                        break;
                    case MARCAR_COMPARECIMENTO:
                        AgendaControle agendaCRM = (AgendaControle) JSFUtilities.getManagedBean("AgendaControle");
                        agendaCRM.novo();
                        break;
                    case REGISTRAR_ACESSO_AVULSO:
                        RegistrarAcessoAvulsoControle registrarAcessoAvulsoControle = (RegistrarAcessoAvulsoControle) JSFUtilities.getManagedBean("RegistrarAcessoAvulsoControle");
                        registrarAcessoAvulsoControle.inicializarDados();
                        break;
                    case CLUBE_VANTAGENS_CONFIGURACOES:
                        ItemCampanhaControle itemCampanhaControle = (ItemCampanhaControle) JSFUtilities.getManagedBean("ItemCampanhaControle");
                        return itemCampanhaControle.abrirItemCampanha();
                    case CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE:
                        ClubeVantagensControle configItemCampanhaControle = JSFUtilities.getManagedBean(ClubeVantagensControle.class);
                        return configItemCampanhaControle.carregarBI();
                    case CLUBE_VANTAGENS_ATIVAR:
                        ClubeVantagensControle clubeVantagensControle = JSFUtilities.getManagedBean(ClubeVantagensControle.class);
                        return clubeVantagensControle.ativarClubeVantagens();
                    case CLUBE_VANTAGENS_CAMPANHA:
                        ClubeVantagensControle controleClubeVantagens = JSFUtilities.getManagedBean(ClubeVantagensControle.class);
                        return controleClubeVantagens.abrirClubeVantagens();
                    case QUESTIONARIO:
                        QuestionarioControle questionarioControle = JSFUtilities.getManagedBean(QuestionarioControle.class);
                        questionarioControle.limparMsg();
                        questionarioControle.setCadastroPesquisa(false);
                        break;
                    case PESQUISA:
                        QuestionarioControle pesquisaControle = JSFUtilities.getManagedBean(QuestionarioControle.class);
                        pesquisaControle.limparMsg();
                        pesquisaControle.setCadastroPesquisa(true);
                        break;
                    case RELATORIO_BVS:
                        RelatorioBVsControle relatorioBVsControle = JSFUtilities.getManagedBean(RelatorioBVsControle.class);
                        relatorioBVsControle.limparMsg();
                        relatorioBVsControle.limparCampos();
                        relatorioBVsControle.setRelatorioPesquisa(false);
                        break;
                    case RELATORIO_PESQUISA:
                        RelatorioBVsControle relatorioPesqBVsControle = JSFUtilities.getManagedBean(RelatorioBVsControle.class);
                        relatorioPesqBVsControle.limparMsg();
                        relatorioPesqBVsControle.limparCampos();
                        relatorioPesqBVsControle.setRelatorioPesquisa(true);
                        break;
                    case IMPORTACAO:
                        ImportacaoControle importacaoControle = JSFUtilities.getManagedBean(ImportacaoControle.class);
                        importacaoControle.novo();
                        break;
                    case FINAN_CONTA:
                        ContaControle contaControle = (ContaControle) JSFUtilities.getManagedBean("ContaControle");
                        contaControle.abrirConta();
    //                    funcSelecionada.getFuncionalidadeSistemaEnumAux().setUrl(contaControle.getMsgAlert());
                        break;
                    case ADM_BUSINESS_INTELLIGENCE:
                        BIControle biControle = (BIControle) JSFUtilities.getManagedBean("BIControle");
                        biControle.irTelaBI();
                        break;
                    case BUSINESS_INTELLIGENCE:
                        BIControle biControleNaoTrabalharComPontuacao = JSFUtilities.getManagedBean(BIControle.class);
                        biControleNaoTrabalharComPontuacao.irTelaBI();
                        break;
                    case ADM_VENDA_RAPIDA:
                        InclusaoVendaRapidaControle inclusaoVendaRapidaControle = JSFUtilities.getManagedBean(InclusaoVendaRapidaControle.class);
                        inclusaoVendaRapidaControle.irParaTelaVendaRapida();
                        break;
                    case SOCIAL_MAILING:
                        LoginControle loginControleSocial = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                        loginControleSocial.abrirSocialMailing();
                        return loginControleSocial.getMsgAlert();
                    case PESSOAS_CLIENTES:
                        redirect("/faces/clientes.jsp");
                        break;
                    case PESSOAS_INCLUIR_CLEINTE:
                        redirect("/faces/preCadastro.jsp");
                        break;
                    case CLUBE_DE_BENEFICIOS:
                        redirect("/faces/tela1.jsp?cb=true");
                        break;
                }
            }
            String retorno = validarFuncionalidadeCliente();
            if (!UteisValidacao.emptyString(retorno)) {
                return retorno;
            }
        } finally {
            this.abrirPopUp = gerarAbrirPopUp();
        }
        return null;
    }

    public String validarFuncionalidadeCliente() throws Exception {
        if (funcSelecionada.getFuncionalidadeClienteEnumAux().getFuncionalidadeClienteEnum() != null) {
            switch (funcSelecionada.getFuncionalidadeClienteEnumAux().getFuncionalidadeClienteEnum()) {
                case NOVO_CONTRATO:
                    ClienteControle clienteControle = JSFUtilities.getManagedBean(ClienteControle.class);
                    String retorno = clienteControle.mostrarListaContratosARenovarEARematricular();
                    funcSelecionada.getFuncionalidadeClienteEnumAux().setUrl(clienteControle.getMostrarRichModalPanelListaContratosARenovarOuRematricular() + ";");
                    if (retorno.toUpperCase().contains("RICHFACES")) {
                        return "editarCliente";
                    }

                    return retorno;
                case NOVO_ATESTADO:
                    abrirFuncCliente = true;
                    return "editarCliente";
                case ULTIMO_BOLETO_VISITA:
                    QuestionarioClienteCRMControle boletim = (QuestionarioClienteCRMControle) JSFUtilities.getManagedBean("QuestionarioClienteCRMControle");
                    abrirFuncCliente = true;
                    boletim.novoCRM();
                    return "editarCliente";
                case HISTORICO_BOLETINS:
                    QuestionarioClienteControle boletins = (QuestionarioClienteControle) JSFUtilities.getManagedBean("QuestionarioClienteControle");
                    abrirFuncCliente = true;
                    boletins.novo();
                    return "editarCliente";
                case REALIZAR_CONTATO:
                    HistoricoContatoControle realizarContato = (HistoricoContatoControle) JSFUtilities.getManagedBean("HistoricoContatoControle");
                    realizarContato.inicializarContatoAvulso();
                    funcSelecionada.getFuncionalidadeClienteEnumAux().setUrl(realizarContato.getExecutarAberturaPopupRealizarContato());
                    abrirFuncCliente = true;
                    return "editarCliente";
                case HISTORICO_CONTATOS:
                    HistoricoContatoControle historicoContatos = (HistoricoContatoControle) JSFUtilities.getManagedBean("HistoricoContatoControle");
                    historicoContatos.inicializarHistoricoContato();
                    abrirFuncCliente = true;
                    return "editarCliente";
                case HISTORICO_INDICACOES:
                    IndicacaoControle indicacoes = (IndicacaoControle) JSFUtilities.getManagedBean("IndicacaoControle");
                    indicacoes.historicoIndicacao();
                    abrirFuncCliente = true;
                    return "editarCliente";
                case LANCAR_MENSAGEM_CATRACA:
                    ClienteControle msgCatraca = JSFUtilities.getManagedBean(ClienteControle.class);
                    msgCatraca.editarClienteMensagemCatraca();
                    abrirFuncCliente = true;
                    return "editarCliente";
                case LANCAR_AVISO_CONSULTOR:
                    ClienteControle msgConsultor = JSFUtilities.getManagedBean(ClienteControle.class);
                    msgConsultor.editarClienteMensagemConsultor();
                    abrirFuncCliente = true;
                    return "editarCliente";
                case LANCAR_AVISO_MEDICO:
                    ClienteControle msgMedico = JSFUtilities.getManagedBean(ClienteControle.class);
                    msgMedico.editarClienteMensagemMedico();
                    abrirFuncCliente = true;
                    return "editarCliente";
                case LANCAR_OBJETIVO:
                    ClienteControle clienteObjetivo = JSFUtilities.getManagedBean(ClienteControle.class);
                    clienteObjetivo.editarClienteMensagemObjetivo();
                    abrirFuncCliente = true;
                    return "editarCliente";
                case PRODUTO_SERVICO:
                    VendaAvulsaControle vendaAvulsa = JSFUtilities.getManagedBean(VendaAvulsaControle.class);
                    return vendaAvulsa.novo();
                case DIARIA:
                    AulaAvulsaDiariaControle diaria = (AulaAvulsaDiariaControle) JSFUtilities.getManagedBean("AulaAvulsaDiariaControle");
                    return diaria.novoDiariaCliente();
                case VINCULAR_CARTEIRA:
                    abrirFuncCliente = true;
                    return "editarCliente";
                case ADICIONAR_CLASSIFICACAO:
                    abrirFuncCliente = true;
                    return "editarCliente";
                case ASSOCIAR_GRUPOS:
                    abrirFuncCliente = true;
                    return "editarCliente";
            }
        }
        return "";
    }
    
    
    
    public void autorizar(String permissao, String descricao) throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), permissao,descricao);
            }
        }
    }

    //Caso o item selecionado no campo sugestão seje um cliente
    //ira redirecionar para tela editar cliente.
    //Case: Funcionalidade entaum irá abrir o modulo da funcionalidadeNome exceto zillyon
    // e executa os metodos necessários para exibir a funcionalidadeNome;
    public String abrirComCasoNavegacao() throws Exception {
        setarFuncionalidade();
        MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);

        if (funcSelecionada != null && !funcSelecionada.getDescricao()) {

            boolean vendaAvulsa = false;
            boolean usarNovaVersaoVendaAvulsa = false;
            boolean caixaEmAberto = false;
            boolean usarNovaVersaoCaixaEmAberto = false;
            try {
                if (funcSelecionada.getFuncionalidadeSistemaEnumAux() != null &&
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum() != null &&
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.VENDA_AVULSA) &&
                        getUsuarioLogado() != null && !UteisValidacao.emptyNumber(getUsuarioLogado().getCodigo())) {
                    vendaAvulsa = true;
                    usarNovaVersaoVendaAvulsa = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.VENDA_AVULSA, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
                }
                if (funcSelecionada.getFuncionalidadeSistemaEnumAux() != null &&
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum() != null &&
                        funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO) &&
                        getUsuarioLogado() != null && !UteisValidacao.emptyNumber(getUsuarioLogado().getCodigo())) {
                    caixaEmAberto = true;
                    usarNovaVersaoCaixaEmAberto = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.CAIXA_ABERTO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (((caixaEmAberto && usarNovaVersaoCaixaEmAberto) ||
                    (vendaAvulsa && usarNovaVersaoVendaAvulsa)) ||
                    (!caixaEmAberto && !vendaAvulsa &&
                            funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum() != null &&
                            (FuncionalidadeSistemaEnum.CANAL_CLIENTE.equals(funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum()) ||
                                    Arrays.stream(UrlRedirectNZWEnum.values()).anyMatch(
                                            urlRedirectNZWEnum -> funcSelecionada.getFuncionalidadeSistemaEnumAux()
                                                    .getFuncionalidadeSistemaEnum().equals(urlRedirectNZWEnum.getFuncionalidadeSistemaEnum())) ||
                                    Arrays.stream(UrlRedirectPAYEnum.values()).anyMatch(
                                            urlRedirectPAYEnum -> funcSelecionada.getFuncionalidadeSistemaEnumAux()
                                                    .getFuncionalidadeSistemaEnum().equals(urlRedirectPAYEnum.getFuncionalidadeSistemaEnum()))))) {
                String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
                if (modulos.contains("NZW") || modulos.contains("PAY")) {
                    return "";
                } else {
                    return funcSelecionada.getUrl();
                }
            } else if (funcSelecionada.isCliente()) {
                LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                loginControle.setModuloAberto(ModuloAberto.ZILLYONWEB);
                getFacade().getCliente().marcaCliente(funcSelecionada.getClienteVO().getCodigo(), getUsuarioLogado().getCodigo());
                ClientesMarcadosControle controlMarc = (ClientesMarcadosControle) JSFUtilities.getManagedBean(ClientesMarcadosControle.class.getSimpleName());
                controlMarc.processaClientesMarcados();
                String novaTelaCliente = controlMarc.getLinkNovaTelaCliente(funcSelecionada.getClienteVO().getMatricula());
                if (!UteisValidacao.emptyString(novaTelaCliente)) {
                    redirectUrl(novaTelaCliente);
                } else {
                    redirect("/faces/clienteNav.jsp?page=cliente&matricula="+funcSelecionada.getClienteVO().getMatricula());
                }
                return "";
            } else {
                String navegacao = preparaFuncionalidade();
                if (navegacao != null) {
                    if(funcSelecionada.getFuncionalidade() != null && !navegacao.contains("('modalConsCaixa')")) {
                        inicializarModulo(funcSelecionada.getFuncionalidade());
                    }
                    if(navegacao.equals("editarCliente")){
                         redirect("/faces/clienteNav.jsp?page=cliente&matricula="+clienteEditar.getMatricula());
                         return "";
                    }
                    return navegacao;
                }else{
                    return funcSelecionada.getUrl();
                }

            }

        }

        if(menuControle != null){
            menuControle.setUrlGoBackRedirect(null);
        }

        return "";
    }

    public void inicializarModulo(ModuloAberto moduloAberto) {
        LoginControle loginControle = JSFUtilities.getManagedBean(LoginControle.class);
        if (loginControle.getModuloAberto() != moduloAberto) {
            if (moduloAberto == ModuloAberto.ZILLYONWEB) {
                loginControle.abrirZillyonWeb();
            } else if (moduloAberto == ModuloAberto.CANAL_CLIENTE) {
                loginControle.abrirModuloCanalCliente();
            } else if (moduloAberto == ModuloAberto.CRMWEB) {
                loginControle.abrirModuloCRM();
            } else if (moduloAberto == ModuloAberto.CE) {
                loginControle.abrirCE();
            } else if (moduloAberto == ModuloAberto.FINAN) {
                loginControle.abrirModuloFinanceiro();
            } else if (moduloAberto == ModuloAberto.GESTAOSTUDIO) {
                loginControle.abrirModuloEstudio();
            }
        }
    }

    public Boolean getContemPagesUrl() {

        return !request().getRequestURI().contains("pages");
    }

    public void preparaTelaEditarCliente(ClienteVO cliente, ActionEvent evt) throws Exception {
        setNavigationCase(tratarNavigationCaseIntegracaoModulo("tela3", "modulo_lista_clientes", evt));
        ClienteControle control = JSFUtilities.getManagedBean(ClienteControle.class);
        control.inicializaDados();
        setMensagemID("msg_entre_dados");
        control.prepararTelaCliente((cliente), true);
    }

    public String irTelaEditarCliente() throws Exception {

        if (getNavigationCase().contains("4bf2add2267962ea87f029fef8f75a2f")) {
            return "modulo_visualiza_cliente?modulo=4bf2add2267962ea87f029fef8f75a2f";
        }
        return tratarNavigationCaseIntegracaoModulo("editarCliente", "modulo_visualiza_cliente");
    }

    public void prepararFuncionalidade(ActionEvent evt){
        try{
            String nomeFuncionalidade = (String)evt.getComponent().getAttributes().get("funcionalidade");
            prepararFuncionalidade(nomeFuncionalidade, evt);
            this.abrirPopUp = gerarAbrirPopUp();
        }catch (Exception ex){
            Uteis.logar(ex,FuncionalidadeControle.class);
        }
    }

    public void prepararFuncionalidade(String nomeFuncionalidade, ActionEvent evt){
        try{
            IdLocalicazaoMenuEnum idLocalicazaoMenuEnum = null;
            if(evt != null && evt.getComponent() != null && evt.getComponent().getAttributes() != null){
                String idLocalizacaoMenu = (String) evt.getComponent().getAttributes().get("idLocalizacaoMenu");
                try{
                    idLocalicazaoMenuEnum = IdLocalicazaoMenuEnum.valueOf(idLocalizacaoMenu);
                }catch (Exception e){
                    Uteis.logarDebug("Não foi possivel encontrar o id de localização do menu: "+ idLocalizacaoMenu);
                }
            }

            funcSelecionada = new ClienteFuncionalidadeTO();
            funcSelecionada.setIdLocalicazaoMenuEnum(idLocalicazaoMenuEnum);
            funcSelecionada.setCliente(false);
            funcSelecionada.getFuncionalidadeClienteEnumAux().setFuncionalidadeClienteEnum(FuncionalidadeClienteEnum.obterPorNome(nomeFuncionalidade));
            funcSelecionada.setDescricao(false);
            funcSelecionada.getFuncionalidadeSistemaEnumAux().setFuncionalidadeSistemaEnum(FuncionalidadeSistemaEnum.obterPorNome(nomeFuncionalidade));
            prepararFuncionalidade(funcSelecionada, evt);
            this.abrirPopUp = gerarAbrirPopUp();
        }catch (Exception ex){
            Uteis.logar(ex,FuncionalidadeControle.class);
        }
    }
    //Executas as ActionListeners caso necessário
    //para executar as Funcionalidades
    public void abrirComAcaoListener(ActionEvent evt) {
        try {
            setRotulo("");
            funcSelecionada = (ClienteFuncionalidadeTO) request().getAttribute("result");
            if(evt != null && evt.getComponent() != null && evt.getComponent().getAttributes() != null ){
                String idLocalizacaoMenu = null;
                try{
                    IdLocalicazaoMenuEnum idLocalicazaoMenuEnum = null;
                    idLocalizacaoMenu = (String) evt.getComponent().getAttributes().get("idLocalizacaoMenu");
                    idLocalicazaoMenuEnum = IdLocalicazaoMenuEnum.valueOf(idLocalizacaoMenu);
                    funcSelecionada.setIdLocalicazaoMenuEnum(idLocalicazaoMenuEnum);
                }catch (Exception e){
                    Uteis.logarDebug("Não foi possivel encontrar o id de localização do menu: "+ idLocalizacaoMenu);
                }
            }

            prepararFuncionalidade(funcSelecionada,evt);
            this.abrirPopUp = gerarAbrirPopUp();
        } catch (Exception erro) {
            erro.printStackTrace();
        }
    }

    public void prepararFuncionalidade(ClienteFuncionalidadeTO obj, ActionEvent evt) throws Exception {
        LoginControle loginControle = JSFUtilities.getManagedBean(LoginControle.class);
        if (obj != null && !obj.getDescricao()) {
            if (!obj.isCliente()) {
                if (obj.getFuncionalidadeSistemaEnumAux() != null
                        && obj.getFuncionalidadeSistemaEnumAux().getModulo() != null
                        && !obj.getFuncionalidadeSistemaEnumAux().getModulo().isZwUi()) {
                    loginControle.setModuloAberto(obj.getFuncionalidadeSistemaEnumAux().getModulo());
                }


                if (obj.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum() != null)
                    switch (obj.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum()) {
                        case CRM_BI:
                            BusinessIntelligenceCRMControle businessIntelligenceCRMControle = (BusinessIntelligenceCRMControle) JSFUtilities.getManagedBean("BusinessIntelligenceCRMControle");
                            businessIntelligenceCRMControle.carregarBusinessIntelligence();
                            redirect("/faces/telaBICRM.jsp");
                            break;
                        case FIN_BI:
                            BIFinanceiroControle biFinanceiroControle = (BIFinanceiroControle) JSFUtilities.getManagedBean("BIFinanceiroControle");
                            biFinanceiroControle.inicializar();
                            redirect("/faces/pages/finan/relatorios.jsp");
                            break;
                        case BUSINESS_INTELLIGENCE_CE:
                            BIControle biControle = (BIControle) JSFUtilities.getManagedBean("BIControle");
                            biControle.inicializarRelatoriosCE();
                            redirect("/faces/pages/ce/relatorio/telaRelatorio.jsp");
                            break;
                        case CONVENIO_COBRANCA:
                            ConvenioCobrancaControle convenioCobrancaControle = (ConvenioCobrancaControle) JSFUtilities.getManagedBean("ConvenioCobrancaControle");
                            convenioCobrancaControle.novo();
                            break;
                        case PINPAD:
                            PinPadControle pinPadControle = (PinPadControle) JSFUtilities.getManagedBean("PinPadControle");
                            pinPadControle.novo();
                            break;
                        case VENDA_AVULSA:
                        case VENDA_AVULSA_EST:
                            VendaAvulsaControle vendaAvulsa = JSFUtilities.getManagedBean(VendaAvulsaControle.class);
                            vendaAvulsa.prepare(evt);
                            break;
                        case LANCAMENTOS:
                            MovContaControle lancamento = JSFUtilities.getManagedBean(MovContaControle.class);
                            evt.getComponent().getAttributes().put("tipoPesquisa", "1");
                            lancamento.consultarLancamentos(evt);
                            break;
                        case LANCAMENTO_CONTA_RAPIDO:
                            MovContaControle lancamentoRapido = JSFUtilities.getManagedBean(MovContaControle.class);
                            lancamentoRapido.iniciarNovoLancamentoRapido();
                            break;

                        case CONTAS_A_PAGAR:
                            MovContaControle contasPagar = JSFUtilities.getManagedBean(MovContaControle.class);
                            if (evt != null) {
                                evt.getComponent().getAttributes().put("tipoPesquisa", "2");
                                contasPagar.consultarLancamentos(evt);
                            } else {
                                contasPagar.consultarLancamentosTipoPesquisa(2);
                            }
                            break;
                        case CONTAS_A_RECEBER:
                            MovContaControle contasReceber = JSFUtilities.getManagedBean(MovContaControle.class);
                            if (evt != null) {
                                evt.getComponent().getAttributes().put("tipoPesquisa", "3");
                                contasReceber.consultarLancamentos(evt);
                            } else {
                                contasReceber.consultarLancamentosTipoPesquisa(3);
                            }
                            break;
                        case ULTIMOS_LANCAMENTOS:
                            MovContaControle ultimosLancamentos = JSFUtilities.getManagedBean(MovContaControle.class);
                            evt.getComponent().getAttributes().put("tipoPesquisa", "99");
                            ultimosLancamentos.consultarLancamentos(evt);
                            break;
//  joao alcides: estou comentando este recurso ate que seja finalizado
//                        case MAPA_ESTATISTICO:
//                            MapaEstatisticoControle mapaEstatisticoControle = (MapaEstatisticoControle) JSFUtilities.getManagedBean("MapaEstatisticoControle");
//                            mapaEstatisticoControle.inicializar();
//                            break;
                        case HISTORICO_PONTOS_DOTZ:
                            HistoricoPontosParceiroFidelidadeControle histDotz = (HistoricoPontosParceiroFidelidadeControle) JSFUtilities.getManagedBean("HistoricoPontosParceiroFidelidadeControle");
                            histDotz.inicializarDados();
                            break;
                        case MAILING:
                            notificarRecursoEmpresa(RecursoSistema.fromDescricao(funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().toString()));
                            break;
                        case CONTATO_PESSOAL:
                            notificarRecursoEmpresa(RecursoSistema.fromDescricao(funcSelecionada.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum().toString()));
                            break;
                        case NOTAS_FISCAIS:
                            loginControle.abrirModuloNotaFiscal();
                            redirect("/faces/notaFiscal.jsp");
                            break;
                    }
                if (obj.getFuncionalidadeClienteEnumAux().getFuncionalidadeClienteEnum() != null) {
                    prepararFuncionalidadeDeCliente(obj, evt);
                }
            }
        }
    }

    private void prepararFuncionalidadeDeCliente(ClienteFuncionalidadeTO obj, ActionEvent evt) {
        switch (obj.getFuncionalidadeClienteEnumAux().getFuncionalidadeClienteEnum()) {
            case NOVO_ATESTADO:
                AtestadoControle atestadoControle = (AtestadoControle) JSFUtilities.getManagedBean("AtestadoControle");
                evt.getComponent().getAttributes().put("cliente", clienteEditar);
                atestadoControle.prepararAtestado(evt);
                break;
            case VINCULAR_CARTEIRA:
                ClienteControle clienteControle = JSFUtilities.getManagedBean(ClienteControle.class);
                evt.getComponent().getAttributes().put("cliente", clienteEditar);
                evt.getComponent().getAttributes().put("aba", "abaVinculo");
                clienteControle.abrirAba(evt);
                break;
            case ADICIONAR_CLASSIFICACAO:
                ClienteControle classificacao = JSFUtilities.getManagedBean(ClienteControle.class);
                evt.getComponent().getAttributes().put("cliente", clienteEditar);
                evt.getComponent().getAttributes().put("aba", "abaGrupo");
                classificacao.abrirAba(evt);
                break;
            case ASSOCIAR_GRUPOS:
                ClienteControle grupos = JSFUtilities.getManagedBean(ClienteControle.class);
                evt.getComponent().getAttributes().put("cliente", clienteEditar);
                evt.getComponent().getAttributes().put("aba", "abaGrupo");
                grupos.abrirAba(evt);
                break;
            case PRODUTO_SERVICO:
                VendaAvulsaControle produto = JSFUtilities.getManagedBean(VendaAvulsaControle.class);
                produto.prepare(evt);
                break;
        }
    }

    public void montarFotos(List<ClienteVO> clientes) {
        for (ClienteVO cliente : clientes) {
            if (!isFotosNaNuvem()) {
                this.clientesFoto.put(cliente.getCodigo(), cliente.getPessoa().getFoto());
            }else {
                this.clientesFotoKey.put(cliente.getCodigo(), cliente.getPessoa().getFotoKey());
            }
        }
    }

    public String getAbrirPopTelaCliente() {

        //Será aberto somente uma vez a popUp após solicitado
        if (abrirFuncCliente) {
            abrirFuncCliente = false;
            return funcSelecionada.getFuncionalidadeClienteEnumAux().getUrl().replace("[contexto]", request().getContextPath() + "/faces/");
        }
        return "";
    }

    public String getAbrirPopUp() {
        return this.abrirPopUp == null || this.abrirPopUp.equals("semValor") ? "" : this.abrirPopUp;
    }

    public String gerarAbrirPopUp() throws Exception {
        MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
        if (funcSelecionada != null && !funcSelecionada.isCliente() && !funcSelecionada.getDescricao()) {
            //Caso seja uma funcionalidadeNome do sistema
            FuncionalidadeSistemaEnumAux funcionalidadeSelecionada = funcSelecionada.getFuncionalidadeSistemaEnumAux();
            if (funcionalidadeSelecionada.getFuncionalidadeSistemaEnum() != null && !funcionalidadeSelecionada.getUrl().equals("link") && (!funcionalidadeSelecionada.getUrl().equals("semValor")
                    || !funcSelecionada.equals(""))) {
                //Adiciona contexto ao Abrir popUp devido a chamada poder acontecer em qualquer lugar do sistema
                if (request().getContextPath() != null) {
                    LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
                    String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
                        String funcionalidadeNome = funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getName();
                    if (funcionalidadeNome.equals(UrlRedirectNZWEnum.PLANO.getFuncionalidadeSistemaEnum().getName())) {
                        if (loginControle != null && !loginControle.getApresentarAcessoNovoPlano()) {
                            return funcSelecionada.getFuncionalidadeSistemaEnumAux().getUrl().replace("[contexto]", request().getContextPath() + "/faces/");
                        }
                    }
                    if(funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.CANAL_CLIENTE)){
                        return "window.open('" + loginControle.getAbrirNovaPlataforma(
                                funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().getSigla(), null,
                                funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getUrl()) +"', '_self')";
                    }

                    if(funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().equals(ModuloAberto.NOVO_TREINO) ||
                            funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().equals(ModuloAberto.AVALIACAO_FISICA) ||
                            funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().equals(ModuloAberto.CANAL_CLIENTE) ||
                            funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().equals(ModuloAberto.PACTO_PAY) ||
                            funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().equals(ModuloAberto.GRADUACAO) ||
                            funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().equals(ModuloAberto.CROSS) ||
                            funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().equals(ModuloAberto.AGENDA)){
                        return "window.open('" + loginControle.getAbrirNovaPlataforma(funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getModulo().getSigla()) +
                                "&redirect=/" + funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().getUrl() + "', '_self')";
                    }
                    if (modulos != null && modulos.contains("NZW")
                            && !getUsuarioLogado().getUsuarioAdminPACTO() && loginControle != null
                    ) {

                        boolean usarNovaVersao = true;
                        try {
                            if (funcionalidadeSelecionada.getFuncionalidadeSistemaEnum() != null &&
                                    funcionalidadeSelecionada.getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.VENDA_AVULSA) &&
                                    getUsuarioLogado() != null && !UteisValidacao.emptyNumber(getUsuarioLogado().getCodigo())) {
                                usarNovaVersao = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.VENDA_AVULSA, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        for (UrlRedirectNZWEnum fse : UrlRedirectNZWEnum.values()) {
                            if (usarNovaVersao && fse.getFuncionalidadeSistemaEnum().getName().equals(funcionalidadeNome)) {
                                if (menuControle != null) {
                                    menuControle.setUrlGoBackRedirect(null);
                                }
                                return "window.open('" + loginControle.getAbrirNovaPlataforma("NZW") + "&redirect=/adm/" + fse.getUri() + "', '_self')";
                            }
                        }
                    }
                    if (modulos != null && modulos.contains("PAY")
                            && !getUsuarioLogado().getUsuarioAdminPACTO() && loginControle != null
                    ) {
                        for (UrlRedirectPAYEnum fse : UrlRedirectPAYEnum.values()) {
                            if (fse.getFuncionalidadeSistemaEnum().getName().equals(funcionalidadeNome)) {
                                if (menuControle != null) {
                                    menuControle.setUrlGoBackRedirect(null);
                                }
                                return "window.open('" + loginControle.getAbrirNovaPlataforma("PAY") + "&redirect=pactopay/" + fse.getUri() +"', '_self')";
                            }
                        }
                    }
                    return funcSelecionada.getFuncionalidadeSistemaEnumAux().getUrl().replace("[contexto]", request().getContextPath() + "/faces/");
                }

            } else
                //Caso seja uma funcionalidadeNome do cliente
                if (funcionalidadeSelecionada.getFuncionalidadeSistemaEnum() != null && funcionalidadeSelecionada.getUrl().equals("link")) {
                    switch (funcionalidadeSelecionada.getFuncionalidadeSistemaEnum()) {
//                        case FAQ_PERSONALIZADO:
//                            SuporteControle faqP = (SuporteControle) JSFUtilities.getManagedBean("SuporteControle");
//                            return "  window.open('" + faqP.getUrlFAQ() + "');";
                        case WIKI_PACTO:
                            SuporteControle wiki = (SuporteControle) JSFUtilities.getManagedBean("SuporteControle");
                            return "  window.open('" + wiki.getUrlWikiRaiz() + "/P%C3%A1gina_principal');";
                        case UCP:
                        case SUPORTE:
                            SuperControle suporte = (SuperControle) JSFUtilities.getManagedBean("SuperControle");
                            return "  window.open('" + suporte.getContextPath() + "/redir?suporte&solicitacao=true');";
                        case GERADOR_CONSULTAS:
                            BIControle biControle = new BIControle();
                            LoginControle loginControle = JSFUtilities.getManagedBean(LoginControle.class);
                            loginControle.notificarRecursoEmpresaGameOfResults();
                            return "  window.open('" + biControle.getUrlGame(true) + "');";
                    }

                }
        }
        return "";
    }

    public void validarPermissoes(List<GrupoFuncionalidadeSistemaEnum> grupos){
        for (GrupoFuncionalidadeSistemaEnum grupo: grupos) {
            for (FuncionalidadeSistemaEnum fun: grupo.getFuncionalidades()) {
                fun.isRenderizar();
            }
        }
    }
    public Boolean validarPermissao() {
        return validarPermissao("true");
    }

    public boolean validarPermissao(String param) {
        if (param != null) {

            if (param.equals("true")) {
                return true;
            } else if (param.equals("false")) {
                return false;
            } else if (!param.equals("")) {
                try {
                    if (funcHabilitadas.containsKey(param)) {
                        return funcHabilitadas.get(param);
                    }
                    Boolean valor = JSFUtilities.getManagedBeanValue(param) != null && (Boolean) JSFUtilities.getManagedBeanValue(param);
                    funcHabilitadas.put(param, valor);
                    return valor;
                } catch (Exception e) {
                    Uteis.logarDebug("Erro ao validar pemissao de acesso a funcionalidade.");
                    e.printStackTrace();
                    funcHabilitadas.put(param, false);
                    return false;
                }
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

    public Boolean validarModuloNavegar(FuncionalidadeSistemaEnum func) throws Exception {
        LoginControle login = JSFUtilities.getManagedBean(LoginControle.class);
        if (func.getModulo() == null)
            return true;
        if (func.getModulo().equals(ModuloAberto.ZILLYONWEB) && login.isApresentarLinkZW())
            return true;
        if (func.getModulo().equals(ModuloAberto.CRMWEB) && login.isApresentarLinkCRM())
            return true;
        if (func.getModulo().equals(ModuloAberto.CE) && login.isApresentarLinkCentralEventos())
            return true;
        if (func.getModulo().equals(ModuloAberto.FINAN) && login.isApresentarLinkFinanceiro())
            return true;
        if (func.getModulo().equals(ModuloAberto.CANAL_CLIENTE))
            return true;
        if (func.getModulo().equals(ModuloAberto.CRMWEB) && login.isApresentarLinkCRM())
            return true;
        if (func.getModulo().equals(ModuloAberto.CE) && login.isApresentarLinkCE()) {
            try {
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(login.getPerfilAcesso(), getUsuarioLogado(), "ModuloCE", "8.01 - Módulo CE");
            } catch (Exception e) {
                return false;
            }
            return true;
        }
        if (func.getModulo().equals(ModuloAberto.NOVO_TREINO) && login.isApresentarModuloNovoTreino())
            return true;
        if (func.getModulo().equals(ModuloAberto.TREINO_WEB) && login.isApresentarLinkTREINO())
            return true;
        if (func.getModulo().equals(ModuloAberto.PACTO_PAY) && login.isApresentarModuloPactoPay())
            return true;
        if (func.getModulo().equals(ModuloAberto.CROSS) && login.isApresentarModuloCross())
            return true;
        if (func.getModulo().equals(ModuloAberto.AVALIACAO_FISICA) && login.isApresentarModuloAvaliacaoFisica())
            return true;
        if (func.getModulo().equals(ModuloAberto.GRADUACAO) && login.isApresentarModuloGraduacao())
            return true;
        if (func.getModulo().equals(ModuloAberto.AULA_CHEIA) && login.isApresentarModuloAulaCheia())
            return true;
        if (func.getModulo().equals(ModuloAberto.GESTAOSTUDIO) && login.isApresentarLinkEstudio())
            return true;
        if (func.getModulo().equals(ModuloAberto.UCP))
            return true;
        if (func.getModulo().equals(ModuloAberto.NOTAS) && login.isApresentarNotaFiscal())
            return true;
        if (func.getModulo().equals(ModuloAberto.AGENDA) && login.isApresentarModuloNovoTreino())
            return true;

        return  false;
    }

    public ClienteVO getClienteVO() {
        if (getFuncSelecionada().isCliente())
            return getFuncSelecionada().getClienteVO();
        else
            return new ClienteVO();
    }

    public String removerLigacoes(String valor) {

        valor = valor.toUpperCase().replace(" DE ", " ");
        valor = valor.toUpperCase().replace(" EM ", " ");
        valor = valor.toUpperCase().replace(" A ", " ");
        valor = valor.toUpperCase().replace(" AO ", " ");
        valor = valor.toUpperCase().replace(" DO ", " ");
        valor = valor.toUpperCase().replace(" DA ", " ");
        valor = valor.toUpperCase().replace(" OU ", " ");
        valor = valor.toUpperCase().replace(" PARA ", " ");
        return valor;
    }

    public Boolean valorEquivalente(String digitado, String consultado, String[] palavrasChaves, ClienteFuncionalidadeTO item) {

        Boolean verificaLiteral = Uteis.retirarAcentuacao((removerLigacoes(consultado).replace(".", "").replace("%", ""))).toUpperCase().equals(Uteis.retirarAcentuacao(removerLigacoes(digitado.toUpperCase())));
        Boolean comparaPorPalavraDigitada = false;
        Boolean compararComPalavraChave = false;
        if (Uteis.retirarAcentuacao(removerLigacoes(consultado).replace(".", "").replace("%", "")).toUpperCase().contains(Uteis.retirarAcentuacao(removerLigacoes(digitado.toUpperCase() + " ").trim()))) {
            comparaPorPalavraDigitada = true;
        }
        for (String chaves : palavrasChaves) {
            if (Uteis.retirarAcentuacao(removerLigacoes(chaves)).toUpperCase().trim().contains(Uteis.retirarAcentuacao(removerLigacoes(digitado).replace(".", "").replace("%", "")).toUpperCase())
                    || chaves.toUpperCase().trim().contains(digitado.replace(".", "").replace("%", "").toUpperCase())) {
                compararComPalavraChave = true;
                break;
            }
        }
        if (comparaPorPalavraDigitada)
            item.setEquivalencia(3);
        if (compararComPalavraChave)
            item.setEquivalencia(2);
        if (verificaLiteral)
            item.setEquivalencia(1);

        return verificaLiteral || comparaPorPalavraDigitada || compararComPalavraChave;
    }

    public String getPropriertsKeyWords(String mensagemID) {
        String mensagem = "(" + mensagemID + ") Mensagem não localizada nas propriedades de KeyWords.";
        ResourceBundle bundle;
        Locale locale;
        String nomeBundle = "propriedades.KeyWords";
        if (nomeBundle != null) {
            locale = context().getViewRoot().getLocale();
            bundle = ResourceBundle.getBundle(nomeBundle, locale, getCurrentLoader(nomeBundle));
            try {
                mensagem = bundle.getString(mensagemID);
                return mensagem;
            } catch (MissingResourceException e) {
                return mensagem;
            }
        }
        return mensagem;
    }

    private String[] palavrasChaves(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) throws Exception {
        String[] palavrasChave = getPropriertsKeyWords(getEmpresaLogado().getLocale() + "_" + funcionalidadeSistemaEnum.name()).split(",");
        return palavrasChave;
    }

    public void pesquisaGeralClienteFuncionalidade(List<ClienteFuncionalidadeTO> clientes, String digitado) throws Exception {

        List<ClienteFuncionalidadeTO> funcionalidades = new ArrayList<>();
        LoginControle loginControle = JSFUtilities.getManagedBean(LoginControle.class);

        for (FuncionalidadeSistemaEnum funcionalidadeSistemaEnum : FuncionalidadeSistemaEnum.values()) {
            Boolean validarPermissao;
            ClienteFuncionalidadeTO clienteFuncionalidadeTO = new ClienteFuncionalidadeTO();
            validarPermissao = validarPermissao(funcionalidadeSistemaEnum.getExpressaoRenderizar());
            boolean funcionalidadeAtiva = loginControle.isFuncionalidadeAtiva(funcionalidadeSistemaEnum);
            if ((validarPermissao != null && validarPermissao && validarModuloNavegar(funcionalidadeSistemaEnum)) &&
                    funcionalidadeAtiva &&
                    (valorEquivalente(digitado, funcionalidadeSistemaEnum.getDescricao(), palavrasChaves(funcionalidadeSistemaEnum), clienteFuncionalidadeTO) || digitado.equals("%"))) {
                if(funcionalidadeSistemaEnum.equals(FuncionalidadeSistemaEnum.PERFIL_ACESSO_UNIFICADO)){
                    if(isPerfilAcessoUnificado()){
                        clienteFuncionalidadeTO.getFuncionalidadeSistemaEnumAux().setFuncionalidadeSistemaEnum(funcionalidadeSistemaEnum);
                        clienteFuncionalidadeTO.setCliente(false);
                        funcionalidades.add(clienteFuncionalidadeTO);
                    }
                }else{
                    clienteFuncionalidadeTO.getFuncionalidadeSistemaEnumAux().setFuncionalidadeSistemaEnum(funcionalidadeSistemaEnum);
                    clienteFuncionalidadeTO.setCliente(false);
                    funcionalidades.add(clienteFuncionalidadeTO);
                }
            }
        }

        removerFuncionalidadeListaAntiga(funcionalidades);

        //Ordena as Funcionalidades por nome
        Ordenacao.ordenarLista(funcionalidades, "Ordenacao");

        Integer empresa = podeConsultarEmTodasEmpresas ? null : getEmpresaLogado().getCodigo();

        List<ClienteVO> clienteVOs = getFacade().getCliente().consultarClienteFuncionalidade(getKey(),
                digitado,
                empresa,
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        int cont = 0;
        int numeroMaximoClientes = funcionalidades.size() < 5 ? 35 - funcionalidades.size() : 5;
        for (ClienteVO cliente : clienteVOs) {
            if (cont < numeroMaximoClientes) {
                ClienteFuncionalidadeTO clienteFuncionalidadeTO = new ClienteFuncionalidadeTO();
                clienteFuncionalidadeTO.setClienteVO(cliente);
                clienteFuncionalidadeTO.setCliente(true);
                String nomeFormatado = formatarNome(cliente.getPessoa().getNome());
                cliente.getPessoa().setNome(nomeFormatado);
                clientes.add(clienteFuncionalidadeTO);
            }
            cont++;
        }
        montarFotos(clienteVOs);

        //Busca os clientes por Nome
        int numeroMaximoFuncionalidades = clientes.size() < 5 ? 15 - clientes.size() : 5;
        for (int e = 0; e < numeroMaximoFuncionalidades && !funcionalidades.isEmpty() && e < funcionalidades.size(); e++) {
            clientes.add(funcionalidades.get(e));
        }
        if (funcionalidades.isEmpty() && clientes.size() == 1) {
            clienteEditar = clientes.get(0).getClienteVO();
            mostrarClienteComFuncionlidade(clienteEditar, funcionalidades, "", clientes);
        }

        // Por conta corrente
        int numeroConta;
        int numeroContaDv;
        if (digitado.indexOf("-") != -1) {
            String[] conta = digitado.split("-");
            try {
                numeroConta = Integer.parseInt(conta[0]);
                numeroContaDv = Integer.parseInt(conta[1]);

                List<ClienteVO> clientesContaCorrente = getFacade()
                        .getCliente()
                        .consultarClientesPorContaCorrente(numeroConta, numeroContaDv, "pessoa.nome");

                for (ClienteVO cliente : clientesContaCorrente) {
                    clientes.add(criarClienteFuncionalidadeContaCorrente(cliente));
                }
            } catch (Exception e) {
            }
        }

    }

    private void removerFuncionalidadeListaAntiga(List<ClienteFuncionalidadeTO> funcionalidades){
        Iterator<ClienteFuncionalidadeTO> iterator = funcionalidades.iterator();
        while (iterator.hasNext()) {
            ClienteFuncionalidadeTO clienteFuncionalidadeTO = iterator.next();
            if (clienteFuncionalidadeTO.getFuncionalidadeSistemaEnumAux() != null &&
                    clienteFuncionalidadeTO.getFuncionalidadeSistemaEnumAux().getFuncionalidadeSistemaEnum() == FuncionalidadeSistemaEnum.CADASTROS_PRODUTOS_TURMAS_PLANOS) {
                iterator.remove();
                break;
            }
        }
    }

    private String formatarNome(String nome) {
        //Capitalizar somente a primeira letra de cada nome
        StringBuilder formatado = new StringBuilder();
        for (String palavra : nome.toLowerCase().split(" ")) {
            if (!palavra.isEmpty()) {
                formatado.append(Character.toUpperCase(palavra.charAt(0)))
                        .append(palavra.substring(1))
                        .append(" ");
            }
        }
        return formatado.toString().trim();
    }

    private ClienteFuncionalidadeTO criarClienteFuncionalidadeContaCorrente(ClienteVO cliente){
        ClienteFuncionalidadeTO clienteFuncionalidade = new ClienteFuncionalidadeTO();
        clienteFuncionalidade.setClienteVO(cliente);
        clienteFuncionalidade.setCliente(true);
        clienteFuncionalidade.setAutorizacaoCobranca(true);
        String nomeFormatado = formatarNome(cliente.getPessoa().getNome());
        cliente.getPessoa().setNome(nomeFormatado);
        clienteFuncionalidade.setItemDescricao("Conta corrente "+ cliente.getAutorizacao().getContaCorrenteApresentar());

        return clienteFuncionalidade;
    }

    public void pesquisarFuncionalidadesCliente(List<ClienteFuncionalidadeTO> clientes, String digitado) throws Exception {
        List<ClienteFuncionalidadeTO> funcionalidades = new ArrayList<>();
        String matricula = digitado.split(" ")[0];
        String funcionalidade = "";
        if (!digitado.equals(matricula)) {
            funcionalidade = digitado.replace(matricula, "").substring(1);
        }

        Integer empresaCodigo = podeConsultarEmTodasEmpresas ? null : getEmpresaLogado().getCodigo();

        List<ClienteVO> listaClientes = getFacade().getCliente().consultarPorCodigoMatriculaOuCPF(matricula, empresaCodigo, Uteis.NIVELMONTARDADOS_MINIMOS);

        ClienteVO clienteVO = new ClienteVO();
        if (listaClientes.size() == 1) {
            clienteVO = listaClientes.get(0);
        } else {
            for (ClienteVO clienteVO1 : listaClientes) {
                ClienteFuncionalidadeTO clienteFuncionalidadeTO = new ClienteFuncionalidadeTO();
                clienteFuncionalidadeTO.setClienteVO(clienteVO1);
                clienteFuncionalidadeTO.setCliente(true);
                clienteFuncionalidadeTO.setApresentarMatCpf(true);
                String nomeFormatado = formatarNome(clienteVO1.getPessoa().getNome());
                clienteVO1.getPessoa().setNome(nomeFormatado);
                clientes.add(clienteFuncionalidadeTO);
            }
        }

        try {
            int numeroContaCorrente = Integer.parseInt(matricula);
            List<ClienteVO> clientesContaCorrente = getFacade()
                    .getCliente()
                    .consultarClientesPorContaCorrente(numeroContaCorrente, null, "pessoa.nome");

            if (!clientesContaCorrente.isEmpty()) {
                for (ClienteVO cliente : clientesContaCorrente) {
                    clientes.add(criarClienteFuncionalidadeContaCorrente(cliente));
                }
            }
        } catch (Exception ignored) {
        }


        if (clienteVO != null && clienteVO.getCodigo() != 0) {

            ClienteFuncionalidadeTO clienteFuncionalidadeTO = new ClienteFuncionalidadeTO();
            clienteFuncionalidadeTO.setClienteVO(clienteVO);
            clienteFuncionalidadeTO.setCliente(true);
            String nomeFormatado = formatarNome(clienteVO.getPessoa().getNome());
            clienteVO.getPessoa().setNome(nomeFormatado);
            clientes.add(clienteFuncionalidadeTO);
            this.clienteEditar = clienteVO;
            mostrarClienteComFuncionlidade(clienteVO, funcionalidades, funcionalidade, clientes);
        }
    }
    private void montarDescricaoConsultaCliente(String param,boolean porMatricula, List<ClienteFuncionalidadeTO> clientes){
        ClienteFuncionalidadeTO descricaoFunc = new ClienteFuncionalidadeTO();
        descricaoFunc.setDescricao(false);
        descricaoFunc.setItemDescricao(porMatricula ? ("Matrícula: "+param) : ("Identificador remessa: "+param));
        descricaoFunc.setCliente(false);
        descricaoFunc.setSubTitulo(true);
        clientes.add(descricaoFunc);
    }
    public void mostrarClienteComFuncionlidade(ClienteVO clienteVO, List<ClienteFuncionalidadeTO> funcionalidades, String valorFuncPesquizar, List<ClienteFuncionalidadeTO> clientes) {
        ClienteFuncionalidadeTO descricaoFunc = new ClienteFuncionalidadeTO();
        descricaoFunc.setDescricao(true);
        descricaoFunc.setItemDescricao("Operações do Cliente:");
        descricaoFunc.setCliente(false);
        clientes.add(descricaoFunc);
        ClienteFuncionalidadeTO clienteFuncionalidadeTO;
        for (FuncionalidadeClienteEnum funcionalidadeClienteEnum : FuncionalidadeClienteEnum.values()) {
            Boolean validarPermissao;
            clienteFuncionalidadeTO = new ClienteFuncionalidadeTO();
            validarPermissao = validarPermissao(funcionalidadeClienteEnum.getExpressaoRenderizar());
            if ((validarPermissao != null && validarPermissao &&
                    (valorEquivalente(valorFuncPesquizar, funcionalidadeClienteEnum.getDescricao(), funcionalidadeClienteEnum.getPalavrasChaves(), clienteFuncionalidadeTO) || valorFuncPesquizar.equals("%")))) {
                clienteFuncionalidadeTO.getFuncionalidadeClienteEnumAux().setFuncionalidadeClienteEnum(funcionalidadeClienteEnum);
                clienteFuncionalidadeTO.setCliente(false);
                funcionalidades.add(clienteFuncionalidadeTO);
            }
        }
        Ordenacao.ordenarLista(funcionalidades, "Ordenacao");
        for (int e = 0; e < funcionalidades.size(); e++) {
            clientes.add(funcionalidades.get(e));
        }
    }

    @SuppressWarnings("unchecked")
    public List<ClienteFuncionalidadeTO> executarAutocompleteFuncionalidade(Object suggest) {
        List<ClienteFuncionalidadeTO> clientes = new ArrayList<>();
        podeConsultarEmTodasEmpresas = permissao("ConsultarAlunosCaixaAbertoTodasEmpresas");
        try {
            String pref = (String) suggest;
            String[] palavras = pref.split(" ");
            //Busca Funcionalidades pelo nome
            if (UteisValidacao.somenteNumeros(palavras[0]))
                pesquisarFuncionalidadesCliente(clientes, pref);
            else
                pesquisaGeralClienteFuncionalidade(clientes, pref);

        } catch (Exception ex) {
            montarErroComLog(ex);
        }
        return clientes;
    }

    public String getRotulo() {
        return rotulo;
    }

    public void setRotulo(String rotulo) {
        this.rotulo = rotulo;
    }

    public ClienteFuncionalidadeTO getFuncSelecionada() {
        if (funcSelecionada == null)
            funcSelecionada = (ClienteFuncionalidadeTO) request().getAttribute("result");
        return funcSelecionada;
    }

    public Boolean getMostrarModalContratosRenovar() {
        return true;
    }

    public void setMostrarModalContratosRenovar(Boolean mostrarModalContratosRenovar) {
        this.mostrarModalContratosRenovar = mostrarModalContratosRenovar;
    }

    public void paintFoto(OutputStream out, Object data) throws Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String pessoa = request.getParameter("pessoa");
        SuperControle.paintFoto(out, clientesFoto.get(Integer.valueOf(pessoa)));
    }
    public String getPaintFotoDaNuvem() {
        ClienteFuncionalidadeTO cliente = (ClienteFuncionalidadeTO) request().getAttribute("result");
        String foto = clientesFotoKey.get(cliente.getClienteVO().getCodigo()) == null ? "" : clientesFotoKey.get(cliente.getClienteVO().getCodigo());
        return getPaintFotoDaNuvem(foto);
    }

    public void setFuncSelecionada(ClienteFuncionalidadeTO funcSelecionada) {
        this.funcSelecionada = funcSelecionada;
    }

    public Boolean getAbrirFuncCliente() {
        return abrirFuncCliente;
    }

    public boolean isPodeConsultarEmTodasEmpresas() {
        return podeConsultarEmTodasEmpresas;
    }

    public void setPodeConsultarEmTodasEmpresas(boolean podeConsultarEmTodasEmpresas) {
        this.podeConsultarEmTodasEmpresas = podeConsultarEmTodasEmpresas;
    }

    public String getFuncionalidadeNome() {
        return funcionalidadeNome;
    }

    public void setFuncionalidadeNome(String funcionalidadeNome) {
        this.funcionalidadeNome = funcionalidadeNome;
    }


    public Map<String, Boolean> getFuncHabilitadas() {
        return funcHabilitadas;
    }

    public void setFuncHabilitadas(Map<String, Boolean> funcHabilitadas) {
        this.funcHabilitadas = funcHabilitadas;
    }

    public String getFuncionalidadeAberta() {
        return funcionalidadeAberta;
    }

    public void setFuncionalidadeAberta(String funcionalidadeAberta) {
        this.funcionalidadeAberta = funcionalidadeAberta;
    }


    public boolean isBancoMultiEmpresa() {
        return bancoMultiEmpresa;
    }

    public void setBancoMultiEmpresa(boolean bancoMultiEmpresa) {
        this.bancoMultiEmpresa = bancoMultiEmpresa;
    }

    public boolean isPerfilAcessoUnificado() {
        return perfilAcessoUnificado;
    }

    public void setPerfilAcessoUnificado(boolean perfilAcessoUnificado) {
        this.perfilAcessoUnificado = perfilAcessoUnificado;
    }
}
