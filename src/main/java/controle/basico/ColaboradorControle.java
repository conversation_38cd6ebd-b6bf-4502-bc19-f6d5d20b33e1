package controle.basico;

import br.com.pactosolucoes.autorizacaocobranca.controle.AutorizacaoCobrancaControle;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ReplicarRedeEmpresaEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.enumeradores.UsoCreditoPersonalEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estudio.controle.ConfiguracaoEstudioControle;
import br.com.pactosolucoes.estudio.modelo.AgendaVO;
import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.ReplicarRedeEmpresaCallable;
import controle.arquitetura.security.UsuarioControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.financeiro.GestaoRemessasControle;
import controle.modulos.integracao.UsuarioMovelControle;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.PessoaConsultaTO;
import negocio.comuns.arquitetura.HorarioAcessoSistemaVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AlunoComissaoColaboradorVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorDocumentoRhVO;
import negocio.comuns.basico.ColaboradorIndisponivelCrmVO;
import negocio.comuns.basico.ColaboradorModalidadeVO;
import negocio.comuns.basico.ColaboradorRedeEmpresaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.DepartamentoVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.LogControleUsabilidadeVO;
import negocio.comuns.basico.ModalidadeComissaoColaboradorVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProfissaoVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.basico.TurmaComissaoColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TipoAssinaturaBiometricaEnum;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoColaboradorEnum;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PlanoPersonalTextoPadraoVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import servicos.bi.exportador.Exportador;
import servicos.bi.exportador.RelatorioBuilder;
import servicos.discovery.RedeDTO;
import servicos.impl.apf.RecorrenciaService;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.impl.pessoaMs.PessoaMsService;
import servicos.integracao.IntegracaoCadastrosWSConsumer;
import servicos.integracao.TreinoWSConsumer;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas colaboradorForm.jsp colaboradorCons.jsp) com as funcionalidades da
 * classe <code>Colaborador</code>. Implemtação da camada controle (Backing
 * Bean).
 *
 * @see SuperControle
 * @see Colaborador
 * @see ColaboradorVO
 */
public class ColaboradorControle extends SuperControle {

    private ColaboradorVO colaboradorVO = new ColaboradorVO();
    private TipoColaboradorVO tipoColaboradorVO;
    private String msgAlert;
    /**
     * Interface <code>ColaboradorInterfaceFacade</code> responsável pela
     * interconexão da camada de controle com a camada de negócio. Criando uma
     * independência da camada de controle com relação a tenologia de
     * persistência dos dados (DesignPatter: Façade).
     */
    private PessoaVO pessoaVO;
    private ProfissaoVO profissaoVO;
    protected List listaSelectItemProfissao;
    protected List listaSelectItemCidade;
    protected List listaSelectItemEstado;
    protected List listaSelectItemPais;
    protected List listaUltimosAcessos;
    private List tipoConsultaCombo;
    private EnderecoVO enderecoVO;
    private TelefoneVO telefoneVO;
    private EmailVO emailVO;
    protected Boolean adicionar;
    protected Boolean transferirColaboradorEmpresa;
    protected ClienteVO clienteVO;
    protected ClienteVO clienteVinculado;
    protected ColaboradorVO copiaColaboradorVO;
    protected List listaSelectItemEmpresa;
    protected Boolean apresentarBotoes;
    private CepControle cepControle;
    private boolean apresentarOpcaoEstornoProduto;
    private Integer nrPaginaMovProduto;
    private Integer nrPaginaMovPagamento;
    private Integer nrPaginaMovParcela;
    private int nrPaginaPlano;
    private List<LogControleUsabilidadeVO> listaUltimosAcessosSistema;
    private Integer nrPaginaUltimosAcessosSistema;
    private Date dataInicio;
    private Date dataTermino;
    private String acao;
    private String mensagemUltimosAcessosSistema;
    private String valorConsultarProduto = "";
    private List<ProdutoVO> listaProdutos = new ArrayList<ProdutoVO>();
    private ModalidadeComissaoColaboradorVO modalidadeComissaoColaboradorVO = new ModalidadeComissaoColaboradorVO();
    private TurmaComissaoColaboradorVO turmaComissaoColaboradorVO = new TurmaComissaoColaboradorVO();
    private AlunoComissaoColaboradorVO alunoComissaoColaboradorVO = new AlunoComissaoColaboradorVO();
    private ModalidadeComissaoColaboradorVO modalidadeTemp = new ModalidadeComissaoColaboradorVO();
    private TurmaComissaoColaboradorVO turmaTemp = new TurmaComissaoColaboradorVO();
    private AlunoComissaoColaboradorVO alunoTemp = new AlunoComissaoColaboradorVO();
    private Boolean campoConsultaSelectItem = false;
    private String urlGoogle = "";
    private List<SelectItem> listaUsoCreditos = new ArrayList<SelectItem>();
    private String onComplete = "";
    private List<ControleTaxaPersonalVO> controlesTaxaPersonal = new ArrayList<ControleTaxaPersonalVO>();
    private boolean foiPersonal = false;
    private ColaboradorIndisponivelCrmVO colaboradorIndisponivelCrmVO;
    private String campoConsultaUsuario;
    private String valorConsultaUsuario;
    private List listaConsultaUsuario;
    private List<String> listaCamposMostrarColaboradorDinamico;
    private List<String> listaCamposObrigatorioColaboradorDinamico;
    private List<SelectItem> listSelectItemDepartamentos;
    private DepartamentoVO departamentoVO;
    private Boolean atualizarAgenda;
    private boolean permiteCancelarPlanoPersonal;
    private ControleTaxaPersonalVO controleTaxaPersonalParaCancelamento;
    private List<MovParcelaVO> parcelasEmAbertoPlanoPersonal = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> parcelasACancelarPlanoPersonal = new ArrayList<MovParcelaVO>();
    private RecorrenciaService recorrenciaService;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String[] displayIdentificadorFront;
    private String situacaoFiltro;
    private String situacaoFiltroTipo;
    private List<VinculoVO> listaVinculosColaborador = new ArrayList<VinculoVO>();
    private TipoColaboradorEnum tipoVinculoListaColaborador;
    private List<SelectItem> listaColaboradoresTrocarVinculos = new ArrayList<SelectItem>();
    private ColaboradorVO antigoColaboradorVinculo = new ColaboradorVO();
    private ColaboradorVO novoColaboradorVinculo = new ColaboradorVO();
    private String origemOperacaoVinculos;
    private ListaPaginadaTO listaTrasacoes;
    private static final Integer LISTA_PAGINADA_LIMIT = 6;
    private ListaPaginadaTO listaTransacoesVerificacao;
    private static final String LISTA_TRANSACOES = "LISTA_TRANSACOES";
    private static final String LISTA_TRANSACOES_VERIFICACAO = "LISTA_TRANSACOES_VERIFICACAO";
    private ListaPaginadaTO listaPix;
    private List<PixVO> listaPixVo;
    private static final String LISTA_PIX = "LISTA_PIX";
    private ColaboradorModalidadeVO colaboradorModalidadeVO;
    private List listaSelectItemColaboradorModalidade;
    private List<SelectItem> listaTamanhoCamisa;
    private List<SelectItem> listaTamanhoCalsa;
    private ColaboradorDocumentoRhVO colaboradorDocumentoRhVO;
    private List<ColaboradorRedeEmpresaVO> listaColaboradorRedeEmpresa;

    private Boolean editarUsuarioMovel = true;

    private static final String SUBDIRETORIO_ARQUIVOS = "relatoriocolaborador";



    public ColaboradorControle() throws Exception {
        inicializarConfiguracaoSistema();
        obterUsuarioLogado();
        inicializarFacades();
        incluirTabelaCadastroColaboradorDinamico();
        consultarListaCamposDinamico();
        novo();
        identificacaoPessoalInternacional();
        setControleConsulta(new ControleConsulta());
        setPermiteCancelarPlanoPersonal(permissao("CancelamentoContrato_Autorizar"));
        setMensagemID("");
        setUrlGoogle("");
        setMensagemDetalhada("");
        listaTrasacoes = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaTransacoesVerificacao = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaPix = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        montarListaSelectItemColaboradorModalidade();
        setEditarUsuarioMovel(true);
    }

    public void inicializarUsuarioLogado() {
        try {
            colaboradorVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void incluirTabelaCadastroColaboradorDinamico() throws Exception {
        getFacade().getCadastroDinamico().incluirTabelaCadastroDinamico("colaborador", Arrays.asList(CadastroDinamicoColaboradorEnum.values()));
    }

    private void consultarListaCamposDinamico() throws Exception {
        this.listaCamposMostrarColaboradorDinamico = getFacade().getCadastroDinamicoItem().consultarCamposMostrar("colaborador", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        this.listaCamposObrigatorioColaboradorDinamico = getFacade().getCadastroDinamicoItem().consultarCamposObrigatorio("colaborador", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public void upload(UploadEvent upload) throws Exception {
        //getPessoaVO().upload(upload, obterCaminhoWebAplicacaoFoto(), getClienteVO().getMatricula());
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        try {
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getColaboradorVO().getPessoa().setFoto(arrayOutputStream.toByteArray());
            getPessoaVO().setFoto(arrayOutputStream.toByteArray());
            arrayOutputStream.close();
            fi.close();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void recarregarFoto() throws Exception {
        JSFUtilities.getResponse().addHeader("Expires", Calendario.anterior(Calendar.DATE, Calendario.hoje()).toString());
        JSFUtilities.getResponse().addDateHeader("Last-Modified", new Date().getTime());
        JSFUtilities.getResponse().addHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0, post-check=0, pre-check=0");
        JSFUtilities.getResponse().addHeader("Pragma", "no-cache");
        if (isFotosNaNuvem()) {
            final String fotoKey = getFacade().getPessoa().obterFotoKey(
                    getPessoaVO().getCodigo());
            if (!UteisValidacao.emptyString(fotoKey)) {
                getColaboradorVO().getPessoa().setFotoKey(fotoKey + "?time=" + getTimeStamp());
            }
        } else {
            getColaboradorVO().getPessoa().setFoto(getFacade().getPessoa().obterFoto(
                    getKey(),
                    getPessoaVO().getCodigo()));
        }
    }

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        if (getColaboradorVO().getPessoa().getFoto() == null || getColaboradorVO().getPessoa().getFoto().length == 0) {
            recarregarFoto();
        }
        SuperControle.paintFoto(out, getColaboradorVO().getPessoa().getFoto());
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getColaboradorVO().getPessoa().getFotoKey());
    }

    public void removerFoto() {
        try {
            getFacade().getPessoa().removerFoto(getKey(), getPessoaVO().getCodigo());
            getPessoaVO().setFoto(null);
            getPessoaVO().setFotoKey(null);
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
        } catch (Exception e) {
            setErro(true);
            setMensagem(e.getMessage());

        }
    }

    public void removerFotoPersonal() {
        getColaboradorVO().setFotoPersonal(null);
    }

    public List<ModalidadeVO> autocompleteModalidade(Object suggest) {
        String pref = (String) suggest;
        try {
            return (ArrayList<ModalidadeVO>) getFacade().getModalidade().consultarPorNomeModalidadeProfessorComLimite(pref, colaboradorVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        } catch (Exception ex) {
            return new ArrayList<ModalidadeVO>();
        }
    }

    public List<TurmaVO> autocompleteTurma(Object suggest) {
        String pref = (String) suggest;
        try {
            return (ArrayList<TurmaVO>) getFacade().getTurma().consultarPorNomeTurmaProfessorComLimite(pref, colaboradorVO.getCodigo());
        } catch (Exception ex) {
            return new ArrayList<TurmaVO>();
        }
    }

    public List<PessoaVO> autocompleteAluno(Object suggest) {
        String pref = (String) suggest;
        try {
            return (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPorNomeAlunoProfessorComLimite(pref, colaboradorVO.getCodigo());
        } catch (Exception ex) {
            return new ArrayList<PessoaVO>();
        }
    }

    public void selecionarModalidade() throws Exception {
        ModalidadeVO obj = (ModalidadeVO) context().getExternalContext().getRequestMap().get("result");
        this.getModalidadeComissaoColaboradorVO().setModalidade(obj);
    }

    public void selecionarTurma() throws Exception {
        TurmaVO obj = (TurmaVO) context().getExternalContext().getRequestMap().get("result");
        this.getTurmaComissaoColaboradorVO().setTurma(obj);
    }

    public void selecionarAluno() throws Exception {
        PessoaVO obj = (PessoaVO) context().getExternalContext().getRequestMap().get("result");
        this.getAlunoComissaoColaboradorVO().setPessoaVO(obj);
    }

    public void adicionarModalidade() {
        try {
            ModalidadeComissaoColaboradorVO.validarDados(getModalidadeComissaoColaboradorVO());
            int index = 0;
            Iterator i = getColaboradorVO().getListaModalidadesComissaoColaboradorVO().iterator();
            while (i.hasNext()) {
                ModalidadeComissaoColaboradorVO objExistente = (ModalidadeComissaoColaboradorVO) i.next();
                if (objExistente.getModalidade().getCodigo().equals(getModalidadeComissaoColaboradorVO().getModalidade().getCodigo())) {
                    for (int j = 0; j < getColaboradorVO().getListaModalidadesComissaoColaboradorVO().size(); j++) {
                        if (j != index) {
                            if (getColaboradorVO().getListaModalidadesComissaoColaboradorVO().get(j).getModalidade().getCodigo() == getModalidadeComissaoColaboradorVO().getModalidade().getCodigo()) {
                                objExistente.setModalidade(getFacade().getModalidade().consultarPorChavePrimaria(modalidadeTemp.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                getColaboradorVO().getListaModalidadesComissaoColaboradorVO().set(index, objExistente);
                                setModalidadeComissaoColaboradorVO(new ModalidadeComissaoColaboradorVO());
                                modalidadeTemp = new ModalidadeComissaoColaboradorVO();
                                throw new Exception("Não é possível adicionar a mesma modalidade mais de uma vez");
                            }
                        }
                    }
                    getColaboradorVO().getListaModalidadesComissaoColaboradorVO().set(index, getModalidadeComissaoColaboradorVO());
                    setModalidadeComissaoColaboradorVO(new ModalidadeComissaoColaboradorVO());
                    setMensagemDetalhada("", "");
                    return;
                }
                index++;
            }
            getColaboradorVO().getListaModalidadesComissaoColaboradorVO().add(getModalidadeComissaoColaboradorVO());
            setModalidadeComissaoColaboradorVO(new ModalidadeComissaoColaboradorVO());
            setMensagemDetalhada("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarTurma() {
        try {
            TurmaComissaoColaboradorVO.validarDados(getTurmaComissaoColaboradorVO());
            int index = 0;
            Iterator i = getColaboradorVO().getListaTurmaComissaoColaboradorVO().iterator();
            while (i.hasNext()) {
                TurmaComissaoColaboradorVO objExistente = (TurmaComissaoColaboradorVO) i.next();
                if (objExistente.getTurma().getCodigo().equals(getTurmaComissaoColaboradorVO().getTurma().getCodigo())) {
                    for (int j = 0; j < getColaboradorVO().getListaTurmaComissaoColaboradorVO().size(); j++) {
                        if (j != index) {
                            if (getColaboradorVO().getListaTurmaComissaoColaboradorVO().get(j).getTurma().getCodigo()
                                    == getTurmaComissaoColaboradorVO().getTurma().getCodigo()) {
                                objExistente.setTurma(getFacade().getTurma().consultarPorChavePrimaria(turmaTemp.getTurma().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                getColaboradorVO().getListaTurmaComissaoColaboradorVO().set(index, objExistente);
                                setTurmaComissaoColaboradorVO(new TurmaComissaoColaboradorVO());
                                turmaTemp = new TurmaComissaoColaboradorVO();
                                throw new Exception("Não é possível adicionar a mesma turma mais de uma vez");
                            }
                        }
                    }
                    getColaboradorVO().getListaTurmaComissaoColaboradorVO().set(index, getTurmaComissaoColaboradorVO());
                    setTurmaComissaoColaboradorVO(new TurmaComissaoColaboradorVO());
                    return;
                }
                index++;
            }
            getColaboradorVO().getListaTurmaComissaoColaboradorVO().add(getTurmaComissaoColaboradorVO());
            setTurmaComissaoColaboradorVO(new TurmaComissaoColaboradorVO());
            setMensagemDetalhada("");
            setMensagemID("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarAluno() {
        try {
            AlunoComissaoColaboradorVO.validarDados(getAlunoComissaoColaboradorVO());
            int index = 0;
            Iterator i = getColaboradorVO().getListaAlunoComissaoColaboradorVOs().iterator();
            while (i.hasNext()) {
                AlunoComissaoColaboradorVO objExistente = (AlunoComissaoColaboradorVO) i.next();
                if (objExistente.getPessoaVO().getCodigo().equals(getAlunoComissaoColaboradorVO().getPessoaVO().getCodigo())) {
                    for (int j = 0; j < getColaboradorVO().getListaAlunoComissaoColaboradorVOs().size(); j++) {
                        if (j != index) {
                            if (getColaboradorVO().getListaAlunoComissaoColaboradorVOs().get(j).getPessoaVO().getCodigo()
                                    == getAlunoComissaoColaboradorVO().getPessoaVO().getCodigo()) {
                                objExistente.setPessoaVO(getFacade().getPessoa().consultarPorChavePrimaria(alunoTemp.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                getColaboradorVO().getListaAlunoComissaoColaboradorVOs().set(index, objExistente);
                                setAlunoComissaoColaboradorVO(new AlunoComissaoColaboradorVO());
                                alunoTemp = new AlunoComissaoColaboradorVO();
                                throw new Exception("Não é possível adicionar o mesmo aluno mais de uma vez");
                            }
                        }
                    }
                    getColaboradorVO().getListaAlunoComissaoColaboradorVOs().set(index, getAlunoComissaoColaboradorVO());
                    setAlunoComissaoColaboradorVO(new AlunoComissaoColaboradorVO());
                    return;
                }
                index++;
            }
            getColaboradorVO().getListaAlunoComissaoColaboradorVOs().add(getAlunoComissaoColaboradorVO());
            setAlunoComissaoColaboradorVO(new AlunoComissaoColaboradorVO());
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void editarModalidade() {
        try {
            ModalidadeComissaoColaboradorVO obj = (ModalidadeComissaoColaboradorVO) context().getExternalContext().getRequestMap().get("modalidadeComissao");
            setModalidadeComissaoColaboradorVO(obj);
            modalidadeTemp = (ModalidadeComissaoColaboradorVO) obj.getClone(true);
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void editarTurma() {
        try {
            TurmaComissaoColaboradorVO obj = (TurmaComissaoColaboradorVO) context().getExternalContext().getRequestMap().get("turmaComissaoVO");
            setTurmaComissaoColaboradorVO(obj);
            turmaTemp = (TurmaComissaoColaboradorVO) obj.getClone(true);
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void editarAluno() {
        try {
            AlunoComissaoColaboradorVO obj = (AlunoComissaoColaboradorVO) context().getExternalContext().getRequestMap().get("alunoComissaoVO");
            setAlunoComissaoColaboradorVO(obj);
            alunoTemp = (AlunoComissaoColaboradorVO) obj.getClone(true);
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void removerModalidade() throws Exception {
        ModalidadeComissaoColaboradorVO obj = (ModalidadeComissaoColaboradorVO) context().getExternalContext().getRequestMap().get("modalidadeComissao");
        excluirObjModalidadeComissaoVOs(obj.getModalidade().getCodigo());
        setSucesso(true);
        setErro(false);
        setMensagemID("msg_dados_excluidos");
    }

    public void excluirObjModalidadeComissaoVOs(Integer modalidade) throws Exception {
        int index = 0;
        Iterator i = getColaboradorVO().getListaModalidadesComissaoColaboradorVO().iterator();
        while (i.hasNext()) {
            ModalidadeComissaoColaboradorVO objExistente = (ModalidadeComissaoColaboradorVO) i.next();
            if (objExistente.getModalidade().getCodigo().equals(modalidade)) {
                getColaboradorVO().getListaModalidadesComissaoColaboradorVO().remove(index);
                return;
            }
            index++;
        }
    }

    public void removerTurma() throws Exception {
        TurmaComissaoColaboradorVO obj = (TurmaComissaoColaboradorVO) context().getExternalContext().getRequestMap().get("turmaComissaoVO");
        excluirObjTurmaComissaoVOs(obj.getTurma().getCodigo());
        setSucesso(true);
        setErro(false);
        setMensagemID("msg_dados_excluidos");
    }

    public void excluirObjTurmaComissaoVOs(Integer turma) throws Exception {
        int index = 0;
        Iterator i = getColaboradorVO().getListaTurmaComissaoColaboradorVO().iterator();
        while (i.hasNext()) {
            TurmaComissaoColaboradorVO objExistente = (TurmaComissaoColaboradorVO) i.next();
            if (objExistente.getTurma().getCodigo().equals(turma)) {
                getColaboradorVO().getListaTurmaComissaoColaboradorVO().remove(index);
                return;
            }
            index++;
        }
    }

    public void removerAluno() throws Exception {
        AlunoComissaoColaboradorVO obj = (AlunoComissaoColaboradorVO) context().getExternalContext().getRequestMap().get("alunoComissaoVO");
        excluirObjAlunoComissaoVOs(obj.getPessoaVO().getCodigo());
        setSucesso(true);
        setErro(false);
        setMensagemID("msg_dados_excluidos");
    }

    public void excluirObjAlunoComissaoVOs(Integer aluno) throws Exception {
        int index = 0;
        Iterator i = getColaboradorVO().getListaAlunoComissaoColaboradorVOs().iterator();
        while (i.hasNext()) {
            AlunoComissaoColaboradorVO objExistente = (AlunoComissaoColaboradorVO) i.next();
            if (objExistente.getPessoaVO().getCodigo().equals(aluno)) {
                getColaboradorVO().getListaAlunoComissaoColaboradorVOs().remove(index);
                return;
            }
            index++;
        }
    }

    public void limparCampoModalidadeSuggestion() {
        this.getModalidadeComissaoColaboradorVO().setModalidade(new ModalidadeVO());
    }

    public void limparCampoTurmaSuggestion() {
        this.getTurmaComissaoColaboradorVO().setTurma(new TurmaVO());
    }

    public void limparCampoAlunoSuggestion() {
        this.getAlunoComissaoColaboradorVO().setPessoaVO(new PessoaVO());
    }

    public void inicializarConfiguracaoSistema() {
        setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
    }

    public String novo() {
        try {
            setColaboradorVO(new ColaboradorVO());
            getColaboradorVO().setNovoObj(true);
            getColaboradorVO().setSituacao("AT");
            setCepControle(new CepControle());
            inicializarUsuarioLogado();
            setAdicionar(false);
            setPessoaVO(new PessoaVO());
            setClienteVO(new ClienteVO());
            setClienteVinculado(new ClienteVO());
            setCopiaColaboradorVO(new ColaboradorVO());
            setEmailVO(new EmailVO());
            setTelefoneVO(new TelefoneVO());

            setEnderecoVO(new EnderecoVO());
            getEnderecoVO().setEnderecoCorrespondencia(true);

            setProfissaoVO(new ProfissaoVO());
            setDepartamentoVO(new DepartamentoVO());
            getPessoaVO().setAdicionar(false);
            setSucesso(false);
            setErro(false);
            setApresentarBotoes(true);
            setTransferirColaboradorEmpresa(false);
            setTipoColaboradorVO(new TipoColaboradorVO());
            inicializarListasSelectItemTodosComboBox();
            setNrPaginaMovPagamento(5);
            setNrPaginaMovParcela(5);
            setNrPaginaMovProduto(5);
            setNrPaginaPlano(5);
            //atributos da aba ultimos acessos do sistema
            setDataInicio(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            setDataTermino(Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje()));
            setValorConsultarProduto("");
            setListaProdutos(new ArrayList<ProdutoVO>());
            setMensagemDetalhada("");
            setAcao("TO");
            setNrPaginaUltimosAcessosSistema(10);
            foiPersonal = false;
            limparMsg();
            UsuarioMovelControle uMovelControl = getControlador(UsuarioMovelControle.class);
            uMovelControl.init(getColaboradorVO());
            uMovelControl.getuMovel().setKey(getKey());
            setColaboradorModalidadeVO(new ColaboradorModalidadeVO());

            try {
                if (!getUsuarioLogado().getAdministrador()) {
                    EmpresaVO empresaVO = getEmpresaLogado();
                    getPessoaVO().setNacionalidade(empresaVO.getPais().getNacionalidade());
                }
            } catch (Exception ignored) {
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void novoConsultor() throws Exception {
        setColaboradorVO(new ColaboradorVO());
        setCepControle(new CepControle());
        inicializarUsuarioLogado();
        setAdicionar(false);
        setPessoaVO(new PessoaVO());
        setClienteVO(new ClienteVO());
        setClienteVinculado(new ClienteVO());
        setCopiaColaboradorVO(new ColaboradorVO());
        setEmailVO(new EmailVO());
        setTelefoneVO(new TelefoneVO());
        setEnderecoVO(new EnderecoVO());
        setProfissaoVO(new ProfissaoVO());
        setDepartamentoVO(new DepartamentoVO());
        getPessoaVO().setAdicionar(false);
        setSucesso(false);
        setErro(false);
        setApresentarBotoes(false);
        setTransferirColaboradorEmpresa(false);
        setTipoColaboradorVO(new TipoColaboradorVO());
        getTipoColaboradorVO().setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());
        adicionarTipoColaborador();
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_entre_dados");
        setMensagemDetalhada("");

    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>Colaborador</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() throws Exception {
        try {
            setAtencao(false);
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            return prepareEditarColaborador(codigoConsulta);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public String prepareEditarColaborador(Integer codigoConsulta) throws Exception {
        ColaboradorVO obj = getFacade().getColaborador().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        setClienteVinculado(new ClienteVO());
        if (obj.getPessoa().getNome().toUpperCase().startsWith("PACTO") && !getUsuarioLogado().getAdministrador()) {
            throw new Exception("Este colaborador não pode ser editado.");
        } else {
            //buscar vínculo com aluno
            setClienteVinculado(getFacade().getCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS));

        }
        prepararEditarColaborador(obj);
        if (isExibirReplicarRedeEmpresa()) {
            prepararListaReplicarEmpresa();
        }
        return "editar";
    }

    public void prepararEditarColaborador(ColaboradorVO obj) throws Exception {
        obj.setConfigurarTempoEntreAcessos(obj.getTempoEntreAcessos() >= 0);
        obj.registrarObjetoVOAntesDaAlteracao();
        obj.getPessoa().registrarObjetoVOAntesDaAlteracao();
        setColaboradorVO(new ColaboradorVO());
        setPessoaVO(new PessoaVO());
        obj.setNovoObj(false);
        setColaboradorVO(obj);
        setPessoaVO(obj.getPessoa());
        getPessoaVO().setAdicionar(false);
        inicializarAtributosRelacionados(colaboradorVO, obj.getPessoa());
        setTelefoneVO(new TelefoneVO());
        setEmailVO(new EmailVO());
        setEnderecoVO(new EnderecoVO());
        if (obj.getPessoa().getEnderecoVOs().isEmpty()) {
            getEnderecoVO().setEnderecoCorrespondencia(true);
        }
        setCepControle(new CepControle());
        inicializarUsuarioLogado();
        inicializarListasSelectItemTodosComboBox();
        setModalidadeComissaoColaboradorVO(new ModalidadeComissaoColaboradorVO());
        setTurmaComissaoColaboradorVO(new TurmaComissaoColaboradorVO());
        setAlunoComissaoColaboradorVO(new AlunoComissaoColaboradorVO());
        setColaboradorIndisponivelCrmVO(new ColaboradorIndisponivelCrmVO());
        foiPersonal = getMostrarUsoCredito();
        obj.setFotoPersonal(getFacade().getColaborador().obterFotoPersonal(obj.getCodigo()));

        if (getColaboradorVO().getCodigo() > 0) {
            UsuarioMovelControle uMovelControl = (UsuarioMovelControle) getControlador(UsuarioMovelControle.class);
            uMovelControl.init(getColaboradorVO());
            uMovelControl.getuMovel().setKey(getKey());
        }

        consultarListaCamposDinamico();
        LoginControle lgControle = (LoginControle) getControlador(LoginControle.class.getSimpleName());
        if (lgControle.isApresentarLinkEstudio() || lgControle.isApresentarLinkCRM()) {
            acaoListarColaboradorIndisp();
        }
        this.colaboradorVO.setListaDocumentoRh(getFacade().getColaboradorDocumentoRh().consultar(colaboradorVO));
        this.colaboradorVO.setColaboradorInfoRhVO(getFacade().getColaboradorInfoRh().consultar(this.colaboradorVO));
        try {
            UsuarioVO usuario = getFacade().getUsuario().
                    consultarPorColaborador(getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(usuario.getCodigo())) {
                setEditarUsuarioMovel(true);
            } else {
                setEditarUsuarioMovel(false);
            }
        }catch (Exception e) {
            setEditarUsuarioMovel(true);
        }

    }

    public void prepararEdicao(ActionEvent evt) throws Exception {
        context().getExternalContext().getRequestMap().put("colaborador",
                evt.getComponent().getAttributes().get("colaborador"));
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>ColaboradorVO</code>. Esta inicialização é necessária por exigência
     * da tecnologia JSF, que não trabalha com valores nulos para estes
     * atributos.
     */
    public void inicializarAtributosRelacionados(ColaboradorVO colaboradorVO, PessoaVO pessoa) {
        if (colaboradorVO.getPessoa() == null) {
            colaboradorVO.setPessoa(new PessoaVO());
        }
        if (pessoaVO.getProfissao() == null) {
            pessoaVO.setProfissao(new ProfissaoVO());
        }
        if (pessoaVO.getCidade() == null) {
            pessoaVO.setCidade(new CidadeVO());
        }
        if (pessoaVO.getPais() == null) {
            pessoaVO.setPais(new PaisVO());
        }
        if (colaboradorVO.getEmpresa() == null) {
            colaboradorVO.setEmpresa(new EmpresaVO());
        }
    }

    public void preencherNacionalidade() {
        try {
            limparMsg();
            if (UteisValidacao.notEmptyNumber(getPessoaVO().getCodigo())) {
                if (getColaboradorVO().getObjetoVOAntesAlteracao() != null && !((ColaboradorVO) getColaboradorVO().getObjetoVOAntesAlteracao()).getEmpresa().getCodigo().equals(getColaboradorVO().getEmpresa().getCodigo())) {
                    if (getFacade().getColaborador().existeColaboradorEmpresaPorCodigoPessoaSituacao(getPessoaVO().getCodigo(), getColaboradorVO().getEmpresa().getCodigo(), null)) {
                        getColaboradorVO().setEmpresa(((ColaboradorVO) getColaboradorVO().getObjetoVOAntesAlteracao()).getEmpresa());
                        throw new Exception("Não é possível realizar a migração de empresa, pois já existe colaborador para essa pessoa na empresa selecionada.");
                    }
                }
            }
            if (UteisValidacao.emptyString(getPessoaVO().getNacionalidade())) {
                if (getColaboradorVO().getEmpresa().getCodigo() > 0) {
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    getPessoaVO().setNacionalidade(empresaVO.getPais().getNacionalidade());
                }
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gravar() throws Exception {
        this.gravar(false);
    }

    public void gravarCE() throws Exception {
        this.gravar(true);
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe <code>Colaborador</code>. Caso o objeto seja novo (ainda não
     * gravado no BD) é acionado a operação <code>incluir()</code>. Caso
     * contrário é acionado o <code>alterar()</code>. Se houver alguma
     * inconsistência o objeto não é gravado, sendo re-apresentado para o
     * usuário juntamente com uma mensagem de erro.
     */
    public void gravar(boolean centralEventos) throws Exception {
        try {
            acaoGravar(centralEventos);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            this.getPessoaVO().setApresentarRichModalErro(false);
            this.colaboradorVO.setApresentarRichModalErro(false);
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    private void acaoGravar(boolean centralEventos) throws Exception {
        boolean eraProfessorTW = false;
        setMsgAlert("");
        if (centralEventos) {
            this.verificarAutorizacao();
        }
        UsuarioMovelControle uMovelControl = getControlador(UsuarioMovelControle.class);
        if (!UteisValidacao.emptyString(uMovelControl.getuMovel().getKey())) {
            validarChaveComSessao(uMovelControl.getuMovel().getKey());
        }
        if (pessoaVO.getNomeFoto() == null || pessoaVO.getNomeFoto().equals("")) {
            pessoaVO.setNomeFoto("fotoPadrao.jpg");
        }
        setCopiaColaboradorVO(new ColaboradorVO());
        colaboradorVO.setPessoa(pessoaVO);
        colaboradorVO.setValidarCamposDinamico(true);
        if (UteisValidacao.emptyNumber(colaboradorVO.getEmpresa().getCodigo())) {
            colaboradorVO.setEmpresa(getEmpresaLogado());
        }
        if(!UteisValidacao.emptyString(colaboradorVO.getCodigoAfiliadoVitio()) && colaboradorVO.getCodigoAfiliadoVitio().split("").length != 6) {
            throw new ConsistirException("O código vitio do colaborador deve ter obrigatóriamente 6 digitos");
        }
        if (colaboradorVO.isNovoObj()) {
            if (!getAdicionar()) {
                consultarCliente();
            }
            colaboradorVO.setPessoa(pessoaVO);
            if ((getClienteVO().getCodigo() == 0) && (getCopiaColaboradorVO().getCodigo() == 0)) {
                getFacade().getColaborador().incluir(colaboradorVO);

                Map<String, String> dadosEvento = new HashMap<>();
                dadosEvento.put("CodigoColaborador", String.valueOf(colaboradorVO.getCodigo()));
                dadosEvento.put("NomeColaborador", colaboradorVO.getPessoa().getNome());

                incluirLogInclusao(true);
                setMensagemID("msg_dados_gravados");
                setSucesso(true);
                setAtencao(false);
                setErro(false);
                this.getPessoaVO().setApresentarRichModalErro(false);
                this.colaboradorVO.setApresentarRichModalErro(false);

            } else if (getAdicionar() && (getCopiaColaboradorVO().getCodigo().intValue() == 0)) {
                getFacade().getColaborador().incluirSemPessoa(colaboradorVO);

                Map<String, String> dadosEvento = new HashMap<String, String>();
                dadosEvento.put("CodigoColaborador", String.valueOf(colaboradorVO.getCodigo()));
                dadosEvento.put("NomeColaborador", colaboradorVO.getPessoa().getNome());

                incluirLogInclusao(false);
                setMensagemID("msg_dados_gravados");
                setSucesso(true);
                setAtencao(false);
                setErro(false);
                this.getPessoaVO().setApresentarRichModalErro(false);
                this.colaboradorVO.setApresentarRichModalErro(false);
            }
        } else {
            consultarColaborador();
            if (getCopiaColaboradorVO().getCodigo() == 0) {

                //Verifica se o colaborador tem o tipo Professor
                ColaboradorVO antesAlteracao = (ColaboradorVO) getColaboradorVO().getObjetoVOAntesAlteracao();
                boolean ehProfessor = false;
                boolean ehEstudio = false;
                boolean verificarAgendamentos = false;
                for (TipoColaboradorVO tipoColaboradorVO : antesAlteracao.getListaTipoColaboradorVOs()) {
                    if (tipoColaboradorVO.getDescricao().equals(TipoColaboradorEnum.ESTUDIO.getSigla())) {
                        verificarAgendamentos = true;
                    }

                    if (TipoColaboradorEnum.PROFESSOR_TREINO.getSigla().equals(tipoColaboradorVO.getDescricao())) {
                        eraProfessorTW = true;
                    }
                }
                for (TipoColaboradorVO tipoColaboradorVO : getColaboradorVO().getListaTipoColaboradorVOs()) {
                    if (tipoColaboradorVO.getDescricao().equals(TipoColaboradorEnum.PROFESSOR.getSigla())) {
                        ehProfessor = true;
                    }
                    if (tipoColaboradorVO.getDescricao().equals(TipoColaboradorEnum.ESTUDIO.getSigla())) {
                        ehEstudio = true;
                        verificarAgendamentos = false;
                    }
                }

                //verifica se pode retirar o tipo colaborador do Estudio ou inativá-lo
                if (verificarAgendamentos || (ehEstudio && getColaboradorVO().getSituacao().equals("NA"))) {
                    List<AgendaVO> agendamentos = getFacade().getAgendaEstudio().buscarAgendaPorColaboradorPeriodo(getColaboradorVO().getCodigo(), Uteis.getSQLData(Calendario.hoje()), null);
                    if (agendamentos != null && !agendamentos.isEmpty()) {
                        if (ehEstudio && getColaboradorVO().getSituacao().equals("NA")) {
                            throw new ConsistirException("Este colaborador está com " + agendamentos.size() + " agendamento(s) futuro(s) e não pode ser desativado. Retire o(s) agendamento(s) futuro(s) desse colaborador antes de realizar essa operacao");
                        } else {
                            throw new ConsistirException("Este colaborador está com " + agendamentos.size() + " agendamento(s) futuro(s) e não pode ser retirado do tipo de colaborar Estúdio. Retire o(s) agendamento(s) futuro(s) desse colaborador antes de realizar essa operacao");
                        }
                    }
                }

                //Verifica se pode inativá-lo
                if (getColaboradorVO().getSituacao().equals("NA") && !getColaboradorVO().getSituacao().equals(antesAlteracao.getSituacao())) {


                    if (ehProfessor) {
                        List<HorarioTurmaVO> horarios = getFacade().getHorarioTurma().consultarColaboradorEmTurmas(getColaboradorVO(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (horarios.size() > 0) {
                            throw new ConsistirException(String.format("O Professor está em %d turmas ativas, e não pode ser desativado.", horarios.size()));
                        }
                    }

                    UsuarioVO usuarioColaborador = getFacade().getUsuario().consultarPorCodigoColaborador(getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if(usuarioColaborador != null && !UteisValidacao.emptyNumber(usuarioColaborador.getCodigo())) {
                        ServicoNotificacaoPush.enviaNotificacaoDesativacaoUsuario(getKey(), getEmpresaLogado().getCodigo(), usuarioColaborador.getCodigo(), getEmpresaLogado().getNome());
                    }

                    if (validarVinculosColaborador()) {
                        return;
                    }
                }

                verificarAgenda();
                colaboradorVO.getPessoa().setEmpresaInternacional(configuracaoSistema.isUsarSistemaInternacional());
                getFacade().getColaborador().alterar(colaboradorVO);
                if (getColaboradorVO().getSituacao().equals("AT") && !getColaboradorVO().getSituacao().equals(antesAlteracao.getSituacao())) {
                    UsuarioVO usuarioColaborador = getFacade().getUsuario().consultarPorCodigoColaborador(getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if(usuarioColaborador != null && !UteisValidacao.emptyNumber(usuarioColaborador.getCodigo())) {
                        ServicoNotificacaoPush.enviaNotificacaoAtivacaoUsuario(getKey(), getEmpresaLogado().getCodigo(), usuarioColaborador.getCodigo(), getEmpresaLogado().getNome());
                    }
                }
                incluirLogAlteracao();
                registrarLogColaboradorIndisponivel(getColaboradorVO().getListaColaboradorIndisponivelCrmVOS(), getColaboradorVO().getListaOriginalColaboradorIndisponivelCrmVOS());
                getColaboradorVO().setListaOriginalColaboradorIndisponivelCrmVOS(getClonarListaColaboradorIndisponivelCrmVOS(getColaboradorVO().getListaColaboradorIndisponivelCrmVOS()));

                if (isExibirReplicarRedeEmpresa()) {
                    prepararListaReplicarEmpresa();
                    replicarAutomaticoTodas();
                }
                setMensagemID("msg_dados_gravados");
                this.getPessoaVO().setApresentarRichModalErro(false);
                this.colaboradorVO.setApresentarRichModalErro(false);
                setSucesso(true);
                setAtencao(false);
                setErro(false);
            }
        }
        //TipoColaboradorVO.validarDados(getTipoColaboradorVO());
        colaboradorVO.registrarObjetoVOAntesDaAlteracao();
        colaboradorVO.getPessoa().registrarObjetoVOAntesDaAlteracao();
        if ((getMostrarUsoCredito() || foiPersonal)
                && UteisValidacao.emptyString(uMovelControl.getuMovel().getNome())) {
            TreinoWSConsumer.adicionarPersonal(getKey(), colaboradorVO.toProfessorSintetico(), colaboradorVO.getEmpresa().getCodigo());
        }
        if ((uMovelControl == null
                || uMovelControl.getuMovel() == null
                || uMovelControl.getuMovel().getNome() == null
                || uMovelControl.getuMovel().getNome().isEmpty()
                || uMovelControl.getuMovel().getColaborador().getCodigo().intValue() == 0)
                && (colaboradorVO.temTipoColaborador(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())
                || eraProfessorTW)) {
            TreinoWSConsumer.sincronizarProfessor(getKey(), colaboradorVO.toProfessorSintetico(), colaboradorVO.getEmpresa().getCodigo());
        }
        if (getMostrarUsoCredito()) {
            reloadOamdImagem(getKey(), colaboradorVO);
        }
        foiPersonal = getMostrarUsoCredito();
        setColaboradorIndisponivelCrmVO(null);
        if (getFacade().getEmpresa().integracaoMyWellnesHabilitada(colaboradorVO.getEmpresa().getCodigo(), true)) {
            getFacade().getZWFacade().startThreadMyWellness(null, colaboradorVO.getCodigo(), false, null);
        }

        enviarGatewayAcesso();
        try {
            if (getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo())) {
                getFacade().getMgbService().sincronizarProfessorMgb(getEmpresaLogado().getCodigo(), colaboradorVO.getCodigo());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void enviarGatewayAcesso() throws Exception {
        RedeEmpresaVO redeEmpresa = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
        if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {

            if (colaboradorVO.isPermitirAcessoRedeEmpresa() && colaboradorVO.getSincronizadoRedeEmpresa() == null) {
                incluirNoGateway(redeEmpresa);
            } else if ((!colaboradorVO.isPermitirAcessoRedeEmpresa() || !colaboradorVO.getSituacao().equals("AT"))
                    && colaboradorVO.getSincronizadoRedeEmpresa() != null) {
                removerNoGateway(redeEmpresa);
            }

        }
    }

    private void removerNoGateway(RedeEmpresaVO redeEmpresa) {
        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = AcessoSistemaMSService
                .findOnCompany(colaboradorVO.getCodigo(),
                        TipoPessoaEnum.COLABORADOR.getTipo(),
                        getKey(),
                        colaboradorVO.getEmpresa().getCodigo(),
                        redeEmpresa);

        if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            AcessoSistemaMSService.deleteAccessAuthorization(autorizacao, redeEmpresa);
        }
    }

    private void incluirNoGateway(RedeEmpresaVO redeEmpresa) throws Exception {
        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = AcessoSistemaMSService
                .findByCPF(colaboradorVO.getPessoa().getCfp(),
                        TipoPessoaEnum.COLABORADOR.getTipo(),
                        redeEmpresa);

        if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            PessoaConsultaTO pessoaConsultaTO = IntegracaoCadastrosWSConsumer
                    .findByAccessCode(autorizacao.getIntegracao().getUrlZillyonWeb(),
                            autorizacao.getIntegracao().getChave(),
                            autorizacao.getCodAcesso(),
                            TipoPessoaEnum.COLABORADOR);
            if (pessoaConsultaTO == null || !pessoaConsultaTO.getSituacao().equals("AT")) {
                AcessoSistemaMSService.deleteAccessAuthorization(autorizacao, redeEmpresa);
            }
        }

        autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO(colaboradorVO);
        if (colaboradorVO.getSituacao().equals("AT")) {
            autorizacao = AcessoSistemaMSService.insertAccessAuthorization(autorizacao, getKey(), colaboradorVO.getEmpresa().getCodigo(), redeEmpresa);
        }

        if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
            getFacade().getColaborador().alterarSincronizadoRedeEmpresa(colaboradorVO.getCodigo());
            colaboradorVO.setSincronizadoRedeEmpresa(Calendario.hoje());
        }
    }

    public void reloadOamdImagem(final String key, ColaboradorVO colaborador) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("codigo", isMostrarFotoParaPersonal() ? colaborador.getCodigo().toString() : colaborador.getPessoa().getCodigo().toString());
            params.put("codigoEmpresaZW", colaborador.getEmpresa().getCodigo().toString());
            ExecuteRequestHttpService.executeHttpRequest(PropsService.getPropertyValue(key, PropsService.urlTreino) + "/prest/usuario/" + key + "/trocarFoto", params);
        } catch (Exception e) {
            Uteis.logar(e, Empresa.class);
        }

    }

    public boolean isExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean usuarioAdministrador = false;
        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
            e.printStackTrace();
        }
        try {
            integranteFranqueadoraRedeEmpresa = OamdMsService.integranteFranqueadoraRedeEmpresa(getKey());
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (integranteFranqueadoraRedeEmpresa && !colaboradorVO.isNovoObj() && (usuarioAdminPacto || usuarioAdministrador)) {
            return true;
        } else {
            return false;
        }
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaColaboradorRedeEmpresa().clear();
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }
                ColaboradorRedeEmpresaVO colaboradorRedeEmpresaVO = getFacade().getColaboradorRedeEmpresa().consultarPorChaveEmpresaColaborador(redeDTO.getChave(), redeDTO.getEmpresaZw(), colaboradorVO.getCodigo());
                if (colaboradorRedeEmpresaVO == null) {
                    colaboradorRedeEmpresaVO = new ColaboradorRedeEmpresaVO();
                    colaboradorRedeEmpresaVO.setMensagemSituacao("AGUARDANDO REPLICAR COLABORADOR");
                }
                colaboradorRedeEmpresaVO.setChaveDestino(redeDTO.getChave().toLowerCase().trim());
                colaboradorRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                colaboradorRedeEmpresaVO.setRedeDTO(redeDTO);
                getListaColaboradorRedeEmpresa().add(colaboradorRedeEmpresaVO);
            }
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(ColaboradorControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarAutomaticoTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (ColaboradorRedeEmpresaVO obj : getListaColaboradorRedeEmpresa()) {
                if (obj.getDataAtualizacaoInformada()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.COLABORADOR, null, obj, null, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (ColaboradorRedeEmpresaVO obj : getListaColaboradorRedeEmpresa()) {
                if (!obj.getDataAtualizacaoInformada()) {
                    obj.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.COLABORADOR, null, obj, null, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (ColaboradorRedeEmpresaVO obj : getListaColaboradorRedeEmpresa()) {
                if (obj.isSelecionado()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.COLABORADOR, null, obj, null, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(ColaboradorRedeEmpresaVO obj : getListaColaboradorRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public void replicarColaboradorRedeEmpresaGeral() {
        ColaboradorRedeEmpresaVO obj = (ColaboradorRedeEmpresaVO) context().getExternalContext().getRequestMap().get("colaboradorRedeEmpresaReplicacao");
        replicarColaboradorRedeEmpresaUnica(obj);
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        ColaboradorRedeEmpresaVO obj = (ColaboradorRedeEmpresaVO) context().getExternalContext().getRequestMap().get("colaboradorRedeEmpresaReplicacao");
        try {
            obj.setDataatualizacao(null);
            obj.setMensagemSituacao("AGUARDANDO REPLICAR COLABORADOR");
            getFacade().getColaboradorRedeEmpresa().limparDataAtualizacao(colaboradorVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getColaboradorRedeEmpresa().alterarMensagemSituacao(colaboradorVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());

        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void replicarColaboradorRedeEmpresaUnica(ColaboradorRedeEmpresaVO obj) {
        try {
            replicarColaboradorRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());
            try {
                getFacade().getColaboradorRedeEmpresa().alterarMensagemSituacao(colaboradorVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());
            } catch (Exception e) {
            }
            Logger.getLogger(ColaboradorControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarColaboradorRedeEmpresa(ColaboradorRedeEmpresaVO obj) throws Exception {
        ColaboradorRedeEmpresaVO colaboradorRedeEmpresaVO = getFacade().getColaboradorRedeEmpresa().consultarPorChaveEmpresaColaborador(obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw(), colaboradorVO.getCodigo());
        if (colaboradorRedeEmpresaVO == null) {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemPessoaMs = redeEmpresaDataDTO.getServiceUrls().getPessoaMsUrl();

            JSONObject cloneColaboradorOrigem = PessoaMsService.clonarColaborador(colaboradorVO.getCodigo(), urlOrigemPessoaMs, getKey());
            colaboradorRedeEmpresaVO = new ColaboradorRedeEmpresaVO(colaboradorVO.getCodigo(), getKey(), obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getColaboradorRedeEmpresa().inserir(colaboradorRedeEmpresaVO);
            // Perfil não tem na outra academia da rede, então inclui
            JSONObject colaboradorReplicado = PessoaMsService.replicarColaborador(cloneColaboradorOrigem, obj.getRedeDTO().getPessoaMsUrl(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString());
            colaboradorRedeEmpresaVO.setChaveDestino(colaboradorReplicado.getString("chaveDestino"));
            colaboradorRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
            colaboradorRedeEmpresaVO.setDataatualizacao(new Date());
            colaboradorRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(colaboradorRedeEmpresaVO.getDataatualizacao()));
            colaboradorRedeEmpresaVO.setColaboradorReplicado(colaboradorReplicado.getInt("codigoColaboradorNovo"));
            obj.setChaveDestino(colaboradorReplicado.getString("chaveDestino"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getColaboradorRedeEmpresa().alterarDataAtualizacao(colaboradorVO.getCodigo(), getKey(), colaboradorReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), colaboradorReplicado.getInt("codigoColaboradorNovo"), obj.getRedeDTO().getEmpresaZw());
        } else {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemPessoaMs = redeEmpresaDataDTO.getServiceUrls().getPessoaMsUrl();

            JSONObject cloneColaboradorOrigem = PessoaMsService.clonarColaborador(colaboradorVO.getCodigo(), urlOrigemPessoaMs, getKey());
            JSONObject colaboradorReplicado = PessoaMsService.replicarColaborador(cloneColaboradorOrigem, obj.getRedeDTO().getPessoaMsUrl(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString());
            colaboradorRedeEmpresaVO.setChaveDestino(colaboradorReplicado.getString("chaveDestino"));
            colaboradorRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
            colaboradorRedeEmpresaVO.setDataatualizacao(new Date());
            colaboradorRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(colaboradorRedeEmpresaVO.getDataatualizacao()));
            colaboradorRedeEmpresaVO.setColaboradorReplicado(colaboradorReplicado.getInt("codigoColaboradorNovo"));
            obj.setChaveDestino(colaboradorReplicado.getString("chaveDestino"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getColaboradorRedeEmpresa().alterarDataAtualizacao(colaboradorVO.getCodigo(), getKey(), colaboradorReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), colaboradorReplicado.getInt("codigoColaboradorNovo"), obj.getRedeDTO().getEmpresaZw());
        }
    }

    public Integer getListaColaboradorRedeEmpresaSize() {
        return getListaColaboradorRedeEmpresa().size();
    }

    public Integer getListaColaboradorRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (ColaboradorRedeEmpresaVO unid : getListaColaboradorRedeEmpresa()) {
            if (unid.getDataAtualizacaoInformada()) {
                cont++;
            }
        }
        return cont;
    }

    public void incluirLogInclusao(boolean incluiuPessoa) throws Exception {
        //LOG - INICIO
        try {
            colaboradorVO.setObjetoVOAntesAlteracao(new ColaboradorVO());
            colaboradorVO.setNovoObj(true);
            registrarLogObjetoVO(colaboradorVO, colaboradorVO.getCodigo(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            if (incluiuPessoa) {
                colaboradorVO.getPessoa().setObjetoVOAntesAlteracao(new PessoaVO());
                colaboradorVO.getPessoa().setNovoObj(true);
                registrarLogObjetoVO(colaboradorVO.getPessoa(), colaboradorVO.getCodigo(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            } else {
                registrarLogObjetoVO(colaboradorVO.getPessoa(), colaboradorVO.getCodigo(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("COLABORADOR", colaboradorVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE COLABORADOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        colaboradorVO.setNovoObj(false);
        colaboradorVO.registrarObjetoVOAntesDaAlteracao();
        colaboradorVO.getPessoa().setNovoObj(false);
        colaboradorVO.getPessoa().registrarObjetoVOAntesDaAlteracao();
        incluirLogColaboradorRh();
        //LOG - FIM
    }

    public void incluirLogExclusao(boolean excluiPessoa) throws Exception {
        //LOG - INICIO
        try {
            colaboradorVO.setObjetoVOAntesAlteracao(new ColaboradorVO());
            colaboradorVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(colaboradorVO, colaboradorVO.getCodigo(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            if (excluiPessoa) {
                colaboradorVO.getPessoa().setObjetoVOAntesAlteracao(new PessoaVO());
                colaboradorVO.getPessoa().setNovoObj(true);
                registrarLogExclusaoTodosDadosObjetoVO(colaboradorVO.getPessoa(), colaboradorVO.getCodigo(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("COLABORADOR", colaboradorVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE COLABORADOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
    }

    public void incluirLogAlteracao() throws Exception {
        //LOG - INICIO
        try {
            registrarLogObjetoVO(colaboradorVO, colaboradorVO.getCodigo(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            registrarLogObjetoVO(colaboradorVO.getPessoa(), colaboradorVO.getCodigo().intValue(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            incluirLogColaboradorRh();

        } catch (Exception e) {
            registrarLogErroObjetoVO("COLABORADOR", colaboradorVO.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE COLABORADOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        colaboradorVO.registrarObjetoVOAntesDaAlteracao();
        colaboradorVO.getPessoa().registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM

    }

    private void incluirLogColaboradorRh()throws Exception{
        if ((colaboradorVO.getColaboradorInfoRhVO().informouAlgumCampo()) || ((colaboradorVO.getColaboradorInfoRhVO().getCodigo() != null) && (colaboradorVO.getColaboradorInfoRhVO().getCodigo()>0))) {
            registrarLogObjetoVO(colaboradorVO.getColaboradorInfoRhVO(), colaboradorVO.getCodigo().intValue(), "COLABORADOR-Aba RH", colaboradorVO.getPessoa().getCodigo());
            colaboradorVO.getColaboradorInfoRhVO().registrarObjetoVOAntesDaAlteracao();
        }
        if (this.colaboradorVO.getListaDocumentoRh() != null){
            for (ColaboradorDocumentoRhVO colaboradorDocumentoRhVO:this.colaboradorVO.getListaDocumentoRh()){
                // registrar log da inclusão do anexo.
                registrarLogObjetoVO(colaboradorDocumentoRhVO, colaboradorVO.getCodigo().intValue(), "COLABORADOR-Aba RH - Anexo Documentos", colaboradorVO.getPessoa().getCodigo());
                colaboradorDocumentoRhVO.registrarObjetoVOAntesDaAlteracao();
            }
        }

    }


    public void gravarColaboradorAPartirDoFormProfessor() throws Exception {
        try {
            if (pessoaVO.getNomeFoto() == null || pessoaVO.getNomeFoto().equals("")) {
                pessoaVO.setNomeFoto("fotoPadrao.jpg");
            }
            setCopiaColaboradorVO(new ColaboradorVO());
            colaboradorVO.setPessoa(pessoaVO);
            if (colaboradorVO.isNovoObj().booleanValue()) {
                if (!getAdicionar()) {
                    consultarCliente();
                }
                colaboradorVO.setPessoa(pessoaVO);
                if ((getClienteVO().getCodigo().intValue() == 0) && (getCopiaColaboradorVO().getCodigo().intValue() == 0)) {
                    getFacade().getColaborador().incluir(colaboradorVO);
                    setMensagemID("msg_dados_gravados");
                    setSucesso(true);
                    setErro(false);
                    this.getPessoaVO().setApresentarRichModalErro(false);
                    this.colaboradorVO.setApresentarRichModalErro(false);

                } else if (getAdicionar() && (getCopiaColaboradorVO().getCodigo().intValue() == 0)) {
                    getFacade().getColaborador().incluirSemPessoa(colaboradorVO);
                    setMensagemID("msg_dados_gravados");
                    setSucesso(true);
                    setErro(false);

                    this.getPessoaVO().setApresentarRichModalErro(false);
                    this.colaboradorVO.setApresentarRichModalErro(false);
                }
            } else {
                consultarColaborador();
                if (getCopiaColaboradorVO().getCodigo() == 0) {
                    getFacade().getColaborador().alterar(colaboradorVO);

                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(colaboradorVO, colaboradorVO.getPessoa().getCodigo());
                        registrarLogObjetoVO(colaboradorVO.getPessoa(), colaboradorVO.getCodigo().intValue(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("COLABORADOR", colaboradorVO.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE COLABORADOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM

                    setMensagemID("msg_dados_gravados");
                    this.getPessoaVO().setApresentarRichModalErro(false);
                    this.colaboradorVO.setApresentarRichModalErro(false);
                    setSucesso(true);
                    setErro(false);
                }
            }
            colaboradorVO.registrarObjetoVOAntesDaAlteracao();
            colaboradorVO.getPessoa().registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            this.getPessoaVO().setApresentarRichModalErro(false);
            this.colaboradorVO.setApresentarRichModalErro(false);
            setSucesso(false);
            setErro(true);
        }
    }

    public void gravarColaboradorTrocandoEmpresa() throws Exception {
        try {
            if (getTransferirColaboradorEmpresa()) {
                getColaboradorVO().setPessoa(getPessoaVO());
            } else {
                getCopiaColaboradorVO().getEmpresa().setCodigo(colaboradorVO.getEmpresa().getCodigo().intValue());
                getCopiaColaboradorVO().getPessoa().setAdicionar(true);
                setColaboradorVO(getCopiaColaboradorVO());
                setPessoaVO(getCopiaColaboradorVO().getPessoa());
            }
            getFacade().getColaborador().alterar(getColaboradorVO());
            //LOG - INICIO
            try {
                registrarLogObjetoVO(colaboradorVO, colaboradorVO.getPessoa().getCodigo());
                registrarLogObjetoVO(colaboradorVO.getPessoa(), colaboradorVO.getCodigo().intValue(), "COLABORADOR", colaboradorVO.getPessoa().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("COLABORADOR", colaboradorVO.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE COLABORADOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            //LOG - FIM

            getPessoaVO().setApresentarRichModalErro(false);
            getColaboradorVO().setApresentarRichModalErro(false);

            setSucesso(true);
            setErro(false);
            setTelefoneVO(new TelefoneVO());
            setEnderecoVO(new EnderecoVO());
            setEmailVO(new EmailVO());
            inicializarAtributosRelacionados(colaboradorVO, colaboradorVO.getPessoa());
            getPessoaVO().setAdicionar(false);
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            setMensagemID("msg_entre_dados");
            colaboradorVO.registrarObjetoVOAntesDaAlteracao();
            colaboradorVO.getPessoa().registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setTransferirColaboradorEmpresa(true);
            getPessoaVO().setApresentarRichModalErro(false);
            getColaboradorVO().setApresentarRichModalErro(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);

        }
    }

    public void editarColaborador() throws Exception {
        try {
            setColaboradorVO(getCopiaColaboradorVO());
            setPessoaVO(getCopiaColaboradorVO().getPessoa());
            getColaboradorVO().registrarObjetoVOAntesDaAlteracao();
            getColaboradorVO().getPessoa().registrarObjetoVOAntesDaAlteracao();
            getPessoaVO().setApresentarRichModalErro(false);
            getColaboradorVO().setApresentarRichModalErro(false);

            setSucesso(true);
            setErro(false);
            setTelefoneVO(new TelefoneVO());
            setEnderecoVO(new EnderecoVO());
            setEmailVO(new EmailVO());
            inicializarAtributosRelacionados(colaboradorVO, colaboradorVO.getPessoa());
            getPessoaVO().setAdicionar(false);
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            setMensagemID("msg_entre_dados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);

        }
    }

    public void gravarProfissao() {
        try {
            getFacade().getProfissao().incluir(profissaoVO);
            montarListaSelectItemProfissao();
            pessoaVO.getProfissao().setCodigo(profissaoVO.getCodigo());
            setProfissaoVO(new ProfissaoVO());
        } catch (Exception e) {
        }
    }

    public void gravarDepartamento() {
        try {
            if (UteisValidacao.emptyNumber(colaboradorVO.getEmpresa().getCodigo())) {
                colaboradorVO.setEmpresa(getEmpresaLogado());
            }
            getDepartamentoVO().setEmpresaVO(getColaboradorVO().getEmpresa());
            getFacade().getDepartamento().incluir(getDepartamentoVO());
            montarListaDepartamentos();
            colaboradorVO.getDepartamentoVO().setCodigo(getDepartamentoVO().getCodigo());
            setDepartamentoVO(new DepartamentoVO());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * ColaboradorCons.jsp. Define o tipo de consulta a ser executada, por meio
     * de ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getColaborador().consultarPorCodigo(0, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    ColaboradorVO colaborador = getFacade().getColaborador().consultarColaboradorPorCodigo(valorInt, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (colaborador != null) {
                        objs.add(colaborador);
                    }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                objs = getFacade().getColaborador().consultarPorNomePessoa(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("empresa")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getColaborador().consultarPorCodigoEmpresa(valorInt, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("cfp")) {
                objs = getFacade().getColaborador().consultarPorCfp(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacao")) {
                if (getControleConsulta().getValorConsulta().equals("inativo")) {
                    getControleConsulta().setValorConsulta("N");
                }
                if (getControleConsulta().getValorConsulta().equals("ativo")) {
                    getControleConsulta().setValorConsulta("A");
                }
                objs = getFacade().getColaborador().consultarPorSituacao(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoProfissao")) {
                objs = getFacade().getColaborador().consultarPorDescricaoProfissao(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (getControleConsulta().getCampoConsulta().equals("tipoColaborador")) {
                TipoColaboradorEnum tipoColaborador = TipoColaboradorEnum.getTipo(getControleConsulta().getValorConsulta());
                objs = getFacade().getColaborador().consultarPorTipoColaborador(tipoColaborador, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void excluir() {
        this.excluir(false);
    }

    public void excluirCE() {
        this.excluir(true);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>ColaboradorVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            ClienteVO clientePessoa = getFacade().getCliente().consultarPorCodigoPessoa(colaboradorVO.getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyNumber(clientePessoa.getCodigo())) {
                if (centralEventos) {
                    this.verificarAutorizacao();

                }
                getFacade().getColaborador().excluirSemPessoa(colaboradorVO);

            } else {
                getFacade().getColaborador().excluir(colaboradorVO);
                TreinoWSConsumer.alterarProfessor(getKey(), colaboradorVO.toProfessorSintetico(), colaboradorVO.getEmpresa().getCodigo());
            }


            incluirLogExclusao(UteisValidacao.emptyNumber(clientePessoa.getCodigo()));
            novo();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            //LOG - FIM
            redirect("/faces/colaboradorCons.jsp");
            return "consultar";
        } catch (Exception e) {

            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"colaborador\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"colaborador\" violates foreign key")) {
                setMensagemDetalhada("Este colaborador não pode ser excluído, pois está sendo utilizado!");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    public String montarListaHistoricoCompras() throws Exception {
        try {
            setApresentarOpcaoEstornoProduto(false);

            getClienteVO().setListaHistoricoPagamento(new ArrayList<MovPagamentoVO>());
            getClienteVO().setListaHistoricoProduto(new ArrayList<MovProdutoVO>());
            getClienteVO().setListaParcelas(new ArrayList<MovParcelaVO>());

            getClienteVO().setListaHistoricoProduto(Ordenacao.ordenarListaReverse(getFacade().getMovProduto().consultarPorCodigoPessoaParaHistoricoCompras(getColaboradorVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS), "codigo"));
            getClienteVO().setListaHistoricoPagamento(Ordenacao.ordenarListaReverse(getFacade().getMovPagamento().consultarPorCodigoClienteParaHistoricoPagamento(getColaboradorVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR), "codigo"));
            getClienteVO().setListaParcelas(Ordenacao.ordenarListaReverse(getFacade().getMovParcela().consultarParcelasColaboradorPorCodigoPessoa(getColaboradorVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS), "dataVencimento"));

            setControlesTaxaPersonal(getFacade().getControleTaxaPersonal().consultarPorPersonal(getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, true));
            processaProdutos();
            return "";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    private void processaProdutos() {
        for (MovProdutoVO mov : getClienteVO().getListaHistoricoProduto()) {
            if (mov.getProduto().getTipoProduto().equals("PE")
                    || mov.getProduto().getTipoProduto().equals("SE")
                    || mov.getProduto().getTipoProduto().equals("DI")
                    || mov.getProduto().getTipoProduto().equals("AA")
                    || mov.getProduto().getTipoProduto().equals("TP")
                    || mov.getProduto().getTipoProduto().equals("SS")
                    || mov.getProduto().getTipoProduto().equals("CP")) {
                setApresentarOpcaoEstornoProduto(true);
                break;
            }
            if (mov.getSituacao().equalsIgnoreCase("EA")) {
                mov.setMudarCorSituacaoEmAberto("red");
            }
        }
    }

    public void consultarCliente() throws Exception {
        if (!this.getPessoaVO().getCfp().equals("") || this.getPessoaVO().getDataNasc() != null) {
            consultarColaborador();
            if (!this.colaboradorVO.getApresentarRichModalErro() && !this.getPessoaVO().getCfp().equals("")) {
                ClienteVO obj = getFacade().getCliente().consultarPorCfp(this.getPessoaVO().getCfp(), Uteis.NIVELMONTARDADOS_TODOS);
                montarDadosClienteSeEleExistir(obj);
            } else if (!this.colaboradorVO.getApresentarRichModalErro() && this.getPessoaVO().getDataNasc() != null) {
                ClienteVO obj = getFacade().getCliente().consultarExisteCliente(this.getPessoaVO().getNome(), this.getPessoaVO().getDataNasc(), Uteis.NIVELMONTARDADOS_TODOS);
                montarDadosClienteSeEleExistir(obj);
            }
        }
    }

    public void montarDadosClienteSeEleExistir(ClienteVO obj) {
        if (obj.getCodigo().intValue() != 0) {
            this.getPessoaVO().setApresentarRichModalErro(true);
            setClienteVO(obj);
            setMensagemID("");
            setMensagemDetalhada("");
            setMensagem("");
            setErro(false);
        } else {
            this.getPessoaVO().setApresentarRichModalErro(false);
            setClienteVO(new ClienteVO());
        }

    }

    public void consultarColaborador() throws Exception {
        if (!this.getPessoaVO().getCfp().equals("")) {
            ColaboradorVO obj = new ColaboradorVO();
            colaboradorVO.setMsgErroExisteColaborador("");
            if ((colaboradorVO.getCodigo() == null) || (colaboradorVO.getCodigo().intValue() == 0)) {
                montarDadosColaboradorInsercaoSeEleExistirPorCPF(obj);
            } else {
                montarDadosColaboradorAlteracaoSeEleExistirPorCPF(obj);
            }
        } else if (this.getPessoaVO().getDataNasc() != null) {
            ColaboradorVO obj = new ColaboradorVO();
            colaboradorVO.setMsgErroExisteColaborador("");
            if ((colaboradorVO.getCodigo() == null) || (colaboradorVO.getCodigo().intValue() == 0)) {
                montarDadosColaboradorInsercaoSeEleExistirPorDataNasc(obj);
            } else {
                montarDadosColaboradorAlteracaoSeEleExistirPorNomeDataNasc(obj);
            }
        }
    }

    public void montarDadosColaboradorInsercaoSeEleExistirPorCPF(ColaboradorVO obj) throws Exception {
        obj = new Colaborador().consultarPorCfp(this.getPessoaVO().getCfp(), Uteis.NIVELMONTARDADOS_TODOS);
        if (obj.getCodigo().intValue() != 0) {
            validarDadosColaboradorInsercaoSeEleExistir(obj);
            colaboradorVO.setMsgErroExisteColaborador("O Colaborador de nome " + obj.getPessoa().getNome().toUpperCase() + " e o CPF " + getPessoaVO().getCfp() + " já está CADASTRADO na EMPRESA " + obj.getEmpresa().getNome() + ".");
        } else {
            this.colaboradorVO.setApresentarRichModalErro(false);
        }
    }

    public void montarDadosColaboradorInsercaoSeEleExistirPorDataNasc(ColaboradorVO obj) throws Exception {
        obj = new Colaborador().consultarExisteColaborador(this.getPessoaVO().getNome(), this.getPessoaVO().getDataNasc(), Uteis.NIVELMONTARDADOS_TODOS);
        if (obj.getCodigo().intValue() != 0) {
            validarDadosColaboradorInsercaoSeEleExistir(obj);
            colaboradorVO.setMsgErroExisteColaborador("O Colaborador de nome " + obj.getPessoa().getNome().toUpperCase() + " e a Data de Nascimento " + getPessoaVO().getDataNasc_Apresentar() + " já está CADASTRADO na EMPRESA " + obj.getEmpresa().getNome() + ".");
        } else {
            this.colaboradorVO.setApresentarRichModalErro(false);
        }
    }

    public void montarDadosColaboradorAlteracaoSeEleExistirPorCPF(ColaboradorVO obj) throws Exception {
        obj = new Colaborador().consultarPorCfp(this.getPessoaVO().getCfp(), Uteis.NIVELMONTARDADOS_TODOS);
        if ((obj.getCodigo().intValue() != 0 && !colaboradorVO.getCodigo().equals(obj.getCodigo().intValue())) &&
                (!(obj.getPessoa().getCodigo().equals(colaboradorVO.getPessoa().getCodigo())))) {
            this.colaboradorVO.setApresentarRichModalErro(true);
            setCopiaColaboradorVO(obj);
            colaboradorVO.setMsgErroExisteColaborador("O Colaborador de nome " + obj.getPessoa().getNome().toUpperCase() + " e o CPF " + getPessoaVO().getCfp() + " já está CADASTRADO na EMPRESA " + obj.getEmpresa().getNome() + ".");
        } else {
            this.colaboradorVO.setApresentarRichModalErro(false);
        }
    }

    public void montarDadosColaboradorAlteracaoSeEleExistirPorNomeDataNasc(ColaboradorVO obj) throws Exception {
        obj = new Colaborador().consultarExisteColaborador(this.getPessoaVO().getNome(), this.getPessoaVO().getDataNasc(), Uteis.NIVELMONTARDADOS_TODOS);
        if (obj.getCodigo().intValue() != 0 && !colaboradorVO.getCodigo().equals(obj.getCodigo().intValue()) && !colaboradorVO.getPessoa().getCodigo().equals(obj.getPessoa().getCodigo())) {
            this.colaboradorVO.setApresentarRichModalErro(true);
            setCopiaColaboradorVO(obj);
            colaboradorVO.setMsgErroExisteColaborador("O Colaborador de nome " + obj.getPessoa().getNome().toUpperCase() + " e a Data de Nascimento " + getPessoaVO().getDataNasc_Apresentar() + " já está CADASTRADO na EMPRESA " + obj.getEmpresa().getNome() + ".");
        } else {
            this.colaboradorVO.setApresentarRichModalErro(false);
        }
    }

    public void validarDadosColaboradorInsercaoSeEleExistir(ColaboradorVO obj) throws Exception {
        this.colaboradorVO.setApresentarRichModalErro(true);
        setCopiaColaboradorVO(obj);
        if (getEmpresaLogado().getCodigo().intValue() != 0 && getEmpresaLogado().getCodigo().equals(obj.getEmpresa().getCodigo().intValue())) {
            colaboradorVO.setApresentarBotaoTransferirColaboradorEmpresa(false);
        } else if (getColaboradorVO().getEmpresa().getCodigo().intValue() != 0 && getColaboradorVO().getEmpresa().getCodigo().equals(obj.getEmpresa().getCodigo().intValue())) {
            colaboradorVO.setApresentarBotaoTransferirColaboradorEmpresa(false);
        } else if (getColaboradorVO().getEmpresa().getCodigo().intValue() == 0) {
            colaboradorVO.setApresentarBotaoTransferirColaboradorEmpresa(false);
        } else {
            colaboradorVO.setApresentarBotaoTransferirColaboradorEmpresa(true);
        }
        setMensagemID("");
        setMensagemDetalhada("");
        setMensagem("");
        setErro(false);
    }

    public void adicionarPessoa() throws Exception {
        this.setPessoaVO(getClienteVO().getPessoa());
        pessoaVO.registrarObjetoVOAntesDaAlteracao();
        montarListaSelectItemPais();
        montarListaSelectItemEstado();
        montarListaSelectItemCidade();
        setAdicionar(true);
    }

    public void setarFalso() throws Exception {
        this.getPessoaVO().setApresentarRichModalErro(false);
    }

    public void adicionarTelefone() throws Exception {
        try {
            if (!getPessoaVO().getCodigo().equals(new Integer(0))) {
                telefoneVO.setPessoa(getPessoaVO().getCodigo());
            }
            if(configuracaoSistema.isUsarSistemaInternacional()) {
                formatarTelefones();
            }
            getTelefoneVO().setUsarSistemaInternacional(configuracaoSistema.isUsarSistemaInternacional());
            getPessoaVO().adicionarObjTelefoneVOs(getTelefoneVO());
            this.setTelefoneVO(new TelefoneVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void editarTelefone() throws Exception {
        TelefoneVO obj = (TelefoneVO) context().getExternalContext().getRequestMap().get("telefone");
        setTelefoneVO(obj);
        setSucesso(true);
        setErro(false);
    }

    public void removerTelefone() throws Exception {
        TelefoneVO obj = (TelefoneVO) context().getExternalContext().getRequestMap().get("telefone");
        getPessoaVO().excluirObjTelefoneVOs(obj.getNumero());
        setMensagemID("msg_dados_excluidos");
        setSucesso(true);
        setErro(false);
    }

    public void adicionarEndereco() throws Exception {
        try {
            if (!getPessoaVO().getCodigo().equals(new Integer(0))) {
                enderecoVO.setPessoa(getPessoaVO().getCodigo());
            }
            getPessoaVO().adicionarObjEnderecoVOs(getEnderecoVO());
            this.setEnderecoVO(new EnderecoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setErro(false);
            setAtencao(true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void editarEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO) context().getExternalContext().getRequestMap().get("endereco");
        setEnderecoVO(obj);
        setSucesso(true);
        setErro(false);
    }

    public void removerEndereco() throws Exception {
        EnderecoVO obj = (EnderecoVO) context().getExternalContext().getRequestMap().get("endereco");
        getPessoaVO().excluirObjEnderecoVOs(obj);
        setSucesso(true);
        setErro(false);
        setMensagemID("msg_dados_excluidos");
    }

    public void adicionarEmail() throws Exception {
        try {
            if (!getPessoaVO().getCodigo().equals(new Integer(0))) {
                emailVO.setPessoa(getPessoaVO().getCodigo());
            }
            getPessoaVO().adicionarObjEmailVOs(getEmailVO());
            this.setEmailVO(new EmailVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }

    }

    public void editarEmail() throws Exception {
        EmailVO obj = (EmailVO) context().getExternalContext().getRequestMap().get("email");
        setEmailVO(obj);
        setSucesso(true);
        setErro(false);
    }

    public void removerEmail() throws Exception {
        EmailVO obj = (EmailVO) context().getExternalContext().getRequestMap().get("email");
        getPessoaVO().excluirObjEmailOs(obj);
        setMensagemID("msg_dados_excluidos");
        setSucesso(true);
        setErro(false);
    }

    public void adicionarTipoColaborador() throws Exception {
        try {
            if (!getColaboradorVO().getCodigo().equals(new Integer(0))) {
                colaboradorVO.setCodigo(getColaboradorVO().getCodigo());
            }
            getColaboradorVO().adicionarObjTipoColaboradorVOs(getTipoColaboradorVO());
            this.setTipoColaboradorVO(new TipoColaboradorVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void inicializarTipoColaborador() throws Exception {
        try {
            setColaboradorVO(new ColaboradorVO());
            inicializarUsuarioLogado();
            setAdicionar(false);
            setPessoaVO(new PessoaVO());
            setClienteVO(new ClienteVO());
            setClienteVinculado(new ClienteVO());
            setCopiaColaboradorVO(new ColaboradorVO());
            setEmailVO(new EmailVO());
            setTelefoneVO(new TelefoneVO());
            setEnderecoVO(new EnderecoVO());
            setProfissaoVO(new ProfissaoVO());
            setDepartamentoVO(new DepartamentoVO());
            getPessoaVO().setAdicionar(false);
            setSucesso(false);
            setErro(false);
            setApresentarBotoes(false);
            setTransferirColaboradorEmpresa(false);
            inicializarListasSelectItemTodosComboBox();
            setTipoColaboradorVO(new TipoColaboradorVO());
            getTipoColaboradorVO().setDescricao(TipoColaboradorEnum.PROFESSOR.getSigla());
            getColaboradorVO().adicionarObjTipoColaboradorVOs(getTipoColaboradorVO());
            setTipoColaboradorVO(new TipoColaboradorVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void editarTipoColaboradorVO() throws Exception {
        TipoColaboradorVO obj = (TipoColaboradorVO) context().getExternalContext().getRequestMap().get("tipoColaboradorVO");
        setTipoColaboradorVO(obj);
        setSucesso(true);
        setErro(false);
    }

    public void removerTipoColaborador() throws Exception {
        try {
            TipoColaboradorVO obj = (TipoColaboradorVO) context().getExternalContext().getRequestMap().get("tipoColaboradorVO");
            if (!obj.getColaborador().equals(0) && getFacade().getVinculo().existeVinculoCodigoColaboradorTipoVinculo(getColaboradorVO().getCodigo(), obj.getDescricao())) {
                String empresas = getFacade().getVinculo().consultarEmpresasVinculadas(getColaboradorVO().getCodigo());
                throw new ConsistirException("Colaborador possui vinculos do Tipo " + obj.getDescricao_Apresentar() + ". Acesse o Organizador de Carteiras  na(s) empresa(s) "+ empresas + " e retire os vinculos desse tipo antes de fazer essa operação. Aceesse Casos importantes e busque em clientes com vículos!  ");
            }
            getColaboradorVO().excluirObjTipoColaboradorVOs(obj.getDescricao());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarCEPCadastroCompleto() throws Exception {
        try {
            getCepControle().consultarCEPCadastroCompleto(getEnderecoVO().getCep());
            getEnderecoVO().setBairro(getCepControle().getCepVO().getBairroDescricao().trim());
            getEnderecoVO().setEndereco(getCepControle().getCepVO().getEnderecoLogradouro().trim());
            getEnderecoVO().setComplemento(getCepControle().getCepVO().getEnderecoCompleto().trim());
            setMensagemDetalhada("");
            setMensagemID("");
            setMensagem("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarCep() {
        // usa a variável de tela
        CepVO obj = (CepVO) context().getExternalContext().getRequestMap().get("cep");
        // atualiza os campos correspondentes
        getEnderecoVO().setCep(obj.getEnderecoCep());
        getEnderecoVO().setEndereco(obj.getEnderecoLogradouro());
        getEnderecoVO().setBairro(obj.getBairroDescricao());
    }

    public List getListaSelectItemTipoTelefoneTelefone() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        for (TipoTelefoneEnum tipoTelefone : TipoTelefoneEnum.values()) {
            String value = tipoTelefone.getCodigo();
            String label = tipoTelefone.getDescricao();
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemTipoEnderecoEndereco() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        for (TipoEnderecoEnum obj : TipoEnderecoEnum.values()) {
            String value = obj.getCodigo();
            String label = obj.getDescricao();
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemSexoPessoa() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable sexos = (Hashtable) Dominios.getSexo();
        Enumeration keys = sexos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) sexos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemGeneroPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable generos = (Hashtable) Dominios.getGenero();
        Enumeration keys = generos.keys();

        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) generos.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);

        return objs;
    }

    public List getListaSelectItemEstadoCivilPessoa() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable estadoCivils = (Hashtable) Dominios.getEstadoCivil();
        Enumeration keys = estadoCivils.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) estadoCivils.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemRgUfPessoa() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable estados = (Hashtable) Dominios.getEstado();
        Enumeration keys = estados.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) estados.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getListaSelectItemSituacaoColaborador() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable situacaoColaborador = (Hashtable) Dominios.getSituacaoColaborador();
        Enumeration keys = situacaoColaborador.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoColaborador.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaTamanhoCamisa(){
        if (this.listaTamanhoCamisa == null){
            this.listaTamanhoCamisa = new ArrayList();
            this.listaTamanhoCamisa.add(new SelectItem("", ""));
            this.listaTamanhoCamisa.add(new SelectItem("PP", "PP"));
            this.listaTamanhoCamisa.add(new SelectItem("P", "P"));
            this.listaTamanhoCamisa.add(new SelectItem("M", "M"));
            this.listaTamanhoCamisa.add(new SelectItem("G", "G"));
            this.listaTamanhoCamisa.add(new SelectItem("GG", "GG"));
            this.listaTamanhoCamisa.add(new SelectItem("XG", "XG"));
            this.listaTamanhoCamisa.add(new SelectItem("EG", "EG"));
            this.listaTamanhoCamisa.add(new SelectItem("EGG", "EGG"));
        }
        return this.listaTamanhoCamisa;
    }

    public List getListaTamanhoCalca(){
        if (this.listaTamanhoCalsa == null){
            this.listaTamanhoCalsa = new ArrayList();
            this.listaTamanhoCalsa.add(new SelectItem(0, "0"));
            this.listaTamanhoCalsa.add(new SelectItem(34, "34"));
            this.listaTamanhoCalsa.add(new SelectItem(36, "36"));
            this.listaTamanhoCalsa.add(new SelectItem(38, "38"));
            this.listaTamanhoCalsa.add(new SelectItem(40, "40"));
            this.listaTamanhoCalsa.add(new SelectItem(42, "42"));
            this.listaTamanhoCalsa.add(new SelectItem(44, "44"));
            this.listaTamanhoCalsa.add(new SelectItem(46, "46"));
            this.listaTamanhoCalsa.add(new SelectItem(48, "48"));
            this.listaTamanhoCalsa.add(new SelectItem(50, "50"));
            this.listaTamanhoCalsa.add(new SelectItem(52, "52"));
            this.listaTamanhoCalsa.add(new SelectItem(54, "54"));
            this.listaTamanhoCalsa.add(new SelectItem(56, "56"));
            this.listaTamanhoCalsa.add(new SelectItem(58, "58"));
            this.listaTamanhoCalsa.add(new SelectItem(60, "60"));
            this.listaTamanhoCalsa.add(new SelectItem(62, "62"));
            this.listaTamanhoCalsa.add(new SelectItem(64, "64"));
            this.listaTamanhoCalsa.add(new SelectItem(66, "66"));
            this.listaTamanhoCalsa.add(new SelectItem(68, "68"));
        }
        return this.listaTamanhoCalsa;
    }

    public boolean getVerificarSeTemTipoColaboradorEstudio() {
        for (TipoColaboradorVO tipo : getColaboradorVO().getListaTipoColaboradorVOs()) {
            if (tipo.getDescricao().equals(TipoColaboradorEnum.ESTUDIO.getSigla())) {
                return true;
            }
        }
        return false;
    }

    public boolean getVerificarSeTemTipoPersonal() {
        for (TipoColaboradorVO tipo : getColaboradorVO().getListaTipoColaboradorVOs()) {
            if (tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())
                    || tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())) {
                return true;
            }
        }
        return false;
    }

    public boolean getVerificarSoTemTipoPersonalExterno() {
        for (TipoColaboradorVO tipo : getColaboradorVO().getListaTipoColaboradorVOs()) {
            if (tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())) {
                return true;
            }
        }
        return false;
    }

    public boolean getMostrarUsoCredito() throws Exception {
        return getVerificarSeTemTipoPersonal()
                && (getUsuarioLogado().getAdministrador() || getEmpresaLogado().isUsarGestaoCreditosPersonal());
    }

    public List<SelectItem> getListaSelectItemTipoColaborador() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        for (TipoColaboradorEnum tipo : TipoColaboradorEnum.values()) {
            objs.add(new SelectItem(tipo.getSigla(), tipo.getDescricao()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        Ordenacao.ordenarLista(objs, "label");
        setListaSelectItemEmpresa(objs);
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (getUsuarioLogado().getAdministrador()) {
                montarListaSelectItemEmpresa("");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getEmpresa().consultarPorNome(nomePrm, true, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public List consultarPessoaPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getPessoa().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void montarListaSelectItemPais(String prm) throws Exception {
        List resultadoConsulta = consultarPaisPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            PaisVO obj = (PaisVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        Ordenacao.ordenarLista(objs, "label");
        setListaSelectItemPais(objs);
    }

    public void montarListaSelectItemPais() {
        try {
            montarListaSelectItemPais("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarPaisPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getPais().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void montarListaSelectItemEstado(String prm) throws Exception {
        List resultadoConsulta = consultarEstadoPorCodigoPais(this.getPessoaVO().getPais().getCodigo());
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            EstadoVO obj = (EstadoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
        Ordenacao.ordenarLista(objs, "label");
        setListaSelectItemEstado(objs);
        if (this.getPessoaVO().getPais().getCodigo().intValue() == 0) {
            this.getPessoaVO().getEstadoVO().setCodigo(new Integer(0));
            montarListaSelectItemCidade(prm);
        }
    }

    public void montarListaSelectItemEstado() {
        try {
            montarListaSelectItemEstado("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarCidadePorCodigoEstado(Integer nomePrm) throws Exception {
        List lista = getFacade().getCidade().consultarPorCodigoEstado(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public List consultarEstadoPorCodigoPais(Integer nomePrm) throws Exception {
        List lista = getFacade().getPais().consultarEstadoPorPais(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void montarListaSelectItemCidade(String prm) throws Exception {
        List resultadoConsulta = consultarCidadePorCodigoEstado(getPessoaVO().getEstadoVO().getCodigo());
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            CidadeVO obj = (CidadeVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        Ordenacao.ordenarLista(objs, "label");
        setListaSelectItemCidade(objs);
    }

    public void montarListaSelectItemCidade() {
        try {
            montarListaSelectItemCidade("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarCidadePorCodigoPais(Integer nomePrm) throws Exception {
        List lista = getFacade().getCidade().consultarPorCodigoPais(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void montarListaSelectItemProfissao(String prm) throws Exception {
        List resultadoConsulta = consultarProfissaoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ProfissaoVO obj = (ProfissaoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
        Ordenacao.ordenarLista(objs, "label");
        setListaSelectItemProfissao(objs);
    }

    public void montarListaSelectItemProfissao() {
        try {
            montarListaSelectItemProfissao("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarProfissaoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getProfissao().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemEmpresa();
        montarListaSelectItemProfissao();
        montarListaSelectItemPais();
        montarListaSelectItemEstado();
        montarListaSelectItemCidade();
        montarListaTipoConsultaCombo();
        montarListaUsoCreditoPersonal();
        montarListaDepartamentos();
    }

    public void realizarConsultaLogObjetoSelecionado() throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if (colaboradorVO.getPessoa() != null && UteisValidacao.emptyNumber(colaboradorVO.getPessoa().getCodigo())) {
            Integer codColaborador = getFacade().getPessoa().obterPessoaColaborador(colaboradorVO.getCodigo());
            colaboradorVO.getPessoa().setCodigo(codColaborador);
        }
        loginControle.consultarLogObjetoSelecionado("", 0, colaboradorVO.getPessoa().getCodigo());
    }

    public void realizarConsultaLogObjetoGeral() throws Exception {
        colaboradorVO.setCodigo(0);
        realizarConsultaLogObjetoSelecionado();
    }

    public void montarListaUsoCreditoPersonal() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem(null, null));
        for (UsoCreditoPersonalEnum ucp : UsoCreditoPersonalEnum.values()) {
            itens.add(new SelectItem(ucp.getCodigo(), ucp.getNome()));
        }
        listaUsoCreditos = itens;
    }

    public void montarListaDepartamentos() {
        try {
            int empresa = 0;
            if (!getUsuarioLogado().getAdministrador()) {
                empresa = getEmpresaLogado().getCodigo();
            }
            List<DepartamentoVO> resultadoConsulta = getFacade().getDepartamento().consultarPorNome("", empresa, false, Uteis.NIVELMONTARDADOS_TODOS);
            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem(0, ""));
            for (DepartamentoVO departamentoVO : resultadoConsulta) {
                objs.add(new SelectItem(departamentoVO.getCodigo(), departamentoVO.getNome()));
            }
            Ordenacao.ordenarLista(objs, "label");
            setListSelectItemDepartamentos(objs);
            setErro(false);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            setErro(true);
        }
    }

    public void montarListaTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("situacao", "Situação"));
        itens.add(new SelectItem("tipoColaborador", "Tipo de Colaborador"));
        try {
            if (getUsuarioLogado().getAdministrador().equals(true)) {
                itens.add(new SelectItem("empresa", "Empresa"));
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
        Ordenacao.ordenarLista(itens, "label");
        setTipoConsultaCombo(itens);
    }

    public void alterarCampoConsulta() {
        String campoSelecionado = getControleConsulta().getCampoConsulta();
        getControleConsulta().setValorConsulta("");
        if (campoSelecionado.equals("empresa")
                || campoSelecionado.equals("situacao")
                || campoSelecionado.equals("tipoColaborador")) {
            setCampoConsultaSelectItem(Boolean.TRUE);
        } else {
            setCampoConsultaSelectItem(Boolean.FALSE);
        }
    }

    public List<SelectItem> getListaSelectItemConsulta() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("", ""));
        try {
            if (getControleConsulta().getCampoConsulta().equals("tipoColaborador")) {
                return getListaSelectItemTipoColaborador();
            } else if (getControleConsulta().getCampoConsulta().equals("situacao")) {
                return getListaSelectItemSituacaoColaborador();
            } else if (getControleConsulta().getCampoConsulta().equals("empresa")) {
                return getListaSelectItemEmpresa();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setMensagemDetalhada("", "");
        setSucesso(false);
        setAtencao(false);
        setErro(false);
        return "consultar";
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public String montarListaUltimosAcessos() throws Exception {
        try {
            setListaUltimosAcessos(new ArrayList());
            setListaUltimosAcessos(
                    getFacade().getAcessoColaborador().consultarUltimos5Acessos(getColaboradorVO(),
                            Uteis.NIVELMONTARDADOS_TODOS));
            return "";
        } catch (Exception e) {
            setListaUltimosAcessos(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public String montarListaUltimosAcessosSistema() throws Exception {
        try {
            setListaUltimosAcessosSistema(new ArrayList());
            //transforma as datas para String para realizar a consulta usando o formato yyyy-MM-dd
            String dataInicio = Uteis.getDataFormatoBD(getDataInicio());
            String dataFim = Uteis.getDataFormatoBD(getDataTermino());
            validarDados();
            UsuarioVO usuario = getFacade().getUsuario().
                    consultarPorColaboradorEmpresa(getColaboradorVO().getCodigo(), getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (usuario.getCodigo().intValue() != 0) {
                setListaUltimosAcessosSistema(
                        getFacade().getLogControleUsabilidade().
                                consultarPorIntervaloDatasUsuario10UltimosAcessos(usuario.getCodigo(), dataInicio, dataFim, getAcao(), false,
                                        Uteis.NIVELMONTARDADOS_TELACONSULTA));
            }
            setSucesso(true);
            setErro(false);
            setMensagemUltimosAcessosSistema("Dados Consultados com Sucesso");
            return "";
        } catch (Exception e) {
            setListaUltimosAcessos(new ArrayList());
            montarErro(e);
            return "";
        }
    }

    public String imprimirListaUltimosAcessosSistema() throws Exception {
        try {
            //transforma as datas para String para realizar a consulta usando o formato yyyy-MM-dd
            String dataInicio = Uteis.getDataFormatoBD(getDataInicio());
            String dataFim = Uteis.getDataFormatoBD(getDataTermino());
            validarDados();
            UsuarioVO usuario = getFacade().getUsuario().
                    consultarPorColaboradorEmpresa(getColaboradorVO().getCodigo(), getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (usuario.getCodigo().intValue() != 0) {
                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd_HH.mm.ss.SSS");
                LocalDateTime now = LocalDateTime.now();
                String nomeArquivo = "relatorioColaboradorAcessoSistemaExcel_"+dtf.format(now)+".xlsx";
                List lista = getFacade().getLogControleUsabilidade().
                        consultarAcessosRelatorioPorPeriodo(usuario.getCodigo(), colaboradorVO.getCodigo(), dataInicio, dataFim, getAcao());
                gerarRelatorioExcel(lista, gravarArquivoRelatorio(nomeArquivo), true);
                setMsgAlert("location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + nomeArquivo+"'");
            } else {
                throw new Exception("Não é possível imprimir o relatório. Usuário não encontrado.");
            }
            setSucesso(true);
            setErro(false);
            setMensagemUltimosAcessosSistema("Dados Consultados com Sucesso");
            return "";
        } catch (Exception e) {
            montarErro(e);
            return "";
        }
    }

    public String imprimirListaUltimosAcessosCatraca() throws Exception {
        try {
            //transforma as datas para String para realizar a consulta usando o formato yyyy-MM-dd
            String dataInicio = Uteis.getDataFormatoBD(getDataInicio());
            String dataFim = Uteis.getDataFormatoBD(getDataTermino());
            validarDados();
            UsuarioVO usuario = getFacade().getUsuario().
                    consultarPorColaboradorEmpresa(getColaboradorVO().getCodigo(), getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (usuario.getCodigo().intValue() != 0) {
                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMdd_HH.mm.ss.SSS");
                LocalDateTime now = LocalDateTime.now();
                String nomeArquivo = "relatorioColaboradorAcessoCatracaExcel_"+dtf.format(now)+".xlsx";
                List lista = getFacade().getAcessoColaborador().
                        consultarUltimosAcessosRelatorio(colaboradorVO.getPessoa().getCodigo(), dataInicio, dataFim);
                gerarRelatorioExcel(lista, gravarArquivoRelatorio(nomeArquivo), false);
                setMsgAlert("location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + nomeArquivo+"'");
            } else {
                throw new Exception("Não é possível imprimir o relatório. Usuário não encontrado.");
            }
            setSucesso(true);
            setErro(false);
            setMensagemUltimosAcessosSistema("Dados Consultados com Sucesso");
            return "";
        } catch (Exception e) {
            montarErro(e);
            return "";
        }
    }

    private File gravarArquivoRelatorio(String s) throws Exception{
        File arquivo = new File(Uteis.obterCaminhoWeb() + File.separator + "relatorio" + File.separator + s);
        if (arquivo.exists()) {
            arquivo.delete();
        }else{
            new File(getDiretorioArquivos() + File.separator + SUBDIRETORIO_ARQUIVOS).mkdirs();
        }
        arquivo.createNewFile();
        return arquivo;
    }

    public void consultarProduto() {
        try {
            setListaProdutos(getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(getValorConsultarProduto(), "TP", false, Uteis.NIVELMONTARDADOS_TODOS));
            if (getListaProdutos().isEmpty()) {
                setMensagemDetalhada("msg_dados_consultados_vazio", "");
            } else {
                setMensagemDetalhada("msg_dados_consultados", "");
            }
        } catch (Exception e) {
            setListaProdutos(new ArrayList<ProdutoVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarProduto() throws Exception {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
        colaboradorVO.setProdutoDefault(obj);
        setMensagemDetalhada("", "");
        setValorConsultarProduto("");
    }

    public void validarDados() throws Exception {
        long dias = Uteis.nrDiasEntreDatas(dataInicio, dataTermino);
        dias = dias + 1;
        //Validação necessária para evitar que a tela fique lenta. Limite de 6 meses para pesquisa
        if (dias > 186) {
            throw new Exception("Não é possível realizar a consulta para o período de datas informado. Intervalo de dias superior a 6 meses.");
        }

        if (getDataInicio().compareTo(getDataTermino()) > 0) {
            throw new Exception("A Data de Início deve ser menor que a Data De Término para pesquisa.");
        }
    }

    public List<SelectItem> getListaSelectItemAcao() {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem("LA", "LOGADO"));
        lista.add(new SelectItem("LO", "LOGOUT"));
        lista.add(new SelectItem("TO", "TODAS"));
        return lista;
    }

    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public EnderecoVO getEnderecoVO() {
        return enderecoVO;
    }

    public void setEnderecoVO(EnderecoVO enderecoVO) {
        this.enderecoVO = enderecoVO;
    }

    public List getListaSelectItemCidade() {
        return listaSelectItemCidade;
    }

    public void setListaSelectItemCidade(List listaSelectItemCidade) {
        this.listaSelectItemCidade = listaSelectItemCidade;
    }

    public List getListaSelectItemPais() {
        return listaSelectItemPais;
    }

    public void setListaSelectItemPais(List listaSelectItemPais) {
        this.listaSelectItemPais = listaSelectItemPais;
    }

    public void setListaUltimosAcessos(List listaUltimosAcessos) {
        this.listaUltimosAcessos = listaUltimosAcessos;
    }

    public List getListaUltimosAcessos() {
        return listaUltimosAcessos;
    }

    public List getListaSelectItemProfissao() {
        return listaSelectItemProfissao;
    }

    public void setListaSelectItemProfissao(List listaSelectItemProfissao) {
        this.listaSelectItemProfissao = listaSelectItemProfissao;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public TelefoneVO getTelefoneVO() {
        return telefoneVO;
    }

    public void setTelefoneVO(TelefoneVO telefoneVO) {
        this.telefoneVO = telefoneVO;
    }

    public EmailVO getEmailVO() {
        return emailVO;
    }

    public void setEmailVO(EmailVO emailVO) {
        this.emailVO = emailVO;
    }

    public Boolean getAdicionar() {
        return adicionar;
    }

    public void setAdicionar(Boolean adicionar) {
        this.adicionar = adicionar;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ClienteVO getClienteVinculado() {
        return clienteVinculado;
    }

    public void setClienteVinculado(ClienteVO clienteVinculado) {
        this.clienteVinculado = clienteVinculado;
    }

    public ColaboradorVO getCopiaColaboradorVO() {
        return copiaColaboradorVO;
    }

    public void setCopiaColaboradorVO(ColaboradorVO copiaColaboradorVO) {
        this.copiaColaboradorVO = copiaColaboradorVO;
    }

    public ProfissaoVO getProfissaoVO() {
        return profissaoVO;
    }

    public void setProfissaoVO(ProfissaoVO profissaoVO) {
        this.profissaoVO = profissaoVO;
    }

    public List getListaSelectItemEstado() {
        return listaSelectItemEstado;
    }

    public void setListaSelectItemEstado(List listaSelectItemEstado) {
        this.listaSelectItemEstado = listaSelectItemEstado;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public TipoColaboradorVO getTipoColaboradorVO() {
        return tipoColaboradorVO;
    }

    public void setTipoColaboradorVO(TipoColaboradorVO tipoColaboradorVO) {
        this.tipoColaboradorVO = tipoColaboradorVO;
    }

    public Boolean getTransferirColaboradorEmpresa() {
        return transferirColaboradorEmpresa;
    }

    public void setTransferirColaboradorEmpresa(Boolean transferirColaboradorEmpresa) {
        this.transferirColaboradorEmpresa = transferirColaboradorEmpresa;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public CepControle getCepControle() {
        return cepControle;
    }

    public void setCepControle(CepControle cepControle) {
        this.cepControle = cepControle;
    }

    public boolean isApresentarOpcaoEstornoProduto() {
        return apresentarOpcaoEstornoProduto;
    }

    public void setApresentarOpcaoEstornoProduto(boolean apresentarOpcaoEstornoProduto) {
        this.apresentarOpcaoEstornoProduto = apresentarOpcaoEstornoProduto;
    }

    public Integer getNrPaginaMovProduto() {
        return nrPaginaMovProduto;
    }

    public void setNrPaginaMovProduto(Integer nrPaginaMovProduto) {
        this.nrPaginaMovProduto = nrPaginaMovProduto;
    }

    public Integer getNrPaginaMovPagamento() {
        return nrPaginaMovPagamento;
    }

    public void setNrPaginaMovPagamento(Integer nrPaginaMovPagamento) {
        this.nrPaginaMovPagamento = nrPaginaMovPagamento;
    }

    public Integer getNrPaginaMovParcela() {
        return nrPaginaMovParcela;
    }

    public void setNrPaginaMovParcela(Integer nrPaginaMovParcela) {
        this.nrPaginaMovParcela = nrPaginaMovParcela;
    }

    public List<LogControleUsabilidadeVO> getListaUltimosAcessosSistema() {
        return listaUltimosAcessosSistema;
    }

    public void setListaUltimosAcessosSistema(List<LogControleUsabilidadeVO> ultimosAcessosSistema) {
        this.listaUltimosAcessosSistema = ultimosAcessosSistema;
    }

    public Integer getNrPaginaUltimosAcessosSistema() {
        return nrPaginaUltimosAcessosSistema;
    }

    public void setNrPaginaUltimosAcessosSistema(Integer nrPaginaUltimosAcessosSistema) {
        this.nrPaginaUltimosAcessosSistema = nrPaginaUltimosAcessosSistema;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getAcao() {
        return acao;
    }

    public void setAcao(String acao) {
        this.acao = acao;
    }

    public String getMensagemUltimosAcessosSistema() {
        return mensagemUltimosAcessosSistema;
    }

    public void setMensagemUltimosAcessosSistema(String mensagemUltimosAcessosSistema) {
        this.mensagemUltimosAcessosSistema = mensagemUltimosAcessosSistema;
    }

    public List<ProdutoVO> getListaProdutos() {
        return listaProdutos;
    }

    public void setListaProdutos(List<ProdutoVO> listaProdutos) {
        this.listaProdutos = listaProdutos;
    }

    public String getValorConsultarProduto() {
        return valorConsultarProduto;
    }

    public void setValorConsultarProduto(String valorConsultarProduto) {
        this.valorConsultarProduto = valorConsultarProduto;
    }

    public void abriTelaDefinirSenhaAcesso() throws Exception {
        HttpSession session = (HttpSession) context().getExternalContext().getSession(true);
        session.setAttribute(PessoaControle.chavePessoaAlterarSenhaAcesso, pessoaVO);
        PessoaControle pesControle = (PessoaControle) getControlador(PessoaControle.class);
        if (pesControle == null) {
            pesControle = new PessoaControle();
        }
        pesControle.setPessoaVO(pessoaVO);
        pesControle.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getColaboradorVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        pesControle.montarMensagemTelaInicial();
        pesControle.setErro(false);

    }

    public ModalidadeComissaoColaboradorVO getModalidadeComissaoColaboradorVO() {
        return modalidadeComissaoColaboradorVO;
    }

    public void setModalidadeComissaoColaboradorVO(ModalidadeComissaoColaboradorVO modalidadeComissaoColaboradorVO) {
        this.modalidadeComissaoColaboradorVO = modalidadeComissaoColaboradorVO;
    }

    public TurmaComissaoColaboradorVO getTurmaComissaoColaboradorVO() {
        return turmaComissaoColaboradorVO;
    }

    public void setTurmaComissaoColaboradorVO(TurmaComissaoColaboradorVO turmaComissaoColaboradorVO) {
        this.turmaComissaoColaboradorVO = turmaComissaoColaboradorVO;
    }

    public AlunoComissaoColaboradorVO getAlunoComissaoColaboradorVO() {
        return alunoComissaoColaboradorVO;
    }

    public void setAlunoComissaoColaboradorVO(AlunoComissaoColaboradorVO alunoComissaoColaboradorVO) {
        this.alunoComissaoColaboradorVO = alunoComissaoColaboradorVO;
    }

    public void setCampoConsultaSelectItem(Boolean campoConsultaSelectItem) {
        this.campoConsultaSelectItem = campoConsultaSelectItem;
    }

    public Boolean getCampoConsultaSelectItem() {
        return campoConsultaSelectItem;
    }

    public void setTipoConsultaCombo(List tipoConsultaCombo) {
        this.tipoConsultaCombo = tipoConsultaCombo;
    }

    public List getTipoConsultaCombo() {
        return tipoConsultaCombo;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        if (permissao("ConsultarInfoTodasEmpresas")) {
            getEmpresaLogado().setCodigo(null);
        }
        List listaParaImpressao = getFacade().getColaborador().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), getSituacaoFiltro(), getSituacaoFiltroTipo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void sincronizarAgenda() throws Exception {
        try {
            getFacade().getAgendaEstudio().sincronizarGoogleAgenda(getColaboradorVO());
            setSucesso(true);
            setErro(false);
            setAtencao(false);
            setMensagem("Sincronização realizada com sucesso!");
            setMensagemDetalhada("", "");
        } catch (Exception ex) {
            setSucesso(false);
            setErro(false);
            setAtencao(true);
            setMensagem("Problemas ao sincronizar com o Google Calendar");
            setMensagemDetalhada("", ex.getMessage());
        }
    }

    public void dessincronizarAgenda() throws Exception {
        try {
            getFacade().getAgendaEstudio().dessincronizarGoogleAgenda(getColaboradorVO());
            setSucesso(true);
            setErro(false);
            setAtencao(false);
            setMensagem("Dessincronização realizada com sucesso!");
            setMensagemDetalhada("", "");
        } catch (Exception ex) {
            setSucesso(false);
            setErro(false);
            setAtencao(true);
            setMensagem("Problemas ao dessincronizar com o Google Calendar");
            setMensagemDetalhada("", ex.getMessage());
        }
    }

    public void setUrlGoogle(String urlGoogle) {
        this.urlGoogle = urlGoogle;
    }

    public String getUrlGoogle() {
        String urlCalendar = PropsService.getPropertyValue(PropsService.urlStudioCalendar);
        String googleClientID = PropsService.getPropertyValue(PropsService.googleClientID);
        return "https://accounts.google.com/ServiceLogin?service=lso&passive=1209600&continue=https://accounts.google.com/o/oauth2/auth?scope%3Dhttps://www.googleapis.com/auth/calendar%2Bhttps://www.googleapis.com/auth/calendar.readonly%26response_type%3Dcode%26access_type%3Doffline%26redirect_uri%3D" + urlCalendar + "auth.php%26approval_prompt%3Dforce%26client_id%3D" + googleClientID + "%26hl%3Dpt%26from_login%3D1%26as%3D-4cdc3ffd4db6c4bf&ltmpl=popup";
    }

    public List<SelectItem> getListaUsoCreditos() {
        return listaUsoCreditos;
    }

    public void setListaUsoCreditos(List<SelectItem> listaUsoCreditos) {
        this.listaUsoCreditos = listaUsoCreditos;
    }

    public boolean isMostrarFotoParaPersonal() throws Exception {
        return UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())
                || (getMostrarUsoCredito() && getEmpresaLogado().getConfigsPersonal().isUsarFotoPersonal());
    }

    public void paintFotoSemPesquisarNoBanco(OutputStream out, Object data) throws IOException, Exception {
        super.paintFotoEmpresa(out, getColaboradorVO().getFotoPersonal());
    }

    public void uploadFotoPersonal(UploadEvent upload) throws Exception {
        // getEmpresaVO().upload(upload, obterCaminhoWebAplicacaoFoto(), true);
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        try {
            BufferedImage outImage = ImageIO.read(item1);
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getColaboradorVO().setFotoPersonal(arrayOutputStream.toByteArray());
            arrayOutputStream.close();
            fi.close();
            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            if (isCMYK(item1)) {
                setMensagemDetalhada("msg_erro", "Essa imagem está no formato inadequado. Por favor converta para RGB.");
                setSucesso(false);
                setErro(true);
                throw new ConsistirException("A Imagem está no formato inadequado. Converter para RGB");
            }
        }
    }

    public String getOnComplete() {
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public void verificaColaborador() {
        if (this.getColaboradorVO().getPessoa().getCodigo() == null || this.getColaboradorVO().getPessoa().getCodigo() == 0) {
            setSucesso(false);
            setErro(true);
            setOnComplete("");
            setMensagemDetalhada("msg_erro", "Grave o cadastro do colaborador para poder adicionar a foto.");
        } else {
            //chave,codPessoa,contextPath,
            setOnComplete(String.format("setAttributesModalCapFoto('%s', '%s', '%s', '');"
                            + "Richfaces.showModalPanel('modalCapFotoHTML5');",
                    getKey(), this.getColaboradorVO().getPessoa().getCodigo(),
                    request().getContextPath()));
        }
    }

    public ColaboradorIndisponivelCrmVO getColaboradorIndisponivelCrmVO() {
        if (colaboradorIndisponivelCrmVO == null) {
            colaboradorIndisponivelCrmVO = new ColaboradorIndisponivelCrmVO();
        }
        return colaboradorIndisponivelCrmVO;
    }

    public void setColaboradorIndisponivelCrmVO(ColaboradorIndisponivelCrmVO colaboradorIndisponivelCrmVO) {
        this.colaboradorIndisponivelCrmVO = colaboradorIndisponivelCrmVO;
    }

    public List getTipoConsultaComboUsuario() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    public String getCampoConsultaUsuario() {
        if (campoConsultaUsuario == null) {
            campoConsultaUsuario = "";
        }
        return campoConsultaUsuario;
    }

    public void setCampoConsultaUsuario(String campoConsultaUsuario) {
        this.campoConsultaUsuario = campoConsultaUsuario;
    }

    public String getValorConsultaUsuario() {
        if (valorConsultaUsuario == null) {
            valorConsultaUsuario = "";
        }
        return valorConsultaUsuario;
    }

    public void setValorConsultaUsuario(String valorConsultaUsuario) {
        this.valorConsultaUsuario = valorConsultaUsuario;
    }

    public List getListaConsultaUsuario() {
        if (listaConsultaUsuario == null) {
            listaConsultaUsuario = new ArrayList(0);
        }
        return listaConsultaUsuario;
    }

    public void setListaConsultaUsuario(List listaConsultaUsuario) {
        this.listaConsultaUsuario = listaConsultaUsuario;
    }

    public List<ColaboradorVO> consultarColaboradorSuplente(Object suggest) {
        String pref = (String) suggest;
        List objs;
        int codigoDesconsiderar = 0;
        if (getColaboradorVO().getCodigo() != 0 && getColaboradorVO().getCodigo() != null)
            codigoDesconsiderar = getColaboradorVO().getCodigo();
        try {
            if (pref.equals("%")) {
                objs = getFacade().getColaborador().consultarPorTipoColaboradorAtivoNomePessoa("", codigoDesconsiderar, colaboradorVO.getListaTipoColaboradorVOs().listIterator(), "AT", getEmpresaLogado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                objs = getFacade().getColaborador().consultarPorTipoColaboradorAtivoNomePessoa(pref, codigoDesconsiderar, colaboradorVO.getListaTipoColaboradorVOs().listIterator(), "AT", getEmpresaLogado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setListaConsultaUsuario(objs);
            setMensagemID("msg_dados_consultados");
            return objs;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return null;
        }
    }

    public void selecionarColaboradorSuplenteSuggestionBox() throws Exception {
        ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("colaborador");
        getColaboradorIndisponivelCrmVO().setColaboradorSuplenteVO(colaboradorVO);
    }

    public void selecionarColaborador() {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
        getColaboradorIndisponivelCrmVO().setColaboradorSuplenteVO(obj);
    }

    public void editarColaboradorIndisponibilidade() throws Exception {
        ColaboradorIndisponivelCrmVO obj = (ColaboradorIndisponivelCrmVO) context().getExternalContext().getRequestMap().get("colaboradorIndisponivel");
        setColaboradorIndisponivelCrmVO(obj);
        removerColaboradorIndisponivel(obj);
    }

    public void removerColaboradorIndisponivel(ColaboradorIndisponivelCrmVO colaboradorIndisponivelCrmVO) throws Exception {
        for (int i = 0; i < getColaboradorVO().getListaColaboradorIndisponivelCrmVOS().size(); i++) {
            ColaboradorIndisponivelCrmVO obj = (ColaboradorIndisponivelCrmVO) getColaboradorVO().getListaColaboradorIndisponivelCrmVOS().get(i);
            if (obj == colaboradorIndisponivelCrmVO) {
                if (obj.getCodigo() != null && obj.getCodigo() != 0) {
                    getFacade().getColaboradorIndisponivelCrm().excluir(obj);
                }
                getColaboradorVO().getListaColaboradorIndisponivelCrmVOS().remove(i);
                break;
            }
        }
    }

    public void prepararRemoverColaboradorIndisponivel() throws Exception {
        ColaboradorIndisponivelCrmVO colaboradorIndisponivelCrmVO = (ColaboradorIndisponivelCrmVO) context().getExternalContext().getRequestMap().get("colaboradorIndisponivel");
        removerColaboradorIndisponivel(colaboradorIndisponivelCrmVO);
    }

    public void prepararAdicionarColaboradorIndisponivelCrm() throws Exception {
        try {
            if (!getColaboradorVO().getSituacao().equals("AT")) {
                throw new Exception("Colaborador " + getColaboradorVO().getPessoa().getNome() + " não está ativo!");
            }
            getColaboradorIndisponivelCrmVO().setEmpresaVO(getEmpresaLogado());
            getColaboradorIndisponivelCrmVO().setColaboradorIndisponivelVO(getColaboradorVO());
//            if (getColaboradorMesmoTipoColaboradorMesmoPerfilAcesso()){
            adicionarListaColaboradorIndisponivelCrm();
            setColaboradorIndisponivelCrmVO(null);
//            }else {
//                throw new Exception("Este Colaborador não possui o mesmo Tipo Colaborador e/ou não possui o mesmo Perfil Acesso!");
//            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Boolean getColaboradorMesmoTipoColaboradorMesmoPerfilAcesso() throws Exception {
        boolean ok = false;
        for (TipoColaboradorVO tipoColaboradorI : getColaboradorIndisponivelCrmVO().getColaboradorIndisponivelVO().getListaTipoColaboradorVOs()) {
            for (TipoColaboradorVO tipoColaboradorS : getColaboradorIndisponivelCrmVO().getColaboradorSuplenteVO().getListaTipoColaboradorVOs()) {
                if (tipoColaboradorI.getDescricao().equals(tipoColaboradorS.getDescricao())) {
                    ok = true;
                    break;
                }
            }
        }
        if (ok) {
            UsuarioVO usuarioI = getFacade().getUsuario().consultarPorCodigoColaborador(getColaboradorIndisponivelCrmVO().getColaboradorIndisponivelVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioS = getFacade().getUsuario().consultarPorCodigoColaborador(getColaboradorIndisponivelCrmVO().getColaboradorSuplenteVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            for (int i = 0; i < usuarioI.getUsuarioPerfilAcessoVOs().size(); i++) {
                UsuarioPerfilAcessoVO perfilI = (UsuarioPerfilAcessoVO) usuarioI.getUsuarioPerfilAcessoVOs().get(i);
                for (int s = 0; s < usuarioS.getUsuarioPerfilAcessoVOs().size(); s++) {
                    UsuarioPerfilAcessoVO perfilS = (UsuarioPerfilAcessoVO) usuarioS.getUsuarioPerfilAcessoVOs().get(s);
                    if (perfilI.getPerfilAcesso().getCodigo().equals(perfilS.getPerfilAcesso().getCodigo())) {
                        ok = true;
                        break;
                    }
                }
            }
        }
        return ok;
    }

    public void adicionarListaColaboradorIndisponivelCrm() throws Exception {
        setMensagemID("");
        setMensagem("");
        if (getColaboradorIndisponivelCrmVO().getColaboradorSuplenteVO().getCodigo() == 0) {
            throw new ConsistirException("Favor informar o colaborador Suplente!");
        }
        if (getColaboradorIndisponivelCrmVO().getDtFim() == null) {
            throw new ConsistirException("Informe uma Data de Fim!");
        }
        if (getColaboradorIndisponivelCrmVO().getDtInicio() == null) {
            throw new ConsistirException("Informe uma Data Início!");
        }
        if (getColaboradorIndisponivelCrmVO().getDtInicio().compareTo(getColaboradorIndisponivelCrmVO().getDtFim()) > 0) {
            throw new ConsistirException("Data Fim menor que Data Início!");
        }
        if (!getColaboradorIndisponivelCrmVO().getColaboradorSuplenteVO().getSituacao().equals("AT")) {
            throw new ConsistirException("Colaborador não está ativo!");
        }
        //Não é mais necessária essa validação pois a consulta ja é filtrada pelos grupos do colaborador
//        if (!getFacade().getGrupoColaboradorParticipante().verificarColaboradorSubstitutoPossuiMesmoGrupoColaboradorDoColaborador(getColaboradorIndisponivelCrmVO().getColaboradorSuplenteVO().getCodigo(), getColaboradorVO().getCodigo())){
//            throw new ConsistirException("Colaborador não está no mesmo Grupo Colaborador CRM!");
//        }

        //Não é  permitido adicionar O propio colaborador indiponível como suplente
        if (getColaboradorIndisponivelCrmVO().getColaboradorSuplenteVO().getCodigo().equals(getColaboradorVO().getCodigo()))
            throw new ConsistirException("Colaborador substituto não pode ser também o suplente!");

        //Tem como fim verificar se já existe algum outro colaborador que já esteja substituindo no período selecionado
        for (int i = 0; i < getColaboradorVO().getListaColaboradorIndisponivelCrmVOS().size(); i++) {
            ColaboradorIndisponivelCrmVO obj = (ColaboradorIndisponivelCrmVO) getColaboradorVO().getListaColaboradorIndisponivelCrmVOS().get(i);

            if (Calendario.entre(getColaboradorIndisponivelCrmVO().getDtInicio(), obj.getDtInicio(), obj.getDtFim())
                    || Calendario.entre(getColaboradorIndisponivelCrmVO().getDtFim(), obj.getDtInicio(), obj.getDtFim())
                    || Calendario.entre(obj.getDtInicio(), getColaboradorIndisponivelCrmVO().getDtInicio(), getColaboradorIndisponivelCrmVO().getDtFim())
                    || Calendario.entre(obj.getDtFim(), getColaboradorIndisponivelCrmVO().getDtInicio(), getColaboradorIndisponivelCrmVO().getDtFim())
                    || Calendario.igual(obj.getDtInicio(), getColaboradorIndisponivelCrmVO().getDtInicio())
                    || Calendario.igual(obj.getDtFim(), getColaboradorIndisponivelCrmVO().getDtFim())) {
                throw new ConsistirException("Colaborador já lançado com este período!");
            }
        }
        getColaboradorIndisponivelCrmVO().setEmpresaVO(getColaboradorVO().getEmpresa());
        getColaboradorVO().getListaColaboradorIndisponivelCrmVOS().add(getColaboradorIndisponivelCrmVO());
    }

    public void inicializarConsultaColaborador() throws Exception {
        setCampoConsultaUsuario(null);
        setValorConsultaUsuario(null);
        setListaConsultaUsuario(null);
    }

    public String acaoListarColaboradorIndisp() throws Exception {
        ConfiguracaoEstudioControle configuracaoEstudioControle = (ConfiguracaoEstudioControle) JSFUtilities.getManagedBean("configuracaoEstudioControle");
        String retorno = null;
        try {
            getColaboradorVO().setListaColaboradorIndisponivelCrmVOS(getFacade().getColaboradorIndisponivelCrm()
                    .consultarPorColaborador(getColaboradorVO().getCodigo(), getColaboradorVO().getEmpresa().getCodigo()));

            retorno = configuracaoEstudioControle.acaoListarColaboradorIndisp();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return retorno;
    }

    public List getClonarListaColaboradorIndisponivelCrmVOS(List lista) throws Exception {
        List listaNova = new ArrayList<ColaboradorIndisponivelCrmVO>(0);
        for (int i = 0; i < lista.size(); i++) {
            listaNova.add((ColaboradorIndisponivelCrmVO) ((ColaboradorIndisponivelCrmVO) lista.get(i)).getClone(false));
        }
        return listaNova;
    }

    public void registrarLogColaboradorIndisponivel(List listaAlterada, List listaOriginal) throws Exception {
        if (listaOriginal == null) {
            for (int i = 0; i < listaAlterada.size(); i++) {
                ColaboradorIndisponivelCrmVO obj = (ColaboradorIndisponivelCrmVO) listaAlterada.get(i);
                registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("ColaboradorSubstituto", "", obj.getColaboradorSuplenteVO().getCodigo() + " " + obj.getColaboradorSuplenteVO().getPessoa().getNome(), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataInicio", "", Uteis.getData(obj.getDtInicio()), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataFim", "", Uteis.getData(obj.getDtFim()), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("Motivo", "", obj.getMotivo(), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            }
        } else {
            for (int i = 0; i < listaOriginal.size(); i++) {
                ColaboradorIndisponivelCrmVO obj = (ColaboradorIndisponivelCrmVO) listaOriginal.get(i);
                boolean excluido = true;
                for (int a = 0; a < listaAlterada.size(); a++) {
                    ColaboradorIndisponivelCrmVO alterado = (ColaboradorIndisponivelCrmVO) listaAlterada.get(a);
                    if (obj.getColaboradorSuplenteVO().getCodigo().equals(alterado.getColaboradorSuplenteVO().getCodigo())) {
                        if (!obj.getDtInicio().equals(alterado.getDtInicio())) {
                            registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataInicio", Uteis.getData(obj.getDtInicio()), Uteis.getData(alterado.getDtInicio()), "ALTERAÇÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                        }
                        if (!obj.getDtFim().equals(alterado.getDtFim())) {
                            registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataFim", Uteis.getData(obj.getDtFim()), Uteis.getData(alterado.getDtFim()), "ALTERAÇÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                        }
                        if (!obj.getMotivo().equals(alterado.getMotivo())) {
                            registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("Motivo", obj.getMotivo(), alterado.getMotivo(), "ALTERAÇÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                        }
                        excluido = false;
                    }
                }
                if (excluido) {
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("ColaboradorSubstituto", obj.getColaboradorSuplenteVO().getCodigo() + " " + obj.getColaboradorSuplenteVO().getPessoa().getNome(), "", "EXCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataInicio", Uteis.getData(obj.getDtInicio()), "", "EXCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataFim", Uteis.getData(obj.getDtFim()), "", "EXCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("Motivo", obj.getMotivo(), "", "EXCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                }
            }

            for (int i = 0; i < listaAlterada.size(); i++) {
                ColaboradorIndisponivelCrmVO alterado = (ColaboradorIndisponivelCrmVO) listaAlterada.get(i);
                boolean incluido = true;
                for (int a = 0; a < listaOriginal.size(); a++) {
                    ColaboradorIndisponivelCrmVO obj = (ColaboradorIndisponivelCrmVO) listaOriginal.get(a);
                    if (obj.getColaboradorSuplenteVO().getCodigo().equals(alterado.getColaboradorSuplenteVO().getCodigo())) {
                        incluido = false;
                    }
                }
                if (incluido) {
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("ColaboradorSubstituto", "", alterado.getColaboradorSuplenteVO().getCodigo() + " " + alterado.getColaboradorSuplenteVO().getPessoa().getNome(), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataInicio", "", Uteis.getData(alterado.getDtInicio()), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("DataFim", "", Uteis.getData(alterado.getDtFim()), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                    registrarLogObjetoVO(montarRegistroLogColaboradorIndisponivel("Motivo", "", alterado.getMotivo(), "INCLUSÃO"), getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                }
            }
        }
    }

    public List<String> getListaCamposObrigatorioColaboradorDinamico() {
        return listaCamposObrigatorioColaboradorDinamico;
    }

    public void setListaCamposObrigatorioColaboradorDinamico(List<String> listaCamposObrigatorioColaboradorDinamico) {
        this.listaCamposObrigatorioColaboradorDinamico = listaCamposObrigatorioColaboradorDinamico;
    }

    public List<String> getListaCamposMostrarColaboradorDinamico() {
        return listaCamposMostrarColaboradorDinamico;
    }

    public void setListaCamposMostrarColaboradorDinamico(List<String> listaCamposMostrarColaboradorDinamico) {
        this.listaCamposMostrarColaboradorDinamico = listaCamposMostrarColaboradorDinamico;
    }

    public LogVO montarRegistroLogColaboradorIndisponivel(String campo, String valorCampoAnterior, String valorCampoAlterado, String operacao) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(getColaboradorVO().getCodigo().toString());
        obj.setNomeEntidade("COLABORADOR");
        obj.setNomeEntidadeDescricao("ColaboradorIndisponivelCrmVO");
        obj.setOperacao(operacao);
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setNomeCampo("ColaboradorIndisponivelCrmVO [" + campo + "]");
        obj.setDataAlteracao(Calendario.hoje());
        obj.setValorCampoAnterior(valorCampoAnterior);
        obj.setValorCampoAlterado(valorCampoAlterado);
        return obj;
    }

    public boolean isMostrarAbaEndereco() {
        return (this.listaCamposMostrarColaboradorDinamico.contains(CadastroDinamicoColaboradorEnum.CEP.toString())) ||
                (this.listaCamposMostrarColaboradorDinamico.contains(CadastroDinamicoColaboradorEnum.ENDERECO.toString())) ||
                (this.listaCamposMostrarColaboradorDinamico.contains(CadastroDinamicoColaboradorEnum.COMPLEMENTO.toString())) ||
                (this.listaCamposMostrarColaboradorDinamico.contains(CadastroDinamicoColaboradorEnum.NUMERO.toString())) ||
                (this.listaCamposMostrarColaboradorDinamico.contains(CadastroDinamicoColaboradorEnum.BAIRRO.toString())) ||
                (this.listaCamposMostrarColaboradorDinamico.contains(CadastroDinamicoColaboradorEnum.ENDERECOCORRESPONDENCIA.toString())) ||
                (this.listaCamposMostrarColaboradorDinamico.contains(CadastroDinamicoColaboradorEnum.TIPOENDERECO.toString()));
    }

    public void preencherAutorizacoesCobranca(ActionEvent evt) {
        try {
            AutorizacaoCobrancaControle control = (AutorizacaoCobrancaControle) JSFUtilities.getManagedBeanValue(AutorizacaoCobrancaControle.class);
            control.setColaborador(colaboradorVO);
            try {
                if (colaboradorVO.getCodigo() != 0) {
                    colaboradorVO.getEmpresa().setAgruparParcelasPorCartao(getFacade().getEmpresa().consultarPorChavePrimaria(colaboradorVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA).isAgruparParcelasPorCartao());
                }
            } catch (Exception e) {
                //ignore
            }
            control.setNegociacao(false);
            control.init();
            control.novo();
        } catch (Exception e) {
            setMensagemDetalhada(e);
            Logger.getLogger(ColaboradorControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void preencherItensCobranca() throws Exception {
        List<RemessaItemVO> itensRemessa = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoPessoa(colaboradorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<RemessaItemVO> itensBoleto = new ArrayList<RemessaItemVO>();
        List<RemessaItemVO> itens = new ArrayList<RemessaItemVO>();

        for (RemessaItemVO item : itensRemessa) {
            if (item.getMovParcelas().size() == 0) {
                itens.add(item);
            } else {
                itensBoleto.add(item);
            }
        }
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaEstorno", itens);
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaBoleto", itensBoleto);
        GestaoRemessasControle control = (GestaoRemessasControle) getControlador(GestaoRemessasControle.class);
        control.validarApresentacaoDaColunaRetornoManual();

        //lista de transações normais
        List<TransacaoVO> transacoes = getFacade().getTransacao().consultarTelaCliente(colaboradorVO.getPessoa().getCodigo(), 6, 0);
        listaTrasacoes.setCount(getFacade().getTransacao().obterCountTransacaoCliente(colaboradorVO.getPessoa().getCodigo()));
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoes", transacoes);

        //lista de transações de verificação do cartão
        List<TransacaoVO> transacoesVerificacao = getFacade().getTransacao().consultarTelaClienteTransacaoVerificacao(colaboradorVO.getPessoa().getCodigo(), 6, 0);
        listaTransacoesVerificacao.setCount(getFacade().getTransacao().obterCountTransacaoVerificacaoCliente(colaboradorVO.getPessoa().getCodigo()));
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoesVerificacao", transacoesVerificacao);

        //lista de transações de Pix
        listaPixVo = getFacade().getPix().consultarPorPessoaTelaCliente(colaboradorVO.getPessoa().getCodigo(), LISTA_PAGINADA_LIMIT, 0);
        listaPix.setCount(getFacade().getPix().quantidadePorPessoa(colaboradorVO.getPessoa().getCodigo()));
    }

    public List<SelectItem> getListSelectItemDepartamentos() {
        return listSelectItemDepartamentos;
    }

    public void setListSelectItemDepartamentos(List<SelectItem> listSelectItemDepartamentos) {
        this.listSelectItemDepartamentos = listSelectItemDepartamentos;
    }

    public DepartamentoVO getDepartamentoVO() {
        if (departamentoVO == null) {
            departamentoVO = new DepartamentoVO();
        }
        return departamentoVO;
    }

    public void setDepartamentoVO(DepartamentoVO departamentoVO) {
        this.departamentoVO = departamentoVO;
    }

    public void verificarSituacaoColaborador() throws Exception {
        if (getColaboradorVO() != null && getColaboradorVO().getSituacao().equals("NA") && !UteisValidacao.emptyNumber(getColaboradorVO().getCodigo())) {
            List<negocio.comuns.crm.AgendaVO> agendas = getFacade().getAgenda().consultarPorColaboradorResponsavel(getColaboradorVO().getCodigo(), Calendario.hoje(), Uteis.NIVELMONTARDADOS_MINIMOS);
            if (!agendas.isEmpty()) {
                VinculoAgendaControle vinculoAgendaControle = (VinculoAgendaControle) getControlador(VinculoAgendaControle.class);
                vinculoAgendaControle.carregarDadosVinculo(getColaboradorVO(), agendas);
                vinculoAgendaControle.setarPropriedades("Agendamentos vinculados ao colaborador " + getColaboradorVO().getPessoa().getNome(), "Para inativar o colaborador escolha um novo responsável pelos agendamentos dos alunos abaixo:",
                        this, "acaoVinculoAgenda", "", "acaoVinculoAgenda", "", "form", "Selecione o novo responsável pelos agendamentos já cadastrados para esse colaborador:");
                setOnComplete("Richfaces.showModalPanel('mdlVinculoAgenda');");
            }
        } else {
            setOnComplete("");
        }
    }

    public void acaoVinculoAgenda() {
        VinculoAgendaControle vinculoAgendaControle = (VinculoAgendaControle) getControlador(VinculoAgendaControle.class);

        if (UteisValidacao.emptyNumber(vinculoAgendaControle.getUsuarioVOSelecionado())) {
            getColaboradorVO().setSituacao("AT");
            atualizarAgenda = false;
            setMessageInInput("form:situacao", "É necessário selecionar um novo colaborador para poder inativar");
        } else {
            atualizarAgenda = true;
        }
        setOnComplete("");
    }

    private void verificarAgenda() throws Exception {
        if (this.atualizarAgenda != null && this.atualizarAgenda) {
            this.atualizarAgenda = false;
            VinculoAgendaControle vinculoAgendaControle = (VinculoAgendaControle) getControlador(VinculoAgendaControle.class);
            UsuarioVO usuario = getFacade().getUsuario().consultarPorColaborador(getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            getColaboradorVO().setCodigoUsuarioColaboradorTransfAgendaAntigo(usuario.getCodigo());
            getColaboradorVO().setCodigoUsuarioColaboradorTransfAgendaNovo(vinculoAgendaControle.getUsuarioVOSelecionado());
        }
    }

    public void redirecionarUsuario() throws Exception {
        UsuarioControle usuarioControle = (UsuarioControle) getControlador(UsuarioControle.class);
        try {
            usuarioControle.setUsuarioVO(null);
            List<UsuarioVO> usuarios = getFacade().getUsuario().consultarPorNomeIdentico(getColaboradorVO().getPessoa().getNome(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!usuarios.isEmpty()) {
                usuarioControle.editarParametro(usuarios.get(0)); // 'nome' é chave única na tabela usuario
            } else {
                usuarioControle.setUsuarioVO(new UsuarioVO());
                usuarioControle.getUsuario().setNovoObj(true);
                usuarioControle.setUsuarioPerfilAcessoVO(new UsuarioPerfilAcessoVO());
                usuarioControle.setApresentarCampoSenhaUsuario(true);
                usuarioControle.setPesquisar(true);
                usuarioControle.setCampoConsultaUsuario("");
                usuarioControle.setValorConsultaUsuario("");
                usuarioControle.setListaConsultaUsuario(new ArrayList());
                usuarioControle.inicializarUsuarioLogado();
                usuarioControle.inicializarListasSelectItemTodosComboBox();
                usuarioControle.limparDados();
                usuarioControle.preencherHorariosAcessosSistemaPadrao();
                usuarioControle.setTipoColaboradorVO(new TipoColaboradorVO());
                usuarioControle.setColaborador(true);
                usuarioControle.setCliente(false);
                usuarioControle.setNovoColaborador(false);
                usuarioControle.preencherUsuarioEmail();
                usuarioControle.consultarTodosHorariosSemanaPutMap();
                usuarioControle.selecionarColaboradorParametro(getColaboradorVO());
            }
        } catch (Exception ex) {
            usuarioControle.montarErro(ex);
            usuarioControle.setMensagemDetalhada("Não foi encontrado usuário para este colaborador. Foi criado um novo usuário, onde é necessário finalizar o cadastro e gravar a operação!");
        }
    }

    public void confirmarExcluirAssinaturaFacial() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Excluir Template Facial",
                "Deseja excluir o template facial do colaborador?",
                this, "excluirAssinaturaFacial", "", "", "", "form:panelDadosColaborador,form:panelMensagemErro");
    }

    public void excluirAssinaturaFacial() {
        try {
            getFacade().getPessoa().excluirAssinaturaBiometriaComLog(getColaboradorVO().getPessoa(), TipoAssinaturaBiometricaEnum.FACIAL, getUsuarioLogado());
            setSucesso(true);
            setErro(false);
            setAtencao(false);
            setMensagemDetalhada("Template facial excluído com sucesso!");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void confirmarExcluirAssinaturaDigital() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Excluir Template Digital",
                "Deseja excluir o template digital do colaborador?",
                this, "excluirAssinaturaDigital", "", "", "", "form:panelDadosColaborador,form:panelMensagemErro");
    }

    public void excluirAssinaturaDigital() {
        try {
            getFacade().getPessoa().excluirAssinaturaBiometriaComLog(getColaboradorVO().getPessoa(), TipoAssinaturaBiometricaEnum.DIGITAL, getUsuarioLogado());
            setSucesso(true);
            setErro(false);
            setAtencao(false);
            setMensagemDetalhada("Template digital excluído com sucesso!");
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public void preencherAssinaturasBiometricas(ActionEvent evt) {
        try {
            if (getColaboradorVO() != null && !UteisValidacao.emptyNumber(getColaboradorVO().getPessoa().getCodigo())) {
                getColaboradorVO().getPessoa().setAssinaturaBiometriaDigital(getFacade().getPessoa().obterAssinaturaBiometriaDigital(getColaboradorVO().getPessoa().getCodigo()));
                getColaboradorVO().getPessoa().setAssinaturaBiometriaFacial(getFacade().getPessoa().obterAssinaturaBiometriaFacial(getColaboradorVO().getPessoa().getCodigo()));
            }
        } catch (Exception e) {
            Logger.getLogger(ClienteControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public int getNrPaginaPlano() {
        return nrPaginaPlano;
    }

    public void setNrPaginaPlano(int nrPaginaPlano) {
        this.nrPaginaPlano = nrPaginaPlano;
    }

    public List<ControleTaxaPersonalVO> getControlesTaxaPersonal() {
        return controlesTaxaPersonal;
    }

    public void setControlesTaxaPersonal(List<ControleTaxaPersonalVO> controlesTaxaPersonal) {
        this.controlesTaxaPersonal = controlesTaxaPersonal;
    }

    public boolean getPermiteCancelarPlanoPersonal() {
        return permiteCancelarPlanoPersonal;
    }

    public void setPermiteCancelarPlanoPersonal(boolean permiteCancelarPlanoPersonal) {
        this.permiteCancelarPlanoPersonal = permiteCancelarPlanoPersonal;
    }

    public RecorrenciaService getRecorrenciaService() throws Exception {
        if (recorrenciaService == null) {
            recorrenciaService = new RecorrenciaService(getFacade().getColaborador().getCon());
        }
        return recorrenciaService;
    }

    public void selecionarControleTaxaPersonalParaCancelamento(ActionEvent event) throws Exception {
        setControleTaxaPersonalParaCancelamento((ControleTaxaPersonalVO) event.getComponent().getAttributes().get("controleTaxaPersonal"));

        List<MovParcelaVO> parcelasCancelamento = getRecorrenciaService()
                .simularCancelamentoPlanoPersonal(getControleTaxaPersonalParaCancelamento(), "");
        setParcelasACancelarPlanoPersonal(new ArrayList<MovParcelaVO>());
        setParcelasEmAbertoPlanoPersonal(new ArrayList<MovParcelaVO>());

        for (MovParcelaVO parcela : parcelasCancelamento) {
            if (parcela.getSituacao().equals("EA")) {
                getParcelasEmAbertoPlanoPersonal().add(parcela);
            }

            if (parcela.getSituacao().equals("CA")) {
                getParcelasACancelarPlanoPersonal().add(parcela);
            }
        }
        Uteis.logar("teste");
    }

    public void cancelarPlanoPersonal() {
        try {
            getRecorrenciaService().cancelarPlanoPersonal(getControleTaxaPersonalParaCancelamento(), "", getUsuarioLogado());
            setMensagem("O plano foi cancelado");
        } catch (Exception e) {
            montarErroComLog(e);
        }
    }

    public void setControleTaxaPersonalParaCancelamento(ControleTaxaPersonalVO controleTaxaPersonal) {
        this.controleTaxaPersonalParaCancelamento = controleTaxaPersonal;
    }

    public ControleTaxaPersonalVO getControleTaxaPersonalParaCancelamento() {
        return controleTaxaPersonalParaCancelamento;
    }

    public void setRecorrenciaService(RecorrenciaService recorrenciaService) {
        this.recorrenciaService = recorrenciaService;
    }

    public List<MovParcelaVO> getParcelasACancelarPlanoPersonal() {
        return parcelasACancelarPlanoPersonal;
    }

    public void setParcelasACancelarPlanoPersonal(List<MovParcelaVO> parcelasCancelamentoPlanoPersonal) {
        this.parcelasACancelarPlanoPersonal = parcelasCancelamentoPlanoPersonal;
    }

    public void setParcelasEmAbertoPlanoPersonal(List<MovParcelaVO> parcelasEmAbertoPlanoPersonal) {
        this.parcelasEmAbertoPlanoPersonal = parcelasEmAbertoPlanoPersonal;
    }

    public List<MovParcelaVO> getParcelasEmAbertoPlanoPersonal() {
        return parcelasEmAbertoPlanoPersonal;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

    public List<SelectItem> getListaSelectItemTipoFiltro() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AD", "Administrador"));
        objs.add(new SelectItem("CR", "Coordenador"));
        objs.add(new SelectItem("CO", "Consultor"));
        objs.add(new SelectItem("ES", "Estúdio"));
        objs.add(new SelectItem("FO", "Fornecedor"));
        objs.add(new SelectItem("FC", "Funcionário"));
        objs.add(new SelectItem("MD", "Médico"));
        objs.add(new SelectItem("OR", "Orientador"));
        objs.add(new SelectItem("PE", "Personal Externo"));
        objs.add(new SelectItem("PT", "Personal Trainer"));
        objs.add(new SelectItem("PI", "Personal Interno"));
        objs.add(new SelectItem("PR", "Professor"));
        objs.add(new SelectItem("TW", "Professor (TreinoWeb)"));
        objs.add(new SelectItem("TE", "Terceirizado"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro = "AT";
        }
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public String getSituacaoFiltroPadrao() {
        situacaoFiltro = "AT";
        return situacaoFiltro;
    }

    public String getSituacaoFiltroTipo() {
        if (situacaoFiltroTipo == null) {
            situacaoFiltroTipo = "TD";
        }
        return situacaoFiltroTipo;
    }

    public void setSituacaoFiltroTipo(String situacaoFiltroTipo) {
        this.situacaoFiltroTipo = situacaoFiltroTipo;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("metodochamar");

        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        if (obj.equals("removerFoto")) {
            control.init("Exclusão de Foto",
                    "Deseja Remover a Foto?",
                    this, obj, "", "", "", "form");
        } else {
            control.init("Exclusão de Colaborador",
                    "Deseja excluir o Colaborador?",
                    this, obj, "", "", "", "grupoBtnRemoverFoto,form,panelFoto");

        }

    }

    public boolean isApresentarCadastrarCidade() {
        return getPessoaVO().getPais().getCodigo() > 0 && getPessoaVO().getEstadoVO().getCodigo() > 0;
    }

    public void prepararParaCapturarFoto() {
        if (getColaboradorVO().isNovoObj()) {
            try {
                acaoGravar(false);
            } catch (Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
                this.getPessoaVO().setApresentarRichModalErro(false);
                this.colaboradorVO.setApresentarRichModalErro(false);
                setSucesso(false);
                setAtencao(false);
                setErro(true);
            }
        }
    }

    public List<VinculoVO> getListaVinculosColaborador() {
        return listaVinculosColaborador;
    }

    public void setListaVinculosColaborador(List<VinculoVO> listaVinculosColaborador) {
        this.listaVinculosColaborador = listaVinculosColaborador;
    }

    public TipoColaboradorEnum getTipoVinculoListaColaborador() {
        return tipoVinculoListaColaborador;
    }

    public void setTipoVinculoListaColaborador(TipoColaboradorEnum tipoVinculoListaColaborador) {
        this.tipoVinculoListaColaborador = tipoVinculoListaColaborador;
    }

    public List<SelectItem> getListaColaboradoresTrocarVinculos() {
        return listaColaboradoresTrocarVinculos;
    }

    public int getListaColaboradoresTrocarVinculosSize() {
        if (listaColaboradoresTrocarVinculos == null) {
            listaColaboradoresTrocarVinculos = new ArrayList<>();
        }
        return listaColaboradoresTrocarVinculos.size();
    }

    public void setListaColaboradoresTrocarVinculos(List<SelectItem> listaColaboradoresTrocarVinculos) {
        this.listaColaboradoresTrocarVinculos = listaColaboradoresTrocarVinculos;
    }

    public ColaboradorVO getAntigoColaboradorVinculo() {
        return antigoColaboradorVinculo;
    }

    public void setAntigoColaboradorVinculo(ColaboradorVO antigoColaboradorVinculo) {
        this.antigoColaboradorVinculo = antigoColaboradorVinculo;
    }

    public ColaboradorVO getNovoColaboradorVinculo() {
        return novoColaboradorVinculo;
    }

    public void setNovoColaboradorVinculo(ColaboradorVO novoColaboradorVinculo) {
        this.novoColaboradorVinculo = novoColaboradorVinculo;
    }
    public ListaPaginadaTO getListaTrasacoes() {
        return listaTrasacoes;
    }

    public void setListaTrasacoes(ListaPaginadaTO listaTrasacoes) {
        this.listaTrasacoes = listaTrasacoes;
    }

    public boolean existeVinculosColaborador(ColaboradorVO colaboradorVO, String origem) throws Exception {
        antigoColaboradorVinculo = colaboradorVO;
        this.origemOperacaoVinculos = origem;
        novoColaboradorVinculo = new ColaboradorVO();
        for (TipoColaboradorEnum tipoColaborador : TipoColaboradorEnum.values()) {
            this.listaVinculosColaborador = getFacade().getVinculo().consultarVinculoPorCodigoColaborador(colaboradorVO.getCodigo(), tipoColaborador.getSigla(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            if (!listaVinculosColaborador.isEmpty()) {
                tipoVinculoListaColaborador = tipoColaborador;
                montarSelectItemColaboradoresTipoVinculo(colaboradorVO, tipoColaborador);
                break;
            }
        }
        return !listaVinculosColaborador.isEmpty();
    }

    public String getOrigemOperacaoVinculos() {
        return origemOperacaoVinculos;
    }

    public void setOrigemOperacaoVinculos(String origemOperacaoVinculos) {
        this.origemOperacaoVinculos = origemOperacaoVinculos;
    }

    public void removerVinculosParaInativar() throws Exception {
        getFacade().getVinculo().removerVinculos(listaVinculosColaborador, origemOperacaoVinculos, getUsuarioLogado(), true);
    }

    public void transferirVinculosParaInativar() throws Exception {
        if (UteisValidacao.emptyNumber(this.novoColaboradorVinculo.getCodigo())) {
            throw new ConsistirException("É necessário informar o consultor responsável pelos clientes.");
        }
        novoColaboradorVinculo = getFacade().getColaborador().consultarPorChavePrimaria(novoColaboradorVinculo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<Integer> codigosAgendamentosTransferir = getFacade().getAgenda().consultarCodigoAgendaFuturasPorVinculos(listaVinculosColaborador, Calendario.hoje(), novoColaboradorVinculo.getEmpresa().getCodigo());
        getFacade().getVinculo().transferirVinculos(listaVinculosColaborador, novoColaboradorVinculo, origemOperacaoVinculos, getUsuarioLogado(), codigosAgendamentosTransferir, tipoVinculoListaColaborador.getSigla());

    }


    public void montarSelectItemColaboradoresTipoVinculo(ColaboradorVO colaboradorVO, TipoColaboradorEnum tipoColaborador) throws Exception {
        List<ColaboradorVO> listaConsultor = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(tipoColaborador, "AT", colaboradorVO.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        this.listaColaboradoresTrocarVinculos = new ArrayList<SelectItem>();
        this.listaColaboradoresTrocarVinculos.add(new SelectItem(0, ""));
        for (ColaboradorVO novoColaboradorVO : listaConsultor) {
            if (!colaboradorVO.getCodigo().equals(novoColaboradorVO.getCodigo())) {
                this.listaColaboradoresTrocarVinculos.add(new SelectItem(novoColaboradorVO.getCodigo(), novoColaboradorVO.getPessoa().getNome()));
            }
        }
    }

    public void atualizarListaColaboradoresTrocarVinculo() {
        try {
            montarSelectItemColaboradoresTipoVinculo(antigoColaboradorVinculo, tipoVinculoListaColaborador);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void transferirVinculosColaborador() {
        try {
            transferirVinculosParaInativar();
            gravar(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void removerVinculoColaborador() {
        ColaboradorControle control = (ColaboradorControle) getControlador(ColaboradorControle.class);
        try {
            control.removerVinculosParaInativar();
            gravar(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private boolean validarVinculosColaborador() throws Exception {
        setMsgAlert("");
        if (existeVinculosColaborador(colaboradorVO, "COLABORADOR - INATIVAR")) {
            setMsgAlert("Richfaces.showModalPanel('modalVinculoCliente');");
            return true;
        } else {
            setMsgAlert("Richfaces.hideModalPanel('modalVinculoCliente');");
        }
        return false;
    }

    public void imprimirPlanoPersonal(ActionEvent event) throws Exception {
        ControleTaxaPersonalVO controleTaxaPersonalVO = (ControleTaxaPersonalVO) event.getComponent().getAttributes().get("controleTaxaPersonalImpressao");

        setMsgAlert("");
        setMensagemDetalhada("");
        try {

            if (controleTaxaPersonalVO.getCodigo() != 0 && controleTaxaPersonalVO.getPlano() != null) {
                JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", controleTaxaPersonalVO.getEmpresa());
                PlanoPersonalTextoPadraoVO planoPersonalTextoPadraoVO = getFacade().getPlanoPersonalTextoPadrao().consultarPlanoPersonalTextoPadrao(controleTaxaPersonalVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                ColaboradorVO colaborador = getFacade().getColaborador().consultarPorCodigoPessoa(controleTaxaPersonalVO.getPersonal().getPessoa().getCodigo(), controleTaxaPersonalVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                PlanoTextoPadraoVO planoTextoPadraoVO = getFacade().getPlanoTextoPadrao().consultarPorChavePrimaria(planoPersonalTextoPadraoVO.getPlanoTextoPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                planoPersonalTextoPadraoVO.setPlanoTextoPadrao(planoTextoPadraoVO);
                controleTaxaPersonalVO.setPlanoPersonalTextoPadrao(planoPersonalTextoPadraoVO);
                controleTaxaPersonalVO.getPlano().getPlanoTextoPadrao().substituirTagsTextoPadraoPlanoPersonal(getKey(), colaborador, controleTaxaPersonalVO, Conexao.getFromSession(), getEmpresaLogado().getDescMoeda());

            } else {
                throw new Exception("Não foi possível emitir o Plano Personal. Dados não encontrados!");
            }
            if(controleTaxaPersonalVO.getPlanoPersonalTextoPadrao().getPlanoTextoPadrao().getTexto().length() == 0){
                throw new Exception("Não foi possível emitir o Plano Personal. O Plano não possui texto!");
            }

            setMensagemID("");
            setMensagemDetalhada("", "");
            setSucesso(false);
            setErro(false);
            setMsgAlert("abrirPopup('VisualizarContrato', 'RelatorioContrato', 730, 545);");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void consultarTransacoes(ListaPaginadaTO paginacao) throws Exception {
        List<TransacaoVO> transacoes = getFacade().getTransacao().consultarTelaCliente(colaboradorVO.getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset());
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoes", transacoes);
    }

    private void consultarTransacoesVerificacao(ListaPaginadaTO paginacao) throws Exception{
        List<TransacaoVO> transacoesverificacao = getFacade().getTransacao().consultarTelaClienteTransacaoVerificacao(colaboradorVO.getPessoa().getCodigo(),paginacao.getLimit(),paginacao.getOffset());
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoesVerificacao", transacoesverificacao);
    }

    private void consultarPix(ListaPaginadaTO paginacao) throws Exception {
        setListaPixVo(getFacade().getPix().consultarPorPessoaTelaCliente(colaboradorVO.getPessoa().getCodigo(), paginacao.getLimit(), paginacao.getOffset()));
    }

    public void proximaPagina(ActionEvent evt) throws Exception {
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.proximaPagina();
        carregarListaPaginacao(paginacao,codigo);
    }

    public void paginaAnterior(ActionEvent evt) throws Exception {
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.paginaAnterior();
        carregarListaPaginacao(paginacao,codigo);
    }

    public void ultimaPagina(ActionEvent evt) throws Exception {
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.ultimaPagina();
        carregarListaPaginacao(paginacao,codigo);
    }

    public void primeiraPagina(ActionEvent evt) throws Exception {
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.primeiraPagina();
        carregarListaPaginacao(paginacao,codigo);
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOffset(0);
        carregarListaPaginacao(paginacao,codigo);
    }

    public ListaPaginadaTO getListaTransacoesVerificacao() {
        return listaTransacoesVerificacao;
    }

    public void setListaTransacoesVerificacao(ListaPaginadaTO listaTransacoesVerificacao) {
        this.listaTransacoesVerificacao = listaTransacoesVerificacao;
    }

    private void carregarListaPaginacao(ListaPaginadaTO paginacao, String codigo) throws Exception {
        if (codigo.equals(LISTA_TRANSACOES)) {
            consultarTransacoes(paginacao);
        } else if (codigo.equals(LISTA_TRANSACOES_VERIFICACAO)) {
            consultarTransacoesVerificacao(paginacao);
        } else if (codigo.equals(LISTA_PIX)) {
            consultarPix(paginacao);
        }
    }
    private ListaPaginadaTO obterPaginacaoPorCodigo(String codigo) throws Exception{
        if(codigo.equals(LISTA_TRANSACOES)){
            return listaTrasacoes;
        } else if(codigo.equals(LISTA_TRANSACOES_VERIFICACAO)) {
            return listaTransacoesVerificacao;
        } else if (codigo.equals(LISTA_PIX)) {
            return listaPix;
        }
        return null;
    }

    public ListaPaginadaTO getListaPix() {
        return listaPix;
    }

    public void setListaPix(ListaPaginadaTO listaPix) {
        this.listaPix = listaPix;
    }

    public List<PixVO> getListaPixVo() {
        return listaPixVo;
    }

    public void setListaPixVo(List<PixVO> listaPixVo) {
        this.listaPixVo = listaPixVo;
    }

    public void formatarTelefones() throws Exception {
        if (!UteisValidacao.emptyString(configuracaoSistema.getMascaraTelefone())) {
            if (!UteisValidacao.emptyString(getTelefoneVO().getNumero())){
                getTelefoneVO().setNumero(TelefoneVO.aplicarMascara(getTelefoneVO().getNumero(), configuracaoSistema.getMascaraTelefone()));
            }
        }
    }

    public List getListaSelectItemColaboradorModalidade() {
        if (listaSelectItemColaboradorModalidade == null){
            listaSelectItemColaboradorModalidade = new ArrayList();
        }
        return listaSelectItemColaboradorModalidade;
    }

    public void setListaSelectItemColaboradorModalidade(List listaSelectItemColaboradorModalidade) {
        this.listaSelectItemColaboradorModalidade = listaSelectItemColaboradorModalidade;
    }

    public ColaboradorModalidadeVO getColaboradorModalidadeVO() {
        return colaboradorModalidadeVO;
    }

    public void setColaboradorModalidadeVO(ColaboradorModalidadeVO colaboradorModalidadeVO) {
        this.colaboradorModalidadeVO = colaboradorModalidadeVO;
    }

    public void montarListaSelectItemColaboradorModalidade() {
        try {
            List resultadoConsulta = getFacade().getModalidade().consultarPorNome("", getColaboradorVO().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator i = resultadoConsulta.iterator();
            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem(0, ""));
            while (i.hasNext()) {
                ModalidadeVO obj = (ModalidadeVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }

            setListaSelectItemColaboradorModalidade(objs);
        } catch (Exception e){
            montarErro(e);
        }
    }

    public void adicionarColaboradorModalidade() throws Exception{
        try {
            if (getColaboradorVO() != null && !UteisValidacao.emptyNumber(getColaboradorVO().getCodigo())) {
                colaboradorModalidadeVO.setColaboradorVO(getColaboradorVO());
            }
            if (getColaboradorModalidadeVO().getModalidadeVO() != null && !UteisValidacao.emptyNumber(getColaboradorModalidadeVO().getModalidadeVO().getCodigo())) {
                Integer campoConsulta = getColaboradorModalidadeVO().getModalidadeVO().getCodigo();
                ModalidadeVO modalidadeVO = getFacade().getModalidade().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_MINIMOS);
                getColaboradorModalidadeVO().setModalidadeVO(modalidadeVO);
            }
            getColaboradorVO().adicionarObjColaboradorModalidadesVOs(getColaboradorModalidadeVO());

            this.setColaboradorModalidadeVO(new ColaboradorModalidadeVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(false);
            setAtencao(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void removerColaboradorModalidade() throws Exception {
        try {
            setMensagemDetalhada("");
            ColaboradorModalidadeVO obj = (ColaboradorModalidadeVO) context().getExternalContext().getRequestMap().get("colaboradorModalidade");
            getColaboradorVO().excluirObjColaboradorModalidadesVOs(obj.getModalidadeVO().getCodigo());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setAtencao(false);
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
        }

    }


    public List getListaSelectItemCargaHoraria() throws Exception {
        List<SelectItem> objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        objs.add(new SelectItem(2, "2 horas"));
        objs.add(new SelectItem(4, "4 horas"));
        objs.add(new SelectItem(6, "6 horas"));
        objs.add(new SelectItem(8, "8 horas"));
        objs.add(new SelectItem(12, "12 horas"));
        return objs;
    }

    public void uploadDocumentoRh(UploadEvent upload) {
        this.colaboradorDocumentoRhVO.setOnCompleteAnexoDocumentoRh("");
        try {
            UploadItem item = upload.getUploadItem();
            if (item.getFile().length() > 20971520) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("Arquivo tem tamanho superior a 20 Megabytes");
                throw new ConsistirException("Tamanho Superior a 20 Megabytes");
            }
            this.colaboradorDocumentoRhVO.setArquivoDocumentoRh(item.getFile());
            this.colaboradorDocumentoRhVO.setNomeAnexo(item.getFileName());
            String[] partes = item.getFileName().split("\\.");
            this.colaboradorDocumentoRhVO.setExtensaoAnexo("." + partes[partes.length - 1]);
            montarSucessoGrowl("Arquivo enviado com sucesso, agora clique em  'gravar arquivo' para concluir a operação.");
            this.colaboradorDocumentoRhVO.setOnCompleteAnexoDocumentoRh(getMensagemNotificar());
        } catch (Exception e) {
            this.colaboradorDocumentoRhVO.setOnCompleteAnexoDocumentoRh(getMensagemNotificar());
            montarErro(e);
        }
    }

    public void gravarAnexoDocumentoRh() {
        try {
            if ((this.colaboradorDocumentoRhVO.getDescricaoDocumento() == null) || (this.colaboradorDocumentoRhVO.getDescricaoDocumento().trim().equals(""))){
                throw new ConsistirException("O campo 'Descrição do documento' é obrigatório.");
            }
            if (this.colaboradorDocumentoRhVO.getArquivoDocumentoRh() != null) {
                montarSucessoDadosGravados();
                if (colaboradorVO.getListaDocumentoRh() == null){
                    colaboradorVO.setListaDocumentoRh(new ArrayList<>());
                }
                this.colaboradorDocumentoRhVO.setUsuarioVO(getUsuarioLogado());
                this.colaboradorVO.getListaDocumentoRh().add(this.colaboradorDocumentoRhVO);
                this.colaboradorDocumentoRhVO = null;

            } else {
                throw new ConsistirException("Seleciona o arquivo a ser gravado.");
            }
        } catch (Exception ex) {
            montarErro(ex);
            this.colaboradorDocumentoRhVO.setOnCompleteAnexoDocumentoRh("");
        }
    }

    public void anexarNovoDocumentoRh(){
        try{
            this.colaboradorDocumentoRhVO = new ColaboradorDocumentoRhVO();
            this.colaboradorDocumentoRhVO.setUsuarioVO(new UsuarioVO());
            this.colaboradorDocumentoRhVO.setObjetoVOAntesAlteracao(new ColaboradorDocumentoRhVO());
            colaboradorDocumentoRhVO.setColaboradorVO(colaboradorVO);
            this.colaboradorDocumentoRhVO.setIdentificadorAnexo(getFacade().getColaboradorDocumentoRh().pesquisarIdentificarAnexo());
        } catch (Exception ex) {
            montarErro(ex);
        }

    }


    public void limparAnexoDocumentoRh() {
        this.colaboradorDocumentoRhVO.setArquivoDocumentoRh(null);
    }

    public void confirmaExcluirAnexoDocumentoRh(ColaboradorDocumentoRhVO obj) {
        this.colaboradorDocumentoRhVO = obj;
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Anexo",
                "Deseja excluir o Anexo '" + obj.getDescricaoDocumento() + "'?",
                this, "excluirAnexoDocumentoRh", "", "", "", "form:tableDocumentosRh");

    }

    public void excluirAnexoDocumentoRh() {
        try {
            MidiaService.getInstance().deleteObject(this.colaboradorDocumentoRhVO.getAnexo(), MidiaEntidadeEnum.ANEXO_ARQUIVOS_RH,this.colaboradorDocumentoRhVO.getIdentificadorAnexo().toString());
            getFacade().getColaboradorDocumentoRh().excluir(this.colaboradorDocumentoRhVO.getCodigo());
            this.colaboradorVO.setListaDocumentoRh(getFacade().getColaboradorDocumentoRh().consultar(colaboradorVO));
            this.colaboradorDocumentoRhVO.setObjetoVOAntesAlteracao(new ColaboradorDocumentoRhVO());
            this.colaboradorDocumentoRhVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(this.colaboradorDocumentoRhVO, colaboradorVO.getCodigo(), "COLABORADOR-Aba RH - Anexo Documentos", colaboradorVO.getPessoa().getCodigo());
            this.colaboradorDocumentoRhVO = null;
            montarSucessoGrowl("Anexo excluído com sucesso!");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public ColaboradorDocumentoRhVO getColaboradorDocumentoRhVO() {
        return colaboradorDocumentoRhVO;
    }

    public void setColaboradorDocumentoRhVO(ColaboradorDocumentoRhVO colaboradorDocumentoRhVO) {
        this.colaboradorDocumentoRhVO = colaboradorDocumentoRhVO;
    }


    public Boolean getEditarUsuarioMovel() {
        return editarUsuarioMovel;
    }

    public void setEditarUsuarioMovel(Boolean editarUsuarioMovel) {
        this.editarUsuarioMovel = editarUsuarioMovel;
    }

    public List<ColaboradorRedeEmpresaVO> getListaColaboradorRedeEmpresa() {
        if (this.listaColaboradorRedeEmpresa == null) {
            this.listaColaboradorRedeEmpresa = new ArrayList<>();
        }
        return listaColaboradorRedeEmpresa;
    }

    public void setListaColaboradorRedeEmpresa(List<ColaboradorRedeEmpresaVO> listaColaboradorRedeEmpresa) {
        this.listaColaboradorRedeEmpresa = listaColaboradorRedeEmpresa;
    }

    public List<SelectItem> getItensCategoriaPessoa(){
        return TipoPessoa.getObterListaSI();
    }

    public String getApresentarCnpj() {
        String cnpj = "";
        if (!UteisValidacao.emptyString(displayIdentificadorFront[2])) {
            cnpj = displayIdentificadorFront[2];
        }
        if (this.listaCamposObrigatorioColaboradorDinamico.contains("CPF")) {
            cnpj = "* " + cnpj;
        }
        return cnpj;
    }

    private void gerarRelatorioExcel(List<ItemRelatorioTO> resultado, File arquivo, boolean sistema) throws  Exception{
        String filtros = montarFiltrosAcesso();
        RelatorioBuilder relatorio = new RelatorioBuilder();
        relatorio.dado(resultado);

        if (sistema) {
            relatorio.titulo("Relatório do Colaborador - Últimos Acessos no Sistema");
        } else {
            relatorio.titulo("Relatório do Colaborador - Últimos Acessos na Catraca");
        }

        for(String filtro : filtros.split("\\|;")){
            String filtroSeparado[] = filtro.split(":");
            relatorio.addFiltro(filtroSeparado[0], filtroSeparado[1]);
        }

        relatorio.addColuna("Data de cadastro", "dataCadastroApresentar")
                .addColuna("Nome","nome")
                .addColuna("Data de nascimento","dataNascimentoApresentar")
                .addColuna("Tipo pessoa","categoria")
                .addColuna("CPF","cpf")
                .addColuna("RG","rg")
                .addColuna("Orgão emissor","rgOrgao")
                .addColuna("Estado de emissão","rgUf")
                .addColuna("Cidade","cidade")
                .addColuna("Estado","estado")
                .addColuna("País","pais")
                .addColuna("Cód. colaborador","codColaborador")
                .addColuna("Logradouro","logradouro")
                .addColuna("Número","numero")
                .addColuna("Complemento","complemento")
                .addColuna("Bairro","bairro")
                .addColuna("CEP","cep")
                .addColuna("Telefone","telefone")
                .addColuna("Email","email")
                .addColuna("Cód. Acesso","ultimoAcesso");
        if (sistema) {
            relatorio.addColuna("Empresa Acesso","nomeEmpresa")
                    .addColuna("Data/Hora","dataHoraUltimoAcessoApresentar")
                    .addColuna("Dia da semana","diaSemanaUltimoAcesso");
        } else {
            relatorio.addColuna("Sentido", "sentidoApresentar")
                    .addColuna("Local de acesso", "localAcesso")
                    .addColuna("Coletor", "coletor")
                    .addColuna("Data/Hora Entrada", "dataHoraSemanaInicioOperacaoApresentar")
                    .addColuna("Data/Hora Saída", "dataHoraSemanaFimOperacaoApresentar")
                    .addColuna("Meio de identificação", "meioIdentificacaoEntrada");
        }

        Exportador.exportarExcel(relatorio, arquivo);
    }

    public String montarFiltrosAcesso() throws Exception {
        StringBuilder filtros = new StringBuilder();
        if (getDataInicio() != null && getDataTermino() != null) {
            filtros.append("Período consulta: ").append(Uteis.getDataAplicandoFormatacao(getDataInicio(), "dd/MM/yyyy")).append(" a ").append(Uteis.getDataAplicandoFormatacao(getDataTermino(), "dd/MM/yyyy")).append(" |; ");
        }
        String filtrosApresentar = filtros.toString();
        if (filtros.toString().lastIndexOf(" |; ") != -1) {
            filtrosApresentar = filtros.substring(0, filtros.toString().lastIndexOf(" |; "));
        }
        return filtrosApresentar;
    }

    public boolean isClienteVinculadoColaborador() {
        return getClienteVinculado() != null && !UteisValidacao.emptyNumber(getClienteVinculado().getCodigo());
    }

    public void confirmarSepararVinculoClienteColaborador() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Dados cadastrais aluno/colaborador",
                "Tem certeza que deseja separar os dados cadastrais?",
                this, "separarVinculoClienteColaborador", "", "", "", "form,panelExistePessoa,panelExisteColaborador,modalVinculoCliente");
    }

    public String separarVinculoClienteColaborador() throws Exception {
        try {
            //buscar vínculo com aluno
            setClienteVinculado(getFacade().getCliente().consultarPorCodigoPessoa(getPessoaVO().getCodigo(), 0, Uteis.NIVELMONTARDADOS_TODOS));

            if (!isClienteVinculadoColaborador()) {
                return "editar";
            }

            PessoaVO pessoaAluno = getClienteVinculado().getPessoa();

            PessoaVO pessoaColaborador = new PessoaVO();
            pessoaColaborador.setCodigo(0);
            pessoaColaborador.setDataCadastro(Calendario.hoje());
            pessoaColaborador.setNome(pessoaAluno.getNome());
            pessoaColaborador.setDataNasc(pessoaAluno.getDataNasc());
            pessoaColaborador.setNomePai(pessoaAluno.getNomePai());
            pessoaColaborador.setCpfPai(pessoaAluno.getCpfPai());
            pessoaColaborador.setRgPai(pessoaAluno.getRgPai());
            pessoaColaborador.setNomeMae(pessoaAluno.getNomeMae());
            pessoaColaborador.setCpfMae(pessoaAluno.getCpfMae());
            pessoaColaborador.setRgMae(pessoaAluno.getRgMae());
            pessoaColaborador.setRg(pessoaAluno.getRg());
            pessoaColaborador.setRgOrgao(pessoaAluno.getRgOrgao());
            pessoaColaborador.setRgUf(pessoaAluno.getRgUf());
            pessoaColaborador.setCidade(pessoaAluno.getCidade());
            pessoaColaborador.setEstadoVO(pessoaAluno.getEstadoVO());
            pessoaColaborador.setPais(pessoaAluno.getPais());
            pessoaColaborador.setEstadoCivil(pessoaAluno.getEstadoCivil());
            pessoaColaborador.setNacionalidade(pessoaAluno.getNacionalidade());
            pessoaColaborador.setSexo(pessoaAluno.getSexo());
            pessoaColaborador.setGrauInstrucao(pessoaAluno.getGrauInstrucao());
            pessoaColaborador.setWebPage(pessoaAluno.getWebPage());
            pessoaColaborador.setGenero(pessoaAluno.getGenero());
            pessoaColaborador.setRne(pessoaAluno.getRne());
            pessoaColaborador.setPassaporte(pessoaAluno.getPassaporte());
            pessoaColaborador.setContatoEmergencia(pessoaAluno.getContatoEmergencia());
            pessoaColaborador.setTelefoneEmergencia(pessoaAluno.getTelefoneEmergencia());
            pessoaColaborador.setEnderecoVOs(new ArrayList());
            pessoaColaborador.setTelefoneVOs(new ArrayList());
            pessoaColaborador.setEmailVOs(new ArrayList());

            pessoaColaborador.setCfp(pessoaAluno.getCfp());
            pessoaAluno.setCfp(null);


            if (pessoaAluno.getEnderecoVOs() != null) {
                for (EnderecoVO item : pessoaAluno.getEnderecoVOs()) {
                    EnderecoVO itemAux = (EnderecoVO) item.getClone(true);
                    itemAux.setObjetoVOAntesAlteracao(new EnderecoVO());
                    itemAux.setCodigo(0);
                    itemAux.setNovoObj(true);
                    pessoaColaborador.getEnderecoVOs().add(itemAux);
                }
            }

            if (pessoaAluno.getTelefones() != null) {
                for (TelefoneVO item : pessoaAluno.getTelefoneVOs()) {
                    TelefoneVO itemAux = (TelefoneVO) item.getClone(true);
                    itemAux.setObjetoVOAntesAlteracao(new EnderecoVO());
                    itemAux.setCodigo(0);
                    itemAux.setNovoObj(true);
                    pessoaColaborador.getTelefoneVOs().add(itemAux);
                }
            }

            if (pessoaAluno.getEmailVOs() != null) {
                for (EmailVO item : pessoaAluno.getEmailVOs()) {
                    EmailVO itemAux = (EmailVO) item.getClone(true);
                    itemAux.setObjetoVOAntesAlteracao(new EmailVO());
                    itemAux.setCodigo(0);
                    itemAux.setNovoObj(true);
                    pessoaColaborador.getEmailVOs().add(itemAux);
                }
            }

            getFacade().getPessoa().separarPessoaColaborador(colaboradorVO.getCodigo(), pessoaColaborador, pessoaAluno);
            registraLogSeparacaoPessoaColaborador(pessoaColaborador.getCodigo(), pessoaColaborador, pessoaAluno);
            registraLogSeparacaoPessoaColaborador(pessoaAluno.getCodigo(), pessoaColaborador, pessoaAluno);

            prepareEditarColaborador(colaboradorVO.getCodigo());

            setMensagemDetalhada("", "");
            setMensagem("Dados cadastrais ALUNO/COLABORADOR separados com sucesso");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            this.getPessoaVO().setApresentarRichModalErro(false);
            this.colaboradorVO.setApresentarRichModalErro(false);
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
        return "editar";
    }

    public void registraLogSeparacaoPessoaColaborador(Integer codigoPessoa, PessoaVO pessoaColaborador, PessoaVO pessoaAluno) throws Exception {
        try {

            LogVO obj = new LogVO();
            obj.setChavePrimaria(codigoPessoa.toString());
            obj.setNomeEntidade("PESSOA");
            obj.setNomeEntidadeDescricao("PESSOA");
            obj.setDescricao("SEPARAÇÃO PESSOA ALUNO/COLABORADOR");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");

            //INFORMAÇÕES DO CADASTRO DO USUÁRIO
            String str = "\nSEPARAÇÃO PESSOA ALUNO/COLABORADOR\n";
            str += "Matrícula aluno: " + getClienteVinculado().getMatricula() + "\n";
            str += "Código colaborador: " + getColaboradorVO().getCodigo() + "\n";
            str += "Código pessoa aluno: " + pessoaAluno.getCodigo() + "\n";
            str += "Código pessoa colaborador: " + pessoaColaborador.getCodigo() + "\n";
            if(!UteisValidacao.emptyString(pessoaColaborador.getCfp())) {
                str += "*** CPF " + pessoaColaborador.getCfp() + " mantido no colaborador e removido do aluno " + "\n";
            }

            obj.setValorCampoAlterado(str);
            obj.setDataAlteracao(Calendario.hoje());
            registrarLogObjetoVO(obj, codigoPessoa);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PESSOA", codigoPessoa, "ERRO AO GERAR LOG DE SEPARAÇÃO PESSOA ALUNO/COLABORADOR", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
}
