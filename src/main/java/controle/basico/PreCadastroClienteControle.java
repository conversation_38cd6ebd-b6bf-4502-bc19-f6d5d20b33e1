/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.financeiro.VendaAvulsaControle;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteSescTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.ParceiroFidelidadeVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.integracao.impl.parceirofidelidade.dotz.ParceiroFidelidadeAPIDotzImpl;
import servicos.integracao.impl.parceirofidelidade.to.RetornoParceiroTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PreCadastroClienteControle extends SuperControle {

    private String termoConsulta = "";
    private List<ClienteVO> clientes = new ArrayList<>();
    private boolean habilitarBotaoNovo = false;
    private String mensagemConsulta = "";
    private boolean maisDeUmaEmpresa;
    private String cpfParceiro;
    private ClienteSescTO clienteSesc;
    private ClienteSescTO clienteSescNovo;
    private boolean apresentarRichModalValidarCpfIguais;
    private boolean novaTelaIncluirClientePadrao = false;
    private boolean novaTelaIncluirClientePadraoEmpresa = false;

    public void validarConsulta() throws Exception {

        if (UteisValidacao.emptyString(termoConsulta)) {
            throw new Exception(getMensagemInternalizacao("informe_um_dos_filtros"));
        }

    }

    public PreCadastroClienteControle() {
        try {
            List<EmpresaVO> empresa = getFacade().getEmpresa().consultarEmpresas();
            maisDeUmaEmpresa = empresa.size() > 1;
            this.processarRecursoPadrao();
        } catch (Exception e) {
            Uteis.logar(e, PreCadastroClienteControle.class);
            maisDeUmaEmpresa = true;
        }
    }

    private void processarRecursoPadrao() {
        try {
            this.novaTelaIncluirClientePadraoEmpresa = getFacade().getUsuario().recursoPadraoEmpresa(TipoInfoMigracaoEnum.INCLUIR_CLIENTE, getEmpresaLogado().getCodigo());
        }catch (Exception e){
            Uteis.logar(e, PreCadastroClienteControle.class);
        }
        try {
            this.novaTelaIncluirClientePadrao = (this.novaTelaIncluirClientePadraoEmpresa ||
                    (getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.INCLUIR_CLIENTE,
                            getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo())));
        }catch (Exception e){
            Uteis.logar(e, PreCadastroClienteControle.class);
        }
    }

    public String novo() {
        this.setMsgAlert("");
        processarRecursoPadrao();
        if (this.novaTelaIncluirClientePadrao) {
            this.verificarAbrirNovaTelaIncluirCliente();
            return "";
        }
        mensagemConsulta = "";
        habilitarBotaoNovo = false;
        limparMsg();
        termoConsulta = "";
        clientes = new ArrayList<ClienteVO>();
        MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);

        boolean ignorarHiistoricoNavegacaoModulo = false;

        if (isApresentarMenuZWUI() && loginControle != null) {
            ignorarHiistoricoNavegacaoModulo = loginControle.isIgnorarHistoricoNavegacaoModulo();
            loginControle.setIgnorarHistoricoNavegacaoModulo(true);
        }

        try {
            //Joao Alcides: estou colocando aqui para limpar a venda avulsa sempre que for adicionar um novo aluno, pois ela influencia
            //no BV a ser mostrado
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) getControlador(VendaAvulsaControle.class.getSimpleName());
            vendaAvulsaControle.novo();
            ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
            clienteControle.setMensagemClienteRestricao("");
            JSFUtilities.storeOnSession(ClienteVO.FLAG_PERMITE_GRAVAR_CLIENTE, Boolean.FALSE);
            clienteControle.novo();
            clienteControle.getClienteVO().setApresentarRichModalCadastroRapido(false);
            clienteControle.getClienteVO().setApresentarBotaoTransferirClienteEmpresa(false);
            clienteControle.getClienteVO().setApresentarRichModalErro(false);
        } catch (Exception e) {
        }

        if (isApresentarMenuZWUI()) {
            setarModuloAberto(ModuloAberto.PESSOAS);
            if (menuControle != null) {
                menuControle.setExibirFavoritos(false);
                menuControle.setExibirConfiguracao(false);
                menuControle.processarLabelBtnVoltar();
                menuControle.setReprocessar(true);
            }
            if (loginControle != null) {
                loginControle.setIgnorarHistoricoNavegacaoModulo(ignorarHiistoricoNavegacaoModulo);
            }
        }

        return "preCadastro";
    }

    public String novoCliente() {
        return prepararNovoCliente(null, false);
    }

    public String prepararNovoClienteComMesmoCpf() {
        return prepararNovoCliente(getClienteSescNovo(), true);
    }

    private String prepararNovoCliente(ClienteSescTO clienteSescTO, boolean habilitarCadastroMesmoCpf) {
        setApresentarRichModalValidarCpfIguais(false);
        setClienteSescNovo(clienteSescTO);
        try {
            ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
            clienteControle.novo();
            clienteControle.getClienteVO().setApresentarRichModalCadastroRapido(false);
            clienteControle.getClienteVO().setApresentarBotaoTransferirClienteEmpresa(false);
            clienteControle.getClienteVO().setApresentarRichModalErro(false);
            clienteControle.getClienteVO().setMsgErroExisteCliente("");

            String textoTmp = Uteis.removerMascara(termoConsulta);
            if (textoTmp.length() == 11 && UteisValidacao.somenteNumeros(textoTmp)) {
                if (UteisValidacao.isValidCPF(termoConsulta)) {
                    clienteControle.getPessoaVO().setCfp(Uteis.formatarCpfCnpj(textoTmp, false));
                    if (habilitarCadastroMesmoCpf) {
                        clienteControle.setIgnorarCPFDuplicado(true);
                    } else {
                        clienteControle.consultarPessoaJaCadastrada();
                    }
                }

            }

            String letras = Uteis.tirarCaracteres(termoConsulta, false);
            clienteControle.getPessoaVO().setNome(
                    letras == null || letras.isEmpty() ? "" : letras.toUpperCase());
            clienteControle.inicializarConfiguracaoSistema(false);

            if (clienteSescTO != null) {
                PaisVO paisVO = getFacade().getPais().consultarPorNome("Brasil", Uteis.NIVELMONTARDADOS_MINIMOS);
                EstadoVO estadoVO = getFacade().getEstado().consultarPorSiglaUf(getClienteSesc().getEndereco().getUf(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                CidadeVO cidadeVO = getFacade().getCidade().consultarPorNome(getClienteSesc().getEndereco().getMunicipio().trim(), Uteis.NIVELMONTARDADOS_MINIMOS);
                CategoriaVO categoriaVO = getFacade().getCategoria().consultarPorNomeExterno(getClienteSesc().getCategoriaCliente(), Uteis.NIVELMONTARDADOS_MINIMOS);
                ClienteVO clienteVO = getClienteSesc().toClienteVO(paisVO, estadoVO, cidadeVO, null, categoriaVO);
                EnderecoVO enderecoVO = getClienteSesc().toEnderecoVO();
                TelefoneVO telefoneFixoVO = getClienteSesc().toTelefoneFixoVO();
                TelefoneVO telefoneCelularVO = getClienteSesc().toTelefoneCelularVO();
                EmailVO emailVO = getClienteSesc().toEmailVO();

                ClienteVO clienteController = clienteControle.getClienteVO();
                clienteVO.setEmpresa(clienteController.getEmpresa());
                clienteVO.setMatricula(clienteController.getMatricula());
                clienteVO.setCodigoMatricula(clienteController.getCodigoMatricula());
                clienteVO.setCodAcesso(clienteController.getCodAcesso());
                clienteVO.setCodAcessoAlternativo(clienteController.getCodAcessoAlternativo());

                clienteVO.setApresentarRichModalCadastroRapido(false);
                clienteVO.setApresentarBotaoTransferirClienteEmpresa(false);
                clienteVO.setApresentarRichModalErro(false);
                clienteVO.setMsgErroExisteCliente("");

                clienteControle.setEmailVO(emailVO);
                clienteControle.setEnderecoResidencialVO(enderecoVO);

                clienteVO.setDataValidadeCarteirinha(clienteSescTO.getDataVencimentoHabilitacaoSesc());

                if (!UteisValidacao.emptyString(clienteSescTO.getCategoriaCliente())) {
                    CategoriaVO categoriaExterna = getFacade().getCategoria().consultarPorNomeExterno(clienteSescTO.getCategoriaCliente(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (categoriaExterna != null && !UteisValidacao.emptyNumber(categoriaExterna.getCodigo())) {
                        clienteVO.setCategoria(categoriaExterna);
                    }
                }

                if (!UteisValidacao.emptyString(telefoneFixoVO.getNumero())) {
                    clienteControle.setTelefoneResidencialVO(telefoneFixoVO);
                }
                if (!UteisValidacao.emptyString(telefoneCelularVO.getNumero())) {
                    clienteControle.setTelefoneCelularVO(telefoneCelularVO);
                }
                if (!UteisValidacao.emptyString(clienteSescTO.getNomePai())) {
                    clienteVO.getPessoa().setNomePai(clienteSescTO.getNomePai());
                }
                if (!UteisValidacao.emptyString(clienteSescTO.getNomeMae())) {
                    clienteVO.getPessoa().setNomeMae(clienteSescTO.getNomeMae());
                }
                if (!UteisValidacao.emptyString(clienteSescTO.getCpfResponsavel()) && clienteSescTO.getNomeResponsavel().equals(clienteVO.getPessoa().getNomePai())) {
                    clienteVO.getPessoa().setCpfPai(clienteSescTO.getCpfResponsavel());
                } else if (!UteisValidacao.emptyString(clienteSescTO.getCpfResponsavel()) && clienteSescTO.getNomeResponsavel().equals(clienteVO.getPessoa().getNomeMae())) {
                    clienteVO.getPessoa().setCpfMae(clienteSescTO.getCpfResponsavel());
                }

                clienteControle.setClienteVO(clienteVO);
                clienteControle.setPessoaVO(clienteVO.getPessoa());

                if (!UteisValidacao.emptyString(clienteSescTO.getNomeResponsavel())) {
                    boolean paiResponsavel = clienteSescTO.getNomeResponsavel().equals(clienteVO.getPessoa().getNomePai());
                    clienteControle.incluirPessoaResponsavel(paiResponsavel);
                }

                clienteControle.inicializarUsuarioLogado();

                clienteControle.montarListaSelectItemEstado("");
                clienteControle.montarListaSelectItemCidade("");
            }
            return "novo";
        } catch (Exception e) {
            setMensagemDetalhada(e);
            ConfiguracaoSistemaVO configuracaoSistemaVO = (ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA);
            if (e.getMessage() != null && e.getMessage().contains("CPF_CADASTRADO") && configuracaoSistemaVO != null && !configuracaoSistemaVO.isValidarCpfDuplicado()) {
                setApresentarRichModalValidarCpfIguais(true);
            }
            return "";
        }

    }

    public String converterPassivoIndicado() {
        try {
            ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");

            ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
            clienteControle.novo();

            String nome = "";
            List<TelefoneVO> telefoneVOList = new ArrayList<TelefoneVO>();
            List<EmailVO> emailVOList = new ArrayList<EmailVO>();

            if (obj.getCodigoIndicado() != 0) {
                IndicadoVO indicadoVO = getFacade().getIndicado().consultarPorChavePrimaria(obj.getCodigoIndicado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                nome = indicadoVO.getNomeIndicado();
                clienteControle.getPessoaVO().setCfp(indicadoVO.getCpf() != null ? indicadoVO.getCpf() : null);

                //CELULAR INDICADO
                if (!indicadoVO.getTelefoneIndicado().isEmpty()) {
                    TelefoneVO telCelular = new TelefoneVO();
                    telCelular.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
                    telCelular.setNumero(indicadoVO.getTelefoneIndicado());
                    clienteControle.setTelefoneCelularVO(telCelular);
                    telefoneVOList.add(telCelular);
                }

                //TEL RESIDENCIAL INDICADO
                if (!indicadoVO.getTelefone().isEmpty()) {
                    TelefoneVO telResidencial = new TelefoneVO();
                    telResidencial.setTipoTelefone(TipoTelefoneEnum.RESIDENCIAL.getCodigo());
                    telResidencial.setNumero(indicadoVO.getTelefone());
                    clienteControle.setTelefoneResidencialVO(telResidencial);
                    telefoneVOList.add(telResidencial);
                }

                //EMAIL INDICADO
                if (!indicadoVO.getEmail().isEmpty()) {
                    EmailVO email = new EmailVO();
                    email.setEmailCorrespondencia(true);
                    email.setEmail(indicadoVO.getEmail());
                    clienteControle.setEmailVO(email);
                    emailVOList.add(email);
                }

            } else if (obj.getCodigoPassivo() != 0) {
                PassivoVO passivoVO = getFacade().getPassivo().consultarPorChavePrimaria(obj.getCodigoPassivo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                nome = passivoVO.getNome();

                //CELULAR PASSIVO
                if (!passivoVO.getTelefoneCelular().isEmpty()) {
                    TelefoneVO telCelular = new TelefoneVO();
                    telCelular.setTipoTelefone(TipoTelefoneEnum.CELULAR.getCodigo());
                    telCelular.setNumero(passivoVO.getTelefoneCelular());
                    clienteControle.setTelefoneCelularVO(telCelular);
                    telefoneVOList.add(telCelular);
                }

                //TEL RESIDENCIAL PASSIVO
                if (!passivoVO.getTelefoneResidencial().isEmpty()) {
                    TelefoneVO telResidencial = new TelefoneVO();
                    telResidencial.setTipoTelefone(TipoTelefoneEnum.RESIDENCIAL.getCodigo());
                    telResidencial.setNumero(passivoVO.getTelefoneResidencial());
                    clienteControle.setTelefoneResidencialVO(telResidencial);
                    telefoneVOList.add(telResidencial);
                }

                //TEL COMERCIAL PASSIVO
                if (!passivoVO.getTelefoneTrabalho().isEmpty()) {
                    TelefoneVO telComercial = new TelefoneVO();
                    telComercial.setTipoTelefone(TipoTelefoneEnum.COMERCIAL.getCodigo());
                    telComercial.setNumero(passivoVO.getTelefoneTrabalho());
                    clienteControle.setTelefoneComercialVO(telComercial);
                    telefoneVOList.add(telComercial);
                }

                //EMAIL INDICADO
                if (!passivoVO.getEmail().isEmpty()) {
                    EmailVO email = new EmailVO();
                    email.setEmailCorrespondencia(true);
                    email.setEmail(passivoVO.getEmail());
                    clienteControle.setEmailVO(email);
                    emailVOList.add(email);
                }
            }

            clienteControle.getClienteVO().setApresentarRichModalCadastroRapido(false);
            clienteControle.getClienteVO().setApresentarBotaoTransferirClienteEmpresa(false);
            clienteControle.getClienteVO().setApresentarRichModalErro(false);
            clienteControle.getClienteVO().setMsgErroExisteCliente("");
            clienteControle.getPessoaVO().setNome(nome);
            clienteControle.getClienteVO().setCodigoIndicado(obj.getCodigoIndicado());
            clienteControle.getClienteVO().setCodigoPassivo(obj.getCodigoPassivo());
            clienteControle.getPessoaVO().setTelefoneVOs(telefoneVOList);
            clienteControle.getPessoaVO().setEmailVOs(emailVOList);
            clienteControle.inicializarConfiguracaoSistema(false);
            return "novo";
        } catch (Exception e) {
            setMensagemDetalhada(e);
            return "";
        }

    }

    public void consultarPreCadastro() {
        try {
            setMsgAlert("");
            mensagemConsulta = "";
            habilitarBotaoNovo = false;
            clientes = new ArrayList<>();
            limparMsg();
            limparMsgClienteRestricao();
            validarConsulta();
            termoConsulta = StringUtilities.vaildaApostofre(termoConsulta);
            clientes = getFacade().getCliente().consultarPreCadastro(getKey(),
                    termoConsulta,
                    null, maisDeUmaEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<ClienteVO> passivos = getFacade().getCliente().consultarPreCadastroPassivo(termoConsulta, maisDeUmaEmpresa);
            List<ClienteVO> indicados = getFacade().getCliente().consultarPreCadastroIndicado(termoConsulta, maisDeUmaEmpresa);

            if (!UteisValidacao.emptyList(passivos)) {
                clientes.addAll(passivos);
            }
            if (!UteisValidacao.emptyList(indicados)) {
                clientes.addAll(indicados);
            }

            if (clientes != null && !clientes.isEmpty() && clientes.size() > 1) {
                mensagemConsulta = "Exibindo " + clientes.size() + " resultados próximos a \"" + termoConsulta + "\"";
            } else if (clientes.size() < 1) {
                mensagemConsulta = "Nenhum resultado para \"" + termoConsulta + "\"";
            }

            if (getEmpresaLogado().isUtilizaGestaoClientesComRestricoes()) {
                ClienteControle clienteControle = getControlador(ClienteControle.class);
                if (clienteControle != null && clienteControle.clientePossuiRestricao(termoConsulta)) {
                    return;
                }
            }

            habilitarBotaoNovo = true;

            final ConfiguracaoSistemaVO configuracaoSistemaVO = (ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA);
            if (configuracaoSistemaVO != null && configuracaoSistemaVO.getSesc()) {
                final boolean cpf = termoConsulta.matches("\\d{3}.\\d{3}.\\d{3}-\\d{2}") || termoConsulta.matches("\\d{11}");
                final String termoConsultaFormatado = cpf ? Uteis.removerMascara(termoConsulta) : termoConsulta;

                integracaoSescGO(configuracaoSistemaVO, cpf, termoConsultaFormatado);
                integracaoSescDF(cpf, termoConsultaFormatado);
            }


        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    void integracaoSescGO(ConfiguracaoSistemaVO configuracaoSistemaVO, boolean cpf, String termoConsultaFormatado) throws Exception {
        if (!UteisValidacao.emptyString(configuracaoSistemaVO.getChavePublicaSESC())) {
            final boolean apenasNumeros = termoConsulta.matches("\\d+");
            final boolean matriculaSesc = termoConsulta.matches("\\d{4}-\\d{6}-\\d");

            ClienteSescTO clienteSescTO = null;

            if (configuracaoSistemaVO.getApiSescGo() && (cpf || apenasNumeros)) {
                clienteSescTO = getFacade().getCliente().consultarPorCpfOuMatriculaApiSescGo(termoConsultaFormatado, configuracaoSistemaVO);
            } else {
                if (matriculaSesc) {
                    clienteSescTO = getFacade().getCliente().consultarPorMatriculaSESC(termoConsultaFormatado, configuracaoSistemaVO);
                } else if (cpf) {
                    clienteSescTO = getFacade().getCliente().consultarPorCpfSESC(termoConsultaFormatado, configuracaoSistemaVO);
                }
            }

            if (clienteSescTO != null) {
                formatarDadosSesc(clienteSescTO);
                setClienteSesc(clienteSescTO);

                MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
                control.setMensagemDetalhada("", "");
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                if (clienteSescTO.getDataVencimentoHabilitacaoSesc().before(Calendario.hoje())) {
                    control.init("Incluir cliente SESC",
                            "O cliente <b>" + clienteSescTO.getNome() + "</b> (" + clienteSescTO.getHabilitacao() + ") está com a habilitação vencida.",
                            this, "Fechar", "", "");
                } else {
                    control.init("Incluir cliente SESC",
                            "Encontrado o cliente <b>" + clienteSescTO.getNome() + "</b> (" + clienteSescTO.getHabilitacao() + ").<br/>Deseja importar?",
                            this, "incluirClienteSesc", "", "", "", "grupoBtnExcluir");
                }
            }
        }
    }

    void integracaoSescDF(boolean cpf, String termoConsultaFormatado) throws Exception {
        if (!clientes.isEmpty()) {
            return;
        }

        final EmpresaVO empresa = getEmpresaLogado();
        if (empresa.getUsarSescDf() && cpf) {
            final ClienteSescTO clienteSescTO = getFacade().getCliente().consultarPorCpfApiSescDf(termoConsultaFormatado, empresa.getTokenSescDf());

            if (clienteSescTO != null) {
                formatarDadosSesc(clienteSescTO);
                setClienteSesc(clienteSescTO);

                MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
                control.setMensagemDetalhada("", "");
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");

                control.init("Incluir cliente SESC DF",
                        "Encontrado o cliente <b>" + clienteSescTO.getNome() + "</b> (" + clienteSescTO.getHabilitacao() + ").<br/>Deseja importar?",
                        this, "incluirClienteSesc", "", "", "", "grupoBtnExcluir");
            }

        }
    }

    private void limparMsgClienteRestricao() {
        try {
            ClienteControle clienteControle = getControlador(ClienteControle.class);
            clienteControle.setMensagemClienteRestricao("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void formatarDadosSesc(ClienteSescTO clienteSescTO) {
        if (clienteSescTO.getEstadoCivil() != null) {
            switch (clienteSescTO.getEstadoCivil().toUpperCase()) {
                case "SOLTEIRO":
                    clienteSescTO.setEstadoCivil("S");
                    break;
                case "CASADO":
                    clienteSescTO.setEstadoCivil("C");
                    break;
                case "AMASIADO":
                    clienteSescTO.setEstadoCivil("A");
                    break;
                case "VIUVO":
                    clienteSescTO.setEstadoCivil("V");
                    break;
                case "DIVORCIADO":
                    clienteSescTO.setEstadoCivil("D");
                    break;
                case "SEPARADO":
                    clienteSescTO.setEstadoCivil("P");
                    break;
                case "UNIAOESTAVEL":
                    clienteSescTO.setEstadoCivil("U");
                    break;
                default:
                    clienteSescTO.setEstadoCivil(null);
                    break;
            }
        }
    }

    public String incluirClienteSesc() {
        return prepararNovoCliente(getClienteSesc(), false);
    }

    public void consultarSaldoParceiroFidelidade() {
        try {
            mensagemConsulta = "";
            habilitarBotaoNovo = false;
            clientes = new ArrayList<>();
            limparMsg();

            if (UteisValidacao.emptyString(getCpfParceiro())) {
                throw new Exception("Informe um CPF para consultar o saldo DOTZ.");
            }

            if (!SuperVO.verificaCPF(getCpfParceiro())) {
                throw new Exception("Informe um CPF válido.");
            }

            ParceiroFidelidadeVO parceiroFidelidadeVO = getFacade().getParceiroFidelidade().consultarPorEmpresaETipo(getEmpresaLogado().getCodigo(), TipoParceiroEnum.DOTZ, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ParceiroFidelidadeAPIDotzImpl parceiroFidelidadeAPIDotz = new ParceiroFidelidadeAPIDotzImpl(parceiroFidelidadeVO);
            RetornoParceiroTO retornoParceiroTO = parceiroFidelidadeAPIDotz.consultarSaldo(getCpfParceiro(), true);
            mensagemConsulta = retornoParceiroTO.getMensagem();
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void fecharApresentarRichModalValidarCpfIguais() {
        setApresentarRichModalValidarCpfIguais(false);
    }

    public List<ClienteVO> getClientes() {
        return clientes;
    }

    public void setClientes(List<ClienteVO> clientes) {
        this.clientes = clientes;
    }

    public String getTermoConsulta() {
        return termoConsulta;
    }

    public void setTermoConsulta(String termoConsulta) {
        this.termoConsulta = termoConsulta;
    }

    public boolean isHabilitarBotaoNovo() {
        return habilitarBotaoNovo;
    }

    public void setHabilitarBotaoNovo(boolean habilitarBotaoNovo) {
        this.habilitarBotaoNovo = habilitarBotaoNovo;
    }

    public String getMensagemConsulta() {
        return mensagemConsulta;
    }

    public void setMensagemConsulta(String mensagemConsulta) {
        this.mensagemConsulta = mensagemConsulta;
    }

    public boolean isMaisDeUmaEmpresa() {
        return maisDeUmaEmpresa;
    }

    public void setMaisDeUmaEmpresa(boolean maisDeUmaEmpresa) {
        this.maisDeUmaEmpresa = maisDeUmaEmpresa;
    }

    public Integer getCodigoEmpresa() {
        try {
            return getEmpresaLogado().getCodigo();
        } catch (Exception e) {
            return 0;
        }
    }

    public String getCpfParceiro() {
        if (cpfParceiro == null) {
            cpfParceiro = "";
        }
        return cpfParceiro;
    }

    public void setCpfParceiro(String cpfParceiro) {
        this.cpfParceiro = cpfParceiro;
    }

    public ClienteSescTO getClienteSesc() {
        if (clienteSesc == null) {
            clienteSesc = new ClienteSescTO();
        }
        return clienteSesc;
    }

    public void setClienteSesc(ClienteSescTO clienteSesc) {
        this.clienteSesc = clienteSesc;
    }

    public ClienteSescTO getClienteSescNovo() {
        return clienteSescNovo;
    }

    public void setClienteSescNovo(ClienteSescTO clienteSescNovo) {
        this.clienteSescNovo = clienteSescNovo;
    }

    public boolean isApresentarRichModalValidarCpfIguais() {
        return apresentarRichModalValidarCpfIguais;
    }

    public void setApresentarRichModalValidarCpfIguais(boolean apresentarRichModalValidarCpfIguais) {
        this.apresentarRichModalValidarCpfIguais = apresentarRichModalValidarCpfIguais;
    }

    public boolean isNovaTelaIncluirClientePadrao() {
        return novaTelaIncluirClientePadrao;
    }

    public void setNovaTelaIncluirClientePadrao(boolean novaTelaIncluirClientePadrao) {
        this.novaTelaIncluirClientePadrao = novaTelaIncluirClientePadrao;
    }

    public boolean isNovaTelaIncluirClientePadraoEmpresa() {
        return novaTelaIncluirClientePadraoEmpresa;
    }

    public void setNovaTelaIncluirClientePadraoEmpresa(boolean novaTelaIncluirClientePadraoEmpresa) {
        this.novaTelaIncluirClientePadraoEmpresa = novaTelaIncluirClientePadraoEmpresa;
    }

    public void verificarAbrirNovaTelaIncluirCliente() {
        try {
            this.setMsgAlert("");
            this.processarRecursoPadrao();
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            if (this.isNovaTelaIncluirClientePadrao() &&
                    loginControle != null &&
                    loginControle.isApresentarModuloNovoTreino()) {
                this.abrirNovaTelaIncluirCliente();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void abrirNovaTelaIncluirCliente() {
        try {
            limparMsg();
            setMsgAlert("");
            getFacade().getUsuario().gravarRecurso(TipoInfoMigracaoEnum.INCLUIR_CLIENTE, getUsuarioLogado().getCodigo(), "true", getUsuarioLogado());
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String openWindow = "window.open('"
                    + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_ZW.getSiglaModulo())
                    + "&redirect=/adm/adicionar-cliente', '_self')";
            notificarRecursoEmpresa(RecursoSistema.PADRAO_NOVA_TELA_INCLUIR_CLIENTE);
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }
}
