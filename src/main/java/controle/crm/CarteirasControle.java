package controle.crm;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.FaixaHorarioAcessoClienteVO;
import negocio.comuns.crm.FiltroCarteiraTO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.crm.InfoCarteiraTO;
import negocio.comuns.crm.VinculosTipoTO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.utilitarias.gerador.CampoGR;
import negocio.comuns.utilitarias.gerador.ItemFiltroGR;
import negocio.comuns.utilitarias.gerador.TipoCampoGREnum;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import servicos.integracao.treino.dto.SinteticoMsDTO;

import static negocio.comuns.crm.FiltroCarteiraTO.descobrirTotalQuantidadeVinculoFiltroSelecionado;

/**
 * Created with IntelliJ IDEA. User: glauco Date: 02/09/13 Time: 13:49
 */
public class CarteirasControle extends SuperControle {

    private static final Integer ADICIONAR = 1;
    private static final Integer TRANSFERIR = 2;
    private static final Integer REMOVER = 3;
    private List<SelectItem> listaTiposColaboradores = new ArrayList<SelectItem>();
    private List<SelectItem> listaColaboradores = new ArrayList<SelectItem>();
    private List<SelectItem> listaPeriodoAcesso = new ArrayList<SelectItem>();
    private List<SelectItem> listaSituacaoCliente = new ArrayList<SelectItem>();
    private List<FiltroCarteiraTO> listaSugestoes = new ArrayList<FiltroCarteiraTO>();
    private List<InfoCarteiraTO> listaVerColaboradores = new ArrayList<InfoCarteiraTO>();
    private FiltroCarteiraTO filtroCarteira = new FiltroCarteiraTO();
    private FiltroCarteiraTO filtroBuscarCarteira = new FiltroCarteiraTO();
    private FiltroCarteiraTO filtroOrganizarCarteira = new FiltroCarteiraTO();
    private FiltroCarteiraTO filtroVerTodasCarteiras = new FiltroCarteiraTO();
    private FiltroCarteiraTO destino = new FiltroCarteiraTO();
    private Integer operacao = 0;
    private List<VinculoVO> vinculosAlterados = new ArrayList<VinculoVO>();
    private List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
    private List<SelectItem> listaCamposFiltroAvancado = new ArrayList<SelectItem>();
    private List<CampoGR> camposFiltroAvancado = new ArrayList<CampoGR>();
    private List<ItemFiltroGR> filtrosAvancados = new ArrayList<ItemFiltroGR>();
    private boolean apresentarFiltrosAvancados = false;
    private ConfiguracaoSistemaCRMVO configuracaoSistema = new ConfiguracaoSistemaCRMVO();
    private String nomeClasse = "Carteiras";
    private String dispararDataTables = "";
    private List<Integer> codigosAgendamentosTransferir;
    private Boolean transferirAgendamentos;
    private Boolean colaboradorSelecionadoPossuiUsuario;
    private Boolean habilitarBotaoConfirmarTransferencia = Boolean.TRUE;
    private Integer totalClientes;
    private int qtdItensCasosImportantes = 1000;
    private Date dataOperacaoTransarencia = Calendario.hoje();
    private int codUsuarioResponsavelTransf = 0;
    private List<SelectItem> selectUsuariosOperacaoTransf;
    private int quantidadeClienteSelecionado;
    /**
     * Representa o número informado no modal 'Organizar' para transferor aleatóriamente sob demanda.
     */
    private Integer quantidadeAlunosParaTransferencia;
    /**
     * Se habilitado, irar transferir por quantidade e aleatório, conforme o valor em {@link #quantidadeAlunosParaTransferencia}.
     */
    private boolean transferirPorQuantidade;
    /**
     * Representa o número de alunos marcados na lista.
     */
    private Integer quantidadeAlunosSelecionadosParaTransferencia;
    private String mensagemAlertaValidacaoTransferencia;
    private String idModalTransferencia = "filtrarConfirma";
    private boolean marcarTodos = false;
    private boolean marcarTodosPagina = false;
    private boolean buscar;
    private boolean casosImportantesAvaliar = false;
    private Boolean clientesSemProfessor = false;
    private Boolean ColabInativosVinculos = false;
    private Boolean clienteComVinculos = false;
    private Boolean ClientesSemCon = false;
    private Boolean consultorCartVazia = false;
    private Boolean panelCarteiras = false;
    private Boolean panelTodasCarteiras = false;
    private String resultado = "";
    private String onCompleteDetalhes = "";

    public CarteirasControle() throws Exception {
        inicializar();
    }

    public void inicializar() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        consultarSugestoes(this.qtdItensCasosImportantes);
        carregarListaTiposColaboradores();
        carregarListaColaboradores();
        carregarListaEmpresas();
        carregarListaPeriodosAcesso();
        carregarListaSituacaoCliente();
        carregarListaCamposConsulta();
        carregarListaCamposFiltroAvancado();
        montarUsuarioOperacaoTransferencia();
    }

    private void consultarSugestoes(int qtdItens) throws Exception {
        setListaSugestoes(getFacade().getCarteirasRel().consultarSugestoes(obtenhaCodigoDaEmpresaSelecionada(),
                getConfiguracaoSistema().isConsiderarProfessorTreinoWeb(), qtdItens));

        for(FiltroCarteiraTO filtro : listaSugestoes){

            if(filtro.isClientesSemVinculo() && filtro.getTipoVinculo().equals("PR")){
                setClientesSemProfessor(true);
            }

            if(filtro.isAlunosTreinoSemVinculo()){
                setClientesSemProfessor(true);
            }

            if(filtro.isConsultorInativoIrregular()){
                setColabInativosVinculos(true);
            }

            if(filtro.isClientesInconsistentes()){
                setClienteComVinculos(true);
            }

            if (filtro.isClientesSemVinculo() && filtro.getTipoVinculo().equals("CO")) {
                setClientesSemCon(true);
            }

            if(filtro.isConsultorCarteiraVazia()){
                setConsultorCartVazia(true);
            }
        }
    }

    public void atualizarSugestoes() throws Exception {
        consultarSugestoes(getQtdItensCasosImportantes());
    }

    public Object carregarListaTiposColaboradores() {
        setListaTiposColaboradores(new ArrayList<SelectItem>());
        getListaTiposColaboradores().add(new SelectItem("", "-"));

        for (TipoColaboradorEnum tipoColaborador : TipoColaboradorEnum.values()) {
            if (!tipoColaborador.equals(TipoColaboradorEnum.FORNECEDOR)) {
                getListaTiposColaboradores().add(new SelectItem(tipoColaborador.getSigla(), tipoColaborador.getDescricao()));
            }
            Ordenacao.ordenarLista(getListaTiposColaboradores(), "label");
        }
        return true;
    }

    public Object carregarListaColaboradores() throws Exception {
        setListaColaboradores(new ArrayList<>());
        getListaColaboradores().add(new SelectItem("", "-"));
        if (!getFiltroCarteira().getTipoVinculo().isEmpty()) {
            TipoColaboradorEnum tipoColaboradorEnum = TipoColaboradorEnum.getTipo(getFiltroCarteira().getTipoVinculo());
            List<ColaboradorVO> colaboradorVOs = getFacade().getColaborador().consultarPorNomeTipoVinculoPossivel(
                    "", tipoColaboradorEnum, obtenhaCodigoDaEmpresaSelecionada(), Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA);
            for (ColaboradorVO colaboradorVO : colaboradorVOs) {
                getListaColaboradores().add(new SelectItem(colaboradorVO.getCodigo(), colaboradorVO.getPessoa_Apresentar()));
            }
        }
        return true;
    }

    public Object carregarListaEmpresas() throws Exception {
        List<EmpresaVO> empresaVOs = getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        setListaEmpresas(new ArrayList<SelectItem>());
        getListaEmpresas().add(new SelectItem("", "-"));
        for (EmpresaVO empresaVO : empresaVOs) {
            getListaEmpresas().add(new SelectItem(empresaVO.getCodigo(), empresaVO.getNome()));
        }
        return true;
    }

    public Object carregarListaPeriodosAcesso() throws Exception {
        setListaPeriodoAcesso(new ArrayList<SelectItem>());
        getListaPeriodoAcesso().add(new SelectItem("", "-"));
        List<FaixaHorarioAcessoClienteVO> faixas = getFacade().getFaixaHorarioAcessoCliente().consultarFaixas();
        for (FaixaHorarioAcessoClienteVO faixa : faixas) {
            getListaPeriodoAcesso().add(new SelectItem(faixa.getNomePeriodo(), faixa.getNomePeriodo()));
        }
        getListaPeriodoAcesso().add(new SelectItem("semAcesso", "Sem acesso"));
        return true;
    }

    public Object carregarListaSituacaoCliente() throws Exception {
        setListaSituacaoCliente(new ArrayList<SelectItem>());
        getListaSituacaoCliente().add(new SelectItem("", "-"));
        for (SituacaoClienteEnum situacaoCliente : SituacaoClienteEnum.values()) {
            getListaSituacaoCliente().add(new SelectItem(situacaoCliente.getCodigo(), situacaoCliente.getDescricao()));
        }
        // Adicionando filtros específicos
        getListaSituacaoCliente().add(new SelectItem("AT,VE", "Ativo e Vencido"));
        return true;
    }

    public Object carregarListaCamposConsulta() throws Exception {
        setCamposFiltroAvancado(new ArrayList<CampoGR>());
        getCamposFiltroAvancado().add(new CampoGR("sdw.datamatricula","","", "Data de Matrícula", TipoCampoGREnum.DATA));
        getCamposFiltroAvancado().add(new CampoGR("cont.situacaocontrato = 'RE' AND cont.datalancamento","","", "Data da Última Rematrícula", TipoCampoGREnum.DATA));
        getCamposFiltroAvancado().add(new CampoGR("cont.situacaocontrato = 'RN' AND cont.datalancamento","","", "Data de Renovação do Contrato", TipoCampoGREnum.DATA));
        getCamposFiltroAvancado().add(new CampoGR("cont.datarenovarrealizada is null AND cont.datarematricularealizada is null AND sdw.datavigenciaateajustada","","", "Data de Vencimento", TipoCampoGREnum.DATA));
        getCamposFiltroAvancado().add(new CampoGR("cli.categoria ","","categoriaCliente", "Categoria Cliente", TipoCampoGREnum.CHAVE_ESTRANGEIRA));
        getCamposFiltroAvancado().add(new CampoGR("pl.descricao ","","planoCliente", "Plano Cliente", TipoCampoGREnum.CHAVE_ESTRANGEIRA));
        return true;
    }

    public Object carregarListaCamposFiltroAvancado() {
        setListaCamposFiltroAvancado(new ArrayList<SelectItem>());
        getListaCamposFiltroAvancado().add(new SelectItem("", "-"));
        for (CampoGR campo : getCamposFiltroAvancado()) {
            getListaCamposFiltroAvancado().add(new SelectItem(campo.getColuna(), campo.getNomeApresentar()));
        }
        return true;
    }

    public Object inicializarFiltrosAvancado() {
        setApresentarFiltrosAvancados(true);
        if(getFiltrosAvancados().size() == 0) {
            getFiltrosAvancados().add(new ItemFiltroGR());
        }
        return true;
    }

    public Object retirarFiltrosAvancados() {
        setApresentarFiltrosAvancados(false);
        setFiltrosAvancados(new ArrayList<ItemFiltroGR>());
        return true;
    }

    public void carregarListaGrupoColaborador(ActionEvent event) throws Exception {
        FiltroCarteiraTO filtroCarteiraTO = (FiltroCarteiraTO) event.getComponent().getAttributes().get("filtro");
        carregarListaGrupoColaborador(filtroCarteiraTO);
        consultarCarteiras(getFiltroOrganizarCarteira());
    }

    public void carregarListaGrupoColaborador(FiltroCarteiraTO filtroCarteiraTO) throws Exception {
        filtroCarteiraTO.setListaGrupoColaborador(new ArrayList<>());
        filtroCarteiraTO.getListaGrupoColaborador().add(new SelectItem("", "-"));
        if (!filtroCarteiraTO.getTipoVinculo().isEmpty()) {
            List<GrupoColaboradorVO> grupoColaboradorVOs = getFacade().getGrupoColaborador().
                    consultarPorTipoGrupo(
                    filtroCarteiraTO.getTipoVinculo(),
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS,
                    obtenhaCodigoDaEmpresaSelecionada());
            for (GrupoColaboradorVO grupoColaborador : grupoColaboradorVOs) {
                filtroCarteiraTO.getListaGrupoColaborador().add(
                        new SelectItem(grupoColaborador.getCodigo(), grupoColaborador.getDescricao()));
            }
        }else  if(filtroCarteiraTO != null && UteisValidacao.emptyString(filtroCarteiraTO.getTipoVinculo())){
            List<GrupoColaboradorVO> grupoColaboradorVOs = getFacade().getGrupoColaborador().
                    consultarPorOrganizadorCarteira(filtroCarteiraTO.getEmpresaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (GrupoColaboradorVO grupoColaborador : grupoColaboradorVOs) {
                filtroCarteiraTO.getListaGrupoColaborador().add(
                        new SelectItem(grupoColaborador.getCodigo(), grupoColaborador.getDescricao()));
            }
        }
    }

    public void verificarPossibilidadeTransferirVinculosFuturos(ActionEvent event) throws  Exception{
       this.habilitarBotaoConfirmarTransferencia = !(!this.colaboradorSelecionadoPossuiUsuario && this.transferirAgendamentos);
    }

    public Object alterarEmpresa() throws Exception {
        consultarSugestoes(this.qtdItensCasosImportantes);
        carregarListaColaboradores();
        getFiltroCarteira().setTipoVinculo("");
        return true;
    }

    public void alterarTipoColaborador(ActionEvent event) throws Exception {
        getFiltroCarteira().setColaborador(new ColaboradorVO());
        carregarListaColaboradores();
        carregarListaGrupoColaborador(event);
    }

    public Object alterarListaVerColaboradores() throws Exception {
        getFiltroCarteira().getEmpresaVO().setCodigo(obtenhaCodigoDaEmpresaSelecionada());
        setListaVerColaboradores(getFacade().getCarteirasRel().consultarColaboradores(getFiltroCarteira(),false));
        return true;
    }

    private Integer obtenhaCodigoDaEmpresaSelecionada() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return getFiltroCarteira().getEmpresaVO().getCodigo();
        } else {
            return getEmpresaLogado().getCodigo();
        }
    }

    private void prepararFiltroEBuscar(FiltroCarteiraTO filtroCarteiraTO, boolean reconsulta, Integer quantidadeClienteSelecionado) throws Exception {
        setFiltroBuscarCarteira(filtroCarteiraTO);
        if (getFiltroBuscarCarteira().getEmpresaVO().getCodigo() == 0) {
            getFiltroBuscarCarteira().getEmpresaVO().setCodigo(obtenhaCodigoDaEmpresaSelecionada());
        }
        preencherColaboradorSelecionado(filtroCarteiraTO);
        getFiltroBuscarCarteira().setListaDeVinculos(new ArrayList<FiltroCarteiraTO>());
        if (getFiltroBuscarCarteira().getCodigoColaborador() > 0) {
            preencherQuantidadeVinculosColaboradorSelecionado();
        }

        if (!UteisValidacao.emptyString(filtroCarteiraTO.getTipoVinculo())) {
            getFiltroOrganizarCarteira().setTipoVinculo(filtroCarteiraTO.getTipoVinculo());
            carregarListaGrupoColaborador(filtroCarteiraTO);
        }
        resultado = "";
        if (reconsulta) filtroCarteiraTO.setQtdVinculos(filtroCarteiraTO.getQtdVinculos() - quantidadeClienteSelecionado );
        if(filtroCarteiraTO.isSugestao() && filtroCarteiraTO.getNomeColaborador().isEmpty() && !filtroCarteiraTO.getTipoVinculo().isEmpty()){
            resultado = filtroCarteiraTO.getQtdVinculos() + " clientes ativos sem " + filtroCarteiraTO.getTipoVinculo_Apresentar();
            if(filtroCarteiraTO.getQtdVinculos() == 0 ) filtroCarteiraTO.setQtdVinculos(1);
        }
        colocarNaSessao(getFiltroBuscarCarteira());
    }

    private void colocarNaSessao(FiltroCarteiraTO filtroCarteiraTO) {
        JSFUtilities.storeOnSession("filtroCarteiraTO", filtroCarteiraTO);
    }

    public String obterFiltrosAvancados() throws Exception {
        StringBuilder sb = new StringBuilder();

        for (ItemFiltroGR item : filtrosAvancados) {
            item.validarItem();
            if (!item.getCampoGR().getColuna().isEmpty()) {
                sb.append(item.obterSQL());
            }
        }

        return sb.toString();
    }

    public List<SelectItem> obterListaValoresFK(String entidade) throws Exception{
        if(entidade.equals("categoriaCliente")){
            List lista = getFacade().getCategoria().consultarPorNome("",false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
          return  JSFUtilities.getSelectItemListFrom(lista,"nome","codigo",false,false);
        }
        if(entidade.equals("planoCliente")){
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            List lista = getFacade().getPlano().consultarPorNomeEmpresa(loginControle.getEmpresa().getNome(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return  JSFUtilities.getSelectItemListFrom(lista,"descricao","descricao",false,false);
        }
      return null;
    }
    public String obterFiltrosJoinTabela() throws Exception {
        StringBuilder sb = new StringBuilder();

        for (ItemFiltroGR item : filtrosAvancados) {
            item.validarItem();
            if (!item.getCampoGR().getTabelaJoin().isEmpty() && item.getCampoGR().getTipoCampo() == TipoCampoGREnum.CHAVE_ESTRANGEIRA) {
                sb.append(item.getCampoGR().getTabelaJoin());
            }
        }

        return sb.toString();
    }
    public String obterTextoFiltrosAvancados() throws Exception {
        StringBuilder sb = new StringBuilder();

        for (ItemFiltroGR item : filtrosAvancados) {
            item.validarItem();
            if (!item.getValor1().toString().isEmpty()) {
                sb.append(item.obterTexto());
            }
        }

        return sb.toString();
    }

    public String buscarFiltroSelecionado() {
        setCasosImportantesAvaliar(false);
        limparMsg();
        setBuscar(true);
        setPanelTodasCarteiras(false);
        setPanelCarteiras(true);
        setMarcarTodos(false);
        setMarcarTodosPagina(false);
        this.quantidadeClienteSelecionado = 0;
        try {
            if(getFiltroCarteira().getColaborador().getCodigo() == 0){
                setPanelTodasCarteiras(true);
                setPanelCarteiras(false);
                setFiltroVerTodasCarteiras(getFiltroCarteira());
                consultarCarteiras(getFiltroCarteira());
            }
            prepararFiltroEBuscar(getFiltroCarteira(), false,0);
            setMensagemDetalhada("", "");
            return "carteirasCRM";
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
        return "";
    }

    public void escolherSugestao() throws Exception {
        setCasosImportantesAvaliar(true);
        setBuscar(false);
        setPanelTodasCarteiras(false);
        setPanelCarteiras(true);
        setMarcarTodos(false);
        setMarcarTodosPagina(false);
        this.quantidadeClienteSelecionado = 0;
        prepararFiltroEBuscar((FiltroCarteiraTO) JSFUtilities.getFromRequest("sugestao"), false,0);
    }

    public void reconsultar() throws Exception {
        if (getFiltroBuscarCarteira().isSugestao()) {
            prepararFiltroEBuscar(getFiltroBuscarCarteira(), true, this.quantidadeClienteSelecionado );
            this.quantidadeClienteSelecionado = 0;
        } else {
            buscarFiltroSelecionado();
        }
    }

    public void preencherColaboradorSelecionado(FiltroCarteiraTO filtroCarteira) throws Exception {
        if (filtroCarteira.getCodigoColaborador() > 0) {
            filtroCarteira.setColaborador(getFacade().getColaborador().consultarPorChavePrimaria(
                    filtroCarteira.getCodigoColaborador(),
                    Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA));
        } else {
            filtroCarteira.setColaborador(new ColaboradorVO());
        }
    }

    private void preencherQuantidadeVinculosColaboradorSelecionado() throws Exception {
        for (TipoColaboradorVO tipoColaboradorVO : getFiltroBuscarCarteira().getColaborador().getListaTipoColaboradorVOs()) {
            FiltroCarteiraTO vinculo = new FiltroCarteiraTO();
            vinculo.setTipoVinculo(tipoColaboradorVO.getDescricao());
            getFiltroBuscarCarteira().getListaDeVinculos().add(vinculo);
        }
        getFacade().getCarteirasRel().consultarQuantidadeDeVinculos(getFiltroBuscarCarteira());
    }

    public Boolean alterarSelecionado() {
        FiltroCarteiraTO filtroCarteiraSelecionado = (FiltroCarteiraTO) JSFUtilities.getFromRequest("vinculo");
        for (FiltroCarteiraTO filtro : getFiltroBuscarCarteira().getListaDeVinculos()) {
            filtro.setSelecionado(filtro.equals(filtroCarteiraSelecionado));
            if (filtro.equals(filtroCarteiraSelecionado)) {
                getFiltroBuscarCarteira().setTipoVinculo(filtro.getTipoVinculo());
            }
        }
        return true;
    }
    public void validarPermissaoOrganizarCarteiras() throws Exception {
        List<PermissaoVO> permissoes = new ArrayList<PermissaoVO>();
        for(UsuarioPerfilAcessoVO perfil : (List<UsuarioPerfilAcessoVO>) getUsuarioLogado().getUsuarioPerfilAcessoVOs()){
            if((int)perfil.getEmpresa().getCodigo() == (int)getEmpresaLogado().getCodigo()){
                permissoes = perfil.getPerfilAcesso().getPermissaoVOs();
            }
        }

        for (PermissaoVO permissao : permissoes) {
            if (permissao.getNomeEntidade().equalsIgnoreCase("OrganizadorCarteira")) {
                if (permissao.getPermissoes().equals("(0)") || permissao.getPermissoes().equals("(12)")) {
                    String msgErro = "Este Usuário (" + getUsuarioLogado().getNome()
                            + ") não possui permissão para  ALTERAR Organizador Carteiras";
                    setMsgAlert("alert('" + msgErro + "');");

                }
            }
        }
    }

    public void abrirModalOrganizarSelecionarTodos() throws Exception {
        validarAbrirModalOrganizar(true);
    }

    public void abrirModalOrganizar() throws Exception {
        setarQuantidadeClienteSelecionado();
        validarAbrirModalOrganizar(isMarcarTodos());
    }

    public void selecionarTodos() throws Exception {
        if(isMarcarTodos()){
            setMarcarTodos(false);
            setMarcarTodosPagina(false);
        }else{
            setMarcarTodos(true);
            setMarcarTodosPagina(true);
        }
        setarQuantidadeClienteSelecionado();
    }

    public void selecionarTodosPagina() throws Exception {
        if(isMarcarTodosPagina()){
            setMarcarTodos(false);
            setMarcarTodosPagina(false);
        }else{
            setMarcarTodos(false);
            setMarcarTodosPagina(true);
        }
        setarQuantidadeClienteSelecionado();
    }

    private void validarAbrirModalOrganizar(boolean isMarcarTodos) throws Exception {
        setTransferirPorQuantidade(isMarcarTodos);
        if (isTransferirPorQuantidade() && (!UteisValidacao.emptyNumber(descobrirTotalQuantidadeVinculoFiltroSelecionado(filtroBuscarCarteira)))) {
            quantidadeAlunosSelecionadosParaTransferencia = descobrirTotalQuantidadeVinculoFiltroSelecionado(filtroBuscarCarteira);
        } else {
            if(getQuantidadeClienteSelecionado() > 0){
                quantidadeAlunosSelecionadosParaTransferencia = getQuantidadeClienteSelecionado();
            }
        }
        if (isMarcarTodos) {
            quantidadeAlunosParaTransferencia = descobrirTotalQuantidadeVinculoFiltroSelecionado(filtroBuscarCarteira);
            setMsgAlert("Richfaces.showModalPanel('filtrar');");
        }
        else {
            quantidadeAlunosParaTransferencia = quantidadeAlunosSelecionadosParaTransferencia;
            setMsgAlert("Richfaces.showModalPanel('filtrar');");
        }

        if(!UteisValidacao.emptyNumber(quantidadeAlunosSelecionadosParaTransferencia)){
            setTransferirPorQuantidade(false);
        }else{
            setTransferirPorQuantidade(true);
        }

        validarPermissaoOrganizarCarteiras();
        limparDestino();
        carregarListaGrupoColaborador(getFiltroOrganizarCarteira());
        consultarCarteiras(getFiltroOrganizarCarteira());
    }

    public void setarQuantidadeClienteSelecionado() throws Exception {
        String clientesSelecionados = context().getExternalContext().getRequestParameterMap().get("clientesSelecionados");
        if(isMarcarTodos() && quantidadeClienteSelecionado > 0){
                clientesSelecionados =  getFacade().getCarteirasRel().consultarSugestaoTodos(filtroBuscarCarteira);
        }
        String clientesPagina = context().getExternalContext().getRequestParameterMap().get("arrayItensPagina");
        if (!UteisValidacao.emptyString(clientesSelecionados) && !UteisValidacao.emptyString(clientesPagina)) {
            int todos = descobrirTotalQuantidadeVinculoFiltroSelecionado(filtroBuscarCarteira);
            int marcadosAtual = (Integer.valueOf(clientesSelecionados.split(",").length));
            if (isMarcarTodos()) {
                if (marcadosAtual < todos) {
                    setMarcarTodos(false);
                    setMarcarTodosPagina(false);
                    quantidadeClienteSelecionado = marcadosAtual;
                    return;
                }
            } else if (!isMarcarTodos() && !isMarcarTodosPagina()) {
                if (marcadosAtual == todos) {
                    setMarcarTodos(true);
                    setMarcarTodosPagina(true);
                    quantidadeClienteSelecionado = todos;
                    return;
                } else {
                    quantidadeClienteSelecionado = marcadosAtual;
                    return;
                }
            }
        }
        else{
            if(UteisValidacao.emptyString(clientesSelecionados)){
                quantidadeClienteSelecionado = 0;
                setMarcarTodos(false);
                setMarcarTodosPagina(false);
            }
        }

        if(isMarcarTodos()){
            quantidadeClienteSelecionado = descobrirTotalQuantidadeVinculoFiltroSelecionado(filtroBuscarCarteira);
            return;
        }

        if (StringUtils.isBlank(clientesSelecionados)) {
            quantidadeClienteSelecionado = 0;
            quantidadeAlunosSelecionadosParaTransferencia = 0;
            return;
        }

        quantidadeClienteSelecionado = clientesSelecionados.split(",").length;
    }
    public int getQuantidadeClienteSelecionado() {
        return quantidadeClienteSelecionado;
    }

    private void limparDestino() {
        for (InfoCarteiraTO infoCarteiraTO : getFiltroOrganizarCarteira().getInfoCarteiraTOs()) {
            for (VinculosTipoTO vinculosTipoTO : infoCarteiraTO.getQtdVinculosColaborador()) {
                vinculosTipoTO.desselecionarLinha();
            }
        }

        setDestino(new FiltroCarteiraTO());
    }

    public void consultarCarteiras(ActionEvent event) throws Exception {
        consultarCarteiras((FiltroCarteiraTO) event.getComponent().getAttributes().get("filtro"));
    }

    private void consultarCarteiras(FiltroCarteiraTO filtroCarteira) throws Exception {
        filtroCarteira.getEmpresaVO().setCodigo(obtenhaCodigoDaEmpresaSelecionada());
        int codGrupoColabSelec = 0;
        for(SelectItem grupoColab : filtroCarteira.getListaGrupoColaborador()){
            if(!UteisValidacao.emptyNumber(filtroCarteira.getGrupoColaboradorVO().getCodigo()) &&
                    filtroCarteira.getGrupoColaboradorVO().getCodigo().equals(grupoColab.getValue().hashCode())){
                codGrupoColabSelec = filtroCarteira.getGrupoColaboradorVO().getCodigo();
            }
        }
        filtroCarteira.getGrupoColaboradorVO().setCodigo(codGrupoColabSelec);
        filtroCarteira.setInfoCarteiraTOs(getFacade().getCarteirasRel().consultarColaboradores(filtroCarteira,false));
        Integer totalCliente = 0;
        if (filtroCarteira.getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla())) {
            filtroCarteira.setTipoVinculo("");
        }
        for (InfoCarteiraTO infoCarteiraTO : filtroCarteira.getInfoCarteiraTOs()) {
            totalCliente += infoCarteiraTO.getTotalVinculos();
        }
        setTotalClientes(totalCliente) ;
        limparDestino();
    }

    public boolean isMostrarBotaoRemover() {
        return (!getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && !getFiltroBuscarCarteira().getTipoVinculo().equals("")
                && getFiltroBuscarCarteira().getCodigoColaborador() >= 1)
                || (getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()) && !getFiltroBuscarCarteira().isAlunosTreinoSemVinculo());
    }

    public boolean isMostrarBotaoTransferir() {
        if(getDestino().getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla()) &&
                getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla())){
            return true;
        }
        if(getDestino().getTipoVinculo().equals(TipoColaboradorEnum.TERCEIRIZADO.getSigla()) &&
                getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.TERCEIRIZADO.getSigla())){
            return true;
        }
        if(getDestino().getTipoVinculo().equals(TipoColaboradorEnum.ORIENTADOR.getSigla()) &&
                getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.ORIENTADOR.getSigla())){
            return true;
        }
        if (vinculosSaoDistintos()
            && qualquerVinculoEhDoTipo("TW")){
            return false;
        }
        else  if (getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && transferirPorQuantidade
                && getDestino().getCodigoColaborador() > 0){
            return true;
        }
        else if (getFiltroBuscarCarteira().isSugestao()
                && getFiltroBuscarCarteira().isClientesInconsistentes()
                && getFiltroBuscarCarteira().getTipoVinculo().equals(destino.getTipoVinculo())) {
            return true;
        } else if (getFiltroBuscarCarteira().isSugestao()
                && (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla()) || getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla()))
                && getFiltroBuscarCarteira().getColaborador().getSituacao().equals("NA")
                && getDestino().getCodigoColaborador() > 0) {
            return true;
        } else if (getFiltroBuscarCarteira().isSugestao()
                && getDestino().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && getDestino().getCodigoColaborador() > 0) {
            return false;
        } else if (!getFiltroBuscarCarteira().isSugestao()
                && getFiltroBuscarCarteira().getTipoVinculo().equals(getDestino().getTipoVinculo())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.TERCEIRIZADO.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.ADMINISTRADOR.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.FUNCIONARIO.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.MEDICO.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.ORIENTADOR.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PERSONAL_TRAINER.getSigla())
                && getDestino().getCodigoColaborador() > 0) {
            return true;
        } else if (getFiltroBuscarCarteira().getCodigoColaborador() > 0 && getDestino().getCodigoColaborador() > 0
                && getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())) {
            return true;
        }else if (getDestino().getCodigoColaborador() > 0 && transferirPorQuantidade){
            return true;
        } else if (!resultado.contains("sem")
                    && ambosVinculosSaoDoTipo("PR")){
            return true;
        }

        return false;
    }

    public boolean isMostrarBotaoAdicionar() {
        if(getFiltroBuscarCarteira().isSugestao()
                && getDestino().getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla())){
            return true;
        }
        else if (transferirPorQuantidade){
            return false;
        }
        else if (getFiltroBuscarCarteira().isSugestao()
                && getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && getFiltroBuscarCarteira().isClientesInconsistentes()) {
            return false;
        } else if (getFiltroBuscarCarteira().isSugestao()
                && (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla()) || getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla()))
                && getFiltroBuscarCarteira().getColaborador().getSituacao().equals("NA")
                && getDestino().getCodigoColaborador() > 0) {
            return false;
        } else if (getFiltroBuscarCarteira().isSugestao()
                && getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && getDestino().getCodigoColaborador() > 0
                && getDestino().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())) {
            return true;
        } else if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())
                   && vinculosSaoDistintos()) {
            return getFiltroBuscarCarteira().getCodigoColaborador() != 0 && getDestino().getCodigoColaborador() > 0;
        } else if (getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && getDestino().getCodigoColaborador() > 0
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())) {
            return true;
        } else if (!getFiltroBuscarCarteira().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && !getDestino().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())
                && getDestino().getCodigoColaborador() > 0
                && (vinculosSaoDistintos()
                    || ambosVinculosSaoDoTipo("PR"))
                    || resultado.contains("sem")) {
            return true;
        }
        return false;
    }

    public boolean vinculosSaoDistintos(){
        return !getFiltroBuscarCarteira().getTipoVinculo().equals(getDestino().getTipoVinculo());
    }

    public boolean qualquerVinculoEhDoTipo(String... tiposDeVinculo){
        for (String tipo : tiposDeVinculo){
            if(getFiltroBuscarCarteira().getTipoVinculo().equals(tipo)
                    || getDestino().getTipoVinculo().equals(tipo))
                return true;
        }
        return false;
    }
    public boolean ambosVinculosSaoDoTipo(String... tiposDeVinculo){
        for (String tipo : tiposDeVinculo){
            if(getFiltroBuscarCarteira().getTipoVinculo().equals(tipo)
                    && getDestino().getTipoVinculo().equals(tipo))
                return true;
        }
        return false;
    }

    public boolean isMostrarMensagemExplicativa() {
        return !isMostrarBotaoAdicionar()
                && !isMostrarBotaoTransferir()
                && getDestino().getCodigoColaborador() > 0
                && !getDestino().getTipoVinculo().equals("");
    }

    public void selecionarColaborador() {
        InfoCarteiraTO infoCarteiraTO = (InfoCarteiraTO) JSFUtilities.getFromRequest("infoCarteira");
        getFiltroCarteira().getColaborador().setCodigo(infoCarteiraTO.getColaborador().getCodigo());
    }

    public void voltarSugestoes() {
        try {
            setPanelCarteiras(false);
            setPanelTodasCarteiras(false);
            consultarSugestoes(this.qtdItensCasosImportantes);
        }catch (Exception e){
            montarErro(e);
        }
    }

    private void irParaTela(String tela) {
        LoginControle controle = (LoginControle) getControlador(LoginControle.class);
        controle.setJspMostrarCRM(tela);
    }

    public void adicionar() throws Exception {
        setOperacao(ADICIONAR);
        contarVinculosAlterados();
        verificarAgendamentos();
        setHabilitarBotaoConfirmarTransferencia(true);
        if (!UteisValidacao.emptyList(getVinculosAlterados()) && !UteisValidacao.emptyString(getVinculosAlterados().get(0).getTipoVinculo())) {
            setOnCompleteDetalhes("Richfaces.hideModalPanel('filtrar');Richfaces.showModalPanel('filtrarConfirma');");
        } else {
            setOnCompleteDetalhes("try{ Notifier.warning(\"É obrigatório selecionar um vínculo!\",\"Selecione o vínculo\");} catch(e){}");
        }
    }

    public void transferir() throws Exception {
        try {
            String tipoOrigem = filtroBuscarCarteira.getTipoVinculo();
            String tipoDestino = filtroOrganizarCarteira.getTipoVinculo();
            if (!tipoOrigem.equals(tipoDestino)) {
                idModalTransferencia = "modalAlertaValidacao";
                mensagemAlertaValidacaoTransferencia = "Não é possivel transferir alunos entre tipos de colaboradores diferentes, faça a transferência selecionando a quantidade pelo checkbox no nome dos alunos e adicione para o outro tipo que desejar";
                return;
            }
            idModalTransferencia = "filtrarConfirma";
            if (transferirPorQuantidade) {
                notificarRecursoEmpresa(RecursoSistema.TRANSFERIR_CARTEIRA_POR_QUANTIDADE);
                Integer totalQuantidadeVinculoSelecionado = quantidadeAlunosParaTransferencia;

                if (totalQuantidadeVinculoSelecionado != null) {
                    if (quantidadeAlunosParaTransferencia > totalQuantidadeVinculoSelecionado) {
                        idModalTransferencia = "modalAlertaValidacao";
                        mensagemAlertaValidacaoTransferencia = "Não é possível transferir um número acima do limite de vínculos.";
                    } else if (quantidadeAlunosParaTransferencia == 0) {
                        idModalTransferencia = "modalAlertaValidacao";
                        mensagemAlertaValidacaoTransferencia = "Não é possível transfeir 0 vínculos.";
                    }
                }
            } else {
                notificarRecursoEmpresa(RecursoSistema.TRANSFERIR_CARTEIRA_POR_SELECAO);
            }

            setOperacao(TRANSFERIR);
            contarVinculosAlterados();
            verificarAgendamentos();
            verificarPossibilidadeTransferirVinculosFuturos(null);
        } catch (Exception e){
            montarErro(e);
        }
    }

    /**
     * Verifica se existem agendamentos para serem transferidos de um consultor para outro.
     */
    private void verificarAgendamentos() throws  Exception{
        if(getVinculosAlterados() != null && !getVinculosAlterados().isEmpty()) {
            if (getDestino().getCodigoColaborador() != 0) {
                this.codigosAgendamentosTransferir = getFacade().getAgenda().consultarCodigoAgendaFuturasPorVinculos(getVinculosAlterados(), Calendario.hoje(), getFiltroBuscarCarteira().getEmpresaVO().getCodigo());
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(getDestino().getCodigoColaborador(), Uteis.NIVELMONTARDADOS_MINIMOS);
                UsuarioVO usuarioDestino = getFacade().getUsuario().consultarPorCodigoPessoa(colaboradorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setColaboradorSelecionadoPossuiUsuario(usuarioDestino != null && usuarioDestino.getCodigo() != 0);
                setTransferirAgendamentos(this.codigosAgendamentosTransferir != null && !this.codigosAgendamentosTransferir.isEmpty());
            }
        }else{
            setTransferirAgendamentos(Boolean.TRUE);
            this.codigosAgendamentosTransferir = null;
        }
    }

    public void remover() {
        try {
            limparMsg();
            setMsgAlert("");
            setBuscar(false);
            setOperacao(REMOVER);
            contarVinculosAlterados();
            if (UteisValidacao.emptyList(getVinculosAlterados())) {
                throw new Exception("Nenhum cliente selecionado!");
            }
            verificarAgendamentos();
            setHabilitarBotaoConfirmarTransferencia(true);
            setMarcarTodosPagina(false);
            setMarcarTodos(false);
        } catch (Exception ex) {
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public String getAcao() {
        if (getOperacao().equals(ADICIONAR)) {
            return "Adicionando";
        } else if (getOperacao().equals(TRANSFERIR)) {
            return "Transferindo";
        } else if (getOperacao().equals(REMOVER)) {
            return "Removendo";
        }
        return "";
    }

    public void contarVinculosAlterados() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        String listaClientes;
        FiltroCarteiraTO sugestaoSelecionada = (FiltroCarteiraTO) JSFUtilities.getFromSession("filtroCarteiraTO");
        if(isMarcarTodos() && !UteisValidacao.emptyNumber(getOperacao()) && getOperacao().equals(TRANSFERIR) &&
                sugestaoSelecionada != null && sugestaoSelecionada.isSugestao() && sugestaoSelecionada.isInconsistencia()){
            listaClientes = getFacade().getCarteirasRel().consultarCodigosClientesInconsistenciaSugestao(getEmpresaLogado().getCodigo(), sugestaoSelecionada);
        }else if(isMarcarTodos() && !UteisValidacao.emptyNumber(getOperacao()) && getOperacao().equals(ADICIONAR) &&
                sugestaoSelecionada != null && sugestaoSelecionada.isSugestao() && sugestaoSelecionada.isClientesSemVinculo()){
            if(sugestaoSelecionada.isAlunosTreinoSemVinculo()){
                if(isMarcarTodos()){
                    listaClientes =  getFacade().getCarteirasRel().consultarSugestaoTodos(filtroBuscarCarteira);
                }else{
                    listaClientes = validarClientesSelecionados(false);
                }
            }else{
                listaClientes = getFacade().getCarteirasRel().consultarCodigosClientesSemVinculoSugestao(getConfiguracaoSistema().isConsiderarProfessorTreinoWeb(), getEmpresaLogado().getCodigo(), sugestaoSelecionada);
            }
        }else if (isMarcarTodos() && isBuscar()){
            listaClientes = getFacade().getCarteirasRel().consultarCodigosClientes(filtroCarteira);
        }else{
            listaClientes = validarClientesSelecionados(false);
        }

        List<VinculoVO> vinculoVOs = new ArrayList<VinculoVO>();
        if (getOperacao() > 0) {
            if (getOperacao().equals(ADICIONAR)) {
                String[] clientes = listaClientes.split(",");
                for (String cliente : clientes) {
                    if (UteisValidacao.emptyString(cliente)) {
                        continue;
                    }
                    VinculoVO vinculoVO = new VinculoVO();
                    vinculoVO.setColaborador(getDestino().getColaborador());
                    ClienteVO clienteVO = new ClienteVO();
                    clienteVO.setCodigo(Integer.parseInt(cliente));
                    vinculoVO.setCliente(clienteVO);

                    vinculoVO.setTipoVinculo(getDestino().getTipoVinculo());
                    vinculoVOs.add(vinculoVO);
                }
            } else if (getOperacao().equals(REMOVER)) {
                vinculoVOs = getFacade().getVinculo().consultarPorFiltroCarteiraListaClientes(
                        listaClientes,
                        getFiltroBuscarCarteira(),
                        Uteis.NIVELMONTARDADOS_MINIMOS);
            } else if (getOperacao().equals(TRANSFERIR)) {
                vinculoVOs = montarVinculosParaTransferencia(listaClientes);
            }
        }

        setVinculosAlterados(vinculoVOs);
    }

    private List<VinculoVO> montarVinculosParaTransferencia(String listaClientes) throws Exception {
        if (transferirPorQuantidade) {
            Integer totalQuantidadeVinculoSelecionado = descobrirTotalQuantidadeVinculoFiltroSelecionado(filtroBuscarCarteira);
            if (!UteisValidacao.emptyNumber(totalQuantidadeVinculoSelecionado)) {
                boolean isQuantidadeTransferenciaIgualTodosDoFiltro = quantidadeAlunosParaTransferencia.equals(totalQuantidadeVinculoSelecionado);
                return getFacade().getVinculo().consultarPorFiltroCarteiraListaClientes(
                        "",
                        getFiltroBuscarCarteira(),
                        isQuantidadeTransferenciaIgualTodosDoFiltro ? null : quantidadeAlunosParaTransferencia,
                        Uteis.NIVELMONTARDADOS_MINIMOS);
            }
        }
        return getFacade().getVinculo().consultarPorFiltroCarteiraListaClientes(
                listaClientes,
                getFiltroBuscarCarteira(),
                quantidadeAlunosParaTransferencia,
                Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    private String validarClientesSelecionados(boolean ehOrganizar) {
        String clientesSelecionados = "";
        clientesSelecionados = context().getExternalContext().getRequestParameterMap().get("clientesSelecionados");
        String msgAlert;
        if (ehOrganizar || !isOperacaoRemover()) {
            msgAlert = "Richfaces.showModalPanel('filtrar');";
        } else {
            msgAlert = "Richfaces.showModalPanel('filtrarConfirma');";
        }
        setMsgAlert(msgAlert);

        return clientesSelecionados;
    }

    public void validarCredenciais(){
        try {

            if(getUsuarioLogado().getColaboradorVO().getTipoColaborador().equals("")){
                montarMsgAlert("Não possui credenciais para está operação");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void reverterTransferencia() {
        try {
            validarPermissao("Vinculo", "2.29 - Vínculos de cliente e de colaborador", getUsuarioLogado());
            limparMsg();
            setMsgAlert("");
            if(codUsuarioResponsavelTransf == 0){
                throw new ConsistirException("Selecione pelo menos um usuário.");
            }
            UsuarioVO usuario = getFacade().getUsuario().consultarPorCodigo(getCodUsuarioResponsavelTransf(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<HistoricoVinculoVO> histVinculos = getFacade().getHistoricoVinculo().consultarUltimoHistoricoSaidaTransferencia(getDataOperacaoTransarencia(), usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if(UteisValidacao.emptyList(histVinculos)){
                throw new ConsistirException("Não foram encontrados registros para esse colaborador nessa data.");
            }

            SinteticoMsDTO sinteticoSincronizar = new SinteticoMsDTO();
            sinteticoSincronizar.setChave(getKey());
            sinteticoSincronizar.setUrlRequisitar(getUrlTreino());
            for (HistoricoVinculoVO historico : histVinculos) {
                List<VinculoVO> vinculosClient = getFacade().getVinculo().consultarPorCodigoCliente(historico.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);
                for (VinculoVO vinculo : vinculosClient) {
                    if(sinteticoSincronizar.getUsuariosAtualizar().size() == 500){
                        atualizarSinteticoTreinoSinteticoMs(sinteticoSincronizar);
                        sinteticoSincronizar.setUsuariosAtualizar(new ArrayList<>());
                    }
                    int codColaboradorSaiu = vinculo.getColaborador().getCodigo();
                    if (vinculo.getTipoVinculo().equals(historico.getTipoColaborador()) && vinculo.getCliente().getCodigo().equals(historico.getCliente().getCodigo())) {
                        vinculo.setColaborador(historico.getColaborador());
                        getFacade().getVinculo().alterar(vinculo, "CRM-REVERT", getUtilizarSinteticoMs() ? sinteticoSincronizar : null);
                        HistoricoVinculoVO hist;
                        hist = new HistoricoVinculoVO(vinculo.getCliente().getCodigo(), codColaboradorSaiu, "SD", vinculo.getTipoVinculo(), "CRM-REVERT", getUsuarioLogado());
                        getFacade().getHistoricoVinculo().incluirSemCommit(hist, false);
                        hist = new HistoricoVinculoVO(vinculo.getCliente().getCodigo(), historico.getColaborador().getCodigo(), "EN", historico.getTipoColaborador(), Calendario.hoje(), "CRM-REVERT", getUsuarioLogado());
                        getFacade().getHistoricoVinculo().incluirSemCommit(hist, false);
                    }
                }
            }
            atualizarSinteticoTreinoSinteticoMs(sinteticoSincronizar);
            montarSucessoGrowl("Revert realizado com sucesso.");
            notificarRecursoEmpresa(RecursoSistema.USOU_REVERTE_TRANSFERENCIA_CARTEIRAS_CRM);
            registrarLogReversao();
        } catch (Exception e) {
            if(e.getMessage().contains("Este Usuário")){
                montarErro(e);
            }else if(e.getMessage().contains("Selecione pelo menos")){
                Uteis.logar(e.getMessage(), CarteirasControle.class);
                montarErro("Selecione pelo menos um usuário.");
            } else if(e.getMessage().contains("Não foram encontrados registros")){
                montarErro(e);
            }
            else{
                Uteis.logar(e.getMessage(), CarteirasControle.class);
                montarErro("Não foi possível reverter transferência de carteiras no momento, entre me contato com nosso suporte.");
            }
        }

    }

    private void atualizarSinteticoTreinoSinteticoMs(SinteticoMsDTO sinteticoSincronizar) throws Exception {
        if(getUtilizarSinteticoMs()) {
            Uteis.executeRequestSintetico(Uteis.getUrlDiscovery("sinteticoMs") + "/sintetico/addFila", sinteticoSincronizar.toJSON().toString(), new HashMap<>());
        }
    }

    private void registrarLogReversao() throws Exception{
        LogVO obj = new LogVO();
        obj.setChavePrimaria(getUsuarioLogado().getCodigo().toString());
        obj.setNomeEntidade("CARTEIRAS");
        obj.setNomeEntidadeDescricao("Carteiras");
        obj.setOperacao("REVERSÃO");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("REVERSÃO de vínculos realizada por: " + obj.getResponsavelAlteracao());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
    }

    public void montarUsuarioOperacaoTransferencia() throws Exception {
        try {
            selectUsuariosOperacaoTransf = new ArrayList<>();
            selectUsuariosOperacaoTransf.add(new SelectItem(0, ""));
            List<UsuarioVO> usuarios = getFacade().getUsuario().consultarTodosUsuariosColaboradorPorEmpresa(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(usuarios, "Nome");
            for (UsuarioVO usuario : usuarios) {
                selectUsuariosOperacaoTransf.add(new SelectItem(usuario.getCodigo(), usuario.getNome()));
            }
        }catch(Exception e){
            Uteis.logar(e.getMessage(), CarteirasControle.class);
        }
    }

    public void processar() throws Exception {
        if (getOperacao() > 0) {
            if (getOperacao().equals(ADICIONAR)) {

                if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())) {
                    List<Integer> codigosAgendaTransferir = null;
                    if(this.transferirAgendamentos != null && this.transferirAgendamentos){
                        codigosAgendaTransferir = this.codigosAgendamentosTransferir;
                        this.codigosAgendamentosTransferir = null;
                    }
                    getFacade().getVinculo().transferirVinculosTreino(getVinculosAlterados(), getDestino().getColaborador(), "CARTEIRAS - ADICIONAR", getUsuarioLogado(), codigosAgendaTransferir);
                } else {
                    getFacade().getVinculo().incluirVinculos(getVinculosAlterados(), "CARTEIRAS - ADICIONAR", getUsuarioLogado());
                }
                try {
                    LogVO obj = new LogVO();
                    obj.setChavePrimaria(getDestino().getCodigoColaborador().toString());
                    obj.setNomeEntidade("CARTEIRAS");
                    obj.setNomeEntidadeDescricao("Carteiras");
                    obj.setOperacao("INCLUSÃO");
                    obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                    obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                    obj.setNomeCampo("MENSAGEM");
                    obj.setValorCampoAlterado("ADICIONADO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rPARA O COLABORADOR: " + getDestino().getNomeColaborador().toUpperCase() + " \n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                    obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                    registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO("CARTEIRAS", getDestino().getColaborador().getPessoa().getCodigo(), "ERRO AO ADICIONAR VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            } else if (getOperacao().equals(REMOVER)) {
                if (getVinculosAlterados().size() == 0){
                    setMsgAlert("");
                    montarErro("Selecione pelo menos um Aluno!");
                    setMensagemDetalhada("Selecione pelo menos um Aluno!");
                }else {
                    getFacade().getVinculo().removerVinculos(getVinculosAlterados(), "CARTEIRAS - REMOVER", getUsuarioLogado(), true);
                    try {
                        LogVO obj = new LogVO();
                        obj.setChavePrimaria(getVinculosAlterados().get(0).getColaborador().getPessoa().toString());
                        obj.setNomeEntidade("CARTEIRAS");
                        obj.setNomeEntidadeDescricao("Carteiras");
                        obj.setOperacao("EXCLUSÃO");
                        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                        obj.setNomeCampo("MENSAGEM");
                        obj.setValorCampoAlterado("REMOVIDO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rDO COLABORADOR: " + getFacade().getColaborador().obterNomePessoa(getVinculosAlterados().get(0).getColaborador().getCodigo()) + "\n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                        registrarLogObjetoVO(obj, getVinculosAlterados().get(0).getColaborador().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CARTEIRAS", getVinculosAlterados().get(0).getColaborador().getPessoa().getCodigo(), "ERRO AO REMOVER VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();

                    }
                }
            } else if (getOperacao().equals(TRANSFERIR)) {
                List<Integer> codigosAgendaTransferir = null;
                if(this.transferirAgendamentos != null && this.transferirAgendamentos){
                    codigosAgendaTransferir = this.codigosAgendamentosTransferir;
                    this.codigosAgendamentosTransferir = null;
                }
                if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla())) {
                        FiltroCarteiraTO filtroCarteiraTO = (FiltroCarteiraTO) JSFUtilities.getFromSession("filtroCarteiraTO");
                        getFacade().getVinculo().transferirVinculosTreino(getVinculosAlterados(), getDestino().getColaborador(), "CARTEIRAS - TRANSFERIR", getUsuarioLogado(), codigosAgendaTransferir);
                        filtroCarteiraTO.setQtdVinculos( filtroCarteiraTO.getQtdVinculos() - getVinculosAlterados().size());
                    try {
                        LogVO obj = new LogVO();
                        obj.setChavePrimaria(getDestino().getCodigoColaborador().toString());
                        obj.setNomeEntidade("CARTEIRAS");
                        obj.setNomeEntidadeDescricao("Carteiras");
                        obj.setOperacao("TRANSFERIR");
                        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                        obj.setNomeCampo("MENSAGEM");
                        obj.setValorCampoAlterado("TRANSFERIDO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rCOLABORADOR ANTERIOR: " + getFacade().getColaborador().obterNomePessoa(getVinculosAlterados().get(0).getColaborador().getCodigo()) + "\n\rCOLABORADOR DESTINO: " + getDestino().getNomeColaborador() + "\n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                        registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CARTEIRAS", getDestino().getColaborador().getPessoa().getCodigo(), "ERRO AO TRANSFERIR VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                } else if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.PROFESSOR.getSigla())){
                    getFacade().getVinculo().transferirVinculos(getVinculosAlterados(), getDestino().getColaborador(), "CARTEIRAS - TRANSFERIR", getUsuarioLogado(), codigosAgendaTransferir);
                    try {
                        LogVO obj = new LogVO();
                        obj.setChavePrimaria(getDestino().getCodigoColaborador().toString());
                        obj.setNomeEntidade("CARTEIRAS");
                        obj.setNomeEntidadeDescricao("Carteiras");
                        obj.setOperacao("TRANSFERIR");
                        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                        obj.setNomeCampo("MENSAGEM");
                        obj.setValorCampoAlterado("TRANSFERIDO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rCOLABORADOR ANTERIOR: " + getFacade().getColaborador().obterNomePessoa(getVinculosAlterados().get(0).getColaborador().getCodigo()) + "\n\rCOLABORADOR DESTINO: " + getDestino().getNomeColaborador() + "\n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                        registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CARTEIRAS", getDestino().getColaborador().getPessoa().getCodigo(), "ERRO AO TRANSFERIR VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                } else if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())){
                    getFacade().getVinculo().transferirVinculosConsultor(getVinculosAlterados(), getDestino().getColaborador(), "CARTEIRAS - TRANSFERIR", getUsuarioLogado(), codigosAgendaTransferir);
                    try {
                        LogVO obj = new LogVO();
                        obj.setChavePrimaria(getDestino().getCodigoColaborador().toString());
                        obj.setNomeEntidade("CARTEIRAS");
                        obj.setNomeEntidadeDescricao("Carteiras");
                        obj.setOperacao("TRANSFERIR");
                        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                        obj.setNomeCampo("MENSAGEM");
                        obj.setValorCampoAlterado("TRANSFERIDO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rCOLABORADOR ANTERIOR: " + getFacade().getColaborador().obterNomePessoa(getVinculosAlterados().get(0).getColaborador().getCodigo()) + "\n\rCOLABORADOR DESTINO: " + getDestino().getNomeColaborador() + "\n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                        registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CARTEIRAS", getDestino().getColaborador().getPessoa().getCodigo(), "ERRO AO TRANSFERIR VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                } else if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.ORIENTADOR.getSigla())){
                    getFacade().getVinculo().transferirVinculos(getVinculosAlterados(), getDestino().getColaborador(), "CARTEIRAS - TRANSFERIR", getUsuarioLogado(), codigosAgendaTransferir, TipoColaboradorEnum.ORIENTADOR.getSigla());
                    try {
                        LogVO obj = new LogVO();
                        obj.setChavePrimaria(getDestino().getCodigoColaborador().toString());
                        obj.setNomeEntidade("CARTEIRAS");
                        obj.setNomeEntidadeDescricao("Carteiras");
                        obj.setOperacao("TRANSFERIR");
                        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                        obj.setNomeCampo("MENSAGEM");
                        obj.setValorCampoAlterado("TRANSFERIDO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rCOLABORADOR ANTERIOR: " + getFacade().getColaborador().obterNomePessoa(getVinculosAlterados().get(0).getColaborador().getCodigo()) + "\n\rCOLABORADOR DESTINO: " + getDestino().getNomeColaborador() + "\n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                        registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CARTEIRAS", getDestino().getColaborador().getPessoa().getCodigo(), "ERRO AO TRANSFERIR VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                } else if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.COORDENADOR.getSigla())){
                    getFacade().getVinculo().transferirVinculos(getVinculosAlterados(), getDestino().getColaborador(), "CARTEIRAS - TRANSFERIR", getUsuarioLogado(), codigosAgendaTransferir, TipoColaboradorEnum.COORDENADOR.getSigla());
                    try {
                        LogVO obj = new LogVO();
                        obj.setChavePrimaria(getDestino().getCodigoColaborador().toString());
                        obj.setNomeEntidade("CARTEIRAS");
                        obj.setNomeEntidadeDescricao("Carteiras");
                        obj.setOperacao("TRANSFERIR");
                        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                        obj.setNomeCampo("MENSAGEM");
                        obj.setValorCampoAlterado("TRANSFERIDO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rCOLABORADOR ANTERIOR: " + getFacade().getColaborador().obterNomePessoa(getVinculosAlterados().get(0).getColaborador().getCodigo()) + "\n\rCOLABORADOR DESTINO: " + getDestino().getNomeColaborador() + "\n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                        registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CARTEIRAS", getDestino().getColaborador().getPessoa().getCodigo(), "ERRO AO TRANSFERIR VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                } else if (getDestino().getTipoVinculo().equals(TipoColaboradorEnum.TERCEIRIZADO.getSigla())){
                    getFacade().getVinculo().transferirVinculos(getVinculosAlterados(), getDestino().getColaborador(), "CARTEIRAS - TRANSFERIR", getUsuarioLogado(), codigosAgendaTransferir, TipoColaboradorEnum.TERCEIRIZADO.getSigla());
                    try {
                        LogVO obj = new LogVO();
                        obj.setChavePrimaria(getDestino().getCodigoColaborador().toString());
                        obj.setNomeEntidade("CARTEIRAS");
                        obj.setNomeEntidadeDescricao("Carteiras");
                        obj.setOperacao("TRANSFERIR");
                        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                        obj.setNomeCampo("MENSAGEM");
                        obj.setValorCampoAlterado("TRANSFERIDO: " + getQtdVinculosAlterados().toUpperCase() + "\n\rCOLABORADOR ANTERIOR: " + getFacade().getColaborador().obterNomePessoa(getVinculosAlterados().get(0).getColaborador().getCodigo()) + "\n\rCOLABORADOR DESTINO: " + getDestino().getNomeColaborador() + "\n\rTIPO DE VÍNCULO: " + getVinculosAlterados().get(0).getTipoVinculo_Apresentar().toUpperCase() + "");
                        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                        registrarLogObjetoVO(obj, getDestino().getColaborador().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("CARTEIRAS", getDestino().getColaborador().getPessoa().getCodigo(), "ERRO AO TRANSFERIR VINCULO CARTEIRAS", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                }
            }
            reconsultar();
        }
    }

    public String getQtdVinculosAlterados() {
        return getVinculosAlterados().size() + " vínculos";
    }

    public void setarDestino() {
        for (InfoCarteiraTO infoCarteiraTO : getFiltroOrganizarCarteira().getInfoCarteiraTOs()) {
            for (VinculosTipoTO vinculosTipoTO : infoCarteiraTO.getQtdVinculosColaborador()) {
                vinculosTipoTO.desselecionarLinha();
            }
        }

        InfoCarteiraTO infoCarteira = (InfoCarteiraTO) context().getExternalContext().getRequestMap().get("infoCarteira");
        VinculosTipoTO vinculosTipo = (VinculosTipoTO) context().getExternalContext().getRequestMap().get("tipoVinculoCarteira");
        vinculosTipo.selecionarLinha();

        FiltroCarteiraTO infoCarteiraDestino = new FiltroCarteiraTO();
        infoCarteiraDestino.setColaborador(infoCarteira.getColaborador());
        infoCarteiraDestino.setTipoVinculo(vinculosTipo.getTipoVinculo());
        setDestino(infoCarteiraDestino);
    }

    public boolean isOperacaoAdicionar() {
        return getOperacao().equals(ADICIONAR);
    }

    public boolean isOperacaoRemover() {
        return getOperacao().equals(REMOVER);
    }

    public boolean isOperacaoTransferir() {
        return getOperacao().equals(TRANSFERIR);
    }

    public void abrirTelaClienteColaboradorCarteiras() throws Exception {
        String codigoCliente = context().getExternalContext().getRequestParameterMap().get("codCliente");
        ClienteVO obj = getFacade().getCliente().consultarPorCodigo(Integer.parseInt(codigoCliente), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class.getSimpleName());
        if (clienteControle == null) {
            clienteControle = new ClienteControle();
        }
        if (obj.getCodigo() != 0) {
            clienteControle.setarCliente(obj);
            clienteControle.acaoAjax();
            setOnCompleteDetalhes("abrirPopup('clienteNav.jsp?page=viewCliente', 'ClienteGestaoCarteiras', 1024, 700)");
        }
    }

    public void detalharAluno() throws Exception {
        String codigoCliente = context().getExternalContext().getRequestParameterMap().get("codCliente");
        ClienteVO obj = getFacade().getCliente().consultarPorCodigo(Integer.parseInt(codigoCliente), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class.getSimpleName());
        if (clienteControle == null) {
            clienteControle = new ClienteControle();
        }
        clienteControle.detalharCliente(obj);
        setListaClientes(new ArrayList<ClienteVO>());
        getListaClientes().add(obj);
    }

    public void paintFoto(OutputStream out, Object data) throws Exception {
        getFiltroBuscarCarteira().getColaborador().getPessoa().setFoto(
                getFacade().getPessoa().obterFoto(getKey(),
                getFiltroBuscarCarteira().getColaborador().getPessoa().getCodigo()));
        SuperControle.paintFoto(out, getFiltroBuscarCarteira().getColaborador().getPessoa().getFoto());
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getFiltroBuscarCarteira().getColaborador().getPessoa().getFotoKey());
    }

    public void irParaTelaCliente() {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isMostrarSeletorNome() {
        return !getFiltroCarteira().getTipoVinculo().equals("");
    }

    public void prepararBuscarTodasCarteiras() {
        try {
            limparMsg();
            getFiltroVerTodasCarteiras().getColaborador().setListaTipoColaboradorVOs(new ArrayList<TipoColaboradorVO>());
            for (InfoCarteiraTO infoCarteiraTO : getFiltroVerTodasCarteiras().getInfoCarteiraTOs()) {
                for (VinculosTipoTO vinculosTipoTO : infoCarteiraTO.getQtdVinculosColaborador()) {
                    vinculosTipoTO.desselecionarLinha();
                }
            }

            InfoCarteiraTO infoCarteira = (InfoCarteiraTO) context().getExternalContext().getRequestMap().get("infoCarteira");
            VinculosTipoTO vinculosTipo = (VinculosTipoTO) context().getExternalContext().getRequestMap().get("tipoVinculoCarteira");
            vinculosTipo.selecionarLinha();

            getFiltroVerTodasCarteiras().setColaborador(infoCarteira.getColaborador());
            getFiltroVerTodasCarteiras().setTipoVinculo(vinculosTipo.getTipoVinculo());
            for (VinculosTipoTO vinculosTipoTO : infoCarteira.getQtdVinculosColaborador()) {
                TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
                tipoColaboradorVO.setColaborador(getFiltroVerTodasCarteiras().getColaborador().getCodigo());
                tipoColaboradorVO.setDescricao(vinculosTipoTO.getTipoVinculo());
                getFiltroVerTodasCarteiras().getColaborador().getListaTipoColaboradorVOs().add(tipoColaboradorVO);
            }
            alterarFiltroCarteira(getFiltroVerTodasCarteiras());
            prepararFiltroEBuscar(getFiltroVerTodasCarteiras(), false,0);
            buscarFiltroSelecionado();
            setSucesso(true);
            setErro(false);
        }catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
            e.printStackTrace();
        }
    }

    public void buscarTodasCarteiras() throws Exception {
        alterarFiltroCarteira(getFiltroVerTodasCarteiras());
        prepararFiltroEBuscar(getFiltroVerTodasCarteiras(), false,0);
    }

    private void alterarFiltroCarteira(FiltroCarteiraTO filtroCarteiraTO) throws Exception {
        setFiltroCarteira(filtroCarteiraTO);
        carregarListaColaboradores();
    }

    public boolean isMostrarBotaoBuscarVerTodasCarteiras() {
        return !getFiltroVerTodasCarteiras().getTipoVinculo().equals("") && getFiltroVerTodasCarteiras().getCodigoColaborador() > 0;
    }

    public void carregarMdlVerTodasCarteiras() throws Exception {
        consultarCarteiras(getFiltroVerTodasCarteiras());
    }

    public Object adicionarItemFiltroAvancado() {
        getFiltrosAvancados().add(new ItemFiltroGR());
        verificarAgrupador();
        return true;
    }

    public Object removerItemFiltroAvancado() {
        ItemFiltroGR obj = (ItemFiltroGR) context().getExternalContext().getRequestMap().get("filtro");
        getFiltrosAvancados().remove(obj);
        verificarAgrupador();
        return true;
    }

    private void verificarAgrupador() {
        for (ItemFiltroGR item : getFiltrosAvancados()) {
            if (!item.isPermiteAgrupador()) {
                item.setPermiteAgrupador(true);
                item.setAgrupador("AND");
            }
        }
        if (getFiltrosAvancados().size() > 0) {
            getFiltrosAvancados().get(getFiltrosAvancados().size() - 1).setAgrupador("");
            getFiltrosAvancados().get(getFiltrosAvancados().size() - 1).setPermiteAgrupador(false);
        }
    }

    public boolean getDesenharBotao() {
        return getFiltrosAvancados().size() != 1;
    }

    public Object atualizarCondicoes() {
        ItemFiltroGR obj = (ItemFiltroGR) context().getExternalContext().getRequestMap().get("filtro");
        boolean encontrou = false;
        for (CampoGR campo : getCamposFiltroAvancado()) {
            if (campo.getColuna().equals(obj.getCampoGR().getColuna())) {
                encontrou = true;
                CampoGR novoCampo = new CampoGR();
                novoCampo.setColuna(campo.getColuna());
                novoCampo.setNomeApresentar(campo.getNomeApresentar());
                novoCampo.setTipoCampo(campo.getTipoCampo());
                novoCampo.setTabelaJoin(campo.getTabelaJoin());
                novoCampo.setEntidade(campo.getEntidade());
                obj.setCampoGR(novoCampo);
            }
        }
        if (!encontrou) {
            obj.setCampoGR(new CampoGR());
            obj.setApresentarEntradas(false);
        }
        obj.preencherCondicoes();
        return false;
    }

    public Object atualizarEntradas() {
        try {
            ItemFiltroGR obj = (ItemFiltroGR) context().getExternalContext().getRequestMap().get("filtro");
            obj.setApresentarValor1(true);
            if (obj.getCondicao().equals("between")) {
                obj.setApresentarValor2(true);
            } else {
                obj.setApresentarValor2(false);
            }
            if(obj.getCampoGR().getTipoCampo() == TipoCampoGREnum.CHAVE_ESTRANGEIRA) {
                obj.setApresentarValor1(false);
                obj.setApresentarValor2(false);
                obj.getCampoGR().setValoresChaveEstrangeira(obterListaValoresFK(obj.getCampoGR().getEntidade()));
            }
            obj.setApresentarEntradas(!obj.getCondicao().equals(""));
        }catch (Exception ex){
            montarErro(ex);
        }
        return false;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String clientesSelecionados = context().getExternalContext().getRequestParameterMap().get("clientesSelecionados");
        FiltroCarteiraTO filtroCarteiraTO = (FiltroCarteiraTO) JSFUtilities.getFromSession("filtroCarteiraTO");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getCarteirasRel().consultarParaImpressao(filtroCarteiraTO,
                clientesSelecionados == null ? "" : clientesSelecionados,
                ordem,
                campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog(nomeClasse));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, 0);
    }

    public String getDispararDataTables() {
        if (dispararDataTables.equals("")) {
            String contextoApp = context().getExternalContext().getRequestMap().get("contexto").toString();
            setDispararDataTables("dispararDatatables('" + contextoApp + "');");
        }
        return dispararDataTables;
    }

    public void setDispararDataTables(String dispararDataTables) {
        this.dispararDataTables = dispararDataTables;
    }

    /**
     * Gets and Sets
     */
    public List<FiltroCarteiraTO> getListaSugestoes() {
        return listaSugestoes;
    }

    public void setListaSugestoes(List<FiltroCarteiraTO> listaSugestoes) {
        this.listaSugestoes = listaSugestoes;
    }

    public List<SelectItem> getListaTiposColaboradores() {
        return listaTiposColaboradores;
    }

    public void setListaTiposColaboradores(List<SelectItem> listaTiposColaboradores) {
        this.listaTiposColaboradores = listaTiposColaboradores;
    }

    public List<SelectItem> getListaColaboradores() {
        return listaColaboradores;
    }

    public void setListaColaboradores(List<SelectItem> listaColaboradores) {
        this.listaColaboradores = listaColaboradores;
    }

    public FiltroCarteiraTO getFiltroCarteira() {
        return filtroCarteira;
    }

    public void setFiltroCarteira(FiltroCarteiraTO filtroCarteira) {
        this.filtroCarteira = filtroCarteira;
    }

    public List<SelectItem> getListaPeriodoAcesso() {
        return listaPeriodoAcesso;
    }

    public void setListaPeriodoAcesso(List<SelectItem> listaPeriodoAcesso) {
        this.listaPeriodoAcesso = listaPeriodoAcesso;
    }

    public List<SelectItem> getListaSituacaoCliente() {
        return listaSituacaoCliente;
    }

    public void setListaSituacaoCliente(List<SelectItem> listaSituacaoCliente) {
        this.listaSituacaoCliente = listaSituacaoCliente;
    }

    public List<InfoCarteiraTO> getListaVerColaboradores() {
        return listaVerColaboradores;
    }

    public void setListaVerColaboradores(List<InfoCarteiraTO> listaVerColaboradores) {
        this.listaVerColaboradores = listaVerColaboradores;
    }

    public FiltroCarteiraTO getFiltroBuscarCarteira() {
        return filtroBuscarCarteira;
    }

    public void setFiltroBuscarCarteira(FiltroCarteiraTO filtroBuscarCarteira) {
        this.filtroBuscarCarteira = filtroBuscarCarteira;
    }

    public FiltroCarteiraTO getFiltroVerTodasCarteiras() {
        return filtroVerTodasCarteiras;
    }

    public void setFiltroVerTodasCarteiras(FiltroCarteiraTO filtroVerTodasCarteiras) {
        this.filtroVerTodasCarteiras = filtroVerTodasCarteiras;
    }

    public FiltroCarteiraTO getFiltroOrganizarCarteira() {
        return filtroOrganizarCarteira;
    }

    public void setFiltroOrganizarCarteira(FiltroCarteiraTO filtroOrganizarCarteira) {
        this.filtroOrganizarCarteira = filtroOrganizarCarteira;
    }

    public Integer getOperacao() {
        return operacao;
    }

    public void setOperacao(Integer operacao) {
        this.operacao = operacao;
    }

    public FiltroCarteiraTO getDestino() {
        return destino;
    }

    public void setDestino(FiltroCarteiraTO destino) {
        this.destino = destino;
    }

    public List<VinculoVO> getVinculosAlterados() {
        return vinculosAlterados;
    }

    public void setVinculosAlterados(List<VinculoVO> vinculosAlterados) {
        this.vinculosAlterados = vinculosAlterados;
    }

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public List<CampoGR> getCamposFiltroAvancado() {
        return camposFiltroAvancado;
    }

    public void setCamposFiltroAvancado(List<CampoGR> camposFiltroAvancado) {
        this.camposFiltroAvancado = camposFiltroAvancado;
    }

    public List<ItemFiltroGR> getFiltrosAvancados() {
        return filtrosAvancados;
    }

    public void setFiltrosAvancados(List<ItemFiltroGR> filtrosAvancados) {
        this.filtrosAvancados = filtrosAvancados;
    }

    public boolean isApresentarFiltrosAvancados() {
        return apresentarFiltrosAvancados;
    }

    public void setApresentarFiltrosAvancados(boolean apresentarFiltrosAvancados) {
        this.apresentarFiltrosAvancados = apresentarFiltrosAvancados;
    }

    public List<SelectItem> getListaCamposFiltroAvancado() {
        return listaCamposFiltroAvancado;
    }

    public void setListaCamposFiltroAvancado(List<SelectItem> listaCamposFiltroAvancado) {
        this.listaCamposFiltroAvancado = listaCamposFiltroAvancado;
    }

    public ConfiguracaoSistemaCRMVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaCRMVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public List<Integer> getCodigosAgendamentosTransferir() {
        return codigosAgendamentosTransferir;
    }

    public void setCodigosAgendamentosTransferir(List<Integer> codigosAgendamentosTransferir) {
        this.codigosAgendamentosTransferir = codigosAgendamentosTransferir;
    }

    public Boolean getTransferirAgendamentos() {
        return transferirAgendamentos;
    }

    public void setTransferirAgendamentos(Boolean transferirAgendamentos) {
        this.transferirAgendamentos = transferirAgendamentos;
    }

    public Boolean getColaboradorSelecionadoPossuiUsuario() {
        return colaboradorSelecionadoPossuiUsuario;
    }

    public void setColaboradorSelecionadoPossuiUsuario(Boolean colaboradorSelecionadoPossuiUsuario) {
        this.colaboradorSelecionadoPossuiUsuario = colaboradorSelecionadoPossuiUsuario;
    }

    public Boolean getHabilitarBotaoConfirmarTransferencia() {
        return habilitarBotaoConfirmarTransferencia;
    }

    public void setHabilitarBotaoConfirmarTransferencia(Boolean habilitarBotaoConfirmarTransferencia) {
        this.habilitarBotaoConfirmarTransferencia = habilitarBotaoConfirmarTransferencia;
    }

    public Integer getTotalClientes() {
        if (totalClientes == null) {
            totalClientes = 0;
        }
        return totalClientes;
    }

    public void setTotalClientes(Integer totalClientes) {
        this.totalClientes = totalClientes;
    }

    public Integer getQuantidadeAlunosParaTransferencia() {
        return quantidadeAlunosParaTransferencia;
    }

    public void setQuantidadeAlunosParaTransferencia(Integer quantidadeAlunosParaTransferencia) {
        this.quantidadeAlunosParaTransferencia = quantidadeAlunosParaTransferencia;
    }

    public boolean isTransferirPorQuantidade() {
        return transferirPorQuantidade;
    }

    public void setTransferirPorQuantidade(boolean transferirPorQuantidade) {
        this.transferirPorQuantidade = transferirPorQuantidade;
    }

    public Integer getQuantidadeAlunosSelecionadosParaTransferencia() {
        return quantidadeAlunosSelecionadosParaTransferencia;
    }

    public void setQuantidadeAlunosSelecionadosParaTransferencia(Integer quantidadeAlunosSelecionadosParaTransferencia) {
        this.quantidadeAlunosSelecionadosParaTransferencia = quantidadeAlunosSelecionadosParaTransferencia;
    }

    public String getIdModalTransferencia() {
        return idModalTransferencia;
    }

    public void setIdModalTransferencia(String idModalTransferencia) {
        this.idModalTransferencia = idModalTransferencia;
    }

    public String getMensagemAlertaValidacaoTransferencia() {
        return mensagemAlertaValidacaoTransferencia;
    }

    public void setMensagemAlertaValidacaoTransferencia(String mensagemAlertaValidacaoTransferencia) {
        this.mensagemAlertaValidacaoTransferencia = mensagemAlertaValidacaoTransferencia;
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public boolean isBuscar() {
        return buscar;
    }

    public void setBuscar(boolean buscar) {
        this.buscar = buscar;
    }

    public int getQtdItensCasosImportantes() {
        return qtdItensCasosImportantes;
    }

    public void setQtdItensCasosImportantes(int qtdItensCasosImportantes) {
        this.qtdItensCasosImportantes = qtdItensCasosImportantes;
    }

    public Date getDataOperacaoTransarencia() {
        return dataOperacaoTransarencia;
    }

    public void setDataOperacaoTransarencia(Date dataOperacaoTransarencia) {
        this.dataOperacaoTransarencia = dataOperacaoTransarencia;
    }

    public int getCodUsuarioResponsavelTransf() {
        return codUsuarioResponsavelTransf;
    }

    public void setCodUsuarioResponsavelTransf(int codUsuarioResponsavelTransf) {
        this.codUsuarioResponsavelTransf = codUsuarioResponsavelTransf;
    }

    public List<SelectItem> getSelectUsuariosOperacaoTransf() {
        return selectUsuariosOperacaoTransf;
    }

    public void setSelectUsuariosOperacaoTransf(List<SelectItem> selectUsuariosOperacaoTransf) {
        this.selectUsuariosOperacaoTransf = selectUsuariosOperacaoTransf;
    }

    public void aplicarFiltrosAvancados(){
        try {
            setMsgAlert("");
            montarSucessoGrowl("");
            getFiltroCarteira().setFiltroAvancado(obterFiltrosAvancados());
            getFiltroCarteira().setFiltroJoinTabela(obterFiltrosJoinTabela());
            getFiltroCarteira().setFiltroAvancadoTexto(obterTextoFiltrosAvancados());
            montarSucessoGrowl("Filtros avançados aplicados com sucesso! " +
                    "Continue filtrando, caso tenha concluído, basta clicar em buscar.");
            setMsgAlert("Richfaces.hideModalPanel('modalFiltroGestaoCarteiras');");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
            e.printStackTrace();
        }
    }

    public void limparFiltrosAvancados(){
        try {
            montarSucessoGrowl("");
            getFiltroCarteira().setFiltroAvancado("");
            getFiltroCarteira().setFiltroJoinTabela("");
            getFiltroCarteira().setFiltroAvancadoTexto("");
            setFiltrosAvancados(new ArrayList<ItemFiltroGR>());
            getFiltrosAvancados().add(new ItemFiltroGR());
            montarSucessoGrowl("Filtros limpos com sucesso!");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
            e.printStackTrace();
        }
    }

    public Boolean getClientesSemProfessor() {
        return clientesSemProfessor;
    }

    public void setClientesSemProfessor(Boolean clientesSemProfessor) {
        this.clientesSemProfessor = clientesSemProfessor;
    }

    public Boolean getColabInativosVinculos() {
        return ColabInativosVinculos;
    }

    public void setColabInativosVinculos(Boolean colabInativosVinculos) {
        ColabInativosVinculos = colabInativosVinculos;
    }

    public Boolean getClienteComVinculos() {
        return clienteComVinculos;
    }

    public void setClienteComVinculos(Boolean clienteComVinculos) {
        this.clienteComVinculos = clienteComVinculos;
    }

    public Boolean getClientesSemCon() {
        return ClientesSemCon;
    }

    public void setClientesSemCon(Boolean clientesSemCon) {
        ClientesSemCon = clientesSemCon;
    }

    public Boolean getConsultorCartVazia() {
        return consultorCartVazia;
    }

    public void setConsultorCartVazia(Boolean consultorCartVazia) {
        this.consultorCartVazia = consultorCartVazia;
    }

    public Boolean getPanelCarteiras() {
        return panelCarteiras;
    }

    public void setPanelCarteiras(Boolean panelCarteiras) {
        this.panelCarteiras = panelCarteiras;
    }

    public Boolean getPanelTodasCarteiras() {
        return panelTodasCarteiras;
    }

    public void setPanelTodasCarteiras(Boolean panelTodasCarteiras) {
        this.panelTodasCarteiras = panelTodasCarteiras;
    }

    public String getResultado() {
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public String getApresentarBlocoTabela() {
        return (resultado.isEmpty()) ? "" : "width: 100%; margin-left: 0px;";
    }

    public String getOnCompleteDetalhes() {
        return onCompleteDetalhes;
    }

    public void setOnCompleteDetalhes(String onCompleteDetalhes) {
        this.onCompleteDetalhes = onCompleteDetalhes;
    }

    public boolean isMarcarTodosPagina() {
        return marcarTodosPagina;
    }

    public void setMarcarTodosPagina(boolean marcarTodosPagina) {
        this.marcarTodosPagina = marcarTodosPagina;
    }

    public boolean isCasosImportantesAvaliar() {
        return casosImportantesAvaliar;
    }

    public void setCasosImportantesAvaliar(boolean casosImportantesAvaliar) {
        this.casosImportantesAvaliar = casosImportantesAvaliar;
    }

 }
