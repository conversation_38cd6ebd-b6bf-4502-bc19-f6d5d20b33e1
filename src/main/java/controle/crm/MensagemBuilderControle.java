package controle.crm;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.MensagemEntregabilidadeControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.*;
import negocio.comuns.utilitarias.*;
import servicos.propriedades.PropsService;

import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.model.SelectItem;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;

public class MensagemBuilderControle extends SuperControle {

    private List<MsgBuildDTO> templates;
    private Boolean visaoAgendados = false;
    private ModeloMensagemVO modelo;
    private Integer limite;
    private Integer emailsHoje = 0;
    private Integer emailsMes = 0;
    private List<SelectItem> tipos;
    private List<MsgBuildDTO> predefinidos = new ArrayList<>();
    private List<MsgBuildDTO> predefinidosAntigos = new ArrayList<>();
    private Boolean mostrarAntigos = false;
    private Boolean building = false;
    private Boolean replicarEmpresa = false;
    private Boolean editandoModelo = false;
    private Boolean configEnvio = false;
    private String html;
    private String configs;
    private String rows;
    private String texto;
    private Integer codigoSelecionado;
    private Integer codigoTemplateSelecionado;
    private MeioEnvio meioEnvio = MeioEnvio.EMAIL;
    private Boolean modeloAntigoSelecionado = false;
    private String modalMensagemEntregabilidade;
    private String msgAlert;
    private String url = PropsService.getPropertyValue(PropsService.urlVendasOnline);
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
    private String webHookFluxo;
    private Integer idConfiguracaoGymbotPro;

    private boolean habilitarbotconversa = false;
    private boolean habilitargymbotpro = false;
    private List<ConfiguracaoIntegracaoBotConversaVO> configuracaoBotConversa =  new ArrayList<>();;



    public List<SelectItem> getListaFluxo() throws Exception {
        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        List<SelectItem> lista = new ArrayList<>();
        List<ConfiguracaoIntegracaoBotConversaVO>  listafluxo = getFacade().getMalaDireta().consultarFluxoEmpresa(empresaVO.getCodigo());
        for (ConfiguracaoIntegracaoBotConversaVO fluxo : listafluxo) {
            if (fluxo.getTipofluxo() != null && fluxo.getTipofluxo().equals("0") && fluxo.getAtivo()) {
                lista.add(new SelectItem(fluxo.getUrlwebhoobotconversa(), fluxo.getDescricao()));
            }
        }
        if (!listafluxo.isEmpty()) {
            this.setHabilitarbotconversa(true);
        } else {
            this.setHabilitarbotconversa(false);
        }
        return lista;
    }

    public List<SelectItem> getListaFluxoGymbotPro() throws Exception {
        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        List<SelectItem> lista = new ArrayList<>();
        List<ConfiguracaoIntegracaoGymbotProVO>  listafluxo = getFacade().getMalaDireta().consultarFluxoEmpresaGymbotPro(empresaVO.getCodigo());
        for (ConfiguracaoIntegracaoGymbotProVO fluxo : listafluxo) {
            if (fluxo.getTipofluxo() != null && fluxo.getTipofluxo().equals("0") && fluxo.getAtivo()) {
                lista.add(new SelectItem(fluxo.getCodigo(), fluxo.getDescricao()));
            }
        }
        if (!listafluxo.isEmpty()) {
            this.setHabilitargymbotpro(true);
        } else {
            this.setHabilitargymbotpro(false);
        }
        return lista;
    }

    public MensagemBuilderControle(){
    }

    public void atualizarEmailsHoje(){
        try {
            emailsHoje = getFacade().getMalaDireta().countEmails(Calendario.hoje(), true, configuracaoSistemaCRMVO.getIntegracaoPacto());
            emailsMes = getFacade().getMalaDireta().countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, configuracaoSistemaCRMVO.getIntegracaoPacto());
        }catch (Exception e){
            emailsMes = 0;
            emailsHoje = 0;
        }

    }

    public String init() {
        try {
            modeloAntigoSelecionado = false;
            mostrarAntigos = false;
            editandoModelo = false;
            visaoAgendados = false;
            meioEnvio = MeioEnvio.EMAIL;
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.novo();
            malaDiretaControle.setConsultarMeioEnvio(meioEnvio.getCodigo());
            malaDiretaControle.consultarPaginado();
            configuracaoSistemaCRMVO = malaDiretaControle.getConfiguracaoSistemaCRMVO();
            getListaFluxo();
            getListaFluxoGymbotPro();
            codigoSelecionado = null;
            building = false;
            configEnvio = false;
            replicarEmpresa = false;
            montarListaTipos();
            montarPredefinidos();
            obterAntigos();
            atualizarEmailsHoje();
            limite = getFacade().getConfiguracaoSistemaCRM().obterLimiteDiario();
            initTemplates();
        }catch (Exception e){
            montarErro(e);
            Uteis.logar(e, MensagemBuilderControle.class);
        }
        return "mensagemBuilder";
    }

    public void acaoEsconderAntigos(){
        this.mostrarAntigos = false;
    }

    public void acaoMostrarAntigos(){
        try {
            this.mostrarAntigos = true;
            obterAntigos();
        }catch (Exception e){
            montarErro(e);
        }
    }

    private void obterAntigos() throws Exception {
        predefinidosAntigos = getFacade().getModeloMensagem().consultarModelosAntigos();

        if (meioEnvio.getDescricao().equals(MeioEnvio.WHATSAPP.getDescricao())){
            predefinidosAntigos =  getFacade().getModeloMensagem().consultarMensagensWhatsAppBuilder(getEmpresaLogado().getCodigo(), getKey(), meioEnvio);
        }

        for(MsgBuildDTO msg : predefinidosAntigos){
            msg.setNome(URLDecoder.decode(msg.getNome(), "iso-8859-1"));
            msg.setHtml(msg.getHtml().replace("<script ", "<a ").replace("</script>", "</a>" ));
        }
    }

    public void initTemplates(){
        try {
            templates = Ordenacao.ordenarListaReverse(getFacade().getModeloMensagem().getTemplatesPacto(getKey()), "codigo");
        }catch (Exception e){
            templates = new ArrayList<>();
        }
    }

    public void montarPredefinidos() throws Exception{
        predefinidos = getFacade().getModeloMensagem().consultarMensagensBuilder(meioEnvio);

        if (meioEnvio.getDescricao().equals(MeioEnvio.WHATSAPP.getDescricao())){
            predefinidos =  getFacade().getModeloMensagem().consultarMensagensWhatsAppBuilder(getEmpresaLogado().getCodigo(), getKey(), meioEnvio);
        }

        for(MsgBuildDTO msg : predefinidos){
            msg.setNome(URLDecoder.decode(msg.getNome(), "iso-8859-1"));
        }
    }

    public void montarListaTipos() {
        tipos = new ArrayList<>();
        Hashtable tipoMensagem = Dominios.getTipoMensagem();
        Enumeration keys = tipoMensagem.keys();
        tipos.add(new SelectItem("", ""));
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoMensagem.get(value);
            tipos.add(new SelectItem(value, label));
        }
    }

    public void novoComTemplate() {
        novo();
        for(MsgBuildDTO template : templates){
            if(template.getCodigo().equals(codigoTemplateSelecionado)){
                html = template.getHtml();
                configs = template.getBody();
                modelo = new ModeloMensagemVO();
                modelo.setConfigs(configs);
            }
        }
    }

    public void novo() {
        try {
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.novo();
            editandoModelo = false;
            building = true;
            replicarEmpresa = false;
            configEnvio = false;
            zerar();
            codigoSelecionado = null;
        }catch (Exception e){
            montarErro(e);
        }
    }

    private void zerar() {
        building = true;
        replicarEmpresa = false;
        html = "";
        texto = "";
        configs = "";
        modeloAntigoSelecionado = false;
        modelo = new ModeloMensagemVO();
    }

    public void prosseguirEnvio(){
        try {
            initGravar();
            setMsgAlert("");
            configEnvio = true;
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);


            if(configuracaoSistemaCRMVO.getUsarRemetentePadraoGeral()){
                malaDiretaControle.getMalaDiretaVO().getRemetente().setNome(configuracaoSistemaCRMVO.getRemetentePadrao());
            }
            else {
                malaDiretaControle.getMalaDiretaVO().setRemetente(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioLogado().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL));
            }

            if (!UteisValidacao.emptyNumber(getIdConfiguracaoGymbotPro())) {

                ConfiguracaoIntegracaoGymbotProVO configuracaoIntegracaoGymbotProVO = getFacade().getMalaDireta().consultarFluxoGymbotPro(getIdConfiguracaoGymbotPro(), getEmpresaLogado().getCodigo());
                malaDiretaControle.getMalaDiretaVO().setConfigGymbotPro(configuracaoIntegracaoGymbotProVO);

            }
            malaDiretaControle.getMalaDiretaVO().setUrlwebhoobotconversa(getWebHookFluxo());
            malaDiretaControle.getMalaDiretaVO().setMensagem(getMeioEmail() ? html : texto);
            malaDiretaControle.getMalaDiretaVO().setConfigs(getModelo().getConfigs());
            malaDiretaControle.getMalaDiretaVO().setMeioDeEnvio(meioEnvio.getCodigo());
            malaDiretaControle.getMalaDiretaVO().setMeioDeEnvioEnum(meioEnvio);
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar(true));
        }
    }

    public void voltarBuilder(){
        configEnvio = false;
        configs = modelo.getConfigs();
        removeTags();
        modelo.setConfigs(configs);
        MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
        malaDiretaControle.setExibirReplicarRedeEmpresa(false);
    }
    public void openPopup(){
        try{
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            setMsgAlert("window.open('" + loginControle.getAbrirNCRM() + "&redirect=/crm/template-whatsapp', 'Whatsapp')");
        }catch (Exception e){
            Uteis.logar(e, MensagemBuilderControle.class);
        }
    }


    public void voltarLista(){
        try {
            setMsgAlert("");
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            cancelar();
            entrarVisaoAgendados();
            malaDiretaControle.consultarPaginado();
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar(true));
        }
    }

    public void visualizarMensagemGrupo(){
        try {
            setMsgAlert("");
            MalaDiretaVO obj = (MalaDiretaVO) context().getExternalContext().getRequestMap().get("malaDireta");
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.editar(obj);
            meioEnvio = malaDiretaControle.getMalaDiretaVO().getMeioDeEnvioEnum();
            visaoAgendados = false;
            modeloAntigoSelecionado = false;
            configEnvio = true;
            building = true;
            replicarEmpresa = false;
            html = obj.getMensagem();
            texto = obj.getMensagem();
            modelo = new ModeloMensagemVO();
            modelo.setConfigs(obj.getConfigs());
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar(true));
        }

    }
    public void msgEntregabilidade(){
        setMsgAlert("");
        limparMsg();
        MensagemEntregabilidadeControle control = (MensagemEntregabilidadeControle) getControlador(MensagemEntregabilidadeControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemEntregabilidade("Richfaces.showModalPanel('mdlMensagemEntregabilidade');");
        control.init("Status de Entregabilidade", "Não foi possível enviar os e-mails devido ao seu limite de contatos. Para solucionar esta situação, faça uma atualização do seu plano junto a WAGI na PactoStore.", this, "", "",",mdlMensagemGenerica");

    }
    public void initExcluir() {

        final AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                ModeloMensagemVO modeloMensagemVO = new ModeloMensagemVO();
                modeloMensagemVO.setCodigo(codigoSelecionado);
                getFacade().getModeloMensagem().desativar(modeloMensagemVO.getCodigo());
                setMensagem("Modelo removido com sucesso!");
                montarPredefinidos();
                obterAntigos();
                if(mostrarAntigos){
                    mostrarAntigos = !UteisValidacao.emptyList(predefinidosAntigos);
                }
                recarregarPagina();
                setProcessandoOperacao(false);
                montarSucessoGrowl("Modelo removido com sucesso!");
                auto.setRenderComponents("mensagembuilder");
                setExecutarAoCompletar("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');" + getMensagemNotificar(true));
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }

        };
        limparMsg();
        auto.autorizar("Excluir modelo de mensagem", "ModeloMensagem",
                "Você precisa da permissão \"7.09 - Modelo Mensagem\" para executar essa ação.",
                "mensagembuilder", listener);
    }

    public void excluir() throws Exception{
        setMsgAlert("");
        ModeloMensagemVO modeloMensagemVO = new ModeloMensagemVO();
        modeloMensagemVO.setCodigo(codigoSelecionado);
        getFacade().getModeloMensagem().desativar(modeloMensagemVO.getCodigo());
        setMensagemID("");
        setMensagem("Modelo removido com sucesso!");
        setSucesso(true);
        setMsgAlert("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');" + getMensagemNotificar(true));
        montarPredefinidos();
        obterAntigos();
        if(mostrarAntigos){
            mostrarAntigos = !UteisValidacao.emptyList(predefinidosAntigos);
        }
    }

    public void editar(){
        try {
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.novo();
            editandoModelo = true;
            building = true;
            replicarEmpresa = false;
            configEnvio = false;
            montarBuild();
        }catch (Exception e){
            montarErro(e);
        }

    }

    public void editarOld(){
        try {
            ModeloMensagemControle modeloMensagemControle = getControlador(ModeloMensagemControle.class);
            modeloMensagemControle.editar(codigoSelecionado);
        }catch (Exception e){
            montarErro(e);
        }

    }


    public void enviarModeloWhatsApp(){
        try {
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.novo();
            editandoModelo = false;
            building = true;
            replicarEmpresa = false;
            configEnvio = false;
            montarBuild();
            prosseguirEnvio();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void enviarModelo(){
        try {
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.novo();
            editandoModelo = false;
            building = true;
            replicarEmpresa = false;
            configEnvio = false;
            montarBuild();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void montarBuild(){
        zerar();
        if(UteisValidacao.emptyNumber(codigoSelecionado)){
           return;
        }
        boolean modeloObtido = obterModelo(predefinidos);
        if(!modeloObtido){
            try {
                modeloAntigoSelecionado = obterModelo(predefinidosAntigos);
                ModeloMensagemVO modeloMensagemVO = getFacade().getModeloMensagem().consultarPorChavePrimaria(codigoSelecionado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                html = modeloMensagemVO.getMensagem();
                prosseguirEnvio();
            }catch (Exception e){
                zerar();
                codigoSelecionado = null;
            }
        }
    }

    private boolean obterModelo(List<MsgBuildDTO> modelos) {
        for(MsgBuildDTO msg : modelos){
            if(msg.getCodigo().equals(codigoSelecionado)){
                configs = msg.getBody();
                removeTags();
                modelo.setConfigs(configs);
                modelo.setTipoMensagem(msg.getTipo());
                modelo.setCodigo(codigoSelecionado);
                modelo.setTitulo(msg.getNome());
                texto = msg.getHtml();
                return true;
            }
        }
        return false;
    }

    private void removeTags() {
        if(configs != null){
            JSONObject content =   new JSONObject(configs);
            content.remove("amp");
            content.getJSONObject("chunks").remove("tags");
            content.getJSONObject("design").getJSONObject("body").remove("headers");
            content.getJSONObject("design").getJSONObject("body").remove("footers");
            configs = content.toString();
        }
    }


    public String getBody(){
        try {
            return new JSONObject(modelo.getConfigs()).toString();
        }catch (Exception e){
            return new JSONObject().toString();
        }

    }

    public void initGravar() throws Exception{
        try {
            if(getMeioEmail()){
                JSONObject jsonDesign = new JSONObject(configs);
                jsonDesign.getJSONObject("design").getJSONObject("body").put("rows", new JSONArray(rows.replace("\"[", "[").replace("]\"", "]").replace("\\\"", "\"").replace("\\\\", "\\")));
                modelo.setConfigs(jsonDesign.toString());
                html = footerEmails(html);
                modelo.setMensagem(html);
            }else{
                modelo.setMensagem(texto);
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public void cancelar() throws IOException {
        building = false;
    }

    public void gravarEnviando() {
        gravar(true);
    }

    public void gravarConfig() {
        try {
            setMsgAlert("");
            getFacade().getConfiguracaoSistemaCRM().alterarLimiteDiarioLimite(limite);
            montarSucesso(null);
            setMensagem("Limite diário alterado!");
            setMsgAlert(getMensagemNotificar(true) + "; Richfaces.hideModalPanel('modalconfigs');");
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar(true));
        }
    }
    public void gravar() {
        gravar(false);
    }

    public void gravar(boolean enviar) {
        try {
            setMsgAlert("");
            if(getMeioSms()){
                if(!modelo.getMensagem().contains("TAG_NOME") && !modelo.getMensagem().contains("TAG_PNOME")){
                    throw new ConsistirException("Para envio de SMS é obrigatório incluir uma das TAGS [TAG_PNOME ou TAG_NOME], para que o envio não seja bloqueado pela política de anti-spam das operadoras!");
                }
            }
            if(getMeioBotConversa()){
                modelo.setMensagem(modelo.getMensagem() + "TAG_PNOME;" + "TAG_NOME;" );
                if(getWebHookFluxo().equals("")){
                    throw new ConsistirException("Para envio de um fluxo para o GymBot é obrigatório selecionar um fluxo!");
                }
            }

            modelo.setMeioDeEnvio(this.meioEnvio.getCodigo());
            modelo.setAtivo(true);
            modelo.setBuilder(true);
            if(UteisValidacao.emptyNumber(codigoSelecionado)){
                getFacade().getModeloMensagem().incluir(modelo);
            } else {
                getFacade().getModeloMensagem().alterar(modelo);
            }
            codigoSelecionado = null;
            montarSucesso(null);
            setMensagem("Seu modelo personalizado de mensagem foi salvo!");
            if(enviar){
                prosseguirEnvio();
            }else{
                cancelar();
            }
            montarPredefinidos();
            setMsgAlert(getMensagemNotificar(true) + "; Richfaces.hideModalPanel('modalsave');");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar(true));
        }
    }

    public void simEnviarSomenteSaldoRestanteDia(){
        gravarMaladireta(true, false);
    }

    public void simEnviarSomenteSaldoRestanteMes(){
        gravarMaladireta(true, true);
    }

    public void gravarMaladireta() throws IOException {
        gravarMaladireta(false, false);
    }

    public void gravarMaladireta(boolean simEnviarSomenteSaldoRestanteDia, boolean simEnviarSomenteSaldoRestanteMes){
        try {
            setMsgAlert("");
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.getMalaDiretaVO().setMeioDeEnvioEnum(meioEnvio);
            malaDiretaControle.getMalaDiretaVO().setMeioDeEnvio(meioEnvio.getCodigo());
            malaDiretaControle.getMalaDiretaVO().setSmsMarketing(false);
            malaDiretaControle.getMalaDiretaVO().setEnvioHabilitado(true);
            malaDiretaControle.getMalaDiretaVO().setNovoObj(UteisValidacao.emptyNumber(malaDiretaControle.getMalaDiretaVO().getCodigo()));
            if(getMeioSms()){
                malaDiretaControle.consultarClientes();
                malaDiretaControle.consultarSaldo();
                Integer destinatarios = malaDiretaControle.getListaAmostra().size();
                Integer saldo = malaDiretaControle.getSaldo() == null || malaDiretaControle.getSaldo().getBalance() == null ? 0 : malaDiretaControle.getSaldo().getBalance();
                Integer saldoMarketing = malaDiretaControle.getSaldoMarketing() == null || malaDiretaControle.getSaldoMarketing().getBalance() == null ? 0 : malaDiretaControle.getSaldoMarketing().getBalance();
                malaDiretaControle.getMalaDiretaVO().setSmsMarketing(destinatarios > saldo);
                if(destinatarios > saldo){
                    malaDiretaControle.getMalaDiretaVO().setEnvioHabilitado(destinatarios < saldoMarketing);
                }
            }
            malaDiretaControle.setSimEnviarSomenteSaldoRestanteDia(simEnviarSomenteSaldoRestanteDia);
            malaDiretaControle.setSimEnviarSomenteSaldoRestanteMes(simEnviarSomenteSaldoRestanteMes);
            malaDiretaControle.getMalaDiretaVO().setIdTemplate(codigoSelecionado);
            String returnGravar = malaDiretaControle.gravar();
            if (returnGravar.equals("OK")) {
                cancelar();
                malaDiretaControle.setConsultarMeioEnvio(meioEnvio.getCodigo());
                entrarVisaoAgendados();
                setMensagemID("");
                if(!UteisValidacao.emptyNumber(configuracaoSistemaCRMVO.getLimiteDiarioEmails()) && !configuracaoSistemaCRMVO.getIntegracaoPacto() && simEnviarSomenteSaldoRestanteDia){
                    setMensagem("Envio agendado com sucesso. Será enviado para "+malaDiretaControle.getQtdEmailsRestandoEnviarDia()+" destinatários devido ao limite diário.");
                }else if(!UteisValidacao.emptyNumber(configuracaoSistemaCRMVO.getLimiteDiarioEmails()) && configuracaoSistemaCRMVO.getIntegracaoPacto() && simEnviarSomenteSaldoRestanteDia && !simEnviarSomenteSaldoRestanteMes){
                    setMensagem("Envio agendado com sucesso. Será enviado para "+malaDiretaControle.getQtdEmailsRestandoEnviarDia()+" destinatários devido ao limite diário.");
                }else if(!UteisValidacao.emptyNumber(configuracaoSistemaCRMVO.getLimiteDiarioEmails()) && configuracaoSistemaCRMVO.getIntegracaoPacto() && simEnviarSomenteSaldoRestanteDia && simEnviarSomenteSaldoRestanteMes){
                    setMensagem("Envio agendado com sucesso. Será enviado para "+malaDiretaControle.getQtdEmailsRestandoEnviarMensal()+" destinatários devido ao limite mensal.");
                }else {
                    setMensagem("Envio agendado com sucesso!");
                }
                setSucesso(true);
                setMsgAlert(
                        (malaDiretaControle.getMalaDiretaVO().getSmsMarketing() && !malaDiretaControle.getMalaDiretaVO().getEnvioHabilitado() ?
                                "Richfaces.showModalPanel('comprarCreditoMarketing');" : getMensagemNotificar(true)));
            }else if(returnGravar.equals("LIMITE-DIARIO-ATINGIDO")){
                setMsgAlert("Richfaces.showModalPanel('limiteDiarioAtingido');");
            }else if(returnGravar.equals("LIMITE-MENSAL-ATINGIDO")){
                setMsgAlert("Richfaces.hideModalPanel('limiteDiarioAtingido');Richfaces.showModalPanel('limiteMensalAtingido');");
            } else {
                throw new Exception(returnGravar);
            }
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar(true));
        }
    }

    public void habilitarEnvio() throws Exception{
        setMsgAlert("");
        setSucesso(false);
        MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
        malaDiretaControle.consultarClientes();
        malaDiretaControle.consultarSaldo();
        Integer destinatarios = malaDiretaControle.getListaAmostra().size();
        Integer saldoMarketing = malaDiretaControle.getSaldoMarketing() == null || malaDiretaControle.getSaldoMarketing().getBalance() == null ? 0 : malaDiretaControle.getSaldoMarketing().getBalance();
        malaDiretaControle.getMalaDiretaVO().setSmsMarketing(destinatarios > saldoMarketing);
        setMensagemID("");
        if(destinatarios > saldoMarketing){
            setMsgAlert("Richfaces.showModalPanel('comprarCreditoMarketing');");
        }else{
            malaDiretaControle.habilitarEnvio(malaDiretaControle.getMalaDiretaVO());
            setMensagem("Envio agendado com sucesso!");
            setSucesso(true);
            setMsgAlert(getMensagemNotificar(true));
        }
    }

    public void forcarEnvio(){
        try {
            setMsgAlert("");
            setSucesso(false);
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.getMalaDiretaVO().setSmsMarketing(false);
            setMensagemID("");
            malaDiretaControle.habilitarEnvio(malaDiretaControle.getMalaDiretaVO());
            setMensagem("Envio agendado com sucesso!");
            setSucesso(true);
        }catch (Exception e){
            montarErro(e);
            setMensagem(e.getMessage() == null ? "Erro ao habilitar envio" : e.getMessage());
        }
        setMsgAlert(getMensagemNotificar(true));
    }

    public Integer getPercentualEnviadosEmail(){
        try {
            Integer totalUsar = emailsHoje > limite ? limite : emailsHoje;
            return limite == null || limite == 0 ? 0 : new Double((totalUsar.doubleValue()/limite.doubleValue())*100.0).intValue();
        }catch (Exception e){
            return 0;
        }
    }

    public Integer getPercentualEnviadosEmailMes(){
        try {
            Integer limitePacto = ((MalaDiretaControle) JSFUtilities.getFromSession(MalaDiretaControle.class.getSimpleName())).getConfigCRM().getLimiteMensalPacto();
            Integer emailsMesUsar = emailsMes > limitePacto ? limitePacto : emailsMes;
            return limitePacto == null || limitePacto == 0 ? 0 : new Double((emailsMesUsar.doubleValue()/limitePacto.doubleValue())*100.0).intValue();
        }catch (Exception e){
            return 0;
        }
    }

    public String abrirPactoStore() {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            return loginControle.abrirModuloCanalClientePactoStore();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public List getTipos() throws Exception {
        return tipos;
    }

    public ModeloMensagemVO getModelo() {
        return modelo;
    }

    public void setModelo(ModeloMensagemVO modelo) {
        this.modelo = modelo;
    }

    public void setTipos(List<SelectItem> tipos) {
        this.tipos = tipos;
    }

    public Boolean getBuilding() {
        return building;
    }

    public void setBuilding(Boolean building) {
        this.building = building;
    }

    public List<MsgBuildDTO> getPredefinidos() {
        return predefinidos;
    }

    public void setPredefinidos(List<MsgBuildDTO> predefinidos) {
        this.predefinidos = predefinidos;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public String getConfigs() {
        return configs;
    }

    public void setConfigs(String configs) {
        this.configs = configs;
    }

    public Integer getCodigoSelecionado() {
        return codigoSelecionado;
    }

    public void setCodigoSelecionado(Integer codigoSelecionado) {
        this.codigoSelecionado = codigoSelecionado;
    }

    public MeioEnvio getMeioEnvio() {
        return meioEnvio;
    }

    public void setMeioEnvio(MeioEnvio meioEnvio) {
        this.meioEnvio = meioEnvio;
    }

    public void selecionarMeioEmail(){
        try {
            this.meioEnvio = MeioEnvio.EMAIL;
            mudarMeio();
            montarPredefinidos();
            limite = getFacade().getConfiguracaoSistemaCRM().obterLimiteDiario();
            atualizarEmailsHoje();
        }catch (Exception e){
            montarErro(e);
        }
    }

    private void mudarMeio() {
        MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
        malaDiretaControle.setConsultarMeioEnvio(this.meioEnvio.getCodigo());
        malaDiretaControle.consultarPaginado();
    }

    public void selecionarMeioApp(){
        try {
            this.meioEnvio = MeioEnvio.APP;
            mudarMeio();
            montarPredefinidos();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void selecionarMeioWebHook(){
        try {
            this.meioEnvio = MeioEnvio.WEBHOOK;
            mudarMeio();
            montarPredefinidos();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void selecionarMeioBotConversa(){
        try {
            this.meioEnvio = MeioEnvio.GYMBOT;
            mudarMeio();
            montarPredefinidos();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void selecionarMeioGymbotPro(){
        try {
            this.meioEnvio = MeioEnvio.GYMBOT_PRO;
            mudarMeio();
            montarPredefinidos();
        }catch (Exception e){
            montarErro(e);
        }
    }


    public void selecionarMeioWhatsapp(){
        try {
            this.meioEnvio = MeioEnvio.WHATSAPP;
            mudarMeio();
            montarPredefinidos();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void selecionarMeioSms(){
        try {
            this.meioEnvio = MeioEnvio.SMS;
            mudarMeio();
            montarPredefinidos();

            try {
                MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
                malaDiretaControle.consultarSaldo();
            } catch (Exception ex){
                ex.printStackTrace();
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    public Boolean getMeioEmail(){
        return this.meioEnvio.equals(MeioEnvio.EMAIL);
    }

    public Boolean getMeioSms(){
        return this.meioEnvio.equals(MeioEnvio.SMS);
    }

    public Boolean getMeioApp(){
        return this.meioEnvio.equals(MeioEnvio.APP);
    }

    public Boolean getMeioWebhook(){
        return this.meioEnvio.equals(MeioEnvio.WEBHOOK);
    }

    public Boolean getMeioWhatsApp(){
        return this.meioEnvio.equals(MeioEnvio.WHATSAPP);
    }

    public Boolean getMeioBotConversa(){ return this.meioEnvio.equals(MeioEnvio.GYMBOT);
    }

    public Boolean getMeioGymbotPro(){ return this.meioEnvio.equals(MeioEnvio.GYMBOT_PRO);
    }

    public Boolean getVisaoAgendados() {
        return visaoAgendados;
    }

    public void setVisaoAgendados(Boolean visaoAgendados) {
        this.visaoAgendados = visaoAgendados;
    }

    public void entrarVisaoAgendados(){
        this.visaoAgendados = true;
        try {
            MalaDiretaControle malaDiretaControle = getControlador(MalaDiretaControle.class);
            malaDiretaControle.consultarPaginado();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void sairVisaoAgendados(){
        this.visaoAgendados = false;
    }

    public Boolean getConfigEnvio() {
        return configEnvio;
    }

    public void setConfigEnvio(Boolean configEnvio) {
        this.configEnvio = configEnvio;
    }

    public String getRows() {
        return rows;
    }

    public void setRows(String rows) {
        this.rows = rows;
    }

    public Integer getLimite() {
        if(limite == null){
            limite = 0;
        }
        return limite;
    }

    public void setLimite(Integer limite) {
        this.limite = limite;
    }

    public Integer getEmailsHoje() {
        return emailsHoje;
    }

    public void setEmailsHoje(Integer emailsHoje) {
        this.emailsHoje = emailsHoje;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public Boolean getEditandoModelo() {
        return editandoModelo;
    }

    public void setEditandoModelo(Boolean editandoModelo) {
        this.editandoModelo = editandoModelo;
    }

    public Integer getEmailsMes() {
        return emailsMes;
    }

    public void setEmailsMes(Integer emailsMes) {
        this.emailsMes = emailsMes;
    }

    public List<MsgBuildDTO> getTemplates() {
        if(templates == null){
            templates = new ArrayList<>();
        }
        return templates;
    }

    public void setTemplates(List<MsgBuildDTO> templates) {
        this.templates = templates;
    }

    public Integer getCodigoTemplateSelecionado() {
        return codigoTemplateSelecionado;
    }

    public void setCodigoTemplateSelecionado(Integer codigoTemplateSelecionado) {
        this.codigoTemplateSelecionado = codigoTemplateSelecionado;
    }

    public Boolean getMostrarAntigos() {
        return mostrarAntigos;
    }

    public void setMostrarAntigos(Boolean mostrarAntigos) {
        this.mostrarAntigos = mostrarAntigos;
    }

    public List<MsgBuildDTO> getPredefinidosAntigos() {
        return predefinidosAntigos;
    }

    public void setPredefinidosAntigos(List<MsgBuildDTO> predefinidosAntigos) {
        this.predefinidosAntigos = predefinidosAntigos;
    }

    public Boolean getModeloAntigoSelecionado() {
        return modeloAntigoSelecionado;
    }

    public void setModeloAntigoSelecionado(Boolean modeloAntigoSelecionado) {
        this.modeloAntigoSelecionado = modeloAntigoSelecionado;
    }

    public String getModalMensagemEntregabilidade() {
        return modalMensagemEntregabilidade;
    }

    public void setModalMensagemEntregabilidade(String modalMensagemEntregabilidade) {
        this.modalMensagemEntregabilidade = modalMensagemEntregabilidade;
    }

    @Override
    public String getMsgAlert() {
        return msgAlert;
    }

    @Override
    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String footerEmails(String html) throws Exception {
        if (html.contains("unsubscribe-optin")) {
            return html;
        }
        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        String urlUnsubscribe = url + "/unsubscribe-optin?key=";

        String parametrosEmpresa = getKey() + "&" + empresaVO.getCodigo() + "&" + empresaVO.getEmail() + "&";
        parametrosEmpresa = Criptografia.encrypt(parametrosEmpresa, Crypt_KEY , AlgoritmoCriptoEnum.ALGORITMO_AES);

        String parametrosCliente = "TAG_CODCLIENTE " + "TAG_EMAIL_CLIENTE";

        String[] result = html.split("</body>");

        String footer = "</body>" +
                "<footer>" +
                "<p style=\"text-align: center; font-size: 13px; color: #83888F;\">" +
                "Se deseja não receber mais mensagens como esta, clique " +
                "<a target=\"_blank\" style=\"color: inherit; font-size: 13px\" href=\"" + urlUnsubscribe + parametrosEmpresa + "+" + parametrosCliente + "\"><b>aqui</b></a>." +
                "</p>" +
                "<p style=\"text-align: center; font-size: 10px; color: #83888F;\">" +
                "Caso o link acima não funcione, clique <unsubscribe>aqui</unsubscribe>" +
                "</p>" +
                "</footer>";


        html = result[0] + footer + result[1];
        return html;
    }


    public Boolean getReplicarEmpresa() {
        return replicarEmpresa;
    }

    public void setReplicarEmpresa(Boolean replicarEmpresa) {
        this.replicarEmpresa = replicarEmpresa;
    }

    public void acionarAbaReplicarEmpresa() {
        visaoAgendados = false;
        modeloAntigoSelecionado = false;
        configEnvio = false;
        building = false;
        replicarEmpresa = true;
    }

    public void acionarAbaConfigEnvio() {
        visaoAgendados = false;
        modeloAntigoSelecionado = false;
        replicarEmpresa = false;
        configEnvio = true;
        building = true;
    }

    public String getWebHookFluxo() {
        return webHookFluxo;
    }

    public void setWebHookFluxo(String webHookFluxo) {
        this.webHookFluxo = webHookFluxo;
    }

    public Integer getIdConfiguracaoGymbotPro() {
        return idConfiguracaoGymbotPro;
    }

    public void setIdConfiguracaoGymbotPro(Integer idConfiguracaoGymbotPro) {
        this.idConfiguracaoGymbotPro = idConfiguracaoGymbotPro;
    }

    public List<ConfiguracaoIntegracaoBotConversaVO> getConfiguracaoBotConversa() {
        return configuracaoBotConversa;
    }

    public void setConfiguracaoBotConversa(List<ConfiguracaoIntegracaoBotConversaVO> configuracaoBotConversa) {
        this.configuracaoBotConversa = configuracaoBotConversa;
    }

    public boolean isHabilitarbotconversa() {
        return habilitarbotconversa;
    }

    public void setHabilitarbotconversa(boolean habilitarbotconversa) {
        this.habilitarbotconversa = habilitarbotconversa;
    }

    public boolean isHabilitargymbotpro() {
        return habilitargymbotpro;
    }

    public void setHabilitargymbotpro(boolean habilitargymbotpro) {
        this.habilitargymbotpro = habilitargymbotpro;
    }
}
