package controle.contrato;


import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.AssinaturaDigital;
import negocio.facade.jdbc.contrato.Contrato;

import javax.faces.context.FacesContext;
import java.io.File;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AssinaturaDigitalControle extends SuperControle {

    private ContratoVO contratoVO;
    private String cpfResponsavel;
    private String tokenContrato;
    private boolean tudoCerto = false;
    private boolean contratoAssinadoComSucesso = false;
    private Boolean contratoJaAssinado;
    private String nomePessoaContrato;
    private EmpresaVO empresaVO;
    private String ipAddress;

    private boolean whats = false;


    public AssinaturaDigitalControle() {
        try {
            contratoAssinadoComSucesso = false;
            tokenContrato = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("token");
            tokenContrato = Criptografia.decrypt(tokenContrato, SuperControle.Crypt_KEY_Contrato, SuperControle.Crypt_ALGORITM);

            //verifica se assinatura vem da msg por whatsapp
            String whatsParam = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("whats");
            whats = "true".equalsIgnoreCase(whatsParam);

            Pattern rgx = Pattern.compile("codigoContrato:([^;]+); chave:(.+)");
            Matcher match = rgx.matcher(tokenContrato);
            if(match.find()) {
                String codigo = match.group(1);
                String chave = match.group(2);

                Connection con = null;
                try {
                    con = new DAO().obterConexaoEspecifica(chave);
                    init(Integer.parseInt(codigo), con);
                    initEmpresa(contratoVO.getEmpresa().getCodigo(), con);
                    initResponsavel(contratoVO.getPessoa(), con);
                    contratoJaAssinado = verificaSeContratoJaFoiAssinado(Integer.parseInt(codigo), con);
                    nomePessoaContrato = Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(contratoVO.getPessoa().getNome());
                } finally {
                    if (con != null) {
                        con.close();
                    }
                }
            }
            if (contratoVO != null && !contratoJaAssinado) {
                tudoCerto = true;
            }
        } catch (Exception e) {
            tudoCerto = false;
            e.printStackTrace();
        }
    }

    public void validarDadosAssinaturaCliente() {
        try {
            String cpf = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("form:cpf");
            cpf = cpf.trim();
            if (UteisValidacao.emptyString(cpf) || !UteisValidacao.isValidCPF(cpf)) {
                throw new ConsistirException("Notifier.error('Informe um CPF válido');");
            }

            String emailRecebido = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("form:email");
            emailRecebido = emailRecebido.trim();
            if (!UteisValidacao.validaEmail(emailRecebido)) {
                throw new ConsistirException("Notifier.error('Informe um email válido');");
            }

            //setar IP
            if (!UteisValidacao.emptyString(ipAddress)) {
                setIp(ipAddress);
            }
            enviar();

            Pattern rgx = Pattern.compile("codigoContrato:([^;]+); chave:(.+)");
            Matcher match = rgx.matcher(tokenContrato);
            if (match.find()) {
                String codigo = match.group(1);
                String chave = match.group(2);

                Connection con = null;
                try {
                    con = new DAO().obterConexaoEspecifica(chave);

                    if (contratoVO != null) {
                        if (validarEmailRecebimento(contratoVO.getPessoa().getEmailVOs(), emailRecebido) && validaCpf(cpf)) {
                            setMsgAlert("");
                            String[] emails = emailRecebido.split(";");
                            atualizarInformacoesAssinaturaContrato(Integer.parseInt(codigo), getIp(), con);
                            enviarConfirmacaoAssinaturaContrato(emails, con);
                            if(whats) {
                                //INSERINDO LOGS DE CONTRATO ASSINADO VIA LINK ENVIADO POR WHATSAPP
                                Log logDAO = new Log(con);
                                LogVO logVO = new LogVO();
                                logVO.setOperacao("INCLUIR ASSINATURA DIGITAL");
                                logVO.setChavePrimaria(contratoVO.getCodigo().toString());
                                logVO.setResponsavelAlteracao("PACTOBR");
                                logVO.setOrigem("LINK VIA WHATSAPP");
                                logVO.setPessoa(contratoVO.getPessoa().getCodigo());
                                logVO.setNomeEntidade("CONTRATO");
                                logVO.setDataAlteracao(new Date());
                                logVO.setNomeCampo("dataassinaturacontrato");
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                                String dataFormatada = sdf.format(new Date());
                                logVO.setValorCampoAlterado(dataFormatada);
                                logVO.setDescricao("Contrato foi assinado via whatsapp");

                                logDAO.incluir(logVO);
                            }
                            contratoAssinadoComSucesso = true;
                            tudoCerto = true;
                        } else {
                            setMsgAlert("nomeOuEmailIncorreto()");
                        }
                    } else {
                        setMsgAlert("contratoOuTokenNaoExistente()");
                    }
                } catch (Exception ex){
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Erro ao tentar validar informações do contrato: " + ex.getMessage());
                } finally {
                    if (con != null) {
                        con.close();
                    }
                }
            }
        } catch (ConsistirException ce) {
            setMsgAlert(ce.getMessage());
        } catch (Exception ce) {
            tudoCerto = false;
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Erro ao tentar assinar contrato eletronicamente: " + ce.getMessage());
            ce.printStackTrace();
            setMsgAlert(ce.getMessage());
        }
    }

    private boolean validarEmailRecebimento(List<EmailVO> emailsPessoa, String emailInformado) {
        if (UteisValidacao.emptyList(emailsPessoa) || Util.isEmptyString(emailInformado)) {
            return false;
        }

        for (EmailVO emailVO : emailsPessoa){
            if (emailVO.getEmail().trim().equalsIgnoreCase(emailInformado.trim())) {
                return true;
            }
        }

        return false;
    }

    private boolean validaCpf(String cpf) {
        boolean retorno = false;
        if(!Util.isEmptyString(contratoVO.getPessoa().getCfp())) {
            retorno = contratoVO.getPessoa().getCfp().replaceAll("[^0-9]", "").equals(cpf.replaceAll("[^0-9]", ""));
        }

        if (!retorno) {
            retorno = (contratoVO.getPessoa().getCpfPai().replaceAll("[^0-9]", "").equals(cpf.replaceAll("[^0-9]", ""))) ||
                    (contratoVO.getPessoa().getCpfMae().replaceAll("[^0-9]", "").equals(cpf.replaceAll("[^0-9]", ""))) ||
                    (cpfResponsavel != null && cpfResponsavel.replaceAll("[^0-9]", "").equals(cpf.replaceAll("[^0-9]", "")));
        }

        return retorno;
    }

    private void enviarConfirmacaoAssinaturaContrato(String[] emails, Connection con) throws Exception {
        try {
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();

            if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
                throw new Exception("Configure o servidor de envio de e-mail!");
            }
            String nomeEmpresa = empresaVO.getNome();
            UteisEmail email = new UteisEmail();
            String textoEmail = montarTextoEmailConfirmacao(nomeEmpresa);
            textoEmail = arranjarImagens(textoEmail, contratoVO, con);
            email.novo(nomeEmpresa + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
            substituiImagensRodape(email);
            email.setRemetente(contratoVO.getResponsavelContrato());
            email.enviarEmailN(emails, textoEmail, nomeEmpresa + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, AssinaturaDigitalControle.class);
            setMsgAlert("erroEnvioEmailConfirmacao()");
        }
    }

    private String montarTextoEmailConfirmacao(String nomeEmpresa) throws Exception {
        try {
            String nomeAluno = contratoVO.getPessoa().getNome();
            String dataNascimento = contratoVO.getPessoa().getDataNasc_Apresentar();
            String cpf = contratoVO.getPessoa().getCfp();
            String email = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("form:email");
            String nomeConsultor = contratoVO.getConsultor().getPessoa().getNome();


            File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailConfirmacaoAssinaturaContratoCliente.txt").toURI());
            StringBuilder arqui = FileUtilities.readContentFile(arq.getAbsolutePath());
            String aux = arqui.toString()
                    .replaceAll("#NOME_DO_ALUNO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                    .replaceAll("#NOME_DA_EMPRESA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeEmpresa))
                    .replaceAll("#NOME_CONSULTOR", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeConsultor))
                    .replaceAll("#DATA_NASCIMENTO", dataNascimento)
                    .replaceAll("#CPF", cpf)
                    .replaceAll("#EMAIL_ASSINATURA", email)
                    .replaceAll("#IP", getIp())
                    .replaceAll("#DATA_ASSINATURA", Uteis.getDataComHora(contratoVO.getDataAssinaturaContrato()))
                    .replaceAll("#ENDERECO_DA_EMPRESA", empresaVO.getEndereco())
                    .replaceAll("#CIDADE_UF_DA_EMPRESA", empresaVO.getCidade_Apresentar())
                    .replaceAll("#TELEFONE_EMPRESA", empresaVO.getPrimeiroTelefoneNaoNulo())
                    .replaceAll("#EMAIL_CONTATO_EMPRESA", empresaVO.getEmail());
            return aux;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, AssinaturaDigitalControle.class);
            throw e;
        }
    }

    private Boolean verificaSeContratoJaFoiAssinado(Integer codigo, Connection con) throws Exception {
        AssinaturaDigital assinaturaDigital = new AssinaturaDigital(con);
        return assinaturaDigital.verificaSeContratoJaFoiAssinado(codigo);
    }

    private void init(Integer codigo, Connection con) throws Exception {
        initContratoVO(codigo, con);
    }

    private void initEmpresa(Integer codigoEmpresa, Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        empresaVO = empresaDAO.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    private void initContratoVO(Integer codigo, Connection con) throws Exception {
        Contrato contratoDAO = new Contrato(con);
        contratoVO = contratoDAO.consultarPorCodigo(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    private void initResponsavel(PessoaVO pessoaVO, Connection con) throws Exception {
        Pessoa pessoaDAO = new Pessoa(con);
        try {
            PessoaCPFTO responsavel = pessoaDAO.obterCpfValidandoIdade(pessoaVO);
            cpfResponsavel = responsavel.getCpfResponsavel();
        } catch (Exception e) {
            Logger.getLogger(AssinaturaDigitalControle.class.getName()).info("Não foi possível carregar o responsável: " + e.getMessage());
        }
    }

    private void atualizarInformacoesAssinaturaContrato(Integer codigo, String ip, Connection con) throws Exception {
        Date dataAssinaturaContrato = new Date(System.currentTimeMillis());
        contratoVO.setDataAssinaturaContrato(dataAssinaturaContrato);

        AssinaturaDigital assinaturaDigital = new AssinaturaDigital(con);
        assinaturaDigital.atualizarIPAssinaturaContrato(codigo, ip, dataAssinaturaContrato);
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public boolean isTudoCerto() {
        return tudoCerto;
    }

    public void setTudoCerto(boolean tudoCerto) {
        this.tudoCerto = tudoCerto;
    }

    public boolean isContratoAssinadoComSucesso() {
        return contratoAssinadoComSucesso;
    }

    public void setContratoAssinadoComSucesso(boolean contratoAssinadoComSucesso) {
        this.contratoAssinadoComSucesso = contratoAssinadoComSucesso;
    }

    public String getNomePessoaContrato() {
        return nomePessoaContrato;
    }

    public void setNomePessoaContrato(String nomePessoaContrato) {
        this.nomePessoaContrato = nomePessoaContrato;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public boolean isWhats() {
        return whats;
    }

    public void setWhats(boolean whats) {
        this.whats = whats;
    }
}
