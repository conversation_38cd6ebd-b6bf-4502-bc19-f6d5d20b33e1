/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.contrato.ValidacaoHistoricoContrato;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TrancamentoContratoControle extends SuperControle {

    private TrancamentoContratoVO trancamentoContratoVO;
    private List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs;
    private List<ContratoVO> listaContratoVOs;
    private List<ProdutoVO> listaProdutoTrancamentoVOs;
    private List<TrancamentoContratoVO> listaTrancamentoVOs;
    private Boolean apresentarBotoes;
    private String visualizacaoObservacao;
    private String nomeArquivoComprovanteOperacao;
    //será temporário até divulgação desse recurso

    public static final String MENSAGEM_SISTEMA_NAO_LOGADO_EM_EMPRESA = "Não foi possível gerar (MovProduto) de Trancamento, pois o SISTEMA NÃO ESTÁ LOGADO EM NENHUMA EMPRESA.";

    public TrancamentoContratoControle() throws Exception {
        obterUsuarioLogado();
    }

    public void inicializarUsuarioLogado() {
        try {
            getTrancamentoContratoVO().getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
            getTrancamentoContratoVO().getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
            getTrancamentoContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception ignored) {
        }
    }

    public void inicializarEmpresaLogado() throws Exception {
        getTrancamentoContratoVO().setEmpresa(getEmpresaLogado().getCodigo());
    }

    public String novo() throws Exception {
        ContadorTempo.limparCronometro();
        ContadorTempo.iniciarContagem();
        notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_TRANCAMENTO);
        try {
            setProcessandoOperacao(false);
            setTrancamentoContratoVO(new TrancamentoContratoVO());
            setListaContratoVOs(new ArrayList<>());
            inicializarUsuarioLogado();
            inicializarEmpresaLogado();
            inicializarListasSelectItemTodosComboBox();
             //validar se o contrato possui um trancamento sem retorno
            ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(getTrancamentoContratoVO().getContratoVO());
            obterNomeCliente();
            setMensagemID("msg_entre_dados");
            setSucesso(false);
            setErro(false);
            setApresentarBotoes(true);
            getTrancamentoContratoVO().setResponsavelAlteracaoDataRetroativa(new UsuarioVO());
            return "trancamento";
        } catch (Exception e) {
            trancamentoContratoVO.setMensagemErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroTrancamento";
        }
    }

    public void gravar() throws Exception {
        setProcessandoOperacao(true);

        if (estahDeslogado()) {
            throw new Exception(MENSAGEM_SISTEMA_NAO_LOGADO_EM_EMPRESA);
        } else {
            trancamentoContratoVO.setEmpresa(getEmpresaLogado().getCodigo());
        }

        if (getFacade().getContrato().existeRenovacaoContrato(trancamentoContratoVO.getContratoVO().getCodigo())) {
            throw new ConsistirException("Trancamento não pode ser feito, pois esse contrato já foi renovado");
        }

        getFacade().getTrancamentoContrato().incluir(trancamentoContratoVO, getUsuarioLogado());
        ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
        clienteControle.getClienteVO().setSituacao("TR");

        montarSucesso("msg_dados_gravados");
        setApresentarBotoes(false);
        notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_TRANCAMENTO_SUCESSO, ContadorTempo.encerraContagem());
        //LOG - INICIO
        try {
            trancamentoContratoVO.setObjetoVOAntesAlteracao(new TrancamentoContratoVO());
            trancamentoContratoVO.setNovoObj(true);
            registrarLogObjetoVO(trancamentoContratoVO, trancamentoContratoVO.getCodigo(), "TRANCAMENTOCONTRATO", trancamentoContratoVO.getContratoVO().getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("TRANCAMENTOCONTRATO", trancamentoContratoVO.getContratoVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO TRANCAMENTO CONTRATO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
    }
    /**
     * Valida a permissão do usuário em alterar a data de trancamento do contrato,
     * caso o usuário escolha uma data retroativa
     */
    public void validarDataTrancamento() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);

                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                trancamentoContratoVO.setResponsavelAlteracaoDataRetroativa(auto.getUsuario());

                montarSucessoGrowl("");
                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                trancamentoContratoVO.setDataTrancamento(null);
                setProcessandoOperacao(false);
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                trancamentoContratoVO.setDataTrancamento(null);
            }
        };

        limparMsg();
        try {
            if (trancamentoContratoVO.getDataTrancamento() == null) {
                throw new ConsistirException("O campo DATA TRANCAMENTO (Trancamento) deve ser informado. ");
            }
            //valida se a data é igual ou maior que o dia de hoje
            if (Calendario.menor(trancamentoContratoVO.getDataTrancamento(), Calendario.hoje())) {
                auto.autorizar("Confirmação de Data de Trancamento Retroativo", "LiberarDataTrancamentoRetroativo_Autorizar",
                        "Você precisa da permissão \"3.16 - Lançamento de data de trancamento retroativo\"",
                        "pnlDtTrancamento", listener);

            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
        }
    }

    /**
     *
     * Efetua o cálculo do trancamento do cliente
     * @return
     */
    public String calculoTrancamentoCliente() {
        try {
            //valida se a data não foi escolhida
            if (trancamentoContratoVO.getDataTrancamento() == null) {
                throw new ConsistirException("O campo DATA TRANCAMENTO (Trancamento) deve ser informado. ");
            }
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            ValidacaoContratoOperacao.validarPeriodoOperacao("Trancamento", trancamentoContratoVO.getDataTrancamento(),trancamentoContratoVO.getContratoVO(), clienteControle.getListaSelectItemContratoOperacao());
            ValidacaoHistoricoContrato.validarTrancamentoRetornoPendente(trancamentoContratoVO);

            //trancamentoContratoVO.setValorTotalSomaProdutoContratos(trancamentoContratoVO.getContratoVO().getSomaProduto());
            trancamentoContratoVO.setNrDiasContrato(getFacade().getZWFacade().obterNrDiasContrato(trancamentoContratoVO.getContratoVO()));
            if(!trancamentoContratoVO.getContratoVO().isVendaCreditoTreino()){
                trancamentoContratoVO.setNrDiasBonus(getFacade().getZWFacade().obterNrDiasOperacoesBonusNoContratoParaDias(trancamentoContratoVO.getContratoVO(), trancamentoContratoVO.getDataTrancamento()));
            }

            //numero dias utilizado ate o momento
            Integer nrDiasUtilizados = getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(trancamentoContratoVO.getContratoVO(),Uteis.obterDataAnterior(trancamentoContratoVO.getDataTrancamento(), 1));
            trancamentoContratoVO.setNrDiasUtilizadosPeloClienteContrato(nrDiasUtilizados);
            trancamentoContratoVO.setValorBaseContrato(trancamentoContratoVO.getContratoVO().getValorBaseCalculo());
            trancamentoContratoVO.setValorDiaContratoValorBase(getFacade().getZWFacade().obterValorDiaContratoValorBase(trancamentoContratoVO.getContratoVO()));
            trancamentoContratoVO.setNrDiasCongelado(trancamentoContratoVO.getContratoVO().obterNrDiasRestantesProFinalDoContrato(trancamentoContratoVO.getNrDiasContrato(), trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato()));


            if(trancamentoContratoVO.getNrDiasBonus() > 0){
                trancamentoContratoVO.setNrDiasCongelado(trancamentoContratoVO.getNrDiasCongelado() + trancamentoContratoVO.getNrDiasBonus());
            } else {
                trancamentoContratoVO.setNrDiasBonus(0); // dias negativos já são computados no calculo dos dias uteis do sistema
            }

            if (trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() >= trancamentoContratoVO.getNrDiasContrato()) {
                trancamentoContratoVO.setValorUtilizadoPeloClienteBase(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getContratoVO().getValorBaseCalculo()));
                trancamentoContratoVO.setValorCongelado(0.0);
            } else if (trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() == 0) {
                trancamentoContratoVO.setValorUtilizadoPeloClienteBase(0.0);
                trancamentoContratoVO.setValorCongelado(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getContratoVO().getValorBaseCalculo()));

            } else {
                trancamentoContratoVO.setValorUtilizadoPeloClienteBase(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getNrDiasUtilizadosPeloClienteContrato() * trancamentoContratoVO.getValorDiaContratoValorBase()));
                trancamentoContratoVO.setValorCongelado(Uteis.arredondarForcando2CasasDecimais(trancamentoContratoVO.getValorBaseContrato() - trancamentoContratoVO.getValorUtilizadoPeloClienteBase()));

            }
            if (trancamentoContratoVO.getNrDiasCongelado() < 0) {
                trancamentoContratoVO.setMensagemErro(true);
                setErro(true);
                setMensagemDetalhada("Não é possível realizar esta operação de trancamento na data informada, pois nesse período seu contrato terminou. ");
            } else {
                trancamentoContratoVO.setMensagemErro(false);
                setErro(false);
                setMensagemDetalhada("");
                setMensagem("");
                setMensagemID("");
            }
            return "trancamentoContratoFinalizado";
        } catch (Exception e) {
            trancamentoContratoVO.setMensagemErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }

    }

    public void montarDadosContratoParaTrancamento() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                getTrancamentoContratoVO().setContratoVO(clienteControle.getContratoVO());
                getListaContratoVOs().add(clienteControle.getContratoVO());
            } else {
                throw new Exception("Não foi possível inicializar os dados do Contrato.");
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void validarDadosTrancamento() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                setProcessandoOperacao(true);

                trancamentoContratoVO.setResponsavelOperacao(auto.getUsuario());
                gravar();

                montarSucessoGrowl("");
                setApresentarBotoes(false);
                setProcessandoOperacao(false);
                setExecutarAoCompletar("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
                setProcessandoOperacao(false);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };

        limparMsg();
        trancamentoContratoVO.setMensagemErro(false);
        setErro(false);

        try {
            if (trancamentoContratoVO.getNrDiasCongelado() < 0) {
                throw new Exception("Não é possível realizar esta operação de trancamento na data informada, pois nesse período seu contrato terminou.");
            }

            if (UteisValidacao.emptyNumber(trancamentoContratoVO.getProdutoTrancamento().getCodigo())) {
                throw new Exception("Selecione o produto trancamento !");
            }

            Integer codigoProduto = trancamentoContratoVO.getProdutoTrancamento().getCodigo();
            ProdutoVO produtoTrancamentoBanco = getFacade().getProduto().consultarPorCodigoProduto(codigoProduto, Uteis.NIVELMONTARDADOS_MINIMOS);

            if (!"TR".equals(produtoTrancamentoBanco.getTipoProduto()) || !"TR".equals(trancamentoContratoVO.getProdutoTrancamento().getTipoProduto())) {
                throw new Exception("O Produto selecionado não está mais do tipo Trancamento, por favor verifique o produto e tente novamente.");
            }

            TrancamentoContratoVO.validarDados(trancamentoContratoVO);

            if (!trancamentoContratoVO.getMensagemManutencao()) {
                auto.autorizar("Confirmação de trancamento de contrato", "Trancamento_Autorizar",
                        "Você precisa da permisso \"3.10 - Lançar trancamento de contratos\"",
                        "form", listener);
            }

            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(trancamentoContratoVO.getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

            int acessos = getFacade().getAcessoCliente().consultarQtdAcessosEntreDatas(
                    cliente, trancamentoContratoVO.getDataTrancamento(),
                    trancamentoContratoVO.getDataFimTrancamento(), false);
            // se cliente teve algum acesso neste periodo
            if (acessos > 0) {
                auto.autorizar("Confirmação de Atestado com Acesso", "AtestadoCarencia_Autorizar",
                        "Aluno Possui " + acessos + " Acessos no Período Informado. " +
                                "Você precisa da permissão \"3.18 - Atestado, Trancamento ou Férias com Frequência - Autorizar\"",
                        "form", listener);
                }

        } catch (Exception e) {
            trancamentoContratoVO.setMensagemErro(true);
            montarErro(e.getMessage());
        }
    }

    public String voltar() {
        trancamentoContratoVO.setMensagemErro(false);
        return "voltar";
    }

    public void consultarResponsavel() {
        try {
            trancamentoContratoVO.setResponsavelOperacao(getFacade().getUsuario().consultarPorChavePrimaria(trancamentoContratoVO.getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getTrancamentoContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarListasSelectItemTodosComboBox() throws Exception {
        try {
            montarDadosContratoParaTrancamento();
            montarDadosListaJustificativaOperacaoVOs();
            montarDadosListaProdutoTrancamentoVOs();
        } catch (Exception e) {
            throw e;
        }
    }

    public void obterNomeCliente() {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                getTrancamentoContratoVO().getContratoVO().getPessoa().setNome(clienteControle.getClienteVO().getPessoa().getNome());
            } else {
                throw new Exception("Não foi possível inicializar o nome do cliente.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs() {
        try {
            montarDadosListaJustificativaOperacaoVOs("TR");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs(String prm) throws Exception, Exception, Exception, Exception {
        try {
            List resultadoConsulta = consultarTipoJustificativaOperacaoPorTipo(prm);
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            while (i.hasNext()) {
                JustificativaOperacaoVO obj = (JustificativaOperacaoVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
            }
            setListaJustificativaOperacaoVOs(objs);
        } catch (Exception e) {
            throw e;
        }

    }

    public List consultarTipoJustificativaOperacaoPorTipo(String prm) throws Exception {
        List lista = new ArrayList();
        try {
            lista = getFacade().getJustificativaOperacao().consultarPorTipoOperacao(prm, getEmpresaLogado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDadosListaProdutoTrancamentoVOs() {
        try {
            montarDadosListaProtudoTrancamentoVOs("TR");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosListaProtudoTrancamentoVOs(String prm) throws Exception {
        try {
            List resultadoConsulta = consultarTipoProdutoTrancamento(prm);
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            while (i.hasNext()) {
                ProdutoVO obj = (ProdutoVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
            }
            setListaProdutoTrancamentoVOs(objs);
        } catch (Exception e) {
            throw e;
        }
    }

    public List consultarTipoProdutoTrancamento(String prm) throws Exception {
        List lista = new ArrayList();
        try {
            lista = getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo("", prm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    public void obterValorProduto() {
        try {
            if (getTrancamentoContratoVO().getProdutoTrancamento().getCodigo().intValue() != 0) {
                ProdutoVO obj = getFacade().getProduto().consultarPorChavePrimaria(getTrancamentoContratoVO().getProdutoTrancamento().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getTrancamentoContratoVO().setValorTrancamento(obj.getValorFinal());
                getTrancamentoContratoVO().setProdutoTrancamento(obj);
                getTrancamentoContratoVO().setApresentarPeriodoTrancamento(true);
                getTrancamentoContratoVO().setDataFimTrancamento(Uteis.obterDataFutura2(getTrancamentoContratoVO().getDataTrancamento(), (obj.getNrDiasVigencia() - 1)));
                getTrancamentoContratoVO().setDataRetorno(Uteis.obterDataFutura2(getTrancamentoContratoVO().getDataTrancamento(), (obj.getNrDiasVigencia())));
            } else {
                getTrancamentoContratoVO().setValorTrancamento(new Double(0));
                getTrancamentoContratoVO().setApresentarPeriodoTrancamento(false);
                getTrancamentoContratoVO().setDataFimTrancamento(negocio.comuns.utilitarias.Calendario.hoje());
                getTrancamentoContratoVO().setDataRetorno(negocio.comuns.utilitarias.Calendario.hoje());
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
            setErro(false);
        } catch (Exception e) {
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void limparMensagem() {
        setMensagemDetalhada("");
        setMensagem("");
        setMensagemID("");
        setErro(false);
    }

    public List<JustificativaOperacaoVO> getListaJustificativaOperacaoVOs() {
        return listaJustificativaOperacaoVOs;
    }

    public void setListaJustificativaOperacaoVOs(List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs) {
        this.listaJustificativaOperacaoVOs = listaJustificativaOperacaoVOs;
    }

    public List<ProdutoVO> getListaProdutoTrancamentoVOs() {
        return listaProdutoTrancamentoVOs;
    }

    public void setListaProdutoTrancamentoVOs(List<ProdutoVO> listaProdutoTrancamentoVOs) {
        this.listaProdutoTrancamentoVOs = listaProdutoTrancamentoVOs;
    }

    public TrancamentoContratoVO getTrancamentoContratoVO() {
        return trancamentoContratoVO;
    }

    public void setTrancamentoContratoVO(TrancamentoContratoVO trancamentoContratoVO) {
        this.trancamentoContratoVO = trancamentoContratoVO;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public List<TrancamentoContratoVO> getListaTrancamentoVOs() {
        return listaTrancamentoVOs;
    }

    public void setListaTrancamentoVOs(List<TrancamentoContratoVO> listaTrancamentoVOs) {
        this.listaTrancamentoVOs = listaTrancamentoVOs;
    }

    public String getVisualizacaoObservacao() {
        if (visualizacaoObservacao == null) {
            visualizacaoObservacao = "";
        }
        return visualizacaoObservacao;
    }

    public void setVisualizacaoObservacao(String visualizacaoObservacao) {
        this.visualizacaoObservacao = visualizacaoObservacao;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        trancamentoContratoVO = null;
        listaJustificativaOperacaoVOs = new ArrayList<JustificativaOperacaoVO>();
        listaContratoVOs = new ArrayList<ContratoVO>();
        listaProdutoTrancamentoVOs = new ArrayList<ProdutoVO>();
        listaTrancamentoVOs = new ArrayList<TrancamentoContratoVO>();
        apresentarBotoes = null;
        visualizacaoObservacao = null;

    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao= "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }

    public void imprimirComprovanteOperacao() {
        try {
            if (getTrancamentoContratoVO().getContratoOperacaoVO().getCodigo() != 0) {
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getTrancamentoContratoVO().getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteOperacao(getTrancamentoContratoVO().getContratoOperacaoVO(), empresaVO));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante da operação.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void autorizarVencimentoParcelas() throws Exception {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                trancamentoContratoVO.getResponsavelAlteracaoDataVencimentoParcelas().setCodigo(auto.getUsuario().getCodigo());
                trancamentoContratoVO.getResponsavelAlteracaoDataVencimentoParcelas().setNome(auto.getUsuario().getNome());
                montarSucessoGrowl("");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                trancamentoContratoVO.setAlterarVencimentoparcelas(false);
                trancamentoContratoVO.setResponsavelAlteracaoDataVencimentoParcelas(new UsuarioVO());
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                trancamentoContratoVO.setAlterarVencimentoparcelas(false);
            }
        };

        limparMsg();
        if (trancamentoContratoVO.isAlterarVencimentoparcelas()) {
            auto.autorizar("Confirmação de Alteração do Vencimento das Parcelas", "alterarVencimentoParcelasTrancamento",
                    "Você precisa da permissão \"3.38 - Alterar vencimento das parcelas no trancamento\"",
                    "form", listener);
            setMensagemDetalhada("", "");
        } else {
            trancamentoContratoVO.setResponsavelAlteracaoDataVencimentoParcelas(new UsuarioVO());
        }
    }

}
