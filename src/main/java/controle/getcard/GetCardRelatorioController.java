package controle.getcard;


import com.sun.xml.ws.util.StringUtils;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.CancelamentoGetCardTO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import org.json.JSONObject;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.List;

public class GetCardRelatorioController extends SuperControle {

    private CancelamentoGetCardTO cancelTOSelecionado;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();
    private boolean exibirModalParametros = false;
    private boolean exibirPix = false;
    private String dadosConcatenados;

    public CancelamentoGetCardTO getCancelTOSelecionado() {
        return cancelTOSelecionado;
    }

    public void setCancelTOSelecionado(CancelamentoGetCardTO cancelTOSelecionado) {
        this.cancelTOSelecionado = cancelTOSelecionado;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public boolean isExibirModalParametros() {
        return exibirModalParametros;
    }

    public void setExibirModalParametros(boolean exibirModalParametros) {
        this.exibirModalParametros = exibirModalParametros;
    }
    public void fecharPanelDadosParametros() {
        this.setExibirModalParametros(false);
        this.setExibirPix(false);
    }

    public boolean isExibirPix() {
        return exibirPix;
    }

    public void setExibirPix(boolean exibirPix) {
        this.exibirPix = exibirPix;
    }

    public void exibirParams(ActionEvent evt) {
        String params = (String) evt.getComponent().getAttributes().get("params");
        CancelamentoGetCardTO cancelGetCard = (CancelamentoGetCardTO) evt.getComponent().getAttributes().get("cancelGetCard");
        cancelTOSelecionado = null;
        listaParametrosSelecionado = null;
        if (params != null && cancelGetCard != null) {
            cancelTOSelecionado = cancelGetCard;
            setExibirModalParametros(true);
            setMensagemDetalhada("");
            setMensagem("");
            if (params.equals("envio")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(cancelTOSelecionado.getParamsenvio());
                } catch (Exception e) {
                    setMensagem("Erro ao obter detalhes do envio!");
                    setMensagemDetalhada(e.getMessage());
                }
            } else if (params.equals("resposta")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(cancelTOSelecionado.getParamsrespcancel());
                }catch (Exception e) {
                    setMensagem("Erro ao obter resposta da administradora, necessário reenviar esta transação!");
                    setMensagemDetalhada(e.getMessage());
                }
            }
        }
    }

    public void tratarRecibo(ActionEvent evt) {
        limparMsg();
        setMsgAlert("");
        String params = (String) evt.getComponent().getAttributes().get("params");
        CancelamentoGetCardTO cancelGetCard = (CancelamentoGetCardTO) evt.getComponent().getAttributes().get("cancelGetCard");

        if (params != null && cancelGetCard != null) {
            dadosConcatenados = new JSONObject(cancelGetCard.getParamsrespcancel()).getJSONObject("response").getString("CupomCliente");
            if (params.equals("enviarEmail")) {
                try {
                    UteisEmail uteisEmail = new UteisEmail();
                    PessoaVO pessoa = getFacade().getPessoa().consultarPorChavePrimaria(cancelGetCard.getPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    String nomeEmpresa = StringUtils.capitalize(getEmpresaLogado().getNome().toLowerCase());
                    uteisEmail.novo(("Comp. Cancelamento GetCard - " + pessoa.getNome() + " - " + nomeEmpresa).toUpperCase(), getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS));
                    uteisEmail.setRemetente(getUsuarioLogado());

                    List<EmailVO> emails = getFacade().getEmail().consultarEmails(cancelGetCard.getPessoa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (emails != null && !emails.isEmpty()) {
                        emails.forEach(email -> {
                            try {
                                uteisEmail.enviarEmail(email.getEmail(), pessoa.getNome(), getHtmlEmail(), nomeEmpresa.toUpperCase());
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
                        montarSucessoGrowl("E-mail Enviado com Sucesso!");
                    }
                } catch (Exception ex) {
                    montarErro("Erro ao enviar email!");
                }
            }
        }
    }

    public String getHtmlComprovante() {
        return dadosConcatenados;
    }

    public String getHtmlEmail() {
        return "<html>\n" +
                "<head>\n" +
                "    <title>Comprovante de Cancelamento GetCard</title>\n" +
                "    <style>\n" +
                "\n" +
                "        body {\n" +
                "            font-family: Arial, sans-serif;\n" +
                "        }\n" +
                "\n" +
                "        .comprovante {\n" +
                "            border: 1px solid #ccc;\n" +
                "            padding: 20px;\n" +
                "            width: 400px;\n" +
                "            margin: auto;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "<div class='comprovante'><h2 style=\"text-align: center\">Comprovante de Cancelamento GetCard</h2>\n" +
                "    <pre style=\"margin-left: 65px\">"+dadosConcatenados+"</pre>\n" +
                "</div>\n" +
                "</body>\n" +
                "</html>";
    }
}
