package controle.arquitetura;

import negocio.comuns.utilitarias.UrlRedirectNZWEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class FuncionalidadeSistemaEnumAux implements Cloneable {

    private FuncionalidadeSistemaEnum funcionalidadeSistemaEnum;
    private String descricao;
    private ModuloAberto modulo;
    private String url;
    private String expressaoRenderizar;
    private String[] palavrasChaves;
    private String wiki;
    private String descricaoTruncada;
    private boolean existeWiki;
    private String[] subFuncionalidadesNomes = new String[]{};
    private String tipo;
    private List<FuncionalidadeSistemaEnum> subFuncionalidades = new ArrayList<>();
    private String reRenderElement = "none";
    private boolean renderizar = true;
    private String descricaoMenulateral = "";
    private boolean novaVersao = true;

    @Override
    public FuncionalidadeSistemaEnumAux clone(){
        try {
            return (FuncionalidadeSistemaEnumAux) super.clone();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static FuncionalidadeSistemaEnum obterPorNome(final String nome) {
        for (FuncionalidadeSistemaEnum i : FuncionalidadeSistemaEnum.values()) {
            if (i.name().equals(nome)) {
                return i;
            }
        }
        return null;
    }

    public void prepararSubFuncionalidade() {
        this.subFuncionalidades = new ArrayList<FuncionalidadeSistemaEnum>();
        for (String nome : this.subFuncionalidadesNomes) {
            this.subFuncionalidades.add(obterPorNome(nome));
        }
    }

    public String getName() {
        return getFuncionalidadeSistemaEnum().name();
    }

    public FuncionalidadeSistemaEnum getFuncionalidadeSistemaEnum() {
        return funcionalidadeSistemaEnum;
    }

    public void setFuncionalidadeSistemaEnum(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        this.funcionalidadeSistemaEnum = funcionalidadeSistemaEnum;
    }

    public String getDescricao() {
        if (descricao == null || descricao.trim().length() == 0) {
            if (getFuncionalidadeSistemaEnum() != null) {
                descricao = getFuncionalidadeSistemaEnum().getDescricao();
            } else {
                descricao = "FuncionalidadeSistemaEnum não encontrada!";
            }
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public ModuloAberto getModulo() {
        if (modulo == null) {
            if (getFuncionalidadeSistemaEnum() == null) {
                Uteis.logarDebug(String.format("A funcionalidade %s está sem FuncionalidadeSistemaEnum", getDescricao()));
            } else {
                modulo = getFuncionalidadeSistemaEnum().getModulo();
            }
        }
        return modulo;
    }

    public void setModulo(ModuloAberto modulo) {
        this.modulo = modulo;
    }

    public String getUrl() {
        url = (url == null || url.trim().length() == 0) && getFuncionalidadeSistemaEnum() != null ? getFuncionalidadeSistemaEnum().getUrl() : url;
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getExpressaoRenderizar() {
        return expressaoRenderizar;
    }

    public void setExpressaoRenderizar(String expressaoRenderizar) {
        this.expressaoRenderizar = expressaoRenderizar;
    }

    public String[] getPalavrasChaves() {
        palavrasChaves = palavrasChaves == null || palavrasChaves.length == 0 ? getFuncionalidadeSistemaEnum().getPalavrasChaves() : palavrasChaves;
        return palavrasChaves;
    }

    public void setPalavrasChaves(String[] palavrasChaves) {
        this.palavrasChaves = palavrasChaves;
    }

    public String getWiki() {
        wiki = wiki == null || wiki.trim().length() == 0 ? getFuncionalidadeSistemaEnum().getWiki() : wiki;
        return wiki;
    }

    public void setWiki(String wiki) {
        this.wiki = wiki;
    }

    public String getDescricaoTruncada() {
        descricaoTruncada = descricaoTruncada == null || descricaoTruncada.trim().length() == 0 ? getFuncionalidadeSistemaEnum().getDescricaoTruncada() : descricaoTruncada;
        return descricaoTruncada;
    }

    public void setDescricaoTruncada(String descricaoTruncada) {
        this.descricaoTruncada = descricaoTruncada;
    }

    public boolean isExisteWiki() {
        return existeWiki;
    }

    public void setExisteWiki(boolean existeWiki) {
        this.existeWiki = existeWiki;
    }

    public String[] getSubFuncionalidadesNomes() {
        subFuncionalidadesNomes = subFuncionalidadesNomes == null || subFuncionalidadesNomes.length == 0 ? getFuncionalidadeSistemaEnum().getSubFuncionalidadesNomes() : subFuncionalidadesNomes;
        return subFuncionalidadesNomes;
    }

    public void setSubFuncionalidadesNomes(String[] subFuncionalidadesNomes) {
        this.subFuncionalidadesNomes = subFuncionalidadesNomes;
    }

    public String getTipo() {
        tipo = tipo == null || tipo.trim().length() == 0 ? getFuncionalidadeSistemaEnum().getTipo() : tipo;
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public List<FuncionalidadeSistemaEnum> getSubFuncionalidades() {
        return subFuncionalidades;
    }

    public void setSubFuncionalidades(List<FuncionalidadeSistemaEnum> subFuncionalidades) {
        this.subFuncionalidades = subFuncionalidades;
    }

    public String getReRenderElement() {
        reRenderElement = reRenderElement == null || reRenderElement.trim().length() == 0 ? getFuncionalidadeSistemaEnum().getReRenderElement() : reRenderElement;
        return reRenderElement;
    }

    public void setReRenderElement(String reRenderElement) {
        this.reRenderElement = reRenderElement;
    }

    public boolean isRenderizar() {
        return renderizar;
    }

    public void setRenderizar(boolean renderizar) {
        this.renderizar = renderizar;
    }

    public String getDescricaoMenulateral() {
        String menuFun = this.funcionalidadeSistemaEnum.getDescricaoMenulateral();
        if (UteisValidacao.emptyString(menuFun)) {
            menuFun = this.funcionalidadeSistemaEnum.getDescricao();
        }
        if (!UteisValidacao.emptyString(menuFun) &&
                this.isNovaVersao() &&
                !UteisValidacao.emptyString(UrlRedirectNZWEnum.getUriByFuncSisEnum(this.getFuncionalidadeSistemaEnum()))) {
            menuFun += " . ";
        }
        return menuFun;
    }

    public String getDescricaoTruncadaMenuLateral() {
        getFuncionalidadeSistemaEnum().getDescricaoTruncadaMenuLateral();
        return descricaoMenulateral;
    }

    public void setDescricaoMenulateral(String descricaoMenulateral) {
        this.descricaoMenulateral = descricaoMenulateral;
    }

    @Override
    public String toString() {
        return "FuncionalidadeSistemaEnumAux{" +
                "funcionalidadeSistemaEnum=" + funcionalidadeSistemaEnum +
                ", descricao='" + descricao + '\'' +
                ", modulo=" + modulo +
                ", url='" + url + '\'' +
                ", expressaoRenderizar='" + expressaoRenderizar + '\'' +
                ", palavrasChaves=" + Arrays.toString(palavrasChaves) +
                ", wiki='" + wiki + '\'' +
                ", descricaoTruncada='" + descricaoTruncada + '\'' +
                ", existeWiki=" + existeWiki +
                ", subFuncionalidadesNomes=" + Arrays.toString(subFuncionalidadesNomes) +
                ", tipo='" + tipo + '\'' +
                ", subFuncionalidades=" + subFuncionalidades +
                ", reRenderElement='" + reRenderElement + '\'' +
                ", renderizar=" + renderizar +
                ", descricaoMenulateral='" + descricaoMenulateral + '\'' +
                '}';
    }

    public boolean isNovaVersao() {
        return novaVersao;
    }

    public void setNovaVersao(boolean novaVersao) {
        this.novaVersao = novaVersao;
    }
}
