/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.threads;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoCaucaoCredito;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.ce.comuns.to.NegEvCaucaoCreditoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoAmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoProdutoLocacaoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoServicoTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao.NegEvCaucaoCredito;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao.NegEvPerfilEventoAmbiente;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao.NegEvPerfilEventoProdutoLocacao;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao.NegEvPerfilEventoServico;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.basico.ClienteTitularDependenteVO;
import negocio.comuns.basico.GenericoAtributosVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoCamposPesquisarRateio;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.MovProduto;
import org.apache.commons.lang.StringUtils;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.jdbc.financeiro.CentroCustosDRE;
import relatorio.negocio.jdbc.financeiro.ContratoModalidadePercentual;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.MesProcessar;
import relatorio.negocio.jdbc.financeiro.ProdutoRatear;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 *
 * <AUTHOR>
 */
public class ThreadDemonstrativoFinanceiro extends Thread implements Serializable{

    private boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao;
    private boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao;
    private MesProcessar mesProcessar;
    private int empresa;
    private TipoVisualizacaoRelatorioDF tipoVisualizacao;
    private TipoRelatorioDF tipoRelatorio;
    private List<DemonstrativoFinanceiro> listaDemonstrativo;
    private RateioIntegracaoTO rateioNaoDefinido;
    private transient Connection con;
    private boolean terminouExecucao = false;
    private String msgErro = "";
    private List<RateioIntegracaoTO> listaRateiosIntegracao = null;
    private TipoFonteDadosDF tipoFonteDadosDF = null;
    private List<Integer> listaFiltroCentroCusto = new ArrayList<Integer>();
    private boolean montarNivelSuperior = true;
    private Map<String, TipoEquivalenciaDRE> equivalencias;
    private boolean dre = false;
    private Map<String, String> novosCodigos;
    private ArrayList<MovProdutoModalidadeVO> cacheMovProdutoModalidade = new ArrayList();
    public boolean usarCentralEventos = false;
    private Map<String, Date> mapaInicios = new HashMap<String, Date>();
    private boolean agruparValorProdutoMMasModalidades;
    private String filtroContas;
    private boolean fluxoCaixa = false;
    private boolean apresentarDevolucoesRel = true;


    private Map<String, Map<String, Double>> mapaConsistencia = new HashMap<String, Map<String, Double>>();
    private Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaPlanoContas = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);
    private Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaCentroCusto = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);

    void adicionarMapa(String nome, Double valor, String tipo){
        Map<String, Double> get = mapaConsistencia.get(nome);
        if(get == null){
            get = new HashMap<String, Double>();
            mapaConsistencia.put(nome, get);
        }
        Double valorGet = get.get(tipo);
        if(valorGet == null){
            valorGet = 0.0;
        }
            get.put(tipo, valor+valorGet);
    }
    void validarConsistencia(){
        Set<String> keySet = mapaConsistencia.keySet();
        for(String key : keySet){
            Map<String, Double> get = mapaConsistencia.get(key);
            if((get.get("consulta") == null || get.get("rateio") == null)
                    || Uteis.arredondarForcando2CasasDecimais(get.get("consulta").doubleValue()) !=
                    Uteis.arredondarForcando2CasasDecimais(get.get("rateio").doubleValue())){
                System.out.println(key+" - consulta: "+get.get("consulta")
                        +" - rateio: "+get.get("rateio"));
            }
        }
    }
    
    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }

    public boolean isTerminouExecucao() {
        return terminouExecucao;
    }

    public void setTerminouExecucao(boolean terminouExecucao) {
        this.terminouExecucao = terminouExecucao;
    }

    public ThreadDemonstrativoFinanceiro() {
    }

    public ThreadDemonstrativoFinanceiro(Connection con,
            List<DemonstrativoFinanceiro> listaDemonstrativo,
            MesProcessar mesProcessar,
            TipoVisualizacaoRelatorioDF tipoVisualizacao,
            String rateioNaoDefinido,
            boolean usarCE) {
        this.con = con;
        this.listaDemonstrativo = listaDemonstrativo;
        this.mesProcessar = mesProcessar;
        this.tipoVisualizacao = tipoVisualizacao;
        this.rateioNaoDefinido = new RateioIntegracaoTO();
        this.rateioNaoDefinido.setCodigoPlano(rateioNaoDefinido);
        this.usarCentralEventos = usarCE;

    }


	public ThreadDemonstrativoFinanceiro(Connection con,
                                         List<DemonstrativoFinanceiro> listaDemonstrativo,
                                         TipoRelatorioDF tipoRelatorio,
                                         TipoVisualizacaoRelatorioDF tipoVisualizacao,
                                         MesProcessar mesProcessar,
                                         String rateioNaoDefinido,
                                         int empresa,
                                         List<Integer> listaFiltroCentroCusto,
                                         TipoFonteDadosDF tipoFonteDados,
                                         boolean dre,
                                         Map<String, String> novosCodigos,
                                         boolean usarCE,
                                         boolean agruparValorProdutoMMasModalidades,
                                         String filtroContas,
                                         boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao,
                                         boolean fluxoCaixa,
                                         boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao) throws Exception {
        this.con = con;
        this.tipoRelatorio = tipoRelatorio;
        this.tipoVisualizacao = tipoVisualizacao;
        this.mesProcessar = mesProcessar;
        this.empresa = empresa;
        this.listaDemonstrativo = listaDemonstrativo;
        this.rateioNaoDefinido = new RateioIntegracaoTO();
        this.rateioNaoDefinido.setCodigoCentro(rateioNaoDefinido);
        this.rateioNaoDefinido.setCodigoPlano(rateioNaoDefinido);
        this.terminouExecucao = false;
        this.tipoFonteDadosDF = tipoFonteDados;
        this.listaFiltroCentroCusto = listaFiltroCentroCusto;
        this.dre = dre;
        this.filtroContas = filtroContas;
        this.novosCodigos = novosCodigos;
        this.usarCentralEventos = usarCE;
        this.agruparValorProdutoMMasModalidades = agruparValorProdutoMMasModalidades;
        this.incluirParcelasRecorrenciaEmRelatorioReceitaProvisao = incluirParcelasRecorrenciaEmRelatorioReceitaProvisao;
        this.fluxoCaixa = fluxoCaixa;
        this.incluirParcelasEmAbertoEmRelatorioReceitaProvisao = incluirParcelasEmAbertoEmRelatorioReceitaProvisao;

    }

    @Override
    public void run() {
        try {

            executarProcessamentoRelatorio();

        } catch (OutOfMemoryError | Exception e) {
            e.printStackTrace();
            this.msgErro  = e.getMessage();
            Uteis.logar(null, msgErro);
        } finally{
            this.terminouExecucao = true;
        }
    }

    public void executarProcessoSemThread(List<MesProcessar> listaMesProcessar) {
        try {
            for (MesProcessar mp : listaMesProcessar) {
                switch (tipoRelatorio) {
                    case COMPETENCIA:
                        processarRelatorioPorCompetencia(mp, empresa);
                        break;
                    case COMPETENCIA_QUITADA:
                        processarRelatorioPorCompetencia(mp, empresa, TipoRelatorioDF.COMPETENCIA_QUITADA);
                        break;
                    case COMPETENCIA_NAO_QUITADA:
                        processarRelatorioPorCompetencia(mp, empresa, TipoRelatorioDF.COMPETENCIA_NAO_QUITADA);
                        break;
                    case RECEITA:
                        processarRelatorioPorReceita(mp, empresa, TipoRelatorioDF.RECEITA);
                        break;
                    case FATURAMENTO_DE_CAIXA:
                        processarRelatorioPorFaturamentoCaixa(mp, empresa);
                        break;
                    case FATURAMENTO:
                        processarRelatorioPorFaturamento(mp, empresa);
                        break;
                    case RECEITAPROVISAO:
                        processarRelatorioPorReceita(mp, empresa, TipoRelatorioDF.RECEITAPROVISAO);
                        break;
                }
            }
        } catch (Exception e) {
            System.out.println("Erro Execução Thread mês: " + this.mesProcessar.getMesAno());
            System.out.println("Erro: " + e.getMessage());
        }
        this.terminouExecucao = true;

    }

    private void executarProcessamentoRelatorio() throws Exception {
        equivalencias = obterEquivalencias();
        switch (tipoRelatorio) {
            case COMPETENCIA:
                processarRelatorioPorCompetencia(this.mesProcessar, this.empresa);
                break;
            case COMPETENCIA_QUITADA:
                processarRelatorioPorCompetencia(this.mesProcessar, this.empresa, TipoRelatorioDF.COMPETENCIA_QUITADA);
                break;
            case COMPETENCIA_NAO_QUITADA:
                processarRelatorioPorCompetencia(this.mesProcessar, this.empresa, TipoRelatorioDF.COMPETENCIA_NAO_QUITADA);
                break;
            case RECEITA:
                processarRelatorioPorReceita(this.mesProcessar, this.empresa,TipoRelatorioDF.RECEITA);
                break;
            case FATURAMENTO_DE_CAIXA:
                processarRelatorioPorFaturamentoCaixa(this.mesProcessar, this.empresa);
                break;
            case FATURAMENTO:
                processarRelatorioPorFaturamento(this.mesProcessar, this.empresa);
                break;
            case RECEITAPROVISAO:
                processarRelatorioPorReceita(this.mesProcessar, this.empresa,TipoRelatorioDF.RECEITAPROVISAO);
                break;
        }
    }

    private void processarRelatorioPorReceita(MesProcessar mesProcessar, int empresa, TipoRelatorioDF tipoRelatorioDF) throws Exception {

        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.ZILLYON_WEB)  || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) {
            // Realizar o rateio das vendas realizadas no ZillyonWeb.
            processarDadosPorReceitaZillyonWeb(mesProcessar, empresa);
        }
        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.FINANCEIRO)) {
            // Realizar o rateio dos lançamentos realizados no módulo Financeiro
            realizarRateioLancamentosDoFinanceiro(mesProcessar, empresa, tipoRelatorioDF);
        }
        if (((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.CENTRAL_EVENTOS)  || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) && usarCentralEventos) {
            // Realizar o rateio dos lançamentos realizados no módulo central de eventos
        	processarDadosPorReceitaCentralEventos(mesProcessar, empresa);
        }
        
        
    }

    private void processarRelatorioPorCompetencia(MesProcessar mesProcessar, int empresa, TipoRelatorioDF tipoRelatorioDF) throws Exception {
        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.ZILLYON_WEB) || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) {
            // Realizar o rateio dos registros da tabela "movProduto"
            processarDadosPorCompetenciaZillyonWeb(mesProcessar, empresa, tipoRelatorioDF);
        }
        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.FINANCEIRO)) {
            // Realizar o rateio dos lançamentos realizados no módulo Financeiro
            realizarRateioLancamentosDoFinanceiro(mesProcessar, empresa, tipoRelatorioDF);
        }
        if (((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.CENTRAL_EVENTOS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) && usarCentralEventos) {
            // Realizar o rateio dos lançamentos realizados no módulo central de eventos
            processarDadosPorCompetenciaCentralEventos(mesProcessar, empresa);
        }
    }

    private void processarRelatorioPorCompetencia(MesProcessar mesProcessar, int empresa) throws Exception {
        processarRelatorioPorCompetencia(mesProcessar, empresa, TipoRelatorioDF.COMPETENCIA);
    }

    private void processarRelatorioPorFaturamentoCaixa(MesProcessar mesProcessar, int empresa) throws Exception {

        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.ZILLYON_WEB) || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) {
            // Realizar o rateio das vendas realizadas no ZillyonWeb.
            processarDadosPorFaturamentoCaixaZillyonWeb(mesProcessar, empresa);
        }
        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.FINANCEIRO)) {
            // Realizar o rateio dos lançamentos realizados no módulo Financeiro
            realizarRateioLancamentosDoFinanceiro(mesProcessar, empresa, TipoRelatorioDF.FATURAMENTO_DE_CAIXA);
        }
        
        if (((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.CENTRAL_EVENTOS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) && usarCentralEventos) {
            // Realizar o rateio dos lançamentos realizados no módulo central de eventos
        	processarDadosPorFaturamentoCaixaCentralEventos(mesProcessar, empresa);
        }
        
    }

    private void processarRelatorioPorFaturamento(MesProcessar mesProcessar, int empresa) throws Exception {

        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.ZILLYON_WEB) || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) {
            // Realizar o rateio das vendas realizadas no ZillyonWeb.
            processarDadosPorFaturamentoZillyonWeb(mesProcessar, empresa);
        }
        if ((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.FINANCEIRO)) {
            // Realizar o rateio dos lançamentos realizados no módulo Financeiro
            realizarRateioLancamentosDoFinanceiro(mesProcessar, empresa, TipoRelatorioDF.FATURAMENTO);
        }
        if (((this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.CENTRAL_EVENTOS) || (this.tipoFonteDadosDF == TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO)) && usarCentralEventos) {
            // Realizar o rateio dos lançamentos realizados no módulo central de eventos
        	processarDadosPorFaturamentoCentralEventos(mesProcessar, empresa);
        }
    }

    private void processarDadosPorReceitaZillyonWeb(MesProcessar mesProcessar, int empresa) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        List<LancamentoDF> listaLancamentosDF = null;

        if(incluirParcelasRecorrenciaEmRelatorioReceitaProvisao ||
                incluirParcelasEmAbertoEmRelatorioReceitaProvisao){
            listaLancamentosDF = this.consultarParcelasRecorrenciaEmAbertoPorDataCompensacao(mesProcessar, empresa);
            dividirValorDoPagamentoParaCadaProduto(listaLancamentosDF);
            realizarRateioZillyonWeb(listaLancamentosDF, mesProcessar);
        }

        logarMetodo(mesProcessar, true, "consultarPagtosDiferenteDeChequeECartaoCredito");
//         Realizar o rateio dos pagamentos cuja forma de pagamento seja diferente de cheque e cartão de crédito.
        listaLancamentosDF = this.consultarPagtosDiferenteDeChequeECartaoCredito(mesProcessar, empresa);
        dividirValorDoPagamentoParaCadaProduto(listaLancamentosDF);
        realizarRateioZillyonWeb(listaLancamentosDF, mesProcessar);
        logarMetodo(mesProcessar, false, "consultarPagtosDiferenteDeChequeECartaoCredito");
        logarMetodo(mesProcessar, true, "consultarPagtosEmCheque");
        // Realizar o rateio dos pagamentos relalizados em cheque.
        listaLancamentosDF = this.consultarPagtosEmCheque(mesProcessar, empresa);
        dividirValorDoPagamentoParaCadaProduto(listaLancamentosDF);
        realizarRateioZillyonWeb(listaLancamentosDF, mesProcessar);
        logarMetodo(mesProcessar, false, "consultarPagtosEmCheque");
        logarMetodo(mesProcessar, true, "consultarPagtosEmCartaoCredito");
        // Realizar o rateio dos pagamentos relalizados em Cartão de Crédito.
        listaLancamentosDF = this.consultarPagtosEmCartaoCredito(mesProcessar, empresa);
        dividirValorDoPagamentoParaCadaProduto(listaLancamentosDF);
        realizarRateioZillyonWeb(listaLancamentosDF, mesProcessar);
        logarMetodo(mesProcessar, false, "consultarPagtosEmCartaoCredito");

//        validarConsistencia();
    }

    private List<LancamentoDF> consultarParcelasRecorrenciaEmAbertoPorDataCompensacao(MesProcessar mesProcessar, int empresa) throws SQLException {
        List<LancamentoDF> lancamentos = new ArrayList<LancamentoDF>();

        String dataInicial = Uteis.getDataFormatoBD(Uteis.getDateTime(mesProcessar.getDataIni().getTime(), 0, 0, 0));
        String dataFinal = Uteis.getDataFormatoBD(Uteis.getDateTime(mesProcessar.getDataFim().getTime(), 23, 59, 59));

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * \n");
        sql.append("FROM (SELECT CASE \n");
        sql.append("               WHEN q1.diasCompensacaoCobranca IS NULL THEN q1.datavencimento \n");
        sql.append("               ELSE q1.datavencimento + INTERVAL '1 day ' * q1.diasCompensacaoCobranca \n");
        sql.append("             END as dataCompensacao, \n");
        sql.append("               q1.* \n");
        sql.append("               FROM (SELECT movparcela.codigo, \n");
        sql.append("                            movparcela.descricao, \n");
        sql.append("                            movparcela.contrato, \n");
        sql.append("                            movparcela.datavencimento, \n");
        sql.append("                            movparcela.empresa, \n");
        sql.append("                            movparcela.pessoa, \n");
        sql.append("                            movparcela.valorparcela, \n");
        sql.append("                            cliente.codigo as cliente, \n");
        sql.append("                            cliente.matricula, \n");
        sql.append("                            pessoa.nome    as nomeCliente, \n");
        sql.append("                            (SELECT CASE when autorizacaocobrancacliente.tipoautorizacao = 1 then 30 when autorizacaocobrancacliente.tipoautorizacao = 2 then 2 else conveniocobranca.diasparacompensacao end as diasparacompensacao \n");
        sql.append("                             FROM autorizacaocobrancacliente \n");
        sql.append("                                    INNER JOIN conveniocobranca \n");
        sql.append("                                      ON conveniocobranca.codigo = autorizacaocobrancacliente.conveniocobranca \n");
        sql.append("                             WHERE autorizacaocobrancacliente.cliente = cliente.codigo \n");
        sql.append("                               AND autorizacaocobrancacliente.tipoautorizacao IN (1, 2) \n");
        sql.append("                               AND autorizacaocobrancacliente.ativa IS TRUE \n");
        sql.append("                             ORDER BY autorizacaocobrancacliente.codigo DESC \n");
        sql.append("                             LIMIT 1)      as diasCompensacaoCobranca, \n");
        sql.append("                             (SELECT tipoautorizacao \n");
        sql.append("                               FROM autorizacaocobrancacliente \n");
        sql.append("                               WHERE autorizacaocobrancacliente.cliente = cliente.codigo \n");
        sql.append("                                   AND autorizacaocobrancacliente.ativa IS TRUE \n");
        sql.append("                               ORDER BY autorizacaocobrancacliente.codigo DESC \n");
        sql.append("                             LIMIT 1)      as tipoAutorizacaoCobranca,\n");
        sql.append("                            movProdutoParcelaUnico.* \n");
        sql.append("                     FROM movparcela \n");
        sql.append("                            LEFT JOIN contrato ON contrato.codigo = movparcela.contrato \n");
        sql.append("                            INNER JOIN pessoa ON pessoa.codigo = movparcela.pessoa \n");
        sql.append("                            INNER JOIN cliente ON cliente.pessoa = pessoa.codigo \n");
        sql.append("                            INNER JOIN (SELECT movprodutoparcela.movparcela, \n");
        sql.append("                                                           produto.categoriaproduto, \n");
        sql.append("                                                           produto.codigo        as codigoProduto, \n");
        sql.append("                                                           produto.tipoproduto   as tipoProduto, \n");
        sql.append("                                                           movproduto.totalfinal as valorMovProdutoParcela, \n");
        sql.append("                                                           movproduto.descricaomovprodutomodalidade as descmovpmodalidade, \n");
        sql.append("                                                           produto.descricao     as descricaoProduto, \n");
        sql.append("                                                           movproduto.codigo     as codigoMovProduto \n");
        sql.append("                                        FROM movprodutoparcela \n");
        sql.append("                                               INNER JOIN movproduto ON movproduto.codigo = movprodutoparcela.movproduto \n");
        sql.append("                                               INNER JOIN produto \n");
        sql.append("                                                 ON produto.codigo = movproduto.produto) as movProdutoParcelaUnico \n");
        sql.append("                              ON movProdutoParcelaUnico.movparcela = movparcela.codigo \n");
        sql.append("                     WHERE movparcela.empresa = " + empresa).append(" \n");
        sql.append("                       AND movparcela.situacao = 'EA' \n");
        if (incluirParcelasRecorrenciaEmRelatorioReceitaProvisao) {
            sql.append("                       AND (contrato.regimerecorrencia IS true or " +
                    "exists(select codigo from autorizacaocobrancacliente acc " +
                    "where acc.cliente = cliente.codigo " +
                    "and ativa IS TRUE )) \n");
        }
        sql.append(") as q1 ) AS q2 ");
        sql.append("WHERE dataCompensacao BETWEEN '").append(dataInicial).append("' AND '").append(dataFinal).append("' ");

        PreparedStatement ps = con.prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        List<Integer> parcelasJaAdds = new ArrayList<>();
        while (rs.next()){
            if(parcelasJaAdds.contains(rs.getInt("codigo"))){
                continue;
            }
            parcelasJaAdds.add(rs.getInt("codigo"));
            LancamentoDF lancamento = new LancamentoDF();
            lancamento.setCodigoPessoa(rs.getInt("pessoa"));
            lancamento.setDescricaoLancamento(rs.getString("descricao"));
            lancamento.setContrato(rs.getInt("contrato"));
            lancamento.setMovProduto(rs.getInt("codigoMovProduto"));
            lancamento.setCodProduto(rs.getInt("codigoProduto"));
            lancamento.setEmpresa(rs.getInt("empresa"));
            lancamento.setTipoAutorizacaoCobranca(TipoAutorizacaoCobrancaEnum.valueOf(rs.getInt("tipoAutorizacaoCobranca")));
            lancamento.setTotalMovProdutoParcela(rs.getDouble("valorparcela"));
            lancamento.setNomePessoa(rs.getString("nomeCliente"));
            lancamento.setValorLancamento(rs.getDouble("valorparcela"));
            lancamento.setDiaConta(rs.getDate("dataCompensacao"));

            ProdutoRatear prod = new ProdutoRatear();
            prod.setCodigoCategoriaProduto(rs.getInt("categoriaproduto"));
            prod.setCodigoPessoa(rs.getInt("pessoa"));
            prod.setCodigoProduto(rs.getInt("codigoProduto"));
            prod.setContrato(rs.getInt("contrato"));
            prod.setTipoProduto(rs.getString("tipoProduto"));
            prod.setNomePessoa(rs.getString("nomeCliente"));
            prod.setValorMovProdutoParcela(rs.getDouble("valorparcela"));
            prod.setValorRatear(rs.getDouble("valorparcela"));
            prod.setDescricaoProduto(rs.getString("descricaoProduto"));
            if(!UteisValidacao.emptyString(rs.getString("descmovpmodalidade"))){
                prod.setListaContratoModalidadePercentual(obterListaContratoModalidadePercentual(new StringBuilder(rs.getString("descmovpmodalidade"))));
            }

            lancamento.getListaProdutoRatear().add(prod);

            lancamentos.add(lancamento);
        }

        return lancamentos;
    }

    private void logarMetodo(MesProcessar mesProcessar, boolean iniciar, String nomeMetodo){
        if(iniciar){
            System.out.println(mesProcessar.getMesAno() + " vou começar a processar "+nomeMetodo);
            mapaInicios.put(nomeMetodo, Calendario.hoje());
        }else{
            Long segundosDemora = segundosDemora(nomeMetodo);
            System.out.println(mesProcessar.getMesAno() + " vou terminar de processar "+nomeMetodo+". "+segundosDemora+" segundos");
        }
        
    }
    private Long segundosDemora(String chave){
        Date inicio = mapaInicios.get(chave);
        return inicio == null ? 0 : ((Calendario.hoje().getTime() - inicio.getTime()) / 1000);
    }

    private void processarDadosPorFaturamentoCaixaZillyonWeb(MesProcessar mesProcessar, int empresa) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        List<LancamentoDF> listaLancamentosDF = null;

        // Realizar o rateio dos pagamentos cuja forma de pagamento seja diferente de cheque e cartão de crédito.
        listaLancamentosDF = this.consultarPagamentos(mesProcessar, empresa);
        listaLancamentosDF = removerLancamentosPagamentoChequesDevolvidos(listaLancamentosDF);
        dividirValorDoPagamentoParaCadaProduto(listaLancamentosDF);
        realizarRateioZillyonWeb(listaLancamentosDF, mesProcessar);
    }

    private List<LancamentoDF> removerLancamentosPagamentoChequesDevolvidos(List<LancamentoDF> listaLancamentosDF) {
        if (!this.apresentarDevolucoesRel) {
            List<LancamentoDF> novaListaSemDevolucaoCheque = new ArrayList<>();
            for (LancamentoDF lancamentoDF : listaLancamentosDF) {
                if (!lancamentoDF.getDescricaoLancamento().equalsIgnoreCase("PAGAMENTO DE CHEQUES DEVOLVIDOS")) {
                    novaListaSemDevolucaoCheque.add(lancamentoDF);
                }
            }
            return novaListaSemDevolucaoCheque;
        } else {
            return listaLancamentosDF;
        }
    }

    private void processarDadosPorFaturamentoZillyonWeb(MesProcessar mesProcessar, int empresa) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        List<LancamentoDF> listaLancamentosDF = this.consultarMovProduto(0, null, mesProcessar, empresa, TipoRelatorioDF.FATURAMENTO, true);
        realizarRateioZillyonWeb(listaLancamentosDF, mesProcessar);
        System.out.println("Thread Mes:" + mesProcessar.getMesAno() + " Terminou. ");
    }

    private void realizarRateioZillyonWeb(List<LancamentoDF> listaLancamentosDF, MesProcessar mesProcessar) throws Exception {
        /* Realizar o rateio de cada Lançamento, para os seus respectivos Plano de Contas e Centro de Custos.
         *
         */
//        long l1 = System.currentTimeMillis();
        try {
            for (LancamentoDF lancamentoDF : listaLancamentosDF) {
                lancamentoDF.setFluxocaixa(fluxoCaixa);
                for (ProdutoRatear produtoRatear : lancamentoDF.getListaProdutoRatear()) {
                    lancamentoDF.setValorMultaJuros(produtoRatear.getMultaJuros());
                    boolean ratearProdutoMMparaCentroCustoModalidade =  this.agruparValorProdutoMMasModalidades &&
                                                                        ((produtoRatear.getTipoProduto().equals("MM"))  &&
                                                                        (produtoRatear.getListaContratoModalidadePercentual() != null) &&
                                                                        (produtoRatear.getListaContratoModalidadePercentual().size() > 0));
                    if ((produtoRatear.getTipoProduto().equals("PM")) || (ratearProdutoMMparaCentroCustoModalidade)){
                        if (ratearProdutoMMparaCentroCustoModalidade){
                            lancamentoDF.setDescricaoLancamento("MANUTENÇÃO MODALIDADE");
                        }else{
                            lancamentoDF.setDescricaoLancamento("PLANO");
                        }
                        // Realizar rateio para venda de Plano
                        realizarRateioPlano(produtoRatear, lancamentoDF, mesProcessar);

                    } else if ((produtoRatear.getTipoProduto().equals("DI"))
                            || (produtoRatear.getTipoProduto().equals("AA"))) {
                        // Realizar rateio para venda de Diária e Aula Avulsa
                        lancamentoDF.setDescricaoLancamento(produtoRatear.getDescricaoProduto());
                        realizarRateioAulaAvulsa_E_Diaria(produtoRatear, lancamentoDF, mesProcessar);

                    } else {
                        // Realizar rateio para venda de Produto/Serviço
                        lancamentoDF.setDescricaoLancamento(produtoRatear.getDescricaoProduto());
                        lancamentoDF.setTipoProduto(produtoRatear.getTipoProduto());
                        realizarRateioVendaProdutoServico(produtoRatear, lancamentoDF, mesProcessar);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

//        long l2 = System.currentTimeMillis();
//        Uteis.logar(null, l2 - l1 + "ms - realizarRateioZillyonWeb");


    }

    public static List<RateioIntegracaoTO> pesquisarRateios(List<RateioIntegracaoTO> listaTodosRateios,
            TipoCamposPesquisarRateio campoPesquisar,
            int valorPesquisar,
            TipoVisualizacaoRelatorioDF tipoVisualizacaoRelatorio, Integer empresa) {

        List<RateioIntegracaoTO> listaRateios = new ArrayList<RateioIntegracaoTO>();
        if (valorPesquisar <= 0) {
            return listaRateios;
        }
        for (RateioIntegracaoTO obj : listaTodosRateios) {
            if ((tipoVisualizacaoRelatorio == TipoVisualizacaoRelatorioDF.PLANOCONTA)
                    && ((obj.getCodigoPlano() == null) || (obj.getCodigoPlano().equals("")))) {
                /* Se estiver gerando o relatório com a visualiação de plano de contas, então
                 * consultar somente os rateios de plano de contas.
                 */
                continue;
            }

            if ((tipoVisualizacaoRelatorio == TipoVisualizacaoRelatorioDF.CENTROCUSTO)
                    && ((obj.getCodigoCentro() == null) || (obj.getCodigoCentro().equals("")))) {
                /* Se estiver gerando o relatório com a visualiação de centro de custos, então
                 * consultar somente os rateios de centro de custo.
                 */
                continue;
            }
            
            if(!UteisValidacao.emptyNumber(empresa) && !UteisValidacao.emptyNumber(obj.getEmpresa())
                    && !empresa.equals(obj.getEmpresa())){
                continue;
            }

            switch (campoPesquisar) {
                case PRODUTO: {
                    if ((obj.getCodigoProduto() != null) && (obj.getCodigoProduto() == valorPesquisar)) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case CATEGORIAPRODUTO: {
                    if ((obj.getCodigoCategoria() != null) && (obj.getCodigoCategoria() == valorPesquisar)) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case MODALIDADE: {
                    if ((obj.getCodigoModalidade() != null) && (obj.getCodigoModalidade() == valorPesquisar)) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case SERVICO: {
                    if ((obj.getCodigoServico() != null) && (obj.getCodigoServico() == valorPesquisar)) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case TIPO_PRODUTO: {
                    if ((obj.getCodigoTipoProdutoCE() != null) && (obj.getCodigoTipoProdutoCE() == valorPesquisar)) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case PRODUTO_CE: {
                    if ((obj.getCodigoProdutoCE() != null) && (obj.getCodigoProdutoCE() == valorPesquisar)) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case AMBIENTES: {
                    if (obj.getRateioAmbientes()) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case AMBIENTE: {
                    if ((obj.getCodigoAmbiente() != null) && (obj.getCodigoAmbiente() == valorPesquisar)) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case SERVICOS: {
                    if (obj.getRateioServicos()) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case CREDITO: {
                    if (obj.isCredito()) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case DEVOLUCAO_CREDITO: {
                    if (obj.isDevolucaoCredito()) {
                        listaRateios.add(obj);
                    }
                    break;
                }
                case GERAL_MODALIDADES: {
                    if (obj.isGeralModalidade()) {
                        listaRateios.add(obj);
                    }
                    break;
                }
            }
        }
        return listaRateios;
    }

    /*
     * Objetivo: Consultar todos os rateios cadastrados no ZillyonWeb.
     */
    public List<RateioIntegracaoTO> consultarTodosRateiosIntegracao(Connection con) throws Exception {
        rateioMultiEmpresaPlanoContas = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);
        rateioMultiEmpresaCentroCusto = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);
        List<RateioIntegracaoTO> listaRateio = new ArrayList<RateioIntegracaoTO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select rt.codigo as codigorateio, ");
        sql.append("rt.percentagem as percentagemrateio, ");
        sql.append("rt.planoconta as planocontasrateio, ");
        sql.append("rt.centrocusto as centrocustosrateio, ");
        sql.append("rt.modalidade as modalidaderateio, ");
        sql.append("rt.categoriaproduto as codigocategoria, ");
        sql.append("rt.produto as produtorateio, ");
        sql.append("rt.produtoce, ");
        sql.append("rt.tipoproduto, ");
        sql.append("rt.servico, ");
        sql.append("rt.ambiente, ");
        sql.append("rt.servicos, ");
        sql.append("rt.ambientes, ");
        sql.append("rt.credito, ");
        sql.append("rt.empresa, ");
        sql.append("rt.geralmodalidade, ");
        sql.append("rt.devolucaocredito, ");
        sql.append("pl.nome as nomeplano, ");
        sql.append("pl.tipoes, ");
        sql.append("cc.nome as nomecentro, ");
        sql.append("pl.codigoplanocontas as codigoplano, pl.nome as nomeplano, ");
        sql.append("cc.codigocentrocustos as codigocentro, cc.nome as nomecentro ");
        sql.append("from rateiointegracao rt ");
        sql.append("left join planoconta pl on rt.planoconta = pl.codigo ");
        sql.append("left join centrocusto cc on rt.centrocusto = cc.codigo where rt.planocontarateio is null");
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        while (rs.next()) {
            RateioIntegracaoTO rateio = new RateioIntegracaoTO();
            rateio.setCodigo(rs.getInt("codigorateio"));
            rateio.setEmpresa(rs.getInt("empresa"));
            rateio.setCodigoCategoria(rs.getInt("codigocategoria"));
            rateio.setCodigoCentro(rs.getString("codigocentro"));
            rateio.setCodigoCentroCustos(rs.getInt("centrocustosrateio"));
            rateio.setCodigoPlano(rs.getString("codigoplano"));
            rateio.setCodigoPlanoContas(rs.getInt("planocontasrateio"));
            rateio.setNomeCentro(rs.getString("nomecentro"));
            rateio.setNomePlano(rs.getString("nomeplano"));
            double perc = rs.getDouble("percentagemrateio");
            rateio.setCodigoModalidade(rs.getInt("modalidaderateio"));
            rateio.setCodigoProduto(rs.getInt("produtorateio"));
            rateio.setCredito(rs.getBoolean("credito"));
            rateio.setDevolucaoCredito(rs.getBoolean("devolucaocredito"));
            rateio.setGeralModalidade(rs.getBoolean("geralmodalidade"));
            rateio.setPercentagem(perc);

            rateio.setCodigoAmbiente(rs.getInt("ambiente"));
            rateio.setCodigoServico(rs.getInt("servico"));
            rateio.setCodigoProdutoCE(rs.getInt("produtoce"));
            rateio.setCodigoTipoProdutoCE(rs.getInt("tipoproduto"));
            rateio.setRateioAmbientes(rs.getBoolean("ambientes"));
            rateio.setRateioServicos(rs.getBoolean("servicos"));

            rateio.setTipoES(TipoES.getTipoPadrao(rs.getInt("tipoes")));
            if(!UteisValidacao.emptyNumber(rateio.getEmpresa())){
                if(rateio.getCodigoPlanoContas() > 0){
                    preencherMapaRateioMultiEmpresa(rateio, rateioMultiEmpresaPlanoContas);
                } else {
                    preencherMapaRateioMultiEmpresa(rateio, rateioMultiEmpresaCentroCusto);
                }
            }
            listaRateio.add(rateio);
        }
        return listaRateio;
    }
    
   public static Map<String, Object> obterDoQueSeTrataRateio(RateioIntegracaoTO rateio){
       TipoCamposPesquisarRateio tipoCampo = null;
       Integer valor = null;
       if(!UteisValidacao.emptyNumber(rateio.getCodigoModalidade())){
           tipoCampo = TipoCamposPesquisarRateio.MODALIDADE;
           valor = rateio.getCodigoModalidade();
       }else if(!UteisValidacao.emptyNumber(rateio.getCodigoProduto())){
           tipoCampo = TipoCamposPesquisarRateio.PRODUTO;
           valor = rateio.getCodigoProduto();
       }else if(!UteisValidacao.emptyNumber(rateio.getCodigoCategoria())){
           tipoCampo = TipoCamposPesquisarRateio.CATEGORIAPRODUTO;
           valor = rateio.getCodigoCategoria();
       }else if(!UteisValidacao.emptyNumber(rateio.getCodigoAmbiente())){
           tipoCampo = TipoCamposPesquisarRateio.AMBIENTE;
           valor = rateio.getCodigoAmbiente();
       }else if(!UteisValidacao.emptyNumber(rateio.getCodigoServico())){
           tipoCampo = TipoCamposPesquisarRateio.SERVICO;
           valor = rateio.getCodigoServico();
       }else if(!UteisValidacao.emptyNumber(rateio.getCodigoProdutoCE())){
           tipoCampo = TipoCamposPesquisarRateio.PRODUTO_CE;
           valor = rateio.getCodigoProdutoCE();
       }else if(!UteisValidacao.emptyNumber(rateio.getCodigoTipoProdutoCE())){
           tipoCampo = TipoCamposPesquisarRateio.TIPO_PRODUTO;
           valor = rateio.getCodigoTipoProdutoCE();
       }else if(rateio.getRateioAmbientes()){
           tipoCampo = TipoCamposPesquisarRateio.AMBIENTES;
           valor = 0;
       }else if(rateio.getRateioServicos()){
           tipoCampo = TipoCamposPesquisarRateio.SERVICOS;
           valor = 0;
       }else if(rateio.isCredito()){
           tipoCampo = TipoCamposPesquisarRateio.CREDITO;
           valor = 0;
       }else if(rateio.isDevolucaoCredito()){
           tipoCampo = TipoCamposPesquisarRateio.DEVOLUCAO_CREDITO;
           valor = 0;
       } else if(!UteisValidacao.emptyNumber(rateio.getPlanoContasRateio())){
           tipoCampo = TipoCamposPesquisarRateio.PLANOCONTA;
           valor = rateio.getPlanoContasRateio();
       }else if(rateio.isGeralModalidade()){
           tipoCampo = TipoCamposPesquisarRateio.GERAL_MODALIDADES;
           valor = 0;
       }
       Map<String, Object> mapa = new HashMap<String, Object>();
       mapa.put("valor", valor);
       mapa.put("tipo", tipoCampo);
       return mapa;
   } 
    
   public static void preencherMapaRateioMultiEmpresa(RateioIntegracaoTO rateio, Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresa){
       Map<String, Object> map = obterDoQueSeTrataRateio(rateio);
       TipoCamposPesquisarRateio tipoCampo = (TipoCamposPesquisarRateio) map.get("tipo");
       Integer valor = (Integer) map.get("valor");
       Map<Integer, Set<Integer>> mapa = rateioMultiEmpresa.get(tipoCampo);
       if(mapa == null){
           mapa = new HashMap<Integer, Set<Integer>>();
       }
       Set<Integer> empresas = mapa.get(valor);
       if(empresas == null){
           empresas = new HashSet<Integer>();
       }
       empresas.add(rateio.getEmpresa());
       mapa.put(valor, empresas);
       rateioMultiEmpresa.put(tipoCampo, mapa);
   }
   
   public static Boolean temRateioDaEmpresa(Integer empresa, RateioIntegracaoTO rateio, Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaPlanoContas, Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaCentroCusto){
        Map<String, Object> map = obterDoQueSeTrataRateio(rateio);
        TipoCamposPesquisarRateio tipoCampo = (TipoCamposPesquisarRateio) map.get("tipo");
        Integer valor = (Integer) map.get("valor");
        Map<Integer, Set<Integer>> mapa;
        if(rateio.getCodigoPlanoContas() > 0){
               mapa = rateioMultiEmpresaPlanoContas.get(tipoCampo);
        } else {
               mapa = rateioMultiEmpresaCentroCusto.get(tipoCampo);
        }
        if(mapa == null)
            return false;
        Set<Integer> set = mapa.get(valor);
        return set != null && set.contains(empresa);
       
   }

    private void realizarRateioVendaProdutoServico(ProdutoRatear produtoRatear, LancamentoDF lancamentoDF, MesProcessar mesProcessar) throws Exception {
        /*
         * Realiza o rateio de venda de Produto/Serviço.
         * Os rateios estão definidos para os produtos e suas categorias.
         * Regra de rateio:
         *        - Se tiver rateio definido para o produto, então estes rateios serão utilizados.
         *        - Se não tiver rateio definido para o produto, então considerar os rateios definidos para a categoria do produto.
         *        - Se não tiver definido rateio para o produto e nem para a categoria do produto, então os valores serão
         *          rateados para o Plano de contas/Centro de Custo "Não Informado"
         *
         */

        LancamentoDF lancamento = null;

        List<RateioIntegracaoTO> listaRateios;
        TipoCamposPesquisarRateio tipoCampoPesquisaRateio = TipoCamposPesquisarRateio.PRODUTO;
        int codigoCampoPesquisa = produtoRatear.getCodigoProduto();
        // Pesquisar rateios definido para o produto.
        listaRateios = pesquisarRateios(this.listaRateiosIntegracao,
                TipoCamposPesquisarRateio.PRODUTO,
                produtoRatear.getCodigoProduto(),
                this.tipoVisualizacao, lancamentoDF.getEmpresa());
        if (listaRateios.size() <= 0) {
            // Pesquisar rateios definido para a categoria do produto.
            tipoCampoPesquisaRateio = TipoCamposPesquisarRateio.CATEGORIAPRODUTO;
            codigoCampoPesquisa = produtoRatear.getCodigoCategoriaProduto();
            listaRateios = pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.CATEGORIAPRODUTO,
                    produtoRatear.getCodigoCategoriaProduto(),
                    this.tipoVisualizacao, lancamentoDF.getEmpresa());

        }

        if (dre) {
            // Pesquisar rateios definido para o produto.
            List<RateioIntegracaoTO> listaRateiosDRE = pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.PRODUTO,
                    produtoRatear.getCodigoProduto(),
                    TipoVisualizacaoRelatorioDF.CENTROCUSTO, lancamentoDF.getEmpresa());
            if (listaRateiosDRE.size() <= 0) {
                // Pesquisar rateios definido para a categoria do produto.
                tipoCampoPesquisaRateio = TipoCamposPesquisarRateio.CATEGORIAPRODUTO;
                codigoCampoPesquisa = produtoRatear.getCodigoCategoriaProduto();
                listaRateiosDRE = pesquisarRateios(this.listaRateiosIntegracao,
                        TipoCamposPesquisarRateio.CATEGORIAPRODUTO,
                        produtoRatear.getCodigoCategoriaProduto(),
                        TipoVisualizacaoRelatorioDF.CENTROCUSTO, lancamentoDF.getEmpresa());

            }
            lancamentoDF.setRateiosDRE(listaRateiosDRE);
            lancamentoDF.setCmRec(null);
        }

        if (listaRateios.size() > 0) {
            for (RateioIntegracaoTO rateio : listaRateios) {
                /*lancamento = lancamentoDF.clone();// Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
                realizarRateio((produtoRatear.getValorRatear() * rateio.getPercent()),
                rateio,
                lancamento,
                mesProcessar);*/
                lancamentoDF.getRateios().addAll(listaRateios);
                realizarRateioComFiltro(codigoCampoPesquisa,
                        tipoCampoPesquisaRateio,
                        produtoRatear.getValorRatear(),
                        rateio,
                        lancamentoDF,
                        mesProcessar);
            }

        } else {
            //Totalizar os valores que não foram definidos rateios.
            lancamento = lancamentoDF.clone();// Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
            lancamento.setCmRec(null);
            realizarRateio((produtoRatear.getValorRatear()),
                    this.rateioNaoDefinido,
                    lancamento,
                    mesProcessar);
        }


    }

    /**
     * Realiza o rateio de venda de Plano.
     * Os rateios estão definidos para as modalidades do Plano.
     * @param produtoRatear
     * @param lancamentoDF
     * @param mesProcessar
     * @throws Exception
     */
    private void realizarRateioPlano(ProdutoRatear produtoRatear, LancamentoDF lancamentoDF, MesProcessar mesProcessar) throws Exception {
        LancamentoDF lancamento = null;
        List<ContratoModalidadePercentual> listaModalidadesPercentual = produtoRatear.getListaContratoModalidadePercentual();
        Double perc = 0.0;
        if(listaModalidadesPercentual != null){
            for(ContratoModalidadePercentual obj : listaModalidadesPercentual){
                perc += obj.getPercentagem();
            }
        }
        double valorResiduo = Uteis.arredondarForcando2CasasDecimais(produtoRatear.getValorRatear());
        for (ContratoModalidadePercentual obj : listaModalidadesPercentual) {
            //Essa condição é devido a Pagamento de centavos do ProdutosPagos na tebela CC de centavos, ser menor do que o número de modalidades.
            //Isso gerava um aumento no valor exibido do rateio.
            //Ex: 0,08 centavos / 12 modalidades = 12 modalidade de 1 centavo, aumentando em 0,04 centavos o valor total.
            if (valorResiduo > 0.0) {
                if(perc.equals(new Double(0.0))){
                    Double valorProdutoRatear = Uteis.arredondarForcando2CasasDecimais(produtoRatear.getValorRatear() / listaModalidadesPercentual.size());
                    obj.setValor(valorResiduo >= valorProdutoRatear ? valorProdutoRatear : Uteis.arredondarForcando2CasasDecimais(valorResiduo));
                    valorResiduo -= Uteis.arredondarForcando2CasasDecimais(obj.getValor());
                } else {
                    Double valorperc = (new Double(obj.getPercentagem()).equals(new Double(0.0)) && perc.equals(new Double(0.0))) ? 0.0 : obj.getPercentagem()/perc;
                    // Calcular o valor que cada modalidade representa em relação ao valor do Lancamento.
                    Double valorProdutoRatear = Uteis.arredondarForcando2CasasDecimais(listaModalidadesPercentual.size() == 1 ?
                            produtoRatear.getValorRatear() : valorperc * produtoRatear.getValorRatear());
                    obj.setValor(valorResiduo >= valorProdutoRatear ? valorProdutoRatear : Uteis.arredondarForcando2CasasDecimais(valorResiduo));
                    valorResiduo -= Uteis.arredondarForcando2CasasDecimais(obj.getValor());
                }
            } else {
                obj.setValor(0.0);
            }
            obj.setNomeParcela(produtoRatear.getDescricaoParcela());
        }
        //Teste se o if do comentário acima resolveu o problema no Fluxo de Caixa e Demonstrativo Financeiro.
        //Se não resolver, descomentar o código abaixo e voltar a parte acima como era antes.
        if (listaModalidadesPercentual.size() > 0 && valorResiduo > 0.0) {
            listaModalidadesPercentual.get(0).setValor(listaModalidadesPercentual.get(0).getValor() + Uteis.arredondarForcando2CasasDecimais(valorResiduo));
        }

        lancamentoDF.setRateioModalidades(listaModalidadesPercentual);
        if (listaModalidadesPercentual.size() <= 0) {

            // Para os contratos que não tem registros em "contratoModalidade" lançar como não Definido plano de Contas.
            lancamento = lancamentoDF.clone();// Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
            realizarRateio(produtoRatear.getValorRatear(),
                    this.rateioNaoDefinido,
                    lancamento,
                    mesProcessar);
        }

        for (ContratoModalidadePercentual contratoModalidadePerc : listaModalidadesPercentual) {
            
            // Realizar o rateio de cada modalidade
            lancamentoDF.setCmRec(contratoModalidadePerc);
            // Pesquisa os rateios definido para cada modalidade.
            List<RateioIntegracaoTO> listaRateiosModalidade = pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.MODALIDADE,
                    contratoModalidadePerc.getCodigoModalidade(),
                    this.tipoVisualizacao, lancamentoDF.getEmpresa());
            if (listaRateiosModalidade.isEmpty()) {
                listaRateiosModalidade = pesquisarRateios(this.listaRateiosIntegracao,
                        TipoCamposPesquisarRateio.GERAL_MODALIDADES,
                        contratoModalidadePerc.getCodigoModalidade(),
                        this.tipoVisualizacao, lancamentoDF.getEmpresa());
            }
            if (listaRateiosModalidade.size() > 0) {
                lancamentoDF.getRateios().addAll(listaRateiosModalidade);
                for (RateioIntegracaoTO rateio : listaRateiosModalidade) {
                    //lancamento = lancamentoDF.clone(); // Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
                    /*realizarRateio(contratoModalidadePerc.getValor() * rateio.getPercent(),
                    rateio,
                    lancamento,
                    mesProcessar);*/
                    realizarRateioComFiltro(contratoModalidadePerc.getCodigoModalidade(),
                            TipoCamposPesquisarRateio.MODALIDADE,
                            contratoModalidadePerc.getValor(),
                            rateio,
                            lancamentoDF,
                            mesProcessar);
                }
            } else {
                lancamento = lancamentoDF.clone(); // Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
                //Totalizar os valores que não foram definidos rateios.
                realizarRateio(contratoModalidadePerc.getValor(),
                        this.rateioNaoDefinido,
                        lancamento,
                        mesProcessar);

            }
        }
    }

    private double retornarValorRateioComFiltroCentroCusto(Integer codigoPesquisaRateio, TipoCamposPesquisarRateio tipoCampoPesquisar, double valorRatear, Integer empresa) {
        // Pesquisa os rateios de centro de custo.
        List<RateioIntegracaoTO> listaRateios = pesquisarRateios(this.listaRateiosIntegracao,
                tipoCampoPesquisar,
                codigoPesquisaRateio,
                TipoVisualizacaoRelatorioDF.CENTROCUSTO, empresa);
        double valorRetornar = 0.0;
        for (RateioIntegracaoTO rateio : listaRateios) {
            if (this.listaFiltroCentroCusto.contains(rateio.getCodigoCentroCustos())) {
                valorRetornar += (valorRatear * rateio.getPercent());
            }
        }

        return valorRetornar;

    }

    private void realizarRateioComFiltro(int codigoPesquisaRateio,
            TipoCamposPesquisarRateio tipoPesquisarRateio, double valor,
            RateioIntegracaoTO rateio, LancamentoDF lancamentoDF,
            MesProcessar mesProcessar) throws Exception {
        LancamentoDF lancamento = lancamentoDF.clone(); // Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
        double valorRatear = valor * rateio.getPercent();

      if (this.listaFiltroCentroCusto.size() > 0) {
          valorRatear = retornarValorRateioComFiltroCentroCusto(codigoPesquisaRateio, tipoPesquisarRateio, valorRatear, lancamentoDF.getEmpresa());
      }

        if (valorRatear > 0) {
            realizarRateio(valorRatear,
                    rateio,
                    lancamento,
                    mesProcessar,
                    false);
        }

    }

    private List<LancamentoDF> setToList(Set<LancamentoDF> listaLancamentos) {
        List<LancamentoDF> listaResult = new ArrayList<LancamentoDF>();
        for (LancamentoDF obj : listaLancamentos) {
            listaResult.add(obj);
        }
        return listaResult;
    }

    private void somarValorPagamento(TotalizadorMesDF totalizadorMesesDF, LancamentoDF lancamentoDF, double valorRatear, boolean listaNaoAtribuido) {
        /*
         * Procurar o Lançamento na Lista e atualizar o valor do Lançamento.
         */
        List<LancamentoDF> listaLancamentos = null;
        if (listaNaoAtribuido) {
            listaLancamentos = setToList(totalizadorMesesDF.getListaLancamentosNaoAtribuido());
        } else {
            listaLancamentos = setToList(totalizadorMesesDF.getListaLancamentos());
        }
        int indice = listaLancamentos.indexOf(lancamentoDF);
        if (indice >= 0) {
            LancamentoDF objLancamento = listaLancamentos.get(indice);
            objLancamento.setValorLancamento(objLancamento.getValorLancamento() + valorRatear);
        }
        listaLancamentos = null;
    }
    
    

    private void realizarRateio(double valorRatear, RateioIntegracaoTO rateioIntegracaoTo,
            LancamentoDF lancamentoDF, MesProcessar mesProcessar) throws Exception {
        realizarRateio(valorRatear, rateioIntegracaoTo, lancamentoDF, mesProcessar, true);
    }

    private void realizarRateio(double valorRatear, RateioIntegracaoTO rateioIntegracaoTo,
                                LancamentoDF lancamentoDF, MesProcessar mesProcessar, boolean validarEmpresa) throws Exception {
        final boolean naoRealizarRateio =
                validarEmpresa
                        && (!UteisValidacao.emptyNumber(rateioIntegracaoTo.getEmpresa()) && !rateioIntegracaoTo.getEmpresa().equals(lancamentoDF.getEmpresa()))
                        || (UteisValidacao.emptyNumber(rateioIntegracaoTo.getEmpresa()) && temRateioDaEmpresa(lancamentoDF.getEmpresa(), rateioIntegracaoTo,rateioMultiEmpresaPlanoContas,rateioMultiEmpresaCentroCusto));

        if(naoRealizarRateio) {
            return;
        }
//    	rateioIntegracaoTo.setTipoES(lancamentoDF.getTipoProduto().equals("DV") ? TipoES.SAIDA : rateioIntegracaoTo.getTipoES());
        /*
         * Totalizar o valor do rateio para o Plano de Conta/Centro de Custo.
         */
        if (rateioIntegracaoTo.getTipoES() == TipoES.SAIDA || lancamentoDF.getTipoProduto().equals("DV")) {
            valorRatear = valorRatear * -1;
        }
        adicionarMapa(lancamentoDF.getNomePessoa(), valorRatear, "rateio");
//        System.out.println(valorRatear);
        lancamentoDF.setValorLancamento(valorRatear);
        lancamentoDF.setTipoES(lancamentoDF.getTipoProduto().equals("DV") ? TipoES.SAIDA : rateioIntegracaoTo.getTipoES());
        if ((this.tipoVisualizacao == TipoVisualizacaoRelatorioDF.CENTROCUSTO) && (lancamentoDF.getTipoES() == null)) {
            // Neste caso está visualizando o relatório por centro de custo e o lançamento é de um recebimento do ZillyonWeb
            lancamentoDF.setTipoES(TipoES.ENTRADA);
        }

        String codigoAgrupador = "";
        if ((this.tipoVisualizacao == TipoVisualizacaoRelatorioDF.PLANOCONTA) || dre) {
            if (dre) {
                codigoAgrupador = novosCodigos.get(rateioIntegracaoTo.getCodigoPlano());

            } else {
                codigoAgrupador = rateioIntegracaoTo.getCodigoPlano();
            }
        } else {
            codigoAgrupador = rateioIntegracaoTo.getCodigoCentro();
        }


        DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
        TotalizadorMesDF totMeses = new TotalizadorMesDF();
        DemonstrativoFinanceiro demonstrativoFinanceiro;
        TotalizadorMesDF totalizadorMesesDF;
        int indiceTotalizador;
        int indiceDF;

        totMeses.setMesProcessar(mesProcessar);
        df.setCodigoAgrupador(codigoAgrupador);
        indiceDF = this.listaDemonstrativo.indexOf(df);
        // Verifica se econtrou o Código Agrupador.
        if ((indiceDF >= 0) && (valorRatear != 0)) {
            demonstrativoFinanceiro = this.listaDemonstrativo.get(indiceDF);
            indiceTotalizador = demonstrativoFinanceiro.getListaTotalizadorMeses().indexOf(totMeses);
            totalizadorMesesDF = demonstrativoFinanceiro.getListaTotalizadorMeses().get(indiceTotalizador);
            totalizadorMesesDF.setTotalNivel(valorRatear);
            if (dre) {
                setarRateioCentroCustos(valorRatear, demonstrativoFinanceiro, lancamentoDF);
            }
            //Procurar o Lançamento na Lista e atualizar o valor do Lançamento.
            somarValorPagamento(totalizadorMesesDF, lancamentoDF, valorRatear, false);


            // a lista "getListaLancamentos()" é um Set, desta forma se o Lançamento já foi adicionado, então a adição não é feita novamente.
            totalizadorMesesDF.getListaLancamentos().add(lancamentoDF);


            // Totalizar os valores por tipo de autorização de cobrança.
            // Veja o ticket https://app.assembla.com/spaces/plataforma-zw/tickets/12853-fluxo-de-caixa---just-fit---maurin/details
            for(Map.Entry<TipoAutorizacaoCobrancaEnum, Double> totalTipoAutorizacaoCobranca
                    : totalizadorMesesDF.getTotaisPorTipoAutorizacaoCobranca().entrySet()) {

                if(lancamentoDF.getTipoAutorizacaoCobranca() != null && lancamentoDF.getTipoAutorizacaoCobranca().equals(totalTipoAutorizacaoCobranca.getKey())){
                    totalTipoAutorizacaoCobranca.setValue(totalTipoAutorizacaoCobranca.getValue() + lancamentoDF.getValorLancamento());
                }
            }


            if ((indiceDF + 1) < listaDemonstrativo.size()) {
                // Verifica se o nível que está sendo totalizado é ou não de último nível.
                String nivelAtual = demonstrativoFinanceiro.getCodigoAgrupador();
                DemonstrativoFinanceiro dfProximoNivel = this.listaDemonstrativo.get(indiceDF + 1);
                String proximoNivel = dfProximoNivel.getCodigoAgrupador();
                String nivelPaiDoProximoNivel = "";
                if (proximoNivel.length() > nivelAtual.length()) {
                    nivelPaiDoProximoNivel = proximoNivel.substring(0, nivelAtual.length());
                }
                if (nivelPaiDoProximoNivel.equals(nivelAtual)) {
                    /* O nível que está sendo totalizado não é de último nível, então
                     * totalizar os valores atribuído a este nível à variável "totalNaoAtribuido"
                     * Obs.: Pode acontecer de um nível num determinado momento ser de último nível,
                     *       e em um outro momento o mesmo ser pai de outros níveis, devido a alteração
                     *       do cadastro do plano de contas.
                     */
                    demonstrativoFinanceiro.setHouveNaoAtribuido(true);
                    totalizadorMesesDF.setTotalNaoAtribuido(valorRatear);
                    LancamentoDF lancamentoNaoAt = lancamentoDF.clone();// Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
                    //Procurar o Lançamento na Lista e atualizar o valor do Lançamento.
                    somarValorPagamento(totalizadorMesesDF, lancamentoNaoAt, valorRatear, true);
                    // a lista "getListaLancamentosNaoAtribuido()" é um Set, desta forma se o Lançamento já foi adicionado, então a adição não é feita novamente.
                    totalizadorMesesDF.getListaLancamentosNaoAtribuido().add(lancamentoNaoAt);
                }
            }

            // Realizar rateio para os níveis superiores.
            while (codigoAgrupador.length() != 3) {
                codigoAgrupador = codigoAgrupador.substring(0, codigoAgrupador.length() - 4);
                df.setCodigoAgrupador(codigoAgrupador);
                indiceDF = this.listaDemonstrativo.indexOf(df);
                if (indiceDF == -1){
                    throw new ConsistirException("O código agrupador: '" + codigoAgrupador + "' da conta: (cód.: " + lancamentoDF.getMovConta() + " | Descrição: "
                            + lancamentoDF.getDescricaoLancamento() + ") não existe ou foi excluido! Entre na conta a pagar ou a receber mencionada, e realize o rateio dela novamente, informando o plano de contas e centro de custos.");
                }
                demonstrativoFinanceiro = this.listaDemonstrativo.get(indiceDF);
                indiceTotalizador = demonstrativoFinanceiro.getListaTotalizadorMeses().indexOf(totMeses);
                totalizadorMesesDF = demonstrativoFinanceiro.getListaTotalizadorMeses().get(indiceTotalizador);
                LancamentoDF lancamentoPai = lancamentoDF.clone();// Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.
                //Procurar o Lançamento na Lista e atualizar o valor do Lançamento.
                somarValorPagamento(totalizadorMesesDF, lancamentoPai, valorRatear, false);
                totalizadorMesesDF.setTotalNivel(valorRatear);
                if (dre) {
                    setarRateioCentroCustos(valorRatear, demonstrativoFinanceiro, lancamentoPai);
                }
                // a lista "getListaLancamentos()" é um Set, desta forma se o Lançamento já foi adicionado, então a adição não é feita novamente.
                if (isMontarNivelSuperior()) {
                    totalizadorMesesDF.getListaLancamentos().add(lancamentoPai);
                }
            }
        }
    }

    private void realizarRateioAulaAvulsa_E_Diaria(ProdutoRatear produtoRatear, LancamentoDF lancamentoDF, MesProcessar mesProcessar) throws Exception {
        /*
         * Realiza o rateio de venda de Aula Avulsa e Diária.
         * Regra de rateio para Aula Avulsa e Diária:
         *   - Se o relatório for por Plano de Contas, então considerar os rateios definidos no cadastro do Produto.
         *   - Se o relatório for por Centro de Custo, então considerar os rateios definidos para a modalidade que foi
         *     vendida a Aula ou a Diária.
         *
         */
        LancamentoDF lancamento = null;
        List<RateioIntegracaoTO> listaRateios = null;

        int modalidade = consultarModalidadeVendaDiariaAulaAvulsa(produtoRatear.getCodigoMovProdutoAulaAvulsaEDiaria(), this.con);
        // Pesquisar os rateios definidos para o Produto "Aula avulsa" ou "Diária".
        listaRateios = pesquisarRateios(this.listaRateiosIntegracao, TipoCamposPesquisarRateio.PRODUTO, produtoRatear.getCodigoProduto(), this.tipoVisualizacao, lancamentoDF.getEmpresa());

        if(listaRateios.size() <= 0 && this.tipoVisualizacao == TipoVisualizacaoRelatorioDF.CENTROCUSTO){
            listaRateios = pesquisarRateios(this.listaRateiosIntegracao, TipoCamposPesquisarRateio.MODALIDADE, modalidade, this.tipoVisualizacao, lancamentoDF.getEmpresa());
        }
        if (listaRateios.size() <= 0) {
            // Não foi informado um rateio para o produto, desta forma, pesquisar os rateios da categoria do produto.
            listaRateios = pesquisarRateios(this.listaRateiosIntegracao, TipoCamposPesquisarRateio.CATEGORIAPRODUTO, produtoRatear.getCodigoCategoriaProduto(), this.tipoVisualizacao, lancamentoDF.getEmpresa());
        }

        if (dre) {
            // Pesquisar rateios definido para o produto.
            List<RateioIntegracaoTO> listaRateiosDRE = pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.PRODUTO,
                    produtoRatear.getCodigoProduto(),
                    TipoVisualizacaoRelatorioDF.CENTROCUSTO, lancamentoDF.getEmpresa());
            if (listaRateiosDRE.size() <= 0) {
                // Pesquisar rateios definido para a categoria do produto.
                listaRateiosDRE = pesquisarRateios(this.listaRateiosIntegracao,
                        TipoCamposPesquisarRateio.CATEGORIAPRODUTO,
                        produtoRatear.getCodigoCategoriaProduto(),
                        TipoVisualizacaoRelatorioDF.CENTROCUSTO, lancamentoDF.getEmpresa());

            }
            lancamentoDF.setRateiosDRE(listaRateiosDRE);
            lancamentoDF.setCmRec(null);
        }

        if (listaRateios.size() > 0) {
            lancamentoDF.getRateios().addAll(listaRateios);
            for (RateioIntegracaoTO rateio : listaRateios) {
                realizarRateioComFiltro(modalidade,
                        TipoCamposPesquisarRateio.MODALIDADE,
                        produtoRatear.getValorRatear(),
                        rateio,
                        lancamentoDF,
                        mesProcessar);
            }
        } else {
            //Totalizar os valores que não foram definidos rateios.
            lancamento = lancamentoDF.clone();// Clonar o lançamento, pois o mesmo pode ser adicionado para outros plano de contas/Centro de Custo.

            realizarRateio(produtoRatear.getValorRatear(),
                    this.rateioNaoDefinido,
                    lancamento,
                    mesProcessar);

        }

    }

    private void processarDadosPorCompetenciaZillyonWeb(MesProcessar mesProcessar, int empresa, TipoRelatorioDF tipoRelatorioDF) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        List<LancamentoDF> listaLancamentosDF = this.consultarMovProduto(mesProcessar.getDataIni().get(Calendar.YEAR), mesProcessar.getMesAno(), null, empresa, tipoRelatorioDF, false);
        realizarRateioZillyonWeb(listaLancamentosDF, mesProcessar);
        System.out.println("Thread Mes:" + mesProcessar.getMesAno() + " Terminou. ");
    }

    public static int consultarModalidadeVendaDiariaAulaAvulsa(int codigoMovProduto, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select avd.modalidade ");
        sql.append(" from aulaavulsadiaria avd ");
        sql.append(" inner join movParcela movParc on movParc.aulaavulsadiaria = avd.codigo ");
        sql.append(" inner join movProdutoParcela  prodParc on prodParc.movparcela = movParc.codigo");
        sql.append(" inner join movProduto mov on mov.codigo = prodParc.movproduto");
        sql.append(" where mov.codigo = ");
        sql.append(codigoMovProduto);
        Statement stm = con.createStatement();
        ResultSet dadosSQL = stm.executeQuery(sql.toString());
        if (dadosSQL.next()) {
            return dadosSQL.getInt("modalidade");
        } else {
            return 0;
        }
    }

    private List<LancamentoDF> consultarMovProduto(int anoReferencia, String mesAnoReferencia, MesProcessar mesProcessar, int empresa, TipoRelatorioDF tipoRelatorioDF, Boolean usarValorFaturado) throws Exception {
        Date dataInicio = null;
        Date dataFim = null;
        if (mesProcessar != null) {
            dataInicio = mesProcessar.getDataIni().getTime();
            dataFim = mesProcessar.getDataFim().getTime();
        }
        StringBuilder sql = consultaCompetenciaFaturamento(anoReferencia, mesAnoReferencia, dataInicio, dataFim, empresa, false, tipoRelatorioDF, usarValorFaturado, null);
        Statement stm = con.createStatement();
        ResultSet dadosSQL = stm.executeQuery(sql.toString());
        List<LancamentoDF> listaLancamentosDF = new ArrayList<LancamentoDF>();
        while (dadosSQL.next()) {
            LancamentoDF lancamentoDF = new LancamentoDF();
            lancamentoDF.setMovProduto(dadosSQL.getInt("codigo"));
            lancamentoDF.setCodProduto(dadosSQL.getInt("produto"));
            lancamentoDF.setContrato(dadosSQL.getInt("contrato"));
            lancamentoDF.setCodigoPessoa(dadosSQL.getInt("pessoa"));
            lancamentoDF.setDiaConta(dadosSQL.getDate("datalancamento"));
            lancamentoDF.setMesReferencia(dadosSQL.getString("mesreferencia"));
            if (lancamentoDF.getCodigoPessoa() > 0) {
                lancamentoDF.setNomePessoa(dadosSQL.getString("nome"));
            } else {
                lancamentoDF.setNomePessoa("CONSUMIDOR"); // Neste caso a venda foi feita para um consumidor
            }
            if(usarValorFaturado){
                lancamentoDF.setValorLancamento(dadosSQL.getDouble("valorFaturado"));
            }else{
                lancamentoDF.setValorLancamento(dadosSQL.getDouble("totalFinal"));
            }
            lancamentoDF.setDevolucaoCheque((dadosSQL.getInt("chequeDevolucao") > 0)? true : false);
            lancamentoDF.setDescricaoLancamento(dadosSQL.getString("descricao"));
            lancamentoDF.setEmpresa(dadosSQL.getInt("empresa"));
            lancamentoDF.setNomeEmpresa(dadosSQL.getString("nomeEmpresa"));
            lancamentoDF.setTipoProduto(dadosSQL.getString("tipoproduto"));

            ProdutoRatear produtoRatear = new ProdutoRatear();
            produtoRatear.setCodigoProduto(dadosSQL.getInt("produto"));
            produtoRatear.setTipoProduto(dadosSQL.getString("tipoproduto"));
            produtoRatear.setCodigoCategoriaProduto(dadosSQL.getInt("categoriaproduto"));
            if(usarValorFaturado){
                produtoRatear.setValorRatear(dadosSQL.getDouble("valorFaturado"));
            }else{
                produtoRatear.setValorRatear(dadosSQL.getDouble("totalFinal"));
            }

            produtoRatear.setDescricaoProduto(dadosSQL.getString("descprod"));
            produtoRatear.setListaContratoModalidadePercentual(obterListaContratoModalidadePercentual(new StringBuilder(dadosSQL.getString("descmovpmodalidade"))));
            if ((produtoRatear.getTipoProduto().equals("DI"))
                    || (produtoRatear.getTipoProduto().equals("AA"))) {
                produtoRatear.setCodigoMovProdutoAulaAvulsaEDiaria(dadosSQL.getInt("codigo"));
            }

            lancamentoDF.getListaProdutoRatear().add(produtoRatear);

            listaLancamentosDF.add(lancamentoDF);
        }
        consultarProdutosDevolucao(listaLancamentosDF, null, null, empresa, true, anoReferencia, mesAnoReferencia);
        return listaLancamentosDF;
    }



    public static List<ProdutoRatear> pesquisarProdutosDoPagamento(int codigoMovPagamento, 
            String produtos, Connection con, String produtosFiltrar) throws Exception {
        /*
         * Pesquisar os produtos que o pagamento pagou.
         * Esclarecimento acerca de um pagamento:
         *    1 - Um pagamento pode pagar uma ou várias parcelas(movParcela).
         *    2 - Uma parcela está vinculada a um ou vários produtos(MovProduto)
         */

        String codsProdutos = "";
        Map<Integer, Double> produtosValores = new HashMap<Integer, Double>();
        if (!UteisValidacao.emptyString(produtos)) {
            String[] split = produtos.split("\\|");
            for (String str : split) {
                if (!str.isEmpty() && !str.equals("null")) {
                    String[] prodvalor = str.split(",");
                    if(prodvalor.length <4){
                        continue;
                    }
                    codsProdutos = codsProdutos + ", " + prodvalor[0];
                    Double valor = produtosValores.get(Integer.valueOf(prodvalor[0]));
                    if(valor == null){
                        valor = Double.valueOf(prodvalor[3]);
                    }else{
                        valor += Double.valueOf(prodvalor[3]);
                    }
                    produtosValores.put(Integer.valueOf(prodvalor[0]), valor);
                    
                }
            }
        }

        StringBuilder sql = new StringBuilder();
        sql.delete(0, sql.length());
        sql.append("select  pgtoMovParc.recibopagamento, prod.codigo codigoProduto, prod.descricao descricaoProduto, coalesce(movProd.descricaomovprodutomodalidade, '') as descricaomovprodutomodalidade,  ");
        sql.append("prod.tipoproduto, prod.categoriaproduto, movProd.empresa, emp.nome as nomeEmpresa, movProd.pessoa,   ");
        sql.append("movProd.contrato, movProd.codigo as movprodcodigo, cont.valorfinal valorContrato, pess.nome, \n");
        sql.append("vin.colaborador as consultor, pla.descricao as plano, \n");
        sql.append("movProd.chequeDevolucao, ");
        if (UteisValidacao.emptyString(codsProdutos)) {
        	sql.append(" pgtoMovParc.valorPago,  sum(prodParc.valorPago) totalMovProdutoParcela,\n");
        }
        sql.append("movProd.quitado as produtoQuitado ,  movProd.quantidade qtdProduto, movProd.vendaavulsa,responsavel.nome as NomeResponsavel, parc.personal, parc.aulaavulsadiaria,\n");
        sql.append("coalesce(mprodmj.totalfinal, 0) as multajuros, va.nomeComprador, va.tipocomprador\n");
        sql.append("from PagamentoMovParcela pgtoMovParc\n");
        sql.append("inner join movProdutoParcela prodParc on prodParc.movparcela = pgtoMovParc.movparcela\n");
        sql.append("inner join movParcela parc on parc.codigo = pgtoMovParc.movparcela\n");
        sql.append("inner join movProduto movProd  on movProd.codigo = prodParc.movproduto\n");
        sql.append("left join Pessoa pess  on pess.Codigo = movProd.Pessoa\n");
        sql.append("inner join Produto prod on prod.codigo = movProd.produto\n");
        if(!UteisValidacao.emptyString(produtosFiltrar)){
            sql.append("AND prod.tipoproduto in (").append(produtosFiltrar).append(") \n");
        }
        sql.append("inner join Usuario responsavel ON responsavel.codigo = movProd.responsavellancamento\n");
        sql.append("left join contrato cont on cont.codigo = movProd.contrato\n");
        sql.append("left join empresa emp on emp.codigo = movProd.empresa\n");
        sql.append("left join plano pla ON pla.codigo = cont.plano\n");
        sql.append("left join cliente cli on movProd.pessoa = cli.pessoa\n");
        sql.append("left join vinculo vin on (cli.codigo = vin.cliente and vin.tipovinculo = 'CO')\n");
        sql.append("left join movproduto mprodmj ON mprodmj.movprodutooriginal = movProd.codigo\n");
        sql.append("left join vendaavulsa va on movProd.vendaavulsa = va.codigo\n");

        sql.append("where movProd.totalfinal > 0 ");
        sql.append(" and  pgtoMovParc.MovPagamento = ");
        sql.append(codigoMovPagamento);
        
        if (!UteisValidacao.emptyString(codsProdutos)) {
            sql.append(" and movProd.Codigo IN (").append(codsProdutos.replaceFirst(",", "")).append(") ");
        }

        sql.append("and coalesce(movProd.movprodutooriginal, 0) = 0\n");
        sql.append(" group by pgtoMovParc.recibopagamento, prod.codigo, ").
                append("prod.descricao, prod.tipoproduto, prod.categoriaproduto, ").
                append("movProd.empresa, emp.nome, movProd.pessoa, movProd.contrato, ").
                append("cont.valorfinal, movProd.codigo, ").
                append("pess.nome, vin.colaborador, movProd.descricaomovprodutomodalidade, pla.descricao,movProd.vendaavulsa,parc.personal, parc.aulaavulsadiaria, movProd.chequeDevolucao ");
        if (UteisValidacao.emptyString(codsProdutos)) {
        	sql.append(", pgtoMovParc.valorPago");
        }
        sql.append(",produtoQuitado\n");
        sql.append(",qtdProduto\n");
        sql.append(",NomeResponsavel\n");
        sql.append(",mprodmj.totalfinal\n");
        sql.append(",va.nomeComprador, va.tipocomprador\n");
        Statement stm2 = con.createStatement();
        ResultSet dadosProdutosPagos = stm2.executeQuery(sql.toString());

        List<ProdutoRatear> listaProdutoRatear = new ArrayList<ProdutoRatear>();
        while (dadosProdutosPagos.next()) {
            ProdutoRatear produtoRatear = new ProdutoRatear();
            produtoRatear.setCodigoProduto(dadosProdutosPagos.getInt("codigoProduto"));
            produtoRatear.setValorTotalContrato(dadosProdutosPagos.getDouble("valorContrato"));
            produtoRatear.setTipoProduto(dadosProdutosPagos.getString("tipoproduto"));
            produtoRatear.setDescricaoProduto(dadosProdutosPagos.getString("descricaoProduto"));
            produtoRatear.setCodigoCategoriaProduto(dadosProdutosPagos.getInt("categoriaproduto"));
            produtoRatear.setContrato(dadosProdutosPagos.getInt("contrato"));
            produtoRatear.setPersonal(dadosProdutosPagos.getInt("personal"));
            produtoRatear.setAulaavulsadiaria(dadosProdutosPagos.getInt("aulaavulsadiaria")); 
            produtoRatear.setVendaAvulsa(dadosProdutosPagos.getInt("vendaavulsa"));
            produtoRatear.setPlanoDescricao(dadosProdutosPagos.getString("plano"));
            produtoRatear.setCodigoPessoa(dadosProdutosPagos.getInt("pessoa"));
            produtoRatear.setEmpresa(dadosProdutosPagos.getInt("empresa"));
            produtoRatear.setNomeEmpresa(dadosProdutosPagos.getString("nomeEmpresa"));
            produtoRatear.setRecibo(dadosProdutosPagos.getInt("recibopagamento"));
            produtoRatear.setCodigoMovProduto(dadosProdutosPagos.getInt("movprodcodigo"));
            produtoRatear.setListaContratoModalidadePercentual(obterListaContratoModalidadePercentual(
                    new StringBuilder(dadosProdutosPagos.getString("descricaomovprodutomodalidade"))));
            produtoRatear.setCodigoConsultor(dadosProdutosPagos.getInt("consultor"));
            produtoRatear.setQuantidade(dadosProdutosPagos.getInt("qtdProduto"));
            produtoRatear.setResponsavelLancamento(dadosProdutosPagos.getString("NomeResponsavel"));
            produtoRatear.setDevolucaoCheque((dadosProdutosPagos.getInt("chequeDevolucao") > 0)?true : false);
            if (UteisValidacao.emptyString(codsProdutos)) {
                produtoRatear.setValorMovProdutoParcela(dadosProdutosPagos.getDouble("totalMovProdutoParcela"));
                produtoRatear.setValorRatear(dadosProdutosPagos.getDouble("valorPago"));
            } else {
                produtoRatear.setValorMovProdutoParcela(produtosValores.get(dadosProdutosPagos.getInt("movprodcodigo")) + Uteis.arredondarForcando2CasasDecimais(dadosProdutosPagos.getDouble("multajuros")));
                produtoRatear.setValorRatear(produtosValores.get(dadosProdutosPagos.getInt("movprodcodigo"))+ Uteis.arredondarForcando2CasasDecimais(dadosProdutosPagos.getDouble("multajuros")));
            }
            produtoRatear.setMultaJuros(Uteis.arredondarForcando2CasasDecimais(dadosProdutosPagos.getDouble("multajuros")));

            if (produtoRatear.getCodigoPessoa() > 0) {
                produtoRatear.setNomePessoa(dadosProdutosPagos.getString("nome"));
            } else if(!UteisValidacao.emptyString(dadosProdutosPagos.getString("tipocomprador")) && dadosProdutosPagos.getString("tipocomprador").equals("CN")) {
                produtoRatear.setNomePessoa(dadosProdutosPagos.getString("nomeComprador")); // Neste caso a venda foi feita para um consumidor
            } else {
                produtoRatear.setNomePessoa("CONSUMIDOR"); // Neste caso a venda foi feita para um consumidor
            }

            if ((produtoRatear.getTipoProduto().equals("DI"))
                    || (produtoRatear.getTipoProduto().equals("AA"))) {
                /* Pesquisar o código do movProduto para os produtos do tipo "Diária" e "Aula Avulsa",
                 *  este código será utilizado para pesquisar a modalidade que a Diária ou a Aula Avulsa foi vendida, para então realizar o rateio.
                 */
                sql.delete(0, sql.length());
                sql.append("select distinct mov.codigo,mod.nome as nomemodalidade,diaria.codigo codigoMovProduto ");
                sql.append("from movProduto mov ");
                sql.append("inner join movProdutoParcela movParc on movParc.movproduto = mov.codigo ");
                sql.append("inner join movParcela parc on movParc.movparcela = parc.codigo ");
                if(produtoRatear.getCodigoPessoa() > 0){
                    sql.append(" and parc.pessoa = ").append(produtoRatear.getCodigoPessoa()).append("\n");
                }
                sql.append(" inner join pagamentomovparcela pgtoParc on pgtoParc.movparcela = movParc.movparcela ");
                sql.append(" inner join aulaavulsadiaria  diaria on diaria.codigo = parc.aulaavulsadiaria left join modalidade mod on mod.codigo = diaria.modalidade ");
                sql.append(" where pgtoParc.movpagamento = ");
                sql.append(codigoMovPagamento);
                sql.append("and mov.produto = ");
                sql.append(produtoRatear.getCodigoProduto());
                Statement stm3 = con.createStatement();
                ResultSet dadosMovProduto = stm3.executeQuery(sql.toString());
                if (dadosMovProduto.next()) {
                    produtoRatear.setCodigoMovProdutoAulaAvulsaEDiaria(dadosMovProduto.getInt("codigo"));
                    produtoRatear.setModalidadeDiaria(dadosMovProduto.getString("nomemodalidade"));
                }
            }
            listaProdutoRatear.add(produtoRatear);
        }

        return listaProdutoRatear;

    }
    public static StringBuilder consultaCompetenciaFaturamentoPaginada(int anoReferencia, String mesAnoReferencia, Date dataIni, Date dataFim, int empresa, boolean comissao, TipoRelatorioDF tipoRelatorioDF, boolean usarValorFaturado, Date dataContratosLancadosAPartir, int offset, int limit ) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select mov.*, prod.tipoproduto, prod.categoriaproduto, prod.descricao as descprod, pess.nome,pess.codigo as codigoPessoa, coalesce(mov.descricaomovprodutomodalidade, '') as descmovpmodalidade, emp.nome as nomeEmpresa \n");
        if (comissao) {
            if(usarValorFaturado){
                sql.append(",valorFaturado                                      AS valor\n");
            }else{
                sql.append(",totalfinal                                      AS valor\n");
            }

            sql.append(",''                                              AS pagDesc\n");
            sql.append(",'|' || mov.codigo || ',' || prod.tipoProduto || ',' || coalesce(contrato,0) || ',' || totalfinal AS produtospagos\n");
            sql.append(",null AS datacompesancao\n");
            sql.append(",0  AS recibopagamento\n");
        }
        sql.append(" from movProduto mov \n");
        sql.append(" left join Pessoa pess on pess.codigo = mov.pessoa \n");
        sql.append(" inner join Produto prod on prod.codigo = mov.produto \n");
        sql.append(" left join Empresa emp on emp.codigo = mov.empresa \n");
        sql.append(" where (prod.tipoProduto not in (").append(MovProduto.PRODUTOS_IGNORADOS).append(") \n"); // ver comentario em MovProduto
        if(!comissao) {
            sql.append(" or (prod.tipoProduto = '").append(TipoProduto.CHEQUE_DEVOLVIDO.getCodigo()).append("') \n"); // ver comentario em MovProduto
        }
        sql.append(")");
        sql.append(" and mov.movpagamentocc is null \n"); // isso para não considerar produtos de pagamento de Débito em conta
        if (usarValorFaturado) {
            sql.append(" and mov.valorFaturado > 0 \n");
        } else {
            sql.append(" and mov.totalFinal > 0 \n");
        }
        if(comissao && tipoRelatorioDF.equals(TipoRelatorioDF.FATURAMENTO)){
            sql.append(" and mov.descricao not like 'PLANO TRANSFERIDO%' \n"); // isso para não considerar produtos de pagamento de Débito em conta
        }
        if(dataIni == null && dataFim == null) {
            sql.append(" and mov.anoReferencia = ").append(anoReferencia).append("\n");
            sql.append(" and mov.mesReferencia = '").append(mesAnoReferencia).append("' \n");
        } else {
            sql.append(" and mov.datalancamento >= '").append(Uteis.getDateTime(dataIni, 0, 0, 0)).append("' \n");
            sql.append(" and mov.datalancamento <= '").append(Uteis.getDateTime(dataFim, 23, 59, 59)).append("' \n");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" and mov.empresa = ").append(empresa).append("\n");
        }
        if (tipoRelatorioDF.equals(TipoRelatorioDF.COMPETENCIA_QUITADA)) {
            sql.append(" and mov.situacao = 'PG' \n");
        } else if (tipoRelatorioDF.equals(TipoRelatorioDF.COMPETENCIA_NAO_QUITADA)) {
            sql.append(" and mov.situacao = 'EA' \n");
        } else {
            if(!usarValorFaturado)
                sql.append(" and mov.situacao != 'CA' \n");
        }

        if(dataContratosLancadosAPartir != null) {
            sql.append(" AND EXISTS (");
            sql.append("   SELECT 1 FROM contrato c ");
            sql.append("   WHERE c.codigo = mov.contrato ");
            sql.append("   AND c.datalancamento >= '").append(Uteis.getDataJDBC(dataContratosLancadosAPartir)).append("' ");
            sql.append(" ) ");

        }
        sql.append(" order by mov.codigo ");
        sql.append(" LIMIT ").append(limit).append(" OFFSET ").append(offset);
        return sql;
    }


    public static StringBuilder consultaCompetenciaFaturamento(int anoReferencia, String mesAnoReferencia, Date dataIni, Date dataFim, int empresa, boolean comissao, TipoRelatorioDF tipoRelatorioDF, boolean usarValorFaturado, Date dataContratosLancadosAPartir) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select mov.*, prod.tipoproduto, prod.categoriaproduto, prod.descricao as descprod, pess.nome,pess.codigo as codigoPessoa, coalesce(mov.descricaomovprodutomodalidade, '') as descmovpmodalidade, emp.nome as nomeEmpresa \n");
        if (comissao) {
            if(usarValorFaturado){
                sql.append(",valorFaturado                                      AS valor\n");
            }else{
                sql.append(",totalfinal                                      AS valor\n");
            }

            sql.append(",''                                              AS pagDesc\n");
            sql.append(",'|' || mov.codigo || ',' || prod.tipoProduto || ',' || coalesce(contrato,0) || ',' || totalfinal AS produtospagos\n");
            sql.append(",null AS datacompesancao\n");
            sql.append(",0  AS recibopagamento\n");
        }
        sql.append(" from movProduto mov \n");
        sql.append(" left join Pessoa pess on pess.codigo = mov.pessoa \n");
        sql.append(" inner join Produto prod on prod.codigo = mov.produto \n");
        sql.append(" left join Empresa emp on emp.codigo = mov.empresa \n");
        sql.append(" where (prod.tipoProduto not in (").append(MovProduto.PRODUTOS_IGNORADOS).append(") \n"); // ver comentario em MovProduto
        if(!comissao) {
            sql.append(" or (prod.tipoProduto = '").append(TipoProduto.CHEQUE_DEVOLVIDO.getCodigo()).append("') \n"); // ver comentario em MovProduto
        }
        sql.append(")");
        sql.append(" and mov.movpagamentocc is null \n"); // isso para não considerar produtos de pagamento de Débito em conta
        if (usarValorFaturado) {
            sql.append(" and mov.valorFaturado > 0 \n");
        } else {
            sql.append(" and mov.totalFinal > 0 \n");
        }
        if(comissao && tipoRelatorioDF.equals(TipoRelatorioDF.FATURAMENTO)){
           sql.append(" and mov.descricao not like 'PLANO TRANSFERIDO%' \n"); // isso para não considerar produtos de pagamento de Débito em conta
        }
        if(dataIni == null && dataFim == null) {
            sql.append(" and mov.anoReferencia = ").append(anoReferencia).append("\n");
            sql.append(" and mov.mesReferencia = '").append(mesAnoReferencia).append("' \n");
        } else {
            sql.append(" and mov.datalancamento >= '").append(Uteis.getDateTime(dataIni, 0, 0, 0)).append("' \n");
            sql.append(" and mov.datalancamento <= '").append(Uteis.getDateTime(dataFim, 23, 59, 59)).append("' \n");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" and mov.empresa = ").append(empresa).append("\n");
        }
        if (tipoRelatorioDF.equals(TipoRelatorioDF.COMPETENCIA_QUITADA)) {
            sql.append(" and mov.situacao = 'PG' \n");
        } else if (tipoRelatorioDF.equals(TipoRelatorioDF.COMPETENCIA_NAO_QUITADA)) {
            sql.append(" and mov.situacao = 'EA' \n");
        } else {
            if(!usarValorFaturado)
                sql.append(" and mov.situacao != 'CA' \n");
        }

        if(dataContratosLancadosAPartir != null) {
            sql.append(" AND EXISTS (");
            sql.append("   SELECT 1 FROM contrato c ");
            sql.append("   WHERE c.codigo = mov.contrato ");
            sql.append("   AND c.datalancamento >= '").append(Uteis.getDataJDBC(dataContratosLancadosAPartir)).append("' ");
            sql.append(" ) ");

        }
        
        sql.append(" order by mov.codigo ");
        return sql;
    }

    private List<LancamentoDF> consultarPagtosDiferenteDeChequeECartaoCredito(MesProcessar mesProcessar, int empresa) throws Exception {
        /* Consultar os pagamentos cuja forma de pagamento seja diferente de cheque e cartão de Crédito.
         * Neste caso a fonte de dados de pesquisa será a tabela "MovPagamento"
         */
        Date dataIni = Uteis.getDateTime(mesProcessar.getDataIni().getTime(), 0, 0, 0);
        Date dataFim = Uteis.getDateTime(mesProcessar.getDataFim().getTime(), 23, 59, 59);
        StringBuilder sql = consultaPagamentosDiferentesDeChequeECartaoCredito(empresa, dataIni,
                dataFim, false, null, false, null,false,null, filtroContas, null, null);
        Statement stm = con.createStatement();
        ResultSet dadosMovPagamento = stm.executeQuery(sql.toString());

        List<LancamentoDF> listaLancamentosDF = new ArrayList<LancamentoDF>();
        GenericoAtributosVO codigosProdutosCredito = consultarCodigoCategoriaProdutoCredito(con);
        while (dadosMovPagamento.next()) {
//            System.out.println(dadosMovPagamento.getDouble("valor"));
            // Pesquisar os produtos que o pagamento pagou.
     //       List<ProdutoRatear> listaProdutoRatear = pesquisarProdutosDoPagamento(dadosMovPagamento.getInt("CodigoMovPagamento"), dadosMovPagamento.getString("produtospagos"), this.con, null);
            List<ProdutoRatear> listaProdutoRatear = pesquisarProdutosDoPagamento(dadosMovPagamento.getInt("CodigoMovPagamento"), dadosMovPagamento.getString("produtospagos")+dadosMovPagamento.getString("produtospagoscancelados"), this.con, null);
//            adicionarMapa(dadosMovPagamento.getString("nomepagador"), dadosMovPagamento.getDouble("valor"), "consulta");
            Integer conta = null;
            try {
                conta = dadosMovPagamento.getInt("conta");
            }catch (Exception e){

            }
            if (!listaProdutoRatear.isEmpty()){
	            dividirValorPagamentoPorContrato(listaLancamentosDF,
	                    listaProdutoRatear,
	                    dadosMovPagamento.getDouble("valor"),
	                    dadosMovPagamento.getInt("recibopagamento"),
	                    TipoFormaPagto.AVISTA,
                        dadosMovPagamento.getString("pagDesc"),
	                    dadosMovPagamento.getInt("CodigoMovPagamento"),
                        null,
	                    dadosMovPagamento.getDate("datacompesancao"),
	                    conta,
	                    this.con, false, null, dadosMovPagamento.getString("pagDesc"), null);
            } else {
            	if (dadosMovPagamento.getInt("recibopagamento") == 0){

                    dividirMovPagamentoContaAluno(listaLancamentosDF,
            				dadosMovPagamento.getInt("CodigoMovPagamento"), 
            				dadosMovPagamento.getDouble("valor"),  TipoFormaPagto.AVISTA, 
            				dadosMovPagamento.getInt("pessoa"), dadosMovPagamento.getString("nomepagador"),
                            dadosMovPagamento.getInt("codigoempresa"), codigosProdutosCredito, "", null,
                            dadosMovPagamento.getDate("datacompesancao"),
                            conta,
                            this.con, dadosMovPagamento.getString("pagDesc"));
            	}
            }
        }
        consultarProdutosDevolucao(listaLancamentosDF, dataIni, dataFim, empresa, false, 0, "");
        return listaLancamentosDF;
    }

    private static String obterListaCodigosPlanos(List<PlanoVO> planosFiltrar) {
        String codigoPlanos = "";
        if (!UteisValidacao.emptyList(planosFiltrar)) {
            codigoPlanos = Uteis.retornarCodigos(planosFiltrar);
        }
        return codigoPlanos;
    }

    public static StringBuilder consultaPagamentosDiferentesDeChequeECartaoCredito(int empresa,
                                                                                   Date dataIni, Date dataFim, boolean ehComissao,
                                                                                   Date dataReceb, boolean ehGestaoNotas,
                                                                                   Integer codPessoa,  boolean familia,
                                                                                   ClienteTitularDependenteVO clienteTitularDependenteVO,
                                                                                   String filtroContas, List<PlanoVO> planosFiltrar, Date dataContratoLancamentoAPartir) throws Exception {

        String codPlanos = obterListaCodigosPlanos(planosFiltrar);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n" +
                "  movPag.pessoa,\n" +
                "  movpag.nomepagador,\n" +
                "  movPag.produtospagos,\n" +
                "  movPag.codigo                                                            AS codigoMovPagamento,\n" +
                "  movPag.empresa                                                            AS codigoempresa,\n" +
                "  movPag.valor,\n" +
                "  movPag.recibopagamento,\n" +
                "  formaPag.descricao                                                       AS pagDesc\n");
        if (ehComissao) {
            sql.append("  ,movpag.datalancamento\n");
            sql.append("  ,usu.colaborador                                                  AS atendente\n");
        }
        if (ehComissao || ehGestaoNotas) {
            sql.append(" ,'' as  produtospagoscancelados\n");
        } else {
            sql.append(" ,movPag.produtospagoscancelados\n");
        }
        if(!UteisValidacao.emptyString(filtroContas)){
            sql.append("  ,mc.conta\n");
        }
        sql.append("  ,movpag.datapagamento as datacompesancao\n");
        if (ehGestaoNotas) {
            if (familia){
               sql.append(" ,familia.clienteTitular \n");
            }
            sql.append("  ,formaPag.codigo AS codformapagamento\n");
            sql.append("  ,nota.codigo AS codigonotafiscal\n");
            sql.append("  ,nfse.codigo AS nfse\n");
            sql.append("  ,nfse.sequencialfamilia\n");
            sql.append("  ,nfse.rps \n");
            sql.append("  ,dataPagamento as dataReferenciaItem \n");
            sql.append("  ,nfse.situacaoNotaFiscal as situacaonfse \n");
            sql.append("  ,nfse.valor as valornfse \n");
            sql.append("  ,nfse.dataenvio as dataenvionfse \n");
            sql.append("  ,nfse.dataemissao as dataemissaonfse \n");
            sql.append("  ,nfse.nrnotamanual AS nrnotamanual\n");
            sql.append("  ,cli.codigo as codcliente\n");
            sql.append("  ,pe.cfp as cpf");
            sql.append("  ,cli.matricula as matriculacliente\n");
            sql.append("  ,col.codigo as codcolaborador\n");
            sql.append("  ,pes.cfp as cpfcolaborador\n");
            sql.append("  ,usuario_logado.nome as nome_usuario_logado\n");
            sql.append("  ,(SELECT exists( \n" +
                    "      SELECT\n" +
                    "        codigo\n" +
                    "      FROM nfseemitida\n" +
                    "      WHERE movpagamento IN\n" +
                    "            (SELECT\n" +
                    "               mpag.codigo\n" +
                    "             FROM movpagamento mpag \n" +
                    "             WHERE mpag.codigo = movPag.codigo))) AS emitidaAntes\n");
        }
        sql.append("from MovPagamento movPag  \n");
        if(!UteisValidacao.emptyString(filtroContas)){
            sql.append(" left join movconta mc on movpag.movconta = mc.codigo \n");
        }
        sql.append(" inner join formaPagamento formaPag on formaPag.codigo = movPag.formapagamento  \n");
        if (ehComissao) {
            sql.append("inner join recibopagamento rp on movpag.recibopagamento = rp.codigo\n");
            sql.append("INNER JOIN usuario usu on rp.responsavellancamento = usu.codigo \n");
        }
        if (ehGestaoNotas) {
            sql.append(" LEFT JOIN nfseemitida nfse ON (nfse.recibopagamento = movPag.recibopagamento OR nfse.movpagamento = movPag.codigo)\n");
            sql.append(" LEFT JOIN notafiscal nota on (nota.nfseemitida = nfse.codigo or (nfse.notafamilia and nota.sequencialfamilia = nfse.sequencialfamilia)) \n");
            sql.append(" LEFT JOIN cliente cli ON movPag.pessoa = cli.pessoa\n");
            sql.append(" LEFT JOIN colaborador col ON movPag.pessoa = col.pessoa\n");
            sql.append(" LEFT JOIN recibopagamento recibo ON movPag.recibopagamento = recibo.codigo\n");
            sql.append(" LEFT JOIN pessoa pe ON cli.pessoa = pe.codigo\n");
            sql.append(" LEFT JOIN pessoa pes ON col.pessoa = pes.codigo\n");
            sql.append(" LEFT JOIN usuario usuario_logado ON usuario_logado.codigo = nota.usuario\n");

            if (familia){
                sql.append(" INNER JOIN clientetitulardependente familia ON familia.cliente = cli.codigo \n");
            }
        }

        if (!UteisValidacao.emptyString(codPlanos) || dataContratoLancamentoAPartir != null) {
            sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = movPag.codigo \n");
            sql.append(" INNER JOIN movparcela par ON par.codigo = pmp.movparcela \n");
            if (UteisValidacao.emptyString(codPlanos) && dataContratoLancamentoAPartir != null) {
                sql.append(" INNER JOIN contrato con ON con.codigo = par.contrato and con.datalancamento >= '").append(Uteis.getDataHoraJDBC(dataContratoLancamentoAPartir, "00:00:00")).append("'\n");
            } else {
                sql.append(" INNER JOIN contrato con ON con.codigo = par.contrato and con.plano in (").append(codPlanos).append(") \n");
            }
        }
        
        sql.append("where (formaPag.tipoformapagamento not in ('CA','CH','CC'))\n");
        sql.append(" and (movPag.recibopagamento is not null or movPag.credito = true)\n");
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" and movPag.empresa = ");
            sql.append(empresa);
        }
        if (ehGestaoNotas){
            sql.append( " and coalesce(movPag.valor,0) > 0  \n" );
        }
        sql.append(" and ");
        if (ehGestaoNotas) {
            sql.append(" ( ");
        }
        sql.append(" ((dataPagamento >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"))
				        .append("') and (dataPagamento <= '")
				        .append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))\n");
        if (dataReceb != null) {
            sql.append(" and (movPag.datalancamento >= '").append(Uteis.getDataJDBC(dataReceb)).append("')\n");
        }
        if (ehGestaoNotas) {
            sql.append("OR (formaPag.tipoformapagamento = 'CC'\n");
            sql.append("    AND (recibo.data >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00")).append("'\n");
            sql.append("          and recibo.data <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))\n");
        }
        if (ehGestaoNotas) {
            if (codPessoa != null && !(UtilReflection.objetoMaiorQueZero(clienteTitularDependenteVO, "getCodigo()"))) {
                sql.append(" ) and movPag.pessoa = ").append(codPessoa).append("\n");
            }else{
               sql.append(") \n");
            }

        }
        if ((ehGestaoNotas) && (familia)){
            if ((UtilReflection.objetoMaiorQueZero(clienteTitularDependenteVO, "getCodigo()"))) {
                if (clienteTitularDependenteVO.getCodigo().equals(clienteTitularDependenteVO.getClienteTitular().getCodigo())){
                    // titular
                    sql.append(" AND familia.clienteTitular = ").append(clienteTitularDependenteVO.getCodigo());
                }else{
                    // dependente
                    sql.append(" AND familia.cliente = ").append(clienteTitularDependenteVO.getClienteVO().getCodigo());
                }
            }
        }
        if(!UteisValidacao.emptyString(filtroContas) && !filtroContas.equals("fluxo_caixa")){
            sql.append("and (mc.conta in (").append(filtroContas).append(")");
            sql.append(filtroContas.contains("-1") ? " or mc.conta is null " : "");
            sql.append(")");
        }
        
        sql.append("group by movPag.pessoa,movPag.nomepagador, movPag.codigo,movPag.empresa, movPag.valor, movPag.recibopagamento, movPag.produtospagos, formaPag.descricao,movpag.datapagamento ");
        if(!UteisValidacao.emptyString(filtroContas)){
            sql.append(" ,mc.conta ");
        }
        if (ehComissao) {
            sql.append("  ,movpag.datalancamento\n");
            sql.append("  ,movpag.datapagamento\n");
            sql.append("  ,usu.colaborador\n");
        }
        if (ehGestaoNotas) {
            sql.append("  , formaPag.codigo\n");
            sql.append("  , nota.codigo\n");
            sql.append("  , nfse.codigo\n");
            sql.append("  , nfse.sequencialfamilia\n");
            sql.append("  , nfse.rps\n");
            sql.append("  , dataPagamento \n");
            sql.append("  , nfse.situacaoNotaFiscal \n");
            sql.append("  , nfse.valor \n");
            sql.append("  , nfse.dataenvio \n");
            sql.append("  , nfse.dataemissao \n");
            sql.append("  , nfse.nrnotamanual\n");
            sql.append("  , cli.codigo\n");
            sql.append("  , cli.matricula\n");
            sql.append("  , recibo.contrato\n");
            sql.append("  , pe.cfp\n");
            sql.append("  , col.codigo\n");
            sql.append("  , pes.cfp\n");
            sql.append("  , usuario_logado.nome\n");
            if ((ehGestaoNotas) && (familia)){
                sql.append(" ,familia.clienteTitular \n");
            }

        }
        return sql;
    }

    public static void dividirMovPagamentoContaAluno(
            List<LancamentoDF> listaLancamentosDF, int codigoPagamento, double valorPagamento,
            TipoFormaPagto tipoFormaPagto, int pessoa, String nomePessoa, int empresa,
            GenericoAtributosVO codigosProdutosCredito, final String responsavelPagamento, Integer consultor,
            Date dia, Integer conta,
            Connection con) throws Exception {
        dividirMovPagamentoContaAluno(listaLancamentosDF, codigoPagamento, valorPagamento, tipoFormaPagto, pessoa, nomePessoa, empresa, codigosProdutosCredito, responsavelPagamento, consultor, dia, conta,con, null);
    }

        public static void dividirMovPagamentoContaAluno(
			List<LancamentoDF> listaLancamentosDF, int codigoPagamento, double valorPagamento,
			TipoFormaPagto tipoFormaPagto, int pessoa, String nomePessoa, int empresa,
            GenericoAtributosVO codigosProdutosCredito, final String responsavelPagamento, Integer consultor,
            Date dia, Integer conta,
            Connection con, String descricaoFormaPagamento) throws Exception {
    	
        ProdutoRatear produtoRatear = new ProdutoRatear();
        produtoRatear.setCodigoProduto((Integer)codigosProdutosCredito.obterAtributo("codigoProdutoCredito"));
        produtoRatear.setValorTotalContrato(0.0);
        produtoRatear.setTipoProduto("CC");
        produtoRatear.setDescricaoProduto(MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO);
        produtoRatear.setCodigoCategoriaProduto((Integer)codigosProdutosCredito.obterAtributo("categoriaProdutoCredito"));
        produtoRatear.setContrato(0);
        produtoRatear.setCodigoPessoa(pessoa);
        produtoRatear.setRecibo(0);
        produtoRatear.setCodigoMovProduto(0);
       	produtoRatear.setValorMovProdutoParcela(valorPagamento);
        produtoRatear.setValorRatear(valorPagamento);
        produtoRatear.setNomePessoa(nomePessoa);
        produtoRatear.setEmpresa(empresa);

        StringBuilder sql = new StringBuilder();
        sql.delete(0, sql.length());
        sql.append("select   vin.colaborador as consultor ");
        sql.append("from movpagamento pag \n");
        sql.append("left join cliente cli on pag.pessoa = cli.pessoa\n");
        sql.append("left join vinculo vin on (cli.codigo = vin.cliente and vin.tipovinculo = 'CO')\n");
        sql.append("where pag.codigo = ");
        sql.append(codigoPagamento);

        Statement stm2 = con.createStatement();
        ResultSet dadosVinculo = stm2.executeQuery(sql.toString());

        if(dadosVinculo.next()) {
           produtoRatear.setCodigoConsultor(dadosVinculo.getInt("consultor"));
        }
        if(!UteisValidacao.emptyNumber(consultor) && !consultor.equals(produtoRatear.getCodigoConsultor())){
            return;
        }
        
    	LancamentoDF lancamento = new LancamentoDF();
        lancamento.setRecibo(0);
        lancamento.setMovPagamento(codigoPagamento);
        lancamento.setTipoFormaPagto(tipoFormaPagto);
        lancamento.setDescricaoFormaPagamento(descricaoFormaPagamento);
        lancamento.setCodigoPessoa(pessoa);
        lancamento.setNomePessoa(nomePessoa);
        lancamento.setTotalMovProdutoParcela(valorPagamento);
        lancamento.setDescricaoLancamento(MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO);
        lancamento.setResponsavelLancamento(responsavelPagamento);
        lancamento.setResponsavelPagamento(responsavelPagamento);
        lancamento.setContrato(0);
        lancamento.setDiaConta(dia);
        lancamento.setConta(conta);
        lancamento.setTipoProduto("CC");
        lancamento.getListaProdutoRatear().add(produtoRatear);
        lancamento.setMovProduto(produtoRatear.getCodigoMovProdutoAulaAvulsaEDiaria());
        lancamento.setCodProduto(produtoRatear.getCodigoProduto());
        lancamento.setTotalProdutosDiferenteDePlano(produtoRatear.getValorMovProdutoParcela());
        lancamento.setEmpresa(empresa);
        

        
        lancamento.setQtdePgtosParaPagarParcela(1);
    
        lancamento.setValorLancamento(valorPagamento);
        
        listaLancamentosDF.add(lancamento);

		
	}

	private void consultarProdutosDevolucao(List<LancamentoDF> listaLancamentosDF, Date inicio, Date fim, int empresa, Boolean competencia, int anoReferencia, String mesAnoReferencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.datalancamento, mp.codigo AS movProduto, p.codigo AS produto, p.tipoproduto, p.categoriaproduto, ");
        sql.append("mp.totalfinal AS valor, mp.contrato, pe.codigo AS pessoa, pe.nome, mp.descricao, mp.empresa ");
        sql.append("FROM movproduto mp, produto p, pessoa pe ");
        sql.append("WHERE mp.produto = p.codigo ");
        sql.append("AND pe.codigo = mp.pessoa ");
        sql.append("AND (p.tipoproduto LIKE 'DV' OR p.tipoproduto LIKE 'DC')");
        if (competencia) {
            sql.append(" and mp.anoReferencia = ").append(anoReferencia);
            sql.append(" and mp.mesReferencia = '").append(mesAnoReferencia).append("' ");
        } else {
            sql.append("AND mp.datalancamento BETWEEN '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(inicio), "00:00:00")).append("' AND '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(fim), "23:59:59")).append("' ");
        }
        sql.append("AND mp.empresa = ").append(empresa);
        sql.append(" AND mp.situacao = 'PG' "); //os cancelados tiveram as contas no financeiro excluídas e não devem ser apresentados
        sql.append(" AND mp.codigo NOT IN (SELECT movproduto FROM movconta WHERE movproduto is not null)");

        Statement stm = con.createStatement();
        ResultSet dados = stm.executeQuery(sql.toString());
        while (dados.next()) {
            LancamentoDF devolucao = new LancamentoDF();
            devolucao.setCodigoPessoa(dados.getInt("pessoa"));
            devolucao.setDescricaoLancamento(dados.getString("descricao"));
            devolucao.setContrato(dados.getInt("contrato"));
            devolucao.setMovProduto(dados.getInt("movProduto"));
            devolucao.setCodProduto(dados.getInt("produto"));
            devolucao.setEmpresa(dados.getInt("empresa"));
            devolucao.setTipoFormaPagto(TipoFormaPagto.AVISTA);
            devolucao.setTotalMovProdutoParcela(dados.getDouble("valor"));
            devolucao.setNomePessoa(dados.getString("nome"));
            devolucao.setValorLancamento(dados.getDouble("valor"));
            devolucao.setDiaConta(dados.getDate("datalancamento"));
            ProdutoRatear prod = new ProdutoRatear();
            prod.setCodigoCategoriaProduto(dados.getInt("categoriaproduto"));
            prod.setCodigoPessoa(dados.getInt("pessoa"));
            prod.setCodigoProduto(dados.getInt("produto"));
            prod.setContrato(dados.getInt("contrato"));
            prod.setTipoProduto(dados.getString("tipoproduto"));
            prod.setNomePessoa(dados.getString("nome"));
            prod.setValorMovProdutoParcela(dados.getDouble("valor"));
            prod.setValorRatear(dados.getDouble("valor"));
            prod.setDescricaoProduto(dados.getString("descricao"));
            devolucao.getListaProdutoRatear().add(prod);

            listaLancamentosDF.add(devolucao);
        }
    }

    private List<LancamentoDF> consultarPagamentos(MesProcessar mesProcessar, int empresa) throws Exception {
        Date dataIni = Uteis.getDateTime(mesProcessar.getDataIni().getTime(), 0, 0, 0);
        Date dataFim = Uteis.getDateTime(mesProcessar.getDataFim().getTime(), 23, 59, 59);
        StringBuilder sql = consultaFaturamentoRecebido(empresa, dataIni, dataFim, null, null,false, null, false, null, null, null,false, null,null,null, null, true, null);
        Statement stm = con.createStatement();
        ResultSet dadosMovPagamento = stm.executeQuery(sql.toString());

        TipoFormaPagto tp = TipoFormaPagto.AVISTA;
        List<LancamentoDF> listaLancamentosDF = new ArrayList<LancamentoDF>();
        GenericoAtributosVO codigosProdutosCredito = consultarCodigoCategoriaProdutoCredito(con);
        while (dadosMovPagamento.next()) {

            // Pesquisar os produtos que o pagamento pagou.
            if(dadosMovPagamento.getInt("recibopagamento") > 0){

                Boolean arredondarPraCima = false;
                //verificar se o valor booleano é para arredondar em 2 casas decimais ou não pra ficar de acordo com or ecibo
                try {
                     arredondarPraCima = arredondarPraCima(dadosMovPagamento.getDouble("valor"));
                } catch (Exception e){
                    //ignore
                }

                List<ProdutoRatear> listaProdutoRatear = pesquisarProdutosDoPagamento(dadosMovPagamento.getInt("CodigoMovPagamento"), dadosMovPagamento.getString("produtospagos") + dadosMovPagamento.getString("produtospagoscancelados"), this.con, null);
                dividirValorPagamentoPorContrato(listaLancamentosDF,
                        listaProdutoRatear,
                        arredondarPraCima ? Uteis.arredondarForcando2CasasDecimais(dadosMovPagamento.getDouble("valor")) : dadosMovPagamento.getDouble("valor"),
                        dadosMovPagamento.getInt("recibopagamento"),
                        tp.getTipoFormaPagtoSigla((dadosMovPagamento.getString("tipoformapagamento"))),
                        dadosMovPagamento.getString("pagDesc"),
                        dadosMovPagamento.getInt("CodigoMovPagamento"),
                        null,
                        dadosMovPagamento.getDate("datalancamento"),null,
                        this.con, false, null, null);
            } else{
                dividirMovPagamentoContaAluno(listaLancamentosDF, 
            				dadosMovPagamento.getInt("CodigoMovPagamento"), 
            				dadosMovPagamento.getDouble("valor"),   tp.getTipoFormaPagtoSigla((dadosMovPagamento.getString("tipoformapagamento"))), 
            				dadosMovPagamento.getInt("pessoa"), dadosMovPagamento.getString("nomepagador"),
                        dadosMovPagamento.getInt("codigoempresa"),codigosProdutosCredito,"", null,
                        null, null,
                        this.con);
            }
        }
        return listaLancamentosDF;
    }

    public Boolean arredondarPraCima(Double valor) {
        String[] split = String.valueOf(valor).replace(".", ",").split(",");
        if (split[1].length() > 2) {
            if (split[1].substring(2, 4).equals("99")) ;
            return true;
        }
        return false;
    }

    public static StringBuilder consultaFaturamentoRecebido(int empresa, Date dataIni,
                                                            Date dataFim, Date dataReceb, Date dataContratosLancadosAPartir, boolean comissao,
                                                            String tipo, boolean ehGestaoNotas,
                                                            Integer operador, Integer consultor, Integer codPessoa,  boolean familia, ClienteTitularDependenteVO clienteTitularDependenteVO, Boolean retiraEdicaoPagamento,
                                                            Boolean retirarRecebiveisComPendencia, List<PlanoVO> planosFiltrar) throws Exception {
        return consultaFaturamentoRecebido(empresa, dataIni, dataFim, dataReceb, dataContratosLancadosAPartir, comissao, tipo, ehGestaoNotas, operador, consultor, codPessoa,  familia, clienteTitularDependenteVO, retiraEdicaoPagamento, retirarRecebiveisComPendencia, planosFiltrar, false, null);
    }

    public static StringBuilder consultaFaturamentoRecebido(int empresa, Date dataIni,
                                                            Date dataFim, Date dataReceb, Date dataContratosLancadosAPartir, boolean comissao,
                                                            String tipo, boolean ehGestaoNotas,
                                                            Integer operador, Integer consultor, Integer codPessoa,  boolean familia, ClienteTitularDependenteVO clienteTitularDependenteVO, Boolean retiraEdicaoPagamento,
                                                            Boolean retirarRecebiveisComPendencia, List<PlanoVO> planosFiltrar, boolean isIgnorarFiltroTipo, Map<String, Integer> filtroFaturamentoRecebido) throws Exception {

        String codPlanos = obterListaCodigosPlanos(planosFiltrar);

        //Atenção! ao alterar essa sql, a sql do metodo consultaFaturamentoRecebidoCC() dever ser avaliada, pois existe a possibilidade de união entres essas
        StringBuilder sql = new StringBuilder();
        sql.append("select movPag.pessoa,movPag.nomepagador,movPag.empresa as codigoempresa, movPag.produtospagos, movPag.codigo CodigoMovPagamento,usu.codigo as codresponsavelpagamento, usu.nome as responsavelpagamento,");
        sql.append("movPag.valortotal valor,");
        sql.append("movPag.recibopagamento, movPag.datalancamento, movPag.formapagamento, formaPag.tipoformapagamento,formaPag.descricao pagDesc ");
        if ((ehGestaoNotas) && (familia)){
            sql.append(" ,familia.clienteTitular \n");
        }
        if (comissao || ehGestaoNotas) {
            sql.append(",movPag.nomepagador\n" +
                    "  ,movPag.datalancamento\n" +
                    "  ,movPag.datapagamento AS datacompesancao\n" +
                    "  ,formaPag.descricao AS formapagamento\n"
            );
        }
        if (comissao || ehGestaoNotas) {
            sql.append("  ,'' as produtospagoscancelados\n");
        } else {
            sql.append(" ,movPag.produtospagoscancelados\n");
        }
        if (comissao) {
            sql.append("  ,usu.colaborador                                                  AS atendente\n");
        }
        if (ehGestaoNotas) {
            sql.append("  ,pe.datanasc as dataNascimento\n");
            sql.append("  ,pe.nomemae as nomeMae\n");
            sql.append("  ,pe.nomepai as nomePai\n");
            sql.append("  ,nfce.id_nfce \n");
            sql.append("  ,nfce.codigo as codNFCe \n");
            sql.append("  ,(SELECT\n" +
                    "  exists(\n" +
                    "      (SELECT\n" +
                    "        codigo\n" +
                    "      FROM notafiscalconsumidoreletronica\n" +
                    "      WHERE recibopagamento is not null and recibopagamento = movPag.recibopagamento)\n" +
                    "      UNION\n" +
                    "\t(SELECT\n" +
                    "        codigo\n" +
                    "      FROM notafiscalconsumidoreletronica\n" +
                    "      WHERE movpagamento is not null and movpagamento = movPag.recibopagamento)\n" +
                    "      UNION\n" +
                    "      (SELECT\n" +
                    "        n.codigo\n" +
                    "      FROM notafiscalconsumidoreletronica n\n" +
                    "\tINNER JOIN cheque c on c.codigo = n.cheque\n" +
                    "      WHERE n.cheque is not null and c.movpagamento = movPag.codigo)\n" +
                    "\tUNION\n" +
                    "      (SELECT\n" +
                    "        n.codigo\n" +
                    "      FROM notafiscalconsumidoreletronica n\n" +
                    "\tINNER JOIN cartaocredito c on c.codigo = n.cartaocredito\n" +
                    "      WHERE n.cartaocredito is not null and c.movpagamento = movPag.codigo)\n" +
                    "      )) AS nfceEmitidaAntes\n");
            sql.append("  ,formaPag.codigo AS codformapagamento\n");
            sql.append("  ,nfse.codigo AS nfse\n");
            sql.append("  ,nfse.sequencialfamilia\n");
            sql.append("  ,nfse.rps\n");
            sql.append("  ,movPag.datalancamento as dataReferenciaItem \n");
            sql.append("  ,nfse.situacaoNotaFiscal as situacaonfse \n");
            sql.append("  ,nf.codigo as codigonotafiscal  \n");
            sql.append("  ,notaNFCe.codigo as codigonotafiscalNFCE  \n");
            sql.append("  ,nfse.valor as valornfse \n");
            sql.append("  ,nfse.dataenvio as dataenvionfse \n");
            sql.append("  ,nfse.dataemissao as dataemissaonfse \n");
            sql.append("  ,nfse.nrnotamanual AS nrnotamanual\n");
            sql.append("  ,cli.codigo as codcliente\n");
            sql.append("  ,cli.matricula as matriculacliente\n");
            sql.append("  ,pe.cfp as cpf\n");
            sql.append("  ,col.codigo as codColaborador\n");
            sql.append("  ,peC.cfp as cpfColaborador\n");
            sql.append("  ,exists(select codigo from nfseemitida  where movpagamento is not null and movpagamento = movPag.codigo) as existeDinheiroEmitido\n");
            sql.append("  ,exists(select codigo from nfseemitida  where cheque is not null and cheque in (select codigo from cheque where movpagamento = movPag.codigo)) as existeChequeEmitido\n");
            sql.append("  ,exists(select codigo from nfseemitida  where cartaocredito is not null and cartaocredito in (select codigo from cartaocredito where movpagamento = movPag.codigo)) as existeCartaoEmitido\n");
            sql.append("  ,(SELECT\n" +
                    "  exists(\n" +
                    "      SELECT\n" +
                    "        codigo\n" +
                    "      FROM nfseemitida\n" +
                    "      WHERE recibopagamento IN (SELECT\n" +
                    "                                  codigo\n" +
                    "                                FROM recibopagamento\n" +
                    "                                WHERE recibopagamento.contrato = recpag.contrato))) AS emitidaAntes\n");
        }
        sql.append("from MovPagamento movPag  \n");
        sql.append("inner join formaPagamento formaPag on formaPag.codigo = movPag.formapagamento  \n");
        if(comissao || ehGestaoNotas){
            sql.append("inner join movProdutoParcela movProdParc on movProdParc.recibopagamento = movPag.recibopagamento \n");
            sql.append("inner join MovProduto movProd on movProd.codigo = movProdParc.movproduto\n");
        } else{
            sql.append("left join movProdutoParcela movProdParc on movProdParc.recibopagamento = movPag.recibopagamento \n");
            sql.append("left join MovProduto movProd on movProd.codigo = movProdParc.movproduto\n");
        }
        if (comissao) {
            sql.append("INNER JOIN recibopagamento rp ON movpag.recibopagamento = rp.codigo\n");
            sql.append("INNER JOIN usuario usu on rp.responsavellancamento = usu.codigo \n");
        } else {
            sql.append(" INNER JOIN usuario usu ON usu.codigo = movPag.responsavelpagamento ");
        }
        if (!UteisValidacao.emptyNumber(operador)) {
            sql.append(" and usu.colaborador = ").append(operador).append("\n");
        }

        if (ehGestaoNotas ||
                (Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("categoriaSelecionada") != 0) ||
                (Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("formaPagamentoSelecionada") != 0)) {
            sql.append("LEFT JOIN cliente cli ON movPag.pessoa = cli.pessoa\n");
        }

        if (ehGestaoNotas) {
            sql.append("LEFT JOIN recibopagamento recpag ON movpag.recibopagamento = recpag.codigo\n");
            sql.append("LEFT JOIN nfseemitida nfse ON nfse.recibopagamento = movpag.recibopagamento\n");
            sql.append("LEFT JOIN notafiscal nf ON nf.nfseemitida = nfse.codigo\n");
            sql.append("LEFT JOIN pessoa pe ON cli.pessoa = pe.codigo\n");
            sql.append("LEFT JOIN colaborador col ON movPag.pessoa = col.pessoa\n");
            sql.append(" and col.empresa = movpag.empresa\n");
            sql.append("LEFT JOIN pessoa peC ON col.pessoa = peC.codigo\n");
            sql.append("LEFT JOIN notafiscalconsumidoreletronica nfce ON nfce.recibopagamento = movpag.recibopagamento\n");
            sql.append("LEFT JOIN notafiscal notaNFCe ON notaNFCe.notafiscalconsumidoreletronica = nfce.codigo\n");
            if (familia){
                sql.append(" INNER JOIN clientetitulardependente familia ON familia.cliente = cli.codigo \n");
            }
        }

        if (!UteisValidacao.emptyString(codPlanos)) {
            sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = movPag.codigo \n");
            sql.append(" INNER JOIN movparcela par ON par.codigo = pmp.movparcela \n");
            sql.append(" INNER JOIN contrato con ON con.codigo = par.contrato and con.plano in (").append(codPlanos).append(") \n");
        }

        if(!UteisValidacao.emptyNumber(consultor)){
            sql.append(" INNER JOIN cliente cli ON movPag.pessoa = cli.pessoa\n");
            sql.append(" INNER JOIN vinculo vin ON vin.cliente = cli.codigo and vin.colaborador = ").append(consultor).append(" \n");
        }

        if(tipo != null && ((!UteisValidacao.emptyString(tipo)) && !tipo.equals("'CC'"))){
            sql.append(" INNER JOIN produto p ON movProd.produto = p.codigo ");
            if(!UteisValidacao.emptyString(tipo)){
                sql.append(" AND p.tipoproduto IN (").append(tipo).append(") \n");
            }
        }
        
        if (retiraEdicaoPagamento != null) {
            if (retiraEdicaoPagamento) {
                sql.append(" left join  pagamentomovparcela pmp on pmp.movpagamento = movPag.codigo \n");
                sql.append(" left join movparcela mpar on mpar.codigo = pmp.movparcela \n");
            }
        }
        
        if (retirarRecebiveisComPendencia != null) {
            if (retirarRecebiveisComPendencia) {
                sql.append(" left join cheque ch on ch.movpagamento =  movPag.codigo \n");
                sql.append(" left join historicocheque hc on hc.cheque = ch.codigo and hc.datafim is null \n");
                sql.append(" left join movconta mc on mc.lote = hc.lote and mc.tipooperacao <> 4 \n");
                sql.append(" left join conta co on co.codigo = mc.conta \n");
                sql.append(" left join tipoconta tc on tc.codigo = co.tipoconta \n");
            }
        }

        if(Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("modalidadeSelecionada") != 0){
            sql.append(" left join contratomodalidade cm ON cm.contrato = movProd.contrato\n");
        }
        if(Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("turmaSelecionada") != 0){
            sql.append(" left join matriculaalunohorarioturma maht on maht.contrato = movProd.contrato\n");
            sql.append(" left join horarioturma ht on ht.codigo = maht.horarioturma\n");
            sql.append(" left join turma turma on turma.codigo = ht.turma\n");
        }
        if (Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("categoriaSelecionada") != 0) {
            if(tipo == null && ((UteisValidacao.emptyString(tipo)) && !tipo.equals("'CC'"))) {
                sql.append(" INNER JOIN produto p ON movProd.produto = p.codigo ");
            }
            sql.append(" left join categoriaproduto catprod on p.categoriaproduto = catprod.codigo\n");
        }

        sql.append("where (formaPag.tipoFormaPagamento <> 'CC') ");
        if(familia){
            if ((UtilReflection.objetoMaiorQueZero(clienteTitularDependenteVO, "getCodigo()"))) {
                if (clienteTitularDependenteVO.getCodigo().equals(clienteTitularDependenteVO.getClienteTitular().getCodigo())){
                    // titular
                    sql.append(" AND familia.clienteTitular = ").append(clienteTitularDependenteVO.getCodigo());
                }else{
                    // dependente
                    sql.append(" AND familia.cliente = ").append(clienteTitularDependenteVO.getClienteVO().getCodigo());
                }
            }
        }

        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" and movPag.empresa = ").append(empresa);
        }
        
        if (ehGestaoNotas) {
            sql.append(" and movProd.situacao != 'CA' ");
            if ((codPessoa != null) && (!(UtilReflection.objetoMaiorQueZero(clienteTitularDependenteVO, "getCodigo()")))){
                sql.append(" and movPag.pessoa = ").append(codPessoa).append("\n");
            }
            sql.append(" and ((movPag.depositoCC) or (movPag.credito <> 't')) ");
        }
        if (!ehGestaoNotas) {
            sql.append(" and (movPag.valor > 0) ");
        }
        if(!isIgnorarFiltroTipo) {
            if (UteisValidacao.emptyString(tipo) || !tipo.equals("'CC'")) {
                sql.append(" and movPag.recibopagamento is not null ");
            } else {
                sql.append(" and movPag.recibopagamento is null ");
            }
        }
        

        sql.append(" and ((movPag.datalancamento >= '").append(Uteis.getDataJDBC(dataIni)).append("') and (movPag.datalancamento <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
        if (dataReceb != null) {
            sql.append(" and (movPag.datalancamento >= '").append(Uteis.getDataJDBC(dataReceb)).append("') \n");
        }
        if(dataContratosLancadosAPartir != null) {
            sql.append(" AND EXISTS (");
            sql.append("   SELECT 1 FROM contrato c ");
            sql.append("   WHERE c.codigo = movProd.contrato ");
            sql.append("   AND c.datalancamento >= '").append(Uteis.getDataJDBC(dataContratosLancadosAPartir)).append("' ");
            sql.append(" ) ");

        }
        
        if (retiraEdicaoPagamento != null) {
            if (retiraEdicaoPagamento) {
                sql.append(" and(mpar.descricao not like '%EDIÇÃO DE PAGAMENTO%' or mpar.codigo is null) \n");
            }
        }

        if (retirarRecebiveisComPendencia != null) {
            if (retirarRecebiveisComPendencia) {
                sql.append(" and (formaPag.tipoFormaPagamento <> 'CH'  OR (formaPag.tipoFormaPagamento = 'CH' AND tc.comportamento not in (4,5,6))) \n");
            }
        }

        if(Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("modalidadeSelecionada") != 0){
            sql.append(" and cm.modalidade = ").append(filtroFaturamentoRecebido.get("modalidadeSelecionada")).append("\n");
        }
        if(Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("turmaSelecionada") != 0){
            sql.append(" and turma.codigo = ").append(filtroFaturamentoRecebido.get("turmaSelecionada")).append("\n");
        }
        if(Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("formaPagamentoSelecionada") != 0){
            sql.append(" and movpag.formapagamento = ").append(filtroFaturamentoRecebido.get("formaPagamentoSelecionada")).append("\n");
        }
        if(Objects.nonNull(filtroFaturamentoRecebido) && filtroFaturamentoRecebido.get("categoriaSelecionada") != 0){
            sql.append(" and cli.categoria = ").append(filtroFaturamentoRecebido.get("categoriaSelecionada")).append("\n");
        }

        sql.append("group by movPag.pessoa,movPag.nomepagador,movPag.empresa, movPag.codigo,usu.codigo, usu.nome , ");
        sql.append("movPag.valortotal,");
        sql.append(" movPag.recibopagamento, movPag.produtospagos, movPag.datalancamento, movPag.formapagamento, formaPag.tipoformapagamento,formaPag.descricao ");
        if (comissao || ehGestaoNotas) {
            sql.append(",movPag.nomepagador\n" +
                    "  ,movPag.datalancamento\n" +
                    "  ,movPag.datapagamento\n" +
                    "  ,formaPag.descricao\n"
            );
        }
        if (comissao) {
            sql.append("  , usu.colaborador\n");
        }
        if (ehGestaoNotas) {
            sql.append("  , nfce.id_nfce\n");
            sql.append("  , nfce.codigo\n");
            sql.append("  , nfse.sequencialfamilia\n");
            sql.append("  , formaPag.codigo\n");
            sql.append("  , nfse.codigo\n");
            sql.append("  , nfse.rps\n");
            sql.append("  , movPag.datalancamento \n");
            sql.append("  , nfse.situacaoNotaFiscal \n");
            sql.append("  , nf.codigo \n");
            sql.append("  , notaNFCe.codigo \n");
            sql.append("  , nfse.valor \n");
            sql.append("  , nfse.dataenvio \n");
            sql.append("  , nfse.dataemissao \n");
            sql.append("  , nfse.nrnotamanual\n");
            sql.append("  , cli.codigo\n");
            sql.append("  , cli.matricula\n");
            sql.append("  , pe.cfp\n");
            sql.append("  , recpag.contrato\n");
            sql.append("  , col.codigo\n");
            sql.append("  , peC.cfp\n");
            sql.append("  , existeDinheiroEmitido\n");
            sql.append("  , existeChequeEmitido\n");
            sql.append("  , existeCartaoEmitido\n");
            sql.append("  , pe.datanasc\n");
            sql.append("  , pe.nomemae\n");
            sql.append("  , pe.nomepai\n");
            if ((ehGestaoNotas) && (familia)){
                sql.append(" ,familia.clienteTitular \n");
            }
        }
        
        return sql;
    }

    private List<LancamentoDF> consultarPagtosEmCheque(MesProcessar mesProcessar, int empresa) throws Exception {
        /* Consultar os pagamentos realizados em cheque.
         * Neste caso a fonte de dados de pesquisa será a tabela "cheque"
         */

        Date dataIni = Uteis.getDateTime(mesProcessar.getDataIni().getTime(), 0, 0, 0);
        Date dataFim = Uteis.getDateTime(mesProcessar.getDataFim().getTime(), 23, 59, 59);

        StringBuilder sql = consultaPagamentosEmCheque(empresa, dataIni, dataFim, false,
                null, false, null,false, null, filtroContas,null, false, null, "", null, false);

        Statement stm = con.createStatement();
        ResultSet dadosPagtoCheque = stm.executeQuery(sql.toString());
        List<LancamentoDF> listaLancamentosDF = new ArrayList<LancamentoDF>();
        GenericoAtributosVO codigosProdutosCredito = consultarCodigoCategoriaProdutoCredito(con);

        while (dadosPagtoCheque.next()) {
            if (!UteisValidacao.emptyString(filtroContas) && !UteisValidacao.emptyString(dadosPagtoCheque.getString("composicao"))){
                Statement stm2 = con.createStatement();
                StringBuilder sqlComposicao = consultaPagamentosEmCheque(empresa, dataIni, dataFim, false,
                        null, false, null,false, null, filtroContas,null, false, null, dadosPagtoCheque.getString("composicao"), null, false);
                ResultSet composicaoCheque = stm2.executeQuery(sqlComposicao.toString());
                while (composicaoCheque.next()) {
                    realizarRateiosReferenteMovPagamentoCheque(composicaoCheque, listaLancamentosDF, codigosProdutosCredito);
                }
            }
            realizarRateiosReferenteMovPagamentoCheque(dadosPagtoCheque, listaLancamentosDF, codigosProdutosCredito);
        }
        return listaLancamentosDF;
    }

    private void realizarRateiosReferenteMovPagamentoCheque(ResultSet dadosPagtoCheque, List<LancamentoDF> listaLancamentosDF, GenericoAtributosVO codigosProdutosCredito) throws Exception {
        // Pesquisar os produtos que o cheque pagou.
        List<ProdutoRatear> listaProdutoRatear = new ArrayList<>();
        if(UteisValidacao.emptyString(dadosPagtoCheque.getString("produtospagos")+dadosPagtoCheque.getString("produtospagoscancelados"))){ // consultar os produtos pagos caso no cheque esteja vazio.
            ResultSet resultPagto = SuperFacadeJDBC.criarConsulta("select MovP.nomePagador, MovP.produtospagos, MovP.produtospagoscancelados, MovP.valor, MovP.formaPagamento, MovP.recibopagamento, fp.tipoformapagamento, fp.descricao, MovP.DataPagamento, p.nome "
                    + "from MovPagamento MovP "
                    + "inner join formapagamento fp on fp.codigo = MovP.formapagamento "
                    + "left join pessoa p on p.codigo = movP.pessoa "
                    + "left join cliente cli on cli.pessoa = movP.pessoa "
                    + "where MovP.codigo =" + dadosPagtoCheque.getInt("CodigoMovPagamento"), this.con);
            while (resultPagto.next()){
                listaProdutoRatear = pesquisarProdutosDoPagamento(dadosPagtoCheque.getInt("CodigoMovPagamento"), resultPagto.getString("produtospagos") + resultPagto.getString("produtospagoscancelados"), this.con, null);
            }
        }else {
            listaProdutoRatear = pesquisarProdutosDoPagamento(dadosPagtoCheque.getInt("CodigoMovPagamento"), dadosPagtoCheque.getString("produtospagos") + dadosPagtoCheque.getString("produtospagoscancelados"), this.con, null);
        }


        Integer conta = null;
        try {
            conta = dadosPagtoCheque.getInt("conta");
        }catch (Exception e){

        }

        if (!listaProdutoRatear.isEmpty()){

             dividirValorPagamentoPorContrato(listaLancamentosDF,
                     listaProdutoRatear,
                     dadosPagtoCheque.getDouble("valor"),
                     dadosPagtoCheque.getInt("recibopagamento"),
                     TipoFormaPagto.CHEQUE,
                     dadosPagtoCheque.getString("pagDesc"),
                     dadosPagtoCheque.getInt("CodigoMovPagamento"),
                     null,
                     dadosPagtoCheque.getDate("datacompesancao"),
                     conta,
                     this.con, false, null, dadosPagtoCheque.getString("pagdesc"), "");
        } else {
            if (dadosPagtoCheque.getInt("recibopagamento") == 0){
                dividirMovPagamentoContaAluno(listaLancamentosDF,
                        dadosPagtoCheque.getInt("CodigoMovPagamento"),
                        dadosPagtoCheque.getDouble("valor"),  TipoFormaPagto.CHEQUE,
                        dadosPagtoCheque.getInt("pessoa"), dadosPagtoCheque.getString("nomepagador"),
                        dadosPagtoCheque.getInt("codigoempresa"),codigosProdutosCredito,"",null,
                        dadosPagtoCheque.getDate("datacompesancao"),
                        conta, this.con, dadosPagtoCheque.getString("pagdesc"));
            }
        }
    }

    public static StringBuilder consultaPagamentosEmCheque(int empresa, Date dataIni, Date dataFim, boolean ehComissao,
                                                           Date dataReceb, boolean ehGestaoNotas,
                                                           Integer codPessoa,  boolean familia, ClienteTitularDependenteVO clienteTitularDependenteVO,
                                                           String filtroContas,Boolean retirarRecebiveisComPendencia, boolean usarDataOriginalNFSe, List<PlanoVO> planosFiltrar, String composicao, Date dataContratoLancamentoAPartir, boolean considerarCompensacaoOriginal) throws Exception {
        return consultaPagamentosEmCheque(empresa, dataIni, dataFim, ehComissao,
        dataReceb, ehGestaoNotas, codPessoa, familia, clienteTitularDependenteVO, filtroContas, retirarRecebiveisComPendencia, null, usarDataOriginalNFSe, planosFiltrar, composicao, dataContratoLancamentoAPartir, considerarCompensacaoOriginal);
    }

    public static StringBuilder consultaPagamentosEmCheque(int empresa, Date dataIni, Date dataFim, boolean ehComissao,
                                                           Date dataReceb, boolean ehGestaoNotas,
                                                           Integer codPessoa,  boolean familia, ClienteTitularDependenteVO clienteTitularDependenteVO,
                                                           String filtroContas,Boolean retirarRecebiveisComPendencia, Integer codigoCheque,
                                                           boolean usarDataOriginalNFSe, List<PlanoVO> planosFiltrar, String composicao, Date dataContratoLancamentoAPartir, boolean considerarCompensacaoOriginal) throws Exception {

        String codPlanos = obterListaCodigosPlanos(planosFiltrar);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n" +
                "  movPag.pessoa,\n" +
                "  movpag.nomepagador,\n" +
                "  cheque.produtospagos,\n" +
                "  movPag.codigo                                                            AS codigoMovPagamento,\n" +
                "  movPag.empresa                                                            AS codigoempresa,\n" +
                "  cheque.valor,\n" +
                "  cheque.codigo                                                            AS codigoCheque,\n" +
                "  movPag.recibopagamento,\n" +
                "  formaPag.descricao                                                       AS pagDesc\n");
        if (ehComissao) {
            sql.append("  ,movpag.datalancamento\n");

            sql.append("  ,usu.colaborador                                                  AS atendente\n");
        }
        if (ehComissao || ehGestaoNotas) {
            sql.append(" ,'' as  produtospagoscancelados\n");
        } else {
            sql.append(" ,cheque.produtospagoscancelados\n");
        }

        if (!UteisValidacao.emptyString(filtroContas)) {
            sql.append("  ,mc.conta\n");
        }
        if (considerarCompensacaoOriginal) {
            sql.append("  ,coalesce(cheque.dataoriginal, cheque.datacompesancao) as datacompesancao\n");
        } else {
            sql.append("  ,cheque.datacompesancao\n");
        }
        sql.append("  ,cheque.composicao\n");
        if ((ehGestaoNotas) && (familia)){
            sql.append(" ,familia.clienteTitular \n");
        }

        if (ehGestaoNotas) {
            sql.append("  ,formaPag.codigo AS codformapagamento\n");
            sql.append("  ,nota.codigo AS codigonotafiscal\n");
            sql.append("  ,nfse.codigo  AS nfse\n");
            sql.append("  ,nfse.sequencialfamilia\n");
            sql.append("  ,nfse.rps \n");
            sql.append("  ,usuario_logado.nome as nome_usuario_logado \n");
            if (usarDataOriginalNFSe) {
                sql.append("  ,CASE WHEN cheque.dataoriginal is null THEN cheque.datacompesancao\n" +
                        "ELSE cheque.dataoriginal END  as dataReferenciaItem \n");
            } else {
                sql.append("  ,cheque.datacompesancao as dataReferenciaItem \n");
            }
            sql.append("  ,nfse.situacaoNotaFiscal as situacaonfse \n");
            sql.append("  ,nfse.valor as valornfse \n");
            sql.append("  ,nfse.dataenvio as dataenvionfse \n");
            sql.append("  ,nfse.dataemissao as dataemissaonfse \n");
            sql.append("  ,nfse.nrnotamanual AS nrnotamanual\n");
            sql.append("  ,cli.codigo as codcliente\n");
            sql.append("  ,cli.matricula as matriculacliente\n");
            sql.append("  ,pe.cfp as cpf");
            sql.append("  ,col.codigo as codcolaborador\n");
            sql.append("  ,pes.cfp as cpfcolaborador\n");
            sql.append("  ,(SELECT exists( \n" +
                    "      SELECT\n" +
                    "        codigo\n" +
                    "      FROM nfseemitida\n" +
                    "      WHERE cheque IN\n" +
                    "            (SELECT\n" +
                    "               c.codigo\n" +
                    "             FROM cheque c\n" +
                    "               INNER JOIN movpagamento mpag ON c.movpagamento = mpag.codigo\n" +
                    "               INNER JOIN recibopagamento rp ON rp.codigo = mpag.recibopagamento\n" +
                    "             WHERE rp.contrato = recibo.contrato))) AS emitidaAntes\n");
        }
        sql.append("from cheque \n");
        sql.append("inner join MovPagamento movPag on movPag.codigo = cheque.movpagamento \n");
        sql.append("inner join formaPagamento formaPag on formaPag.codigo = movPag.formapagamento  \n");
        if(!UteisValidacao.emptyString(filtroContas)){
            sql.append(" left join historicocheque hc on hc.cheque = cheque.codigo and hc.datafim is null\n");
            sql.append(" left join movconta mc on mc.lote = hc.lote and mc.tipooperacao <> ").append(TipoOperacaoLancamento.TRANSFERENCIA.getCodigo()).append("\n");
        }

        if (retirarRecebiveisComPendencia != null) {
            if (retirarRecebiveisComPendencia) {
                sql.append(" left join historicocheque hch on hch.cheque = cheque.codigo and hch.datafim is null\n");
                sql.append(" left join movconta mc on mc.lote = hch.lote and mc.tipooperacao <> ").append(TipoOperacaoLancamento.TRANSFERENCIA.getCodigo()).append("\n");
                sql.append(" inner join conta co on co.codigo = mc.conta\n");
                sql.append(" inner join tipoconta tc on tc.codigo = co.tipoconta\n");
            }
        }

        if (!UteisValidacao.emptyString(codPlanos) || dataContratoLancamentoAPartir != null) {
            sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = movPag.codigo \n");
            sql.append(" INNER JOIN movparcela par ON par.codigo = pmp.movparcela \n");
            if (UteisValidacao.emptyString(codPlanos) && dataContratoLancamentoAPartir != null) {
                sql.append(" INNER JOIN contrato con ON con.codigo = par.contrato and con.datalancamento >= '").append(Uteis.getDataHoraJDBC(dataContratoLancamentoAPartir, "00:00:00")).append("'\n");
            } else {
                sql.append(" INNER JOIN contrato con ON con.codigo = par.contrato and con.plano in (").append(codPlanos).append(") \n");
            }
        }
        
        if (ehComissao) {
            sql.append("inner join recibopagamento rp on movpag.recibopagamento = rp.codigo \n");
            sql.append("INNER JOIN usuario usu on rp.responsavellancamento = usu.codigo \n");
        }
        if (ehGestaoNotas) {
            sql.append(" LEFT JOIN nfseemitida nfse ON (nfse.recibopagamento = movPag.recibopagamento or cheque.codigo = nfse.cheque) \n");
            sql.append(" LEFT JOIN notafiscal nota on (nota.nfseemitida = nfse.codigo or (nfse.notafamilia and nota.sequencialfamilia = nfse.sequencialfamilia))  \n");
            sql.append(" LEFT JOIN cliente cli ON movPag.pessoa = cli.pessoa\n");
            sql.append(" LEFT JOIN colaborador col ON movPag.pessoa = col.pessoa AND col.empresa = movPag.empresa\n");
            sql.append(" LEFT JOIN recibopagamento recibo ON movPag.recibopagamento = recibo.codigo\n");
            sql.append(" LEFT JOIN pessoa pe ON pe.codigo = cli.pessoa\n");
            sql.append(" LEFT JOIN pessoa pes ON col.pessoa = pes.codigo\n");
            sql.append(" LEFT JOIN usuario usuario_logado ON usuario_logado.codigo = nota.usuario\n");
            if (familia){
                sql.append(" INNER JOIN clientetitulardependente familia ON familia.cliente = cli.codigo \n");
            }
        }
        
        sql.append("where (formaPag.tipoformapagamento = 'CH') \n");


        if (ehGestaoNotas && codPessoa != null) {
            sql.append("          and movPag.pessoa = ").append(codPessoa).append("\n");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" and movPag.empresa = ").append(empresa).append("\n");
        }
        
        if(codigoCheque == null){

            if ((ehGestaoNotas && usarDataOriginalNFSe) || considerarCompensacaoOriginal) {
                sql.append(" and (");
                sql.append(" (cheque.dataoriginal is null and (cheque.datacompesancao >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00")).append("') and (cheque.datacompesancao <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
                sql.append(" OR ");
                sql.append(" (cheque.dataoriginal is not null and (cheque.dataoriginal >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00")).append("') and (cheque.dataoriginal <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
                sql.append(") ");
            } else {
                sql.append(" and ((cheque.datacompesancao >= '");
                sql.append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"));
                sql.append("') and (cheque.datacompesancao <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))\n");
            }
        }else{
            sql.append(" AND cheque.codigo = ").append(codigoCheque).append("\n");
        }

        if (dataReceb != null) {
            if(codigoCheque == null){
                sql.append(" and (movPag.datalancamento >= '").append(Uteis.getDataJDBC(dataReceb)).append("')\n");
            }
        }
        sql.append(" and cheque.situacao = 'EA' \n");
        if(familia){
            if ((UtilReflection.objetoMaiorQueZero(clienteTitularDependenteVO, "getCodigo()"))) {
                if (clienteTitularDependenteVO.getCodigo().equals(clienteTitularDependenteVO.getClienteTitular().getCodigo())){
                    // titular
                    sql.append(" AND familia.clienteTitular = ").append(clienteTitularDependenteVO.getCodigo());
                }else{
                    // dependente
                    sql.append(" AND familia.cliente = ").append(clienteTitularDependenteVO.getClienteVO().getCodigo());
                }
            }
        }
        if(!UteisValidacao.emptyString(filtroContas) && !filtroContas.equals("fluxo_caixa") && UteisValidacao.emptyString(composicao)){
            sql.append("and (mc.conta in (").append(filtroContas).append(")");
            sql.append(filtroContas.contains("-1") ? " or mc.conta is null " : "");
            sql.append(")");
        }
        
        if (retirarRecebiveisComPendencia != null) {
            if (retirarRecebiveisComPendencia) {
                sql.append(" and tc.comportamento not in (4,5,6)\n");
            }
        }

        if (!UteisValidacao.emptyString(filtroContas) && !UteisValidacao.emptyString(composicao)) {
            sql.append(" and cheque.codigo in (").append(composicao).append(") ");
        }

        sql.append("\n group by movPag.pessoa, movPag.nomepagador, movPag.codigo,movPag.empresa,cheque.valor, cheque.codigo, movPag.recibopagamento, cheque.produtospagos, formaPag.descricao,cheque.datacompesancao ");
        if (!UteisValidacao.emptyString(filtroContas)) {
            sql.append("  ,mc.conta\n");
        }
        if (ehComissao) {
            sql.append("  ,movpag.datalancamento\n");
            sql.append("  ,cheque.datacompesancao\n");
            sql.append("  ,usu.colaborador\n");
        }
        if (ehGestaoNotas) {
            sql.append("  , formaPag.codigo\n");
            sql.append("  , nota.codigo\n");
            sql.append("  , nfse.codigo\n");
            sql.append("  , nfse.sequencialfamilia\n");
            sql.append("  , nfse.rps\n");
            sql.append("  , dataReferenciaItem \n");
            sql.append("  , nfse.situacaoNotaFiscal \n");
            sql.append("  , nfse.valor \n");
            sql.append("  , nfse.dataenvio \n");
            sql.append("  , nfse.dataemissao \n");
            sql.append("  , nfse.nrnotamanual\n");
            sql.append("  , cli.codigo\n");
            sql.append("  , cli.matricula\n");
            sql.append("  , recibo.contrato\n");
            sql.append("  , pe.cfp");
            sql.append("  , col.codigo\n");
            sql.append("  , pes.cfp\n");
            sql.append("  , usuario_logado.nome\n");
            if ((ehGestaoNotas) && (familia)){
                sql.append(" ,familia.clienteTitular \n");
            }

        }
        return sql;
    }

    public static void dividirValorPagamentoPorContrato(List<LancamentoDF> listaLancamentosDF,
                                                        List<ProdutoRatear> listaProdutoRatear,
                                                        double valorPagamento,
                                                        int recibopagamento,
                                                        TipoFormaPagto tipoFormaPagto,
                                                        String formaPagApresentar,
                                                        int codigoMovPagamento,
                                                        Date dataLancamento,
                                                        Date dia, Integer conta,
                                                        Connection con, boolean faturamentoRecZW,
                                                        Integer consultor, String responsavelPagamento) throws Exception {
        dividirValorPagamentoPorContrato(listaLancamentosDF, listaProdutoRatear, valorPagamento, recibopagamento, tipoFormaPagto, formaPagApresentar, codigoMovPagamento, dataLancamento, dia, conta, con, faturamentoRecZW, consultor, formaPagApresentar, responsavelPagamento);
    }

    public static void dividirValorPagamentoPorContrato(List<LancamentoDF> listaLancamentosDF,
                                                        List<ProdutoRatear> listaProdutoRatear,
                                                        double valorPagamento,
                                                        int recibopagamento,
                                                        TipoFormaPagto tipoFormaPagto,
                                                        String formaPagApresentar,
                                                        int codigoMovPagamento,
                                                        Date dataLancamento,
                                                        Date dia, Integer conta,
                                                        Connection con, boolean faturamentoRecZW,
                                                        Integer consultor, String descricaoFormaPagamento, String responsavelPagamento) throws Exception {
        /*Dividir o valor do pagamento para cada contrato ou produto avulso pago.
         * Obs.: O sistema permite juntar vários contratos, ou vários produtos de pessoas diferentes e efetuar um só pagamento.
         *       Desta forma, tem que ratear o valor do pagamento para os contratos ou produtos que o mesmo pagou.
         */
        List<LancamentoDF> listaLancamentoLocal = new ArrayList<LancamentoDF>();
        LancamentoDF lancamento;
        double totalGeralMovProdutoParcela = 0;

        for (ProdutoRatear produtoRatear : listaProdutoRatear) {
            // Os MovProdutos que tiverem contrato, serão agrupados por contrato, e os que não tem contrato, serão agrupados por pessoa.
            totalGeralMovProdutoParcela += produtoRatear.getValorMovProdutoParcela();
            lancamento = pesquisarLancamento(listaLancamentoLocal, produtoRatear, faturamentoRecZW);
            if (lancamento == null) {
                lancamento = new LancamentoDF();
                lancamento.setRecibo(recibopagamento);
                lancamento.setMovPagamento(codigoMovPagamento);
                lancamento.setTipoFormaPagto(tipoFormaPagto);
                lancamento.setDescricaoFormaPagamento(descricaoFormaPagamento);
                lancamento.setFormaPagApresentar(formaPagApresentar);
                lancamento.setCodigoPessoa(produtoRatear.getCodigoPessoa());
                lancamento.setNomePessoa(produtoRatear.getNomePessoa());
                lancamento.setTotalMovProdutoParcela(produtoRatear.getValorMovProdutoParcela());
                lancamento.setDescricaoLancamento(produtoRatear.getDescricaoProduto());
                lancamento.setContrato(produtoRatear.getContrato());
                lancamento.setVendaAvulsa(produtoRatear.getVendaAvulsa());
                lancamento.setAulaavulsadiaria(produtoRatear.getAulaavulsadiaria());
                lancamento.setModalidadeDiaria(produtoRatear.getModalidadeDiaria());
                lancamento.setPersonal(produtoRatear.getPersonal());
                lancamento.setTipoProduto(produtoRatear.getTipoProduto());
                lancamento.getListaProdutoRatear().add(produtoRatear);
                lancamento.setMovProduto(produtoRatear.getCodigoMovProdutoAulaAvulsaEDiaria());
                lancamento.setCodProduto(produtoRatear.getCodigoProduto());
                lancamento.setDataLancamento(dataLancamento);
                lancamento.setDiaConta(dia);
                lancamento.setConta(conta);
                lancamento.setEmpresa(produtoRatear.getEmpresa());
                lancamento.setNomeEmpresa(produtoRatear.getNomeEmpresa());
                lancamento.setQuantidade(produtoRatear.getQuantidade());
                lancamento.setResponsavelLancamento(produtoRatear.getResponsavelLancamento());
                lancamento.setResponsavelPagamento(responsavelPagamento != null ? responsavelPagamento :  "");
                lancamento.setDevolucaoCheque(produtoRatear.isDevolucaoCheque());
                if (produtoRatear.getCodigoConsultor() > 0) {
                    lancamento.setCodigoConsultor(produtoRatear.getCodigoConsultor());
                }
                if (!produtoRatear.getTipoProduto().equals("PM")) {
                    lancamento.setTotalProdutosDiferenteDePlano(produtoRatear.getValorMovProdutoParcela());
                } else {
                    lancamento.setQtdeParcelasPagaPlano(1);
                }
                if (faturamentoRecZW){
                     lancamento.setValorLancamento(produtoRatear.getValorRatear());
                }

                //Verifica se a parcela foi paga em mais de uma vez.
                int QtdePagtoParaParcela = 0;
                if (produtoRatear.getCodigoMovParcela() != 0) {
                    QtdePagtoParaParcela = retornarQtdePagamentosForamNecessariosParaPagarParcela(produtoRatear.getCodigoMovParcela(), con);
                }
                lancamento.setQtdePgtosParaPagarParcela(QtdePagtoParaParcela);

                listaLancamentoLocal.add(lancamento);
            } else {
                lancamento.getListaProdutoRatear().add(produtoRatear);
                lancamento.setTotalMovProdutoParcela(lancamento.getTotalMovProdutoParcela() + produtoRatear.getValorMovProdutoParcela());
                if (!produtoRatear.getTipoProduto().equals("PM")) {
                    lancamento.setTotalProdutosDiferenteDePlano(lancamento.getTotalProdutosDiferenteDePlano() + produtoRatear.getValorMovProdutoParcela());
                } else {
                    lancamento.setQtdeParcelasPagaPlano(lancamento.getQtdeParcelasPagaPlano() + 1);
                }
                lancamento.setTotalPlano(produtoRatear.getValorTotalContrato() - lancamento.getTotalProdutosDiferenteDePlano());
                 if (faturamentoRecZW){
                     lancamento.setValorLancamento(lancamento.getValorLancamento() + produtoRatear.getValorRatear());
                }

            }

        }
        double tot = 0.0;
       
        for (LancamentoDF obj : listaLancamentoLocal) {
                double percentagem = 1;
                // Ratear o valor do pagamento para cada contrato ou produto avulso.
                if (Uteis.arredondarForcando2CasasDecimais(totalGeralMovProdutoParcela) != 0.0) {
                    percentagem = Uteis.arredondarForcando2CasasDecimais(obj.getTotalMovProdutoParcela()) / Uteis.arredondarForcando2CasasDecimais(totalGeralMovProdutoParcela);
                }
                obj.setValorLancamento(percentagem * valorPagamento);
                
            tot = tot + obj.getValorLancamento();
            if (UteisValidacao.emptyNumber(consultor) || (consultor > 0 && obj.getCodigoConsultor() == consultor)) {
                listaLancamentosDF.add(obj);
            }
        }
    }

    private static LancamentoDF pesquisarLancamento(List<LancamentoDF> listaLancamento, ProdutoRatear produtoRatear, boolean separarProdutos) {
        // Os MovProdutos que tiverem contrato, serão agrupados por contrato, e os que não tem contrato, serão agrupados por Pessoa
        LancamentoDF lancamentoResult = null;
        for (LancamentoDF obj : listaLancamento) {
            if (produtoRatear.getContrato() > 0) {
                // Comparar pelo Número do Contrato.
                if (obj.getContrato() == produtoRatear.getContrato() && (!separarProdutos ||  obj.getDescricaoLancamento().equals(produtoRatear.getDescricaoProduto()))) {
                    lancamentoResult = obj;
                    break;
                }
            } else if (produtoRatear.getVendaAvulsa() > 0) {
                // Comparar pelo código da pessoa
                if ( obj.getVendaAvulsa() == produtoRatear.getVendaAvulsa() && (!separarProdutos)) {
                    lancamentoResult = obj;
                    break;
                }
            } else if (produtoRatear.getAulaavulsadiaria()  > 0) {
                // Comparar pelo código da pessoa
                if ( obj.getAulaavulsadiaria() == produtoRatear.getAulaavulsadiaria() && (!separarProdutos)) {
                    lancamentoResult = obj;
                    break;
                }
            } else if (produtoRatear.getPersonal()  > 0) {
                // Comparar pelo código da pessoa
                if ( obj.getPersonal() == produtoRatear.getPersonal() && (!separarProdutos)) {
                    lancamentoResult = obj;
                    break;
                }
            }

        }
        return lancamentoResult;
    }

    private List<LancamentoDF> consultarPagtosEmCartaoCredito(MesProcessar mesProcessar, int empresa) throws Exception {
        /* Consultar os pagamentos realizados em Cartão de Crédito.
         * Neste caso a fonte de dados de pesquisa será a tabela "cartaoCredito"
         */
        Date dataIni = Uteis.getDateTime(mesProcessar.getDataIni().getTime(), 0, 0, 0);
        Date dataFim = Uteis.getDateTime(mesProcessar.getDataFim().getTime(), 23, 59, 59);
        StringBuilder sql = consultaPagamentoEmCartaoCredito(empresa, dataIni, dataFim,
                false, null, false, null,false,null, filtroContas, false, null, "", null, false);

        Statement stm = con.createStatement();
        ResultSet dadosMovPagamento = stm.executeQuery(sql.toString());

        List<LancamentoDF> listaLancamentosDF = new ArrayList<LancamentoDF>();
        GenericoAtributosVO codigosProdutosCredito = consultarCodigoCategoriaProdutoCredito(con);
        while (dadosMovPagamento.next()) {
            if (!UteisValidacao.emptyString(filtroContas) && !UteisValidacao.emptyString(dadosMovPagamento.getString("composicao"))){
                Statement stm2 = con.createStatement();
                StringBuilder sqlComposicao = consultaPagamentoEmCartaoCredito(empresa, dataIni, dataFim,
                        false, null, false, null, false, null, filtroContas, false, null, dadosMovPagamento.getString("composicao"), null, false);
                ResultSet composicaoCartaoCredito = stm2.executeQuery(sqlComposicao.toString());
                while (composicaoCartaoCredito.next() && !fluxoCaixa) {
                    Integer conta = dadosMovPagamento.getInt("conta");
                    realizarRateiosReferenteMovPagamentoCartaoCredito(composicaoCartaoCredito, listaLancamentosDF, codigosProdutosCredito, conta);
                }
            }
            realizarRateiosReferenteMovPagamentoCartaoCredito(dadosMovPagamento, listaLancamentosDF, codigosProdutosCredito, 0);
        }

        //Ajusta lista pelo filtro Contas que falhava quando marcava Não Movimentados
        if (!UteisValidacao.emptyString(filtroContas) && !fluxoCaixa) {
            String[] array = filtroContas.split(",");
            List<LancamentoDF> clone = new ArrayList<>(listaLancamentosDF);
            for (LancamentoDF item: clone){
                boolean contem = false;
                for (int i = 0; i < array.length; i++) {
                    if (Integer.toString(item.getConta()).equals(array[i])) {
                        contem = true;
                    }
                }
                if (!contem) {
                    listaLancamentosDF.remove(item);
                }
            }
        }

        return listaLancamentosDF;
    }

    private void realizarRateiosReferenteMovPagamentoCartaoCredito(ResultSet dadosMovPagamento, List<LancamentoDF> listaLancamentosDF, GenericoAtributosVO codigosProdutosCredito,
                                                                   Integer contaRecebida) throws Exception {
        // Pesquisar os produtos que o cartão de crédito pagou.
        List<ProdutoRatear> listaProdutoRatear = pesquisarProdutosDoPagamento(dadosMovPagamento.getInt("CodigoMovPagamento"),
                dadosMovPagamento.getString("produtospagos")+dadosMovPagamento.getString("produtospagoscancelados"), this.con, null);
        Integer conta = null;
        try {
            conta = dadosMovPagamento.getInt("conta");

            if (conta == 0 && conta != null) {
                conta = contaRecebida;
            }

        }catch (Exception e){

        }

        if (!listaProdutoRatear.isEmpty()){
            dividirValorPagamentoPorContrato(listaLancamentosDF,
                    listaProdutoRatear,
                    dadosMovPagamento.getDouble("valor"),
                    dadosMovPagamento.getInt("recibopagamento"),
                    TipoFormaPagto.CARTAOCREDITO,
                 dadosMovPagamento.getString("pagDesc"),
                    dadosMovPagamento.getInt("CodigoMovPagamento"),
                 null,
                    dadosMovPagamento.getDate("datacompesancao"),
                    conta,
                    this.con, false, null, dadosMovPagamento.getString("pagdesc"), "");
       } else {
           if (dadosMovPagamento.getInt("recibopagamento") == 0){
               dividirMovPagamentoContaAluno(listaLancamentosDF,
                       dadosMovPagamento.getInt("CodigoMovPagamento"),
                       dadosMovPagamento.getDouble("valor"),  TipoFormaPagto.CARTAOCREDITO,
                       dadosMovPagamento.getInt("pessoa"),dadosMovPagamento.getString("nomepagador"),
                    dadosMovPagamento.getInt("codigoempresa"),
                    codigosProdutosCredito,"", null,
                    dadosMovPagamento.getDate("datacompesancao"),
                    conta, this.con, dadosMovPagamento.getString("pagdesc"));
           }
       }
    }

    public static StringBuilder consultaPagamentoEmCartaoCredito(int empresa, Date dataIni, Date dataFim,
                                                                 boolean ehComissao, Date dataReceb,
                                                                 boolean ehGestaoNotas, Integer codPessoa,
                                                                 boolean familia, ClienteTitularDependenteVO clienteTitularDependenteVO,
                                                                 String filtroContas, boolean usarDataOriginalNFSe, List<PlanoVO> planosFiltrar, String composicao, Date dataContratoLancamentoAPartir, boolean considerarCompensacaoOriginal) throws Exception {

        String codPlanos = obterListaCodigosPlanos(planosFiltrar);

        StringBuilder sql = new StringBuilder();
        String distinct = ehGestaoNotas ? " DISTINCT ON (nfse.jsonenviar, cc.produtospagos) " : "";
        sql.append("SELECT "+distinct+
                "  movPag.pessoa,\n" +
                        (UteisValidacao.emptyString(filtroContas) ? "" : "mc.conta,\n")+
                "  cc.produtospagos,\n" +
                "  movpag.nomepagador,\n" +
                "  movPag.codigo                                                            AS codigoMovPagamento,\n" +
                "  movPag.empresa                                                            AS codigoempresa,\n" +
                "  cc.valor,\n" +
                "  cc.codigo                                                                AS codigoCartaoCredito,\n" +
                "  movPag.recibopagamento,\n" +
                "  formaPag.descricao                                                       AS pagDesc\n");
        if (ehComissao) {
            sql.append("  ,movpag.datalancamento\n");
            sql.append("  ,usu.colaborador                                                  AS atendente\n");
        }
        if (ehComissao || ehGestaoNotas) {
            sql.append(" ,'' as  produtospagoscancelados\n");
        } else {
            sql.append(" ,cc.produtospagoscancelados\n");
        }
        if (considerarCompensacaoOriginal) {
            sql.append("  ,coalesce(cc.dataoriginal, cc.datacompesancao) as datacompesancao\n");
        } else {
            sql.append("  ,cc.datacompesancao\n");
        }
        sql.append("  ,cc.composicao\n");
        if ((ehGestaoNotas) && (familia)){
            sql.append(" ,familia.clienteTitular \n");
        }

        if (ehGestaoNotas) {
            sql.append("  ,formaPag.codigo AS codformapagamento\n");
            sql.append("  ,nota.codigo AS codigonotafiscal\n");
            sql.append("  ,nfse.codigo AS nfse\n");
            sql.append("  ,nfse.sequencialfamilia\n");
            sql.append("  ,nfse.rps \n");
            sql.append(" ,usuario_logado.nome as nome_usuario_logado \n");
            if (usarDataOriginalNFSe) {
                sql.append("  ,CASE WHEN cc.dataoriginal is null THEN cc.datacompesancao\n" +
                        "ELSE cc.dataoriginal END  as dataReferenciaItem \n");
            } else {
                sql.append("  ,cc.datacompesancao as dataReferenciaItem \n");
            }
            sql.append("  ,nfse.situacaoNotaFiscal as situacaonfse \n");
            sql.append("  ,nfse.valor as valornfse \n");
            sql.append("  ,nfse.dataenvio as dataenvionfse \n");
            sql.append("  ,nfse.dataemissao as dataemissaonfse \n");
            sql.append("  ,nfse.nrnotamanual AS nrnotamanual\n");
            sql.append("  ,cli.codigo as codcliente\n");
            sql.append("  ,pe.cfp as cpf");
            sql.append("  ,cli.matricula as matriculacliente\n");
            sql.append("  ,col.codigo as codcolaborador\n");
            sql.append("  ,pes.cfp as cpfcolaborador\n");
            sql.append("  ,(SELECT exists( \n" +
                    "      SELECT\n" +
                    "        codigo\n" +
                    "      FROM nfseemitida\n" +
                    "      WHERE cartaocredito IN\n" +
                    "            (SELECT\n" +
                    "               cc.codigo\n" +
                    "             FROM cartaocredito cc\n" +
                    "               INNER JOIN movpagamento mpag ON cc.movpagamento = mpag.codigo\n" +
                    "               INNER JOIN recibopagamento rp ON rp.codigo = mpag.recibopagamento\n" +
                    "             WHERE rp.contrato = recibo.contrato))) AS emitidaAntes\n");
        }
        sql.append("from cartaoCredito cc ");
        sql.append("inner join MovPagamento movPag on movPag.codigo = cc.movpagamento ");
        sql.append("inner join formaPagamento formaPag on formaPag.codigo = movPag.formapagamento  ");
        if(!UteisValidacao.emptyString(filtroContas)){
            sql.append("left join historicocartao hc on hc.cartao = cc.codigo and hc.datafim is null\n");
            sql.append("left join movconta mc on mc.lote = hc.lote and mc.tipooperacao <> ").append(TipoOperacaoLancamento.TRANSFERENCIA.getCodigo()).append("\n");
        }
        if (ehComissao) {
            sql.append("INNER JOIN recibopagamento rp on movpag.recibopagamento = rp.codigo \n");
            sql.append("INNER JOIN usuario usu on rp.responsavellancamento = usu.codigo \n");
        }
        if (ehGestaoNotas) {
            sql.append(" LEFT JOIN nfseemitida nfse ON (nfse.recibopagamento = movPag.recibopagamento OR nfse.cartaocredito = cc.codigo) \n");
            sql.append(" LEFT JOIN notafiscal nota on (nota.nfseemitida = nfse.codigo or (nfse.notafamilia and nota.sequencialfamilia = nfse.sequencialfamilia))  \n");
            sql.append(" LEFT JOIN cliente cli ON movPag.pessoa = cli.pessoa\n");
            sql.append(" LEFT JOIN colaborador col ON movPag.pessoa = col.pessoa\n");
            sql.append(" LEFT JOIN recibopagamento recibo ON movPag.recibopagamento = recibo.codigo\n");
            sql.append(" LEFT JOIN pessoa pe ON cli.pessoa = pe.codigo\n");
            sql.append(" LEFT JOIN pessoa pes ON col.pessoa = pes.codigo\n");
            sql.append(" LEFT JOIN usuario usuario_logado ON nota.usuario = usuario_logado.codigo\n");
            if (familia){
                sql.append(" INNER JOIN clientetitulardependente familia ON familia.cliente = cli.codigo \n");
            }
        }

        if (!UteisValidacao.emptyString(codPlanos) || (dataContratoLancamentoAPartir != null)){
            sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = movPag.codigo \n");
            sql.append(" INNER JOIN movparcela par ON par.codigo = pmp.movparcela \n");
            if(UteisValidacao.emptyString(codPlanos) && dataContratoLancamentoAPartir != null){
                sql.append(" INNER JOIN contrato con ON con.codigo = par.contrato and con.datalancamento >= '").append(Uteis.getDataHoraJDBC(dataContratoLancamentoAPartir, "00:00:00")).append("'\n");
            }else {
                sql.append(" INNER JOIN contrato con ON con.codigo = par.contrato and con.plano in (").append(codPlanos).append(") \n");
            }
        }
        
        sql.append("where (formaPag.tipoformapagamento = 'CA') ");
        if (ehGestaoNotas && codPessoa != null) {
            sql.append("          and movPag.pessoa = ").append(codPessoa).append("\n");
        }
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" and movPag.empresa = ").append(empresa);
        }
        if(!UteisValidacao.emptyString(filtroContas) && !filtroContas.equals("fluxo_caixa") && UteisValidacao.emptyString(composicao)){
            sql.append(" and (mc.conta in (").append(filtroContas).append(")");
            sql.append(filtroContas.contains("-1") ? " or hc.codigo is null " : "");
            sql.append(") ");
        }
        if ((ehGestaoNotas && usarDataOriginalNFSe) || considerarCompensacaoOriginal) {
            sql.append(" and (");
            sql.append(" (cc.dataoriginal is null and (cc.datacompesancao >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00")).append("') and (cc.datacompesancao <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
            sql.append(" OR ");
            sql.append(" (cc.dataoriginal is not null and (cc.dataoriginal >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00")).append("') and (cc.dataoriginal <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
            sql.append(") ");
        } else {
            sql.append(" and ((cc.datacompesancao >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00")).append("') and (cc.datacompesancao <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
        }
        if (dataReceb != null) {
            sql.append(" and (movPag.datalancamento >= '").append(Uteis.getDataJDBC(dataReceb)).append("')\n");
        }
        sql.append(" and cc.situacao != 'CA' ");
        if(familia){
            if ((UtilReflection.objetoMaiorQueZero(clienteTitularDependenteVO, "getCodigo()"))) {
                if (clienteTitularDependenteVO.getCodigo().equals(clienteTitularDependenteVO.getClienteTitular().getCodigo())){
                    // titular
                    sql.append(" AND familia.clienteTitular = ").append(clienteTitularDependenteVO.getCodigo());
                }else{
                    // dependente
                    sql.append(" AND familia.cliente = ").append(clienteTitularDependenteVO.getClienteVO().getCodigo());
                }
            }
        }
        if (!UteisValidacao.emptyString(filtroContas) && !UteisValidacao.emptyString(composicao)) {
            sql.append(" and cc.codigo in (").append(composicao).append(") ");
        }
        
        sql.append("\n group by ");
        sql.append(UteisValidacao.emptyString(filtroContas) ? "" : "mc.conta,");
        sql.append("movPag.pessoa, movPag.codigo,movPag.empresa, movpag.nomepagador, cc.valor, cc.codigo, movPag.recibopagamento, cc.produtospagos, formaPag.descricao,cc.datacompesancao  ");
        if (ehComissao) {
            sql.append("  ,movpag.datalancamento\n");
            sql.append("  ,cc.datacompesancao\n");
            sql.append("  ,usu.colaborador\n");
        }
        if (ehGestaoNotas) {
            sql.append("  , formaPag.codigo\n");
            sql.append("  , nota.codigo\n");
            sql.append("  , nfse.codigo\n");
            sql.append("  , nfse.sequencialfamilia\n");
            sql.append("  , nfse.rps\n");
            sql.append("  , dataReferenciaItem \n");
            sql.append("  , nfse.situacaoNotaFiscal \n");
            sql.append("  , nfse.valor \n");
            sql.append("  , nfse.dataenvio \n");
            sql.append("  , nfse.dataemissao \n");
            sql.append("  , nfse.nrnotamanual\n");
            sql.append("  , cli.codigo\n");
            sql.append("  , cli.matricula\n");
            sql.append("  , recibo.contrato\n");
            sql.append("  , pe.cfp\n");
            sql.append("  , col.codigo\n");
            sql.append("  , pes.cfp\n");
            sql.append("  , usuario_logado.nome\n");
            if ((ehGestaoNotas) && (familia)){
                sql.append(" ,familia.clienteTitular \n");
            }
            sql.append(" order by nfse.jsonenviar, cc.produtospagos, codigonotafiscal DESC NULLS LAST");
        }
        return sql;
    }

    private static int retornarQtdePagamentosForamNecessariosParaPagarParcela(int movParcela, Connection con) throws Exception {
        int qtde = 1;
        String sql = "select count(*) totalPagto from pagamentomovparcela where movParcela = " + movParcela;
        Statement stm = con.createStatement();
        ResultSet resultDados = stm.executeQuery(sql);
        if (resultDados.next()) {
            qtde = resultDados.getInt("totalPagto");
        }

        return qtde;
    }

    public static void dividirValorDoPagamentoParaCadaProduto(List<LancamentoDF> listaLancamentoDF) {
        /*
         * Dividir o valor do pagamento para os produtos da parcela.
         */


        for (LancamentoDF lancamentoDf : listaLancamentoDF) {
           
            for (ProdutoRatear produtoRatear : lancamentoDf.getListaProdutoRatear()) {
                double percentagem = 1;
                // Neste caso, não foi pago produtos junto com plano.
                if (lancamentoDf.getTotalMovProdutoParcela() != 0.0) {
                    percentagem = produtoRatear.getValorMovProdutoParcela() / lancamentoDf.getTotalMovProdutoParcela();
                }
                produtoRatear.setValorRatear(percentagem * lancamentoDf.getValorLancamento());
                
            }
        }
    }

    private void realizarRateioLancamentosDoFinanceiro(MesProcessar mesProcessar, int empresa, TipoRelatorioDF tipo) throws Exception {


           StringBuilder sql = new StringBuilder();
           Date dataIni = Uteis.getDateTime(mesProcessar.getDataIni().getTime(), 0, 0, 0);
           Date dataFim = Uteis.getDateTime(mesProcessar.getDataFim().getTime(), 23, 59, 59);

           sql.append("select mov.conta, mov.datavencimento, mov.dataquitacao, mov.datacompetencia, mov.codigo codigoMovConta, mov.pessoa, coalesce(mov.descricao, movRat.descricao) descricao , mov.empresa, mov.valor valorLancamento, ");
           sql.append("       movRat.valor valorRateio, movRat.tipoes, ");
           sql.append("       pc.codigoplanocontas, cc.codigocentrocustos, forma.tipoFormaPagamento, movRat.planoconta, movRat.centrocusto, pess.nome, forma.descricao formaPagamento  ");
           sql.append("from MovConta mov ");
           sql.append("inner join Pessoa pess on pess.codigo = mov.pessoa ");
           sql.append("inner join MovContaRateio movRat on movRat.movConta = mov.Codigo ");
           if (!tipo.equals(TipoRelatorioDF.COMPETENCIA) && !tipo.equals(TipoRelatorioDF.COMPETENCIA_NAO_QUITADA)
                   && !tipo.equals(TipoRelatorioDF.COMPETENCIA_QUITADA) && !tipo.equals(TipoRelatorioDF.RECEITAPROVISAO)) {
               sql.append("inner join formaPagamento forma on forma.codigo = movRat.formaPagamento ");
           } else {

               sql.append("left join formaPagamento forma on forma.codigo = movRat.formaPagamento ");
           }
           sql.append("left join PlanoConta pc on pc.codigo = movRat.planoconta ");
           sql.append("left join centrocusto cc on cc.codigo = movRat.centrocusto ");

           switch (tipo) {
               case COMPETENCIA:
                   sql.append(" where ((mov.dataCompetencia >= '" + Uteis.getDataJDBC(dataIni) + "') and (mov.dataCompetencia <= '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'))");
                   break;
               case COMPETENCIA_QUITADA:
                   sql.append(" where ((mov.dataCompetencia >= '" + Uteis.getDataJDBC(dataIni) + "') and (mov.dataCompetencia <= '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'))\n");
                   sql.append(" AND (mov.dataQuitacao is not null)");
                   break;
               case COMPETENCIA_NAO_QUITADA:
                   sql.append(" where ((mov.dataCompetencia >= '" + Uteis.getDataJDBC(dataIni) + "') and (mov.dataCompetencia <= '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'))\n");
                   sql.append(" AND (mov.dataQuitacao is null)");
                   break;
               case FATURAMENTO:
                   sql.append(" where ((mov.datalancamento >= '"
                           + Uteis.getDataJDBC(dataIni) + "') and (mov.datalancamento <= '"
                           + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'))");
                   break;
               case FATURAMENTO_DE_CAIXA:
                   sql.append(" where ((mov.dataQuitacao >= '" + Uteis.getDataJDBC(dataIni)
                           + "') and (mov.dataQuitacao <= '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "')"
                           + " and (mov.datalancamento >= '" + Uteis.getDataJDBC(dataIni)
                           + "') and (mov.datalancamento <= '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'))");
                   break;
               case RECEITA:
                   sql.append(" where ((mov.dataQuitacao >= '" + Uteis.getDataJDBC(dataIni) + "') and (mov.dataQuitacao <= '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'))");
                   break;
               case RECEITAPROVISAO:
                   sql.append(" where ((mov.datavencimento >= '" + Uteis.getDataJDBC(dataIni) + "') and (mov.datavencimento <= '" + Uteis.getDataHoraJDBC(dataFim, "23:59:59") + "'))");
                   break;
           }
           sql.append(" and (mov.tipooperacao = " + TipoOperacaoLancamento.PAGAMENTO.getCodigo()
                   + " or mov.tipooperacao = " + TipoOperacaoLancamento.RECEBIMENTO.getCodigo()
                   + " or mov.tipooperacao = " + TipoOperacaoLancamento.DEVOLUCAO_CHEQUE.getCodigo()
                   + (UteisValidacao.emptyString(filtroContas) ? "" : (" or mov.tipooperacao = " + TipoOperacaoLancamento.AJUSTESALDO.getCodigo()))
                   + " or mov.tipooperacao = " + TipoOperacaoLancamento.RECEBIVEL_AVULSO.getCodigo() + ") ");
           //sql.append(" and mov.empresa = ").append(empresa);

           sql.append(" and (forma.tipoFormaPagamento <> 'CC' or forma.codigo is null) ");
           if (empresa > 0) {
               sql.append(" and mov.empresa = ").append(empresa);
           }
           if (!UteisValidacao.emptyString(filtroContas) && !filtroContas.equals("fluxo_caixa")) {
               sql.append("and (mov.conta in (").append(filtroContas).append(")");
               sql.append(filtroContas.contains("-1") ? " or mov.conta is null " : "");
               sql.append(")");
           }
           Statement stm = con.createStatement();
           ResultSet dadosConsulta = stm.executeQuery(sql.toString());


           while (dadosConsulta.next()) {
               if (dadosConsulta.getString("descricao").contains("Cheque devolvido") && !this.apresentarDevolucoesRel) {
                   continue;
               }
               LancamentoDF lancamentoDF = new LancamentoDF();
               lancamentoDF.setValorLancamento(dadosConsulta.getDouble("valorRateio"));
               if (tipo.equals(TipoRelatorioDF.RECEITAPROVISAO) || tipo.equals(TipoRelatorioDF.FATURAMENTO)) {
                   lancamentoDF.setDiaConta(dadosConsulta.getDate("datavencimento"));
               } else {
                   lancamentoDF.setDiaConta(dadosConsulta.getDate("dataQuitacao"));
               }
               if (tipo.equals(TipoRelatorioDF.COMPETENCIA) || tipo.equals(TipoRelatorioDF.COMPETENCIA_NAO_QUITADA)) {
                   lancamentoDF.setDiaConta(dadosConsulta.getDate("datacompetencia"));
               }
               lancamentoDF.setDescricaoLancamento(dadosConsulta.getString("descricao"));
               lancamentoDF.setCodigoPessoa(dadosConsulta.getInt("pessoa"));
               lancamentoDF.setNomePessoa(dadosConsulta.getString("nome"));
               lancamentoDF.setMovConta(dadosConsulta.getInt("codigoMovConta"));
               lancamentoDF.setEmpresa(dadosConsulta.getInt("empresa"));
               if (!tipo.equals(TipoRelatorioDF.COMPETENCIA) && !tipo.equals(TipoRelatorioDF.COMPETENCIA_NAO_QUITADA)
                       && !tipo.equals(TipoRelatorioDF.COMPETENCIA_QUITADA)) {
                   lancamentoDF.setDescricaoFormaPagamento(dadosConsulta.getString("formaPagamento"));
               }

               if (dadosConsulta.getString("tipoFormaPagamento") != null) {
                   if (dadosConsulta.getString("tipoFormaPagamento").equals("CA")) {
                       lancamentoDF.setTipoFormaPagto(TipoFormaPagto.CARTAOCREDITO);
                   } else if (dadosConsulta.getString("tipoFormaPagamento").equals("CH")) {
                       lancamentoDF.setTipoFormaPagto(TipoFormaPagto.CHEQUE);
                   } else if (dadosConsulta.getString("tipoFormaPagamento").equals("CD")) {
                       lancamentoDF.setTipoFormaPagto(TipoFormaPagto.CARTAODEBITO);
                   } else if (dadosConsulta.getString("tipoFormaPagamento").equals("CC")) {
                       lancamentoDF.setTipoFormaPagto(TipoFormaPagto.CREDITOCONTACORRENTE);
                   } else {
                       lancamentoDF.setTipoFormaPagto(TipoFormaPagto.AVISTA);
                   }
               }

               // realizar os rateios do lançamento.
               RateioIntegracaoTO rateio = null;
               if (this.tipoVisualizacao == TipoVisualizacaoRelatorioDF.CENTROCUSTO) {
                   // Lançamentos que não foram definidos centro de custos
                   if (dadosConsulta.getString("codigocentrocustos") == null) {
                       rateio = this.rateioNaoDefinido;
                       rateio.setTipoES(TipoES.getTipoPadrao(dadosConsulta.getInt("tipoes")));
                   }
               } else {
                   // Lançamentos que não foram definidos Plano de Contas
                   if (dadosConsulta.getString("codigoplanocontas") == null) {
                       rateio = this.rateioNaoDefinido;
                       rateio.setTipoES(TipoES.getTipoPadrao(dadosConsulta.getInt("tipoes")));
                       if (dadosConsulta.getString("codigocentrocustos") != null && dadosConsulta.getInt("centrocusto") != 0) {
                           rateio.setCodigoCentro(dadosConsulta.getString("codigocentrocustos"));
                           rateio.setCodigoCentroCustos(dadosConsulta.getInt("centrocusto"));
                       }
                   }
               }
               if (rateio == null) {
                   rateio = new RateioIntegracaoTO();
                   rateio.setTipoES(TipoES.getTipoPadrao(dadosConsulta.getInt("tipoes")));
                   rateio.setCodigoPlano(dadosConsulta.getString("codigoplanocontas"));
                   rateio.setCodigoCentro(dadosConsulta.getString("codigocentrocustos"));
                   rateio.setCodigoCentroCustos(dadosConsulta.getInt("centrocusto"));
                   rateio.setCodigoPlanoContas(dadosConsulta.getInt("planoconta"));
               }
               boolean realizarRateio = true;
               if (this.listaFiltroCentroCusto.size() > 0) {
                   realizarRateio = listaFiltroCentroCusto.contains(rateio.getCodigoCentroCustos());
               }

               if (realizarRateio) {
                   lancamentoDF.getRateios().add(rateio);
                   realizarRateio(dadosConsulta.getDouble("valorRateio"),
                           rateio,
                           lancamentoDF,
                           mesProcessar);
               }
           }

    }

    public static List<ContratoModalidadePercentual> retornarListaContratoModalidades_OLD(LancamentoDF lancamento, TipoRelatorioDF tipoDF, Connection con) throws Exception {
        List<ContratoModalidadePercentual> lista = new ArrayList<ContratoModalidadePercentual>();

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT m.nome, SUM(valor) as ValorFinalModalidade, mm.modalidade, 	SUM(mp.totalfinal) as mensalidade \n");
        sql.append(" FROM movprodutomodalidade mm \n");
        sql.append(" INNER JOIN movproduto mp ON mp.codigo = mm.movproduto AND mp.contrato = " + lancamento.getContrato() + "\n");
        sql.append(" INNER JOIN modalidade m ON m.codigo = mm.modalidade  \n");
        if (tipoDF == TipoRelatorioDF.RECEITA || tipoDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
            sql.append(" INNER JOIN movprodutoparcela mpp ON mpp.recibopagamento = " + lancamento.getRecibo()
                    + " AND mpp.movproduto = mm.movproduto  \n");
        }
        if (tipoDF == TipoRelatorioDF.COMPETENCIA || tipoDF == TipoRelatorioDF.FATURAMENTO) {
            sql.append(" WHERE mp.codigo = " + lancamento.getMovProduto());
        }
        sql.append(" GROUP BY m.nome, mm.modalidade ");


        Statement stm = con.createStatement();
        ResultSet resultDados = stm.executeQuery(sql.toString());
        while (resultDados.next()) {
            ContratoModalidadePercentual contratoModalidade = new ContratoModalidadePercentual();
            contratoModalidade.setCodigoModalidade(resultDados.getInt("modalidade"));
            contratoModalidade.setContrato(lancamento.getContrato());
            contratoModalidade.setValor(resultDados.getFloat("ValorFinalModalidade"));
            contratoModalidade.setNomeModalidade(resultDados.getString("nome"));

            Double mensalidade = resultDados.getDouble("mensalidade");
            mensalidade = mensalidade > 0 ? mensalidade : 1;
            contratoModalidade.setPercentagem(contratoModalidade.getValor() / mensalidade);

            lista.add(contratoModalidade);
        }
        return lista;

    }

    public static List<ContratoModalidadePercentual> obterListaContratoModalidadePercentual(StringBuilder movProdutoModalidades) {
        List<ContratoModalidadePercentual> listaModalidadesPercentual = new ArrayList();
        String[] modalidades = movProdutoModalidades.toString().split("\\^");
        for (int i = 0; i < modalidades.length; i++) {
            String modalidade = modalidades[i];
            if (!modalidade.isEmpty()) {
                ContratoModalidadePercentual contModalPorc = new ContratoModalidadePercentual();
                //
                String[] attrsModal = modalidade.split(";");
                //previnir erro
                if(attrsModal.length > 5){
                    attrsModal = normalizarDescModalidade(attrsModal);
                }

                for (int j = 0; j < (attrsModal.length); j++) {
                    String attr = attrsModal[j];
                    //regMM.codigo||';'||regMM.codM||';'||regMM.nome||';'||regMM.valor||';'||porc||';'||regMM.contrato || '^';
                    switch (j) {
                        case 0: {// Código Modalidade
                            contModalPorc.setCodigoModalidade(new Integer(attr));
                            break;
                        }
                        case 1: {//Nome Modalidade
                            contModalPorc.setNomeModalidade(attr);
                            break;
                        }
                        case 2: {//Valor Modalidade
                            contModalPorc.setValor(new Double(attr));
                            break;
                        }
                        case 3: {//Porcentagem Modalidade
                            contModalPorc.setPercentagem(new Double(attr));
                            break;
                        }
                        case 4: {//Código Contrato
                            contModalPorc.setContrato(new Integer(attr));
                            break;
                        }
                    }//switch
                }//for
                listaModalidadesPercentual.add(contModalPorc);
            }//if !modalidade.isEmpty
        }
        return listaModalidadesPercentual;
    }

    private static String[] normalizarDescModalidade(String[] attrsModal){
        String[] atributos = new String[5];
        int pont = 0;
        for (int j = 0; j < (attrsModal.length); j++) {
            if(j < 2){
                atributos[j] = attrsModal[j];
                pont++;
            }else{
                //0egMM.codM||';'||1regMM.nome||';'||2regMM.valor||';'||porc||';'||regMM.contrato || '^';
                try {
                    switch (pont) {
                        case 2: {//Valor Modalidade
                            atributos[pont] = new Double(attrsModal[j]).toString();
                            break;
                        }
                        case 3: {//Porcentagem Modalidade
                            atributos[pont] = new Double(attrsModal[j]).toString();
                            break;
                        }
                        case 4: {//Código Contrato
                            atributos[pont] = new Integer(attrsModal[j]).toString();
                            break;
                        }
                    }
                    pont++;
                }catch (Exception e){
                    //ignorar
                }
            }
        }
        return atributos;
    }

    public static List<ContratoModalidadePercentual> retornarListaContratoModalidades(LancamentoDF lancamento) throws Exception {
        List<ProdutoRatear> lista = lancamento.getListaProdutoRatear();
        List<ContratoModalidadePercentual> result = new ArrayList<ContratoModalidadePercentual>();
        for (ProdutoRatear produtoRatear : lista) {
            result.addAll(produtoRatear.getListaContratoModalidadePercentual());
        }
        return result;

    }

    public static List<ContratoModalidadePercentual> retornarListaContratoModalidades_WALLER_TESTE(LancamentoDF lancamento, TipoRelatorioDF tipoDF, Connection con) throws Exception {
        List<ContratoModalidadePercentual> lista = new ArrayList<ContratoModalidadePercentual>();

        StringBuilder sql = new StringBuilder();
//        sql.append(" SELECT m.nome, SUM(valor) as ValorFinalModalidade, mm.modalidade, 	SUM(mp.totalfinal) as mensalidade \n");
//        sql.append(" FROM movprodutomodalidade mm \n");
//        sql.append(" INNER JOIN movproduto mp ON mp.codigo = mm.movproduto AND mp.contrato = ").append(lancamento.getContrato()).append("\n");
//        sql.append(" INNER JOIN modalidade m ON m.codigo = mm.modalidade  \n");
        sql.append(" SELECT distinct m.nome, valor as ValorFinalModalidade, mm.modalidade FROM movprodutomodalidade mm  \n");
        sql.append(" INNER JOIN modalidade m ON m.codigo = mm.modalidade \n");
        if (tipoDF == TipoRelatorioDF.RECEITA || tipoDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
            sql.append(" INNER JOIN movprodutoparcela mpp ON mpp.movproduto = mm.movproduto  \n");
        }

        sql.append(" WHERE  mm.contrato = ").append(lancamento.getContrato()).append("\n");
        if (tipoDF == TipoRelatorioDF.RECEITA || tipoDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
            sql.append(" AND mpp.recibopagamento = ").append(lancamento.getRecibo());
        }
        if (tipoDF == TipoRelatorioDF.COMPETENCIA || tipoDF == TipoRelatorioDF.FATURAMENTO) {
            sql.append(" AND mm.movproduto = ").append(lancamento.getMovProduto());
        }
//        sql.append(" GROUP BY m.nome, mm.modalidade ");

        Statement stm = con.createStatement();
        ResultSet resultDados = stm.executeQuery(sql.toString());
        double valorMensalidade = 0.0f;
        while (resultDados.next()) {
            ContratoModalidadePercentual contratoModalidade = new ContratoModalidadePercentual();
            contratoModalidade.setCodigoModalidade(resultDados.getInt("modalidade"));
            contratoModalidade.setContrato(lancamento.getContrato());
            contratoModalidade.setValor(resultDados.getFloat("ValorFinalModalidade"));
            contratoModalidade.setNomeModalidade(resultDados.getString("nome"));

            lista.add(contratoModalidade);
            valorMensalidade += contratoModalidade.getValor();
        }
        // Calcular a percentagem que cada modalidade representa em relação ao valor da mensalidade.
        for (ContratoModalidadePercentual obj : lista) {
            if (valorMensalidade != 0.0f) {
                obj.setPercentagem(obj.getValor() / (valorMensalidade + 0.0f));
            } else {
                obj.setPercentagem(1);
            }


        }
        return lista;

    }

    public static List<ContratoModalidadePercentual> retornarListaContratoModalidades(int contrato, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select cm.modalidade, cm.ValorFinalModalidade, m.nome ");
        sql.append("from contratomodalidade cm ");
        sql.append("inner join modalidade m on m.codigo = cm.modalidade ");
        sql.append(" where cm.ValorFinalModalidade > 0 and cm.contrato = ");
        sql.append(contrato);
        Statement stm = con.createStatement();
        ResultSet resultDados = stm.executeQuery(sql.toString());
        List<ContratoModalidadePercentual> lista = new ArrayList<ContratoModalidadePercentual>();
        double valorMensalidade = 0.0f;
        while (resultDados.next()) {
            ContratoModalidadePercentual contratoModalidade = new ContratoModalidadePercentual();
            contratoModalidade.setCodigoModalidade(resultDados.getInt("modalidade"));
            contratoModalidade.setContrato(contrato);
            contratoModalidade.setValor(resultDados.getFloat("ValorFinalModalidade"));
            contratoModalidade.setNomeModalidade(resultDados.getString("nome"));
            lista.add(contratoModalidade);
            valorMensalidade += contratoModalidade.getValor();
        }
        // Calcular a percentagem que cada modalidade representa em relação ao valor da mensalidade.
        for (ContratoModalidadePercentual obj : lista) {
            if (valorMensalidade != 0.0f) {
                obj.setPercentagem(obj.getValor() / (valorMensalidade + 0.0f));
            } else {
                obj.setPercentagem(1);
            }


        }
        return lista;
    }

    public List<LancamentoDF> processarRelatorioFechamentoCaixa(int prCodigoCaixa, boolean rateiar) throws Exception {

        List<LancamentoDF> lancamentos = new ArrayList<LancamentoDF>();

        StringBuilder sql = new StringBuilder();
        sql.append("select mov.codigo codigoMovConta, mov.pessoa, coalesce(movRat.descricao, mov.descricao) descricao , mov.empresa, mov.valor valorLancamento, ");
        sql.append("       movRat.valor valorRateio, movRat.tipoes, mov.conta, conta.tipoconta, (SELECT descricao FROM conta WHERE codigo = mov.contaorigem) as contaorigem, ");
        sql.append("       pc.codigoplanocontas, cc.codigocentrocustos, movRat.planoconta, movRat.centrocusto, pess.nome  ");
        sql.append("from MovConta mov ");
        sql.append("inner join caixaMovConta cmc on cmc.movConta = mov.codigo ");
        sql.append("inner join Pessoa pess on pess.codigo = mov.pessoa ");
        sql.append("inner join MovContaRateio movRat on movRat.movConta = mov.Codigo ");
        sql.append("inner join conta on conta.codigo = mov.conta ");
        sql.append("left join PlanoConta pc on pc.codigo = movRat.planoconta ");
        sql.append("left join centrocusto cc on cc.codigo = movRat.centrocusto ");
        sql.append(" where cmc.caixa =").append(prCodigoCaixa);
        Statement stm = con.createStatement();
        ResultSet dadosConsulta = stm.executeQuery(sql.toString());
        while (dadosConsulta.next()) {
            LancamentoDF lancamentoDF = new LancamentoDF();
            lancamentoDF.setValorLancamento(dadosConsulta.getDouble("valorRateio"));
            lancamentoDF.setDescricaoLancamento(dadosConsulta.getString("descricao") + (UteisValidacao.emptyString(dadosConsulta.getString("contaorigem")) ? "" : " - CONTA ORIGEM : "+dadosConsulta.getString("contaorigem")));
            lancamentoDF.setCodigoPessoa(dadosConsulta.getInt("pessoa"));
            lancamentoDF.setNomePessoa(dadosConsulta.getString("nome"));
            lancamentoDF.setMovConta(dadosConsulta.getInt("codigoMovConta"));
            lancamentoDF.setEmpresa(dadosConsulta.getInt("empresa"));
            lancamentoDF.setTipoConta(dadosConsulta.getInt("tipoconta"));
            lancamentoDF.setConta(dadosConsulta.getInt("conta"));



            if (rateiar) {
                // realizar os rateios do lançamento.
                RateioIntegracaoTO rateio = null;
                if (dadosConsulta.getString("codigoplanocontas") == null) {
                    // Lançamentos que não foram definidos Plano de Contas
                    rateio = this.rateioNaoDefinido;
                    rateio.setTipoES(TipoES.getTipoPadrao(dadosConsulta.getInt("tipoes")));
                } else {
                    rateio = new RateioIntegracaoTO();
                    rateio.setTipoES(TipoES.getTipoPadrao(dadosConsulta.getInt("tipoes")));
                    rateio.setCodigoPlano(dadosConsulta.getString("codigoplanocontas"));
                    rateio.setCodigoCentro(dadosConsulta.getString("codigocentrocustos"));
                    rateio.setCodigoCentroCustos(dadosConsulta.getInt("centrocusto"));
                    rateio.setCodigoPlanoContas(dadosConsulta.getInt("planoconta"));
                }

                realizarRateio(dadosConsulta.getDouble("valorRateio"),
                        rateio,
                        lancamentoDF,
                        this.mesProcessar);
            } else {
                lancamentoDF.setTipoES(TipoES.getTipoPadrao(dadosConsulta.getInt("tipoes")));
            }

            lancamentos.add(lancamentoDF);
        }
        return lancamentos;
    }

    public void setMontarNivelSuperior(boolean montarNivelSuperior) {
        this.montarNivelSuperior = montarNivelSuperior;
    }

    public boolean isMontarNivelSuperior() {
        return montarNivelSuperior;
    }

    public boolean isIncluirParcelasRecorrenciaEmRelatorioReceitaProvisao() {
        return incluirParcelasRecorrenciaEmRelatorioReceitaProvisao;
    }

    public void setIncluirParcelasRecorrenciaEmRelatorioReceitaProvisao(boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao) {
        this.incluirParcelasRecorrenciaEmRelatorioReceitaProvisao = incluirParcelasRecorrenciaEmRelatorioReceitaProvisao;
    }

    public boolean isIncluirParcelasEmAbertoEmRelatorioReceitaProvisao() {
        return incluirParcelasEmAbertoEmRelatorioReceitaProvisao;
    }

    public void setIncluirParcelasEmAbertoEmRelatorioReceitaProvisao(boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao) {
        this.incluirParcelasEmAbertoEmRelatorioReceitaProvisao = incluirParcelasEmAbertoEmRelatorioReceitaProvisao;
    }

    public boolean isApresentarDevolucoesRel() {
        return apresentarDevolucoesRel;
    }

    public void setApresentarDevolucoesRel(boolean apresentarDevolucoesRel) {
        this.apresentarDevolucoesRel = apresentarDevolucoesRel;
    }

    public class PagtoMovParcela implements Serializable {

        private int codigoMovParcela = 0;
        private double valorPago = 0;
        private double totalMovProdutoParcela = 0;
        private List<ProdutoRatear> listaProdutoRatear = new ArrayList<ProdutoRatear>();

        public int getCodigoMovParcela() {
            return codigoMovParcela;
        }

        @Override
        public boolean equals(Object obj) {
            if ((!(obj instanceof PagtoMovParcela)) || (obj == null)) {
                return false;
            }
            if (((PagtoMovParcela) obj).getCodigoMovParcela() == this.codigoMovParcela) {
                return true;
            }
            return false;
        }

        public void setCodigoMovParcela(int codigoMovParcela) {
            this.codigoMovParcela = codigoMovParcela;
        }

        public List<ProdutoRatear> getListaProdutoRatear() {
            return listaProdutoRatear;
        }

        public void setListaProdutoRatear(List<ProdutoRatear> listaProdutoRatear) {
            this.listaProdutoRatear = listaProdutoRatear;
        }

        public double getValorPago() {
            return valorPago;
        }

        public void setValorPago(double valorPago) {
            this.valorPago = valorPago;
        }

        public double getTotalMovProdutoParcela() {
            return totalMovProdutoParcela;
        }

        public void setTotalMovProdutoParcela(double totalMovProdutoParcela) {
            this.totalMovProdutoParcela = totalMovProdutoParcela;
        }
    }

    //TODO verificar necessidade
    public Map<String, TipoEquivalenciaDRE> obterEquivalencias() throws Exception {
        Map<String, TipoEquivalenciaDRE> mapa = new HashMap<String, TipoEquivalenciaDRE>();
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select codigoplanocontas, equivalenciadre from planoconta", con);
        while (resultSet.next()) {
            mapa.put(resultSet.getString(1), TipoEquivalenciaDRE.getTipoEquivalenciaDRE(resultSet.getInt(2)));
        }
        return mapa;
    }

    public void setarRateioCentroCustos(Double valor, DemonstrativoFinanceiro df, LancamentoDF lancamento) {
        List<RateioIntegracaoTO> listaRateios = new ArrayList<RateioIntegracaoTO>();
        if (!UteisValidacao.emptyNumber(lancamento.getMovConta())) {
            for (RateioIntegracaoTO rt : lancamento.getRateios()) {
                if (!UteisValidacao.emptyString(rt.getCodigoCentro())) {
                    listaRateios.add(rt);
                }
            }

        } else if (lancamento.getCmRec() == null) {
            listaRateios = lancamento.getRateiosDRE();
        } else {
            listaRateios = pesquisarRateios(this.listaRateiosIntegracao,
                    TipoCamposPesquisarRateio.MODALIDADE,
                    lancamento.getCmRec().getCodigoModalidade(),
                    TipoVisualizacaoRelatorioDF.CENTROCUSTO,null);
        }
        if (listaRateios.isEmpty()) {
            RateioIntegracaoTO rt = new RateioIntegracaoTO();
            rt.setPercentagem(1.0);
            rt.setCodigoCentroCustos(0);
            listaRateios.add(rt);
        }
        for (RateioIntegracaoTO rt : listaRateios) {
            int indexOf = df.getListaCentros().indexOf(new CentroCustosDRE(rt.getCodigoCentroCustos()));
            if (indexOf < 0) {
                indexOf = df.getListaCentros().indexOf(new CentroCustosDRE(0));
            }
            LancamentoDF clone = lancamento.clone();
            if (UteisValidacao.emptyNumber(lancamento.getMovConta()) && !UteisValidacao.emptyNumber(rt.getCodigo())) {
                clone.setValorLancamento(valor * rt.getPercent());
            } else {
                clone.setValorLancamento(valor);
            }
            if (df.getListaCentros().get(indexOf).getLancamentos().contains(clone)) {
                int index = df.getListaCentros().get(indexOf).getLancamentos().indexOf(clone);
                LancamentoDF lancamentoDF = df.getListaCentros().get(indexOf).getLancamentos().get(index);
                lancamentoDF.setValorLancamento(lancamentoDF.getValorLancamento() + clone.getValorLancamento());
                df.getListaCentros().get(indexOf).setTotal(df.getListaCentros().get(indexOf).getTotal() + clone.getValorLancamento());
            } else {
                df.getListaCentros().get(indexOf).getLancamentos().add(clone);
                df.getListaCentros().get(indexOf).setTotal(df.getListaCentros().get(indexOf).getTotal() + clone.getValorLancamento());
            }
        }
    }
    
    
    public static GenericoAtributosVO consultarCodigoCategoriaProdutoCredito(Connection con) throws SQLException {
        GenericoAtributosVO generico = new GenericoAtributosVO();
    	StringBuilder sql = new StringBuilder();
        sql.append("select codigo, categoriaProduto ");
        sql.append("from produto");
        sql.append(" where tipoproduto = 'CC' ");
        Statement stm = con.createStatement();
        ResultSet resultDados = stm.executeQuery(sql.toString());
        List<ContratoModalidadePercentual> lista = new ArrayList<ContratoModalidadePercentual>();
        double valorMensalidade = 0.0f;
        while (resultDados.next()) {
            generico.adicionarAtributo("codigoProdutoCredito", resultDados.getInt("codigo"));
            generico.adicionarAtributo("categoriaProdutoCredito", resultDados.getInt("categoriaproduto"));
        }
        return generico;
    }
    
    private List<LancamentoDF> consultarPagtosCentralEventosPorFormaPgto(MesProcessar mesProcessar, int empresa, TipoFormaPagto formaPgto, TipoRelatorioDF tipo) throws Exception {
        StringBuilder sql = new StringBuilder();
        Date dataIni = Uteis.getDateTime(mesProcessar.getDataIni().getTime(), 0, 0, 0);
        Date dataFim = Uteis.getDateTime(mesProcessar.getDataFim().getTime(), 23, 59, 59);
        
		switch (tipo) {
		case RECEITA: {
			switch (formaPgto) {
			case AVISTA:
				montarSQLCEVista(empresa, sql, dataIni, dataFim);
				break;
			case CARTAOCREDITO:
				montarSQLCE(empresa, sql, dataIni, dataFim, "cartaocredito", "CA");
				break;
			case CHEQUE:
				montarSQLCE(empresa, sql, dataIni, dataFim, "cheque", "CH");
				break;
			}
		}
			break;
		case FATURAMENTO_DE_CAIXA:
                        montarSQLCEFatuamentoRecebido(empresa, sql, dataIni, dataFim);
			break;
                case FATURAMENTO: 
			montarSQLCEFaturamentoCompetencia(empresa, sql, dataIni, dataFim, false);
			break;    
		default:
			montarSQLCEFaturamentoCompetencia(empresa, sql, dataIni, dataFim, true);
			break;
			
		}
        
        Statement stm = con.createStatement();
        ResultSet dadosMovPagamento = stm.executeQuery(sql.toString());

        List<LancamentoDF> listaLancamentosDF = adicionarDadosCentralEventos(dadosMovPagamento, formaPgto);
        return listaLancamentosDF;
    }
    
   

	/**
	 * Responsável por 
	 * <AUTHOR> Alcides
	 * 14/03/2013
	 */
	private void montarSQLCEVista(int empresa, StringBuilder sql, Date dataIni, Date dataFim) throws Exception {
		StringBuilder formaPagtos = new StringBuilder();
		formaPagtos.append("'CA','CH','CC'");
		sql.append(" SELECT distinct ne.*, nec.nomeevento, mp.valor as valorpagamento, mp.codigo as movpag, mp.empresa as empresapag, \n");
		sql.append("  mp.empresa as empresapag, mp.pessoa as pessoapag, mp.nomepagador FROM movpagamento mp \n");
		sql.append(" inner join formaPagamento formaPag on formaPag.codigo = mp.formapagamento  \n");
		sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mp.codigo\n");
		sql.append(" INNER JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = pmp.movparcela\n");
		sql.append(" INNER JOIN negociacaoeventocontrato nec ON nec.codigo = necp.contrato\n");
		sql.append(" INNER JOIN negociacaoevento ne ON ne.eventointeresse = nec.eventointeresse\n");
		sql.append(" where (formaPag.tipoformapagamento not in (").append(formaPagtos).append("))\n");
		sql.append(" AND not credito "); 
                if(!UteisValidacao.emptyNumber(empresa)){
                    sql.append(" and mp.empresa = ").append(empresa);
                }
		sql.append(" and ((mp.datalancamento >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"))
				        .append("') and (mp.datalancamento <= '")
				        .append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
		
//		if(faturamento){
//			sql.append(" and ((ne.dataevento >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"))
//	        .append("') and (ne.dataevento <= '")
//	        .append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
//		}
	}
        
        private void montarSQLCEFatuamentoRecebido(int empresa, StringBuilder sql, Date dataIni, Date dataFim) throws Exception {
		StringBuilder formaPagtos = new StringBuilder();
		formaPagtos.append("'CC'");
		sql.append(" SELECT distinct ne.*, nec.nomeevento, mp.valor as valorpagamento, mp.codigo as movpag, mp.empresa as empresapag, \n");
		sql.append("  mp.empresa as empresapag, mp.pessoa as pessoapag, mp.nomepagador, formaPag.tipoformapagamento  FROM movpagamento mp \n");
		sql.append(" inner join formaPagamento formaPag on formaPag.codigo = mp.formapagamento  \n");
		sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mp.codigo\n");
		sql.append(" INNER JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = pmp.movparcela\n");
		sql.append(" INNER JOIN negociacaoeventocontrato nec ON nec.codigo = necp.contrato\n");
		sql.append(" INNER JOIN negociacaoevento ne ON ne.eventointeresse = nec.eventointeresse\n");
		sql.append(" where (formaPag.tipoformapagamento not in (").append(formaPagtos).append("))\n");
		sql.append(" AND not credito "); 
                if(!UteisValidacao.emptyNumber(empresa)){
                    sql.append(" and mp.empresa = ").append(empresa);
                }
		sql.append(" and ((mp.datalancamento >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"))
				        .append("') and (mp.datalancamento <= '")
				        .append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
		
//		if(faturamento){
//			sql.append(" and ((ne.dataevento >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"))
//	        .append("') and (ne.dataevento <= '")
//	        .append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
//		}
	}

	/**
	 * Responsável por 
	 * <AUTHOR> Alcides
	 * 14/03/2013
	 */
	private void montarSQLCE(int empresa, StringBuilder sql, Date dataIni, Date dataFim, String nomeTabela, String fp) throws Exception {
		sql.append(" select distinct ne.codigo,ne.valortotal, nec.nomeevento, "+nomeTabela+".valor as valorpagamento, \n");
		sql.append(" mp.codigo as movpag, mp.empresa as empresapag, mp.pessoa as pessoapag, mp.nomepagador\n");
		sql.append(" from ").append(nomeTabela).append(" \n");
		sql.append(" inner join MovPagamento mp on mp.codigo = "+nomeTabela+".movpagamento \n");
		sql.append(" inner join formaPagamento formaPag on formaPag.codigo = mp.formapagamento  \n");
		sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mp.codigo\n");
		sql.append(" INNER JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = pmp.movparcela\n");
		sql.append(" INNER JOIN negociacaoeventocontrato nec ON nec.codigo = necp.contrato\n");
		sql.append(" INNER JOIN negociacaoevento ne ON ne.eventointeresse = nec.eventointeresse\n");
		sql.append(" where (formaPag.tipoformapagamento = '").append(fp).append("')  ");
                if(!UteisValidacao.emptyNumber(empresa)){
                    sql.append(" and mp.empresa = ").append(empresa);
                    
                }
		sql.append(" and (("+nomeTabela+".datacompesancao >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00")).append("') \n");
		sql.append(" and ("+nomeTabela+".datacompesancao <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("')) and "+nomeTabela+".situacao != 'CA' \n");
	}

    private void montarSQLCEFaturamentoCompetencia(int empresa, StringBuilder sql, Date dataIni, Date dataFim, boolean competencia) throws Exception {
        sql.append(" SELECT ne.*, ei.nomeevento, ne.valortotal as valorpagamento, 0 as movpag, ne.empresa as empresapag, \n");
        sql.append(" p.codigo as pessoapag, p.nome as nomepagador from negociacaoevento ne \n");
        sql.append(" inner join eventointeresse ei on ei.codigo = ne.eventointeresse \n");
        sql.append(" inner join interessado i on i.codigo = ei.interessado \n");
        sql.append(" inner join pessoa p on p.codigo = i.pessoa \n");
        sql.append(" WHERE ");
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append(" empresa = ").append(empresa).append(" AND ");
        }
        if (competencia) {
            sql.append("  ((ne.dataevento >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"))
                    .append("') and (ne.dataevento <= '")
                    .append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
        } else {
            sql.append("  ((ne.datacadastro  >= '").append(Uteis.getDataHoraJDBC(Calendario.getDataComHoraZerada(dataIni), "00:00:00"))
                    .append("') and (ne.datacadastro  <= '")
                    .append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
        }
    }
	/**
	 * <AUTHOR> Alcides
	 * 14/03/2013
	 */
	private List<LancamentoDF> adicionarDadosCentralEventos(ResultSet dadosMovPagamento, TipoFormaPagto tipoFP) throws Exception, SQLException {

        NegEvPerfilEventoAmbiente negEvAmbiente = new NegEvPerfilEventoAmbiente(con);
        NegEvPerfilEventoServico negEvServico = new NegEvPerfilEventoServico(con);
        NegEvPerfilEventoProdutoLocacao negEvProdLoc = new NegEvPerfilEventoProdutoLocacao(con);
        NegEvCaucaoCredito negEvCredito = new NegEvCaucaoCredito(con);
        
        List<LancamentoDF> listaLancamentosDF = new ArrayList<LancamentoDF>();
        TipoFormaPagto formasEnum = TipoFormaPagto.AVISTA;
        while (dadosMovPagamento.next()) {
     //---------------------------AMBIENTES----------------------------------------//   	
        	
        	List<NegEvPerfilEventoAmbienteTO> ambientes = negEvAmbiente.consultarPorNegociacaoEvento(dadosMovPagamento.getInt("codigo"));
        	double valorPagamento = dadosMovPagamento.getDouble("valorpagamento");
        	
        	for(NegEvPerfilEventoAmbienteTO amb : ambientes){
//        		amb.setPerfilEventoSazonalidadeTOs(CEControle.getCEFacade().consultarPorPerfilEventoAmbiente(pea.getCodigo()));
        		addLancamentoCentralEventos(dadosMovPagamento, amb.getDescricaoAmbiente(), 
        								(valorPagamento*(amb.getValor()+(amb.getVlrSazonalidade())-amb.getDesconto()))/dadosMovPagamento.getDouble("valortotal"),
        								amb.getCodigoAmbiente(), TipoCamposPesquisarRateio.AMBIENTE, TipoCamposPesquisarRateio.AMBIENTES, 
        								amb.getCodigoAmbiente(), (tipoFP != null ? tipoFP : formasEnum.getTipoFormaPagtoSigla(dadosMovPagamento.getString("tipoformapagamento"))) );
            }
//---------------------------SERVICOS----------------------------------------//
        	
        	List<NegEvPerfilEventoServicoTO> servicos = negEvServico.consultarPorNegociacaoEvento(dadosMovPagamento.getInt("codigo"));
        	for(NegEvPerfilEventoServicoTO serv : servicos){
        		addLancamentoCentralEventos(dadosMovPagamento, serv.getDescricaoServico(), 
        								(valorPagamento*serv.getValor())/dadosMovPagamento.getDouble("valortotal"), 
        								serv.getCodigoServico(), TipoCamposPesquisarRateio.SERVICO, 
        								TipoCamposPesquisarRateio.SERVICOS, serv.getCodigoServico(), (tipoFP != null ? tipoFP : formasEnum.getTipoFormaPagtoSigla(dadosMovPagamento.getString("tipoformapagamento"))));
            }
//---------------------------PRODUTO LOCACAO----------------------------------------//
        	for(TipoProdutoLocacao tipo : TipoProdutoLocacao.values()){
        		List<NegEvPerfilEventoProdutoLocacaoTO> produtos = negEvProdLoc.consultarPorNegociacaoEvento(dadosMovPagamento.getInt("codigo"), tipo);
        		
        		for(NegEvPerfilEventoProdutoLocacaoTO prod : produtos){
        			addLancamentoCentralEventos(dadosMovPagamento, prod.getDescricaoProdutoLocacao(), 
    						(valorPagamento*prod.getValor())/dadosMovPagamento.getDouble("valortotal"), 
    						prod.getCodigoProdutoLocacao(), TipoCamposPesquisarRateio.PRODUTO_CE, 
    						TipoCamposPesquisarRateio.TIPO_PRODUTO, tipo.getEntidade().getTipo(), (tipoFP != null ? tipoFP : formasEnum.getTipoFormaPagtoSigla(dadosMovPagamento.getString("tipoformapagamento"))));
        		}
        	}
//---------------------------crédito de bebidas----------------------------------------//
                List<NegEvCaucaoCreditoTO> creditos = negEvCredito.consultarPorNegociacaoEvento(dadosMovPagamento.getInt("codigo"), TipoCaucaoCredito.CREDITO, true);
                if (creditos != null) {
                    for (NegEvCaucaoCreditoTO credito : creditos) {
                        addLancamentoCentralEventos(dadosMovPagamento, "CRÉDITO",
                                (valorPagamento * credito.getValor()) / dadosMovPagamento.getDouble("valortotal"),
                                credito.getCodigo(), TipoCamposPesquisarRateio.CREDITO,
                                TipoCamposPesquisarRateio.CREDITO, credito.getCodigo(), (tipoFP != null ? tipoFP : formasEnum.getTipoFormaPagtoSigla(dadosMovPagamento.getString("tipoformapagamento"))));
                    }
                }
                
        }
		return listaLancamentosDF;
	}
    
    private void processarDadosPorReceitaCentralEventos(MesProcessar mesProcessar, int empresa) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        consultarPagtosCentralEventosPorFormaPgto(mesProcessar, empresa, TipoFormaPagto.AVISTA, TipoRelatorioDF.RECEITA);
        consultarPagtosCentralEventosPorFormaPgto(mesProcessar, empresa, TipoFormaPagto.CARTAOCREDITO, TipoRelatorioDF.RECEITA);
        consultarPagtosCentralEventosPorFormaPgto(mesProcessar, empresa, TipoFormaPagto.CHEQUE, TipoRelatorioDF.RECEITA);
        
    }
    
    private void processarDadosPorFaturamentoCaixaCentralEventos(MesProcessar mesProcessar, int empresa) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        consultarPagtosCentralEventosPorFormaPgto(mesProcessar, empresa, null, TipoRelatorioDF.FATURAMENTO_DE_CAIXA);
    }
    
    private void processarDadosPorCompetenciaCentralEventos(MesProcessar mesProcessar, int empresa) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        consultarPagtosCentralEventosPorFormaPgto(mesProcessar, empresa, TipoFormaPagto.AVISTA, TipoRelatorioDF.COMPETENCIA);
        
    }
    
   private void processarDadosPorFaturamentoCentralEventos(MesProcessar mesProcessar, int empresa) throws Exception {
        this.listaRateiosIntegracao = consultarTodosRateiosIntegracao(this.con);
        consultarPagtosCentralEventosPorFormaPgto(mesProcessar, empresa, TipoFormaPagto.AVISTA, TipoRelatorioDF.FATURAMENTO);
        
    }
    
    
    
    private void addLancamentoCentralEventos(ResultSet dadosMovPagamento, String descricao,
            Double valorLancamento, Integer codigoEntidade, TipoCamposPesquisarRateio tipoRateio,
            TipoCamposPesquisarRateio tipoSuperRateio, Integer codSuperEntidade, TipoFormaPagto tipoForma) throws Exception {
        LancamentoDF lanc = new LancamentoDF();
        lanc.setDescricaoLancamento(dadosMovPagamento.getString("nomeevento") + " - " + descricao);
        lanc.setValorLancamento(valorLancamento);
        lanc.setTipoFormaPagto(tipoForma);
        lanc.setCodigoPessoa(dadosMovPagamento.getInt("pessoapag"));
        lanc.setNomePessoa(dadosMovPagamento.getString("nomepagador"));
        lanc.setEmpresa(dadosMovPagamento.getInt("empresapag"));
        lanc.setMovPagamento(dadosMovPagamento.getInt("movpag"));
        lanc.setNegociacaoEvento(dadosMovPagamento.getInt("codigo"));
        lanc.setMovProduto(codigoEntidade);
        List<RateioIntegracaoTO> listaRateios = pesquisarRateios(this.listaRateiosIntegracao,
                tipoRateio,
                codigoEntidade,
                tipoVisualizacao,
                null);
        lanc.setRateios(listaRateios);
        if (listaRateios.isEmpty()) {
            listaRateios = pesquisarRateios(this.listaRateiosIntegracao,
                    tipoSuperRateio,
                    codSuperEntidade,
                    tipoVisualizacao,
                    null);
            lanc.setRateios(listaRateios);
            if (listaRateios.isEmpty()) {
                realizarRateio(lanc.getValorLancamento(), this.rateioNaoDefinido, lanc, mesProcessar);
            }
        }

        if (dre) {
            // Pesquisar rateios
            List<RateioIntegracaoTO> listaRateiosDRE = pesquisarRateios(this.listaRateiosIntegracao,
                    tipoRateio,codigoEntidade,TipoVisualizacaoRelatorioDF.CENTROCUSTO,
                    null);
            if (listaRateiosDRE.size() <= 0) {
                // Pesquisar rateios definido para a categoria do produto.
                listaRateiosDRE = pesquisarRateios(this.listaRateiosIntegracao,tipoSuperRateio,
                        codSuperEntidade, TipoVisualizacaoRelatorioDF.CENTROCUSTO,
                        null);

            }
            lanc.setRateiosDRE(listaRateiosDRE);
            lanc.setCmRec(null);
        }


        if (this.listaFiltroCentroCusto.size() > 0) {

            List<RateioIntegracaoTO> listaRateiosCC = pesquisarRateios(this.listaRateiosIntegracao,
                    tipoRateio, codigoEntidade, TipoVisualizacaoRelatorioDF.CENTROCUSTO,null);

            double valorRatear = 0.0;
            if (listaRateiosCC == null || listaRateiosCC.isEmpty()) {
                listaRateiosCC = pesquisarRateios(this.listaRateiosIntegracao,
                        tipoSuperRateio, codSuperEntidade, TipoVisualizacaoRelatorioDF.CENTROCUSTO,null);
            }
            for (RateioIntegracaoTO rateio : listaRateiosCC) {
                if (this.listaFiltroCentroCusto.contains(rateio.getCodigoCentroCustos())) {
                    valorRatear += (lanc.getValorLancamento() * rateio.getPercent());
                }
            }
            if (valorRatear > 0.0) {
                for (RateioIntegracaoTO rt : listaRateios) {
                    realizarRateio(valorRatear, rt, lanc, mesProcessar);
                }
            }

        } else {
            for (RateioIntegracaoTO rt : listaRateios) {
                realizarRateio(lanc.getValorLancamento(), rt, lanc, mesProcessar);
            }
        }


    }


    public static StringBuilder consultaFaturamentoRecebidoCC(int empresa, Date dataIni, Date dataFim, Date dataReceb,
           String tipo, Integer operador, Integer consultor) throws Exception {
        //Atenção! ao alterar essa sql, a sql do metodo consultaFaturamentoRecebido() dever ser avaliada, pois existe a possibilidade de união entres essas
        StringBuilder sql = new StringBuilder();
        sql.append("\nUNION ALL \n ");
        sql.append("select movPag.pessoa,movPag.nomepagador,movPag.empresa as codigoempresa, movPag.produtospagos, movPag.codigo CodigoMovPagamento,usu.codigo as codresponsavelpagamento, usu.nome as responsavelpagamento, movPag.valortotal valor, movPag.recibopagamento, movPag.datalancamento,movPag.formapagamento, formaPag.tipoformapagamento,formaPag.descricao pagDesc, movPag.produtospagoscancelados \n");
        sql.append("from MovPagamento movPag  \n");
        sql.append("inner join formaPagamento formaPag on formaPag.codigo = movPag.formapagamento  \n");
        sql.append("inner join movProdutoParcela movProdParc on movProdParc.recibopagamento = movPag.recibopagamento \n");
        sql.append("inner join MovProduto movProd on movProd.codigo = movProdParc.movproduto\n");
        sql.append("inner join recibopagamento rp on rp.codigo = movPag.recibopagamento \n");
        if(!UteisValidacao.emptyString(tipo) && !tipo.equals("'CC'")){
            sql.append("INNER JOIN produto p ON movProd.produto = p.codigo AND p.tipoproduto  in (").append(tipo).append(") \n");
        }
        if(!UteisValidacao.emptyNumber(consultor)){
            sql.append(" INNER JOIN cliente cli ON movPag.pessoa = cli.pessoa\n");
            sql.append(" INNER JOIN vinculo vin ON vin.cliente = cli.codigo and vin.colaborador = ").append(consultor).append(" \n");
        }
         sql.append(" INNER JOIN usuario usu ON usu.codigo = movPag.responsavelpagamento ");
        if (!UteisValidacao.emptyNumber(operador)) {
           sql.append(" and usu.colaborador = ").append(operador).append("\n");
        }
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and movPag.empresa = ").append(empresa).append(" \n");
        }
        sql.append(" and (formaPag.tipoFormaPagamento = 'CC')  \n");
        sql.append(" and ((movPag.datalancamento >= '").append(Uteis.getDataJDBC(dataIni)).append("') and (movPag.datalancamento <= '").append(Uteis.getDataHoraJDBC(dataFim, "23:59:59")).append("'))");
        if (dataReceb != null) {
            sql.append(" and (movPag.datalancamento >= '").append(Uteis.getDataJDBC(dataReceb)).append("')\n");
        }
        sql.append("group by movPag.pessoa,movPag.nomepagador,movPag.empresa,movPag.codigo, usu.codigo, usu.nome,movPag.valortotal, movPag.recibopagamento, movPag.produtospagos, movPag.datalancamento,movPag.formapagamento, formaPag.tipoformapagamento,formaPag.descricao     ");
        return sql;
    }


}
