package controle.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Arrays;
import java.util.List;

public enum GrupoFuncionalidadeSistemaEnum {

    /**
     * MODULO ADM
     */

    EST_CONFIG("Inicio", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CONFIG_EST
    }, MenuFuncionalidadeEnum.EST_INICIO),
    EST_SERVICOS("Serviços", "fa-icon-calendar", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.DISPONIBILIDADE_EST,
            FuncionalidadeSistemaEnum.VENDA_AVULSA_EST,
            FuncionalidadeSistemaEnum.CAIXA_ABERTO_EST,
            FuncionalidadeSistemaEnum.PACOTE_EST
    }, MenuFuncionalidadeEnum.EST_INICIO),
    EST_AGENDAS("Agendas", "fa-icon-calendar-check-o", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.AGENDA_MENSAL_EST,
            FuncionalidadeSistemaEnum.AMBIENTE_EST,
            FuncionalidadeSistemaEnum.PROFISSIONAL_EST,
            FuncionalidadeSistemaEnum.INDIVIDUAL_EST
    }, MenuFuncionalidadeEnum.EST_INICIO),
    ADM_BOLETIM_VISITA("Boletim de visita", "fa-icon-file-alt", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.PERGUNTA,
            FuncionalidadeSistemaEnum.QUESTIONARIO
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_AUXILIARES),
    ADM_CADASTROS_AUXILIARES("Cadastros Auxiliares", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.COLABORADOR,
            FuncionalidadeSistemaEnum.CLIENTE,
            FuncionalidadeSistemaEnum.CATEGORIA_CLIENTES,
            FuncionalidadeSistemaEnum.PROFISSAO,
            FuncionalidadeSistemaEnum.DEPARTAMENTO,
            FuncionalidadeSistemaEnum.GRAU_DE_INSTRUCAO,
            FuncionalidadeSistemaEnum.CLASSIFICACAO,
            FuncionalidadeSistemaEnum.GRUPO_DESCONTO,
            FuncionalidadeSistemaEnum.PARENTESCO,
            FuncionalidadeSistemaEnum.PAIS,
            FuncionalidadeSistemaEnum.CIDADE,
            FuncionalidadeSistemaEnum.PESQUISA
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_AUXILIARES, "false"),

    ADM_CONTROLE_ESTOQUE("Controle de Estoque", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.BALANCO,
            FuncionalidadeSistemaEnum.COMPRA,
            FuncionalidadeSistemaEnum.CARDEX,
            FuncionalidadeSistemaEnum.CONFIGURAR_PRODUTO_ESTOQUE,
            FuncionalidadeSistemaEnum.POSICAO_DO_ESTOQUE
    }, MenuFuncionalidadeEnum.ADM_INICIO, false, true),
    ADM_PRODUTOS("Produtos", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CATEGORIA_PRODUTO,
            FuncionalidadeSistemaEnum.PRODUTO,
            FuncionalidadeSistemaEnum.LANCAMENTO_PRODUTO_COLETIVO,
            FuncionalidadeSistemaEnum.TAMANHO_ARMARIO
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_PRODUTOS_PLANOS_TURMAS, new GrupoFuncionalidadeSistemaEnum[]{
            ADM_CONTROLE_ESTOQUE
    }),
    ADM_PLANOS("Planos", "fa-icon-file-alt", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.PLANO,
            FuncionalidadeSistemaEnum.HORARIO,
            FuncionalidadeSistemaEnum.DESCONTO,
            FuncionalidadeSistemaEnum.PACOTE,
            FuncionalidadeSistemaEnum.CONDICAO_DE_PAGAMENTO,
            FuncionalidadeSistemaEnum.CONVENIO_DE_DESCONTO,
            FuncionalidadeSistemaEnum.TIPO_PLANO
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_PRODUTOS_PLANOS_TURMAS),
    ADM_TURMAS("Turmas", "fa-icon-file-alt", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.MODALIDADE,
            FuncionalidadeSistemaEnum.TIPO_MODALIDADE,
            FuncionalidadeSistemaEnum.AMBIENTE,
            FuncionalidadeSistemaEnum.NIVEL_TURMA,
            FuncionalidadeSistemaEnum.TURMA
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_PRODUTOS_PLANOS_TURMAS),


    ADM_CONFIG_FINANCEIRAS("Config. Financeiras", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CONSULTA_DE_RECIBOS,
            FuncionalidadeSistemaEnum.CONSULTA_DE_CUPONS_FISCAIS,
            FuncionalidadeSistemaEnum.GESTAO_NFCE,
            FuncionalidadeSistemaEnum.GESTAO_NOTAS,
            FuncionalidadeSistemaEnum.CONFIGURACAO_NOTAFISCAL,
            FuncionalidadeSistemaEnum.VENDA_CONSUMIDOR,
            FuncionalidadeSistemaEnum.MOVIMENTO_CC_CLIENTE,
            FuncionalidadeSistemaEnum.BANCO,
            FuncionalidadeSistemaEnum.CONTA_CORRENTE,
            FuncionalidadeSistemaEnum.TIPO_RETORNO,
            FuncionalidadeSistemaEnum.TIPO_REMESSA,
            FuncionalidadeSistemaEnum.CONVENIO_COBRANCA,
            FuncionalidadeSistemaEnum.PINPAD,
            FuncionalidadeSistemaEnum.FORMA_PAGAMENTO,
            FuncionalidadeSistemaEnum.OPERADORA_CARTAO,
            FuncionalidadeSistemaEnum.METAS_FINANCEIRO_VENDA,
            FuncionalidadeSistemaEnum.TAXA_COMISSAO,
            FuncionalidadeSistemaEnum.INDICE_FINANCEIRO_REAJUSTE_PRECO,
            FuncionalidadeSistemaEnum.ADQUIRENTE,
            FuncionalidadeSistemaEnum.MODELO_ORCAMENTO,
            FuncionalidadeSistemaEnum.IMPOSTO
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_CONFIG_FINANCEIRAS, false, true),


    ADM_ACESSO_SISTEMA("Acesso ao Sistema", "fa-icon-key", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.EMPRESA,
            FuncionalidadeSistemaEnum.PERFIL_ACESSO,
            FuncionalidadeSistemaEnum.USUARIO,
            FuncionalidadeSistemaEnum.LOCAL_ACESSO,
            FuncionalidadeSistemaEnum.LOCAL_IMPRESSAO,
            FuncionalidadeSistemaEnum.SERVIDOR_FACIAL,
            FuncionalidadeSistemaEnum.CONTROLE_LOG,
            FuncionalidadeSistemaEnum.INTEGRACAO_ACESSO,
            FuncionalidadeSistemaEnum.AUTORIZACAO_ACESSO,
            FuncionalidadeSistemaEnum.GERADOR_CONSULTAS,
            FuncionalidadeSistemaEnum.IMPORTACAO,
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_ACESSO_SISTEMA, false, true),


    ADM_CONFIG_CONTRATO("Config. Contrato", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.MODELO_CONTRATO,
            FuncionalidadeSistemaEnum.MOVIMENTO_PRODUTO,
            FuncionalidadeSistemaEnum.JUSTIFICATIVA_OPERACAO,
            FuncionalidadeSistemaEnum.IMPRIME_RECIBO_BANCO,
    }, MenuFuncionalidadeEnum.ADM_CADASTROS_CONFIG_CONTRATO),

    ADM_RELATORIO_CLIENTES("Relatório de clientes", "fa-icon-user", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.GERAL_CLIENTES,
            FuncionalidadeSistemaEnum.RELATORIO_VISITANTES,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_CANCELADOS,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_TRANCADOS,
            FuncionalidadeSistemaEnum.RELATORIO_BONUS,
            FuncionalidadeSistemaEnum.RELATORIO_ATESTADO,
            FuncionalidadeSistemaEnum.ANIVERSARIANTES,
            FuncionalidadeSistemaEnum.CONTRATOS_DURACAO,
            FuncionalidadeSistemaEnum.GYM_PASS_RELATORIO,
            FuncionalidadeSistemaEnum.GOGOOD_RELATORIO,
            FuncionalidadeSistemaEnum.LISTA_CLIENTES_SIMPLIFICADA,
            FuncionalidadeSistemaEnum.SALDO_CREDITO,
            FuncionalidadeSistemaEnum.HISTORICO_PONTOS_ALUNO,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_ORCAMENTOS,
            FuncionalidadeSistemaEnum.RELATORIO_CONVIDADOS,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_COBRANCA_BLOQUEADA,
            FuncionalidadeSistemaEnum.CLIENTES_COM_RESTRICOES,
            FuncionalidadeSistemaEnum.SMD_RELATORIO,
    }, MenuFuncionalidadeEnum.ADM_BI, "false"),
    ADM_ACESSOS("Acessos", "fa-icon-signin", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.LISTA_ACESSOS,
            FuncionalidadeSistemaEnum.TOTALIZADOR_ACESSOS,
            FuncionalidadeSistemaEnum.FECHAMENTO_ACESSOS,
            FuncionalidadeSistemaEnum.REGISTRAR_ACESSO_AVULSO,
            FuncionalidadeSistemaEnum.INDICADOR_ACESSOS,
            FuncionalidadeSistemaEnum.TOTALIZADOR_TICKETS
    }, MenuFuncionalidadeEnum.ADM_BI),
    ADM_RELATORIO_ARMARIOS("Relatório de Armários", "fa-icon-inbox", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.RELATORIO_GERAL_ARMARIOS
    }, MenuFuncionalidadeEnum.ADM_BI, "false"),
    ADM_RELATORIO_TURMAS("Turmas", "fa-icon-group", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.LISTA_CHAMADA,
            FuncionalidadeSistemaEnum.FREQUENCIA_OCUPACAO_TURMAS,
            FuncionalidadeSistemaEnum.DESCONTO_OCUPACAO_TURMAS,
            FuncionalidadeSistemaEnum.MAPA_TURMAS,
            FuncionalidadeSistemaEnum.CONSULTA_DE_TURMAS
    }, MenuFuncionalidadeEnum.ADM_BI),
    ADM_RELATORIO_FINANCIERO("Relatório Financeiro", "fa-icon-money", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.FECHAMENTO_CAIXA_OPERADOR,
            FuncionalidadeSistemaEnum.COMPETENCIA_MENSAL,
            FuncionalidadeSistemaEnum.FATURAMENTO_PERIODO,
            FuncionalidadeSistemaEnum.FATURAMENTO_RECEBIDO_PERIODO,
            FuncionalidadeSistemaEnum.RECEITA_PERIODO,
            FuncionalidadeSistemaEnum.RELATORIO_PARCELAS,
            FuncionalidadeSistemaEnum.RELATORIO_PRODUTOS,
            FuncionalidadeSistemaEnum.SALDO_CONTA_CORRENTE,
            FuncionalidadeSistemaEnum.TRANSACOES_PIX,
            FuncionalidadeSistemaEnum.PEDIDOS_PINPAD
    }, MenuFuncionalidadeEnum.ADM_BI),
    ADM_RELATORIO_ESTATISTICO("Relatório Estatístico", "fa-icon-line-chart", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.PREVISAO_RENOVACAO_CONTRATO,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES,
            FuncionalidadeSistemaEnum.RELATORIO_BVS,
            FuncionalidadeSistemaEnum.RELATORIO_REPASSE,
            FuncionalidadeSistemaEnum.RELATORIO_PESQUISA,
            FuncionalidadeSistemaEnum.SGP_MODALIDADES_COM_TURMAS,
            FuncionalidadeSistemaEnum.SGP_MODALIDADES_SEM_TURMAS,
            FuncionalidadeSistemaEnum.SGP_TURMAS,
            FuncionalidadeSistemaEnum.SGP_AVALIACOES_FISICAS
    }, MenuFuncionalidadeEnum.ADM_BI),
    ADM_RELATORIO_ESTUDIO("Estúdio", "icon-estudio", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.COMISSAO_EST,
            FuncionalidadeSistemaEnum.DIARIO,
            FuncionalidadeSistemaEnum.AGENDAMENTOS,
            FuncionalidadeSistemaEnum.CLIENTES_SEM_SESSAO
    }, MenuFuncionalidadeEnum.ADM_BI, "LoginControle.apresentarLinkEstudio"),
    ADM_BUSINESS_INTELLIGENCE_ZW_UI_NAO_TRABALHA_COM_PONTUCAO(FuncionalidadeSistemaEnum.BUSINESS_INTELLIGENCE, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE(FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE, MenuFuncionalidadeEnum.ADM_INICIO, true, false),


    ADM_AUTORIZACAO_ACESSO_ZW_UI(FuncionalidadeSistemaEnum.AUTORIZACAO_ACESSO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_BALANCO_ZW_UI(FuncionalidadeSistemaEnum.BALANCO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_CAIXA_EM_ABERTO_ZW_UI(FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_CADEX_ZW_UI(FuncionalidadeSistemaEnum.CARDEX, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_COMPRA_ZW_UI(FuncionalidadeSistemaEnum.COMPRA, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_CONFIGURAR_PRODUTO_ESTOQUE_ZW_UI(FuncionalidadeSistemaEnum.CONFIGURAR_PRODUTO_ESTOQUE, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_ORCAMENTO_ZW_UI(FuncionalidadeSistemaEnum.ORCAMENTO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_DIARIA_ZW_UI(FuncionalidadeSistemaEnum.DIARIA, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_FREEPASS_ZW_UI(FuncionalidadeSistemaEnum.FREE_PASS, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_DE_ARMARIOS_ZW_UI(FuncionalidadeSistemaEnum.GESTAO_DE_ARMARIOS, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_NEGATIVACOES_ZW_UI(FuncionalidadeSistemaEnum.GESTAO_NEGATIVACOES, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_PERSONAL_ZW_UI(FuncionalidadeSistemaEnum.GESTAO_PERSONAL, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_DE_REMESSAS_ZW_UI(FuncionalidadeSistemaEnum.GESTAO_DE_REMESSAS, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_DE_BOLETOS_ONLINE_ZW_UI(FuncionalidadeSistemaEnum.GESTAO_DE_BOLETOS_ONLINE, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_DE_TRANSACOES_ZW_UI(FuncionalidadeSistemaEnum.GESTAO_DE_TRANSACOES, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_DE_TURMA_ZW_UI(FuncionalidadeSistemaEnum.GESTAO_DE_TURMA, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_GESTAO_VENDAS_ONLINE_ZW_UI(FuncionalidadeSistemaEnum.VENDAS_ONLINE, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_RECIBO_EM_BRANCO_ZW_UI(FuncionalidadeSistemaEnum.IMPRIME_RECIBO_BANCO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_NEGOCIACAO_ZW_UI(FuncionalidadeSistemaEnum.NEGOCIACAO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_OPERACOES_COLETIVAS_ZW_UI(FuncionalidadeSistemaEnum.OPERACOES_COLETIVAS, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_POSICAO_DO_ESTOQUE_ZW_UI(FuncionalidadeSistemaEnum.POSICAO_DO_ESTOQUE, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_REGISTRAR_ACESSO_AVULSO_ZW_UI(FuncionalidadeSistemaEnum.REGISTRAR_ACESSO_AVULSO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_SORTEIO_ZW_UI(FuncionalidadeSistemaEnum.SORTEIO, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_VENDA_AVULSA_ZW_UI(FuncionalidadeSistemaEnum.VENDA_AVULSA, MenuFuncionalidadeEnum.ADM_INICIO, true, false),
    ADM_VENDA_RAPIDA_ZW_UI(FuncionalidadeSistemaEnum.ADM_VENDA_RAPIDA, MenuFuncionalidadeEnum.ADM_INICIO, true, false),

    PESSOAS_CLIENTES_ADM(FuncionalidadeSistemaEnum.PESSOAS_CLIENTES, MenuFuncionalidadeEnum.PESSOAS_INICIO, true, false),
    PESSOAS_ALUNO_TREINO(FuncionalidadeSistemaEnum.TREINO_ALUNOS, MenuFuncionalidadeEnum.PESSOAS_INICIO, true, false),
    PESSOAS_INCLUIR_CLIENTE(FuncionalidadeSistemaEnum.PESSOAS_INCLUIR_CLEINTE, MenuFuncionalidadeEnum.PESSOAS_INICIO, true, false),


    ADM_RELATORIOS_ZW_UI("Relatórios", "", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.ANIVERSARIANTES,
            FuncionalidadeSistemaEnum.RELATORIO_GERAL_ARMARIOS,
            FuncionalidadeSistemaEnum.RELATORIO_BVS,
            FuncionalidadeSistemaEnum.CLIENTE,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_CANCELADOS,
            FuncionalidadeSistemaEnum.RELATORIO_ATESTADO,
            FuncionalidadeSistemaEnum.RELATORIO_BONUS,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_TRANCADOS,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_COBRANCA_BLOQUEADA,
            FuncionalidadeSistemaEnum.COMISSAO_VARIAVEL,
            FuncionalidadeSistemaEnum.GESTAO_DE_COMISSAO,
            FuncionalidadeSistemaEnum.COMPETENCIA_MENSAL,
            FuncionalidadeSistemaEnum.CONSULTA_DE_RECIBOS,
            FuncionalidadeSistemaEnum.CONSULTA_DE_TURMAS,
            FuncionalidadeSistemaEnum.CONTRATOS_DURACAO,
            FuncionalidadeSistemaEnum.CONTROLE_LOG,
            FuncionalidadeSistemaEnum.RELATORIO_CONVIDADOS,
            FuncionalidadeSistemaEnum.DESCONTO_OCUPACAO_TURMAS,
            FuncionalidadeSistemaEnum.FATURAMENTO_PERIODO,
            FuncionalidadeSistemaEnum.FATURAMENTO_RECEBIDO_PERIODO,
            FuncionalidadeSistemaEnum.FECHAMENTO_ACESSOS,
            FuncionalidadeSistemaEnum.FECHAMENTO_CAIXA_OPERADOR,
            FuncionalidadeSistemaEnum.FREQUENCIA_OCUPACAO_TURMAS,
            FuncionalidadeSistemaEnum.GERAL_CLIENTES,
            FuncionalidadeSistemaEnum.GYM_PASS_RELATORIO,
            FuncionalidadeSistemaEnum.GOGOOD_RELATORIO,
            FuncionalidadeSistemaEnum.HISTORICO_PONTOS_ALUNO,
            FuncionalidadeSistemaEnum.INDICADOR_ACESSOS,
            FuncionalidadeSistemaEnum.LISTA_ACESSOS,
            FuncionalidadeSistemaEnum.LISTA_CHAMADA,
            FuncionalidadeSistemaEnum.LISTA_CLIENTES_SIMPLIFICADA,
            FuncionalidadeSistemaEnum.MAPA_TURMAS,
            FuncionalidadeSistemaEnum.MOVIMENTO_CC_CLIENTE,
            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_ORCAMENTOS,
            FuncionalidadeSistemaEnum.RELATORIO_PARCELAS,
            FuncionalidadeSistemaEnum.PEDIDOS_PINPAD,
            FuncionalidadeSistemaEnum.RELATORIO_DE_PERSONAL,
            FuncionalidadeSistemaEnum.RELATORIO_PESQUISA,
            FuncionalidadeSistemaEnum.PREVISAO_RENOVACAO_CONTRATO,
            FuncionalidadeSistemaEnum.RELATORIO_PRODUTOS,
            FuncionalidadeSistemaEnum.RECEITA_PERIODO,
            FuncionalidadeSistemaEnum.RELATORIO_REPASSE,
            FuncionalidadeSistemaEnum.SALDO_CONTA_CORRENTE,
            FuncionalidadeSistemaEnum.SALDO_CREDITO,
            FuncionalidadeSistemaEnum.SGP_AVALIACOES_FISICAS,
            FuncionalidadeSistemaEnum.SGP_MODALIDADES_COM_TURMAS,
            FuncionalidadeSistemaEnum.SGP_MODALIDADES_SEM_TURMAS,
            FuncionalidadeSistemaEnum.SGP_TURMAS,
            FuncionalidadeSistemaEnum.TOTALIZADOR_ACESSOS,
            FuncionalidadeSistemaEnum.TOTALIZADOR_TICKETS,
            FuncionalidadeSistemaEnum.TRANSACOES_PIX,
            FuncionalidadeSistemaEnum.VENDA_CONSUMIDOR,
            FuncionalidadeSistemaEnum.RELATORIO_VISITANTES,
            FuncionalidadeSistemaEnum.SMD_RELATORIO,
    }, MenuFuncionalidadeEnum.ADM_INICIO, true, false),


    ADM_CLUBE_VANTAGENS("Clube de Vantagens", "fa-icon-gift", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CONFIGURACOES,
            FuncionalidadeSistemaEnum.BRINDE,
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CAMPANHA,
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_ATIVAR
    }, MenuFuncionalidadeEnum.ADM_CADASTROS, true, false),

    ADM_CLUBE_VANTAGENS_OLD("Clube de Vantagens", "fa-icon-gift", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CONFIGURACOES,
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CAMPANHA,
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE,
            FuncionalidadeSistemaEnum.BRINDE,
            FuncionalidadeSistemaEnum.HISTORICO_PONTOS_ALUNO
    }, MenuFuncionalidadeEnum.ADM_CLUBE_VANTAGENS, false, true),

    /**
     * MODULO CRM
     */
    CRM_RELATORIOS("Relatórios", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.AGENDAMENTOS_CRM,
            FuncionalidadeSistemaEnum.CONTATO_APP,
            FuncionalidadeSistemaEnum.CONSULTA_HISTORICO_CONTATO,
            FuncionalidadeSistemaEnum.INDICACAO,
            FuncionalidadeSistemaEnum.PASSIVO,
            FuncionalidadeSistemaEnum.TOTALIZADOR_METAS,

    }, MenuFuncionalidadeEnum.CRM_INICIO),
    CRM_CADASTRO("Cadastros", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.EVENTO,
            FuncionalidadeSistemaEnum.FERIADO,
            FuncionalidadeSistemaEnum.GRUPO_COLABORADOR,
            FuncionalidadeSistemaEnum.CRM_META_EXTA,
            FuncionalidadeSistemaEnum.MODELO_MENSAGEM,
            FuncionalidadeSistemaEnum.OBJECAO,
            FuncionalidadeSistemaEnum.TEXTO_PADRAO

    }, MenuFuncionalidadeEnum.CRM_INICIO),

    CRM_RELATORIOS_BI("Relatórios", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.AGENDAMENTOS_CRM,
            FuncionalidadeSistemaEnum.CONTATO_APP,
            FuncionalidadeSistemaEnum.CONSULTA_HISTORICO_CONTATO,
            FuncionalidadeSistemaEnum.INDICACAO,
            FuncionalidadeSistemaEnum.PASSIVO,

    }, MenuFuncionalidadeEnum.CRM_BI),
    CRM_CADASTRO_BI("Cadastros", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.EVENTO,
            FuncionalidadeSistemaEnum.GRUPO_COLABORADOR,
            FuncionalidadeSistemaEnum.FERIADO,
            FuncionalidadeSistemaEnum.CRM_META_EXTA,
            FuncionalidadeSistemaEnum.MODELO_MENSAGEM,
            FuncionalidadeSistemaEnum.OBJECAO,
            FuncionalidadeSistemaEnum.TEXTO_PADRAO

    }, MenuFuncionalidadeEnum.CRM_BI),


    /**
     * MODULO FINANCEIRO
     */

    ABRIR_CAIXA(FuncionalidadeSistemaEnum.ABRIR_CAIXA, MenuFuncionalidadeEnum.FIN_INICIO, true, false),
    BLOQUEIO_CAIXA(FuncionalidadeSistemaEnum.BLOQUEIO_CAIXA, MenuFuncionalidadeEnum.FIN_INICIO, true, false),
    FECHAR_CAIXA(FuncionalidadeSistemaEnum.FECHAR_CAIXA, MenuFuncionalidadeEnum.FIN_INICIO, true, false),
    LOTES(FuncionalidadeSistemaEnum.LOTES, MenuFuncionalidadeEnum.FIN_INICIO, true, false),
    LANCAMENTO_CONTA_RAPIDO(FuncionalidadeSistemaEnum.LANCAMENTO_CONTA_RAPIDO, MenuFuncionalidadeEnum.FIN_INICIO, true, false),
    NOVA_CONTA_PAGAR(FuncionalidadeSistemaEnum.NOVA_CONTA_PAGAR, MenuFuncionalidadeEnum.FIN_INICIO, true, false),
    NOVA_CONTAS_RECEBER(FuncionalidadeSistemaEnum.NOVA_CONTAS_RECEBER, MenuFuncionalidadeEnum.FIN_INICIO, true, false),
    RECEBIVEIS(FuncionalidadeSistemaEnum.RECEBIVEIS, MenuFuncionalidadeEnum.FIN_INICIO, true, false),


    FIN_RELATORIOS_ZWUI("Relatórios", "fa-icon-rocket", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CAIXA_ADIMISTRATIVO,
            FuncionalidadeSistemaEnum.CONSULTAR_CAIXA,
            FuncionalidadeSistemaEnum.CONTAS_A_PAGAR,
            FuncionalidadeSistemaEnum.CONTAS_A_RECEBER,
            FuncionalidadeSistemaEnum.RELATORIO_DEVOLUCAO_CHEQUE,
            FuncionalidadeSistemaEnum.DEMONSTRATIVO_FINAN,
            FuncionalidadeSistemaEnum.FECHAMENTO_CAIXA_PLANO_CONTAS,
            FuncionalidadeSistemaEnum.DRE,
            FuncionalidadeSistemaEnum.FLUXO_CAIXA_FINAN,
            FuncionalidadeSistemaEnum.MOVIMENTACOES_FINAN,
            FuncionalidadeSistemaEnum.RESUMO_CONTAS,
            FuncionalidadeSistemaEnum.LANCAMENTOS,
            FuncionalidadeSistemaEnum.RELATORIO_ORCAMENTARIO,
    }, MenuFuncionalidadeEnum.FIN_INICIO, true),

    FIN_CADASTROS_ZWUI("Cadastros", "fa-icon-rocket", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CENTRO_CUSTOS,
            FuncionalidadeSistemaEnum.CHEQUES_CARTOES_AVULSOS,
            FuncionalidadeSistemaEnum.FINAN_CONTA,
            FuncionalidadeSistemaEnum.CONTA_CONTABIL,
            FuncionalidadeSistemaEnum.FORNCEDOR,
            FuncionalidadeSistemaEnum.METAS_DO_FINANCEIRO,
            FuncionalidadeSistemaEnum.PESSOA,
            FuncionalidadeSistemaEnum.PLANO_CONTAS,
            FuncionalidadeSistemaEnum.RATEIO_INTEGRACAO,
            FuncionalidadeSistemaEnum.TIPO_CONTA,
            FuncionalidadeSistemaEnum.TIPO_DOCUMENTO,
            FuncionalidadeSistemaEnum.RELATORIO_ORCAMENTARIO_CONFIGURACAO,
    }, MenuFuncionalidadeEnum.FIN_INICIO, true),

    FIN_RELATORIOS("Lançamentos", "fa-icon-rocket", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.LANCAMENTOS,
            FuncionalidadeSistemaEnum.CONTAS_A_PAGAR,
            FuncionalidadeSistemaEnum.CONTAS_A_RECEBER,
            FuncionalidadeSistemaEnum.LANCAMENTO_CONTA_RAPIDO,
    }, MenuFuncionalidadeEnum.FIN_INICIO, false, true),

    FIN_GESTAO("Gestão", "fa-icon-cogs", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.RECEBIVEIS,
            FuncionalidadeSistemaEnum.LOTES,
    }, MenuFuncionalidadeEnum.FIN_INICIO, false, true),

    FIN_CAIXA("Caixa", "fa-icon-money", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CAIXA_ADIMISTRATIVO,
            FuncionalidadeSistemaEnum.ABRIR_CAIXA,
            FuncionalidadeSistemaEnum.FECHAR_CAIXA,
            FuncionalidadeSistemaEnum.CONSULTAR_CAIXA,
            FuncionalidadeSistemaEnum.BLOQUEIO_CAIXA,
    }, MenuFuncionalidadeEnum.FIN_INICIO, "true", "ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas", false, true),

    FIN_CONTAS("Contas", "fa-icon-archive", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.RESUMO_CONTAS,
    }, MenuFuncionalidadeEnum.FIN_INICIO, "true", "ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas", false, true),

    FIN_CADASTROS("Cadastros", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.FIN_CADASTROS_AUXILIARES,
            FuncionalidadeSistemaEnum.FIN_CONFIG_FINANCEIRAS,
    }, MenuFuncionalidadeEnum.FIN_CADASTROS, false, true),

    FIN_CADASTROS_AUXILIARES("Cadastros Auxiliares", "fa-icon-list", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.FORNCEDOR,
            FuncionalidadeSistemaEnum.PESSOA,
            FuncionalidadeSistemaEnum.FINAN_CONTA,
            FuncionalidadeSistemaEnum.TIPO_CONTA,
            FuncionalidadeSistemaEnum.TIPO_DOCUMENTO,
            FuncionalidadeSistemaEnum.CATEGORIA_CLIENTES,
            FuncionalidadeSistemaEnum.CLIENTE,
            FuncionalidadeSistemaEnum.COLABORADOR,
            FuncionalidadeSistemaEnum.GRAU_DE_INSTRUCAO,
            FuncionalidadeSistemaEnum.PROFISSAO,
            FuncionalidadeSistemaEnum.PAIS,
            FuncionalidadeSistemaEnum.CIDADE
    }, MenuFuncionalidadeEnum.FIN_INICIO, false, true),

    FIN_CONFIG_FINANCEIRAS("Config. financeiras", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.RATEIO_INTEGRACAO,
            FuncionalidadeSistemaEnum.PLANO_CONTAS,
            FuncionalidadeSistemaEnum.CENTRO_CUSTOS,
            FuncionalidadeSistemaEnum.CHEQUES_CARTOES_AVULSOS,
            FuncionalidadeSistemaEnum.MOVIMENTO_CC_CLIENTE,
            FuncionalidadeSistemaEnum.MOVIMENTO_PRODUTO,
            FuncionalidadeSistemaEnum.BANCO,
            FuncionalidadeSistemaEnum.FORMA_PAGAMENTO,
            FuncionalidadeSistemaEnum.OPERADORA_CARTAO,
            FuncionalidadeSistemaEnum.METAS_FINANCEIRO_VENDA,
            FuncionalidadeSistemaEnum.IMPOSTO
    }, MenuFuncionalidadeEnum.FIN_INICIO, false, true),

    FIN_RELATORIOS_FIN("Rel. Financeiros", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.DEMONSTRATIVO_FINAN,
            FuncionalidadeSistemaEnum.FECHAMENTO_CAIXA_PLANO_CONTAS,
            FuncionalidadeSistemaEnum.DRE,
            FuncionalidadeSistemaEnum.MOVIMENTACOES_FINAN,
            FuncionalidadeSistemaEnum.RELATORIO_DEVOLUCAO_CHEQUE,
            FuncionalidadeSistemaEnum.FLUXO_CAIXA_FINAN,
    }, MenuFuncionalidadeEnum.FIN_BI, false, true),

    /**
     * MODULO ADM
     */

    ADM_OPERCOES_VENDA("Operações de Venda", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO,
            FuncionalidadeSistemaEnum.VENDA_AVULSA,
            FuncionalidadeSistemaEnum.DIARIA,
            FuncionalidadeSistemaEnum.ORCAMENTO,
            FuncionalidadeSistemaEnum.FREE_PASS
    }, MenuFuncionalidadeEnum.ADM_INICIO, false, true),
    ADM_GESTAO("Gestão", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.GESTAO_PERSONAL,
            FuncionalidadeSistemaEnum.GESTAO_DE_TRANSACOES,
            FuncionalidadeSistemaEnum.GESTAO_DE_TURMA,
            FuncionalidadeSistemaEnum.GESTAO_DE_REMESSAS,
            FuncionalidadeSistemaEnum.GESTAO_DE_BOLETOS_ONLINE,
            FuncionalidadeSistemaEnum.GESTAO_NEGATIVACOES,
            FuncionalidadeSistemaEnum.GESTAO_DE_ARMARIOS,
            FuncionalidadeSistemaEnum.VENDAS_ONLINE,
            FuncionalidadeSistemaEnum.VENDAS_ONLINE_ADQUIRA
    }, MenuFuncionalidadeEnum.ADM_INICIO, false, true),
    ADM_COMISAO("Comissão", "fa-icon-percent", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.GESTAO_DE_COMISSAO,
            FuncionalidadeSistemaEnum.COMISSAO_VARIAVEL,
    }, MenuFuncionalidadeEnum.ADM_INICIO, false, true),


    ADM_OUTROS("Outras opções", "fa-icon-folder-close", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.AUTORIZACAO_ACESSO,
            FuncionalidadeSistemaEnum.CONSULTA_DE_TURMAS,
            FuncionalidadeSistemaEnum.MAPA_TURMAS,
            FuncionalidadeSistemaEnum.CANAL_CLIENTE_PACTO_STORE,
            FuncionalidadeSistemaEnum.OPERACOES_COLETIVAS,
            FuncionalidadeSistemaEnum.RELATORIO_DE_PERSONAL,
            FuncionalidadeSistemaEnum.SORTEIO,
    }, MenuFuncionalidadeEnum.ADM_INICIO, false, true),

    /**
     * Módulos com 2 submenus
     */
    ADM_RELATORIOS("Relatórios", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.RELATORIO_DE_PERSONAL,
    }, MenuFuncionalidadeEnum.ADM_INICIO, new GrupoFuncionalidadeSistemaEnum[]{
            ADM_RELATORIO_CLIENTES,
            ADM_ACESSOS,
            ADM_RELATORIO_ARMARIOS,
            ADM_RELATORIO_TURMAS,
            ADM_RELATORIO_FINANCIERO,
            ADM_RELATORIO_ESTATISTICO,
            ADM_RELATORIO_ESTUDIO
    }, false, true),
    ADM_PRODUTOS_PLANOS_TURMAS("Produtos, Planos e Turmas", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
    }, MenuFuncionalidadeEnum.ADM_INICIO, new GrupoFuncionalidadeSistemaEnum[]{
            ADM_PRODUTOS,
            ADM_PLANOS,
            ADM_TURMAS
    }, true),

    ADM_CADASTROS("Cadastros", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.ADQUIRENTE,
            FuncionalidadeSistemaEnum.AMBIENTE,
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_ATIVAR,
            FuncionalidadeSistemaEnum.BANCO,
            FuncionalidadeSistemaEnum.BRINDE,
            FuncionalidadeSistemaEnum.CAMPANHA_CUPOM_DESCONTO,
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CAMPANHA,
            FuncionalidadeSistemaEnum.CATEGORIA_CLIENTES,
            FuncionalidadeSistemaEnum.CATEGORIA_PRODUTO,
            FuncionalidadeSistemaEnum.CIDADE,
            FuncionalidadeSistemaEnum.CLASSIFICACAO,
            FuncionalidadeSistemaEnum.COLABORADOR,
            FuncionalidadeSistemaEnum.CONDICAO_DE_PAGAMENTO,
            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CONFIGURACOES,
            FuncionalidadeSistemaEnum.CONTA_CORRENTE,
            FuncionalidadeSistemaEnum.CONVENIO_COBRANCA,
            FuncionalidadeSistemaEnum.CONVENIO_DE_DESCONTO,
            FuncionalidadeSistemaEnum.DEPARTAMENTO,
            FuncionalidadeSistemaEnum.DESCONTO,
            FuncionalidadeSistemaEnum.EMPRESA,
            FuncionalidadeSistemaEnum.FORMA_PAGAMENTO,
            FuncionalidadeSistemaEnum.GRAU_DE_INSTRUCAO,
            FuncionalidadeSistemaEnum.GRUPO_DESCONTO,
            FuncionalidadeSistemaEnum.HISTORICO_PONTOS_ALUNO,
            FuncionalidadeSistemaEnum.HORARIO,
            FuncionalidadeSistemaEnum.IMPORTACAO,
            FuncionalidadeSistemaEnum.IMPOSTO,
            FuncionalidadeSistemaEnum.INDICE_FINANCEIRO_REAJUSTE_PRECO,
            FuncionalidadeSistemaEnum.INTEGRACAO_ACESSO,
            FuncionalidadeSistemaEnum.JUSTIFICATIVA_OPERACAO,
            FuncionalidadeSistemaEnum.LANCAMENTO_PRODUTO_COLETIVO,
            FuncionalidadeSistemaEnum.LOCAL_ACESSO,
            FuncionalidadeSistemaEnum.LOCAL_IMPRESSAO,
            FuncionalidadeSistemaEnum.METAS_FINANCEIRO_VENDA,
            FuncionalidadeSistemaEnum.MODALIDADE,
            FuncionalidadeSistemaEnum.MODELO_CONTRATO,
            FuncionalidadeSistemaEnum.MODELO_ORCAMENTO,
            FuncionalidadeSistemaEnum.MOVIMENTO_PRODUTO,
            FuncionalidadeSistemaEnum.NIVEL_TURMA,
            FuncionalidadeSistemaEnum.OPERADORA_CARTAO,
            FuncionalidadeSistemaEnum.PACOTE,
            FuncionalidadeSistemaEnum.PAIS,
            FuncionalidadeSistemaEnum.PARENTESCO,
            FuncionalidadeSistemaEnum.PERFIL_ACESSO,
            FuncionalidadeSistemaEnum.PERFIL_ACESSO_UNIFICADO,
            FuncionalidadeSistemaEnum.PERGUNTA,
            FuncionalidadeSistemaEnum.PESQUISA,
            FuncionalidadeSistemaEnum.PINPAD,
            FuncionalidadeSistemaEnum.PLANO,
            FuncionalidadeSistemaEnum.PRODUTO,
            FuncionalidadeSistemaEnum.PROFISSAO,
            FuncionalidadeSistemaEnum.QUESTIONARIO,
            FuncionalidadeSistemaEnum.SERVIDOR_FACIAL,
            FuncionalidadeSistemaEnum.TAMANHO_ARMARIO,
            FuncionalidadeSistemaEnum.TAXA_COMISSAO,
            FuncionalidadeSistemaEnum.TIPO_MODALIDADE,
            FuncionalidadeSistemaEnum.TIPO_PLANO,
            FuncionalidadeSistemaEnum.TIPO_REMESSA,
            FuncionalidadeSistemaEnum.TIPO_RETORNO,
            FuncionalidadeSistemaEnum.TURMA,
            FuncionalidadeSistemaEnum.USUARIO
    }, MenuFuncionalidadeEnum.ADM_INICIO, true, false),

    ADM_CADASTROS_OLD("Seção", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CADASTROS_AUXILIARES,
            FuncionalidadeSistemaEnum.CADASTROS_PRODUTOS_TURMAS_PLANOS,
            FuncionalidadeSistemaEnum.CADASTROS_CONFIG_FINANCEIRA,
            FuncionalidadeSistemaEnum.CADASTROS_ACESSO_SISTEMA,
            FuncionalidadeSistemaEnum.CADASTROS_CONFIG_CONTRATO
    }, MenuFuncionalidadeEnum.ADM_CADASTROS),

    ADM_NOTAS("Configurações", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.CONFIGURACAO_NOTAFISCAL,
            FuncionalidadeSistemaEnum.PRODUTO
    }, MenuFuncionalidadeEnum.NOTAS_INICIO),

    PESSOAS_CADASTROS("Cadastros", "fa-icon-briefcase", new FuncionalidadeSistemaEnum[]{
            FuncionalidadeSistemaEnum.COLABORADOR,
            FuncionalidadeSistemaEnum.TREINO_CADASTRO_COLABORADORES,
            FuncionalidadeSistemaEnum.PERFIL_ACESSO,
            FuncionalidadeSistemaEnum.PERFIL_ACESSO_UNIFICADO,
            FuncionalidadeSistemaEnum.TREINO_PERFIL_ACESSO,
            FuncionalidadeSistemaEnum.PESSOA,
            FuncionalidadeSistemaEnum.USUARIO,
            FuncionalidadeSistemaEnum.TREINO_USUARIOS

    }, MenuFuncionalidadeEnum.PESSOAS_INICIO),
    ;

    private String descricao;
    private String iconeClass;
    private FuncionalidadeSistemaEnum[] funcionalidades;
    private GrupoFuncionalidadeSistemaEnum[] subMenu;
    private FuncionalidadeSistemaEnum funcionalidade;
    private MenuFuncionalidadeEnum menu;
    private boolean renderizar = true;
    private String reRend = "true";
    private String expressaoRenderizar;
    private String expressaoRenderizarGrupo;
    private boolean somenteMenuNovo;
    private boolean somenteMenuAntigo;

    private static GrupoFuncionalidadeSistemaEnum[] oldMenus;
    private static GrupoFuncionalidadeSistemaEnum[] newMenus;

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu) {
        this.descricao = descricao;
        this.iconeClass = iconeClass;
        this.funcionalidades = funcionalidades;
        this.menu = menu;
        this.somenteMenuNovo = false;
        this.somenteMenuAntigo = false;
    }

    GrupoFuncionalidadeSistemaEnum(FuncionalidadeSistemaEnum funcionalidade, MenuFuncionalidadeEnum menu, Boolean somenteMenuNovo, Boolean somenteMenuAntigo) {
        this.funcionalidade = funcionalidade;
        this.menu = menu;
        this.somenteMenuNovo = false;
        this.somenteMenuAntigo = false;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu, Boolean somenteMenuNovo) {
        this.descricao = descricao;
        this.iconeClass = iconeClass;
        this.funcionalidades = funcionalidades;
        this.menu = menu;
        this.somenteMenuNovo = somenteMenuNovo;
        this.somenteMenuAntigo = false;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu, Boolean somenteMenuNovo, Boolean somenteMenuAntigo) {
        this(descricao, iconeClass, funcionalidades, menu, somenteMenuNovo, somenteMenuAntigo, null, null);
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu, Boolean somenteMenuNovo, Boolean somenteMenuAntigo, String expressaoRenderizar, String expressaoRenderizarGrupo) {
        this.descricao = descricao;
        this.iconeClass = iconeClass;
        this.funcionalidades = funcionalidades;
        this.menu = menu;
        this.somenteMenuNovo = somenteMenuNovo;
        this.somenteMenuAntigo = somenteMenuAntigo;
        this.expressaoRenderizar = expressaoRenderizar;
        this.expressaoRenderizarGrupo = expressaoRenderizarGrupo;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu, String reRender) {
        this(descricao, iconeClass, funcionalidades, menu, false);
        this.reRend = reRender;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu, String reRender, String expressaoRenderizar, boolean somenteMenuNovo, boolean somenteMenuAntigo) {
        this(descricao, iconeClass, funcionalidades, menu, false);
        this.reRend = reRender;
        this.expressaoRenderizar = expressaoRenderizar;
        this.somenteMenuNovo = somenteMenuNovo;
        this.somenteMenuAntigo = somenteMenuAntigo;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu, GrupoFuncionalidadeSistemaEnum[] subMenu) {
        this(descricao, iconeClass, funcionalidades, menu, false);
        this.subMenu = subMenu;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades, MenuFuncionalidadeEnum menu, GrupoFuncionalidadeSistemaEnum[] subMenu, Boolean somenteMenuNovo) {
        this(descricao, iconeClass, funcionalidades, menu, somenteMenuNovo);
        this.subMenu = subMenu;
        this.somenteMenuNovo = somenteMenuNovo;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String iconeClass, FuncionalidadeSistemaEnum[] funcionalidades,
                                   MenuFuncionalidadeEnum menu, GrupoFuncionalidadeSistemaEnum[] subMenu,
                                   Boolean somenteMenuNovo, Boolean somenteMenuAntigo) {
        this(descricao, iconeClass, funcionalidades, menu, false);
        this.subMenu = subMenu;
        this.somenteMenuNovo = somenteMenuNovo;
        this.somenteMenuAntigo = somenteMenuAntigo;
    }

    GrupoFuncionalidadeSistemaEnum(String descricao, String icone, FuncionalidadeSistemaEnum[] funcionalidadeSistemaEnums, MenuFuncionalidadeEnum menu, boolean somenteMenuNovo, String expressaoRenderizar) {
        this.descricao = descricao;
        this.iconeClass = icone;
        this.funcionalidades = funcionalidadeSistemaEnums;
        this.menu = menu;
        this.somenteMenuNovo = somenteMenuNovo;
        this.expressaoRenderizar = expressaoRenderizar;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getIconeClass() {
        return iconeClass;
    }

    public FuncionalidadeSistemaEnum[] getFuncionalidades() {
        return funcionalidades;
    }

    public MenuFuncionalidadeEnum getMenu() {
        return menu;
    }

    public String getNomeMenu() {
        if (getMenu() != null) {
            return getMenu().getName();
        }
        return "";
    }

    public boolean isRenderizar() {
        return renderizar;
    }

    public String getReRend() {
        return reRend;
    }

    public String getName() {
        return name();
    }

    public GrupoFuncionalidadeSistemaEnum[] getSubMenu() {
        return subMenu;
    }

    public String getExpressaoRenderizar() {
        return expressaoRenderizar;
    }

    public boolean isSomenteMenuNovo() {
        return somenteMenuNovo;
    }

    public boolean isSomenteMenuAntigo() {
        return somenteMenuAntigo;
    }

    public String getExpressaoRenderizarGrupo() {
        return expressaoRenderizarGrupo;
    }

    public void ordenarFuncionalidadesMenuZWUI() {
        try {
            if (((SuperControle) JSFUtilities.getManagedBean("SuperControle")).getMenuZwUi() && somenteMenuNovo || (!somenteMenuNovo && somenteMenuAntigo)) {
                for (int i = 0; i < funcionalidades.length; i++) {
                    if (UteisValidacao.emptyString(funcionalidades[i].getDescricaoMenulateral())) {
                        funcionalidades[i].setDescricaoMenulateral(funcionalidades[i].getDescricao());
                    }
                }

                List<FuncionalidadeSistemaEnum> funcionalidadesOrdenadas = Ordenacao.ordenarLista(
                        Arrays.asList(funcionalidades), "descricaoMenulateral");
                funcionalidades = (FuncionalidadeSistemaEnum[]) funcionalidadesOrdenadas.toArray();
            }
        } catch (Exception e) {
            Uteis.logar(e, GrupoFuncionalidadeSistemaEnum.class);
        }
    }

    public FuncionalidadeSistemaEnum getFuncionalidade() {
        return funcionalidade;
    }

    public void setFuncionalidade(FuncionalidadeSistemaEnum funcionalidade) {
        this.funcionalidade = funcionalidade;
    }

    public static GrupoFuncionalidadeSistemaEnum[] valuesOldMenu() {
        if (oldMenus == null) {
            oldMenus = Arrays.stream(values())
                    .filter(it -> !it.somenteMenuNovo)
                    .toArray(GrupoFuncionalidadeSistemaEnum[]::new);
        }
        return oldMenus;
    }

    public static GrupoFuncionalidadeSistemaEnum[] valuesNewMenu() {
        if (newMenus == null) {
            newMenus = Arrays.stream(values())
                    .filter(it -> !it.somenteMenuAntigo)
                    .toArray(GrupoFuncionalidadeSistemaEnum[]::new);
        }
        return newMenus;
    }
}
