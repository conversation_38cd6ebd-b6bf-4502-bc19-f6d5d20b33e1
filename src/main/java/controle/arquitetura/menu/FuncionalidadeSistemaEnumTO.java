package controle.arquitetura.menu;

import controle.arquitetura.FuncionalidadeSistemaEnum;
import negocio.comuns.utilitarias.UrlRedirectNZWEnum;
import negocio.comuns.utilitarias.UteisValidacao;

public class FuncionalidadeSistemaEnumTO {

    private boolean favorito;
    private FuncionalidadeSistemaEnum funcionalidade;
    private boolean novaVersao = true;

    public FuncionalidadeSistemaEnumTO() {

    }

    public FuncionalidadeSistemaEnumTO(FuncionalidadeSistemaEnum funcionalidade, boolean favorito) {
        this.favorito = favorito;
        this.funcionalidade = funcionalidade;
    }

    public boolean isFavorito() {
        return favorito;
    }

    public void setFavorito(boolean favorito) {
        this.favorito = favorito;
    }

    public FuncionalidadeSistemaEnum getFuncionalidade() {
        return funcionalidade;
    }

    public void setFuncionalidade(FuncionalidadeSistemaEnum funcionalidade) {
        this.funcionalidade = funcionalidade;
    }

    public boolean isNovaVersao() {
        return novaVersao;
    }

    public void setNovaVersao(boolean novaVersao) {
        this.novaVersao = novaVersao;
    }

    public String getDescricaoMenulateral() {
        String menuFun = this.getFuncionalidade().getDescricaoMenulateral();
        if (UteisValidacao.emptyString(menuFun)) {
            menuFun = this.getFuncionalidade().getDescricao();
        }
        if (!UteisValidacao.emptyString(menuFun) &&
                this.isNovaVersao() &&
                !UteisValidacao.emptyString(UrlRedirectNZWEnum.getUriByFuncSisEnum(this.getFuncionalidade()))) {
            menuFun += " . ";
        }
        return menuFun;
    }
}
