package controle.arquitetura.menu;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.MenuSistemaGrupoEnum;
import controle.arquitetura.MenuSistemaSubgrupoEnum;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MenuExplorarGrupo {
    private Modulo modulo;
    private List<MenuExplorarConfig> menuExplorarConfigs;
    private final List<MenuExplorarGrupo> MENU_EXPLORAR_GRUPOS = new ArrayList<>();

    public static final MenuExplorarGrupo ADM = new MenuExplorarGrupo(Modulo.ZILLYON_WEB, new ArrayList<>());
    public static final MenuExplorarGrupo TREINO = new MenuExplorarGrupo(Modulo.NOVO_TREINO, new ArrayList<>());
    public static final MenuExplorarGrupo CRM = new MenuExplorarGrupo(Modulo.CUSTOMER_RELATIONSHIP_MANAGEMENT, new ArrayList<>());
    public static final MenuExplorarGrupo FIN = new MenuExplorarGrupo(Modulo.FINANCEIRO, new ArrayList<>());
    public static final MenuExplorarGrupo PACTO_PAY = new MenuExplorarGrupo(Modulo.PACTO_PAY, new ArrayList<>());
    public static final MenuExplorarGrupo CROSS = new MenuExplorarGrupo(Modulo.CROSS, new ArrayList<>());
    public static final MenuExplorarGrupo AVALIACAO_FISICA = new MenuExplorarGrupo(Modulo.AVALIACAO_FISICA, new ArrayList<>());
    public static final MenuExplorarGrupo GRADUACAO = new MenuExplorarGrupo(Modulo.GRADUACAO, new ArrayList<>());
    public static final MenuExplorarGrupo NOTA = new MenuExplorarGrupo(Modulo.NOTAS, new ArrayList<>());
    public static final MenuExplorarGrupo GAME = new MenuExplorarGrupo(Modulo.GAME_OF_RESULTS, new ArrayList<>());
    public static final MenuExplorarGrupo CANAL_CLIENTE = new MenuExplorarGrupo(Modulo.CANAL_CLIENTE, new ArrayList<>());
    public static final MenuExplorarGrupo AULA_CHEIA = new MenuExplorarGrupo(Modulo.AULA_CHEIA, new ArrayList<>());
    public static final MenuExplorarGrupo CENTRAL_DE_EVENTOS = new MenuExplorarGrupo(Modulo.CENTRAL_DE_EVENTOS, new ArrayList<>());
    public static final MenuExplorarGrupo ESTUDIO = new MenuExplorarGrupo(Modulo.STUDIO, new ArrayList<>());
    public static final MenuExplorarGrupo AGENDA = new MenuExplorarGrupo(Modulo.AGENDA, new ArrayList<>());

    public MenuExplorarGrupo() {
        this.buildMenus();
    }

    private MenuExplorarGrupo(Modulo modulo, List<MenuExplorarConfig> menuExplorarConfig) {
        this.modulo = modulo;
        this.menuExplorarConfigs = menuExplorarConfig;
    }

    public Modulo getModulo() {
        return modulo;
    }

    public List<MenuExplorarConfig> getMenuExplorarConfigs() {
        return menuExplorarConfigs;
    }

    public void buildMenus() {
        buildMenuAdm();
        buildMenuTreino();
        buildMenuCrm();
        buildMenuFin();
        buildMenuAgenda();
        buildMenuNotas();
        buildMenuPactopay();
        buildMenuAvaliacaoFisica();
        buildMenuCross();
        buildMenuGraduacao();
        buildMenuWithoutFunctionalities(GAME);
        buildMenuWithoutFunctionalities(CANAL_CLIENTE);
        buildMenuEstudio();
        buildMenuCentralEventos();
        buildMenuWithoutFunctionalities(AULA_CHEIA);
    }

    private void buildMenuWithoutFunctionalities(MenuExplorarGrupo game) {
        if (isPermiteRenderizarModulo(game.getModulo())) {
            MENU_EXPLORAR_GRUPOS.add(game);
        }
    }

    private boolean isPermiteRenderizarModulo(Modulo modulo) {
        boolean renderizarModulo = false;
        try {
            renderizarModulo = getManagedBeanValue(modulo.getExpresaoRenderizar());
        } catch (Exception e) {
            Uteis.logarDebug("Falha ao processar exibição do modulo " + modulo.name());
            e.printStackTrace();
        }
        return renderizarModulo;
    }

    private boolean getManagedBeanValue(final String param) {
        return JSFUtilities.getManagedBeanValue(param) != null && (Boolean) JSFUtilities.getManagedBeanValue(param);
    }

    private void buildMenuEstudio() {
        if (isPermiteRenderizarModulo(ESTUDIO.getModulo())) {
            ESTUDIO.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(ESTUDIO, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AGENDAMENTOS,
                            FuncionalidadeSistemaEnum.DIARIO,
                    }));
            addMenuExplorarConfigs(ESTUDIO, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.RELATORIOS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CLIENTES_SEM_SESSAO,
                            FuncionalidadeSistemaEnum.COMISSAO_EST
                    }));
            MENU_EXPLORAR_GRUPOS.add(ESTUDIO);
        }
    }

    private  void buildMenuNotas() {
        if (isPermiteRenderizarModulo(NOTA.getModulo())) {
            NOTA.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(NOTA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.GESTAO_NFCE,
                            FuncionalidadeSistemaEnum.GESTAO_NOTAS,
                            FuncionalidadeSistemaEnum.NOTAS_FISCAIS,
                    }));
            addMenuExplorarConfigs(NOTA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.CADASTROS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CONFIGURACAO_NOTAFISCAL,
                            FuncionalidadeSistemaEnum.CONSULTA_DE_CUPONS_FISCAIS,
                    }));
            MENU_EXPLORAR_GRUPOS.add(NOTA);
        }
    }

    private  void buildMenuGraduacao() {
        if (isPermiteRenderizarModulo(GRADUACAO.getModulo())) {
            GRADUACAO.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(GRADUACAO, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.GRADUACAO_AVALIACOES_PROGRESSO,
                    }));
            addMenuExplorarConfigs(GRADUACAO, new MenuExplorarConfig(MenuSistemaGrupoEnum.RELATORIOS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.GRADUACAO_ATIVIDADES,
                            FuncionalidadeSistemaEnum.GRADUACAO_FICHA_TENICA
                    }));
            MENU_EXPLORAR_GRUPOS.add(GRADUACAO);
        }
    }

    private  void buildMenuAvaliacaoFisica() {
        if (isPermiteRenderizarModulo(AVALIACAO_FISICA.getModulo())) {
            AVALIACAO_FISICA.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(AVALIACAO_FISICA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AVALIACAO_FISICA_BI
                    }));
            addMenuExplorarConfigs(AVALIACAO_FISICA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.CADASTROS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AVALIACAO_ANAMNESES,
                            FuncionalidadeSistemaEnum.AVALIACAO_OBJETIVOS
                    }));
            MENU_EXPLORAR_GRUPOS.add(AVALIACAO_FISICA);
        }
    }

    private void buildMenuCross() {
        if (isPermiteRenderizarModulo(CROSS.getModulo())) {
            CROSS.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(CROSS, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CROSS_BI,
                    }));
            addMenuExplorarConfigs(CROSS, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CROSS_MONITOR,
                            FuncionalidadeSistemaEnum.CROSS_WOD,
                    }));
            addMenuExplorarConfigs(CROSS, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.RELATORIOS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CROSS_APARELHOS,
                            FuncionalidadeSistemaEnum.CROSS_ATIVIDADE,
                            FuncionalidadeSistemaEnum.CROSS_BENCHMARKS,
                            FuncionalidadeSistemaEnum.CROSS_TIPO_WOD,
                            FuncionalidadeSistemaEnum.CROSS_TIPOS_BENCHMARK,
                    }));
            MENU_EXPLORAR_GRUPOS.add(CROSS);
        }
    }

    private void buildMenuPactopay() {
        if (isPermiteRenderizarModulo(PACTO_PAY.getModulo())) {
            PACTO_PAY.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(PACTO_PAY, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.PACTOPAY_BI,
                            FuncionalidadeSistemaEnum.PACTOPAY_BI_REGUA_COBRANCA
                    }));
            addMenuExplorarConfigs(PACTO_PAY, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.PACTOPAY_CARTAO_CREDITO,
                            FuncionalidadeSistemaEnum.PACTOPAY_CARTAO_CREDITO_ONLINE,
                            FuncionalidadeSistemaEnum.PACTOPAY_PIX,
                    },
                    new MenuExplorarConfig[]{
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.PACTOPAY_REGUA_COBRANCA,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.PACTOPAY_CONFIGURACAO_EMAIL,
                                            FuncionalidadeSistemaEnum.PACTOPAY_CONFIGURACAO_FASES
                                    }
                            ),
                    }
            ));
            addMenuExplorarConfigs(PACTO_PAY,
                    new MenuExplorarConfig(
                            MenuSistemaGrupoEnum.RELATORIOS,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.PACTOPAY_PARCELAS_EM_ABERTO
                            }
                    ));
            MENU_EXPLORAR_GRUPOS.add(PACTO_PAY);
        }
    }

    private void buildMenuTreino() {
        if (isPermiteRenderizarModulo(TREINO.getModulo())) {
            TREINO.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(TREINO, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.TREINO_BI,
                    }));
             addMenuExplorarConfigs(TREINO, new MenuExplorarConfig(
                     MenuSistemaGrupoEnum.OPERACOES,
                     new FuncionalidadeSistemaEnum[]{
                             FuncionalidadeSistemaEnum.TREINO_PRESCRICAO,
                             FuncionalidadeSistemaEnum.TREINO_RANKING,
                             FuncionalidadeSistemaEnum.TREINO_EM_CASA,
                     },
                     new MenuExplorarConfig[]{
                             new MenuExplorarConfig(
                                     MenuSistemaSubgrupoEnum.TREINO_OPERACOES_GESTAO_PERSONAL,
                                     new FuncionalidadeSistemaEnum[]{
                                             FuncionalidadeSistemaEnum.TREINO_ACAMPANHAMENTO_PERSONAL,
                                             FuncionalidadeSistemaEnum.TREINO_COLABORADORES,
                                             FuncionalidadeSistemaEnum.TREINO_GESTAO_CREDITOS,
                                             FuncionalidadeSistemaEnum.TREINO_PERSONAL,
                                     }
                             ),
                     }
             ));
             addMenuExplorarConfigs(TREINO, new MenuExplorarConfig(
                     MenuSistemaGrupoEnum.RELATORIOS,
                     new FuncionalidadeSistemaEnum[]{
                             FuncionalidadeSistemaEnum.TREINO_PROFESSORES_ALUNOS_AVISO_MEDICO,
                             FuncionalidadeSistemaEnum.TREINO_ANDAMENTO,
                             FuncionalidadeSistemaEnum.TREINO_APLICATIVOS_ATIVOS,
                             FuncionalidadeSistemaEnum.TREINO_ATIVIDADES_PROFESSORES,
                             FuncionalidadeSistemaEnum.TREINO_CARTEIRA,
                             FuncionalidadeSistemaEnum.TREINO_GESTAO_CREDITOS,
                             FuncionalidadeSistemaEnum.TREINO_PROFESSORES_SUBSTITUIDOS
                     }
             ));
             addMenuExplorarConfigs(TREINO, new MenuExplorarConfig(
                     MenuSistemaGrupoEnum.CADASTROS,
                     new FuncionalidadeSistemaEnum[]{
                             FuncionalidadeSistemaEnum.TREINO_APARELHOS,
                             FuncionalidadeSistemaEnum.TREINO_ATIVIDADE,
                             FuncionalidadeSistemaEnum.TREINO_CATETORIA_ATIVIDADE,
                             FuncionalidadeSistemaEnum.TREINO_FICHAS_PREDEFINIDAS,
                             FuncionalidadeSistemaEnum.TREINO_PERFIL_ACESSO,
                             FuncionalidadeSistemaEnum.TREINO_NIVEIS,
                             FuncionalidadeSistemaEnum.TREINO_PROGRAMAS_PREDEFINIDOS,
                     }
             ));
            MENU_EXPLORAR_GRUPOS.add(TREINO);
        }
    }

    public void buildMenuFin() {
        if (isPermiteRenderizarModulo(FIN.getModulo())) {
            FIN.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(FIN, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.FIN_BI
                    }));
            addMenuExplorarConfigs(FIN,new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new MenuExplorarConfig[]{
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.FIN_OPERACOES_CAIXA,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.ABRIR_CAIXA,
                                            FuncionalidadeSistemaEnum.FECHAR_CAIXA,
                                            FuncionalidadeSistemaEnum.BLOQUEIO_CAIXA,
                                            FuncionalidadeSistemaEnum.LOTES,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.FIN_OPERACOES_CONTA,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.FINAN_CONTA,
                                            FuncionalidadeSistemaEnum.LANCAMENTO_CONTA_RAPIDO,
                                            FuncionalidadeSistemaEnum.NOVA_CONTA_PAGAR,
                                            FuncionalidadeSistemaEnum.NOVA_CONTAS_RECEBER
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.FIN_OPERACOES_RECEBIVEIS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.RECEBIVEIS
                                    }
                            )
                    }
            ));
            addMenuExplorarConfigs(FIN, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.RELATORIOS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CAIXA_ADIMISTRATIVO,
                            FuncionalidadeSistemaEnum.RELATORIO_DEVOLUCAO_CHEQUE,
                            FuncionalidadeSistemaEnum.CONSULTAR_CAIXA,
                            FuncionalidadeSistemaEnum.CONTAS_A_PAGAR,
                            FuncionalidadeSistemaEnum.CONTAS_A_RECEBER,
                            FuncionalidadeSistemaEnum.DRE,
                            FuncionalidadeSistemaEnum.DEMONSTRATIVO_FINAN,
                            FuncionalidadeSistemaEnum.FECHAMENTO_CAIXA_PLANO_CONTAS,
                            FuncionalidadeSistemaEnum.FLUXO_CAIXA_FINAN,
                            FuncionalidadeSistemaEnum.MOVIMENTACOES_FINAN,
                            FuncionalidadeSistemaEnum.RELATORIO_ORCAMENTARIO,
                            FuncionalidadeSistemaEnum.RESUMO_CONTAS,
                            FuncionalidadeSistemaEnum.LANCAMENTOS,
                    }
            ));
            addMenuExplorarConfigs(FIN, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.CADASTROS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CENTRO_CUSTOS,
                            FuncionalidadeSistemaEnum.CHEQUES_CARTOES_AVULSOS,
                            FuncionalidadeSistemaEnum.FINAN_CONTA,
                            FuncionalidadeSistemaEnum.CONTA_CONTABIL,
                            FuncionalidadeSistemaEnum.FORNCEDOR,
                            FuncionalidadeSistemaEnum.METAS_DO_FINANCEIRO,
                            FuncionalidadeSistemaEnum.RELATORIO_ORCAMENTARIO_CONFIGURACAO,
                            FuncionalidadeSistemaEnum.PESSOA,
                            FuncionalidadeSistemaEnum.PLANO_CONTAS,
                            FuncionalidadeSistemaEnum.RATEIO_INTEGRACAO,
                            FuncionalidadeSistemaEnum.TIPO_CONTA,
                            FuncionalidadeSistemaEnum.TIPO_DOCUMENTO,
                    }
            ));
            MENU_EXPLORAR_GRUPOS.add(FIN);
        }
    }

    private void buildMenuCrm() {
        if (isPermiteRenderizarModulo(CRM.getModulo())) {
            CRM.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(CRM, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CRM_BI
                    }));
            addMenuExplorarConfigs(CRM,
                    new MenuExplorarConfig(
                            MenuSistemaGrupoEnum.OPERACOES,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.MAILING,
                                    FuncionalidadeSistemaEnum.CONTATO_PESSOAL,
                                    FuncionalidadeSistemaEnum.CARTEIRAS,
                                    FuncionalidadeSistemaEnum.MARCAR_COMPARECIMENTO,
                                    FuncionalidadeSistemaEnum.CRM_META_DIARIA,
                                    FuncionalidadeSistemaEnum.CRM_REGISTRO_PARALISACAO,
                            }
                    ));
            addMenuExplorarConfigs(CRM,
                    new MenuExplorarConfig(
                            MenuSistemaGrupoEnum.RELATORIOS,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.AGENDAMENTOS_CRM,
                                    FuncionalidadeSistemaEnum.CONTATO_APP,
                                    FuncionalidadeSistemaEnum.CONSULTA_HISTORICO_CONTATO,
                                    FuncionalidadeSistemaEnum.INDICACAO,
                                    FuncionalidadeSistemaEnum.PASSIVO,
                                    FuncionalidadeSistemaEnum.TOTALIZADOR_METAS,
                            }
                    ));
            addMenuExplorarConfigs(CRM,
                    new MenuExplorarConfig(
                            MenuSistemaGrupoEnum.CADASTROS,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.EVENTO,
                                    FuncionalidadeSistemaEnum.FERIADO,
                                    FuncionalidadeSistemaEnum.GRUPO_COLABORADOR,
                                    FuncionalidadeSistemaEnum.CRM_META_EXTA,
                                    FuncionalidadeSistemaEnum.MODELO_MENSAGEM,
                                    FuncionalidadeSistemaEnum.OBJECAO,
                                    FuncionalidadeSistemaEnum.TEXTO_PADRAO
                            }
                    ));
            MENU_EXPLORAR_GRUPOS.add(CRM);
        }
    }

    private void buildMenuAdm() {
        if (isPermiteRenderizarModulo(ADM.getModulo())) {
            ADM.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(ADM,new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.ADM_BUSINESS_INTELLIGENCE,
                            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE,
                    }));

            addMenuExplorarConfigs(ADM, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES, new MenuExplorarConfig[]{
                    new MenuExplorarConfig(
                            MenuSistemaSubgrupoEnum.ADM_OPERACOES_ESTOQUE,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.BALANCO,
                                    FuncionalidadeSistemaEnum.CARDEX,
                                    FuncionalidadeSistemaEnum.COMPRA,
                                    FuncionalidadeSistemaEnum.CONFIGURAR_PRODUTO_ESTOQUE,
                                    FuncionalidadeSistemaEnum.POSICAO_DO_ESTOQUE,
                            }
                    ),
                    new MenuExplorarConfig(
                            MenuSistemaSubgrupoEnum.ADM_OPERACOES_GESTAO,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.GESTAO_DE_ARMARIOS,
                                    FuncionalidadeSistemaEnum.GESTAO_NEGATIVACOES,
                                    FuncionalidadeSistemaEnum.GESTAO_PERSONAL,
                                    FuncionalidadeSistemaEnum.GESTAO_DE_REMESSAS,
                                    FuncionalidadeSistemaEnum.GESTAO_DE_BOLETOS_ONLINE,
                                    FuncionalidadeSistemaEnum.GESTAO_DE_TRANSACOES,
                                    FuncionalidadeSistemaEnum.GESTAO_DE_TURMA,
                                    FuncionalidadeSistemaEnum.VENDAS_ONLINE,
                                    FuncionalidadeSistemaEnum.VENDAS_ONLINE_ADQUIRA
                            }
                    ),
                    new MenuExplorarConfig(
                            MenuSistemaSubgrupoEnum.ADM_OPERACOES_VENDA,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.NEGOCIACAO,
                                    FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO,
                                    FuncionalidadeSistemaEnum.DIARIA,
                                    FuncionalidadeSistemaEnum.FREE_PASS,
                                    FuncionalidadeSistemaEnum.ORCAMENTO,
                                    FuncionalidadeSistemaEnum.VENDA_AVULSA,
                                    FuncionalidadeSistemaEnum.ADM_VENDA_RAPIDA,
                            }
                    ),
                    new MenuExplorarConfig(
                            MenuSistemaSubgrupoEnum.ADM_OUTRAS_OPERACOES,
                            new FuncionalidadeSistemaEnum[]{
                                    FuncionalidadeSistemaEnum.AUTORIZACAO_ACESSO,
                                    FuncionalidadeSistemaEnum.IMPRIME_RECIBO_BANCO,
                                    FuncionalidadeSistemaEnum.OPERACOES_COLETIVAS,
                                    FuncionalidadeSistemaEnum.REGISTRAR_ACESSO_AVULSO,
                                    FuncionalidadeSistemaEnum.SORTEIO,
                            }
                    ),
            }));

            addMenuExplorarConfigs(ADM, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.RELATORIOS,
                    new MenuExplorarConfig[]{
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_RELATORIOS_ACESSO,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.FECHAMENTO_ACESSOS,
                                            FuncionalidadeSistemaEnum.INDICADOR_ACESSOS,
                                            FuncionalidadeSistemaEnum.LISTA_ACESSOS,
                                            FuncionalidadeSistemaEnum.TOTALIZADOR_ACESSOS,
                                            FuncionalidadeSistemaEnum.TOTALIZADOR_TICKETS,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_RELATORIOS_CLIENTES,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.ANIVERSARIANTES,
                                            FuncionalidadeSistemaEnum.CLIENTE,
                                            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_CANCELADOS,
                                            FuncionalidadeSistemaEnum.RELATORIO_ATESTADO,
                                            FuncionalidadeSistemaEnum.RELATORIO_BONUS,
                                            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_TRANCADOS,
                                            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_COBRANCA_BLOQUEADA,
                                            FuncionalidadeSistemaEnum.CONTRATOS_DURACAO,
                                            FuncionalidadeSistemaEnum.RELATORIO_CONVIDADOS,
                                            FuncionalidadeSistemaEnum.GERAL_CLIENTES,
                                            FuncionalidadeSistemaEnum.GOGOOD_RELATORIO,
                                            FuncionalidadeSistemaEnum.HISTORICO_PONTOS_ALUNO,
                                            FuncionalidadeSistemaEnum.LISTA_CLIENTES_SIMPLIFICADA,
                                            FuncionalidadeSistemaEnum.VENDA_CONSUMIDOR,
                                            FuncionalidadeSistemaEnum.RELATORIO_VISITANTES,
                                            FuncionalidadeSistemaEnum.CLIENTES_COM_RESTRICOES,
                                            FuncionalidadeSistemaEnum.GYM_PASS_RELATORIO,
                                            FuncionalidadeSistemaEnum.SMD_RELATORIO,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_RELATORIOS_COMISSAO,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.COMISSAO_VARIAVEL,
                                            FuncionalidadeSistemaEnum.GESTAO_DE_COMISSAO,
                                            FuncionalidadeSistemaEnum.RELATORIO_REPASSE
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_RELATORIOS_ESTATISTICO,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.PREVISAO_RENOVACAO_CONTRATO,
                                            FuncionalidadeSistemaEnum.RELATORIO_BVS,
                                            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES,
                                            FuncionalidadeSistemaEnum.RELATORIO_PESQUISA
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_RELATORIOS_FINANCEIRO,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.COMPETENCIA_MENSAL,
                                            FuncionalidadeSistemaEnum.CONSULTA_DE_RECIBOS,
                                            FuncionalidadeSistemaEnum.FATURAMENTO_PERIODO,
                                            FuncionalidadeSistemaEnum.FATURAMENTO_RECEBIDO_PERIODO,
                                            FuncionalidadeSistemaEnum.FECHAMENTO_CAIXA_OPERADOR,
                                            FuncionalidadeSistemaEnum.MOVIMENTO_CC_CLIENTE,
                                            FuncionalidadeSistemaEnum.RELATORIO_PARCELAS,
                                            FuncionalidadeSistemaEnum.PEDIDOS_PINPAD,
                                            FuncionalidadeSistemaEnum.RECEITA_PERIODO,
                                            FuncionalidadeSistemaEnum.SALDO_CONTA_CORRENTE,
                                            FuncionalidadeSistemaEnum.SALDO_CREDITO,
                                            FuncionalidadeSistemaEnum.TRANSACOES_PIX
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_RELATORIOS_TURMAS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.CONSULTA_DE_TURMAS,
                                            FuncionalidadeSistemaEnum.CONVITE_AULA_EXPERIMENTAL,
                                            FuncionalidadeSistemaEnum.DESCONTO_OCUPACAO_TURMAS,
                                            FuncionalidadeSistemaEnum.FREQUENCIA_OCUPACAO_TURMAS,
                                            FuncionalidadeSistemaEnum.LISTA_CHAMADA,
                                            FuncionalidadeSistemaEnum.MAPA_TURMAS,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_OUTROS_RELATORIOS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.RELATORIO_GERAL_ARMARIOS,
                                            FuncionalidadeSistemaEnum.CONTROLE_LOG,
                                            FuncionalidadeSistemaEnum.RELATORIO_CLIENTES_ORCAMENTOS,
                                            FuncionalidadeSistemaEnum.RELATORIO_DE_PERSONAL,
                                            FuncionalidadeSistemaEnum.RELATORIO_PRODUTOS,
                                            FuncionalidadeSistemaEnum.SGP_MODALIDADES_COM_TURMAS,
                                            FuncionalidadeSistemaEnum.SGP_MODALIDADES_SEM_TURMAS,
                                            FuncionalidadeSistemaEnum.SGP_TURMAS,
                                            FuncionalidadeSistemaEnum.SGP_AVALIACOES_FISICAS
                                    }
                            ),
                    }
            ));

            addMenuExplorarConfigs(ADM, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.CADASTROS,
                    new MenuExplorarConfig[]{
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_CADASTROS_ACESSO,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.INTEGRACAO_ACESSO,
                                            FuncionalidadeSistemaEnum.LOCAL_ACESSO,
                                            FuncionalidadeSistemaEnum.LOCAL_IMPRESSAO,
                                            FuncionalidadeSistemaEnum.PERFIL_ACESSO,
                                            FuncionalidadeSistemaEnum.PERFIL_ACESSO_UNIFICADO,
                                            FuncionalidadeSistemaEnum.SERVIDOR_FACIAL
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_CADASTROS_CLUBE_DE_VANTAGENS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_ATIVAR,
                                            FuncionalidadeSistemaEnum.BRINDE,
                                            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CAMPANHA,
                                            FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CONFIGURACOES,
                                            FuncionalidadeSistemaEnum.HISTORICO_PONTOS_ALUNO
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_CADASTROS_CONTRATO,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.JUSTIFICATIVA_OPERACAO,
                                            FuncionalidadeSistemaEnum.MODELO_CONTRATO,
                                            FuncionalidadeSistemaEnum.MODELO_ORCAMENTO,
                                            FuncionalidadeSistemaEnum.MOVIMENTO_PRODUTO,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_CADASTROS_FINANCEIROS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.ADQUIRENTE,
                                            FuncionalidadeSistemaEnum.BANCO,
                                            FuncionalidadeSistemaEnum.CONDICAO_DE_PAGAMENTO,
                                            FuncionalidadeSistemaEnum.CONTA_CORRENTE,
                                            FuncionalidadeSistemaEnum.CONVENIO_COBRANCA,
                                            FuncionalidadeSistemaEnum.PINPAD,
                                            FuncionalidadeSistemaEnum.FORMA_PAGAMENTO,
                                            FuncionalidadeSistemaEnum.IMPOSTO,
                                            FuncionalidadeSistemaEnum.INDICE_FINANCEIRO_REAJUSTE_PRECO,
                                            FuncionalidadeSistemaEnum.METAS_FINANCEIRO_VENDA,
                                            FuncionalidadeSistemaEnum.OPERADORA_CARTAO,
                                            FuncionalidadeSistemaEnum.TAXA_COMISSAO,
                                            FuncionalidadeSistemaEnum.TIPO_REMESSA,
                                            FuncionalidadeSistemaEnum.TIPO_RETORNO,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_CADASTROS_PLANOS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.CAMPANHA_CUPOM_DESCONTO,
                                            FuncionalidadeSistemaEnum.CONVENIO_DE_DESCONTO,
                                            FuncionalidadeSistemaEnum.DESCONTO,
                                            FuncionalidadeSistemaEnum.HORARIO,
                                            FuncionalidadeSistemaEnum.MODALIDADE,
                                            FuncionalidadeSistemaEnum.PACOTE,
                                            FuncionalidadeSistemaEnum.PLANO,
                                            FuncionalidadeSistemaEnum.TIPO_MODALIDADE,
                                            FuncionalidadeSistemaEnum.TIPO_PLANO,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_CADASTROS_PRODUTOS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.CATEGORIA_PRODUTO,
                                            FuncionalidadeSistemaEnum.LANCAMENTO_PRODUTO_COLETIVO,
                                            FuncionalidadeSistemaEnum.PRODUTO
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_CADASTROS_TURMAS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.AMBIENTE,
                                            FuncionalidadeSistemaEnum.NIVEL_TURMA,
                                            FuncionalidadeSistemaEnum.TURMA,
                                    }
                            ),
                            new MenuExplorarConfig(
                                    MenuSistemaSubgrupoEnum.ADM_OUTROS_CADASROS,
                                    new FuncionalidadeSistemaEnum[]{
                                            FuncionalidadeSistemaEnum.CATEGORIA_CLIENTES,
                                            FuncionalidadeSistemaEnum.CIDADE,
                                            FuncionalidadeSistemaEnum.CLASSIFICACAO,
                                            FuncionalidadeSistemaEnum.COLABORADOR,
                                            FuncionalidadeSistemaEnum.DEPARTAMENTO,
                                            FuncionalidadeSistemaEnum.EMPRESA,
                                            FuncionalidadeSistemaEnum.GRAU_DE_INSTRUCAO,
                                            FuncionalidadeSistemaEnum.GRUPO_DESCONTO,
                                            FuncionalidadeSistemaEnum.IMPORTACAO,
                                            FuncionalidadeSistemaEnum.PAIS,
                                            FuncionalidadeSistemaEnum.PARENTESCO,
                                            FuncionalidadeSistemaEnum.PERGUNTA,
                                            FuncionalidadeSistemaEnum.PESQUISA,
                                            FuncionalidadeSistemaEnum.PROFISSAO,
                                            FuncionalidadeSistemaEnum.QUESTIONARIO,
                                            FuncionalidadeSistemaEnum.TAMANHO_ARMARIO,
                                            FuncionalidadeSistemaEnum.USUARIO
                                    }
                            ),
                    }
            ));

            MENU_EXPLORAR_GRUPOS.add(ADM);
        }
    }

    private void buildMenuCentralEventos() {
        if (isPermiteRenderizarModulo(CENTRAL_DE_EVENTOS.getModulo())) {
            CENTRAL_DE_EVENTOS.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(CENTRAL_DE_EVENTOS, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.BUSINESS_INTELLIGENCE_CE,
                    }));
            addMenuExplorarConfigs(CENTRAL_DE_EVENTOS, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.SIMULAR_ORCAMENTO,
                            FuncionalidadeSistemaEnum.LISTA_PROSPECTS,
                            FuncionalidadeSistemaEnum.AGENDA_VISISTA,
                            FuncionalidadeSistemaEnum.CONVERSAS,
                    }));
            addMenuExplorarConfigs(CENTRAL_DE_EVENTOS, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.RELATORIOS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AGENDA_EVENTOS,
                            FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO_CE,
                            FuncionalidadeSistemaEnum.PESQUISA_GERAL,
                            FuncionalidadeSistemaEnum.GESTAO_CREDITO,
                    }));
            addMenuExplorarConfigs(CENTRAL_DE_EVENTOS, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.CADASTROS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.CADASTRO_INICIAL_CE,
                    }));
            MENU_EXPLORAR_GRUPOS.add(CENTRAL_DE_EVENTOS);
        }
    }

    private void buildMenuAgenda() {
        if (isPermiteRenderizarModulo(AGENDA.getModulo())) {
            AGENDA.menuExplorarConfigs.clear();
            addMenuExplorarConfigs(AGENDA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.BI,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AGENDA_BI
                    }));
            addMenuExplorarConfigs(AGENDA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.OPERACOES,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AGENDA_AULAS,
                            FuncionalidadeSistemaEnum.AGENDA_SERVICOS,
                    }));
            addMenuExplorarConfigs(AGENDA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.RELATORIOS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AGENDA_AMBIENTE,
                            FuncionalidadeSistemaEnum.AGENDA_MODALIDADE,
                            FuncionalidadeSistemaEnum.AGENDA_INDICADORES,
                            FuncionalidadeSistemaEnum.AGENDA_AULA_EXCLUIDA,
                            FuncionalidadeSistemaEnum.AGENDA_TV_AULA,
                            FuncionalidadeSistemaEnum.AGENDA_TV_GESTOR
                    }));
            addMenuExplorarConfigs(AGENDA, new MenuExplorarConfig(
                    MenuSistemaGrupoEnum.CADASTROS,
                    new FuncionalidadeSistemaEnum[]{
                            FuncionalidadeSistemaEnum.AGENDA_CONFIGURAR_AULAS,
                            FuncionalidadeSistemaEnum.AGENDA_TIPOS_AGENDAMENTO,
                    }));
            MENU_EXPLORAR_GRUPOS.add(AGENDA);
        }
    }

    private void addMenuExplorarConfigs(MenuExplorarGrupo grupo, MenuExplorarConfig config){
        if(config == null){
            return;
        }

        boolean temFuncionalidades = config.getFuncionalidades() != null && !config.getFuncionalidades().isEmpty();

        if(!temFuncionalidades && config.getSubgrupos() != null){
            temFuncionalidades = Arrays.stream(config.getSubgrupos()).anyMatch(subgrupo -> subgrupo.getFuncionalidades() != null && !subgrupo.getFuncionalidades().isEmpty());
        }

        if(temFuncionalidades){
            grupo.menuExplorarConfigs.add(config);
        }
    }

    public List<MenuExplorarGrupo> getMenuExplorarGrupos() {
        return MENU_EXPLORAR_GRUPOS;
    }
}
