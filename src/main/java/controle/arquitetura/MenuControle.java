/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import controle.arquitetura.menu.FuncionalidadeSistemaEnumTO;
import controle.arquitetura.menu.MenuExplorarConfig;
import controle.arquitetura.menu.MenuExplorarGrupo;
import controle.arquitetura.security.LoginControle;
import controle.basico.ConfiguracaoSistemaControle;
import controle.basico.FuncionalidadeControle;
import controle.basico.MenuAcessoFacilControle;
import controle.crm.ConfiguracaoSistemaCRMControle;
import controle.financeiro.ConfiguracaoFinanceiroControle;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.notificacao.IdLocalicazaoMenuEnum;
import servicos.notificacao.NotificacaoMsService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static java.util.Objects.isNull;

public class MenuControle extends SuperControle {

    private boolean apresentarTopo = true;
    private List<GrupoFuncionalidadeSistemaEnumAux> gruposExibirAux;
    private List<String> menus;
    private String lastURI = null;
    private ModuloAberto ultimoModuloAcessado;
    private String ultimaPaginaAcessada;
    private List<String> ultimasPaginasAcessadas;
    private String subMenu = null;
    private boolean reprocessar = false;
    private List<MenuExplorarGrupo> gruposMenuExplorar;
    private JSONObject modulosJson;
    private String siglaModulo;
    private int paginaAtualModulos = 1;
    private String taskbarSelected;
    private boolean exibirFavoritos;
    private String oncompleteModulo;
    private String onCompleteModuloAtual = "";

    private List<Modulo> modulosConfiguracoes;
    private boolean exibirConfiguracao;
    private String labelBtnVoltar;
    private List<Modulo> historicoNavegacaoModulos;
    private static List<Modulo> NO_BACK_MODULES = Arrays.asList(new Modulo[]{Modulo.PESSOAS});
    private String urlGoBackRedirect;

    public void processarLabelBtnVoltar(){
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        if(getHistoricoNavegacaoModulos() == null || getHistoricoNavegacaoModulos().size() == 0){
            Modulo modulo = Modulo.fromSigla(loginControle.getModuloAberto().getSigla());
            this.labelBtnVoltar = "Retornar ao "+ StringUtils.capitalize(modulo.getDescricao());
        }else{
            this.labelBtnVoltar = "Retornar ao "+ StringUtils.capitalize(getUltimoModuloVisitado().getDescricao());
        }
    }

    public boolean isExibirBtnVoltar(){
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        boolean moduloPesssoas = false;
        if(loginControle != null && loginControle.getModuloAberto() != null && loginControle.getModuloAberto().equals(ModuloAberto.PESSOAS)){
            moduloPesssoas = true;
        }
        return exibirFavoritos || exibirConfiguracao || moduloPesssoas;
    }

    public void processarFavoritos(){
        setExibirFavoritos(true);
        setExibirConfiguracao(false);
        setUrlGoBackRedirect(null);
        processarLabelBtnVoltar();
        MenuAcessoFacilControle acessoFacilControle = JSFUtilities.getControlador(MenuAcessoFacilControle.class);
        acessoFacilControle.obterFuncionalidades();
    }

    public boolean isExibirMenuExplorar(){
//        return isUsuarioPacto();
        return true;
    }

    private String scriptAbrirPopUp = "";

    private HttpServletRequest getRequest(){
        return (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
    }

    public void addHistoricoNavegacaoModulos(Modulo modulo){
        if(getHistoricoNavegacaoModulos() == null){
            setHistoricoNavegacaoModulos(new ArrayList<>());
        }
        if(!(getHistoricoNavegacaoModulos().size() > 0 && getHistoricoNavegacaoModulos().get(getHistoricoNavegacaoModulos().size()-1).equals(modulo))){
            getHistoricoNavegacaoModulos().add(modulo);
        }
    }

    private String getIdRecurso(){

            String idRecurso = getRequest().getParameter("idRecurso");
            if(idRecurso != null && !idRecurso.trim().isEmpty() && !idRecurso.equals("undefined") && !idRecurso.equals("null")){
                return idRecurso;
            }else {
                return null;
            }
    }

    private IdLocalicazaoMenuEnum getIdLocalizacaoMenu(){
        try{
            String idLocalizacaoMenu = getRequest().getParameter("idLocalizacaoMenu");
            if(idLocalizacaoMenu != null && !idLocalizacaoMenu.trim().isEmpty() && !idLocalizacaoMenu.equals("undefined") && !idLocalizacaoMenu.equals("null")){
                return IdLocalicazaoMenuEnum.valueOf(idLocalizacaoMenu);
            }
        }catch (Exception e){
            Uteis.logarDebug("Erro ao obter idLocalizacaoMenu: "+e.getMessage());
        }

        return null;
    }

    public String abrirModulo() {
        String moduloRedirecionar = "";
        String idLocalizacaoMenu = "";
        setOncompleteModulo("");
        setExibirConfiguracao(false);
        try {
            moduloRedirecionar = getRequest().getParameter("siglaModulo");
            idLocalizacaoMenu = getRequest().getParameter("idLocalizacaoMenu");
            LoginControle loginControle = (LoginControle) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("LoginControle");
            Modulo modulo = Modulo.fromSigla(moduloRedirecionar);
            setExibirFavoritos(false);
            setTaskbarSelected(null);
            setUrlGoBackRedirect(null);
            addHistoricoNavegacaoModulos(modulo);
            if(getIdLocalizacaoMenu() != null && loginControle != null && loginControle.getModulo() != null){
                IdLocalicazaoMenuEnum idLocalicazaoMenuEnum = IdLocalicazaoMenuEnum.valueOf(idLocalizacaoMenu);
                NotificacaoMsService.notificarAcessoMenuModulo(modulo, idLocalicazaoMenuEnum);
            }
            if(Modulo.ZILLYON_WEB.equals(modulo)){
                redirectUrl(loginControle.getAbrirNovaPlataforma(Modulo.NOVO_TREINO.getSiglaModulo(), null, "/adm/home"));
                return "";
            }
            if (modulo.isZwUi()) {
                redirectUrl(loginControle.getAbrirNovaPlataforma(moduloRedirecionar));
            }

            if (moduloRedirecionar.equals(Modulo.CUSTOMER_RELATIONSHIP_MANAGEMENT.getSiglaModulo())) {
                return loginControle.abrirModuloCRM();
            }

            if (moduloRedirecionar.equals(Modulo.ZILLYON_WEB.getSiglaModulo())) {
                return loginControle.abrirZillyonWeb();
            }

            if (moduloRedirecionar.equals(Modulo.AULA_CHEIA.getSiglaModulo())) {
                return loginControle.getAbrirAulaCheia();
            }

            if (moduloRedirecionar.equals(Modulo.FINANCEIRO.getSiglaModulo())) {
                return loginControle.abrirModuloFinanceiro();
            }

            if (moduloRedirecionar.equals(Modulo.STUDIO.getSiglaModulo())) {
                return loginControle.abrirModuloEstudio();
            }

            if (moduloRedirecionar.equals(Modulo.UNIVERSIDADE_COORPORATIVA_PACTO.getSiglaModulo())) {
                return loginControle.getLinkModuloUCP();
            }

            if (moduloRedirecionar.equals(Modulo.CANAL_CLIENTE.getSiglaModulo())) {
                setOncompleteModulo("abrirMsgNotificacaoCanalCliente()");
                return "";
            }

            if (moduloRedirecionar.equals(Modulo.NOTAS.getSiglaModulo())) {
                return loginControle.abrirModuloNotaFiscal();
            }

            if (moduloRedirecionar.equals(Modulo.GAME_OF_RESULTS.getSiglaModulo())) {
                setOncompleteModulo("window.open('"+loginControle.getAbrirModuloGame()+"', '_blank')");
            }

            if (moduloRedirecionar.equals(Modulo.CENTRAL_DE_EVENTOS.getSiglaModulo())) {
                return loginControle.abrirCE();
            }
            if(moduloRedirecionar.equals(Modulo.NOVO_TREINO.getSiglaModulo())){
                return loginControle.getAbrirTreinoNovo();
            }

            Uteis.logarDebug("Não foi possivel redirecionar para o modulo " + moduloRedirecionar + ". A navegaçãoo não está tratada em MenuControle.abrirModulo");
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao redirecionar para o modulo " + moduloRedirecionar);
            ex.printStackTrace();
        }

        return "";
    }

    private boolean isExibirMenu(){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String embed = request.getParameter("embed");

        return embed == null || embed.trim().isEmpty() ? true : Boolean.parseBoolean(embed);
    }

    public void prepararMenuExibir(String menuNomes) {
        this.menus = (Arrays.asList(menuNomes.split(",")));
        processarMenus();
    }

    public void processarMenus() {
        FuncionalidadeControle funCtrl = JSFUtilities.getControlador(FuncionalidadeControle.class);
        funCtrl.getFuncHabilitadas().remove(FuncionalidadeSistemaEnum.ABRIR_CAIXA.getExpressaoRenderizar());
        funCtrl.getFuncHabilitadas().remove(FuncionalidadeSistemaEnum.FECHAR_CAIXA.getExpressaoRenderizar());

        long millisInicioTrabalhaComPontuacao = System.currentTimeMillis();
        final boolean empresaTrabalhaComPontuacao = isEmpresaTrabalhaComPontuacao();
        long millisTrabalhaComPontuacao = System.currentTimeMillis() - millisInicioTrabalhaComPontuacao;
        Uteis.logarDebug(String.format(
                "Processamento se a empresa trabalha com Pontos demorou %d milissegundos.", millisTrabalhaComPontuacao));

        boolean isMenuNovo = getMenuZwUi();
        if (isMenuNovo) {
            processarMenusZwUI(funCtrl, empresaTrabalhaComPontuacao);
            if(gruposMenuExplorar == null){
                processarMenuExplorar();
            }
        } else {
            processarMenusAntigo(funCtrl, empresaTrabalhaComPontuacao);
        }
    }

    private void processarMenusAntigo(FuncionalidadeControle funCtrl, boolean empresaTrabalhaComPontuacao) {
        long millisInicioProcessarMenus = System.currentTimeMillis();

        this.gruposExibirAux = new ArrayList<>();
        Optional.ofNullable(menus)
                .orElse(Collections.emptyList())
                .forEach(m -> {
                    for (GrupoFuncionalidadeSistemaEnum g : GrupoFuncionalidadeSistemaEnum.valuesOldMenu()) {
                        if (g.getNomeMenu().equals(m)
                                || (!MenuFuncionalidadeEnum.ADM_CADASTROS.getName().equals(m) && g.getNomeMenu().startsWith(m))) {

                            processarGrupoFuncionalidade(funCtrl, empresaTrabalhaComPontuacao, g);
                        }
                    }
                });
        long millisProcessarMenu = System.currentTimeMillis() - millisInicioProcessarMenus;
        Uteis.logarDebug(String.format(
                "Processamento do menu (ANTIGO) todo demorou %d milissegundos.", millisProcessarMenu));
    }

    private void processarGrupoFuncionalidade(FuncionalidadeControle funCtrl, boolean empresaTrabalhaComPontuacao, GrupoFuncionalidadeSistemaEnum g) {
        GrupoFuncionalidadeSistemaEnumAux gAux = getGrupoFuncionalidadeSistemaEnumAux(funCtrl, g);

        if (!gruposExibirAux.contains(gAux)
                && (
                        gAux.isPossuiFuncionalidades() ||
                        gAux.isPossuiSubMenu() ||
                        (gAux.getFuncionalidade() != null)
                )
                && (gAux.isRenderizar() || gAux.getFuncionalidade() != null)
        ) {
            gruposExibirAux.add(gAux);
        }

        if (isGrupoFuncionalidadeSistemaBI(g)) {
            avaliarApresentacaoBIAdm(funCtrl, empresaTrabalhaComPontuacao, gAux);
        }
    }

    public void processarMenusFinaneiro(){
        gruposMenuExplorar.stream().forEach(menuExplorarGrupo -> {
            if(menuExplorarGrupo.getModulo() != null && menuExplorarGrupo.getModulo().equals(Modulo.FINANCEIRO)){
               menuExplorarGrupo.buildMenuFin();
            }
        });
    }

    public void processarMenuExplorar() {
        if (gruposMenuExplorar == null && getMenuZwUi()) {
            long millisInicioProcessamentoMenu = System.currentTimeMillis();
            MenuExplorarGrupo menuExplorarGrupo = new MenuExplorarGrupo();
            gruposMenuExplorar = menuExplorarGrupo.getMenuExplorarGrupos();

            processarModulosMenu();
            processarNomeMenuExplorar();

            long millisProcessamentoMenu = System.currentTimeMillis() - millisInicioProcessamentoMenu;
            Uteis.logarDebug(String.format(
                    "Processamento do menu explorar demorou %d milissegundos.", millisProcessamentoMenu));
        }
    }

    private void processarNomeMenuExplorar() {
        try {
            boolean novoCaixa = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.CAIXA_ABERTO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            boolean novoVenda = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.VENDA_AVULSA, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            boolean novoNego = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.NEGOCIACAO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            for (MenuExplorarGrupo grupo : this.gruposMenuExplorar) {
                for (MenuExplorarConfig grupoConfig : grupo.getMenuExplorarConfigs()) {
                    try {
                        if (grupoConfig.getFuncionalidadesFavorito() != null) {
                            for (FuncionalidadeSistemaEnumTO funcionalidadeSistemaEnumTO : grupoConfig.getFuncionalidadesFavorito()) {
                                if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO)) {
                                    funcionalidadeSistemaEnumTO.setNovaVersao(novoCaixa);
                                } else if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(FuncionalidadeSistemaEnum.VENDA_AVULSA)) {
                                    funcionalidadeSistemaEnumTO.setNovaVersao(novoVenda);
                                } else if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(FuncionalidadeSistemaEnum.NEGOCIACAO)) {
                                    funcionalidadeSistemaEnumTO.setNovaVersao(novoNego);
                                }
                            }
                        }
                        if (grupoConfig.getSubgrupos() != null) {
                            for (MenuExplorarConfig subGrupo : grupoConfig.getSubgrupos()) {
                                for (FuncionalidadeSistemaEnumTO funcionalidadeSistemaEnumTO : subGrupo.getFuncionalidadesFavorito()) {
                                    if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO)) {
                                        funcionalidadeSistemaEnumTO.setNovaVersao(novoCaixa);
                                    } else if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(FuncionalidadeSistemaEnum.VENDA_AVULSA)) {
                                        funcionalidadeSistemaEnumTO.setNovaVersao(novoVenda);
                                    } else if (funcionalidadeSistemaEnumTO.getFuncionalidade().equals(FuncionalidadeSistemaEnum.NEGOCIACAO)) {
                                        funcionalidadeSistemaEnumTO.setNovaVersao(novoNego);
                                    }
                                }
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarMenusZwUI(FuncionalidadeControle funCtrl,
                                    final boolean empresaTrabalhaComPontuacao) {
        long millisInicioProcessarMenus = System.currentTimeMillis();

        this.gruposExibirAux = new ArrayList<>();
        Optional.ofNullable(menus)
                .orElse(Collections.emptyList())
                .forEach(m -> {
                    for (GrupoFuncionalidadeSistemaEnum g : GrupoFuncionalidadeSistemaEnum.valuesNewMenu()) {
                        if (g.getNomeMenu().equals(m)
                                || (!MenuFuncionalidadeEnum.ADM_CADASTROS.getName().equals(m) && g.getNomeMenu().startsWith(m))) {
                            processarGrupoFuncionalidade(funCtrl, empresaTrabalhaComPontuacao, g);
                        }
                    }
                });

        processarNomeMenuZwUI();
        long millisProcessarMenu = System.currentTimeMillis() - millisInicioProcessarMenus;
        Uteis.logarDebug("Processamento do menu (NOVO) todo demorou " + millisProcessarMenu + " milissegundos.");
    }

    private void processarNomeMenuZwUI() {
        try {
            boolean novoCaixa = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.CAIXA_ABERTO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            boolean novoVenda = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.VENDA_AVULSA, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            boolean novoNego = getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.NEGOCIACAO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo());
            for (GrupoFuncionalidadeSistemaEnumAux grupo : this.gruposExibirAux) {
                try {
                    if (grupo.getFuncionalidade() != null) {
                        if (grupo.getFuncionalidade().getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO)) {
                            grupo.getFuncionalidade().setNovaVersao(novoCaixa);
                        } else if (grupo.getFuncionalidade().getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.VENDA_AVULSA)) {
                            grupo.getFuncionalidade().setNovaVersao(novoVenda);
                        } else if (grupo.getFuncionalidade().getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.NEGOCIACAO)) {
                            grupo.getFuncionalidade().setNovaVersao(novoNego);
                        }
                    }
                    if (grupo.getFuncionalidades() != null) {
                        for (FuncionalidadeSistemaEnumAux func : grupo.getFuncionalidades()) {
                            if (func.getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO)) {
                                func.setNovaVersao(novoCaixa);
                            } else if (func.getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.VENDA_AVULSA)) {
                                func.setNovaVersao(novoVenda);
                            } else if (func.getFuncionalidadeSistemaEnum().equals(FuncionalidadeSistemaEnum.NEGOCIACAO)) {
                                func.setNovaVersao(novoNego);
                            }
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private boolean isGrupoFuncionalidadeSistemaBI(GrupoFuncionalidadeSistemaEnum grupoFuncionalidadeSistemaEnum) {
        return
                GrupoFuncionalidadeSistemaEnum.CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE.equals(grupoFuncionalidadeSistemaEnum)||
                GrupoFuncionalidadeSistemaEnum.ADM_BUSINESS_INTELLIGENCE_ZW_UI_NAO_TRABALHA_COM_PONTUCAO.equals(grupoFuncionalidadeSistemaEnum);
    }

    private void avaliarApresentacaoBIAdm(FuncionalidadeControle funCtrl, boolean empresaTrabalhaComPontuacao, GrupoFuncionalidadeSistemaEnumAux gAux) {
        if (!empresaTrabalhaComPontuacao) {
            if (gAux.getGrupoFuncionalidadeSistemaEnum().toString().equals(getGrupoFuncionalidadeSistemaEnumAux(funCtrl, GrupoFuncionalidadeSistemaEnum.CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE).getGrupoFuncionalidadeSistemaEnum().toString())
            ) {
                gruposExibirAux.remove(gAux);
            }
        }
    }

    private boolean isEmpresaTrabalhaComPontuacao() {
        try {
            return getEmpresaLogado().isTrabalharComPontuacao();
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao obter empresa logada");
            return false;
        }
    }

    private void processarModulosMenu() {
        modulosConfiguracoes = new ArrayList<>();
        JSONObject json = new JSONObject();
        JSONArray jsonArray = new JSONArray();

        if (gruposMenuExplorar != null) {
            List<Modulo> modulosExplorar = new ArrayList<>();
            gruposMenuExplorar.forEach(it -> {
                modulosExplorar.add(it.getModulo());
                if( it.getModulo().isPossuiConfiguracao() ){
                    if (UteisValidacao.emptyString(it.getModulo().getExpresaoRenderizarConfig()) ||
                            isPermiteExibirGrupo(it.getModulo().getExpresaoRenderizarConfig())) {
                        if (it.getModulo().equals(Modulo.NOVO_TREINO)) {
                            modulosConfiguracoes.add(Modulo.INTEGRACOES);
                            modulosConfiguracoes.add(Modulo.NOVO_TREINO);
                        } else {
                            modulosConfiguracoes.add(it.getModulo());
                        }
                    }
                }
//                modulosConfiguracoes.stream().sorted((m1, m2) -> m1.getDescricao().compareTo(m2.getDescricao()));
                JSONObject jsonModulo = new JSONObject();
                jsonModulo.put("iconeRelaiveUrl", it.getModulo().getIconeRelaiveUrl());
                jsonModulo.put("siglaModulo", it.getModulo().getSiglaModulo());
                jsonModulo.put("descricao", it.getModulo().getDescricao());
                jsonArray.put(jsonModulo);

            });
            modulosConfiguracoes.sort((s1,s2) -> s1.getDescricao().compareTo(s2.getDescricao()));
            json.put("modulos",  jsonArray);
            this.modulosJson =  json;
        }
    }

    private GrupoFuncionalidadeSistemaEnumAux getGrupoFuncionalidadeSistemaEnumAux(FuncionalidadeControle funcionalidadeControle, GrupoFuncionalidadeSistemaEnum grupo) {
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        GrupoFuncionalidadeSistemaEnumAux gAux = new GrupoFuncionalidadeSistemaEnumAux();
        grupo.ordenarFuncionalidadesMenuZWUI();
        gAux.setGrupoFuncionalidadeSistemaEnum(grupo);
        gAux.setRenderizar(isNull(grupo.getExpressaoRenderizar()) ? grupo.isRenderizar() : funcionalidadeControle.validarPermissao(grupo.getExpressaoRenderizar()));
        gAux.setRenderizarGrupo(isNull(grupo.getExpressaoRenderizarGrupo()) || isExibirGrupo(grupo));
        ArrayList<GrupoFuncionalidadeSistemaEnumAux> subMenusAux = new ArrayList<>();
        int cont = 0;

        if (grupo.getSubMenu() != null) {
            for (GrupoFuncionalidadeSistemaEnum grupoAux : grupo.getSubMenu()) {
                GrupoFuncionalidadeSistemaEnumAux subMenuAux = getGrupoFuncionalidadeSistemaEnumAux(funcionalidadeControle, grupoAux);
                subMenusAux.add(subMenuAux);
            }
            GrupoFuncionalidadeSistemaEnumAux[] subMenusAdd = new GrupoFuncionalidadeSistemaEnumAux[subMenusAux.size()];
            gAux.setSubMenu(subMenusAux.toArray(subMenusAdd));
        }

        if (grupo.getFuncionalidades() != null) {
            gAux.setFuncionalidades(new FuncionalidadeSistemaEnumAux[grupo.getFuncionalidades().length]);
            cont = 0;
            for (FuncionalidadeSistemaEnum fun : grupo.getFuncionalidades()) {
                fun.prepararSubFuncionalidade();
                FuncionalidadeSistemaEnumAux funAux = new FuncionalidadeSistemaEnumAux();
                funAux.setFuncionalidadeSistemaEnum(fun);
                funAux.setSubFuncionalidades(fun.getSubFuncionalidades());
                gAux.getFuncionalidades()[cont] = funAux.clone();
                funAux = gAux.getFuncionalidades()[cont];
                boolean permiteRenderizar = isPermiteRenderizar(funcionalidadeControle, fun, loginControle);
                funAux.setRenderizar(permiteRenderizar);
                cont++;
            }
        } else if (grupo.getFuncionalidade() != null) {
            grupo.getFuncionalidade().prepararSubFuncionalidade();
            FuncionalidadeSistemaEnumAux funAux = new FuncionalidadeSistemaEnumAux();
            funAux.setFuncionalidadeSistemaEnum(grupo.getFuncionalidade());
            funAux.setSubFuncionalidades(grupo.getFuncionalidade().getSubFuncionalidades());
            boolean permiteRenderizar = isPermiteRenderizar(funcionalidadeControle, grupo.getFuncionalidade(), loginControle);
            funAux.setRenderizar(permiteRenderizar);
            gAux.setFuncionalidade(funAux.clone());
            gAux.setMenuSolto(true);
        }

        gAux.setRenderizar(gAux.isRenderizar() && permiteRenrizarGrupo(gAux));

        return gAux;
    }

    private boolean permiteRenrizarGrupo(GrupoFuncionalidadeSistemaEnumAux grupo) {
        if(grupo == null || grupo.getFuncionalidades() == null || grupo.getFuncionalidades().length == 0){
            return false;
        }

        return java.util.Arrays.stream(grupo.getFuncionalidades()).anyMatch(f -> f.isRenderizar());
    }

    private static boolean isPermiteRenderizar(FuncionalidadeControle funcionalidadeControle, FuncionalidadeSistemaEnum fun, LoginControle loginControle) {
        boolean expressaoRenderizar = funcionalidadeControle.validarPermissao(fun.getExpressaoRenderizar());
        boolean ativa = loginControle.isFuncionalidadeAtiva(fun);
        boolean permiteRenderizar = expressaoRenderizar && ativa;
        return permiteRenderizar;
    }

    private boolean isPermiteExibirGrupo(String param) {
        if (param != null) {

            if (param.equals("true")) {
                return true;
            } else if (param.equals("false")) {
                return false;
            } else if (!param.equals("")) {
                try {
                    return JSFUtilities.getManagedBeanValue(param) != null && (Boolean) JSFUtilities.getManagedBeanValue(param);
                } catch (Exception e) {
                    Uteis.logar("Erro ao validar pemissao.");
                    Uteis.logar(e, FuncionalidadeControle.class);
                    return false;
                }
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

    private boolean isExibirGrupo(GrupoFuncionalidadeSistemaEnum grupo) {
        String param = grupo.getExpressaoRenderizarGrupo();
        if (param != null) {

            if (param.equals("true")) {
                return true;
            } else if (param.equals("false")) {
                return false;
            } else if (!param.equals("")) {
                try {
                    return JSFUtilities.getManagedBeanValue(param) != null && (Boolean) JSFUtilities.getManagedBeanValue(param);
                } catch (Exception e) {
                    Uteis.logar("Erro ao validar pemissao de acesso ao grupo de funcionalidade.");
                    Uteis.logar(e, FuncionalidadeControle.class);
                    return false;
                }
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

    public void setApresentarTopo(boolean apresentarTopo) {
        this.apresentarTopo = apresentarTopo;
    }

    public boolean isApresentarMenu(){
        Boolean menu = (Boolean) JSFUtilities.getFromSession("menu");
        if(menu != null && !menu) {
            return false;
        }else{
            return true;
        }
    }

    public boolean isApresentarTopo() {
        String param = request().getParameter("state");
        if (param != null) {
            if (param.equals("AC")) {
                setApresentarTopo(false);
                return apresentarTopo;
            } else {
                setApresentarTopo(true);
                return apresentarTopo;
            }
        } else {
            setApresentarTopo(true);
            return apresentarTopo;
        }
    }

    public void processarConfiguracao(){
        setExibirFavoritos(false);
        setExibirConfiguracao(true);
        setUrlGoBackRedirect(null);
        this.processarLabelBtnVoltar();
    }

    public void processarRetornarModulo(){
        setOnCompleteModuloAtual("");
        String siglaModuloAtual = getSiglaModulo();
        Modulo ultimoModuloVisitado = getUltimoModuloVisitado();
        setSiglaModulo(ultimoModuloVisitado.getSiglaModulo());
        LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class);
        if(ultimoModuloVisitado.isZwUi() || ultimoModuloVisitado.equals(Modulo.ZILLYON_WEB)){
            String urlRedirect = "";
            if (ultimoModuloVisitado.equals(Modulo.ZILLYON_WEB)) {
                urlRedirect = loginControle.getAbrirNovaPlataforma("NZW");
            } else {
                urlRedirect = loginControle.getAbrirNovaPlataforma(ultimoModuloVisitado.getSiglaModulo());
            }
            try {
                redirectUrl(urlRedirect);
                return;
            } catch (IOException e) {
                Uteis.logarDebug("Erro ao redirecionar para a url: " + urlRedirect);
                e.printStackTrace();
            }
        }
        if(loginControle != null){
            loginControle.setModuloAberto(ultimoModuloVisitado);
        }
        setExibirFavoritos(false);
        setExibirConfiguracao(false);
        List<String> menus = MenuFuncionalidadeEnum.getMenusZwUiFromURI(loginControle.getModuloAberto()).get();
        if(!menus.equals(this.menus)){
            JSFUtilities.getRequest().setAttribute("grupoFuncionalidadeMenu", menus);
            setReprocessar(true);
        }
        String paginaRetornar = null;
        if(getUltimasPaginasAcessadas() != null){
            int  i = getUltimasPaginasAcessadas().size() - 1;
            while(i >= 0) {
                String pagina = getUltimasPaginasAcessadas().get(i);
                boolean isPaginaModuloPessoa = pagina.equals("clientes.jsp") || pagina.equals("preCadastro.jsp") || pagina.equals("cliente.jsp") || pagina.equals("clienteNav.jsp");
                if(!isPaginaModuloPessoa){
                    paginaRetornar = pagina;
                    break;
                }
                i--;
            }
        }

        try {
            if(paginaRetornar != null){
                redirect("/faces/"+paginaRetornar);
            } else {
                loginControle.abrirModuloAtual();
                setOnCompleteModuloAtual(loginControle.getOnCompleteModuloAtual());
            }
        } catch (IOException e) {
            Uteis.logarDebug("Não foi possivel processar o retorno do modulo com redirect para pagina"+paginaRetornar+". Modulo atual: "+siglaModuloAtual+", Pagina: "+paginaRetornar);
        }
    }

    public void actionConfiguracao(ActionEvent evt){
        try {

            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class);
            String siglaFuncionalidade = (String)evt.getComponent().getAttributes().get("funcionalidade");

            String urlPath = JSFUtilities.getRequest().getContextPath() + JSFUtilities.getRequest().getServletPath();

            Modulo modulo = Modulo.fromSigla(siglaFuncionalidade);
            switch (modulo) {
                case ZILLYON_WEB:
                    prepararConfiguracaoSistemaControle();
                    setScriptAbrirPopUp("abrirPopup('"+ urlPath +"/configuracaoSistemaForm.jsp', 'ConfiguracaoSistema', 1024, 768);");
                    break;
                case FINANCEIRO:
                    prepararConfiguracaoFinanceiroControle();
                    setScriptAbrirPopUp("abrirPopup('"+ urlPath +"/pages/finan/finanConfiguracoes.jsp?modulo=financeiroWeb', 'ConfiguracaoFinanceiro', 1024, 768)");
                    break;
                case CUSTOMER_RELATIONSHIP_MANAGEMENT:
                    prepararConfiguracaoSistemaCRMControle();
                    setScriptAbrirPopUp("abrirPopup('"+ urlPath +"/configuracaoSistemaCRMForm.jsp', 'ConfiguracaoCrm', 1024, 768);");
                    break;
                case INTEGRACOES:
                    redirectUrl(loginControle.getAbrirNovaPlataforma(siglaFuncionalidade, null, "/config/integracoes-v2"));
                    break;
                case NOVO_TREINO:
                    redirectUrl(loginControle.getAbrirNovaPlataforma(siglaFuncionalidade, null, "/config"));
                    break;
            }
        }catch (Exception ex){
            Uteis.logarDebug("Erro ao abrir configuração do modulo");
            ex.printStackTrace();
        }
    }

    private void prepararConfiguracaoSistemaCRMControle() throws Exception {
        ConfiguracaoSistemaCRMControle configuracaoSistemaCRMControle = (ConfiguracaoSistemaCRMControle) JSFUtilities.getFromSession(ConfiguracaoSistemaCRMControle.class);
        if(configuracaoSistemaCRMControle == null){
            configuracaoSistemaCRMControle = new ConfiguracaoSistemaCRMControle();
        }
        configuracaoSistemaCRMControle.iniciar();
    }

    private void prepararConfiguracaoFinanceiroControle() {
        ConfiguracaoFinanceiroControle configuracaoFinanceiroControle = (ConfiguracaoFinanceiroControle) JSFUtilities.getFromSession(ConfiguracaoFinanceiroControle.class);
        if(configuracaoFinanceiroControle == null){
            configuracaoFinanceiroControle = new ConfiguracaoFinanceiroControle();
        }
        configuracaoFinanceiroControle.preparaEdicao();
    }

    private void prepararConfiguracaoSistemaControle() throws Exception {
        ConfiguracaoSistemaControle configuracaoSistemaControle = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession(ConfiguracaoSistemaControle.class);
        if(configuracaoSistemaControle == null){
            configuracaoSistemaControle = new ConfiguracaoSistemaControle();
        }
        configuracaoSistemaControle.novo();
    }

    public List<GrupoFuncionalidadeSistemaEnumAux> getGruposExibirAux() {
        return gruposExibirAux;
    }

    public void setGruposExibirAux(List<GrupoFuncionalidadeSistemaEnumAux> gruposExibirAux) {
        this.gruposExibirAux = gruposExibirAux;
    }

    public String getLastURI() {
        return lastURI;
    }

    public void setLastURI(String lastURI) {
        this.lastURI = lastURI;
    }

    public boolean isReprocessar() {
        return reprocessar;
    }

    public void setReprocessar(boolean reprocessar) {
        this.reprocessar = reprocessar;
    }

    public void fecharSubMenu() {
        subMenu = null;
    }

    public void abrirSubMenu() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        this.subMenu = request.getParameter("menuAberto");
    }

    public String getSubMenu() {
        return subMenu;
    }

    public void setSubMenu(String subMenu) {
        this.subMenu = subMenu;
    }

    public List<MenuExplorarGrupo> getGruposMenuExplorar() {
        return gruposMenuExplorar;
    }

    public void setGruposMenuExplorar(List<MenuExplorarGrupo> gruposMenuExplorar) {
        this.gruposMenuExplorar = gruposMenuExplorar;
    }
    public String getSiglaModulo() {
        return siglaModulo;
    }

    public void setSiglaModulo(String siglaModulo) {
        this.siglaModulo = siglaModulo;
    }

    public int getPaginaAtualModulos() {
        return paginaAtualModulos;
    }

    public void setPaginaAtualModulos(int paginaAtualModulos) {
        this.paginaAtualModulos = paginaAtualModulos;
    }

    public void onSelectTaskbarAgendaItem(){
        selectTaskbarItem("AGENDA");
    }

    public void selectTaskbarItem(String taskbarSelected){
        setTaskbarSelected(taskbarSelected);
        setUrlGoBackRedirect(null);
        String idRecurso = null;

        if(getTaskbarSelected() != null){
            if(getTaskbarSelected().equals("linkFavorito")){
                idRecurso = "favorites";
            }

            if(getTaskbarSelected().equals("linkaddClienteMenuSuperiorZWUI")){
                idRecurso = "add-person";
            }

            if(getTaskbarSelected().equals("linkClientesMenuSuperiorZWUI")){
                idRecurso = "pessoas";
            }

            if(getTaskbarSelected().equals("linkMenuNovoBIMenuSuperiorZWUI")){
                idRecurso = "bi";
            }

            if(getTaskbarSelected().equals("AGENDA")){
                idRecurso = "agn";
            }

            if(getTaskbarSelected().equals("CONFIG")){
                idRecurso = "config";
            }

            if(idRecurso != null){
                NotificacaoMsService.notificarAcessoMenuRecurso(idRecurso, IdLocalicazaoMenuEnum.LATERAL_SUBMODULO);
            }else {
                Uteis.logarDebug("Não foi possível encontrar o id do recurso para notificar o acesso. Item " +getTaskbarSelected());
            }
        }

        Uteis.logar("teste");
    }

    public void onSelectTaskbarItem(ActionEvent evt) {
        try {
            String taskbarSelected = (String) evt.getComponent().getAttributes().get("taskbarSelected");
            selectTaskbarItem(taskbarSelected);
        } catch (Exception erro) {
            Uteis.logarDebug("Não foi possivel selecionar o item da taskbar. Item "+taskbarSelected);
        }
    }

    public String getTaskbarSelected() {
        return taskbarSelected;
    }

    public void setTaskbarSelected(String taskbarSelected) {
        this.taskbarSelected = taskbarSelected;
    }

    public List<Modulo> getModulosConfiguracoes() {return modulosConfiguracoes;}

    public void setModulosConfiguracoes(List<Modulo> modulosConfiguracoes) {this.modulosConfiguracoes = modulosConfiguracoes;}

    public boolean isExibirConfiguracao() {return exibirConfiguracao;}
    public void setExibirConfiguracao(boolean exibirConfiguracao) {this.exibirConfiguracao = exibirConfiguracao;}

    public String getScriptAbrirPopUp() {return scriptAbrirPopUp;}

    public void setScriptAbrirPopUp(String scriptAbrirPopUp) {this.scriptAbrirPopUp = scriptAbrirPopUp;}


    public boolean isExibirFavoritos() {
        return exibirFavoritos;
    }

    public void setExibirFavoritos(boolean exibirFavoritos) {
        this.exibirFavoritos = exibirFavoritos;
    }

    public String getOncompleteModulo() {
        return oncompleteModulo;
    }

    public void setOncompleteModulo(String oncompleteModulo) {
        this.oncompleteModulo = oncompleteModulo;
    }

    public JSONObject getModulosJson() {
        return modulosJson;
    }

    public void setModulosJson(JSONObject modulosJson) {
        this.modulosJson = modulosJson;
    }

    public String getLabelBtnVoltar() {
        return labelBtnVoltar;
    }

    public void setLabelBtnVoltar(String labelBtnVoltar) {
        this.labelBtnVoltar = labelBtnVoltar;
    }

    public List<Modulo> getHistoricoNavegacaoModulos() {
        if(historicoNavegacaoModulos == null){
            historicoNavegacaoModulos = new ArrayList<>();
        }
        return historicoNavegacaoModulos;
    }

    public Modulo getUltimoModuloVisitado(){
        Modulo ultimoModuloVisitado = null;
        for (int i = historicoNavegacaoModulos.size() -1; i >= 0; i--){
            Modulo modulo = historicoNavegacaoModulos.get(i);
            if(!NO_BACK_MODULES.stream().anyMatch(nbm -> nbm.equals(modulo))){
                ultimoModuloVisitado = modulo;
                break;
            }
        }

        if(ultimoModuloVisitado == null){
            return Modulo.ZILLYON_WEB;
        }

        return ultimoModuloVisitado;
    }

    public void setHistoricoNavegacaoModulos(List<Modulo> historicoNavegacaoModulos) {
        this.historicoNavegacaoModulos = historicoNavegacaoModulos;
    }


    public List<String> getMenus() {
        return menus;
    }

    public void setMenus(List<String> menus) {
        this.menus = menus;
    }

    public void addUlimasPaginasAcessas(String pagina){
        if(ultimasPaginasAcessadas == null){
            ultimasPaginasAcessadas = new ArrayList<>();
        }
        if(ultimasPaginasAcessadas.size() == 0 || (!ultimasPaginasAcessadas.get(ultimasPaginasAcessadas.size() -1).equals(pagina))){
            ultimasPaginasAcessadas.add(pagina);
            if(ultimasPaginasAcessadas.size() > 5){
                ultimasPaginasAcessadas.remove(0);
            }
        }
    }

    public void setUltimaPaginaAcessada(String ultimaPaginaAcessada) {
        this.ultimaPaginaAcessada = ultimaPaginaAcessada;
    }

    public String getUltimaPaginaAcessada() {
        return ultimaPaginaAcessada;
    }

    public ModuloAberto getUltimoModuloAcessado() {
        return ultimoModuloAcessado;
    }

    public void setUltimoModuloAcessado(ModuloAberto modulo) {
        this.ultimoModuloAcessado = modulo;
    }

    public List<String> getUltimasPaginasAcessadas() {
        return ultimasPaginasAcessadas;
    }

    public void setUltimasPaginasAcessadas(List<String> ultimasPaginasAcessadas) {
        this.ultimasPaginasAcessadas = ultimasPaginasAcessadas;
    }

    public void setUrlGoBackRedirect(String urlGoBackRedirect) {
        this.urlGoBackRedirect = urlGoBackRedirect;
    }

    public String getUrlGoBackRedirect() {
        return urlGoBackRedirect;
    }

    public void abrirMenuExplorar() {
        try {
            System.out.println("teste");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getOnCompleteModuloAtual() {
        return onCompleteModuloAtual;
    }

    public void setOnCompleteModuloAtual(String onCompleteModuloAtual) {
        this.onCompleteModuloAtual = onCompleteModuloAtual;
    }
}
