/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.GrupoFuncionalidadeSistemaEnum;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.OperacoesClienteService;
import controle.arquitetura.OperacoesTelaClienteEnum;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.LogoutControle;
import controle.arquitetura.session.SessionTO;
import controle.arquitetura.session.listener.SessionState;
import controle.basico.AtualizarDadosControle;
import controle.basico.ClienteControle;
import controle.basico.ClientesMarcadosControle;
import controle.basico.ColaboradorControle;
import controle.basico.ConsultaClienteControle;
import controle.basico.FuncionalidadeControle;
import controle.basico.HistoricoPontosControle;
import controle.basico.LancarBrindeClienteControle;
import controle.basico.MovimentoContaCorrenteClienteControle;
import controle.basico.PreCadastroClienteControle;
import controle.basico.QuestionarioClienteCRMControle;
import controle.basico.QuestionarioClienteControle;
import controle.basico.TelaClienteControle;
import controle.contrato.AfastamentoContratoControle;
import controle.contrato.ContratoControle;
import controle.crm.BusinessIntelligenceCRMControle;
import controle.crm.MetaCRMControle;
import controle.estoque.CompraControle;
import controle.financeiro.AulaAvulsaDiariaControle;
import controle.financeiro.BIFinanceiroControle;
import controle.financeiro.GestaoRecebiveisControle;
import controle.financeiro.MovPagamentoControle;
import controle.financeiro.MovParcelaControle;
import controle.financeiro.VendaAvulsaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.basico.webservice.ParametrosLoginDiretoModuloNotasJSON;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.ConfiguracaoProdutoEmpresa;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.beanutils.ConversionException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import relatorio.controle.financeiro.ReceitaPorPeriodoSinteticoRelControleRel;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class OIDServlet extends HttpServlet {

    private static final String TEXT_HTML_CHARSET_UTF_8 = "text/html;charset=UTF-8";
    public static final String URL_ZW_DIFERENTE_TREINO = "urlZwDiferenteTreino";
    public static final String URL_ZW_LOCAL = "urlZwLocal";
    public static final String PARAM_DESLOGAR = "deslogar";

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType(TEXT_HTML_CHARSET_UTF_8);

        PrintWriter out = response.getWriter();
        LoginControle loginControl;
        Connection c = null;
        boolean iniciouCtx = false;
        try {
            String lgn = request.getParameter("lgn");
            String codUsuario;
            String key = "";
            Integer codEmpresa = 0;
            String uri;
            String urlLogin;
            String login = "";
            String dataSistema = "";
            String codColaborador = "";
            String codCliente = "";
            String userOamd = "";
            String tokenNT = "";
            String timevld = "";
            String urlOamd;
            String apresentarTreinoWeb = "";
            String ip = "N/C";
            Integer sessionId = null;
            String tokenOamd = "";
            Date dataLogar = null;
            String tokenNZW = "";

            JSONArray permissoesOAMD = null;

            boolean desv;
            boolean deslogar = false;
            boolean isRequestToNFe = false;
            boolean novoLogin = false;

            ParametrosLoginDiretoModuloNotasJSON parametrosLoginDiretoModuloNotasJSON = null;

            JSONObject json = null;
            if (lgn == null) {
                key = request.getParameter("key");
                codUsuario = request.getParameter("idUserZW");
                codEmpresa = Integer.parseInt(request.getParameter("idEmpresa"));
                login = request.getParameter("login");
                dataSistema = request.getParameter("data");
                uri = request.getParameter("urlRedirect");
                userOamd = "";
            } else {
                lgn = Uteis.desencriptar(lgn, "chave_login_unificado");
                json = new JSONObject(lgn);
                uri = json.getString("urlRedirect");
                codUsuario = json.getString("idUserZW");

                try {
                    String logoutString = request.getParameter("logout");
                    if (logoutString != null) {
                        Boolean logout = Boolean.valueOf(logoutString);
                        if(logout) {
                            key = json.getString("key");

                            LogoutControle logoutControle = getLogoutControleBySession(request);
                            if (logoutControle != null) {
                                logoutControle.doLogout();
                            }
                            request.getSession().invalidate();
                            String zwJSId = Uteis.desencriptar(request.getParameter("zwJSId"), "chave_login_unificado");
                            if (zwJSId != null) {
                                Map<String, SessionTO> sesisonStates = SessionState.getInstance();
                                if (sesisonStates.containsKey(zwJSId)) {
                                    sesisonStates.get(zwJSId).getSession().invalidate();
                                    sesisonStates.remove(zwJSId);
                                }
                            }

                            return;
                        }
                    }
                } catch (Exception e) {
                }

                try {
                    sessionId = json.getInt("sessionId");
                } catch (Exception e) {
                    //ignore
                }
                try {
                    tokenOamd = json.getString("tokenOamd");
                } catch (Exception e) {
                    //ignore
                }
                isRequestToNFe = isRequestToModuloNFe(uri, request);
                if (sessionId != null) {
                    request.getSession().setAttribute("sessionId", sessionId);
                }
                if (tokenOamd != null && !tokenOamd.isEmpty()) {
                    request.getSession().setAttribute("tokenOamd", tokenOamd);
                }

                try {
                    tokenNZW = json.optString("tokenNZW");
                    if (!tokenNZW.isEmpty()) {
                        request.getSession().setAttribute("tokenNZW", tokenNZW);
                    }
                } catch (Exception ignore) {
                }

                try {
                    String menuParam = request.getParameter("menu");
                    if (menuParam != null) {
                        Boolean menu = Boolean.valueOf(menuParam);
                        request.getSession().setAttribute("menu", menu);
                    }
                } catch (Exception e) {
                }

                if (!isRequestToNFe) {
                    key = json.getString("key");
                    urlLogin = json.getString("urlLogin");
                    request.getSession().setAttribute("urlLogin", urlLogin);
                    codEmpresa = json.getInt("idEmpresa");

                    apresentarTreinoWeb = getStringFromJsonIgnoreException(json, "aprensentarTreino");
                    login = getStringFromJsonIgnoreException(json, "login");
                    codColaborador = getStringFromJsonIgnoreException(json, "codColaborador");
                    codCliente = getStringFromJsonIgnoreException(json, "codCliente");
                    dataSistema = getStringFromJsonIgnoreException(json, "dataSistema");
                    timevld = getStringFromJsonIgnoreException(json, "timevld");
                    ip = getStringFromJsonIgnoreException(json, "ip");
                    if (ip == null || ip.isEmpty()) {
                        ip = ExecuteRequestHttpService.obterIPExterno();
                    }
                    deslogar = getBooleanFromJsonIgnoreException(json, PARAM_DESLOGAR);
                    novoLogin = getBooleanFromJsonIgnoreException(json, "novoLogin");
                    try {
                        request.getSession().setAttribute("integNewLogin", novoLogin);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    try {
                        if (UteisValidacao.emptyString(urlLogin)) {
                            urlLogin = obterUrlLogin(key, novoLogin);
                            request.getSession().setAttribute("urlLogin", urlLogin);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    try {
                        userOamd = json.getString("userOamd");
                        if (json.has("permissoesOAMD")) {
                            permissoesOAMD = json.getJSONArray("permissoesOAMD") != null ? json.getJSONArray("permissoesOAMD") : null;
                        } else if (!UteisValidacao.emptyString(userOamd)) {
                            //buscar permissões no OAMD
                            String permissoesString = obterPermissoesUsuarioOAMD(userOamd);
                            if (!UteisValidacao.emptyString(permissoesString)) {
                                permissoesOAMD = new JSONArray(permissoesString);
                            }
                        }
                    } catch (Exception ignored) {
                    }

                    try {
                        urlOamd = json.getString("urlOamd");
                        request.getSession().setAttribute("URL_OAMD_EMPRESAS", urlOamd);
                    } catch (Exception ignored) {
                    }

                    try {
                        tokenNT = json.getString("tokenNT");
                    } catch (Exception ignored) {
                    }

                    try {
                        if (json.getBoolean(URL_ZW_DIFERENTE_TREINO)) {
                            request.getSession().setAttribute(URL_ZW_DIFERENTE_TREINO, true);
                            request.getSession().setAttribute(URL_ZW_LOCAL, json.getString(URL_ZW_LOCAL));
                        }
                    } catch (Exception ignored) {
                        request.getSession().setAttribute(URL_ZW_DIFERENTE_TREINO, false);
                    }
                }

                request.getSession().setAttribute("data", dataSistema);
            }

            try {
                if (!codUsuario.equals(((UsuarioVO)request.getSession().getAttribute("logado")).getCodigo().toString())) {
                    deslogar = true;
                }
            } catch (Exception ignored) {
            }

            if (!UteisValidacao.emptyString(dataSistema)) {
                try {
                    dataLogar = new SimpleDateFormat("dd/MM/yyyy").parse(dataSistema);
                    desv = true;
                } catch (Exception e) {
                    desv = false;
                }
            } else {
                desv = false;
            }

            if (codUsuario != null && !codUsuario.isEmpty()) {
                final int idUsuario = Integer.valueOf(codUsuario);
                if (!JSFUtilities.isJSFContext()) {
                    FacesContext ctx = JSFUtilities.init(request, response);
                    iniciouCtx = true;
                }
                try {
                    if (!UteisValidacao.emptyString(timevld) && Long.parseLong(timevld) < Calendar.getInstance().getTimeInMillis()) {
                        throw new ConsistirException("Validação do usuário expirada, favor retorne ao Login e informe novamente senha e usuário");
                    }

                    if (isRequestToNFe) {
                        loginControl = getLoginControleOrCreate(request, isRequestToNFe);
                        if (loginControl != null && deslogar) {
                            invalidarSessao(request, response);
                        }

                        parametrosLoginDiretoModuloNotasJSON = createParametrosLoginDiretoNFeFromJSON(json, out);
                        redirecionarModuloNotas(request, response, loginControl, parametrosLoginDiretoModuloNotasJSON);
                        return;
                    }

                    String chaveSession = (String) JSFUtilities.getFromSession(JSFUtilities.KEY);
                    if (!UteisValidacao.emptyString(chaveSession) && !UteisValidacao.emptyString(key) && !chaveSession.equals(key))
                        invalidarSessao(request, response);

                    c = Conexao.getFromSession() != null ? Conexao.getFromSession() : Conexao.initSession(key);
                    Usuario usuarioFacade = new Usuario(c);
                    UsuarioVO usuarioVO = usuarioFacade.consultarPorChavePrimaria(idUsuario, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    loginControl = getLoginControleOrCreate(request, isRequestToNFe);
                    if (loginControl != null && deslogar) {
                        invalidarSessao(request, response);
                    }

                    usuarioVO.setUserOamd(userOamd);
                    usuarioVO.setPermissoesOAMD(permissoesOAMD != null ? permissoesOAMD.toString() : null);
                    loginControl.setPermissoesOAMD(usuarioVO.getPermissoesOAMD());
                    loginControl.setUsername(usuarioVO.getUsername());
                    loginControl.setSenha(usuarioVO.getSenha());
                    loginControl.setUserOamd(userOamd);

                    //selecionar a Empresa Logada
                    List<SelectItem> l = loginControl.getListaSelectItemEmpresa();
                    if (codEmpresa != null && codEmpresa.intValue() != 0) {
                        // Comentada linhas abaixo pois o lista de empresa estava vindo vazia, e esta é uma solução temporária para poder realizar o login no sistema
//                        for (SelectItem item : l) {
//                            Integer empresa = (Integer) item.getValue();
//                            if (empresa.intValue() == Integer.valueOf(codEmpresa).intValue()) {
                        loginControl.setEmpresa(new Empresa(c).consultarPorChavePrimaria(codEmpresa,
                                Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
//                                break;
//                            }
//                        }
                    }

                    if (loginControl.getEmpresa().isBloqueioTemporario()) {
                        request.getSession().invalidate();
                        response.sendError(HttpServletResponse.SC_BAD_REQUEST);
                        return;
                    }

                    loginControl.setValidarTreinoLoginApp(UteisValidacao.emptyString(apresentarTreinoWeb) ? false : apresentarTreinoWeb.equals("true"));
                    request.setAttribute("pseudoUser", usuarioVO.getUsername());
                    request.setAttribute("pseudoUserCode", usuarioVO.getCodigo());
                    request.getSession().setAttribute(LoginControle.class.getSimpleName(), loginControl);
                    request.getSession().setAttribute(LoginControle.NomeChaveUsuarioSessao, usuarioVO);
                    request.getSession().setAttribute("ip", ip);
                    if (!UteisValidacao.emptyString(login)) {
                        request.getSession().setAttribute("vindoLogin", true);
                    }
                    if (desv) {
                        request.getSession().setAttribute("desv", "true");
                        JSFUtilities.setManagedBeanValue("Regras.dataSistema", dataLogar);
                        Calendario.setDataOnSession(dataLogar);
                    }

                    boolean ignorarValidacaoBloqueio = (getUrlRedirect(uri, request).equals(PropsService.uriPlano) ||
                            getUrlRedirect(uri, request).equals(PropsService.uriTurma)) ||
                            !loginControl.isApresentarMensagensLogin();

                    String retorno = loginControl.login(ignorarValidacaoBloqueio);
                    loginControl.getUsuario().setTokenNT(tokenNT);

                    if (retorno != null && retorno.equals("erroLogin")
                            && (!UteisValidacao.emptyString(loginControl.getMensagemAtualizacaoBD())
                            || (!UteisValidacao.emptyString(loginControl.getMensagemBloqueio()) && loginControl.getDiasParaBloqueio() >= 0))) {
                        response.sendRedirect(request.getContextPath() + "/faces/login.jsp");
                    } else if (retorno != null && retorno.toLowerCase().contains("login")
                            && loginControl.getMensagemDetalhada().isEmpty()) {
                        prepareControllers(request, response, out, uri, codColaborador, codCliente, c, key);
                    } else {
                        try {
                            if(retorno.equals("erroLogin") && loginControl.getMensagemDetalhada().contains("erro_login_sem_modulo")){
                                Uteis.logar("erro_login_sem_modulo: " + lgn);
                                LogoutControle logoutControle = (LogoutControle) request.getSession().getAttribute(LogoutControle.class.getSimpleName());
                                String logoutControleURL = logoutControle.getURL();
                                String urlLoginDeslogar = (String) JSFUtilities.getFromSession("urlLoginDeslogar");
                                logoutControle.doLogout();
                                response.sendRedirect(urlLoginDeslogar == null ? logoutControleURL : urlLoginDeslogar);
                            } else {
                                out.println("Retorno: " + retorno + " - Mensagem: " + loginControl.getMensagemDetalhada());
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                            out.println("Retorno: " + retorno + " - Mensagem: " + loginControl.getMensagemDetalhada());
                        }
                    }

                } catch (Exception ex) {
                    out.println(ex.getMessage());
                    Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        } catch (JSONException ex) {
            Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            FacesContext context = FacesContext.getCurrentInstance();
            if (iniciouCtx && context != null) {
                context.release();
            }
            out.close();
        }
    }

    private String obterPermissoesUsuarioOAMD(String username) {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/usuario/obterPermissoesOAMD?username=" + URLEncoder.encode(username, "UTF-8");
            String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
            String resposta = null;
            if (response != null && response.trim().startsWith("{")) {
                JSONObject responseJson = new JSONObject(response);
                if (responseJson.has("return")) {
                    resposta = new JSONObject(new JSONObject(response).optString("return")).optJSONArray("permissoesOAMD").toString();
                }
            }
            if (resposta == null) {
                return null;
            }
            return resposta;
        } catch (Exception ex) {
            Uteis.logar(ex, LoginControle.class);
        }
        return null;
    }

    private String obterUrlLogin(String key, boolean novoLogin) throws Exception {
        ClientDiscoveryDataDTO dataDTO = DiscoveryMsService.urlsChave(key);
        String urlLoginNovo = dataDTO.getServiceUrls().getLoginFrontUrl();
        if (novoLogin && !UteisValidacao.emptyString(urlLoginNovo)) {
            return urlLoginNovo;
        }
        //URL login antigo
        return dataDTO.getServiceUrls().getLoginAppUrl();
    }

    private void redirecionarModuloNotas(HttpServletRequest request, HttpServletResponse response,
                                         LoginControle loginControl, ParametrosLoginDiretoModuloNotasJSON parametrosLoginDireto) throws Exception {
        loginControl.prepararModuloNFe(parametrosLoginDireto);
        response.sendRedirect(request.getContextPath() + "/faces/indexNFe.jsp");
    }

    private String getUrlRedirect(String uri, HttpServletRequest request) {
        return request.getParameter("urlRedirect") != null ?
                request.getParameter("urlRedirect") : uri;
    }

    private String getUrlGoBackRedirect(HttpServletRequest request) throws IOException {
        return request.getParameter("urlGoBackRedirect") != null ?
                request.getParameter("urlGoBackRedirect") : null;
    }

    private String getModuloGoBackRedirect(HttpServletRequest request) throws IOException {
        String param = request.getParameter("moduloGoBackRedirect");
        if (param != null && !"null".equalsIgnoreCase(param.trim())) {
            return param.trim();
        }
        return null;
    }

    private String getFuncionalidadeEnumNome(HttpServletRequest request) throws IOException {
        return request.getParameter("funcionalidadeNome") != null ?
                request.getParameter("funcionalidadeNome") : null;
    }

    private String getParamClienteMatricula(HttpServletRequest request) throws IOException {
        return request.getParameter("matriculaCliente") != null ?
                request.getParameter("matriculaCliente") : null;
    }

    private String getParamSolicitacaoCompra(HttpServletRequest request) throws IOException {
        return request.getParameter("solicitacaoCompra") != null ?
                request.getParameter("solicitacaoCompra") : null;
    }

    private String getParamCodigoColaborador(HttpServletRequest request) throws IOException {
        return request.getParameter("codigoColaborador") != null ?
                request.getParameter("codigoColaborador") : null;
    }

    private String getParamCodPessoa(HttpServletRequest request) throws IOException {
        return request.getParameter("codPessoa") != null ?
                request.getParameter("codPessoa") : null;
    }

    private String getParamCodCliente(HttpServletRequest request) throws IOException {
        return request.getParameter("codCliente") != null ?
                request.getParameter("codCliente") : null;
    }

    private String getPaginaJspNome(HttpServletRequest request) throws IOException {
        return request.getParameter("jspPage") != null ?
                request.getParameter("jspPage") : null;
    }

    private GrupoFuncionalidadeSistemaEnum getGrupoFuncionalidadeMenuNovoMenu(String funcionalidadeNome) {
        String grupoNome = "";
        for (GrupoFuncionalidadeSistemaEnum grupo : GrupoFuncionalidadeSistemaEnum.values()) {
            if (grupo.isSomenteMenuNovo() || !grupo.isSomenteMenuAntigo()) {
                if (grupo.getFuncionalidades() != null) {
                    for (int i = 0; i < grupo.getFuncionalidades().length; i++) {
                        if (grupo.getFuncionalidades()[i].getName().equals(funcionalidadeNome)) {
                            return grupo;
                        }
                    }
                } else if (grupo.getFuncionalidade() != null && grupo.getFuncionalidade().getName().equals(funcionalidadeNome)) {
                    return grupo;
                }
            }
        }
        return null;
    }

    private void prepareControllers(HttpServletRequest request, HttpServletResponse response, PrintWriter out,
                                    String uri, String codColaborador, String codCliente, Connection con, String chave) throws Exception {
        String urlRedirect = getUrlRedirect(uri, request);
        String urlGoBackRedirect = getUrlGoBackRedirect(request);
        String moduloGoBackRedirect = getModuloGoBackRedirect(request);
        String funcionalidadeNome = getFuncionalidadeEnumNome(request);
        String origem = (request.getParameter("origem") != null ? request.getParameter("origem") : null);
        String jspPage = getPaginaJspNome(request);
        LoginControle loginControl = getLoginControleBySession(request);
        FuncionalidadeControle funcionalidadeControle = getFuncionalidadeControleOrCreate(request);
        MenuControle menuControle = getMenuControleOrCreate(request);

        menuControle.setUrlGoBackRedirect(urlGoBackRedirect);
        try {
            AtualizarDadosControle atualizarDadosControle = getAtualizarDadosControleBySession(request);
            if (atualizarDadosControle != null){
                atualizarDadosControle.liberarBackingBeanMemoria("AtualizarDadosControle");
            }
        } catch (Exception ignored){}

        if (moduloGoBackRedirect != null) {
            if (loginControl != null) {
                loginControl.setIgnorarHistoricoNavegacaoModulo(true);
            }
            try {
                Modulo modulo = Modulo.fromSigla(moduloGoBackRedirect);
                if (modulo != null) {
                    menuControle.addHistoricoNavegacaoModulos(modulo);
                    menuControle.processarLabelBtnVoltar();
                }
            } catch (Exception e) {
                Uteis.logarDebug("Erro ao processar moduloGoBackRedirect porque a sigla do modulo " + moduloGoBackRedirect + " não existe no enum Modulo do ZW");
                e.printStackTrace();
            }

        } else {
            menuControle.setExibirFavoritos(false);
            menuControle.setExibirConfiguracao(false);
        }

        if (loginControl != null) {
            if (urlRedirect.equals(PropsService.uriInicial)) {
                urlRedirect = "uriInicial";
                loginControl.abrirZillyonWeb();
//                BIControle biControle = (BIControle) JSFUtilities.getManagedBean("BIControle");
//                if (biControle == null) {
//                    biControle = new BIControle();
//                }
//                biControle.initDados();
//                biControle.initBI();
//                StartBICallable startBI = new StartBICallable(request, response, biControle);
//                ExecutorService executorService = Executors.newFixedThreadPool(16);
//                executorService.submit(startBI);

            } else if (urlRedirect.equals(PropsService.uriCadastros)) {
                loginControl.abrirZillyonWeb();
            } else if (urlRedirect.equals(PropsService.uriBI)) {
                loginControl.abrirZillyonWeb();
                if (loginControl.getPermissaoAcessoMenuVO().getVisualizarBI()) {
                    loginControl.inicializarMetodosTelaInicial();
                } else {
                    out.println("Você precisa da permissão \"2.43 - Visualizar Business Intelligence\"");
                }
            } else if (urlRedirect.equals(PropsService.uriAgenda)) {//ok
                loginControl.abrirModuloEstudio();
            } else if (urlRedirect.equals(PropsService.uriFinan)) {
                loginControl.abrirModuloFinanceiro();
            } else if (urlRedirect.equals(PropsService.uriCRM)) {
                loginControl.abrirModuloCRM();
            } else if (urlRedirect.equals(PropsService.uriNota)) {
                loginControl.abrirModuloNotaFiscal();
            } else if (urlRedirect.equals(PropsService.uriCarteiras)) {
                loginControl.abrirModuloCRM();
                loginControl.abrirCarteirasCRM();
            } else if (urlRedirect.equals(PropsService.uriBIMobile)) {
                loginControl.abrirZillyonWeb();
                loginControl.inicializarMetodosTelaInicial();
                BIControle biControle = (BIControle) JSFUtilities.getManagedBean("BIControle");
                if (biControle == null) {
                    biControle = new BIControle();
                }
                biControle.initDados();
                urlRedirect = "uriBIMobile";
            } else if (urlRedirect.equals(PropsService.uriBICRM)) {
                loginControl.abrirModuloCRM();
                BusinessIntelligenceCRMControle businessIntelligenceCRMControle = (BusinessIntelligenceCRMControle) JSFUtilities.getManagedBean("BusinessIntelligenceCRMControle");
                if (businessIntelligenceCRMControle == null) {
                    businessIntelligenceCRMControle = new BusinessIntelligenceCRMControle();
                }
                businessIntelligenceCRMControle.carregarBusinessIntelligence();
            } else if (urlRedirect.equals(PropsService.uriBIFinan)) {
                loginControl.abrirModuloFinanceiro();
                BIFinanceiroControle biFinanceiroControle = (BIFinanceiroControle) JSFUtilities.getManagedBean("BIFinanceiroControle");
                if (biFinanceiroControle == null) {
                    biFinanceiroControle = new BIFinanceiroControle();
                }
                biFinanceiroControle.inicializar();
            } else if (urlRedirect.equals(PropsService.uriCanal)) {
                loginControl.abrirModuloCanalCliente();
            } else if (urlRedirect.equals(PropsService.uriStore)) {
                loginControl.abrirModuloCanalClientePactoStore();
            } else if (urlRedirect.equals(PropsService.uriColaborador)) {
                loginControl.abrirZillyonWeb();
            } else if (urlRedirect.equals(PropsService.uriUsuario)) {
                loginControl.abrirZillyonWeb();
            } else if (urlRedirect.equals(PropsService.uriPerfil)) {
                loginControl.abrirZillyonWeb();
            } else if (urlRedirect.equals(PropsService.uriPessoa)) {
                loginControl.abrirZillyonWeb();
            }
        }

        VendaAvulsaControle vendaAvulsaControle = null;
        if (urlRedirect.equals(PropsService.uriClientes)) {//ok
            try {
                if (loginControl != null) {
                    loginControl.abrirZillyonWeb();
                }
                Uteis.logar(null, "Vou instanciar ConsultaClienteControle...");
                ConsultaClienteControle control = (ConsultaClienteControle) request.getSession().getAttribute(ConsultaClienteControle.class.getSimpleName());
                if (control == null) {
                    control = new ConsultaClienteControle();
                }
                Uteis.logar(null, "Vou preparar os params...");
                Map<String, Object> p = new HashMap<String, Object>();
                p.put("paginaInicial", "paginaInicial");
                p.put("letraConsultPag", "A");
                p.put("tipoConsulta", "letra");
                Uteis.logar(null, "Vou consultar os clientes...");
                ActionEvent evtP = JSFUtilities.createEventParameter(p);
                control.consultarPaginadoListener(evtP);
                Uteis.logar(null, "Vou executar a acao.");
                control.acao();
                if (loginControl.isApresentarMenuZWUI()) {
                    loginControl.setModuloAberto(ModuloAberto.PESSOAS);
                }
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao abrir na tela de clientes");
                if (loginControl != null) {
                    loginControl.abrirZillyonWeb();
                    urlRedirect = PropsService.uriInicial;
                }
            }
        } else if (urlRedirect.startsWith("pagamentoDiaria")) {
            JSFUtilities.removeFromSession(MovParcelaControle.class);
            JSFUtilities.removeFromSession(ContratoControle.class);
            JSFUtilities.removeFromSession(VendaAvulsaControle.class);
            loginControl.abrirZillyonWeb();
            String codigoVenda = urlRedirect.split("_")[1];
            AulaAvulsaDiariaControle diariaControle = new AulaAvulsaDiariaControle();
            AulaAvulsaDiaria diariaDao = new AulaAvulsaDiaria(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            AulaAvulsaDiariaVO diariaVO = diariaDao.consultarPorChavePrimaria(Integer.parseInt(codigoVenda), Uteis.NIVELMONTARDADOS_TODOS);
            diariaControle.setAulaAvulsaDiariaVO(diariaVO);
            JSFUtilities.storeOnSession(AulaAvulsaDiariaControle.class.getSimpleName(), diariaControle);
            MovPagamentoControle mpg = new MovPagamentoControle();
            mpg.setEmpresa(diariaControle.getAulaAvulsaDiariaVO().getEmpresa());
            JSFUtilities.storeOnSession(MovPagamentoControle.class.getSimpleName(), mpg);
        } else if (urlRedirect.startsWith("pagamentoVenda")) {
            JSFUtilities.removeFromSession(MovParcelaControle.class);
            JSFUtilities.removeFromSession(ContratoControle.class);
            JSFUtilities.removeFromSession(AulaAvulsaDiariaControle.class);
            loginControl.abrirZillyonWeb();
            String codigoVenda = urlRedirect.split("_")[1];
            vendaAvulsaControle = new VendaAvulsaControle();
            VendaAvulsa vendaAvulsaDao = new VendaAvulsa(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            VendaAvulsaVO vendaAvulsaVO = vendaAvulsaDao.consultarPorChavePrimaria(Integer.parseInt(codigoVenda), Uteis.NIVELMONTARDADOS_TODOS);
            prepararVendaAvulsaConfiguracaoProdutoEmpresa(vendaAvulsaVO, (Conexao.getFromSession() != null ? Conexao.getFromSession() : con));
            vendaAvulsaControle.setVendaAvulsaVO(vendaAvulsaVO);
            JSFUtilities.storeOnSession(VendaAvulsaControle.class.getSimpleName(), vendaAvulsaControle);
            MovPagamentoControle mpg = new MovPagamentoControle();
            mpg.setEmpresa(vendaAvulsaControle.getVendaAvulsaVO().getEmpresa());
            JSFUtilities.storeOnSession(MovPagamentoControle.class.getSimpleName(), mpg);
        } else if (urlRedirect.startsWith("pagamentoContrato")) {
            JSFUtilities.removeFromSession(MovParcelaControle.class);
            JSFUtilities.removeFromSession(MovPagamentoControle.class);
            JSFUtilities.removeFromSession(VendaAvulsaControle.class);
            JSFUtilities.removeFromSession(ContratoControle.class);
            loginControl.abrirZillyonWeb();
            String codigoContrato = urlRedirect.split("_")[1];
            ContratoControle contratoControle = new ContratoControle();
            Contrato contratoDao = new Contrato(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            contratoControle.setContratoVO(contratoDao.consultarPorChavePrimaria(Integer.parseInt(codigoContrato), Uteis.NIVELMONTARDADOS_TODOS));
            JSFUtilities.storeOnSession(ContratoControle.class.getSimpleName(), contratoControle);
            MovPagamentoControle mpg = new MovPagamentoControle();
            mpg.setEmpresa(contratoControle.getContratoVO().getEmpresa());
            JSFUtilities.storeOnSession(MovPagamentoControle.class.getSimpleName(), mpg);
        } else if (urlRedirect.startsWith("caixaEmAberto")) {
            loginControl.abrirZillyonWeb();
            String nome = urlRedirect.split("_")[1];
            MovParcelaControle mpc = (MovParcelaControle) request.getSession().getAttribute(MovParcelaControle.class.getSimpleName());
            boolean obterFiltroClienteAPartirContratoControle = true;
            if (mpc == null) {
                mpc = new MovParcelaControle("");
            }
            obterFiltroClienteAPartirContratoControle = false;
            mpc.setValorConsulta(nome);
            mpc.setIncluirParcelasRecorrencia(Boolean.TRUE);
            mpc.novo(false, false, obterFiltroClienteAPartirContratoControle);
            JSFUtilities.storeOnSession(MovParcelaControle.class.getSimpleName(), mpc);

        } else if (urlRedirect.equals(PropsService.uriVendaAvulsa)) {
            loginControl.abrirZillyonWeb();
            if (loginControl != null) {
                loginControl.abrirZillyonWeb();
            }

            VendaAvulsaControle control = (VendaAvulsaControle) request.getSession().getAttribute(VendaAvulsaControle.class.getSimpleName());
            if (control == null) {
                control = new VendaAvulsaControle();
            }
            control.novoSemComprador();
            control.preencherCompraCredito(UteisValidacao.emptyString(codColaborador) ? null : Integer.valueOf(codColaborador));
            JSFUtilities.storeOnSession(VendaAvulsaControle.class.getSimpleName(), control);
        } else if (urlRedirect.equals(PropsService.uriHistoricoBV) && codCliente != null && !codCliente.isEmpty()) {
            if (loginControl != null) {
                loginControl.abrirZillyonWeb();
            }

            ClienteControle control = new ClienteControle();
            ClienteVO cli = new ClienteVO();
            cli.setCodigo(Integer.valueOf(codCliente));
            control.prepararTelaCliente(cli, true);
            request.getSession().setAttribute(ClienteControle.class.getSimpleName(), control);
            QuestionarioClienteCRMControle controlBV = new QuestionarioClienteCRMControle(control.getClienteVO());
            request.getSession().setAttribute(QuestionarioClienteCRMControle.class.getSimpleName(), controlBV);
        } else if (urlRedirect.equals(PropsService.uriPlano)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirFuncionalidade("plano");
        } else if (urlRedirect.equals(PropsService.uriTurma)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirFuncionalidade("turma");
        } else if (urlRedirect.equals(PropsService.uriConfiguracao)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirZillyonWeb();
        } else if (urlRedirect.equals(PropsService.uriPrecadastro)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }

            if (loginControl.isApresentarMenuZWUI()) {
                loginControl.setModuloAberto(ModuloAberto.PESSOAS);
                menuControle.setReprocessar(true);
                PreCadastroClienteControle preCadastroClienteControle = (PreCadastroClienteControle) request.getSession().getAttribute(PreCadastroClienteControle.class.getSimpleName());
                if (preCadastroClienteControle == null) {
                    preCadastroClienteControle = new PreCadastroClienteControle();
                    JSFUtilities.storeOnSession(PreCadastroClienteControle.class.getSimpleName(), preCadastroClienteControle);
                }
                preCadastroClienteControle.novo();
            } else {
                loginControl.abrirZillyonWeb();
            }
        } else if (urlRedirect.equals(PropsService.uriColaborador)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirZillyonWeb();
        } else if (urlRedirect.equals(PropsService.uriUsuario)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirZillyonWeb();
        } else if (urlRedirect.equals(PropsService.uriPerfil)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirZillyonWeb();
        } else if (urlRedirect.equals(PropsService.uriPessoa)) {
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirZillyonWeb();
        } else if (urlRedirect.equals(PropsService.uriCliente)) {
            ClientesMarcadosControle clientesMarcadosControle = new ClientesMarcadosControle();
            clientesMarcadosControle.abrirClienteMarcandoComoRecente(getParamClienteMatricula(request));
        } else if (urlRedirect.equals(PropsService.uriCompra)) {
            CompraControle compraControle = new CompraControle();
            compraControle.abrirTelaCompra();
        } else if (isContratoOperacao(request)) {
            urlRedirect = loadClienteContrato(request, chave);
        } else if (isOperacaoFinanceiro(request)) {
            urlRedirect = loadCliente(request, chave);
        }

        String urlFuncionalidade;

        if (funcionalidadeNome != null) {
            funcionalidadeControle = getFuncionalidadeControleOrCreate(request);
            if (loginControl == null) {
                loginControl = getLoginControleBySession(request);
            }
            loginControl.abrirZillyonWeb();
            GrupoFuncionalidadeSistemaEnum grupoFuncionalidadeSistemaEnum = getGrupoFuncionalidadeMenuNovoMenu(funcionalidadeNome);
            if (grupoFuncionalidadeSistemaEnum != null) {
                JSFUtilities.getControlador(MenuControle.class).prepararMenuExibir(grupoFuncionalidadeSistemaEnum.getMenu().getName());
                JSFUtilities.getControlador(MenuControle.class).setSubMenu(grupoFuncionalidadeSistemaEnum.getName());
                JSFUtilities.getControlador(FuncionalidadeControle.class).setFuncionalidadeNome(funcionalidadeNome);
                JSFUtilities.getControlador(FuncionalidadeControle.class).setFuncionalidadeAberta(funcionalidadeNome);
            } else {
                JSFUtilities.getControlador(FuncionalidadeControle.class).setFuncionalidadeNome(funcionalidadeNome);
                JSFUtilities.getControlador(FuncionalidadeControle.class).setFuncionalidadeAberta(funcionalidadeNome);
            }
            try {
                String urlBase = request.getRequestURL().toString().replace(request.getContextPath(), "").replace("/oid", "");
                JSFUtilities.storeOnSession("urlBrowser", urlBase);
            } catch (Exception ex) {}
            funcionalidadeControle.prepararFuncionalidade(funcionalidadeNome, null);
            String navegacao = funcionalidadeControle.abrirComCasoNavegacao();
            urlFuncionalidade = funcionalidadeControle.getAbrirPopUp();
            if (funcionalidadeNome.equals(FuncionalidadeSistemaEnum.DIARIA.getName())) {
                prepararTelaDiaria(request, con);
            } else if (funcionalidadeNome.equals(FuncionalidadeSistemaEnum.CAIXA_EM_ABERTO.getName())) {
                prepararTelaCaixaEmAberto(request, con);
            } else if (funcionalidadeNome.equals(FuncionalidadeSistemaEnum.RECEBIVEIS.getName()) &&
                    !UteisValidacao.emptyString(getParamClienteMatricula(request))) {
                prepararGestaoRecebiveis(request, con);
            } else if (funcionalidadeNome.equals(FuncionalidadeSistemaEnum.CONSULTA_DE_TURMAS.getName())) {
                prepararTelaConsultaTurmas(request, con);
            } else if (funcionalidadeNome.equals(FuncionalidadeSistemaEnum.COLABORADOR.getName())) {
                String codigoColaborador = getParamCodigoColaborador(request);
                if (!UteisValidacao.emptyString(codigoColaborador)) {
                    prepararColaborador(request, codigoColaborador);
                    urlFuncionalidade = urlFuncionalidade.replace("colaboradorCons.jsp", "colaboradorForm.jsp");
                }
            } else if (funcionalidadeNome.equals(FuncionalidadeSistemaEnum.CONTATO_AVULSO.getName())) {
                String matricula = getParamClienteMatricula(request);
                String cliente = getParamCodCliente(request);
                if (!UteisValidacao.emptyString(cliente)) {
                    prepararContatoAvulso(request, matricula, cliente);
                }
            }

            FuncionalidadeSistemaEnum funcionalidade = FuncionalidadeSistemaEnum.valueOf(funcionalidadeNome);
            processarFuncionalidade(funcionalidade, request, con);


            if (urlFuncionalidade.contains("abrirPopup")) {
                int index = 0;
                if (urlFuncionalidade.contains("abrirPopup(")) {
                    urlFuncionalidade = urlFuncionalidade.replaceAll("abrirPopup\\('", "");
                    index = funcionalidadeControle.getAbrirPopUp().replaceAll("abrirPopup\\('", "").indexOf(",");
                } else if (urlFuncionalidade.contains("abrirPopupMaximizada(")) {
                    urlFuncionalidade = urlFuncionalidade.replaceAll("abrirPopupMaximizada\\('", "");
                    index = funcionalidadeControle.getAbrirPopUp().replaceAll("abrirPopupMaximizada\\('", "").indexOf(",");
                } else if (urlFuncionalidade.contains("abrirPopupPDFImpressao(")) {
                    urlFuncionalidade = urlFuncionalidade.replaceAll("abrirPopupPDFImpressao\\('", "");
                    index = funcionalidadeControle.getAbrirPopUp().replaceAll("abrirPopupPDFImpressao\\('", "").indexOf(",");
                } else if (urlFuncionalidade.contains("abrirPopupTopoPagina(")) {
                    urlFuncionalidade = urlFuncionalidade.replaceAll("abrirPopupTopoPagina\\('", "");
                    index = funcionalidadeControle.getAbrirPopUp().replaceAll("abrirPopupTopoPagina\\('", "").indexOf(",");
                }
                urlRedirect = urlFuncionalidade.substring(0, index - 1);
                String joinQueryParam = urlRedirect.contains("?") ? "&" : "?";
                urlRedirect += joinQueryParam + "from=popup";
            } else if (jspPage != null) {
                if (funcionalidadeNome.equals(FuncionalidadeSistemaEnum.RECEBIVEIS.getName()) &&
                        !UteisValidacao.emptyString(getParamClienteMatricula(request))) {
                    urlRedirect = request.getContextPath() + "/faces/pages/finan/" + jspPage;
                } else {
                    urlRedirect = request.getContextPath() + "/faces/" + jspPage;
                }
            }
           if (urlFuncionalidade.contains("showModalPanel")) {
                switch (funcionalidadeControle.getFuncSelecionada().getFuncionalidadeSistemaEnumAux().getModulo()) {
                    case CRMWEB:
                        urlRedirect = PropsService.uriCRM;
                        break;
                    case ZILLYONWEB:
                        urlRedirect = PropsService.uriInicial;
                        break;
                    case FINAN:
                        urlRedirect = PropsService.uriFinan;
                        break;
                    default:
                        urlRedirect = PropsService.uriInicial;
                        break;
                }
                urlRedirect = getUrlWithContextUsingProps(request, urlRedirect);
            }
            if (!urlRedirect.contains("faces")) {
                urlRedirect = request.getContextPath() + "/faces/" + urlRedirect;
            }
            if ((navegacao.equals("semValor")) && !urlFuncionalidade.contains("abrirPopup")) {
                // nesse caso a navegacao ja ocorreu no funcionalidadeControle.abrirComCasoNavegacao()
                return;
            }
        } else if (urlRedirect.startsWith("pagamentoVenda") || urlRedirect.startsWith("pagamentoContrato")  || urlRedirect.startsWith("pagamentoDiaria")) {
            urlRedirect = (request.getContextPath() + "/faces/pagamento.jsp");
        } else if (urlRedirect.startsWith("caixaEmAberto")) {
            urlRedirect = (request.getContextPath() + "/faces/tela8.jsp");
        } else {
            if (urlRedirect.equals(PropsService.uriCliente)) {
                urlRedirect = getClienteRedirect(request, urlRedirect);
            } else if (urlRedirect.equals(PropsService.uriCompra)){
                urlRedirect = getCompraRedirect(request, urlRedirect);
            } else if (jspPage != null) {
                menuControle.setExibirFavoritos(false);
                menuControle.setExibirConfiguracao(false);
                urlRedirect = request.getContextPath() + "/faces/" + jspPage;
            } else {


                boolean isRedirectToExternalUse = false;
                try {

                    if (request.getSession() != null) {
                        String urlRedirectToMobile = request.getSession().getAttribute("urlRedirectToMobile").toString();
                        if (!UteisValidacao.emptyString(urlRedirectToMobile)) {
                            urlRedirect = urlRedirectToMobile;
                            isRedirectToExternalUse = true;
                        }
                    }

                } catch (Exception ex) {
                    //Do Nothing
                } finally {
                    request.getSession().removeAttribute("urlRedirectToMobile");
                }

                if (!isRedirectToExternalUse) {
                    urlRedirect = getUrlWithContextUsingProps(request, urlRedirect);
                }
            }
        }

        if (loginControl != null) {
            loginControl.setIgnorarHistoricoNavegacaoModulo(false);
        }

//        response.setHeader("Cache-Control", "private");
//        response.setDateHeader("Expires", System.currentTimeMillis() + 86400000L); // um dia
//        response.setDateHeader("Age", System.currentTimeMillis() + 86400000L);
        if (origem != null && origem.equalsIgnoreCase("angular")) {
            urlRedirect = (urlRedirect + (urlRedirect.contains("?") ? "&" : "?") + "angular=t");
        }
        response.sendRedirect(urlRedirect);
    }

    private void prepararVendaAvulsaConfiguracaoProdutoEmpresa(VendaAvulsaVO obj, Connection con) throws Exception {
        ConfiguracaoProdutoEmpresa configuracaoProdutoEmpresaDAO;
        try {
            configuracaoProdutoEmpresaDAO = new ConfiguracaoProdutoEmpresa(con);
            for (ItemVendaAvulsaVO item : obj.getItemVendaAvulsaVOs()) {
                ConfiguracaoProdutoEmpresaVO cfgEmpresa = configuracaoProdutoEmpresaDAO.consultarPorProdutoEmpresa(item.getProduto().getCodigo(), obj.getEmpresa().getCodigo());
                if (cfgEmpresa != null) {
                    item.getProduto().setValorFinal(cfgEmpresa.getValor());
                    item.getProduto().setValorBaseCalculo(cfgEmpresa.getValor());
                }
            }
        } finally {
            configuracaoProdutoEmpresaDAO = null;
        }
    }

    public void processarFuncionalidade(FuncionalidadeSistemaEnum funcionalidadeNome, HttpServletRequest request, Connection con) throws Exception {
        Map<FuncionalidadeSistemaEnum, Runnable> funcionalidadeActions = new HashMap<>();
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.VENDA_AVULSA, () -> tratarExcecao(() -> prepararTelaVendaAvulsa(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.DIARIA, () -> tratarExcecao(() -> prepararTelaDiariaCliente(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.MOVIMENTO_CC_CLIENTE, () -> tratarExcecao(() -> prepararTelaMovimentoFinanceiro(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.RELATORIO_PONTUACAO, () -> tratarExcecao(() -> prepararTelaRelatorioPontuacao(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.TRANSFERENCIA_SALDO_CONTA_CORRENTE, () -> tratarExcecao(() -> prepararTelaTransferenciaDeSaldo(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.RECEBER_DEBITO_CONTA_CORRENTE, () -> tratarExcecao(() -> prepararTelaDeDebitoContaCorrente(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.LANCAMENTO_BRINDE, () -> tratarExcecao(() -> prepararTelaLancarBrinde(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.BOLETIM_VISITA_CLIENTE, () -> tratarExcecao(() -> prepararTelaBoletimVisita(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.AJUSTE_SALDO_CONTA_CORRENTE, () -> tratarExcecao(() -> prepararTelaAjusteSaldoContaCorrente(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.AJUSTE_PONTUACAO_CONTA_CORRENTE, () -> tratarExcecao(() -> prepararTelaAjusteDePontuacao(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.ATUALIZAR_BV, () -> tratarExcecao(() -> prepararTelaAtualizarBV(request, con)));
        funcionalidadeActions.put(FuncionalidadeSistemaEnum.RECEITA_PERIODO, () -> tratarExcecao(() -> prepararTelaReceitaPorPeriodoSinteticoRelControleRel(request, con)));




        Runnable selectedAction = funcionalidadeActions.get(funcionalidadeNome);
        if (selectedAction != null) {
            selectedAction.run();
        }
    }

    private void tratarExcecao(ExcecaoFuncionalidadeRunnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @FunctionalInterface
    interface ExcecaoFuncionalidadeRunnable {
        void run() throws Exception;
    }

private void prepararTelaVendaAvulsa(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararVendaAvulsa = Boolean.parseBoolean(request.getParameter("prepararVendaAvulsa"));
        if (prepararVendaAvulsa) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            VendaAvulsaControle vendaAvulsaControle = getVendaAvulsaControleOrCreate(request);

            vendaAvulsaControle.novo();
            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            vendaAvulsaControle.getVendaAvulsaVO().setCliente(clienteVO);
            vendaAvulsaControle.carregaDados(clienteVO);

            request.getSession().setAttribute(VendaAvulsaControle.class.getSimpleName(), vendaAvulsaControle);
        }
    }

    private void prepararTelaRelatorioPontuacao(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararTelaPontuacao = Boolean.parseBoolean(request.getParameter("prepararTelaPontuacao"));
        if (prepararTelaPontuacao) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            HistoricoPontosControle historicoPontosControle = getHistoricoPontosControleOrCreate(request);

            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            historicoPontosControle.montarListaHistoricoExterno(clienteVO);

            request.getSession().setAttribute(HistoricoPontosControle.class.getSimpleName(), historicoPontosControle);
        }
    }

    private void prepararTelaMovimentoFinanceiro(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararMovimentoFinanceiro = Boolean.parseBoolean(request.getParameter("prepararTelaMovimentoFinanceiro"));
        if (prepararMovimentoFinanceiro) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            MovimentoContaCorrenteClienteControle movimentoContaCorrenteClienteControle = getMovimentoContaCorrenteClienteControleOrCreate(request);

            movimentoContaCorrenteClienteControle.novo();
            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            movimentoContaCorrenteClienteControle.carregarDados(clienteVO);

            request.getSession().setAttribute(MovimentoContaCorrenteClienteControle.class.getSimpleName(), movimentoContaCorrenteClienteControle);
        }
    }

    private void prepararTelaAjusteSaldoContaCorrente(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararTelaDeAjusteDeSaldo = Boolean.parseBoolean(request.getParameter("prepararTelaDeAjusteDeSaldo"));
        if (prepararTelaDeAjusteDeSaldo) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            MovimentoContaCorrenteClienteControle movimentoContaCorrenteClienteControle = getMovimentoContaCorrenteClienteControleOrCreate(request);

            movimentoContaCorrenteClienteControle.novo();
            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);

            ClienteControle clienteControle = getClienteControleOrCreate(request);
            clienteControle.inicializaDados();
            clienteControle.prepararTelaCliente(clienteVO, true);
            request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);

            movimentoContaCorrenteClienteControle.carregarDados(clienteVO);

            request.getSession().setAttribute(MovimentoContaCorrenteClienteControle.class.getSimpleName(), movimentoContaCorrenteClienteControle);
        }
    }

    private void prepararTelaBoletimVisita(HttpServletRequest request, Connection con) throws Exception {
        ClienteMensagem clienteMensagemDAO;
        Cliente clienteDAO;
        QuestionarioCliente questionarioClienteDAO;
        try {
            boolean prepararTelaBoletimVisita = Boolean.parseBoolean(request.getParameter("prepararTelaBoletimVisita"));
            if (prepararTelaBoletimVisita) {
                clienteMensagemDAO = new ClienteMensagem(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
                clienteDAO = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
                questionarioClienteDAO = new QuestionarioCliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);

                String matricula = request.getParameter("matriculaCliente");
                ClienteVO clienteVO = clienteDAO.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                ClienteControle clienteControle = getClienteControleOrCreate(request);
                clienteControle.inicializaDados();
                clienteControle.prepararTelaCliente(clienteVO, true);
                request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);

                QuestionarioClienteControle questionarioClienteControle = getQuestionarioClienteControleOrCreate(request);
                questionarioClienteControle.inicializarCliente();

                String codigoAviso = request.getParameter("codigoAviso");
                if (!UteisValidacao.emptyString(codigoAviso)) {
                    ClienteMensagemVO clienteMensagemVO = clienteMensagemDAO.consultarPorChavePrimaria(Integer.parseInt(codigoAviso), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    QuestionarioClienteVO qc = questionarioClienteDAO.consultarPorChavePrimaria(clienteMensagemVO.getQuestionarioCliente().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    qc.setNovoObj(false);
                    qc.registrarObjetoVOAntesDaAlteracao();
                    questionarioClienteControle.setQuestionarioClienteVO(qc);

                    if ((qc.getConsultor() != null)) {
                        qc.setCodigoColaboradorAntesAlteracao(qc.getConsultor().getCodigo());
                        if ((qc.getConsultor().getSituacao().equals("NA"))) {
                            boolean deveAdicionar = true;
                            // Isto impede que outra pessoa mude o consultor do BV
                            // pendente caso o consultor original esteja inativo
                            questionarioClienteControle.getListaSelectConsultor().clear();
                            questionarioClienteControle.setApresentarBotaoAtualizar(false);

                            for (Object o : questionarioClienteControle.getListaSelectConsultor()) {
                                SelectItem object = (SelectItem) o;
                                Integer codigo = Integer.valueOf(object.getValue().toString());

                                if (codigo.intValue() == qc.getConsultor().getCodigo().intValue()) {
                                    deveAdicionar = false;
                                    break;
                                }
                            }
                            if (deveAdicionar) {
                                ((ArrayList) questionarioClienteControle.getListaSelectConsultor()).add(new SelectItem(qc.getConsultor().getCodigo(), qc.getConsultor().getPessoa().getNome()
                                        + " (inativo)"));
                            }
                        }
                    }
                } else {
                    questionarioClienteControle.abrirTelaBV();
                }


                request.getSession().setAttribute(QuestionarioClienteControle.class.getSimpleName(), questionarioClienteControle);
            }
        } finally {
            clienteMensagemDAO = null;
            clienteDAO = null;
            questionarioClienteDAO = null;
        }
    }

    private void prepararTelaTransferenciaDeSaldo(HttpServletRequest request, Connection con) throws Exception {
        boolean preparartransferenciaDeSaldo = Boolean.parseBoolean(request.getParameter("prepararTelaDeTransferenciaDeSaldo"));
        if (preparartransferenciaDeSaldo) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            MovimentoContaCorrenteClienteControle movimentoContaCorrenteClienteControle = getMovimentoContaCorrenteClienteControleOrCreate(request);

            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            movimentoContaCorrenteClienteControle.novaTransferencia(clienteVO);

            request.getSession().setAttribute(MovimentoContaCorrenteClienteControle.class.getSimpleName(), movimentoContaCorrenteClienteControle);
        }
    }

    private void prepararTelaDeDebitoContaCorrente(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararTelaDeDebitoContaCorrente = Boolean.parseBoolean(request.getParameter("prepararTelaDeDebitoContaCorrente"));
        if (prepararTelaDeDebitoContaCorrente) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            MovimentoContaCorrenteClienteControle movimentoContaCorrenteClienteControle = getMovimentoContaCorrenteClienteControleOrCreate(request);

            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            movimentoContaCorrenteClienteControle.novoReceberParcelaDebito(clienteVO);

            request.getSession().setAttribute(MovimentoContaCorrenteClienteControle.class.getSimpleName(), movimentoContaCorrenteClienteControle);
        }
    }

    private void prepararTelaLancarBrinde(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararTelaLancarBrinde = Boolean.parseBoolean(request.getParameter("prepararTelaLancarBrinde"));
        if (prepararTelaLancarBrinde) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            LancarBrindeClienteControle lancarBrindeClienteControle = getLancarBrindeClienteControleOrCreate(request);

            lancarBrindeClienteControle.lancarNovoBrinde();
            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            lancarBrindeClienteControle.setClienteSelecionado(clienteVO);

            request.getSession().setAttribute(LancarBrindeClienteControle.class.getSimpleName(), lancarBrindeClienteControle);
        }
    }

    private void prepararTelaAtualizarBV(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararTelaAtualizarBV = Boolean.parseBoolean(request.getParameter("prepararTelaAtualizarBV"));
        if (prepararTelaAtualizarBV) {

            String codCliente = request.getParameter("codCliente");

            Cliente clienteDAO = new Cliente(con);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(Integer.valueOf(codCliente), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            clienteDAO = null;

            TelaClienteControle telaClienteControle = getTelaClienteControleOrCreate(request);
            telaClienteControle.setCliente(clienteVO);
            request.getSession().setAttribute(TelaClienteControle.class.getSimpleName(), telaClienteControle);
            FacesContext.getCurrentInstance().getExternalContext().getRequestMap().put(TelaClienteControle.class.getSimpleName(), telaClienteControle);

            ClienteControle clienteControle = getClienteControleOrCreate(request);
            ClienteVO cli = new ClienteVO();
            cli.setCodigo(Integer.valueOf(codCliente));
            clienteControle.inicializaDados();
            clienteControle.prepararTelaCliente(cli, true);
            request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);

            telaClienteControle = getTelaClienteControleOrCreate(request);
            telaClienteControle.pegarClienteControle();
            telaClienteControle.abrirQuestionarioBV();
            request.getSession().setAttribute(TelaClienteControle.class.getSimpleName(), telaClienteControle);
        }
    }

    private void prepararTelaReceitaPorPeriodoSinteticoRelControleRel(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararTelaRelReceita = Boolean.parseBoolean(request.getParameter("prepararTelaReceitaPorPeriodoSinteticoRelControleRel"));
        if (prepararTelaRelReceita) {
            ReceitaPorPeriodoSinteticoRelControleRel  receitaPorPeriodoSinteticoRelControleRel = getRelaReceitaPorPeriodoSinteticoRelControleRelOrCreate(request);

            request.getSession().setAttribute(ReceitaPorPeriodoSinteticoRelControleRel.class.getSimpleName(), receitaPorPeriodoSinteticoRelControleRel);
        }
    }

    private ReceitaPorPeriodoSinteticoRelControleRel getRelaReceitaPorPeriodoSinteticoRelControleRelOrCreate(HttpServletRequest request) throws Exception {
        ReceitaPorPeriodoSinteticoRelControleRel relaReceitaPorPeriodoSinteticoRelControleRel = getRelaReceitaPorPeriodoSinteticoRelControleRelBySession(request);

        if (relaReceitaPorPeriodoSinteticoRelControleRel == null) {
            relaReceitaPorPeriodoSinteticoRelControleRel = (ReceitaPorPeriodoSinteticoRelControleRel) JSFUtilities.getManagedBean("ReceitaPorPeriodoSinteticoRelControleRel");
        }

        return relaReceitaPorPeriodoSinteticoRelControleRel;
    }

    private ReceitaPorPeriodoSinteticoRelControleRel getRelaReceitaPorPeriodoSinteticoRelControleRelBySession(HttpServletRequest request) {
        return (ReceitaPorPeriodoSinteticoRelControleRel) request.getSession().getAttribute(ReceitaPorPeriodoSinteticoRelControleRel.class.getSimpleName());
    }

    private void prepararTelaAjusteDePontuacao(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararTelaAjusteDePontuacao = Boolean.parseBoolean(request.getParameter("prepararTelaAjusteDePontuacao"));
        if (prepararTelaAjusteDePontuacao) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            LancarBrindeClienteControle lancarBrindeClienteControle = getLancarBrindeClienteControleOrCreate(request);

            lancarBrindeClienteControle.lancarNovoBrinde();
            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            lancarBrindeClienteControle.setClienteSelecionadoAjuste(clienteVO);

            request.getSession().setAttribute(LancarBrindeClienteControle.class.getSimpleName(), lancarBrindeClienteControle);
        }
    }


    private void prepararTelaDiariaCliente(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararDiaria = Boolean.parseBoolean(request.getParameter("prepararDiaria"));
        if (prepararDiaria) {

            StringBuilder matriculaCliente = new StringBuilder(request.getParameter("matriculaCliente"));
            AulaAvulsaDiariaControle aulaAvulsaDiariaControle = getAulaAvulsaDiariaControleOrCreate(request);

            aulaAvulsaDiariaControle.novoDiaria();
            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente.toString(), false, Uteis.NIVELMONTARDADOS_ROBO);
            aulaAvulsaDiariaControle.carregaDados(clienteVO);

            request.getSession().setAttribute(AulaAvulsaDiariaControle.class.getSimpleName(), aulaAvulsaDiariaControle);
        }
    }

    private void prepararTelaDiaria(HttpServletRequest request, Connection con) throws Exception {
        AulaAvulsaDiariaControle aulaAvulsaDiariaControle = getAulaAvulsaDiariaControleOrCreate(request);
        aulaAvulsaDiariaControle.novoDiaria();
        request.getSession().setAttribute(AulaAvulsaDiariaControle.class.getSimpleName(), aulaAvulsaDiariaControle);
    }

    private void prepararGestaoRecebiveis(HttpServletRequest request, Connection con) throws Exception {
        String matriculaCliente = request.getParameter("matriculaCliente");
        Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
        ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        GestaoRecebiveisControle gestaoRecebiveisControle = getGestaoRecebiveisControleOrCreate(request);
        gestaoRecebiveisControle.limparFiltros();
        gestaoRecebiveisControle.setMatricula(clienteVO.getMatricula());
        gestaoRecebiveisControle.consultarRecebiveis();
        request.getSession().setAttribute(GestaoRecebiveisControle.class.getSimpleName(), gestaoRecebiveisControle);
    }

    private void prepararTelaCaixaEmAberto(HttpServletRequest request, Connection con) throws Exception {
        boolean prepararCaixaEmAberto = Boolean.parseBoolean(request.getParameter("prepararCaixaEmAberto"));
        if (prepararCaixaEmAberto) {
            String matriculaCliente = request.getParameter("matriculaCliente");
            Cliente cliente = new Cliente(Conexao.getFromSession() != null ? Conexao.getFromSession() : con);
            ClienteVO clienteVO = UteisValidacao.emptyString(matriculaCliente) ? new ClienteVO() : cliente.consultarPorMatricula(matriculaCliente, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            MovParcelaControle movParcelaControle = getMovParcelaControleOrCreate(request, clienteVO.getNome_Apresentar());
            movParcelaControle.setValorConsulta(clienteVO.getNome_Apresentar());
            movParcelaControle.setMatricula(clienteVO.getMatricula());
            movParcelaControle.setIncluirParcelasRecorrencia(Boolean.TRUE);
            movParcelaControle.setDataInicio(null);
            movParcelaControle.setDataTermino(null);

            movParcelaControle.novo(false, false, false);
            request.getSession().setAttribute(MovParcelaControle.class.getSimpleName(), movParcelaControle);
        }
    }

    private void prepararColaborador(HttpServletRequest request, String codigoColaborador) {
        try {
            if (!UteisValidacao.emptyString(codigoColaborador)) {
                ColaboradorControle colaboradorControle = getColaboradorControleOrCreate(request);
                colaboradorControle.prepareEditarColaborador(Integer.parseInt(codigoColaborador));
                request.getSession().setAttribute(ColaboradorControle.class.getSimpleName(), colaboradorControle);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void prepararContatoAvulso(HttpServletRequest request, String matricula, String codCliente) {
        try {
            if (!UteisValidacao.emptyString(codCliente)) {
                ClienteControle clienteControle = getClienteControleOrCreate(request);
                ClienteVO cli = new ClienteVO();
                cli.setCodigo(Integer.valueOf(codCliente));
                clienteControle.inicializaDados();
                clienteControle.prepararTelaCliente(cli, true);
                request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);

                MetaCRMControle metaCRMControle = getMetaCRMControleOrCreate(request);
                metaCRMControle.inicializarContatoAvulso();
                request.getSession().setAttribute(MetaCRMControle.class.getSimpleName(), metaCRMControle);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void prepararTelaConsultaTurmas(HttpServletRequest request, Connection c) throws Exception {
        boolean prepararConsultaTurma = Boolean.parseBoolean(request.getParameter("prepararConsultaTurma"));
        if (prepararConsultaTurma) {
            String codHorarioTurma = request.getParameter("codHorarioTurma");
            HorarioTurma horarioTurmaDao = new HorarioTurma(c);
            HorarioTurmaVO horarioTurmaVO = horarioTurmaDao.consultarPorChavePrimaria(Integer.parseInt(codHorarioTurma), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            String codCliente = request.getParameter("codCliente");

            String codContrato = request.getParameter("codContrato");
            ContratoVO contratoVO = null;
            if (!UteisValidacao.emptyString(codContrato)) {
                Contrato contratoDAO = new Contrato(c);
                contratoVO = contratoDAO.consultarPorChavePrimaria(Integer.valueOf(codContrato), Uteis.NIVELMONTARDADOS_TODOS);
                contratoDAO = null;
            }

            String codAulaDesmarcada = request.getParameter("codAulaDesmarcada");
            AulaDesmarcadaVO aulaDesmarcadaVO = null;
            if (!UteisValidacao.emptyString(codAulaDesmarcada)) {
                Integer codAulaD = 0;
                try {
                    codAulaD = Integer.valueOf(codAulaDesmarcada);
                    if (!UteisValidacao.emptyNumber(codAulaD)) {
                        AulaDesmarcada aulaDesmarcadaDAO = new AulaDesmarcada(c);
                        aulaDesmarcadaVO = aulaDesmarcadaDAO.consultarPorCodigo(codAulaD, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        aulaDesmarcadaDAO = null;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            ClienteControle clienteControle = getClienteControleOrCreate(request);
            ClienteVO cli = new ClienteVO();
            cli.setCodigo(Integer.valueOf(codCliente));
            clienteControle.inicializaDados();
            clienteControle.prepararTelaCliente(cli, true);
            if (contratoVO != null) {
                clienteControle.setContratoVO(contratoVO);
            }
            clienteControle.montarConsultaTurma(horarioTurmaVO,
                    (aulaDesmarcadaVO != null && aulaDesmarcadaVO.getDataOrigem() != null) ? aulaDesmarcadaVO.getDataOrigem() : Calendario.proximoDiaSemana(horarioTurmaVO.getDiaSemanaNumero(), Calendario.hoje()));
            request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);
        }
    }

    private VendaAvulsaControle getVendaAvulsaControleOrCreate(HttpServletRequest request) throws Exception {
        VendaAvulsaControle controle = getVendaAvulsaControleBySession(request);

        if (controle == null) {
            controle = new VendaAvulsaControle();
        }

        return controle;
    }

    private MovimentoContaCorrenteClienteControle getMovimentoContaCorrenteClienteControleOrCreate(HttpServletRequest request) throws Exception {
        MovimentoContaCorrenteClienteControle controle = getMovimentoContaCorrenteClienteControleBySession(request);

        if (controle == null) {
            controle = new MovimentoContaCorrenteClienteControle();
        }

        return controle;
    }

    private QuestionarioClienteControle getQuestionarioClienteControleOrCreate(HttpServletRequest request) throws Exception {
        QuestionarioClienteControle controle = getQuestionarioClienteControleBySession(request);
        if (controle == null) {
            controle = new QuestionarioClienteControle();
        }
        return controle;
    }

    private LancarBrindeClienteControle getLancarBrindeClienteControleOrCreate(HttpServletRequest request) throws Exception {
        LancarBrindeClienteControle controle = getLancarBrindeClienteControleBySession(request);

        if (controle == null) {
            controle = new LancarBrindeClienteControle();
        }

        return controle;
    }

    private HistoricoPontosControle getHistoricoPontosControleOrCreate(HttpServletRequest request) throws Exception {
        HistoricoPontosControle controle = getHistoricoPontosControleControleBySession(request);

        if (controle == null) {
            controle = new HistoricoPontosControle();
        }

        return controle;
    }

    private AulaAvulsaDiariaControle getAulaAvulsaDiariaControleOrCreate(HttpServletRequest request) throws Exception {
        AulaAvulsaDiariaControle controle = getAulaAvulsaDiariaControleBySession(request);

        if (controle == null) {
            controle = new AulaAvulsaDiariaControle();
        }

        return controle;
    }

    private MovParcelaControle getMovParcelaControleOrCreate(HttpServletRequest request, String nomePessoa) throws Exception {
        MovParcelaControle controle = getMovParcelaControleBySession(request);

        if (controle == null) {
            controle = new MovParcelaControle(nomePessoa);
        }

        return controle;
    }

    private GestaoRecebiveisControle getGestaoRecebiveisControleOrCreate(HttpServletRequest request) throws Exception {
        GestaoRecebiveisControle controle = getGestaoRecebiveisControleBySession(request);
        if (controle == null) {
            controle = new GestaoRecebiveisControle();
        }
        return controle;
    }

    private GestaoRecebiveisControle getGestaoRecebiveisControleBySession(HttpServletRequest request) {
        return (GestaoRecebiveisControle) request.getSession().getAttribute(GestaoRecebiveisControle.class.getSimpleName());
    }

    private ColaboradorControle getColaboradorControleOrCreate(HttpServletRequest request) throws Exception {
        ColaboradorControle controle = getColaboradorControleBySession(request);
        if (controle == null) {
            controle = new ColaboradorControle();
        }
        return controle;
    }

    private MetaCRMControle getMetaCRMControleOrCreate(HttpServletRequest request) throws Exception {
        MetaCRMControle controle = getMetaCRMControleBySession(request);
        if (controle == null) {
            controle = new MetaCRMControle();
        }
        return controle;
    }

    private MetaCRMControle getMetaCRMControleBySession(HttpServletRequest request) {
        return (MetaCRMControle) request.getSession().getAttribute(MetaCRMControle.class.getSimpleName());
    }

    private ColaboradorControle getColaboradorControleBySession(HttpServletRequest request) {
        return (ColaboradorControle) request.getSession().getAttribute(ColaboradorControle.class.getSimpleName());
    }

    private MovParcelaControle getMovParcelaControleBySession(HttpServletRequest request) {
        return (MovParcelaControle) request.getSession().getAttribute(MovParcelaControle.class.getSimpleName());
    }

    private VendaAvulsaControle getVendaAvulsaControleBySession(HttpServletRequest request) {
        return (VendaAvulsaControle) request.getSession().getAttribute(VendaAvulsaControle.class.getSimpleName());
    }

    private MovimentoContaCorrenteClienteControle getMovimentoContaCorrenteClienteControleBySession(HttpServletRequest request) {
        return (MovimentoContaCorrenteClienteControle) request.getSession().getAttribute(MovimentoContaCorrenteClienteControle.class.getSimpleName());
    }

    private QuestionarioClienteControle getQuestionarioClienteControleBySession(HttpServletRequest request) {
        return (QuestionarioClienteControle) request.getSession().getAttribute(QuestionarioClienteControle.class.getSimpleName());
    }

    private LancarBrindeClienteControle getLancarBrindeClienteControleBySession(HttpServletRequest request) {
        return (LancarBrindeClienteControle) request.getSession().getAttribute(LancarBrindeClienteControle.class.getSimpleName());
    }

    private HistoricoPontosControle getHistoricoPontosControleControleBySession(HttpServletRequest request) {
        return (HistoricoPontosControle) request.getSession().getAttribute(HistoricoPontosControle.class.getSimpleName());
    }

    private AulaAvulsaDiariaControle getAulaAvulsaDiariaControleBySession(HttpServletRequest request) {
        return (AulaAvulsaDiariaControle) request.getSession().getAttribute(AulaAvulsaDiariaControle.class.getSimpleName());
    }

    private boolean isOperacaoFinanceiro(HttpServletRequest request) {
        return Boolean.valueOf(request.getParameter("isOperacaoFinanceiro"));
    }

    private String loadClienteContrato(HttpServletRequest request, String chave) throws Exception {
        String codCliente = request.getParameter("codCliente");
        String codContrato = request.getParameter("codContrato");
        String operacaoEnumName = request.getParameter("operacaoClienteEnumName");

        ClienteControle clienteControle = getClienteControleOrCreate(request);
        ClienteVO cli = new ClienteVO();
        cli.setCodigo(Integer.valueOf(codCliente));
        clienteControle.inicializaDados();
        clienteControle.prepararTelaCliente(cli, true);
        request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);

        TelaClienteControle telaClienteControle = getTelaClienteControleOrCreate(request);
        telaClienteControle.pegarClienteControle();
        if (!UteisValidacao.emptyString(codContrato)) {
            ContratoVO contratoVO = new ContratoVO();
            contratoVO.setCodigo(Integer.parseInt(codContrato));
            clienteControle.setContratoVO(contratoVO);
            telaClienteControle.selecionarContrato(Integer.parseInt(codContrato), false);
            clienteControle.selecionarDadosContrato(false);
            telaClienteControle.apresentarOperacaoNoMenuCliente();
            if (OperacoesTelaClienteEnum.AFASTAMENTO.name().equals(operacaoEnumName)) {
                clienteControle.apresentarOperacaoNoMenuCliente();
            }
        }

        request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);
        request.getSession().setAttribute(TelaClienteControle.class.getSimpleName(), telaClienteControle);

        OperacoesClienteService operacoesClienteService = new OperacoesClienteService(codCliente, OperacoesTelaClienteEnum.valueOf(operacaoEnumName), chave);
        return operacoesClienteService.abrirOperacaoCliente(request);
    }

    private String loadCliente(HttpServletRequest request, String chave) throws Exception {
        String codCliente = request.getParameter("codCliente");
        String operacaoEnumName = request.getParameter("operacaoClienteEnumName");

        ClienteControle clienteControle = getClienteControleOrCreate(request);
        ClienteVO cli = new ClienteVO();
        cli.setCodigo(Integer.valueOf(codCliente));
        clienteControle.inicializaDados();
        clienteControle.prepararTelaCliente(cli, true);

        request.getSession().setAttribute(ClienteControle.class.getSimpleName(), clienteControle);

        OperacoesClienteService operacoesClienteService = new OperacoesClienteService(codCliente, OperacoesTelaClienteEnum.valueOf(operacaoEnumName), chave);
        return operacoesClienteService.abrirOperacaoCliente(request);
    }

    private TelaClienteControle getTelaClienteControleOrCreate(HttpServletRequest request) throws Exception {
        TelaClienteControle telaClienteControle = getTelaClienteControleBySession(request);

        if (telaClienteControle == null) {
            telaClienteControle = (TelaClienteControle) JSFUtilities.getManagedBean("TelaClienteControle");
        }

        return telaClienteControle;
    }

    private TelaClienteControle getTelaClienteControleBySession(HttpServletRequest request) {
        return (TelaClienteControle) request.getSession().getAttribute(TelaClienteControle.class.getSimpleName());
    }

    private ClienteControle getClienteControleOrCreate(HttpServletRequest request) throws Exception {
        ClienteControle afastamentoContratoControle = getClienteControleBySession(request);

        if (afastamentoContratoControle == null) {
            afastamentoContratoControle = (ClienteControle) JSFUtilities.getManagedBean("ClienteControle");
        }

        return afastamentoContratoControle;
    }

    private ClienteControle getClienteControleBySession(HttpServletRequest request) {
        return (ClienteControle) request.getSession().getAttribute(ClienteControle.class.getSimpleName());
    }

    private Boolean isContratoOperacao(HttpServletRequest request) {
        return Boolean.parseBoolean(request.getParameter("isContratoOperacao"));
    }

    private Boolean isTelaCliente(HttpServletRequest request) {
        return isContratoOperacao(request) || isOperacaoFinanceiro(request);
    }

    private String getClienteRedirect(HttpServletRequest request, String urlRedirect) throws IOException {
        return getUrlWithContextUsingProps(request, urlRedirect) + getParamClienteMatricula(request);
    }

    private String getCompraRedirect(HttpServletRequest request, String urlRedirect) throws IOException {
        return getUrlWithContextUsingProps(request, urlRedirect).replace(PropsService.uriCompra,
                "faces/compraCons.jsp?solicitacaoCompra="+getParamSolicitacaoCompra(request));
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

    private LoginControle getLoginControleOrCreate(HttpServletRequest request, boolean isRequestToNFe) throws Exception {
        LoginControle loginControl = getLoginControleBySession(request);

        if (loginControl == null) {
            if (isRequestToNFe) {
                loginControl = (LoginControle) JSFUtilities.getManagedBean("LoginControle");
            } else {
                return new LoginControle();
            }
        }

        return loginControl;
    }

    private String getUrlWithContextUsingProps(HttpServletRequest request, String urlRedirect) {
        String propertyValue = PropsService.getPropertyValue(urlRedirect);
        if (propertyValue != null) {
            return request.getContextPath() + PropsService.getPropertyValue(urlRedirect);
        }
        return urlRedirect;
    }

    private MenuControle getMenuControleOrCreate(HttpServletRequest request) throws Exception {
        MenuControle menuControle = getMenuControleBySession(request);

        if (menuControle == null) {
            menuControle = new MenuControle();
            JSFUtilities.storeOnSession(MenuControle.class.getSimpleName(), menuControle);
            return menuControle;
        }

        return menuControle;
    }

    private FuncionalidadeControle getFuncionalidadeControleOrCreate(HttpServletRequest request) throws Exception {
        FuncionalidadeControle loginControl = getFuncionalidadeControleBySession(request);

        if (loginControl == null) {
            FuncionalidadeControle funcionalidadeControle = new FuncionalidadeControle();
            JSFUtilities.storeOnSession(FuncionalidadeControle.class.getSimpleName(), funcionalidadeControle);
            return funcionalidadeControle;
        }

        return loginControl;
    }

    private LoginControle getLoginControleBySession(HttpServletRequest request) {
        return (LoginControle) request.getSession().getAttribute(LoginControle.class.getSimpleName());
    }

    private AtualizarDadosControle getAtualizarDadosControleBySession(HttpServletRequest request) {
        return (AtualizarDadosControle) request.getSession().getAttribute(AtualizarDadosControle.class.getSimpleName());
    }

    private FuncionalidadeControle getFuncionalidadeControleBySession(HttpServletRequest request) {
        return (FuncionalidadeControle) request.getSession().getAttribute(FuncionalidadeControle.class.getSimpleName());
    }

    private MenuControle getMenuControleBySession(HttpServletRequest request) {
        return (MenuControle) request.getSession().getAttribute(MenuControle.class.getSimpleName());
    }

    private AfastamentoContratoControle getAfastamentoContratoControleBySession(HttpServletRequest request) {
        return (AfastamentoContratoControle) request.getSession().getAttribute(AfastamentoContratoControle.class.getSimpleName());
    }

    private ParametrosLoginDiretoModuloNotasJSON createParametrosLoginDiretoNFeFromJSON(JSONObject json, PrintWriter out) {
        try {
            return new ParametrosLoginDiretoModuloNotasJSON(json.optString("parametrosNFe"));
        } catch (Exception e) {
            ConversionException ex = new ConversionException("Não foi recuperar os parâmetros de login direto para o módulo de notas!", e);
            out.println(ex.getMessage());
            Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
            throw ex;
        }
    }

    private String getStringFromJsonIgnoreException(JSONObject json, String key) {
        try {
            return json.getString(key);
        } catch (Exception ignored) {
            return "";
        }
    }

    private boolean getBooleanFromJsonIgnoreException(JSONObject json, String key) {
        try {
            return json.getBoolean(key);
        } catch (Exception ignored) {
            return false;
        }
    }

    private boolean isRequestToModuloNFe(String uri, HttpServletRequest request) {
        return getUrlRedirect(uri, request).equals(PropsService.urlNFSe);
    }

    /**
     * Invalida a sessão atual e redireciona para a mesma URI, porém sinalizando que não dever passar aqui novamente, através do par�metro {@link #PARAM_DESLOGAR}.
     *
     * <h2>Devo ter a garantia que vou retornar exatamente os mesmos parâmetros originais no request, com exceção do campo que indica para
     * não entrar aqui novamente.</h2>
     */
    private void invalidarSessao(HttpServletRequest request, HttpServletResponse response) throws IOException, JSONException {
        JSONObject json = retornarJSONParametrosLogin(request);

        if (json != null) {
            request.getSession().invalidate();
            json.put(PARAM_DESLOGAR, false);
            String urlRedirect = request.getContextPath() + "/oid?lgn=" + Uteis.encriptar(json.toString(), "chave_login_unificado");
            if(!UteisValidacao.emptyString(request.getParameter("funcionalidadeNome"))){
                urlRedirect = urlRedirect + "&funcionalidadeNome=" + request.getParameter("funcionalidadeNome");
            }
            if(!UteisValidacao.emptyString(request.getParameter("jspPage"))){
                urlRedirect = urlRedirect + "&jspPage=" + request.getParameter("jspPage");
            }
            if(!UteisValidacao.emptyString(request.getParameter("codCliente"))){
                urlRedirect = urlRedirect + "&codCliente=" + request.getParameter("codCliente");
            }
            if(!UteisValidacao.emptyString(request.getParameter("operacaoClienteEnumName"))){
                urlRedirect = urlRedirect + "&operacaoClienteEnumName=" + request.getParameter("operacaoClienteEnumName");
            }
            if(!UteisValidacao.emptyString(request.getParameter("isContratoOperacao"))){
                urlRedirect = urlRedirect + "&isContratoOperacao=" + request.getParameter("isContratoOperacao");
            }
            if(!UteisValidacao.emptyString(request.getParameter("urlRedirect"))){
                urlRedirect = urlRedirect + "&urlRedirect=" + request.getParameter("urlRedirect");
            }
            response.sendRedirect(urlRedirect);
        }
    }

    private JSONObject retornarJSONParametrosLogin(HttpServletRequest request) throws JSONException {
        String lgn = request.getParameter("lgn");

        if (StringUtils.isNotBlank(lgn)) {
            return new JSONObject(Uteis.desencriptar(lgn, "chave_login_unificado"));
        }

        return null;
    }


    public static void main(String[] args) {
        System.out.println(Uteis.desencriptar("2eca84987d8c8489999ad57eff1dff1deb36132336f80a1221e72cd029f70c1210da4bd453a0a768b64001001a28ff15db29f77dc08caf64b671a265bb51d22ff37ed98dcaa9a7728386bc73af778cc86aef39c951a27ed97fcb728e91d820ef0cf50b17e024fa3fca5fb1b77a9f68c940dd6aa27db368b9b2b8b37e8fd57fc29bb39aad81d463f6411d09421a22313a0546d629f335c292a99465b849c787ec7a8480946bb74ad926e834e252fa60ee46c681a6a789b76ef64a0f3ee755ff3dea49ef4d1558cf7ca0b87ba792b57ca28eba62c470d084d5a890be61a99783a089b760dd7cff4805251f1221021c1c04410448d451f0280f083fe952f554ce91cb889286a480bc7f9f848da79785bb71d25dfc292af17bc25bab7e8a6f849dd165f04ee67fd36afd59eb52ee7ed86aeb0916de31f128ef34fb7fdd6fb759c2848cbb90968282a2b27cc37c83a68ab57ab399949c9d62f351c9230558f9160e2e3af6133cd754a2b384938fbe47c548f841d531cf6ad556ae79be78a548f02df93cf93df44ccaa2919799aa956fd927f138f94dc349f20c0e1036f163be63c37c8d85d641c576a58f7ea9a2986edb200369c269bd51f658fb0d331435ce6bda6bf22e0738ce41e145d17d83ba4ed571d82df152fd271ce93ece72998992899465be6cdb3cd74cf12bfb143bdd69dc4fcb74ae9b869483a68f67d07dab69d2799c7aaf7089e345e744db41e651ad96b96b83ba41e73df836f701150a17e653d45bef5bf021f223012b00200c31f23b32df2616e5768c84bf7d909b68a26beb3ac690b4719578aa84a8b077c87b9b9e64cb36f516330628ff2ccb50ed29f93acb74b94fc3999c92afaf7ba7bf7cb541fc20e62df12815e776d87382c27ef0093dd52eca5bde79c751ef3ff33fce59ab76ee7eff19e32ec97787818bc498d3788582989860e366ed10ec30f2081525e635ff1401101419e83b350e46df0dee3b3a0f5f978e8d68ab77b26be94b0e282b2126d059f138c15ae021ec66c681da9dada0758f9a9a65966cb170ba413c034ba97a8ab07e8389919a66fe4e2ff633cb84d0ad7ac04fd653d559f6360451e43003391c3f1f38026da6b08bd867fe57ea22e072f505", "chave_login_unificado"));
    }

    private LogoutControle getLogoutControleOrCreate(HttpServletRequest request) throws Exception {
        LogoutControle logoutControle = getLogoutControleBySession(request);

        if (logoutControle == null) {
            logoutControle = (LogoutControle) JSFUtilities.getManagedBean("LogoutControle");
        }

        return logoutControle;
    }

    private LogoutControle getLogoutControleBySession(HttpServletRequest request) {
        return (LogoutControle) request.getSession().getAttribute(LogoutControle.class.getSimpleName());
    }
}

