package controle.arquitetura.security;

import br.com.pactosolucoes.aas.authorization.entity.Autorizacao;
import br.com.pactosolucoes.aas.authorization.enumerator.Entidade;
import br.com.pactosolucoes.aas.authorization.enumerator.Funcionalidade;
import br.com.pactosolucoes.aas.authorization.enumerator.Operacao;
import br.com.pactosolucoes.atualizadb.negocio.PovoadorDadosBancoInicial;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ReplicarRedeEmpresaEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.plano.PlanoControle;
import negocio.comuns.arquitetura.*;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.OpcaoPerfilAcesso;
import negocio.comuns.utilitarias.OpcoesPerfilAcesso;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import org.apache.commons.beanutils.BeanComparator;
import org.json.JSONObject;
import org.postgresql.util.PSQLException;
import servicos.discovery.RedeDTO;
import servicos.impl.admCoreMs.AdmCoreMsService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * perfilAcessoForm.jsp perfilAcessoCons.jsp) com as funcionalidades da classe <code>PerfilAcesso</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see PerfilAcesso
 * @see PerfilAcessoVO
 */
public class PerfilAcessoControle extends SuperControle {

    private String modulo;
    private String permissoesModulo;
    private PerfilAcessoVO perfilAcessoVO;
    private String acao;
    private String moduloSelecionado;
    private String entidadeSelecionada;
    private String acaoSelecionada;
    private String permissaoModuloSelecionado;
    private PermissaoVO permissaoVO;
    private List listaSelectItemTodasFuncionalidadesZillyon;
    private List listaSelectItemModulosZillyon;
    private List listaSelectItemTodasAcoesZillyon;
    private boolean expandirRetrairEntidades;
    private boolean expandirRetrairFuncionalidade;
    private String onComplete;
    private HashMap<String,String> hintsPermissoes;
    private List<SelectItem> tipos = new ArrayList<SelectItem>();
    private boolean adicionarHintsWiki = true;
    private PerfilAcessoVO perfilAcessoVOClone;
    private String situacaoFiltro;
    private String msgAlert;
    private List<PerfilAcessoRedeEmpresaVO> listaPerfisAcessoRedeEmpresa;

    public PerfilAcessoControle() throws Exception {
        obterUsuarioLogado();
        inicializarSemUser();
    }

    public PerfilAcessoControle(Boolean semUser) throws Exception {
        inicializarSemUser();
    }

    private void inicializarSemUser() {
        inicializar();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    private void inicializarDadosControle() {
        moduloSelecionado = "";
        entidadeSelecionada = "";
        permissaoModuloSelecionado = "";
    }

    private void inicializar() {
        setModulo("");
        setAcao("");
        setPermissoesModulo("(0)(1)(2)(3)(9)(12)");
        inicializarListaSelectItemModulos();
        inicializarListaSelectItemEntidadesModulo();
        inicializarListaSelectItemTodasAcoesZillyon();

        expandirRetrairEntidades = true;
        expandirRetrairFuncionalidade = true;
    }

    public void inicializarListaSelectItemModulos() {
        List objs = new ArrayList();
        objs.add(new SelectItem(" TODOS", " <<TODOS MÓDULOS>> "));
        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Enumeration keys = modulos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            Hashtable modulo = (Hashtable) modulos.get(value);
            objs.add(new SelectItem(value, value));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        setListaSelectItemModulosZillyon(objs);
    }

    private void inicializarComboBox() {
        inicializarListaSelectItemModulos();
        inicializarListaSelectItemEntidadesModulo();
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>PerfilAcesso</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        this.setMarcarTudo(Boolean.FALSE);
        inicializaListaPermissoes();
        setPerfilAcessoVO(new PerfilAcessoVO());
        setPermissaoVO(new PermissaoVO());
        this.entidadesAutorizacoes = new ArrayList<Autorizacao>();
        this.funcionalidades = new ArrayList<Autorizacao>();
        inicializarDadosControle();
        inicializarComboBox();
        this.setOperacoesTela("");
        setMensagemID("msg_entre_dados");
        setSucesso(true);
        setErro(false);
        inicializarFlags();
        return "editar";
    }

    /**
     * Responsável por
     * <AUTHOR>
     * 05/04/2011
     */
    private void inicializarFlags() {
        expandirRetrairEntidades = true;
        expandirRetrairFuncionalidade = true;
        marcarTudoFuncionalidadesCE = false;
        marcarTudoOperacoesCE = false;
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>PerfilAcesso</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            this.inicializaListaPermissoes();
            PerfilAcessoVO obj = getFacade().getPerfilAcesso().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
//            setPerfilAcessoVO(new PerfilAcessoVO());
            setPerfilAcessoVO(obj);
            this.carregarPerfilAcesso(perfilAcessoVO);
            setPermissaoVO(new PermissaoVO());
            inicializarDadosControle();
            inicializarFlags();
            inicializarComboBox();
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
            limparMsg();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>PerfilAcesso</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editarCE() {
        try {
            //chamada ao metodo de editar padrao
            editar();
            this.entidadesAutorizacoes = new ArrayList<Autorizacao>();
            this.funcionalidades = new ArrayList<Autorizacao>();
            //Criando variavel auxiliar
            Autorizacao autorizacao = null;

            //Laco para determinar quais entidades foram selecionadas
            for (Entidade entidade : Entidade.values()) {
                //Consultando se a entidade possui autorizacao
                autorizacao = getFacade().getCentralEventosFacade().consultarEntidadeAutorizacaoRegras(entidade.getCodigo(), this.getPerfilAcessoVO().getCodigo());
                //Verificando se a entidade possui autorizacao, se nao possuir cria um objeto autorizacao
                if (autorizacao == null) {
                    autorizacao = new Autorizacao();
                    autorizacao.setEntidade(entidade.getCodigo());
                    autorizacao.setDescricao(entidade.getDescricao());
                }
                //Adicionando a lista de entidade autorizadas
                entidadesAutorizacoes.add(autorizacao);
            }

            //Laco para determinar quais funcionalidades foram selecionadas
            for (Funcionalidade funcionalidade : Funcionalidade.values()) {
                //Consultando se a funcionalidade possui autorizacao
                autorizacao = getFacade().getCentralEventosFacade().consultarFuncionalidadeAutorizacaoRegras(funcionalidade.getCodigo(), this.getPerfilAcessoVO().getCodigo());
                //Verificando se a entidade possui autorizacao, se nao possuir cria um objeto autorizacao
                //senao seta o objeto autorizacao obtido como selecionado
                if (autorizacao == null) {
                    autorizacao = new Autorizacao();
                    autorizacao.setFuncionalidade(funcionalidade.getCodigo());
                    autorizacao.setDescricao(funcionalidade.getDescricao());
                } else {
                    autorizacao.setSelecionado(true);
                }
                //Adicionando a lista de funcionalidades
                funcionalidades.add(autorizacao);
            }
            carregaOperacaoTela();
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "";
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>PerfilAcesso</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            limparMsg();
            if (centralEventos) {
                this.verificarAutorizacao();
                //verificar as permissoes selecionadas
                montaPermissoesSalvar();
                if (perfilAcessoVO.isNovoObj().booleanValue()) {
                    getFacade().getPerfilAcesso().incluir(perfilAcessoVO, true);
                    //LOG - INICIO
                    registraLogInclusao();
                    //LOG - FIM
                } else {
                    getFacade().getPerfilAcesso().alterar(perfilAcessoVO, true);
                    registraLogAlteracao();
                }

                this.gravarAutorizacaoRegras();
            } else {
                //verificar as permissoes selecionadas
                montaPermissoesSalvar();
                if (perfilAcessoVO.isNovoObj().booleanValue()) {
                    getFacade().getPerfilAcesso().incluir(perfilAcessoVO);
                    registraLogInclusao();
                    //LOG - FIM
                } else {
                    getFacade().getPerfilAcesso().alterar(perfilAcessoVO);
                    registraLogAlteracao();
                }
                perfilAcessoVO.registrarObjetoVOAntesDaAlteracao();
            }
            notificarRecursoEmpresaPerfilAcesso();
            perfilAcessoVOClone = (PerfilAcessoVO) perfilAcessoVO.getClone(true);
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
                replicarAutomaticoTodas();
            }
            Uteis.reloadTreinoCacheUsuario(getKey(), null);
            montarSucesso("msg_dados_gravados_sucesso");
            return "editar";
        } catch (Exception e) {
            if (e.getMessage().contains("ERRO: duplicar valor da chave viola a restrição de unicidade \"perfilacesso_nome_key\"\n") || e.getMessage().contains("ERROR: duplicate key value violates unique constraint \"perfilacesso_nome_key\" ")){
                montarErro("Já existe um perfil de acesso com este nome ");
            }else{
                montarErro(e);
            }
            return "editar";
        }
    }

    /**
     * Responsável por chamar o método gravar indicando que não deve usar as funcionalidades do CE 
     * <AUTHOR>
     * 23/03/2011
     * @return
     */
    public void gravar() {
        gravar(false);

    }

    /**
     * Responsável por chamar o método gravar indicando que deve usar as funcionalidades do CE 
     * <AUTHOR>
     * 23/03/2011
     * @return
     */
    public void gravarCE() {
        gravar(true);
    }

    /**
     * Responsável por gravar as autorizacões para o CE concedidas pelo usuário
     * <AUTHOR>
     * 23/03/2011
     * @throws Exception
     */
    public void gravarAutorizacaoRegras() throws Exception {
        this.setaOperacao();
        List<Autorizacao> autorizacoes = new ArrayList<Autorizacao>();
        //preparar a lista com entidades que possuem operações selecionadas
        for (Autorizacao obj : this.entidadesAutorizacoes) {
            if (!obj.getOperacao().equals("")) {
                autorizacoes.add(obj);
            }
        }
        //preparar a lista com funcionalidades selecionadas
        for (Autorizacao obj : this.funcionalidades) {
            if (obj.getSelecionado()) {
                autorizacoes.add(obj);
            }
        }
        //gravar no BD
        getFacade().getCentralEventosFacade().inlcuirAutorizacaoRegras(autorizacoes, perfilAcessoVO.getCodigo());
        carregaOperacaoTela();
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP PerfilAcessoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    @SuppressWarnings("unchecked")
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getPerfilAcesso().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getPerfilAcesso().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>PerfilAcessoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getPerfilAcesso().excluir(perfilAcessoVO, true);
                //registrar log
                registrarLogExclusaoObjetoVO(perfilAcessoVO, perfilAcessoVO.getCodigo().intValue(), "PERFIL ACESSO", 0);
            } else {
                getFacade().getPerfilAcesso().excluir(perfilAcessoVO);
                incluirLogExclusao();
            }
            setPerfilAcessoVO(new PerfilAcessoVO());
            setPermissaoVO(new PermissaoVO());
            setMensagemID("msg_dados_excluidos");
            montarSucesso("msg_dados_excluidos");
            redirect("/faces/perfilAcessoCons.jsp");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (PSQLException e) {
            if (e.toString().contains("\"fk_usuarioperfilacesso_perfilacesso\"")) {
                montarErro("Este Perfil não pode ser excluído, pois está sendo utilizado por um ou mais usuários!");
            } else if(e.toString().contains("\"formapagamentoperfilacesso_perfilacesso_fkey\"")) {
                try {
                    montarErro("Este Perfil não pode ser excluído, pois está sendo utilizado nas formas de pagamento: " + getFacade().getFormaPagamentoPerfilAcesso().obterFormaPagamentoPorPerfilAcesso(perfilAcessoVO.getCodigo()));
                } catch (Exception e2) {
                    montarErro("Este Perfil não pode ser excluído, pois está sendo utilizado!");
                }
            } else {
                montarErro("Este Perfil não pode ser excluído, pois está sendo utilizado!");
            }
            return "editar";
        } catch (Exception e) {
            montarErro(e);
            return "editar";
        }
    }

    /**
     * Responsável por chamar o método EXCLUIR indicando que não deve usar as funcionalidades do CE 
     * <AUTHOR>
     * 23/03/2011
     * @return
     */
    public void excluir() {
        excluir(false);
    }

    /**
     * Responsável por chamar o método EXCLUIR indicando que deve usar as funcionalidades do CE 
     * <AUTHOR>
     * 23/03/2011
     * @return
     */
    public String excluirCE() {
        return excluir(true);

    }

    public void processarAdicionarPermissoesModuloZillyon(Hashtable modulo) throws Exception {
        Enumeration entidades = modulo.elements();
        while (entidades.hasMoreElements()) {
            OpcaoPerfilAcesso entidade = (OpcaoPerfilAcesso) entidades.nextElement();
            PermissaoVO permissao = new PermissaoVO();
            permissao.setCodPerfilAcesso(getPerfilAcessoVO().getCodigo());
            permissao.setNomeEntidade(entidade.getNome());
            permissao.setTituloApresentacao(entidade.getTitulo());
            permissao.setPermissoes(permissoesModulo);
            getPerfilAcessoVO().adicionarObjPermissaoVOs(permissao);
        }
    }

    public String adicionarTodasPermissoesModuloZillyon() {
        try {
            if (this.getModulo().equals(" TODOS")) {
                Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
                Enumeration i = modulos.elements();
                while (i.hasMoreElements()) {
                    Hashtable modulo = (Hashtable) i.nextElement();
                    processarAdicionarPermissoesModuloZillyon(modulo);
                }
            } else {
                Hashtable modulo = (Hashtable) OpcoesPerfilAcesso.getAplicacao().get(this.getModulo());
                processarAdicionarPermissoesModuloZillyon(modulo);
            }
            this.modulo = "";
            //this.permissoesModulo = "";
            this.setPermissaoVO(new PermissaoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String adicionarPermissaoModulo() throws Exception {
        try {
            if (!getPerfilAcessoVO().getCodigo().equals(new Integer(0))) {
                permissaoVO.setCodPerfilAcesso(getPerfilAcessoVO().getCodigo());
            }
            getPerfilAcessoVO().adicionarObjPermissaoVOs(getPermissaoVO());
            this.setPermissaoVO(new PermissaoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String obterTituloReferenteOpcaoPerfilAcesso(String nomeOpcao) {
        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Enumeration e = modulos.elements();
        while (e.hasMoreElements()) {
            Hashtable modulo = (Hashtable) e.nextElement();
            Enumeration i = modulo.elements();
            while (i.hasMoreElements()) {
                OpcaoPerfilAcesso opcao = (OpcaoPerfilAcesso) i.nextElement();
                if (opcao.getNome().equals(nomeOpcao)) {
                    return opcao.getTitulo();
                }
            }
        }
        return nomeOpcao;
    }

    /**
     * Adicionar nível de permissão selecionada para todos os módulos do sistema.
     * @throws java.lang.Exception
     */
    private void adicionarPermissaoParaTodosOsModulos() throws Exception {
        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Enumeration keys = modulos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            Hashtable modulo = (Hashtable) modulos.get(value);
            adicionarPermissaoParaModulo(modulo, value, this.getPermissoesModulo());
        }
    }

    /**
     * Adicionar nível de permissão selecionada para um único módulo
     * selecionado do sistema.
     * @throws java.lang.Exception
     */
    private void adicionarPermissaoParaTodasEntidadesModuloSelecionado() throws Exception {
        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Hashtable modulo = (Hashtable) modulos.get(this.getModuloSelecionado());
        adicionarPermissaoParaModulo(modulo, this.getModuloSelecionado(), this.getPermissoesModulo());
    }

    public void adicionarPermissao() throws Exception {
        try {
            if (!getPerfilAcessoVO().getCodigo().equals(new Integer(0))) {
                permissaoVO.setCodPerfilAcesso(getPerfilAcessoVO().getCodigo());
            }
            if (!this.getExisteModuloSelecionado()) {
                adicionarPermissaoParaTodosOsModulos();
            } else {
                if (!this.getExisteEntidadeSelecionada()) {
                    adicionarPermissaoParaTodasEntidadesModuloSelecionado();
                } else {
                    adicionarPermissaoParaEntidadeSelecionada();
                }
            }
            this.setPermissaoModuloSelecionado("");
            this.setEntidadeSelecionada(" TODOS");
            this.setPermissaoVO(new PermissaoVO());
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Adicionar nível de permissão selecionada para uma única entidade
     * selecionada do sistema.
     * @throws java.lang.Exception
     */
    private void adicionarPermissaoParaEntidadeSelecionada() throws Exception {
        adicionarPermissaoParaEntidade(this.getModuloSelecionado(), this.getEntidadeSelecionada(), this.getPermissaoModuloSelecionado(), OpcaoPerfilAcesso.TP_ENTIDADE);
    }

    private void adicionarPermissaoParaModulo(Hashtable entidadesModulo, String nomeModulo, String nivelPermissao) throws Exception {
        Enumeration keys = entidadesModulo.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            OpcaoPerfilAcesso opcao = (OpcaoPerfilAcesso) entidadesModulo.get(value);
            if (opcao.getTipo() == OpcaoPerfilAcesso.TP_ENTIDADE) {
                adicionarPermissaoParaEntidade(nomeModulo, opcao.getNome(), nivelPermissao, opcao.getTipo());
            }
        }
    }

    private void adicionarPermissaoParaEntidade(String moduloSelecionado,
            String entidadeSelecionada,
            String nivelPermissao,
            Integer tipoPermissao) throws Exception {
        PermissaoVO obj = new PermissaoVO();
        obj.setNomeEntidade(entidadeSelecionada);
        obj.setTituloApresentacao(this.getTituloApresentarEntidade(moduloSelecionado, entidadeSelecionada));
        obj.setPermissoes(nivelPermissao);
        obj.setTipoPermissao(tipoPermissao);
        getPerfilAcessoVO().adicionarObjPermissaoVOs(obj);
    }

    public String getTituloApresentarEntidade(String moduloPrm, String entidadePrm) {
        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Hashtable entidades = (Hashtable) modulos.get(moduloPrm);
        Enumeration keys = entidades.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            OpcaoPerfilAcesso opcaoPerfilAcesso = (OpcaoPerfilAcesso) entidades.get(value);
            if (opcaoPerfilAcesso.getNome().equals(entidadePrm)) {
                return opcaoPerfilAcesso.getTitulo();
            }
        }
        return entidadePrm;
    }

    public String adicionarPermissaoAcao() throws Exception {
        try {
            if (!getPerfilAcessoVO().getCodigo().equals(new Integer(0))) {
                permissaoVO.setCodPerfilAcesso(getPerfilAcessoVO().getCodigo());
            }
            permissaoVO.setNomeEntidade(acao);
            permissaoVO.setTituloApresentacao(obterTituloReferenteOpcaoPerfilAcesso(acao));
            permissaoVO.setPermissoes("(0)(1)(2)(3)(9)(12)");
            getPerfilAcessoVO().adicionarObjPermissaoVOs(getPermissaoVO());
            setAcao("");

            setPermissaoVO(new PermissaoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String editarPermissao() throws Exception {
        PermissaoVO obj = (PermissaoVO) context().getExternalContext().getRequestMap().get("permissao");
        setPermissaoVO(obj);
        return "editar";
    }

    public String removerPermissao() throws Exception {
        PermissaoVO obj = (PermissaoVO) context().getExternalContext().getRequestMap().get("permissao");
        getPerfilAcessoVO().excluirObjPermissaoVOs(obj.getNomeEntidade());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void inicializarListaSelectItemEntidadesModulo() {
        List objs = new ArrayList();
        List objsAcoes = new ArrayList();
        if (!getExisteModuloSelecionado()) {
            setListaSelectItemTodasFuncionalidadesZillyon(objs);
            return;
        }
        objs.add(new SelectItem(" TODOS", " <<TODAS ENTIDADES>> "));
        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Hashtable entidades = (Hashtable) modulos.get(getModuloSelecionado());
        Enumeration keys = entidades.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            OpcaoPerfilAcesso entidade = (OpcaoPerfilAcesso) entidades.get(value);
            if (entidade.getTipo() == OpcaoPerfilAcesso.TP_ENTIDADE) {
                objs.add(new SelectItem(entidade.getNome(), entidade.getTitulo()));
            }
        }
        setListaSelectItemTodasFuncionalidadesZillyon(Ordenacao.ordenarLista(objs, "value"));
    }

    public void inicializarPermissaoEntidadesModulo() {
        this.permissaoVO.setNomeEntidade(this.entidadeSelecionada);
        Iterator i = this.getPerfilAcesso().getPermissaoVOs().iterator();
        while (i.hasNext()) {
            PermissaoVO obj = (PermissaoVO) i.next();
            if (obj.getNomeEntidade().equals(this.entidadeSelecionada)) {
                permissaoModuloSelecionado = obj.getPermissoes();
                return;
            }
        }
        permissaoModuloSelecionado = "";
    }

    public void inicializarPermissaoAcao() {
        this.permissaoVO.setNomeEntidade(this.acaoSelecionada);
        Iterator i = this.getPerfilAcesso().getPermissaoVOs().iterator();
        while (i.hasNext()) {
            PermissaoVO obj = (PermissaoVO) i.next();
            if (obj.getNomeEntidade().equals(this.acaoSelecionada)) {
                permissaoModuloSelecionado = obj.getPermissoes();
                return;
            }
        }
        permissaoModuloSelecionado = "";
    }

    public Boolean getExisteModuloSelecionado() {
        if ((getModuloSelecionado() != null)
                && (!moduloSelecionado.equals(""))
                && (!moduloSelecionado.equals(" TODOS"))) {
            return true;
        }
        return false;
    }

    public Boolean getExisteEntidadeSelecionada() {
        if (!getExisteModuloSelecionado()) {
            return false;
        }
        if ((getEntidadeSelecionada() != null)
                && (!entidadeSelecionada.equals(""))
                && (!entidadeSelecionada.equals(" TODOS"))) {
            return true;
        }
        return false;
    }

    public Boolean getExisteAcaoSelecionada() {
        if (!getExisteModuloSelecionado()) {
            return false;
        }
        if ((getAcaoSelecionada() != null)
                && (!acaoSelecionada.equals(""))
                && (!acaoSelecionada.equals(" TODOS"))) {
            return true;
        }
        return false;
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getListaSelectItemPermissoesPermissao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable nivelAcessos = (Hashtable) Dominios.getNivelAcesso();
        Enumeration keys = nivelAcessos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) nivelAcessos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public PermissaoVO getPermissaoVO() {
        return permissaoVO;
    }

    public void setPermissaoVO(PermissaoVO permissaoVO) {
        this.permissaoVO = permissaoVO;
    }

    public PerfilAcessoVO getPerfilAcessoVO() {
        if (perfilAcessoVO == null) {
            perfilAcessoVO = new PerfilAcessoVO();
        }
        return perfilAcessoVO;
    }

    public void setPerfilAcessoVO(PerfilAcessoVO perfilAcessoVO) {
        this.perfilAcessoVO = perfilAcessoVO;
    }

    public List getListaSelectItemModulosZillyon() {
        return listaSelectItemModulosZillyon;
    }

    public void setListaSelectItemModulosZillyon(List listaSelectItemModulosZillyon) {
        this.listaSelectItemModulosZillyon = listaSelectItemModulosZillyon;
    }

    public List getListaSelectItemTodasFuncionalidadesZillyon() {
        return listaSelectItemTodasFuncionalidadesZillyon;
    }

    public void setListaSelectItemTodasFuncionalidadesZillyon(List listaSelectItemTodasFuncionalidadesZillyon) {
        this.listaSelectItemTodasFuncionalidadesZillyon = listaSelectItemTodasFuncionalidadesZillyon;
    }

    public String getPermissoesModulo() {
        if (permissoesModulo == null) {
            permissoesModulo = "";
        }
        return permissoesModulo;
    }

    public void setPermissoesModulo(String permissoesModulo) {
        this.permissoesModulo = permissoesModulo;
    }

    public String getModulo() {
        if (modulo == null) {
            modulo = "";
        }
        return modulo;
    }

    public void setModulo(String modulo) {
        this.modulo = modulo;
    }

    public List getListaSelectItemTodasAcoesZillyon() {
        return listaSelectItemTodasAcoesZillyon;
    }

    public void setListaSelectItemTodasAcoesZillyon(List listaSelectItemTodasAcoesZillyon) {
        this.listaSelectItemTodasAcoesZillyon = listaSelectItemTodasAcoesZillyon;
    }

    public String getAcao() {
        if (acao == null) {
            acao = "";
        }
        return acao;
    }

    public void setAcao(String acao) {
        this.acao = acao;
    }

    /**
     * @return the moduloSelecionado
     */
    public String getModuloSelecionado() {
        return moduloSelecionado;
    }

    /**
     * @param moduloSelecionado the moduloSelecionado to set
     */
    public void setModuloSelecionado(String moduloSelecionado) {
        this.moduloSelecionado = moduloSelecionado;
    }

    /**
     * @return the entidadeSelecionada
     */
    public String getEntidadeSelecionada() {
        return entidadeSelecionada;
    }

    /**
     * @param entidadeSelecionada the entidadeSelecionada to set
     */
    public void setEntidadeSelecionada(String entidadeSelecionada) {
        this.entidadeSelecionada = entidadeSelecionada;
    }

    /**
     * @return the acaoSelecionada
     */
    public String getAcaoSelecionada() {
        return acaoSelecionada;
    }

    /**
     * @param acaoSelecionada the acaoSelecionada to set
     */
    public void setAcaoSelecionada(String acaoSelecionada) {
        this.acaoSelecionada = acaoSelecionada;
    }

    /**
     * @return the permissaoModuloSelecionado
     */
    public String getPermissaoModuloSelecionado() {
        return permissaoModuloSelecionado;
    }

    /**
     * @param permissaoModuloSelecionado the permissaoModuloSelecionado to set
     */
    public void setPermissaoModuloSelecionado(String permissaoModuloSelecionado) {
        this.permissaoModuloSelecionado = permissaoModuloSelecionado;
    }
    //------------------------------ INICIO - REFACTOR DA VIEW -----------------------------------------//
    private List<Map<String, Object>> listaPermissoes;
    private List<PerfilAcessoVO> perfis;
    private Boolean marcarTudo;
    private Boolean marcarTudoOperacoesCE;
    private Boolean marcarTudoFuncionalidadesCE;
    private List<Autorizacao> entidadesAutorizacoes = new ArrayList<Autorizacao>();
    private List<Autorizacao> funcionalidades = new ArrayList<Autorizacao>();
    private Integer codigoEntidade;
    private String codigoOperacao;
    private String operacoesTela;

    /**
     * 
     * <AUTHOR>
     * @return lista com mapa de modulos e entidades
     */
    public List<Map<String, Object>> inicializaListaPermissoes() {
        try {
            this.setListaPermissoes(new ArrayList<Map<String, Object>>());
            if(isAdicionarHintsWiki()) {
                hintsPermissoes = getFacade().getWikiManager().obterHintsPerfilAcesso();
            }
            //percorre os modulos do zillyon
            for (String modulo : getListaModulos()) {
                Map<String, Object> mapa = new HashMap<String, Object>();
                //insere o nome do modulo no mapa
                mapa.put("modulo", modulo);
                //insere a lista de entidades do modulo no mapa
                mapa.put("entidades", montaPermissao(getListaEntidades(modulo)));
                mapa.put("acoes", montaPermissao(getListaAcoes(modulo)));
                mapa.put("selecionarTodoEntidade", Boolean.FALSE);
                mapa.put("selecionarTodoAcao", Boolean.FALSE);
                mapa.put("expandir", Boolean.FALSE);
                //adiciona na lista de permissões
                contarPermissoes(mapa);
                this.getListaPermissoes().add(mapa);
            }
            return this.getListaPermissoes();
        }catch (Exception ex){
            montarErro(ex);
        }
        return null;
    }
    public List<Map<String, Object>> inicializaListaGrupo(String modulo) {
        List<Map<String, Object>> mapa = new ArrayList<>();


//        mapa.put("entidades", montaPermissao(getListaEntidades(modulo)));
//        mapa.put("acoes", montaPermissao(getListaAcoes(modulo)));
        return mapa;
    }

    /**
     * Monta uma lista com as entidades dos módulos do zillyon
     * <AUTHOR> 
     * @return lista com entidades do modulo
     */
    @SuppressWarnings("unchecked")
    public List<OpcaoPerfilAcesso> getListaEntidades(String nomeModulo) {
        //recuperar modulos
        Hashtable modulos = OpcoesPerfilAcesso.getAplicacao();
        //recuperar entidades
        Hashtable entidades = (Hashtable) modulos.get(nomeModulo);
        //enumeration com chaves da tabela de entidades
        Enumeration<String> keysEntidades = entidades.keys();
        List<OpcaoPerfilAcesso> listaEntidades = new ArrayList<OpcaoPerfilAcesso>();
        while (keysEntidades.hasMoreElements()) {
            String chave = (String) keysEntidades.nextElement();
            OpcaoPerfilAcesso opcao = (OpcaoPerfilAcesso) entidades.get(chave);
            if (opcao.getTipo() == OpcaoPerfilAcesso.TP_ENTIDADE) {
                listaEntidades.add(opcao);
            }
        }
        return listaEntidades;

    }
    public void adicionarHint(PermissaoVO perm,HashMap map){
            if (map != null) {
                String codigo = perm.getTituloApresentacao().split("-")[0].trim();
                perm.setHint((String)map.get(codigo));
            }
    }
    public List<OpcaoPerfilAcesso> getListaAcoes(String nomeModulo) {
        //recuperar modulos
        Hashtable modulos =  OpcoesPerfilAcesso.getAplicacao();
        //recuperar entidades
        Hashtable entidades = (Hashtable) modulos.get(nomeModulo);
        //enumeration com chaves da tabela de entidades
        Enumeration<String> keysEntidades = entidades.keys();
        List<OpcaoPerfilAcesso> listaEntidades = new ArrayList<OpcaoPerfilAcesso>();
        while (keysEntidades.hasMoreElements()) {
            String chave = (String) keysEntidades.nextElement();
            OpcaoPerfilAcesso opcao = (OpcaoPerfilAcesso) entidades.get(chave);
            if (opcao.getTipo() == OpcaoPerfilAcesso.TP_FUNCIONALIDADE
                    || opcao.getTipo() == OpcaoPerfilAcesso.TP_FUNCIONALIDADE_FINANCEIRO || opcao.getTipo() == OpcaoPerfilAcesso.TP_FUNCIONALIDADE_ESTOQUE) {
                listaEntidades.add(opcao);
            }
        }
        return listaEntidades;

    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    /**
     * Comparator para ordenar a lista de entidades
     * <AUTHOR>
     *
     */
    class PermissaoOrdemValor extends BeanComparator {

        private static final long serialVersionUID = -4556056960668754276L;

        @SuppressWarnings("unchecked")
        public PermissaoOrdemValor() {
            super("tituloApresentacao", new Comparator() {

                public int compare(Object o1, Object o2) {
                    return ((String) o1).compareTo((String) o2);
                }
            });
        }
    }

    /**
     * Método Responsavel por agrupar as permissões que são especificas de funcionalidades do ZillyonWeb
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<OpcaoPerfilAcesso> inicializarListaSelectItemTodasAcoesZillyon() {
        List objs = new ArrayList();

        Hashtable modulos = (Hashtable) OpcoesPerfilAcesso.getAplicacao();
        Enumeration e = modulos.elements();
        while (e.hasMoreElements()) {
            Hashtable modulo = (Hashtable) e.nextElement();
            Enumeration i = modulo.elements();
            while (i.hasMoreElements()) {
                OpcaoPerfilAcesso opcao = (OpcaoPerfilAcesso) i.nextElement();
                if (opcao.getTipo() == OpcaoPerfilAcesso.TP_FUNCIONALIDADE) {
                    objs.add(opcao);
                }
            }
        }
        return objs;

    }

    /**
     * Responsavel por montar a lista com opcões de permissoes 
     * @param opcoes
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public List<PermissaoVO> montaPermissao(List<OpcaoPerfilAcesso> opcoes) {
        List<PermissaoVO> permissoes = new ArrayList<PermissaoVO>();
        for (OpcaoPerfilAcesso opcao : opcoes) {
            PermissaoVO permissao = new PermissaoVO();
            if (!getPerfilAcessoVO().getCodigo().equals(new Integer(0))) {
                permissao.setCodPerfilAcesso(getPerfilAcessoVO().getCodigo());
            }
            //relacionar os campos correspondentes
            permissao.setTituloApresentacao(opcao.getTitulo());
            permissao.setNomeEntidade(opcao.getNome());
            permissao.setTipoPermissao(opcao.getTipo());
            permissao.setPermissoes("(0)(1)(2)(3)(9)(12)");
            permissao.setApresentarPermissao(opcao.getApresentarPermissao());
            permissoes.add(permissao);
            if(isAdicionarHintsWiki()){
                adicionarHint(permissao,hintsPermissoes);
            }
        }
        Collections.sort((List) permissoes, new PermissaoOrdemValor());
        return permissoes;
    }

    /**
     * <AUTHOR>
     * @return lista com nomes dos modulos 
     */
    public boolean apresentarCentralEventos(){
        LoginControle lc = (LoginControle) JSFUtilities.getManagedBean("LoginControle");
        if(lc == null){
            return true;
        }
        return lc.isApresentarLinkCE();
    }
    public boolean apresentarEstudio(){
        LoginControle lc = (LoginControle) JSFUtilities.getManagedBean("LoginControle");
        if(lc == null){
            return true;
        }
        return lc.isApresentarLinkEstudio();
    }
    @SuppressWarnings("unchecked")
    public List<String> getListaModulos() {
        //obter módulos
        Hashtable modulos = OpcoesPerfilAcesso.getAplicacao();
        Enumeration keys = modulos.keys();
        List<String> nomesModulos = new ArrayList<String>();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            if(value.equals(OpcoesPerfilAcesso.ZW_CE) && apresentarCentralEventos()) {
                nomesModulos.add(value);
            }else if(value.equals(OpcoesPerfilAcesso.ZW_EST) && apresentarEstudio()) {
                nomesModulos.add(value);
            }  else if(!value.equals(OpcoesPerfilAcesso.ZW_CE) && !value.equals(OpcoesPerfilAcesso.ZW_EST)){
                nomesModulos.add(value);
            }
        }
        //ordenar lista
        Collections.sort(nomesModulos);
        return nomesModulos;
    }

    /**
     * Responsavel por adicionar ao objeto que será salvo uma lista com permissões selecionadas pelo usuario
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    private void montaPermissoesSalvar() {
        perfilAcessoVO.setPermissaoVOs(new ArrayList());
        for (Map<String, Object> mapa : listaPermissoes) {
            //obter lista de permissoes
            List<PermissaoVO> entidades = (List<PermissaoVO>) mapa.get("entidades");
            //percorrer entidadees
            for (PermissaoVO entidade : entidades) {
                //verificar se foi selecionada a entidade
                if (entidade.getSelecionado()) {
                    //adicionar a lista das que serão salvas
                    perfilAcessoVO.getPermissaoVOs().add(entidade);
                }
            }
            for (PermissaoVO entidade : (List<PermissaoVO>) mapa.get("acoes")) {
                //verificar se foi selecionada a entidade
                if (entidade.getSelecionado()) {
                    //adicionar a lista das que serão salvas
                    perfilAcessoVO.getPermissaoVOs().add(entidade);
                }
            }

            // validação da chave "ZW ADM"
            if ("ZW ADM".equals(mapa.get("modulo"))) {
                //Esse recurso era um permissão que era livre de seleção e agora se tornou fixa para todos os usuário.
                PermissaoVO permissao = new PermissaoVO();
                permissao.setCodPerfilAcesso(1);
                permissao.setNomeEntidade("OperadoraCartao");
                permissao.setPermissoes("(0)(1)(2)(9)(12)");
                permissao.setTituloApresentacao("4.15 - Operadora de Cartão");
                permissao.setTipoPermissao(OpcaoPerfilAcesso.TP_ENTIDADE);
                permissao.setSelecionado(true);
                permissao.setApresentarPermissao("4.15 - Operadora de Cartão");
                permissao.setNovoObj(true);
                permissao.setValidarDados(true);

                perfilAcessoVO.getPermissaoVOs().add(permissao);
            }
        }
    }

    /**
     * Metodo responsavel por carregar um perfil de acesso e montar a tela de edição de perfil
     * @param perfilAcessoVO
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    private void carregarPerfilAcesso(PerfilAcessoVO perfilAcessoVO) throws InstantiationException, IllegalAccessException {
        carregarPermissoesEditar(perfilAcessoVO.getPermissaoVOs());
        perfilAcessoVOClone = (PerfilAcessoVO) perfilAcessoVO.getClone(false);
    }
    public void carregarPermissoesEditar(List<PermissaoVO> permissoes){
        //percorrer as permissões do perfil carregado
        for (Object obj : permissoes) {
            PermissaoVO permissao = (PermissaoVO) obj;
            for (Map<String, Object> mapa : listaPermissoes) {
                // obter lista geral de permissoes
                List<PermissaoVO> entidades = (List<PermissaoVO>) mapa.get("entidades");
                // percorrer entidadees
                for (PermissaoVO entidade : entidades) {
                    //se a permissão está incluida na lista do perfil carregado, marcar como selecionada
                    if (entidade.getNomeEntidade().equals(permissao.getNomeEntidade())) {
                        entidade.setPermissoes(permissao.getPermissoes());
                        entidade.setSelecionado(Boolean.TRUE);
                        break;
                    }
                }
                for (PermissaoVO entidade : (List<PermissaoVO>)mapa.get("acoes")) {
                    //se a permissão está incluida na lista do perfil carregado, marcar como selecionada
                    if (entidade.getNomeEntidade().equals(permissao.getNomeEntidade())) {
                        entidade.setPermissoes(permissao.getPermissoes());
                        entidade.setSelecionado(Boolean.TRUE);
                        break;
                    }
                }
                contarPermissoes(mapa);
            }
        }
    }
    public void aplicarPermissoesPadraoTipo(){
        try {
            inicializaListaPermissoes();
            if(getPerfilAcessoVO() != null && !UteisValidacao.emptyNumber(getPerfilAcessoVO().getTipo().getId())) {
                if (getPerfilAcessoVO().getTipo() == PerfilUsuarioEnum.CONSULTOR) {
                    carregarPermissoesEditar(PovoadorDadosBancoInicial.obterPermissoesConsultor());
                }else if(getPerfilAcessoVO().getTipo() == PerfilUsuarioEnum.PROFESSOR){
                    carregarPermissoesEditar(PovoadorDadosBancoInicial.obterPermissoesProfessor());
                }else if(getPerfilAcessoVO().getTipo() == PerfilUsuarioEnum.GERENTE){
                    carregarPermissoesEditar(PovoadorDadosBancoInicial.obterPermissoesGerente());
                }else if(getPerfilAcessoVO().getTipo() == PerfilUsuarioEnum.ADMINISTRADOR){
                    carregarPermissoesEditar(PovoadorDadosBancoInicial.obterPermissoesAdiministrador());
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }
    /**
     * Método responsável pela ação de selecionar/desmarcar todas as entidades de um módulo
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public void selecionarPermissao() {
        Map<String, Object> permissoes = (Map<String, Object>) context().getExternalContext().getRequestMap().get("permissoes");
        contarPermissoes(permissoes);
    }
    public void contarPermissoes(Map<String, Object> permissoes) {

        List<PermissaoVO> cadastros = (List<PermissaoVO>) permissoes.get("entidades");
        List<PermissaoVO> acoes = (List<PermissaoVO>) permissoes.get("acoes");
        int countEntidade = 0;
        int countAcoes = 0;
        for(PermissaoVO permissao : cadastros){
            if(permissao.getSelecionado()){
                countEntidade ++;
            }
        }
        for(PermissaoVO permissao : acoes){
            if(permissao.getSelecionado()){
                permissao.setPermissoes("(0)(1)(2)(3)(9)(12)");
                countAcoes ++;
            }
        }
        permissoes.put("countEntidade",countEntidade);
        permissoes.put("countAcao",countAcoes);
    }
    public void selecionarTudoEntidade() {
        //obter o módulo clicado
        Map<String, Object> permissoes = (Map<String, Object>) context().getExternalContext().getRequestMap().get("permissoes");
        //obter a lista das entidades do módulo
        List<PermissaoVO> cadastros = (List<PermissaoVO>) permissoes.get("entidades");
        //se o objeto foi marcado
        selecionarPermissoes(cadastros,(Boolean) permissoes.get("selecionarTodoEntidade"));
        contarPermissoes(permissoes);
    }
    public void selecionarTudoFuncionalidade() {
        //obter o módulo clicado
        Map<String, Object> permissoes = (Map<String, Object>) context().getExternalContext().getRequestMap().get("permissoes");
        //obter a lista das entidades do módulo
        List<PermissaoVO> acoes = (List<PermissaoVO>) permissoes.get("acoes");
        //se o objeto foi marcado
        selecionarPermissoes(acoes,(Boolean) permissoes.get("selecionarTodoAcao"));
        contarPermissoes(permissoes);
    }

    public void selecionarPermissoes(List<PermissaoVO> entidades,boolean selecionarTodos){
        if (selecionarTodos) {
            //percorrer a lista de entidades marcando as entidades e dando permissão total
            for (PermissaoVO entidade : entidades) {
                entidade.setSelecionado(Boolean.TRUE);
                entidade.setPermissoes("(0)(1)(2)(3)(9)(12)");
            }
            //se foi desmarcado
        } else {
            //percorrer a lista de entidades desmarcando as entidades
            for (PermissaoVO entidade : entidades) {
                entidade.setSelecionado(Boolean.FALSE);
                entidade.setPermissoes("");
            }
        }
    }
    /**
     * Método responsável pela ação de selecionar/desmarcar todas as entidades
     *
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public void selecionarTudo() {

        if (this.getMarcarTudo()) {
            // percorrer a lista de entidades marcando as entidades e dando permissão total
            for (Map<String, Object> mapa : this.getListaPermissoes()) {
                List<PermissaoVO> entidades = (List<PermissaoVO>) mapa.get("entidades");
                for (PermissaoVO entidade : entidades) {
                    entidade.setSelecionado(Boolean.TRUE);
                    entidade.setPermissoes("(0)(1)(2)(3)(9)(12)");
                }


            }
            selecionarTodasOperacoes();
            selecionarTodasFuncionalidades();
            // se foi desmarcado
        } else {
            // percorrer a lista de entidades desmarcando as entidades
            for (Map<String, Object> mapa : this.getListaPermissoes()) {
                List<PermissaoVO> entidades = (List<PermissaoVO>) mapa.get("entidades");
                for (PermissaoVO entidade : entidades) {
                    entidade.setSelecionado(Boolean.FALSE);
                    if (entidade.getTipoPermissao() == OpcaoPerfilAcesso.TP_ENTIDADE) {
                        entidade.setPermissoes("");
                    }
                }
            }
            desmarcarTodasOperacoes();
            desmarcarTodasFuncionalidades();
        }
    }

    /**
     * Responsável por
     * <AUTHOR>
     * 05/04/2011
     */
    private void desmarcarTodasFuncionalidades() {
        //DESMARCAR todas as funcionalidades
        for (Autorizacao autorizacao : funcionalidades) {
            autorizacao.setSelecionado(false);
        }
    }

    /**
     * Responsável por
     * <AUTHOR>
     * 05/04/2011
     */
    private void desmarcarTodasOperacoes() {
        //DESMARCAR todas as operacoes nas entidades
        for (Autorizacao autorizacao : entidadesAutorizacoes) {
            autorizacao.setOperacao("");
        }
        setOperacoesTela("");
    }

    /**
     * Responsável por
     * <AUTHOR>
     * 05/04/2011
     */
    private void selecionarTodasFuncionalidades() {
        //selecionar todas as funcionalidades
        for (Autorizacao autorizacao : funcionalidades) {
            autorizacao.setSelecionado(true);
        }
    }

    /**
     * Responsável por
     * <AUTHOR>
     * 05/04/2011
     */
    private void selecionarTodasOperacoes() {
        String todasOperacoes = "";
        //obter todas possiveis operacoes
        for (Operacao op : Operacao.values()) {
            todasOperacoes += op.getCodigo();
        }
        //setar todas as operacoes nas entidades
        for (Autorizacao autorizacao : entidadesAutorizacoes) {
            autorizacao.setOperacao(todasOperacoes);

        }
        carregaOperacaoTela();
    }

    /**
     * Pesquisar perfis de acesso para copiar permissões
     * <AUTHOR>
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public void consultarPerfilCopiar() throws Exception {
        this.setPerfis(getFacade().getPerfilAcesso().consultarPorParteDoNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS));
    }

    /**
     * Seleciona o perfil que será fonte da copia das permissões
     * <AUTHOR>
     */
    public void selecionarPerfilCopiar() throws IllegalAccessException, InstantiationException {
        //obter o perfil
        PerfilAcessoVO obj = (PerfilAcessoVO) context().getExternalContext().getRequestMap().get("perfil");
        //fazer a copia das permissões
        this.carregarPerfilAcesso(obj);
        setPerfis(new ArrayList<PerfilAcessoVO>());
        this.getControleConsulta().setValorConsulta("");
    }
    public void abrirModalCopiarPerfil(){
        try {
            getControleConsulta().setValorConsulta("");
        }catch (Exception ex){
            montarErro(ex);
        }
    }

    /**
     * Responsavel por abrir os paineis de modulo.
     * <AUTHOR>
     */
    public void expandirModulos() {
        this.alterarPaineis(Boolean.TRUE);
    }

    /**
     * Responsavel por retrair os paineis de modulo.
     * <AUTHOR>
     */
    public void retrairModulos() {
        this.alterarPaineis(Boolean.FALSE);
    }

    /**
     * Percorre a lista de permissoes setando o campo expandir com o valor passado como parametro
     * @param controle
     */
    private void alterarPaineis(Boolean controle) {
        for (Map<String, Object> mapa : this.getListaPermissoes()) {
            mapa.put("expandir", controle);
        }

        expandirRetrairEntidades = controle;
        expandirRetrairFuncionalidade = controle;
    }

    /**
     * Responsável por montar uma lista com as autorizacoes das entidades
     * <AUTHOR>
     * 23/03/2011
     * @return
     */
    public List<Autorizacao> getEntidadesAutorizacoes() {
        if (entidadesAutorizacoes.isEmpty()) {
            for (Entidade entidade : Entidade.values()) {
                Autorizacao autorizacao = new Autorizacao();
                autorizacao.setEntidade(entidade.getCodigo());
                autorizacao.setDescricao(entidade.getDescricao());
                entidadesAutorizacoes.add(autorizacao);
            }
        }

        return entidadesAutorizacoes;

    }

    /**
     * Responsável por montar uma lista com as funcionalidades do CE 
     * <AUTHOR>
     * 23/03/2011
     * @return
     */
    public List<Autorizacao> getFuncionalidades() {
        if (funcionalidades.isEmpty()) {
            for (Funcionalidade funcionalidade : Funcionalidade.values()) {
                Autorizacao autorizacao = new Autorizacao();
                autorizacao.setFuncionalidade(funcionalidade.getCodigo());
                autorizacao.setDescricao(funcionalidade.getDescricao());
                funcionalidades.add(autorizacao);
            }
        }

        return funcionalidades;
    }

    /**
     * Responsável por setar adicionar na entidade marcada a operação escolhida pelo usuário
     * <AUTHOR>
     * 23/03/2011
     */
    public void setaOperacao() {
        for (Autorizacao autorizacao : this.entidadesAutorizacoes) {
            String permissoes = "";
            //percorrer a superstring com as permissoes
            while (this.getOperacoesTela().contains("" + autorizacao.getEntidade())) {
                int position = this.getOperacoesTela().indexOf("" + autorizacao.getEntidade()) + 3;
                permissoes += this.getOperacoesTela().substring(position, position + 1);
                this.setOperacoesTela(this.getOperacoesTela().replaceFirst("" + autorizacao.getEntidade(), ""));
            }
            autorizacao.setOperacao(permissoes);
        }
    }

    /**
     * Responsável por carregar na string que armazena as operacoes as permissoes das entidades 
     * <AUTHOR>
     * 04/04/2011
     */
    public void carregaOperacaoTela() {
        this.setOperacoesTela("");
        //percorrer entidades
        for (Autorizacao autorizacao : this.entidadesAutorizacoes) {
            //percorrer permissoes de operacoes
            for (int x = 0; x < autorizacao.getOperacao().trim().length(); x++) {
                this.setOperacoesTela(this.getOperacoesTela() + autorizacao.getEntidade() + autorizacao.getOperacao().charAt(x));
            }
        }
    }

    /**
     * Responsável por marcar ou desmarcar todas as operacoes do ce 
     * <AUTHOR>
     * 05/04/2011
     */
    public void selecionarTudoOperacoesCE() {
        if (this.getMarcarTudoOperacoesCE()) {
            this.selecionarTodasOperacoes();
        } else {
            this.desmarcarTodasOperacoes();
        }
    }

    /**
     * Responsável por marcar ou desmarcar todas as funcionalidades do ce 
     * <AUTHOR>
     * 05/04/2011
     */
    public void selecionarTudoFuncionalidadesCE() {
        if (this.getMarcarTudoFuncionalidadesCE()) {
            this.selecionarTodasFuncionalidades();
        } else {
            this.desmarcarTodasFuncionalidades();
        }
    }

    //------------------------------ FIM - REFACTOR DA VIEW -----------------------------------------//
    public Operacao[] getOperacoes() {
        return Operacao.values();
    }

    /**
     * @param listaPermissoes the listaPermissoes to set
     */
    public void setListaPermissoes(List<Map<String, Object>> listaPermissoes) {
        this.listaPermissoes = listaPermissoes;
    }

    /**
     * @return the listaPermissoes
     */
    public List<Map<String, Object>> getListaPermissoes() {
        if (listaPermissoes == null) {
            listaPermissoes = new ArrayList<Map<String, Object>>();
        }
        return listaPermissoes;
    }

    /**
     * @return the perfis
     */
    public List<PerfilAcessoVO> getPerfis() {
        return perfis;
    }

    /**
     * @param perfis the perfis to set
     */
    public void setPerfis(List<PerfilAcessoVO> perfis) {
        this.perfis = perfis;
    }

    /**
     * @param marcarTudo the marcarTudo to set
     */
    public void setMarcarTudo(Boolean marcarTudo) {
        this.marcarTudo = marcarTudo;
    }

    /**
     * @return the marcarTudo
     */
    public Boolean getMarcarTudo() {
        if (marcarTudo == null) {
            marcarTudo = Boolean.FALSE;
        }
        return marcarTudo;
    }

    /**
     * @param codigoOperacao the codigoOperacao to set
     */
    public void setCodigoOperacao(String codigoOperacao) {
        this.codigoOperacao = codigoOperacao;
    }

    /**
     * @return the codigoOperacao
     */
    public String getCodigoOperacao() {
        return codigoOperacao;
    }

    /**
     * @param codigoEntidade the codigoEntidade to set
     */
    public void setCodigoEntidade(Integer codigoEntidade) {
        this.codigoEntidade = codigoEntidade;
    }

    /**
     * @return the codigoEntidade
     */
    public Integer getCodigoEntidade() {
        return codigoEntidade;
    }

    /**
     * @return O campo expandirRetrairEntidades.
     */
    public boolean isExpandirRetrairEntidades() {
        return this.expandirRetrairEntidades;
    }

    /**
     * @param expandirRetrairEntidades O novo valor de expandirRetrairEntidades.
     */
    public void setExpandirRetrairEntidades(boolean expandirRetrairEntidades) {
        this.expandirRetrairEntidades = expandirRetrairEntidades;
    }

    /**
     * @return O campo expandirRetrairFuncionalidade.
     */
    public boolean isExpandirRetrairFuncionalidade() {
        return this.expandirRetrairFuncionalidade;
    }

    /**
     * @param expandirRetrairFuncionalidade O novo valor de expandirRetrairFuncionalidade.
     */
    public void setExpandirRetrairFuncionalidade(boolean expandirRetrairFuncionalidade) {
        this.expandirRetrairFuncionalidade = expandirRetrairFuncionalidade;
    }

    /**
     * @param operacoesTela the operacoesTela to set
     */
    public void setOperacoesTela(String operacoesTela) {
        this.operacoesTela = operacoesTela;
    }

    /**
     * @return the operacoesTela
     */
    public String getOperacoesTela() {
        if (operacoesTela == null) {
            operacoesTela = "";
        }
        return operacoesTela;
    }

    /**
     * @param marcarTudoFuncionalidadesCE the marcarTudoFuncionalidadesCE to set
     */
    public void setMarcarTudoFuncionalidadesCE(Boolean marcarTudoFuncionalidadesCE) {
        this.marcarTudoFuncionalidadesCE = marcarTudoFuncionalidadesCE;
    }

    /**
     * @return the marcarTudoFuncionalidadesCE
     */
    public Boolean getMarcarTudoFuncionalidadesCE() {
        return marcarTudoFuncionalidadesCE;
    }

    /**
     * @param marcarTudoOperacoesCE the marcarTudoOperacoesCE to set
     */
    public void setMarcarTudoOperacoesCE(Boolean marcarTudoOperacoesCE) {
        this.marcarTudoOperacoesCE = marcarTudoOperacoesCE;
    }

    /**
     * @return the marcarTudoOperacoesCE
     */
    public Boolean getMarcarTudoOperacoesCE() {
        return marcarTudoOperacoesCE;
    }
    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPerfilAcesso().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void exportarUsuariosPerfil(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "").replace("<span>", "").replace("</span>", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");

        List listaParaImpressao = getFacade().getPerfilAcesso().consultarUsuariosDoPerfil(perfilAcessoVO.getCodigo(), filtro, ordem, campoOrdenacao, getSituacaoFiltro());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public List<SelectItem> getTipos() {
           return PerfilUsuarioEnum.getSelectListPerfilUsuarioEnum();
    }

    public void setTipos(List<SelectItem> tipos) {
        this.tipos = tipos;
    }

    public void realizarConsultaLogObjetoSelecionado() throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "PERFIL ACESSO";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        List lista = getFacade().getLog().consultarPorNomeCodigoEntidadeAgrupado(nomeClasse.toUpperCase(), perfilAcessoVO.getCodigo(), null, null, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);
        loginControle.setListaConsultaLog(lista);
    }
    
    public void realizarConsultaLogObjetoGeral() throws Exception {
        perfilAcessoVO = new PerfilAcessoVO();
        realizarConsultaLogObjetoSelecionado();
    }
    

    public void registraLogAlteracao() throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(perfilAcessoVO.getCodigo().toString());
            obj.setNomeEntidade("PERFIL ACESSO");
            obj.setNomeEntidadeDescricao("PERFIL ACESSO");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");
            
            String alteracoes = ""; 
            if(!perfilAcessoVO.getNome().equals(((PerfilAcessoVO) perfilAcessoVO.getObjetoVOAntesAlteracao()).getNome())){
                alteracoes +=  "NOME PERFIL: " +((PerfilAcessoVO) perfilAcessoVO.getObjetoVOAntesAlteracao()).getNome();
            }
            if( perfilAcessoVO.getTipo() != null && !perfilAcessoVO.getTipo().equals(((PerfilAcessoVO) perfilAcessoVO.getObjetoVOAntesAlteracao()).getTipo())){
                alteracoes +=  "\nTIPO PERFIL: " + perfilAcessoVO.getTipo().toString();
            }else {
                alteracoes +=  "\nTIPO PERFIL: ";
            }
            if(!UteisValidacao.emptyString(alteracoes)){
                obj.setValorCampoAnterior(alteracoes);
            }
            List<PermissaoVO> permissoesAnt = ((PerfilAcessoVO) perfilAcessoVO.getObjetoVOAntesAlteracao()).getPermissaoVOs();

            List<String> permissoesAlteradas = new ArrayList<String>();
            List<String> permissoesExcluidas = new ArrayList<String>();
            List<String> permissoesAdicionadas = new ArrayList<String>();

            for (PermissaoVO permissaoAnt : permissoesAnt) {
                boolean existe = false;

                for (PermissaoVO permissao : perfilAcessoVO.getPermissaoVOs()) {
                    if (permissaoAnt.getNomeEntidade().equals(permissao.getNomeEntidade())) {
                        existe = true;
                        permissao.setVerificado(true);
                        if (!permissaoAnt.getPermissoes_Apresentar().equals(permissao.getPermissoes_Apresentar())) {
                            permissoesAlteradas.add(permissao.getTituloApresentacao() + " -- NIVEL PERMISSAO: " + permissao.getPermissoes_Apresentar());
                            break;
                        }
                    }
                }

                if (!existe) {
                    permissoesExcluidas.add(permissaoAnt.getTituloApresentacao() + " -- NIVEL PERMISSAO: " + permissaoAnt.getPermissoes_Apresentar());
                }
            }

            for (PermissaoVO permissaoAtu : perfilAcessoVO.getPermissaoVOs()) {
                if (!permissaoAtu.isVerificado()) {
                    permissoesAdicionadas.add(permissaoAtu.getTituloApresentacao() + " -- NIVEL PERMISSAO: " + permissaoAtu.getPermissoes_Apresentar());
                }
                permissaoAtu.setVerificado(false);
            }

            String str = "CODIGO PERFIL: " + perfilAcessoVO.getCodigo().toString()
                    + "\nNOME PERFIL: " + perfilAcessoVO.getNome()
                    + "\nTIPO PERFIL: " + (perfilAcessoVO.getTipo() != null ? perfilAcessoVO.getTipo().toString() : "")
                    + "\n";

            String alteradas = listaAlteracaoLog(permissoesAlteradas);
            String excluidas = listaAlteracaoLog(permissoesExcluidas);
            String adicionadas = listaAlteracaoLog(permissoesAdicionadas);
            if(UteisValidacao.emptyString(alteracoes) && UteisValidacao.emptyString(alteradas) && UteisValidacao.emptyString(excluidas) && UteisValidacao.emptyString(adicionadas)){
                return;
            }
            if (!alteradas.isEmpty()){
                str += "PERMISSÕES ALTERADAS: \n" + alteradas + "\n";
            }
            if (!excluidas.isEmpty()){
                str += "PERMISSÕES EXCLUIDAS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()){
                str += "PERMISSÕES ADICIONADAS: \n" + adicionadas + "\n";
            }

            obj.setValorCampoAlterado(str);
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            registrarLogObjetoVO(obj, perfilAcessoVO.getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERFIL ACESSO", perfilAcessoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PERFIL ACESSO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
    
    public void registraLogInclusao() throws Exception{
        //LOG - INICIO
        try {
            List<LogVO> logs = new ArrayList<LogVO>();
            LogVO objBase = new LogVO();
            objBase.setChavePrimaria(perfilAcessoVO.getCodigo().toString());
            objBase.setNomeEntidade("PERFIL ACESSO");
            objBase.setNomeEntidadeDescricao("PERFIL ACESSO");
            objBase.setOperacao("INCLUSÃO");
            objBase.setResponsavelAlteracao(getUsuarioLogado().getNome());
            objBase.setUserOAMD(getUsuarioLogado().getUserOamd());
            objBase.setNomeCampo("MENSAGEM");
            objBase.setValorCampoAlterado("");
            objBase.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            List<PermissaoVO> permissoes = getFacade().getPermissao().consultarPermissaos(perfilAcessoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            StringBuffer logPermissoes = new StringBuffer();
            logPermissoes.append("CODIGO PERFIL: " + perfilAcessoVO.getCodigo().toString() +  "\nNOME PERFIL: " + perfilAcessoVO.getNome() +  "\nTIPO PERFIL: " + perfilAcessoVO.getTipo().toString()  + "\nPERMISSÕES: \n");
            Iterator i = permissoes.iterator();
            while (i.hasNext()) {
                PermissaoVO objetos = (PermissaoVO) i.next();
                if(logPermissoes.length() > 5000){ // campo é indexado em banco, e essa indexação tem limitação no tamanho do texto
                    LogVO logVO = (LogVO) objBase.getClone(true);
                    logVO.setValorCampoAlterado(logPermissoes.toString());
                    logs.add(logVO);
                    logPermissoes = new StringBuffer("CODIGO PERFIL: " + perfilAcessoVO.getCodigo().toString() +  "\nNOME PERFIL: " + perfilAcessoVO.getNome() +  "\nTIPO PERFIL: " + perfilAcessoVO.getTipo().toString()  + "\nPERMISSÕES: \n");
                } else {
                    logPermissoes.append(objetos.getTituloApresentacao() + " -- NIVEL PERMISSÃO: "  + objetos.getPermissoes_Apresentar() + "\n");
                }
            }
            LogVO logVO = (LogVO) objBase.getClone(true);
            logVO.setValorCampoAlterado(logPermissoes.toString());
            logs.add(logVO);
            registrarLogObjetoVO(logs, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERFIL ACESSO", perfilAcessoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PERFIL ACESSO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaPerfisAcessoRedeEmpresa().clear();

            Map<String, PerfilAcessoRedeEmpresaVO> mapaUnidadesPorChave = new HashMap<>();

            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }
                PerfilAcessoRedeEmpresaVO perfilAcessoRedeEmpresaVO = getFacade().getPerfilAcessoRedeEmpresa().consultarPorChavePerfilAcesso(redeDTO.getChave(), perfilAcessoVO.getCodigo());
                if (perfilAcessoRedeEmpresaVO == null) {
                    perfilAcessoRedeEmpresaVO = new PerfilAcessoRedeEmpresaVO();
                    perfilAcessoRedeEmpresaVO.setMensagemSituacao("AGUARDANDO REPLICAR PERFIL DE ACESSO");
                }
                perfilAcessoRedeEmpresaVO.setChaveDestino(redeDTO.getChave().toLowerCase().trim());
                perfilAcessoRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                perfilAcessoRedeEmpresaVO.setRedeDTO(redeDTO);
                mapaUnidadesPorChave.put(perfilAcessoRedeEmpresaVO.getChaveDestino(), perfilAcessoRedeEmpresaVO);
            }
            getListaPerfisAcessoRedeEmpresa().addAll(mapaUnidadesPorChave.values());
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarAutomaticoTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PerfilAcessoRedeEmpresaVO obj : getListaPerfisAcessoRedeEmpresa()) {
                if (obj.getDataAtualizacaoInformada()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PERFIL_ACESSO, null, null, obj, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PerfilAcessoRedeEmpresaVO obj : getListaPerfisAcessoRedeEmpresa()) {
                if (!obj.getDataAtualizacaoInformada()) {
                    obj.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PERFIL_ACESSO, null, null, obj, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PerfilAcessoRedeEmpresaVO obj : getListaPerfisAcessoRedeEmpresa()) {
                if (obj.isSelecionado()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PERFIL_ACESSO, null, null, obj, null, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(PerfilAcessoRedeEmpresaVO obj : getListaPerfisAcessoRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public void replicarUsuarioRedeEmpresaGeral() {
        PerfilAcessoRedeEmpresaVO obj = (PerfilAcessoRedeEmpresaVO) context().getExternalContext().getRequestMap().get("perfilAcessoRedeEmpresaReplicacao");
        replicarPerfilAcessoRedeEmpresaUnica(obj);
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        PerfilAcessoRedeEmpresaVO obj = (PerfilAcessoRedeEmpresaVO) context().getExternalContext().getRequestMap().get("perfilAcessoRedeEmpresaReplicacao");
        try {
            obj.setDataatualizacao(null);
            obj.setMensagemSituacao("AGUARDANDO REPLICAR PERFIL ACESSO");
            getFacade().getPerfilAcessoRedeEmpresa().limparDataAtualizacao(perfilAcessoVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getPerfilAcessoRedeEmpresa().alterarMensagemSituacao(perfilAcessoVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao());

        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public Integer getListaPerfisAcessoRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (PerfilAcessoRedeEmpresaVO unid : getListaPerfisAcessoRedeEmpresa()) {
            if (unid.getDataAtualizacaoInformada()) {
                cont++;
            }
        }
        return cont;
    }

    public Integer getListaPerfisAcessoRedeEmpresaSize() {
        return getListaPerfisAcessoRedeEmpresa().size();
    }

    public boolean isExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean permiteReplicarRedeEmpresa = false;
        boolean usuarioAdministrador = false;
        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }
        try {
            integranteFranqueadoraRedeEmpresa = OamdMsService.integranteFranqueadoraRedeEmpresa(getKey());
            permiteReplicarRedeEmpresa = getFacade().getConfiguracaoSistema().obterReplicarRedeEmpresa("permitirReplicarPerfilAcessoRedeEmpresa");
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (integranteFranqueadoraRedeEmpresa && !perfilAcessoVO.isNovoObj() && permiteReplicarRedeEmpresa && (usuarioAdminPacto || usuarioAdministrador)) {
            return true;
        } else {
            return false;
        }
    }

    public void replicarPerfilAcessoRedeEmpresaUnica(PerfilAcessoRedeEmpresaVO obj) {
        try {
            replicarPerfilAcessoRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());
            try {
                getFacade().getPerfilAcessoRedeEmpresa().alterarMensagemSituacao(perfilAcessoVO.getCodigo(), obj.getChaveOrigem(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao());
            } catch (Exception e) {
            }
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarPerfilAcessoRedeEmpresa(PerfilAcessoRedeEmpresaVO obj) throws Exception {
        PerfilAcessoRedeEmpresaVO perfilAcessoRedeEmpresaVO = getFacade().getPerfilAcessoRedeEmpresa().consultarPorChavePerfilAcesso(obj.getChaveDestino(), perfilAcessoVO.getCodigo());
        if (perfilAcessoRedeEmpresaVO == null) {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemAdmCoreUrl = redeEmpresaDataDTO.getServiceUrls().getAdmCoreUrl();

            JSONObject clonePerfilAcessoOrigem = AdmCoreMsService.clonarPerfilAcesso(perfilAcessoVO.getCodigo(), urlOrigemAdmCoreUrl, getKey());
            if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                clonePerfilAcessoOrigem.put("nome", "CÓPIA DE " + clonePerfilAcessoOrigem.getString("nome").toUpperCase().replace("CÓPIA DE ", ""));
            }
            perfilAcessoRedeEmpresaVO = new PerfilAcessoRedeEmpresaVO(perfilAcessoVO.getCodigo(), getKey(), obj.getChaveDestino());
            getFacade().getPerfilAcessoRedeEmpresa().inserir(perfilAcessoRedeEmpresaVO);
            // Perfil não tem na outra academia da rede, então inclui
            JSONObject perfilAcessoReplicado = AdmCoreMsService.replicarPerfilAcesso(clonePerfilAcessoOrigem, obj.getRedeDTO().getAdmCoreMsUrl(), obj.getChaveDestino());
            perfilAcessoRedeEmpresaVO.setChaveDestino(perfilAcessoReplicado.getString("chaveDestino"));
            perfilAcessoRedeEmpresaVO.setDataatualizacao(new Date());
            perfilAcessoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(perfilAcessoRedeEmpresaVO.getDataatualizacao()));
            perfilAcessoRedeEmpresaVO.setPerfilAcessoReplicado(perfilAcessoReplicado.getInt("codigoPerfilAcessoNovo"));
            obj.setChaveDestino(perfilAcessoReplicado.getString("chaveDestino"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getPerfilAcessoRedeEmpresa().alterarDataAtualizacao(perfilAcessoVO.getCodigo(), getKey(), perfilAcessoReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), perfilAcessoReplicado.getInt("codigoPerfilAcessoNovo"));
        } else {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String urlOrigemAdmCoreUrl = redeEmpresaDataDTO.getServiceUrls().getAdmCoreUrl();

            JSONObject cloneFeriadoOrigem = AdmCoreMsService.clonarPerfilAcesso(perfilAcessoVO.getCodigo(), urlOrigemAdmCoreUrl, getKey());
            if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                cloneFeriadoOrigem.put("nome", "CÓPIA DE " + cloneFeriadoOrigem.getString("nome").toUpperCase().replace("CÓPIA DE ", ""));
            }
            JSONObject perfilAcessoReplicado = AdmCoreMsService.replicarPerfilAcesso(cloneFeriadoOrigem, obj.getRedeDTO().getAdmCoreMsUrl(), obj.getChaveDestino());
            perfilAcessoRedeEmpresaVO.setChaveDestino(perfilAcessoReplicado.getString("chaveDestino"));
            perfilAcessoRedeEmpresaVO.setDataatualizacao(new Date());
            perfilAcessoRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(perfilAcessoRedeEmpresaVO.getDataatualizacao()));
            perfilAcessoRedeEmpresaVO.setPerfilAcessoReplicado(perfilAcessoReplicado.getInt("codigoPerfilAcessoNovo"));
            obj.setChaveDestino(perfilAcessoReplicado.getString("chaveDestino"));
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getPerfilAcessoRedeEmpresa().alterarDataAtualizacao(perfilAcessoVO.getCodigo(), getKey(), perfilAcessoReplicado.getString("chaveDestino"), obj.getMensagemSituacao(), perfilAcessoReplicado.getInt("codigoPerfilAcessoNovo"));
        }
    }

    public String listaAlteracaoLog(List<String> lista) throws Exception {
        StringBuilder str = new StringBuilder("");

        for (String mensagem : lista) {
        str.append(mensagem).append("\n");
        }
        return str.toString();
    }

    public String abrirUsuario() {
        try {
            setOnComplete("");
            UsuarioControle usuarioControle = (UsuarioControle) context().getExternalContext().getSessionMap().get(UsuarioControle.class.getSimpleName());
            if (usuarioControle == null) {
                setSucesso(false);
                setErro(true);
                setMensagemDetalhada("msg_erro", "Usuário Não Inicializado.");
                setOnComplete("alert('Usuário Não Inicializado');");
                return "";
            }

            String retorno = usuarioControle.editar();
            if (retorno.equals("editar")) {
                setOnComplete("abrirPopup('usuarioForm.jsp', 'Usuário', 820, 595);");

            } else {
                setOnComplete("alert('" + retorno + "');");
            }

        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public boolean isAdicionarHintsWiki() {
        return adicionarHintsWiki;
    }

    public void setAdicionarHintsWiki(boolean adicionarHintsWiki) {
        this.adicionarHintsWiki = adicionarHintsWiki;
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            perfilAcessoVO.setObjetoVOAntesAlteracao(new PerfilAcessoVO());
            perfilAcessoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(perfilAcessoVO, perfilAcessoVO.getCodigo(), "PERFIL ACESSO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PERFIL ACESSO", perfilAcessoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PERFIL ACESSO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void notificarRecursoEmpresaPerfilAcesso() {
        for (PermissaoVO item : perfilAcessoVO.getPermissaoVOs())
            if (item.getNomeEntidade().equals("ConsultarInfoTodasEmpresas")) {
                if (item.getSelecionado()) {
                    if (!perfilAcessoVOClone.equals(perfilAcessoVO) && !perfilAcessoVOClone.getPermissaoVOs().contains(item)) {
                        notificarRecursoEmpresa(RecursoSistema.PERMISSAO_CONSULTAR_REDE_DE_EMPRESAS_MARCOU);
                    }
                }
            }
        for (PermissaoVO vo : perfilAcessoVO.getPermissaoVOs()) {
            if (vo.getNomeEntidade().equals("ConsultarAlunosCaixaAbertoTodasEmpresas")) {
                if (vo.getSelecionado()) {
                    if (!perfilAcessoVOClone.equals(perfilAcessoVO) && !perfilAcessoVOClone.getPermissaoVOs().contains(vo)) {
                        notificarRecursoEmpresa(RecursoSistema.PERMISSAO_CAIXA_EM_ABERTO_REDE_DE_EMPRESAS_MARCOU);
                    }
                }
            }

        }
    }

    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("TD", "Todos"));
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        return objs;
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro = "AT";
        }
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Perfil de Acesso",
                "Deseja excluir o Perfil de Acesso?",
                this, "excluir", "", "", "", "grupoMensagem");
    }

    public List<PerfilAcessoRedeEmpresaVO> getListaPerfisAcessoRedeEmpresa() {
        if (listaPerfisAcessoRedeEmpresa == null) {
            listaPerfisAcessoRedeEmpresa = new ArrayList<>();
        }
        return listaPerfisAcessoRedeEmpresa;
    }

    public void setListaPerfisAcessoRedeEmpresa(List<PerfilAcessoRedeEmpresaVO> listaPerfisAcessoRedeEmpresa) {
        this.listaPerfisAcessoRedeEmpresa = listaPerfisAcessoRedeEmpresa;
    }

}
