package controle.arquitetura.security;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.aas.authorization.enumerator.Funcionalidade;
import br.com.pactosolucoes.aas.authorization.rules.AutorizacaoRegras;
import br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin;
import br.com.pactosolucoes.atualizadb.to.AtualizacaoTO;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.SinalizadorEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.enumeradores.TipoQRCodeEnum;
import br.com.pactosolucoes.estudio.modelo.ConfiguracaoEstudioVO;
import br.com.pactosolucoes.integracao.pactopay.front.FacilitePayConfigDTO;
import br.com.pactosolucoes.oamd.controle.basico.InicioControle;
import br.com.pactosolucoes.premium.ResultadoVitioJSON;
import br.com.pactosolucoes.premium.RetornoVitioJSON;
import br.com.pactosolucoes.premium.StatusRespostaVitio;
import br.com.pactosolucoes.socialmailing.controle.SocialMailingControle;
import br.com.pactosolucoes.socialmailing.modelo.SocialMailGrupoVO;
import br.com.pactosolucoes.socialmailing.modelo.TipoSocialMailEnum;
import controle.arquitetura.FavoritoEnum;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.RoboControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.servico.RecursoMsService;
import controle.arquitetura.servlet.OIDServlet;
import controle.arquitetura.threads.ThreadAgendamentoFinanceiro;
import controle.basico.CanalPactoControle;
import controle.basico.ClientesMarcadosControle;
import controle.basico.EmbaralhadorControle;
import controle.crm.AberturaMetaControle;
import controle.crm.MetaCRMControle;
import controle.financeiro.CaixaControle;
import controle.nfe.NotaFiscalDeServicoControle;
import controle.notaFiscal.NotaFiscalControle;
import negocio.comuns.arquitetura.DiaRoboVO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.HorarioAcessoSistemaVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.OctadeskUserTO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PerfilUsuarioNFeVO;
import negocio.comuns.arquitetura.PermissaoAcessoMenuVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.CargoEnum;
import negocio.comuns.basico.enumerador.FuncaoEnum;
import negocio.comuns.basico.enumerador.TipoEmpresaFinanceiro;
import negocio.comuns.basico.enumerador.TipoNotificacaoUsuarioEnum;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.nfe.enumerador.TipoNotaEnum;
import negocio.comuns.notaFiscal.StatusEnotasEnum;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ConstantesAcesso;
import negocio.comuns.utilitarias.NotificacaoUsuarioVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SistemaException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.PerfilAcesso;
import negocio.facade.jdbc.basico.FacilitePay;
import negocio.facade.jdbc.basico.webservice.ParametrosLoginDiretoModuloNotasJSON;
import negocio.facade.jdbc.nfe.EmpresaNFe;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.utilitarias.NotificacaoUsuario;
import negocio.interfaces.arquitetura.PerfilAcessoInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.CustomerSuccessTO;
import negocio.oamd.DetalheEmpresaTO;
import negocio.oamd.DiaExtraService;
import negocio.oamd.RedeEmpresaVO;
import negocio.oamd.dto.DiaExtraDTO;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.impl.login.TokenDTO;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.treino.IdiomaBancoEnum;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.Random;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.util.Objects.isNull;

public class LoginControle extends SuperControle {

    public static final String NomeChaveUsuarioSessao = "logado";
    private String username = "";
    private String senha = "";
    private String senha1 = "";
    private String senha2 = "";
    private String senhaAtual = "";
    private String nome = "";
    private String nomeEmpresa = "";
    protected List listaSelectItemEmpresa;
    protected List listTipoColaborador;
    private List listaConsultaLog;
    private LogVO logVO;
    private String nomeClasse;
    private Boolean apresentarBotoesRobo;
    private Boolean apresentarProcessamento;
    private Boolean ativarPoolProcessamento;
    private Boolean ativarPool;
    private Boolean rotinaEmAndamento;
    private String mensagemRobo;
    private Date dataUltimoProcessamentoRobo;
    private Date dataProcessamentoRobo;
    private PermissaoAcessoMenuVO permissaoAcessoMenuVO;
    private Boolean abrirRichModalModulo;
    private Boolean abrirRichModalDiasParaBloqueio;
    private Boolean abrirRichModalSolicitarDesbloqueio;
    private Boolean abrirRichModalBoletosInicio;
    private List<DiaRoboVO> diasSemProcessamento = new ArrayList<DiaRoboVO>();
    private int diasParaBloqueio = -1;
    private String ipCliente = "";
    private boolean mostrarModalExpiracaoSenha = false;
    private long qtdDiasFaltaExpirar = 0;
    private String diasFaltaExpirarFormatado = "";
    private String javaScriptFecharPopUpAlteracaoSenha = "";
    private String tituloMensagemBloqueio = "";
    private String tituloModalBloqueio = "";
    private String infoMensagemBloqueio = "";
    private String mensagemBloqueio = "";
    private boolean verificarExecucaoRoboBackground = false;
    private boolean apresentarIconeExecucaoRoboBackground = false;
    private ModuloAberto moduloAberto;
    private PerfilUsuarioNFeVO perfilNFe;
    private int numDeTentativas = 0;
    private String jspMostrarCRM = "inicioCRM.jsp";
    private boolean apresentarLinkBoletos = false;
    private boolean existemNovosBoletos = false;
    private boolean exibirNotificacaoExpiracaoSistema = false;
    private boolean exibirTrocaModulo = false;
    private String userOamd = "";
    private String permissoesOAMD = null;
    private Boolean validarTreinoLoginApp = true;
    private List<ModuloAberto> modulosHabilitados;
    private String senhaQRCode;
    private String strQRCode;
    private String emailQRCode;
    private List<SelectItem> emailsQRCode;
    private Boolean qrCodeAutorizado = false;
    private Boolean novoEmailQR = false;
    private TipoQRCodeEnum tipoQR = TipoQRCodeEnum.APPGESTOR;
    private UsuarioEmailVO usuarioEmailVO = null;
    private String urlAssinaturaDigitalDireta;
    private Map<String, Boolean> mapaConfigs = new HashMap<String, Boolean>();
    private String msgModalValorNFSeEmitido;
    private String msgModalNotificaRemessas;
    private String msgModalNotificaCertificado;
    private Boolean concederMaisUmDia;
    private Boolean priorizarVendaRapida = false;
    private String codigoColaboradorMovidesk;
    private String codigoEmpresaMovidesk;
    private String codigoEmpresaFinMovidesk;
    private String urlNFSe;
    private boolean gerarLogControleUsabilidadeLogin = true;
    private String msgModalUsuariosAtivos;
    private String msgModalPlanosInativos;
    private List<PlanoVO> listaPlanosProximosInativar;
    private boolean ignorarPlanosInativosHoje;
    private Boolean mostrarBotaoNotaFiscalPerfilCliente;
    private Boolean validarTelaRelatorios;
    private String urlBannerEmergencia = "";
    private String mensagemCliente = "";
    private String telefoneCliente = "";
    private int diasExtras;
    private List<SelectItem> listaDias;
    private boolean abrirRichModalAlertaSistemaExpirado = false;
    private String usaAppTreino = "";
    private String sabeDoCanalDoCliente = "";
    private boolean usouTodosDiasExtrasPermitidos = false;
    private Boolean apresentarPactoStore;
    private String tokenNT = "";
    private String tokenNZW = "";
    private RedeEmpresaVO redeEmpresaVO;
    private CustomerSuccessTO responsavelPacto;

    List<ItemCampanhaJSON> itemsCampanha = new ArrayList<ItemCampanhaJSON>();
    private ConfiguracaoSistemaVO configuracaoSistema;

    private Boolean ignorarValidacaoBloqueio = false;

    private Boolean concordoTermosUsoAnuncio = false;
    private Boolean naoQueroLucrar = false;
    private Boolean apresentarModalAnuncio = false;
    private String motivoRecusa = "";
    private boolean apresentarModalUsuarioGeral = false;
    private UsuarioEmailVO usuarioEmailAlterarVO;
    private TokenDTO tokenDTO;

    private Boolean exibirQrCodeUsuarioChave;

    private Boolean permiteExclusaoClienteUsuarioComum =false;
    private Boolean negociacaoHabilitada = false;

    private String labelVencimentoCertificado = "";
    private UsuarioTreinoDTO usuarioTreinoDTO;
    private String moduloAbertoSigla;
    private String lastJspPage;
    private boolean ignorarHistoricoNavegacaoModulo = false;
    private List<FuncionalidadeSistemaEnum> funcionalidadesInativas;
    private List<FuncionalidadeSistemaEnum> funcionalidadesFavoritos;
    private boolean apresentarMensagensLogin = true;
    private boolean podeLancarAviso = true;

    private boolean apresentarLinkIncluirCliente = false;

    private OctadeskUserTO octadeskUserTO = null;
    private List<AvisoInternoDTO> avisosInternos;

    private String mensagemSuporte;

    private boolean temPerfilAcessoUnificado = false;

    private boolean abrirRelatorioFrequenciaTurma = false;
    private String onCompleteModuloAtual = "";

    public LoginControle() throws Exception {
        init();
    }

    public void init() throws Exception {
        setUsuario(new UsuarioVO());
        setEmpresa(new EmpresaVO());
        setPerfilAcesso(new PerfilAcessoVO());
        setApresentarBotoesRobo(false);
        setListTipoColaborador(new ArrayList());
        setListaSelectItemEmpresa(new ArrayList());
        setNomeClasse("");
        inicializarFacades();
        inicializarListasSelectItemTodosComboBox();
        setAbrirRichModalModulo(false);
        setAbrirRichModalDiasParaBloqueio(false);
        setAbrirRichModalSolicitarDesbloqueio(false);
        setAbrirRichModalBoletosInicio(false);
        setMensagemID("msg_entre_prmlogin");
        inicializarTemPerfilAcessoUnificado();
    }

    public LoginControle(boolean noInit) {
        //faz nada agora
    }

    public void inicializarTemPerfilAcessoUnificado(){
        try {
            if (!UteisValidacao.emptyString((String) JSFUtilities.getFromSession(JSFUtilities.KEY))) {
                setTemPerfilAcessoUnificado(getFacade().getPerfilAcesso().temPerfilUnificado());
            }
        } catch (Exception e) {
            setTemPerfilAcessoUnificado(false);
            e.printStackTrace();
        }
    }

    public void inicializarListasSelectItemTodosComboBox() {
        if (!isNFe()) {
            if (!UteisValidacao.emptyString((String) JSFUtilities.getFromSession(JSFUtilities.KEY)))
                montarListaSelectItemEmpresa();
        }
    }

    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public String oamd() {
        return "oamd";
    }

    public String login() {
        return login(false);
    }

    @SuppressWarnings("unchecked")
    public String login(Boolean ignorarValidacaoBloqueio) {
        try {
            this.ignorarValidacaoBloqueio = ignorarValidacaoBloqueio;
            apresentarMensagensLogin = false;
            limparContextosSessao();

            if (!isNFe()) {
                if (JSFUtilities.isJSFContext()) {
                    CaptchaControle captchaControle = (CaptchaControle) getControlador(CaptchaControle.class);

                    if (!captchaControle.validarCodigo()) {
                        if (getNumDeTentativas() >= CaptchaControle.NUM_TENTATIVAS_INVALIDAS) {
                            throw new ConsistirException(CaptchaControle.MSG_ERRO_CODIGO_SEGURANCA);
                        }
                    }
                    setNumDeTentativas(getNumDeTentativas() + 1);
                }

                UsuarioVO usuario = ((JSFUtilities.isJSFContext() && !isNull(JSFUtilities.getFromSession("verificarLoginUsuario"))) ?
                        (UsuarioVO) JSFUtilities.getFromSession("verificarLoginUsuario") :
                        getFacade().getControleAcesso().verificarLoginUsuario(getUsername(), getSenha().toUpperCase()));

                if (PropsService.isTrue(PropsService.validarUsuarioOAMD) && (usuario.getUsername().equals("PACTOBR") || usuario.getUsername().equals("admin") || usuario.getUsername().equals("RECOR")) && getUserOamd().equals("")) {
                    throw new Exception("Usuário " + usuario.getUsername() + " só pode acessar via OAMD");
                }
                setPermiteExclusaoClienteUsuarioComum(usuario.isPermiteExclusaoCliente());

                usuario.setUserOamd(getUserOamd());
                usuario.setPermissoesOAMD(getPermissoesOAMD());
                EmpresaVO empresa = new EmpresaVO();
                PerfilAcessoVO perfil = new PerfilAcessoVO();
                PerfilAcessoInterfaceFacade perfilAcessoFacade = new PerfilAcesso();

                Iterator i = usuario.getUsuarioPerfilAcessoVOs().iterator();
                while (i.hasNext()) {
                    UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = (UsuarioPerfilAcessoVO) i.next();
                    if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().equals(getEmpresa().getCodigo())) {
                        perfil = perfilAcessoFacade.consultarPorChavePrimaria(usuarioPerfilAcessoVO.getPerfilAcesso().getCodigo(),
                                Uteis.NIVELMONTARDADOS_TODOS);
                        setNomeEmpresa(usuarioPerfilAcessoVO.getEmpresa().getNome());
                        empresa = getFacade().getEmpresa().consultarPorChavePrimaria(usuarioPerfilAcessoVO.getEmpresa().getCodigo(),
                                usuario.getAdministrador(), Uteis.NIVELMONTARDADOS_TODOS);
                        if (empresa.isBloqueioTemporario()) {
                            throw new Exception("Error");
                        }
                        break;
                    }
                }


                if (!usuario.getAdministrador()) {
                    if (perfil.getCodigo() == 0) {
                        throw new Exception("Acesso Negado! Empresa , Usuário e Senha não Conferem!");
                    }
                    if (validarSistemaSupenso(empresa.getDataSuspensao()) && !usuario.getUsername().equalsIgnoreCase("admin")) {
                        throw new Exception("Seu sistema está suspenso! Favor entrar em contato com o departamento financeiro (62) 3251-5820.");
                    }

                    if (isBloquearEmpresasNaoTipifcadas()) {
                        if (TipoEmpresaFinanceiro.NAO_TIPIFICADO.equals(empresa.getTipoEmpresa())) {
                            throw new Exception("Empresa não autorizada a ter acesso. Por favor, valide junto ao administrativo.");
                        }
                    }
                }
                //verificação será feita para todos os usuários exceto para administrador
/*                if (!usuario.getAdministrador()) {
                    verificarSenhaExpirada(usuario);
                }*/
                // Ticket #21220 - Gleidson pediu para não haver mais a opção de expirar senha.
                // Ticket #22076 - Retirando as demais validações de expiração de senha.

                JSFUtilities.storeOnSession(JSFUtilities.TZ, empresa.getTimeZoneDefault());
                JSFUtilities.storeOnSession(JSFUtilities.UTILIZA_MOVIDESK, Boolean.valueOf(PropsService.getPropertyValue(getKey(), PropsService.UTILIZAR_MOVI_DESK)));
                JSFUtilities.storeOnSession(JSFUtilities.UTILIZA_CHAT_MOVIDESK, Boolean.valueOf(PropsService.getPropertyValue(getKey(), PropsService.UTILIZAR_CHAT_MOVI_DESK)));
                JSFUtilities.storeOnSession(JSFUtilities.GRUPO_CHAT_MOVIDESK, PropsService.getPropertyValue(getKey(), PropsService.GRUPO_CHAT_MOVI_DESK));

                boolean utilizarOctadesk = Boolean.parseBoolean(PropsService.getPropertyValue(getKey(), PropsService.UTILIZAR_OCTADESK));
                JSFUtilities.storeOnSession(JSFUtilities.UTILIZA_OCTADESK, utilizarOctadesk);

                boolean utilizarGymbot = Boolean.parseBoolean(PropsService.getPropertyValue(getKey(), PropsService.UTILIZAR_GYMBOT));
                JSFUtilities.storeOnSession(JSFUtilities.UTILIZA_GYMBOT, utilizarGymbot);

                JSFUtilities.storeOnSession(JSFUtilities.VERSAO_SISTEMA, getVersaoSistema());
                String versaoDb = getVersaoBD() == null ? "" : getVersaoBD().toString();
                JSFUtilities.storeOnSession(JSFUtilities.VERSAO_DB, versaoDb);
                ConfiguracaoSistemaVO configuracaoSistema = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
                configuracaoSistema.inicializarConfiguracaoSistemaCliente();

                JSFUtilities.storeOnSession(JSFUtilities.CONFIGURACAO_SISTEMA, configuracaoSistema);

                RedeEmpresaVO redeEmpresa = OAMDService.consultarRedeEmpresa(getKey());
                setRedeEmpresaVO(redeEmpresa);
                JSFUtilities.storeOnSession(JSFUtilities.REDE_EMPRESA, redeEmpresaVO);

                DetalheEmpresaTO detalheEmpresaTO = consultarDetalhesEmpresa();
                if (detalheEmpresaTO != null) {
                    if (!UteisValidacao.emptyString(detalheEmpresaTO.getNivelAtendimento())) {
                        JSFUtilities.storeOnSession(JSFUtilities.NIVEL_ATENDIMENTO, detalheEmpresaTO.getNivelAtendimento());
                    }

                    CustomerSuccessTO responsavelPacto = detalheEmpresaTO.getCustomerSuccess();
                    if (responsavelPacto != null) {
                        setResponsavelPacto(responsavelPacto);
                        JSFUtilities.storeOnSession(JSFUtilities.RESPONSAVEL_PACTO, responsavelPacto);
                    }
                }

                if (isAtivarAnuncioVitio() && apresentarAnuncio(usuario, perfil)) {
                    RetornoVitioJSON retornoVitioJSON = consultarVitio(usuario);
                    if (retornoVitioJSON != null && UteisValidacao.emptyString(retornoVitioJSON.getError())) {
                        if (retornoVitioJSON.getTemAceite() != null && retornoVitioJSON.getUsuarioRespondeu() != null &&
                                !retornoVitioJSON.getTemAceite() && !retornoVitioJSON.getUsuarioRespondeu()) {
                            setApresentarModalAnuncio(true);
                        }
                    } else {
                        String erro = "Erro ao consultar no OAMD";
                        if (retornoVitioJSON != null) {
                            erro = retornoVitioJSON.getError();
                        }

                        Uteis.logar(null, "Falha ao consultar RetornoVitio no OAMD: " + erro);
                    }
                }

                this.configuracaoSistema = configuracaoSistema;

                //verificação do horário de acesso ao sistema de acordo com o cadastro de usuário
                if (!usuario.getAdministrador()) {
                    verificarHorariosAcessosPermitidos(usuario);
                }
                setNome(usuario.getNome());
                setUsuario(usuario);
                setEmpresa(empresa);
                if (!getUsuario().getUsuarioRecorrencia()) {
                    preencherTokenNovoTreino();
                    prenecherPermissoesUsuarioTreino();
                    preencherTokenNZW();
                    preencherFuncionalidadesFavoritos();
                    if (Uteis.isInstanciaZW() && Uteis.isHabilitarNicho()) {
                        preencherFuncionalidadesInativas();
                    }
                }

                if (UteisValidacao.emptyString(getUsuario().getLinguagem())) {
                    try {
                        LocaleEnum localeEnum = LocaleEnum.obterLocale(empresa.getLocaleTexto());
                        getUsuario().setLinguagem(localeEnum.getLocalize());
                        getUsuario().setSiglaIdiomaNovaPlataforma(localeEnum.getSiglaNovaPlataforma());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    LocaleEnum localeEnum = LocaleEnum.obterLocale(getUsuario().getLinguagem());
                    getUsuario().setLinguagem(localeEnum.getLocalize());
                    getUsuario().setSiglaIdiomaNovaPlataforma(localeEnum.getSiglaNovaPlataforma());
                }

                /*getEmpresa().setFoto(getFacade().getEmpresa().obterFoto(getKey(), getEmpresa().getCodigo(), EntidadeMidiaEnum.FOTO_EMPRESA));
                 getEmpresa().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(), getEmpresa().getCodigo(), EntidadeMidiaEnum.FOTO_EMPRESA_RELATORIO));
                 getEmpresa().setFotoEmail(getFacade().getEmpresa().obterFoto(getKey(), getEmpresa().getCodigo(), EntidadeMidiaEnum.FOTO_EMPRESA_EMAIL));*/
                setPerfilAcesso(perfil);
                setApresentarLinkIncluirCliente(getFacade().getControleAcesso().verificarPermissaoFuncionalidade("Cliente", getUsuario(), ConstantesAcesso.INCLUIR));
                setMensagemDetalhada("", "");

                setNumDeTentativas(0);
                usuarioEmailVO = getFacade().getUsuarioEmail().consultarPorUsuario(getUsuario().getCodigo());
                getUsuario().setEmail(usuarioEmailVO.getEmail());
                validarAtualizacaoUsuarioGeral();
                try {
                    InicioControle inicioControle = (InicioControle) getControlador(InicioControle.class);
                    if (inicioControle == null) {
                        inicioControle = new InicioControle();
                    }
                    inicioControle.preencherEmpresasLogin();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                if ("ADMINISTRADOR".equalsIgnoreCase(perfil.getNome()) || (perfil.getTipo() != null && perfil.getTipo().equals(PerfilUsuarioEnum.ADMINISTRADOR))) {
                    verificarValorNotasEmidas();
                }

                if (configuracaoSistema.isExibirModalInativarUsuarios() && !getUsuario().getUsuarioPactoSolucoes()) {
                    setMsgModalUsuariosAtivos("");
                    if (!usuario.isModalInativar() || !Calendario.igual(Calendario.hoje(), usuario.getExibirModalInativarUsersHoje())) {
                        usuario.setShowModalInativar(false);
                        try {
                            List<UsuarioVO> usuariosInativar = getFacade().getUsuario().consultarUsuariosSemAcessoPorUmMes();
                            if (!usuariosInativar.isEmpty()) {
                                validarPermissaoEntidade(getEmpresa(), getUsuario(), "Usuario", ConstantesAcesso.ALTERAR);
                                setMsgModalUsuariosAtivos("Processar");
                            }
                        } catch (Exception ignored) {
                        }
                    }
                }
                // ---------------
                // PLANOS INATIVOS
                // ---------------
                // Verifica se o modal de planos inativos deve ser exibido
                if (configuracaoSistema.isExibirModalPlanosInativos()) {
                    if (!usuario.isShowModalPlanos() || !Calendario.igual(Calendario.hoje(), usuario.getDataExibirModalPlanos())) {
                        try {
                            List<PlanoVO> planosProximosInativar = getFacade().getPlano()
                                    .consultarPlanosProximosInativar(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

                            if (!planosProximosInativar.isEmpty()) {
                                setListaPlanosProximosInativar(planosProximosInativar);
                                setMsgModalPlanosInativos("Processar");
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }



                for (PermissaoVO permissao : perfil.getPermissaoVOs()) {
                    if (permissao.getNomeEntidade().equals("GestaoRemessas")) {
                        verificarNotificaGestaoRemessas();
                    }
                }

                verificaNotificacaoVencimentoCertificado();

                try {
                    notificarRecursoEmpresa(getIntegracaoNovoLogin() ? RecursoSistema.LOGIN_NOVO : RecursoSistema.LOGIN_ANTIGO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                notificarRecursoEmpresa(RecursoSistema.LOGIN);
                try {
                    priorizarVendaRapida = getFacade().getConfiguracaoSistema().isPriorizarVendaRapida();
                } catch (Exception e) {
                    Uteis.logar(e, LoginControle.class);
                }
                try {
                    this.podeLancarAviso = this.getPermissaoAcessoMenuVO().getPermitirAvisosInternos();
                    negociacaoHabilitada = Uteis.isHabilitarTodasFuncionalidadesBeta() ||
                            (getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.NEGOCIACAO, getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo()) &&
                                    !getFacade().getEmpresa().temPlanoPacote(getEmpresa().getCodigo()) &&
                                    (this.getPermissaoAcessoMenuVO().getVendaRapidaTelaPadraoLancarContrato() == null || !this.getPermissaoAcessoMenuVO().getVendaRapidaTelaPadraoLancarContrato()));
                }catch (Exception e){
                    negociacaoHabilitada = false;
                }
                verificarBannerEmergencia();
                verificarEmbaralharDados();

                if (utilizarOctadesk) {
                    fillOctadeskInfo();
                }

                obterAvisosInternos();

                return atualizaDb();
                // caso não deseje usar o atualizador descomente
                // a linha abaixo e comente a linha acima
                // return abrirModalModulos();
            } else {
                return prepararModuloNFe();
            }
        } catch (Exception e) {
            this.setAbrirRichModalModulo(false);
            this.setAbrirRichModalDiasParaBloqueio(false);
            this.setAbrirRichModalSolicitarDesbloqueio(false);
            this.setAbrirRichModalBoletosInicio(false);
            this.setExibirModalAtualizacaoBD(false);
            this.setExibirModalExibicaoAtualizacoesBD(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            failOverCookie(null, null);
            return "erroLogin";
        }
    }

    private void fillOctadeskInfo() {
        octadeskUserTO = new OctadeskUserTO();
        try {
            octadeskUserTO.setName(getUsuarioLogado().getNome());
            octadeskUserTO.setEmail(getEmailUsuarioLogado());
            octadeskUserTO.setCnpj(getEmpresaLogado().getCNPJ());
            octadeskUserTO.setIdFavorecido(String.valueOf(getEmpresaLogado().getCodEmpresaFinanceiro()));
            octadeskUserTO.setIdUsuario(String.valueOf(getUsuarioLogado().getCodigo()));
            octadeskUserTO.setPerfilDeAcesso(getPerfilUsuarioLogado().getNome());
            octadeskUserTO.setCpf(getUsuarioLogado().getColaboradorVO().getPessoa().getCfp());
        } catch (Exception e) {
            Uteis.logar(e, LoginControle.class);
        }
    }

    private void preencherFuncionalidadesInativas() {
        if (getKey() != null && getEmpresa() != null) {
            setFuncionalidadesInativas(RecursoMsService.consultarFuncionalidadesInativas(getKey(), getEmpresa().getCodigo()));
        }
    }

    private void prenecherPermissoesUsuarioTreino() {
        JSONObject jsonResponse = null;
        JSONObject jsonContent = null;
        String urlTreino = null;

        try {
            if(isApresentarModuloNovoTreino()){
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                headers.put("Authorization", "Bearer " + tokenNT);

                urlTreino = getUrlTreino() + "/prest/psec/validateToken";

                String response = ExecuteRequestHttpService.executeHttpRequest(urlTreino, null, headers, "GET", "UTF-8");

                jsonResponse = new JSONObject(response);
                jsonContent = jsonResponse.getJSONObject("content");
                usuarioTreinoDTO = new UsuarioTreinoDTO(jsonContent);
            }else{
                usuarioTreinoDTO = null;
            }
        } catch (Exception e) {
            Uteis.logarDebug("Falha ao validar token de treino para obter permissões do usuário");
            Uteis.logarDebug("jsonResponse: " + jsonResponse);
            Uteis.logarDebug("jsonContent: " + jsonContent);
            Uteis.logarDebug("urlTreino: " + urlTreino);
            Uteis.logarDebug("tokenNT: " + tokenNT);
        }
    }

    private void processarPermissaoAcessoMenuTreino() {
        if(usuarioTreinoDTO != null && isApresentarModuloNovoTreino()){
            try{
                boolean verVisaoGetal = usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("ver_gestao_geral") && funcionalidade.isPossuiFuncionalidade());
                boolean verGestaoCreditos = isApresentarGestaoPersonal() && usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("gestao_creditos_personal") && funcionalidade.isPossuiFuncionalidade());

                permissaoAcessoMenuVO.setTreinoBI(true);
                permissaoAcessoMenuVO.setTreinoBiPersonalizado(true);
                permissaoAcessoMenuVO.setTreinoAcompanhamentoPersonal(isApresentarGestaoPersonal() && usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("acompanhar_personal") && funcionalidade.isPossuiFuncionalidade()));
                 permissaoAcessoMenuVO.setTreinoColaboradores(isApresentarGestaoPersonal() && usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                         .stream()
                         .anyMatch(funcionalidade -> funcionalidade.getNome().equals("gestao_personal") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setTreinoGestaoCreditos(verGestaoCreditos);
                permissaoAcessoMenuVO.setTreinoPersonais(isApresentarGestaoPersonal() && usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("gestao_personal") && funcionalidade.isPossuiFuncionalidade())
                );
                permissaoAcessoMenuVO.setTreinoRelatorioAndamento(verVisaoGetal);
                permissaoAcessoMenuVO.setTreinoRelatorioAplicativosAtivos(true);
                permissaoAcessoMenuVO.setTreinoRelatorioIndicadoresCarteiraProfessores(verVisaoGetal);
                permissaoAcessoMenuVO.setTreinoRelatorioIndicadoresAtividadesProfessores(verVisaoGetal);
                permissaoAcessoMenuVO.setTreinoRelatorioProfessoresAlunosAvisosMedicos(verVisaoGetal);
                permissaoAcessoMenuVO.setTreinoRelatorioProfessoresSubstitutos(verVisaoGetal);
                permissaoAcessoMenuVO.setTreinoRelatorioRanking(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("ranking_professores") && funcionalidade.isPossuiFuncionalidade())
                );
                permissaoAcessoMenuVO.setTreinoCadastroNiveis(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("niveis") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setTreinoEmCasa(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("programa_treino")));
                permissaoAcessoMenuVO.setTreinoCadastroAparelhos(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("aparelhos") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setTreinoCadastroAtividades(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("atividades") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setTreinoCadastroCategoriaAtividades(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("categoria_atividade") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setTreinoCadastroFichasPredefinidas(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("fichas_pre_definidas") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setTreinoCadastroProgramasPredefinidos(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("programas_predefinidos") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setTreinoCadastroProgramasPredefinidos(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("tela_prescricao_treino") && funcionalidade.isPossuiFuncionalidade())
                );
                permissaoAcessoMenuVO.setTreinoCadastroAlunos(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("alunos") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setTreinoUsuarios(true);
                permissaoAcessoMenuVO.setTreinoPrescricaoTreino(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("prescricao_treino")));

                permissaoAcessoMenuVO.setTreinoRelatorioAplicativosAtivos(true);

                // TODO: Este recurso será alterado do modulo do novo treino para o novo adm. Quando isso for feito, esta permissão e a configuração de menus deve mudar tambem
                boolean temPermissaoPerfilAcesso = usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("perfil_usuario") &&
                        funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")));

                if(isTemPerfilAcessoUnificado()){
                    permissaoAcessoMenuVO.setTreinoPerfilAcesso(false);
                }else{
                    permissaoAcessoMenuVO.setTreinoPerfilAcesso(temPermissaoPerfilAcesso);
                }

            }catch (Exception e){
                Uteis.logarDebug("Falha ao processar permissão de acesso ao menu do treino");
                e.printStackTrace();
            }
        }
    }

    private void processarPermissaoAcessoMenuAvaliacaoFisica() {
        if(usuarioTreinoDTO != null && isApresentarModuloAvaliacaoFisica()){
            try{
                permissaoAcessoMenuVO.setAvaliacaoFisicaBI(true);
                permissaoAcessoMenuVO.setAvaliacaoFisicaCadastroAnamnese(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("anamnese") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
                permissaoAcessoMenuVO.setAvaliacaoFisicaCadastroObjetivos(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("objetivos") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
            }catch (Exception e){
                Uteis.logarDebug("Falha ao processar permissão de acesso ao menu do modulo avaliação fisica");
                e.printStackTrace();
            }
        }
    }

    private void processarPermissaoAcessoMenuPactoPay() {
        if(usuarioTreinoDTO != null && isApresentarModuloPactoPay()){
            try {
                permissaoAcessoMenuVO.setPactoPayBI(true);
                permissaoAcessoMenuVO.setPactoPayCartaoCredito(true);
                permissaoAcessoMenuVO.setPactoPayParcelasEmAberto(true);
                permissaoAcessoMenuVO.setPactoPayCreditoOnline(true);
                permissaoAcessoMenuVO.setPactoPayPix(true);
            } catch (Exception e) {
                Uteis.logarDebug("Falha ao processar permissão de acesso ao menu do modulo pacto pay");
                e.printStackTrace();
            }

            FacilitePay facilitePayDAO;
            try {
                permissaoAcessoMenuVO.setPactoPayReguaCobranca(false);
                permissaoAcessoMenuVO.setPactoPayReguaCobrancaDashboard(false);
                permissaoAcessoMenuVO.setPactoPayReguaCobrancaConfigEmail(false);

                //somente se tiver o módulo
                if (this.isApresentarModuloFacilitePay()) {
                    facilitePayDAO = new FacilitePay(Conexao.getFromSession());
                    FacilitePayConfigDTO facilitePayConfigDTO = facilitePayDAO.configFacilitePay(this.getEmpresaLogado().getCodigo(), this.getUsuarioLogado().getCodigo(), this.getUsuarioLogado().getUsername());
                    permissaoAcessoMenuVO.setPactoPayReguaCobranca(facilitePayConfigDTO.getReguaCobranca());
                    permissaoAcessoMenuVO.setPactoPayReguaCobrancaConfigEmail(facilitePayConfigDTO.getReguaCobrancaConfiguracaoEmail());
                    permissaoAcessoMenuVO.setPactoPayReguaCobrancaDashboard(facilitePayConfigDTO.getReguaCobranca());
                }
            } catch (Exception e) {
                Uteis.logarDebug("Falha ao processar permissão de acesso ao menu do modulo FacilitePay");
                e.printStackTrace();
            } finally {
                facilitePayDAO = null;
            }
        }
    }

    private void processarPermissaoAcessoMenuCross() {
        if(usuarioTreinoDTO != null && isApresentarModuloCross()){
            try{
                permissaoAcessoMenuVO.setCrossBi(true);
                permissaoAcessoMenuVO.setCrossWod(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("wod") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setCrossMonitor(true);
                permissaoAcessoMenuVO.setCrossCadastroAparelhos(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("aparelhos_wod") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setCrossCadastroAtividades(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("atividades_wod") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setCrossCadastroBenchmarks(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("benchmark") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setCrossCadastroTiposBenchmark(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("tipo_benchmark") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setCrossCadastroTipoWod(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("cadastrar_tipo_wod") && funcionalidade.isPossuiFuncionalidade()));
            }catch (Exception e){
                Uteis.logarDebug("Falha ao processar permissão de acesso ao menu do modulo cross");
                e.printStackTrace();
            }
        }
    }

    private void processarPermissaoAcessoMenuGraduacao() {
        if(usuarioTreinoDTO != null && isApresentarModuloGraduacao()){
            try{
                permissaoAcessoMenuVO.setGraduacaoAvaliacoesProgresso(true);
                permissaoAcessoMenuVO.setGraduacaoFichaTenica(true);
                permissaoAcessoMenuVO.setGraduacaoAtividades(true);
            }catch (Exception e){
                Uteis.logarDebug("Falha ao processar permissão de acesso ao menu do modulo graduacao");
                e.printStackTrace();
            }
        }
    }

    private void processarPermissaoAcessoMenuAgenda() {
        if(usuarioTreinoDTO != null && isApresentarModuloAgenda()){
            try{
                permissaoAcessoMenuVO.setAgendaBi(true);
                permissaoAcessoMenuVO.setAgendaTvAula(true);
                permissaoAcessoMenuVO.setAgendaTvGestor(true);
                permissaoAcessoMenuVO.setAgendaAulas(true);
                permissaoAcessoMenuVO.setAgendaServicos(true);
                permissaoAcessoMenuVO.setAgendaAmbiente(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("ambientes") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setAgendaModalidade(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("modalidades") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setAgendaIndicadores(true);
                permissaoAcessoMenuVO.setAgendaAulaExcluida(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("excluir_aulas_dia") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setAgendaConfigurarAula(usuarioTreinoDTO.getPerfilUsuario().getFuncionalidades()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getNome().equals("cadastro_aulas") && funcionalidade.isPossuiFuncionalidade()));
                permissaoAcessoMenuVO.setAgendaTipoAgendamento(usuarioTreinoDTO.getPerfilUsuario().getRecursos()
                        .stream()
                        .anyMatch(funcionalidade -> funcionalidade.getRecurso().equals("tipo_evento") &&
                                funcionalidade.getTipoPermissoes().stream().anyMatch(tipoPermissao -> tipoPermissao.equals("CONSULTAR") || tipoPermissao.equals("TOTAL_EXCETO_EXCLUIR") || tipoPermissao.equals("TOTAL")))
                );
            }catch (Exception e){
                Uteis.logarDebug("Falha ao processar permissão de acesso ao menu do modulo agenda");
                e.printStackTrace();
            }
        }
    }

    private boolean validarSistemaSupenso(Date dataSuspensao) throws Exception {
        if(dataSuspensao != null){
            Date meioDia = Calendario.getDataComHora(Calendario.hoje(), "12:00:00");
            if((Calendario.menor(dataSuspensao , Calendario.hoje()) || (Calendario.igual(dataSuspensao , Calendario.hoje()) && Calendario.menorOuIgualComHora(meioDia, Calendario.hoje())))) {
                return true;
            }
        }
        return false;
    }

    private void preencherTokenNZW() {
        try {
            Object sessionId = JSFUtilities.getFromSession("sessionId");

            if (sessionId != null) {
                // Este é o formato correto, os links de redirecionamento devem propagar o token via session_id do login.
                // Todas as aplicações estão sendo refatoradas para utilizar somente esse parâmetro.
                return;
            }
            String tokenOamd = (String) JSFUtilities.getFromSession("tokenOamd");
            if (tokenOamd != null && !tokenOamd.isEmpty()) {
                // O token já foi preenchido via OIDServelet
                // Isso ocorre quando é um redirecionado do login, oamd, novo adm, treino, apoio, etc.
                // O token já foi geradano via autenticação a partir destas aplicações, e será reutilizado.
                // FIXME: Para manter a retrocompatibidade. Devemos remover isso após refatorar as outras aplicações, para utilizar session_id do login.
                tokenNZW = tokenOamd;
                return;
            }

            if (!UteisValidacao.emptyString((String) JSFUtilities.getFromSession("tokenNZW"))) {
                tokenNZW = (String) JSFUtilities.getFromSession("tokenNZW");
                return;
            }

            // FIXME: Para manter a retrocompatibidade. Devemos remover isso após refatorar as outras aplicações, para utilizar session_id do login.
            tokenNZW = AutenticacaoMsService.token(getKey(), getUsuario().getUsername(), getSenha(), Uteis.encriptar(request().getSession(false).getId(), "chave_login_unificado"));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private DetalheEmpresaTO consultarDetalhesEmpresa() {
        try {
            final String url = "%s/prest/empresaFinanceiro/consultarDetalhesAtendimento?key=%s&codigoEmpresaZw=%s";
            String urlGet = String.format(url, PropsService.getPropertyValue(PropsService.urlOamd), getKey(), getEmpresa().getCodigo());

            String response = ExecuteRequestHttpService.executeRequestGet(urlGet, new HashMap<>());
            JSONObject resposta = new JSONObject(response).optJSONObject("return");
            if (resposta == null) {
                return null;
            }
            return JSONMapper.getObject(resposta, DetalheEmpresaTO.class);
        } catch (Exception ex) {
            Uteis.logar(ex, LoginControle.class);
        }
        return null;
    }

    private void preencherTokenNovoTreino() {
        try {
            if (!UteisValidacao.emptyString(getUsuario().getTokenNT())) {
                tokenNT = getUsuario().getTokenNT();
            } else {
                tokenNT = validarUsuarioSistemaPacto();
            }
        } catch(Exception ex) {
            ex.printStackTrace();
        }
    }

    private void verificaNotificacaoVencimentoCertificado() {
        try {
            NotificacaoUsuarioVO notificacaoVencimentoCertificado = getFacade().getNotificacaoUsuario().consultarNotificacao(getUsuarioLogado(), TipoNotificacaoUsuarioEnum.VENCIMENTO_CERTIFICADO, null, Calendario.hoje(), getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //não existe notificacao criada, então vamos processar e verificar se existe certificado para ser apresentado
            if (UteisValidacao.emptyNumber(notificacaoVencimentoCertificado.getCodigo())) {

                List<ConfiguracaoNotaFiscalVO> configuracaoNotaFiscalVOS = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(getEmpresa().getCodigo(), null, true, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!UteisValidacao.emptyList(configuracaoNotaFiscalVOS)) {

                    NotificacaoUsuarioVO notificacaoUsuarioVO = new NotificacaoUsuarioVO();
                    notificacaoUsuarioVO.getUsuarioVO().setCodigo(getUsuario().getCodigo());
                    notificacaoUsuarioVO.setEmpresaVO(getEmpresa());
                    notificacaoUsuarioVO.getTipo().setCodigo(TipoNotificacaoUsuarioEnum.VENCIMENTO_CERTIFICADO.getCodigo());
                    notificacaoUsuarioVO.setDataLancamento(Calendario.hoje());
                    notificacaoUsuarioVO.setApresentarHoje(true);
                    NotificacaoUsuario notificacaoUsuario = new NotificacaoUsuario();
                    String msgCertificado = "";

                    for (ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO : configuracaoNotaFiscalVOS) {

                        if (configuracaoNotaFiscalVO.getDataCertificado() != null && Calendario.diferencaEmDias(Calendario.hoje(), configuracaoNotaFiscalVO.getDataCertificado()) < 0) {
                            msgCertificado += " - " + configuracaoNotaFiscalVO.getDescricao() + " está vencido <br/>";
                            labelVencimentoCertificado = " Vencido(s)";

                        }else if (configuracaoNotaFiscalVO.getDataCertificado() != null && Calendario.diferencaEmDias(Calendario.hoje(), configuracaoNotaFiscalVO.getDataCertificado()) == 0){
                            msgCertificado += " - " + configuracaoNotaFiscalVO.getDescricao() + " vence hoje <br/>";
                            labelVencimentoCertificado = " Vencendo";
                        }
                        else if (configuracaoNotaFiscalVO.getDataCertificado() != null && Calendario.diferencaEmDias(Calendario.hoje(), configuracaoNotaFiscalVO.getDataCertificado()) <= 30) {
                            msgCertificado += " - " + configuracaoNotaFiscalVO.getDescricao() + " vence em: " + Calendario.diferencaEmDias(Calendario.hoje(), configuracaoNotaFiscalVO.getDataCertificado()) + " dia(s).<br/>";
                            labelVencimentoCertificado = " Vencendo";
                        }

                    }

                    if (!UteisValidacao.emptyString(msgCertificado)) {

                        notificacaoUsuarioVO.setMensagem(msgCertificado);
                        notificacaoUsuario.incluir(notificacaoUsuarioVO);

                    }
                }
            }
            verificarNotificaCertificado();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * Prepara o módulo de notas, tendo a requisição como origem da tela de login do módulo.
     */
    public String prepararModuloNFe() throws Exception {
        return prepararModuloNFe(null);
    }

    /**
     * @param parametrosLoginDireto parametros que configuram a inicialização do módulo de notas, quando o acesso é direto, ou seja, não é de origem
     *                              da tela de login do módulo.
     */
    public String prepararModuloNFe(ParametrosLoginDiretoModuloNotasJSON parametrosLoginDireto) throws Exception {
        UsuarioNFeVO usuario;
        EmpresaNFeVO empresaNFe;
        boolean usuarioAcessoDiretoModuloNotas = false;

        if (parametrosLoginDireto != null) {
            Connection connection = criarEGerenciarConexaoNaSessao(parametrosLoginDireto.getChaveNFSe());

            usuario = criarUsuarioNFeByParametrosLoginDireto(parametrosLoginDireto);
            PerfilUsuarioNFeVO perfilUsuarioNFeVO = criarPerfilUsuarioNFeByParametrosLoginDireto(parametrosLoginDireto);

            empresaNFe = criarEmpresaByParametrosLoginDireto(parametrosLoginDireto, connection);
            perfilUsuarioNFeVO.setEmpresa(empresaNFe);
            usuario.setPerfilUsuarioNFe(perfilUsuarioNFeVO);
            usuarioAcessoDiretoModuloNotas = true;
        } else {
            usuario = (UsuarioNFeVO) getFacade().getControleAcesso().verificarLoginUsuario(getUsername(), getSenha().toUpperCase());
        }

        PerfilUsuarioNFeVO perfilUsuarioNFe = usuario.getPerfilUsuarioNFe();
        empresaNFe = perfilUsuarioNFe.getEmpresa();

        setNome(usuario.getNome());
        setUsuario(usuario);
        setEmpresa(empresaNFe);
        JSFUtilities.storeOnSession("timeZoneID", empresaNFe.getTimeZoneDefault());

        setPerfilAcesso(perfilUsuarioNFe);
        setPerfilNFe(perfilUsuarioNFe);

        setMensagem("");
        setMensagemDetalhada("");

        if (context() != null) {
            HttpSession session = (HttpSession) context().getExternalContext().getSession(true);
            session.setAttribute(LoginControle.NomeChaveUsuarioSessao, getUsuario());
        }

        NotaFiscalDeServicoControle notaFiscalControle = (NotaFiscalDeServicoControle) getControlador(NotaFiscalDeServicoControle.class.getSimpleName());
        notaFiscalControle.setUsuarioAcessouDiretoModuloNotas(usuarioAcessoDiretoModuloNotas);

        if (parametrosLoginDireto != null) {
            notaFiscalControle.getFiltro().setTipoNota(getFiltroModeloNotaByParametrosAcessoDireto(parametrosLoginDireto));
        }

        notaFiscalControle.consultar();

        return "nfe";
    }

    private Integer getFiltroModeloNotaByParametrosAcessoDireto(ParametrosLoginDiretoModuloNotasJSON parametrosLoginDireto) {
        if (parametrosLoginDireto.isUsarNFCe()) {
            return TipoNotaEnum.NFCE.getCodigo();
        }

        return TipoNotaEnum.NFSE.getCodigo();
    }

    private UsuarioNFeVO criarUsuarioNFeByParametrosLoginDireto(ParametrosLoginDiretoModuloNotasJSON parametrosLoginDireto) {
        final String username = parametrosLoginDireto.getNomeUsuario();
        final String senhaFake = "X";

        UsuarioNFeVO usuario = new UsuarioNFeVO();
        usuario.setAdministrador(parametrosLoginDireto.isAdministrador());
        usuario.setNome(username);
        usuario.setUsuario(username);
        usuario.setSenha(senhaFake);

        return usuario;
    }

    private EmpresaNFeVO criarEmpresaByParametrosLoginDireto(ParametrosLoginDiretoModuloNotasJSON parametrosLoginDireto, Connection con) throws Exception {
        EmpresaNFe empresaNFeDao = new EmpresaNFe(con);
        EmpresaNFeVO empresaNFe = empresaNFeDao.consultarPorChave(parametrosLoginDireto.getChaveNFSe());
        if (UteisValidacao.emptyNumber(empresaNFe.getId_Empresa())) {
            throw new Exception("Chave não encontrada no módulo NFSe.");
        }

        empresaNFe.setUsaModuloNFCe(parametrosLoginDireto.isUsarNFCe());
        empresaNFe.setUsaModuloNFSe(parametrosLoginDireto.isUsarNFSe());

        return empresaNFe;
    }

    private PerfilUsuarioNFeVO criarPerfilUsuarioNFeByParametrosLoginDireto(ParametrosLoginDiretoModuloNotasJSON parametrosLoginDireto) {
        PerfilUsuarioNFeVO perfilUsuarioNFeVO = new PerfilUsuarioNFeVO();
        perfilUsuarioNFeVO.setPermiteCancelarNota(true);
        perfilUsuarioNFeVO.setPermiteAlterarNumeroRPS(parametrosLoginDireto.getPermissaoAlterarRPS());

        return perfilUsuarioNFeVO;
    }

    private void failOverCookie(UsuarioVO user, final Integer empresa) {
        try {
            if (PropsService.isTrue(PropsService.cookieFailover)) {
                if (user != null && user.getCodigo() > 0) {
                    user.setChave(getKey());
                    user.setCodEmpresaLogada(empresa);
                    user.setTimeZoneEmpresa((String) JSFUtilities.getFromSession("timeZoneID"));
                    user.setSenhaNaoCriptografada(getSenha().toUpperCase());
                    user.setModulosHabilitados((String) JSFUtilities.getFromSession("modulosHabilitados"));
                    Cookie c = new Cookie(JSFUtilities.COOKIE_CREDENTIALS_FAILOVER,
                            Uteis.encriptarCookie(user.toJSON()));
                    JSFUtilities.addCookie(c);
                } else {
                    JSFUtilities.removeCookie(JSFUtilities.COOKIE_CREDENTIALS_FAILOVER);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void verificarHorariosAcessosPermitidos(UsuarioVO usuarioVO) throws Exception {
        //obtem dia da semana e hora do dia atual
        String dataAtual = Uteis.getHoraAtual();
        String diaSemana = Calendario.getDiaDaSemanaAbreviado(Calendario.hoje());
        //consulta os horários permitidos de acesso

        List<HorarioAcessoSistemaVO> listaHorarioAcessoSistemaVOs = getFacade().
                getHorarioAcessoSistema().consultarHorarioAtualDentroIntervalosPermitidos(
                        usuarioVO.getCodigo().intValue(), dataAtual, diaSemana, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaHorarioAcessoSistemaVOs.isEmpty()) {
            throw new Exception("Acesso não permitido para esse horário.");
        }
    }

    /**
     * Método de fechar modal de expiração de senha
     */
    public void fecharPanelExpiracaoSenha() {
        setMostrarModalExpiracaoSenha(false);
    }

    /**
     * Metodo que verifica quantos dias faltam para o bloqueio do sistema e toma
     * as acoes necessarias de acordo com o resultado obtido, como segue abaixo:
     * <p>
     * - Menor que zero o sistema está liberado para acesso (entra no sistema
     * direto); - Igual a zero o sistema está bloqueado para acesso (Será
     * mostrado uma tela com mensagem); - Maior que zero determina o numero de
     * dias que falta para bloqueio (Será mostrado uma tela com mensagem);
     * <p>
     * Autor: Pedro Y. Saito Criado em 26/01/2011
     */
    private String abrirTelaDiasParaBloqueio() throws Exception {
        if (isAdmin()) {
            this.setAbrirRichModalDiasParaBloqueio(false);
            this.setAbrirRichModalSolicitarDesbloqueio(false);
            this.setAbrirRichModalBoletosInicio(false);
            this.setAbrirRichModalModulo(true);
            this.setExibirModalAtualizacaoBD(false);
            this.setExibirModalExibicaoAtualizacoesBD(false);
            return "erroLogin";
        }
        if (!isUsouTodosDiasExtrasPermitidos() && !isDataExpiracaoOutrosVencida()) {
            verificarPermissaoExibirNotificacaoExpiracaoSistema();
            diasParaBloqueio = getFacade().getControleAcesso().getDiasParaBloqueio(getEmpresaLogado().getCodigo());
            String expiracaoEm = (String) JSFUtilities.getFromSession("dataExpiracao");
            Date dataExpiracao = (expiracaoEm != null) ? Uteis.getDate(expiracaoEm) : null;
            getEmpresaLogado().setDataExpiracao(dataExpiracao);
            setInfoMensagemBloqueio("");
            setMensagemBloqueio("");
            setMensagemDetalhada("");

            if (diasParaBloqueio >= 0) {
                try {
                    validarPermissao("BoletosSistema", "Boletos do Sistema", this.getUsuario());
                    setApresentarLinkBoletos(true);
                    setExistemNovosBoletos(getEmpresaLogado().isExistemNovosBoletosPacto());
                } catch (Exception ex) {
                    setExistemNovosBoletos(false);
                    setApresentarLinkBoletos(false);
                }
            }

            Date meioDia = Calendario.getDataComHora(Calendario.hoje(), "12:00:00");
            this.setConcederMaisUmDia(false);

            if (((diasParaBloqueio == 0 && Calendario.maiorOuIgualComHora(Calendario.hoje(), meioDia))
                    || (dataExpiracao != null && Calendario.maior(Calendario.hoje(), dataExpiracao))) && !ignorarValidacaoBloqueio) {


                this.setAbrirRichModalBoletosInicio(false);
                this.setAbrirRichModalModulo(false);
                this.setExibirModalAtualizacaoBD(false);
                this.setExibirModalExibicaoAtualizacoesBD(false);
                this.setConcederMaisUmDia(true);
                JSFUtilities.storeOnSession(LoginControle.NomeChaveUsuarioSessao, null);
                if(getUsuarioLogado().getPossuiPerfilAcessoAdministrador()) {
                    this.setAbrirRichModalSolicitarDesbloqueio(true);
                    setTituloMensagemBloqueio("Seu sistema Pacto está Bloqueado!");
                    StringBuilder mensagemBloqueio = new StringBuilder();
                    mensagemBloqueio.append("Olá, cliente! Entendemos que algumas situações fogem do nosso controle e este pode ser o seu caso. <br/>");
                    mensagemBloqueio.append("Para continuar utilizando o sistema Pacto você deve realizar o pagamento do boleto disponível <br/> aqui no Canal do Cliente.<br/>");
                    mensagemBloqueio.append("Se você acredita que esta mensagem está aparecendo para você por engano, <br/>fale com a nossa equipe para contornarmos esta situação.<br/><br/>");

                    setMensagemBloqueio(mensagemBloqueio.toString());
                    gerarDiasPermitidos();
                } else {
                    this.setAbrirRichModalDiasParaBloqueio(true);


                    setTituloMensagemBloqueio("Seu sistema Pacto está Bloqueado!");


                    setMensagemBloqueio("Faça login com um usuário que tenha perfil de administrador para solicitar o desbloqueio do sistema.");
                }
                return "erroLogin";
            } else if (((diasParaBloqueio > 0 || (diasParaBloqueio == 0 && Calendario.menorComHora(Calendario.hoje(), meioDia)))) && !ignorarValidacaoBloqueio) {
                if (getUsuarioLogado().getPossuiPerfilAcessoAdministrador()) {
                    this.setAbrirRichModalDiasParaBloqueio(true);
                    this.setAbrirRichModalBoletosInicio(false);
                    this.setAbrirRichModalModulo(false);
                    this.setExibirModalAtualizacaoBD(false);
                    this.setExibirModalExibicaoAtualizacoesBD(false);


                    StringBuilder mensagemBloqueio = new StringBuilder();
                        setTituloModalBloqueio("Bloqueio agendado");
                        setTituloMensagemBloqueio("Temos um recado importante para você!");
                        if (diasParaBloqueio == 0) {
                            mensagemBloqueio.append("Ao  <span style=\"border-radius: 3px; font-weight: bold; color: #BA202F;\">meio do dia</span> ");
                            mensagemBloqueio.append(" seu sistema Pacto será bloqueado devido a pendências financeiras.");
                        } else {
                            mensagemBloqueio.append("Falta").append(diasParaBloqueio > 1 ? "m" : "").append(" apenas <span style=\"border-radius: 3px; font-weight: bold; color: #BA202F;\">").append(diasParaBloqueio).append(" dia").append(diasParaBloqueio > 1 ? "s" : "")
                                    .append("</span> ");
                            mensagemBloqueio.append("para que seu sistema Pacto seja bloqueado devido a pendências financeiras.");
                        }
                        mensagemBloqueio.append("<br/><span style=\"border-radius: 3px; font-weight: bold;\">Após este prazo, você não conseguirá executar nenhuma das funções disponíveis para você.</span>");
                        mensagemBloqueio.append("<br/><span>Realize o pagamento e evite o bloqueio da sua operação.</span>");
                        mensagemBloqueio.append("<br/><span>Caso você já tenha feito o pagamento, desconsidere esta mensagem.</span>");

                    setMensagemBloqueio(mensagemBloqueio.toString());
                } else {
                    this.setAbrirRichModalDiasParaBloqueio(false);
                    this.setAbrirRichModalSolicitarDesbloqueio(false);
                    this.setAbrirRichModalBoletosInicio(false);
                    this.setAbrirRichModalModulo(true);
                    this.setExibirModalAtualizacaoBD(false);
                    this.setExibirModalExibicaoAtualizacoesBD(false);
                }
                return "erroLogin";
            } else {
                if (this.isApresentarMenuModulos()) {
                    this.setAbrirRichModalModulo(Boolean.TRUE);
                    this.setAbrirRichModalDiasParaBloqueio(false);
                    this.setAbrirRichModalSolicitarDesbloqueio(false);
                    this.setAbrirRichModalBoletosInicio(false);
                    this.setExibirModalAtualizacaoBD(Boolean.FALSE);
                    this.setExibirModalExibicaoAtualizacoesBD(Boolean.FALSE);
                    return "erroLogin";
                } else {
                    if (this.isApresentarLinkZW()) {
                        abrirZillyonWeb();
                        return executarRobo() ? "login" : "robo";
                    } else if (this.isApresentarLinkEstudio()) {
                        abrirModuloEstudio();
                        return executarRobo() ? "loginEstudio" : "robo";
                    } else {
                        notificarRecursoEmpresa(RecursoSistema.ERRO_LOGIN_SEM_MODULOS);
                        setMensagemDetalhada("erro_login_sem_modulo: Nenhum módulo principal habilitado para esta Empresa.");
                        return "erroLogin";
                    }

                }
            }
        } else {
            setMensagemDetalhada("Seu sistema está suspenso! Favor entrar em contato com o departamento financeiro (62) 3251-5820.");
            return redirecionaSistemaLogin();
        }
    }

    private void gerarDiasPermitidos() throws Exception {
        getListaDias().clear();
        int quantidadeDeDias = SuperControle.MAXIMO_DIAS_EXTRA_CONCEDIDOS - getEmpresaLogado().getTotalDiasExtras();
        if(quantidadeDeDias > 5) {
            quantidadeDeDias = 5;
        }
        for (int x = 1; x <= quantidadeDeDias; x++) {
            String label = "";
            if (x == 1) {
                label = x + " dia";
            } else {
                label = x + " dias";
            }
            listaDias.add(new SelectItem(x, label, label, false));
        }
    }

    private boolean isDataExpiracaoOutrosVencida() throws Exception {
        return getValidarBloqueioOutros() && getEmpresaLogado().getDataExpiracaoOutros() != null &&
                (getEmpresaLogado().getDataExpiracaoOutros().getTime() - new Date().getTime() <= 0);
    }

    private void setConcederMaisUmDia(boolean conceder) {
        this.concederMaisUmDia = conceder;
    }

    public Boolean getConcederMaisUmDia() {
        return concederMaisUmDia != null && concederMaisUmDia;
    }

    private void verificarPermissaoExibirNotificacaoExpiracaoSistema() {
        try {
//            validarPermissao("NotificacaoExpiracaoSistema", "Notificacao Expiracao Sistema", this.getUsuario());
            //irá apresentar para todos por solitação da diretoria
            setExibirNotificacaoExpiracaoSistema(true);
        } catch (Exception ex) {
            setExibirNotificacaoExpiracaoSistema(false);
        }
    }

    /**
     * Metodo de validacao da tela de mensagem (id="panelDiasParaBloqueio"
     * [login.jsp]), onde se o diasParaBloqueio for zero o sistema volta para
     * tela de login, senao o sistema realiza o processo normal
     * <p>
     * Autor: Pedro Y. Saito Criado em 26/01/2011
     */
    public String verificarDiasParaBloqueio() throws SistemaException, Exception {
        setMensagemDetalhada("msg_erro", "");
        Date meioDia = Calendario.getDataComHora(Calendario.hoje(), "12:00:00");
        String expiracaoEm = (String) JSFUtilities.getFromSession("dataExpiracao");
        Date dataExpiracao = (expiracaoEm != null) ? Uteis.getDate(expiracaoEm) : null;

        if (getEmpresaLogado() != null) {
            if ((diasParaBloqueio == 0 && Calendario.maiorOuIgualComHora(Calendario.hoje(), meioDia)
                    || (dataExpiracao != null && Calendario.maior(Calendario.hoje(), dataExpiracao)))) {
                this.setAbrirRichModalDiasParaBloqueio(false);
                this.setAbrirRichModalSolicitarDesbloqueio(false);
                this.setAbrirRichModalBoletosInicio(false);
                this.setAbrirRichModalModulo(false);
                this.setExibirModalAtualizacaoBD(false);
                this.setExibirModalExibicaoAtualizacoesBD(false);
                setMensagemDetalhada("msg_erro", "Entrar em contato!");
                logout();
                return redirecionaSistemaLogin();
            }
        }

        this.setAbrirRichModalModulo(Boolean.TRUE);
        this.setAbrirRichModalDiasParaBloqueio(false);
        this.setAbrirRichModalSolicitarDesbloqueio(false);
        this.setAbrirRichModalBoletosInicio(false);
        this.setExibirModalAtualizacaoBD(Boolean.FALSE);
        this.setExibirModalExibicaoAtualizacoesBD(Boolean.FALSE);

        return abrirZillyonWeb();

    }

    public String redirecionaSistemaLogin() {
        try {
            String chave = (String) JSFUtilities.getFromSession("key");
            String urlLogin = getUrlLoginExterno();
            if (urlLogin != null && urlLogin.contains("pactosolucoes.com.br")) {
                response().sendRedirect(urlLogin + "/" + (chave == null ? "" : chave));
                return "";
            } else {
                return "inicio";
            }
        } catch (Exception e) {
            return "inicio";
        }
    }

    private String atualizaDb() throws Exception {
        // Obter a versão atual do banco de dados do sistema
        if (getFacade().getSinalizadorSistema().consultarPorSinalizador(SinalizadorEnum.BDATUALIZANDO)) {
            throw new Exception(SinalizadorEnum.BDATUALIZANDO.getMensagem());
        }

        List<String> processosARodar = new ArrayList<>();
        Integer versaoAtual = 0;
        if (SuperControle.getValidarVersaoBD()) {
            versaoAtual = getFacade().getAtualizadorBD().obterVersaoAtual();
            processosARodar = getFacade().getAtualizadorBD().processosARodar();
        }

        // Se a versão for igual a especificada
        if (!SuperControle.getValidarVersaoBD() || (versaoAtual.equals(this.getVersaoBD()) && processosARodar.isEmpty())) {
            // Exibir modal de escolha de módulos do sistema
            return abrirModalModulos();
        }
        // Se a versão do banco for menor que a versão especificada
        this.setMensagemAtualizacaoBD("");
        if (!processosARodar.isEmpty()) {
            this.setMensagemAtualizacaoBD("Existem processos de atualização de banco de dados a serem rodados: " + String.join(", ", processosARodar) + ".\n");
        }
        if (versaoAtual < this.getVersaoBD()) {
            this.setMensagemAtualizacaoBD(this.getMensagemAtualizacaoBD() + "O Banco de dados do sistema encontra-se na versão " + versaoAtual + " enquanto a versão especificada da aplicação é a " + this.getVersaoBD() + ".");
        }

        this.setAbrirRichModalModulo(false);
        this.setAbrirRichModalDiasParaBloqueio(false);
        this.setAbrirRichModalSolicitarDesbloqueio(false);
        this.setAbrirRichModalBoletosInicio(false);
        this.setExibirModalAtualizacaoBD(true);
        this.setBancoDesatualizado(true);
        return "erroLogin";
    }

    public String abrirModalModulos() throws Exception {
        return abrirTelaDiasParaBloqueio();
    }

    private void preencherSessao() throws Exception {
        limparContextosSessao();
        setPermissaoAcessoMenuVO(new PermissaoAcessoMenuVO());
        montarPermissoesMenu(getPerfilAcesso(), getUsuario());
        processarPermissaoAcessoMenuTreino();
        processarPermissaoAcessoMenuAvaliacaoFisica();
        processarPermissaoAcessoMenuPactoPay();
        processarPermissaoAcessoMenuCross();
        processarPermissaoAcessoMenuGraduacao();
        processarPermissaoAcessoMenuAgenda();
        if (gerarLogControleUsabilidadeLogin) {
            gerarLogControleUsabilidade("Logado");
            gerarLogControleUsabilidadeLogin = false;
        }
        if (context() != null) {
            HttpSession session = (HttpSession) context().getExternalContext().getSession(true);
            session.setAttribute(LoginControle.NomeChaveUsuarioSessao, getUsuario());
            failOverCookie(getUsuario(), getEmpresa().getCodigo());

            Cookie c = new Cookie(JSFUtilities.COOKIE_KEY, getKey());
            JSFUtilities.addCookie(c);
            JSFUtilities.addCookie(new Cookie(JSFUtilities.COOKIE_LOGADO, "true"));
        }
        MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
        menuControle.processarMenuExplorar();
    }

    public String abrirModuloPessoas() {
        try {
            preencherSessao();
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.PESSOAS);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            if(getFuncionalidadeControle() != null) {
                getFuncionalidadeControle().setarFuncionalidade();
            }
            processarMenu();
            return executarRobo() ? "login" : "robo";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public String abrirZillyonWeb() {
        try {
            preencherSessao();
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.ZILLYONWEB);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            if (isApresentarLinkEstudio()) {
                inicializarConfiguracaoStudio();
            }
            if(getFuncionalidadeControle() != null) {
                getFuncionalidadeControle().setarFuncionalidade();
            }
            processarMenu();
            return executarRobo() ? "login" : "robo";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public String abrirFuncionalidade(String funcionalidade) {
        try {
            preencherSessao();
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.ZILLYONWEB);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            inicializarConfiguracaoStudio();
            processarMenu();
            return funcionalidade;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

     public String abrirModuloCRM() {
        try {
            preencherSessao();
            setAbrirRichModalModulo(false);
            this.setAbrirRichModalDiasParaBloqueio(false);
            this.setAbrirRichModalSolicitarDesbloqueio(false);
            this.setAbrirRichModalBoletosInicio(false);
            validarAberturaMeta();
            carregarNovoCRM();
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.CRMWEB);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            processarMenu();
            return executarRobo() ? "crm" : "robo";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public String getAbrirModuloCanalCliente() {
        return abrirModuloCanalCliente();
    }

    public void processarMenu(){
        ClientesMarcadosControle controlMarc = (ClientesMarcadosControle) JSFUtilities.getManagedBean(ClientesMarcadosControle.class.getSimpleName());
        if (controlMarc != null) {
            controlMarc.processaClientesMarcados();
        }
    }

    public String abrirModuloCanalCliente() {
        try {
            CanalPactoControle canalPactoControle = (CanalPactoControle) JSFUtilities.getFromSession(CanalPactoControle.class.getSimpleName());
             if (canalPactoControle == null) {
                canalPactoControle = new CanalPactoControle();
            }
            setModuloAberto(ModuloAberto.CANAL_CLIENTE);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            canalPactoControle.setPagDirecionar("");
            processarMenu();
            return canalPactoControle.abrirTelaMinhaConta();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public String abrirModuloCanalClientePactoStore() {
        try {
            CanalPactoControle canalPactoControle = (CanalPactoControle) JSFUtilities.getFromSession(CanalPactoControle.class.getSimpleName());
            if (canalPactoControle == null) {
                canalPactoControle = new CanalPactoControle();
            }
            setModuloAberto(ModuloAberto.CANAL_CLIENTE);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            processarMenu();
            return canalPactoControle.abrirTelaMinhaContaPactoStore();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public void abrirModuloCanalClienteViaPopUp() {
        try {
            CanalPactoControle canalPactoControle = (CanalPactoControle) JSFUtilities.getFromSession(CanalPactoControle.class.getSimpleName());
            if (canalPactoControle == null) {
                canalPactoControle = new CanalPactoControle();
            }
            setModuloAberto(ModuloAberto.CANAL_CLIENTE);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            canalPactoControle.abrirTelaMinhaContaViaPopUp();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String abrirModuloNotaFiscal() {
        try {
            preencherSessao();
            setAbrirRichModalModulo(false);
            this.setAbrirRichModalDiasParaBloqueio(false);
            this.setAbrirRichModalSolicitarDesbloqueio(false);
            this.setAbrirRichModalBoletosInicio(false);
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.NOTAS);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            NotaFiscalControle notaFiscalControle = (NotaFiscalControle) JSFUtilities.getFromSession(NotaFiscalControle.class.getSimpleName());
            if (notaFiscalControle != null) {
                notaFiscalControle.inicializar();
            }
            processarMenu();
            return executarRobo() ? "notaFiscal" : "robo";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public String abrirCarteirasCRM() {
        notificarRecursoEmpresa(RecursoSistema.CARTEIRAS);
        setJspMostrarCRM("./pages/crm/carteirasInicio.jsp");
        return "carteirasCRM";
    }

    private void limparContextosSessao() {
        JSFUtilities.storeOnSession("contexto", null);
        JSFUtilities.storeOnSession("contextoFinan", null);
    }

    private void consultarCaixaEmAbertoUsuario(UsuarioVO usuarioVo) throws Exception {
        CaixaControle caixaControle = (CaixaControle) JSFUtilities.getFromSession(CaixaControle.class.getSimpleName());
        if (caixaControle == null) {
            CaixaVO caixaVo = getFacade().getFinanceiro().getCaixa().consultarCaixaEmAberto(usuarioVo.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            getFacade().getFinanceiro().getCaixa().obterValoresResumidosCaixa(caixaVo, isApresentarOpenBank());
            JSFUtilities.setManagedBeanValue(CaixaControle.class.getSimpleName() + ".caixaVoEmAberto", caixaVo);
        }
    }

    public String abrirModuloFinanceiro() {
        try {
            preencherSessao();
            setAbrirRichModalModulo(false);
            this.setAbrirRichModalDiasParaBloqueio(false);
            this.setAbrirRichModalSolicitarDesbloqueio(false);
            this.setAbrirRichModalBoletosInicio(false);
            consultarCaixaEmAbertoUsuario(getUsuario());
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.FINAN);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            processarMenu();
            return executarRobo() ? "financeiro" : "robo";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    private void addHistoricoNavegacaoModulos(ModuloAberto moduloAberto){
        MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
        if(menuControle != null){
            menuControle.addHistoricoNavegacaoModulos(Modulo.fromSigla(moduloAberto.getSigla()));
        }
    }

    private void setSiglaModuloAtual(ModuloAberto moduloAberto){
        MenuControle menucontrole = JSFUtilities.getControlador(MenuControle.class);
        if(menucontrole != null){
            menucontrole.setSiglaModulo(moduloAberto.getSigla());
        }
    }

    public void validarAberturaMeta() throws Exception {
        AberturaMetaControle aberturaMetaControle = (AberturaMetaControle) getControlador("AberturaMetaControle");
        aberturaMetaControle.inicializarDados();

    }

    public String getAbrirAulaCheia() throws Exception {
        return abrirModuloTreino("aulacheia");
    }

    public String getAbrirNovaPlataforma(String moduloSigla) {
        return getAbrirNovaPlataforma(moduloSigla, null);
    }

    public String getAbrirNovaPlataforma(String moduloSigla, String matricula) {
        return getAbrirNovaPlataforma(moduloSigla, matricula, null);
    }

    public String getAbrirNovaPlataforma(String moduloSigla, String matricula, String redirect) {
        try {
            String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);

            Integer codigoEmpresa = getEmpresaLogado().getCodigo();

            String url;
            switch (moduloSigla) {
                case "NZW":
                    url = getUrlNovaPlataformaTreino() + "/" + getUsuario().getSiglaIdiomaNovaPlataforma();
                    break;
                case "PAY":
                    url = getUrlNovaPlataformaTreino() + "/" + getUsuario().getSiglaIdiomaNovaPlataforma();
                    break;
                case "NCRM" :
                    url = getUrlPactoCrmFront() + "/" + getUsuario().getSiglaIdiomaNovaPlataforma();
                    break;
                default:
                    url = getUrlNovaPlataformaTreino() + "/" + getUsuario().getSiglaIdiomaNovaPlataforma();
                    break;
            }
            url += "/adicionarConta?";
            Object sessionId = JSFUtilities.getFromSession("sessionId");
            Object tokenOamd = JSFUtilities.getFromSession("tokenOamd");
            if (sessionId != null) {
                url += "sessionId=" + sessionId;
            } else if (tokenOamd != null) {
                // O token sempre deve ser propagado via sessionId.
                // FIXME: Remover este após corrigir o redirect em todas as aplicações.
                url += "token=" + tokenOamd;
            } else if (tokenNZW != null) {
                // O token deve ser o mesmo, para todas  as aplicações
                // FIXME: Utilizar somente token, após corrgir em todas as aplicações.
                url += "token=" + tokenNZW;
            } else {
                url += "token=" + tokenNT;
            }
            url += "&moduleId=" + moduloSigla;
            if (matricula != null) {
                url += "&matricula=" + matricula;
            }
            url += "&idiomabanco=" + (UteisValidacao.emptyString(getUsuario().getSiglaIdiomaNovaPlataforma()) ?
                    0 : IdiomaBancoEnum.fromValue(getUsuario().getSiglaIdiomaNovaPlataforma().toUpperCase().replace("-", "_")).ordinal());
            url += "&modulos=" + modulos.replaceAll("\\s", "");
            url += "&integracaoZW=true";
            url += "&empresaId=" + codigoEmpresa;
            url += "&urladm=" + getTokenOamd();
            url += "&urlapi=" + getUrlTreino() + "/prest";
            url += "&usuarioOamd=" + getUsuario().getUserOamd();
            url += "&zwJSId=" + Uteis.encriptar(request().getSession(false).getId(), "chave_login_unificado");

            if(menuControle != null && menuControle.getUltimaPaginaAcessada() != null){
                String pagina = menuControle.getUltimaPaginaAcessada();
                if(pagina.equals("clientes.jsp") || pagina.equals("preCadastro.jsp")){
                    if(menuControle.getUltimasPaginasAcessadas() != null){
                        for(int i = menuControle.getUltimasPaginasAcessadas().size() - 1; i >= 0; i--){
                            if(menuControle.getUltimasPaginasAcessadas().get(i).equals("clientes.jsp") || menuControle.getUltimasPaginasAcessadas().get(i).equals("preCadastro.jsp")){
                                continue;
                            }
                            pagina = menuControle.getUltimasPaginasAcessadas().get(i);
                            break;
                        }
                    }
                }
                url += "&goBackPage=" + pagina;
            }

            if(menuControle != null && menuControle.getUltimoModuloVisitado() != null){
                url += "&goBackModule=" + menuControle.getUltimoModuloVisitado().getSiglaModulo();
            }

            if(redirect != null){
                url += "&redirect=" + redirect;
            }else if(menuControle != null && menuControle.getUrlGoBackRedirect() != null){
                url += "&redirect=" + menuControle.getUrlGoBackRedirect();
            }
            url += "&emailusuario=" + getUsuarioLogado().getEmail();
            return url;
        } catch (Exception e) {
            return "";
        }
    }

    private String validarUsuarioSistemaPacto() throws Exception {
        UsuarioMovelVO usuarioMovelVO = DaoAuxiliar.retornarAcessoControle(getKey()).getUsuarioMovelDao().consultarPorUsuario(getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("chave", getKey());
        jsonBody.put("username", getUsuario().getUsername());
        jsonBody.put("senha", "");
        Map<String, String> maps = new HashMap<>();
        maps.put("Content-Type", "application/json");
        String urlTreino = getUrlTreino() + "/prest/login/validar-usuario-movel/";

        String request = ExecuteRequestHttpService.executeHttpRequest(urlTreino, jsonBody.toString(), maps, "POST", "UTF-8");
        JSONObject json = new JSONObject(request);
        if (usuarioMovelVO != null && json.optJSONObject("meta") != null && "Dados incorretos!".equals(json.optJSONObject("meta").optString("message"))) {
            jsonBody.put("username", usuarioMovelVO.getNome());
            request = ExecuteRequestHttpService.executeHttpRequest(urlTreino, jsonBody.toString(), maps, "POST", "UTF-8");
            json = new JSONObject(request);
        }
        return json.optString("content");
    }

    private String getTokenOamd() {
        Map<String, String> tokenOamd = new HashMap<>();
        try {
            tokenOamd.put("oamd", getUserOamd());
            tokenOamd.put("login", (String) JSFUtilities.getFromSession("urlLogin"));
        } catch (Exception ex) {

        }
        return Uteis.encriptar(tokenOamd.toString(), "tkn@4@oamd");
    }

    public String getAbrirTreino() throws Exception {
        return abrirModuloTreino("treino");
    }

    public String getAbrirNZW() throws Exception {
        return getAbrirNovaPlataforma("NZW");
    }

    public String getAbrirNCRM() throws Exception {
        return getAbrirNovaPlataforma("NCRM");
    }

    public String getAbrirTreinoNovo() throws Exception {
        return getAbrirNovaPlataforma("NTR");
    }

    public String getAbrirPactoPay() throws Exception {
        return getAbrirNovaPlataforma("PAY");
    }

    public String getAbrirGraduacao() throws Exception {
        return getAbrirNovaPlataforma("GRD");
    }

    public String getAbrirAvaliacaoFisica() throws Exception {
        return getAbrirNovaPlataforma("NAV");
    }

    public String getAbrirCross() throws Exception {
        return getAbrirNovaPlataforma("NCR");
    }

    public String abrirModuloTreino(String modulo) throws Exception {
        return abrirModuloTreino(modulo, "", false);
    }

    public String abrirModuloTreino(String modulo, String matricula, boolean incluirAluno) throws Exception {
        try {
            //Valida se existe url do treino
            if (UteisValidacao.emptyString(Uteis.getURLLogin())) {
                return "";
            }

            JSONObject json = new JSONObject();
            json.put("login", "true");
            json.put("modulo", modulo);
            json.put("matricula", matricula);
            json.put("incluirAluno", incluirAluno);
            json.put("key", getKey());
            json.put("idUserZW", getUsuario().getCodigo().toString());
            json.put("idUserTR", getUsuario().getCodigo().toString());
            json.put("urlLogin", JSFUtilities.getFromSession("urlLogin"));
            if (getEmpresa() != null && getEmpresa().getCodigo() != null && getEmpresa().getCodigo() > 0) {
                json.put("idEmpresa", getEmpresa().getCodigo().toString());
            } else {
                json.put("idEmpresa", "0");
            }
            json.put("userOamd", getUsuario().getUserOamd());
            SimpleDateFormat formataData = new SimpleDateFormat("dd/MM/yyyy");
            json.put("dataSistema", (Calendario.hoje() != null ? formataData.format(Calendario.hoje()) : ""));
            Calendar dataCalendar = Calendar.getInstance();
            dataCalendar.add(Calendar.MINUTE, 30);
            json.put("timevld", dataCalendar.getTimeInMillis());

            try {
                if ((Boolean) JSFUtilities.getFromSession(OIDServlet.URL_ZW_DIFERENTE_TREINO)) {
                    json.put(OIDServlet.URL_ZW_DIFERENTE_TREINO, true);
                    json.put(OIDServlet.URL_ZW_LOCAL, JSFUtilities.getFromSession(OIDServlet.URL_ZW_LOCAL));
                }
            } catch (Exception ignored) {
                json.put(OIDServlet.URL_ZW_DIFERENTE_TREINO, false);
            }

            String params = "/oid?lgn=" + Uteis.encriptar(json.toString(), "chave_login_unificado");
            return getUrlTreino() + params;
        } catch (JSONException ex) {
            System.out.println(ex.getMessage());
            return "";
        }
    }

    public void carregarNovoCRM() throws Exception {
        MetaCRMControle metaCRMControle = (MetaCRMControle) getControlador("MetaCRMControle");
        metaCRMControle.setMetasProcessadas(false);

    }

    public void irParaZillyonWebDeDentroCRM() {
        try {
        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    public String irParaTelaRelatorios() throws Exception {
        try {
            return "indexRelatorio";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public String inicializarMetodosTelaInicial() throws Exception {
        try {
            return "indexRelatorio";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    /**
     * Metodo que inicializa os relatorios de BI e chama a tela de relatorios do
     * Central de eventos
     * <p>
     * Autor: Pedro Y. Saito Criado em 09/02/2011
     */

    public void logout() {
        try {
            String retorno;
            if (!isNFe()) {
                gerarLogControleUsabilidade("Logout");
            }
            setUsuario(new UsuarioVO());
            setPerfilAcesso(new PerfilAcessoVO());
            ControleAcesso.setUsuario(new UsuarioVO());
            ControleAcesso.setPerfilAcesso(new PerfilAcessoVO());
            HttpSession httpSession = (HttpSession) context().getExternalContext().getSession(false);
            httpSession.invalidate();
            JSFUtilities.addCookie(new Cookie(JSFUtilities.COOKIE_LOGADO, "false"));

            JSFUtilities.setManagedBeanValue(LogoutControle.class.getSimpleName()
                    + ".deveEfetuarLogout", true);

            setMensagemID("msg_entre_prmlogout");

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public static void habilitarProcessarRobo() {
        LoginControle lg = (LoginControle) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("LoginControle");
        try {
            //não exigir usuário mais, qualquer pessoa pode disparar o processo desatualizado
            //lg.validarPermissaoProcessarRobo();
            if (lg.getRotinaEmAndamento()) {
                lg.setAtivarPool(false);
                lg.setAtivarPoolProcessamento(false);
                lg.setMensagem("Rotina já foi processada por outro usuário.. tente logar novamente");
                lg.setMensagemRobo("Rotina processada com sucesso.. tente logar novamente");
                lg.setMensagemDetalhada("");
                lg.setMensagemID("");
            } else {
                lg.setRotinaEmAndamento(true);
                lg.setApresentarProcessamento(true);
                lg.setAtivarPoolProcessamento(true);
                lg.setAtivarPool(true);
            }

        } catch (Exception e) {
            lg.setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public static void processarRoboDesatualizado() {
        LoginControle lg = (LoginControle) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("LoginControle");
        try {
            lg.setAtivarPool(false);
            Boolean rodou = getFacade().getRobo().consultarRotinaRobo(Calendario.hoje());
            if (rodou) {
                lg.setMensagem("Rotina já foi processada por outro usuário.. tente logar novamente");
            } else {
                if (lg.getDataUltimoProcessamentoRobo() == null) {
                    Calendar cal = Calendario.getInstance();
                    cal.add(GregorianCalendar.DATE, -1);
                    lg.setDataUltimoProcessamentoRobo(cal.getTime());
                }
                lg.setDataProcessamentoRobo(Uteis.obterDataFutura2(lg.getDataUltimoProcessamentoRobo(), 1));
                Boolean processarDia = true;
                RoboControle robo = new RoboControle();
                while (processarDia) {
                    robo.getRobo().setUsuarioVO(lg.getUsuario());
                    if (Uteis.getCompareData(lg.getDataProcessamentoRobo(), Calendario.hoje()) == 0) {//se já processou até a data atual, deve gerar o sintético
                        robo.getRobo().setDeveGerarSintetico(true);
                    } else {
                        robo.getRobo().setDeveGerarSintetico(false);
                    }
                    robo.startRoboDesatualizado(lg.getDataProcessamentoRobo(), "");
                    if (Uteis.getCompareData(lg.getDataProcessamentoRobo(), Calendario.hoje()) == 0) {
                        processarDia = false;
                    } else {
                        lg.setDataProcessamentoRobo(Uteis.obterDataFutura2(lg.getDataProcessamentoRobo(), 1));

                    }
                }
                //Criar Thread para gerar os Agendamentos do Módulo Financeiro.
                ThreadAgendamentoFinanceiro.gerarParcelasParaAgendamento(Conexao.getFromSession(), lg.getDataProcessamentoRobo());
            }
            lg.setAtivarPoolProcessamento(false);
            lg.setMensagemRobo("Rotina processada com sucesso. Tente logar novamente.");
            lg.setApresentarBotoesRobo(false);
            lg.setMensagemDetalhada("");
            lg.setMensagem("");
            lg.setMensagemID("");
        } catch (Exception e) {
            lg.setAtivarPool(false);
            lg.setRotinaEmAndamento(false);
            lg.setAtivarPoolProcessamento(false);
            lg.setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void validarPermissaoProcessarRobo() throws Exception {
        try {
            setUsuario(getFacade().getControleAcesso().verificarLoginUsuario(getUsername(), getSenha().toUpperCase()));
            if (getUsuario().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuario().getAdministrador()) {
                    setMensagemDetalhada("");
                    return;
                }
                throw new Exception("O usuário informado não possui perfil de acesso.");
            }

            if (getEmpresa().getCodigo().intValue() == 0) {
                throw new Exception("Não há nenhuma empresa logada");
            }
            Iterator i = getUsuario().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresa().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(
                            getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), getUsuario(),
                            "ProcessarRotinaRobo_Autorizar", "1.06 - Processar Rotina Robo - Autorizar");
                }
            }
            setMensagemDetalhada("");
        } catch (Exception e) {
            throw e;
        }
    }

    public void limparMensagem() {
        setMensagem("");
        setMensagemDetalhada("");
        setMensagemID("");
        setApresentarProcessamento(false);
    }

    public String voltar() throws Exception {
        setMensagem("");
        setMensagemDetalhada("");
        setMensagemID("");
        setApresentarProcessamento(false);
        setUsuario(new UsuarioVO());
        setUsername("");
        setSenha("");
        return "erroLogin";
    }

    public void gerarLogControleUsabilidade(String acao) throws Exception {
        LogControleUsabilidadeVO obj = new LogControleUsabilidadeVO();
        obj.setUsuario(getUsuario());
        obj.setEmpresa(getEmpresa());
        obj.setAcao(acao);
        obj.setEntidade(acao);
        obj.setUserOamd(getUsuario().getUserOamd());

        obj.setMaquina(getIpCliente());

        obj.setDataRegistro(Calendario.hoje());
        getFacade().getLogControleUsabilidade().incluir(obj);
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>CodPerfilAcesso</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), "", ""));
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString(), obj.getNome().toString()));
        }
        setListaSelectItemEmpresa(objs);
    }

    public void montarPermissoesMenu(PerfilAcessoVO perfilAcesso, UsuarioVO usuario) {
        setPermissaoAcessoMenuVO(getPermissaoAcessoMenuVO().montarPermissoesMenu(perfilAcesso.getPermissaoVOs(), usuario, perfilAcesso.isUnificado()));
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>CodPerfilAcesso</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>PerfilAcesso</code>. Esta rotina não recebe parâmetros para
     * filtragem de dados, isto é importante para a inicialização dos dados da
     * tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void consultarLogObjetoSelecionado(String nomeEntidade, Integer codigoEntidade, Integer codigoPessoa, boolean agruparSegundo) {
        setListaConsultaLog(new ArrayList());
        try {
            context().getExternalContext().getSessionMap().put("titulo", getNomeClasse());
            List lista = getFacade().getLog().consultarPorNomeCodigoEntidadeAgrupado(nomeEntidade, codigoEntidade, null, null, codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, agruparSegundo);
            setListaConsultaLog(lista);
        } catch (Exception e) {
            setListaConsultaLog(new ArrayList());
            getListaConsultaLog().clear();
        }
    }

    public void consultarLogListaObjetosSelecionados(Map<Integer, String> nomesCodigosEntidade, Integer codigoPessoa, boolean agruparSegundo) {
        setListaConsultaLog(new ArrayList<>());
        try {
            context().getExternalContext().getSessionMap().put("titulo", getNomeClasse());

            List<Object> listaTotal = new ArrayList<>();

            for (Map.Entry<Integer, String> entry : nomesCodigosEntidade.entrySet()) {
                Integer codigoEntidade= entry.getKey();
                String nomeEntidade = entry.getValue();

                List listaParcial = getFacade().getLog().consultarPorNomeCodigoEntidadeAgrupado(nomeEntidade, codigoEntidade, null, null, codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, agruparSegundo);

                listaTotal.addAll(listaParcial);
            }

            // Ordenar a lista total por data de alterao
            Collections.sort(listaTotal, (obj1, obj2) -> {
                Date dataAlteracao1 = ((LogVO) obj1).getDataAlteracao();
                Date dataAlteracao2 = ((LogVO) obj2).getDataAlteracao();

                return dataAlteracao2.compareTo(dataAlteracao1);
            });

            setListaConsultaLog(listaTotal);
        } catch (Exception e) {
            setListaConsultaLog(new ArrayList<>());
            getListaConsultaLog().clear();
        }
    }

    public void consultarLogObjetoSelecionado(String nomeEntidade, Integer codigoEntidade, Integer codigoPessoa) {
        consultarLogObjetoSelecionado(nomeEntidade, codigoEntidade, codigoPessoa, true);
    }


    public void consultarLogListaObjetosSelecionados(Map<Integer, String> nomesCodigosEntidade, Integer codigoPessoa) {
        consultarLogListaObjetosSelecionados(nomesCodigosEntidade, codigoPessoa, true);
    }

    public void limparListaLog() {
        setListaConsultaLog(new ArrayList());
        getListaConsultaLog().clear();
    }

    public void selecionarDadosLog() throws Exception {
        LogVO obj = (LogVO) context().getExternalContext().getRequestMap().get("log");
        setLogVO(obj);
    }

    public String getNomeEntidadeCamposLog(String mensagemID) {
        String nomes = "";
        ResourceBundle bundle = null;
        Locale locale = null;
        // String nomeBundle = context().getApplication().getMessageBundle();
        String nomeBundle = "propriedades.Aplicacao";
        if (nomeBundle != null) {
            locale = context().getViewRoot().getLocale();
            bundle = ResourceBundle.getBundle(nomeBundle, locale);
            try {
                nomes = bundle.getString(mensagemID);
                return nomes;
            } catch (MissingResourceException e) {
                return nomes;
            }
        }
        return nomes;
    }

    public void alterarSenhaCliente() {
        try {
            validarDados();
            String senhaVelha = getUsuario().getSenha();

            if (!getSenhaAtual().equalsIgnoreCase(senhaVelha)) {
                throw new Exception(""); // a senha atual, é diferente da senha
                // digitada
            }
            if (!getSenha1().equalsIgnoreCase(getSenha2())) {
                throw new Exception("");// a nova senha é diferente da
                // confirmacao da senha ?
            }
            UsuarioVO usuarioAlterar = new UsuarioVO();
            usuarioAlterar = getFacade().getUsuario().consultarPorChavePrimaria(getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            usuarioAlterar.setUsername(getUsername());
            usuarioAlterar.setSenha(getSenha2());
            //gravar data da última alteração para verificar expiração de senha ao logar
            usuarioAlterar.setDataUltimaAlteracaoSenha(Calendario.hoje());
            getFacade().getUsuario().alterarSenhaUsuario(usuarioAlterar, usuarioAlterar.getPermiteAlterarPropriaSenha());
            setMensagemID("msg_senhaAlterada_cli");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
            if (getModuloAberto() != null) {
                if (getModuloAberto().equals(ModuloAberto.ZILLYONWEB)
                        || getModuloAberto().equals(ModuloAberto.CE)
                        || getModuloAberto().equals(ModuloAberto.CRMWEB)
                        || getModuloAberto().equals(ModuloAberto.FINAN)) {
                    setJavaScriptFecharPopUpAlteracaoSenha("window.close();fireElementFromParent('form:btnAtualizaPagina');");
                } else if (getModuloAberto().equals(ModuloAberto.GESTAOSTUDIO)) {
                    setJavaScriptFecharPopUpAlteracaoSenha("window.close();fireElementFromParent('btnAtualizaPagina');");
                }
            } else {
                setJavaScriptFecharPopUpAlteracaoSenha("window.close();fireElementFromParent('form:btnAtualizaPagina');");
            }
            setMostrarModalExpiracaoSenha(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_senha_cli", e.getMessage());
            setJavaScriptFecharPopUpAlteracaoSenha("");
        }

    }

    public void validarDados() throws Exception {
        if (!getUsuarioLogado().getPermiteAlterarPropriaSenha()) {
            throw new Exception("Usuário não possui permissão para alterar sua própria senha");
        }
        if (getNome().equals("")) {
            throw new Exception("O campo NOME USUÁRIO (Usuário) deve ser informado");
        }
        if (getUsername().equals("")) {
            throw new Exception("O campo USER NAME (Usuário) deve ser informado");
        }
        if (getSenhaAtual().equals("")) {
            throw new Exception("O campo SENHA ATUAL (Usuário) deve ser informado");
        }
        if (getSenha1().equals("")) {
            throw new Exception("O campo NOVA SENHA (Usuário) deve ser informado");
        }
        if (getSenha2().equals("")) {
            throw new Exception("O campo CONFIRMAR SENHA (Usuário) deve ser informado");
        }
    }

    public String getMensagemSuporte() {
        if (mensagemSuporte == null) {
            try {
                EmpresaVO empresaLogada = getEmpresaLogado();
                UsuarioVO usuarioLogado = getUsuarioLogado();

                StringBuilder sb = new StringBuilder();
                sb.append("Empresa: ").append(empresaLogada.getNome()).append("\n");
                sb.append("Código Cliente: ").append(empresaLogada.getCodEmpresaFinanceiro()).append("\n");
                if (getRedeEmpresaVO() != null) {
                    sb.append("Rede: ").append(getRedeEmpresaVO().getNome()).append("\n");
                }
                sb.append("Solicitante: ").append(usuarioLogado.getNome()).append("\n");
                sb.append("Cargo: ").append(getPerfilAcesso().getNome()).append("\n");
                String nivelAtendimento = (String) JSFUtilities.getFromSession(JSFUtilities.NIVEL_ATENDIMENTO);
                if (!UteisValidacao.emptyString(nivelAtendimento)) {
                    sb.append("Categoria: ").append(nivelAtendimento);
                }
                mensagemSuporte = URLEncoder.encode(sb.toString(), "UTF-8");
            } catch (Exception ex) {
                montarErro("Erro ao preparar mensagem para Gymbot");
                ex.printStackTrace();
            }
        }
        return mensagemSuporte;
    }

    public void setMensagemSuporte(String mensagemSuporte) {
        this.mensagemSuporte = mensagemSuporte;
    }

    // /**
    // * Metodo para capturar a foto do cliente. *
    // * @param event
    // * @throws java.io.IOException
    // * @throws java.lang.Exception trata o erro se a matricula não for gerada
    // para que a foto tenha o mesmo nome da matricula
    // */
    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        if (getEmpresa().getFoto() == null || getEmpresa().getFoto().length == 0) {
            getEmpresa().setFoto(getFacade().getEmpresa().obterFoto(getKey(),
                    getEmpresa().getCodigo(),
                    MidiaEntidadeEnum.FOTO_EMPRESA));
            if (!UteisValidacao.emptyNumber(getEmpresa().getCodigo()) && isFotosNaNuvem()) {
                getEmpresa().setFotoKey(MidiaService.getInstance().genKey(getKey(),
                        MidiaEntidadeEnum.FOTO_EMPRESA, getEmpresa().getCodigo().toString()));
            }
        }
        SuperControle.paintFotoEmpresa(out, getEmpresa().getFoto());
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getEmpresa().getFotoKey());
    }

    public Boolean getUsuarioisColaborador() throws Exception {
        return getUsuario().getColaboradorVO().getCodigo() != 0;
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNomeLogin(nomePrm, true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public String getSenha() {
        if (senha == null) {
            senha = "";
        }
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List ListaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = ListaSelectItemEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public List getListaConsultaLog() {
        return listaConsultaLog;
    }

    public void setListaConsultaLog(List listaConsultaLog) {
        this.listaConsultaLog = listaConsultaLog;
    }

    public LogVO getLogVO() {
        return logVO;
    }

    public void setLogVO(LogVO logVO) {
        this.logVO = logVO;
    }

    public String getNomeClasse() {
        if (nomeClasse == null) {
            nomeClasse = "";
        }
        return nomeClasse;
    }

    public void setNomeClasse(String nomeClasse) {
        this.nomeClasse = nomeClasse;
    }

    public String getSenha1() {
        if (senha1 == null) {
            senha1 = "";
        }
        return senha1;
    }

    public void setSenha1(String senha1) throws UnsupportedEncodingException {
        this.senha1 = senha1;
    }

    public String getSenha2() {
        if (senha2 == null) {
            senha2 = "";
        }
        return senha2;
    }

    public void setSenha2(String senha2) throws UnsupportedEncodingException {
        this.senha2 = senha2;
    }

    public String getSenhaAtual() {
        if (senhaAtual == null) {
            senhaAtual = "";
        }
        return senhaAtual;
    }

    public void setSenhaAtual(String senhaAtual) throws UnsupportedEncodingException {
        senhaAtual = senhaAtual.toUpperCase();
        this.senhaAtual = Uteis.encriptar(senhaAtual);
    }

    /**
     * @return the permissaoAcessoMenuVO
     */
    public PermissaoAcessoMenuVO getPermissaoAcessoMenuVO() {
        try {
            if (permissaoAcessoMenuVO == null) {
                setPermissaoAcessoMenuVO(new PermissaoAcessoMenuVO().montarPermissoesMenu(getPerfilAcesso().getPermissaoVOs(), getUsuario(), getPerfilAcesso().isUnificado()));
            }
        } catch (Exception ex) {
            System.out.println("Não foi possivel montar permissaoAcessoMenuVO");
        }

        return permissaoAcessoMenuVO;
    }

    /**
     * @param permissaoAcessoMenuVO the permissaoAcessoMenuVO to set
     */
    public void setPermissaoAcessoMenuVO(PermissaoAcessoMenuVO permissaoAcessoMenuVO) {
        this.permissaoAcessoMenuVO = permissaoAcessoMenuVO;
    }

    public List getListTipoColaborador() {
        return listTipoColaborador;
    }

    public void setListTipoColaborador(List listTipoColaborador) {
        this.listTipoColaborador = listTipoColaborador;
    }

    public Boolean getApresentarBotoesRobo() {
        return apresentarBotoesRobo;
    }

    public void setApresentarBotoesRobo(Boolean apresentarBotoesRobo) {
        this.apresentarBotoesRobo = apresentarBotoesRobo;
    }

    public Date getDataUltimoProcessamentoRobo() {
        return dataUltimoProcessamentoRobo;
    }

    public void setDataUltimoProcessamentoRobo(Date dataUltimoProcessamentoRobo) {
        this.dataUltimoProcessamentoRobo = dataUltimoProcessamentoRobo;
    }

    public String getMensagemRobo() {
        if (mensagemRobo == null) {
            mensagemRobo = "";
        }
        return mensagemRobo;
    }

    public void setMensagemRobo(String mensagemRobo) {
        this.mensagemRobo = mensagemRobo;
    }

    public Boolean getApresentarProcessamento() {
        return apresentarProcessamento;
    }

    public void setApresentarProcessamento(Boolean apresentarProcessamento) {
        this.apresentarProcessamento = apresentarProcessamento;
    }

    public String getDataProcessamentoRobo_Apresentar() {
        return Uteis.getData(dataProcessamentoRobo);
    }

    public Date getDataProcessamentoRobo() {
        return dataProcessamentoRobo;
    }

    public void setDataProcessamentoRobo(Date dataProcessamentoRobo) {
        this.dataProcessamentoRobo = dataProcessamentoRobo;
    }

    public Boolean getAtivarPoolProcessamento() {
        return ativarPoolProcessamento;
    }

    public void setAtivarPoolProcessamento(Boolean ativarPoolProcessamento) {
        this.ativarPoolProcessamento = ativarPoolProcessamento;
    }

    public Boolean getAtivarPool() {
        return ativarPool;
    }

    public void setAtivarPool(Boolean ativarPool) {
        this.ativarPool = ativarPool;
    }

    public Boolean getRotinaEmAndamento() {
        return rotinaEmAndamento;
    }

    public void setRotinaEmAndamento(Boolean rotinaEmAndamento) {
        this.rotinaEmAndamento = rotinaEmAndamento;
    }

    /**
     * @return the abrirRichModalModulo
     */
    public Boolean getAbrirRichModalModulo() {
        return abrirRichModalModulo;
    }

    /**
     * @param abrirRichModalModulo the abrirRichModalModulo to set
     */
    public void setAbrirRichModalModulo(Boolean abrirRichModalModulo) {
        this.abrirRichModalModulo = abrirRichModalModulo;
    }

    public Boolean getApresentarLinkParaModuloCRM() {
        //return ControleAcesso.verificarPermissaoCRM();
        return isApresentarLinkCRM();
    }


    public void abrirSocialMailing() {
        try {
            preencherSessao();
            setMsgAlert("");
            try {
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(getPerfilAcesso(), getUsuario(),
                        "PermissaoAcessarSocialMailing", "2.40 - Permissão para Acessar o Social Mailing");

                SocialMailingControle social = (SocialMailingControle) getControlador(SocialMailingControle.class.getSimpleName());
                UsuarioControle usuarioControle = (UsuarioControle) getControlador(UsuarioControle.class.getSimpleName());
                usuarioControle.getUsuario().setNrMensagensNaoLidas(0);
                social.setTipoSocialMail(TipoSocialMailEnum.CONVERSAS);
                social.atualizarConversas();
                setMsgAlert("abrirPopup('" + JSFUtilities.getRequest().getContextPath() + "/faces/socialMailing.jsp', 'SocialMailing', 725, 620)");

            } catch (Exception e) {
                montarMsgAlert(e.getMessage());
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void abrirSocialMailingSolicitacao() {
        try {
            preencherSessao();
            setMsgAlert("");
            try {
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(getPerfilAcesso(), getUsuario(),
                        "PermissaoAcessarSocialMailing", "2.40 - Permissão para Acessar o Social Mailing");
                SocialMailingControle social = (SocialMailingControle) getControlador(SocialMailingControle.class.getSimpleName());
                UsuarioControle usuarioControle = (UsuarioControle) getControlador(UsuarioControle.class.getSimpleName());
                SocialMailGrupoVO socialMailGrupoVO = (SocialMailGrupoVO) context().getExternalContext().getRequestMap().get("grupo");
                social.setTipoSocialMail(TipoSocialMailEnum.SOLICITACAO);
                if (socialMailGrupoVO != null) {
                    social.preparaAbrirSolicitacoes();
                    social.escolherGrupo(socialMailGrupoVO);
                } else {
                    social.atualizarConversas();
                }
                usuarioControle.atualizarNrMsgNaoLidas(false);
                setMsgAlert("abrirPopup('" + JSFUtilities.getRequest().getContextPath() + "/faces/socialMailing.jsp', 'SocialMailing', 725, 620)");
            } catch (Exception e) {
                montarMsgAlert(e.getMessage());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String abrirCE() {
        try {
            setPermissaoAcessoMenuVO(new PermissaoAcessoMenuVO());
            montarPermissoesMenu(getPerfilAcesso(), getUsuario());
            gerarLogControleUsabilidade("Logado");
            FacesContext facesContext = FacesContext.getCurrentInstance();
            HttpSession session = (HttpSession) facesContext.getExternalContext().getSession(true);
            session.setAttribute("logado", getUsuario());
            setMsgAlert("");
            try {
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(getPerfilAcesso(), getUsuario(),
                        "ModuloCE", "8.01 - Módulo CE");
            } catch (Exception e) {
                montarMsgAlert(e.getMessage());
                return "";
            }
            AutorizacaoRegras regras = (AutorizacaoRegras) JSFUtilities.getFromSession("Regras");
            if (regras == null) {
                regras = new AutorizacaoRegras();
            }
            regras.inicializarAtributos();
            JSFUtilities.storeOnSession("Regras", regras);
            // o CE não roda o robô
            // return executarRobo() ? "loginCE" : "robo";
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.CE);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            montarModulosHabilitados();
            return "loginCE";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    private void inicializarConfiguracaoStudio() throws Exception {
        ConfiguracaoEstudioVO configuracaoEstudioVO = (ConfiguracaoEstudioVO) JSFUtilities.getFromSession("configuracaoEstudio");
        if (configuracaoEstudioVO == null) {
            configuracaoEstudioVO = new ConfiguracaoEstudioVO();
            configuracaoEstudioVO.setEmpresaVO(getEmpresa());
            configuracaoEstudioVO = getFacade().getConfiguracaoEstudio().consultarPorCodigo(configuracaoEstudioVO, false);
            JSFUtilities.storeOnSession("configuracaoEstudio", configuracaoEstudioVO);
        }
    }

    public String abrirModuloEstudio() {
        try {
            preencherSessao();
            inicializarConfiguracaoStudio();
            setAbrirRichModalModulo(false);
            setModuloAberto(ModuloAberto.GESTAOSTUDIO);
            this.moduloAbertoSigla = this.moduloAberto.getSigla();
            processarMenu();
            return "loginEstudio";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public void acaoFecharModalEmpresaEstudio() {
        try {
            inicializarConfiguracaoStudio();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getLinkModuloUCP() throws Exception {
        return getContextPath() + "/redir?up";
    }

    public String getAbrirModuloGame() {
        return new BIControle().getUrlGame();
    }

    /**
     * Executa o Robô checando todas as circunstâncias necessárias.
     *
     * @return <code>boolean</code> indicando se o robô foi executado.
     * @throws Exception
     * <AUTHOR>
     */
    private Boolean executarRobo() throws Exception {
        setAbrirRichModalModulo(false);
        this.setAbrirRichModalDiasParaBloqueio(false);
        this.setAbrirRichModalSolicitarDesbloqueio(false);
        this.setAbrirRichModalBoletosInicio(false);

        return !verificarRoboExecutouCorretamente().equals(false);
        /*
         * ThreadRoboControle threadRoboControle = (ThreadRoboControle)
         * context().getExternalContext().getApplicationMap().get(
         * "ThreadRoboControle"); if (threadRoboControle != null) {
         * threadRoboControle.getThreadRobo().setDiasDesatualizados(diasSemProcessamento);
         * threadRoboControle.iniciarRobo();
         }
         */
    }

    /**
     * Verifica se a execução do Robô ocorreu como esperado.
     *
     * @return <code>true</code> caso o Robô tenha sido executado no dia atual
     * ou até 4 dias antes e <code>false</code> caso contrário.
     * @throws Exception
     * <AUTHOR>
     */
    private Boolean verificarRoboExecutouCorretamente() throws Exception {
        boolean sucesso = true;
        /**
         * Verificar se está em ambiente JSF e se Robô deve ser executado apenas
         * em regime Serviço (noturno) sem interferir no uso da camada Web.
         * Setar o flag 'verificarExecucaoRoboBackground' caso o processamento
         * diário já tenha se iniciado
         */
        this.setVerificarExecucaoRoboBackground(getFacade().getRobo().diaAtualEstaEmProcessamento(Calendario.hoje()));
        this.setApresentarIconeExecucaoRoboBackground(this.isVerificarExecucaoRoboBackground());
        if (Conexao.isRoboApenasComoServico()) {
            return sucesso;
        }

        // consultar para saber se o houve processamento nesse dia.
        Boolean rodou = getFacade().getRobo().consultarRotinaRobo(Calendario.hoje());

        // consultar novamente para saber se ou processamento futuro a essa data.
        if (verificarSeDataExecucaoEFutura()) {
            sucesso = false;
        }

        if (!rodou) {
            obterNumeroDiasSemProcessamento();
            /*
             * se houver mais de quatro dias que o Robô não é executado: então, pede a intervenção do usuário; senão, executa o robô
             * automaticamente, considerando que as datas estão corretas.
             */
            /*if (isExibeMensagemRoboAtualizando()) {
             // W.M - 25/10/2010 - Já retorno verdadeiro, pois se houver dias desatualizados, o método
             // já preencheu a lista dos dias. A Thread será iniciada com esses dias,
             // quando o usuário logar.

             return true;

             } else {*/
            setMensagemRobo("Não foi possivel logar no sistema, pois houve um problema com o servidor.\n\r"
                    + " Você está tentando logar com a data do dia: " + Uteis.getData(Calendario.hoje()) + ".\n\r"
                    + "E o robô está DESATUALIZADO, a última vez em que o robô processou sua rotina foi no dia "
                    + Uteis.getData(getDataUltimoProcessamentoRobo()) + ". "
                    + "Você pode resolver este problema clicando no botão abaixo e aguardar até que seja finalizado este processamento. "
                    + "Isso levará poucos minutos.");
            setApresentarBotoesRobo(true);
            setApresentarProcessamento(false);
            setAtivarPoolProcessamento(false);
            setAtivarPool(false);
            setRotinaEmAndamento(false);
            setDataProcessamentoRobo(Uteis.obterDataFutura2(getDataUltimoProcessamentoRobo(), 1));
            setUsername("");
            setSenha("");
            setUsuario(new UsuarioVO());
            sucesso = false;
            //}
        }

        return sucesso;
    }

    /**
     * Verifica se a última data em que o Robô foi executado é maior que a data
     * corrente do sistema.
     *
     * @return <code>true</code> caso a última data em que o Robô foi executado
     * é maior que a data corrente do sistema, <code>false</code> caso
     * contrário.
     * @throws Exception
     * <AUTHOR>
     */
    private Boolean verificarSeDataExecucaoEFutura() throws Exception {
        Boolean rodou = getFacade().getRobo().validarSeExisteDia(Calendario.hoje());
        if (rodou) {
            setDataUltimoProcessamentoRobo(getFacade().getRobo().consultaParaObterUltimoDiaprocessado());
            setMensagemRobo("Não foi possivel logar no sistema, pois houve um problema com a data do servidor.\n\r"
                    + " Você está tentando logar com a data do dia: " + Uteis.getData(Calendario.hoje()) + ".\n\r"
                    + " E foi encontrado no banco de dados uma data maior que é: " + Uteis.getData(getDataUltimoProcessamentoRobo())
                    + " chame o administrador do sistema para modificar a data do servidor para "
                    + Uteis.getData(getDataUltimoProcessamentoRobo()));
            setApresentarBotoesRobo(false);
        }
        return rodou;
    }

    public void vazio() {
        // método usado para evitar o componente de status atrapalhar o popup de tela
    }

    public Long obterNumeroDiasSemProcessamento() throws Exception {
        diasSemProcessamento.clear();
        setDataUltimoProcessamentoRobo(getFacade().getRobo().consultaParaObterUltimoDiaprocessado());
        Long numDiasSemProcessamento = new Long(1);

        if (getDataUltimoProcessamentoRobo() != null) {
            Date dataUltProcessamento = Uteis.getDate(Uteis.getData(getDataUltimoProcessamentoRobo(), "dd/MM/yyyy"));
            numDiasSemProcessamento = Uteis.nrDiasEntreDatasSemHoraZerada(dataUltProcessamento, Calendario.hoje());
            if ((numDiasSemProcessamento.longValue() > 0) /*
             * && (numDiasSemProcessamento.longValue() <= 4)
             */) {
                Calendar cal = Calendario.getInstance();
                cal.setTime(dataUltProcessamento);
                cal.add(Calendar.DAY_OF_MONTH, 1);
                for (long i = 0; i < numDiasSemProcessamento; i++) {
                    DiaRoboVO.adicionaUnicamente(cal.getTime(), diasSemProcessamento);
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }
                Ordenacao.ordenarLista(diasSemProcessamento, "dia");
            }
        } else {
            Calendar cal = Calendario.getInstance();
            cal.add(GregorianCalendar.DATE, -1);
            setDataProcessamentoRobo(cal.getTime());
            setDataUltimoProcessamentoRobo(getDataProcessamentoRobo());
            Date dataUltProcessamento = Uteis.getDate(Uteis.getData(getDataUltimoProcessamentoRobo(), "dd/MM/yyyy"));
            numDiasSemProcessamento = Uteis.nrDiasEntreDatasSemHoraZerada(dataUltProcessamento, Calendario.hoje());
            if ((numDiasSemProcessamento.longValue() > 0) /*
             * && (numDiasSemProcessamento.longValue() <= 4)
             */) {
                cal.setTime(dataUltProcessamento);
                cal.add(Calendar.DAY_OF_MONTH, 1);
                for (long i = 0; i < numDiasSemProcessamento; i++) {
                    DiaRoboVO.adicionaUnicamente(cal.getTime(), diasSemProcessamento);
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }
                Ordenacao.ordenarLista(diasSemProcessamento, "dia");
            }

        }
        return numDiasSemProcessamento;
    }

    public boolean isExibeMensagemRoboAtualizando() throws Exception {
        //Long numDias = obterNumeroDiasSemProcessamento();
        //return ((numDias.longValue() > 0)/* && (numDias.longValue() <= 4)*/);
        return false;
    }

    /*
     * --------------- ATUALIZADOR DE BANCO DE DADOS ---------------
     */
    private Date horarioAnteriorAtualizacao;
    private String mensagemAtualizacaoBD;
    private Boolean exibirModalAtualizacaoBD;
    private Boolean exibirModalExibicaoAtualizacoesBD;
    private Boolean bancoDesatualizado;
    private List<AtualizacaoTO> atualizacoesBD;
    private String detalheAtualizacao;

    /**
     * @return O campo mensagemAtualizacaoBD.
     */
    public String getMensagemAtualizacaoBD() {
        return mensagemAtualizacaoBD;
    }

    /**
     * @param mensagemAtualizacaoBD O novo valor de mensagemAtualizacaoBD.
     */
    public void setMensagemAtualizacaoBD(String mensagemAtualizacaoBD) {
        this.mensagemAtualizacaoBD = mensagemAtualizacaoBD;
    }

    /**
     * @return O campo exibirModalAtualizacaoBD.
     */
    public Boolean getExibirModalAtualizacaoBD() {
        if (exibirModalAtualizacaoBD == null) {
            exibirModalAtualizacaoBD = Boolean.FALSE;
        }
        return exibirModalAtualizacaoBD;
    }

    /**
     * @param exibirModalAtualizacaoBD O novo valor de exibirModalAtualizacaoBD.
     */
    public void setExibirModalAtualizacaoBD(Boolean exibirModalAtualizacaoBD) {
        this.exibirModalAtualizacaoBD = exibirModalAtualizacaoBD;
    }

    /**
     * @return O campo exibirModalExibicaoAtualizacoesBD.
     */
    public Boolean getExibirModalExibicaoAtualizacoesBD() {
        if (exibirModalExibicaoAtualizacoesBD == null) {
            exibirModalExibicaoAtualizacoesBD = Boolean.FALSE;
        }
        return exibirModalExibicaoAtualizacoesBD;
    }

    /**
     * @param exibirModalExibicaoAtualizacoesBD O novo valor de
     *                                          exibirModalExibicaoAtualizacoesBD.
     */
    public void setExibirModalExibicaoAtualizacoesBD(Boolean exibirModalExibicaoAtualizacoesBD) {
        this.exibirModalExibicaoAtualizacoesBD = exibirModalExibicaoAtualizacoesBD;
    }

    /**
     * @return O campo bancoDesatualizado.
     */
    public Boolean getBancoDesatualizado() {
        if (bancoDesatualizado == null) {
            bancoDesatualizado = Boolean.FALSE;
        }
        return bancoDesatualizado;
    }

    /**
     * @param bancoDesatualizado O novo valor de bancoDesatualizado.
     */
    public void setBancoDesatualizado(Boolean bancoDesatualizado) {
        this.bancoDesatualizado = bancoDesatualizado;
    }

    /**
     * @return O campo atualizacoesBD.
     */
    public List<AtualizacaoTO> getAtualizacoesBD() {
        return atualizacoesBD;
    }

    /**
     * @param atualizacoesBD O novo valor de atualizacoesBD.
     */
    public void setAtualizacoesBD(List<AtualizacaoTO> atualizacoesBD) {
        this.atualizacoesBD = atualizacoesBD;
    }

    /**
     * @return O campo detalheAtualizacao.
     */
    public String getDetalheAtualizacao() {
        return detalheAtualizacao;
    }

    /**
     * @param detalheAtualizacao O novo valor de detalheAtualizacao.
     */
    public void setDetalheAtualizacao(String detalheAtualizacao) {
        this.detalheAtualizacao = detalheAtualizacao;
    }

    public String atualizarBD() throws Exception {
        this.setExibirModalAtualizacaoBD(Boolean.FALSE);
        this.setExibirModalExibicaoAtualizacoesBD(Boolean.TRUE);
        List<String> listaAtualizacoes = new ArrayList();
        this.setAtualizacoesBD(null);

        try {
            listaAtualizacoes = getFacade().getAtualizadorBD().atualizar(this.getVersaoBD(), this.getUsuario().getCodigo());
            this.setMensagemAtualizacaoBD("O Banco de Dados foi atualizado com sucesso");
        } catch (SistemaException e) {
            this.setMensagemAtualizacaoBD(e.getMessage());
        }
        this.setAtualizacoesBD(new ArrayList<AtualizacaoTO>());

        for (String obj : listaAtualizacoes) {
            AtualizacaoTO atu = new AtualizacaoTO();
            atu.setScript(obj);
            List<AtualizacaoTO> lista = getFacade().getAtualizadorBD().consultar(atu);

            if (!lista.isEmpty()) {
                this.getAtualizacoesBD().add(lista.get(0));
            }
        }
        Collections.reverse(this.getAtualizacoesBD());
        return "erroLogin";
    }

    public void exibirDescricaoAtualizacao() {
        final AtualizacaoTO atualizacao = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
        this.setDetalheAtualizacao(atualizacao.getDescricao());
    }

    public void exibirScriptAtualizacao() {
        final AtualizacaoTO atualizacao = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
        this.setDetalheAtualizacao(atualizacao.getScript());
    }

    public void exibirMensagemAtualizacao() {
        final AtualizacaoTO atualizacao = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
        this.setDetalheAtualizacao(atualizacao.getMensagem());
    }

    public void exibirStackTraceAtualizacao() {
        final AtualizacaoTO atualizacao = (AtualizacaoTO) JSFUtilities.getRequestAttribute("atualizacao");
        this.setDetalheAtualizacao(atualizacao.getStackTrace());
    }

    /**
     * @return O campo abrirRichModalDiasParaBloqueio.
     */
    public Boolean getAbrirRichModalDiasParaBloqueio() {
        return this.abrirRichModalDiasParaBloqueio;
    }

    /**
     * @param abrirRichModalDiasParaBloqueio O novo valor de
     *                                       abrirRichModalDiasParaBloqueio.
     */
    public void setAbrirRichModalDiasParaBloqueio(Boolean abrirRichModalDiasParaBloqueio) {
        this.abrirRichModalDiasParaBloqueio = abrirRichModalDiasParaBloqueio;
        this.setAbrirRichModalSolicitarDesbloqueio(false);
    }

    /**
     * @return O campo diasParaBloqueio.
     */
    public int getDiasParaBloqueio() {
        return this.diasParaBloqueio;
    }

    /**
     * @param diasParaBloqueio O novo valor de diasParaBloqueio.
     */
    public void setDiasParaBloqueio(int diasParaBloqueio) {
        this.diasParaBloqueio = diasParaBloqueio;
    }

    public List<DiaRoboVO> getDiasSemProcessamento() {
        return diasSemProcessamento;
    }

    /*
     * ------------ CENTRAL DE EVENTOS ---------------------------
     */

    /**
     * Responsável por verificar se haverá a exibição do link de apresentar a
     * visualização do log do sistema
     *
     * @return
     * <AUTHOR> 31/03/2011
     */
    public Boolean getApresentarLinkLog() {
        try {
            // setar a funcionalidade como "visualizar log"
            super.aasFuncionalidade = Funcionalidade.VISUALIZAR_LOG.getCodigo();
            super.aasOperacao = "";
            //verificar permissão do user
            super.verificarAutorizacao();
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    public boolean isApresentarLinkCE() {
        try {
            if (getUsuarioLogado().getAdministrador()) {
                return true;
            }
            String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
            return modulos != null
                    && modulos.contains("CE")
                    && getPermissaoAcessoMenuVO() != null && getPermissaoAcessoMenuVO().getModuloCE();
        } catch (Exception ignored) {
            return false;
        }
    }

    public boolean isApresentarLinkZW() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("ZW");
    }

    public void montarModulosHabilitados() {
        try {
            montarModulosHabilitados(JSFUtilities.getFromSession("modulosHabilitados").toString());
        } catch (Exception ignored) {
        }
    }

    public void montarModulosHabilitados(String modulosHabilitados) {
        try {
            String[] modulos = modulosHabilitados.split(",");
            getModulosHabilitados().clear();
            getModulosHabilitados().add(ModuloAberto.CANAL_CLIENTE);
            if (isApresentarNotaFiscal()) {
                getModulosHabilitados().add(ModuloAberto.NOTAS);
            }
            for (String modulo : modulos) {
                if (validarModuloPorSigla(modulo.trim())) {
                    ModuloAberto obj = ModuloAberto.obterPorSigla(modulo.trim());
                    if (obj != null) {
                        getModulosHabilitados().add(obj);
                    }
                }
            }
            Ordenacao.ordenarLista(getModulosHabilitados(), "ordem");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean validarModuloPorSigla(String sigla) {
        if (sigla.equals("ZW")) {
            return true;
        } else if (sigla.equals("CRM")) {
            return isApresentarLinkCRM();
        } else if (sigla.equals("FIN")) {
            return isApresentarLinkFinanceiro();
        } else if (sigla.equals("CE")) {
            return isApresentarLinkCE() && getPermissaoAcessoMenuVO() != null && getPermissaoAcessoMenuVO().getModuloCE();
        } else if (sigla.equals("EST")) {
            return isApresentarLinkEstudio();
        } else if (sigla.equals("TR")) {
            return isApresentarModuloTreino();
        } else if (sigla.equals("NTR")) {
            return isApresentarModuloNovoTreino();
        } else if (sigla.equals("SLC")) {
            return isApresentarModuloAulaCheia();
        }
        return false;
    }

    public boolean isApresentarLinkCRM() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("CRM");
    }

    public boolean isApresentarLinkCentralEventos() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("CE");
    }

    public boolean isApresentarLinkTREINO() {
        return false;
    }

    public boolean isApresentarLinkGame() {
        //String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        //10/10/22 11:09 Gleidson disse ao Lucas Gontijo que o Game é liberado para todas empresas, sem exceção
        //10/04/23 O módulo estava aparecendo para usuários sem permissao 2.61, foi ajustado para exibir apenas para quem tem essa permissão.
        boolean exibirModuloGameOfResults = false;
        if (this.getPermissaoAcessoMenuVO().getGameOfResults()){
            exibirModuloGameOfResults = true;
        }
        return exibirModuloGameOfResults;
    }

    public boolean isApresentarVendas() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("NVO");
    }

    public boolean isApresentarOpenBank() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("OPB");
    }

    public boolean isApresentarLinkZWAuto() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("ZAA");
    }

    public boolean isApresentarModuloTreino() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return (modulos != null && modulos.contains("TR")) && getValidarTreinoLoginApp();
    }

    public boolean isApresentarModuloNovoTreino() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return (modulos != null && modulos.contains("NTR")) && getValidarTreinoLoginApp();
    }

    public boolean isApresentarMenuNovoBI() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return (modulos != null && modulos.contains("NZW"));
    }

    public boolean isApresentarModuloFacilitePay() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains(Modulo.FACILITE_PAY.getSiglaModulo());
    }

    public boolean isApresentarModuloAgenda() {
        return isApresentarModuloNovoTreino();
    }

    public boolean isApresentarModuloCross() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains(Modulo.CROSS.getSiglaModulo());
    }

    public boolean isApresentarModuloGraduacao() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains(Modulo.GRADUACAO.getSiglaModulo());
    }

    public boolean isApresentarModuloAvaliacaoFisica() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains(Modulo.AVALIACAO_FISICA.getSiglaModulo());
    }

    public boolean isApresentarModuloAulaCheia() {
        return false;
    }

    public boolean isApresentarModuloNovaPlataforma() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return (modulos != null &&
                (modulos.contains("NTR") || modulos.contains("NCR") || modulos.contains("NAV") || modulos.contains("NGR")))
                && getValidarTreinoLoginApp();
    }

    public boolean isApresentarLinkAulaCheia() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("SLC");
    }

    public boolean isApresentarLinkFinanceiro() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("FIN");
    }

    public boolean isApresentarLinkFinanceiroPropagandaFinanceiro() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return !isApresentarLinkFinanceiro();
    }

    public boolean isApresentarLinkSmartbox() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("SBX");
    }

    public boolean isApresentarLinkEstudio() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("EST");
    }

    public boolean isApresentarGestaoPersonal() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("GP");
    }

    public boolean isApresentarNotaFiscal() {
        try {
            if (getUsuarioLogado().getAdministrador()) {
                return true;
            }
            return getPermissaoAcessoMenuVO() != null && getPermissaoAcessoMenuVO().getModuloNotaFiscal();
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean isMostrarBotaoNotaFiscalPerfilCliente() throws Exception {
        if (mostrarBotaoNotaFiscalPerfilCliente == null) {
            if (getUsuarioLogado().getAdministrador()) {
                mostrarBotaoNotaFiscalPerfilCliente = true;
            }
            if (getPermissaoAcessoMenuVO().getAlunoAbaNotaFiscal()) {
                String chaveNFSe = getEmpresaLogado().getChaveNFSe();
                boolean isPossuiUsoAlgumNFe = getEmpresaLogado().getUsarNFSe() || getEmpresaLogado().isUsarNFCe();
                boolean isPossuiChaveNFse = chaveNFSe != null && (chaveNFSe.length() > 0);

                mostrarBotaoNotaFiscalPerfilCliente = isPossuiUsoAlgumNFe || isPossuiChaveNFse;
            } else {
                mostrarBotaoNotaFiscalPerfilCliente = false;
            }
        }
        return mostrarBotaoNotaFiscalPerfilCliente;
    }

    public boolean isApresentarMenuModulos() throws Exception {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && (modulos.split(",").length > 1);
    }

    public String getInstanceName() {
        return System.getProperty("com.sun.aas.instanceName", "");
    }

    /**
     * @return the mostrarModalExpiracaoSenha
     */
    public boolean isMostrarModalExpiracaoSenha() {
        return mostrarModalExpiracaoSenha;
    }

    /**
     * @param mostrarModalExpiracaoSenha the mostrarModalExpiracaoSenha to set
     */
    public void setMostrarModalExpiracaoSenha(boolean mostrarModalExpiracaoSenha) {
        this.mostrarModalExpiracaoSenha = mostrarModalExpiracaoSenha;
    }

    /**
     * @return the qtdDiasFaltaExpirar
     */
    public long getQtdDiasFaltaExpirar() {
        return qtdDiasFaltaExpirar;
    }

    /**
     * @param qtdDiasFaltaExpirar the qtdDiasFaltaExpirar to set
     */
    public void setQtdDiasFaltaExpirar(long qtdDiasFaltaExpirar) {
        this.qtdDiasFaltaExpirar = qtdDiasFaltaExpirar;
    }

    /**
     * @return the diasFaltaExpirarFormatado
     */
    public String getDiasFaltaExpirarFormatado() {
        return diasFaltaExpirarFormatado;
    }

    /**
     * @param diasFaltaExpirarFormatado the diasFaltaExpirarFormatado to set
     */
    public void setDiasFaltaExpirarFormatado(String diasFaltaExpirarFormatado) {
        this.diasFaltaExpirarFormatado = diasFaltaExpirarFormatado;
    }

    /**
     * @return the javaScriptFecharPopUpAlteracaoSenha
     */
    public String getJavaScriptFecharPopUpAlteracaoSenha() {
        return javaScriptFecharPopUpAlteracaoSenha;
    }

    /**
     * @param javaScriptFecharPopUpAlteracaoSenha the
     *                                            javaScriptFecharPopUpAlteracaoSenha to set
     */
    public void setJavaScriptFecharPopUpAlteracaoSenha(String javaScriptFecharPopUpAlteracaoSenha) {
        this.javaScriptFecharPopUpAlteracaoSenha = javaScriptFecharPopUpAlteracaoSenha;
    }

    public String getMensagemBloqueio() {
        return mensagemBloqueio;
    }

    public void setMensagemBloqueio(String mensagemBloqueio) {
        this.mensagemBloqueio = mensagemBloqueio;
    }

    public String getInfoMensagemBloqueio() {
        return infoMensagemBloqueio;
    }

    public void setInfoMensagemBloqueio(String infoMensagemBloqueio) {
        this.infoMensagemBloqueio = infoMensagemBloqueio;
    }

    public boolean isVerificarExecucaoRoboBackground() {
        return verificarExecucaoRoboBackground;
    }

    public void setVerificarExecucaoRoboBackground(boolean verificarExecucaoRoboBackground) {
        this.verificarExecucaoRoboBackground = verificarExecucaoRoboBackground;
    }

    public boolean isApresentarIconeExecucaoRoboBackground() {
        return apresentarIconeExecucaoRoboBackground;
    }

    public void setApresentarIconeExecucaoRoboBackground(boolean apresentarIconeExecucaoRoboBackground) {
        this.apresentarIconeExecucaoRoboBackground = apresentarIconeExecucaoRoboBackground;
    }

    /**
     * @return the moduloAberto
     */
    public ModuloAberto getModuloAberto() {
        return moduloAberto;
    }

    public Modulo getModulo(){
        return Modulo.fromSigla(moduloAberto.getSigla());
    }

    /**
     * @param moduloAberto the moduloAberto to set
     */
    public void setModuloAberto(ModuloAberto moduloAberto) {
        this.moduloAberto = moduloAberto;
        setSiglaModuloAtual(moduloAberto);
        if(!ignorarHistoricoNavegacaoModulo){
            addHistoricoNavegacaoModulos(moduloAberto);
        }
    }

    public void setModuloAberto(Modulo modulo) {
        this.moduloAberto = ModuloAberto.obterPorSigla(modulo.getSiglaModulo());
        addHistoricoNavegacaoModulos(this.moduloAberto);
    }

    public PerfilUsuarioNFeVO getPerfilNFe() {
        return perfilNFe;
    }

    public void setPerfilNFe(PerfilUsuarioNFeVO perfilNFe) {
        this.perfilNFe = perfilNFe;
    }

    public int getNumDeTentativas() {
        return numDeTentativas;
    }

    public void setNumDeTentativas(int numDeTentativas) {
        this.numDeTentativas = numDeTentativas;
    }

    public String getJspMostrarCRM() {
        return jspMostrarCRM;
    }

    public void setJspMostrarCRM(String jspMostrar) {
        this.jspMostrarCRM = jspMostrar;
    }

    public void recarregarPagina(ActionEvent event) {
        String includePrincipal = (String) event.getComponent().getAttributes().get("include");
        setJspMostrarCRM(includePrincipal);
    }

    public String inicializarBoletos() {
        setAbrirRichModalBoletosInicio(true);
        setAbrirRichModalDiasParaBloqueio(false);
        this.setAbrirRichModalSolicitarDesbloqueio(false);
        return "erroLogin";
    }

    public boolean isApresentarLinkBoletos() {
        return apresentarLinkBoletos;
    }

    public void setApresentarLinkBoletos(boolean apresentarLinkBoletos) {
        this.apresentarLinkBoletos = apresentarLinkBoletos;
    }

    public String getTituloMensagemBloqueio() {
        return tituloMensagemBloqueio;
    }

    public void setTituloMensagemBloqueio(String tituloMensagemBloqueio) {
        this.tituloMensagemBloqueio = tituloMensagemBloqueio;
    }

    public Boolean getValidarTreinoLoginApp() {
        return validarTreinoLoginApp;
    }

    public void setValidarTreinoLoginApp(Boolean validarTreinoLoginApp) {
        this.validarTreinoLoginApp = validarTreinoLoginApp;
    }

    public void setExistemNovosBoletos(boolean existemNovosBoletos) {
        this.existemNovosBoletos = existemNovosBoletos;
    }

    public boolean isExistemNovosBoletos() {
        return existemNovosBoletos;
    }

    public String getUserOamd() {
        return userOamd;
    }

    public void setUserOamd(String userOamd) {
        this.userOamd = userOamd;
    }

    public List<ModuloAberto> getModulosHabilitados() {
        if (modulosHabilitados == null) {
            modulosHabilitados = new ArrayList<ModuloAberto>();
        }
        return modulosHabilitados;
    }

    public boolean getModuloIndependente() {
        return getModulosHabilitados().size() == 1;
    }

    public String getFavIconModule() {
        ModuloAberto modulo = getModuloAberto();
        if (modulo != null) {
            switch (modulo) {
                case ZILLYONWEB:
                    return "/imagens_flat/pct-icone-fundo-administrativo.svg";
                case FINAN:
                    return "/imagens_flat/pct-icone-fundo-financeiro.svg";
                case CRMWEB:
                    return "/imagens_flat/pct-icone-fundo-crm.svg";
                case NOTAS:
                    return "/imagens_flat/pct-icone-fundo-nota-fiscal.svg";
                case CANAL_CLIENTE:
                    return "/imagens_flat/pct-icone-fundo-canal-do-cliente.svg";
                case CE:
                    return "imagens_flat/genteCE.svg";
                default:
                    return "/imagens/iconZW/favicon-32x32.png";
            }
        }
        return "/imagens/iconZW/favicon-32x32.png";
    }

    public String getAbrirModulo() {
        try {
            ModuloAberto modulo = (ModuloAberto) request().getAttribute("modulo");
            return abrirModuloGenerico(modulo, false);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public String abrirModuloAtual() {
        try {
            setOnCompleteModuloAtual("");
            String url = abrirModuloGenerico(getModuloAberto(), true);
            if (url != null && url.contains("http")) {
                setOnCompleteModuloAtual("window.open('"+url+"','_self')");
                return "";
            }
            return url;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }

    private String abrirModuloGenerico(ModuloAberto modulo, boolean voltarModuloAtual) {
        try {
            if (modulo == ModuloAberto.ZILLYONWEB) {
                if (voltarModuloAtual) {
                    return getAbrirNZW();
                } else {
                    return abrirZillyonWeb();
                }
            } else if (modulo == ModuloAberto.CRMWEB) {
                return abrirModuloCRM();
            } else if (modulo == ModuloAberto.CANAL_CLIENTE) {
                return abrirModuloCanalCliente();
            } else if (modulo == ModuloAberto.FINAN) {
                return abrirModuloFinanceiro();
            } else if (modulo == ModuloAberto.CE) {
                return abrirCE();
            } else if (modulo == ModuloAberto.TREINO_WEB) {
                return abrirModuloTreino("treino");
            } else if (modulo == ModuloAberto.AULA_CHEIA) {
                return abrirModuloTreino("aulacheia");
            } else if (modulo == ModuloAberto.UCP) {
                return getLinkModuloUCP();
            } else if (modulo == ModuloAberto.NOTAS) {
                return abrirModuloNotaFiscal();
            }
        } catch (Exception ignored) {
        }
        return "";
    }

    public String abrirModulo() {
        try {
            if (this.moduloAberto == ModuloAberto.ZILLYONWEB) {
                return abrirZillyonWeb();
            } else if (this.moduloAberto == ModuloAberto.CANAL_CLIENTE) {
                return abrirModuloCanalCliente();
            } else if (this.moduloAberto == ModuloAberto.CRMWEB) {
                return abrirModuloCRM();
            } else if (this.moduloAberto == ModuloAberto.FINAN) {
                return abrirModuloFinanceiro();
            } else if (this.moduloAberto == ModuloAberto.CE) {
                return abrirCE();
            } else if (this.moduloAberto == ModuloAberto.TREINO_WEB) {
                return abrirModuloTreino("treino");
            } else if (this.moduloAberto == ModuloAberto.AULA_CHEIA) {
                return abrirModuloTreino("aulacheia");
            } else if (this.moduloAberto == ModuloAberto.UCP) {
                return getLinkModuloUCP();
            }
        } catch (Exception ignored) {
        }
        return "";
    }


    public Boolean getModuloAtualCrm() {
        return getModuloAberto() == ModuloAberto.CRMWEB;
    }

    public Boolean getModuloAtualZw() {
        return getModuloAberto() == ModuloAberto.ZILLYONWEB;
    }

    public Boolean getModuloAtualCe() {
        return getModuloAberto() == ModuloAberto.CE;
    }

    public Boolean getModuloAtualFin() {
        return getModuloAberto() == ModuloAberto.FINAN;
    }

    public Boolean getModuloAtualEst() {
        return getModuloAberto() == ModuloAberto.GESTAOSTUDIO;
    }

    public String getModulosAbertoStyle() {
        return "moduloAberto " + getModuloAberto().getSigla();
    }

    public String getTamanhoModulosAbertos() {
        return (100.0 / (getModulosHabilitados().size() - 1.0)) + "";
    }

    public void setModulosHabilitados(List<ModuloAberto> modulosHabilitados) {
        this.modulosHabilitados = modulosHabilitados;
    }


    public void abrirModalDadosAppAssinatura() {
        setMsgAlert("showModalQRCode();");
    }

    public void preparaDadosAppAssinatura() {
        try {
            JSONObject paramsJson = new JSONObject();
            paramsJson.put("chave", getKey());
            paramsJson.put("usuario", getUsuarioLogado().getCodigo());
            paramsJson.put("empresaLogada", getEmpresaLogado().getCodigo());
            String params = Uteis.encriptar(paramsJson.toString(), "pactotknssntrdgtl");
            urlAssinaturaDigitalDireta = "/assinatura/contratos.html?token=" + params + "&tela=inicio";
            Map<ConfiguracaoBIEnum, Boolean> configs = getFacade().getConfiguracaoBI().mapaConfigs(getEmpresaLogado().getCodigo(), ConfiguracaoBIEnum.ASSINATURA_APTIDAO_FISICA,
                    ConfiguracaoBIEnum.ASSINATURA_FOTO_ALUNO,
                    ConfiguracaoBIEnum.ASSINATURA_DOCUMENTOS,
                    ConfiguracaoBIEnum.ASSINATURA_COMPROVANTE_ENDERECO);
            mapaConfigs = new HashMap<String, Boolean>();
            for (ConfiguracaoBIEnum cfg : configs.keySet()) {
                mapaConfigs.put(cfg.name(), configs.get(cfg));
            }
            tipoQR = TipoQRCodeEnum.ASSINATURA_DIGITAL;
            qrCodeAutorizado = true;
            setSenhaQRCode("");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void abrirModalDadosAppGestor() {
        preparaDadosAppGestor();
        setMsgAlert("showModalQRCode();");
    }
    public void preparaDadosAppGestor () {
        try {
            tipoQR = TipoQRCodeEnum.APPGESTOR;
            novoEmailQR = false;
            qrCodeAutorizado = true;
            emailQRCode = "";
            usuarioEmailVO = getFacade().getUsuarioEmail().consultarPorUsuario(getUsuarioLogado().getCodigo());
            if (usuarioEmailVO == null || UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
                List<EmailVO> emails = getFacade().getEmail().consultarEmails(
                        getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo(),
                        false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (UteisValidacao.emptyList(emails)) {
                    novoEmailQR = true;
                } else {
                    emailQRCode = emails.get(0).getEmail();
                }
                usuarioEmailVO = new UsuarioEmailVO();
                usuarioEmailVO.setUsuario(getUsuarioLogado().getCodigo());
                usuarioEmailVO.setEmail(emailQRCode);

            } else {
                if (UteisValidacao.emptyString(usuarioEmailVO.getEmail())) {
                    if (!getUsuarioLogado().getColaboradorVO().getCodigo().equals(0)) {
                        boolean adicionou = false;
                        for (EmailVO emailVO : getUsuarioLogado().getColaboradorVO().getPessoa().getEmailVOs()) {
                            if (UteisValidacao.emptyNumber(getFacade().getUsuarioEmail().consultarPorEmail(emailVO.getEmail()).getCodigo())) {
                                usuarioEmailVO.setEmail(emailVO.getEmail());
                                adicionou = true;
                                break;
                            }
                        }
                        if (!adicionou) {
                            usuarioEmailVO = new UsuarioEmailVO();
                            usuarioEmailVO.setUsuario(getUsuarioLogado().getCodigo());
                        }
                    }
                    getFacade().getUsuarioEmail().alterar(usuarioEmailVO);
                }
                emailQRCode = usuarioEmailVO.getEmail();
            }
            setMsgAlert("");
            setSenhaQRCode("");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void abrirModalDadosAppCartaoVacinacao() {
        setMsgAlert("showModalQRCode();");
    }

    public void preparaDadosAppCartaoVacinacao() {
        try {
            JSONObject paramsJson = new JSONObject();
            paramsJson.put("chave", getKey());
            paramsJson.put("usuario", getUsuarioLogado().getCodigo());
            paramsJson.put("empresaLogada", getEmpresaLogado().getCodigo());
            String params = Uteis.encriptar(paramsJson.toString(), "pactotknssntrdgtl");
            urlAssinaturaDigitalDireta = "/assinatura/contratos.html?token=" + params + "&tela=vacina";
            Map<ConfiguracaoBIEnum, Boolean> configs = getFacade().getConfiguracaoBI().mapaConfigs(getEmpresaLogado().getCodigo(), ConfiguracaoBIEnum.ASSINATURA_APTIDAO_FISICA,
                    ConfiguracaoBIEnum.ASSINATURA_FOTO_ALUNO,
                    ConfiguracaoBIEnum.ASSINATURA_DOCUMENTOS,
                    ConfiguracaoBIEnum.ASSINATURA_COMPROVANTE_ENDERECO);
            mapaConfigs = new HashMap<>();
            for (ConfiguracaoBIEnum cfg : configs.keySet()) {
                mapaConfigs.put(cfg.name(), configs.get(cfg));
            }
            tipoQR = TipoQRCodeEnum.CARTAO_VACINA;
            qrCodeAutorizado = true;
            setSenhaQRCode("");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void preparaDadosFormularioParQ() {
        try {
            JSONObject paramsJson = new JSONObject();
            paramsJson.put("chave", getKey());
            paramsJson.put("usuario", getUsuarioLogado().getCodigo());
            paramsJson.put("empresaLogada", getEmpresaLogado().getCodigo());
            String params = Uteis.encriptar(paramsJson.toString(), "pactotknssntrdgtl");
            urlAssinaturaDigitalDireta = "/assinatura/contratos.html?token=" + params + "&tela=par-q";
            Map<ConfiguracaoBIEnum, Boolean> configs = getFacade().getConfiguracaoBI().mapaConfigs(getEmpresaLogado().getCodigo(), ConfiguracaoBIEnum.ASSINATURA_APTIDAO_FISICA,
                    ConfiguracaoBIEnum.ASSINATURA_FOTO_ALUNO,
                    ConfiguracaoBIEnum.ASSINATURA_DOCUMENTOS,
                    ConfiguracaoBIEnum.ASSINATURA_COMPROVANTE_ENDERECO);
            mapaConfigs = new HashMap<String, Boolean>();
            for (ConfiguracaoBIEnum cfg : configs.keySet()) {
                mapaConfigs.put(cfg.name(), configs.get(cfg));
            }
            tipoQR = TipoQRCodeEnum.FORMULARIO_PAR_Q;
            qrCodeAutorizado = true;
            setSenhaQRCode("");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void validarSenhaQRCodeZWUI() {
        try {
            if (novoEmailQR) {
                if (!UteisEmail.getValidEmail(emailQRCode)) {
                    throw new Exception("E-mail inválido!");
                }
                EmailVO email = new EmailVO();
                email.setPessoa(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                email.setEmail(emailQRCode);
                getFacade().getEmail().incluir(email);
                usuarioEmailVO.setEmail(emailQRCode);
            }

            if (UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
                getFacade().getUsuarioEmail().incluir(usuarioEmailVO);
            } else {
                if (!UteisEmail.getValidEmail(emailQRCode)) {
                    throw new Exception("E-mail inválido!");
                }
                getFacade().getUsuarioEmail().alterar(usuarioEmailVO);
            }
            setMsgAlert("");
            UsuarioVO usuarioVO = getFacade().getControleAcesso().verificarLoginUsuario(getUsuarioLogado().getUsername(), senhaQRCode.toUpperCase());
            setMsgAlert("startQRCodeZWUI(); showQRCodeZWUI();");
            qrCodeAutorizado = true;
            adicionarUsuarioServicoDescobrir(getKey(), emailQRCode, usuarioVO);
            novoEmailQR = false;
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void validarSenhaQRCode() {
        try {
            if (novoEmailQR) {
                if (!UteisEmail.getValidEmail(emailQRCode)) {
                    throw new Exception("E-mail inválido!");
                }
                EmailVO email = new EmailVO();
                email.setPessoa(getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
                email.setEmail(emailQRCode);
                getFacade().getEmail().incluir(email);
                usuarioEmailVO.setEmail(emailQRCode);
            }

            if (UteisValidacao.emptyNumber(usuarioEmailVO.getCodigo())) {
                getFacade().getUsuarioEmail().incluir(usuarioEmailVO);
            } else {
                if (!UteisEmail.getValidEmail(emailQRCode)) {
                    throw new Exception("E-mail inválido!");
                }
                getFacade().getUsuarioEmail().alterar(usuarioEmailVO);
            }
            setMsgAlert("");
            UsuarioVO usuarioVO = getFacade().getControleAcesso().verificarLoginUsuario(getUsuarioLogado().getUsername(), senhaQRCode.toUpperCase());
            setMsgAlert("startQRCode(); showQRCode();");
            qrCodeAutorizado = true;
            adicionarUsuarioServicoDescobrir(getKey(), emailQRCode, usuarioVO);
            novoEmailQR = false;
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void enviarEmailUsuarioApp() {
        try {
            getFacade().getUsuarioEmail().enviarEmailUsuarioApp(getKey(), getUsuarioLogado().getNome(), emailQRCode, senhaQRCode.toUpperCase());
        } catch (Exception e) {
            //ignorar erro na tentativa de enviar email do app
        }
    }

    public void gravarConfigAssinatura() {
        try {
            for (String k : mapaConfigs.keySet()) {
                ConfiguracaoBIEnum cfg = ConfiguracaoBIEnum.valueOf(k);
                getFacade().getConfiguracaoBI().incluirPorConfig(getEmpresaLogado().getCodigo(), cfg, mapaConfigs.get(k).toString());
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }

    }

    public String getUrlAssinaturaDigitalDireta() throws Exception {
        if (!UteisValidacao.emptyString(urlAssinaturaDigitalDireta)) {
            final String protocol = (String) JSFUtilities.getFromSession(JSFUtilities.PROTOCOL_LOGGED);
            StringBuffer url = request().getRequestURL();
            String path = Uteis.getPathPortApp(protocol, url.toString(), request().getContextPath());
            return path + urlAssinaturaDigitalDireta;
        }
        return "";
    }

    public String getUrlAssinaturaDigital() throws Exception {
        return "/QRCode?w=325&h=325&encr=n&qrtext=" + getUrlAssinaturaDigitalDireta().replace("&", "%26");
    }

    public void verificarExibirQrCodeUsuarioChave() throws Exception{
        if (UteisValidacao.emptyString(emailQRCode) ||
                UteisValidacao.emptyString(senhaQRCode) ||
                getEmpresaLogado() == null ||
                UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
            setExibirQrCodeUsuarioChave(false);
        } else {
            setExibirQrCodeUsuarioChave(true);
        }
    }

    public String getQrCodeUsuarioChave() {
        try {
            JSONObject json = new JSONObject();
            json.put("email", emailQRCode);
            json.put("senha", senhaQRCode.toUpperCase());
            json.put("empresa", getEmpresaLogado().getCodigo());
            return "/QRCode?w=325&h=325&encr=s&qrtext=" + Uteis.encriptar(json.toString(), "TxTQrcOdE");
        } catch (Exception ex) {
            montarErro(ex);
            return "Erro ao montar QR Code";
        }
    }

    public void adicionarUsuarioServicoDescobrir(String ctx, String email, UsuarioVO usuarioVO) throws Exception {
        if (email.contains("@")) {
            String url = String.format("%s/prest/usuarioapp/%s/v3/gerarUsuarioRedeEmpresa",
                    PropsService.getPropertyValue(PropsService.urlOamd),
                    ctx);
            Map<String, String> params = new HashMap<String, String>();
            params.put("email", email);
            params.put("cpf", usuarioVO.getColaboradorVO().getPessoa().getCfp());
            params.put("telefone", usuarioVO.getColaboradorVO().getPessoa().getTelefones());
            params.put("dataNascimento", Uteis.getDataAplicandoFormatacao(usuarioVO.getColaboradorVO().getPessoa().getDataNasc(), "dd/MM/yyyy"));
            params.put("senha", usuarioVO.getSenha());
            params.put("empresaFinanceiro", UteisValidacao.notEmptyNumber(getEmpresaLogado().getCodEmpresaFinanceiro()) ? getEmpresaLogado().getCodEmpresaFinanceiro().toString() : "0");
            String result = ExecuteRequestHttpService.executeRequest(url, params, false, "utf-8");
            if (result.contains("erro")) {
                throw new Exception(new JSONObject(result).optString("erro"));
            }
        }
    }

    public List<ItemCampanhaJSON> getItemsCampanha() {
        return itemsCampanha;
    }

    public void setItemsCampanha(List<ItemCampanhaJSON> itemsCampanha) {
        this.itemsCampanha = itemsCampanha;
    }

    public boolean isExibirNotificacaoExpiracaoSistema() {
        return exibirNotificacaoExpiracaoSistema;
    }

    public void setExibirNotificacaoExpiracaoSistema(boolean exibirNotificacaoExpiracaoSistema) {
        this.exibirNotificacaoExpiracaoSistema = exibirNotificacaoExpiracaoSistema;
    }

    public EmpresaNFeVO getEmpresaNFe() {
        if (getEmpresa() instanceof EmpresaNFeVO) {
            return (EmpresaNFeVO) getEmpresa();
        }
        return new EmpresaNFeVO();
    }

    public String getSenhaQRCode() {
        return senhaQRCode;
    }

    public void setSenhaQRCode(String senhaQRCode) {
        this.senhaQRCode = senhaQRCode;
    }

    public Boolean getQrCodeAutorizado() {
        return qrCodeAutorizado;
    }

    public void setQrCodeAutorizado(Boolean qrCodeAutorizado) {
        this.qrCodeAutorizado = qrCodeAutorizado;
    }

    public String getStrQRCode() {
        return strQRCode;
    }

    public void setStrQRCode(String strQRCode) {
        this.strQRCode = strQRCode;
    }

    public String getEmailQRCode() {
        return emailQRCode;
    }

    public void setEmailQRCode(String emailQRCode) {
        this.emailQRCode = emailQRCode;
    }

    public List<SelectItem> getEmailsQRCode() {
        return emailsQRCode;
    }

    public void setEmailsQRCode(List<SelectItem> emailsQRCode) {
        this.emailsQRCode = emailsQRCode;
    }

    public Boolean getNovoEmailQR() {
        return novoEmailQR;
    }

    public void setNovoEmailQR(Boolean novoEmailQR) {
        this.novoEmailQR = novoEmailQR;
    }

    public String getPermissoesOAMD() {
        return permissoesOAMD;
    }

    public void setPermissoesOAMD(String permissoesOAMD) {
        this.permissoesOAMD = permissoesOAMD;
    }

    public TipoQRCodeEnum getTipoQR() {
        return tipoQR;
    }

    public void setTipoQR(TipoQRCodeEnum tipoQR) {
        this.tipoQR = tipoQR;
    }

    public Map<String, Boolean> getMapaConfigs() {
        return mapaConfigs;
    }

    public void setMapaConfigs(Map<String, Boolean> mapaConfigs) {
        this.mapaConfigs = mapaConfigs;
    }

    private void verificarValorNotasEmidas() {
        try {
            setMsgModalValorNFSeEmitido("");

            Integer diaAtual = Calendario.hoje().getDate();

            if (getEmpresa() != null &&
                    getEmpresa().getUsarNFSe() &&
                    !UteisValidacao.emptyNumber(getEmpresa().getValorMensalEmitirNFSe()) && (diaAtual > 20)) {

                //NOTAS ENOTAS
                if (getEmpresa().isUsaEnotas()) {
                    Date dataInicial = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
                    Date dataFinal = Uteis.obterUltimoDiaMes(Calendario.hoje());

                    Double valorEmitidoAtual = getFacade().getNotaFiscal().consultarValorTotal(getEmpresa().getCodigo(), 0,
                            StatusEnotasEnum.AUTORIZADA, dataInicial, dataFinal);

                    if (valorEmitidoAtual < getEmpresa().getValorMensalEmitirNFSe()) {
                        String valorAtual = Formatador.formatarValorMonetario(valorEmitidoAtual);
                        String valorDesejado = Formatador.formatarValorMonetario(getEmpresa().getValorMensalEmitirNFSe());
                        setMsgModalValorNFSeEmitido("Não foram emitidas notas fiscais suficientes para o valor total do mês.<br/><br/>Valor desejado " + valorDesejado + "<br/><br/>Valor emitido: " + valorAtual);
                    }
                } else if (getEmpresa().isUsaNotasDelphi() && !UteisValidacao.emptyString(getEmpresa().getChaveNFSe())) {
                    //NOTAS DELPHI

                    String dataReferencia = Uteis.getDataAplicandoFormatacao(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), "dd-MM-yyyy");
                    String chaveNFSe = getEmpresa().getChaveNFSe();

                    Map<String, String> headers = new HashMap<String, String>();
                    headers.put("Content-Type", "application/x-www-form-urlencoded");

                    Map<String, String> corpo = new HashMap<String, String>();
                    corpo.put("gestaoNotas", chaveNFSe);
                    corpo.put("mes", dataReferencia);

                    String urlConsultar = PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nota";
                    String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");

                    JSONObject jsonObject = new JSONObject(executeRequest);
                    JSONArray lista = new JSONArray(jsonObject.get("return").toString());

                    Double valorEmitidoAtual = 0.0;
                    for (int e = 0; e < lista.length(); e++) {
                        JSONObject obj = lista.getJSONObject(e);
                        String status = obj.getString("status");
                        if (status.equals(StatusNotaEnum.AUTORIZADO.getDescricao())) {
                            valorEmitidoAtual = obj.getDouble("valor");
                            break;
                        }
                    }

                    if (valorEmitidoAtual < getEmpresa().getValorMensalEmitirNFSe()) {
                        String valorAtual = Formatador.formatarValorMonetario(valorEmitidoAtual);
                        String valorDesejado = Formatador.formatarValorMonetario(getEmpresa().getValorMensalEmitirNFSe());
                        setMsgModalValorNFSeEmitido("Não foram emitidas notas fiscais suficientes para o valor total do mês.<br/><br/>Valor desejado " + valorDesejado + "<br/><br/>Valor emitido: " + valorAtual);
                    }
                }
            }
        } catch (Exception e) {
            setMsgModalValorNFSeEmitido("");
        }
    }

    private void verificarNotificaGestaoRemessas() {
        try {
            setMsgModalNotificaRemessas("");
            NotificacaoUsuarioVO notificacaoUsuarioVO = getFacade().getNotificacaoUsuario().consultarNotificacao(getUsuarioLogado(), TipoNotificacaoUsuarioEnum.REMESSA_SEM_RETORNO, true, Calendario.hoje(), getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (notificacaoUsuarioVO != null && !UteisValidacao.emptyNumber(notificacaoUsuarioVO.getCodigo())) {
                setMsgModalNotificaRemessas(notificacaoUsuarioVO.getMensagem());
                notificacaoUsuarioVO.setApresentarHoje(false);
                getFacade().getNotificacaoUsuario().alterar(notificacaoUsuarioVO);
            }

        } catch (Exception e) {
            setMsgModalNotificaRemessas("");
        }
    }

    private void verificarNotificaCertificado() {
        try {
            setMsgModalNotificaCertificado("");
            NotificacaoUsuarioVO notificacaoUsuarioVO = getFacade().getNotificacaoUsuario().consultarNotificacao(getUsuarioLogado(), TipoNotificacaoUsuarioEnum.VENCIMENTO_CERTIFICADO, true, Calendario.hoje(), getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (notificacaoUsuarioVO != null && !UteisValidacao.emptyNumber(notificacaoUsuarioVO.getCodigo())) {
                setMsgModalNotificaCertificado(notificacaoUsuarioVO.getMensagem());
                notificacaoUsuarioVO.setApresentarHoje(false);
                getFacade().getNotificacaoUsuario().alterar(notificacaoUsuarioVO);
            }

        } catch (Exception e) {
            setMsgModalNotificaCertificado("");
        }
    }

    public String getMsgModalValorNFSeEmitido() {
        if (msgModalValorNFSeEmitido == null) {
            msgModalValorNFSeEmitido = "";
        }
        return msgModalValorNFSeEmitido;
    }

    public void setMsgModalValorNFSeEmitido(String msgModalValorNFSeEmitido) {
        this.msgModalValorNFSeEmitido = msgModalValorNFSeEmitido;
    }

    public String getMsgModalNotificaRemessas() {
        if (msgModalNotificaRemessas == null) {
            msgModalNotificaRemessas = "";
        }
        return msgModalNotificaRemessas;
    }

    public void setMsgModalNotificaRemessas(String msgModalNotificaRemessas) {
        this.msgModalNotificaRemessas = msgModalNotificaRemessas;
    }

    public boolean isApresentarModalNotificaRemessas() {
        return !UteisValidacao.emptyString(getMsgModalNotificaRemessas());
    }

    public boolean isApresentarModalNotificaCertificado() {
        return !UteisValidacao.emptyString(getMsgModalNotificaCertificado());
    }

    public String getMsgModalNotificaCertificado() {
        return msgModalNotificaCertificado;
    }

    public void setMsgModalNotificaCertificado(String msgModalNotificaCertificado) {
        this.msgModalNotificaCertificado = msgModalNotificaCertificado;
    }

    public boolean isApresentarModalValorNFSeEmitido() {
        return !UteisValidacao.emptyString(getMsgModalValorNFSeEmitido());
    }

    public void naoApresentarModalNotificaRemessas() {
        try {
            setMsgModalNotificaRemessas("");
        } catch (Exception ignored) {
        }
    }

    public void naoApresentarModalNotificaCertificado() {
        setMsgModalNotificaCertificado("");
    }

    public void naoApresentarModalValorNFSeEmitido() {
        try {
            setMsgModalValorNFSeEmitido("");
        } catch (Exception ignored) {
        }
    }

    public Boolean getPriorizarVendaRapida() {
        return priorizarVendaRapida;
    }

    public void setPriorizarVendaRapida(Boolean priorizarVendaRapida) {
        this.priorizarVendaRapida = priorizarVendaRapida;
    }

    public Boolean getAbrirRichModalBoletosInicio() {
        return abrirRichModalBoletosInicio;
    }

    public void setAbrirRichModalBoletosInicio(Boolean abrirRichModalBoletosInicio) {
        this.abrirRichModalBoletosInicio = abrirRichModalBoletosInicio;
    }

    public void consultarLogEntidadesSelecionado(List<String> nomesEntidade, Integer codigoEntidade, Integer codigoPessoa) {
        setListaConsultaLog(new ArrayList());
        try {
            context().getExternalContext().getSessionMap().put("titulo", getNomeClasse());
            List lista = getFacade().getLog().consultarPorNomesEntidadeAgrupado(nomesEntidade, codigoEntidade, null, null, codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
            setListaConsultaLog(lista);
        } catch (Exception e) {
            setListaConsultaLog(new ArrayList());
            getListaConsultaLog().clear();
        }
    }

    public String getGrupoMoviDesk() {
        final Object grupoMoviDesk = JSFUtilities.getFromSession(JSFUtilities.GRUPO_CHAT_MOVIDESK);
        if (grupoMoviDesk == null || StringUtils.isBlank((String) grupoMoviDesk)
                || StringUtils.equalsIgnoreCase("null", (String) grupoMoviDesk)) {
            return "52429C99928D447E8E6D43F0B1F08F02";
        }
        return (String) grupoMoviDesk;
    }

    public boolean validaFormDesbloquerio() {
        try {
            if (null == mensagemCliente || mensagemCliente.trim().length() < 4) {
                throw new Exception("Informe uma sugestão mais detalhada!");
            }
            if (null == sabeDoCanalDoCliente || sabeDoCanalDoCliente.trim().isEmpty()) {
                throw new Exception("Informe se você sabe sobre o Canal do Cliente!");
            }
            if (null == telefoneCliente || telefoneCliente.trim().length() < 8) {
                throw new Exception("Informe um telefone válido!");
            }
            return true;
        } catch (Exception e) {
            montarErro(e);
            return false;
        }
    }

    public String desbloquearSistema() throws Exception {
        if (validaFormDesbloquerio()) {
            boolean diaExtraRegistrado = false;
            try {
                getEmpresaLogado().setDataConcessaoDiaExtra(
                        getFacade().getEmpresa().concederDiasExtras(getEmpresaLogado().getCodigo(), getDiasExtras()));
                diaExtraRegistrado = true;
            } catch (Exception e) {
                Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE,
                        "Não foi possível permitir uso do sistema na data atual.", e);
            }

            if (diaExtraRegistrado) {
                final DiaExtraDTO diaExtraDTO = new DiaExtraDTO();
                diaExtraDTO.setChave((String) JSFUtilities.getFromSession(JSFUtilities.KEY));
                diaExtraDTO.setCodigoEmpresa(getEmpresaLogado().getCodigo());
                diaExtraDTO.setNomeEmpresa(getEmpresaLogado().getNome());
                diaExtraDTO.setCodigoUsuario(getUsuarioLogado().getCodigo());
                diaExtraDTO.setNomeUsuario(getUsuarioLogado().getUsername());
                diaExtraDTO.setDataDesbloqueio(new Date().getTime());
                diaExtraDTO.setDiasExtras(getDiasExtras());
                diaExtraDTO.setMensagem(getMensagemCliente());
                diaExtraDTO.setTelefone(getTelefoneCliente());
                diaExtraDTO.setUsaAppTreino(getUsaAppTreino());
                diaExtraDTO.setSabeDoCanalDoCliente(getSabeDoCanalDoCliente());

                JSFUtilities.storeOnSession(JSFUtilities.DIAS_PARA_EXPIRAR, getDiasExtras());
                try {
                    DiaExtraService.getInstance().registrarDiaExtra(diaExtraDTO);
                } catch (Exception e) {
                }
            }
            return verificarDiasParaBloqueio();
        }
        return "";
    }

    public String permitirUsarHoje() throws Exception {
        boolean diaExtraRegistrado = false;
        try {
            getEmpresaLogado().setDataConcessaoDiaExtra(
                    getFacade().getEmpresa().concederDiasExtras(getEmpresaLogado().getCodigo(), getDiasExtras()));
            diaExtraRegistrado = true;
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE,
                    "Não foi possível permitir uso do sistema na data atual.", e);
        }

        if (diaExtraRegistrado) {
            final DiaExtraDTO diaExtraDTO = new DiaExtraDTO();
            diaExtraDTO.setChave((String) JSFUtilities.getFromSession(JSFUtilities.KEY));
            diaExtraDTO.setCodigoEmpresa(getEmpresaLogado().getCodigo());
            diaExtraDTO.setNomeEmpresa(getEmpresaLogado().getNome());
            diaExtraDTO.setCodigoUsuario(getUsuarioLogado().getCodigo());
            diaExtraDTO.setNomeUsuario(getUsuarioLogado().getUsername());
            diaExtraDTO.setDataDesbloqueio(getEmpresaLogado().getDataConcessaoDiaExtra().getTime());

            DiaExtraService.getInstance().registrarDiaExtra(diaExtraDTO);
        }

        return verificarDiasParaBloqueio();
    }

    public boolean getUtilizaChatMovidesk() {
        if (JSFUtilities.isJSFContext()) {
            final Object utilizarMoviDesk = JSFUtilities.getFromSession(JSFUtilities.UTILIZA_CHAT_MOVIDESK);
            if (utilizarMoviDesk != null && (Boolean) utilizarMoviDesk) {
                try {
                    final String chave = getKey();
                    final Integer codigoEmpresaFinaceiro = getEmpresaLogado().getCodEmpresaFinanceiro();
                    final Integer codigoColaborador = getUsuarioLogado().getColaboradorVO().getCodigo();

                    codigoColaboradorMovidesk = codigoEmpresaFinaceiro + "-" + codigoColaborador;
                    codigoEmpresaMovidesk = codigoEmpresaFinaceiro.toString();
                    codigoEmpresaFinMovidesk = codigoEmpresaFinaceiro.toString();

                    return !UteisValidacao.emptyNumber(codigoEmpresaFinaceiro) && !UteisValidacao.emptyNumber(codigoColaborador);
                } catch (Exception e) {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "Erro ao verificar se o cliente pode usar o CHAT do Movidesk", e);
                    return false;
                }
            }
        }
        return false;
    }

    public Boolean getUtilizaOctadesk() {
        if (JSFUtilities.isJSFContext()) {
            return (Boolean) JSFUtilities.getFromSession(JSFUtilities.UTILIZA_OCTADESK);
        }
        return false;
    }

    public Boolean getUtilizaGymbot() {
        if (JSFUtilities.isJSFContext()) {
            return (Boolean) JSFUtilities.getFromSession(JSFUtilities.UTILIZA_GYMBOT);
        }
        return false;
    }

    public static String sorteiaNumeroWhatsApp() {
//        Random random = new Random();
//        String[] numeros = {"5562994109331", "5562983294142", "5562984347163","5562983294224","5562981730707","5562983179845"};
//        try {
//            return numeros[random.nextInt(3)];
//        } catch (Exception e) {
//            return numeros[0];
//        }
        return "556234140314";
    }

    public String getWhatsFinanceiro() {
        try {
            return obterLinkWhatsFinanceiro(getEmpresaLogado(),getNome());
        } catch (Exception exception) {
            Uteis.logar(exception.getMessage());
        }
        return "";
    }

    public static String obterLinkWhatsFinanceiro(EmpresaVO empresaVO, String nomeUsuario) {
        StringBuilder whatsapp = new StringBuilder("https://api.whatsapp.com/send?phone=");
        whatsapp.append(sorteiaNumeroWhatsApp());
        whatsapp.append("&text=");
        StringBuilder text = new StringBuilder();
        text.append("Empresa: ").append(empresaVO.getCodigo());
        try {
            text.append(". Financeiro: ").append(empresaVO.getCodEmpresaFinanceiro().toString());
        } catch (Exception ignored) {
        }
        text.append(". Usuario: ").append(nomeUsuario);
        text.append(". Conversa iniciada a partir da tela de bloqueio do sistema.");

        try {
            whatsapp.append(URLEncoder.encode(text.toString(), "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            whatsapp.append(text.toString());
        }
        return whatsapp.toString();
    }

    public String getCodigoColaboradorMovidesk() {
        if (codigoColaboradorMovidesk == null) {
            codigoColaboradorMovidesk = "";
        }

        return codigoColaboradorMovidesk;
    }

    public String getCodigoEmpresaMovidesk() {
        if (codigoEmpresaMovidesk == null) {
            codigoEmpresaMovidesk = "";
        }

        return codigoEmpresaMovidesk;
    }

    public String getCodigoEmpresaFinMovidesk() {
        return codigoEmpresaFinMovidesk;
    }

    public void notificarAssinaturaDigital() {
        notificarRecursoEmpresa(RecursoSistema.ASSINATURA_DIGITAL_TOTALACESSOS_VIANAVEGADOR);
    }

    public void notificarCartaoVacina() {
        notificarRecursoEmpresa(RecursoSistema.CARTAO_DE_VACINA_TOTALACESSOS_VIANAVEGADOR);
    }

    //utilizado para apresentar a configuracao de nota fiscal em FuncionalidadeSistemaEnum
    public boolean isUtilizaEmissaoNotaFiscal() {
        try {

            boolean temPermissaoUtilizaNotaFiscal = permissaoAcessoMenuVO.getPermiteAlterarRPS() ? true : false;

            if ((getUsuarioLogado().getUsuarioPactoSolucoes() || getEmpresaLogado().isUsarNFCe() || getEmpresaLogado().getUsarNFSe())
                    && temPermissaoUtilizaNotaFiscal) {
                return true;
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

    public void notificarRecursoEmpresaVendaRapida() {
        notificarRecursoEmpresa(RecursoSistema.VENDA_RAPIDA);
    }

    public void notificarRecursoEmpresaGameOfResults() {
        notificarRecursoEmpresa(RecursoSistema.GAME_OF_RESULTS);
    }

    public void notificarRecursoPactoUniversidade() {
        notificarRecursoEmpresa(RecursoSistema.PACTO_UNIVERSIDADE);
    }

    public String getUrlNFSe() {
        return urlNFSe;
    }

    public void setUrlNFSe(String urlNFSe) {
        this.urlNFSe = urlNFSe;
    }

    private void verificarEmbaralharDados() throws Exception {
        if (getKey().startsWith(SuperControle.nomeInicialChaveEmbaralhamento) && !getFacade().getSinalizadorSistema().consultarPorSinalizador(SinalizadorEnum.BDEMBARALHADO)) {
            try {
                new EmbaralhadorControle().embaralharDados(getKey());
                getFacade().getSinalizadorSistema().incluir(SinalizadorEnum.BDEMBARALHADO, true);
            } catch (Exception e) {
                throw new Exception("Falha ao embaralhar dados do banco");
            }
        }
    }

    public void naoApresentarModalVerificarUsuarios() {
        try {
            setMsgModalUsuariosAtivos("");
        } catch (Exception ignored) {
        }
    }

    public String getMsgModalUsuariosAtivos() {
        return (msgModalUsuariosAtivos == null ? "" : this.msgModalUsuariosAtivos);
    }

    public void setMsgModalUsuariosAtivos(String msgModalUsuariosAtivos) {
        this.msgModalUsuariosAtivos = msgModalUsuariosAtivos;
    }

    public void verificarBannerEmergencia() {
        try {
            urlBannerEmergencia = "";
            if (getUsuarioLogado() != null && getUsuarioLogado().getPossuiPerfilAcessoAdministrador()) {
                String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/emergencia/banner/" + getKey();
                String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
                JSONObject resposta = new JSONObject(response).getJSONObject("content");
                if (resposta.getBoolean("temBanner")) {
                    urlBannerEmergencia = resposta.getString("banner");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            urlBannerEmergencia = "";
        }
    }

    public String getUrlBannerEmergencia() {
        return urlBannerEmergencia;
    }

    public boolean isApresentarModalVerificarUsuarios() {
        try {
            return !UteisValidacao.emptyString(getMsgModalUsuariosAtivos()) && !getUsuarioLogado().isModalInativar();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public boolean isIgnorarPlanosInativosHoje() {
        return ignorarPlanosInativosHoje;
    }

    public void setIgnorarPlanosInativosHoje(boolean ignorarPlanosInativosHoje) {
        this.ignorarPlanosInativosHoje = ignorarPlanosInativosHoje;
    }

    public List<PlanoVO> getListaPlanosProximosInativar() {
        return listaPlanosProximosInativar;
    }

    public void setListaPlanosProximosInativar(List<PlanoVO> listaPlanosProximosInativar) {
        this.listaPlanosProximosInativar = listaPlanosProximosInativar;
    }

    public void atualizarListaPlanos() {
        try {
            listaPlanosProximosInativar = getFacade().getPlano().consultarPlanosProximosInativar(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isApresentarModalPlanosInativos() {
        try {
            UsuarioVO usuario = getUsuarioLogado();
            Date dataExibicao = usuario.getDataExibirModalPlanos();

            // Se a dataExibicao não for nula e for igual a hoje, não exibe o modal
            if (dataExibicao != null && Calendario.igual(dataExibicao, Calendario.hoje())) {
                return false;
            }
            return !UteisValidacao.emptyString(getMsgModalPlanosInativos());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    // Método para fechar o modal, ou seja, não exibir a mensagem novamente
    public void naoApresentarModalPlanosInativos() {
        try {
            UsuarioVO usuario = getUsuarioLogado();
            this.ignorarPlanosInativosHoje = true;
            usuario.setDataExibirModalPlanos(new Date());
            setMsgModalPlanosInativos("");
        } catch (Exception ignored) {
        }
    }

    // Getter e setter para a mensagem do modal de planos inativos
    public String getMsgModalPlanosInativos() {
        return msgModalPlanosInativos;
    }

    public void setMsgModalPlanosInativos(String msgModalPlanosInativos) {
        this.msgModalPlanosInativos = msgModalPlanosInativos;
    }

    public String getMensagemCliente() {
        return mensagemCliente;
    }

    public void setMensagemCliente(String mensagemCliente) {
        this.mensagemCliente = mensagemCliente;
    }

    public String getTelefoneCliente() {
        return telefoneCliente;
    }

    public void setTelefoneCliente(String telefoneCliente) {
        this.telefoneCliente = telefoneCliente;
    }

    public int getDiasExtras() {
        return diasExtras;
    }

    public void setDiasExtras(int diasExtras) {
        this.diasExtras = diasExtras;
    }

    public List<SelectItem> getListaDias() {
        if (null == listaDias) {
            listaDias = new ArrayList<SelectItem>();
        }
        return listaDias;
    }

    public boolean isAbrirRichModalAlertaSistemaExpirado() {
        try {
            abrirRichModalAlertaSistemaExpirado = (boolean) JSFUtilities.getFromSession("abrirRichModalAlertaSistemaExpirado");
        } catch (Exception e) {
            abrirRichModalAlertaSistemaExpirado = false;
        }
        return abrirRichModalAlertaSistemaExpirado;
    }

    public void setAbrirRichModalAlertaSistemaExpirado(boolean abrirRichModalAlertaSistemaExpirado) {
        this.abrirRichModalAlertaSistemaExpirado = abrirRichModalAlertaSistemaExpirado;
    }

    public String getUsaAppTreino() {
        return usaAppTreino;
    }

    public void setUsaAppTreino(String usaAppTreino) {
        this.usaAppTreino = usaAppTreino;
    }

    public boolean isUsouTodosDiasExtrasPermitidos() {
        return usouTodosDiasExtrasPermitidos;
    }

    public void setUsouTodosDiasExtrasPermitidos(boolean usouTodosDiasExtrasPermitidos) {
        this.usouTodosDiasExtrasPermitidos = usouTodosDiasExtrasPermitidos;
    }

    public Boolean getApresentarPactoStore() {
        if (apresentarPactoStore == null) {
            inicializarApresentarPactoStore();
        }
        return apresentarPactoStore;
    }

    public void setApresentarPactoStore(Boolean apresentarPactoStore) {
        this.apresentarPactoStore = apresentarPactoStore;
    }

    private void inicializarApresentarPactoStore() {
        try {
            boolean administrador = getUsuarioLogado().getPossuiPerfilAcessoAdministrador();
            if (!administrador) {
                this.apresentarPactoStore = false;
                return;
            }

            boolean config = getFacade().getConfiguracaoSistema().isApresentarMarketPlace();
            if (!config) {
                this.apresentarPactoStore = false;
                return;
            }

            this.apresentarPactoStore = true;
        } catch (Exception ex) {
            ex.printStackTrace();
            this.apresentarPactoStore = false;
        }
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public String getMensagemRecursoDesativado() {
        return "Olá! Sou seu sistema Pacto e estou sem recursos para trabalhar :(<br/>" +
                "Esta função está desabilitada temporariamente devido pendências financeiras";
    }

    public String getInfoMensagemRecursoDesativado() {
        return "Vamos virar esse jogo?<br/><br/>" +
                "Estou ao seu lado, selecione uma opção:";
    }

    public Boolean getApresentarAcessoNovoPlano() {
        try {
            if (!getPermissaoAcessoMenuVO().getPlano()) {
                return false;
            }
            if (getPossuiModuloNZW() && !getUsuarioLogado().getUsuarioAdminPACTO()) {
                if (getConfiguracaoSistema().getForcarUtilizacaoPlanoAntigo()) {
                    return false;
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean getPossuiModuloNZW() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("NZW");
    }

    public boolean isPossuiModuloPAY() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains("PAY");
    }

    public boolean isApresentarModuloPactoPay() {
        try {
            return isPossuiModuloPAY() &&
                    getPermissaoAcessoMenuVO() != null &&
                    getPermissaoAcessoMenuVO().getModuloPactoPay();
        } catch (Exception ex) {
            return false;
        }
    }


    public RedeEmpresaVO getRedeEmpresaVO() {
        return redeEmpresaVO;
    }
    public void setRedeEmpresaVO(RedeEmpresaVO redeEmpresaVO) {
        this.redeEmpresaVO = redeEmpresaVO;
    }

    public boolean isUtilizaModuloGestaoRedes() {
        return getRedeEmpresaVO() != null && getRedeEmpresaVO().getGestaoRedes();
    }

    public Boolean getValidarTelaRelatorios(){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        return !"ADM-BI".equals(request.getAttribute("grupoFuncionalidadeMenu"));
    }

    public CustomerSuccessTO getResponsavelPacto() {
        return responsavelPacto;
    }

    public void setResponsavelPacto(CustomerSuccessTO responsavelPacto) {
        this.responsavelPacto = responsavelPacto;
    }

    public UsuarioEmailVO getUsuarioEmailVO() {
        if (usuarioEmailVO == null) {
            usuarioEmailVO = new UsuarioEmailVO();
        }
        return usuarioEmailVO;
    }

    public void setUsuarioEmailVO(UsuarioEmailVO usuarioEmailVO) {
        this.usuarioEmailVO = usuarioEmailVO;
    }

    public Boolean getConcordoTermosUsoAnuncio() {
        if (concordoTermosUsoAnuncio == null) {
            concordoTermosUsoAnuncio = false;
        }
        return concordoTermosUsoAnuncio;
    }

    public void setConcordoTermosUsoAnuncio(Boolean concordoTermosUsoAnuncio) {
        this.concordoTermosUsoAnuncio = concordoTermosUsoAnuncio;
    }

    public String getStyle_ConcordoTermoUsoAnuncio() {
        if (getConcordoTermosUsoAnuncio()) {
            return "background-color: #2EC750 !important; cursor: ponter;";
        }
        return "background-color: #F3F3F4 !important; cursor: default;";
    }

    public String getClass_ConcordoTermoUsoAnuncio() {
        if (getConcordoTermosUsoAnuncio()) {
            return "";
        }
        return "botao-li-aceito-desabilitado";
    }

    public Boolean getNaoQueroLucrar() {
        if (naoQueroLucrar == null) {
            naoQueroLucrar = false;
        }
        return naoQueroLucrar;
    }

    public void setNaoQueroLucrar(Boolean naoQueroLucrar) {
        this.naoQueroLucrar = naoQueroLucrar;
    }

    public void acaoNaoQueroLucrar() {
        naoQueroLucrar = true;
    }

    public void acaoSaibaMais() {
        naoQueroLucrar = false;
    }

    public Boolean getApresentarModalAnuncio() {
        if (apresentarModalAnuncio == null) {
            apresentarModalAnuncio = false;
        }
        return apresentarModalAnuncio;
    }

    public void setApresentarModalAnuncio(Boolean apresentarModalAnuncio) {
        this.apresentarModalAnuncio = apresentarModalAnuncio;
    }

    public void finalizarNaoQueroLucrar() {
        apresentarModalAnuncio = false;
    }

    private boolean apresentarAnuncio(UsuarioVO usuarioVO, PerfilAcessoVO perfilAcessoVO) {
        if (usuarioVO.getAdministrador()) {
            return false;
        }

        if (usuarioVO.getUsuarioPactoSolucoes()) {
            return false;
        }

        PessoaVO pessoa = usuarioVO.getColaboradorVO().getPessoa();
        return FuncaoEnum.SOCIOPROPRIETARIO.equals(pessoa.getFuncaoEnum())
                || (CargoEnum.DIRECAO.equals(pessoa.getCargoEnum()) && PerfilUsuarioEnum.ADMINISTRADOR.equals(perfilAcessoVO.getTipo()));
    }

    public void enviarRejeicao() {
        try {
            ResultadoVitioJSON resultadoVitioJSON = prepararResultadoVitio();
            resultadoVitioJSON.setStatus(StatusRespostaVitio.RECUSADO);
            resultadoVitioJSON.setMotivoRecusa(getMotivoRecusa());

            String urlVitioSave = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/vitio/save";
            ExecuteRequestHttpService.post(urlVitioSave, resultadoVitioJSON.toString(), new HashMap<>());

            montarSucessoGrowl("Obrigado por sua resposta!");
            setApresentarModalAnuncio(false);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void enviarAceite() {
        try {
            ResultadoVitioJSON resultadoVitioJSON = prepararResultadoVitio();
            resultadoVitioJSON.setStatus(StatusRespostaVitio.ACEITO);
            resultadoVitioJSON.setMotivoRecusa("");

            String urlVitioSave = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/vitio/save";
            ExecuteRequestHttpService.post(urlVitioSave, resultadoVitioJSON.toString(), new HashMap<>());

            List<String> emails = new ArrayList<>();
            emails.add(getUsuarioEmailVO().getEmail());
            enviarEmailVitio(emails.toArray(new String[emails.size()]), SuperControle.getConfiguracaoSMTPNoReply());

            montarSucessoGrowl("Obrigado por sua resposta!");
            setApresentarModalAnuncio(false);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void enviarEmailVitio(String[] emails, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) throws Exception {
        UteisEmail email = new UteisEmail();

        File arq = new File(getClass().getResource("/vitio/email/template-aceitoutermos.html").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "UTF-8");

        email.novo("Vitio - Confirmação", configuracaoSistemaCRMVO);
        email.setRemetente(configuracaoSistemaCRMVO.getRemetentePadraoMailing());

        boolean existeEmailValido = false;
        for (String emailEnviar : emails) {
            if (UteisValidacao.validaEmail(emailEnviar)) {
                existeEmailValido = true;
            }
        }
        if (existeEmailValido) {
            MsgTO msg = new MsgTO(new StringBuffer(texto.toString()), "Confirmação de parceria", "Vitio", true,
                    configuracaoSistemaCRMVO, false, emails);

            UteisValidacao.enfileirarEmail(msg);
        } else {
            throw new Exception("No foi possível enviar o contrato pois o cliente no possui um email válido.");
        }
    }

    private ResultadoVitioJSON prepararResultadoVitio() throws Exception {
        ResultadoVitioJSON resultadoVitioJSON = new ResultadoVitioJSON();
        resultadoVitioJSON.setCodUsuario(getUsuario().getCodigo());
        resultadoVitioJSON.setNome(getUsuario().getNome());

        try {
            String telefone = getUsuario().getColaboradorVO().getPessoa().getTelefoneVOs().get(0).getNumeroApresentar();
            resultadoVitioJSON.setTelefone(telefone);
        } catch (Exception ignored) {
            resultadoVitioJSON.setTelefone("Não informado");
        }

        resultadoVitioJSON.setEmail(getUsuario().getEmail());
        resultadoVitioJSON.setIp(getIp());
        resultadoVitioJSON.setChave(getKey());
        resultadoVitioJSON.setDataResposta(Calendario.hoje());
        return resultadoVitioJSON;
    }

    private RetornoVitioJSON consultarVitio(UsuarioVO usuarioVO) {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/vitio/"+getKey()+"/apresentar-anuncio/" + usuarioVO.getCodigo();
            String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
            if (response == null) {
                return null;
            }
            JSONObject resposta = new JSONObject(response);
            return JSONMapper.getObject(resposta, RetornoVitioJSON.class);
        } catch (Exception ex) {
            Uteis.logar(ex, LoginControle.class);
        }
        return null;
    }

    public String getMotivoRecusa() {
        if (motivoRecusa == null) {
            motivoRecusa = "";
        }
        return motivoRecusa;
    }

    public void setMotivoRecusa(String motivoRecusa) {
        this.motivoRecusa = motivoRecusa;
    }

    public Boolean getAbrirRichModalSolicitarDesbloqueio() {
        return abrirRichModalSolicitarDesbloqueio;
    }

    public void setAbrirRichModalSolicitarDesbloqueio(Boolean abrirRichModalSolicitarDesbloqueio) {
        this.abrirRichModalSolicitarDesbloqueio = abrirRichModalSolicitarDesbloqueio;
    }

    public String getSabeDoCanalDoCliente() {
        return sabeDoCanalDoCliente;
    }

    public void setSabeDoCanalDoCliente(String sabeDoCanalDoCliente) {
        this.sabeDoCanalDoCliente = sabeDoCanalDoCliente;
    }

    public String getTituloModalBloqueio() {
        return tituloModalBloqueio;
    }

    public void setTituloModalBloqueio(String tituloModalBloqueio) {
        this.tituloModalBloqueio = tituloModalBloqueio;
    }

    public boolean isApresentarModalUsuarioGeral() {
        return apresentarModalUsuarioGeral;
    }

    public void setApresentarModalUsuarioGeral(boolean apresentarModalUsuarioGeral) {
        this.apresentarModalUsuarioGeral = apresentarModalUsuarioGeral;
    }

    public UsuarioEmailVO getUsuarioEmailAlterarVO() {
        if (usuarioEmailAlterarVO == null) {
            usuarioEmailAlterarVO = new UsuarioEmailVO();
        }
        return usuarioEmailAlterarVO;
    }

    public void setUsuarioEmailAlterarVO(UsuarioEmailVO usuarioEmailAlterarVO) {
        this.usuarioEmailAlterarVO = usuarioEmailAlterarVO;
    }

    public TokenDTO getTokenDTO() {
        if (tokenDTO == null) {
            tokenDTO = new TokenDTO();
        }
        return tokenDTO;
    }

    public void setTokenDTO(TokenDTO tokenDTO) {
        this.tokenDTO = tokenDTO;
    }

    private void validarAtualizacaoUsuarioGeral() throws Exception {
        if (usuarioEmailVO == null) {
            usuarioEmailVO = new UsuarioEmailVO();
        }
        usuarioEmailVO.setUsuario(getUsuarioLogado().getCodigo());

        this.setApresentarModalUsuarioGeral(!getUsuario().getUsuarioPactoSolucoes() &&
                (UteisValidacao.emptyString(getUsuario().getUsuarioGeral()) ||
                        UteisValidacao.emptyString(usuarioEmailVO.getEmail()) ||
                        !usuarioEmailVO.isVerificado()));

        this.setTokenDTO(new TokenDTO());
        this.setUsuarioEmailAlterarVO(new UsuarioEmailVO());
        this.getUsuarioEmailAlterarVO().setCodigo(usuarioEmailVO.getCodigo());
        this.getUsuarioEmailAlterarVO().setUsuario(usuarioEmailVO.getUsuario());
        this.getUsuarioEmailAlterarVO().setEmail(usuarioEmailVO.getEmail());
    }

    public void validarEmailDepois() {
        try {
            limparMsg();
            setMsgAlert("");
            this.setApresentarModalUsuarioGeral(false);
            this.setMsgAlert("Richfaces.hideModalPanel('modalUsuarioGeral');");
            notificarRecursoEmpresa(UteisValidacao.emptyString(this.getUsuarioEmailAlterarVO().getEmail()) ?
                    RecursoSistema.VALIDAR_EMAIL_MAIS_TARDE_SEM_EMAIL : RecursoSistema.VALIDAR_EMAIL_MAIS_TARDE_COM_EMAIL);
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void soliticarTokenNovoEmail() {
        try {
            limparMsg();
            setMsgAlert("");
            this.setTokenDTO(new TokenDTO());

            if (UteisValidacao.emptyString(this.getUsuarioEmailAlterarVO().getEmail())) {
                throw new Exception("E-mail não informado");
            }

            if (!UteisValidacao.validaEmail(this.getUsuarioEmailAlterarVO().getEmail())) {
                throw new Exception("E-mail inválido");
            }

            if (getFacade().getUsuarioEmail().existeEmail(this.getUsuarioEmailAlterarVO())) {
                throw new ConsistirException("Já existe um usuário com esse e-mail.");
            }

            String token;
            if (isUsuarioGeralSincronizado()) {
                token = SincronizarUsuarioNovoLogin.solicitarTrocaEmail(this.getUsuario().getCodigo(),
                        true, true, this.getUsuarioEmailAlterarVO(), Conexao.getFromSession(),
                        getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
            } else {
                token = SincronizarUsuarioNovoLogin.enviarEmailNovoUsuario(this.getUsuario().getCodigo(), false, true,
                        this.getUsuarioEmailAlterarVO().getEmail(), Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getUsuarioLogado(),
                        getIpCliente(), getIntegracaoNovoLogin());
            }

            notificarRecursoEmpresa(RecursoSistema.VALIDAR_EMAIL_SOLICITACAO);

            this.getTokenDTO().setToken(token);
            this.getTokenDTO().setUsuarioGeral(this.getUsuario().getUsuarioGeral());
            setMsgAlert("document.getElementById('formModalUsuarioGeral:usuarioTokenEmailLogin').focus();");
            montarSucessoGrowl("Informe o código recebido por e-mail");
        } catch (Exception e) {
            notificarRecursoEmpresa(RecursoSistema.VALIDAR_EMAIL_SOLICITACAO_ERRO);
            e.printStackTrace();
            montarErro(e);
        }
    }

    private boolean isUsuarioGeralSincronizado() throws Exception {
        return !UteisValidacao.emptyString(this.getUsuario().getUsuarioGeral());
    }

    public void validarCodigoVerificacaoEmail() {
        try {
            limparMsg();
            setMsgAlert("");

            if (UteisValidacao.emptyString(this.getTokenDTO().getCodigoVerificacao())) {
                throw new Exception("Código de verificação não informado");
            }
            if (this.getTokenDTO().getCodigoVerificacao().length() < 6) {
                throw new Exception("Código de verificação incompleto");
            }
            SincronizarUsuarioNovoLogin.validarToken(this.getUsuario().getCodigo(), this.getTokenDTO(),
                    Conexao.getFromSession(), getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), "ATUALIZACAO_EMAIL_USUARIO",
                    getIntegracaoNovoLogin());


            setApresentarModalUsuarioGeral(false);
            setMsgAlert("Richfaces.hideModalPanel('modalUsuarioGeral');");
            montarSucessoGrowl("E-mail validado com sucesso");
            notificarRecursoEmpresa(RecursoSistema.VALIDAR_EMAIL_VALIDAR_CODIGO);
        } catch (Exception ex) {
            notificarRecursoEmpresa(RecursoSistema.VALIDAR_EMAIL_VALIDAR_CODIGO_ERRO);
            ex.printStackTrace();
            montarErro(ex);
            setMensagemDetalhada(ex);
        }
    }

    public void abrirListaRapidaAcessos(){
        if(UteisValidacao.emptyString(tokenNT)){
            preencherTokenNovoTreino();
        }
    }


    public Boolean getExibirQrCodeUsuarioChave() {
        try {
            verificarExibirQrCodeUsuarioChave();
        } catch (Exception ignore){
            exibirQrCodeUsuarioChave = false;
        }
        return exibirQrCodeUsuarioChave;
    }

    public void setExibirQrCodeUsuarioChave(Boolean exibirQrCodeUsuarioChave) {
        this.exibirQrCodeUsuarioChave = exibirQrCodeUsuarioChave;
    }

    public Boolean getPermiteExclusaoClienteUsuarioComum() {
        return permiteExclusaoClienteUsuarioComum;
    }

    public void setPermiteExclusaoClienteUsuarioComum(Boolean permiteExclusaoClienteUsuarioComum) {
        this.permiteExclusaoClienteUsuarioComum = permiteExclusaoClienteUsuarioComum;
    }

    public String getLabelVencimentoCertificado() {
        return labelVencimentoCertificado;
    }

    public void setLabelVencimentoCertificado(String labelVencimentoCertificado) {
        this.labelVencimentoCertificado = labelVencimentoCertificado;
    }

    public String getModuloAbertoSigla() {
        return moduloAbertoSigla;
    }

    public void setModuloAbertoSigla(String moduloAbertoSigla) {
        this.moduloAbertoSigla = moduloAbertoSigla;
    }

    public void setLastJspPage(String lastJspPage) {
        this.lastJspPage = lastJspPage;
    }

    public String getLastJspPage() {
        return lastJspPage;
    }

    public boolean isIgnorarHistoricoNavegacaoModulo() {
        return ignorarHistoricoNavegacaoModulo;
    }

    public void setIgnorarHistoricoNavegacaoModulo(boolean ignorarHistoricoNavegacaoModulo) {
        this.ignorarHistoricoNavegacaoModulo = ignorarHistoricoNavegacaoModulo;
    }
    public Boolean getNegociacaoHabilitado(){
        return negociacaoHabilitada;
    }

    public void setFuncionalidadesInativas(List<FuncionalidadeSistemaEnum> funcionalidadesInativas) {
        this.funcionalidadesInativas = funcionalidadesInativas;
    }

    public List<FuncionalidadeSistemaEnum> getFuncionalidadesInativas() {
        return funcionalidadesInativas;
    }

    public boolean isFuncionalidadeAtiva(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        return funcionalidadesInativas == null || !funcionalidadesInativas.contains(funcionalidadeSistemaEnum);
    }

    public boolean isBiCrmAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.CRM_BI);
    }

    public boolean isMailingAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.MAILING);
    }

    public boolean isContatoPessoaAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.CONTATO_PESSOAL);
    }

    public boolean isGestaoCarteirasAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.CARTEIRAS);
    }

    public boolean isMarcarComparecimentoAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.MARCAR_COMPARECIMENTO);
    }

    public boolean isMetaDiariaAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.CRM_META_DIARIA);
    }

    public boolean isRegistroParalizacaoAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.CRM_REGISTRO_PARALISACAO);
    }

    public boolean isBiFinanceiroAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.FIN_BI);
    }

    public boolean isMetaFinancieroAtivo(){
        return isFuncionalidadeAtiva(FuncionalidadeSistemaEnum.METAS_DO_FINANCEIRO);
    }

    public String getTokenNZW() {
        return tokenNZW;
    }

    public void setTokenNZW(String tokenNZW) {
        this.tokenNZW = tokenNZW;
    }

    public String getTokenNT() {
        return tokenNT;
    }

    public void setTokenNT(String tokenNT) {
        this.tokenNT = tokenNT;
    }

    public boolean isApresentarMensagensLogin() {
        return apresentarMensagensLogin;
    }

    public boolean isEmpresaLogadaIsFranqueadora() {
        try {
            if (redeEmpresaVO == null) {
                redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
            }
            if (redeEmpresaVO != null && !UteisValidacao.emptyString(redeEmpresaVO.getChaveFranqueadora())
                    && redeEmpresaVO.getChaveFranqueadora().equals(getKey())) {
                return true;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isEmpresaLogadaNaoPossuiFranqueadora() {
        if (redeEmpresaVO == null) {
            redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
        }
        return redeEmpresaVO == null || UteisValidacao.emptyString(redeEmpresaVO.getChaveFranqueadora());
    }

    public List<FuncionalidadeSistemaEnum> getFuncionalidadesFavoritos() {
        if (funcionalidadesFavoritos == null) {
            funcionalidadesFavoritos = new ArrayList<>();
        }
        return funcionalidadesFavoritos;
    }

    public void setFuncionalidadesFavoritos(List<FuncionalidadeSistemaEnum> funcionalidadesFavoritos) {
        this.funcionalidadesFavoritos = funcionalidadesFavoritos;
    }

    public void preencherFuncionalidadesFavoritos() {
        try {
            if (getKey() != null && getEmpresa() != null) {
                setFuncionalidadesFavoritos(getFacade().getFavorito().consultarAcessoFacil(this.getUsuario().getCodigo(), FavoritoEnum.ACESSO_FACIL_FAVORITO, 15));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isFuncionalidadeFavorito(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        return funcionalidadesFavoritos != null && funcionalidadesFavoritos.contains(funcionalidadeSistemaEnum);
    }

    public boolean isPodeLancarAviso() {
        return podeLancarAviso;
    }

    public void setPodeLancarAviso(boolean podeLancarAviso) {
        this.podeLancarAviso = podeLancarAviso;
    }

    public boolean isApresentarLinkIncluirCliente() {
        return apresentarLinkIncluirCliente;
    }

    public void setApresentarLinkIncluirCliente(boolean apresentarLinkIncluirCliente) {
        this.apresentarLinkIncluirCliente = apresentarLinkIncluirCliente;
    }

    public OctadeskUserTO getOctadeskUserTO() {
        if (octadeskUserTO == null) {
            octadeskUserTO = new OctadeskUserTO();
        }
        return octadeskUserTO;
    }

    public void setOctadeskUserTO(OctadeskUserTO octadeskUserTO) {
        this.octadeskUserTO = octadeskUserTO;
    }

    public void obterAvisosInternos() {
        try {
            Boolean permissaoAvisos = getPermissaoAcessoMenuVO().getPermitirAvisosInternos();
            List<AvisoInternoDTO> avisoInternoDTOS = getFacade().getPerfilAcesso().avisosInternos(getEmpresaLogado().getCodigo(),
                    getUsuario().getCodigo(), permissaoAvisos);
            setAvisosInternos(avisoInternoDTOS);
        }catch (Exception e) {
            Uteis.logar(e, LoginControle.class);
        }
    }

    public List<AvisoInternoDTO> getAvisosInternos() {
        if (avisosInternos == null) {
            avisosInternos = new ArrayList();
        }

        return avisosInternos;
    }

    public void setAvisosInternos(List<AvisoInternoDTO> avisosInternos) {
        this.avisosInternos = avisosInternos;
    }

    public boolean isTemPerfilAcessoUnificado() {
        return temPerfilAcessoUnificado;
    }

    public void setTemPerfilAcessoUnificado(boolean temPerfilAcessoUnificado) {
        this.temPerfilAcessoUnificado = temPerfilAcessoUnificado;
    }

    public boolean isAbrirRelatorioFrequenciaTurma() {
        try {
            getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(getPerfilAcesso(), getUsuario(),
                    "ConsultarRelatorioFrequenciaTurmas", "10.08 - Consultar relatório de frequencia de turmas");
            return true;
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }

        return false;
    }

    public void setAbrirRelatorioFrequenciaTurma(boolean abrirRelatorioFrequenciaTurma) {
        this.abrirRelatorioFrequenciaTurma = abrirRelatorioFrequenciaTurma;
    }

    public String getOnCompleteModuloAtual() {
        return onCompleteModuloAtual;
    }

    public void setOnCompleteModuloAtual(String onCompleteModuloAtual) {
        this.onCompleteModuloAtual = onCompleteModuloAtual;
    }
}
