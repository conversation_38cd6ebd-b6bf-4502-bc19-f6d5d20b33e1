package controle.arquitetura.security;

import br.com.pactosolucoes.ce.controle.FornecedorControle;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ReplicarRedeEmpresaEnum;
import controle.basico.ColaboradorControle;
import controle.crm.MalaDiretaControle;
import controle.financeiro.PlanoContasControle;
import controle.plano.PlanoControle;
import controle.plano.ProdutoControle;
import negocio.comuns.arquitetura.PerfilAcessoRedeEmpresaVO;
import negocio.comuns.arquitetura.PlanoContaRedeEmpresaVO;
import negocio.comuns.arquitetura.UsuarioRedeEmpresaVO;
import negocio.comuns.basico.ColaboradorRedeEmpresaVO;
import negocio.comuns.crm.MalaDiretaRedeEmpresaVO;
import negocio.comuns.plano.FornecedorRedeEmpresaVO;
import negocio.comuns.plano.PlanoRedeEmpresaVO;
import negocio.comuns.plano.ProdutoRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.Callable;

public class ReplicarRedeEmpresaCallable implements Callable<String> {

    private final HttpServletRequest request;
    private final HttpServletResponse response;
    private final ReplicarRedeEmpresaEnum replicarRedeEmpresa;
    private final UsuarioRedeEmpresaVO usuarioRedeEmpresa;
    private final ColaboradorRedeEmpresaVO colaboradorRedeEmpresa;
    private final PerfilAcessoRedeEmpresaVO perfilAcessoRedeEmpresa;
    private final MalaDiretaRedeEmpresaVO malaDiretaRedeEmpresaVO;
    private final PlanoRedeEmpresaVO planoRedeEmpresaVO;
    private final ProdutoRedeEmpresaVO produtoRedeEmpresaVO;
    private final FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO;
    private final PlanoContaRedeEmpresaVO planoContaRedeEmpresaVO;

    public ReplicarRedeEmpresaCallable(HttpServletRequest request,
                                       HttpServletResponse response,
                                       ReplicarRedeEmpresaEnum replicarRedeEmpresa,
                                       UsuarioRedeEmpresaVO usuarioRedeEmpresa,
                                       ColaboradorRedeEmpresaVO colaboradorRedeEmpresa,
                                       PerfilAcessoRedeEmpresaVO perfilAcessoRedeEmpresa,
                                       MalaDiretaRedeEmpresaVO malaDiretaRedeEmpresaVO,
                                       PlanoRedeEmpresaVO planoRedeEmpresaVO,
                                       ProdutoRedeEmpresaVO produtoRedeEmpresaVO,
                                       FornecedorRedeEmpresaVO fornecedorRedeEmpresaVO,
                                       PlanoContaRedeEmpresaVO planoContaRedeEmpresaVO) {
        this.request = request;
        this.response = response;
        this.replicarRedeEmpresa = replicarRedeEmpresa;
        this.usuarioRedeEmpresa = usuarioRedeEmpresa;
        this.colaboradorRedeEmpresa = colaboradorRedeEmpresa;
        this.perfilAcessoRedeEmpresa = perfilAcessoRedeEmpresa;
        this.malaDiretaRedeEmpresaVO = malaDiretaRedeEmpresaVO;
        this.planoRedeEmpresaVO = planoRedeEmpresaVO;
        this.produtoRedeEmpresaVO = produtoRedeEmpresaVO;
        this.fornecedorRedeEmpresaVO = fornecedorRedeEmpresaVO;
        this.planoContaRedeEmpresaVO = planoContaRedeEmpresaVO;
    }

    @Override
    public String call() throws Exception {
        try {
            JSFUtilities.getFacesContext(request, response);
            switch (replicarRedeEmpresa) {
                case USUARIO:
                    replicarUsuarios(usuarioRedeEmpresa);
                    break;
                case COLABORADOR:
                    replicarColaborador(colaboradorRedeEmpresa);
                    break;
                case PERFIL_ACESSO:
                    replicarPerfilAcesso(perfilAcessoRedeEmpresa);
                    break;
                case MALA_DIRETA:
                    replicarMalaDireta(malaDiretaRedeEmpresaVO);
                    break;
                case PLANO:
                    replicarPlano(planoRedeEmpresaVO);
                    break;
                case PRODUTO:
                    replicarProduto(produtoRedeEmpresaVO);
                    break;
                case FORNECEDOR:
                    replicarFornecedor(fornecedorRedeEmpresaVO);
                    break;
                case PLANO_CONTA:
                    replicarPlanoConta(planoContaRedeEmpresaVO);
                    break;
            }
        } catch (Exception e) {
            Uteis.logar("nao foi possivel replicar " + replicarRedeEmpresa.name());
            e.printStackTrace();
        }

        return "carregado";
    }

    private void replicarUsuarios(UsuarioRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar usuario");
        long time1 = System.currentTimeMillis();
        UsuarioControle usuarioControle = JSFUtilities.getControlador(UsuarioControle.class);
        usuarioControle.replicarUsuarioRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou Usuario..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação usuario: " + (time2 - time1) + "ms");
    }

    private void replicarColaborador(ColaboradorRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar colaborador");
        long time1 = System.currentTimeMillis();
        ColaboradorControle colaboradorControle = JSFUtilities.getControlador(ColaboradorControle.class);
        colaboradorControle.replicarColaboradorRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou colaborador..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação colaborador: " + (time2 - time1) + "ms");
    }

    private void replicarPerfilAcesso(PerfilAcessoRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar perfil acesso");
        long time1 = System.currentTimeMillis();
        PerfilAcessoControle perfilAcessoControle = JSFUtilities.getControlador(PerfilAcessoControle.class);
        perfilAcessoControle.replicarPerfilAcessoRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou perfil acesso..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação perfil acesso: " + (time2 - time1) + "ms");
    }

    private void replicarMalaDireta(MalaDiretaRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar mala direta");
        long time1 = System.currentTimeMillis();
        MalaDiretaControle malaDiretaControle = JSFUtilities.getControlador(MalaDiretaControle.class);
        malaDiretaControle.replicarMalaDiretaRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou mala direta..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação mala direta: " + (time2 - time1) + "ms");
    }

    private void replicarPlano(PlanoRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar plano");
        long time1 = System.currentTimeMillis();
        PlanoControle planoControle = JSFUtilities.getControlador(PlanoControle.class);
        planoControle.replicarPlanoRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou plano..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação plano: " + (time2 - time1) + "ms");
    }

    private void replicarProduto(ProdutoRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar produto");
        long time1 = System.currentTimeMillis();
        ProdutoControle produtoControle = JSFUtilities.getControlador(ProdutoControle.class);
        produtoControle.replicarProdutoRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou produto..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação produto: " + (time2 - time1) + "ms");
    }

    private void replicarFornecedor(FornecedorRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar fornecedor");
        long time1 = System.currentTimeMillis();
        final FornecedorControle fornecedorControle = JSFUtilities.getControlador(FornecedorControle.class);
        fornecedorControle.replicarFornecedorRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou fornecedor..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação fornecedor: " + (time2 - time1) + "ms");
    }

    private void replicarPlanoConta(PlanoContaRedeEmpresaVO obj) {
        Uteis.logar("Comecei replicar plano de contas");
        long time1 = System.currentTimeMillis();
        final PlanoContasControle planoContasControle = JSFUtilities.getControlador(PlanoContasControle.class);
        planoContasControle.replicarPlanoContaRedeEmpresaUnica(obj);
        Uteis.logar(null, "Replicou plano de contas..");
        long time2 = System.currentTimeMillis();
        Uteis.logar("replicação plano de contas: " + (time2 - time1) + "ms");
    }

}
