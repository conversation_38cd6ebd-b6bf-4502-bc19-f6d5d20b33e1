package controle.arquitetura;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 01/09/2015.
 */
public enum FuncionalidadeSistemaEnum {

    CAIXA_EM_ABERTO("Caixa em Aberto", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.caixaEmAberto || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Caixa_em_Aberto", "como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"),
    NEGOCIACAO("Negociação", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.fecharNegociacaoContrato || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:<PERSON>ai<PERSON>_em_Aberto", "como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"),
    VENDA_AVULSA("Venda Avulsa", "semValor", "(LoginControle.permissaoAcessoMenuVO.vendaAvulsa || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Venda_Avulsa", "como-registrar-vendas-para-quem-nao-e-cliente/"),
    DIARIA("Di\u00E1ria", "semValor", "LoginControle.permissaoAcessoMenuVO.aulaAvulsaDiaria", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Diaria", "como-realizar-uma-venda-de-diaria/"),
    FREE_PASS("FreePass", "semValor", "LoginControle.permissaoAcessoMenuVO.permissaoFreePass",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Free_Pass", "como-lancar-free-pass/"),
    ORCAMENTO("Criar Orçamento", "semValor", "LoginControle.permissaoAcessoMenuVO.modeloOrcamento",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Orcamento", null),
    CONSULTA_DE_TURMAS("Consulta de Turma",
            "abrirPopup('[contexto]consultarTurmaForm.jsp', 'ConsultarTurma', 905, 660);",
            "LoginControle.permissaoAcessoMenuVO.consultaTurma",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Consulta_de_Turma", "o-que-e-consulta-de-turma/"),
    RELATORIO_FREQUENCIA_TURMAS("Relat\u00F3rio de Frequ\u00EAncia de Turmas",
            "abrirPopup('[contexto]consultarFrequenciaTurmaForm.jsp', 'ConsultarTurma', 905, 660);",
            "LoginControle.permissaoAcessoMenuVO.relatorioFrequenciaTurma",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Consulta_de_Turma", "o-que-e-consulta-de-turma/"),

    MAPA_TURMAS("Mapa de Turmas",
            "abrirPopup('[contexto]consultarTurmaForm.jsp?tipo=mapa', 'MapaTurma', 905, 660);",
            "LoginControle.permissaoAcessoMenuVO.mapaTurma",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Mapa_de_Turma", null),
    GESTAO_PERSONAL("Gest\u00E3o de Personal", "semValor", "LoginControle.permissaoAcessoMenuVO.gestaoPersonal",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Gestao_Personal", "como-lancar-taxa-mensal-para-personal-gestao-de-personal/"),
    RELATORIO_DE_PERSONAL("Relat\u00F3rio de Personal", "semValor",
            "LoginControle.permissaoAcessoMenuVO.relatorioPersonal",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Relatorio_Personal", null, "Personal"),
    GESTAO_DE_TRANSACOES("Gest\u00E3o de Transa\u00E7\u00F5es", "semValor",
            "LoginControle.permissaoAcessoMenuVO.gestaoTransacoes",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Operacional:Recorr%EAncia#Gest.C3.A3o_de_Transa.C3.A7.C3.B5es", null),
    GESTAO_DE_REMESSAS("Gest\u00E3o de Remessas", "gestaoRemessas",
            "LoginControle.permissaoAcessoMenuVO.gestaoRemessas",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Operacional:Remessas#GestaoRemessas", "como-acompanho-as-cobrancas-em-cartao-de-credito-enviadas-por-remessa-dos-meus-alunos-dcc/"),
    GESTAO_DE_COMISSAO("Comiss\u00E3o para Professor", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.gestaoComissao || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Comiss\u00E3o_para_Professor", "como-configurar-comissao-para-professor-no-sistema-pacto/"),
    COMISSAO_VARIAVEL("Comiss\u00E3o para Consultor",
            "abrirPopup('[contexto]relatorio/comissaoVariavel.jsp','ComissaoVariavel',1000,595);",
            "LoginControle.permissaoAcessoMenuVO.comissaoVariavel",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Comissao_para_Consultor", "como-gerar-o-relatorio-de-comissao-para-os-consultores/"),
    GESTAO_DE_ARMARIOS("Gest\u00E3o de Arm\u00E1rios", "gestaoArmario",
            "LoginControle.permissaoAcessoMenuVO.gestaoArmario",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Gestao_Armarios", "como-configurar-o-gestao-de-armarios/"),

    GESTAO_DE_BOLETOS_ONLINE("Gest\u00E3o de Boletos Online", "semValor",
            "LoginControle.permissaoAcessoMenuVO.gestaoBoletosOnline",
            ModuloAberto.ZILLYONWEB, "", ""),

    // Cadastros Auxiliares
    CADASTROS_AUXILIARES("Cadastros auxiliares", "indexBasico", "!LoginControle.menuZwUi",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Cadastros_Auxiliares", null),
    CADASTROS_PRODUTOS_TURMAS_PLANOS("Produtos, Planos e Turmas", "indexPlano", "true",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT", null),
    CADASTROS_CONFIG_FINANCEIRA("Config. Financeiras", "indexFinanceiro", "!LoginControle.menuZwUi",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras", null),
    CADASTROS_ACESSO_SISTEMA("Acesso ao sistema", "indexArquitetura", "!LoginControle.menuZwUi",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Acesso_ao_Sistema", null),
    CADASTROS_CONFIG_CONTRATO("Config. de contrato", "indexContrato", "!LoginControle.menuZwUi",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._de_Contrato", null),

    COLABORADOR("Colaborador (ADM)", "abrirPopup('[contexto]colaboradorCons.jsp','Colaborador',1090,595);",
            "LoginControle.permissaoAcessoMenuVO.colaborador",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Colaborador", "como-cadastrar-um-novo-colaborador/"),
    CLIENTE("Cliente", "abrirPopup('[contexto]clienteCons.jsp','Cliente',800,595);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeCliente",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Cadastros:Clientes", null, "Cliente"),
    CONFIGURACAO_NOTAFISCAL("Config. Nota Fiscal",
            "abrirPopup('[contexto]configuracaoNotaFiscalCons.jsp','Configura\u00E7\u00E3o Nota Fiscal',1000,650);",
            "LoginControle.utilizaEmissaoNotaFiscal",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:ConfigNotaFiscal", "interno-como-configurar-o-config-nota-fiscal-para-emitir-nota-fiscal-nfs-e/"),
    CLASSIFICACAO("Classifica\u00E7\u00E3o",
            "abrirPopup('[contexto]classificacaoCons.jsp','Classificacao',850,595);",
            "LoginControle.permissaoAcessoMenuVO.classificacao", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Classificacoes", null),
    QUESTIONARIO("Question\u00E1rio", "abrirPopup('[contexto]questionarioCons.jsp','Questionario',900,595);",
            "LoginControle.permissaoAcessoMenuVO.questionario",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Questionario", null),
    PESQUISA("Pesquisa de Satisfação / NPS", "abrirPopup('[contexto]questionarioCons.jsp','Questionario',850,595);",
            "LoginControle.permissaoAcessoMenuVO.pesquisa",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Pesquisa", null, "Pesquisa de Satisfação/NPS"),
    CATEGORIA_CLIENTES("Categoria de Clientes",
            "abrirPopup('[contexto]categoriaCons.jsp','Categoria de Clientes',850,595);",
            "LoginControle.permissaoAcessoMenuVO.categoria",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Categoria_de_Clientes", null),
    PROFISSAO("Profiss\u00E3o", "abrirPopup('[contexto]profissaoCons.jsp','Profissao',850,595);",
            "LoginControle.permissaoAcessoMenuVO.profissao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Profissao", null),
    GRAU_DE_INSTRUCAO("Grau de Instru\u00E7\u00E3o", "abrirPopup('[contexto]grauInstrucaoCons.jsp','',850,595);",
            "LoginControle.permissaoAcessoMenuVO.grauInstrucao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Grau_de_Instrucao", null),
    GRUPO_DESCONTO("Grupo com desconto", "abrirPopup('[contexto]grupoCons.jsp','Grupo com Desconto',850,595);",
            "LoginControle.permissaoAcessoMenuVO.grupo", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Grupo", null),
    PARENTESCO("Parentesco", "abrirPopup('[contexto]parentescoCons.jsp','Parentesco',850,595);",
            "LoginControle.permissaoAcessoMenuVO.parentesco",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Parentesco", null),
    PAIS("Pa\u00EDs", "abrirPopup('[contexto]paisCons.jsp','Pais',850,595);",
            "LoginControle.permissaoAcessoMenuVO.pais", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Pais", null),
    CIDADE("Cidade", "abrirPopup('[contexto]cidadeCons.jsp','Cidade',850,595);",
            "LoginControle.permissaoAcessoMenuVO.cidade", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Cidade", null),
    PERGUNTA("Pergunta", "abrirPopup('[contexto]perguntaCons.jsp','Pergunta',850,595);",
            "LoginControle.permissaoAcessoMenuVO.pergunta", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Pergunta", null),
    CATEGORIA_PRODUTO("Categoria de Produtos",
            "abrirPopup('[contexto]categoriaProdutoCons.jsp','CategoriaProduto',850,595);",
            "LoginControle.permissaoAcessoMenuVO.categoriaProduto",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Categoria_de_Produto", null),
    IMPOSTO("Imposto", "/adm/config-financeiras/imposto-produto",
            "LoginControle.permissaoAcessoMenuVO.impostoProduto", ModuloAberto.ZILLYONWEB,
            "", "como-configurar-o-imposto-padrao-para-os-produtos"),
    PRODUTO("Produto", "abrirPopup('[contexto]produtoCons.jsp','Produto',850,595);",
            "LoginControle.permissaoAcessoMenuVO.produto", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:PPT:Produto", "configuracao-de-produto-para-emissao-de-nfc-e-nf-e/"),
    BRINDE("Brindes", "abrirPopup('[contexto]brindeCons.jsp','Brinde',850,650);",
            "LoginControle.empresaLogado.trabalharComPontuacao && LoginControle.permissaoAcessoMenuVO.brinde", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Pontos:Brinde", null),
    LANCAMENTO_BRINDE("Lançamento de Brinde", "abrirPopup('[contexto]lancarBrindeClienteCons.jsp','Brinde',850,650);",
            "LoginControle.empresaLogado.trabalharComPontuacao && LoginControle.permissaoAcessoMenuVO.brinde", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Pontos:Brinde", null),
    LANCAMENTO_PRODUTO_COLETIVO("Lan\u00E7amento de produto coletivo",
            "abrirPopup('[contexto]lancamentoProdutoColetivoCons.jsp','LancamentoProdutoColetivo',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.lancamentoProdutoColetivo",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Produtos_Coletivos", null),
    CONTROLE_ESTOQUE("Controle de estoque", "controleEstoque", "!LoginControle.menuZwUi",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Controle_de_Estoque", null),
    TAMANHO_ARMARIO("Tamanho de Arm\u00E1rio",
            "abrirPopup('[contexto]tamanhoArmarioCons.jsp','TamanhoArmario',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.tamanhoArmario",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Tamanho_Armario", null),
    PLANO("Plano", "abrirPopup('[contexto]planoCons.jsp','Plano',850,595);",
            "LoginControle.permissaoAcessoMenuVO.plano", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:PPT:Plano", null),
    MODALIDADE("Modalidade", "abrirPopup('[contexto]modalidadeCons.jsp','Modalidade',850,595);",
            "LoginControle.permissaoAcessoMenuVO.modalidade",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Modalidade", null),
    DESCONTO("Desconto", "abrirPopup('[contexto]descontoCons.jsp','Desconto',850,595);",
            "LoginControle.permissaoAcessoMenuVO.desconto", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:PPT:Desconto", null),
    PACOTE("Pacote", "abrirPopup('[contexto]composicaoCons.jsp','Composicao',850,595);",
            "LoginControle.permissaoAcessoMenuVO.composicao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Pacote", null),
    CONDICAO_DE_PAGAMENTO("Condi\u00E7\u00E3o de Pagamento",
            "abrirPopup('[contexto]condicaoPagamentoCons.jsp','CondicaoPagamento',850,760);",
            "LoginControle.permissaoAcessoMenuVO.condicaoPagamento",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Condicao_de_Pagamento", null),
    CONVENIO_DE_DESCONTO("Conv\u00EAnio de Desconto",
            "abrirPopup('[contexto]convenioDescontoCons.jsp','Convenio',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.convenioDesconto",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._de_Contrato:Convênio_de_Desconto", null),
    TIPO_PLANO("Tipo de Plano",
            "abrirPopup('[contexto]planoTipoCons.jsp','Tipo Plano',850,760);",
            "LoginControle.permissaoAcessoMenuVO.planoTipo",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:TipoPlano", null),
    AMBIENTE("Ambiente", "abrirPopup('[contexto]ambienteCons.jsp','Ambiente',850,595);",
            "LoginControle.permissaoAcessoMenuVO.ambiente",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Ambiente", "conhecimento/como-cadastrar-ambiente/"),
    NIVEL_TURMA("N\u00EDvel de Turma", "abrirPopup('[contexto]nivelTurmaCons.jsp','NivelTurma',850,595);",
            "(LoginControle.permissaoAcessoMenuVO.nivelTurma)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:PPT:Nivel", null),
    TURMA("Turma", "/agenda/turma",
            "(LoginControle.permissaoAcessoMenuVO.turma)", ModuloAberto.AGENDA,
            "ZillyonWeb:Cadastros:PPT:Turma", null),
    HORARIO("Hor\u00E1rio", "abrirPopup('[contexto]horarioCons.jsp','Horario',850,595);",
            "LoginControle.permissaoAcessoMenuVO.horario", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:PPT:Horario", null),
    BALANCO("Balanço", "abrirPopup('[contexto]balancoCons.jsp','Balanco',1000,650);", "LoginControle.permissaoAcessoMenuVO.cadastrarBalanco",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Controle_de_Estoque:Balan\u00E7o", null),
    CARDEX("Cardex", "abrirPopup('[contexto]cardexCons.jsp','Cardex',1000,650);", "LoginControle.permissaoAcessoMenuVO.visualizarCardex",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Controle_de_Estoque:Cardex", "como-acompanhar-as-movimentacoes-do-meu-estoque/"),
    COMPRA("Compra", "abrirPopup('[contexto]compraCons.jsp','Compra',1000,650);", "LoginControle.permissaoAcessoMenuVO.cadastrarCompra",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Controle_de_Estoque:Compra_Estoque", "como-cadastrar-as-compras-de-produtos-de-estoque/"),
    CONFIGURAR_PRODUTO_ESTOQUE("Configurar Produto Estoque",
            "abrirPopup('[contexto]produtoEstoqueCons.jsp','ProdutoEstoque',1000,650);", "LoginControle.permissaoAcessoMenuVO.configurarProdutoEstoque",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Controle_de_Estoque:Configurar_Produto_Estoque", null),
    POSICAO_DO_ESTOQUE("Posi\u00E7\u00E3o do Estoque",
            "abrirPopup('[contexto]relatorioEstoqueProduto.jsp','PosicaoEstoque',1000,650);", "LoginControle.permissaoAcessoMenuVO.visualizarPosicaoEstoque",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Controle_de_Estoque:Posicao_Estoque", null),
    CONVITE_AULA_EXPERIMENTAL("Convite Aula Experimental",
            "abrirPopup('[contexto]tipoConviteAulaExperimentalCons.jsp','Convite',1000,800);",
            "LoginControle.permissaoAcessoMenuVO.conviteAulaExperimental",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Free_Pass", "como-lancar-free-pass/"),
    CAMPANHA_CUPOM_DESCONTO("Campanha Cupom Desconto",
            "abrirPopup('[contexto]campanhaCupomDescontoCons.jsp','Campanha Cupom Desconto',1100,900);",
            "LoginControle.permissaoAcessoMenuVO.campanhaCupomDesconto",
            ModuloAberto.ZILLYONWEB, "", null),
    INDICE_FINANCEIRO_REAJUSTE_PRECO("\u00CDndice Financeiro Reajuste Pre\u00E7os",
            "abrirPopup('[contexto]indiceFinanceiroReajustePrecoCons.jsp','indiceFinanceiroReajustePreco',1000,595);",
            "LoginControle.permissaoAcessoMenuVO.indiceFinanceiroReajustePreco",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Indice_Financeiro", null, "Ajustar Índices Financeiros"),
    DEPARTAMENTO("Departamento", "abrirPopup('[contexto]departamentoCons.jsp','Departamento',1000,650);", "true",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Departamento", null),
    DADOS_USUSARIO("Configura\u00E7\u00F5es Usu\u00E1rio",
            "abrirPopup('[contexto]alterarDadosCadastraisUsuario.jsp','Dados Cadastrais',820,600);",
            "LoginControle.usuario.permiteAlterarPropriaSenha", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Meus_Dados", "como-o-proprio-usuario-faz-para-alterar-os-dados-do-seu-perfil/"),
    // Inicio Config. Financeiras
    CONSULTA_DE_RECIBOS("Consulta de Recibos",
            "abrirPopup('[contexto]estornoReciboCons.jsp','EstornoRecibo',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeConsultaRecibo",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Consulta_de_Recibo", "como-estornar-o-recibo-de-pagamento-de-um-colaborador/"),
    CONSULTA_DE_CUPONS_FISCAIS("Cupons Fiscais",
            "abrirPopup('[contexto]cuponsCons.jsp','ConsultaCupom',1100,650);",
            "LoginControle.permissaoAcessoMenuVO.cupomFiscal",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Config._Financeiras:Consulta_de_Cupons_Fiscais#Consulta_do_Cupom_Fiscal", null),
    GESTAO_NOTAS("Gest\u00E3o de Notas", "abrirPopup('[contexto]indexGestaoNotas.jsp','GestaodeNotas',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.gestaoNotas",
            ModuloAberto.NOTAS, "ZillyonWeb:Cadastros:Config._Financeiras:Gestao_de_Notas", "como-emitir-nota-fiscal-pelo-gestao-de-notas-ou-pelo-gestao-de-nfc-e/"),
    NOTAS_FISCAIS("Notas Fiscais", "notaFiscal",
            "LoginControle.permissaoAcessoMenuVO.gestaoNotas",
            ModuloAberto.NOTAS, "ZillyonWeb:Cadastros:Config._Financeiras:Gestao_de_Notas", "como-emitir-nota-fiscal-pelo-gestao-de-notas-ou-pelo-gestao-de-nfc-e/"),
    GESTAO_NFCE("Gest\u00E3o de NFC-e", "abrirPopup('[contexto]indexGestaoNFCe.jsp','GestaoNFCe',1000,650);",
            "(LoginControle.permissaoAcessoMenuVO.gestaoNFCe && LoginControle.empresaLogado.usarNFCe)",
            ModuloAberto.NOTAS,
            "ZillyonWeb:Cadastros:Config._Financeiras:Gestao_de_NFCe", "como-emitir-nota-fiscal-pelo-gestao-de-notas-ou-pelo-gestao-de-nfc-e/"),
    VENDA_CONSUMIDOR("Venda de Consumidor",
            "abrirPopup('[contexto]vendaConsumidorCons.jsp','VendaConsumidor',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.vendaConsumidor",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Venda_de_Consumidor", null),
    MOVIMENTO_CC_CLIENTE("Mov. de CC do Cliente",
            "abrirPopup('[contexto]movimentoContaCorrenteClienteCons.jsp','MovimentoContaCorrenteCliente',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeMovimentacaoContaCorrenteCliente",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Config._Financeiras:Movimento_de_Conta_Corrente_do_Cliente", "como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"),
    BANCO("Banco", "abrirPopup('[contexto]bancoCons.jsp','Banco',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.banco", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Config._Financeiras:Banco", null),
    CONTA_CORRENTE("Conta Corrente", "abrirPopup('[contexto]contaCorrenteCons.jsp','ContaCorrente',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.contaCorrente",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Conta_Corrente", null),

    TRANSFERENCIA_SALDO_CONTA_CORRENTE("Transferência Saldo Conta Corrente",
            "abrirPopup('[contexto]transferenciaContaClienteForm.jsp','ContaCorrente',1000,650);",
            "false",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Conta_Corrente", null),

    RECEBER_DEBITO_CONTA_CORRENTE("Transferência Saldo Conta Corrente",
            "abrirPopup('[contexto]gerarProdutoContaCorrenteForm.jsp','ContaCorrente',1000,650);",
            "false",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Conta_Corrente", null),



    TIPO_REMESSA("Tipo de Remessa", "abrirPopup('[contexto]tipoRemessaCons.jsp','TipoRemessa',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.tipoRemessa",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Config._Financeiras:Tipo_Remessa", null),
    TIPO_RETORNO("Tipo de Retorno", "abrirPopup('[contexto]tipoRetornoCons.jsp','TipoRetorno',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.tipoRetorno",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Tipo_Retorno", null),
    CONVENIO_COBRANCA("Conv\u00EAnio de Cobran\u00E7a",
            "abrirPopup('[contexto]convenioCobrancaCons.jsp','ConvenioCobranca',1000,750);",
            "LoginControle.permissaoAcessoMenuVO.convenioCobranca",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Conv\u00EAnio_de_Cobran\u00E7a", null),
    PINPAD("Pinpad",
            "abrirPopup('[contexto]pinPadCons.jsp','Pinpad',1000,750);",
            "LoginControle.permissaoAcessoMenuVO.formaPagamento",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:PinPad", null),
    FORMA_PAGAMENTO("Formas de Pagamento",
            "abrirPopup('[contexto]formaPagamentoCons.jsp','FormaPagamento',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.formaPagamento",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Formas_de_Pagamento", "como-cadastrar-uma-forma-de-pagamento/"),
    OPERADORA_CARTAO("Operadora de Cart\u00E3o",
            "abrirPopup('[contexto]operadoraCartaoCons.jsp','OperadoraCartao',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.operadoraCartao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Operadora_de_Cart\u00E3o", null),
    METAS_FINANCEIRO_VENDA("Metas Financeiras de Vendas", "abrirPopup('[contexto]metasFinanceiroCons.jsp','MetasFinanceiro',850,670);", "LoginControle.permissaoAcessoMenuVO.cadastroMetas", ModuloAberto.ZILLYONWEB,
            "FinanceiroWeb:Inicial:Metas_do_Financeiro", "como-cadastrar-editar-metas-financeiras/"),
    TAXA_COMISSAO("Taxas de Comiss\u00E3o",
            "abrirPopup('[contexto]comissaoGeralConfiguracaoCons.jsp','ComissaoGeralConfiguracao', 1120, 650);",
            "LoginControle.permissaoAcessoMenuVO.taxasComissao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Taxas_Comissao", null),
    EMPRESA("Empresa", "abrirPopup('[contexto]empresaCons.jsp','Empresa',800,595);",
            "LoginControle.permissaoAcessoMenuVO.empresa",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Acesso_ao_sistema:Empresa", "configurando-os-dados-da-empresa-para-emissao-de-notas-fiscais/"),
    PERFIL_ACESSO("Perfil de Acesso (ADM)", "abrirPopup('[contexto]perfilAcessoCons.jsp','PerfilAcesso',850,595);",
            "LoginControle.permissaoAcessoMenuVO.perfilAcesso",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Acesso_ao_sistema:Perfil_Acesso", "quais-sao-as-permissoes-do-perfil-de-acesso-do-modulo-adm/"),
    USUARIO("Usu\u00E1rio (ADM)", "abrirPopup('[contexto]usuarioCons.jsp','Usuario',1070,600);",
            "LoginControle.permissaoAcessoMenuVO.usuario", ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Acesso_ao_Sistema:Usuario", "como-cadastro-um-novo-usuario-no-sistema/"),
    LOCAL_ACESSO("Local de Acesso", "abrirPopup('[contexto]localAcessoCons.jsp','LocalAcesso',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.localAcesso",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Acesso_ao_sistema:Local_de_Acesso", null),
    SERVIDOR_FACIAL("Servidor Facial", "abrirPopup('[contexto]servidorFacialCons.jsp','ServidorFacial',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.servidorFacial",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Reconhecimento_Facial", null),
    IMPORTACAO("Importa\u00E7\u00E3o", "abrirPopup('[contexto]importacao.jsp','Importacao',1000,800);",
            "(LoginControle.permissaoAcessoMenuVO.importacao || LoginControle.permissaoAcessoMenuVO.importacaoProduto || LoginControle.permissaoAcessoMenuVO.importacaoColaborador || LoginControle.permissaoAcessoMenuVO.importacaoFornecedor || LoginControle.permissaoAcessoMenuVO.importacaoConta)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Importacao", null),
    CONTROLE_LOG("Controle de Log", "abrirPopup('[contexto]controleLogCons.jsp','ControleLog',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.log",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Acesso_ao_sistema:Controle_de_Log", "como-utilizar-o-controle-de-log/"),
    AUTORIZACAO_ACESSO("Autoriza\u00E7\u00E3o de Acesso",
            "abrirPopup('[contexto]autorizacaoAcessoGrupoCons.jsp','Autorizacao',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.incluirAutorizacaoAGE",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Acesso_ao_sistema:Autoriza\u00E7\u00E3o_Acesso", "como-realizar-a-autorizacao-acesso-permitindo-que-os-alunos-de-uma-unidade-acesse-outra/"),

    ATUALIZACOES_BD("Atualiza\u00E7\u00F5es do  BD",
            "abrirPopup('[contexto]atualizacoesBD.jsp','Atualiza\u00E7\u00F5es',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.log", ModuloAberto.ZILLYONWEB, "", null),
    INTEGRACAO_ACESSO("Integra\u00E7\u00E3o de Acesso",
            "abrirPopup('[contexto]integracaoAcessoCons.jsp','Integracao',1000,650);", "LoginControle.permissaoAcessoMenuVO.localAcesso",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Acesso_ao_sistema:Integra\u00E7\u00E3o_Acesso", null),
    GERADOR_CONSULTAS("Gerador de Consultas", "link",
            "LoginControle.permissaoAcessoMenuVO.geradorConsultas",
            ModuloAberto.ZILLYONWEB, "", null),
    SORTEIO("Sorteio", "semValor", "LoginControle.permissaoAcessoMenuVO.sorteio",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Sorteio", null),
    OPERACOES_COLETIVAS("Operações Coletivas", "semValor", "LoginControle.permissaoAcessoMenuVO.operacoesColetivas",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Operacoes_Coletivas", "para-que-serve-o-lancamento-de-produto-coletivo/"),
    // CONFIG. CONTRATO
    MODELO_CONTRATO("Modelo de Contrato",
            "abrirPopup('[contexto]planoTextoPadraoCons.jsp','PlanoTextoPadrao',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.planoTextoPadrao",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Config._de_Contrato:Modelo_de_Contrato_e_Recibo", null),
    MOVIMENTO_PRODUTO("Movimento do Produto", "abrirPopup('[contexto]movProdutoCons.jsp','MovProduto',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.movProduto",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._de_Contrato:Movimento_do_Produto", "relatorio-movimento-do-produto/"),
    JUSTIFICATIVA_OPERACAO("Justificativa de Opera\u00E7\u00E3o",
            "abrirPopup('[contexto]justificativaOperacaoCons.jsp','JustificativaOperacao',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.justificativaOperacao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._de_Contrato:Justificativa_de_Opera\u00E7\u00E3o", null),
    IMPRIME_RECIBO_BANCO("Imprime Recibo em Branco", "semValor", "LoginControle.permissaoAcessoMenuVO.permitirImprimirReciboEmBranco",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Imprimir_Recibo_em_Branco", null),

    // RELATORIO BI
    GERAL_CLIENTES("Geral de Clientes", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.geralClientes)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Relatorios_Geral_Clientes", null),
    RELATORIO_VISITANTES("Visitantes", "relatorioVisitantes",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeVisitantes",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_Visitantes", null, "Visitantes"),
    RELATORIO_CLIENTES_CANCELADOS("Clientes Cancelados", "relatorioAlunoCancelados",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeClientesCancelados",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_Clientes_Cancelados", "relatorio-de-cancelamentos/", "Clientes Cancelados"),
    RELATORIO_CLIENTES_TRANCADOS("Clientes Trancados", "relatorioAlunoTrancados",
            "LoginControle.permissaoAcessoMenuVO.relatorioClientesTrancados",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_Clientes_Trancados", "onde-consigo-gerar-um-relatorio-que-apresente-o-total-de-clientes-que-estao-trancados/", "Clientes Trancados"),
    RELATORIO_BONUS("Clientes com B\u00F4nus", "relatorioAlunosBonus",
            "LoginControle.permissaoAcessoMenuVO.relatorioClientesComBonus",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_Clientes_Bonus", "como-emitir-um-relatorio-de-todos-os-clientes-com-bonus/", "Clientes com B\u00F4nus"),
    RELATORIO_ATESTADO("Clientes com Atestado", "relatorioAlunosAtestado",
            "LoginControle.permissaoAcessoMenuVO.relatorioClientesComAtestado",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_Clientes_Atestados", "como-ter-uma-relacao-dos-alunos-que-possuem-atestado-medico-lancado/", "Clientes com Atestado"),
    CONTRATOS_DURACAO("Contratos por Dura\u00E7\u00E3o",
            "abrirPopup('[contexto]relatorio/clientePorDuracaoRel.jsp','ClientePorDuracao',850,595);",
            "(LoginControle.permissaoAcessoMenuVO.clientePorDuracaoRel)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Contratos_por_Duracao", null),

    ANIVERSARIANTES("Aniversariantes",
            "abrirPopup('[contexto]relatorio/clientePorAniversarioRel.jsp','aniversariantes',820,595);",
            "LoginControle.permissaoAcessoMenuVO.aniversariantes",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Aniversariantes", "como-gerar-uma-lista-de-aniversariantes/"),

    RELATORIO_PONTUACAO("Relatorio de Pontua\u00E7\u00E3o",
            "abrirPopup('[contexto]relatorio/historicoPontosResumo.jsp','pontuacao',820,595);",
            "LoginControle.empresaLogado.trabalharComPontuacao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Pontuacao", "como-gerar-uma-lista-de-pontuacao/"),

    LISTA_ACESSOS("Lista de Acessos", "abrirPopup('[contexto]relatorio/listaAcessoRel.jsp','listaAcesso',850,595);",
            "LoginControle.permissaoAcessoMenuVO.totalizadorFrequenciaRel",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_de_Acessos", null),
    INDICADOR_ACESSOS("Indicador de Acesso",
            "abrirPopup('[contexto]relatorio/indicadorAcessoRel.jsp','indicadorAcesso',1050,595);",
            "LoginControle.permissaoAcessoMenuVO.relatorioIndicadorDeAcessos",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_de_Acessos#Lista_de_Acessos", null),
    TOTALIZADOR_ACESSOS("Totalizador de Acessos",
            "abrirPopup('[contexto]relatorio/totalizadorFrequenciaRel.jsp','totalizadorFrequencia',850,595);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeTotalizadorDeAcessos",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_de_Acessos#Totalizador_de_Acessos", null),
    TOTALIZADOR_TICKETS("Totalizador de Tickets",
            "abrirPopup('[contexto]relatorio/totalizadorTicketsRel.jsp', 'totalizadorTickets', 850, 595);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeTotalizadorDeTickets",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relatorios_de_Acessos#Totalizador_de_Tickets", "relatorio-totalizador-de-tickets/"),
    FECHAMENTO_ACESSOS("Fechamento Acessos",
            "abrirPopupMaximizada('[contexto]relatorio/fechamentoAcessosRel.jsp','fechamentoAcesso');",
            "LoginControle.permissaoAcessoMenuVO.relatorioFechamentoDeAcessos",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Relatorios_de_Acessos#Fechamento_de_Controle_de_Acessos", "relatorio-fechamento-de-acessos/"),
    LISTA_CLIENTES_SIMPLIFICADA("Lista de Clientes Simplificada", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.clienteRel)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Lista_Clientes_Simplificada", null, "Clientes Simplificada"),
    SALDO_CREDITO("Saldo de Cr\u00E9ditos",
            "abrirPopup('[contexto]relatorio/saldoCreditoRel.jsp','SaldoCredito',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.saldoCreditosClientes)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Saldo_De_Creditos", null),
    RELATORIO_CLIENTES_ORCAMENTOS("Relatorio de Or\u00E7amentos",
            "abrirPopup('[contexto]orcamentoCons.jsp','OrcamentoCliente',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.modeloOrcamento)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Saldo_De_Creditos", null, "Or\u00E7amentos"),
    LISTA_CHAMADA("Lista de Chamada",
            "abrirPopup('[contexto]relatorio/matriculaAlunoHorarioTurmaRel.jsp','MatriculaAlunoHorarioTurma',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.listaChamada)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Lista_de_Chamada", null),
    FREQUENCIA_OCUPACAO_TURMAS("Frequ\u00EAncia e Ocupa\u00E7\u00E3o de Turmas",
            "abrirPopup('[contexto]relatorio/frequenciaOcupacaoTurmas.jsp','FrequenciaOcupacaoTurmas',1000,760);",
            "LoginControle.permissaoAcessoMenuVO.frequenciaOcupacaoTurmasRel",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:prt_Frequencia_Ocupacao_Turma",
            null, "Frequ\u00EAncia e Ocupa\u00E7\u00E3o"),
    FECHAMENTO_CAIXA_OPERADOR("Fechamento de Caixa por Operador",
            "abrirPopup('[contexto]relatorio/caixaPorOperador.jsp','CaixaPorOperador',780,595);",
            "LoginControle.permissaoAcessoMenuVO.caixaPorOperadorRel",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Fechamento_de_Caixa_por_Operador", "como-verificar-o-relatorio-de-fechamento-de-caixa-por-operador/"),
    COMPETENCIA_MENSAL("Compet\u00EAncia Mensal",
            "abrirPopup('[contexto]relatorio/competenciaSinteticoRel.jsp','CompetenciaSintetico',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.competenciaMensalRel )",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Competencia_Mensal", "como-gerar-um-relatorio-competencia-mensal/"),
    FATURAMENTO_PERIODO("Relatório de Faturamento",
            "abrirPopup('[contexto]relatorio/faturamentoSinteticoRel.jsp','FaturamentoSintetico',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.faturamentoSinteticoRel)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Faturamento_por_Per%EDodo",
            null, "Faturamento"),
    FATURAMENTO_RECEBIDO_PERIODO("Relatório de Faturamento Recebido",
            "abrirPopup('[contexto]relatorio/faturamentoSinteticoRel.jsp','FaturamentoSintetico',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.relatorioDeFaturamentoRecebidoPeríodo)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Faturamento_Recebido_por_Per%EDodo", "onde-vejo-o-faturamento-recebido-por-periodo-da-minha-empresa/", "Faturamento Recebido"),
    RECEITA_PERIODO("Receita por Per\u00EDodo",
            "abrirPopup('[contexto]relatorio/receitaPorPeriodoSinteticoRel.jsp','ReceitaPorPeriodoSintetico',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.receitaPorPeriodoSinteticoRel)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Receita_por_Periodo", "como-verificar-a-receita-por-periodo-da-empresa/"),
    RELATORIO_PARCELAS("Relatório de Parcelas", "abrirPopupMaximizada('[contexto]relatorio/parcelaEmAbertoRel20.jsp','ParcelaEmAberto');",
            "(LoginControle.permissaoAcessoMenuVO.parcelaEmAbertoRel)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Parcelas", "relatorio-de-parcelas/", "Parcelas"),
    GESTAO_NEGATIVACOES("Gestão de Negativações",
            "abrirPopup('[contexto]relatorio/parcelaSPC.jsp','GestaoNegativacoes',1024,700);",
            "(LoginControle.permissaoAcessoMenuVO.gestaoNegativacoes)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Parcelas",
            "relatorio-de-parcelas/", "Gestão de Negativações"),
    RELATORIO_PRODUTOS("Relat\u00F3rio de Produtos",
            "abrirPopup('[contexto]relatorio/produtosRel.jsp','RelatorioProdutos',1050,585);",
            "(LoginControle.permissaoAcessoMenuVO.relatorioDeProdutosComVigencia)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Produtos", null, "Produtos (com vigência)"),
    SALDO_CONTA_CORRENTE("Saldo Conta Corrente",
            "abrirPopup('[contexto]relatorio/saldoContaCorrenteRel.jsp','ParcelaEmAberto',780,585);",
            "(LoginControle.permissaoAcessoMenuVO.saldoContaCorrenteRel)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Saldo_de_Conta_Corrente", "como-tirar-um-relatorio-dos-alunos-que-possuem-saldo-na-conta-corrente/"),
    TRANSACOES_PIX("Transações Pix",
            "abrirPopupMaximizada('[contexto]pix/relatorioTransacoesPix.jsp','TransacoesPix',980,700);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeTransacoesPix",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:TransacoesPix", "relatorio-transacoes-pix/"),

    PEDIDOS_PINPAD("Pedidos Pinpad",
            "abrirPopupMaximizada('[contexto]pinpadpedidos.jsp','PedidosPinpad',980,700);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDePedidosPinpad",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:PedidosPADM_VENDA_AVULSA_ZW_UIinpad", "integracao-com-stone-connect/"),
    PREVISAO_RENOVACAO_CONTRATO("Previs\u00E3o de Renova\u00E7\u00E3o por Contrato",
            "abrirPopup('[contexto]renovacaoAnaliticoForm.jsp','Renova\u00E7\u00E3o',980,700);",
            "(LoginControle.permissaoAcessoMenuVO.relatorioDePrevisaoRenovacao)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Previsao_de_Renovacao",
            "como-tirar-um-relatorio-dos-contratos-a-vencer/", "Previs\u00E3o de Renova\u00E7\u00E3o"),
    RELATORIO_CLIENTES("Relat\u00F3rio de Clientes",
            "abrirPopup('[contexto]relatorioClientesForm.jsp','Relatório de Clientes',980,595);",
            "(LoginControle.permissaoAcessoMenuVO.relatorioClientes)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relat%F3rio_Clientes", "como-utilizar-os-filtros-do-relatorio-de-clientes/", "Clientes"),
    RELATORIO_BVS("Relat\u00F3rio de BVs",
            "abrirPopup('[contexto]relatorioBVsForm.jsp','Relat\u00F3rio de BVs',1135,683);",
            "LoginControle.permissaoAcessoMenuVO.relatorioBVs",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relat%F3rio_BVs", "como-tirar-um-relatorio-das-respostas-dos-bvs-boletins-de-visitas-questionarios-respondidos-pelos-clientes/", "BVs"),
    RELATORIO_PESQUISA("Relat\u00F3rio de Pesquisas",
            "abrirPopup('[contexto]relatorioBVsForm.jsp','Relat\u00F3rio de Pesquisas',1135,683);",
            "LoginControle.permissaoAcessoMenuVO.pesquisa",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relat%F3rio_Pesquisas", null, "Pesquisas"),
    RELATORIO_REPASSE("Relat\u00F3rio de Repasse",
            "abrirPopup('[contexto]relatorioRepasse.jsp','Relat\u00F3rio de Repasse',1135,683);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDeRepasse",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:Relat%F3rio_Repasse", "relatorio-de-repasse/", "Repasse"),
    RELATORIO_GERAL_ARMARIOS("Relat\u00F3rio de Arm\u00E1rios",
            "abrirPopup('[contexto]relatorio/relatorioArmarioRel.jsp','ArmarioRel',850,595);", "LoginControle.permissaoAcessoMenuVO.relatorioDeArmarios",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Relatorio_Armario", null, "Arm\u00E1rios"),
    HISTORICO_PONTOS_ALUNO("Hist\u00F3rico de Pontos",
            "abrirPopup('[contexto]relatorio/historicoPontosRel.jsp','HistoricoPontos',850,595);",
            "(LoginControle.empresaLogado.trabalharComPontuacao && LoginControle.permissaoAcessoMenuVO.historicoPontos)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Pontos:Historico_de_Pontos", null),
    DESCONTO_OCUPACAO_TURMAS("Desconto por Ocupa\u00E7\u00E3o na Turma",
            "abrirPopup('[contexto]relatorio/descontoOcupacaoTurmas.jsp','DescontoOcupacaoTurmas',1000,760);",
            "LoginControle.permissaoAcessoMenuVO.relatorioDescontoPorOcupacao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:prt_Desconto_Ocupacao_Turma",
            "relatorio-de-desconto-por-ocupacao-na-turma/", "Desconto por Ocupa\u00E7\u00E3o"),
    // INICIO CRM
    CARTEIRAS("Gest\u00E3o de Carteiras", "semValor", "LoginControle.permissaoAcessoMenuVO.organizadorCarteira",
            ModuloAberto.CRMWEB,
            "CRMWeb:Cadastros:Carteiras", "o-que-e-a-gestao-de-carteiras/"),
    CRM_REGISTRO_PARALISACAO("Registro de Paralisação", "Richfaces.showModalPanel('modalIniciaQuarentenaCRM')",
            "LoginControle.permissaoAcessoMenuVO.crmQuarentena",
            ModuloAberto.CRMWEB, "CRMWeb:Opera\u00E7\u00F5es:Meta_Extra", "bi-meta-extra-crm/"),
    CRM_META_EXTA("Meta extra", "abrirPopup('crmExtraCons.jsp', 'Meta Extra', 1000, 650);",
            "LoginControle.permissaoAcessoMenuVO.crmExtraCRM",
            ModuloAberto.CRMWEB, "CRMWeb:Opera\u00E7\u00F5es:Meta_Extra", "bi-meta-extra-crm/"),
    CRM_META_DIARIA("Meta Diária", "semValor",
            "LoginControle.permissaoAcessoMenuVO.visualizarMeta",
            ModuloAberto.CRMWEB, "CRMWeb:Opera\u00E7\u00F5es:Meta_Diaria", null),
    GRUPO_COLABORADOR("Grupo Colaborador",
            "abrirPopup('[contexto]grupoColaboradorCons.jsp','GrupoColaborador',780,595);",
            "LoginControle.permissaoAcessoMenuVO.grupoColaborador",
            ModuloAberto.CRMWEB, "CRMWeb:Cadastros:Grupo_Colaborador", "como-cadastrar-grupo-colaborador-no-crm/"),
    EVENTO("Evento", "abrirPopup('[contexto]eventoCons.jsp','Evento',780,595);",
            "LoginControle.permissaoAcessoMenuVO.evento", ModuloAberto.CRMWEB,
            "CRMWeb:Cadastros:Evento", "como-criar-um-novo-evento/"),
    OBJECAO("Obje\u00E7\u00E3o", "abrirPopup('[contexto]objecaoCons.jsp','Objecao',780,595);",
            "LoginControle.permissaoAcessoMenuVO.objecao", ModuloAberto.CRMWEB,
            "CRMWeb:Cadastros:Objecao", null),
    MODELO_MENSAGEM("Modelo de Mensagem",
            "semValor",
            "LoginControle.permissaoAcessoMenuVO.modeloMensagem", ModuloAberto.CRMWEB,
            "CRMWeb:Cadastros:Modelo_de_Mensagem", null),
    TEXTO_PADRAO("Script", "abrirPopup('[contexto]textoPadraoCons.jsp','Script',780,595);",
            "LoginControle.permissaoAcessoMenuVO.script",
            ModuloAberto.CRMWEB, "", null),
    FERIADO("Feriado", "abrirPopup('[contexto]feriadoCons.jsp','Feriado',780,595);",
            "LoginControle.permissaoAcessoMenuVO.feriado", ModuloAberto.CRMWEB,
            "CRMWeb:Cadastros:Feriado", "como-cadastrar-editar-excluir-feriados/"),
    MARCAR_COMPARECIMENTO("Marcar Comparecimento",
            "abrirPopup('[contexto]confirmarComparecimentoAgendadoForm.jsp','ConfirmarAgendados',780,595);",
            "LoginControle.permissaoAcessoMenuVO.agenda", ModuloAberto.CRMWEB,
            "CRMWeb:Relatorios:Fechamento_de_Caixa_por_Operador", "como-verificar-o-relatorio-de-fechamento-de-caixa-por-operador/"),
    MAILING("Contato em Grupo", "semValor",
            "LoginControle.permissaoAcessoMenuVO.malaDireta",
            ModuloAberto.CRMWEB, "CRMWeb:Recursos_Extras:Mailing", "como-confirmar-o-comparecimento-de-um-cliente-agendado-pelo-crm/"),
    CONSULTA_HISTORICO_CONTATO("Consulta Hist\u00F3rico Contato",
            "abrirPopup('[contexto]historicoContatoCons.jsp','historicoContatoCons',765,595);",
            "LoginControle.permissaoAcessoMenuVO.historicoContato",
            ModuloAberto.CRMWEB, "CRMWeb:Cadastros:Historico_Contato", "relatorio-historico-de-contatos-crm/", "Hist\u00F3rico de Contato"),
    TOTALIZADOR_METAS("Totalizador de Metas",
            "abrirPopupTopoPagina('totalizadorMeta.jsp','TotalizadorMeta',816,595);",
            "LoginControle.permissaoAcessoMenuVO.totalizadorMeta", ModuloAberto.CRMWEB,
            "", null),
    PASSIVO("Receptivo", "abrirPopup('[contexto]passivoCons.jsp','Passivo',1024,595);",
            "LoginControle.permissaoAcessoMenuVO.passivo", ModuloAberto.CRMWEB,
            "ZillyonWeb:Opera\u00E7\u00F5es:ContatoReceptivo", "como-cadastrar-receptivos-no-modulo-crm/"),
    INDICACAO("Indica\u00E7\u00E3o", "abrirPopup('[contexto]indicacaoCons.jsp','indicacao',780,595);",
            "LoginControle.permissaoAcessoMenuVO.indicacao", ModuloAberto.CRMWEB,
            "ZillyonWeb:Opera\u00E7\u00F5es:Indicacao", "como-emitir-um-relatorio-de-todas-as-indicacoes-feitas-no-mes/"),
    CONTATO_APP("Contatos APP", "abrirPopup('[contexto]contatosApp.jsp','contatosApp',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.relatorioContatosAPP",
            ModuloAberto.CRMWEB, "CRMWeb:Opera%E7%F5es:ContatosApp", "como-enviar-notificacao-para-o-app-de-um-unico-aluno/"),
    AGENDAMENTOS_CRM("Agendamentos", "abrirPopup('[contexto]agendamentos.jsp','contatosApp',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.relatorioAgendamentos",
            ModuloAberto.CRMWEB, "CRMWeb:Opera\u00E7\u00F5es:Agendamentos", "relatorio-de-agendamentos-do-crm/"),

    // INICIO FINANCEIRO
    LANCAMENTOS("Ver lan\u00E7amentos", "semValor", "LoginControle.permissaoAcessoMenuVO.visualizarLancamentos", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Lan%E7amentos", "relatorio-ver-lancamentos/"),
    LANCAMENTO_CONTA_RAPIDO("Lan\u00E7amento de conta r\u00E1pido", "semValor", "LoginControle.permissaoAcessoMenuVO.visualizarLancamentos",
            ModuloAberto.FINAN, "FinanceiroWeb:Inicial:Lan%E7amentos", "relatorio-ver-lancamentos/", "Lan\u00E7amento R\u00E1pido"),
//    CONTAS_PAGAR("Ver contas a Pagar", "semValor", "LoginControle.permissaoAcessoMenuVO.lancarContasPagar", ModuloAberto.FINAN,
//            "FinanceiroWeb:Inicial:Lan%E7amentos:Pagamento", "como-fazer-agendamento-de-despesa-conta-contas-a-pagar-e-contas-a-receber-no-financeiro/", "Contas a pagar"),
    NOVA_CONTA_PAGAR("Nova Conta a Pagar", "semValor", "LoginControle.permissaoAcessoMenuVO.lancarContasPagar", ModuloAberto.FINAN,
            "/FinanceiroWeb:Inicial:Lan%E7amentos", "relatorio-ver-lancamentos/"),
//    CONTAS_RECEBER("Ver contas a Receber", "semValor", "LoginControle.permissaoAcessoMenuVO.lancarContasPagar", ModuloAberto.FINAN,
//            "FinanceiroWeb:Inicial:Lan%E7amentos:Recebimento", "como-fazer-agendamento-de-despesa-conta-contas-a-pagar-e-contas-a-receber-no-financeiro/", "Contas a Receber"),
    NOVA_CONTAS_RECEBER("Nova Conta a Receber", "semValor", "LoginControle.permissaoAcessoMenuVO.lancarContasPagar", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Lan%E7amentos:Recebimento", "como-fazer-agendamento-de-despesa-conta-contas-a-pagar-e-contas-a-receber-no-financeiro/"),
    ULTIMOS_LANCAMENTOS("\u00DAltimos lan\u00E7amentos financeiros (IAE)", "semValor", "LoginControle.permissaoAcessoMenuVO.visualizarLancamentos",
            ModuloAberto.FINAN, "FinanceiroWeb:Inicial:Lan%E7amentos", "relatorio-ver-lancamentos/"),
    RECEBIVEIS("Receb\u00EDveis", "semValor", "LoginControle.permissaoAcessoMenuVO.gestaoRecebiveis", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Gest%E3o_de_Receb%EDveis", null),
    LOTES("Lotes", "semValor", "LoginControle.permissaoAcessoMenuVO.gestaoLotes", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Gest%E3o_de_Lotes", null),
    FORNCEDOR("Fornecedor", "semValor", "LoginControle.permissaoAcessoMenuVO.fornecedor", ModuloAberto.FINAN,
            "Cadastros:Cadastros:Fornecedor", null),
    PESSOA("Pessoa (FIN)", "abrirPopup('[contexto]/faces/faces/pessoaCons.jsp', 'Pessoa', 800, 595);", "LoginControle.permissaoAcessoMenuVO.pessoa",
            ModuloAberto.FINAN, "Cadastros:Cadastros:Fornecedor", "como-cadastrar-um-fornecedor/"),
    CAIXA_ADIMISTRATIVO("Caixa Administrativo", "semValor", "(LoginControle.permissaoAcessoMenuVO.abrirCaixaAdm && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Caixa_Administrativo", "relatorio-caixa-administrativo/"),
    CONSULTAR_CAIXA("Consultar caixa", "Richfaces.showModalPanel('modalConsCaixa')", "((LoginControle.permissaoAcessoMenuVO.abrirCaixaAdm || LoginControle.permissaoAcessoMenuVO.consultarHistCaixaAdm) && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)",
            ModuloAberto.FINAN, "FinanceiroWeb:Inicial:Caixa_Administrativo#Consultar_Caixa", null, "Consultar Caixa"),
    ABRIR_CAIXA("Abrir caixa", "Richfaces.showModalPanel('modalAbrirCaixa')",
            "!CaixaControle.usuarioTemCaixaEmAberto and ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas and LoginControle.permissaoAcessoMenuVO.abrirCaixaAdm", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Caixa_Administrativo#Abrir%20caixa", null, new String[]{},"modalAbrirCaixa"),
    FECHAR_CAIXA("Fechar caixa", "Richfaces.showModalPanel('modalFecharCaixas')",
            "CaixaControle.usuarioTemCaixaEmAberto and ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas and LoginControle.permissaoAcessoMenuVO.abrirCaixaAdm", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Caixa_Administrativo#Fechar%20caixa", null, new String[]{},
            "modalFecharCaixas"),

    RESUMO_CONTAS("Resumo de Contas", "semValor", "(LoginControle.permissaoAcessoMenuVO.resumoContas && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)",
            ModuloAberto.FINAN, "FinanceiroWeb:Inicial:Resumo_de_Contas", null),
    FIN_CADASTROS_AUXILIARES("Cadastros Auxiliares", "telaCadastroAuxiliares", "!LoginControle.menuZwUi",
            ModuloAberto.FINAN, "", null),
    FIN_CONFIG_FINANCEIRAS("Config. financeiras", "telaCadastroConfigFinanceira", "LoginControle.permissaoAcessoMenuVO.configuracaoFinanceiro",
            ModuloAberto.FINAN, "", null),
    FINAN_CONTA("Conta", "abrirPopup('[contexto]finanContaCons.jsp?modulo=financeiroWeb','Contas',780,595);",
            "(LoginControle.permissaoAcessoMenuVO.conta && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)",
            ModuloAberto.FINAN,
            "FinanceiroWeb:Business_Intelligence:Demonstra%E7%E3o_do_Resultado_do_Exerc%EDcio", "o-que-e-dre-e-para-que-serve/"),
    DEMONSTRATIVO_FINAN("Demonstrativo Financeiro", "semValor", "LoginControle.permissaoAcessoMenuVO.BIFinanceiro",
            ModuloAberto.FINAN, "FinanceiroWeb:Relat%F3rios:Demonstrativo_Financeiro", null),
    FECHAMENTO_CAIXA_PLANO_CONTAS("Fechamento Caixa por Plano de Contas", "semValor", "LoginControle.configuracaoSistema.sesc",
            ModuloAberto.FINAN, "FinanceiroWeb:Relat%F3rios:Fechamento_Caixa_por_Plano_de_Contas", null),
    DRE("D.R.E Financeiro", "semValor", "LoginControle.permissaoAcessoMenuVO.BIFinanceiro",
            ModuloAberto.FINAN,
            "FinanceiroWeb:Business_Intelligence:Demonstra%E7%E3o_do_Resultado_do_Exerc%EDcio", "o-que-e-dre-e-para-que-serve/", "D.R.E Financeiro"),
    FLUXO_CAIXA_FINAN("Fluxo de Caixa", "semValor", "LoginControle.permissaoAcessoMenuVO.fluxoCaixa", ModuloAberto.FINAN,
            "FinanceiroWeb:Relat%F3rios:Fluxo_Caixa", "como-ver-meu-fluxo-de-caixa/"),
    MOVIMENTACOES_FINAN("Movimenta\u00E7\u00F5es Financeiras", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.BIFinanceiro && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)",
            ModuloAberto.FINAN, "FinanceiroWeb:Business_Intelligence:Movimenta\u00E7\u00F5es_Financeiras", "como-realizar-o-lancamento-de-cheques-cartoes-avulsos/"),
    RELATORIO_DEVOLUCAO_CHEQUE("Relat\u00F3rio Cheques Devolvidos",
            "semValor",
            "(LoginControle.permissaoAcessoMenuVO.BIFinanceiro && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)",
            ModuloAberto.FINAN, "Business_Intelligence:Relat\u00F3rio_Cheques_Devolvidos", "como-vejo-os-cheques-que-foram-devolvidos/", "Cheques Devolvidos"),
    RATEIO_INTEGRACAO("Rateio Integra\u00E7\u00E3o", "semValor", "LoginControle.permissaoAcessoMenuVO.rateioIntegracao", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Rateio_Integra%E7%E3o", "como-categorizar-receitas-e-despesas-no-financeiro-rateio-integracao/"),
    PLANO_CONTAS("Plano de Contas", "semValor", "LoginControle.permissaoAcessoMenuVO.planoContas", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Plano_de_Contas", "como-cadastrar-um-plano-de-contas/"),
    CHEQUES_CARTOES_AVULSOS("Cheques/Cart\u00F5es Avulsos", "semValor", "LoginControle.permissaoAcessoMenuVO.lancamentosAvulsos",
            ModuloAberto.FINAN, "FinanceiroWeb:Business_Intelligence:Relat\u00F3rio_Cheques_Devolvidos", "como-vejo-os-cheques-que-foram-devolvidos/"),
    CENTRO_CUSTOS("Centro de Custos", "semValor", "LoginControle.permissaoAcessoMenuVO.centroCustos", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Centro_de_Custos", "como-cadastrar-um-centro-de-custos/"),
    TIPO_CONTA("Tipo de Conta", "semValor", "(LoginControle.permissaoAcessoMenuVO.tipoConta && ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas)",
            ModuloAberto.FINAN, "ZillyonWeb:Cadastros:Cadastros:Tipo_de_Conta", "tipo-de-contas-e-seus-comportamentos/"),
    CONTA_CONTABIL("Conta Cont\u00E1bil", "semValor", "ContaContabilControle.mostrarContaContabil",
            ModuloAberto.FINAN, "ZillyonWeb:Cadastros:Cadastros:Conta", "como-cadastrar-ou-editar-uma-conta-para-movimentacoes-financeiras/"),
    TIPO_DOCUMENTO("Tipo de Documento", "semValor", "LoginControle.permissaoAcessoMenuVO.tipoDocumento", ModuloAberto.FINAN,
            "ZillyonWeb:Cadastros:Cadastros:Tipo_de_Documento", "como-cadastrar-um-tipo-de-documento/"),
    RELATORIO_ORCAMENTARIO("Relat\u00F3rio Or\u00E7ament\u00E1rio", "semValor", "LoginControle.permissaoAcessoMenuVO.BIFinanceiro",
            ModuloAberto.FINAN, "", null, "Relat\u00F3rio Or\u00E7ament\u00E1rio"),
    RELATORIO_ORCAMENTARIO_CONFIGURACAO("Relat\u00F3rio Or\u00E7ament\u00E1rio Configura\u00E7\u00E3o", "abrirPopup('[contexto]relOrcamentarioConf.jsp','Relatrio Oramentrio',1090,595);",
            "LoginControle.permissaoAcessoMenuVO.BIFinanceiro", ModuloAberto.FINAN, "", null, "Orçamentário - Configuração"),
    // INICIO CENTRAL EVENTOS
    BUSINESS_INTELLIGENCE_CE("BI Central de Eventos", "semValor", "true", ModuloAberto.CE,
            "", null, "BI Central de Eventos"),
    SIMULAR_ORCAMENTO("Simular Or\u00E7amento", "semValor", "true", ModuloAberto.CE,
            "Central_de_Eventos:Opera%E7%F5es:Or%E7amento_Detalhado", null),
    LISTA_PROSPECTS("Lista de Prospects", "semValor", "true", ModuloAberto.CE,
            "Central_de_Eventos:Consultas:ListaProspects", null),
    AGENDA_VISISTA("Agenda Visita", "semValor", "true", ModuloAberto.CE,
            "Central_de_Eventos:Opera%E7%F5es:AgendaVisita", null),
    CONVERSAS("Conversas", "semValor", "true", ModuloAberto.CE,
            "Central_de_Eventos:Conversas:Conversas", null),
    CAIXA_EM_ABERTO_CE("Caixa em aberto", "semValor", "true", ModuloAberto.CE,
            "ZillyonWeb:Inicial:Caixa_em_Aberto", "como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"),
    PESQUISA_GERAL("Pesquisa Geral", "semValor", "true", ModuloAberto.CE,
            "Central_de_Eventos:Consultas:Pesquisa_Geral", null),
    GESTAO_CREDITO("Gest\u00E3o de cr\u00E9dito", "semValor", "true", ModuloAberto.CE,
            "Central_de_Eventos:", null),
    TIPO_AMBIENTE("Tipo Ambiente",
            "abrirPopup('[contexto]pages/ce/cadastros/tipoAmbiente.jsp?modulo=centralEventos','Tipo_de_Ambiente',900,595);",
            "true", ModuloAberto.CE, "Central_de_Eventos:Cadastros:PP:Tipo_Ambiente", null),
    PERFIL_EVENTO("Tabela de Pre\u00E7os / Perfil Evento",
            "abrirPopup('[contexto]pages/ce/cadastros/perfilEvento.jsp?modulo=centralEventos','aa',900,595);",
            "true", ModuloAberto.CE,
            "Central_de_Eventos:Cadastros:PP:Tabela_Pre\u00E7o_Perfil_Evento", null),
    SERVICOS("Servi\u00E7os",
            "abrirPopup('[contexto]pages/ce/cadastros/servico.jsp?modulo=centralEventos','',900,595);",
            "true", ModuloAberto.CE, "Central_de_Eventos:Cadastros:PP:Servi\u00E7o", null),
    CADASTRO_INICIAL_CE("Cadastro Inicial(CE)", "semValor", "true", ModuloAberto.CE,
            "/Central_de_Eventos:Cadastros:Cadastro_Inicial", null),
    AGENDA_EVENTOS("Agenda de Eventos", "semValor", "true", ModuloAberto.CE,
            "Central_de_Eventos:Consultas:AgendadeEventos", null),
    ANIVERSARIANTES_CE("Clientes Aniversariante",
            "abrirPopup('[contexto]relatorio/clientePorAniversarioRel.jsp?modulo=centralEventos','aniversariantes',780,595);",
            "true", ModuloAberto.CE, "ZillyonWeb:Relatorios:Aniversariantes", "como-gerar-uma-lista-de-aniversariantes/"),
    FECHAMENTO_CAIXA_CE("Fechamento de Caixa",
            "abrirPopup('[contexto]relatorio/caixaPorOperador.jsp?modulo=centralEventos','CaixaPorOperador',780,595);",
            "true", ModuloAberto.CE,
            "ZillyonWeb:Relatorios:Fechamento_de_Caixa_por_Operador", "como-verificar-o-relatorio-de-fechamento-de-caixa-por-operador/"),
    RECEITA_PERIODO_CE("Receita por Per\u00EDodo",
            "abrirPopup('[contexto]relatorio/receitaPorPeriodoSinteticoRel.jsp?modulo=centralEventos','ReceitaPorPeriodoSintetico?modulo=centralEventos',780,595);",
            "true", ModuloAberto.CE, "", null),
    PARCELA_ABERTO("Parcelas em Aberto",
            "abrirPopup('[contexto]relatorio/parcelaEmAbertoRel20.jsp?modulo=centralEventos','ParcelaEmAberto',780,585);",
            "true", ModuloAberto.CE, "ZillyonWeb:Inicial:Caixa_em_Aberto", "como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"),
    SALDO_CONTA_CORRENT("Saldo Conta Corrente",
            "abrirPopup('[contexto]relatorio/saldoContaCorrenteRel.jsp?modulo=centralEventos','ParcelaEmAberto',780,585);",
            "true", ModuloAberto.CE, "", null),
    CATEGORIA_CLIENTE_CE("Categoria Cliente - CE",
            "abrirPopup('[contexto]categoriaCons.jsp?modulo=centralEventos','ParcelaEmAberto',780,595);",
            "true", ModuloAberto.CE,
            "ZillyonWeb:Cadastros_Auxiliares:Categoria_de_Clientes", null),
    CLIENTE_CE("Cliente - CE", "abrirPopup('[contexto]clienteCons.jsp?modulo=centralEventos','Cliente',780,595);",
            "true",
            ModuloAberto.CE, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Cliente", "tela-do-cliente-modulo-adm/"),
    COLABORADOR_CE("Colaborador - CE",
            "abrirPopup('[contexto]colaboradorCons.jsp?modulo=centralEventos','Colaborador',1090,595);",
            "LoginControle.permissaoAcessoMenuVO.colaborador",
            ModuloAberto.CE, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Colaborador", "como-cadastrar-um-novo-colaborador/"),
    GRAU_DE_INSTRUCAO_CE("Grau de Instru\u00E7\u00E3o - CE",
            "abrirPopup('[contexto]grauInstrucaoCons.jsp?modulo=centralEventos','',780,595);", "true",
            ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Grau_de_Instrucao", null),
    PROFISSAO_CE("Profiss\u00E3o - CE",
            "abrirPopup('[contexto]profissaoCons.jsp?modulo=centralEventos','Profissao',780,595);", "true",
            ModuloAberto.CE, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Profissao", null),
    PAIS_CE("Pa\u00EDs - CE", "abrirPopup('[contexto]paisCons.jsp?modulo=centralEventos','Pais',780,595);", "true",
            ModuloAberto.CE, "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Pais", null),
    CIDADE_CE("Cidade - CE", "abrirPopup('[contexto]cidadeCons.jsp?modulo=centralEventos','Cidade',780,595);",
            "true", ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Cadastros_Auxiliares:Cidade", null),
    PRODUTO_LOCACAO("Produto Loca\u00E7\u00E3o",
            "abrirPopup('[contexto]/faces/pages/ce/cadastros/produtoLocacao.jsp?modulo=centralEventos','ProdutoLocacao',900,595);",
            "true", ModuloAberto.CE, "Central_de_Eventos:Cadastros:PP:Produto", null),
    EMPRESA_CE("Empresa", "abrirPopup('[contexto]/faces/empresaCons.jsp?modulo=centralEventos','Empresa',800,595);",
            "true", ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Acesso_ao_sistema:Empresa", "configurando-os-dados-da-empresa-para-emissao-de-notas-fiscais/"),
    PERFIL_ACESSO_CE("Perfil Acesso",
            "abrirPopup('[contexto]/faces/perfilAcessoCons.jsp?modulo=centralEventos','perfilAcessoCons',800,595);",
            "true", ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Acesso_ao_sistema:Perfil_Acesso", "quais-sao-as-permissoes-do-perfil-de-acesso-do-modulo-adm/"),
    USUARIO_CE("Usu\u00E1rio",
            "abrirPopup('[contexto]/faces/usuarioCons.jsp?modulo=centralEventos','Usu\u00E1rio',820,595);",
            "true", ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Acesso_ao_Sistema:Usuario", "como-cadastro-um-novo-usuario-no-sistema/"),
    CONTROLE_LOG_CE("Controle de Log",
            "abrirPopup('[contexto]/faces/controleLogCons.jsp?modulo=centralEventos','controleLogCons',800,595);",
            "true", ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Acesso_ao_sistema:Controle_de_Log", "como-utilizar-o-controle-de-log/"),
    FORNECEDOR_CE("Fornecedor",
            "abrirPopup('[contexto]/faces/pages/ce/cadastros/fornecedor.jsp?modulo=centralEventos','Fornecedor',900,595);",
            "true", ModuloAberto.CE, "ZillyonWeb:Cadastros:Cadastros:Fornecedor", "como-cadastrar-um-fornecedor/"),
    AMBIENTE_CE("Ambiente", "abrirPopup('[contexto]ambienteCons.jsp?modulo=centralEventos','Ambiente',900,595);",
            "true", ModuloAberto.CE, "ZillyonWeb:Cadastros:PPT:Ambiente", "conhecimento/como-cadastrar-ambiente/"),
    ESTORNO_RECIBO_CE("Estorno de Recibos",
            "abrirPopup('[contexto]estornoReciboCons.jsp?modulo=centralEventos','EstornoRecibo',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.estornoRecibo", ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Config._Financeiras:Consulta_de_Recibo", null),
    MOVIMENTO_CC_CLIENTE_CE("Movimento de Conta Corrente do Cliente",
            "abrirPopup('[contexto]movimentoContaCorrenteClienteCons.jsp?modulo=centralEventos','MovimentoContaCorrenteCliente',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.movimentoContaCorrenteCliente",
            ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Config._Financeiras:Movimento_de_Conta_Corrente_do_Cliente", "como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"),
    FORMA_PAGAMENTO_CE("Formas de Pagamento",
            "abrirPopup('[contexto]formaPagamentoCons.jsp?modulo=centralEventos','FormaPagamento',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.formaPagamento", ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Config._Financeiras:Formas_de_Pagamento", "como-cadastrar-uma-forma-de-pagamento/"),
    OPERADORA_CARTAO_CE("Operadora de Cart\u00E3o",
            "abrirPopup('[contexto]operadoraCartaoCons.jsp?modulo=centralEventos','OperadoraCartao',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.operadoraCartao",
            ModuloAberto.CE,
            "ZillyonWeb:Cadastros:Config._Financeiras:Operadora_de_Cart\u00E3o", null),
    //INICIO STUDIO
    COMISSAO_EST("Comiss\u00E3o Est\u00FAdio",
            "semValor",
            "LoginControle.apresentarLinkEstudio",
            ModuloAberto.GESTAOSTUDIO,
            "", null, "Comiss\u00E3o"),
    DIARIO("Di\u00E1rio",
            "semValor",
            "LoginControle.apresentarLinkEstudio",
            ModuloAberto.GESTAOSTUDIO,
            "Gest%E3oStudio:Relat%F3rios:Di%E1rio", "relatorio-de-fechamento-diario-agenda-studio/"),
    AGENDAMENTOS("Agendamentos",
            "semValor",
            "LoginControle.apresentarLinkEstudio",
            ModuloAberto.GESTAOSTUDIO,
            "", null),
    CLIENTES_SEM_SESSAO("Cliente sem Sess\u00E3o",
            "semValor",
            "LoginControle.apresentarLinkEstudio",
            ModuloAberto.GESTAOSTUDIO,
            "Gest%E3oStudio:Relat%F3rios:Cliente_sessao", "relatorio-clientes-sem-sessao-agenda-studio/"),
    CONFIG_EST("Configura\u00E7\u00F5es",
            "abrirPopup('[contexto]/faces/pages/estudio/configuracaoEstudio.jsp','ConfiguracaoSistema',1024,768);",
            "LoginControle.permissaoAcessoMenuVO.configuracaoSistema",
            ModuloAberto.GESTAOSTUDIO,
            "ZillyonWeb:Configura\u00E7\u00F5es", null),
    DISPONIBILIDADE_EST("Disponibilidade",
            "semValor",
            "LoginControle.apresentarLinkEstudio",
            ModuloAberto.GESTAOSTUDIO,
            "", null),
    VENDA_AVULSA_EST("Venda Avulsa",
            "semValor",
            "LoginControle.apresentarLinkEstudio",
            ModuloAberto.GESTAOSTUDIO,
            "ZillyonWeb:Inicial:Venda_Avulsa", "como-fazer-uma-venda-de-produto-para-aluno-venda-avulsa/"),
    CAIXA_ABERTO_EST("Caixa em Aberto",
            "semValor",
            "(LoginControle.apresentarLinkEstudio && (LoginControle.permissaoAcessoMenuVO.caixaEmAberto || LoginControle.usuarioLogado.administrador))",
            ModuloAberto.GESTAOSTUDIO,
            "ZillyonWeb:Inicial:Caixa_em_Aberto", "como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"),
    PACOTE_EST("Pacote",
            "semValor",
            "LoginControle.apresentarLinkEstudio",
            ModuloAberto.GESTAOSTUDIO,
            "Gest%E3oStudio:Servi%E7os:Pacote", null),
    AGENDA_MENSAL_EST("Mensal",
            "semValor",
            "LoginControle.permissaoAcessoMenuVO.agendaMensal",
            ModuloAberto.GESTAOSTUDIO,
            "", null),
    AMBIENTE_EST("Ambiente",
            "semValor",
            "LoginControle.permissaoAcessoMenuVO.agendaAmbiente",
            ModuloAberto.GESTAOSTUDIO,
            "", null),
    PROFISSIONAL_EST("Profissional",
            "semValor",
            "LoginControle.permissaoAcessoMenuVO.agendaProfissional",
            ModuloAberto.GESTAOSTUDIO,
            "Gest%E3oStudio:Agendas:Profissional", null),
    INDIVIDUAL_EST("Individual",
            "semValor",
            "LoginControle.permissaoAcessoMenuVO.agendaIndividual",
            ModuloAberto.GESTAOSTUDIO,
            "Gest%E3oStudio:Agendas:Individual", null),
    //Configurações
    CONFIG_ZW("Configura\u00E7\u00F5es",
            "abrirPopup('[contexto]configuracaoSistemaForm.jsp','ConfiguracaoSistema',1024,768);",
            "LoginControle.permissaoAcessoMenuVO.configuracaoSistema",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Configura\u00E7\u00F5es", null),
    GOOG_CALENDAR("Google Calendar", "window.open('[contexto]googlecalendar.jsp','GoogleCalendar',820,620);",
            "FuncionalidadeControle.contemPagesUrl", ModuloAberto.ZILLYONWEB, "", null),
    VELOCIMETRO("Veloc\u00EDmetro", "semValor",
            "true", ModuloAberto.ZILLYONWEB, "", null),
    DOC_VELOCIDADE("Doc. Velocidade", "semValor", "FuncionalidadeControle.contemPagesUrl",
            ModuloAberto.ZILLYONWEB, "", null),
    ALTERAR_SENHA("Alterar Senha",
            "abrirPopup('[contexto]/faces/alterarSenhaClienteForm.jsp','AlterarSenha',410,350);",
            "LoginControle.usuario.permiteAlterarPropriaSenha",
            ModuloAberto.ZILLYONWEB, "", null), // FAQ_PERSONALIZADO("FAQ Personalizado", "link",
    // "SuporteControle.apresentarFAQ",new String[]{""},
    // ModuloAberto.ZILLYONWEB),
    UCP("UCP", "link", "true", ModuloAberto.ZILLYONWEB, "", null),
    WIKI_PACTO("WikiPacto", "link", "true", ModuloAberto.ZILLYONWEB, "", ""),
    CONFIG_CRM("Configura\u00E7\u00F5es",
            "abrirPopup('[contexto]configuracaoSistemaCRMForm.jsp','configuracaoSistemaCRM',870,595);",
            "LoginControle.permissaoAcessoMenuVO.configuracaoSistemaCRM",
            ModuloAberto.CRMWEB, "CRMWeb:Configura%E7%F5es_CRM", "como-cadastrar-objecoes-no-crm/"),
    CONFIG_FIN("Configura\u00E7\u00F5es", "semValor", "LoginControle.permissaoAcessoMenuVO.configuracaoFinanceiro",
            ModuloAberto.FINAN, "FinanceiroWeb:Configura\u00E7\u00F5es", "configuracoes-da-engrenagem-financeiro-_-aba-configuracoes-gerais/"),
    SUPORTE("Suporte", "link", "true", ModuloAberto.ZILLYONWEB, "", null),
    BOLETO("Boleto Pacto", "financeiroPacto", "LoginControle.permissaoAcessoMenuVO.boletosSistema",
            ModuloAberto.ZILLYONWEB, "", null),
    CONFIG_ESTUDIO("Configura\u00E7\u00F5es", "semValor", "true", ModuloAberto.GESTAOSTUDIO,
            "ZillyonWeb:Configura\u00E7\u00F5es", "configuracoes-da-engrenagem-adm_-aba-basico/"), // Projeto em standBy
    // "(LoginControle.permissaoAcessoMenuVO.conviteBI)"
    CONVITES_AULAS("Relat\u00F3rio de Convites de Aula Experimental",
            "abrirPopup('[contexto]relatorio/relatorioConvitesAulaExperimental.jsp','ConvitesAulaExperimental',1190,595);",
            "(false)", ModuloAberto.ZILLYONWEB, "", null),
    BLOQUEIO_CAIXA("Bloqueio de caixa", "semValor", "(ConfiguracaoFinanceiroControle.confFinanceiro.usarMovimentacaoContas && LoginControle.permissaoAcessoMenuVO.bloqueioCaixa)", ModuloAberto.FINAN, "", "como-lancar-bloqueio-no-caixa-financeiro/"),
    GYM_PASS_RELATORIO("Relat\u00F3rio Wellhub",
            "abrirPopup('[contexto]relatorio/listaAcessoGymPassRel.jsp','PermiteVisualizaGymPassPeriodo',1024,700);",
            "LoginControle.permissaoAcessoMenuVO.permiteVisualizaGymPassPeriodo",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Recurso_GymPass", null, "Wellhub"),
    GOGOOD_RELATORIO("Relat\u00F3rio Gogood",
            "abrirPopup('[contexto]relatorio/naoContem.jsp','PermiteVisualizaGogoodPeriodo',1024,700);",
            "LoginControle.permissaoAcessoMenuVO.permiteVisualizaGogoodPeriodo",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Recurso_Gogood", null, "Gogood"),
    REGISTRAR_ACESSO_AVULSO("Registrar Acesso Manual",
            "abrirPopup('[contexto]relatorio/registrarAcessoAvulso.jsp','Registrar Acesso Manual',1024,700);",
            "LoginControle.permissaoAcessoMenuVO.permiteRegistrarAcessoAvulso",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Registrar_Acesso_Manual", "como-fazer-um-registro-de-acesso-manual/"),
    ADQUIRENTE("Adquirente", "abrirPopup('[contexto]adquirenteCons.jsp','Adquirente',800,595);", "true",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Financeiro:Adquirente", null),
    MODELO_ORCAMENTO("Modelo de Or\u00E7amento",
            "abrirPopup('[contexto]modeloOrcamentoCons.jsp','ModeloOrcamento',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.modeloOrcamento",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Config.Orcamento:Modelo_de_Orcamento", null),
    CONTAS_A_PAGAR("Contas a Pagar", "", "LoginControle.permissaoAcessoMenuVO.lancarContasPagar", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Lan%E7amentos:Pagamento",  null,"Contas a Pagar"),
    CONTAS_A_RECEBER("Contas a Receber", "", "LoginControle.permissaoAcessoMenuVO.lancarContasPagar", ModuloAberto.FINAN,
            "FinanceiroWeb:Inicial:Lan%E7amentos:Recebimento", null,"Contas a Receber"),
//    IMPORTADOR_FINANCEIRO("Importador financeiro", "semValor", "true", ModuloAberto.FINAN,
//            "ZillyonWeb:Cadastros:Config._Financeiras:ImportadorExcel"),
//joão alcides: estou comentando este recurso até que seja finalizado
//    MAPA_ESTATISTICO("Mapa estat\u00E1stico", "abrirPopup('[contexto]relatorio/mapaEstatistico.jsp', '', 1135, 683);",
//            "LoginControle.permissaoAcessoMenuVO.mapaEstatistico",
//            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:MapaEstatistico"),
    HISTORICO_PONTOS_DOTZ("Hist\u00F3rico Dotz",
            "abrirPopup('[contexto]relatorio/historicoPontosParceiroRel.jsp', 'HistoricoDotz', 850, 595);",
            "(LoginControle.empresaLogado.usarParceiroFidelidade)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Relatorios:HistoricoDot", null),
    GESTAO_DE_TURMA("Gest\u00E3o de Turma", "gestaoTurma", "LoginControle.permissaoAcessoMenuVO.gestaoTurma",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Gestao_Turma", "para-que-serve-o-gestao-de-turma/"),

//    GESTAO_FAMILIA("Gestão de Famílias",
//
//            "gestaoFamilia", "true", ModuloAberto.ZILLYONWEB,
//            "ZillyonWeb:Inicial:Gestao_Familia", null),
    CLUBE_VANTAGENS_ATIVAR("Ativar Clube Vantagens", "semValor", "!LoginControle.empresaLogado.trabalharComPontuacao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Clube_de_Vantagens#BI_Clube_de_Vantagens", "como-ver-qual-produto-ou-plano-mais-acumulou-pontos-no-clube-de-vantagens-bi-clube-de-vantagens/"),
    CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE("BI Clube de Vantagens", "semValor", "LoginControle.empresaLogado.trabalharComPontuacao",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Clube_de_Vantagens#BI_Clube_de_Vantagens", "como-ver-qual-produto-ou-plano-mais-acumulou-pontos-no-clube-de-vantagens-bi-clube-de-vantagens/","BI Clube de Vantagens"),
    CLUBE_VANTAGENS_CONFIGURACOES("Configura\u00E7\u00F5es do Clube de Vantagens", "semValor", "(LoginControle.empresaLogado.trabalharComPontuacao && LoginControle.permissaoAcessoMenuVO.configurarClubeDeVantagens)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Clube_de_Vantagens#Como_configurar_o_Clube_de_Vantagens.3F", "como-configurar-o-clube-de-vantagens/", "Configura\u00E7\u00E3o do Clube de Vantagens"),
    CLUBE_VANTAGENS_CAMPANHA("Campanha", "semValor", "(LoginControle.empresaLogado.trabalharComPontuacao && LoginControle.permissaoAcessoMenuVO.campanhaDuracao)",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Clube_de_Vantagens#Campanhas", "com-ver-minhas-campanhas-do-clube-de-vantagem/", "Campanha do Clube de Vantagens"),
    RELATORIO_CONVIDADOS("Relat\u00F3rio de Convidados",
            "abrirPopup('[contexto]relatorio/listaAcessoConvidadosRel.jsp', 'Relat\u00F3rio de Convidados', 1024, 700);",
            "LoginControle.permissaoAcessoMenuVO.permiteVisualizaConvitesPeriodo", ModuloAberto.ZILLYONWEB,
            "Convidado", null, "Convidados"),
    VENDAS_ONLINE("Gest\u00E3o de Vendas Online",
            "vendasOnline",
            "(LoginControle.apresentarVendas && LoginControle.permissaoAcessoMenuVO.vendasOnline)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Vendas_Online:Gest\u00E3o", null),
    VENDAS_ONLINE_ADQUIRA("Gest\u00E3o de Vendas Online",
            "vendasOnlineAdquira",
            "(!LoginControle.apresentarVendas)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Vendas_Online:Gest\u00E3o", null),
    TIPO_MODALIDADE("Tipo de Modalidade",
            "abrirPopup('[contexto]tipoModalidadeCons.jsp', 'Tipo Modalidade', 1024, 700);",
            "LoginControle.permissaoAcessoMenuVO.tipoModalidade", ModuloAberto.ZILLYONWEB, "Convidado", null),
    CANAL_CLIENTE("Canal do Cliente", "canal-cliente/adicionarConta", "true", ModuloAberto.NOVO_TREINO,
            "FinanceiroWeb:Inicial:Lan%E7amentos:Recebimento", null),
    RELATORIO_CLIENTES_COBRANCA_BLOQUEADA("Clientes com cobrança automática bloqueada",
            "abrirPopup('[contexto]relatorio/relatorioPessoaCobrancaBloqueada.jsp', 'Relat\u00F3rio de Clientes com Cobrança Automática Bloqueada', 1024, 700);",
            "LoginControle.permissaoAcessoMenuVO.permiteRelatorioClientesCobrancaBloqueada", ModuloAberto.ZILLYONWEB,
            "ClienteCobrancaBloqueada", "como-obter-um-relatorio-dos-clientes-com-cobranca-automatica-bloqueada/", "Cobranças Bloqueadas"),

    CONTATO_PESSOAL("Contato Pessoal", "abrirPopup('[contexto]mailing.jsp','mailing',1000,650);",
            "LoginControle.permissaoAcessoMenuVO.malaDireta",
            ModuloAberto.CRMWEB, "CRMWeb:Recursos_Extras:Mailing", "como-confirmar-o-comparecimento-de-um-cliente-agendado-pelo-crm/"),
    CANAL_CLIENTE_PACTO_STORE("Pacto Store+", "canal-cliente/adicionarConta", "LoginControle.apresentarPactoStore", ModuloAberto.NOVO_TREINO, "ZillyonWeb:CanalCliente", null, true),
    SGP_MODALIDADES_COM_TURMAS("SGP Modalidades com Turmas",
            "abrirPopup('[contexto]relatorio/sgpModalidadesComTurma.jsp','',900,600);",
            "LoginControle.configuracaoSistema.sesc",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Relatorios_de_Frequencia:Relatorio_Turmas", null),
    SGP_MODALIDADES_SEM_TURMAS("SGP Modalidades sem Turmas",
            "abrirPopup('[contexto]relatorio/sgpModalidadesSemTurma.jsp','',900,600);",
            "LoginControle.configuracaoSistema.sesc",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Relatorios_de_Frequencia:Relatorio_Turmas", null),
    SGP_TURMAS("SGP Mapa Estatístico",
            "abrirPopup('[contexto]relatorio/sgpTurma.jsp','',900,600);",
            "LoginControle.configuracaoSistema.sesc",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Relatorios_de_Frequencia:Relatorio_Turmas", null),
    SGP_AVALIACOES_FISICAS("SGP Avaliações físicas",
            "abrirPopup('[contexto]relatorio/sgpAvaliacaoFisicaCons.jsp','',900,600);",
            "LoginControle.configuracaoSistema.sesc",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:SGPAvaliacoesFisicas", null),
    ADM_BUSINESS_INTELLIGENCE("BI Administrativo", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.visualizarBI || LoginControle.usuario.administrador)",
            ModuloAberto.ZILLYONWEB, "", null, "BI Administrativo"),
    BUSINESS_INTELLIGENCE("Business Intelligence", "semValor", "(LoginControle.permissaoAcessoMenuVO.visualizarBI || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.ZILLYONWEB, "", null, "BI Administrativo"),
    ADM_VENDA_RAPIDA("Venda Rápida", "semValor", "LoginControle.permissaoAcessoMenuVO.vendaRapida",
            ModuloAberto.ZILLYONWEB, "", "como-funciona-o-venda-rapida/", "Venda Rápida"),
    CRM_BI("BI CRM", "semValor", "(LoginControle.permissaoAcessoMenuVO.businessIntelligenceCRM || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.CRMWEB, "", null, "BI CRM"),

    FIN_BI("BI Financeiro", "semValor", "(LoginControle.permissaoAcessoMenuVO.biFinanceiro || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.FINAN, "", null, "BI Financeiro"),

    TREINO_BI("BI Treino", "treino/bi", "LoginControle.permissaoAcessoMenuVO.treinoBI",
            ModuloAberto.NOVO_TREINO, "", null, "BI Treino"),
    TREINO_EM_CASA("Treino em casa", "treino/home-fit/lista-treinos", "LoginControle.permissaoAcessoMenuVO.treinoEmCasa",
            ModuloAberto.NOVO_TREINO, "", null, "Treino em Casa"),
    TREINO_APLICATIVOS_ATIVOS("Aplicativos Ativos", "treino/bi/alunos-app", "LoginControle.permissaoAcessoMenuVO.treinoRelatorioAplicativosAtivos",
            ModuloAberto.NOVO_TREINO, "", null, "Aplicativos Ativos"),
    TREINO_CARTEIRA("Indicadores da carteira dos professores", "treino/gestao/carteira-professores", "LoginControle.permissaoAcessoMenuVO.treinoRelatorioIndicadoresCarteiraProfessores",
            ModuloAberto.NOVO_TREINO, "", null, "Indicadores da Carteira dos Professores"),
    TREINO_ATIVIDADES_PROFESSORES("Indicadores das atividades dos professores", "treino/gestao/atividade-professores", "LoginControle.permissaoAcessoMenuVO.treinoRelatorioIndicadoresAtividadesProfessores",
            ModuloAberto.NOVO_TREINO, "", null, "Indicadores das Atividades dos Professores"),
    TREINO_PROFESSORES_ALUNOS_AVISO_MEDICO("Alunos com aviso médico", "treino/gestao/professores-alunos-aviso-medico", "LoginControle.permissaoAcessoMenuVO.treinoRelatorioProfessoresAlunosAvisosMedicos",
            ModuloAberto.NOVO_TREINO, "", null, "Alunos com aviso médico"),
    TREINO_PROFESSORES_SUBSTITUIDOS("Professores substituídos", "treino/gestao/professores-substituidos", "LoginControle.permissaoAcessoMenuVO.treinoRelatorioProfessoresSubstitutos",
            ModuloAberto.NOVO_TREINO, "", null, "Professores Substituídos"),
    TREINO_ANDAMENTO("Andamento", "treino/gestao/andamento-programas", "LoginControle.permissaoAcessoMenuVO.treinoRelatorioAndamento",
            ModuloAberto.NOVO_TREINO, "", null, "Andamento"),
    TREINO_PERSONAL("Personais", "treino/gestao/personais", "LoginControle.permissaoAcessoMenuVO.treinoPersonais",
            ModuloAberto.NOVO_TREINO, "", null, "Personais"),
    TREINO_GESTAO_CREDITOS("Gestão de créditos", "treino/gestao/gestao-credito", "LoginControle.permissaoAcessoMenuVO.treinoGestaoCreditos",
            ModuloAberto.NOVO_TREINO, "", null, "Gestão de Créditos"),
    TREINO_RANKING("Ranking", "treino/gestao/ranking-professores", "LoginControle.permissaoAcessoMenuVO.treinoRelatorioRanking",
            ModuloAberto.NOVO_TREINO, "", null, "Ranking"),
    TREINO_ACAMPANHAMENTO_PERSONAL("Acompanhamento de personal", "treino/gestao/acompanhamento-personal", "LoginControle.permissaoAcessoMenuVO.treinoAcompanhamentoPersonal",
            ModuloAberto.NOVO_TREINO, "", null, "Acompanhamento de Personal"),
    TREINO_COLABORADORES("Colaboradores (Gestão de personal)", "treino/gestao/colaboradores-personal", "LoginControle.permissaoAcessoMenuVO.treinoColaboradores",
            ModuloAberto.NOVO_TREINO, "", null),
    TREINO_APARELHOS("Aparelhos", "treino/cadastros/aparelhos", "LoginControle.permissaoAcessoMenuVO.treinoCadastroAparelhos",
            ModuloAberto.NOVO_TREINO, "", null, "Aparelhos"),
    TREINO_ATIVIDADE("Atividades", "treino/cadastros/atividades", "LoginControle.permissaoAcessoMenuVO.treinoCadastroAtividades",
            ModuloAberto.NOVO_TREINO, "", null, "Atividades"),
    TREINO_CATETORIA_ATIVIDADE("Categoria de Atividades", "treino/cadastros/categoria-atividades", "LoginControle.permissaoAcessoMenuVO.treinoCadastroCategoriaAtividades",
            ModuloAberto.NOVO_TREINO, "", null, "Categoria de Atividades"),
    TREINO_FICHAS_PREDEFINIDAS("Fichas Predefinidas", "treino/cadastros/fichas-predefinidas/list", "LoginControle.permissaoAcessoMenuVO.treinoCadastroFichasPredefinidas",
            ModuloAberto.NOVO_TREINO, "", null, "Fichas Prédefinidas"),
    TREINO_PROGRAMAS_PREDEFINIDOS("Programas predefinidos", "treino/cadastros/programas-predefinidos/list", "LoginControle.permissaoAcessoMenuVO.treinoCadastroProgramasPredefinidos",
            ModuloAberto.NOVO_TREINO, "", null, "Programas predefinidos"),
    TREINO_NIVEIS("Níveis", "treino/cadastros/niveis", "LoginControle.permissaoAcessoMenuVO.treinoCadastroNiveis",
            ModuloAberto.NOVO_TREINO, "", null, "Níveis"),
    TREINO_PRESCRICAO("Prescrição de treino", "treino/montagem-programa/prescricao", "LoginControle.permissaoAcessoMenuVO.treinoPrescricaoTreino",
            ModuloAberto.NOVO_TREINO, "", null, "Prescrição de treino"),
    TREINO_ALUNOS("Alunos (Treino)", "cadastros/alunos/listagem", "LoginControle.permissaoAcessoMenuVO.treinoCadastroAlunos",
            ModuloAberto.NOVO_TREINO, "", null),
    TREINO_CADASTRO_COLABORADORES("Colaboradores (Treino)", "cadastros/colaboradores", "LoginControle.permissaoAcessoMenuVO.treinoCadastroAlunos",
            ModuloAberto.NOVO_TREINO, "", null, "Colaboradores (Treino)"),
    TREINO_USUARIOS("Usuário (Treino)", "colaboradores", "LoginControle.permissaoAcessoMenuVO.treinoUsuarios",
            ModuloAberto.NOVO_TREINO, "", null, "Usuários (Treino)"),
    TREINO_PERFIL_ACESSO("Perfil de acesso (Treino)", "pessoas/perfil-acesso", "LoginControle.permissaoAcessoMenuVO.treinoPerfilAcesso",
            ModuloAberto.NOVO_TREINO, "", null, "Perfil de acesso (Treino)"),

    AGENDA_BI("BI Agenda", "agenda/painel/bi", "LoginControle.permissaoAcessoMenuVO.agendaBi",
            ModuloAberto.AGENDA, "", null, "BI Agenda"),
    AGENDA_TV_AULA("TV Aula", "tv-aula", "LoginControle.permissaoAcessoMenuVO.agendaTvAula",
            ModuloAberto.AGENDA, "", null, "TV Aula"),
    AGENDA_TV_GESTOR("TV Gestor", "tv-gestor", "LoginControle.permissaoAcessoMenuVO.agendaTvGestor",
            ModuloAberto.AGENDA, "", null, "TV Gestor"),
    AGENDA_AULAS("Agenda de aulas", "agenda/painel/turmas", "LoginControle.permissaoAcessoMenuVO.agendaAulas",
            ModuloAberto.AGENDA, "", null, "Agenda de aulas"),
    AGENDA_SERVICOS("Agenda de serviços", "agenda/painel/servicos/agendamentos", "LoginControle.permissaoAcessoMenuVO.agendaServicos",
            ModuloAberto.AGENDA, "", null, "Agenda de serviços"),
    AGENDA_AMBIENTE("Ambientes (Agenda)", "agenda/ambiente", "LoginControle.permissaoAcessoMenuVO.agendaAmbiente",
            ModuloAberto.AGENDA, "", null, "Ambientes (Agenda)"),
    AGENDA_MODALIDADE("Modalidades (Agenda)", "agenda/modalidade", "LoginControle.permissaoAcessoMenuVO.agendaModalidade",
            ModuloAberto.AGENDA, "", null, "Modalidades (Agenda)"),
    AGENDA_INDICADORES("Indicadores da Agenda", "agenda/indicadores-agenda", "LoginControle.permissaoAcessoMenuVO.agendaIndicadores",
            ModuloAberto.AGENDA, "", null, "Indicadores da Agenda"),
    AGENDA_AULA_EXCLUIDA("Aulas Excluídas", "agenda/aula-excluida", "LoginControle.permissaoAcessoMenuVO.agendaAulaExcluida",
            ModuloAberto.AGENDA, "", null, "Aulas Excluídas"),
    AGENDA_CONFIGURAR_AULAS("Configurar Aulas", "agenda/aula", "LoginControle.permissaoAcessoMenuVO.agendaConfigurarAula",
            ModuloAberto.AGENDA, "", null, "Configurar Aulas"),
    AGENDA_TIPOS_AGENDAMENTO("Tipos de Agendamentos", "agenda/tipo-agendamento", "LoginControle.permissaoAcessoMenuVO.agendaTipoAgendamento",
            ModuloAberto.AGENDA, "", null, "Tipos de Agendamentos"),
    AVALIACAO_FISICA_BI("BI Avaliação Física", "avaliacao/bi", "LoginControle.permissaoAcessoMenuVO.avaliacaoFisicaBI",
            ModuloAberto.AVALIACAO_FISICA, "", null, "BI Avaliação Fisica"),
    AVALIACAO_ANAMNESES("Anamneses", "avaliacao/anamneses", "LoginControle.permissaoAcessoMenuVO.avaliacaoFisicaCadastroAnamnese",
            ModuloAberto.AVALIACAO_FISICA, "", null, "Anamneses"),
    AVALIACAO_OBJETIVOS("Objetivos", "avaliacao/cadastros/objetivos", "LoginControle.permissaoAcessoMenuVO.avaliacaoFisicaCadastroObjetivos",
            ModuloAberto.AVALIACAO_FISICA, "", null, "Objetivos"),
    PACTOPAY_BI("BI PactoPay", "pactopay/dashboard", "LoginControle.permissaoAcessoMenuVO.pactoPayBI",
            ModuloAberto.PACTO_PAY, "", null, "BI Pactopay"),
    PACTOPAY_BI_REGUA_COBRANCA("BI Régua de Cobrança", "pactopay/regua/dashboard", "LoginControle.permissaoAcessoMenuVO.pactoPayReguaCobrancaDashboard",
            ModuloAberto.PACTO_PAY, "", null, "BI Régua de Cobrança"),
    PACTOPAY_CARTAO_CREDITO("Cartão de crédito", "pactopay/cartao", "LoginControle.permissaoAcessoMenuVO.pactoPayCartaoCredito",
            ModuloAberto.PACTO_PAY, "", null, "Cartão de Crédito"),
    PACTOPAY_PARCELAS_EM_ABERTO("Parcelas em aberto", "pactopay/parcelas", "LoginControle.permissaoAcessoMenuVO.pactoPayParcelasEmAberto",
            ModuloAberto.PACTO_PAY, "", null, "Parcelas em aberto"),
    PACTOPAY_REGUA_COBRANCA("Régua de cobrança", "pactopay/regua", "LoginControle.permissaoAcessoMenuVO.pactoPayReguaCobranca",
            ModuloAberto.PACTO_PAY, "", null, "Regua de Cobrança"),
    PACTOPAY_CONFIGURACAO_FASES("Configuração de fases", "pactopay/regua/configuracao-de-fases", "LoginControle.permissaoAcessoMenuVO.pactoPayReguaCobranca",
            ModuloAberto.PACTO_PAY, "", null, "Configuração de fases"),
    PACTOPAY_CONFIGURACAO_EMAIL("Configuração de email", "pactopay/regua/configuracao-de-email", "LoginControle.permissaoAcessoMenuVO.pactoPayReguaCobrancaConfigEmail",
            ModuloAberto.PACTO_PAY, "", null, "Configuração de email"),
    PACTOPAY_CARTAO_CREDITO_ONLINE("Transações Cartão de crédito", "pactopay/transacoes/credito-online", "LoginControle.permissaoAcessoMenuVO.pactoPayCreditoOnline",
            ModuloAberto.PACTO_PAY, "", null, "Transações Cartão de Crédito"),
    PACTOPAY_PIX("Pix", "pactopay/transacoes/pix", "LoginControle.permissaoAcessoMenuVO.pactoPayPix",
            ModuloAberto.PACTO_PAY, "", null, "Pix"),
    CROSS_BI("BI Cross", "cross/bi", "LoginControle.permissaoAcessoMenuVO.crossBi",
            ModuloAberto.CROSS, "", null, "BI Cross"),
    CROSS_WOD("Wod", "cross/cadastros/wods", "LoginControle.permissaoAcessoMenuVO.crossWod",
            ModuloAberto.CROSS, "", null, "WOD"),
    CROSS_MONITOR("Monitor", "cross/monitor/preview", "LoginControle.permissaoAcessoMenuVO.crossMonitor",
            ModuloAberto.CROSS, "", null, "Monitor"),
    CROSS_APARELHOS("Aparelhos", "cross/cadastros/aparelhos", "LoginControle.permissaoAcessoMenuVO.crossCadastroAparelhos",
            ModuloAberto.CROSS, "", null, "Aparelhos"),
    CROSS_ATIVIDADE("Atividades", "cross/cadastros/atividades-cross", "LoginControle.permissaoAcessoMenuVO.crossCadastroAtividades",
            ModuloAberto.CROSS, "", null, "Atividades"),
    CROSS_BENCHMARKS("Benchmarks", "cross/cadastros/benchmarks", "LoginControle.permissaoAcessoMenuVO.crossCadastroBenchmarks",
            ModuloAberto.CROSS, "", null, "Benchmarks"),
    CROSS_TIPOS_BENCHMARK("Tipos Benchmark", "cross/cadastros/tipos-benchmark", "LoginControle.permissaoAcessoMenuVO.crossCadastroTiposBenchmark",
            ModuloAberto.CROSS, "", null, "Tipos Benchmark"),
    CROSS_TIPO_WOD("Tipo Wod", "cross/cadastros/tipos-wod", "LoginControle.permissaoAcessoMenuVO.crossCadastroTipoWod",
            ModuloAberto.CROSS, "", null, "Tipo WOD"),
    GRADUACAO_AVALIACOES_PROGRESSO("Avaliações de Progresso", "graduacao/avaliacoes-progresso", "LoginControle.permissaoAcessoMenuVO.graduacaoAvaliacoesProgresso",
            ModuloAberto.GRADUACAO, "", null, "Avaliações de Progresso"),
    GRADUACAO_ATIVIDADES("Atividades", "graduacao/atividade", "LoginControle.permissaoAcessoMenuVO.graduacaoAtividades",
            ModuloAberto.GRADUACAO, "", null, "Atividades"),
    GRADUACAO_FICHA_TENICA("Fichas Técnicas", "graduacao/ficha", "LoginControle.permissaoAcessoMenuVO.graduacaoFichaTenica",
            ModuloAberto.GRADUACAO, "", null, "Fichas Técnicas"),
    METAS_DO_FINANCEIRO("Metas do Financeiro", "abrirPopup('[contexto]metasFinanceiroForm.jsp', 'MetasFinanceiro', 850, 670);", "LoginControle.permissaoAcessoMenuVO.cadastroMetas", ModuloAberto.ZILLYONWEB,
            "", "como-cadastrar-editar-metas-financeiras/"),
    PESSOAS_CLIENTES("Clientes (ADM)", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.cliente || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.PESSOAS, "", null),
    PESSOAS_INCLUIR_CLEINTE("Incluir cliente", "semValor",
            "(LoginControle.permissaoAcessoMenuVO.cliente || LoginControle.usuarioLogado.administrador)",
            ModuloAberto.PESSOAS, "", null),
    TOKENS_OPERACOES("Relatório de Tokens",
            "abrirPopup('[contexto]relatorioTokensOperacoes.jsp','TokensOperacores',780,750);",
            "true",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:TokensOperacoes", "relatorio-tokens-operacoes/"),
    PERFIL_ACESSO_UNIFICADO("Perfil de Acesso (Unificado)", "perfil-acesso-unificado",
            "LoginControle.permissaoAcessoMenuVO.perfilAcessoUnificado",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Cadastros:Acesso_ao_sistema:Perfil_Acesso",
            "quais-sao-as-permissoes-do-perfil-de-acesso-do-modulo-adm/"),

    CONTATO_AVULSO("Contato Avulso", "abrirPopup('[contexto]newRealizarContatoForm.jsp', 'Realizar Contato Avulso', 850, 700);",
            "false",
            ModuloAberto.CRMWEB, "CRMWeb:ContatoAvulso", null),

    BOLETIM_VISITA_CLIENTE("Question\\u00E1rio Cliente", "abrirPopup('[contexto]questionarioClienteForm.jsp','QuestionarioCliente',1000,650);",
            "false",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Inicial:Questionario_Cliente", null),
    CLUBE_DE_BENEFICIOS("Clube de benefícios", "",
            "false",
            ModuloAberto.ZILLYONWEB, "", null),
    SOCIAL_MAILING("Social Mailing", "abrirPopup('[contexto]socialMailing.jsp', 'Social Mailing', 725, 620);",
            "LoginControle.permissaoAcessoMenuVO.permissaoAcessarSocialMailing",
            ModuloAberto.ZILLYONWEB, "CRMWeb:ContatoAvulso", null),

    AJUSTE_SALDO_CONTA_CORRENTE("Ajustes Saldo Conta Corrente",
            "abrirPopup('[contexto]ajustesSaldoContaCorrente.jsp','ContaCorrente',1000,650);",
            "false",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Conta_Corrente", null),

    AJUSTE_PONTUACAO_CONTA_CORRENTE("Ajustes Saldo Conta Corrente",
            "abrirPopup('[contexto]ajustePontosCliente.jsp','ContaCorrente',1000,650);",
            "false",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Conta_Corrente", null),
    PACTOPAY_COMUNICACAO("Régua de Cobrança - Comunicações",
            "abrirPopupMaximizada('[contexto]reguaCobrancaComunicacao.jsp','PedidosPinpad',980,700);",
            "LoginControle.usuarioLogado.usuarioPactoSolucoes",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Relatorios:Regua_Cobranca_Comunicados", "integracao-com-stone-connect/"),
    ATUALIZAR_BV("Atualizar Boletim Visita",
            "semValor",
            "false",
            ModuloAberto.ZILLYONWEB, "ZillyonWeb:Cadastros:Config._Financeiras:Conta_Corrente", null),
    CLIENTES_COM_RESTRICOES("Clientes com restrições", "semValor",
            "LoginControle.empresaLogado.utilizaGestaoClientesComRestricoes && (LoginControle.empresaLogadaIsFranqueadora || LoginControle.empresaLogadaNaoPossuiFranqueadora)",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Clientes_com_restricoes", null, "Clientes com Restrições"),
    LOCAL_IMPRESSAO("Local de impressão", "abrirPopup('[contexto]localImpressaoCons.jsp','LocalImpressao',1000,650);",
            "LoginControle.empresaLogado.utilizarPactoPrint",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Local_imporessao", null, "Local de impressão"),
    SOLICITACAO_COMPRA("Solicitação de Compra", "/adm/solicitacao-compra/",
            "false",
            ModuloAberto.ZILLYONWEB,
            "", null, "Solicitação de Compra"),
    SMD_RELATORIO("Relat\u00F3rio SMD",
            "",
            "LoginControle.permissaoAcessoMenuVO.permiteVisualizaSMDPeriodo",
            ModuloAberto.ZILLYONWEB,
            "ZillyonWeb:Inicial:Recurso_SMD", null, "SMD"),
    ;

    private String descricao;
    private ModuloAberto modulo;
    private String url;
    private String expressaoRenderizar;
    private String[] palavrasChaves;
    private String wiki;
    private String baseConhecimento;
    private String descricaoTruncada;
    private boolean existeWiki;
    private String[] subFuncionalidadesNomes = new String[] {};
    private String tipo;
    private List<FuncionalidadeSistemaEnum> subFuncionalidades = new ArrayList<>();
    private String reRenderElement = "none";
    private boolean renderizar = true;
    private String descricaoMenulateral = "";
    private boolean novidade = false;

    FuncionalidadeSistemaEnum(String descricao, String url, String expressaoRenderizar,
                              ModuloAberto modulo, String wiki, String baseConhecimento, boolean novidade) {
        this(descricao, url, expressaoRenderizar, modulo, wiki, baseConhecimento);
        this.novidade = novidade;
    }

    FuncionalidadeSistemaEnum(String descricao, String url, String expressaoRenderizar,
                              ModuloAberto modulo, String wiki, String baseConhecimento) {

        this.descricao = descricao;
        this.url = url;
        this.modulo = modulo;
        this.expressaoRenderizar = expressaoRenderizar;
        this.palavrasChaves = palavrasChaves;
        this.wiki = wiki;
        this.baseConhecimento = baseConhecimento;
        this.descricaoTruncada = this.descricao.length() > 23 ? this.descricao.substring(0, 23) + "..." : this.descricao;
        this.existeWiki = this.wiki.length() > 0;
        this.tipo = "menulink";
    }

    FuncionalidadeSistemaEnum(String descricao, String url, String expressaoRenderizar, ModuloAberto modulo) {
        this.descricao = descricao;
        this.url = url;
        this.modulo = modulo;
        this.expressaoRenderizar = expressaoRenderizar;
    }

    FuncionalidadeSistemaEnum(String descricao, String url, String expressaoRenderizar,
                              ModuloAberto modulo, String wiki, String baseConhecimento, String[] subFun) {

        this(descricao, url, expressaoRenderizar, modulo, wiki, baseConhecimento);
        this.subFuncionalidadesNomes = subFun;
        this.tipo = subFun.length > 0 ? "menudropdown" : "menulink";
    }

    FuncionalidadeSistemaEnum(String descricao, String url, String expressaoRenderizar,
                              ModuloAberto modulo, String wiki, String baseConhecimento, String[] subFun, String reRenderElement) {

        this(descricao, url, expressaoRenderizar,  modulo, wiki, baseConhecimento, subFun);
        this.reRenderElement = reRenderElement;
    }

    FuncionalidadeSistemaEnum(String descricao, String url, String expressaoRenderizar,
                              ModuloAberto modulo, String wiki, String baseConhecimento, String descricaoMenulateral) {

        this(descricao, url, expressaoRenderizar,  modulo, wiki, baseConhecimento);
        this.descricaoMenulateral = descricaoMenulateral;
    }

    public static FuncionalidadeSistemaEnum obterPorNome(final String nome) {
        for (FuncionalidadeSistemaEnum i : FuncionalidadeSistemaEnum.values()) {
            if (i.name().equals(nome)) {
                return i;
            }
        }
        return null;
    }

    public void prepararSubFuncionalidade() {
        this.subFuncionalidades = new ArrayList<>();
        for (String nome : this.subFuncionalidadesNomes) {
            this.subFuncionalidades.add(obterPorNome(nome));
        }
    }

    public String getDescricao() {
        return descricao;
    }

    public String getIconeModulo() {
        return "/faces/beta/imagens/"+modulo.getIconeModulo();
    }

    public String[] getPalavrasChaves() {
        return palavrasChaves;
    }

    public ModuloAberto getModulo() {
        return modulo;
    }

    public String getExpressaoRenderizar() {
        return expressaoRenderizar;
    }

    public String getUrl() {
        return url;
    }

    public String getName() {
        return name();
    }

    public String getWiki() {
        if (baseConhecimento != null) {
            return baseConhecimento;
        }
        return wiki;
    }

    public String getDescricaoTruncada() {
        return descricaoTruncada;
    }

    public boolean isExisteWiki() {
        return existeWiki;
    }

    public String[] getSubFuncionalidadesNomes() {
        return subFuncionalidadesNomes;
    }

    public List<FuncionalidadeSistemaEnum> getSubFuncionalidades() {
        return subFuncionalidades;
    }

    public String getReRenderElement() {
        return reRenderElement;
    }

    public boolean isRenderizar() {
        return renderizar;
    }

    public void setRenderizar(boolean renderizar){
        this.renderizar = renderizar;
    }

    public String getDescricaoMenulateral() {
        if(descricaoMenulateral == null || descricaoMenulateral.trim().isEmpty()){
            return descricao;
        }
        return descricaoMenulateral;
    }

    public void setDescricaoMenulateral(final String descricaoMenulateral) {
        this.descricaoMenulateral = descricaoMenulateral;
    }

    public String getDescricaoTruncadaMenuLateral() {
        return descricaoMenulateral.length() > 0 && descricaoMenulateral.length() <= 23 ? descricaoMenulateral : descricaoTruncada;
    }

    public String getTipo() {
        return tipo;
    }

    public boolean isNovidade() {
        return novidade;
    }

    public String getBaseConhecimento() {
        return baseConhecimento;
    }
}
