package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.enumeradores.TipoVenda;
import br.com.pactosolucoes.enumeradores.UsoCreditoPersonalEnum;
import br.com.pactosolucoes.estudio.modelo.AgendaVO;
import br.com.pactosolucoes.estudio.modelo.DisponibilidadeVO;
import br.com.pactosolucoes.estudio.modelo.PacoteProdutoVO;
import controle.arquitetura.MenuControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import controle.basico.ColaboradorControle;
import controle.contrato.ContratoControle;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.PacotePersonalVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.plano.enumerador.UnidadeMedidaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.integracao.impl.integracaoSistema.IntegracaoSistemaService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas vendaAvulsaForm.jsp vendaAvulsaCons.jsp) com as funcionalidades da
 * classe
 * <code>VendaAvulsa</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see VendaAvulsa
 * @see VendaAvulsaVO
 */
public class VendaAvulsaControle extends SuperControle {

    private VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
    private String campoConsultarCliente;
    private String valorConsultarCliente;
    private List listaConsultarTabelaDesconto;
    private List listaConsultarCliente;
    private String campoConsultarColaborador;
    private String valorConsultarColaborador;
    private List listaConsultarColaborador;
    private String onCompleteDesconto = "";
    /**
     * Interface
     * <code>VendaAvulsaInterfaceFacade</code> responsável pela interconexão da
     * camada de controle com a camada de negócio. Criando uma independência da
     * camada de controle com relação a tenologia de persistência dos dados
     *AulaAvulsaDiariaControle
     */
    private ItemVendaAvulsaVO itemVendaAvulsaVO;
    private String campoConsultarProduto;
    private String valorConsultarProduto;
    private List listaConsultarProduto;
    private List listaSelectItemEmpresa;
    private Boolean abrirRichModalMensagem;
    private Boolean abrirRichConfimacaoPagamento;
    private boolean editarData = false;
    private boolean botaoEditarData = false;
    private String onCompleteValidarData = "";
    private Date dataNova;
    private java.util.Date dataIniAgendar;
    private java.util.Date dataFimAgendar;
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private boolean alterarTipoVenda = false;
    private String tipoVenda = TipoVenda.NORMAL.getSigla();
    private List<SelectItem> listaTipoVenda = new ArrayList<SelectItem>();
    private String onCompleteSuggestionProduto;
    private String onCompleteSuggestionCliente;
    private String onCompleteCadastrarCliente;
    private boolean utilizaLeitorCodigoBarras = false;
    private String clienteCadastrar;
    private String clienteCadastrarCelular;
    private String clienteCadastrarResidencial;
    private String clienteCadastrarEmail;


    public VendaAvulsaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        montarListaSelectItemTipoVenda();
        setConfiguracaoSistemaVO(getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS));
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public void inicializarUsuarioLogado() throws Exception {
        vendaAvulsaVO.getResponsavel().setCodigo(getUsuarioLogado().getCodigo());
        vendaAvulsaVO.getResponsavel().setNome(getUsuarioLogado().getNome());
        vendaAvulsaVO.getResponsavel().setUsername(getUsuarioLogado().getUsername());
    }

    public void inicializarEmpresaLogado() {
        try {
            setOnCompleteSuggestionCliente("");
            setOnCompleteSuggestionProduto("");
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            setUtilizaLeitorCodigoBarras(empresaVO.isUtilizaLeitorCodigoBarras());
            getVendaAvulsaVO().setEmpresa(empresaVO);
        } catch (Exception ignored) {
        }
    }
    public void prepare(ActionEvent evt) {
        setNavigationCase(tratarNavigationCaseIntegracaoModulo("vendaAvulsa", "modulo_venda_avulsa", evt));
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>VendaAvulsa</code> para edição pelo usuário da aplicação.
    */
    public String novo() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.VENDA_AVULSA_PRODUTO_SERVICO);
        dataIniAgendar = null;
        dataFimAgendar = null;
        novoSemComprador();
        validarComprador();
        setarModuloAberto(ModuloAberto.ZILLYONWEB);
        return getNavigationCase();
    }

    public void usarNovaVendaAvulsa() {
        try {
            limparMsg();
            setMsgAlert("");
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            getFacade().getUsuario().gravarRecurso(TipoInfoMigracaoEnum.VENDA_AVULSA, getUsuarioLogado().getCodigo(), "true", getUsuarioLogado());
            notificarRecursoEmpresa(RecursoSistema.PADRAO_NOVA_VENDA_AVULSA);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String openWindow = "window.open('"
                    + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_ZW.getSiglaModulo())
                    + "&redirect=/adm/venda-avulsa', '_self')";
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public String novoSemComprador() throws Exception {
        setVendaAvulsaVO(new VendaAvulsaVO());
        setItemVendaAvulsaVO(new ItemVendaAvulsaVO());
        setListaConsultarTabelaDesconto(new ArrayList());
        setAbrirRichModalMensagem(false);
        setAbrirRichConfimacaoPagamento(false);
        getVendaAvulsaVO().setTipoComprador("CI");
        getVendaAvulsaVO().apresentarComprador();
        inicializarUsuarioLogado();
        inicializarEmpresaLogado();
        if (getVendaAvulsaVO().getEmpresa().getCodigo() == 0) {
            inicializarListasSelectItemTodosComboBox();
            getVendaAvulsaVO().setApresentarEmpresa(true);
        }
        FacesContext facesContext = FacesContext.getCurrentInstance();
        HttpSession session = (HttpSession) facesContext.getExternalContext().getSession(true);
        session.removeAttribute("listaDisponibilidade");
        setMensagemID("msg_entre_dados");
        setEditarData(false);
        setBotaoEditarData(false);
        return getNavigationCase();
    }

    public void carregaDados(ClienteVO clienteVO)throws Exception{
        if(clienteVO != null){
            getVendaAvulsaVO().setCliente(clienteVO);
            getVendaAvulsaVO().setApresentarColaborador(false);
            getVendaAvulsaVO().setApresentarCliente(true);
            getVendaAvulsaVO().setApresentarConsumidor(false);
            getVendaAvulsaVO().setTipoComprador("CI");
            getVendaAvulsaVO().setNomeComprador(clienteVO.getPessoa().getNome());
            getVendaAvulsaVO().setColaborador(new ColaboradorVO());
            getVendaAvulsaVO().setApresentarParcelamento(true);
            getVendaAvulsaVO().setNrVezesParcelamento(1);
            getVendaAvulsaVO().setVencimentoPrimeiraParcela(Calendario.hoje());
            return;
        }

    }

    public void validarComprador() throws Exception{
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle != null) {
            clienteControle.pegarClienteTelaCliente();
            getVendaAvulsaVO().setCliente(clienteControle.getClienteVO());
            getVendaAvulsaVO().setApresentarColaborador(false);
            getVendaAvulsaVO().setApresentarCliente(true);
            getVendaAvulsaVO().setApresentarConsumidor(false);
            getVendaAvulsaVO().setTipoComprador("CI");
            getVendaAvulsaVO().setNomeComprador(clienteControle.getClienteVO().getPessoa().getNome());
            getVendaAvulsaVO().setColaborador(new ColaboradorVO());
            getVendaAvulsaVO().setApresentarParcelamento(true);
            getVendaAvulsaVO().setNrVezesParcelamento(1);
            getVendaAvulsaVO().setVencimentoPrimeiraParcela(Calendario.hoje());
            return;
        }
        ColaboradorControle colaboradorControle = (ColaboradorControle) context().getExternalContext().getSessionMap().get("ColaboradorControle");
        if (colaboradorControle != null) {
            getVendaAvulsaVO().setColaborador(colaboradorControle.getColaboradorVO());
            getVendaAvulsaVO().setApresentarColaborador(true);
            getVendaAvulsaVO().setApresentarCliente(false);
            getVendaAvulsaVO().setApresentarConsumidor(false);
            getVendaAvulsaVO().setTipoComprador("CO");
            getVendaAvulsaVO().setCliente(new ClienteVO());
            getVendaAvulsaVO().setNomeComprador(colaboradorControle.getColaboradorVO().getPessoa().getNome());
            getVendaAvulsaVO().setApresentarParcelamento(true);
            getVendaAvulsaVO().setNrVezesParcelamento(1);
            getVendaAvulsaVO().setVencimentoPrimeiraParcela(Calendario.hoje());
            return;
        }
        if (clienteControle == null && colaboradorControle == null) {
            getVendaAvulsaVO().setApresentarConsumidor(true);
            getVendaAvulsaVO().setApresentarCliente(false);
            getVendaAvulsaVO().setApresentarColaborador(false);
            getVendaAvulsaVO().setCliente(new ClienteVO());
            getVendaAvulsaVO().setColaborador(new ColaboradorVO());
            getVendaAvulsaVO().setNomeComprador("");
            getVendaAvulsaVO().setTipoComprador("CN");
            getVendaAvulsaVO().setApresentarParcelamento(false);
            getVendaAvulsaVO().setNrVezesParcelamento(1);
            getVendaAvulsaVO().setVencimentoPrimeiraParcela(Calendario.hoje());
        }
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>VendaAvulsa</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        VendaAvulsaVO obj = (VendaAvulsaVO) context().getExternalContext().getRequestMap().get("vendaAvulsa");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(false);
        setVendaAvulsaVO(obj);
        setItemVendaAvulsaVO(new ItemVendaAvulsaVO());
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    public void editarCampoData() throws Exception {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                getItemVendaAvulsaVO().setResponsavelAutorizacaoDesconto((UsuarioVO) auto.getUsuario().getClone(true));
                setEditarData(true);
                setDataNova(getVendaAvulsaVO().getDataRegistro());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {

            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        limparMsg();
        auto.autorizar("Confirmação de Data Venda Avulsa", "DataVendaAvulsa",
                "Você precisa da permissão \"2.39 - Alterar data de Venda Avulsa\"",
                "vendaAvulsaContainer", listener);
    }

    public void aplicarData() throws Exception {
        if (dataNova == null) {
            setMensagem(getMensagemInternalizacao("msg_datavendaavulsa"));
        } else {
            setEditarData(false);
            getVendaAvulsaVO().setDataRegistro(getDataNova());
            getVendaAvulsaVO().setVencimentoPrimeiraParcela(getDataNova());
        }

    }

    public void limparAlterarData() throws Exception {
        setEditarData(false);
    }

    public void validarModalAbrir() throws Exception {
        if(getBotaoEditarData()) {
            abrirModalEditarDataVendaAvulsa();
        } else {
            editarCampoDescontoProduto();
        }
    }

    public void abrirModalEditarDataVendaAvulsa() throws Exception {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                getItemVendaAvulsaVO().setResponsavelAutorizacaoDesconto((UsuarioVO) auto.getUsuario().getClone(true));
                setEditarData(true);
                setDataNova(getVendaAvulsaVO().getDataRegistro());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {

            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        limparMsg();
        auto.autorizar("Confirmação de Data Venda Avulsa", "DataVendaAvulsa",
                "Você precisa da permissão \"2.39 - Alterar data de Venda Avulsa\"",
                "form:vendaAvulsaContainer", listener);
    }

    public void validarCampoDescontoProduto() throws Exception {
        limparMsg();
        setBotaoEditarData(false);
        if (UteisValidacao.emptyNumber(getItemVendaAvulsaVO().getProduto().getCodigo())) {
            throw new Exception("Escolha primeiro o produto");
        }
    }

    public void editarCampoDescontoProduto() throws Exception {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().setCodigo(auto.getUsuario().getCodigo());
                getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().setNome(auto.getUsuario().getNome());
                getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().setUsername(auto.getUsuario().getUsername());

                getItemVendaAvulsaVO().setDescontoManual(true);
                getItemVendaAvulsaVO().setApresentarDescontoManual(true);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        try {
            validarCampoDescontoProduto();
            auto.autorizar("Confirmação de Desconto Venda Avulsa", "DescontoVendaAvulsa",
                    "Você precisa da permissão \"2.38 - Desconto em produto de Venda Avulsa\"",
                    "form:vendaAvulsaContainer", listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>VendaAvulsaVO</code>. Esta inicialização é necessária por exigência
     * da tecnologia JSF, que não trabalha com valores nulos para estes
     * atributos.
     */
    public void inicializarAtributosRelacionados(VendaAvulsaVO obj) {
        if (obj.getCliente() == null) {
            obj.setCliente(new ClienteVO());
        }
        if (obj.getColaborador() == null) {
            obj.setColaborador(new ColaboradorVO());
        }
    }
    public void validarDataAgendamento() throws Exception{
        if((dataFimAgendar != null && dataIniAgendar!=null) && Calendario.menor(dataFimAgendar,dataIniAgendar)){
            throw new Exception("A data de início agendamento não poder ser maior que a data fim.");
        }
        if((dataFimAgendar == null && dataIniAgendar!=null) ){
            throw new Exception("A informe a data prazo fim para agendamento.");
        }
        if((dataFimAgendar != null && dataIniAgendar==null) ){
            throw new Exception("A informe a data prazo inicio para agendamento.");
        }
    }
    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe
     * <code>VendaAvulsa</code>. Caso o objeto seja novo (ainda não gravado no
     * BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>. Se houver alguma inconsistência o objeto não é
     * gravado, sendo re-apresentado para o usuário juntamente com uma mensagem
     * de erro.
     */
    public void gravar() throws Exception {
        try {
            context().getExternalContext().getRequestMap().put("responsavelOperacao", this.vendaAvulsaVO.getResponsavel());
            validarDataAgendamento();
            if (vendaAvulsaVO.isNovoObj()) {
                FacesContext facesContext = FacesContext.getCurrentInstance();
                HttpSession session = (HttpSession) facesContext.getExternalContext().getSession(true);
                Object agenda = session.getAttribute("listaDisponibilidade");
                List<DisponibilidadeVO> listaDisponibilidade;
                if (agenda == null) {
                    List<AgendaVO> listaAFaturar = getFacade().getAgendaEstudio().listarAFaturarCliente(vendaAvulsaVO.getCliente().getCodigo(), getEmpresaLogado().getCodigo());
                    listaDisponibilidade = new ArrayList<DisponibilidadeVO>();
                    for (AgendaVO agendaVO : listaAFaturar) {
                        DisponibilidadeVO dis = new DisponibilidadeVO();
                        dis.setVerificadorAgendamento(Boolean.FALSE);
                        dis.setProdutoVO(agendaVO.getProdutoVO());
                        dis.setIdAgenda(agendaVO.getCodigo());
                        listaDisponibilidade.add(dis);
                    }
                } else {
                    listaDisponibilidade = (List<DisponibilidadeVO>) agenda;
                }
                if (!vendaAvulsaVO.getTipoComprador().equals("CI")) {
                    for (ItemVendaAvulsaVO itemVendaAvulsaVO1 : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
                        if (itemVendaAvulsaVO1.getProduto().getTipoProduto().equals("SS")) {
                            throw new Exception("Produtos do tipo sessão não podem ser vendidos para pessoas do tipo "+vendaAvulsaVO.getTipoComprador_Apresentar());
                        }
                    }
                }
                Integer idVendaAvulsa = getFacade().getVendaAvulsa().incluir(vendaAvulsaVO, false, listaDisponibilidade, dataIniAgendar, dataFimAgendar);
                session.removeAttribute("listaDisponibilidade");

                if (!UteisValidacao.emptyNumber(idVendaAvulsa)) {
                    getFacade().getZWFacade().notificarVendaAvulsa(vendaAvulsaVO, vendaAvulsaVO.getResponsavel());
                }
                    
                //LOG - INICIO
                try {
                    vendaAvulsaVO.setObjetoVOAntesAlteracao(new VendaAvulsaVO());
                    vendaAvulsaVO.setNovoObj(true);
                    if (vendaAvulsaVO.getTipoComprador().equals("CI")) {
                        registrarLogObjetoVO(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", vendaAvulsaVO.getCliente().getPessoa().getCodigo());
                    } else if (vendaAvulsaVO.getTipoComprador().equals("CO")) {
                        registrarLogObjetoVO(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", vendaAvulsaVO.getColaborador().getPessoa().getCodigo());
                    } else {
                        registrarLogObjetoVO(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", 0);
                    }
                 } catch (Exception e) {
                    e.printStackTrace();
                }    

                    
            } else {
                VendaAvulsaVO auxVendaAvulsaVO = getFacade().getVendaAvulsa().consultarPorChavePrimaria(vendaAvulsaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getFacade().getVendaAvulsa().alterar(vendaAvulsaVO);
                //LOG - INICIO
                try {
                    if (auxVendaAvulsaVO != null) {
                        vendaAvulsaVO.setObjetoVOAntesAlteracao(auxVendaAvulsaVO);
                        if (vendaAvulsaVO.getTipoComprador().equals("CI")) {
                            registrarLogObjetoVO(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", vendaAvulsaVO.getCliente().getPessoa().getCodigo());
                        } else if (vendaAvulsaVO.getTipoComprador().equals("CO")) {
                            registrarLogObjetoVO(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", vendaAvulsaVO.getColaborador().getPessoa().getCodigo());
                        } else {
                            registrarLogObjetoVO(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", 0);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //LOG - FIM
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            throw e;
        }
    }
    
    private void gravarPontuacaoCliente() throws Exception{
        CampanhaDuracaoVO maiorCampanhaAtiva = getFacade().getCampanhaDuracao().campanhaVigenteMultiplicador(Calendario.hoje(), TipoItemCampanhaEnum.PRODUTO, vendaAvulsaVO.getEmpresa().getCodigo());
        if (vendaAvulsaVO.getEmpresa().isTrabalharComPontuacao() && vendaAvulsaVO.getTipoComprador().equals("CI") &&
                (!vendaAvulsaVO.getEmpresa().isPontuarApenasCategoriasEmCampanhasAtivas() ||
                        (vendaAvulsaVO.getEmpresa().isPontuarApenasCategoriasEmCampanhasAtivas() && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())))) {
            for (ItemVendaAvulsaVO itemVendaAvulsaVO1 : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
                HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
                historicoPontos.setCliente(vendaAvulsaVO.getCliente());
                Integer totalPontos = (maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * itemVendaAvulsaVO1.getProduto().getPontos() : itemVendaAvulsaVO1.getProduto().getPontos());
                totalPontos = totalPontos * itemVendaAvulsaVO1.getQuantidade();
                historicoPontos.setDescricao("Venda Avulsa (Produto) - " + itemVendaAvulsaVO1.getProduto().getDescricao() + " Pontos: " + totalPontos + maiorCampanhaAtiva.getTextoCampanhaApresentar() + ";");
                historicoPontos.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
                historicoPontos.setPontos(totalPontos);
                historicoPontos.setTipoPonto(TipoItemCampanhaEnum.PRODUTO);
                historicoPontos.setDataConfirmacao(Calendario.hoje());
                historicoPontos.setDataaula(Calendario.hoje());
                historicoPontos.setEntrada(true);
                historicoPontos.setProduto(itemVendaAvulsaVO1.getProduto().getCodigo());
                historicoPontos.setCodigoVenda(vendaAvulsaVO.getCodigo());
                if (historicoPontos.getPontos() > 0) {
                    getFacade().getHistoricoPontos().incluir(historicoPontos);
                }
            }
        }
   }
   public Boolean getExisteItemVendaSessao(){
       for(ItemVendaAvulsaVO item : vendaAvulsaVO.getItemVendaAvulsaVOs()){
           if(item.getProduto().getTipoProduto().equals("SS") && !UteisValidacao.emptyNumber(item.getPacoteVO().getCodigo())){
               return true;
           }
       }
       return false;
   }
    private void inicializarCaixa() throws Exception {
        MovParcelaControle mpc = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        if (mpc == null) {
            mpc = new MovParcelaControle(vendaAvulsaVO.getNomeComprador());
        } else {
            mpc.liberarBackingBeanMemoria("MovParcelaControle");
            mpc.setValorConsulta(vendaAvulsaVO.getNomeComprador());
            mpc.novo(false, true);
        }
        context().getExternalContext().getSessionMap().put("MovParcelaControle", mpc);
    }

    public void validarVendaProdutoSemEstoque(VendaAvulsaVO venda) throws Exception {
        if (venda.getEmpresa().getSomenteVendaProdutosComEstoque()) {
            for (ItemVendaAvulsaVO item : venda.getItemVendaAvulsaVOs()) {
                ProdutoEstoqueVO prod = getFacade().getProdutoEstoque()
                        .consultarPorProduto(item.getProduto().getCodigo(), venda.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (prod != null && (prod.getEstoque() <= 0 || (item.getQuantidade() > prod.getEstoque()))) {
                    LoginControle loginControle = (LoginControle) getControlador(LoginControle.class);
                    if (loginControle.getPermissaoAcessoMenuVO().getVisualizarPosicaoEstoque()) {
                        throw new ConsistirException("Não é possível vender o produto " + item.getProduto().getDescricao() + " pois seu estoque atual é " + prod.getEstoque() + ".");
                    } else {
                        throw new ConsistirException("Não é possível vender o produto, estoque insuficiente.");
                    }
                }
            }
        }
    }

    public void validarDadosVendaAvulsaReceber() throws ConsistirException {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                UsuarioVO usuario = auto.getUsuario();
                usuario.setUserOamd(auto.getUsuario().getUserOamd());
                vendaAvulsaVO.setResponsavel(usuario);
                gravar();
                gravarPontuacaoCliente();
                limparControladoresVenda();
                setMensagemDetalhada("");
                setMensagem("");
                setProcessandoOperacao(false);
                if (vendaAvulsaVO.getValorTotal() == 0) {
                    novo();
                    montarSucessoGrowl("Venda realizada com SUCESSO!");
                    setPaginaDestino("vendaAvulsa");
                } else {
                    setPaginaDestino("pagamento");
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        limparMsg();
        try {
            validarDadosVendaAvulsa(false);
            auto.autorizar("Confirmação de Venda", "CaixaEmAberto",
                    "Você precisa da permissão \"2.44 - Operação - Caixa em Aberto\"",
                    "form:vendaAvulsaContainer", listener);
        } catch (Exception e) {
            setAbrirRichConfimacaoPagamento(false);
            setProcessandoOperacao(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
        }
    }

    public void validarDadosVendaAvulsaConfirmar() throws ConsistirException {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                UsuarioVO usuario = auto.getUsuario();
                usuario.setUserOamd(auto.getUsuario().getUserOamd());
                vendaAvulsaVO.setResponsavel(usuario);
                gravar();
                gravarPontuacaoCliente();
                setMensagemDetalhada("", "");
                ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
                if (contratoControle != null) {
                    contratoControle.liberarBackingBeanMemoria("ContratoControle");
                    contratoControle = null;
                }
                MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
                if (movParcelaControle != null) {
                    movParcelaControle.liberarBackingBeanMemoria("MovParcelaControle");
                }
                setMensagem("");
                setMensagemDetalhada("");
                setProcessandoOperacao(false);
                if (vendaAvulsaVO.getValorTotal() == 0.0) {
                    novo();
                    montarSucessoGrowl("Venda realizada com SUCESSO!");
                    setPaginaDestino("vendaAvulsa");
                } else {
                    inicializarCaixa();
                    setPaginaDestino("tela8");
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        limparMsg();
        try {
            validarDadosVendaAvulsa(true);
            auto.autorizar("Confirmação de Caixa em Aberto", "CaixaEmAberto",
                    "Você precisa da permissão \"2.44 - Operação - Caixa em Aberto\"",
                    "form:vendaAvulsaContainer", listener);
        } catch (Exception e) {
            setProcessandoOperacao(false);
            setAbrirRichConfimacaoPagamento(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
        }
    }

    private void validarDadosVendaAvulsa(Boolean confirmar) throws Exception {
        VendaAvulsaVO.validarDados(vendaAvulsaVO);

        if (confirmar != null && confirmar) {

            PessoaVO pessoaVO = null;
            if (vendaAvulsaVO.getCliente() != null &&
                    vendaAvulsaVO.getCliente().getPessoa() != null &&
                    !UteisValidacao.emptyNumber(vendaAvulsaVO.getCliente().getPessoa().getCodigo())) {
                pessoaVO = vendaAvulsaVO.getCliente().getPessoa();
            } else if (vendaAvulsaVO.getColaborador() != null &&
                    vendaAvulsaVO.getColaborador().getPessoa() != null &&
                    !UteisValidacao.emptyNumber(vendaAvulsaVO.getColaborador().getPessoa().getCodigo())) {
                pessoaVO = vendaAvulsaVO.getColaborador().getPessoa();
            }

            if (pessoaVO != null && vendaAvulsaVO.getEmpresa() != null) {

                Double valorLimiteCaixaAbertoVendaAvulsa = pessoaVO.getValorLimiteCaixaAbertoVendaAvulsa();
                if (UteisValidacao.emptyNumber(valorLimiteCaixaAbertoVendaAvulsa)) {
                    valorLimiteCaixaAbertoVendaAvulsa = vendaAvulsaVO.getEmpresa().getValorLimiteCaixaAbertoVendaAvulsa();
                }

                if (!UteisValidacao.emptyNumber(valorLimiteCaixaAbertoVendaAvulsa)) {

                    if (vendaAvulsaVO.getValorTotal() > valorLimiteCaixaAbertoVendaAvulsa) {
                        throw new Exception("O limite máximo para deixar no caixa em aberto é " + Formatador.formatarValorMonetario(valorLimiteCaixaAbertoVendaAvulsa) + ".");
                    }

                    List<MovParcelaVO> listaParcelas = getFacade().getMovParcela().consultarParcelasEmAbertoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    Double valorTotalEmAberto = 0.0;
                    for (MovParcelaVO movParcelaVO : listaParcelas) {
                        valorTotalEmAberto += movParcelaVO.getValorParcela();
                    }

                    if ((valorTotalEmAberto > valorLimiteCaixaAbertoVendaAvulsa) ||
                            ((valorTotalEmAberto + vendaAvulsaVO.getValorTotal()) > valorLimiteCaixaAbertoVendaAvulsa)) {
                        throw new Exception("Cliente já tem " + Formatador.formatarValorMonetario(valorTotalEmAberto) + " em aberto. O limite máximo para deixar no caixa em aberto é " +
                                Formatador.formatarValorMonetario(valorLimiteCaixaAbertoVendaAvulsa) + ".");
                    }
                }
            }
        }

        validarVendaProdutoSemEstoque(vendaAvulsaVO);
        for (ItemVendaAvulsaVO item : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
            if (!vendaAvulsaVO.getTipoComprador().equals("CO") && item.getProduto().getTipoProduto().equals("CP")) {
                throw new Exception(getMensagemInternalizacao("produto_credito_personal_somente_colaborador"));
            }
        }
        setMensagem("");
        setMensagemDetalhada("");
//            setAbrirRichConfimacaoPagamento(true);
    }

    /**
     * <AUTHOR> Alcides 19/06/2013
     */
    public void validarDadosVendaAvulsaEstudio() {
        try {
            validarDadosVendaAvulsaEstudio(false);

            AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    setProcessandoOperacao(true);
                    AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                    UsuarioVO usuario = auto.getUsuario();
                    usuario.setUserOamd(auto.getUsuario().getUserOamd());
                    vendaAvulsaVO.setResponsavel(usuario);
                    gravar();
                    gravarPontuacaoCliente();
                    limparControladoresVenda();
                    getVendaAvulsaVO().setItemVendaAvulsaVOs(new ArrayList<>());
                    setMensagemDetalhada("", "");
                    ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
                    if (contratoControle != null) {
                        contratoControle.liberarBackingBeanMemoria("ContratoControle");
                        contratoControle = null;
                    }
                    MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
                    if (movParcelaControle != null) {
                        movParcelaControle.liberarBackingBeanMemoria("MovParcelaControle");
                    }
                    setMensagem("");
                    setMensagemDetalhada("");
                    setProcessandoOperacao(false);
                    if (vendaAvulsaVO.getValorTotal() == 0.0) {
                        novo();
                        montarSucessoGrowl("Venda realizada com SUCESSO!");
                        setPaginaDestino("vendaAvulsa");
                    } else {
                        inicializarCaixa();
                        setPaginaDestino("tela8");
                    }
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    montarErro(e);
                }

                @Override
                public void onFecharModalAutorizacao() {
                    auto.setRenderComponents("panelAutorizacaoFuncionalidade");
                }
            };

            limparMsg();
            auto.autorizar("Confirmação de Caixa em Aberto", "CaixaEmAberto",
                    "Você precisa da permissão \"2.44 - Operação - Caixa em Aberto\"",
                    "form:vendaAvulsaContainer", listener);
        } catch (Exception e) {
            setProcessandoOperacao(false);
            montarErro(e);
        }
    }

    public void validarDadosVendaAvulsaEstudioPagar() {
        try {
            validarDadosVendaAvulsaEstudio(true);

            AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    setProcessandoOperacao(true);
                    AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                    UsuarioVO usuario = auto.getUsuario();
                    usuario.setUserOamd(auto.getUsuario().getUserOamd());
                    vendaAvulsaVO.setResponsavel(usuario);
                    gravar();
                    gravarPontuacaoCliente();
                    limparControladoresVenda();
                    setMensagemDetalhada("");
                    setMensagem("");
                    setProcessandoOperacao(false);
                    if (vendaAvulsaVO.getValorTotal() == 0) {
                        novo();
                        montarSucessoGrowl("Venda realizada com SUCESSO!");
                        setPaginaDestino("vendaAvulsa");
                    } else {
                        setPaginaDestino("pagamento");
                    }
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    montarErro(e);
                }

                @Override
                public void onFecharModalAutorizacao() {
                    auto.setRenderComponents("panelAutorizacaoFuncionalidade");
                }
            };

            limparMsg();

            auto.autorizar("Confirmação de Venda", "CaixaEmAberto",
                    "Você precisa da permissão \"2.44 - Operação - Caixa em Aberto\"",
                    "form:vendaAvulsaContainer", listener);
        } catch (Exception e) {
            setProcessandoOperacao(false);
            montarErro(e);
        }
    }

    private void validarDadosVendaAvulsaEstudio(boolean pagar) throws Exception {
        limparMsg();
        validarDadosVendaAvulsa(null);
        if (vendaSessao()) {
            if (!vendaAvulsaVO.getTipoComprador().equals("CI")) {
                for (ItemVendaAvulsaVO itemVendaAvulsaVO1 : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
                    if (itemVendaAvulsaVO1.getProduto().getTipoProduto().equals("SS")) {
                        throw new Exception("Produtos do tipo sessão não podem ser vendidos para pessoas do tipo " + vendaAvulsaVO.getTipoComprador_Apresentar());
                    }
                }
            }
            ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
            ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(vendaAvulsaVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            clienteControle.prepararTelaCliente(cliente, false);
            clienteControle.limparContrato();
            String retorno = clienteControle.validarQuestionario();
            clienteControle.setFinalizarVenda(true);
            if (retorno.equals("questionario")) {
                setMsgAlert("Raichfaces.showModalPanel('modlBVEstudio');");
                return;
            }
        }

    }

    public boolean vendaSessao() {
        for (Object obj : vendaAvulsaVO.getItemVendaAvulsaVOs()) {
            ItemVendaAvulsaVO itva = (ItemVendaAvulsaVO) obj;
            if (itva.getProduto().getTipoProduto().equals("SS")) {
                return true;
            }
        }
        return false;
    }

    public String responderBV() {
        return "questionario";
    }

    public void consultarResponsavel() {
        try {
            vendaAvulsaVO.setResponsavel(new Usuario().consultarPorChavePrimaria(vendaAvulsaVO.getResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            vendaAvulsaVO.getResponsavel().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * VendaAvulsaCons.jsp. Define o tipo de consulta a ser executada, por meio
     * de ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getVendaAvulsa().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeComprador")) {
                objs = getFacade().getVendaAvulsa().consultarPorNomeComprador(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>VendaAvulsaVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getVendaAvulsa().excluir(vendaAvulsaVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }


    /*
     * Método responsável por adicionar um novo objeto da classe
     * <code>ItemVendaAvulsa</code> para o objeto <code>vendaAvulsaVO</code> da
     * classe <code>VendaAvulsa</code>
     */
    public void adicionarItemVendaAvulsa() throws Exception {
        try {
            limparMsg();
            setOnCompleteSuggestionProduto("");
            setOnCompleteSuggestionCliente("");
            getItemVendaAvulsaVO().setValorDescontoManual(Uteis.arredondarForcando2CasasDecimais(getItemVendaAvulsaVO().getValorDescontoManual()));

            //quando utiliza leitor ele soma a quantidade ao inves de subistituir caso o produto já tenha sido adicionado
            getItemVendaAvulsaVO().setSomarItemAdicionar(isUtilizaLeitorCodigoBarras());

            if (getItemVendaAvulsaVO().getApresentarDescontoManual()) {
                throw new Exception("Aplique o desconto manual ou limpe os descontos antes de inserir o produto");
            }
            if (!getVendaAvulsaVO().getCodigo().equals(0)) {
                itemVendaAvulsaVO.setVendaAvulsa(getVendaAvulsaVO().getCodigo());
            }

            if (getItemVendaAvulsaVO().getProduto().getCodigo() != 0) {
                Double valorFinalPacote = this.getItemVendaAvulsaVO().getProduto().getValorFinal();
                Integer campoConsulta = getItemVendaAvulsaVO().getProduto().getCodigo();
                ProdutoVO produto = getFacade().getProduto().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);

                // Se estiver vendendo um pacote, valor do produto é puxado do cadastro do pacote
                if (getItemVendaAvulsaVO().getPacoteVO() != null && !UteisValidacao.emptyNumber(getItemVendaAvulsaVO().getPacoteVO().getCodigo())) {
                    for (PacoteProdutoVO pacoteProdutoVO : getItemVendaAvulsaVO ().getPacoteVO().getListaPacoteProduto()){
                        if (pacoteProdutoVO.getProdutoVO().getCodigo() == produto.getCodigo()){
                            produto.setValorBaseCalculo(pacoteProdutoVO.getProdutoVO().getValorBaseCalculo());
                            produto.setValorFinal(pacoteProdutoVO.getProdutoVO().getValorFinal());
                        }
                    }
                }

                getItemVendaAvulsaVO().setProduto(produto);

                if (!produto.getTipoVigencia().equals("")) {
                    if (produto.getTipoVigencia().equals("ID")) {
                        getItemVendaAvulsaVO().setDataVenda(negocio.comuns.utilitarias.Calendario.hoje());
                        getItemVendaAvulsaVO().setDataValidade(Uteis.obterDataFutura2(negocio.comuns.utilitarias.Calendario.hoje(), produto.getNrDiasVigencia()));
                    } else {
                        getItemVendaAvulsaVO().setDataVenda(negocio.comuns.utilitarias.Calendario.hoje());
                        if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), produto.getDataFinalVigencia()) <= 0) {
                            getItemVendaAvulsaVO().setDataValidade(produto.getDataFinalVigencia());
                        } else {
                            throw new Exception("O produto " + produto.getDescricao() + " não pode ser adicionado na lista de venda, pois o mesmo está com a (DATA FINAL VIGÊNCIA) vencido.");
                        }
                    }
                }
                if (valorFinalPacote != null && valorFinalPacote > 0) {
                    if (getTipoVenda().equals(TipoVenda.DEGUSTACAO.getSigla())){
                        getItemVendaAvulsaVO().getProduto().setValorFinal(0.00);
                        String descricao = getItemVendaAvulsaVO().getProduto().getDescricao().replace(getItemVendaAvulsaVO().getProduto().getDescricao(),getItemVendaAvulsaVO().getProduto().getDescricao() + " - Degustação");
                        getItemVendaAvulsaVO().getProduto().setDescricao(descricao);
                    }else if (getTipoVenda().equals(TipoVenda.EVENTO.getSigla())){
                        String descricao =  getItemVendaAvulsaVO().getProduto().getDescricao().replace(getItemVendaAvulsaVO().getProduto().getDescricao(),getItemVendaAvulsaVO().getProduto().getDescricao() + " - Evento");
                        getItemVendaAvulsaVO().getProduto().setDescricao(descricao);
                        getItemVendaAvulsaVO().getProduto().setValorFinal(valorFinalPacote);
                    }else {
                        getItemVendaAvulsaVO().getProduto().setValorFinal(valorFinalPacote);
                    }
                }
            }
            getVendaAvulsaVO().adicionarObjItemVendaAvulsaVOs(getItemVendaAvulsaVO());
            this.setItemVendaAvulsaVO(new ItemVendaAvulsaVO());
            setMensagemID("msg_dados_adicionados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /*
     * Método responsável por disponibilizar dados de um objeto da classe
     * <code>ItemVendaAvulsa</code> para edição pelo usuário.
     */
    public void editarItemVendaAvulsa() throws Exception {
        ItemVendaAvulsaVO obj = (ItemVendaAvulsaVO) context().getExternalContext().getRequestMap().get("itemVendaAvulsa");
        obj.setEdicaoVendaAvulsa(true);
        setItemVendaAvulsaVO(obj);
    }

    /*
     * Método responsável por remover um novo objeto da classe
     * <code>ItemVendaAvulsa</code> do objeto <code>vendaAvulsaVO</code> da
     * classe <code>VendaAvulsa</code>
     */
    public void removerItemVendaAvulsa() throws Exception {
        ItemVendaAvulsaVO obj = (ItemVendaAvulsaVO) context().getExternalContext().getRequestMap().get("itemVendaAvulsa");
        getVendaAvulsaVO().excluirObjItemVendaAvulsaVOs(obj.getProduto().getCodigo());
        setMensagemID("msg_dados_excluidos");
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Produto</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
     * realizam busca pelos parâmentros informados no richModal montando
     * automaticamente o resultado da consulta para apresentação.
     */
    public void consultarCodigoProduto() {
        try {
            if (getItemVendaAvulsaVO().getProduto().getCodigo() == null) {
                getItemVendaAvulsaVO().getProduto().setCodigo(0);
            }
            validarTipoProduto();
        } catch (Exception e) {
            setItemVendaAvulsaVO(new ItemVendaAvulsaVO());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarTipoProduto() throws Exception {
        ProdutoVO obj = getFacade().getProduto().consultarPorCodigoProdutoAtivo(getItemVendaAvulsaVO().getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        TipoProduto tipo = TipoProduto.getTipoProdutoCodigo(obj.getTipoProduto());
        if(tipo != null && tipo.getVendaAvulsa()){
            Integer codigoEmpresa = (getUsuarioLogado().getAdministrador()) ? getVendaAvulsaVO().getEmpresa().getCodigo() : getEmpresaLogado().getCodigo();
            ConfiguracaoProdutoEmpresaVO cfgEmpresa = getFacade().getConfiguracaoProdutoEmpresa().consultarPorProdutoEmpresa(obj.getCodigo(), codigoEmpresa);
            if(cfgEmpresa != null){
               obj.setValorFinal(cfgEmpresa.getValor());
               obj.setValorBaseCalculo(cfgEmpresa.getValor()); 
            }
            getItemVendaAvulsaVO().setProduto(obj);
            getItemVendaAvulsaVO().setValorParcial(obj.getValorFinal());
            limparMsg();
        } else {
            throw new Exception("O produto de descrição: " + obj.getDescricao() + " não pode ser vendido.");
        }
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Produto</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
     * realizam busca pelos parâmentros informados no richModal montando
     * automaticamente o resultado da consulta para apresentação.
     */
    public void consultarProduto() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarProduto().equals("codigo")) {
                if (getValorConsultarProduto().equals("")) {
                    setValorConsultarProduto("0");
                }
                int valorInt = Integer.parseInt(getValorConsultarProduto());
                objs = getFacade().getProduto().consultarPorCodigoTipoProdutoSemPMCDDEAtivo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
//            if (getCampoConsultarProduto().equals("descricaoCategoriaProduto")) {
//                objs = produtoFacade.consultarPorDescricaoCategoriaProduto(getValorConsultarProduto(), Uteis.NIVELMONTARDADOS_TODOS);
//            }
            if (getCampoConsultarProduto().equals("descricao")) {
                objs = getFacade().getProduto().consultarPorDescricaoTipoProdutoSemPMCDDEAtivo(getValorConsultarProduto(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
//            if (getCampoConsultarProduto().equals("tipoVigencia")) {
//                objs = produtoFacade.consultarPorTipoVigencia(getValorConsultarProduto(), true, Uteis.NIVELMONTARDADOS_TODOS);
//            }
            setListaConsultarProduto(objs);
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setListaConsultarProduto(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarProduto() throws Exception {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
        this.getItemVendaAvulsaVO().setProduto(obj);
        setMensagemDetalhada("");
        //Uteis.liberarListaMemoria(this.getListaConsultarProduto());
        this.setValorConsultarProduto(null);
        this.setCampoConsultarProduto(null);
    }

    public void limparCampoProduto() {
        this.getItemVendaAvulsaVO().setProduto(new ProdutoVO());
    }

    /**
     * Rotina responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultarComboProduto() {
        List itens = new ArrayList();
        //   itens.add(new SelectItem("descricaoCategoriaProduto", "Categoria de Produto"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        // itens.add(new SelectItem("tipoVigencia", "Tipo de Vigência"));
        return itens;
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Colaborador</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
     * realizam busca pelos parâmentros informados no richModal montando
     * automaticamente o resultado da consulta para apresentação.
     */
    public void consultarColaborador() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarColaborador().equals("codigo")) {
                if (getValorConsultarColaborador().equals("")) {
                    setValorConsultarColaborador("0");
                }
                int valorInt = Integer.parseInt(getValorConsultarColaborador());
                objs = getFacade().getColaborador().consultarPorCodigo(valorInt, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarColaborador().equals("nome")) {
                objs = getFacade().getColaborador().consultarPorNomePessoa(getValorConsultarColaborador(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarColaborador(objs);
            setMensagemDetalhada("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarColaborador(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarColaborador() throws Exception {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
        this.getVendaAvulsaVO().setColaborador(obj);
        this.getVendaAvulsaVO().setNomeComprador(obj.getPessoa().getNome());
        setMensagemDetalhada("");
        setMensagemID("");
        setMensagem("");
        //Uteis.liberarListaMemoria(this.getListaConsultarColaborador());
        this.setValorConsultarColaborador(null);
        this.setCampoConsultarColaborador(null);
    }

    public void limparCampoColaborador() {
        this.getVendaAvulsaVO().setColaborador(new ColaboradorVO());
        this.getVendaAvulsaVO().setNomeComprador("");

    }

    /**
     * Rotina responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultarComboColaborador() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));

        return itens;
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Cliente</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
     * realizam busca pelos parâmentros informados no richModal montando
     * automaticamente o resultado da consulta para apresentação.
     */
    public void consultarCliente() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCliente().equals("codigo")) {
                if (getValorConsultarCliente().equals("")) {
                    setValorConsultarCliente("0");
                }
                int valorInt = Integer.parseInt(getValorConsultarCliente());
                objs = getFacade().getCliente().consultarPorCodigo(valorInt, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            if (getCampoConsultarCliente().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS, null);
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }


            setListaConsultarCliente(objs);
            setMensagemDetalhada("");
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Cliente</code> por meio dos parametros informados na tela de venda
     * avulsa. Esta rotina é utilizada fundamentalmente por requisições Ajax,
     * que realizam busca pelos parâmentros informados na tela de venda avulsa
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest) {
        getVendaAvulsaVO().setCliente(new ClienteVO());
        String pref = (String) suggest;
        ArrayList<ClienteVO> result = new ArrayList<ClienteVO>();
        try {
            setClienteCadastrar("");
            setClienteCadastrarCelular("");
            setClienteCadastrarResidencial("");
            setClienteCadastrarEmail("");
            setOnCompleteCadastrarCliente("");
            setOnCompleteSuggestionCliente("");
            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().
                        consultarTodosClienteComLimite(vendaAvulsaVO.getEmpresa().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_ROBO);
            } else {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().
                        consultarPorNomeClienteComLimite(vendaAvulsaVO.getEmpresa().getCodigo().intValue(), pref, false, Uteis.NIVELMONTARDADOS_ROBO);
            }


            //cadastrar
            if (isUtilizaLeitorCodigoBarras() && UteisValidacao.emptyList(result)) {
                setClienteCadastrar(pref);
                setClienteCadastrarCelular("");
                setClienteCadastrarResidencial("");
                setClienteCadastrarEmail("");
                setOnCompleteSuggestionCliente("cadastrarClienteVendaAvulsa();");
                return result;
            }

            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void limparSuggestionCliente() {
        setOnCompleteSuggestionCliente("");
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Cliente</code> por meio dos parametros informados na tela de venda
     * avulsa. Esta rotina é utilizada fundamentalmente por requisições Ajax,
     * que realizam busca pelos parâmentros informados na tela de venda avulsa
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public List<ColaboradorVO> executarAutocompleteConsultaColaborador(Object suggest) {
        getVendaAvulsaVO().setColaborador(new ColaboradorVO());
        String pref = (String) suggest;
        ArrayList<ColaboradorVO> result = new ArrayList<ColaboradorVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ColaboradorVO>) getFacade().getColaborador().
                        consultarTodosColaboradorComLimite(vendaAvulsaVO.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO);
            } else {
                result = (ArrayList<ColaboradorVO>) getFacade().getColaborador().
                        consultarPorNomeColaboradorComLimite(
                        pref, vendaAvulsaVO.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ColaboradorVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Cliente</code> por meio dos parametros informados na tela de venda
     * avulsa. Esta rotina é utilizada fundamentalmente por requisições Ajax,
     * que realizam busca pelos parâmentros informados na tela de venda avulsa
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public List<ProdutoVO> executarAutocompleteConsultaProduto(Object suggest) {
        setOnCompleteSuggestionProduto("");
        String pref = (String) suggest;
        String pesquisa = (String) suggest;
        ArrayList<ProdutoVO> result;
        try {

            String codigoBarrasGrama = "";
            Double valorTotal = 0.0;
            if (isUtilizaLeitorCodigoBarras()) {
                try {
                    if (UteisValidacao.somenteNumeros(pref) && pref.length() == 13) {
                        codigoBarrasGrama = pref.substring(0, 7);
                        String valorString =  pref.substring(7, 13);
                        valorTotal = new Double(valorString.substring(0, valorString.length() - 3) + "." + valorString.substring(valorString.length() - 3));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            Integer codigoEmpresa = (getUsuarioLogado().getAdministrador()) ? getVendaAvulsaVO().getEmpresa().getCodigo() : getEmpresaLogado().getCodigo();
            if (pref.equals("%")) {
                result = (ArrayList<ProdutoVO>) getFacade().getProduto().consultarPorTodosProdutosComLimit(false, codigoEmpresa, 50, Uteis.NIVELMONTARDADOS_ROBO);
            } else {
                result = (ArrayList<ProdutoVO>) getFacade().getProduto().consultarParaVendaAvulsa(pref, codigoBarrasGrama, codigoEmpresa, Uteis.NIVELMONTARDADOS_ROBO);
            }

            if (isUtilizaLeitorCodigoBarras() &&
                    !UteisValidacao.emptyList(result) &&
                    result.size() == 1 && ((result.get(0).getCodigoBarras() != null && result.get(0).getCodigoBarras().equalsIgnoreCase(pesquisa)) ||
                    result.get(0).getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo()) && result.get(0).getCodigoBarras().startsWith(codigoBarrasGrama))) {

                boolean produtoDePeso = result.get(0).getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo());

                Integer qtd = 0;
                if (!UteisValidacao.emptyNumber(valorTotal) && produtoDePeso) {
                    try {
                        if (result.get(0).getUnidadeMedida().equalsIgnoreCase(UnidadeMedidaEnum.GRAMA.getCodigo())) {
                            Double valorSoma = 0.0;
                            while (valorSoma < valorTotal) {
                                valorSoma += result.get(0).getValorFinal();
                                if (valorSoma > valorTotal) {
                                    qtd = qtd-1;
                                    break;
                                }
                                ++qtd;
                            }
                            getItemVendaAvulsaVO().setQuantidade(qtd);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

                if (getItemVendaAvulsaVO() != null) {
                    getItemVendaAvulsaVO().setProduto(result.get(0));
                    getItemVendaAvulsaVO().getProduto().setDescricao(result.get(0).getDescricao());
                }

                selecionarProduto(result.get(0));

                //se for igual é pq então adiciona automático
                if (result.get(0).getCodigoBarras().equalsIgnoreCase(pesquisa) && !produtoDePeso &&
                        !getConfiguracaoSistemaVO().isItemVendaAvulsaAutomatico()) {
                    setOnCompleteSuggestionProduto("adicionarProdutoAutomatico();");
                } else if (produtoDePeso && qtd > 0) {
                    setOnCompleteSuggestionProduto("adicionarProdutoAutomatico();");
                } else if (produtoDePeso) {
                    getItemVendaAvulsaVO().getProduto().setDescricao(result.get(0).getDescricao());
                    setOnCompleteSuggestionProduto("atualizarProdutoTela();");
                }
                return result;
            }

            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ProdutoVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarProdutoSuggestionBox() throws Exception {
        ProdutoVO produto = (ProdutoVO) request().getAttribute("result");
        selecionarProduto(produto);

    }

    public void selecionarClienteSuggestionBox() throws Exception {
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("result");
        if (clienteVO != null) {
            getVendaAvulsaVO().setCliente(clienteVO);
        }
    }

    public void selecionarColaboradorSuggestionBox() throws Exception {
        ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("result");
        if (colaboradorVO != null) {
            getVendaAvulsaVO().setColaborador(colaboradorVO);
        }
    }

    public void preencherCompraCredito(Integer codigoColaborador) {
        try {
            if (codigoColaborador == null) {
                return;
            }
            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigo(codigoColaborador, 0, Uteis.NIVELMONTARDADOS_ROBO);
            if (colaboradorVO != null) {
                getVendaAvulsaVO().setColaborador(colaboradorVO);
                getVendaAvulsaVO().setNomeComprador(colaboradorVO.getPessoa().getNome());
                getVendaAvulsaVO().setTipoComprador("CO");
                ProdutoVO produto = null;
                List<ProdutoVO> produtos = getFacade().getProduto().consultarPorDescricaoTipoProduto(
                        colaboradorVO.temTipoColaborador(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla()) ? "CRÉDITO DE PERSONAL INTERNO" : "CRÉDITO DE PERSONAL EXTERNO",
                        TipoProduto.CREDITO_PERSONAL.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                if (produtos != null && !produtos.isEmpty()) {
                    produto = produtos.get(0);
                }
                selecionarProduto(produto);
                if (!UteisValidacao.emptyList(getVendaAvulsaVO().getItemVendaAvulsaVOs())) {
                    setItemVendaAvulsaVO(getVendaAvulsaVO().getItemVendaAvulsaVOs().get(0));
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, VendaAvulsaControle.class);
        }
    }

    public void selecionarCliente() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        this.getVendaAvulsaVO().setCliente(obj);
        this.getVendaAvulsaVO().setNomeComprador(obj.getPessoa().getNome());
        setMensagemDetalhada("");
        setMensagemID("");
        setMensagem("");
        // Uteis.liberarListaMemoria(this.getListaConsultarCliente());
        this.setValorConsultarCliente(null);
        this.setCampoConsultarCliente(null);
    }

    public void limparCampoCliente() {
        this.getVendaAvulsaVO().setCliente(new ClienteVO());
        this.getVendaAvulsaVO().setNomeComprador("");
    }

    /**
     * Método responsável por processar a consulta na entidade
     * <code>Cliente</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
     * realizam busca pelos parâmentros informados no richModal montando
     * automaticamente o resultado da consulta para apresentação.
     */
    public void consultarTabelaDesconto() {
        try {
            if (getItemVendaAvulsaVO().getProduto().getCodigo() != 0) {
                List objs = getFacade().getDesconto().consultarPorTipoProdutoPorEmpresa(
                        getItemVendaAvulsaVO().getProduto().getTipoProduto(), true, getEmpresaLogado().getCodigo());
                setListaConsultarTabelaDesconto(objs);
            }
            setMensagemDetalhada("");
            setMensagemID(
                    "msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarTabelaDesconto(new ArrayList());
            setMensagemDetalhada(
                    "msg_erro", e.getMessage());
        }
    }

    public void selecionarTabelaDesconto() throws Exception {
        DescontoVO obj = (DescontoVO) context().getExternalContext().getRequestMap().get("desconto");
        getItemVendaAvulsaVO().setTabelaDesconto(obj);
        limparDescontoManual();

    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 21/09/2012
     */
    private void limparDescontoManual() {
        getItemVendaAvulsaVO().setDescontoManual(false);
        getItemVendaAvulsaVO().setApresentarDescontoManual(false);
        getItemVendaAvulsaVO().setResponsavelAutorizacaoDesconto(new UsuarioVO());
        getItemVendaAvulsaVO().setValorDescontoManual(0.0);
    }

    public void limparCampoTabelaDesconto() {
        this.getItemVendaAvulsaVO().setTabelaDesconto(new DescontoVO());
        limparDescontoManual();
    }

    /**
     * Rotina responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultarComboCliente() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para
     * campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomeComprador", "Nome Comprador"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID(
                "msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo
     * <code>SelectItem</code> para preencher o comboBox relativo ao atributo
     * <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaSelectItemEmpresa(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo
     * <code>Empresa</code>. Buscando todos os objetos correspondentes a
     * entidade
     * <code>Empresa</code>. Esta rotina não recebe parâmetros para filtragem de
     * dados, isto é importante para a inicialização dos dados da tela para o
     * acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade
     * <code><code> e montar o atributo
     * <code>nome</code> Este atributo é uma lista (
     * <code>List</code>) utilizada para definir os valores a serem apresentados
     * no ComboBox correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return new Empresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemEmpresa();
    }

    /*
     * Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>sexo</code>
     */
    public List getListaSelectItemTipoComprador() throws Exception {
        List objs = new ArrayList();
        //objs.add(new SelectItem("", ""));
        Hashtable sexos = (Hashtable) Dominios.getTipoComprador();
        Enumeration keys = sexos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) sexos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do
     * backing bean. Garantindo uma melhor atuação do Garbage Coletor do Java. A
     * mesma é automaticamente quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        vendaAvulsaVO = null;
        itemVendaAvulsaVO = null;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public String getCampoConsultarProduto() {
        return campoConsultarProduto;
    }

    public void setCampoConsultarProduto(String campoConsultarProduto) {
        this.campoConsultarProduto = campoConsultarProduto;
    }

    public String getValorConsultarProduto() {
        return valorConsultarProduto;
    }

    public void setValorConsultarProduto(String valorConsultarProduto) {
        this.valorConsultarProduto = valorConsultarProduto;
    }

    public List getListaConsultarProduto() {
        return listaConsultarProduto;
    }

    public void setListaConsultarProduto(List listaConsultarProduto) {
        this.listaConsultarProduto = listaConsultarProduto;
    }

    public ItemVendaAvulsaVO getItemVendaAvulsaVO() {
        return itemVendaAvulsaVO;
    }

    public void setItemVendaAvulsaVO(ItemVendaAvulsaVO itemVendaAvulsaVO) {
        this.itemVendaAvulsaVO = itemVendaAvulsaVO;
    }

    public String getCampoConsultarColaborador() {
        return campoConsultarColaborador;
    }

    public void setCampoConsultarColaborador(String campoConsultarColaborador) {
        this.campoConsultarColaborador = campoConsultarColaborador;
    }

    public String getValorConsultarColaborador() {
        return valorConsultarColaborador;
    }

    public void setValorConsultarColaborador(String valorConsultarColaborador) {
        this.valorConsultarColaborador = valorConsultarColaborador;
    }

    public List getListaConsultarColaborador() {
        return listaConsultarColaborador;
    }

    public void setListaConsultarColaborador(List listaConsultarColaborador) {
        this.listaConsultarColaborador = listaConsultarColaborador;
    }

    public String getCampoConsultarCliente() {
        return campoConsultarCliente;
    }

    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public VendaAvulsaVO getVendaAvulsaVO() {
        return vendaAvulsaVO;
    }

    public void setVendaAvulsaVO(VendaAvulsaVO vendaAvulsaVO) {
        this.vendaAvulsaVO = vendaAvulsaVO;
    }

    public List getListaConsultarTabelaDesconto() {
        return listaConsultarTabelaDesconto;
    }

    public void setListaConsultarTabelaDesconto(List listaConsultarTabelaDesconto) {
        this.listaConsultarTabelaDesconto = listaConsultarTabelaDesconto;
    }

    public Boolean getAbrirRichModalMensagem() {
        return abrirRichModalMensagem;
    }

    public void setAbrirRichModalMensagem(Boolean abrirRichModalMensagem) {
        this.abrirRichModalMensagem = abrirRichModalMensagem;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public String getAbrirRichConfimacao() {
        if (getAbrirRichConfimacaoPagamento()) {
            return "Richfaces.showModalPanel('panelConfirmacaoVendaAvulsa'), setFocus(formConfirmacaoVendaAvulsa,'formConfirmacaoVendaAvulsa:username')";
        } else {
            return "Richfaces.hideModalPanel('panelConfirmacaoVendaAvulsa')";
        }
    }

    public void consultarResponsavelAutorizacaoDesconto() {
        try {
            getItemVendaAvulsaVO().setResponsavelAutorizacaoDesconto(
                    getFacade().getUsuario().consultarPorChavePrimaria(getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarPermissaoDesconto() {
        try {

            permissaoFuncionalidade(getFacade().getControleAcesso().verificarLoginUsuario(
                            getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().getCodigo(),
                    getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().getSenha().toUpperCase()), "DescontoVendaAvulsa",
                    "2.38 - Desconto em produto de Venda Avulsa");
            setMensagemDetalhada("");
            getItemVendaAvulsaVO().setDescontoManual(true);
            getItemVendaAvulsaVO().setApresentarDescontoManual(true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            getItemVendaAvulsaVO().setDescontoManual(false);
        }
    }

    public void validarPermissaoData() {
        try {
            permissaoFuncionalidade(getFacade().getControleAcesso().verificarLoginUsuario(
                    getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().getCodigo(),
                    getItemVendaAvulsaVO().getResponsavelAutorizacaoDesconto().getSenha().toUpperCase()), "DataVendaAvulsa",
                    "2.39 - Alterar data de Venda Avulsa");
            setMensagemDetalhada("");
            setEditarData(true);
            setOnCompleteValidarData("Richfaces.hideModalPanel('panelSenhaDesconto')");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setEditarData(false);
            setOnCompleteValidarData("");
        }
    }

    public void editarDescontoProduto() throws Exception {
        setOnCompleteDesconto("");
        this.getItemVendaAvulsaVO().setTabelaDesconto(new DescontoVO());
        if (Uteis.arredondarForcando2CasasDecimais(getItemVendaAvulsaVO().getValorDescontoManualRec()) > Uteis.arredondarForcando2CasasDecimais(getItemVendaAvulsaVO().getProduto().getValorFinal()* getItemVendaAvulsaVO().getQuantidade())) {
            setMensagemID("msg_descontoMaior");
            setOnCompleteDesconto("alert('" + getMensagem() + "');");
            limparMsg();
            return;
        }
        getItemVendaAvulsaVO().setValorDescontoManual(getItemVendaAvulsaVO().getValorDescontoManualRec());
        getItemVendaAvulsaVO().setApresentarDescontoManual(false);
        adicionarItemVendaAvulsa();
    }

    public String getAbrirRichPagar() {
        if (getAbrirRichConfimacaoPagamento()) {
            return "Richfaces.showModalPanel('panelPagarVendaAvulsa'), setFocus(formPagarVendaAvulsa,'formPagarVendaAvulsa:username')";
        } else {
            return "Richfaces.hideModalPanel('panelPagarVendaAvulsa')";
        }
    }

    public Boolean getAbrirRichConfimacaoPagamento() {
        return abrirRichConfimacaoPagamento;
    }

    public void setAbrirRichConfimacaoPagamento(Boolean abrirRichConfimacaoPagamento) {
        this.abrirRichConfimacaoPagamento = abrirRichConfimacaoPagamento;
    }

    public String getFechaPanelSenhaDesconto() {
        if (getItemVendaAvulsaVO().getDescontoManual()) {
            return "Richfaces.hideModalPanel('panelSenhaDesconto')";
        } else {
            return "";
        }
    }

    public void setOnCompleteDesconto(String onCompleteDesconto) {
        this.onCompleteDesconto = onCompleteDesconto;
    }

    public String getOnCompleteDesconto() {
        return onCompleteDesconto;
    }

    public void setEditarData(boolean editarData) {
        this.editarData = editarData;
    }

    public boolean getEditarData() {
        return editarData;
    }

    public void setBotaoEditarData(boolean botaoEditarData) {
        this.botaoEditarData = botaoEditarData;
    }

    public boolean getBotaoEditarData() {
        return botaoEditarData;
    }

    public void setOnCompleteValidarData(String onCompleteValidarData) {
        this.onCompleteValidarData = onCompleteValidarData;
    }

    public String getOnCompleteValidarData() {
        return onCompleteValidarData;
    }

    public void setDataNova(Date dataNova) {
        this.dataNova = dataNova;
    }

    public Date getDataNova() {
        return dataNova;
    }

    public String getOnclickConfirmar() {
        if (editarData) {
            return "if(!confirm('Você não confirmou a alteração da data de lançamento. Salvar a venda sem alterar a data?')){return false;}";
        } else {
            return "";
        }
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public Boolean getCreditoPersonal() {
        return getItemVendaAvulsaVO() != null &&
                getItemVendaAvulsaVO().getProduto() != null &&
                !UteisValidacao.emptyString(getItemVendaAvulsaVO().getProduto().getTipoProduto()) &&
                getItemVendaAvulsaVO().getProduto().getTipoProduto().equals(TipoProduto.CREDITO_PERSONAL.getCodigo());
    }

    public void selecionarPacotePersonal() {
        try {
            PacotePersonalVO obj = (PacotePersonalVO) context().getExternalContext().getRequestMap().get("pacote");
            getItemVendaAvulsaVO().setPacotePersonal(obj.getCodigo());
            getItemVendaAvulsaVO().setQuantidade(obj.getQuantidade());
            Double valor;
            if (getVendaAvulsaVO().getColaborador().getUsoCreditosPersonal() != null
                    && getVendaAvulsaVO().getColaborador().getUsoCreditosPersonal().equals(UsoCreditoPersonalEnum.PERMITIR_POS_PAGO.getCodigo())) {
                valor = obj.getValorPosPago();
            } else {
                valor = obj.getValorPrePago();
            }
            getItemVendaAvulsaVO().getProduto().setValorFinal(valor / obj.getQuantidade());
            getItemVendaAvulsaVO().setValorParcial(valor);
            setMsgAlert("Richfaces.hideModalPanel('panelPacotesCreditoPersonal');");
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    private void selecionarProduto(ProdutoVO produto) {
        try {
            if (produto != null) {
                getItemVendaAvulsaVO().setProduto(produto);
                validarTipoProduto();

                if (!UteisValidacao.emptyString(produto.getTipoProduto())
                        && produto.getTipoProduto().equals(TipoProduto.CREDITO_PERSONAL.getCodigo())) {
                    if (!getVendaAvulsaVO().getTipoComprador().equals("CO")
                            || !(getVendaAvulsaVO().getColaborador().temTipoColaborador(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())
                            || getVendaAvulsaVO().getColaborador().temTipoColaborador(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla()))) {
                        throw new Exception("Produto Crédito de personal só deve ser vendido para personais.");
                    }
                    if (getVendaAvulsaVO().getColaborador().temTipoColaborador(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())
                            && !produto.getDescricao().contains("INTERNO")) {
                        throw new Exception("Para personal interno, escolha o produto CRÉDITO DE PERSONAL INTERNO.");
                    }
                    if (getVendaAvulsaVO().getColaborador().temTipoColaborador(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())
                            && !produto.getDescricao().contains("EXTERNO")) {
                        throw new Exception("Para personal interno, escolha o produto CRÉDITO DE PERSONAL EXTERNO.");
                    }

                    List<PacotePersonalVO> pacotes = getFacade().getProduto().consultarPacotesPersonal(produto.getCodigo());
                    if (!pacotes.isEmpty() && pacotes.get(0).getQuantidade() == 1) {
                        PacotePersonalVO pacote = pacotes.get(0);
                        getItemVendaAvulsaVO().setQuantidade(pacote.getQuantidade());
                        Double valor;
                        if (getVendaAvulsaVO().getColaborador().getUsoCreditosPersonal() != null
                                && getVendaAvulsaVO().getColaborador().getUsoCreditosPersonal().equals(UsoCreditoPersonalEnum.PERMITIR_POS_PAGO.getCodigo())) {
                            valor = pacote.getValorPosPago();
                        } else {
                            valor = pacote.getValorPrePago();
                        }
                        getItemVendaAvulsaVO().setValorParcial(valor);
                        getItemVendaAvulsaVO().getProduto().setValorFinal(valor);
                    } else {
                        getItemVendaAvulsaVO().setQuantidade(1);
                        getItemVendaAvulsaVO().setValorParcial(produto.getValorFinal());
                        getItemVendaAvulsaVO().setProduto(produto);
                    }
                }

                if (getConfiguracaoSistemaVO().isItemVendaAvulsaAutomatico() && !isUtilizaLeitorCodigoBarras()) {
                    adicionarItemVendaAvulsa();
                }
            }

        } catch (Exception e) {
            getItemVendaAvulsaVO().setProduto(new ProdutoVO());
            setMensagemDetalhada(e.getMessage());
        }
    }

    public Date getDataIniAgendar() {
        return dataIniAgendar;
    }

    public void setDataIniAgendar(Date dataIniAgendar) {
        this.dataIniAgendar = dataIniAgendar;
    }

    public Date getDataFimAgendar() {
        return dataFimAgendar;
    }

    public void setDataFimAgendar(Date dataFimAgendar) {
        this.dataFimAgendar = dataFimAgendar;
    }

    public void montarListaSelectItemTipoVenda() {
        try {
            setListaTipoVenda(JSFUtilities.getSelectItemListFromEnum(TipoVenda.class,"sigla","descricao",false));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isAlterarTipoVenda() {
        return alterarTipoVenda;
    }

    public void setAlterarTipoVenda(boolean alterarTipoVenda) {
        this.alterarTipoVenda = alterarTipoVenda;
    }
    public  boolean alterarTipoVenda(){
        if (!isAlterarTipoVenda()){
            setAlterarTipoVenda(true);
        }else {
            setAlterarTipoVenda(false);
        }
        return alterarTipoVenda;
    }

    public List<SelectItem> getListaTipoVenda() {
        return listaTipoVenda;
    }

    public void setListaTipoVenda(List<SelectItem> listaTipoVenda) {
        this.listaTipoVenda = listaTipoVenda;
    }

    public String getTipoVenda() {
        return tipoVenda;
    }

    public void setTipoVenda(String tipoVenda) {
        this.tipoVenda = tipoVenda;
    }

    public String getOnCompleteSuggestionProduto() {
        return onCompleteSuggestionProduto;
    }

    public void setOnCompleteSuggestionProduto(String onCompleteSuggestionProduto) {
        this.onCompleteSuggestionProduto = onCompleteSuggestionProduto;
    }

    public String getOnCompleteSuggestionCliente() {
        return onCompleteSuggestionCliente;
    }

    public void setOnCompleteSuggestionCliente(String onCompleteSuggestionCliente) {
        this.onCompleteSuggestionCliente = onCompleteSuggestionCliente;
    }

    public boolean isUtilizaLeitorCodigoBarras() {
        return utilizaLeitorCodigoBarras;
    }

    public void setUtilizaLeitorCodigoBarras(boolean utilizaLeitorCodigoBarras) {
        this.utilizaLeitorCodigoBarras = utilizaLeitorCodigoBarras;
    }

    public String getOnCompleteCadastrarCliente() {
        return onCompleteCadastrarCliente;
    }

    public void setOnCompleteCadastrarCliente(String onCompleteCadastrarCliente) {
        this.onCompleteCadastrarCliente = onCompleteCadastrarCliente;
    }

    public void cadastrarClienteNovo() {
        try {
            limparMsg();
            setOnCompleteCadastrarCliente("");

            if (UteisValidacao.emptyString(getClienteCadastrar().trim())) {
                throw new Exception("Informe o nome do cliente.");
            }

            if (UteisValidacao.emptyString(getClienteCadastrarCelular().trim()) &&
                    UteisValidacao.emptyString(getClienteCadastrarResidencial().trim())) {
                throw new Exception("Informe pelo menos um telefone.");
            }

            if (!UteisValidacao.emptyString(getClienteCadastrarEmail().trim()) &&
                    !UteisEmail.getValidEmail(getClienteCadastrarEmail())) {
                throw new Exception("O e-mail informado não é válido.");
            }

            ClienteVO clienteVO = getFacade().getCliente().incluirClienteViaWebService(getUsuarioLogado().getColaboradorVO(), getEmpresaLogado(), getClienteCadastrar(),
                    getClienteCadastrarEmail(), getClienteCadastrarResidencial(), getClienteCadastrarCelular());


            getVendaAvulsaVO().setNomeComprador(clienteVO.getPessoa().getNome());
            getVendaAvulsaVO().setCliente(clienteVO);
            setOnCompleteCadastrarCliente("Richfaces.hideModalPanel('modalCadastrarClienteVendaAvulsa');document.getElementById('form:itemVendaAvulsa_produto').focus();");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String getClienteCadastrarCelular() {
        if (clienteCadastrarCelular == null) {
            clienteCadastrarCelular = "";
        }
        return clienteCadastrarCelular;
    }

    public void setClienteCadastrarCelular(String clienteCadastrarCelular) {
        this.clienteCadastrarCelular = clienteCadastrarCelular;
    }

    public String getClienteCadastrarResidencial() {
        if (clienteCadastrarResidencial == null) {
            clienteCadastrarResidencial = "";
        }
        return clienteCadastrarResidencial;
    }

    public void setClienteCadastrarResidencial(String clienteCadastrarResidencial) {
        this.clienteCadastrarResidencial = clienteCadastrarResidencial;
    }

    public String getClienteCadastrar() {
        if (clienteCadastrar == null) {
            clienteCadastrar = "";
        }
        return clienteCadastrar;
    }

    public void setClienteCadastrar(String clienteCadastrar) {
        this.clienteCadastrar = clienteCadastrar;
    }

    public String getClienteCadastrarEmail() {
        if (clienteCadastrarEmail == null) {
            clienteCadastrarEmail = "";
        }
        return clienteCadastrarEmail;
    }

    public void setClienteCadastrarEmail(String clienteCadastrarEmail) {
        this.clienteCadastrarEmail = clienteCadastrarEmail;
    }

}
