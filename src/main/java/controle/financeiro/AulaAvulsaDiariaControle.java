package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import controle.contrato.ContratoControle;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.plano.Produto;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * AulaAvulsaDiariaForm.jsp AulaAvulsaDiariaCons.jsp) com as funcionalidades da classe <code>AulaAvulsaDiaria</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see AulaAvulsaDiaria
 * @see AulaAvulsaDiariaVO
 */
public class AulaAvulsaDiariaControle extends SuperControle {

    protected List listaSelectItemEmpresa;
    protected List listaConsultarCliente;
    protected List listaConsultarColaborador;
    protected List listaSelectItemProdutoAulaAvulsa;
    protected List listaSelectItemProdutoDiaria;
    protected List listaConsultarModalidade;
    protected String valorConsultarCliente;
    protected String valorConsultarColaborador;
    protected String valorConsultarModalidade;
    private AulaAvulsaDiariaVO aulaAvulsaDiariaVO;
    protected Boolean abrirRichModalConfirmacaoPagamento;
    protected String campoConsultarCliente;
    protected String campoConsultarColaborador;
    protected Boolean abrirRichModalMensagem;
    protected String campoConsultarModalidade;
    private String onCompleteDesconto = "";
    private boolean aplicarValor = false;
    private Boolean editarLancamento = false;
    private boolean botaoEditarData = false;

    private List<SelectItem> listaSelectItemDecontos = new ArrayList<SelectItem>();
    private DescontoVO selectItemDesconto;


    public AulaAvulsaDiariaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public String novoAulaAvulsa() throws Exception {
        try {
            setAulaAvulsaDiariaVO(new AulaAvulsaDiariaVO());
            inicializarCliente();
            setAbrirRichModalMensagem(false);
            setAbrirRichModalConfirmacaoPagamento(false);
            inicializarEmpresaLogada();
            inicializarUsuarioLogado();
            setValorConsultarCliente("");
            setValorConsultarColaborador("");
            setValorConsultarModalidade("");
            setCampoConsultarCliente("");
            setCampoConsultarColaborador("");
            setCampoConsultarModalidade("");
            setListaSelectItemEmpresa(new ArrayList());
            setListaSelectItemProdutoAulaAvulsa(new ArrayList());
            setListaSelectItemProdutoDiaria(new ArrayList());
            setListaConsultarCliente(new ArrayList());
            setListaConsultarColaborador(new ArrayList());
            setListaConsultarModalidade(new ArrayList());
            inicializarListasSelectItemTodosComboBoxAulaAvulsa();
            setMensagemID("msg_entre_dados");
            setSucesso(false);
            setMensagem("");
            setMensagemDetalhada("");
            setErro(false);
            setAplicarValor(false);

            return "aulaAvulsa";
        } catch (Exception e) {
            throw e;
        }
    }

    public void editarCampoData() throws Exception {
        setBotaoEditarData(true);
        validarPermissaoAlterarDataLancamento();
    }

    public void inicializarCliente() throws Exception {
        ClienteControle cliente = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        cliente.pegarClienteTelaCliente();
        if (cliente != null && !cliente.getClienteVO().getPessoa().getNome().trim().isEmpty()) {
            aulaAvulsaDiariaVO.setCliente(cliente.getClienteVO());
            aulaAvulsaDiariaVO.setNomeComprador(cliente.getPessoaVO().getNome());
        }
    }

    public void carregaDados(ClienteVO clienteVO) throws Exception {
        if (clienteVO != null && !clienteVO.getPessoa().getNome().trim().isEmpty()) {
            aulaAvulsaDiariaVO.setCliente(clienteVO);
            aulaAvulsaDiariaVO.setNomeComprador(clienteVO.getPessoa().getNome());
        }
    }



    public String novoDiariaTela() throws Exception {
        String retorno = "";
        try {
            retorno = novoDiaria();
        } catch (Exception e) {
            montarErro(e);
        }
        return retorno;
    }

    public String novoDiaria() throws Exception {
        setAulaAvulsaDiariaVO(new AulaAvulsaDiariaVO());
        aulaAvulsaDiariaVO.setCliente(new ClienteVO());
        setAbrirRichModalMensagem(false);
        setAbrirRichModalConfirmacaoPagamento(false);
        inicializarEmpresaLogada();
        inicializarUsuarioLogado();
        setValorConsultarCliente("");
        setValorConsultarColaborador("");
        setValorConsultarModalidade("");
        setCampoConsultarCliente("");
        setCampoConsultarColaborador("");
        setCampoConsultarModalidade("");
        setListaSelectItemEmpresa(new ArrayList());
        setListaSelectItemProdutoAulaAvulsa(new ArrayList());
        setListaSelectItemProdutoDiaria(new ArrayList());
        setListaConsultarCliente(new ArrayList());
        setListaConsultarColaborador(new ArrayList());
        setListaConsultarModalidade(new ArrayList());
        inicializarListasSelectItemTodosComboBoxDiaria();
        setAplicarValor(false);
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setMensagem("");
        setMensagemDetalhada("");
        setErro(false);
        setEditarLancamento(false);
        setBotaoEditarData(true);
        return "diaria";
    }

    public String novoDiariaCliente() throws Exception {
        try {
            notificarRecursoEmpresa(RecursoSistema.VENDA_AVULSA_DIARIA);
            setarModuloAberto(ModuloAberto.ZILLYONWEB);
            setMensagemDetalhada("");
            setAulaAvulsaDiariaVO(new AulaAvulsaDiariaVO());
            inicializarCliente();
            setAbrirRichModalMensagem(false);
            setAbrirRichModalConfirmacaoPagamento(false);
            inicializarEmpresaLogada();
            inicializarUsuarioLogado();
            setValorConsultarCliente("");
            setValorConsultarColaborador("");
            setValorConsultarModalidade("");
            setCampoConsultarCliente("");
            setCampoConsultarColaborador("");
            setCampoConsultarModalidade("");
            setListaSelectItemEmpresa(new ArrayList<>());
            setListaSelectItemProdutoAulaAvulsa(new ArrayList<>());
            setListaSelectItemProdutoDiaria(new ArrayList<>());
            setListaConsultarCliente(new ArrayList<>());
            setListaConsultarColaborador(new ArrayList<>());
            setListaConsultarModalidade(new ArrayList<>());
            inicializarListasSelectItemTodosComboBoxDiaria();
            setAplicarValor(false);
            setMensagemID("msg_entre_dados");
            setSucesso(false);
            setMensagem("");
            setMensagemDetalhada("");
            setErro(false);
            return "diaria";
        } catch (Exception e) {
            montarErro(e);
        }
        return "";
    }

    public String novoDayUseCliente() throws Exception {
        novoDiariaCliente();
        EmpresaVO empresa = aulaAvulsaDiariaVO.getCliente().getEmpresa();
        ModalidadeVO modalidadeVO = getFacade().getModalidade().consultarPorChavePrimaria(empresa.getModalidadeDayUse().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getAulaAvulsaDiariaVO().setModalidade(modalidadeVO);

        ProdutoVO produtoVO = getFacade().getProduto().consultarPorChavePrimaria(empresa.getProdutoDayUse().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        getAulaAvulsaDiariaVO().setProduto(produtoVO);
        obterValorProduto();

        return "diaria";
    }

    public String novoDiaPlusCliente() throws Exception {
        novoDiariaCliente();
        EmpresaVO empresa = aulaAvulsaDiariaVO.getCliente().getEmpresa();
        ModalidadeVO modalidadeVO = getFacade().getModalidade().consultarPorChavePrimaria(empresa.getModalidadeDiaPlus().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getAulaAvulsaDiariaVO().setModalidade(modalidadeVO);

        ProdutoVO produtoVO = getFacade().getProduto().consultarPorChavePrimaria(empresa.getProdutoDiaPlus().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        getAulaAvulsaDiariaVO().setProduto(produtoVO);
        obterValorProduto();

        return "diaria";
    }

    public void gravar() throws Exception {
        try {


        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            throw e;

        }
    }

    public String consultarCliente() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCliente().equals("codigo")) {
                if (getValorConsultarCliente().equals("")) {
                    setValorConsultarCliente("0");
                }
                int valorInt = Integer.parseInt(getValorConsultarCliente());
                objs = getFacade().getCliente().consultarPorCodigo(new Integer(valorInt), getAulaAvulsaDiariaVO().getEmpresa().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getCampoConsultarCliente().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultarCliente(), getAulaAvulsaDiariaVO().getEmpresa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, null);
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                if (valorConsultarCliente.equals("")) {
                    setValorConsultarCliente("0");
                } else {
                    objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(), getAulaAvulsaDiariaVO().getEmpresa().getCodigo().intValue(), getAulaAvulsaDiariaVO().getCliente().getCodigo(), null, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }
            setListaConsultarCliente(objs);
            setMensagemDetalhada("");
            setMensagemID("msg_dados_consultados");
            return "";
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public void consultarResponsavel() {
        try {
            aulaAvulsaDiariaVO.setResponsavel(new Usuario().consultarPorChavePrimaria(aulaAvulsaDiariaVO.getResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void gravarPontosCliente() throws Exception {
        CampanhaDuracaoVO maiorCampanhaAtiva = getFacade().getCampanhaDuracao().campanhaVigenteMultiplicador(Calendario.hoje(), TipoItemCampanhaEnum.PRODUTO, getAulaAvulsaDiariaVO().getProduto().getPontos());
        if (getEmpresaLogado().isTrabalharComPontuacao() && getAulaAvulsaDiariaVO().getProduto().getPontos() > 0 &&
                (!getAulaAvulsaDiariaVO().getEmpresa().isPontuarApenasCategoriasEmCampanhasAtivas() ||
                        (getAulaAvulsaDiariaVO().getEmpresa().isPontuarApenasCategoriasEmCampanhasAtivas() && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())))) {
            HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
            historicoPontos.setCliente(getAulaAvulsaDiariaVO().getCliente());
            historicoPontos.setDataConfirmacao(Calendario.hoje());
            historicoPontos.setDataaula(Calendario.hoje());
            historicoPontos.setEntrada(true);
            Integer pontos = (maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * getAulaAvulsaDiariaVO().getProduto().getPontos() : getAulaAvulsaDiariaVO().getProduto().getPontos());
            historicoPontos.setDescricao("Venda Avulsa (Diaria) - " + getAulaAvulsaDiariaVO().getProduto().getDescricao() + maiorCampanhaAtiva.getTextoCampanhaApresentar());
            historicoPontos.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
            historicoPontos.setPontos(pontos);
            historicoPontos.setTipoPonto(TipoItemCampanhaEnum.PRODUTO);
            historicoPontos.setCodigoVenda(getAulaAvulsaDiariaVO().getCodigo());
            historicoPontos.setProduto(getAulaAvulsaDiariaVO().getProduto().getCodigo());
            getFacade().getHistoricoPontos().incluir(historicoPontos);
        }
    }

    public void selecionarModalidade() throws Exception {
        ModalidadeVO obj = (ModalidadeVO) context().getExternalContext().getRequestMap().get("modalidade");
        this.getAulaAvulsaDiariaVO().setModalidade(obj);
        setMensagemDetalhada("");
        //Uteis.liberarListaMemoria(this.getListaConsultarColaborador());
        this.setValorConsultarModalidade(null);
        this.setCampoConsultarModalidade(null);
        setSucesso(true);
        setErro(false);
    }

    public List getTipoConsultarComboCliente() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("matricula", "Matricula"));
        return itens;
    }

    public List getTipoConsultarComboModalidade() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    public void limparModalidade() {
        getAulaAvulsaDiariaVO().setModalidade(new ModalidadeVO());
        setSucesso(false);
    }

    public void limparCliente() {
        getAulaAvulsaDiariaVO().setCliente(new ClienteVO());
        getAulaAvulsaDiariaVO().setNomeComprador("");

    }

    public void limparProduto() {
        getAulaAvulsaDiariaVO().setProduto(new ProdutoVO());
        obterValorProduto();
    }

    public void inicializarUsuarioLogado() throws Exception {
        try {
            getAulaAvulsaDiariaVO().getResponsavel().setCodigo(getUsuarioLogado().getCodigo());
            getAulaAvulsaDiariaVO().getResponsavel().setUsername(getUsuarioLogado().getUsername());
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarListasSelectItemTodosComboBoxDiaria() {
        if (getAulaAvulsaDiariaVO().getEmpresa().getCodigo().intValue() == 0) {
            montarListaSelectItemEmpresa();
            getAulaAvulsaDiariaVO().setApresentarEmpresa(true);
        }
        montarListaSelectItemProdutoDiaria();
        montarListaSelectItemDescontos();
        setSelectItemDesconto(null);
    }

    private void montarListaSelectItemDescontos() {
        List objs = new ArrayList();
        objs.add(new SelectItem(null, "SELECIONE O DESCONTO PREDEFINIDO AQUI"));

        try {
            List<DescontoVO> resultadoConsultaDecontos = getFacade().getDesconto().consultarPorTipoProdutoPorEmpresa(TipoProduto.DIARIA.getCodigo(), false, getEmpresaLogado().getCodigo());
            if (resultadoConsultaDecontos != null) {
                for (DescontoVO obj : resultadoConsultaDecontos) {
                    if (obj.getAtivo()) {
                        objs.add(new SelectItem(obj, String.format("%s - %s", obj.getDescricao(), obj.getApresentarDescontoValor() ? "R$ " + obj.getValor_Apresentar() : obj.getValor_Apresentar())));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        setListaSelectItemDecontos(objs);
    }

    public void inicializarListasSelectItemTodosComboBoxAulaAvulsa() {
        if (getAulaAvulsaDiariaVO().getEmpresa().getCodigo().intValue() == 0) {
            montarListaSelectItemEmpresa();
            getAulaAvulsaDiariaVO().setApresentarEmpresa(true);
        }

        montarListaSelectItemProdutoAulaAvulsa();
        montarListaSelectItemDescontos();
        setSelectItemDesconto(null);
    }

    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        setListaSelectItemEmpresa(objs);
    }

    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        List lista = new Empresa().consultarPorNome(nomePrm, true, true, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public void montarListaSelectItemProdutoAulaAvulsa() {
        try {
            montarListaSelectItemProdutoAulaAvulsa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemProdutoDiaria() {
        try {
            montarListaSelectItemProdutoDiaria("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemProdutoAulaAvulsa(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        List resultadoConsulta = consultarProduto(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao() + " - valor R$: " + Uteis.getDoubleFormatado(obj.getValorFinal())));
        }
        setListaSelectItemProdutoAulaAvulsa(objs);

    }

    public void montarListaSelectItemProdutoDiaria(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        List resultadoConsulta = consultarProdutoDiaria(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao() + " - valor R$: " + Uteis.getDoubleFormatado(obj.getValorFinal())));
        }
        setListaSelectItemProdutoDiaria(objs);
    }

    public List consultarProduto(String nomePrm) throws Exception {
        List lista = new Produto().consultarPorDescricaoTipoProdutoAtivo(nomePrm, "AA", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    public List consultarProdutoDiaria(String nomePrm) throws Exception {
        List lista = new Produto().consultarPorDescricaoTipoProdutoAtivo(nomePrm, "DI", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    public void obterValorProduto() {
        try {

            if (aulaAvulsaDiariaVO.getProduto().getCodigo().intValue() != 0) {
                if (aulaAvulsaDiariaVO.getProduto().getValorFinal() <= 0) {
                    ProdutoVO obj = new Produto().consultarPorChavePrimaria(aulaAvulsaDiariaVO.getProduto().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    getAulaAvulsaDiariaVO().setProduto(obj);
                }
                getAulaAvulsaDiariaVO().setValor(getAulaAvulsaDiariaVO().getProduto().getValorFinal());
            } else {
                getAulaAvulsaDiariaVO().setValor(0.0);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void validarDadosAulaAvulsa() throws ConsistirException {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                if (getProcessandoOperacao() != null && getProcessandoOperacao()){
                    throw new Exception("Operação em processamento, favor aguardar.");
                }
                setProcessandoOperacao(true);

                getAulaAvulsaDiariaVO().setResponsavel(auto.getUsuario());

                // gravar aulaAvulsaDiaria
                if (getAulaAvulsaDiariaVO().isNovoObj().booleanValue()) {

                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### INCLUSÃO DE DIÁRIA VIA validarDadosAulaAvulsa - AulaAvulsaDiariaControle.java ");
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Matrícula " + aulaAvulsaDiariaVO.getCliente().getMatricula());
                    getFacade().getAulaAvulsaDiaria().incluir(aulaAvulsaDiariaVO);
                    setSucesso(true);
                    setErro(false);

                    //LOG - INICIO
                    try {
                        aulaAvulsaDiariaVO.setObjetoVOAntesAlteracao(new AulaAvulsaDiariaVO());
                        aulaAvulsaDiariaVO.setNovoObj(true);
                        registrarLogObjetoVO(aulaAvulsaDiariaVO, aulaAvulsaDiariaVO.getCodigo().intValue(), "AULAAVULSADIARIA", aulaAvulsaDiariaVO.getCliente().getPessoa().getCodigo());
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("AULAAVULSADIARIA", aulaAvulsaDiariaVO.getCliente().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE AULAAVULSADIARIA", getUsuario().getNome(), getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### FINALIZOU INCLUSÃO DE DIÁRIA VIA validarDadosAulaAvulsa - AulaAvulsaDiariaControle.java ");
                    //LOG - FIM
                } else {
                    AulaAvulsaDiariaVO auxAulaAvulsaDiariaVO = getFacade().getAulaAvulsaDiaria().consultarPorChavePrimaria(aulaAvulsaDiariaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    getFacade().getAulaAvulsaDiaria().alterar(aulaAvulsaDiariaVO);
                    setSucesso(true);
                    setErro(false);

                    //LOG - INICIO
                    try {
                        if (auxAulaAvulsaDiariaVO != null) {
                            aulaAvulsaDiariaVO.setObjetoVOAntesAlteracao(auxAulaAvulsaDiariaVO);
                            registrarLogObjetoVO(aulaAvulsaDiariaVO, aulaAvulsaDiariaVO.getCodigo().intValue(), "MENSAGEMCONSULTOR", aulaAvulsaDiariaVO.getCliente().getPessoa().getCodigo());
                        }
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("AULAAVULSADIARIA", aulaAvulsaDiariaVO.getCliente().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE AULAAVULSADIARIA", getUsuario().getNome(), getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
                setEditarLancamento(false);
                setBotaoEditarData(true);

                gravarPontosCliente();

                MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
                if (movParcelaControle != null) {
                    movParcelaControle.liberarBackingBeanMemoria("MovParcelaControle");
                    movParcelaControle = null;
                }
                MovPagamentoControle movPagamentoControle = (MovPagamentoControle) context().getExternalContext().getSessionMap().get("MovPagamentoControle");
                if (movPagamentoControle != null) {
                    movPagamentoControle.liberarBackingBeanMemoria("MovPagamentoControle");
                    movPagamentoControle = null;
                }
                ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
                if (contratoControle != null) {
                    contratoControle.liberarBackingBeanMemoria("ContratoControle");
                    contratoControle = null;
                }
                VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
                if (vendaAvulsaControle != null) {
                    vendaAvulsaControle.liberarBackingBeanMemoria("VendaAvulsaControle");
                    vendaAvulsaControle = null;
                }
                setSucesso(true);
                setErro(false);
                setMensagemID("msg_dados_gravados");
                setMensagemDetalhada("");
                if (getAulaAvulsaDiariaVO().getValor().doubleValue() == 0) {
                    String retorno = "";
                    if (getAulaAvulsaDiariaVO().getProduto().getTipoProduto().equals("DI")) {
                        retorno = novoDiaria();
                    } else {
                        retorno = novoAulaAvulsa();
                    }
                    getAulaAvulsaDiariaVO().setCliente(new ClienteVO());
                    setMensagem("Venda realizada com SUCESSO!");
                    setMensagemDetalhada("Venda realizada com SUCESSO!");
                    setAbrirRichModalConfirmacaoPagamento(false);
                    setPaginaDestino(retorno);
                } else {
                    setPaginaDestino("pagamento");
                }

                setProcessandoOperacao(false);

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
                setProcessandoOperacao(false);
            }

            @Override
            public void onFecharModalAutorizacao() {
            }
        };

        limparMsg();
        try {
            AulaAvulsaDiariaVO.validarDados(aulaAvulsaDiariaVO);
            auto.autorizar("Confirmação da Venda", "VendaAvulsa",
                    "Você precisa da permissão \"4.11 - Venda Avulsa\"",
                    "form:panelCaixaAberto", listener);

            montarSucessoGrowl("");
            setAbrirRichModalConfirmacaoPagamento(true);
        } catch (Exception e) {
            setProcessandoOperacao(false);
            setAbrirRichModalConfirmacaoPagamento(false);
            montarErro(e);
        }
    }

    public void consultarModalidade() {
        try {
            super.consultar();
            List objs = new ArrayList();
            List lista = new ArrayList();
            if (getCampoConsultarModalidade().equals("codigo")) {
                if (getValorConsultarModalidade().equals("")) {
                    setValorConsultarModalidade("0");
                }
                int valorInt = Integer.parseInt(getValorConsultarModalidade());
                objs = getFacade().getModalidade().consultarPorCodigoAtiva(new Integer(valorInt), getAulaAvulsaDiariaVO().getEmpresa().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_TODOS);
                if (this.getAulaAvulsaDiariaVO().getEmpresa().getCodigo() != 0) {
                    lista = getFacade().getModalidade().consultarPorCodigoAtiva(valorInt, 0, true, Uteis.NIVELMONTARDADOS_TODOS);
                    Iterator i = lista.iterator();
                    while (i.hasNext()) {
                        ModalidadeVO object = (ModalidadeVO) i.next();
                        if (object.getModalidadeEmpresaVOs().isEmpty()) {
                            objs.add(object);
                        }
                    }
                }
            }
            if (getCampoConsultarModalidade().equals("nome")) {
                objs = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado(getValorConsultarModalidade(), getAulaAvulsaDiariaVO().getEmpresa().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_TODOS);
                if (this.getAulaAvulsaDiariaVO().getEmpresa().getCodigo() != 0) {
                    lista = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado(getValorConsultarModalidade(), 0, true, Uteis.NIVELMONTARDADOS_TODOS);
                    Iterator i = lista.iterator();
                    while (i.hasNext()) {
                        ModalidadeVO object = (ModalidadeVO) i.next();
                        if (object.getModalidadeEmpresaVOs().isEmpty()) {
                            objs.add(object);
                        }
                    }
                }
            }
            setListaConsultarModalidade(objs);
            setMensagemID("msg_dados_consultados");

        } catch (Exception e) {
            setListaConsultarModalidade(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarEmpresaLogada() throws Exception {
        try {
            getAulaAvulsaDiariaVO().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        } catch (Exception e) {
            throw e;
        }
    }

    public void selecionarCliente() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        obj = getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        this.getAulaAvulsaDiariaVO().setCliente(obj);
        this.getAulaAvulsaDiariaVO().setNomeComprador(obj.getPessoa().getNome());
        setMensagemDetalhada("");
        this.setValorConsultarCliente(null);
        this.setCampoConsultarCliente(null);
        setErro(false);
        setSucesso(true);
    }

    /**
     * Author Hellison Teodoro de Oliveira
     * 16/05/2014
     */
    public void editarCampoDescontoProduto() throws Exception {

        setOnCompleteDesconto("");
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                try{
                    setProcessandoOperacao(true);

                    setAplicarValor(true);
                    getAulaAvulsaDiariaVO().setDescontoManual(true);
                    getAulaAvulsaDiariaVO().setApresentarDescontoManual(true);

                    DescontoVO selectItemDesconto = getSelectItemDesconto();
                    if (selectItemDesconto != null) {

                        Double valorDesconto = selectItemDesconto.getValor();
                        if (selectItemDesconto.getApresentarDescontoPorcentagem()) {
                            valorDesconto = getAulaAvulsaDiariaVO().getProduto().getValorFinal() * valorDesconto / 100;
                            valorDesconto = Uteis.arredondarForcando2CasasDecimais(valorDesconto);
                        } else {
                            if (valorDesconto > getAulaAvulsaDiariaVO().getProduto().getValorFinal()) {
                                getAulaAvulsaDiariaVO().setValorDescontoManual(0D);
                                setSelectItemDesconto(null);
                                limparMsg();
                                throw new Exception("Desconto não pode ser maior do que o valor do produto!");
                            }
                        }

                        if (valorDesconto > getAulaAvulsaDiariaVO().getProduto().getValorFinal()) {
                            valorDesconto = getAulaAvulsaDiariaVO().getProduto().getValorFinal();
                        }

                        getAulaAvulsaDiariaVO().setValorDescontoManual(valorDesconto);

                    } else {
                        getAulaAvulsaDiariaVO().setValorDescontoManual(0D);
                    }

                    montarSucessoGrowl("");
                    setOnCompleteDesconto("");

                    setProcessandoOperacao(false);
                } catch(Exception ex){
                    montarErro(ex);
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };
        try {
            limparMsg();
            setAplicarValor(false);
            if (!UteisValidacao.emptyNumber(getAulaAvulsaDiariaVO().getProduto().getCodigo())) {
                auto.autorizar("Autorizar Desconto do Produto", "DescontoVendaDiaria",
                        "Você precisa da permissão \"2.46 - Desconto em produto de Venda de Diária\"",
                        "mensagem, panelMesangem, form:panelItemVenda, panelGroupDesconto, form:totalLancado", listener);
            } else {
                setSelectItemDesconto(null);
                limparMsg();
                throw new Exception("Escolha primeiro o produto");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String getFechaPanelSenhaDesconto() {
        if (getAulaAvulsaDiariaVO().isDescontoManual()) {
            return "Richfaces.hideModalPanel('panelSenhaDesconto')";
        } else {
            return "";
        }
    }

    public void limparDescontoManual() {
        setAplicarValor(false);
        getAulaAvulsaDiariaVO().setValorDescontoManual(0.0);
        getAulaAvulsaDiariaVO().setDescontoManual(false);
        getAulaAvulsaDiariaVO().setApresentarDescontoManual(false);
        setSelectItemDesconto(null);
    }

    public void validarPermissaoAlterarDataLancamento() {
        setEditarLancamento(false);
        setBotaoEditarData(true);
        final AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                getAulaAvulsaDiariaVO().setUsuarioResponsavelAlteracaoDataLancamento(auto.getUsuario());
                setBotaoEditarData(false);
                setEditarLancamento(true);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarMsgAlert(getMensagemDetalhada());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("2.67 - Alterar data de lançamento da Diária", "alterarLancamentoDiaria",
                "Você precisa da permissão \"2.67 - Alterar data de lançamento da Diária\"",
                "painelDataLancamento", listener);

    }

    public void validarPermissaoDescontoAulaAvulsa() {
        try {
            permissaoFuncionalidade(getFacade().getControleAcesso().verificarLoginUsuario(
                    getAulaAvulsaDiariaVO().getResponsavel().getCodigo().intValue(),
                    getAulaAvulsaDiariaVO().getResponsavel().getSenha().toUpperCase()), "DescontoAulaAvulsa",
                    "2.45 - Desconto em produto de Aula Avulsa");
            setAplicarValor(true);
            getAulaAvulsaDiariaVO().setDescontoManual(true);
            getAulaAvulsaDiariaVO().setApresentarDescontoManual(true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            getAulaAvulsaDiariaVO().setDescontoManual(false);
        }
    }

    public String getOnComplete() {
        return "";
    }

    public void editarDescontoProduto() throws Exception {
        try {
            setOnCompleteDesconto("");
            if (getAulaAvulsaDiariaVO().getValorDescontoManual() > getAulaAvulsaDiariaVO().getProduto().getValorFinal()) {
                limparMsg();
                throw new Exception("Desconto não pode ser maior do que o valor do produto!");
            }
            getAulaAvulsaDiariaVO().setApresentarDescontoManual(false);
            setSelectItemDesconto(null);
        } catch (Exception ex){
            montarErro(ex);
        }
    }

    public void aplicarData() throws Exception {
        if (getAulaAvulsaDiariaVO().getDataLancamento() == null) {
            setMensagem(getMensagemInternalizacao("msg_datavendaavulsa"));
        } else {
            setEditarLancamento(false);
            getAulaAvulsaDiariaVO().setDataRegistro(getAulaAvulsaDiariaVO().getDataLancamento());
        }

    }


    public void limparAlterarData() throws Exception {
        setEditarLancamento(false);
        aulaAvulsaDiariaVO.setDataLancamento(Calendario.hoje());
    }

    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public List getListaConsultarColaborador() {
        return listaConsultarColaborador;
    }

    public void setListaConsultarColaborador(List listaConsultarColaborador) {
        this.listaConsultarColaborador = listaConsultarColaborador;
    }

    public List getListaConsultarModalidade() {
        return listaConsultarModalidade;
    }

    public void setListaConsultarModalidade(List listaConsultarModalidade) {
        this.listaConsultarModalidade = listaConsultarModalidade;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemrEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemrEmpresa;
    }

    public String getValorConsultarCliente() {
        if (valorConsultarCliente == null) {
            valorConsultarCliente = "";
        }
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public String getValorConsultarColaborador() {
        if (valorConsultarColaborador == null) {
            valorConsultarColaborador = "";
        }
        return valorConsultarColaborador;
    }

    public void setValorConsultarColaborador(String valorConsultarColaborador) {
        this.valorConsultarColaborador = valorConsultarColaborador;
    }

    public AulaAvulsaDiariaVO getAulaAvulsaDiariaVO() {
        return aulaAvulsaDiariaVO;
    }

    public void setAulaAvulsaDiariaVO(AulaAvulsaDiariaVO aulaAvulsaDiariaVO) {
        this.aulaAvulsaDiariaVO = aulaAvulsaDiariaVO;
    }

    public Boolean getAbrirRichModalConfirmacaoPagamento() {
        return abrirRichModalConfirmacaoPagamento;
    }

    public void setAbrirRichModalConfirmacaoPagamento(Boolean abrirRichModalConfirmacaoPagamento) {
        this.abrirRichModalConfirmacaoPagamento = abrirRichModalConfirmacaoPagamento;
    }

    public Boolean getAbrirRichModalMensagem() {
        return abrirRichModalMensagem;
    }

    public void setAbrirRichModalMensagem(Boolean abrirRichModalMensagem) {
        this.abrirRichModalMensagem = abrirRichModalMensagem;
    }

    public String getCampoConsultarCliente() {
        if (campoConsultarCliente == null) {
            this.campoConsultarCliente = "";
        }
        return campoConsultarCliente;
    }

    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    public String getCampoConsultarColaborador() {
        if (campoConsultarColaborador == null) {
            campoConsultarColaborador = "";
        }

        return campoConsultarColaborador;
    }

    public void setCampoConsultarColaborador(String campoConsultarColaborador) {
        this.campoConsultarColaborador = campoConsultarColaborador;
    }

    public String getValorConsultarModalidade() {
        if (valorConsultarModalidade == null) {
            this.valorConsultarModalidade = "";
        }
        return valorConsultarModalidade;
    }

    public void setValorConsultarModalidade(String valorConsultarModalidade) {
        this.valorConsultarModalidade = valorConsultarModalidade;
    }

    public List getListaSelectItemProdutoAulaAvulsa() {
        return listaSelectItemProdutoAulaAvulsa;
    }

    public void setListaSelectItemProdutoAulaAvulsa(List listaSelectItemProdutoAulaAvulsa) {
        this.listaSelectItemProdutoAulaAvulsa = listaSelectItemProdutoAulaAvulsa;
    }

    public List getListaSelectItemProdutoDiaria() {
        return listaSelectItemProdutoDiaria;
    }

    public void setListaSelectItemProdutoDiaria(List listaSelectItemProdutoDiaria) {
        this.listaSelectItemProdutoDiaria = listaSelectItemProdutoDiaria;
    }

    public String getCampoConsultarModalidade() {
        if (campoConsultarModalidade == null) {
            this.campoConsultarModalidade = "";
        }
        return campoConsultarModalidade;
    }

    public void setCampoConsultarModalidade(String campoConsultarModalidade) {
        this.campoConsultarModalidade = campoConsultarModalidade;
    }

    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        listaSelectItemEmpresa.clear();
        listaConsultarCliente.clear();
        listaConsultarColaborador.clear();
        listaSelectItemProdutoAulaAvulsa.clear();
        listaSelectItemProdutoDiaria.clear();
        listaConsultarModalidade.clear();
        valorConsultarCliente = null;
        valorConsultarColaborador = null;
        valorConsultarModalidade = null;
        aulaAvulsaDiariaVO = null;
        abrirRichModalMensagem = null;
        abrirRichModalConfirmacaoPagamento = null;
        campoConsultarCliente = null;
        campoConsultarColaborador = null;
        campoConsultarModalidade = null;
    }

    public String getOnCompleteDesconto() {
        return onCompleteDesconto;
    }

    public void setOnCompleteDesconto(String onCompleteDesconto) {
        this.onCompleteDesconto = onCompleteDesconto;
    }

    public boolean isAplicarValor() {
        return aplicarValor;
    }

    public void setAplicarValor(boolean aplicarValor) {
        this.aplicarValor = aplicarValor;
    }

    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarTodosClienteComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomeClienteComLimite(getEmpresaLogado().getCodigo(), pref, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarClienteSuggestionBox() throws Exception {
        ClienteVO obj = (ClienteVO) request().getAttribute("result");
        obj = getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        this.getAulaAvulsaDiariaVO().setCliente(obj);
        this.getAulaAvulsaDiariaVO().setNomeComprador(obj.getPessoa().getNome());
        setMensagemDetalhada("");
        this.setValorConsultarCliente(null);
        this.setCampoConsultarCliente(null);
        setErro(false);
        setSucesso(true);
    }

    public List<ModalidadeVO> executarAutocompleteConsultaModalidade(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ModalidadeVO> resultModalidade;
        try {
            if (pref.equals("%")) {
                resultModalidade = (ArrayList<ModalidadeVO>) getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado("", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                resultModalidade = (ArrayList<ModalidadeVO>) getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            resultModalidade = (new ArrayList<ModalidadeVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return resultModalidade;
    }

    public void selecionarModalidadeSuggestionBox() throws Exception {
        ModalidadeVO obj = (ModalidadeVO) request().getAttribute("resultModalidade");
        obj = getFacade().getModalidade().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        this.getAulaAvulsaDiariaVO().setModalidade(obj);
        setMensagemDetalhada("");
        this.setValorConsultarModalidade(null);
        this.setCampoConsultarModalidade(null);
        setSucesso(true);
        setErro(false);
    }

    public List<ProdutoVO> executarAutocompleteConsultaProduto(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ProdutoVO> resultProduto;
        try {
            if (pref.equals("%")) {
                resultProduto = (ArrayList<ProdutoVO>) getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo("", "DI", true, Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                resultProduto = (ArrayList<ProdutoVO>) getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(pref, "DI", true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            resultProduto = (new ArrayList<ProdutoVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }

        for (ProdutoVO p : resultProduto) {
            if (p.getConfiguracoesEmpresa() != null && !p.getConfiguracoesEmpresa().isEmpty()) {
                for (ConfiguracaoProdutoEmpresaVO cpe : p.getConfiguracoesEmpresa()) {
                    if (cpe.getEmpresa().getCodigo() == getAulaAvulsaDiariaVO().getEmpresa().getCodigo()) {
                        p.setValorFinal(cpe.getValor());
                    }
                }
            }
        }

        return resultProduto;
    }

    public void selecionarProdutoSuggestionBox() throws Exception {
        try {
            setMsgAlert("");
            ProdutoVO obj = (ProdutoVO) request().getAttribute("resultProduto");
            obj = getFacade().getProduto().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            if (obj != null && !obj.getConfiguracoesEmpresa().isEmpty()) {
                for (ConfiguracaoProdutoEmpresaVO cpe : obj.getConfiguracoesEmpresa()) {
                    if (cpe.getEmpresa().getCodigo() == getAulaAvulsaDiariaVO().getEmpresa().getCodigo()) {
                        obj.setValorFinal(cpe.getValor());
                    }
                }
            }

            this.getAulaAvulsaDiariaVO().setProduto(obj);
            obterValorProduto();
            setMensagemDetalhada("");
            this.setValorConsultarModalidade(null);
            this.setCampoConsultarModalidade(null);
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            montarErro("Não foi possivel obter o produto. Tente novamente!");
            setMsgAlert(getMensagemNotificar());

        }
    }

    public Boolean getEditarLancamento() {
        return editarLancamento;
    }

    public void setEditarLancamento(Boolean editarLancamento) {
        this.editarLancamento = editarLancamento;
    }

    public boolean isBotaoEditarData() {
        return botaoEditarData;
    }

    public void setBotaoEditarData(boolean botaoEditarData) {
        this.botaoEditarData = botaoEditarData;
    }

    public List<SelectItem> getListaSelectItemDecontos() {
        return listaSelectItemDecontos;
    }

    public void setListaSelectItemDecontos(List<SelectItem> listaSelectItemDecontos) {
        this.listaSelectItemDecontos = listaSelectItemDecontos;
    }

    public DescontoVO getSelectItemDesconto() {
        return selectItemDesconto;
    }

    public void setSelectItemDesconto(DescontoVO selectItemDesconto) {
        this.selectItemDesconto = selectItemDesconto;
    }


    public void validaPermissaoDesconto() throws Exception {
        editarCampoDescontoProduto();
    }

    public void concederDesconto() {
        setOnCompleteDesconto("");

    }
}
