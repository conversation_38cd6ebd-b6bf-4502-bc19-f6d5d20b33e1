package controle.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AuxiliarBoletoBancarioTO;
import negocio.comuns.basico.BoletoBancarioTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.RemessaItem;
import org.jboleto.JBoleto;
import org.jboleto.JBoletoBean;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.boleto.BancoEnum;
import servicos.impl.boleto.BoletoService;
import servicos.impl.boleto.sicredi.LayoutRemessaSicrediBoleto;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by GlaucoT on 18/04/2015
 */
public class BoletoBancarioControle extends SuperControleRelatorio {

    private List<MovParcelaVO> parcelasBoleto = new ArrayList<MovParcelaVO>();
    private MovParcelaVO parcelaSacado = new MovParcelaVO();
    private Date dataVencimento = Calendario.hoje();
    private ConvenioCobrancaVO cobrancaVO = new ConvenioCobrancaVO();

    private Double valorTitulo = 0.0;
    private Double valorMultaJuros = 0.0;

    private List<JBoleto> boletos = new ArrayList<JBoleto>();

    private List<String> descricaoProdutos = new ArrayList<String>();

    private boolean bloquearAlterarDataVencimento = false;
    private boolean caixaEmAberto = false;
    private boolean apresentarBotaoGravar = true;
    private boolean apresentarBotaoImprimir = false;
    private boolean agruparBoletosPorVencimento = false;
    private boolean apresentarOpcaoCalcularMultaJuros = true;
    private boolean empresaUtilizaMultaJuros = false;
    private boolean calcularMultaJuros = true;

    private List<BoletoBancarioTO> boletosTO = new ArrayList<BoletoBancarioTO>();
    private boolean apresentarAgruparPorVencimento = true;

    private RemessaItemVO itemReimpressao;
    private List<RemessaItemVO> itensImpressao;
    private BoletoVO itemReimpressaoBoletoVO;
    private String linkBoleto;
    private String onComplete;
    private int scrollerPage;
    private Integer convenioCobrancaSelecionado;
    private List<ConvenioCobrancaVO> listaConvenioCobrancaBoleto;
    private List<BoletoVO> listaBoletosOnlineGerados;
    private boolean usarLabelResponsavelAluno = false;
    private String nomeSacadoBoleto = "";
    private boolean pagamentoEmConjunto = false;
    private boolean boletoUnicoParcelasMesmoVencimento = false;
    private boolean apresentarOpcaoParcelasMesmoVencimento = false;
    private boolean possuiParcelasMesmoVencimento = false;

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "boletos" + File.separator);
    }

    public String inicializarTela() {
        try {
            setPagamentoEmConjunto(false);
            setBoletoUnicoParcelasMesmoVencimento(false);
            setApresentarOpcaoParcelasMesmoVencimento(false);
            setPossuiParcelasMesmoVencimento(false);
            limparMsg();
            setOnComplete("");
            setConvenioCobrancaSelecionado(null);
            setListaConvenioCobrancaBoleto(new ArrayList<>());
            setListaBoletosOnlineGerados(new ArrayList<>());
            setCobrancaVO(null);
            setUsarLabelResponsavelAluno(false);

            setApresentarBotaoGravar(true);
            setApresentarBotaoImprimir(false);
            setApresentarAgruparPorVencimento(true);

            EmpresaVO empresaVO = getEmpresaLogado();
            try {
                if (getParcelaSacado() != null && !UteisValidacao.emptyNumber(getParcelaSacado().getEmpresa().getCodigo())) {
                    empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getParcelaSacado().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                }
                setEmpresaUtilizaMultaJuros(empresaVO.getCobrarAutomaticamenteMultaJuros());
                if (isEmpresaUtilizaMultaJuros()) {
                    setApresentarOpcaoCalcularMultaJuros(true);
                } else {
                    setApresentarOpcaoCalcularMultaJuros(false);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            setCalcularMultaJuros(false);
            setAgruparBoletosPorVencimento(false);
            verificarPermissao();

            MovParcelaControle controle = (MovParcelaControle) getControlador(MovParcelaControle.class);
            if (!UteisValidacao.emptyList(controle.getListaResponsavelPagamento()) && controle.getListaResponsavelPagamento().size() > 1) {
                setPagamentoEmConjunto(true);
            }
            if (controle.validarBasico()) {
                setCaixaEmAberto(true);
                setMensagemDetalhada("", "");

                MovParcelaVO parcelaSacado = new MovParcelaVO();
                boolean consultar = true;
                if (getFacade().getConfiguracaoSistema().utilizarSistemaParaClube()) {
                    Map<Integer, Integer> titularesDependentes = getFacade().getCliente().montarMapaPessoasTitulares();
                    for (MovParcelaVO parcela : controle.getListaParcelasPagar()) {
                        Integer codTitular = titularesDependentes.get(parcela.getPessoa().getCodigo());
                        if (parcela.getPessoa().getCodigo().equals(codTitular)) {
                            parcelaSacado = parcela;
                            consultar = true;
                        }
                    }

                    if (parcelaSacado.getCodigo() == null || parcelaSacado.getCodigo() == 0) {
                        MovParcelaVO parcelaBase = controle.getListaParcelasPagar().get(0);
                        Integer codTitular = titularesDependentes.get(parcelaBase.getPessoa().getCodigo());
                        MovParcelaVO parcelaTitular = new MovParcelaVO();
                        parcelaTitular.setPessoa(getFacade().getPessoa().consultarPorCodigo(codTitular, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        parcelaSacado = parcelaTitular;
                        controle.setMovParcelaVO(parcelaSacado);
                        consultar = false;
                    }
                } else {
                    parcelaSacado = controle.getMovParcelaVO();
                }

                //Responsável pelo pagamento.
                if (consultar) {
                    controle.setMovParcelaVO(getFacade().getMovParcela().consultarPorChavePrimaria(parcelaSacado.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                }

                setParcelaSacado(controle.getMovParcelaVO());
                setNomeSacadoBoleto(controle.getMovParcelaVO().getPessoa().getNome());

                //Descobre o responsável pelo aluno caso houver e seta como 'Nome Sacado' pra exibir na tela
                try {
                    //só considera o responsável preenchido se o aluno for menor de idade
                    PessoaCPFTO pessoaCPFTO = getFacade().getBoleto().obterDadosPessoaPagador(empresaVO.getCodigo(), controle.getMovParcelaVO().getPessoa(), true, true);
                    if (!pessoaCPFTO.getNome().equals(pessoaCPFTO.getNomeResponsavel())) {
                        setUsarLabelResponsavelAluno(true);
                        setNomeSacadoBoleto(pessoaCPFTO.getNomeResponsavel());
                    }
                } catch (Exception ignore) {
                }

                setParcelasBoleto(controle.getListaParcelasPagar());

                validarAutorizacaoCobrancaSacado();

                List<MovParcelaVO> listaParcelasPagar = new ArrayList<>();

                // Montar dados mais completos de cada parcela. Necessário para preencher o objeto de empresa pra usar em seguida no método validarConvenioCompativel
                for (MovParcelaVO movParcelaVOItem : controle.getListaParcelasPagar()) {
                    MovParcelaVO movParcelaVO = getFacade().getMovParcela().consultarPorChavePrimaria(movParcelaVOItem.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    listaParcelasPagar.add(movParcelaVO);
                }

                validarConvenioCompativel(null, listaParcelasPagar);

                calcularDataVencimento();
                validarParcelaEmRemessa(getParcelasBoleto());
                validarParcelaEmBoletoPendente(getParcelasBoleto());
                verificarProcessarAgrupamentoBoletos();

                calcularValorTitulo();
                calcularMultaJuros();

                return "gravarBoleto";
            } else {
                throw new Exception(getMensagemDetalhada());
            }
        } catch (Exception ex) {
            montarErro(ex);
            return "";
        }
    }

    /**
     * Realiza a verificação se uma parcela esta em remessa, impedindo a geração de seu boleto.
     *
     * @param parcelasAvaliar
     * @throws Exception
     */
    private void validarParcelaEmRemessa(List<MovParcelaVO> parcelasAvaliar) throws Exception {
        for (MovParcelaVO movParcela : parcelasAvaliar) {
            if (getFacade().getMovParcela().parcelaEstaBloqueadaPorCobranca(movParcela)) {
                throw new Exception("Não será possível gerar o boleto para a " + movParcela.getDescricao() + " pois ela está em uma remessa gerada ou aguardando retorno.");
            }
        }
    }

    private void validarParcelaEmBoletoPendente(List<MovParcelaVO> parcelasAvaliar) throws Exception {
        for (MovParcelaVO movParcela : parcelasAvaliar) {
            if (getFacade().getMovParcela().parcelaEstaBloqueadaPorBoletoPendente(movParcela, false)) {
                throw new Exception("Não será possível gerar o boleto para a " + movParcela.getDescricao() + " pois já existe um boleto pendente para essa parcela.");
            }
        }
    }

    private void verificarPermissao() {
        try {
            permissaoFuncionalidade(getUsuarioLogado(), "AlterarDataVencimentoBoleto", "4.34 - Permitir alterar a data de vencimento de um boleto");
            setBloquearAlterarDataVencimento(false);
        } catch (Exception ex) {
            setBloquearAlterarDataVencimento(true);
        }
    }

    public void calcularValorTitulo() {
        valorTitulo = 0.0;
        for (MovParcelaVO movParcelaVO : parcelasBoleto) {
            valorTitulo += movParcelaVO.getValorParcela();
        }
    }

    public void calcularDataVencimento() {
        dataVencimento = Uteis.somarDias(Calendario.hoje(), getParcelaSacado().getEmpresa().getQtdDiasVencimentoBoleto());
    }

    public void validarAutorizacaoCobrancaSacado() {
        try {
            setListaConvenioCobrancaBoleto(new ArrayList<>());
            apresentarBotaoGravar = false;

            if (getFacade().getEmpresa().isGerarBoletoCaixaAberto(getEmpresaLogado().getCodigo())) {
                setListaConvenioCobrancaBoleto(getFacade().getConvenioCobranca().consultarPorTipoCobranca(
                        new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO,TipoCobrancaEnum.BOLETO_ONLINE},
                        getParcelaSacado().getEmpresa().getCodigo(), SituacaoConvenioCobranca.ATIVO, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                if (getListaConvenioCobrancaBoleto().size() == 1) {
                    setConvenioCobrancaSelecionado(getListaConvenioCobrancaBoleto().get(0).getCodigo());
                    selecionouConvenioCobranca();
                }
            } else {

                List<AutorizacaoCobrancaClienteVO> autorizacao = obterAutorizacaoClienteBoleto(getFacade().getAutorizacaoCobrancaCliente().getCon());
                if (UteisValidacao.emptyList(autorizacao)) {
                    throw new ConsistirException("Sacado selecionado não tem autorização de cobrança.");
                }

                validarConvenioCompativel(autorizacao, null);

                apresentarBotaoGravar = true;
                getListaConvenioCobrancaBoleto().add(autorizacao.get(0).getConvenio());
                setConvenioCobrancaSelecionado(autorizacao.get(0).getConvenio().getCodigo());
                selecionouConvenioCobranca();
            }

            setErro(false);
            setSucesso(false);
        } catch (Exception ex) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada(ex);
            montarErro(ex);
        }
    }

    public void validarConvenioCompativel(List<AutorizacaoCobrancaClienteVO> autorizacao, List<MovParcelaVO> listaParcelasPagar) throws Exception {
        if (!UteisValidacao.emptyList(autorizacao)) {
            //utilizado para validar se o convênio que foi selecioando para gerar boleto é da mesma empresa que do aluno
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigo(autorizacao.get(0).getCliente().getCodigo(), false, Uteis.NIVELMONTARDADOS_GESTAO_PERSONAL);
            List<ConvenioCobrancaEmpresaVO> convenioCobrancaEmpresaVOS = autorizacao.get(0).getConvenio().getConfiguracoesEmpresa();
            Boolean empresaDoAlunoExisteNoConvenio = false;
            for (ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO : convenioCobrancaEmpresaVOS) {
                if (convenioCobrancaEmpresaVO.getEmpresa().getCodigo().equals(clienteVO.getEmpresa().getCodigo())) {
                    empresaDoAlunoExisteNoConvenio = true;
                    break;
                }
            }
            if (!empresaDoAlunoExisteNoConvenio) {
                throw new Exception("Não foi possível gerar a remessa! A empresa do aluno deve ser a mesma empresa do convênio da autorização de cobrança");
            }
        } else {
            if (!UteisValidacao.emptyList(listaParcelasPagar)) {
                //utilizado para validar se o convênio que foi selecionado para gerar boleto é da mesma empresa da parcela do aluno
                List<ConvenioCobrancaEmpresaVO> convenioCobrancaEmpresaVOS = new ArrayList<>();
                if (getFacade().getEmpresa().isGerarBoletoCaixaAberto(listaParcelasPagar.get(0).getEmpresa().getCodigo())) {
                    List<ConvenioCobrancaVO> conveniosBoleto = getFacade().getConvenioCobranca().consultarPorTipoCobranca(
                            new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO, TipoCobrancaEnum.BOLETO_ONLINE},
                            listaParcelasPagar.get(0).getEmpresa().getCodigo(), SituacaoConvenioCobranca.ATIVO, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyList(conveniosBoleto) && conveniosBoleto.size() == 1) {
                        convenioCobrancaEmpresaVOS = getFacade().getConvenioCobrancaEmpresa().consultarPorConvenioCobranca(conveniosBoleto.get(0).getCodigo());
                    }
                } else {
                    List<AutorizacaoCobrancaClienteVO> autorizacaoCliente = obterAutorizacaoClienteBoleto(getFacade().getAutorizacaoCobrancaCliente().getCon());
                    if (autorizacaoCliente != null && autorizacaoCliente.size() > 0) {
                        convenioCobrancaEmpresaVOS = autorizacaoCliente.get(0).getConvenio().getConfiguracoesEmpresa();
                    }
                }

                if (!UteisValidacao.emptyList(convenioCobrancaEmpresaVOS)) {
                    //para cada parcela irá verificar as configurações de convênio
                    for (MovParcelaVO movParcelaVOItem : listaParcelasPagar) {
                        Boolean empresaDaParcelaDoAlunoExisteNoConvenio = false;
                        for (ConvenioCobrancaEmpresaVO convenioCobrancaEmpresaVO : convenioCobrancaEmpresaVOS) {
                            if (convenioCobrancaEmpresaVO.getEmpresa().getCodigo().equals(movParcelaVOItem.getEmpresa().getCodigo())) {
                                empresaDaParcelaDoAlunoExisteNoConvenio = true;
                                break;
                            }
                        }
                        if (!empresaDaParcelaDoAlunoExisteNoConvenio) {
                            throw new Exception("Não foi possível gerar a remessa! A empresa da parcela do aluno deve ser a mesma empresa do convênio da autorização de cobrança");
                        }
                    }
                }
            }
        }
    }

    private List<AutorizacaoCobrancaClienteVO> obterAutorizacaoClienteBoleto(Connection connection) throws Exception {
        AutorizacaoCobrancaCliente autorizacaoDao = new AutorizacaoCobrancaCliente(connection);
        List<AutorizacaoCobrancaClienteVO> autorizacoesBoleto = autorizacaoDao.consultarPorPessoaTipoAutorizacao(getParcelaSacado().getPessoa().getCodigo(),
                TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO, Uteis.NIVELMONTARDADOS_TODOS);
        autorizacaoDao = null;
        return autorizacoesBoleto;
    }

    public boolean isExisteAutorizacaoBoleto(Connection connection) throws Exception {
        List<AutorizacaoCobrancaClienteVO> autorizacoesBoleto = obterAutorizacaoClienteBoleto(connection);
        for (AutorizacaoCobrancaClienteVO autorizacao : autorizacoesBoleto) {
            cobrancaVO = autorizacao.getConvenio();
            return true;
        }
        return false;
    }

    /**
     * Adiciona a {@link AutorizacaoCobrancaCliente} caso a {@link EmpresaVO} possua o atributo <code>permitirMaillingGerarAutorizacaoCobrancaBoleto</code> marcado como true.
     *
     * @param movParcela {@link MovParcelaVO} correpondente a geração da autorizacao.
     * @param con        Conexao com obanco.
     * @return True caso a autorizacao seja gerada.
     * @throws Exception
     */
    public boolean adicionarAutorizacaoBoleto(MovParcelaVO movParcela, Connection con) throws Exception {
        Empresa empresa = new Empresa(con);
        boolean adicionadoAutorizacao = false;
        EmpresaVO emp = empresa.consultarPorChavePrimaria(movParcela.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (emp.isPermitirMaillingGerarAutorizacaoCobrancaBoleto() && emp.getConvenioBoletoPadrao() != null &&
                emp.getConvenioBoletoPadrao().getCodigo() != null && !UteisValidacao.emptyNumber(emp.getConvenioBoletoPadrao().getCodigo())) {
            AutorizacaoCobrancaCliente auto = new AutorizacaoCobrancaCliente(con);
            auto.incluir(criarAutorizacao(movParcela, emp, con));
            cobrancaVO = emp.getConvenioBoletoPadrao();
            adicionadoAutorizacao = true;
        }
        return adicionadoAutorizacao;
    }

    /**
     * Cria a {@link AutorizacaoCobrancaClienteVO} para o {@link TipoAutorizacaoCobrancaEnum}.BOLETO, para o {@link negocio.comuns.basico.ClienteVO} do {@link negocio.comuns.contrato.ContratoVO}
     * <p>
     * informado na {@link MovParcelaVO}.
     *
     * @param movParcela
     * @param emp
     * @return
     */
    private AutorizacaoCobrancaClienteVO criarAutorizacao(MovParcelaVO movParcela, EmpresaVO emp, Connection con) throws Exception {
        AutorizacaoCobrancaClienteVO auto = new AutorizacaoCobrancaClienteVO();
        Cliente cliente = new Cliente(con);
        auto.setCliente(cliente.consultarPorCodigoPessoa(movParcela.getPessoa().getCodigo(), emp.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS));
        auto.setConvenio(emp.getConvenioBoletoPadrao());
        auto.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
        auto.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
        return auto;
    }

    public void processarAgrupamento(ActionEvent actionEvent) {
        try {
            setPossuiParcelasMesmoVencimento(false);
            if (isAgruparBoletosPorVencimento()) {
                setApresentarOpcaoCalcularMultaJuros(false);
                setApresentarOpcaoParcelasMesmoVencimento(true);
            } else {
                setApresentarOpcaoCalcularMultaJuros(true);
                setApresentarOpcaoParcelasMesmoVencimento(false);
            }
            verificarProcessarAgrupamentoBoletos();
        } catch (Exception ex) {}
    }

    public void selecionouCalculoMultaJuros(ActionEvent actionEvent) {
        if (isCalcularMultaJuros()) {
            setApresentarAgruparPorVencimento(false);
            calcularMultaJuros();
        } else {
            setApresentarAgruparPorVencimento(true);
        }
    }

    public void processarCalculoMultaJuros(ActionEvent actionEvent) {
        calcularMultaJuros();
    }

    public void calcularMultaJuros() {
        try {
            EmpresaVO empresaVO = getEmpresaLogado();
            if (getParcelaSacado() != null && !UteisValidacao.emptyNumber(getParcelaSacado().getEmpresa().getCodigo())) {
                empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getParcelaSacado().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            }
            calcularMultaJuros(empresaVO);
        } catch (Exception ex) {
            Uteis.logar(ex, getClass());
        }
    }

    public void calcularMultaJuros(EmpresaVO empresaVO) {
        try {
            setValorMultaJuros(0.0);
            if (calcularMultaJuros) {
                setValorMultaJuros(getFacade().getMovParcela().montarMultaJurosParcelaVencida(empresaVO, getParcelasBoleto(), getDataVencimento(), false, 1.0, null));
            }
        } catch (Exception ex) {
            Uteis.logar(ex, getClass());
        }
    }

    public void verificarProcessarAgrupamentoBoletos() throws Exception {
        boletosTO = new ArrayList<BoletoBancarioTO>();
        if (agruparBoletosPorVencimento) {
            for (MovParcelaVO movParcelaVO : parcelasBoleto) {

                BoletoBancarioTO boletoInserir = null;
                for (BoletoBancarioTO boleto : boletosTO) {
                    if (boleto.getDataVencimento().equals(movParcelaVO.getDataVencimento())) {
                        boletoInserir = boleto;
                        setPossuiParcelasMesmoVencimento(true);
                        break;
                    }
                }
                if (boletoInserir == null) {
                    boletoInserir = new BoletoBancarioTO();
                    //Caso parcela esteja vencida, o vencimento do boleto terá um acréscimo de 5 dias em relação a hoje.
                    if (isParcelaVencida(movParcelaVO)) {
                        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);
                        boletoInserir.setDataVencimento(Uteis.somarDias(Calendario.hoje(), empresaVO.getQtdDiasVencimentoBoleto()));
                    } else {
                        boletoInserir.setDataVencimento(movParcelaVO.getDataVencimento());
                    }
                    boletoInserir.setEmpresa(movParcelaVO.getEmpresa());
                    boletoInserir.setSacado(getParcelaSacado().getPessoa());
                    boletosTO.add(boletoInserir);
                }
                boletoInserir.getParcelas().add(movParcelaVO);
            }
            Ordenacao.ordenarLista(parcelasBoleto, "dataVencimento");
        } else {

            BoletoBancarioTO boleto = new BoletoBancarioTO();
            boleto.setSacado(getParcelaSacado().getPessoa());
            boleto.setEmpresa(getParcelaSacado().getEmpresa());
            boleto.setDataVencimento(getDataVencimento());
            boleto.setParcelas(getParcelasBoleto());

            boletosTO.add(boleto);
        }
    }

    public String voltar() {
        return "tela8";
    }

    public void gravar() {
        try {
            limparMsg();

            if (cobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE)) {
                gerarBoletoOnline();
            } else {
                gravarBoleto();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public Integer gerarNumeroAleatorio(){
        SimpleDateFormat sdf = new SimpleDateFormat("ddHHmmss");
        Double vlrRandom = 0.0;
        vlrRandom = Math.random() * Double.valueOf(sdf.format(Calendario.hoje()));
        return vlrRandom.intValue();
    }

    public void gerarBoletoOnline() throws Exception {
        setLinkBoleto("");
        setListaBoletosOnlineGerados(new ArrayList<>());
        Ordenacao.ordenarLista(parcelasBoleto, "dataVencimento");

        // Customização SESC para Boleto Caixa Online registar na impressão
        // O padrão é sempre registrar, mas caso configuração marcada, pode mudar comportamento
        boolean registrarBoletoAgora = true;
        if (getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
            registrarBoletoAgora = !getCobrancaVO().isRegistrarBoletoOnlineSomenteNaImpressao();
        }

        if (this.isAgruparBoletosPorVencimento()) {
            //emitir boleto único agrupando parcelas com o mesmo vencimento
            if (isBoletoUnicoParcelasMesmoVencimento()) {
                for (BoletoBancarioTO boleto : getBoletosTO()) {
                    if (!UteisValidacao.emptyList(boleto.getParcelas())) {
                        PessoaVO pessoaSacado;
                        if (boleto.getSacado().getNome().equals(boleto.getParcelas().get(0).getPessoa().getNome())) {
                            pessoaSacado = boleto.getSacado();
                        } else {
                            pessoaSacado = boleto.getParcelas().get(0).getPessoa();
                        }
                        //gerar um boleto único com todas as parcelas
                        BoletoVO boletoVO = getFacade().getBoleto().gerarBoleto(pessoaSacado, getCobrancaVO(), boleto.getParcelas(), boleto.getDataVencimento(),
                                getUsuarioLogado(), OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO, isCalcularMultaJuros(), registrarBoletoAgora);
                        getListaBoletosOnlineGerados().add(boletoVO);
                        if (boletoVO != null && boletoVO.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                            throw new Exception(boletoVO.getErroMensagem());
                        }
                    }
                }
            } else {
                //emitir de acordo com o vencimento de cada parcela
                setListaBoletosOnlineGerados(getFacade().getBoleto().gerarBoletoPorParcela(parcelaSacado.getPessoa(), getCobrancaVO(), parcelasBoleto,
                        getUsuarioLogado(), OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO, isCalcularMultaJuros(), registrarBoletoAgora));

                //Ajusta as datas para serem exibidas no front, caso elas sejam alteradas pelo processo de geração do boleto Pjbank
                for (BoletoBancarioTO boletoBancarioTO : boletosTO) {
                    for (MovParcelaVO movParcelaVO : parcelasBoleto) {
                        if(boletoBancarioTO.getParcelas().get(0).getCodigo() == movParcelaVO.getCodigo() && Calendario.maior(movParcelaVO.getDataVencimento(), boletoBancarioTO.getDataVencimento())){
                            boletoBancarioTO.setDataVencimento(movParcelaVO.getDataVencimento());
                        }
                    }
                }

                if (getListaBoletosOnlineGerados().size() == 1 &&
                        getListaBoletosOnlineGerados().get(0).getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                    throw new Exception(getListaBoletosOnlineGerados().get(0).getErroMensagem());
                }

                //Validar se todos os boletos foram gerados com sucesso
                if (this.getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
                    String msgErro = "";
                    for (BoletoVO boletoVO : getListaBoletosOnlineGerados()) {
                        if (boletoVO.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                            msgErro += boletoVO.getErroMensagem() + "\n";
                        }
                    }
                    if (!UteisValidacao.emptyString(msgErro)) {
                        throw new Exception(msgErro);
                    }
                }
            }
        } else {
            validarVencimentoBoleto();
            //gerar um boleto com todas as parcelas
            BoletoVO boletoVO = getFacade().getBoleto().gerarBoleto(parcelaSacado.getPessoa(), getCobrancaVO(), parcelasBoleto, getDataVencimento(),
                    getUsuarioLogado(), OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO, isCalcularMultaJuros(), registrarBoletoAgora);
            getListaBoletosOnlineGerados().add(boletoVO);
            if (boletoVO != null && boletoVO.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                throw new Exception(boletoVO.getErroMensagem());
            }
        }

        try {
            if (getListaBoletosOnlineGerados().size() == 1) {
                linkBoleto = getListaBoletosOnlineGerados().get(0).getLinkBoleto();
            } else if (getListaBoletosOnlineGerados().size() > 1 && this.getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK)) {
                // A Pjbank que um recurso dentro do metodo getByIds que ao passar o id dos boletos os gera um link com todos os boletos em um único PDF
                // A Caixa Online e o Banco do Brasil Online não tem isso.
                LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(getListaBoletosOnlineGerados());
                BoletosManager boletosManager = new BoletosManager(cobrancaVO.getCredencialPJBank(), cobrancaVO.getChavePJBank(), cobrancaVO);
                linkBoleto = boletosManager.getByIds(pedidos);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            setApresentarBotaoImprimir(false);
            if (ex.getMessage().contains("Credencial não localizada")) {
                throw ex;
            } else if (this.getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) ||
                    this.getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
                throw new Exception(ex.getMessage());
            } else {
                throw new Exception("Não foi possível gerar o boleto. Tente novamente mais tarde");
            }
        }

        if (this.getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE) ||
                (this.getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE) && UteisValidacao.emptyString(linkBoleto))) {
            setApresentarBotaoImprimir(false);
        } else {
            setApresentarBotaoImprimir(true);
        }

        setApresentarBotaoGravar(false);
        setApresentarAgruparPorVencimento(false);
        setApresentarOpcaoParcelasMesmoVencimento(false);
        setSucesso(true);
        setErro(false);
        setMensagemDetalhada("");
        if (UteisValidacao.emptyString(linkBoleto)) {
            montarSucessoGrowl("Boleto(s) gerado(s) com sucesso! Acesse a tela do cliente para imprimir.");
        } else if (getListaBoletosOnlineGerados().size() > 1) {
            montarSucessoGrowl("Boletos gerados com sucesso!");
        } else {
            montarSucessoGrowl("Boleto gerado com sucesso!");
        }
    }

    public boolean isParcelaVencida(MovParcelaVO movParcelaVO) throws Exception {
        if (Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
            return true;
        }
        return false;
    }
    public void validarVencimentoBoleto() throws Exception {
        if (Calendario.menor(getDataVencimento(), Calendario.hoje())) {
            throw new Exception("A data de vencimento do boleto não pode ser menor que Hoje!");
        }
    }

    public void prepararImpressaoBoletosPJBank(Set<String> pedidos) throws Exception {
        if (parcelasBoleto.size() > 1) {
            linkBoleto = "";
            BoletosManager boletosManager = new BoletosManager(cobrancaVO.getCredencialPJBank(), cobrancaVO.getChavePJBank(), cobrancaVO);
            linkBoleto = boletosManager.getByIds(pedidos);
        }
    }

    public void gravarBoleto() throws Exception {
        EmpresaVO empresaVO = getEmpresaLogado();
        if (getParcelaSacado() != null && !UteisValidacao.emptyNumber(getParcelaSacado().getEmpresa().getCodigo())) {
            empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getParcelaSacado().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        }
        gravarBoleto(getUsuarioLogado(), empresaVO);
    }

    public void gravarBoleto(UsuarioVO usuarioVO, EmpresaVO empresaVO) throws Exception {
        setItensImpressao(null);
        setValorTitulo(0.0);

        BoletoService bService = new BoletoService();
        RemessaVO remessa;
        for (BoletoBancarioTO boleto : boletosTO) {
            remessa = bService.obterRemessaBoleto(getCobrancaVO(), empresaVO, usuarioVO);

            Double valorMultaJuros;
            if (!isAgruparBoletosPorVencimento()) {
                boleto.setDataVencimento(getDataVencimento());
                if (isCalcularMultaJuros()) {
                    valorMultaJuros = getFacade().getMovParcela().montarMultaJurosParcelaVencida(empresaVO, boleto.getParcelas(), getDataVencimento(), false, 1.0, null);
                    boleto.setValorMultaJuros(valorMultaJuros);
                }
            } else {
                alterarVencimentoParcelasVencendoFimDeSemanaEFeriado(boleto);
            }

            boleto.calcularValorBoleto();

            setValorTitulo(Uteis.arredondarForcando2CasasDecimais(getValorTitulo() + boleto.getValorBoleto()));

            RemessaItemVO item = bService.adicionarBoletoRemessa(boleto, remessa);
            getItensImpressao().add(item);
        }

        setApresentarBotaoGravar(false);
        setApresentarAgruparPorVencimento(false);
        setApresentarBotaoImprimir(true);


        montarSucessoGrowl("Boleto cadastrado com sucesso.");
    }

    private void alterarVencimentoParcelasVencendoFimDeSemanaEFeriado(BoletoBancarioTO boleto) throws Exception {
        List<MovParcelaVO> listaParcelasAntesAlteracao = new ArrayList<>();
        boolean parcelaFoiAlterada = false;

        try {
            for (MovParcelaVO movParcelaVO : boleto.getParcelas()) {
                listaParcelasAntesAlteracao.add((MovParcelaVO) movParcelaVO.getClone(true));
                Date dataValidar = movParcelaVO.getDataVencimento();

                if (!isParcelaVencida(movParcelaVO)) {
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_EMPRESA_BASICO);
                    while (isFeriado(dataValidar, empresaVO) || !Calendario.isDiaUtil(dataValidar)) {
                        dataValidar = Calendario.somarDias(dataValidar, 1);
                        parcelaFoiAlterada = true;
                    }
                    boleto.setDataVencimento(dataValidar);
                    movParcelaVO.setDataVencimento(dataValidar);
                }
            }

            if (parcelaFoiAlterada) {
                getFacade().getMovParcela().alterarVencimentoListaParcelas(boleto.getParcelas(), listaParcelasAntesAlteracao, false, "GeracaoBoletoPjbank", "CaixaAbertoOuLinkPagamento", true, true);
            }
        } catch (Exception ex) {}
    }

    public boolean isFeriado(Date data, EmpresaVO empresaVO) throws Exception {
        List<Date> dataLimiteFeriado = getFacade().getFeriado().consultarDiasFeriados(data, data, empresaVO);
        if (dataLimiteFeriado.size() > 0){
            return true;
        } else {
            return false;
        }
    }

    public void imprimirBoletoClienteBancoBrasil() {
        montarObjetoParaImprimir();
        executarImprimirBoleto();
    }

    private void montarObjetoParaImprimir() {
        BoletoVO boletoOnlineParaConverterRemessa = getItemReimpressaoBoletoVO();
        try {
            RemessaItemVO boletoImprimir = new RemessaItemVO();
            boletoImprimir.getRemessa().setConvenioCobranca(getFacade().getConvenioCobranca().consultarPorChavePrimaria(boletoOnlineParaConverterRemessa.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            boletoImprimir.getRemessa().getConvenioCobranca().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(boletoOnlineParaConverterRemessa.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            boletoImprimir.getRemessa().getConvenioCobranca().getTipoRemessa().setArquivoLayoutRemessa(ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_BRASIL);
            boletoImprimir.getRemessa().setDataRegistro(boletoOnlineParaConverterRemessa.getDataRegistro());

            boletoImprimir.setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(boletoOnlineParaConverterRemessa.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            boletoImprimir.setDataVencimentoBoleto(boletoOnlineParaConverterRemessa.getDataVencimento());

            List<RemessaItemMovParcelaVO> movParcelasLista = new ArrayList<>();
            Double valorBoleto = 0.0;
            for (BoletoMovParcelaVO boletoMovParcelaVO : boletoOnlineParaConverterRemessa.getListaBoletoMovParcela()) {
                RemessaItemMovParcelaVO remessaItemMovParcelaVO = new RemessaItemMovParcelaVO();
                remessaItemMovParcelaVO.setValorOriginal(boletoMovParcelaVO.getMovParcelaVO().getValorParcela());
                remessaItemMovParcelaVO.setMovParcelaVO(boletoMovParcelaVO.getMovParcelaVO());
                movParcelasLista.add(remessaItemMovParcelaVO);
                valorBoleto += boletoMovParcelaVO.getMovParcelaVO().getValorParcela();
            }
            boletoImprimir.setMovParcelas(movParcelasLista);
            boletoImprimir.setValorItemRemessa(valorBoleto);

            String parametrosCriacaoBoleto = boletoOnlineParaConverterRemessa.getParamsEnvio();
            JsonObject jsonObjectBoleto = new JsonParser().parse(parametrosCriacaoBoleto).getAsJsonObject();
            JsonObject jsonPagador = jsonObjectBoleto.getAsJsonObject("pagador");
            HashMap<String, String> propriedades = new HashMap<>();
            propriedades.put("CpfCnpjPagador", jsonPagador.get("numeroInscricao").getAsString());
            propriedades.put("NomePagador", jsonPagador.get("nome").getAsString());
            boletoImprimir.setProps(propriedades);

            boletoImprimir.setCodigo(boletoOnlineParaConverterRemessa.getIdentificador());
            boletoImprimir.setIdentificador(boletoOnlineParaConverterRemessa.getIdentificador());

            setItemReimpressao(boletoImprimir);
        } catch (Exception ex) {
            String msgErro = "Não foi possível imprimir o boleto!" + ex.getMessage();
            montarErro(msgErro);
            ex.printStackTrace();
            setMensagemDetalhada(msgErro);
        }
    }

    public void imprimirBoletoCliente() {
        executarImprimirBoleto();
    }

    private void executarImprimirBoleto() {
        executarImprimirBoleto(null, null, null);
    }

    public String executarImprimirBoleto(RemessaItemVO remessaItemVO, Connection con, HttpServletRequest request) {
        try {
            //origem do servlet (nova tela do aluno por exemplo) não tem sessão... Origem zw legado tem...
            boolean origemContextoJSF = JSFUtilities.isJSFContext();
            RemessaService service = null;
            if (origemContextoJSF) {
                setCobrancaVO(itemReimpressao.getRemessa().getConvenioCobranca());
                itemReimpressao.getPessoa().setEnderecoVOs(getFacade().getEndereco().consultarPorCodigoPessoa(itemReimpressao.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                itemReimpressao.getPessoa().setCidade(getFacade().getCidade().consultarPorCodigoExato(itemReimpressao.getPessoa().getCidade().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                itemReimpressao.getPessoa().setEstadoVO(getFacade().getEstado().consultarPorCodigo(itemReimpressao.getPessoa().getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                itemReimpressao.setMesesAbertos(getFacade().getRemessaItem().consultarMesesEmAberto(itemReimpressao));
                itemReimpressao.setPorcentagemDescontoBoleto(getCobrancaVO().getDescontoBoleto());
                service = new RemessaService();
            } else if (remessaItemVO != null){
                setItemReimpressao(remessaItemVO);
                setCobrancaVO(remessaItemVO.getRemessa().getConvenioCobranca());
                service = new RemessaService(con);
            }

            itemReimpressao.setIdentificadorEmpresaFinanceiro("");
            boolean layout240 = (itemReimpressao.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                    itemReimpressao.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
            JBoletoBean boletoBean = service.gerarBoletoCobranca(itemReimpressao);
            processarNossoNumero(itemReimpressao, boletoBean);
            JBoleto boleto = new JBoleto(null, boletoBean, itemReimpressao.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco(), layout240);
            boletos = new ArrayList<JBoleto>();
            boletos.add(boleto);

            if (origemContextoJSF) {
                imprimirRelatorio();
            } else {
                imprimirRelatorio(false, con, request);
            }
            if (!origemContextoJSF) {
                ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(DAO.resolveKeyFromConnection(con));
                return clientDiscoveryDataDTO.getServiceUrls().getZwUrl() + "/servlet-relatorio/" + request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
            }
            setMensagemDetalhada("msg_dados_consultados", "");
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            String msgErro = "Não foi possível imprimir o boleto, verifique todas as configurações! " + ex.getMessage();
            montarErro(msgErro);
            ex.printStackTrace();
            setMensagemDetalhada(msgErro);
        }
        return "";
    }

    public void executarImprimirBoletoOrigemSemSessao(RemessaItemVO remessaItemVO) {
        try {
            boletos = new ArrayList<JBoleto>();
            RemessaService service = new RemessaService();
            itemReimpressao.setIdentificadorEmpresaFinanceiro("");
            boolean layout240 = (itemReimpressao.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                    itemReimpressao.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
            JBoletoBean boletoBean = service.gerarBoletoCobranca(itemReimpressao);
            processarNossoNumero(itemReimpressao, boletoBean);
            JBoleto boleto = new JBoleto(null, boletoBean, itemReimpressao.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco(), layout240);
            boletos.add(boleto);

            imprimirRelatorio();
            setMensagemDetalhada("msg_dados_consultados", "");
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            String msgErro = "Não foi possível imprimir o boleto, verifique todas as configurações! " + ex.getMessage();
            montarErro(msgErro);
            ex.printStackTrace();
            setMensagemDetalhada(msgErro);
        }
    }

    public void imprimirBoleto() {
        limparMsg();
        setMsgAlert("");
        try {
            criarBoletos(getItensImpressao(), (String) JSFUtilities.getFromSession("key"));

            imprimirRelatorio();
            setMensagemDetalhada("msg_dados_consultados", "");
            setSucesso(true);
            setErro(false);
            setMsgAlert("abrirPopupPDFImpressao('relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
        } catch (Exception ex) {
            montarErro("Não foi possível imprimir o boleto! Favor verifique todas as configurações no convênio de cobrança, no tipo de remessa e também na conta corrente. Todas devem estar com o mesmo código do banco.");
            ex.printStackTrace();
        }
    }

    public void criarBoletos(List<RemessaItemVO> itensImpressao, String key) throws Exception {
        boletos = new ArrayList<JBoleto>();
        RemessaService service = new RemessaService();
        OAMDService oamdService = new OAMDService();
        for (RemessaItemVO ri : itensImpressao) {
            if (!UteisValidacao.emptyList(ri.getMovParcelas())) {
                ri.setMesesAbertos(getFacade().getRemessaItem().consultarMesesEmAberto(ri));
            }
        }

        for (RemessaItemVO remessaItemVO : itensImpressao) {
            remessaItemVO.setIdentificadorEmpresaFinanceiro("");
            JBoletoBean boletoBean = service.gerarBoletoCobranca(remessaItemVO);
            processarNossoNumero(remessaItemVO, boletoBean);
            boolean layout240 = (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                    remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
            JBoleto boleto = new JBoleto(null, boletoBean, remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco(), layout240);
            boletos.add(boleto);
        }
        oamdService = null;
    }

    private void processarNossoNumero(RemessaItemVO remessaItemVO, JBoletoBean boletoBean) {
        if (remessaItemVO.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO) &&
                remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
            return;
        }
        if (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICREDI) ||
                remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.CARNE_BANCO_SICREDI)){
            boletoBean.setNossoNumero(LayoutRemessaSicrediBoleto.gerarNossoNumero(remessaItemVO,remessaItemVO.getIdentificador(), 2, Integer.parseInt(boletoBean.getAgencia()),
                    Integer.parseInt(boletoBean.getDvAgencia()), Integer.parseInt(boletoBean.getContaCorrente())));
        }else if(remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_BRASIL)){
            String formataNossoNumero = String.format("%010d", remessaItemVO.getIdentificador());
            boletoBean.setNossoNumero(formataNossoNumero);
        }else if (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE)){
            boletoBean.setNossoNumero(remessaItemVO.getIdentificador().toString());
        } else if (!remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_CAIXA)
                && !remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
            boletoBean.setNossoNumero(remessaItemVO.getCodigo().toString());
        }
    }

    public void imprimirBoletosRemessa(RemessaVO remessa) throws Exception{
            setCobrancaVO(remessa.getConvenioCobranca());
            List<RemessaItemVO> lista = getFacade().getRemessaItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO);

            criarBoletos(lista, (String) JSFUtilities.getFromSession("key"));
            imprimirRelatorio();
        }


    private SuperControle enviarEmail(SuperControle controle, PessoaVO pessoaVO) {
        try {
            pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //verificar se o cliente tem email válido
            if (UteisValidacao.emptyList(pessoaVO.getEmailVOs())) {
                controle.setErro(true);
                throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            } else {
                //obter o recibo
                String nomePDF = Uteis.obterCaminhoWeb() + "/relatorio/" + getNomeArquivoRelatorioGeradoAgora();
                File arquivo = new File(nomePDF);
                //obter configurações do envio de email
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
                UteisEmail email = new UteisEmail();
                // assunto do email será "BOLETO"
                email.novo("BOLETO", configuracaoSistemaCRMVO);
                //remetente é o usuario logado
                email.setRemetente(getUsuarioLogado());

                if (cobrancaVO.getTipo().getCodigoBanco() != TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigoBanco()) {
                    email = email.addAnexo(getNomeArquivoRelatorioGeradoAgora(), arquivo);
                }

                String[] emails = new String[pessoaVO.getEmailVOs().size()];
                int i = 0;
                for (Object obj : pessoaVO.getEmailVOs()) {
                    EmailVO emailVO = (EmailVO) obj;
                    emails[i] = emailVO.getEmail();
                    i++;
                }
                String nomeEmpresa = getUsuarioLogado().getAdministrador() ? "" : getEmpresaLogado().getNome();

                String mensagem = gerarHTMLModeloPadraoBoleto(nomePDF);
                email.enviarEmailN(emails, mensagem, "BOLETO", nomeEmpresa);

                controle.apresentarSucessoEmail(true);
            }

            incluirHistoricoContato(pessoaVO);
            controle.setErro(false);
            controle.setSucesso(true);
            controle.setMensagemDetalhada("", "E-mail enviado com sucesso!");
            controle.setApresentarSucessoEmail(true);
        } catch (Exception e) {
            controle.setErro(true);
            controle.setSucesso(false);
            controle.setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return controle;
    }

    public String gerarHTMLModeloPadraoBoleto(String caminhoArquivo) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailBoletoOnline.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "UTF-8");

        String empresaNome = cobrancaVO.getEmpresa().getNome().toUpperCase();
        String empresaUrlLogo = getUrlLogoEmpresa(getKey());
        String empresaEndereco = cobrancaVO.getEmpresa().getEndereco();
        String empresaTelefone = cobrancaVO.getEmpresa().getTelComercial1();
        String empresaEmail = cobrancaVO.getEmpresa().getEmail();
        String mesReferencia = "";
        for (int i = 0 ; i < boletos.size() ; i++) {
            if (i == 0) {
                mesReferencia = boletos.get(i).getBoleto().getInstrucao3();
            } else {
                mesReferencia = mesReferencia + ", " + boletos.get(i).getBoleto().getInstrucao3();
            }
        }
        String dataVencimento = "";
        for (int i = 0 ; i < boletos.size() ; i++) {
            if (i == 0) {
                dataVencimento = boletos.get(i).getBoleto().getDataVencimento();
            } else {
                dataVencimento = dataVencimento + ", " + boletos.get(i).getBoleto().getDataVencimento();
            }
        }
        Double soma = 0.0;
        for (int i = 0 ; i < boletos.size() ; i++) {
            String valor = boletos.get(i).getBoleto().getValorBoleto();
            soma = soma + Double.parseDouble(valor.replaceAll(",", ".")) ;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        df.setMaximumFractionDigits(2);
        String valorBoleto = df.format(soma);
        String nomeAluno = boletos.get(0).getBoleto().getNomeSacado();
        String linhaDigitavel = "";
        for (int i = 0 ; i < boletos.size() ; i++) {
            linhaDigitavel = linhaDigitavel + boletos.get(i).getBoleto().getLinhaDigitavel() + " <br>";
        }
        String linkBoleto = getLinkBoleto();

        boolean enviarBoletoEmAnexo = !UteisValidacao.emptyString(caminhoArquivo);
        boolean enviarLinkBoleto = !UteisValidacao.emptyString(linkBoleto);

        String aux = texto.toString()
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno))
                .replaceAll("#MES_REFERENCIA#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(mesReferencia))
                .replaceAll("#DATA_VENCIMENTO#", dataVencimento)
                .replaceAll("#LINHA_DIGITAVEL#", linhaDigitavel)
                .replaceAll("#VALOR_BOLETO#", valorBoleto)
                .replaceAll("#LINK_BOLETO#", linkBoleto == null ? "" : linkBoleto)
                .replaceAll("#LINK#", enviarLinkBoleto ? "block" : "none")
                .replaceAll("#ANEXO#", enviarBoletoEmAnexo ? "block" : "none");
        return aux;
    }

    public String getUrlLogoEmpresa(String chave) {
        try {
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, cobrancaVO.getEmpresa().getCodigo().toString());
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, cobrancaVO.getEmpresa().getCodigo().toString());
            }
            if (UteisValidacao.emptyString(genKey)) {
                genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA, cobrancaVO.getEmpresa().getCodigo().toString());
            }
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ignored) {
            return "";
        }
    }

    private void incluirHistoricoContato(PessoaVO pessoaVO) {
        try {
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Histórico de Envio de E-mail não gravado, pois cliente não encontrado.");
            }

            HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
            historicoContatoVO.setDia(Calendario.hoje());
            historicoContatoVO.setClienteVO(clienteVO);
            historicoContatoVO.setResponsavelCadastro(getUsuarioLogado());
            historicoContatoVO.setResultado("Envio Email");
            historicoContatoVO.setTipoContato("EM");

            StringBuilder sb = new StringBuilder();
            sb.append("<p>Título: BOLETO</p> ");
            sb.append("<p>Data de envio: ").append(Calendario.getDataAplicandoFormatacao(historicoContatoVO.getDia(), "dd/MM/yyyy")).append("</p> ");
            sb.append("<p>Remetente: ").append(historicoContatoVO.getResponsavelCadastro()).append("</p> ");
            sb.append("<p>------------------------------------------------------</p> ");
            sb.append("<p>Mensagem: UTILIZA ENVIO DE BOLETO PADRÃO</p>");
            historicoContatoVO.setObservacao(sb.toString());
            getFacade().getHistoricoContato().incluirSemCommitSemAtualizarSintetico(historicoContatoVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getDesignIReportRelatorio() throws ConsistirException {
        String nomeArquivo = getCobrancaVO().getTipoRemessa().getArquivoLayoutRemessa().getReport();
        if (UteisValidacao.emptyString(nomeArquivo)) {
            throw new ConsistirException("Configuração do tipo de remessa inválido");
        }
        return "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "boletos" + File.separator + nomeArquivo + ".jrxml";
    }

    public String getDesignSubReportRelatorio() {
        return getCobrancaVO().getTipoRemessa().getArquivoLayoutRemessa().getSubReport();
    }

    private void prepareParams(Map<String, Object> params, boolean origemContextoJSF, Connection con) throws Exception {
        Integer emp;
        if (!origemContextoJSF) {
            emp = itemReimpressao.getRemessa().getEmpresa();
        } else {
            emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        }
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            if (origemContextoJSF) {
                empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else if (con != null) {
                Empresa empresaDAO = new Empresa(con);
                try {
                    empre = empresaDAO.consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } catch (Exception ex) {
                    throw ex;
                } finally {
                    empresaDAO = null;
                }
            }
        }

        params.put("nomeRelatorio", "Boleto");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Boleto");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeDesignSubReport", getDesignSubReportRelatorio());
        if (JSFUtilities.isJSFContext()) {
            params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        } else {
            params.put("nomeUsuario", "MAILING");
        }

        List<AuxiliarBoletoBancarioTO> auxiliarBoletoBancarioTOs = new ArrayList<AuxiliarBoletoBancarioTO>();
        for (JBoleto boleto : boletos) {
            AuxiliarBoletoBancarioTO auxiliar = new AuxiliarBoletoBancarioTO();
            auxiliar.setBoleto(boleto);
            auxiliarBoletoBancarioTOs.add(auxiliar);
        }

        params.put("listaObjetos", auxiliarBoletoBancarioTOs);

        StringBuilder sb = getEnderecoEmpresa(empre);

        params.put("enderecoEmpresa", sb.toString());
        params.put("cnpjEmpresa", empre.getCNPJ());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());

        byte[] propaganda = null;
        if (origemContextoJSF) {
            propaganda = FacadeManager.getFacade().getEmpresa().obterFotoPadrao(
                    DAO.resolveKeyFromConnection(FacadeManager.getFacade().getEmpresa().getCon()),
                    empre.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO);
        } else if (con != null) {
            Empresa empresaDAO = new Empresa(con);
            try {
                propaganda = empresaDAO.obterFotoPadrao(DAO.resolveKeyFromConnection(con), empre.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO);
            } catch (Exception ex) {
                throw ex;
            } finally {
                empresaDAO = null;
            }
        }

        InputStream fs = null;
        if (propaganda != null)
            fs = new ByteArrayInputStream(propaganda);
        params.put("propaganda", fs);
    }

    public StringBuilder getEnderecoEmpresa(EmpresaVO empre) {
        StringBuilder sb = new StringBuilder();
        boolean adicionouEndereco = false;
        if (!UteisValidacao.emptyString(empre.getEndereco())) {
            sb.append(empre.getEndereco());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getNumero())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getNumero());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getSetor())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getSetor());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getCidade_Apresentar())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getCidade_Apresentar());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getEstado().getSigla())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getEstado().getSigla());
            adicionouEndereco = true;
        }
        if (!UteisValidacao.emptyString(empre.getCEP())) {
            if (adicionouEndereco) {
                sb.append(", ");
            }
            sb.append(empre.getCEP());
        }
        return sb;
    }

    private void imprimirRelatorio() throws Exception {
        imprimirRelatorio(true, null, null);
    }

    private void imprimirRelatorio(boolean origemContextoJSF, Connection con, HttpServletRequest request) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        prepareParams(params, origemContextoJSF, con);
        if (origemContextoJSF) {
            apresentarRelatorioObjetos(params);
        } else {
            apresentarRelatorioObjetosComRequest(params, request, con);
        }
        setMensagemDetalhada("", "");
        setMensagemID("msg_entre_prmrelatorio");
    }

    public void enviarEmail() {
        try {
            if (cobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE)) {

                List<EmailVO> emailsEnviar = getFacade().getEmail().consultarEmails(getParcelaSacado().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyList(emailsEnviar)) {
                    throw new Exception(getParcelaSacado().getPessoa().getNome() + " não tem e-mail cadastrado.");
                }

                String[] emails = new String[emailsEnviar.size()];
                int i = 0;
                for (Object obj : emailsEnviar) {
                    EmailVO emailVO = (EmailVO) obj;
                    emails[i] = emailVO.getEmail();
                    i++;
                }

                if (getListaBoletosOnlineGerados().size() == 1) {
                    getFacade().getBoleto().enviarEmailBoleto(getKey(),
                            getListaBoletosOnlineGerados().get(0), emails, true, false);
                } else {
                    getFacade().getBoleto().enviarEmailBoletoLink(getKey(), linkBoleto, getParcelaSacado().getEmpresa().getCodigo(),
                            getParcelaSacado().getPessoa().getCodigo(), emails);
                }

            } else {
                imprimirBoleto();
                BoletoBancarioControle boletoBancarioControle = (BoletoBancarioControle) context().getExternalContext().getSessionMap().get("BoletoBancarioControle");
                enviarEmail(boletoBancarioControle, getParcelaSacado().getPessoa());
            }

            montarSucessoGrowl("E-mail enviado com sucesso.");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void enviarEmailCliente() throws Exception {
        imprimirBoletoCliente();
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        enviarEmail(clienteControle, clienteControle.getClienteVO().getPessoa());
    }

    public boolean enviarEmailClienteBoletoBancoBrasil(BoletoVO boletoVO) {
        boolean emailEnviado = false;
        try {
            setItemReimpressaoBoletoVO(boletoVO);
            imprimirBoletoClienteBancoBrasil();
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            enviarEmail(clienteControle, clienteControle.getClienteVO().getPessoa());
            emailEnviado = clienteControle.getSucesso();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return emailEnviado;
    }

    public List<MovParcelaVO> getParcelasBoleto() {
        return parcelasBoleto;
    }

    public void setParcelasBoleto(List<MovParcelaVO> parcelasBoleto) {
        this.parcelasBoleto = parcelasBoleto;
    }

    public MovParcelaVO getParcelaSacado() {
        return parcelaSacado;
    }

    public void setParcelaSacado(MovParcelaVO parcelaSacado) {
        this.parcelaSacado = parcelaSacado;
    }

    public boolean isApresentarBotaoGravar() {
        return apresentarBotaoGravar;
    }

    public void setApresentarBotaoGravar(boolean apresentarBotaoGravar) {
        this.apresentarBotaoGravar = apresentarBotaoGravar;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getDataVencimento_Apresentar() {
        return Uteis.getData(dataVencimento);
    }

    public ConvenioCobrancaVO getCobrancaVO() {
        return cobrancaVO;
    }

    public void setCobrancaVO(ConvenioCobrancaVO cobrancaVO) {
        this.cobrancaVO = cobrancaVO;
    }

    public Double getValorTitulo() {
        return valorTitulo;
    }

    public void setValorTitulo(Double valorTitulo) {
        this.valorTitulo = valorTitulo;
    }

    public List<JBoleto> getBoletos() {
        return boletos;
    }

    public void setBoletos(List<JBoleto> boletos) {
        this.boletos = boletos;
    }

    public boolean isApresentarBotaoImprimir() {
        return apresentarBotaoImprimir;
    }

    public void setApresentarBotaoImprimir(boolean apresentarBotaoImprimir) {
        this.apresentarBotaoImprimir = apresentarBotaoImprimir;
    }

    public List<String> getDescricaoProdutos() {
        return descricaoProdutos;
    }

    public void setDescricaoProdutos(List<String> descricaoProdutos) {
        this.descricaoProdutos = descricaoProdutos;
    }

    public boolean isAgruparBoletosPorVencimento() {
        return agruparBoletosPorVencimento;
    }

    public void setAgruparBoletosPorVencimento(boolean agruparBoletosPorVencimento) {
        this.agruparBoletosPorVencimento = agruparBoletosPorVencimento;
    }

    public List<BoletoBancarioTO> getBoletosTO() {
        return boletosTO;
    }

    public void setBoletosTO(List<BoletoBancarioTO> boletosTO) {
        this.boletosTO = boletosTO;
    }

    public boolean isApresentarAgruparPorVencimento() {
        return apresentarAgruparPorVencimento;
    }

    public void setApresentarAgruparPorVencimento(boolean apresentarAgruparPorVencimento) {
        this.apresentarAgruparPorVencimento = apresentarAgruparPorVencimento;
    }

    public RemessaItemVO getItemReimpressao() {
        if (itemReimpressao == null) {
            itemReimpressao = new RemessaItemVO();
        }
        return itemReimpressao;
    }

    public void setItemReimpressao(RemessaItemVO itemReimpressao) {
        this.itemReimpressao = itemReimpressao;
    }

    public BoletoVO getItemReimpressaoBoletoVO() {
        if (itemReimpressaoBoletoVO == null) {
            itemReimpressaoBoletoVO = new BoletoVO();
        }
        return itemReimpressaoBoletoVO;
    }

    public void setItemReimpressaoBoletoVO(BoletoVO itemReimpressaoBoletoVO) {
        this.itemReimpressaoBoletoVO = itemReimpressaoBoletoVO;
    }

    public boolean isCalcularMultaJuros() {
        return calcularMultaJuros;
    }

    public void setCalcularMultaJuros(boolean calcularMultaJuros) {
        this.calcularMultaJuros = calcularMultaJuros;
    }

    public boolean isApresentarOpcaoCalcularMultaJuros() {
        return apresentarOpcaoCalcularMultaJuros;
    }

    public void setApresentarOpcaoCalcularMultaJuros(boolean apresentarOpcaoCalcularMultaJuros) {
        this.apresentarOpcaoCalcularMultaJuros = apresentarOpcaoCalcularMultaJuros;
    }

    public Double getValorMultaJuros() {
        return valorMultaJuros;
    }

    public void setValorMultaJuros(Double valorMultaJuros) {
        this.valorMultaJuros = valorMultaJuros;
    }

    public boolean isBloquearAlterarDataVencimento() {
        return bloquearAlterarDataVencimento;
    }

    public void setBloquearAlterarDataVencimento(boolean bloquearAlterarDataVencimento) {
        this.bloquearAlterarDataVencimento = bloquearAlterarDataVencimento;
    }

    public boolean isCaixaEmAberto() {
        return caixaEmAberto;
    }

    public void setCaixaEmAberto(boolean caixaEmAberto) {
        this.caixaEmAberto = caixaEmAberto;
    }

    public String getHintDataVencimentoBoleto() {
        int qtdDias = getParcelaSacado().getEmpresa().getQtdDiasVencimentoBoleto();
        if (qtdDias == 1) {
            return "Vencimento padrão do boleto é para daqui 1 dia, caso tenha permissão poderá alterar.";
        } else if (qtdDias == 0) {
            return "Vencimento padrão do boleto é hoje, caso tenha permissão poderá alterar.";
        } else {
            return "Vencimento padrão do boleto é para daqui " + qtdDias + " dias, caso tenha permissão poderá alterar.";
        }
    }

    public List<RemessaItemVO> getItensImpressao() {
        if (itensImpressao == null) {
            itensImpressao = new ArrayList<RemessaItemVO>();
        }
        return itensImpressao;
    }

    public void setItensImpressao(List<RemessaItemVO> itensImpressao) {
        this.itensImpressao = itensImpressao;
    }

    public void prepararBoletoParaImpressao(RemessaItemVO boleto) {
        setCobrancaVO(boleto.getRemessa().getConvenioCobranca());
        setItensImpressao(new ArrayList<RemessaItemVO>());
        getItensImpressao().add(boleto);
    }

    public String getOnComplete() {
        if(onComplete == null)
            onComplete = "";
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public Integer getConvenioCobrancaSelecionado() {
        if (convenioCobrancaSelecionado == null) {
            convenioCobrancaSelecionado = 0;
        }
        return convenioCobrancaSelecionado;
    }

    public boolean isConvenioSelecionadoPJBank() throws Exception {
        try {
            if (UteisValidacao.emptyList(getListaConvenioCobrancaBoleto())) {
                for (ConvenioCobrancaVO convenio : getListaConvenioCobrancaBoleto())
                    if (convenio.getCodigo().equals(convenioCobrancaSelecionado) && convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK)) {
                        return true;
                    }
            }
        } catch (Exception e) {
        }
        return false;
    }

    public void setConvenioCobrancaSelecionado(Integer convenioCobrancaSelecionado) {
        this.convenioCobrancaSelecionado = convenioCobrancaSelecionado;
    }

    public List<SelectItem> getListaSelectItemConvenioCobrancaBoleto() {
        List<SelectItem> itens = new ArrayList<>();
        for (ConvenioCobrancaVO convenioCobrancaVO : getListaConvenioCobrancaBoleto()) {
            itens.add(new SelectItem(convenioCobrancaVO.getCodigo(), convenioCobrancaVO.getDescricao()));
        }
        Ordenacao.ordenarLista(itens, "label");
        if (itens.size() > 1) {
            itens.add(0, new SelectItem(0, ""));
        }
        return itens;
    }

    public List<ConvenioCobrancaVO> getListaConvenioCobrancaBoleto() {
        if (listaConvenioCobrancaBoleto == null) {
            listaConvenioCobrancaBoleto = new ArrayList<>();
        }
        return listaConvenioCobrancaBoleto;
    }

    public void setListaConvenioCobrancaBoleto(List<ConvenioCobrancaVO> listaConvenioCobrancaBoleto) {
        this.listaConvenioCobrancaBoleto = listaConvenioCobrancaBoleto;
    }

    public boolean isExisteConvenioCobrancaBoleto() {
        return getListaSelectItemConvenioCobrancaBoleto().size() > 0;
    }

    public void selecionouConvenioCobranca() {
        try {
            apresentarBotaoGravar = false;
            setApresentarAgruparPorVencimento(true);

            if (UteisValidacao.emptyNumber(getConvenioCobrancaSelecionado())) {
                return;
            }
            setCobrancaVO(getFacade().getConvenioCobranca().consultarPorChavePrimaria(getConvenioCobrancaSelecionado(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            apresentarBotaoGravar = true;

            boolean temParcelaVencida = false;
            //verifica se tem alguma parcela vencida para definir a regra de qual checkbox deve aparecer na geração do boleto
            if (getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU) || getCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.ITAU)) {
                for (MovParcelaVO movParcelaVO : getParcelasBoleto()) {
                    if (UteisValidacao.dataMenorDataAtualSemHora(movParcelaVO.getDataVencimento())) {
                        temParcelaVencida = true;
                        break;
                    }
                }
            }
            setApresentarAgruparPorVencimento(!temParcelaVencida);
            verificarProcessarAgrupamentoBoletos();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public String getTitleBoletoUnicoParcelasMesmoVencimento() {
        StringBuilder sb = new StringBuilder();
        sb.append("Ao marcar esta opção, nas parcelas que possuem o mesmo vencimento será gerado um boleto único com a soma delas ao invés ");
        sb.append("de gerar um boleto para cada parcela. O recurso funciona de forma híbrida onde as parcelas com mesmo vencimento ");
        sb.append("geram boleto único e as demais geram um boleto normal também. ");
        return sb.toString();
    }

    public String getTitleVencParcelaBoleto() {
        StringBuilder sb = new StringBuilder();
        sb.append("Essa é a data de vencimento da parcela.</br>");
        sb.append("<b>Obs: </b>Caso a parcela esteja vencida, o vencimento do boleto terá um acréscimo de dias de acordo com o que foi</br>");
        sb.append(" definido na configuração \"Dias para calcular o vencimento do Boleto\" lá nas configurações da empresa.");
        return sb.toString();
    }

    public List<BoletoVO> getListaBoletosOnlineGerados() {
        if (listaBoletosOnlineGerados == null) {
            listaBoletosOnlineGerados = new ArrayList<>();
        }
        return listaBoletosOnlineGerados;
    }

    public void setListaBoletosOnlineGerados(List<BoletoVO> listaBoletosOnlineGerados) {
        this.listaBoletosOnlineGerados = listaBoletosOnlineGerados;
    }

    public String getLinkBoleto() {
        if (linkBoleto == null) {
            linkBoleto = "";
        }
        return linkBoleto;
    }

    public void setLinkBoleto(String linkBoleto) {
        this.linkBoleto = linkBoleto;
    }

    public boolean isEmpresaUtilizaMultaJuros() {
        return empresaUtilizaMultaJuros;
    }

    public void setEmpresaUtilizaMultaJuros(boolean empresaUtilizaMultaJuros) {
        this.empresaUtilizaMultaJuros = empresaUtilizaMultaJuros;
    }

    public boolean isUsarLabelResponsavelAluno() {
        return usarLabelResponsavelAluno;
    }

    public void setUsarLabelResponsavelAluno(boolean usarLabelResponsavelAluno) {
        this.usarLabelResponsavelAluno = usarLabelResponsavelAluno;
    }

    public String getNomeSacadoBoleto() {
        return nomeSacadoBoleto;
    }

    public void setNomeSacadoBoleto(String nomeSacadoBoleto) {
        this.nomeSacadoBoleto = nomeSacadoBoleto;
    }

    public boolean isPagamentoEmConjunto() {
        return pagamentoEmConjunto;
    }

    public void setPagamentoEmConjunto(boolean pagamentoEmConjunto) {
        this.pagamentoEmConjunto = pagamentoEmConjunto;
    }

    public boolean isBoletoUnicoParcelasMesmoVencimento() {
        return boletoUnicoParcelasMesmoVencimento;
    }

    public void setBoletoUnicoParcelasMesmoVencimento(boolean boletoUnicoParcelasMesmoVencimento) {
        this.boletoUnicoParcelasMesmoVencimento = boletoUnicoParcelasMesmoVencimento;
    }

    public boolean isApresentarOpcaoParcelasMesmoVencimento() {
        return apresentarOpcaoParcelasMesmoVencimento;
    }

    public void setApresentarOpcaoParcelasMesmoVencimento(boolean apresentarOpcaoParcelasMesmoVencimento) {
        this.apresentarOpcaoParcelasMesmoVencimento = apresentarOpcaoParcelasMesmoVencimento;
    }

    public boolean isPossuiParcelasMesmoVencimento() {
        return possuiParcelasMesmoVencimento;
    }

    public void setPossuiParcelasMesmoVencimento(boolean possuiParcelasMesmoVencimento) {
        this.possuiParcelasMesmoVencimento = possuiParcelasMesmoVencimento;
    }

    public void validarECriarBoletoNaoRegistrado() {
        if (UteisValidacao.emptyString(getItemReimpressaoBoletoVO().getLinkBoleto()) && getItemReimpressaoBoletoVO().getTipo().equals(TipoBoletoEnum.CAIXA)) {
            try {
                ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(getItemReimpressaoBoletoVO().getConvenioCobrancaVO().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(getItemReimpressaoBoletoVO().getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                //Lista de Parcela Vazia, mas lista MovParcela preenchida, pegar parcelas da Lista de MovParcela
                List<MovParcelaVO> listaParcelasBoleto = new ArrayList<>();
                if (UteisValidacao.emptyList(getItemReimpressaoBoletoVO().getListaParcelas()) && !UteisValidacao.emptyList(getItemReimpressaoBoletoVO().getListaBoletoMovParcela())) {
                    for (BoletoMovParcelaVO boletoMovParcelaVO : getItemReimpressaoBoletoVO().getListaBoletoMovParcela()) {
                        listaParcelasBoleto.add(boletoMovParcelaVO.getMovParcelaVO());
                    }
                }

                if (!UteisValidacao.emptyList(listaParcelasBoleto)) {
                    if (listaParcelasBoleto.size() == 1) {
                        List<BoletoVO> listaBoletosRegistrados = getFacade().getBoleto().gerarBoletoPorParcela(pessoaVO,
                                convenioCobrancaVO, listaParcelasBoleto,
                                getUsuarioLogado(), OrigemCobrancaEnum.ZW_MANUAL_TELA_ALUNO, false, true);
                        if (!UteisValidacao.emptyList(listaBoletosRegistrados)) {
                            setItemReimpressaoBoletoVO(listaBoletosRegistrados.get(0));
                        }
                    } else {
                        setItemReimpressaoBoletoVO(getFacade().getBoleto().gerarBoleto(pessoaVO, convenioCobrancaVO, listaParcelasBoleto, getItemReimpressaoBoletoVO().getDataVencimento(),
                                getUsuarioLogado(), OrigemCobrancaEnum.ZW_MANUAL_TELA_ALUNO, false, true));
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                montarErro(ex);
            }
        }
    }

    public void imprimirBoletosRemessaPerfilAluno(List<RemessaItemVO> lstRemessaItems, String key, Connection con, HttpServletRequest request) throws Exception{
        //Foram criados novos métodos adaptados dos originais pois a requisição vinda do perfil do aluno não tem diversas propriedades na classe que são utilizadas
        // e se fosse utilizadas, as alterações nelas ficariam mais complexas e passíveis de ocorrer erros.
        criarBoletosPerfilAluno(lstRemessaItems, key, con);
        Map<String, Object> params = new HashMap<String, Object>();
        prepareParamsTelaAluno(params, con);
        apresentarRelatorioObjetosComRequest(params, request, con);

        setMensagemDetalhada("", "");
        setMensagemID("msg_entre_prmrelatorio");
    }

    public void criarBoletosPerfilAluno(List<RemessaItemVO> itensImpressao, String key, Connection con) throws Exception {
        RemessaItem remessaItemDAO;
        try {
            remessaItemDAO = new RemessaItem(con);
            boletos = new ArrayList<JBoleto>();
            RemessaService service = new RemessaService();
            OAMDService oamdService = new OAMDService();
            for (RemessaItemVO ri : itensImpressao) {
                if (!UteisValidacao.emptyList(ri.getMovParcelas())) {
                    ri.setMesesAbertos(remessaItemDAO.consultarMesesEmAberto(ri));
                }
            }

            for (RemessaItemVO remessaItemVO : itensImpressao) {
                remessaItemVO.setIdentificadorEmpresaFinanceiro("");
                JBoletoBean boletoBean = service.gerarBoletoCobranca(remessaItemVO);
                processarNossoNumero(remessaItemVO, boletoBean);
                boolean layout240 = (remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                        remessaItemVO.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
                JBoleto boleto = new JBoleto(null, boletoBean, remessaItemVO.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco(), layout240);
                boletos.add(boleto);
            }
            oamdService = null;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            remessaItemDAO = null;
        }
    }

    private void prepareParamsTelaAluno(Map<String, Object> params, Connection con) throws Exception {

        EmpresaVO empresa = new EmpresaVO();
        if (getEmpresa() != null && getEmpresa().getCodigo() != 0) {
             if (con != null) {
                Empresa empresaDAO = new Empresa(con);
                try {
                    empresa = empresaDAO.consultarPorChavePrimaria(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } catch (Exception ex) {
                    throw ex;
                } finally {
                    empresaDAO = null;
                }
            }
        }

        params.put("nomeRelatorio", "Boleto");
        params.put("nomeEmpresa", empresa.getNome());

        params.put("tituloRelatorio", "Boleto");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeDesignSubReport", getDesignSubReportRelatorio());
        if (JSFUtilities.isJSFContext()) {
            params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        } else {
            params.put("nomeUsuario", "MAILING");
        }

        List<AuxiliarBoletoBancarioTO> auxiliarBoletoBancarioTOs = new ArrayList<AuxiliarBoletoBancarioTO>();
        for (JBoleto boleto : boletos) {
            AuxiliarBoletoBancarioTO auxiliar = new AuxiliarBoletoBancarioTO();
            auxiliar.setBoleto(boleto);
            auxiliarBoletoBancarioTOs.add(auxiliar);
        }

        params.put("listaObjetos", auxiliarBoletoBancarioTOs);

        StringBuilder sb = getEnderecoEmpresa(empresa);

        params.put("enderecoEmpresa", sb.toString());
        params.put("cnpjEmpresa", empresa.getCNPJ());
        params.put("cidadeEmpresa", empresa.getCidade().getNome());
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());

        byte[] propaganda = null;
        if (con != null) {
            Empresa empresaDAO = new Empresa(con);
            try {
                propaganda = empresaDAO.obterFotoPadrao(DAO.resolveKeyFromConnection(con), empresa.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO);
            } catch (Exception ex) {
                throw ex;
            } finally {
                empresaDAO = null;
            }
        }

        InputStream fs = null;
        if (propaganda != null)
            fs = new ByteArrayInputStream(propaganda);
        params.put("propaganda", fs);
    }

}
