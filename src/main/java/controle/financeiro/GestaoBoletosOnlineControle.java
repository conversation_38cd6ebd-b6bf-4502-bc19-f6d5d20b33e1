package controle.financeiro;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.impl.boleto.ThreadGerarBoletosOnlineGrandeVolume;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class GestaoBoletosOnlineControle extends SuperControleRelatorio {

    public static final String ABA_BOLETOS = "abaBoletos";
    public static final String ABA_PARCELAS = "abaParcelas";

    private boolean consultarInfoTodasEmpresas = false;
    private String empresaSelecionada;
    private EmpresaVO empresaVO;
    private List<SelectItem> conveniosSelectItens = new ArrayList<SelectItem>();
    private String convenioSelecionado;
    private ConvenioCobrancaVO convenio;
    private List<ConvenioCobrancaVO> listaConveniosCompleta;
    private List<SelectItem> listaSelectItemEmpresa;
    private Date dataInicio = Calendario.hoje();
    private Date dataFim = Calendario.hoje();
    private List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();
    private String emailNotificacaoTerminoProcesso;
    private Date dataVencimentoBoletos = Calendario.hoje();
    private List<BoletoVO> listaBoletos = new ArrayList<BoletoVO>();
    private String abaSelecionada = ABA_BOLETOS;

    public GestaoBoletosOnlineControle() throws Exception {
        permiteConsultarTodasEmpresas();
        inicializarEmpresa();
        carregarConvenios();
    }

    private void permiteConsultarTodasEmpresas() {
        try {
            boolean temPermissao = permissao("ConsultarInfoTodasEmpresas");
            consultarInfoTodasEmpresas = temPermissao;
        } catch (Exception ex) {
            setEmpresaVO(new EmpresaVO());
        }
    }

    public void inicializarEmpresa() throws Exception {
        try {
            //selecionar a empresa logada
            setEmpresaSelecionada(getEmpresaLogado().getCodigo().toString());
            selecionouEmpresa();
            montarListaSelectItemEmpresa();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void montarListaSelectItemEmpresa() {
        try {
            List<SelectItem> objs = new ArrayList<SelectItem>();
            if (consultarInfoTodasEmpresas) {
                List<EmpresaVO> empresasCadastradas = getFacade().getEmpresa().consultarPorCodigo(0, true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                Ordenacao.ordenarLista(empresasCadastradas, "nome");
                for (EmpresaVO obj : empresasCadastradas) {
                    objs.add(new SelectItem(obj.getCodigo().toString(), obj.getNome()));
                }
            } else {
                objs.add(new SelectItem(getEmpresaVO().getCodigo().toString(), getEmpresaVO().getNome()));
            }
            setListaSelectItemEmpresa(objs);
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList<SelectItem>());
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void selecionouEmpresaCarregarConvenios() throws Exception {
        selecionouEmpresa();
        carregarConvenios();
    }

    public void selecionouEmpresa() {
        try {
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(Integer.parseInt(getEmpresaSelecionada()), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
        }
    }

    private void carregarConvenios() throws Exception {
        setConveniosSelectItens(new ArrayList<>());
        setListaConveniosCompleta(new ArrayList<>());

        setListaConveniosCompleta(getFacade().getConvenioCobranca().consultarPorEmpresa(getEmpresaVO().getCodigo(),
                SituacaoConvenioCobranca.ATIVO, false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        Ordenacao.ordenarLista(getListaConveniosCompleta(), "descricao");
        for (ConvenioCobrancaVO convenioVO : getListaConveniosCompleta()) {
            if (convenioVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE)) {
                getConveniosSelectItens().add(new SelectItem(convenioVO.getCodigo(), convenioVO.getDescricao()));
            }
        }

        if (UteisValidacao.emptyList(getConveniosSelectItens())) {
            setConvenioSelecionado("");
        }

        for (SelectItem itemUnico : getConveniosSelectItens()) {
            if (itemUnico.getValue() != null) {
                selecionarConvenio();
                break;
            }
        }
    }

    public Object selecionarConvenio() {
        try {
            if (UteisValidacao.emptyString(getConvenioSelecionado())) {
                setConvenioSelecionado(getConveniosSelectItens().get(0).getValue().toString());
            }

            setConvenio(getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(Integer.parseInt(getConvenioSelecionado()), Uteis.NIVELMONTARDADOS_TODOS));
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
        return "";
    }

    public ConvenioCobrancaVO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }

    public List<SelectItem> getConveniosSelectItens() {
        if (conveniosSelectItens == null) {
            conveniosSelectItens = new ArrayList<>();
        }
        return conveniosSelectItens;
    }

    public void setConveniosSelectItens(List<SelectItem> conveniosSelectItens) {
        this.conveniosSelectItens = conveniosSelectItens;
    }

    public List<ConvenioCobrancaVO> getListaConveniosCompleta() {
        if (listaConveniosCompleta == null) {
            listaConveniosCompleta = new ArrayList<ConvenioCobrancaVO>();
        }
        return listaConveniosCompleta;
    }

    public void setListaConveniosCompleta(List<ConvenioCobrancaVO> listaConveniosCompleta) {
        this.listaConveniosCompleta = listaConveniosCompleta;
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList<SelectItem>();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public boolean isConsultarInfoTodasEmpresas() {
        return consultarInfoTodasEmpresas;
    }

    public void setConsultarInfoTodasEmpresas(boolean consultarInfoTodasEmpresas) {
        this.consultarInfoTodasEmpresas = consultarInfoTodasEmpresas;
    }

    public String getEmpresaSelecionada() {
        if (empresaSelecionada == null) {
            empresaSelecionada = "";
        }
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(String empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getConvenioSelecionado() {
        return convenioSelecionado;
    }

    public void setConvenioSelecionado(String convenioSelecionado) {
        this.convenioSelecionado = convenioSelecionado;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public List<MovParcelaVO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public void consultarInformacoes() {
        try {
            clsMessages();
            validarCamposParaConsulta();
            if (ABA_BOLETOS.equals(getAbaSelecionada())) {
                consultarBoletos();
            } else if (ABA_PARCELAS.equals(getAbaSelecionada())) {
                consultarParcelas();
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage() == null ? e.getClass().getName() : e.getMessage());
        }
    }

    private void consultarBoletos() throws Exception {
        List<BoletoVO> lista = new ArrayList<BoletoVO>();

        if (UteisValidacao.emptyString(getConvenioSelecionado())) {
            setListaBoletos(lista);
            throw new Exception("Não tem convênio selecionado.");
        }

        if (UteisValidacao.emptyString(getConvenioSelecionado()) && !UteisValidacao.emptyList(getConveniosSelectItens())) {
            setConvenioSelecionado(getConveniosSelectItens().get(0).getValue().toString());
        }
        if (!UteisValidacao.emptyString(getConvenioSelecionado()) && !getConvenioSelecionado().equals(getConvenio().getCodigo())) {
            setConvenio(getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(Integer.parseInt(getConvenioSelecionado()), Uteis.NIVELMONTARDADOS_TODOS));
        }

        List<BoletoVO> tmp = getFacade().getBoleto().consultarPorDataRegistro(getDataInicio(), getDataFim(), getConvenio(), getEmpresaVO());
        lista.addAll(tmp);

        if (UteisValidacao.emptyList(lista)) {
            setListaBoletos(lista);
            throw new Exception("Não foram encontrados boletos com os dados consultados.");
        }

        preencherConvenioCobrancaEPessoa(lista);

        setListaBoletos(Ordenacao.ordenarLista(lista, "dataRegistro DESC"));
    }

    private void preencherConvenioCobrancaEPessoa(List<BoletoVO> lista) throws Exception {
        //Um Map, para o caso de ter mais de um boleto para mesma pessoa, não consultar novamente
        Map<Integer, PessoaVO> pessoaVOMap = new HashMap<>();
        ConvenioCobrancaVO convenioCobrancaVOClone = (ConvenioCobrancaVO) getConvenio().getClone(false); //Clonar para não alterar o objeto na lista caso mude o valor do SelectItem
        for (BoletoVO boletoVO: lista) {
            boletoVO.setConvenioCobrancaVO(convenioCobrancaVOClone);

            Integer codigoPessoa = boletoVO.getPessoaVO().getCodigo();
            if (pessoaVOMap.containsKey(codigoPessoa)) {
                boletoVO.setPessoaVO(pessoaVOMap.get(codigoPessoa));
            } else {
                PessoaVO pessoaVO = getFacade().getPessoa().consultarPorCodigo(codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                boletoVO.setPessoaVO(pessoaVO);
                pessoaVOMap.put(codigoPessoa, pessoaVO);
            }
        }
    }

    private void consultarParcelas() throws Exception {
        List<MovParcelaVO> lista = new ArrayList<MovParcelaVO>();

        List<MovParcelaVO> tmp = getFacade().getMovParcela().consultarParcelasEmAbertoParaGerarBoletosOnline(getConvenio(), getDataInicio(), getDataFim(), getEmpresaVO());
        lista.addAll(tmp);

        if (UteisValidacao.emptyList(lista)) {
            setListaParcelas(lista);
            throw new Exception("Não foram encontradas parcelas em aberto para gerar boletos online.");
        }

        setListaParcelas(Ordenacao.ordenarLista(lista, "pessoa_Apresentar"));
        getFacade().getMovParcela().montarMultaJurosParcelaVencida(getEmpresaVO(), getConvenio().getTipo().getTipoCobranca(), getListaParcelas(), Calendario.hoje());
    }

    private void validarCamposParaConsulta() throws Exception {
        if (UteisValidacao.emptyNumber(getConvenio().getCodigo())) {
            throw new Exception("Selecione um convênio para realizar a consulta.");
        }

        if (!Calendario.maiorOuIgual(getDataFim(), getDataInicio())) {
            throw new Exception("A data fim deve ser maior ou igual à data de início.");
        }
    }

    public void processoGerarBoletosOnline() {
        try {
            clsMessages();
            gerarBoletosOnline();
            montarSucessoGrowl("Os boletos estão sendo gerados em segundo plano. Aguarde a finalização do processo. Você será notificado por e-mail.");
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage() == null ? e.getClass().getName() : e.getMessage());
        }
    }

    private void gerarBoletosOnline() throws Exception {
        if (UteisValidacao.emptyList(getListaParcelas())) {
            throw new Exception("Não tem parcelas para gerar os boletos.");
        }

        if (UteisValidacao.emptyString(getEmailNotificacaoTerminoProcesso())) {
            throw new Exception("Necessário informar um e-mail para receber Notificação de Termino do processo de Gerar Boletos Online.");
        }

        if (!UteisEmail.getValidEmail(getEmailNotificacaoTerminoProcesso())) {
            throw new Exception("E-mail informado não é válido.");
        }

        if (!Calendario.maiorOuIgual(getDataVencimentoBoletos(), Calendario.hoje())) {
            throw new Exception("A data de vencimento dos boletos deve ser maior ou igual à data de hoje.");
        }

        //Verificar se não tem MovParcela como GerandoBoletoPeloGestaoBoletosOnline para evitar gerar duplicado
        for (MovParcelaVO parcela : getListaParcelas()) {
            if (parcela.getGerandoBoletoPeloGestaoBoletosOnline()) {
                throw new Exception("Já existe um processo de geração de boleto para a parcela " + parcela.getDescricao() + ". Aguarde a finalização do processo.");
            }
        }

        //Se não tiver, colocar as MovParcelas como GerandoBoletoPeloGestaoBoletosOnline para evitar gerar duplicado
        //Na Thread, na medida que for processando os boletos, vai remover a flag de GerandoBoletoPeloGestaoBoletosOnline
        for (MovParcelaVO parcela : getListaParcelas()) {
            parcela.setGerandoBoletoPeloGestaoBoletosOnline(true);
        }
        getFacade().getMovParcela().mudarSituacaoGerandoBoletoGestaoBoletosOnline(true, getListaParcelas());

        //Deve gerar uma nova lista para enviar para a Thread com a data de vencimento das parcelas alteradas para a data que o cliente quer o vencimento dos boletos
        List<MovParcelaVO> listaParcelasAlterada = new ArrayList<>();
        for (MovParcelaVO parcela : getListaParcelas()) {
            MovParcelaVO parcelaAlterada = new MovParcelaVO();
            parcelaAlterada = (MovParcelaVO) parcela.getClone(false); //Clonar para não alterar a lista original
            parcelaAlterada.setDataVencimento(getDataVencimentoBoletos());
            listaParcelasAlterada.add(parcelaAlterada);
        }

        //Passando dados para Thread gerar os boletos em segundo plano e quando terminar, enviar e-mail par ao usuário informando o resultado
        ExecutorService executor = Executors.newSingleThreadExecutor();
        ThreadGerarBoletosOnlineGrandeVolume thread = new ThreadGerarBoletosOnlineGrandeVolume(listaParcelasAlterada, getConvenio(), getEmailNotificacaoTerminoProcesso(),
                getUsuarioLogado(), Conexao.getFromSession(), getEmpresaVO());
        executor.execute(thread);
        executor.shutdown(); //Finaliza o ExecutorService depois de terminar a Thread
    }

    public String getEmailNotificacaoTerminoProcesso() {
        return emailNotificacaoTerminoProcesso;
    }

    public void setEmailNotificacaoTerminoProcesso(String emailNotificacaoTerminoProcesso) {
        this.emailNotificacaoTerminoProcesso = emailNotificacaoTerminoProcesso;
    }

    public Date getDataVencimentoBoletos() {
        return dataVencimentoBoletos;
    }

    public void setDataVencimentoBoletos(Date dataVencimentoBoletos) {
        this.dataVencimentoBoletos = dataVencimentoBoletos;
    }

    public List<BoletoVO> getListaBoletos() {
        return listaBoletos;
    }

    public void setListaBoletos(List<BoletoVO> listaBoletos) {
        this.listaBoletos = listaBoletos;
    }

    public String getAbaSelecionada() {
        return abaSelecionada;
    }

    public void setAbaSelecionada(String abaSelecionada) {
        this.abaSelecionada = abaSelecionada;
    }

    //Método chamado no front-end. Mesmo se exibir cinza no Intellij, não deve ser apagado.
    public String getMatriculaAluno(Integer codigoPessoa) {
        String matricula = "";
        try {
            matricula = getFacade().getCliente().obterMatriculaPorCodigoPessoa(codigoPessoa);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return matricula;
    }

    public boolean exibirCamposGerarBoleto() {
        boolean exibirCamposGerarBoleto = false;

        if (!UteisValidacao.emptyList(getListaParcelas()) && getAbaSelecionada().equals(ABA_PARCELAS)) {
            exibirCamposGerarBoleto = true;
        }

        return exibirCamposGerarBoleto;
    }

}
