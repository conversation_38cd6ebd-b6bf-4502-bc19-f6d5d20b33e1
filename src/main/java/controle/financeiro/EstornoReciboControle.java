package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import com.fasterxml.jackson.databind.ObjectMapper;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.CupomFiscalVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.financeiro.PinPadPedidoVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.ReciboPagamentoFiltroVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.StatusImpressaoEnum;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.notaFiscal.NotaProcessarTO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.TimeZone;

public class EstornoReciboControle extends SuperControle {

    protected EstornoReciboVO estornoReciboVO;
    protected Boolean abrirRichConfirmacaoEstorno = false;
    protected Boolean abrirRichConfirmacaoEmissao = false;
    protected Boolean abrirRichConfirmacaoEmissaoPagamento = false;
    protected Boolean emissaoMovPagamento = false;
    protected Boolean emissaoCheque = false;
    protected Boolean emissaoCartao = false;
    protected Boolean apresentarBotaoEstorno = false;
    protected Boolean apresentarBotaoEmissao = false;
    private ReciboPagamentoVO recibo;
    private List<ChequeVO> listaCheques;
    private List<CartaoCreditoVO> listaCartoes;
    private String paramTipoConsulta;
    private UsuarioVO usuarioLiberacao;
    private Boolean temChequeComLote = Boolean.FALSE;
    private UsuarioVO usuarioEnvioNFSe = new UsuarioVO();
    private UsuarioVO usuarioEnvioNFCe = new UsuarioVO();
    private Boolean usaEcf = Boolean.FALSE;
    private Boolean usaEcfPagamento = Boolean.FALSE;
    private Boolean usaNFSe = false;
    private Boolean usaNFSePagamento = false;
    private boolean usaNFCe = false;
    private boolean usaNFCePagamento = false;
    private MovPagamentoVO pagamentoVO;
    private ChequeVO chequeVO;
    private CartaoCreditoVO cartaoVO;
    private CaixaVO caixaAberto = null;
    private Boolean estornoCappta = false;
    private PinpadTO pinpad;
    private Boolean checkoutPinPad = false;
    private Date dataEstorno = Calendario.hoje();
    private Boolean dataRetroativa = false;
    private String estornarTransacoes = "";
    private boolean estornoGetcard = false;
    private boolean estornoGetcardConfirmar = false;
    private boolean exibirBotaoAbortarEstorno = false;
    private PinPadPedidoVO pinPadPedidoVO;
    private PinPadPedidoVO pinPadPedidoOrigem;
    private String onCompletePinpad;
    private boolean permissaoEstronarTransacao;
    public EstornoReciboControle() throws Exception {
        setControleConsulta(new ControleConsulta());
        setEstornoReciboVO(new EstornoReciboVO());
        setAbrirRichConfirmacaoEstorno(true);
        setAbrirRichConfirmacaoEmissao(true);
        setApresentarBotaoEstorno(true);
        inicializarFacades();
        limparMsg();
        validaPermissaoVisuaalizarBotao();
    }


    private void validaPermissaoVisuaalizarBotao() throws Exception {
        if (JSFUtilities.isJSFContext()) {
            ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
            if (clienteControle.validarPermisaoUsuario("GestaoTransacoes", "4.20 - Gestão Transações")) {
                setPermissaoEstronarTransacao(true);
            } else {
                setPermissaoEstronarTransacao(false);
            }
        } else {
            setPermissaoEstronarTransacao(true);
        }
    }

    public void abrirModalNFSe() throws Exception {
        usuarioEnvioNFSe = getUsuarioLogado();
    }

    public void abrirModalNFCe() throws Exception {
        usuarioEnvioNFCe = getUsuarioLogado();
    }

    public String selecionarRecibo() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            setRecibo(getFacade().getReciboPagamento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS));
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
        return preencherRecibo();
    }

    public String preencherRecibo() {
        notificarRecursoEmpresa(RecursoSistema.RECIBO_CLIENTE_CONSULTA);
        try {
            dataEstorno = Calendario.hoje();
            dataRetroativa = permissao("DataEstornoRecibo");
            temChequeComLote = false;
            boolean cupomEmitido = false;
            setEstornarTransacoes("");
            getRecibo().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getRecibo().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            getEstornoReciboVO().setReciboPagamentoVO(getRecibo());
            getEstornoReciboVO().setListaMovPagamento(getFacade().getMovPagamento().consultarPorCodigoRecibo(getRecibo().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            getEstornoReciboVO().setListaMovParcela(getFacade().getMovParcela().consultarPorCodigoRecibo(getRecibo().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            getFacade().getCupomFiscal().setCon(getFacade().getMovPagamento().getCon());
            //consulta as transações de cartão de crédito relacionadas aos contratos do recibo
            getEstornoReciboVO().montarListaTransacoes(getEstornoReciboVO().getListaMovParcela(), getFacade().getMovPagamento().getCon());
            getEstornoReciboVO().montarListaItensRemessa(getEstornoReciboVO().getListaMovParcela(), getEstornoReciboVO().getListaMovPagamento(), getFacade().getMovPagamento().getCon());
            JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoes", getEstornoReciboVO().getListaTransacoes());
            JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.consultarParaExportar", false);
            JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaEstorno", getEstornoReciboVO().getListaItensRemessa());
            setUsaEcf(getFacade().getConfiguracaoSistema().consultarUsaEcf());
            setUsaNFSe(getEmpresaLogado().getUsarNFSe());
            setUsaNFCe(getEmpresaLogado().isUsarNFCe());
            boolean permitirEstorno = true;
            if (getUsaEcf()) {
                setUsaEcfPagamento(getFacade().getConfiguracaoSistema().consultarUsaEcfPagamento());
                CupomFiscalVO cupom = getFacade().getCupomFiscal().consultaPorRecibo(getRecibo().getCodigo());
                boolean apresentarBotaoEmissao = (cupom == null);
                if (apresentarBotaoEmissao && getUsaEcfPagamento()) {
                    Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
                    while (i.hasNext()) {
                        MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                        cupom = getFacade().getCupomFiscal().consultaPorPagamento(pagamento.getCodigo());
                        if ((cupom == null || cupom.getCodigo().equals(0))) {
                            if (!UteisValidacao.emptyNumber(pagamento.getMovPagamentoOrigemCredito())) {
                                cupom = getFacade().getCupomFiscal().consultaPorPagamento(pagamento.getMovPagamentoOrigemCredito());
                            } else if (Uteis.arredondarForcando2CasasDecimais(pagamento.getValor()) == 0.00) {
                                List<MovPagamentoVO> filhos = getFacade().getMovPagamento().consultarCreditosDependentes(pagamento.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                for (MovPagamentoVO filho : filhos) {
                                    cupom = getFacade().getCupomFiscal().consultaPorPagamento(filho.getCodigo());
                                    if (cupom != null && cupom.getCodigo() > 0) {
                                        break;
                                    }
                                }
                            }
                        }
                        if (cupom != null && cupom.getCodigo() > 0) {
                            pagamento.setCupomEmitido(true);
                            apresentarBotaoEmissao = false;
                            if (cupom.getStatusImpressao() == StatusImpressaoEnum.SUCESSO) {
                                permitirEstorno = false;
                                cupomEmitido = true;
                            }
                        }
                    }
                } else {
                    Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
                    while (i.hasNext()) {
                        MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                        pagamento.setCupomEmitido(true);
                    }
                    if (cupom != null && cupom.getStatusImpressao() == StatusImpressaoEnum.SUCESSO) {
                        permitirEstorno = false;
                        cupomEmitido = true;
                    }
                }
                this.setApresentarBotaoEmissao(apresentarBotaoEmissao);
            }

            if (getUsaNFSe()) {
                setUsaNFSePagamento(getEmpresaLogado().isUsarNFSePorPagamento());

                NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultarPorRecibo(getRecibo().getCodigo());

                boolean apresentarBotaoNFSe = (nfSeEmitidaVO == null);

                if (apresentarBotaoNFSe && getUsaNFSePagamento()) {
                    Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
                    while (i.hasNext()) {
                        MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                        nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorPagamento(pagamento.getCodigo());
                        if (nfSeEmitidaVO != null && nfSeEmitidaVO.getCodigo() > 0) {
                            pagamento.setNfseEmitido(true);
                            apresentarBotaoNFSe = false;
                        }
                    }
                } else {
                    Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
                    while (i.hasNext()) {
                        MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                        pagamento.setNfseEmitido(true);
                    }
                }
            }

            if (isUsaNFCe()) {
                setUsaNFCePagamento(getEmpresaLogado().isUsarNFCePorPagamento());

                NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO = getFacade().getNotaFiscalConsumidorEletronica().consultarPorReciboPagamento(getRecibo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                boolean apresentarBotaoNFCe = UteisValidacao.emptyNumber(notaFiscalConsumidorEletronicaVO.getCodigo());

                if (apresentarBotaoNFCe && isUsaNFCePagamento()) {
                    Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
                    while (i.hasNext()) {
                        MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                        notaFiscalConsumidorEletronicaVO = getFacade().getNotaFiscalConsumidorEletronica().consultaPorMovPagamento(pagamento.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (!UteisValidacao.emptyNumber(notaFiscalConsumidorEletronicaVO.getCodigo())) {
                            pagamento.setNfceEmitido(true);
                            apresentarBotaoNFCe = false;
                        }
                    }
                } else {
                    Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
                    while (i.hasNext()) {
                        MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                        pagamento.setNfceEmitido(true);
                    }
                }
            }

            ConfiguracaoFinanceiroVO confFinan = getFacade().getConfiguracaoFinanceiro().consultar();
            if (confFinan.getUsarMovimentacaoContas()) {
                for (MovPagamentoVO movPag : getEstornoReciboVO().getListaMovPagamento()) {
                    if (!movPag.getCredito()) {
                        if (getFacade().getCheque().verificarContemLote(movPag.getCodigo())
                                || getFacade().getCartaoCredito().verificarContemLote(movPag.getCodigo())) {
                            temChequeComLote = Boolean.TRUE;
                            break;
                        }
                    }
                }
            }
            //!apresentarBotaoEmissao significa que há um cupom impresso para este recibo
            //o status de impressão como sucesso significa que o cupom já foi impresso no módulo fiscal
            //nesse caso não será permitido o estorno do recibo
            setApresentarBotaoEstorno(apresentarBotaoEmissao || permitirEstorno);

            inicializarUsuarioLogado();
            montarHistoricoChequesCartoes();
            if (cupomEmitido) {
                throw new ConsistirException("Esse recibo não pode ser estornado, porque já teve cupom fiscal impresso.");
            }
            setMensagemDetalhada("msg_dados_consultados", "");
            setSucesso(false);
            setErro(false);

            //verificar estorno cappta
            estornoCappta = false;
            estornoGetcard = false;
            estornoGetcardConfirmar = false;
            PinpadTO ppt = new PinpadTO();
            for (MovPagamentoVO mpgto : getEstornoReciboVO().getListaMovPagamento()) {
                try {
                    if (!UteisValidacao.emptyString(mpgto.getRespostaRequisicaoPinpad())) {
                        PinpadDadosTO dadosTO = new PinpadDadosTO(new JSONObject(mpgto.getRespostaRequisicaoPinpad()));
                        if (dadosTO.getPinpadEnum().equals(OpcoesPinpadEnum.GETCARD) &&
                                !UteisValidacao.emptyNumber(dadosTO.getPinpadPedido())) {
                            this.pinPadPedidoOrigem = getFacade().getPinPadPedido().consultarPorChavePrimaria(dadosTO.getPinpadPedido());
                            if (!UteisValidacao.emptyNumber(this.getPinPadPedidoOrigem().getCodigo()) &&
                                    !UteisValidacao.emptyString(this.getPinPadPedidoOrigem().getNrControle()) &&
                                    !UteisValidacao.emptyNumber(this.getPinPadPedidoOrigem().getConvenioCobrancaVO().getCodigo())) {
                                this.getPinPadPedidoOrigem().setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorChavePrimaria(this.getPinPadPedidoOrigem().getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                this.setPinPadPedidoVO((PinPadPedidoVO) this.getPinPadPedidoOrigem().getClone(true));
                                this.getPinPadPedidoVO().setCodigo(null);
                                this.getPinPadPedidoVO().setNovoObj(true);
                                this.getPinPadPedidoVO().setPinPadPedidoOrigem(this.getPinPadPedidoOrigem().getCodigo());
                                this.pinpad = new PinpadTO();
                                this.pinpad.setPinpadEnum(OpcoesPinpadEnum.GETCARD);
                                this.pinpad.setNrControle(this.getPinPadPedidoVO().getNrControle());
                                this.estornoGetcard = true;
                            }
                        }

                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                ppt.setNrControleAut(mpgto);

                //Adicionado número de dias e validação porquê na documentação da Cappta, só permete o Cancelamento de cobrança efetuada no mesmo dia.
                //Se deixar ficar aparecendo o botão vai dar falsa impressão para o usuário, podendo abrir reclamação que tenta cancelar e não funciona, sendo uma negação da Cappta.
                Integer numeroDiasAposPagamento = Calendario.diferencaEmDias(mpgto.getDataLancamento(), Calendario.hoje());
                if (StringUtils.isNotBlank(ppt.getNrControle()) && numeroDiasAposPagamento <= 1) {
                    montarSelectItemPinPadEstorno(mpgto.getFormaPagamento().getCodigo());
                    estornoCappta = true;
                    break;
                }
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
        return "editar";
    }

    public void habilitaPinPadEstorno() {
        try {
            setMsgAlert("");
            checkoutPinPad = false;
            for (MovPagamentoVO mpgto : getEstornoReciboVO().getListaMovPagamento()) {
                if (OpcoesPinpadEnum.CAPPTA.getCodigo().equals(mpgto.getFormaPagamento().getPinpad().getPinpad())) {
                    pinpad = new PinpadTO();
                    pinpad.setNrControleAut(mpgto);
                    checkoutPinPad = true;
                    break;
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void montarSelectItemPinPadEstorno(Integer codigoFormaPagamamento) {
        List objs = new ArrayList();
        String descricao = "";
        objs.add(new SelectItem(null, "Informe um pdv"));

        try {
            List<PinPadVO> resultadoConsulta = getFacade().getPinPad().consultarPorForma(codigoFormaPagamamento);
            if (resultadoConsulta != null) {
                for (PinPadVO obj : resultadoConsulta) {
                    if (obj.getCodigo() != 0 || obj.getCodigo() != null) {
                        if (getEmpresaLogado().getCodigo() == obj.getEmpresa().getCodigo() || obj.getEmpresa().getCodigo() == 0) {
                            descricao = StringUtils.isBlank(obj.getDescricao()) ? OpcoesPinpadEnum.fromCodigo(obj.getPinpad()).getNome() : obj.getDescricao();
                            objs.add(new SelectItem(obj.getCodigo(), String.format(descricao + " - " + obj.getPdvPinpad())));
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void preparaRecibo(ActionEvent evt) throws Exception {
        ReciboPagamentoVO reciboVO = (ReciboPagamentoVO) evt.getComponent().
                getAttributes().get("reciboPagamentoVO");
        setRecibo(getFacade().getReciboPagamento().consultarPorChavePrimaria(reciboVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
    }

    public void preparaRecibo(Integer codigoRecibo) throws Exception {
        setRecibo(getFacade().getReciboPagamento().consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TODOS));
    }

    public void inicializarUsuarioLogado() throws Exception {
        try {
            if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo() != 0) {
                getEstornoReciboVO().getResponsavelEstornoRecivo().setCodigo(getUsuarioLogado().getCodigo());
                getEstornoReciboVO().getResponsavelEstornoRecivo().setUsername(getUsuarioLogado().getUsername());
                getEstornoReciboVO().getResponsavelEstornoRecivo().setNome(getUsuarioLogado().getNome());
                getEstornoReciboVO().getResponsavelEstornoRecivo().setUserOamd(getUsuarioLogado().getUserOamd());
                getEstornoReciboVO().getResponsavelEstornoRecivo().setAdministrador(getUsuarioLogado().getAdministrador());
            } else {
                throw new Exception("Não há usuário logado.");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getReciboPagamento().consultarPorCodigo(valorInt, false,
                        Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getReciboPagamento().consultarPorNomePessoa(
                        getControleConsulta().getValorConsulta(), false,
                        Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }

    }

    public void consultarResponsavelEstornoRecibo() {
        try {
            getEstornoReciboVO().setResponsavelEstornoRecivo(getFacade().getUsuario().consultarPorChavePrimaria(getEstornoReciboVO().getResponsavelEstornoRecivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getEstornoReciboVO().getResponsavelEstornoRecivo().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void consultarResponsavelNFSE() {
        try {
            setUsuarioEnvioNFSe(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioEnvioNFSe().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            limparMsg();
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void consultarResponsavelNFCe() {
        try {
            setUsuarioEnvioNFCe(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioEnvioNFCe().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            limparMsg();
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void confirmarEstornoReciboComNFSe() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                usuarioLiberacao = auto.getUsuario();
                getEstornoReciboVO().setResponsavelEstornoRecivo(auto.getUsuario());
                gravarExcluindoNFSe();
                setExecutarAoCompletar("Richfaces.hideModalPanel('panelConfirmacaoEstornar'); fireElementFromParent('form:btnAtualizaCliente');");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }

        };

        limparMsg();
        try {
            auto.autorizar("Confirmação de Estornar Recibo con NFSE", "EstornoRecibo",
                    "Utilize esta opção somente se o prazo para cancelamento da NFSe estiver expirado, pois a academia poderá pagar o imposto duas vezes sobre o mesmo valor." +
                            "<br>Deseja estornar o recibo mesmo assim? Você precisa da permissão \"4.12 - Estornar recibo\"",
                    "form,panelConfirmacao,msg,panelMensagem, mdlMensagemGenerica, formConfirmacaoEstornar", listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String gravarExcluindoNFSe() {
        getEstornoReciboVO().setExcluirNFSe(true);
        return gravar();
    }

    public String gravar() {
        return gravar(null);
    }

    public String gravarPinpad() {
        return gravar(OpcoesPinpadEnum.CAPPTA);
    }

    public String gravarPinpadGetcard() {
        return gravar(OpcoesPinpadEnum.GETCARD);
    }

    public String gravar(OpcoesPinpadEnum opcoesPinpadEnum) {
        try {
            try {
                if (opcoesPinpadEnum != null && opcoesPinpadEnum.equals(OpcoesPinpadEnum.GETCARD)) {
                    this.setApresentarBotaoEmissao(false);
                    this.setEstornoGetcardConfirmar(false);
                    this.getPinPadPedidoVO().setStatus(StatusPinpadEnum.ESTORNADA);
                    getFacade().getPinPadPedido().alterar(this.getPinPadPedidoVO());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (!UteisValidacao.emptyList(getEstornoReciboVO().getListaTransacoes()) && UteisValidacao.emptyString(getEstornarTransacoes())) {
                throw new Exception("Deve ser selecionado se deseja estornar as transações");
            }
            getEstornoReciboVO().setEstornarOperadora(getEstornarTransacoes().equalsIgnoreCase("SIM"));

            getEstornoReciboVO().setMostrarMsgExcluirNFse(false);
            //quando o cancelamento é feito via pinpad, não precisa validar senha de usuario. existe já toda uma segunrança envolvida
            //que é a senha da administradora e o cartão do aluno
            if (opcoesPinpadEnum != null) {
                this.usuarioLiberacao = getUsuarioLogado();
            }
            context().getExternalContext().getRequestMap().put("responsavelOperacao", this.getEstornoReciboVO().getResponsavelEstornoRecivo());

            if (getIntegraProtheus()) {
                getFacade().getReciboPagamento().inserirIntegracaoProtheus(getEstornoReciboVO().getReciboPagamentoVO().getCodigo(), "E", dataRetroativa ? dataEstorno : null);
            }

            getFacade().getReciboPagamento().estornarReciboPagamento(
                    getEstornoReciboVO(),
                    getFacade().getMovPagamento(),
                    getFacade().getMovProdutoParcela(),
                    getCaixaAberto(), getKey(),
                    dataRetroativa ? dataEstorno : null);
            setAbrirRichConfirmacaoEstorno(false);
            montarSucessoGrowl("Recibo estornado com sucesso!");
            setApresentarBotaoEstorno(false);
            setApresentarBotaoEmissao(false);

            if (!getEstornoReciboVO().getListaTransacoes().isEmpty()) {
                List<TransacaoVO> transacoes = getEstornoReciboVO().getListaTransacoes();
                String msgTransacoesEstornarCielo = "";
                for (TransacaoVO transacaoVO : transacoes) {
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)
                            && !transacaoVO.getAutorizacao().isEmpty()) {
                        msgTransacoesEstornarCielo += transacaoVO.getCodigoExterno() + ", ";
                    }
                }
                List<RemessaItemVO> itens = getEstornoReciboVO().getListaItensRemessa();
                for (RemessaItemVO item : itens) {
                    if (!item.getAutorizacao().equals("000000")) {
                        msgTransacoesEstornarCielo += item.getAutorizacao() + ", ";
                    }
                }
                if (!msgTransacoesEstornarCielo.isEmpty()) {
                    setMensagemDetalhada(String.format("Atenção! O Estorno foi concluído no ZillyonWeb, mas as transações %s precisam ser Estornadas junto à Adminisradora.",
                            new Object[]{msgTransacoesEstornarCielo}));
                }
            }
            if (getDataRetroativa()) {
                if (Calendario.diferencaEmDias(getDataEstorno(), getHoje()) != 0) {
                    //Notificar quando data for diferente de hoje
                    notificarRecursoEmpresa(RecursoSistema.ESTORNO_RECIBO_INFORMANDO_DATA_ESTORNO);
                }
            }
        } catch (ConsistirException ex) {
            setApresentarBotaoEstorno(true);
            setSucesso(false);
            setApresentarBotaoEmissao(true);
            setAbrirRichConfirmacaoEstorno(true);
            if (ex.getMessage().contains("existe nota fiscal emitida")) {
                getEstornoReciboVO().setMostrarMsgExcluirNFse(true);
            }
            setMensagemDetalhada(ex.getMessage());

        } catch (Exception e) {
            setSucesso(false);
            setApresentarBotaoEstorno(true);
            setApresentarBotaoEmissao(true);
            setAbrirRichConfirmacaoEstorno(true);
            montarErro(e);
            e.printStackTrace();
        }
        return "";
    }

    public String emitirCupom() {
        try {
            validarPermissaoCupom();
            getFacade().getCupomFiscalService().setUsuarioLiberacao(usuarioLiberacao);
            getFacade().getCupomFiscalService().incluirCupom(this.getEstornoReciboVO().getReciboPagamentoVO(), true);
            setAbrirRichConfirmacaoEmissao(false);
            Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                pagamento.setCupomEmitido(true);
            }
            if (!getListaCheques().isEmpty()) {
                Iterator j = getListaCheques().iterator();
                while (j.hasNext()) {
                    ChequeVO cheque = (ChequeVO) j.next();
                    cheque.setCupomEmitido(true);
                }
            }
            if (!getListaCartoes().isEmpty()) {
                Iterator j = getListaCartoes().iterator();
                while (j.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) j.next();
                    cartao.setCupomEmitido(true);
                }
            }

            setApresentarBotaoEmissao(false);
        } catch (Exception e) {
            setApresentarBotaoEstorno(true);
            setSucesso(false);
            setErro(true);
            setAbrirRichConfirmacaoEstorno(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public String emitirCupomPagamento() {
        try {
            validarPermissaoCupom();
            getFacade().getCupomFiscalService().setUsuarioLiberacao(usuarioLiberacao);
            getFacade().getCupomFiscalService().incluirCupomPagamento(this.getPagamentoVO(), null, null, true);
            Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
            this.getPagamentoVO().setCupomEmitido(true);
            if (this.getPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                Iterator j = getListaCheques().iterator();
                while (j.hasNext()) {
                    ChequeVO cheque = (ChequeVO) j.next();
                    if (this.getPagamentoVO().getCodigo().equals(cheque.getMovPagamento())) {
                        cheque.setCupomEmitido(true);
                    }
                }
            }
            if (this.getPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                Iterator j = getListaCartoes().iterator();
                while (j.hasNext()) {
                    CartaoCreditoVO cartao = (CartaoCreditoVO) j.next();
                    if (this.getPagamentoVO().getCodigo().equals(cartao.getMovpagamento().getCodigo())) {
                        cartao.setCupomEmitido(true);
                    }
                }
            }

            setAbrirRichConfirmacaoEmissaoPagamento(false);
            setApresentarBotaoEmissao(false);
        } catch (Exception e) {
            setApresentarBotaoEstorno(true);
            setSucesso(false);
            setErro(true);
            setAbrirRichConfirmacaoEstorno(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public String emitirCupomCheque() {
        try {
            validarPermissaoCupom();
            getFacade().getCupomFiscalService().setUsuarioLiberacao(usuarioLiberacao);
            Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                if (pagamento.getCodigo().equals(getChequeVO().getMovPagamento())) {
                    getFacade().getCupomFiscalService().incluirCupomPagamento(pagamento, getChequeVO(), null, true);
                    pagamento.setCupomEmitido(true);
                }
            }
            Iterator j = getListaCheques().iterator();
            while (j.hasNext()) {
                ChequeVO cheque = (ChequeVO) j.next();
                if (cheque.getCodigo().equals(getChequeVO().getCodigo())) {
                    cheque.setCupomEmitido(true);
                }
            }
            setAbrirRichConfirmacaoEmissaoPagamento(false);
            setApresentarBotaoEmissao(false);
        } catch (Exception e) {
            setApresentarBotaoEstorno(true);
            setSucesso(false);
            setErro(true);
            setAbrirRichConfirmacaoEstorno(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public String emitirCupomCartao() {
        try {
            validarPermissaoCupom();
            getFacade().getCupomFiscalService().setUsuarioLiberacao(usuarioLiberacao);
            Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                if (pagamento.getCodigo().equals(getCartaoVO().getMovpagamento().getCodigo())) {
                    getFacade().getCupomFiscalService().incluirCupomPagamento(pagamento, null, getCartaoVO(), true);
                    pagamento.setCupomEmitido(true);
                }
            }
            Iterator j = getListaCartoes().iterator();
            while (j.hasNext()) {
                CartaoCreditoVO cartao = (CartaoCreditoVO) j.next();
                if (cartao.getCodigo().equals(getCartaoVO().getCodigo())) {
                    cartao.setCupomEmitido(true);
                }
            }
            setAbrirRichConfirmacaoEmissaoPagamento(false);
            setApresentarBotaoEmissao(false);
        } catch (Exception e) {
            setApresentarBotaoEstorno(true);
            setSucesso(false);
            setErro(true);
            setAbrirRichConfirmacaoEstorno(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public void prepararCupomPagamento(ActionEvent evt) {
        setarPagamentoVO(evt);
        setAbrirRichConfirmacaoEmissaoPagamento(true);
    }

    public void prepararNFSePagamento(ActionEvent evt) throws Exception {
        abrirModalNFSe();
        setarPagamentoVO(evt);
    }

    public void prepararNFCePagamento(ActionEvent evt) throws Exception {
        abrirModalNFCe();
        setarPagamentoVO(evt);
    }

    private void setarPagamentoVO(ActionEvent evt) {
        MovPagamentoVO pagamento = (MovPagamentoVO) evt.getComponent().getAttributes().get("pagamento");
        if (pagamento != null) {
            setPagamentoVO(pagamento);
        }
        setEmissaoCartao(false);
        setEmissaoCheque(false);
        setEmissaoMovPagamento(true);
    }

    public void prepararCupomCheque(ActionEvent evt) {
        setarChequeVO(evt);
        setAbrirRichConfirmacaoEmissaoPagamento(true);
    }

    private void setarChequeVO(ActionEvent evt) {
        ChequeVO cheque = (ChequeVO) evt.getComponent().getAttributes().get("cheque");
        if (cheque != null) {
            setChequeVO(cheque);
        }
        setEmissaoCartao(false);
        setEmissaoCheque(true);
        setEmissaoMovPagamento(false);
    }

    public void prepararNFSeCheque(ActionEvent evt) throws Exception {
        abrirModalNFSe();
        setarChequeVO(evt);
    }

    public void prepararNFCeCheque(ActionEvent evt) throws Exception {
        abrirModalNFCe();
        setarChequeVO(evt);
    }

    public void prepararCupomCartao(ActionEvent evt) {
        setarCartaoVO(evt);
        setAbrirRichConfirmacaoEmissaoPagamento(true);
    }

    private void setarCartaoVO(ActionEvent evt) {
        CartaoCreditoVO cartao = (CartaoCreditoVO) evt.getComponent().getAttributes().get("cartao");
        if (cartao != null) {
            setCartaoVO(cartao);
        }
        setEmissaoCartao(true);
        setEmissaoCheque(false);
        setEmissaoMovPagamento(false);
    }

    public void prepararNFSeCartao(ActionEvent evt) throws Exception {
        abrirModalNFSe();
        setarCartaoVO(evt);
    }

    public void prepararNFCeCartao(ActionEvent evt) throws Exception {
        abrirModalNFCe();
        setarCartaoVO(evt);
    }

    public void emitirNFSe() {
        try {
            setMsgAlert("");
            validarPermissaoNFSE();
            this.getEstornoReciboVO().getReciboPagamentoVO().setPagamentosDesteRecibo(this.getEstornoReciboVO().getListaMovPagamento());
            NotaProcessarTO notaProcessarTO = new NotaProcessarTO(getKey());

            if (getEmissaoCartao()) {
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaCartaoCredito(TipoNotaFiscalEnum.NFSE, this.getCartaoVO(), getUsuarioLogado(), getKey());
            } else if (getEmissaoCheque()) {
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaCheque(TipoNotaFiscalEnum.NFSE, this.getChequeVO(), getUsuarioLogado(), getKey());
            } else if (getEmissaoMovPagamento()) {
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaMovPagamento(TipoNotaFiscalEnum.NFSE, this.getPagamentoVO(), getUsuarioLogado(), getKey());
            } else {
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, this.getEstornoReciboVO().getReciboPagamentoVO(), getUsuarioLogado(), getKey());
            }

            if (!notaProcessarTO.isSucesso()) {
                throw new Exception(notaProcessarTO.getRetorno());
            }


            try {
                LogVO obj = new LogVO();
                obj.setChavePrimaria(getRecibo().getCodigo().toString());
                obj.setNomeEntidade("CLIENTE");
                obj.setNomeEntidadeDescricao("Cliente - NFSE");
                obj.setOperacao("EMISSÃO DE NOTA");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setNomeCampo("MENSAGEM");
                obj.setValorCampoAlterado("EMISSÃO DE NOTA - RECIBO " + getRecibo().getCodigo() + " - VALOR " + getEmpresaLogado().getMoeda() + " " + getRecibo().getValorTotal() + "");
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                registrarLogObjetoVO(obj, getRecibo().getPessoaPagador().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("CLIENTE", getRecibo().getPessoaPagador().getCodigo(), "ERRO AO EMITIR NFSE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("msg_dadosEnviados", "");
            setMsgAlert("Richfaces.hideModalPanel('panelConfirmacaoNFSE');");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);

            if (e instanceof SocketTimeoutException) {
                setMensagemDetalhada("msg_erro", "Serviço de emissão de notas indisponível, por favor tente novamente em alguns minutos");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

        }
    }

    public void emitirNFCe() {
        try {
            setMsgAlert("");
            validarPermissaoNFCe();
            this.getEstornoReciboVO().getReciboPagamentoVO().setPagamentosDesteRecibo(this.getEstornoReciboVO().getListaMovPagamento());
            NotaProcessarTO notaProcessarTO = new NotaProcessarTO(getKey());
            if (getEmissaoCartao()) {
                MovPagamentoVO movPagamentoVO = getFacade().getMovPagamento().consultarPorChavePrimaria(this.getCartaoVO().getMovpagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaCartaoCredito(TipoNotaFiscalEnum.NFCE, this.getCartaoVO(), getUsuarioEnvioNFCe(), getKey());
            } else if (getEmissaoCheque()) {
                MovPagamentoVO movPagamentoVO = getFacade().getMovPagamento().consultarPorChavePrimaria(this.getChequeVO().getMovPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaCheque(TipoNotaFiscalEnum.NFCE, this.getChequeVO(), getUsuarioEnvioNFCe(), getKey());
            } else if (getEmissaoMovPagamento()) {
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaMovPagamento(TipoNotaFiscalEnum.NFCE, this.getPagamentoVO(), getUsuarioEnvioNFCe(), getKey());
            } else {
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(this.getEstornoReciboVO().getReciboPagamentoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.getEstornoReciboVO().getReciboPagamentoVO().setEmpresa(empresaVO);
                notaProcessarTO = getFacade().getNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFCE, this.getEstornoReciboVO().getReciboPagamentoVO(), getUsuarioEnvioNFCe(), getKey());
            }
            if (!notaProcessarTO.isSucesso()) {
                throw new Exception(notaProcessarTO.getRetorno());
            }

            try {
                LogVO obj = new LogVO();
                obj.setChavePrimaria(getRecibo().getCodigo().toString());
                obj.setNomeEntidade("CLIENTE");
                obj.setNomeEntidadeDescricao("Cliente - NFCe");
                obj.setOperacao("EMISSÃO DE NOTA");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setNomeCampo("MENSAGEM");
                obj.setValorCampoAlterado("EMISSÃO DE NOTA - RECIBO " + getRecibo().getCodigo() + " - VALOR " + getEmpresaLogado().getMoeda() + " " + getRecibo().getValorTotal() + "");
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                registrarLogObjetoVO(obj, getRecibo().getPessoaPagador().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("CLIENTE", getRecibo().getPessoaPagador().getCodigo(), "ERRO AO EMITIR NFCe", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            montarSucesso("msg_dadosEnviados");
            setMsgAlert("Richfaces.hideModalPanel('panelConfirmacaoNFCE');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getOnclickNFSe() {
        return this.getEstornoReciboVO().getReciboPagamentoVO().getNfseEmitida()
                ? "if(!confirm('" + getMensagemInternalizacao("msg_notajaemitida") + "')){return false;}" : "";
    }

    public void validarPermissaoEstornaReciboPagamento() throws Exception {
        if (!getEstornoReciboVO().isExcluirNFSe()) {
            UsuarioVO usuario = getFacade().getControleAcesso().verificarLoginUsuario(getEstornoReciboVO().getResponsavelEstornoRecivo().getCodigo(), getEstornoReciboVO().getResponsavelEstornoRecivo().getSenha().toUpperCase());
            this.usuarioLiberacao = usuario;
            validarPermissao("EstornoRecibo", "Estorno do  Recibo.", usuario);
        }
    }

    private void validarPermissaoCupom() throws Exception {
        UsuarioVO usuario = getFacade().getControleAcesso().verificarLoginUsuario(getEstornoReciboVO().getResponsavelEstornoRecivo().getCodigo(), getEstornoReciboVO().getResponsavelEstornoRecivo().getSenha().toUpperCase());
        this.usuarioLiberacao = usuario;
        validarPermissao("CupomFiscal", "Acesso aos cupons fiscais.", usuario);
    }

    private void validarPermissaoNFSE() throws Exception {
        UsuarioVO usuario = getFacade().getControleAcesso().verificarLoginUsuario(getUsuarioEnvioNFSe().getCodigo(), getUsuarioEnvioNFSe().getSenha().toUpperCase());
        validarPermissao("EnvioNFSe", "Envio NFSe", usuario);
    }

    private void validarPermissaoNFCe() throws Exception {
        UsuarioVO usuario = getFacade().getControleAcesso().verificarLoginUsuario(getUsuarioEnvioNFCe().getCodigo(), getUsuarioEnvioNFCe().getSenha().toUpperCase());
        validarPermissao("GestaoNFCe", "Gestão de NFCe", usuario);
    }

    public boolean getDesenharColunaNomeContrato() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && getEstornoReciboVO().getApresentarNomePorContrato(obj);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public boolean getDesenharColunaNomeVendaAvulsa() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && getEstornoReciboVO().getApresentarNomePorVendaAvulsa(obj);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public boolean getDesenharColunaNomeAulaAvusa() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && getEstornoReciboVO().getApresentarNomePorAulaAvulsaDiaria(obj);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public String getFecharRichModalPanelConfirmacao() {
        if (getAbrirRichConfirmacaoEstorno()) {
            return "Richfaces.showModalPanel('panelConfirmacaoEstornar');" + getMensagemNotificar();
        } else {
            return "Richfaces.hideModalPanel('panelConfirmacaoEstornar');fireElementFromParent('form:btnAtualizaCliente');Notifier.cleanAll();" + getMensagemNotificar();
        }
    }

    public String getFecharRichModalPanelEmissao() {
        if (getAbrirRichConfirmacaoEmissao()) {
            return "Richfaces.showModalPanel('panelConfirmacaoEmitir')";
        } else {
            return "Richfaces.hideModalPanel('panelConfirmacaoEmitir')";
        }
    }

    public String getFecharRichModalPanelEmissaoPagamento() {
        if (getAbrirRichConfirmacaoEmissaoPagamento()) {
            return "Richfaces.showModalPanel('panelConfirmacaoEmitirPagamento')";
        } else {
            setEmissaoCartao(false);
            setEmissaoCheque(false);
            setEmissaoMovPagamento(false);
            return "Richfaces.hideModalPanel('panelConfirmacaoEmitirPagamento')";
        }
    }

    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("codigoRecibo", "Código Recibo"));
        itens.add(new SelectItem("nomePessoa", "Nome Pessoa"));
        return itens;
    }

    public void montarHistoricoChequesCartoes() {
        ArrayList novaListaCheques = new ArrayList();
        ArrayList novaListaCartoes = new ArrayList();
        try {
            // pega o historico de pagamentos
            Iterator i = getEstornoReciboVO().getListaMovPagamento().iterator();
            // para cada pagamento
            while (i.hasNext()) {
                MovPagamentoVO pagamento = (MovPagamentoVO) i.next();
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    Boolean bloqueiaPagamento = false;
                    Iterator it = pagamento.getChequeVOs().iterator();
                    // pega a lista de cheques
                    while (it.hasNext()) {
                        ChequeVO cheque = (ChequeVO) it.next();
                        if (getUsaEcf() && getUsaEcfPagamento()) {
                            if (pagamento.getCupomEmitido()) {
                                cheque.setCupomEmitido(true);
                            } else {
                                CupomFiscalVO cupom = getFacade().getCupomFiscal().consultaPorCheque(cheque.getCodigo());
                                cheque.setCupomEmitido(cupom != null);
                                if (cheque.getCupomEmitido()) {
                                    setApresentarBotaoEmissao(false);
                                    bloqueiaPagamento = true;
                                }
                                if (cupom != null && cupom.getStatusImpressao() == StatusImpressaoEnum.SUCESSO) {
                                    setApresentarBotaoEstorno(false);
                                }
                            }
                        }

                        if (getUsaNFSe() && getUsaNFSePagamento()) {
                            if (pagamento.getNfseEmitido()) {
                                cheque.setNfseEmitido(true);
                            } else {
                                List<NFSeEmitidaVO> notasEmitidas = getFacade().getNFSeEmitida().consultaListaPorCheque(cheque.getObterTodosChequesComposicao());
                                cheque.setNfseEmitido(!notasEmitidas.isEmpty());
                            }
                        }

                        if (isUsaNFCe() && isUsaNFCePagamento()) {
                            if (pagamento.isNfceEmitido()) {
                                cheque.setNfceEmitido(true);
                            } else {
                                List<NotaFiscalConsumidorEletronicaVO> notasEmitidas = getFacade().getNotaFiscalConsumidorEletronica().consultaListaPorCheque(cheque.getObterTodosChequesComposicao());
                                cheque.setNfceEmitido(!UteisValidacao.emptyList(notasEmitidas));
                            }
                        }
                        // adiciona os cheques de cada pagamento na lista
                        novaListaCheques.add(cheque);
                    }
                    if (!pagamento.getCupomEmitido() && bloqueiaPagamento) {
                        pagamento.setCupomEmitido(true);
                    }
                }
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    Boolean bloqueiaPagamento = false;
                    Iterator it = pagamento.getCartaoCreditoVOs().iterator();
                    // pega a lista de cheques
                    while (it.hasNext()) {
                        CartaoCreditoVO cartao = (CartaoCreditoVO) it.next();
                        if (getUsaEcf() && getUsaEcfPagamento()) {
                            if (pagamento.getCupomEmitido()) {
                                cartao.setCupomEmitido(true);
                            } else {
                                CupomFiscalVO cupom = getFacade().getCupomFiscal().consultaPorCartao(cartao.getCodigo());
                                cartao.setCupomEmitido(cupom != null);
                                if (cartao.getCupomEmitido()) {
                                    setApresentarBotaoEmissao(false);
                                    bloqueiaPagamento = true;
                                }
                                if (cupom != null && cupom.getStatusImpressao() == StatusImpressaoEnum.SUCESSO) {
                                    setApresentarBotaoEstorno(false);
                                }
                            }
                        }

                        if (getUsaNFSe() && getUsaNFSePagamento()) {
                            if (pagamento.getNfseEmitido()) {
                                cartao.setNfseEmitido(true);
                            } else {
                                List<NFSeEmitidaVO> notasEmitidas = getFacade().getNFSeEmitida().consultaListaPorCartao(cartao.getObterTodosCartoesComposicao());
                                cartao.setNfseEmitido(!notasEmitidas.isEmpty());
                            }
                        }

                        if (isUsaNFCe() && isUsaNFCePagamento()) {
                            if (pagamento.isNfceEmitido()) {
                                cartao.setNfceEmitido(true);
                            } else {
                                List<NotaFiscalConsumidorEletronicaVO> notasEmitidas = getFacade().getNotaFiscalConsumidorEletronica().consultaListaPorCartao(cartao.getObterTodosCartoesComposicao());
                                cartao.setNfceEmitido(!UteisValidacao.emptyList(notasEmitidas));
                            }
                        }

                        novaListaCartoes.add(cartao);
                    }
                    if (!pagamento.getCupomEmitido() && bloqueiaPagamento) {
                        pagamento.setCupomEmitido(true);
                    }
                }

            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        setListaCheques(Ordenacao.ordenarLista(novaListaCheques, "dataCompensacao"));
        setListaCartoes(Ordenacao.ordenarLista(novaListaCartoes, "dataCompensacao"));
    }

    /**
     * INICIO
     */
    @SuppressWarnings("unchecked")
    public void initConsultaPaginadaListener(ActionEvent evt) {
        setConfPaginacao(new ConfPaginacao());
        setListaConsulta(new ArrayList());
    }

    /**
     * Metodo listener que obtem os parametros necessarios para realizar
     * a paginacao e filtros da tela
     * <p>
     * * Autora: Carla
     * Criado em 14/01/2011
     */
    public void consultarPaginadoListener(ActionEvent evt) {
        //==================================================================================================================================

        //VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
        if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacao(new ConfPaginacao());
            }
        }

        //Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");
        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());
        }

        //==================================================================================================================================

        Object compTipoConsulta = evt.getComponent().getAttributes().get("tipoConsulta");
        if (compTipoConsulta != null && !"".equals(compTipoConsulta.toString())) {
            if (!compTipoConsulta.toString().equals(this.paramTipoConsulta)) {
                setConfPaginacao(new ConfPaginacao());
            }
            this.setParamTipoConsulta(compTipoConsulta.toString());
        }
        consultarPaginado();
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco
     * <p>
     * * Autora: Carla
     * Criado em 14/01/2011
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginado() {
        try {
            super.consultar();
            List objs = new ArrayList();
            ReciboPagamentoFiltroVO filtro = new ReciboPagamentoFiltroVO();
            filtro.setControlarAcesso(true);
            filtro.setNivelMontarDados(Uteis.NIVELMONTARDADOS_TELACONSULTA);

            if ("detalhada".equals(this.getParamTipoConsulta())) {
                if (getEmpresaLogado().getCodigo() != 0) {
                    filtro.setEmpresaVO(new EmpresaVO());
                    filtro.getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
                }
                if (getControleConsulta().getCampoConsulta().equals("codigoRecibo")) {

                    int valorInt = 0;
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                        filtro.setReciboPagamentoVO(new ReciboPagamentoVO());
                        filtro.getReciboPagamentoVO().setCodigo(valorInt);

                    }
                } else if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        filtro.setPessoaVO(new PessoaVO());
                        filtro.getPessoaVO().setNome(getControleConsulta().getValorConsulta().trim());
                    }
                }
                objs = getFacade().getReciboPagamento().consultarPaginado(filtro, getConfPaginacao());
                setListaConsulta(objs);
                setMensagemID("msg_dados_consultados");
                setSucesso(true);
                setErro(false);

                this.getConfPaginacao().definirVisibilidadeLinksNavegacao();
            }
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     * FIm
     **/
    public EstornoReciboVO getEstornoReciboVO() {
        return estornoReciboVO;
    }

    public void setEstornoReciboVO(EstornoReciboVO estornoReciboVO) {
        this.estornoReciboVO = estornoReciboVO;
    }

    public Boolean getAbrirRichConfirmacaoEstorno() {
        return abrirRichConfirmacaoEstorno;
    }

    public void setAbrirRichConfirmacaoEstorno(Boolean abrirRichConfirmacaoEstorno) {
        this.abrirRichConfirmacaoEstorno = abrirRichConfirmacaoEstorno;
    }

    public Boolean getAbrirRichConfirmacaoEmissao() {
        return abrirRichConfirmacaoEmissao;
    }

    public void setAbrirRichConfirmacaoEmissao(Boolean abrirRichConfirmacaoEmissao) {
        this.abrirRichConfirmacaoEmissao = abrirRichConfirmacaoEmissao;
    }

    public Boolean getApresentarBotaoEstorno() {
        return apresentarBotaoEstorno;
    }

    public void setApresentarBotaoEstorno(Boolean apresentarBotaoEstorno) {
        this.apresentarBotaoEstorno = apresentarBotaoEstorno;
    }

    public List<ChequeVO> getListaCheques() {
        return listaCheques;
    }

    public void setListaCheques(List<ChequeVO> cheques) {
        this.listaCheques = cheques;
    }

    public String getParamTipoConsulta() {
        return paramTipoConsulta;
    }

    public void setParamTipoConsulta(String paramTipoConsulta) {
        this.paramTipoConsulta = paramTipoConsulta;
    }

    public Boolean getApresentarBotaoEmissao() {
        return apresentarBotaoEmissao;
    }

    public void setApresentarBotaoEmissao(Boolean apresentarBotaoEmissao) {
        this.apresentarBotaoEmissao = apresentarBotaoEmissao;
    }

    public void setTemChequeComLote(Boolean temChequeComLote) {
        this.temChequeComLote = temChequeComLote;
    }

    public Boolean getTemChequeComLote() {
        return temChequeComLote;
    }

    public void setUsuarioEnvioNFSe(UsuarioVO usuarioEnvioNFSe) {
        this.usuarioEnvioNFSe = usuarioEnvioNFSe;
    }

    public UsuarioVO getUsuarioEnvioNFSe() {
        return usuarioEnvioNFSe;
    }

    public Boolean getUsaEcf() {
        return usaEcf;
    }

    public void setUsaEcf(Boolean emiteCupom) {
        this.usaEcf = emiteCupom;
    }

    public Boolean getUsaEcfPagamento() {
        return usaEcfPagamento;
    }

    public void setUsaEcfPagamento(Boolean emiteCupomPagamento) {
        this.usaEcfPagamento = emiteCupomPagamento;
    }

    public Boolean getAbrirRichConfirmacaoEmissaoPagamento() {
        return abrirRichConfirmacaoEmissaoPagamento;
    }

    public void setAbrirRichConfirmacaoEmissaoPagamento(Boolean abrirRichConfirmacaoEmissaoPagamento) {
        this.abrirRichConfirmacaoEmissaoPagamento = abrirRichConfirmacaoEmissaoPagamento;
    }

    public ReciboPagamentoVO getRecibo() {
        return recibo;
    }

    public void setRecibo(ReciboPagamentoVO recibo) {
        this.recibo = recibo;
    }

    public void setListaCartoes(List<CartaoCreditoVO> listaCartoes) {
        this.listaCartoes = listaCartoes;
    }

    public List<CartaoCreditoVO> getListaCartoes() {
        return listaCartoes;
    }

    public void setPagamentoVO(MovPagamentoVO pagamentoVO) {
        this.pagamentoVO = pagamentoVO;
    }

    public MovPagamentoVO getPagamentoVO() {
        return pagamentoVO;
    }

    public Boolean getEmissaoCheque() {
        return emissaoCheque;
    }

    public void setEmissaoCheque(Boolean emissaoCheque) {
        this.emissaoCheque = emissaoCheque;
    }

    public Boolean getEmissaoCartao() {
        return emissaoCartao;
    }

    public void setEmissaoCartao(Boolean emissaoCartao) {
        this.emissaoCartao = emissaoCartao;
    }

    public void setChequeVO(ChequeVO chequeVO) {
        this.chequeVO = chequeVO;
    }

    public ChequeVO getChequeVO() {
        return chequeVO;
    }

    public void setCartaoVO(CartaoCreditoVO cartaoVO) {
        this.cartaoVO = cartaoVO;
    }

    public CartaoCreditoVO getCartaoVO() {
        return cartaoVO;
    }

    public Boolean getEmissaoMovPagamento() {
        return emissaoMovPagamento;
    }

    public void setEmissaoMovPagamento(Boolean emissaoMovPagamento) {
        this.emissaoMovPagamento = emissaoMovPagamento;
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getReciboPagamento().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, ItemExportacaoEnum.RECIBOS);
            if(exportadorListaControle.getErro()){
                throw new Exception( exportadorListaControle.getMensagemDetalhada());
            }
            setMsgAlert(exportadorListaControle.getOperacaoOnComplete());
        }catch (Exception e){
            montarErro(e);

        }

    }

    public boolean isApresentarBotoaoNFSePagamento() {
        return getUsaNFSe() && getUsaNFSePagamento() && getRecibo().getEmpresa().isApresentarBotoesNFSe();
    }

    public boolean isApresentarBotoaoNFCePagamento() {
        return isUsaNFCe() && isUsaNFCePagamento();
    }

    public Boolean getUsaNFSe() {
        return usaNFSe;
    }

    public void setUsaNFSe(Boolean usaNFSe) {
        this.usaNFSe = usaNFSe;
    }

    public Boolean getUsaNFSePagamento() {
        return usaNFSePagamento;
    }

    public void setUsaNFSePagamento(Boolean usaNFSePagamento) {
        this.usaNFSePagamento = usaNFSePagamento;
    }

    public CaixaVO getCaixaAberto() {
        return caixaAberto;
    }

    public void setCaixaAberto(CaixaVO caixaAberto) {
        this.caixaAberto = caixaAberto;
    }

    public void validarCaixaAbrirModal() {
        try {
            validarCaixaAbrirModal(null);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void validarCaixaAbrirModalCappta() {
        try {
            validarCaixaAbrirModal(OpcoesPinpadEnum.CAPPTA);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void validarCaixaAbrirModalGetcard() {
        try {
            setExibirBotaoAbortarEstorno(false);
            validarCaixaAbrirModal(OpcoesPinpadEnum.GETCARD);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void validarCaixaAbrirModal(OpcoesPinpadEnum opcoesPinpadEnum) throws Exception {
        setMsgAlert("");
        getEstornoReciboVO().setMostrarMsgExcluirNFse(false);
        getEstornoReciboVO().setExcluirNFSe(false);
        limparMsg();

        if (!UteisValidacao.emptyList(getEstornoReciboVO().getListaTransacoes()) && UteisValidacao.emptyString(getEstornarTransacoes())) {
            throw new Exception("Deve ser selecionado se deseja estornar as transações");
        }
        getEstornoReciboVO().setEstornarOperadora(getEstornarTransacoes().equalsIgnoreCase("SIM"));

        boolean podeNecessitarAbrirCaixa = false;
        for (MovPagamentoVO mp : getEstornoReciboVO().getListaMovPagamento()) {
            if (!UteisValidacao.emptyNumber(mp.getMovconta())) {
                podeNecessitarAbrirCaixa = true;
            }
        }

        if (podeNecessitarAbrirCaixa) {
            ConfiguracaoFinanceiroVO cFinan = getFacade().getConfiguracaoFinanceiro().consultar();
            if (cFinan.getUsarMovimentacaoContas()) {
                CaixaControle caixaControle = (CaixaControle) JSFUtilities.getFromSession(CaixaControle.class.getSimpleName());
                setCaixaAberto(caixaControle.getCaixaVoEmAberto());
                if (getCaixaAberto() == null || UteisValidacao.emptyNumber(getCaixaAberto().getCodigo())) {
                    caixaControle.abrirCaixa();
                    if (!caixaControle.getMensagemDetalhada().equals("Não existem contas ativas para abrir caixa")) {
                        if (!caixaControle.getMensagemDetalhada().contains("não possui permissão para esta operação, \"9.17 - ABRIR CAIXA-ADMINISTRATIVO\"")) {
                            setMsgAlert("if(!confirm('Você precisa ter um caixa aberto no financeiro. Deseja abrir?')){return false;};Richfaces.showModalPanel('modalAbrirCaixa')");
                            return;
                        } else if (caixaControle.getMensagemDetalhada().contains("não possui permissão para esta operação, \"9.17 - ABRIR CAIXA-ADMINISTRATIVO\"")) {
                            montarErro("O pagamento já foi movimentado para uma conta no Financeiro. " + caixaControle.getMensagemDetalhada());
                            return;
                        }
                    }
                }
            }
        }

        habilitaPinPadEstorno();
        if (opcoesPinpadEnum == null &&
                getDataRetroativa()) {
            setMsgAlert("Richfaces.showModalPanel('panelConfirmacaoEstornar')");
        } else {
            if (opcoesPinpadEnum != null &&
                    opcoesPinpadEnum.equals(OpcoesPinpadEnum.CAPPTA)) {
                setMsgAlert("fazerCheckout(); Richfaces.showModalPanel('painelEstornoCappta');");
            } else if (opcoesPinpadEnum != null &&
                    opcoesPinpadEnum.equals(OpcoesPinpadEnum.GETCARD)) {
                setMsgAlert("Richfaces.showModalPanel('modalEstornoGetcard');");
            } else {
                abrirConfirmacaoEstornar();
            }
        }
    }

    public void abrirConfirmacaoEstornar() throws Exception {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                usuarioLiberacao = auto.getUsuario();
                getEstornoReciboVO().setResponsavelEstornoRecivo(auto.getUsuario());
                gravar(null);
                if (getSucesso()) {
                    setExecutarAoCompletar("Richfaces.hideModalPanel('panelConfirmacaoEstornar'); fireElementFromParent('form:btnAtualizaCliente');");
                } else {
                    setExecutarAoCompletar("fireElementFromParent('form:btnAtualizaCliente');");
                }

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }

        };

        limparMsg();
        try {
            if (dataRetroativa && Calendario.menor(dataEstorno, getEstornoReciboVO().getReciboPagamentoVO().getData())) {
                throw new Exception("A data do estorno não pode ser menor do que a data do lançamento do pagamento.");
            }

            if (dataRetroativa && Calendario.maior(dataEstorno, Calendario.hoje())) {
                throw new Exception("A data do estorno não pode ser futura.");
            }

            auto.autorizar("Confirmação de Estornar Recibo", "EstornoRecibo",
                    "Você precisa da permissão \"4.12 - Estornar recibo\"",
                    "form, form,panelConfirmacao,msg,panelMensagem, mdlMensagemGenerica, formConfirmacaoEstornar", listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public boolean getEstornoCappta() {
        return estornoCappta;
    }

    public UsuarioVO getUsuarioLiberacao() {
        return usuarioLiberacao;
    }

    public void setUsuarioLiberacao(UsuarioVO usuarioLiberacao) {
        this.usuarioLiberacao = usuarioLiberacao;
    }

    public void setEstornoCappta(Boolean estornoCappta) {
        this.estornoCappta = estornoCappta;
    }

    public PinpadTO getPinpad() {
        return pinpad;
    }

    public void setPinpad(PinpadTO pinpad) {
        this.pinpad = pinpad;
    }

    public boolean isUsaNFCe() {
        return usaNFCe;
    }

    public void setUsaNFCe(boolean usaNFCe) {
        this.usaNFCe = usaNFCe;
    }

    public boolean isUsaNFCePagamento() {
        return usaNFCePagamento;
    }

    public void setUsaNFCePagamento(boolean usaNFCePagamento) {
        this.usaNFCePagamento = usaNFCePagamento;
    }

    public UsuarioVO getUsuarioEnvioNFCe() {
        return usuarioEnvioNFCe;
    }

    public void setUsuarioEnvioNFCe(UsuarioVO usuarioEnvioNFCe) {
        this.usuarioEnvioNFCe = usuarioEnvioNFCe;
    }

    public Boolean getCheckoutPinPad() {
        return checkoutPinPad;
    }

    public void setCheckoutPinPad(Boolean checkoutPinPad) {
        this.checkoutPinPad = checkoutPinPad;
    }

    public Date getDataEstorno() {
        return dataEstorno;
    }

    public void setDataEstorno(Date dataEstorno) {
        this.dataEstorno = dataEstorno;
    }

    public Boolean getDataRetroativa() {
        return dataRetroativa;
    }

    public void setDataRetroativa(Boolean dataRetroativa) {
        this.dataRetroativa = dataRetroativa;
    }

    public void erroPagamentoPinpad() {
        gravarLogCappta("erroPagamentoPinpad - ESTORNO");
    }

    private void gravarLogCappta(String operacao) {
        try {

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setNomeEntidade("CAPPTA");
            obj.setNomeEntidadeDescricao("CAPPTA");
            obj.setOperacao("CAPPTA - " + operacao);

            try {
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("");
                obj.setUserOAMD("");
            }

            StringBuilder infoDados = new StringBuilder();
            try {
                infoDados.append("RECIBO: ").append(getRecibo().getCodigo());
                ObjectMapper mapper = new ObjectMapper();
                mapper.setTimeZone(TimeZone.getTimeZone(TimeZoneEnum.Brazil_East.getId()));
                infoDados.append("\n\nPINPAD: ").append(mapper.writeValueAsString(pinpad));
            } catch (Exception ignored) {
            }

            obj.setNomeCampo("CAPPTA - " + operacao);
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado(infoDados.toString());
            registrarLogObjetoVO(obj, 0);
        } catch (Exception e) {
            try {
                registrarLogErroObjetoVO("CAPPTA", 0, "ERRO AO GERAR LOG DA CAPPTA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public String getEstornarTransacoes() {
        return estornarTransacoes;
    }

    public void setEstornarTransacoes(String estornarTransacoes) {
        this.estornarTransacoes = estornarTransacoes;
    }

    public boolean isPermissaoEstronarTransacao() {
        return permissaoEstronarTransacao;
    }

    public void setPermissaoEstronarTransacao(boolean permissaoEstronarTransacao) {
        this.permissaoEstronarTransacao = permissaoEstronarTransacao;
    }

    public List<SelectItem> getListaSelectItemEstornarTransacoes() {
        List<SelectItem> lista = new ArrayList<>();
        if (!isPermissaoEstronarTransacao()) {
            lista.add(new SelectItem("NAO", "NÃO"));
        }
        else {
            lista.add(new SelectItem("", ""));
            lista.add(new SelectItem("SIM", "SIM"));
            lista.add(new SelectItem("NAO", "NÃO"));
        }
        return lista;
    }

    public boolean isEstornoGetcard() {
        return estornoGetcard;
    }

    public void setEstornoGetcard(boolean estornoGetcard) {
        this.estornoGetcard = estornoGetcard;
    }

    public String getUrlServicoGetCardScope() {
        return PropsService.getPropertyValue(PropsService.urlServicoGetCardScope);
    }

    public PinPadPedidoVO getPinPadPedidoVO() {
        if (pinPadPedidoVO == null) {
            pinPadPedidoVO = new PinPadPedidoVO();
        }
        return pinPadPedidoVO;
    }

    public void setPinPadPedidoVO(PinPadPedidoVO pinPadPedidoVO) {
        this.pinPadPedidoVO = pinPadPedidoVO;
    }

    public void gravarLogGetCard() {
        try {
            if (UteisValidacao.emptyNumber(this.getPinPadPedidoVO().getCodigo())) {
                getFacade().getPinPadPedido().incluir(this.getPinPadPedidoVO());
            }

            JSONObject json = new JSONObject();

            try {
                json.put("pessoa_codigo", this.getRecibo().getPessoaPagador().getCodigo());
                json.put("pessoa_nome", this.getRecibo().getPessoaPagador().getNome());
                json.put("recibo", this.getRecibo().getCodigo());
                json.put("valor", this.getRecibo().getValorTotal());
                json.put("usuarioCodigo", this.getUsuarioLogado().getCodigo());
                json.put("usuario", this.getUsuarioLogado().getNome());
                json.put("empresaCodigo", this.getEmpresaLogado().getCodigo());
                json.put("empresa", this.getEmpresaLogado().getNome());
            } catch (Exception ignored) {
            }

            json.put("retorno", this.getPinPadPedidoVO().getRetornoPinpad());

            getFacade().getPinPad().incluirHistorico(this.getPinPadPedidoVO().getCodigo(),
                    this.getPinPadPedidoVO().getIdExterno(),
                    this.getPinPadPedidoVO().getRetornoPinpadOperacao(),
                    json.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public String getBodyEstornoPinPadGetCard() {
        try {
            int valor = (int) (this.getRecibo().getValorTotal() * 100);

            JSONObject request = new JSONObject();
            request.put("Valor", String.valueOf(valor));
            request.put("Taxa", "0");
            request.put("Empresa", this.getPinPadPedidoVO().getConvenioCobrancaVO().getCodigoAutenticacao01());
            request.put("Filial", this.getPinPadPedidoVO().getConvenioCobrancaVO().getCodigoAutenticacao02());
            request.put("Pdv", this.getPinPadPedidoVO().getPdvPinPad());
            request.put("Controle", this.getPinpad().getNrControle());

            JSONObject json = new JSONObject();
            json.put("request", request);
            return json.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void erroGetCard() {
        String mensagem = "";
        try {
            this.setOnCompletePinpad("");

            mensagem = this.getPinPadPedidoVO().getParamsRespCancel();
            JSONObject json = null;
            try {
                json = new JSONObject(this.getPinPadPedidoVO().getParamsRespCancel());
                mensagem = json.getJSONObject("response").optString("Mensagem");
            } catch (Exception ignored) {}
            try {
                if (json != null && UteisValidacao.emptyString(mensagem)) {
                    mensagem = json.getJSONObject("response").optString("MensagemOperador");
                }
            } catch (Exception ignored) {}
            try {
                if (json != null && UteisValidacao.emptyString(mensagem)) {
                    mensagem = json.getJSONObject("response").optString("MensagemColeta");
                }
            } catch (Exception ignored) {}

            if (!UteisValidacao.emptyString(mensagem)) {
                mensagem = mensagem.replaceFirst("Cancelamento", "");
            }

            if (UteisValidacao.emptyString(mensagem)) {
                mensagem = "Falha ao inicializar GetCard, tente novamente e caso persista verifique se o programa controladorScope está inicializado. ERC";
            }
            throw new Exception(mensagem);
        } catch (Exception ex) {
//            ex.printStackTrace();
            this.setEstornoGetcardConfirmar(false);
            montarErro(ex);
            this.setOnCompletePinpad("alterarMsgModal('" + mensagem + "');" + getMensagemNotificar());
        }
    }

    public void salvarIdExternoGetCard() {
        try {
            if (UteisValidacao.emptyString(this.getPinPadPedidoVO().getIdExternoCancel())) {
                return;
            }
            this.setEstornoGetcardConfirmar(true);
            this.getPinPadPedidoVO().setStatus(StatusPinpadEnum.AGUARDANDO_CANCELAMENTO);
            if (UteisValidacao.emptyNumber(this.getPinPadPedidoVO().getCodigo())) {
                getFacade().getPinPadPedido().incluir(this.getPinPadPedidoVO());
            } else {
                getFacade().getPinPadPedido().alterar(this.getPinPadPedidoVO());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void exibirBotaoAbortarEstornoFalse() {
        setExibirBotaoAbortarEstorno(false);
    }

    public void exibirBotaoAbortarEstornoTrue() {
        setExibirBotaoAbortarEstorno(true);
    }

    public String getOnCompletePinpad() {
        if (onCompletePinpad == null) {
            onCompletePinpad = "";
        }
        return onCompletePinpad;
    }

    public void setOnCompletePinpad(String onCompletePinpad) {
        this.onCompletePinpad = onCompletePinpad;
    }

    public boolean isEstornoGetcardConfirmar() {
        return estornoGetcardConfirmar;
    }

    public void setEstornoGetcardConfirmar(boolean estornoGetcardConfirmar) {
        this.estornoGetcardConfirmar = estornoGetcardConfirmar;
    }

    public PinPadPedidoVO getPinPadPedidoOrigem() {
        if (pinPadPedidoOrigem == null) {
            pinPadPedidoOrigem = new PinPadPedidoVO();
        }
        return pinPadPedidoOrigem;
    }

    public void setPinPadPedidoOrigem(PinPadPedidoVO pinPadPedidoOrigem) {
        this.pinPadPedidoOrigem = pinPadPedidoOrigem;
    }

    public boolean isExibirBotaoAbortarEstorno() {
        return exibirBotaoAbortarEstorno;
    }

    public void setExibirBotaoAbortarEstorno(boolean exibirBotaoAbortarEstorno) {
        this.exibirBotaoAbortarEstorno = exibirBotaoAbortarEstorno;
    }
}
