/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import controle.basico.ColaboradorControle;
import controle.basico.EnvioEmailContratoReciboControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.IdentificadorInternacionalEnum;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.notaFiscal.NotaProcessarTO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;
import net.sf.jasperreports.engine.JRParameter;
import relatorio.controle.arquitetura.SuperControleRecibo;
import relatorio.controle.financeiro.CaixaPorOperadorRelControleRel;
import relatorio.negocio.comuns.financeiro.AuxiliarReciboPagamentoRelTO;
import relatorio.negocio.comuns.financeiro.ReciboPagamentoRelTO;
import relatorio.negocio.jdbc.financeiro.CaixaPorOperadorRel;

import javax.faces.event.ActionEvent;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 */
public class ReciboControle extends SuperControleRecibo {

    private ReciboPagamentoVO reciboVO = null;
    private PessoaVO pessoaVO = null;
    private EmpresaVO empresaVO = new EmpresaVO();
    private String retorno = "";
    private Integer codigoEvento;
    private boolean impressaoVindaHistCompras;
    private List<EmailVO> emailsEnviarRecibo;
    private String modalMensagemGenerica;
    private String origemEmissaoNFCe;
    private String origemEmissaoNFSe;
    private String msgAlert;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String[] displayIdentificadorFront = {IdentificadorInternacionalEnum.BRASIL.getIdentificadorPrimario(),
            IdentificadorInternacionalEnum.BRASIL.getIdentificadorSecundadrio()};

    public ReciboControle(Boolean semSessao) throws Exception {

    }

    public ReciboControle() throws Exception {
        inicializarEmpresa();
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional();
        montarListaSelectItemEmpresa();
        obterUsuarioLogado();
    }

    public static String getDesignIReportRelatorio(Boolean reciboParaImpressoraTermica) {
        if (reciboParaImpressoraTermica) {
            return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "ReciboTermico" + File.separator + "ReciboNovo.jrxml");
        }
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "ReciboNovo.jrxml");
    }

    public static String getCaminhoSubRelatorio(Boolean reciboParaImpressoraTermica) {
        if (reciboParaImpressoraTermica) {
            return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "ReciboTermico" + File.separator);
        }
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
    }

    public void prepareRecibo(ActionEvent evt) {
        origemEmissaoNFCe = (String) evt.getComponent().getAttributes().get("origemEmissaoNFCe");

        origemEmissaoNFSe = (String) evt.getComponent().getAttributes().get("origemEmissaoNFSe");

        ReciboPagamentoVO recibo = (ReciboPagamentoVO) evt.getComponent().getAttributes().get("reciboPagamentoVO");
        if (recibo == null) {
            recibo = (ReciboPagamentoVO) context().getExternalContext().getSessionMap().get("reciboPagamento");
        }
        if (recibo == null) {
            //tentar pelo movContaControle
            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
            if (movParcelaControle != null && !UteisValidacao.emptyList(movParcelaControle.getListaParcelasPagar())) {
                try {
                    Uteis.logarDebug("Não econtrei o recibo, buscando do controlador movParcelaControle");
                    recibo = getFacade().getReciboPagamento().consultarPorParcela(movParcelaControle.getListaParcelasPagar().get(0).getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
                    //colocar na sessão depois de obter a primeira vez
                    context().getExternalContext().getSessionMap().put("reciboPagamento", recibo);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                //Tentar pelo movPagamentoControle (geralmente utilizado para pagamentos pix onde a baixa não tem sessão pois vem do webhook)
                MovPagamentoControle movPagamentoControle = (MovPagamentoControle) JSFUtilities.getFromSession(MovPagamentoControle.class.getSimpleName());
                if (movPagamentoControle != null && !UteisValidacao.emptyNumber(movPagamentoControle.getCodReciboPagamentoPixImprimirCaixaEmAberto())) {
                    try {
                        Uteis.logarDebug("Não econtrei o recibo, buscando do controlador movPagamentoControle");
                        recibo = getFacade().getReciboPagamento().consultarPorCodigo(movPagamentoControle.getCodReciboPagamentoPixImprimirCaixaEmAberto(), Uteis.NIVELMONTARDADOS_TODOS);
                        //colocar na sessão depois de obter a primeira vez
                        context().getExternalContext().getSessionMap().put("reciboPagamento", recibo);
                        //limpar do controlador
                        movPagamentoControle.setCodReciboPagamentoPixImprimirCaixaEmAberto(0);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        Object object = evt.getComponent().getAttributes().get("maisDetalhes");
        if (object == null) {
            setImpressaoVindaHistCompras(false);
        } else {
            setImpressaoVindaHistCompras(Boolean.valueOf(object.toString()));
        }
        Integer evento = (Integer) evt.getComponent().getAttributes().get("codigoEvento");
        if (recibo != null) {
            setReciboVO(recibo);
        }
        if (evento != null) {
            setCodigoEvento(evento);
        }
    }

    /**
     * Responsável por preparar o recibo para ser enviado por email
     * author: alcides
     * 09/05/2011
     */
    public void prepareReciboEmail(ActionEvent evt) throws Exception {
        prepareRecibo(evt);
        imprimirReciboPDF();
        PessoaVO pessoa = (PessoaVO) evt.getComponent().getAttributes().get("pessoaVO");
        if (pessoa != null) {
            setPessoaVO(pessoa);
        }
    }

    public void montarMsgGenerica(String titulo, String msg, boolean msgInformacao, String botaoSim, String botaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica')");

        if (msgInformacao) {
            control.init(titulo, msg, this, "Fechar", "", reRender + ",mdlMensagemGenerica");
        } else {
            control.init(titulo, msg, this, botaoSim, "", botaoNao, "", reRender + ",mdlMensagemGenerica");
        }
    }

    public void montarMsgGenericaPergunta(String titulo, String msgInformacao, String metodoInvocarAoClicarBotaoSim, String onCompleteBotaoSim, String metodoInvocarAoClicarBotaoNao, String onCompleteBotaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init(titulo, msgInformacao, this, metodoInvocarAoClicarBotaoSim, onCompleteBotaoSim, metodoInvocarAoClicarBotaoNao, onCompleteBotaoNao, reRender + ",mdlMensagemGenerica");
    }

    public void confirmarEmitirNFSe() {
        notificarRecursoEmpresa(RecursoSistema.ENVIO_NFSE_CLIENTE);
        try {
            setMsgAlert("");
            limparMsg();
            montarMsgGenericaPergunta("Emitir NFS-e", "Deseja realizar envio da NFS-e?", "emitirNFSeColaboradorConsumidor", "Richfaces.showModalPanel('mdlMensagemGenerica');return false;", null, null, "");
        } catch (Exception e) {
            montarMsgGenerica("Emitir NFS-e", e.getMessage(), true, null, null, "");
        }
    }

    public void confirmarEmitirNFCe() {
        notificarRecursoEmpresa(RecursoSistema.ENVIO_NFCE_CLIENTE);
        try {
            setMsgAlert("");
            limparMsg();
            montarMsgGenericaPergunta("Emitir NFC-e", "Deseja realizar envio da NFC-e?", "emitirNFCe", "Richfaces.showModalPanel('mdlMensagemGenerica');return false;", null, null, "");
        } catch (Exception e) {
            montarMsgGenerica("Emitir NFC-e", e.getMessage(), true, null, null, "");
        }
    }

    public void emitirNFCe() {
        try {
            ReciboPagamentoVO reciboPagamentoVO = getFacade().getReciboPagamento().consultarPorCodigo(getReciboVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            NotaFiscalConsumidorEletronicaVO notaExiste = getFacade().getNotaFiscalConsumidorEletronica().consultarPorReciboPagamento(reciboPagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyNumber(notaExiste.getCodigo())) {
                throw new Exception("Já existe NFC-e emitida para esse recibo.");
            }
            NotaProcessarTO notaProcessarTO = getFacade().getNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFCE, reciboPagamentoVO, getUsuarioLogado(), getKey());
            if (!notaProcessarTO.isSucesso()) {
                throw new Exception(notaProcessarTO.getRetorno());
            }

            try {
                LogVO obj = new LogVO();
                obj.setChavePrimaria(reciboPagamentoVO.getCodigo().toString());
                obj.setNomeEntidade("CLIENTE");
                obj.setNomeEntidadeDescricao("Cliente - NFC-E");
                obj.setOperacao("EMISSÃO DE NOTA FISCAL CONSUMIDOR");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setNomeCampo("MENSAGEM");
                obj.setValorCampoAlterado("EMISSÃO DE NOTA FISCAL CONSUMIDOR - RECIBO " + getReciboVO().getCodigo() + " - VALOR " + getEmpresaLogado().getMoeda() + " " + reciboPagamentoVO.getValorTotal()+ "");
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                if ("COLABORADOR".equals(origemEmissaoNFCe)) {
                    ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(
                            reciboPagamentoVO.getPessoaPagador().getCodigo(),
                            reciboPagamentoVO.getEmpresa().getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    obj.setNomeEntidade("COLABORADOR");
                    obj.setNomeEntidadeDescricao("Colaborador - NFC-E");
                    obj.setChavePrimaria(colaboradorVO.getCodigo().toString());
                }
                registrarLogObjetoVO(obj, reciboPagamentoVO.getPessoaPagador().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("CLIENTE", reciboPagamentoVO.getPessoaPagador().getCodigo(), "ERRO AO REGISTRAR LOG EMITIR NFC-E", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            montarMsgGenerica("Emitir NFC-e", "Dados enviados com sucesso.", true, null, null, "mdlMensagemGenerica");
        } catch (Exception e) {
            montarMsgGenerica("Emitir NFC-e", e.getMessage(), true, null, null, "mdlMensagemGenerica");
        }
    }

    public void emitirNFSe() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        limparMsg();
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                try {
                    setReciboVO(getFacade().getReciboPagamento().consultarPorChavePrimaria(getReciboVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                    getReciboVO().setPagamentosDesteRecibo(getFacade().getMovPagamento().consultarPorCodigoRecibo(getReciboVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));

                    NotaProcessarTO notaProcessarTO = getFacade().getNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, getReciboVO(), getUsuarioLogado(), getKey());
                    if (notaProcessarTO.isSucesso()) {
                        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class.getSimpleName());
                        clienteControle.atualizarPagamentos(isImpressaoVindaHistCompras());
                        try {
                            LogVO obj = new LogVO();
                            obj.setChavePrimaria(getReciboVO().getCodigo().toString());
                            obj.setNomeEntidade("CLIENTE");
                            obj.setNomeEntidadeDescricao("Cliente - NFSE");
                            obj.setOperacao("EMISSÃO DE NOTA");
                            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                            obj.setNomeCampo("MENSAGEM");
                            obj.setValorCampoAlterado("EMISSÃO DE NOTA - RECIBO " + getReciboVO().getCodigo() + " - VALOR " + getEmpresaLogado().getMoeda() + " " + getReciboVO().getValorTotal() + "");
                            obj.setDataAlteracao(Calendario.hoje());
                            registrarLogObjetoVO(obj, getReciboVO().getPessoaPagador().getCodigo());
                        } catch (Exception e) {
                            registrarLogErroObjetoVO("CLIENTE", getReciboVO().getPessoaPagador().getCodigo(), "ERRO AO EMITIR NFSE", getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd());
                            e.printStackTrace();
                        }
                    }
                    montarMsgAlert(notaProcessarTO.getRetorno());
                } catch (Exception e) {
                    setMensagemID("msg_erro");
                    setMensagemDetalhada(e);
                    montarMsgAlert(getMensagemDetalhada());
                }
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Autorização Envio NFSe", "EnvioNFSe",
                "Você precisa da permissão \"Envio NFSe\"",
                "panelAutorizacaoFuncionalidade, listaHistoricoPagamentosContrato, listaHistoricoPagamentos", listener);
    }

    public void emitirNFSeColaboradorConsumidor() {
        try {
            setReciboVO(getFacade().getReciboPagamento().consultarPorChavePrimaria(getReciboVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            getReciboVO().setPagamentosDesteRecibo(getFacade().getMovPagamento().consultarPorCodigoRecibo(getReciboVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            ReciboPagamentoVO reciboPagamentoVO = getReciboVO();
            NotaProcessarTO notaProcessarTO = getFacade().getNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, getReciboVO(), getUsuarioLogado(), getKey());
            if (!notaProcessarTO.isSucesso()) {
                throw new Exception(notaProcessarTO.getRetorno());
            }

            try {
                ClienteControle clienteControle = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class.getSimpleName());
                clienteControle.atualizarPagamentos(isImpressaoVindaHistCompras());

                LogVO obj = new LogVO();
                obj.setChavePrimaria(getReciboVO().getCodigo().toString());
                obj.setNomeEntidade("CLIENTE");
                obj.setNomeEntidadeDescricao("Cliente - NFSE");
                obj.setOperacao("EMISSÃO DE NOTA");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setNomeCampo("MENSAGEM");
                obj.setValorCampoAlterado("EMISSÃO DE NOTA - RECIBO " + getReciboVO().getCodigo() + " - VALOR " + getEmpresaLogado().getMoeda() + " " + getReciboVO().getValorTotal() + "");
                obj.setDataAlteracao(Calendario.hoje());
                if ("COLABORADOR".equals(origemEmissaoNFSe)) {
                    ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(
                            reciboPagamentoVO.getPessoaPagador().getCodigo(),
                            reciboPagamentoVO.getEmpresa().getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    obj.setNomeEntidade("COLABORADOR");
                    obj.setNomeEntidadeDescricao("Colaborador - NFS-E");
                    obj.setChavePrimaria(colaboradorVO.getCodigo().toString());
                }
                registrarLogObjetoVO(obj, getReciboVO().getPessoaPagador().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("CLIENTE", getReciboVO().getPessoaPagador().getCodigo(), "ERRO AO EMITIR NFSE", getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            montarMsgGenerica("Emitir NFS-e", "Dados enviados com sucesso.", true, null, null, "mdlMensagemGenerica");
        } catch (Exception e) {
            montarMsgGenerica("Emitir NFS-e", e.getMessage(), true, null, null, "mdlMensagemGenerica");
        }
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public void prepareReciboEmailCE(ActionEvent evt) throws Exception {
        prepareRecibo(evt);
        imprimirReciboPDFCE();
        setPessoaVO(new PessoaVO());
        getPessoaVO().setEmailVOs(getFacade().getCentralEventosFacade().getNegEvContrato().consultaEmails(getCodigoEvento()));
    }

    /**
     * Responsável por obter o recibo, anexar no email e enviar
     * author: alcides
     * 09/05/2011
     */
    private SuperControle enviarEmail(SuperControle controle) {
        try {
            //verificar se o cliente tem email válido
            if(!getReciboVO().getPessoaPagador().getCodigo().equals(getPessoaVO().getCodigo()) && !getFacade().getReciboPagamento().reciboPagaProdutosPessoa(getReciboVO().getCodigo(), getPessoaVO().getCodigo())){
                throw new Exception("Esse recibo não paga produtos desse pessoa. Certifique-se que não está trabalhando com duas abas do navegador. Consulte novamente o aluno e tente novamente!");
            }

            if (UteisValidacao.emptyList(getEmailsEnviarRecibo())) {
                controle.setErro(true);
                throw new Exception("Não foi possível enviar o recibo pois a pessoa não possui um email válido.");
            } else {
                //obter o recibo
                String nomePDF = Uteis.obterCaminhoWeb() + "/relatorio/" + getNomeArquivoRelatorioGeradoAgora();
                File arquivo = new File(nomePDF);
                //obter configurações do envio de email
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
                UteisEmail email = new UteisEmail();
                // assunto do email será "RECIBO"
                email.novo("RECIBO", configuracaoSistemaCRMVO);
                //remetente é o usuario logado
                email.setRemetente(getUsuarioLogado());
                email = email.addAnexo(getNomeArquivoRelatorioGeradoAgora(), arquivo);

                StringBuilder listaEmails = new StringBuilder();
                List<EmailVO> emailVOList = getEmailsEnviarRecibo();
                if (emailVOList.size() > 0) {
                    int qtdEmails = emailVOList.size();
                    for (int i = 0; i < qtdEmails; i++) {
                        Object obj = emailVOList.get(i);
                        if (obj instanceof EmailVO) {
                            EmailVO emailV = (EmailVO) obj;
                            if (listaEmails.toString().isEmpty()) {
                                listaEmails.append(emailV.getEmail());
                            } else {
                                listaEmails.append(";").append(emailV.getEmail());
                            }
                        }
                    }
                }
                String[] emails = listaEmails.toString().split(";");

                email.enviarEmailN(emails, getTextoEmail(), "RECIBO", getReciboVO().getEmpresa_Apresentar());
                controle.apresentarSucessoEmail(true);
            }
        } catch (Exception e) {
            controle.apresentarSucessoEmail(false);
            controle.setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return controle;
    }

    public void enviarEmailCE() {
        try {
            String nomeEmpresa = getUsuarioLogado().getAdministrador() ? "" : getEmpresaLogado().getNome();
            setMsgAlert("");
            if (UteisValidacao.emptyList(getPessoaVO().getEmailVOs())) {
                montarMsgAlert("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            } else {
                //obter o recibo
                String nomePDF = Uteis.obterCaminhoWeb() + "/relatorio/" + getNomeArquivoRelatorioGeradoAgora();
                File arquivo = new File(nomePDF);
                //obter configurações do envio de email
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
                UteisEmail email = new UteisEmail();
                // assunto do email será "RECIBO"
                email.novo("RECIBO", configuracaoSistemaCRMVO);
                //remetente é o usuario logado
                email.setRemetente(getUsuarioLogado());
                email = email.addAnexo(getNomeArquivoRelatorioGeradoAgora(), arquivo);
                String[] emails = new String[getPessoaVO().getEmailVOs().size()];
                int i = 0;
                for (Object obj : getPessoaVO().getEmailVOs()) {
                    EmailVO emailVO = (EmailVO) obj;
                    emails[i] = emailVO.getEmail();
                    i++;
                }
                email.enviarEmailN(emails, getTextoEmail(), "RECIBO", nomeEmpresa);
                montarMsgAlert("Enviado com sucesso");
            }
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * Responsável por enviar o recibo por email
     *
     * <AUTHOR>
     * 09/05/2011
     */
    public void enviarReciboPagamento() {
        MovPagamentoControle movPagamentoControle = (MovPagamentoControle) context().getExternalContext().getSessionMap().get("MovPagamentoControle");
        movPagamentoControle = (MovPagamentoControle) enviarEmail(movPagamentoControle);
        JSFUtilities.storeOnSession("MovPagamentoControle", movPagamentoControle);
    }

    /**
     * Método que retorna o texto que irá no conteúdo do email.
     * author: alcides
     * 09/05/2011
     */
    private String getTextoEmail() {
        return "Pagador: " + getPessoaVO().getNome() + "<br/>" +
                "Data do recibo: " + Uteis.getData(getReciboVO().getData());
    }

    public void imprimirReciboPDF() throws Exception {
        imprimirReciboPDF(false, empresaVO.isReciboParaImpressoraTermica());
    }

    public void imprimirReciboPDFCE() {
        imprimirReciboPDF(true, false);
        setReciboVO(null);
    }

    public void imprimirReciboPDF(Boolean ce, Boolean reciboParaImpressoraTermica) {
        try {
            notificarRecursoEmpresa(RecursoSistema.RECIBO_CLIENTE_IMPRIME);
            if (!getUsuarioLogado().getAdministrador()) {
                empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            String nomeRelatorio = getFacade().getReciboPagamento().getIdEntidade();
            String titulo = "RECIBO";
            String design = getDesignIReportRelatorio(reciboParaImpressoraTermica);
            String nomeEmpresa = "";
            if (getEmpresaLogado().getCodigo() != 0) {
                nomeEmpresa = getEmpresaLogado().getNome();
            }
            MovPagamentoVO mpVO = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("historicoPagamentos");
            if (mpVO == null) {
                if (reciboVO == null) {
                    reciboVO = (ReciboPagamentoVO) context().getExternalContext().getSessionMap().get("reciboPagamento");
                }
            } else {
                MovPagamentoInterfaceFacade movPagamento = new MovPagamento();
                mpVO = movPagamento.consultarPorChavePrimaria(mpVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                reciboVO = mpVO.getReciboPagamento();
            }
            reciboVO = getFacade().getReciboPagamento().consultarPorChavePrimaria(reciboVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (reciboVO.getPessoaPagador().getCodigo() != 0) {
                reciboVO.setPessoaPagador(getFacade().getPessoa().consultarPorChavePrimaria(reciboVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                if(reciboVO.getPessoaPagador().getCategoriaPessoa().equals(TipoPessoa.JURIDICA) && !UteisValidacao.emptyString(reciboVO.getPessoaPagador().getCnpj())){
                    reciboVO.getPessoaPagador().setCfp(null);
                }
                if (Uteis.calcularIdadePessoa(negocio.comuns.utilitarias.Calendario.hoje(), reciboVO.getPessoaPagador().getDataNasc()) < 18) {
                    if (empresaVO.isEmitirNoNomeResponsavel()) {
                        String nomePagador = "";
                        String cpfPagador = "";
                        PessoaVO pessoaPagador = reciboVO.getPessoaPagador();
                        if (pessoaPagador != null && pessoaPagador.getCodigo() > 0) {
                            nomePagador = pessoaPagador.getNome();
                            cpfPagador = pessoaPagador.getCfp();
                        }
                        ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(reciboVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                            PessoaVO pessoaCliente = clienteVO.getPessoa();
                            if (clienteVO.getPessoaResponsavel() != null && !UteisValidacao.emptyString(clienteVO.getPessoaResponsavel().getNome())) {
                                nomePagador = clienteVO.getPessoaResponsavel().getNome();
                                cpfPagador = clienteVO.getPessoaResponsavel().getCfp();
                            } else if (!UteisValidacao.emptyString(pessoaCliente.getNomeMae()) && !UteisValidacao.emptyString(pessoaCliente.getCpfMae())) {
                                nomePagador = pessoaCliente.getNomeMae();
                                cpfPagador = pessoaCliente.getCpfMae();
                            } else if (!UteisValidacao.emptyString(pessoaCliente.getNomePai()) && !UteisValidacao.emptyString(pessoaCliente.getCpfPai())) {
                                nomePagador = pessoaCliente.getNomePai();
                                cpfPagador = pessoaCliente.getCpfPai();
                            }
                        }

                        reciboVO.setNomeResponsavelLegal(nomePagador);
                        reciboVO.getPessoaPagador().setNomePai(nomePagador);
                        reciboVO.setNomeResponsaveis(reciboVO.getPessoaPagador().getNomeResponsaveis());
                        reciboVO.setCpfResponsavelLegal(cpfPagador);
                    } else {
                        if (!UteisValidacao.emptyString(reciboVO.getPessoaPagador().getNomeMae()) || !UteisValidacao.emptyString(reciboVO.getPessoaPagador().getNomePai())) {
                            reciboVO.setNomeResponsaveis(reciboVO.getPessoaPagador().getNomeResponsaveis());
                        }
                    }
                }
            }
            Integer codigoEventoCE = getFacade().getReciboPagamento().codigoEventoCE(reciboVO.getCodigo());
            if(!UteisValidacao.emptyNumber(codigoEventoCE)){
                ce = true;
                codigoEvento = codigoEventoCE;
            }
            setListaRelatorio(new ArrayList());
            CaixaPorOperadorRel caixaPorOperadorRel = new CaixaPorOperadorRel();
            caixaPorOperadorRel.setMostraContrato(true);
            reciboVO.setEmpresa(getEmpresaLogado());
            caixaPorOperadorRel.carregarDadosReciboPagamentoRelVO(reciboVO);
            if (ce) {
                caixaPorOperadorRel.getReciboPagamentoRelTO().setListaMovParcelaVOs(getFacade().getMovParcela().consultarParcelasEventoPorRecibo(reciboVO.getCodigo()));
                EventoInteresseVO evento = getFacade().getCentralEventosFacade().getEventoInteresse().obter(codigoEvento);
                caixaPorOperadorRel.getReciboPagamentoRelTO().setCentralEventos(true);
                if (evento != null) {
                    caixaPorOperadorRel.getReciboPagamentoRelTO().setDescricaoDevolucao("Evento: " + evento.getNomeEvento());
                }

            }
            
            for (Object obj : caixaPorOperadorRel.getReciboPagamentoRelTO().getListaMovPagamentoVOs()) {
                MovPagamentoVO movPag = (MovPagamentoVO) obj;
                movPag.setChequeVOs(getFacade().getCheque().consultarPagamentoCheques(movPag.getCodigo(), null, false,false,false, Uteis.NIVELMONTARDADOS_TODOS));
                acoesReciboCappta(movPag);
            }

            caixaPorOperadorRel.adicionarObjReciboPagamentoRelVOs(caixaPorOperadorRel.getReciboPagamentoRelTO());
            caixaPorOperadorRel.setReciboPagamentoRelTO(new ReciboPagamentoRelTO());
            caixaPorOperadorRel.contarMarcadoresRelatorio();
            CaixaPorOperadorRelControleRel caixaPorOperadorControle = new CaixaPorOperadorRelControleRel();
            caixaPorOperadorControle.setCaixaPorOperadorRel(caixaPorOperadorRel);
            List <Integer> contratosJaAdicionadosModalidades = new ArrayList<>(); // armazena

            StringBuilder observacoesRecibo = new StringBuilder();
            List<String> infoTurmas = new ArrayList<>();
            for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                ContratoVO contratoVO = new ContratoVO();
                List<MovProdutoVO> produtoVOSRemover = new ArrayList<>();
                for (Object obj : recibo.getListaMovProdutoVOs()) {
                    MovProdutoVO movProduto = (MovProdutoVO) obj;
                    if(movProduto.getTotalFinal()==0 && !getEmpresaLogado().isMostrarValoresZeradosRel()) {
                        produtoVOSRemover.add(movProduto);
                        continue;
                    }

                    if (movProduto.getProduto().getTipoProduto().equals("PM") && movProduto.getMulta() == 0.0 && movProduto.getJuros() == 0.0) {
                        
                        recibo.getMovProduto().getProduto().setTipoProduto(movProduto.getProduto().getTipoProduto());
                        if( UteisValidacao.emptyNumber(contratoVO.getCodigo()) || contratoVO.getCodigo().equals(movProduto.getContrato().getCodigo())){
                            contratoVO = getFacade().getContrato().consultarPorChavePrimaria(movProduto.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        }

                        calcularConvDescontoMovProduto(recibo, movProduto, contratoVO);
                        calcularDescontoExtraMovProduto(recibo, movProduto);


                        if (getEmpresaVO().isMostrarModalidade() && !contratosJaAdicionadosModalidades.contains(reciboVO.getContrato().getCodigo())) {
                            int nivelMontarDados = Uteis.NIVELMONTARDADOS_ROBO;
                            if  (empresaVO.isDetalharNiveisModalidades()) {
                                nivelMontarDados = Uteis.NIVELMONTARDADOS_TODOS;
                            }
                            List contratoModalidadeVO = getFacade().getContratoModalidade().consultarPorCodigoContrato(reciboVO.getContrato().getCodigo(), nivelMontarDados);
                            for (Object contrato : contratoModalidadeVO) {
                                ContratoModalidadeVO contratoMod = (ContratoModalidadeVO) contrato;
                                if (!recibo.getModalidades().contains(contratoMod.getModalidade().getNome())) {
                                    recibo.setModalidades(" | " + contratoMod.getModalidade().getNome() + recibo.getModalidades());
                                }

                                if (empresaVO.isDetalharNiveisModalidades()) {
                                    for (Object contratoModalidadeHorarioTurma : contratoMod.getListaContratoModalidadesHorarioTurmaVOs()) {
                                        ContratoModalidadeHorarioTurmaVO cmhtVO = (ContratoModalidadeHorarioTurmaVO) contratoModalidadeHorarioTurma;
                                        HorarioTurmaVO htVO = cmhtVO.getHorarioTurma();
                                        String infoAdd = "Nível: " + htVO.getNivelTurma().getDescricao() + "; Professor: " + htVO.getProfessor().getPessoa_Apresentar() + " ; Horários: " + htVO.getDiaSemana() + ": " + htVO.getHoraInicial() + " - " + htVO.getHoraFinal() + ";\r\n";

                                        if (!infoTurmas.contains(infoAdd)) {
                                            infoTurmas.add(infoAdd);
                                        }
                                    }
                                }
                            }
                            contratosJaAdicionadosModalidades.add(reciboVO.getContrato().getCodigo());
                        }
                    }
                    if ( movProduto.getProduto().getTipoProduto().equals("DI")){
                        recibo.getMovProduto().setDataInicioVigencia(movProduto.getProduto().getDataInicioVigencia());
                        recibo.getMovProduto().setDataFinalVigencia(movProduto.getProduto().getDataFinalVigencia());
                    }
                }
                recibo.getListaMovProdutoVOs().removeAll(produtoVOSRemover);
                recibo.setModalidades(recibo.getModalidades().replaceFirst("\\|", ""));

                if (empresaVO.isDetalharNiveisModalidades()) {
                    for (String info : infoTurmas) {
                        observacoesRecibo.append(info);
                    }
                }
            }
            observacoesRecibo.append(empresaVO.getObservacaoRecibo());

            boolean sairFor = false;
            for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                for (Object obj : recibo.getListaMovPagamentoVOs()) {
                    MovPagamentoVO movPagamentoVO = (MovPagamentoVO) obj;
                    if (!UteisValidacao.emptyNumber(movPagamentoVO.getConvenio().getCodigo())) {
                        ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(movPagamentoVO.getConvenio().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        movPagamentoVO.setConvenio(convenioCobrancaVO);
                        if (movPagamentoVO.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
                            movPagamentoVO.setObservacao("\nOrigem: Stone Connect\n");
                            sairFor = true;
                            break;
                        }
                    }
                }
                if (sairFor) {
                    break;
                }
            }

            Map<String, Object> params = new HashMap<>();
            String nomeBundle = "bundleRelatorios.resourceBundleRelatorios";
            ResourceBundle relatorioResourceBundle = ResourceBundle.getBundle(nomeBundle, new Locale(getEmpresaLogado().getLocale().getLanguage(),getEmpresaLogado().getLocale().getCountry()));

            params.put(JRParameter.REPORT_LOCALE, new Locale(getEmpresaLogado().getLocale().getLanguage(),getEmpresaLogado().getLocale().getCountry()));
            params.put(JRParameter.REPORT_RESOURCE_BUNDLE, relatorioResourceBundle);
            params.put("nomeRelatorio", nomeRelatorio);
            params.put("tituloRelatorio", titulo);
            params.put("nomeEmpresa", nomeEmpresa);
            params.put("imagemLogo", "");
            params.put("mensagemRel", "");
            params.put("tipoRelatorio", getTipoRelatorio());
            params.put("caminhoParserXML", "/" + getFacade().getReciboPagamento().getIdEntidade() + "/registros");
            params.put("nomeDesignIReport", design);
            params.put("nomeUsuario", getUsuarioLogado().getNome());
            params.put("filtros", "nenhum");
            params.put("dataIni", "");
            params.put("dataFim", "");
            params.put("qtdAV", String.valueOf(caixaPorOperadorRel.getQtdPagamentoAV()));
            params.put("qtdCA", String.valueOf(caixaPorOperadorRel.getQtdPagamentoCA()));
            params.put("qtdCD", String.valueOf(caixaPorOperadorRel.getQtdPagamentoCD()));
            params.put("qtdBB", String.valueOf(caixaPorOperadorRel.getQtdPagamentoBB()));
            params.put("qtdChequeAV", String.valueOf(caixaPorOperadorRel.getQtdPagamentoChAvista()));
            params.put("qtdChequePR", String.valueOf(caixaPorOperadorRel.getQtdPagamentoChPrazo()));
            params.put("qtdOutro", String.valueOf(caixaPorOperadorRel.getQtdPagamentoOutros()));
            params.put("valorAV", caixaPorOperadorRel.getValorPagamentoAV());
            params.put("valorCA", caixaPorOperadorRel.getValorPagamentoCA());
            params.put("valorCD", caixaPorOperadorRel.getValorPagamentoCD());
            params.put("valorBB", caixaPorOperadorRel.getValorPagamentoBB());
            params.put("valorChequeAV", caixaPorOperadorRel.getValorPagamentoChAvista());
            params.put("valorChequePR", caixaPorOperadorRel.getValorPagamentoChPrazo());
            params.put("valorOutro", caixaPorOperadorRel.getValorPagamentoOutros());
            params.put("SUBREPORT_DIR", getCaminhoSubRelatorio(reciboParaImpressoraTermica));
            params.put("SUBREPORT_DIR1", getCaminhoSubRelatorio(reciboParaImpressoraTermica));
            params.put("SUBREPORT_DIR2", getCaminhoSubRelatorio(reciboParaImpressoraTermica));
            params.put("qtdDV", "");
            params.put("valorDV", 0.0);
            params.put("qtdDR", "");
            params.put("valorDR", 0.0);
            params.put("devolucoes", null);
            params.put("totalizadores", null);
            params.put("apresentarDados", !caixaPorOperadorRel.getListaReciboPagamentoRelTOs().isEmpty());
            params.put("somenteSintetico", false);

            params.put("mostrarCnpj", empresaVO.isMostrarCnpj());
            params.put("mostrarModalidade", empresaVO.isMostrarModalidade());
            params.put("detalharPeriodoProduto", empresaVO.isDetalharPeriodoProduto());
            params.put("detalharParcelas", empresaVO.isDetalharParcelas());
            params.put("detalharPagamentos", empresaVO.isDetalharPagamentos());
            params.put("detalharDescontos", empresaVO.isDetalharDescontos());
            params.put("emitirNomeResponsavel", empresaVO.isEmitirNoNomeResponsavel());
            params.put("apresentarAssinaturas", empresaVO.isApresentarAssinaturas());
            params.put("apresentarObservacao", !UteisValidacao.emptyString(empresaVO.getObservacaoRecibo()) || empresaVO.isDetalharNiveisModalidades());
            params.put("observacaoRecibo", observacoesRecibo.toString());
            params.put("group_startnewpage", empresaVO.isQuebrarPaginaRecibo());
            params.put("moeda", empresaVO.getMoeda());
            params.put("identificador", displayIdentificadorFront[0]);

            List<AuxiliarReciboPagamentoRelTO> auxiliarReciboPagamentoRelTOs = new ArrayList<>();
            int qtdVias = empresaVO.getQtdVias();
            if (empresaVO.isReciboParaImpressoraTermica()) {
                qtdVias = 1;
            }
            for (int i = 0; i < qtdVias; i++) {
                for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                    AuxiliarReciboPagamentoRelTO auxiliar = new AuxiliarReciboPagamentoRelTO();
                    auxiliar.setRecibo(recibo);
                    auxiliar.setSequencial(i);
                    auxiliarReciboPagamentoRelTOs.add(auxiliar);
                }
            }

            params.put("listaObjetos", auxiliarReciboPagamentoRelTOs);

            apresentarRelatorioObjetos(params);
        } catch (Exception e) {
            setMsgAlert("");
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
        }
    }

    public void acoesReciboCappta(MovPagamentoVO movPagamentoVO) {
        //observação quando é cappta é automática do pinpad.
        //não imprimir observação de recebimentos pinpad cappta. Observações pinpad cappta podem ser usadas para análises futuras, mas não precisa imprimir nor recibo.
        try {
            if (movPagamentoVO.getFormaPagamento().getPinpad().isCAPPTA()) {
                movPagamentoVO.setObservacao("");
            }
        } catch (Exception ignore) {}
    }

    public void imprimirReciboEmBranco() {
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            String nomeRelatorio = "ReciboEmBranco";
            String titulo = "RECIBO";
            String design = getDesignIReportRelatorio(false);
            String nomeEmpresa = "";
            if (getEmpresaLogado().getCodigo() != 0) {
                nomeEmpresa = getEmpresaLogado().getNome();
            }

            MovPagamentoVO mpVO = new MovPagamentoVO();
            reciboVO = new ReciboPagamentoVO();
            reciboVO.getPagamentosDesteRecibo().add(mpVO);

            setListaRelatorio(new ArrayList());
            CaixaPorOperadorRel caixaPorOperadorRel = new CaixaPorOperadorRel();
            caixaPorOperadorRel.setMostraContrato(true);
            reciboVO.setEmpresa(getEmpresaLogado());
            caixaPorOperadorRel.carregarDadosReciboPagamentoRelVO(reciboVO);

            caixaPorOperadorRel.adicionarObjReciboPagamentoRelVOs(caixaPorOperadorRel.getReciboPagamentoRelTO());
            caixaPorOperadorRel.setReciboPagamentoRelTO(new ReciboPagamentoRelTO());
            caixaPorOperadorRel.contarMarcadoresRelatorio();
            CaixaPorOperadorRelControleRel caixaPorOperadorControle = new CaixaPorOperadorRelControleRel();
            caixaPorOperadorControle.setCaixaPorOperadorRel(caixaPorOperadorRel);

            for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                recibo.setModalidades(recibo.getModalidades().replaceFirst("\\|", ""));
            }

            Map<String, Object> params = new HashMap<>();
            params.put("nomeRelatorio", nomeRelatorio);
            params.put("tituloRelatorio", titulo);
            params.put("nomeEmpresa", nomeEmpresa);
            params.put("imagemLogo", "");
            params.put("mensagemRel", "");
            params.put("tipoRelatorio", getTipoRelatorio());
            params.put("caminhoParserXML", "/" + getFacade().getReciboPagamento().getIdEntidade() + "/registros");
            params.put("nomeDesignIReport", design);
            params.put("nomeUsuario", getUsuarioLogado().getNome());
            params.put("filtros", "nenhum");
            params.put("dataIni", "");
            params.put("dataFim", "");
            params.put("qtdAV", String.valueOf(caixaPorOperadorRel.getQtdPagamentoAV()));
            params.put("qtdCA", String.valueOf(caixaPorOperadorRel.getQtdPagamentoCA()));
            params.put("qtdCD", String.valueOf(caixaPorOperadorRel.getQtdPagamentoCD()));
            params.put("qtdBB", String.valueOf(caixaPorOperadorRel.getQtdPagamentoBB()));
            params.put("qtdChequeAV", String.valueOf(caixaPorOperadorRel.getQtdPagamentoChAvista()));
            params.put("qtdChequePR", String.valueOf(caixaPorOperadorRel.getQtdPagamentoChPrazo()));
            params.put("qtdOutro", String.valueOf(caixaPorOperadorRel.getQtdPagamentoOutros()));
            params.put("valorAV", caixaPorOperadorRel.getValorPagamentoAV());
            params.put("valorCA", caixaPorOperadorRel.getValorPagamentoCA());
            params.put("valorCD", caixaPorOperadorRel.getValorPagamentoCD());
            params.put("valorBB", caixaPorOperadorRel.getValorPagamentoBB());
            params.put("valorChequeAV", caixaPorOperadorRel.getValorPagamentoChAvista());
            params.put("valorChequePR", caixaPorOperadorRel.getValorPagamentoChPrazo());
            params.put("valorOutro", caixaPorOperadorRel.getValorPagamentoOutros());
            params.put("SUBREPORT_DIR", getCaminhoSubRelatorio(false));
            params.put("SUBREPORT_DIR1", getCaminhoSubRelatorio(false));
            params.put("SUBREPORT_DIR2", getCaminhoSubRelatorio(false));
            params.put("qtdDV", "");
            params.put("valorDV", 0.0);
            params.put("qtdDR", "");
            params.put("valorDR", 0.0);
            params.put("devolucoes", null);
            params.put("totalizadores", null);
            params.put("apresentarDados", !caixaPorOperadorRel.getListaReciboPagamentoRelTOs().isEmpty());
            params.put("somenteSintetico", false);

            params.put("mostrarCnpj", empresaVO.isMostrarCnpj());
            params.put("mostrarModalidade", empresaVO.isMostrarModalidade());
            params.put("detalharPeriodoProduto", empresaVO.isDetalharPeriodoProduto());
            params.put("detalharParcelas", empresaVO.isDetalharParcelas());
            params.put("detalharPagamentos", empresaVO.isDetalharPagamentos());
            params.put("detalharDescontos", empresaVO.isDetalharDescontos());
            params.put("emitirNomeResponsavel", empresaVO.isEmitirNoNomeResponsavel());
            params.put("apresentarAssinaturas", empresaVO.isApresentarAssinaturas());
            params.put("apresentarObservacao", !UteisValidacao.emptyString(empresaVO.getObservacaoRecibo()));
            params.put("observacaoRecibo", empresaVO.getObservacaoRecibo());
            params.put("group_startnewpage", empresaVO.isQuebrarPaginaRecibo());
            params.put("moeda", empresaVO.getMoeda());

            List<AuxiliarReciboPagamentoRelTO> auxiliarReciboPagamentoRelTOs = new ArrayList<>();
            for (int i = 0; i < empresaVO.getQtdVias(); i++) {
                for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                    AuxiliarReciboPagamentoRelTO auxiliar = new AuxiliarReciboPagamentoRelTO();
                    auxiliar.setRecibo(recibo);
                    auxiliar.setSequencial(i);
                    auxiliarReciboPagamentoRelTOs.add(auxiliar);
                }
            }

            params.put("listaObjetos", auxiliarReciboPagamentoRelTOs);

            apresentarRelatorioObjetos(params);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        } finally {
            setReciboVO(null);
        }
    }

    public final void inicializarEmpresa() throws Exception {
        if (isApresentarEmpresa()) {
            return;
        }
        empresaVO = getEmpresaLogado();
        if (empresaVO == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        super.montarListaEmpresas();
    }

    public boolean isApresentarEmpresa() throws Exception {
        try {
            return getUsuarioLogado().getAdministrador();
        } catch (Exception e) {
            throw new Exception("Não foi possível encontrar o usuário logado. Entre novamente no sistema.");
        }
    }

    public String getMetodoVazio() {
        return "";
    }

    public void validaEimprime() {
        try {
            if (empresaVO == null || empresaVO.getCodigo() == 0) {
                throw new Exception("Informe de qual empresa o recibo deve ser impresso.");
            }
            empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            imprimirReciboEmBranco();
            retorno = "Richfaces.hideModalPanel('panelRecibo'); abrirPopupPDFImpressao('relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);";
        } catch (Exception e) {
            retorno = "Richfaces.hideModalPanel('panelRecibo');";
        }
    }

    public void escolherAcao() {
        try {
            setMensagemDetalhada("", "");
            retorno = "";

            if (isApresentarEmpresa()) {
                retorno = "Richfaces.showModalPanel('panelRecibo');";
            } else {
                imprimirReciboEmBranco();
                retorno = "abrirPopupPDFImpressao('relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);";
            }
        } catch (Exception e) {
            retorno = "Alert('" + e.getMessage() + "');";
        }
    }

    public String getIrPara() {
        return retorno;
    }

    public ReciboPagamentoVO getReciboVO() {
        return reciboVO;
    }

    public void setReciboVO(ReciboPagamentoVO reciboVO) {
        this.reciboVO = reciboVO;
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public Integer getCodigoEvento() {
        return codigoEvento;
    }

    public void setCodigoEvento(Integer codigoEvento) {
        this.codigoEvento = codigoEvento;
    }

    public boolean isImpressaoVindaHistCompras() {
        return impressaoVindaHistCompras;
    }

    public void setImpressaoVindaHistCompras(boolean impressaoVindaHistCompras) {
        this.impressaoVindaHistCompras = impressaoVindaHistCompras;
    }

    public List<EmailVO> getEmailsEnviarRecibo() {
        if (emailsEnviarRecibo == null) {
            emailsEnviarRecibo = new ArrayList<>();
        }
        return emailsEnviarRecibo;
    }

    public void setEmailsEnviarRecibo(List<EmailVO> emailsEnviarRecibo) {
        this.emailsEnviarRecibo = emailsEnviarRecibo;
    }

    public void prepararModalEnvioReciboPorEmail(ActionEvent evt) throws Exception {
        setMsgAlert("");
        setMensagemDetalhada("");
        try {
            ReciboPagamentoVO recibo = (ReciboPagamentoVO) evt.getComponent().getAttributes().get("reciboPagamentoVO");
            setReciboVO(recibo);
            PessoaVO pessoa = (PessoaVO) evt.getComponent().getAttributes().get("pessoaVO");
            if (pessoa != null) {
                setPessoaVO(pessoa);
            }
            EnvioEmailContratoReciboControle envioEmailContratoReciboControle = (EnvioEmailContratoReciboControle) getControlador(EnvioEmailContratoReciboControle.class.getSimpleName());
            envioEmailContratoReciboControle.prepararListaEmail(getPessoaVO().getCodigo(), false);
            envioEmailContratoReciboControle.setEnvioDeContrato(false);
            setMsgAlert("Richfaces.showModalPanel('modalEnviarContratoEmail');");
        } catch (Exception ex){
            montarErro(ex);
        }
    }

    public void prepararModalEnvioReciboPorEmailColaborador(ActionEvent evt) throws Exception {
        setMsgAlert("");
        setMensagemDetalhada("");
        try {
            ReciboPagamentoVO recibo = (ReciboPagamentoVO) evt.getComponent().getAttributes().get("reciboPagamentoVO");
            setReciboVO(recibo);
            PessoaVO pessoa = (PessoaVO) evt.getComponent().getAttributes().get("pessoaVO");
            if (pessoa != null) {
                setPessoaVO(pessoa);
            }
            EnvioEmailContratoReciboControle envioEmailContratoReciboControle = (EnvioEmailContratoReciboControle) getControlador(EnvioEmailContratoReciboControle.class.getSimpleName());
            envioEmailContratoReciboControle.prepararListaEmail(getPessoaVO().getCodigo(), false);
            envioEmailContratoReciboControle.setReciboColaborador(true);
            setMsgAlert("Richfaces.showModalPanel('modalEnviarContratoEmail');");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void prepararModalEnvioReciboPorEmailPagamento(ActionEvent evt) throws Exception {
        notificarRecursoEmpresa(RecursoSistema.RECIBO_CLIENTE_ENVIAR);
        setMsgAlert("");
        setMensagemDetalhada("");
        try {
            MovPagamentoControle movPagamentoControle = (MovPagamentoControle) context().getExternalContext().getSessionMap().get("MovPagamentoControle");
            ReciboPagamentoVO recibo = movPagamentoControle.getReciboObj();
            setReciboVO(recibo);
            PessoaVO pessoa = (PessoaVO) evt.getComponent().getAttributes().get("pessoaVO");
            if (pessoa != null) {
                setPessoaVO(pessoa);
            }
            EnvioEmailContratoReciboControle envioEmailContratoReciboControle = (EnvioEmailContratoReciboControle) getControlador(EnvioEmailContratoReciboControle.class.getSimpleName());
            envioEmailContratoReciboControle.prepararListaEmail(getPessoaVO().getCodigo(), false);
            envioEmailContratoReciboControle.setReciboPagamento(true);
            envioEmailContratoReciboControle.setEnvioDeContrato(false);
            envioEmailContratoReciboControle.setReciboColaborador(false);
            setMsgAlert("Richfaces.showModalPanel('modalEnviarContratoEmail');");
        } catch (Exception e){
            montarErro(e);
        }
    }

    public String enviarReciboEmailCliente() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.RECIBO_CLIENTE_ENVIAR);
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        return enviarReciboEmail(clienteControle);
    }

    public String enviarReciboEmailColaborador() throws Exception {
        ColaboradorControle colaboradorControle = (ColaboradorControle) context().getExternalContext().getSessionMap().get("ColaboradorControle");
        return enviarReciboEmail(colaboradorControle);
    }

    public String enviarReciboEmailPagamento() throws Exception {
        MovPagamentoControle movPagamentoControle = (MovPagamentoControle) context().getExternalContext().getSessionMap().get("MovPagamentoControle");
        JSFUtilities.storeOnSession("MovPagamentoControle", movPagamentoControle);
        return enviarReciboEmail(movPagamentoControle);
    }

    public String enviarReciboEmail(SuperControle controle) throws Exception {
        String retorno = "";
        EnvioEmailContratoReciboControle envioEmailContratoReciboControle = (EnvioEmailContratoReciboControle) getControlador(EnvioEmailContratoReciboControle.class.getSimpleName());
        try {

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();

            if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
                throw new Exception("Configure o servidor de envio de e-mail!");
            }


            setEmailsEnviarRecibo(envioEmailContratoReciboControle.getListaEmailsEnviar());
            if (UteisValidacao.emptyList(getEmailsEnviarRecibo())) {
                throw new Exception("Adicione um e-mail para enviar o contrato!");
            }

            imprimirReciboPDF();
            enviarEmail(controle);

            setReciboVO(null);
            envioEmailContratoReciboControle.setSucesso(true);
            envioEmailContratoReciboControle.setErro(false);
            envioEmailContratoReciboControle.setMensagemDetalhada("Recibo enviado com sucesso.");
        } catch (Exception e) {
            envioEmailContratoReciboControle.setMensagemDetalhada("msg_erro", e.getMessage());
            envioEmailContratoReciboControle.setSucesso(false);
            envioEmailContratoReciboControle.setErro(true);
            envioEmailContratoReciboControle.setMensagem(retorno);
            retorno = e.getMessage();
        }
        return retorno;
    }

    public String getModalMensagemGenerica() {
        if (modalMensagemGenerica == null) {
            modalMensagemGenerica = "";
        }
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Enviar E-mail",
                "Deseja enviar o e-mail?",
                this, "enviarEmailCE", "", "", "", "");
    }


    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public void inicializarConfiguracaoSistema() throws Exception {
        setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
    }
    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;
    }

}
