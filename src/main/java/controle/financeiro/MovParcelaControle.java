package controle.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import br.com.pactosolucoes.enumeradores.TipoClienteRestricaoEnum;
import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.MenuControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.TelaClienteControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.contrato.ContratoControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.CaixaAbertoTO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ItemMovParcelaVO;
import negocio.comuns.financeiro.MovParcelaFiltroVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.FormatadorNumerico;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteRestricao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.oamd.RedeEmpresaVO;
import servicos.impl.admCoreMs.AdmCoreMsService;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.integracao.rdstationmarketing.AtualizarCamposEnum;
import servicos.oamd.RedeEmpresaService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.apache.commons.lang.StringUtils.EMPTY;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas movParcelaForm.jsp movParcelaCons.jsp) com as funcionalidades da
 * classe <code>MovParcela</code>. Implemtação da camada controle (Backing
 * Bean).
 *
 * @see SuperControle
 * @see MovParcela
 * @see MovParcelaVO
 */
public class MovParcelaControle extends SuperControle {

    private MovParcelaVO movParcelaVO;
    // private MovProdutoParcelaVO movProdutoParcelaVO;
    private ItemMovParcelaVO itemMovParcelaVO;
    protected List<MovParcelaVO> listaParcelasPagar;
    protected List<SelectItem> listaResponsavelPagamento;
    protected Double valorTotalParcela;
    protected Date dataInicio;
    protected Date dataTermino;
    protected String valorConsulta;
    protected String matricula;
    protected String produto;
    protected Integer numeroContratoResponsavel;
    protected Boolean consultarHoje;
    protected Boolean consultarSemana;
    protected List objsContrato;
    protected List objsVendaAvulsa;
    protected List objsAulaAvulsaDiaria;
    private List objsPersonal;
    //atributos criados para o central de eventos
    protected Integer codigoContratoEvento;
    private Boolean existeParcelasCE;
    private Boolean existeParcelasPagasCE;
    private List<MovParcelaVO> parcelasPagas;
    private String paramTipoConsulta;
    private Boolean apresentarComboSituacao = false;
    private boolean incluirParcelasRecorrencia = true;
    private EmpresaVO empresa = new EmpresaVO();
    private Integer filtroCodigoEmpresa;
    private List<SelectItem> listaSelectItemEmpresa = new ArrayList<SelectItem>();
    private Integer codigoEventoInteresse;
    private List<CaixaAbertoTO> itensCaixaAberto;
    private Boolean ordenarPorLancamento;
    private Boolean ordenarPorAlfabetica;
    private Boolean naoApresentarVencimentosDeMesesFuturos;
    private List<CaixaAbertoTO> itensSelecionados;
    private UsuarioVO responsavelCancelamento;
    private Boolean abrirRichConfirmacaoCancelamento ;
    private String confirmacaoCancelamento;

    private CaixaAbertoTO itemParaRenegociar = new CaixaAbertoTO();
    private List<MovParcelaVO> parcelasParaRenegociar = new ArrayList<MovParcelaVO>();
    private String justificativaCancelamento = "";
    private Double valorMultaJuros;


    private boolean existeConvenioCobrancaBoleto = false;
    private boolean existeAutorizacaoBoleto = false;
    private Boolean selecionarTodasParcelas = false;
    private boolean existeParcelaSelecionada = false;
    private Boolean permiteAcessarColaborador = false;

    private boolean apresentarEmpresa = false;
    private boolean apresentarRenegociarParcelas = false;
    private MensagemGenericaControle mensagemControle;
    private List<BoletoVO> listaBoletosPendentes;
    private String onCompleteBoletoPendente;
    private String operacaoOrigemBoletoPendente;
    private boolean apresentarImprimirComprovanteCompra = false;
    private boolean novaTelaCaixaAbertoPadrao = false;
    private boolean novaTelaCaixaAbertoPadraoEmpresa = false;
    private boolean usaPinpad = true;

    public MovParcelaControle() throws Exception {
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
        novo(true, true);
    }

    public MovParcelaControle(boolean ignorarValidacoes) throws Exception {
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
        if(!ignorarValidacoes) {
            novo(true, true);
        }

    }

    /**
     * a criação deste construtor é para evitar o reset dos atributos no construtor
     * principal pela chamada do método novo();
     * @param valorConsulta String
     * @throws Exception
     */
    public MovParcelaControle(String valorConsulta) throws Exception {
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
        setValorConsulta(valorConsulta);
        novo(false, true);
    }

    public void inicializarContrato() throws Exception {
        if (context() == null) {
            return;
        }
        ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
        //pegar na sessão o codigo do contrato de evento, no caso do controle estar sendo acessado do Central de Eventos
        Integer codgContratoEvento = (Integer) context().getExternalContext().getSessionMap().get("codigoContratoEvento");
        //sempre que este controle for acessado pelo Central de Eventos, esta condição será verdadeira
        if (codgContratoEvento != null) {
            this.codigoContratoEvento = codgContratoEvento;
            setConsultarHoje(false);
            consultaCaixaAberto();
            JSFUtilities.removeFromSession("codigoContratoEvento");
        } else {
            this.codigoContratoEvento = null;
            if (contratoControle != null && contratoControle.getContratoVO() != null) {
                getMovParcelaVO().setContrato(contratoControle.getContratoVO());
                setValorConsulta(contratoControle.getContratoVO().getPessoa().getNome());
                setConsultarHoje(false);
                empresa = contratoControle.getContratoVO().getEmpresa();
                consultaCaixaAberto();
                //Não Limpar o ValorCconsulta, já que está trazendo apenas de uma pessoa, isso é mais intuitivo e evita problemas quando vier da tela de cliente.
                //setValorConsulta("");

            }
        }
    }

    private void processarRecursoPadrao() {
        montarUsaPinpad();
        if (this.usaPinpad) {
            this.novaTelaCaixaAbertoPadrao = false;
            this.novaTelaCaixaAbertoPadraoEmpresa = false;
            return;
        }
        try {
            this.novaTelaCaixaAbertoPadraoEmpresa = getFacade().getUsuario().recursoPadraoEmpresa(TipoInfoMigracaoEnum.CAIXA_ABERTO, getEmpresaLogado().getCodigo());
        }catch (Exception e){
            Uteis.logar(e, MovParcelaControle.class);
        }
        try {
            this.novaTelaCaixaAbertoPadrao = (this.novaTelaCaixaAbertoPadraoEmpresa ||
                    (getFacade().getUsuario().recursoHabilitado(TipoInfoMigracaoEnum.CAIXA_ABERTO,
                            getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo())));
        }catch (Exception e){
            Uteis.logar(e, MovParcelaControle.class);
        }
    }

    public void verificarAbrirNovaTela() {
        try {
            setMsgAlert("");
            if (this.isNovaTelaCaixaAbertoPadrao()) {
                this.abrirNovaTela();
            }
        } catch (Exception ex) {
            Uteis.logar(ex, MovParcelaControle.class);
            montarErro(ex);
        }
    }

    public void abrirNovaTela() {
        abrirNovaTela(true);
    }

    public void abrirNovaTela(boolean padrao) {
        try {
            limparMsg();
            setMsgAlert("");
            if (padrao) {
                getFacade().getUsuario().gravarRecurso(TipoInfoMigracaoEnum.CAIXA_ABERTO, getUsuarioLogado().getCodigo(), "true", getUsuarioLogado());
            }
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String filtroPessoa = "";
            try {
                if (!UteisValidacao.emptyString(this.getMatricula())) {
                    ClienteVO clienteVO = getFacade().getCliente().consultarPorMatricula(this.getMatricula(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        filtroPessoa = ("/lista/" + clienteVO.getPessoa().getCodigo().toString());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            String openWindow = "window.open('" + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_ZW.getSiglaModulo()) + "&redirect=adm/caixa-em-aberto" + filtroPessoa +"', '_self')";
            if (padrao) {
                notificarRecursoEmpresa(RecursoSistema.PADRAO_NOVA_TELA_CAIXA_ABERTO);
            } else {
                notificarRecursoEmpresa(RecursoSistema.EXPERIMENTE_TELA_CAIXA_ABERTO);
            }
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void gerarImpressaoContrato() throws Exception {
        CaixaAbertoTO obj = (CaixaAbertoTO) context().getExternalContext().getRequestMap().get("item");
        ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(obj.getContrato(), Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
        if (contrato.getCodigo() != 0) {
            JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", contrato.getEmpresa());
            getFacade().getContratoTextoPadrao().consultarHtmlContrato(contrato.getCodigo(),true);
        } else {
            throw new Exception("Não foi possível emitir o relatório. Dados não encontrados!");
        }
    }

    public String iniciarRenegociacaoParcelas() {
        try {
            CaixaAbertoTO obj = (CaixaAbertoTO) context().getExternalContext().getRequestMap().get("item");

            if (obj.getEmptyParcelas()) {
                throw new Exception("Não existem parcelas a serem renegociadas. Verifique se as parcelas selecionadas já não foram pagas anteriormente.");
            }

            setItemParaRenegociar(obj);

            RenegociacaoControle renegociacaoControle = (RenegociacaoControle) getControlador(RenegociacaoControle.class);
            renegociacaoControle.setQtdParcelas(1);
            renegociacaoControle.setDataPrimeiraParcela(new Date());
            setMensagemDetalhada("", "");
            renegociacaoControle.setMensagem("");
            renegociacaoControle.setMensagemDetalhada("", "");
            montarSucesso("msg_entre_dados");
            setMsgAlert("Richfaces.showModalPanel('mdlRenegociar')");
            return "";
        } catch (Exception ex){
            montarErro(ex);
            setMsgAlert("");
            return "";
        }
    }

    public String prepararRenegociacao() {
        try {
            limparMsg();
            setMsgAlert("");
            this.setListaBoletosPendentes(new ArrayList<>());
            EmpresaVO empresaLogado = getEmpresaLogado();
            VerificaRenegociacao.verificar(empresaLogado);
            List<MovParcelaVO> parcelasParaRenegociar = new ArrayList<MovParcelaVO>();
            setParcelasParaRenegociar(parcelasParaRenegociar);

            for (MovParcelaVO parcelaVO : getItemParaRenegociar().getParcelas()) {
                if (parcelaVO.getParcelaEscolhida()) {
                    if (getFacade().getMovParcela().parcelaEstaBloqueadaPorCobranca(parcelaVO)) {
                        throw new Exception("Não será possível renegociar a " + parcelaVO.getDescricao() + " pois ela está em uma remessa gerada ou aguardando retorno.");
                    }
                    if (getFacade().getMovParcela().parcelaEstaBloqueadaPorBoletoPendente(parcelaVO, false)) {
                        List<BoletoVO> boletosPendentes = getFacade().getBoleto().obterBoletosPendentePorMovParcela(parcelaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                        this.getListaBoletosPendentes().addAll(boletosPendentes);
                    }
                    parcelasParaRenegociar.add(parcelaVO);
                }
            }

            if (!UteisValidacao.emptyList(this.getListaBoletosPendentes())) {
                this.setOperacaoOrigemBoletoPendente("renegociar"); //receber cancelar renegociar
                montarAviso("Existe boleto pendente para as parcelas selecionadas.");
                this.setMsgAlert("Richfaces.showModalPanel('modalCancelarBoletosPendentes');" + getMensagemNotificar());
                return "";
            }

            RenegociacaoControle renegociacaoControle = getControlador(RenegociacaoControle.class);
            renegociacaoControle.limpar();
            renegociacaoControle.setParcelasParaRenegociar(parcelasParaRenegociar);
            renegociacaoControle.setItemParaRenegociar(getItemParaRenegociar());
            renegociacaoControle.prepararRenegociacao();
            setMensagemDetalhada(EMPTY, EMPTY);
            renegociacaoControle.setAlteracaoManual(false);
            return "renegociacao";
        } catch (Exception ex) {
            montarErro(ex);
        }

        return EMPTY;
    }

    public String novo() throws Exception {
        return novo(true, true);
    }

    public String abrirCE() throws Exception {
        novo(true, true);
        return "caixaCE";
    }

    public String novo(boolean original, boolean limparDatas) throws Exception {
        return novo(original, limparDatas, true);
    }

    public String novo(boolean original, boolean limparDatas, boolean inicializarDadosContratoControle) throws Exception {
        processarRecursoPadrao();
        if(permissao("ConsultarParcelasTodasEmpresas")){
            setApresentarEmpresa(true);
        }
        if (context() != null) {
            mensagemControle = getControlador(MensagemGenericaControle.class);
        }

        montarListaSelectItemEmpresa();
        try {
            inicializarEmpresa();
        } catch (Exception e) {
            Uteis.logar("Erro ao consultar empresa: " + e.getMessage(), MovParcelaControle.class);
            e.printStackTrace();
        }
        setMovParcelaVO(new MovParcelaVO());
        setOrdenarPorLancamento(Boolean.TRUE);
        setOrdenarPorAlfabetica(Boolean.FALSE);
        if (getFacade().getConfiguracaoSistema().utilizarSistemaParaClube()) {
            setNaoApresentarVencimentosDeMesesFuturos(true);
        }
        setResponsavelCancelamento(new UsuarioVO());
        itensSelecionados = new ArrayList<CaixaAbertoTO>();

        setItensCaixaAberto(new ArrayList<CaixaAbertoTO>());
        setConfPaginacao(new ConfPaginacao());
        if (limparDatas) {
            setDataInicio(null);
            setDataTermino(null);
        } else {
            setNaoApresentarVencimentosDeMesesFuturos(false);
        }

        setListaParcelasPagar(new ArrayList());
        setListaResponsavelPagamento(new ArrayList());
        setValorTotalParcela(0.0);
        setAbrirRichConfirmacaoCancelamento(false);


        if (original) {
            setIncluirParcelasRecorrencia(true);
            setValorConsulta("");
            setMatricula("");
            setProduto("");

            //Limpa a mensagem de sucesso da tela de caixa aberto
            this.limparMensagemSucesso();

            if (context() != null) {
                RenegociacaoControle renegociacaoControle = (RenegociacaoControle) getControlador(RenegociacaoControle.class);
                //Limpa a imagem de erro da tela de caixa em aberto
                renegociacaoControle.setErro(false);
            }
        }
        setConsultarHoje(false);
        setConsultarSemana(false);
        if(inicializarDadosContratoControle){
            inicializarContrato();
        }
        inicializarUsuarioLogado();
        if(itensCaixaAberto.isEmpty() && !getValorConsulta().isEmpty()){
        	consultaCaixaAberto();
        }
        setMensagemDetalhada("msg_entre_dados", "");
        setNumeroContratoResponsavel(0);
        setConfirmacaoCancelamento("");
        setValorMultaJuros(null);
        setExisteParcelaSelecionada(false);
        setSelecionarTodasParcelas(false);
        if (context() != null) {
            notificarRecursoEmpresa(RecursoSistema.CAIXA_EM_ABERTO);
        }
        setApresentarRenegociarParcelas(validarApresentarRenegociarParcelas());
        processarApresentarImprimirComprovanteCompra();
        return "tela8";
    }

    public final void inicializarEmpresa() throws Exception {
        if(permissao("ConsultarParcelasTodasEmpresas")) {
            filtroCodigoEmpresa = 0;
        }else{
            empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            filtroCodigoEmpresa = empresa.getCodigo();
        }
        if (empresa == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        listaSelectItemEmpresa = new ArrayList<SelectItem>();
        if (!isApresentarEmpresa()) {
            return;
        }
        List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        Iterator i = resultadoConsulta.iterator();
        listaSelectItemEmpresa.add(new SelectItem(0, "TODOS"));
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            listaSelectItemEmpresa.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>MovParcela</code> para alteração. O objeto desta classe é
     * disponibilizado na session da página (request) para que o JSP
     * correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            MovParcelaVO obj = getFacade().getMovParcela().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setMovParcelaVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe
     * <code>MovParcelaVO</code>. Esta inicialização é necessária por exigência
     * da tecnologia JSF, que não trabalha com valores nulos para estes
     * atributos.
     */
    public void inicializarAtributosRelacionados(MovParcelaVO obj) {
        if (obj.getContrato() == null) {
            obj.setContrato(new ContratoVO());
        }
        if (obj.getResponsavel() == null) {
            obj.setResponsavel(new UsuarioVO());
        }

    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe <code>MovParcela</code>. Caso o objeto seja novo (ainda não
     * gravado no BD) é acionado a operação <code>incluir()</code>. Caso
     * contrário é acionado o <code>alterar()</code>. Se houver alguma
     * inconsistência o objeto não é gravado, sendo re-apresentado para o
     * usuário juntamente com uma mensagem de erro.
     */
    public void gravar() throws Exception {
        try {
            if (movParcelaVO.isNovoObj()) {
                getFacade().getMovParcela().incluir(movParcelaVO);
            } else {
                getFacade().getMovParcela().alterar(movParcelaVO);
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            throw e;
        }
    }

    public String validarListaParcelasPagar() throws Exception {
        if (validarBasico()) {
            setMensagemDetalhada("", "");
            setMovParcelaVO(getFacade().getMovParcela().consultarPorChavePrimaria(getMovParcelaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            if (getFacade().getConfiguracaoSistema().utilizarSistemaParaClube()) {
                Map<Integer, Integer> titularesDependentes = getFacade().getCliente().montarMapaPessoasTitulares();
                for (MovParcelaVO parcela : getListaParcelasPagar()) {
                    Integer codTitular = titularesDependentes.get(parcela.getPessoa().getCodigo());
                    MovParcelaVO movParcelaTitular = new MovParcelaVO();
                    movParcelaTitular.setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(codTitular, Uteis.NIVELMONTARDADOS_TODOS));
                    setMovParcelaVO(movParcelaTitular);
//                    if (parcela.getPessoa().getCodigo().equals(codTitular)) {
//                        parcela = getFacade().getMovParcela().consultarPorChavePrimaria(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
//                        setMovParcelaVO(parcela);
//                    }
                }
            }

            return "gravar";
        } else {
            return "erro";
        }
    }
    //?? Chamar esse método quando nécessario na VIEW, ele contem tratamento de erro ??
    public String validarListaParcelasPagarView() {
        try {
            limparMsg();
            this.setMsgAlert("");
            setMensagemDetalhada("");
            this.setListaBoletosPendentes(new ArrayList<>());

            for (MovParcelaVO movParc : getListaParcelasPagar()) {
                if (getFacade().getMovParcela().parcelaEstaBloqueadaPorCobranca(movParc)) {
                    throw new Exception("Não será possível pagar a " + movParc.getDescricao() + " pois ela está em uma remessa gerada, aguardando retorno ou em uma transação pendente.");
                }
                if (getFacade().getBoleto().existeBoletoPendentePorMovParcela(movParc.getCodigo(), true)) {
                    List<BoletoVO> boletosPendentes = getFacade().getBoleto().obterBoletosPendentePorMovParcela(movParc.getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    this.getListaBoletosPendentes().addAll(boletosPendentes);
                }
            }
            if (!UteisValidacao.emptyList(this.getListaBoletosPendentes())) {
                this.setOperacaoOrigemBoletoPendente("receber"); //receber cancelar renegociar
                montarAviso("Existe boleto pendente para as parcelas selecionadas.");
                this.setMsgAlert("Richfaces.showModalPanel('modalCancelarBoletosPendentes');" + getMensagemNotificar());
                return "";
            }
            String ret = validarListaParcelasPagar();
            if (ret.equalsIgnoreCase("erro")) {
                throw new Exception(getMensagemDetalhada());
            } else {
                return ret;
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
            return "";
        }
    }

    public void validarCancelarParcelas() {
        try {
            limparMsg();
            setJustificativaCancelamento("");
            setConfirmacaoCancelamento("");
            setAbrirRichConfirmacaoCancelamento(false);
            this.setMsgAlert("");
            this.setListaBoletosPendentes(new ArrayList<>());

            if (UteisValidacao.emptyList(getListaParcelasPagar())) {
                throw new Exception("Não foi selecionado nenhuma parcela para cancelar.");
            }

            for (MovParcelaVO movParc : getListaParcelasPagar()) {
                if (movParc.isIncluidaSPC()) {
                    throw new Exception("Não será possível cancelar a " + movParc.getDescricao() + " pois ela está incluída no SPC.");
                }

                if (getFacade().getMovParcela().parcelaEstaBloqueadaPorCobranca(movParc)) {
                    throw new Exception("Não será possível cancelar a " + movParc.getDescricao() + " pois ela está em uma remessa gerada, aguardando retorno ou em uma transação pendente.");
                }
                if (getFacade().getMovParcela().parcelaEstaBloqueadaPorBoletoPendente(movParc, false)) {
                    List<BoletoVO> boletosPendentes = getFacade().getBoleto().obterBoletosPendentePorMovParcela(movParc.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    this.getListaBoletosPendentes().addAll(boletosPendentes);
                }
            }

            if (!UteisValidacao.emptyList(this.getListaBoletosPendentes())) {
                this.setOperacaoOrigemBoletoPendente("cancelar"); //receber cancelar renegociar
                montarAviso("Existe boleto pendente para as parcelas selecionadas.");
                this.setMsgAlert("Richfaces.showModalPanel('modalCancelarBoletosPendentes');" + getMensagemNotificar());
            } else {
                setAbrirRichConfirmacaoCancelamento(true);
            }
        } catch (Exception ex) {
            setAbrirRichConfirmacaoCancelamento(false);
            montarErro(ex);
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        obterUsuarioLogado();
        setResponsavelCancelamento(getUsuarioLogado());
    }

    public void consultarResponsavelCancelamento() {
        try {
            setResponsavelCancelamento(getFacade().getUsuario().consultarPorChavePrimaria(getResponsavelCancelamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getResponsavelCancelamento().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            setMensagemDetalhada("msg_dados_consultados", "");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void cancelarParcelas(UsuarioVO responsavelCancelamento) throws Exception {
        Connection con = getFacade().getZWFacade().getCon();
        try {
            limparMsg();
            con.setAutoCommit(false);
            cancelarProdutosVinculados();
            for (MovParcelaVO parcela : getListaParcelasPagar()) {
                List<LogVO> listaLog = new ArrayList<>();
                listaLog.add(parcela.gerarlogCancelamentoParcela(responsavelCancelamento));
                parcela.setSituacao("CA");
                parcela.setJustificativaCancelamento(getJustificativaCancelamento());
                getFacade().getMovParcela().alterarSomenteSituacaoSemCommit(parcela);
                getFacade().getClienteMensagem().excluirClienteMensagemPorMovParcela(parcela.getCodigo());
                getFacade().getPix().excluirPorParcela(parcela.getCodigo(), responsavelCancelamento);
                try {
                    SuperControle.registrarLogObjetoVO(listaLog, parcela.getPessoa().getCodigo());
                } catch (Exception e) {
                    SuperControle.registrarLogErroObjetoVO("CANCELAMENTO PARCELA", parcela.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE CANCELAMENTO DE PARCELA", responsavelCancelamento.getNome(),responsavelCancelamento.getUserOamd());
                    e.printStackTrace();
                    throw new Exception("Erro ao registrar justificativa/Log, tentar novamente!");
                }
            }
            retirarClienteRestricoesInadimplenciaRedeEmpresa(getListaParcelasPagar(), con);
            if(getMovParcelaVO() != null) {
                if(getMovParcelaVO().getPessoa() != null) {
                    atualizarInformacoesRdStationMarketing(getMovParcelaVO().getPessoa());
                }
            }
            setConfirmacaoCancelamento("Parcelas Canceladas com Sucesso!");
            setAbrirRichConfirmacaoCancelamento(false);
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
            setListaParcelasPagar(new ArrayList());
            itensSelecionados = new ArrayList<>();
            setListaResponsavelPagamento(new ArrayList());
            setValorTotalParcela(0.0);
            consultaCaixaAberto();
            con.commit();
            limparMsg();
            montarSucessoGrowl("Parcelas Canceladas com Sucesso!");
        } catch (Exception e) {
            e.printStackTrace();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            con.rollback();
            con.setAutoCommit(true);
            montarErro(e);
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void atualizarInformacoesRdStationMarketing(PessoaVO pessoa) {
        try {
            boolean configsHabilitadas = getFacade().getEmpresa().configsRdStationMarketingEstaoHabilitadas(getEmpresa().getCodigo());
            if(configsHabilitadas) {
                ClienteVO clienteCompleto = getFacade().getCliente().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                getFacade().getZWFacade().startThreadRDStationMarketing(clienteCompleto, null, null,
                        AtualizarCamposEnum.APENAS_SITUACAO_INADIMPLENCIA);
            }
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    private void retirarClienteRestricoesInadimplenciaRedeEmpresa(List<MovParcelaVO> movParcelaVOS, Connection con) {
        try {
            Empresa empresaDAO = new Empresa(con);
            Cliente clienteDAO = new Cliente(con);
            ClienteRestricao clienteRestricaoDAO = new ClienteRestricao(con);

            final Map<Integer, EmpresaVO> mapEmpresas = new HashMap<>();
            final Map<Integer, PessoaVO> mapPessoas = new Hashtable<>();

            for (MovParcelaVO movParcelaVO : movParcelaVOS) {
                if (movParcelaVO.getPessoa() != null) {
                    mapPessoas.put(movParcelaVO.getPessoa().getCodigo(), movParcelaVO.getPessoa());
                }
            }

            final List<ClienteVO> clientesRemoverRestricoes = new ArrayList<>();
            final List<String> cpfs = new ArrayList<>();

            for (PessoaVO pessoaVO : mapPessoas.values()) {
                final ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (clienteVO == null || clienteVO.getDataInclusaoClienteRestricaoRedeEmpresa() == null) {
                    continue;
                }

                EmpresaVO empresaVO;
                if (mapEmpresas.get(clienteVO.getEmpresa().getCodigo()) == null) {
                    empresaVO = empresaDAO.consultarPorChavePrimaria(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    mapEmpresas.put(empresaVO.getCodigo(), empresaVO);
                } else {
                    empresaVO = mapEmpresas.get(clienteVO.getEmpresa().getCodigo());
                }

                if (empresaVO == null || !empresaVO.isUtilizaGestaoClientesComRestricoes()) {
                    continue;
                }

                if (clienteDAO.clienteEstaInadimplente(clienteVO.getCodigo(), empresaVO.getCodigo())) {
                    continue;
                }

                if (UteisValidacao.emptyString(clienteVO.getPessoa().getCfp())) {
                    continue;
                }

                final String cpf = Uteis.tirarCaracteres(clienteVO.getPessoa().getCfp(), true);
                if (cpf.length() != 11) {
                    return;
                }

                clientesRemoverRestricoes.add(clienteVO);
                cpfs.add(cpf);
            }

            if (UteisValidacao.emptyList(cpfs)) {
                return;
            }

            String chave;
            if (JSFUtilities.isJSFContext()) {
                chave = (String) JSFUtilities.getFromSession(JSFUtilities.KEY);
            } else {
                chave = DAO.resolveKeyFromConnection(con);
            }

            final RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
            final boolean temRedeEmpresa = redeEmpresa != null && redeEmpresa.getGestaoRedes();

            if (temRedeEmpresa) {
                AdmCoreMsService.retirarClienteRestricoesCpfs(redeEmpresa, chave, cpfs, TipoClienteRestricaoEnum.INADIMPLENCIA);
            } else {
                clienteRestricaoDAO.excluirSemCommit(cpfs, TipoClienteRestricaoEnum.INADIMPLENCIA);
            }

            for (ClienteVO clienteVO : clientesRemoverRestricoes) {
                clienteDAO.atualizarDataInclusaoClienteRestricao(clienteVO.getCodigo(), null);
            }

            empresaDAO = null;
            clienteDAO = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void fecharRichModalPanelConfirmacao() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        auto.setOnComplete("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                UsuarioVO responsavelCancelamento = auto.getUsuario();

                cancelarParcelas(responsavelCancelamento);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                limparMsg();
            }
        };
        listener.setPaginaDestino("tela8");

        limparMsg();
        try {
            if (getAbrirRichConfirmacaoCancelamento()) {
                auto.autorizar("Confirmação de Cancelamento de Parcelas", "CancelamentoParcela",
                        "Você precisa da permissão \"4.25 - Cancelar parcela em aberto\"",
                        "form:panelCaixaAberto", listener);
            }

        } catch (Exception e) {
            montarErro(e);
        }
    }


    public boolean validarBasico() throws Exception {
    	if (getListaParcelasPagar().isEmpty()) {
            setMensagemDetalhada("Não foi escolhida nenhuma PARCELA PARA PAGAR.");
            return false;
        } else if (getValorTotalParcela() < 0.0) {
            setMensagemDetalhada("Não é possivel realizar o pagamento, pois o campo VALOR TOTAL é menor do que " + getEmpresaLogado().getMoeda() +" 0.00");
            return false;
        }else if(existemParcelasSelecionadasEmMaisDeUmaEmpresa()){
            setMensagemDetalhada("Não é permitido receber parcelas de empresas diferentes ao mesmo tempo.");
            return false;
        } else{
            MovPagamentoControle movPagamentoControle = (MovPagamentoControle) context().getExternalContext().getSessionMap().get("MovPagamentoControle");
            if (movPagamentoControle != null) {
                movPagamentoControle.liberarBackingBeanMemoria("MovPagamentoControle");
            }
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
            if (vendaAvulsaControle != null) {
                vendaAvulsaControle.liberarBackingBeanMemoria("VendaAvulsaControle");
                vendaAvulsaControle = null;
            }
            ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
            if (contratoControle != null) {
                contratoControle.liberarBackingBeanMemoria("ContratoControle");
                contratoControle = null;
            }
            AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");
            if (aulaAvulsaDiariaControle != null) {
                aulaAvulsaDiariaControle.liberarBackingBeanMemoria("AulaAvulsaDiariaControle");
                aulaAvulsaDiariaControle = null;
            }
            setMensagemDetalhada("", "");
            return true;
        }
    }

    public String validarListaParcelasPagarCE() throws Exception {
        if (validarBasico()) {
            FOROUT:
            for (CaixaAbertoTO item : itensCaixaAberto) {
                for (MovParcelaVO parcela : item.getParcelas()) {
                    if (parcela.getParcelaEscolhida()) {
                        this.setCodigoEventoInteresse(item.getCodigoEvento());
                        break FOROUT;
                    }

                }
            }
            return "pagamentoCE";
        } else {
            return "pagamento";
        }
    }

    public void selecionarResponsavelPagamento() {
        try {
            boolean encontrado = false;
            Iterator i = getListaParcelasPagar().iterator();
            while (i.hasNext()) {
                MovParcelaVO movParcela = (MovParcelaVO) i.next();
                if (movParcela.getCodigo().equals(getNumeroContratoResponsavel())) {
                    encontrado = true;
                    setMovParcelaVO(movParcela);
                    break;
                }
            }
            // se nao conseguiu posicionar parcela
            if (!encontrado) {
                // se a lista esta vazia
                if (getListaParcelasPagar().isEmpty()) {
                    setMovParcelaVO(new MovParcelaVO());
                } // se a lista nao esta vazia
                else {
                    setMovParcelaVO(getListaParcelasPagar().get(0));
                }
            }

            validarApresentarOpcaoBoleto();
        } catch (Exception e){
            montarErro(e);
        }
    }

    public void acaoSelecionarTodos() {
        try {
            setMensagemDetalhada("", "");

            for (CaixaAbertoTO caixaAbertoTO : itensCaixaAberto) {
                caixaAbertoTO.setMarcarTodas(selecionarTodasParcelas);
                if (JSFUtilities.isJSFContext()) {
                    request().setAttribute("item", caixaAbertoTO);
                    marcaTodasParcelas();
                }
            }

            setSucesso(false);
            setErro(false);
        } catch (Exception ex) {
            setErro(true);
            setMensagemDetalhada("", "");
        }
    }

    private void validarApresentarOpcaoBoleto() {
        try {
            existeConvenioCobrancaBoleto = false;
            existeAutorizacaoBoleto = false;

            if (UteisValidacao.emptyNumber(getMovParcelaVO().getCodigo())) {
                return;
            }

            //validar se tem permissão se não tiver nem verifica se existeautorização
            validarPermissao("EmitirBoleto", "4.35 - Emitir boletos para Cliente", getUsuarioLogado());

            if (getFacade().getEmpresa().isGerarBoletoCaixaAberto(getEmpresaLogado().getCodigo())) {
                List<ConvenioCobrancaVO> conveniosBoleto = getFacade().getConvenioCobranca().consultarPorTipoCobranca(
                        new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO,TipoCobrancaEnum.BOLETO_ONLINE},
                        getMovParcelaVO().getEmpresa().getCodigo(), SituacaoConvenioCobranca.ATIVO, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                existeConvenioCobrancaBoleto = conveniosBoleto.size() > 0;
            } else {
                List<AutorizacaoCobrancaClienteVO> autorizacoesBoleto = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoaTipoAutorizacao(getMovParcelaVO().getPessoa().getCodigo(),
                        TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO, Uteis.NIVELMONTARDADOS_MINIMOS);
                existeAutorizacaoBoleto = !UteisValidacao.emptyList(autorizacoesBoleto);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void adicionarItemSelecionado(CaixaAbertoTO item, MovParcelaVO parcela, boolean remover) {
        //verifico se o item já está contido na lista de parcelas a pagar
        if (getItensSelecionados().contains(item)) {
            //obtenho o objeto da lista
            int index = getItensSelecionados().indexOf(item);
            CaixaAbertoTO itemEdicao = getItensSelecionados().get(index);

            if (remover) {
                //se for remover, verificar se o item ficará sem parcelas selecionadas, se positivo, remover da lista tbm
                itemEdicao.getParcelasSelecionadas().remove(parcela.getCodigo());
                if (itemEdicao.getNrParcelasSelecionadas().equals(0)) {
                    getItensSelecionados().remove(itemEdicao);
                }
                if (getItensSelecionados().size() == 0) {
                    setExisteParcelaSelecionada(false);
                    setSelecionarTodasParcelas(false);
                }
            } else //adicionar a lista de selecionadas
            {
                itemEdicao.getParcelasSelecionadas().put(parcela.getCodigo(), parcela.getValorParcela());
                setExisteParcelaSelecionada(true);
            }
        } else {
            //adicionar a lista de itens a pagar
            CaixaAbertoTO itemAdicao = item.cloneReduzido();
            itemAdicao.getParcelasSelecionadas().put(parcela.getCodigo(), parcela.getValorParcela());
            getItensSelecionados().add(itemAdicao);
            setExisteParcelaSelecionada(true);
        }
    }

    public boolean existemParcelasSelecionadasEmMaisDeUmaEmpresa(){
        List<Integer> empresas = new ArrayList<Integer>();
        for (MovParcelaVO parcela: getListaParcelasPagar()) {
            if(empresas.indexOf(parcela.getEmpresa().getCodigo()) == -1){
                empresas.add(parcela.getEmpresa().getCodigo());
            }
        }

        return empresas.size() > 1;
    }

    public void selecionarParcela() throws Exception {

        CaixaAbertoTO item = (CaixaAbertoTO) context().getExternalContext().getRequestMap().get("item");
        MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("parcelaContrato");
        selecionarParcelaPagar(item,obj);
    }

    public void selecionarParcelaPagar(CaixaAbertoTO caixaAbertoTO,MovParcelaVO obj)throws Exception{
        if (obj.getParcelaEscolhida() && obj.getSituacao().equals("EA")) {
            obj.setParcelaEscolhida(true);
            getListaParcelasPagar().add(obj);
            adicionarItemSelecionado(caixaAbertoTO, obj, false);
            setValorTotalParcela(getValorTotalParcela() + Uteis.arredondarForcando2CasasDecimais(obj.getValorParcela()));
            if (getMovParcelaVO().getCodigo() == null || getMovParcelaVO().getCodigo() == 0) {
                setMovParcelaVO(obj);
            }
            obj.setMovProdutoParcelaVOs(getFacade().getMovProdutoParcela().consultarPorCodigoMovParcela(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        } else {
            if (obj.getValorMultaJuros() != null) {
                setValorMultaJuros(getValorMultaJuros() - obj.getValorMultaJuros());
            }
            obj.setValorMultaJuros(null);
            desmarcaParcelaListaParcelaPagar(obj);
            adicionarItemSelecionado(caixaAbertoTO, obj, true);
        }

        configListaPagar();
        validarSeTodasParcelasEstaoMarcadas(caixaAbertoTO);
        montarMultaJurosParcelaVencida(Calendario.hoje());
    }

    private void configListaPagar() {
        montarListaResponsavelPagamento();
        setValorTotalParcela(Uteis.arredondarForcando2CasasDecimais(getValorTotalParcela()));
        selecionarResponsavelPagamento();
    }

    public void removerListaParcelaPagar() {
        CaixaAbertoTO item = (CaixaAbertoTO) context().getExternalContext().getRequestMap().get("selecionada");
        int index = getItensCaixaAberto().indexOf(item);
        if (index >= 0) {
            item = getItensCaixaAberto().get(index);
            for (MovParcelaVO obj : item.getParcelas()) {
                desmarcaParcelaListaParcelaPagar(obj);
                configListaPagar();
            }
            validarSeTodasParcelasEstaoMarcadas(item);
        } else {
            Set<Integer> keySet = item.getParcelasSelecionadas().keySet();
            for (Integer key : keySet) {
                MovParcelaVO obj = new MovParcelaVO();
                obj.setCodigo(key);
                desmarcaParcelaListaParcelaPagar(obj);
                configListaPagar();
            }
        }
        getItensSelecionados().remove(item);
        if (getItensSelecionados().isEmpty()) {
            setListaParcelasPagar(new ArrayList());
            setValorTotalParcela(0.0);
        }
    }

    public void desmarcaParcelaListaParcelaPagar(MovParcelaVO obj) {
        int index = 0;
        Iterator i = getListaParcelasPagar().iterator();
        while (i.hasNext()) {
            MovParcelaVO movParcela = (MovParcelaVO) i.next();
            if (movParcela.getCodigo().equals(obj.getCodigo().intValue())) {
                getListaParcelasPagar().remove(index);
                setValorTotalParcela(getValorTotalParcela() - movParcela.getValorParcela());
                obj.setParcelaEscolhida(false);
                removerObjListaResponsavelPagamento(movParcela);
                return;
            }
            index++;
        }
    }

    public void validarSeTodasParcelasEstaoMarcadas(CaixaAbertoTO item) {
        for (MovParcelaVO movParcela : item.getParcelas()) {
            if (!movParcela.getParcelaEscolhida()) {

                item.setMarcarTodas(false);
                return;
            }
        }
        item.setMarcarTodas(true);
    }

    public void montarListaResponsavelPagamento() {
        for (MovParcelaVO obj : getListaParcelasPagar()) {
            int passo = 0;
            if (getListaResponsavelPagamento().isEmpty()) {
                getListaResponsavelPagamento().add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
            } else {
                montarListaResponsavelPagamentoPreenchida(obj, passo);
            }
        }
    }

    public void montarListaResponsavelPagamentoPreenchida(MovParcelaVO obj, int passo) {
        for (SelectItem selItem : getListaResponsavelPagamento()) {
            if (selItem.getLabel().equals(obj.getPessoa().getNome())) {
                passo = 1;
            }
        }
        if (passo == 0) {
            getListaResponsavelPagamento().add(new SelectItem(obj.getCodigo(), obj.getPessoa().getNome()));
        }
    }

    public void removerObjListaResponsavelPagamento(MovParcelaVO obj) {
        int index = 0;
        Iterator i = getListaResponsavelPagamento().iterator();
        while (i.hasNext()) {
            SelectItem movParcela = (SelectItem) i.next();
            if (movParcela.getValue().equals(obj.getCodigo())) {
                getListaResponsavelPagamento().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * MovParcelaCons.jsp. Define o tipo de consulta a ser executada, por meio
     * de ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovParcela().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovParcela().
                        consultarPorDataRegistro(Uteis.getDateTime(valorData, 0, 0, 0),
                        Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacao")) {
                objs = getFacade().getMovParcela().consultarPorSituacao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("contrato")) {
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovParcela().consultarPorContrato(valorInt, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                objs = getFacade().getMovParcela().
                        consultarPorNomeCliente(getControleConsulta().getValorConsulta(),
                                getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }
    /*Inicio*/

    /**
     * Metodo listener que obtem os parametros necessarios para realizar
     * a paginacao e filtros da tela
     *
     ** Autora: Carla
     * Criado em 14/01/2011
     */
    public void consultarPaginadoListener(ActionEvent evt) {
        basicoConsultaPaginada(evt);
        consultarPaginado();
    }

    /**
     * Joao Alcides
     * 10/05/2012
     * @param evt
     */
    public void caixaAbertoListener(ActionEvent evt) {
        basicoConsultaPaginada(evt);
        setConfirmacaoCancelamento("");
        consultaCaixaAberto();

        //Limpara a mensagem de sucesso da tela de caixa aberto
        notificarRecursoEmpresa(RecursoSistema.CAIXA_EM_ABERTO_BTN_CONSULTAR);
        this.limparMensagemSucesso();
    }

    /**
     * Joao Alcides
     * 10/05/2012
     * @param evt
     */
    public void caixaCentralEventoListener(ActionEvent evt) {
        basicoConsultaPaginada(evt);
        setConfirmacaoCancelamento("");
        consultaCentralEventos();
    }

    private void basicoConsultaPaginada(ActionEvent evt) {
        //==================================================================================================================================

        //VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
        if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacao(new ConfPaginacao());
            }
        }

        //Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");
        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());
        }

        //==================================================================================================================================

        Object compTipoConsulta = evt.getComponent().getAttributes().get("tipoConsulta");
        if (compTipoConsulta != null && !"".equals(compTipoConsulta.toString())) {
            if (!compTipoConsulta.toString().equals(this.paramTipoConsulta)) {
                setConfPaginacao(new ConfPaginacao());
            }
            this.setParamTipoConsulta(compTipoConsulta.toString());
        }
    }

    public void limparConsulta() {
        setItensCaixaAberto(new ArrayList<CaixaAbertoTO>());
        setConfPaginacao(new ConfPaginacao());
        setValorConsulta("");
        setMatricula("");
        setProduto("");
        setDataInicio(null);
        setDataTermino(null);
        setConsultarSemana(false);
        setConsultarHoje(false);
        limparMsg();
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco
     *
     ** Autora: Carla
     * Criado em 14/01/2011
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginado() {
        try {
            super.consultar();
            MovParcelaFiltroVO filtro = new MovParcelaFiltroVO();
            filtro.setControlarAcesso(true);
            filtro.setNivelMontarDados(Uteis.NIVELMONTARDADOS_TELACONSULTA);
            filtro.setMovParcelaVO(new MovParcelaVO());
            //seta as datas para nulo para efetuar a consulta corretamente
            filtro.getMovParcelaVO().setDataRegistro(null);
            filtro.getMovParcelaVO().setDataVencimento(null);

            if ("detalhada".equals(this.getParamTipoConsulta())) {
                int valorInt = 0;
                if (getEmpresaLogado().getCodigo() != 0) {
                    filtro.setEmpresaVO(new EmpresaVO());
                    filtro.getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
                }
                if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        filtro.setPessoaVO(new PessoaVO());
                        filtro.getPessoaVO().setNome(getControleConsulta().getValorConsulta().trim());
                    }
                } else if (getControleConsulta().getCampoConsulta().equals("contrato")) {
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                        filtro.setContratoVO(new ContratoVO());
                        filtro.getContratoVO().setCodigo(valorInt);
                    }
                } else if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                        filtro.getMovParcelaVO().setCodigo(valorInt);
                    }
                } else if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        validarDataRegistro(filtro);
                    }
                } else if (getControleConsulta().getCampoConsulta().equals("dataVencimento")) {
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        validarDataVencimento(filtro);
                    }
                } else if (getControleConsulta().getCampoConsulta().equals("situacao")) {
                    if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                        filtro.getMovParcelaVO().setSituacao(getControleConsulta().getValorConsulta().trim());
                    }
                }

                List objs = getFacade().getMovParcela().consultarPaginado(filtro, getConfPaginacao());
                setListaConsulta(objs);
                setMensagemID("msg_dados_consultados");
                setSucesso(true);
                setErro(false);

                this.getConfPaginacao().definirVisibilidadeLinksNavegacao();
            }
        } catch (java.text.ParseException e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", "A data deve ser informada com o seguinte formato: dd/mm/aaaa");
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void validarDataRegistro(MovParcelaFiltroVO filtro) throws Exception {
        Date dataRegistro = Uteis.getSQLData(Uteis.getDate(getControleConsulta().getValorConsulta().trim()));
        if (dataRegistro != null) {
            filtro.getMovParcelaVO().setDataRegistro(dataRegistro);
            return;
        }
    }

    public void validarDataVencimento(MovParcelaFiltroVO filtro) throws Exception {
        Date dataVencimento = Uteis.getSQLData(Uteis.getDate(getControleConsulta().getValorConsulta().trim()));
        if (dataVencimento != null) {
            filtro.getMovParcelaVO().setDataVencimento(dataVencimento);
            return;
        }
    }

    public void consultaCaixaAberto() {
        try {
            getConfPaginacao().setItensPorPagina(15);
            setarDatas();

            //o codigoContratoEvento será diferente de nulo quando este controle for acessado pelo Central de Eventos
            if (codigoContratoEvento != null) {
                //pesquisar o contrato referente ao código obtido
                setItensCaixaAberto(getFacade().getCentralEventosFacade().getNegEvContrato().obterParcelasCaixaAberto(codigoContratoEvento, null, null, null, getConfPaginacao(),
                        getOrdenarPorLancamento(), parcelasSelecionadas()));
                setExisteParcelasCE(Boolean.TRUE);

            } else {
                filtroCodigoEmpresa = filtroCodigoEmpresa == null ? 0 : filtroCodigoEmpresa;
                setItensCaixaAberto(getFacade().getMovParcela().consultaCaixaEmAberto(getMatricula(), getValorConsulta(), getProduto(),
                        filtroCodigoEmpresa, getDataInicio(), getDataTermino(), getConfPaginacao(),
                        this.incluirParcelasRecorrencia, this.getOrdenarPorLancamento(), parcelasSelecionadas(), getNaoApresentarVencimentosDeMesesFuturos(), 0));
            }

            this.getConfPaginacao().definirVisibilidadeLinksNavegacao();
            if (getItensCaixaAberto().isEmpty()) {
                String msg = gerarMensagemErro();
                throw new Exception(msg);
            }
            limparMsg();
        } catch (Exception e) {
            setListaResponsavelPagamento(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultaCentralEventos() {
        try {
            setarDatas();
            //pesquisar o contrato referente ao código obtido
            setItensCaixaAberto(getFacade().getCentralEventosFacade().getNegEvContrato().obterParcelasCaixaAberto(null, getValorConsulta(), getDataInicio(), getDataTermino(),
                    getConfPaginacao(), getOrdenarPorLancamento(), parcelasSelecionadas()));
            setExisteParcelasCE(Boolean.TRUE);
            this.getConfPaginacao().definirVisibilidadeLinksNavegacao();

            if (getItensCaixaAberto().isEmpty()) {
                String msg = gerarMensagemErro();
                throw new Exception(msg);
            }
            limparMsg();
        } catch (Exception e) {
            setListaResponsavelPagamento(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void setarDatas() throws Exception {
        if (getConsultarHoje()) {
            setDataInicio(Calendario.getDataComHoraZerada(Calendario.hoje()));
            setDataTermino(Calendario.getDataComHoraZerada(Calendario.hoje()));
        } else if (getConsultarSemana()) {
            setDataInicio(Uteis.obterPrimeiroEUltimoDiaSemana(true));
            setDataTermino(Uteis.obterPrimeiroEUltimoDiaSemana(false));
        }
    }

    public void limparBusca() {
        setItensCaixaAberto(new ArrayList<CaixaAbertoTO>());
        setListaResponsavelPagamento(new ArrayList());
        setConfPaginacao(new ConfPaginacao());

        limparMensagemSucesso();

        limparMensagemErro();
    }

    public void limparMensagemSucesso(){
        RenegociacaoControle rc = null;
        if (context() != null) {
            rc = getControlador(RenegociacaoControle.class);
        }

    	if(rc != null){

    		rc.setMensagem("");
    		rc.setMensagemDetalhada("", "");
    		rc.setSucesso(false);
    		context().getExternalContext().getSessionMap().put("RenegociacaoControle", rc);
    	}
    }

    public void limparMensagemErro(){

    	setMensagemDetalhada("", "");
    }

    public String gerarMensagemErro() {
        String msg = "Nenhum cliente foi encontrado com os seguintes filtros de pesquisa:";
        if (!getValorConsulta().equals("")) {
            msg = msg + " e CLIENTE -> " + getValorConsulta();
        }
        if (getDataInicio() != null) {
            msg = msg + " e  PERÍODO DE VENCIMENTO DE PARCELAS DE -> " + getDataInicio_Apresentar() + " ";
        }
        if (getDataTermino() != null) {
            msg = msg + " ATÉ " + getDataTermino_Apresentar() + " ";
        }
        return msg.replaceFirst(" e ", "");
    }

    public void consultarPorPeriodo() throws Exception {
        if (Uteis.getCompareData(dataInicio, dataTermino) <= 0) {
            objsVendaAvulsa = getFacade().getMovParcela().consultarPorDataRegistroVendaAvulsa(getValorConsulta(), filtroCodigoEmpresa, getDataInicio(), getDataTermino(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            objsContrato = getFacade().getContrato().consultarPorParcelaPessoaData(getValorConsulta(), filtroCodigoEmpresa, getDataInicio(), getDataTermino(), this.incluirParcelasRecorrencia, Uteis.NIVELMONTARDADOS_PARCELA);
            objsAulaAvulsaDiaria = getFacade().getMovParcela().consultarPorDataRegistroAulaAvulsaDiaria(getValorConsulta(), filtroCodigoEmpresa, getDataInicio(), getDataTermino(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            objsPersonal = getFacade().getMovParcela().consultarPorDataRegistroNegociacaoPersonal(getValorConsulta(), "EA", filtroCodigoEmpresa, getDataInicio(), getDataTermino(), Uteis.NIVELMONTARDADOS_MINIMOS);
        } else {
            throw new Exception("O campo (PERÍODO DE) não pode ser maior que o campo (ATÉ).");
        }
    }

    public void marcaTodasParcelas() throws Exception {
        try {
            CaixaAbertoTO obj = (CaixaAbertoTO) context().getExternalContext().getRequestMap().get("item");

            marcarParcelas(obj);
            configListaPagar();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void marcarParcelas(CaixaAbertoTO obj) throws Exception {
        if (obj.getMarcarTodas()) {
            setValorTotalParcela(0.0);
            for (MovParcelaVO movParc : obj.getParcelas()) {
                if (!movParc.getParcelaEscolhida()) {
                    movParc.setParcelaEscolhida(true);
                    getListaParcelasPagar().add(movParc);
                    if (getMovParcelaVO().getCodigo() == null || getMovParcelaVO().getCodigo() == 0) {
                        setMovParcelaVO(movParc);
                    }
                    adicionarItemSelecionado(obj, movParc, false);
                    movParc.setMovProdutoParcelaVOs(getFacade().getMovProdutoParcela().consultarPorCodigoMovParcela(movParc.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                }
            }
            validarSomaDaParcela();
        } else {
            for (MovParcelaVO movParc : obj.getParcelas()) {
                movParc.setParcelaEscolhida(false);
                desmarcaParcelaListaParcelaPagar(movParc);
                if (movParc.getValorMultaJuros() != null){
                    setValorMultaJuros(getValorMultaJuros()-movParc.getValorMultaJuros());
                    movParc.setValorMultaJuros(null);
                }
                adicionarItemSelecionado(obj, movParc, true);
            }
        }
        montarMultaJurosParcelaVencida(Calendario.hoje());
    }

    public void montarMultaJurosParcelaVencida(Date dataPagamento) throws Exception {
        List<MovParcelaVO> parcelasEscolhidas = new ArrayList<MovParcelaVO>();
        for (MovParcelaVO parcelaVO: getListaParcelasPagar()){
            if (parcelaVO.getParcelaEscolhida()){
                parcelasEscolhidas.add(parcelaVO);
            }
        }
        setValorMultaJuros(getFacade().getMovParcela().montarMultaJurosParcelaVencida(getEmpresaLogado(), parcelasEscolhidas, dataPagamento, false, 1.0, null));
    }

    public void validarSomaDaParcela() {
        for (CaixaAbertoTO item : getItensSelecionados()) {
            Set<Integer> keySet = item.getParcelasSelecionadas().keySet();
            for (Integer key : keySet) {
                setValorTotalParcela(getValorTotalParcela() + Uteis.arredondarForcando2CasasDecimais(item.getParcelasSelecionadas().get(key)));
            }
        }
    }

    public void removerMovParcelaListaParcelaPagar(MovParcelaVO movParc) {
        int index = 0;
        Iterator j = getListaParcelasPagar().iterator();
        while (j.hasNext()) {
            MovParcelaVO movParcela = (MovParcelaVO) j.next();
            if (movParcela.getCodigo().equals(movParc.getCodigo().intValue())) {
                getListaParcelasPagar().remove(index);
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>MovParcelaVO</code> Após a exclusão ela automaticamente aciona a
     * rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getMovParcela().excluir(movParcelaVO);
            setMovParcelaVO(new MovParcelaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void obterFiltroConsultarSemana() {
        setConsultarHoje(false);
    }

    public void obterFiltroConsultarHoje() {
        setConsultarSemana(false);
    }

    public void alternarOrdenacaoLanc() {
        if (ordenarPorLancamento) {
            ordenarPorAlfabetica = Boolean.FALSE;
        } else {
            ordenarPorAlfabetica = Boolean.TRUE;
        }
    }

    public void alternarOrdenacaoAlf() {
        if (ordenarPorAlfabetica) {
            ordenarPorLancamento = Boolean.FALSE;
        } else {
            ordenarPorLancamento = Boolean.TRUE;
        }
    }
    /*
     * Método responsável por remover um novo objeto da classe
     * <code>ContratoModalidade</code> do objeto <code>contratoVO</code> da
     * classe <code>Contrato</code>
     */

    public void removerMovProdutoParcela() throws Exception {
        MovProdutoParcelaVO obj = (MovProdutoParcelaVO) context().getExternalContext().getRequestMap().get("movProdutoParcela");
        getMovParcelaVO().excluirObjMovProdutoParcelaVOs(obj.getMovProduto());
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /*
     * Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>situacao</code>
     */
    public List<SelectItem> getListaSelectItemSituacaoMovParcela() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable situacaoMovParcelas = (Hashtable) Dominios.getSituacaoMovParcela();
        Enumeration keys = situacaoMovParcelas.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoMovParcelas.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void montarListaSelectItemParcela() throws Exception {
        getMovParcelaVO().getContrato().setMovParcelaVOs(consultarParcelaPorDescricao());
    }

    public List consultarParcelaPorDescricao() throws Exception {
        return getFacade().getMovParcela().consultarPorContrato(getMovParcelaVO().getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
    }

    /**
     * Método responsável por inicializar a lista de valores (
     * <code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        // montarListaSelectItemConvenioCobranca();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("nomePessoa", "Nome Cliente"));
        itens.add(new SelectItem("contrato", "Número do Contrato"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("dataRegistro", "Data de Registro"));
        itens.add(new SelectItem("dataVencimento", "Data de Vencimento"));
        itens.add(new SelectItem("situacao", "Situação"));
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    public void habilitarCombo() {
        getControleConsulta().setValorConsulta("");
        apresentarComboSituacao = getControleConsulta().getCampoConsulta().equals("situacao");
    }

    public List<SelectItem> getTipoConsultaComboSituacao() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("PG", "Pago"));
        itens.add(new SelectItem("EA", "Em aberto"));
        itens.add(new SelectItem("CA", "Cancelado"));
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public void exibirParcelas() {
        CaixaAbertoTO obj = (CaixaAbertoTO) context().getExternalContext().getRequestMap().get("item");
        if (obj.getExibeParcelas()) {
            obj.setExibeParcelas(Boolean.FALSE);
        } else {
            obj.setExibeParcelas(Boolean.TRUE);
        }
    }

    public void limparConvenio() {
        getMovParcelaVO().getConvenioCobranca().setCodigo(0);
    }

    public Boolean getFechado() {
        return false;
    }

    public MovParcelaVO getMovParcelaVO() {
        return movParcelaVO;
    }

    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataTermino_Apresentar() {
        return Uteis.getData(dataTermino);
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getValorConsulta() {
        if (valorConsulta == null) {
            valorConsulta = "";
        }
        return valorConsulta;
    }

    public void setValorConsulta(String valorConsulta) {
        this.valorConsulta = valorConsulta;
    }

    public Double getValorTotalParcela() {
        return valorTotalParcela;
    }

    public String getValorTotalParcela_Apresentar() {
        return new FormatadorNumerico().getAsString(null, null,
                this.getValorTotalParcela());
    }

    public void setValorTotalParcela(Double valorTotalParcela) {
        this.valorTotalParcela = valorTotalParcela;
    }

    public List<MovParcelaVO> getListaParcelasPagar() {
        return listaParcelasPagar;
    }

    public void setListaParcelasPagar(List listaParcelasPagar) {
        this.listaParcelasPagar = listaParcelasPagar;
    }

    public List<SelectItem> getListaResponsavelPagamento() {
        return listaResponsavelPagamento;
    }

    public void setListaResponsavelPagamento(List listaResponsavelPagamento) {
        this.listaResponsavelPagamento = listaResponsavelPagamento;
    }

    public Integer getNumeroContratoResponsavel() {
        return numeroContratoResponsavel;
    }

    public void setNumeroContratoResponsavel(Integer numeroContratoResponsavel) {
        this.numeroContratoResponsavel = numeroContratoResponsavel;
    }

    public ItemMovParcelaVO getItemMovParcelaVO() {
        return itemMovParcelaVO;
    }

    public void setItemMovParcelaVO(ItemMovParcelaVO itemMovParcelaVO) {
        this.itemMovParcelaVO = itemMovParcelaVO;
    }

    public Boolean getConsultarHoje() {
        return consultarHoje;
    }

    public void setConsultarHoje(Boolean consultarHoje) {
        this.consultarHoje = consultarHoje;
    }

    public Boolean getConsultarSemana() {
        return consultarSemana;
    }

    public void setConsultarSemana(Boolean consultarSemana) {
        this.consultarSemana = consultarSemana;
    }

    public List getObjsAulaAvulsaDiaria() {
        return objsAulaAvulsaDiaria;
    }

    public void setObjsAulaAvulsaDiaria(List objsAulaAvulsaDiaria) {
        this.objsAulaAvulsaDiaria = objsAulaAvulsaDiaria;
    }

    public List getObjsContrato() {
        return objsContrato;
    }

    public void setObjsContrato(List objsContrato) {
        this.objsContrato = objsContrato;
    }

    public List getObjsVendaAvulsa() {
        return objsVendaAvulsa;
    }

    public void setObjsVendaAvulsa(List objsVendaAvulsa) {
        this.objsVendaAvulsa = objsVendaAvulsa;
    }

    /**
     * @return the paramTipoConsulta
     */
    public String getParamTipoConsulta() {
        return paramTipoConsulta;
    }

    /**
     * @param paramTipoConsulta the paramTipoConsulta to set
     */
    public void setParamTipoConsulta(String paramTipoConsulta) {
        this.paramTipoConsulta = paramTipoConsulta;
    }

    /**
     * @param existeParcelasCE the existeParcelasCE to set
     */
    public void setExisteParcelasCE(Boolean existeParcelasCE) {
        this.existeParcelasCE = existeParcelasCE;
    }

    /**
     * @return the existeParcelasCE
     */
    public Boolean getExisteParcelasCE() {
        if (existeParcelasCE == null) {
            existeParcelasCE = Boolean.FALSE;
        }
        return existeParcelasCE;
    }

    /**
     * @param parcelasPagas the parcelasPagas to set
     */
    public void setParcelasPagas(List<MovParcelaVO> parcelasPagas) {
        this.parcelasPagas = parcelasPagas;
    }

    /**
     * @return the parcelasPagas
     */
    public List<MovParcelaVO> getParcelasPagas() {
        if (parcelasPagas == null) {
            parcelasPagas = new ArrayList<MovParcelaVO>();
        }
        return parcelasPagas;
    }

    /**
     * @param existeParcelasPagasCE the existeParcelasPagasCE to set
     */
    public void setExisteParcelasPagasCE(Boolean existeParcelasPagasCE) {
        this.existeParcelasPagasCE = existeParcelasPagasCE;
    }

    /**
     * @return the existeParcelasPagasCE
     */
    public Boolean getExisteParcelasPagasCE() {
        if (existeParcelasPagasCE == null) {
            existeParcelasPagasCE = Boolean.FALSE;
        }
        return existeParcelasPagasCE;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        movParcelaVO = null;
        itensCaixaAberto = new ArrayList();
        listaParcelasPagar = new ArrayList();
        listaResponsavelPagamento = new ArrayList();
        valorTotalParcela = null;
        dataInicio = null;
        dataTermino = null;
        valorConsulta = null;
        consultarHoje = null;
        consultarSemana = null;
        objsContrato = new ArrayList();
        objsVendaAvulsa = new ArrayList();
        objsAulaAvulsaDiaria = new ArrayList();
        objsPersonal = new ArrayList();
    }

    //-------------------------------------------MÉTODOS CENTRAL DE EVENTOS -------------------------------------//
    /**
     * Método responsavel por montar uma lista com parcelas já pagas de contrato de evento
     * @param lista
     * @throws Exception
     * <AUTHOR>
     */
    public void montarParcelasPagas(List lista) throws Exception {
        ContratoVO contrato = (ContratoVO) lista.get(0);
        //percorrer a lista de parcelas
        for (Object obj : contrato.getMovParcelaVOs()) {
            MovParcelaVO parcela = (MovParcelaVO) obj;
            //verificar status igual a PG
            if (parcela.getSituacao().equals("PG")) {
                //adicionar a lista
                getParcelasPagas().add(parcela);
                setExisteParcelasPagasCE(Boolean.TRUE);
            }
        }

    }

    public boolean getApresentarComboSituacao() {
        return apresentarComboSituacao;
    }

    public void setApresentarComboSituacao(boolean apresentarComboSituacao) {
        this.apresentarComboSituacao = apresentarComboSituacao;
    }

    public List getObjsPersonal() {
        return objsPersonal;
    }

    public void setObjsPersonal(List objsPersonal) {
        this.objsPersonal = objsPersonal;
    }

    public boolean isIncluirParcelasRecorrencia() {
        return incluirParcelasRecorrencia;
    }

    public void setIncluirParcelasRecorrencia(boolean incluirParcelasRecorrencia) {
        this.incluirParcelasRecorrencia = incluirParcelasRecorrencia;
    }

    @Override
    public EmpresaVO getEmpresa() {
        return this.empresa;
    }

    @Override
    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public void setCodigoEventoInteresse(Integer eventoDoContrato) {
        this.codigoEventoInteresse = eventoDoContrato;
    }

    public Integer getCodigoEventoInteresse() {
        return codigoEventoInteresse;
    }

    public void setItensCaixaAberto(List<CaixaAbertoTO> itensCaixaAberto) {
        this.itensCaixaAberto = itensCaixaAberto;
    }

    public List<CaixaAbertoTO> getItensCaixaAberto() {
        if (itensCaixaAberto == null) {
            itensCaixaAberto = new ArrayList<CaixaAbertoTO>();
        }
        return itensCaixaAberto;
    }

    public boolean getApresentarPaginacao() {
        return getConfPaginacao().getNumeroTotalItens() > getConfPaginacao().getItensPorPagina();
    }

    public void setOrdenarPorLancamento(Boolean ordenarPorLancamento) {
        this.ordenarPorLancamento = ordenarPorLancamento;
    }

    public Boolean getOrdenarPorLancamento() {
        return ordenarPorLancamento;
    }

    public void setOrdenarPorAlfabetica(Boolean ordenarPorAlfabetica) {
        this.ordenarPorAlfabetica = ordenarPorAlfabetica;
    }

    public List<CaixaAbertoTO> getItensSelecionados() {
            if(itensSelecionados == null){
                    itensSelecionados = new ArrayList<CaixaAbertoTO>();
            }
            return itensSelecionados;
    }

    public List<Integer> parcelasSelecionadas(){
            List<Integer> lista = new ArrayList<Integer>();
            for(CaixaAbertoTO item : getItensSelecionados()){
                    lista.addAll(item.getParcelasSelecionadas().keySet());
            }
            return lista;
    }

    public Boolean getOrdenarPorAlfabetica() {
        return ordenarPorAlfabetica;
    }

    public UsuarioVO getResponsavelCancelamento() {
        return responsavelCancelamento;
    }

    public boolean getApresentarParcelasSelecionadas() {
        return !itensSelecionados.isEmpty();
    }

    public void setResponsavelCancelamento(UsuarioVO responsavelCancelamento) {
        this.responsavelCancelamento = responsavelCancelamento;
    }

    public Boolean getAbrirRichConfirmacaoCancelamento() {
        return abrirRichConfirmacaoCancelamento;
    }

    public void setAbrirRichConfirmacaoCancelamento(Boolean abrirRichConfirmacaoCancelamento) {
        this.abrirRichConfirmacaoCancelamento = abrirRichConfirmacaoCancelamento;
    }

    private void cancelarProdutosVinculados() throws Exception {
        getFacade().getMovParcela().cancelarProdutosVinculados(getListaParcelasPagar());
    }

    public String getConfirmacaoCancelamento() {
        return confirmacaoCancelamento;
    }

    public void setConfirmacaoCancelamento(String confirmacaoCancelamento) {
        this.confirmacaoCancelamento = confirmacaoCancelamento;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getMovParcela().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public CaixaAbertoTO getItemParaRenegociar() {
        return itemParaRenegociar;
    }

    public void setItemParaRenegociar(CaixaAbertoTO itemParaRenegociar) {
        this.itemParaRenegociar = itemParaRenegociar;
    }

    public List<MovParcelaVO> getParcelasParaRenegociar() {
        return parcelasParaRenegociar;
    }

    public void setParcelasParaRenegociar(List<MovParcelaVO> parcelasParaRenegociar) {
        this.parcelasParaRenegociar = parcelasParaRenegociar;
    }

    public void setApresentarRenegociarParcelas(boolean apresentarRenegociarParcelas) {
        this.apresentarRenegociarParcelas = apresentarRenegociarParcelas;
    }

    public boolean isApresentarRenegociarParcelas() {
        return apresentarRenegociarParcelas;
    }

    public boolean validarApresentarRenegociarParcelas() {
        try {
            if (getUsuarioLogado().getAdministrador()) {
                return true;
            }

            for (UsuarioPerfilAcessoVO usuarioPerfilAcesso : (ArrayList<UsuarioPerfilAcessoVO>) getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(
                            usuarioPerfilAcesso.getPerfilAcesso(), getUsuarioLogado(),
                            "RenegociacaoParcelas", "4.29 - Renegociação de Parcelas");
                }
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public String getJustificativaCancelamento() {
        return justificativaCancelamento;
    }

    public void setJustificativaCancelamento(String justificativaCancelamento) {
        this.justificativaCancelamento = justificativaCancelamento;
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getProduto() {
        if (produto == null) {
            produto = "";
        }
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public Double getValorMultaJuros() {
        if (valorMultaJuros == null){
            valorMultaJuros = 0.0;
        }
        return valorMultaJuros;
    }

    public void setValorMultaJuros(Double valorMultaJuros) {
        this.valorMultaJuros = valorMultaJuros;
    }

    public boolean isExisteAutorizacaoBoleto() {
        return existeAutorizacaoBoleto;
    }

    public void setExisteAutorizacaoBoleto(boolean existeAutorizacaoBoleto) {
        this.existeAutorizacaoBoleto = existeAutorizacaoBoleto;
    }

    public Boolean getNaoApresentarVencimentosDeMesesFuturos() {
        if (naoApresentarVencimentosDeMesesFuturos == null) {
            naoApresentarVencimentosDeMesesFuturos = false;
        }
        return naoApresentarVencimentosDeMesesFuturos;
    }

    public void setNaoApresentarVencimentosDeMesesFuturos(Boolean naoApresentarVencimentosDeMesesFuturos) {
        this.naoApresentarVencimentosDeMesesFuturos = naoApresentarVencimentosDeMesesFuturos;
    }

    public void setSelecionarTodasParcelas(Boolean selecionarTodasParcelas) {
        this.selecionarTodasParcelas = selecionarTodasParcelas;
    }

    public Boolean getSelecionarTodasParcelas() {
        return selecionarTodasParcelas;
    }

    public void validarParcelaRemessa(final MovParcelaVO mp) throws Exception {
        List<RemessaItemVO> itensRemessa = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoParcela(mp.getCodigo(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!itensRemessa.isEmpty()) {
            for (RemessaItemVO item : itensRemessa) {
                if (item.getRemessa().isAguardandoRetorno() && !item.getProps().containsKey(DCCAttEnum.StatusVenda.name())
                        && !item.getRemessa().getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)
                        && !item.getRemessa().getTipo().equals(TipoRemessaEnum.BOLETO)) {
                    throw new ConsistirException(String.format(
                            "Você está tentando renegociar a parcela \"%s\" do Cliente \"%s\". Porém, "
                                    + "ela se encontra em uma Remessa de Cobrança (\"%s\"), "
                                    + "que já foi enviada ao Banco. "
                                    + "É necessário você esperar o processamento do "
                                    + "arquivo de Retorno Bancário. Isso é uma medida de "
                                    + "segurança que evita uma inconsistência do tipo "
                                    + "\"Duplicidade de Pagamentos\".", mp.getCodigo(),
                            mp.getPessoa().getNome(),
                            item.getRemessa().getIdentificador()));
                }
            }
        }
    }

    public void setExisteParcelaSelecionada(boolean existeParcelaSelecionada) {
        this.existeParcelaSelecionada = existeParcelaSelecionada;
    }

    public boolean isExisteParcelaSelecionada() {
        return existeParcelaSelecionada;
    }

    public void desmarcarNaoApresentarVencimentosFuturos() {
        setNaoApresentarVencimentosDeMesesFuturos(false);
    }

    public boolean isApresentarEmpresa() {
        return apresentarEmpresa;
    }

    public void setApresentarEmpresa(boolean apresentarEmpresa) {
        this.apresentarEmpresa = apresentarEmpresa;
    }

    public Integer getFiltroCodigoEmpresa() {
        return filtroCodigoEmpresa;
    }

    public void setFiltroCodigoEmpresa(Integer filtroCodigoEmpresa) {
        this.filtroCodigoEmpresa = filtroCodigoEmpresa;
    }

    public Boolean getPermiteAcessarColaborador() {
        if (permiteAcessarColaborador == null) {
            try {
                validarPermissao("Colaborador", "2.07 - Colaborador", getUsuarioLogado());
                permiteAcessarColaborador = true;
            } catch (Exception ex) {
                permiteAcessarColaborador = false;
            }
        }
        return permiteAcessarColaborador;
    }

    public void setPermiteAcessarColaborador(Boolean permiteAcessarColaborador) {
        this.permiteAcessarColaborador = permiteAcessarColaborador;
    }


    public boolean isExisteConvenioCobrancaBoleto() {
        return existeConvenioCobrancaBoleto;
    }

    public void setExisteConvenioCobrancaBoleto(boolean existeConvenioCobrancaBoleto) {
        this.existeConvenioCobrancaBoleto = existeConvenioCobrancaBoleto;
    }

    public List<BoletoVO> getListaBoletosPendentes() {
        if (listaBoletosPendentes == null) {
            listaBoletosPendentes = new ArrayList<>();
        }
        return listaBoletosPendentes;
    }

    public void setListaBoletosPendentes(List<BoletoVO> listaBoletosPendentes) {
        this.listaBoletosPendentes = listaBoletosPendentes;
    }

    public String getOnCompleteBoletoPendente() {
        if (onCompleteBoletoPendente == null) {
            onCompleteBoletoPendente = "";
        }
        return onCompleteBoletoPendente;
    }

    public void setOnCompleteBoletoPendente(String onCompleteBoletoPendente) {
        this.onCompleteBoletoPendente = onCompleteBoletoPendente;
    }

    public String getOperacaoOrigemBoletoPendente() {
        if (operacaoOrigemBoletoPendente == null) {
            operacaoOrigemBoletoPendente = "";
        }
        return operacaoOrigemBoletoPendente;
    }

    public void setOperacaoOrigemBoletoPendente(String operacaoOrigemBoletoPendente) {
        this.operacaoOrigemBoletoPendente = operacaoOrigemBoletoPendente;
    }

    public String confirmarCancelarBoletoPendente() {
        try {
            limparMsg();
            this.setMsgAlert("");
            this.setOnCompleteBoletoPendente("");

            for (BoletoVO boletoVO : this.getListaBoletosPendentes()) {
                if (!boletoVO.isPodeCancelar()) {
                    throw new Exception("Boleto com vencimento " + boletoVO.getDataVencimentoApresentar() + " não pode ser cancelado. Por favor, verifique a situação do boleto.");
                }
                try {
                    getFacade().getBoleto().cancelarBoleto(boletoVO, getUsuarioLogado(),
                            "Caixa Em Aberto - " + this.getOperacaoOrigemBoletoPendente().toUpperCase());
                } catch (Exception ex) {
                    getFacade().getBoleto().alterarSituacaoComLog(boletoVO, SituacaoBoletoEnum.CANCELAMENTO_PENDENTE, getUsuarioLogado(),
                            "Caixa Em Aberto - " + this.getOperacaoOrigemBoletoPendente().toUpperCase(), ex.getMessage());
                }
            }

            montarSucessoGrowl("Boletos cancelados. Por favor, continue a operação.");
            this.setOnCompleteBoletoPendente("Notifier.cleanAll();Richfaces.hideModalPanel('modalCancelarBoletosPendentes');" + getMensagemNotificar());
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            this.setOnCompleteBoletoPendente(getMensagemNotificar());
            return "";
        }
    }


    public boolean isApresentarImprimirComprovanteCompra() {
        return apresentarImprimirComprovanteCompra;
    }

    public void setApresentarImprimirComprovanteCompra(boolean apresentarImprimirComprovanteCompra) {
        this.apresentarImprimirComprovanteCompra = apresentarImprimirComprovanteCompra;
    }

    private void processarApresentarImprimirComprovanteCompra() {
        try {
            this.apresentarImprimirComprovanteCompra = false;
            this.apresentarImprimirComprovanteCompra = getFacade().getPlanoTextoPadrao().existePorTipoSituacao("CC", "AT", null);
        } catch (Exception ex) {
            ex.printStackTrace();
            this.apresentarImprimirComprovanteCompra = false;
        }
    }

    public void imprimirComprovanteCompra() {
        try {
            limparMsg();
            this.setMsgAlert("");
            CaixaAbertoTO obj = (CaixaAbertoTO) context().getExternalContext().getRequestMap().get("item");
            String texto = getFacade().getPlanoTextoPadrao().gerarComprovanteCompra(obj.getParcelas());
            this.setMsgAlert("abrirPopup('VisualizarContrato', 'ComprovanteCompra', 730, 545);");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public boolean isNovaTelaCaixaAbertoPadrao() {
        return novaTelaCaixaAbertoPadrao;
    }

    public void setNovaTelaCaixaAbertoPadrao(boolean novaTelaCaixaAbertoPadrao) {
        this.novaTelaCaixaAbertoPadrao = novaTelaCaixaAbertoPadrao;
    }

    public boolean isNovaTelaCaixaAbertoPadraoEmpresa() {
        return novaTelaCaixaAbertoPadraoEmpresa;
    }

    public void setNovaTelaCaixaAbertoPadraoEmpresa(boolean novaTelaCaixaAbertoPadraoEmpresa) {
        this.novaTelaCaixaAbertoPadraoEmpresa = novaTelaCaixaAbertoPadraoEmpresa;
    }

    public boolean isUsaPinpad() {
        return usaPinpad;
    }

    public void setUsaPinpad(boolean usaPinpad) {
        this.usaPinpad = usaPinpad;
    }

    private void montarUsaPinpad() {
        try {
            this.usaPinpad = getFacade().getEmpresa().empresaUsaPinpad(this.getEmpresaLogado().getCodigo());
        } catch (Exception ex) {
            ex.printStackTrace();
            this.usaPinpad = true;
        }
    }
}
