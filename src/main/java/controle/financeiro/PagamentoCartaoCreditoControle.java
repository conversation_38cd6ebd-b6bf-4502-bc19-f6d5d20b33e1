package controle.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.codec.binary.Base64;
import servicos.impl.apf.APF;
import servicos.impl.apf.AprovaFacilService;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;
import servicos.interfaces.AprovacaoServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PagamentoCartaoCreditoControle extends SuperControle {

    private ConfiguracaoSistemaVO configuracaoSistema;
    private List<MovParcelaVO> parcelasPagar;
    private Double valorPagar = 0.0;
    private CartaoCreditoTO dadosPagamento;
    private UsuarioVO responsavelPagamento;
    private MovPagamentoVO movPagamentoSelecionado;
    private boolean trocarCartao;
    private Integer convenioCobrancaSelecionado;
    private Integer operadoraCartao;
    private Map<Integer, List<OperadoraCartaoVO>> mapaConvenioBandeiras;
    private Map<Integer, ConvenioCobrancaVO> mapaConvenioCobranca;
    private boolean urlTeste = false;
    private List<SelectItem> bandeirasCartaoSelectItems;
    private PessoaVO pessoaPagamento;
    private String onComplete = "";
    private List<AutorizacaoCobrancaClienteVO> autorizacoes = new ArrayList<AutorizacaoCobrancaClienteVO>();
    private List<AutorizacaoCobrancaColaboradorVO> autorizacoesColaborador = new ArrayList<AutorizacaoCobrancaColaboradorVO>();
    private Boolean usarOutroCartao = false;
    private List<AutorizacaoCobrancaClienteVO> autorizacoesTemp = new ArrayList<>();
    private String tipoParcelamentoStone;
    private String styleClassCartao = "white";
    private String styleClassCartaoRodape = "white";
    private String styleClassCartaoTitles = "titleGrey";
    private MovPagamentoControle movPagamentoControle;
    private ColaboradorVO colaborador;
    private boolean labelColaborador = false;

    public PagamentoCartaoCreditoControle() throws Exception {
        inicializarBandeiras();
    }

    public PagamentoCartaoCreditoControle(MovPagamentoControle movPagamentoControle) throws Exception {
        inicializarBandeiras();
        this.movPagamentoControle = movPagamentoControle;
    }

    private void carregarMapaConvenios() throws Exception {
        getMapaConvenioCobranca().clear();

        List<ConvenioCobrancaVO> listaConvenio = getFacade().getConvenioCobranca().consultarPorTipoCobranca(TipoCobrancaEnum.ONLINE, getEmpresaLogado().getCodigo(), SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        MovPagamentoControle control;
        if (this.movPagamentoControle == null) {
            control = (MovPagamentoControle) JSFUtilities.getFromSession(MovPagamentoControle.class);
        } else {
            control = this.movPagamentoControle;
        }
        if (control != null) {
            List<MovPagamentoVO> lista = control.getListaSelectItemMovPagamento();
            for (MovPagamentoVO mpVO : lista) {
                if (mpVO.getMovPagamentoEscolhida() && mpVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                    listaConvenio.addAll(getFacade().getConvenioCobranca().consultarPorTipoCobranca(TipoCobrancaEnum.ONLINE, mpVO.getEmpresa().getCodigo(), SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
            }
        }

        for (ConvenioCobrancaVO obj : listaConvenio) {
            getMapaConvenioCobranca().put(obj.getCodigo(), obj);
        }
    }

    public void obterAutorizacoesCliente() throws Exception {

        if (isLabelColaborador()) {
            ColaboradorVO colaborador = getFacade().getColaborador().consultarPorCodigoPessoaObjetoSimples(pessoaPagamento.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            //colaborador...
            if (colaborador.getCodigo() != 0) {
                autorizacoesColaborador = new ArrayList<>();
                if (pessoaPagamento != null && !UteisValidacao.emptyNumber(pessoaPagamento.getCodigo())) {
                    List<AutorizacaoCobrancaColaboradorVO> listaAutoColaborador = getFacade().getAutorizacaoCobrancaColaborador().consultarPorPessoaTipoAutorizacao(pessoaPagamento.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (!UteisValidacao.emptyList(listaAutoColaborador)) {
                        AragornService aragornService = new AragornService();
                        for (AutorizacaoCobrancaColaboradorVO obj : listaAutoColaborador) {
                            try {
                                if (!UteisValidacao.emptyString(obj.getTokenAragorn())) {
                                    aragornService.povoarAutorizacaoCobrancaVO(obj, true, true);
                                } else if (obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) &&
                                        !UteisValidacao.emptyNumber(pessoaPagamento.getIdVindi())) {
                                    //vai Utilizar o idVindi Para realizar o cobrança
                                } else if (obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) &&
                                        !UteisValidacao.emptyString(obj.getTokenCielo())) {
                                    //vai Utilizar o tokenCielo para realizar o cobrança
                                } else if ((obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                                        obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) &&
                                        !UteisValidacao.emptyString(obj.getTokenPagoLivre())) {
                                    //vai Utilizar o TokenPagoLivre para realizar o cobrança
                                } else {
                                    //continuar para não adicionar na lista pois não é válida
                                    continue;
                                }
                                autorizacoesColaborador.add(obj);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                        aragornService = null;
                        if (!UteisValidacao.emptyList(autorizacoesColaborador)) {
                            doSelectCartaoColaborador(autorizacoesColaborador.get(0), true);
                        }
                    }
                }
            }
        } else {
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(pessoaPagamento.getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            if (clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                //cliente...
                autorizacoes = new ArrayList<>();
                if (pessoaPagamento != null && !UteisValidacao.emptyNumber(pessoaPagamento.getCodigo())) {
                    List<AutorizacaoCobrancaClienteVO> listaAutoCliente = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoaTipoAutorizacao(pessoaPagamento.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (!UteisValidacao.emptyList(listaAutoCliente)) {
                        AragornService aragornService = new AragornService();
                        for (AutorizacaoCobrancaClienteVO obj : listaAutoCliente) {
                            try {
                                if (!UteisValidacao.emptyString(obj.getTokenAragorn())) {
                                    aragornService.povoarAutorizacaoCobrancaVO(obj, true, true);
                                } else if (obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI) &&
                                        !UteisValidacao.emptyNumber(pessoaPagamento.getIdVindi())) {
                                    //vai Utilizar o idVindi Para realizar o cobrança
                                } else if (obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) &&
                                        !UteisValidacao.emptyString(obj.getTokenCielo())) {
                                    //vai Utilizar o tokenCielo para realizar o cobrança
                                } else if ((obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                                        obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) &&
                                        !UteisValidacao.emptyString(obj.getTokenPagoLivre())) {
                                    //vai Utilizar o TokenPagoLivre para realizar o cobrança
                                } else {
                                    //continuar para não adicionar na lista pois não é válida
                                    continue;
                                }
                                autorizacoes.add(obj);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                        aragornService = null;
                        if (!UteisValidacao.emptyList(autorizacoes)) {
                            doSelectCartao(autorizacoes.get(0), true);
                        }
                    }
                }
            }
        }
    }

    public void selecionarAutorizacaoCobrancaCliente() {
        AutorizacaoCobrancaClienteVO autorizacaoCliente = (AutorizacaoCobrancaClienteVO) context().getExternalContext().getRequestMap().get("auto");
        doSelectCartao(autorizacaoCliente, true);
    }

    public void informarNovoCartao() {
        doSelectCartao(null, true);
    }

    private void doSelectCartao(AutorizacaoCobrancaClienteVO autorizacaoCliente, boolean limparCvv) {
        //limpar usar IdVindi
        dadosPagamento.setUsarIdVindiCobranca(false);
        dadosPagamento.setUsarTokenCieloCobranca(false);
        dadosPagamento.setTokenCielo("");


        //limpar os selecionados
        for (AutorizacaoCobrancaClienteVO obj : getAutorizacoes()) {
            obj.setSelecionado(false);
        }

        if (autorizacaoCliente == null) {
            return;
        }

        limparDadosDoCartao(limparCvv);

        boolean usarIdVindiCobranca = (UteisValidacao.emptyString(autorizacaoCliente.getTokenAragorn()) && autorizacaoCliente.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI));

        boolean usarTokenCieloCobranca = (UteisValidacao.emptyString(autorizacaoCliente.getTokenAragorn()) && autorizacaoCliente.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)
        && !UteisValidacao.emptyString(autorizacaoCliente.getTokenCielo()));

        boolean usarTokenPagoLivre = (UteisValidacao.emptyString(autorizacaoCliente.getTokenAragorn()) &&
                (autorizacaoCliente.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || autorizacaoCliente.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY))
                && !UteisValidacao.emptyString(autorizacaoCliente.getTokenPagoLivre()));

        boolean transacaoOnline = autorizacaoCliente.getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE);

        if (transacaoOnline) {
            this.setConvenioCobrancaSelecionado(autorizacaoCliente.getConvenio().getCodigo());
            dadosPagamento.setTipoTransacaoEnum(autorizacaoCliente.getConvenio().getTipo().getTipoTransacao());
        }
        acaoMudarConvenioCobranca();

        if (usarIdVindiCobranca) {
            dadosPagamento.setParcelas(0);
            dadosPagamento.setBand(OperadorasExternasAprovaFacilEnum.MASTERCARD); //fake
            dadosPagamento.setUsarIdVindiCobranca(true);
            autorizacaoCliente.setSelecionado(true);
        }

        if (usarTokenCieloCobranca) {
            dadosPagamento.setParcelas(0);
            dadosPagamento.setUsarTokenCieloCobranca(true);
            autorizacaoCliente.setSelecionado(true);
        }

        if (!UteisValidacao.emptyString(autorizacaoCliente.getNumeroCartao())) {
            dadosPagamento.setNumero(autorizacaoCliente.getCartaoMascarado());
            dadosPagamento.setBand(autorizacaoCliente.getOperadoraCartao());
            dadosPagamento.setValidade(autorizacaoCliente.getValidadeCartao());
            dadosPagamento.setNomeTitular(autorizacaoCliente.getNomeTitularCartao());
            dadosPagamento.setCpfCnpjPortador(autorizacaoCliente.getCpfTitular());

            //codigo da operadora do cartão
            OperadoraCartaoVO operadoraCartaoVO = getOperadoraCartaoVO(autorizacaoCliente.getOperadoraCartao());
            if (operadoraCartaoVO != null) {
                //codigo da operadora do cartão
                dadosPagamento.setBandeira(operadoraCartaoVO.getCodigo());

                //codigo da operadora do cartão
                operadoraCartao = dadosPagamento.getBandeira();
            }

            if (limparCvv) {
                dadosPagamento.setCodigoSeguranca("");
            }
            dadosPagamento.setTokenAragorn(autorizacaoCliente.getTokenAragorn());
            dadosPagamento.setTokenVindi(autorizacaoCliente.getCodigoExterno());
            dadosPagamento.setTokenCielo(autorizacaoCliente.getTokenCielo());
            dadosPagamento.setTokenPagoLivre(autorizacaoCliente.getTokenPagoLivre());
            dadosPagamento.setIdCardMundiPagg(autorizacaoCliente.getIdCardMundiPagg());
            dadosPagamento.setIdCardPagarMe(autorizacaoCliente.getIdCardPagarMe());

            autorizacaoCliente.setSelecionado(true);

            try {
                selecionaOperadora();
                MovPagamentoControle control;
                if (this.movPagamentoControle == null) {
                    control = (MovPagamentoControle) JSFUtilities.getFromSession(MovPagamentoControle.class);
                } else {
                    control = this.movPagamentoControle;
                }
                List<MovPagamentoVO> lista = control.getListaSelectItemMovPagamento();
                for (MovPagamentoVO mpVO : lista) {
                    if (mpVO.getMovPagamentoEscolhida() && mpVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        mpVO.setNrParcelaCartaoCredito(dadosPagamento.getParcelas());
                        break;
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void doSelectCartaoColaborador(AutorizacaoCobrancaColaboradorVO autorizacaoColaborador, boolean limparCvv) {
        //limpar usar IdVindi
        dadosPagamento.setUsarIdVindiCobranca(false);
        dadosPagamento.setUsarTokenCieloCobranca(false);
        dadosPagamento.setTokenCielo("");


        //limpar os selecionados
        for (AutorizacaoCobrancaColaboradorVO obj : getAutorizacoesColaborador()) {
            obj.setSelecionado(false);
        }

        if (autorizacaoColaborador == null) {
            return;
        }

        limparDadosDoCartao(limparCvv);

        boolean usarIdVindiCobranca = (UteisValidacao.emptyString(autorizacaoColaborador.getTokenAragorn()) && autorizacaoColaborador.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI));

        boolean usarTokenCieloCobranca = (UteisValidacao.emptyString(autorizacaoColaborador.getTokenAragorn()) && autorizacaoColaborador.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)
                && !UteisValidacao.emptyString(autorizacaoColaborador.getTokenCielo()));

        boolean usarTokenPagoLivre = (UteisValidacao.emptyString(autorizacaoColaborador.getTokenAragorn()) &&
                (autorizacaoColaborador.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) || autorizacaoColaborador.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY))
                && !UteisValidacao.emptyString(autorizacaoColaborador.getTokenPagoLivre()));

        boolean transacaoOnline = autorizacaoColaborador.getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE);

        if (transacaoOnline) {
            this.setConvenioCobrancaSelecionado(autorizacaoColaborador.getConvenio().getCodigo());
            dadosPagamento.setTipoTransacaoEnum(autorizacaoColaborador.getConvenio().getTipo().getTipoTransacao());
        }
        acaoMudarConvenioCobranca();

        if (usarIdVindiCobranca) {
            dadosPagamento.setParcelas(0);
            dadosPagamento.setBand(OperadorasExternasAprovaFacilEnum.MASTERCARD); //fake
            dadosPagamento.setUsarIdVindiCobranca(true);
            autorizacaoColaborador.setSelecionado(true);
        }

        if (usarTokenCieloCobranca) {
            dadosPagamento.setParcelas(0);
            dadosPagamento.setUsarTokenCieloCobranca(true);
            autorizacaoColaborador.setSelecionado(true);
        }

        if (!UteisValidacao.emptyString(autorizacaoColaborador.getNumeroCartao())) {
            dadosPagamento.setNumero(autorizacaoColaborador.getCartaoMascarado());

            dadosPagamento.setBand(autorizacaoColaborador.getOperadoraCartao());
            dadosPagamento.setValidade(autorizacaoColaborador.getValidadeCartao());
            dadosPagamento.setNomeTitular(autorizacaoColaborador.getNomeTitularCartao());
            dadosPagamento.setCpfCnpjPortador(autorizacaoColaborador.getCpfTitular());

            //codigo da operadora do cartão
            OperadoraCartaoVO operadoraCartaoVO = getOperadoraCartaoVO(autorizacaoColaborador.getOperadoraCartao());
            if (operadoraCartaoVO != null) {
                //codigo da operadora do cartão
                dadosPagamento.setBandeira(operadoraCartaoVO.getCodigo());

                //codigo da operadora do cartão
                operadoraCartao = dadosPagamento.getBandeira();
            }

            if (limparCvv) {
                dadosPagamento.setCodigoSeguranca("");
            }
            dadosPagamento.setTokenAragorn(autorizacaoColaborador.getTokenAragorn());
            dadosPagamento.setTokenVindi(autorizacaoColaborador.getCodigoExterno());
            dadosPagamento.setTokenCielo(autorizacaoColaborador.getTokenCielo());
            dadosPagamento.setTokenPagoLivre(autorizacaoColaborador.getTokenPagoLivre());
            dadosPagamento.setIdCardMundiPagg(autorizacaoColaborador.getIdCardMundiPagg());
            dadosPagamento.setIdCardPagarMe(autorizacaoColaborador.getIdCardPagarMe());

            autorizacaoColaborador.setSelecionado(true);

            try {
                selecionaOperadora();
                MovPagamentoControle control;
                if (this.movPagamentoControle == null) {
                    control = (MovPagamentoControle) JSFUtilities.getFromSession(MovPagamentoControle.class);
                } else {
                    control = this.movPagamentoControle;
                }
                List<MovPagamentoVO> lista = control.getListaSelectItemMovPagamento();
                for (MovPagamentoVO mpVO : lista) {
                    if (mpVO.getMovPagamentoEscolhida() && mpVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                        mpVO.setNrParcelaCartaoCredito(dadosPagamento.getParcelas());
                        break;
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private OperadoraCartaoVO getOperadoraCartaoVO(OperadorasExternasAprovaFacilEnum anEnum) {
        if (null != anEnum) {
            List<OperadoraCartaoVO> operadoras = this.getMapaConvenioBandeiras().get(this.getConvenioCobrancaSelecionado());
            if (null != operadoras) {
                for (OperadoraCartaoVO o : operadoras) {
                    if (o.getOperadoraIntegracao().equals(anEnum)) {
                        return o;
                    }
                }
            }
        }
        return null;
    }

    /**
     * Responsável por inicializar as Configuracoes do Sistema em VO
     *
     * <AUTHOR> 04/07/2011
     */
    public void inicializarConfiguracaoSistema() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS));
    }

    private void inicializarBandeiras() throws Exception {
        carregarMapaConvenios();
        inicializarConfiguracaoSistema();
        getMapaConvenioBandeiras().clear();

        for (Integer convenio : getMapaConvenioCobranca().keySet()) {
            ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(convenio);
            if (convenioCobrancaVO == null) {
                continue;
            }

            List<OperadoraCartaoVO> listaOpe = getFacade().getOperadoraCartao().consultarOperadorasTipoConvenio(convenioCobrancaVO.getTipo(), false);
            for (OperadoraCartaoVO operadoraCartaoVO : listaOpe) {
                TipoTransacaoEnum tipoTransacaoEnum = operadoraCartaoVO.getTipoTransacaoEnum(false);
                if (tipoTransacaoEnum != null) {
                    List<OperadoraCartaoVO> listaBand = getMapaConvenioBandeiras().get(convenioCobrancaVO.getCodigo());
                    if (listaBand == null) {
                        listaBand = new ArrayList<>();
                    }
                    listaBand.add(operadoraCartaoVO);
                    getMapaConvenioBandeiras().put(convenioCobrancaVO.getCodigo(), listaBand);
                }
            }
        }

        if (getMapaConvenioBandeiras().size() == 1) {
            ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(getMapaConvenioBandeiras().keySet().iterator().next());
            if (convenioCobrancaVO != null) {
                this.setConvenioCobrancaSelecionado(convenioCobrancaVO.getCodigo());
            }
        } else {
            this.setConvenioCobrancaSelecionado(0);
        }
    }

    public List<SelectItem> getOperadorasCartaoCredito() {
        if (UteisValidacao.emptyNumber(this.getConvenioCobrancaSelecionado())) {
            return new ArrayList<>();
        }
        List<OperadoraCartaoVO> operadoras = this.getMapaConvenioBandeiras().get(this.getConvenioCobrancaSelecionado());
        List<SelectItem> itens = new ArrayList<SelectItem>();
        if (operadoras != null) {
            for (OperadoraCartaoVO op : operadoras) {
                itens.add(new SelectItem(op.getCodigo(), op.getOperadoraIntegracao().getDescricao()));
            }
        }
        Ordenacao.ordenarLista(itens, "label");
        itens.add(0, new SelectItem(0, "(Nenhum)"));
        return itens;
    }

    public void acaoMudarConvenioCobranca() {
        this.operadoraCartao = null;
        this.getDadosPagamento().setBand(null);
        this.getDadosPagamento().setBandeira(0);
        this.getDadosPagamento().setAdquirenteMaxiPago(AdquirenteMaxiPagoEnum.NENHUM);
    }

    public List<SelectItem> getConvenioCobrancaSelectItem() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        for (Integer conv : getMapaConvenioBandeiras().keySet()) {
            ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(conv);
            if (convenioCobrancaVO != null) {
                itens.add(new SelectItem(convenioCobrancaVO.getCodigo(), convenioCobrancaVO.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(itens, "label");
        if (itens.size() > 1){
            itens.add(0,new SelectItem(0,"(Selecionar Convenio)"));
        }
        return itens;
    }

    /**
     * Listener para receber os dados do pagamento
     *
     * @param event
     */
    public void listaParcelas(final ActionEvent event) {
        //limpar os atributos do pagamento antes de receber lista de parcelas
        limparAtributos();
        try {
            this.prepararControlador((List<MovParcelaVO>) event.getComponent().getAttributes().get("listaParcelas"));
        } catch (Exception ex) {
            Logger.getLogger(PagamentoCartaoCreditoControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex.getMessage());
        }
    }

    public void preencherParcelas(List<MovParcelaVO> listaParcelas) {
        //limpar os atributos do pagamento antes de receber lista de parcelas
        limparAtributos();
        try {
            this.prepararControlador(listaParcelas);
        } catch (Exception ex) {
            Logger.getLogger(PagamentoCartaoCreditoControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex.getMessage());
        }
    }

    /**
     * Responsável por chamar a tela de pagamento recorrente em cartão de
     * crédito
     *
     * <AUTHOR> 04/07/2011
     */
    public String abrir() throws Exception {
        String retorno = "";
        try {
            inicializarConfiguracaoSistema();
            //verificar se as configurações com o gateway de pagamento estão configuradas
            validarConfiguracoes();
            //inicializar os atributos do pagamento
            validarAtributos();

            retorno = "pagamentoCartao";
            setSucesso(true);
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemID(e.getMessage());
            montarMsgAlert(getMensagem());
            setSucesso(false);
        }
        return retorno;
    }

    public void validar() throws Exception {
        inicializarConfiguracaoSistema();
        //verificar se as configurações com o gateway de pagamento estão configuradas
        validarConfiguracoes();
        //inicializar os atributos do pagamento
        validarAtributos();
    }

    public void novo() {
        try {
            inicializarConfiguracaoSistema();
            //verificar se as configurações com o gateway de pagamento estão configuradas
            validarConfiguracoes();
            //inicializar os atributos do pagamento
            validarAtributos();

            setSucesso(true);
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemID(e.getMessage());
            montarMsgAlert(getMensagem());
            setSucesso(false);
        }
    }

    private AutorizacaoCobrancaClienteVO obterAutorizacaoCobrancaClienteSelecinada() {
        for (AutorizacaoCobrancaClienteVO obj : getAutorizacoes()) {
            if (obj.isSelecionado()) {
                return obj;
            }
        }
        return null;
    }

    private AutorizacaoCobrancaColaboradorVO obterAutorizacaoCobrancaColaboradorSelecinado() {
        for (AutorizacaoCobrancaColaboradorVO obj : getAutorizacoesColaborador()) {
            if (obj.isSelecionado()) {
                return obj;
            }
        }
        return null;
    }

    /**
     * Responsável por consultar o servico de pagamento e registrar a operação
     *
     * <AUTHOR> 08/07/2011
     */
    public void pagar() {
        MovPagamentoControle movPagamentoControle = (MovPagamentoControle) JSFUtilities.getFromSession(MovPagamentoControle.class);
        AutorizacaoCobrancaClienteVO acc = obterAutorizacaoCobrancaClienteSelecinada();
        AutorizacaoCobrancaColaboradorVO accCol = obterAutorizacaoCobrancaColaboradorSelecinado();
        try {
            movPagamentoControle.limparMsg();
            limparMsg();
            setOnComplete("");

            setProcessandoOperacao(true);
            if (verificarLoginUsuario()) {
                getMovPagamentoSelecionado().setResponsavelPagamento(this.getResponsavelPagamento());

                if (null != dadosPagamento.getNumero() && dadosPagamento.getNumero().contains("****") &&
                        (acc != null || accCol != null)) {
                    if (acc != null) {
                        dadosPagamento.setNumero(acc.getNazgDTO().getCard());
                        dadosPagamento.setTokenAragorn(acc.getTokenAragorn());
                        dadosPagamento.setTokenVindi(acc.getCodigoExterno());
                        dadosPagamento.setTokenCielo(acc.getTokenCielo());
                        dadosPagamento.setTokenPagoLivre(acc.getTokenPagoLivre());
                        dadosPagamento.setIdCardMundiPagg(acc.getIdCardMundiPagg());
                        dadosPagamento.setIdCardPagarMe(acc.getIdCardPagarMe());
                    } else if (accCol != null) {
                        dadosPagamento.setNumero(accCol.getNazgDTO().getCard());
                        dadosPagamento.setTokenAragorn(accCol.getTokenAragorn());
                        dadosPagamento.setTokenVindi(accCol.getCodigoExterno());
                        dadosPagamento.setTokenCielo(accCol.getTokenCielo());
                        dadosPagamento.setTokenPagoLivre(accCol.getTokenPagoLivre());
                        dadosPagamento.setIdCardMundiPagg(accCol.getIdCardMundiPagg());
                        dadosPagamento.setIdCardPagarMe(accCol.getIdCardPagarMe());
                    }
                } else {

                    //limpar para garantir que não será utilizado na transação
                    dadosPagamento.setTokenVindi("");
                    dadosPagamento.setTokenCielo("");
                    dadosPagamento.setTokenAragorn("");
                    dadosPagamento.setTokenPagoLivre("");
                    dadosPagamento.setIdCardMundiPagg("");
                    dadosPagamento.setIdCardPagarMe("");

                }
                dadosPagamento.setUsuarioResponsavel(this.getResponsavelPagamento());
                dadosPagamento.setIdPessoaCartao(this.pessoaPagamento.getCodigo());
                this.getDadosPagamento().setValorDocumento(Formatador.formatarValorMonetario(getMovPagamentoSelecionado().getValorTotal()));
                this.getDadosPagamento().setListaParcelas(this.getParcelasPagar());
                this.getDadosPagamento().setIp("RecorrENtE");
                this.getDadosPagamento().setIpClientePacto(this.getIpCliente());
                EmpresaVO emp = getFacade().getEmpresa().consultarPorCodigo(this.getDadosPagamento().getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                this.getDadosPagamento().setUrl(ConfiguracaoSistemaVO.obterURLRecorrencia(emp, this.getConfiguracaoSistema()));
                this.getDadosPagamento().setValor(getMovPagamentoSelecionado().getValorTotal());
                boolean adicionarParcelasJuros = false; // isso para não gerar duplicação de parcelas no pagamento, caso uma primeira tentativa tenha dado errado
                if (movPagamentoControle.isPermitirCalcularJuros()) {
                    movPagamentoControle.verificarEGerarParcelasDeJuros(dadosPagamento.getListaParcelas());
                    adicionarParcelasJuros = true;
                }
                List<MovParcelaVO> parcelas = dadosPagamento.getListaParcelas();
                if (adicionarParcelasJuros) {
                    parcelas.addAll(movPagamentoControle.getParcelasMultasJuros());
                }
                Double valorParcelas = 0.0;
                for (MovParcelaVO movParcelaVO : parcelas) {
                    if (!getFacade().getMovParcela().validarSituacaoParcela(movParcelaVO.getCodigo(), "EA")) { //evitar duplicidade de pagamentos por operações em paralelo
                        throw new ConsistirException(String.format("Parcela %s - %s não está mais em aberto. Por favor, verifique localizando a parcela e tentando receber novamente.",
                                movParcelaVO.getCodigo(), movParcelaVO.getDescricao()));
                    } else {
                        //colocar como em aberto para evitar problemas ao tentar receber após dar erro na primeira tentativa.
                        //caso a parcela não estivesse em aberto teria dado erro no IF acima.. onde valida a situação.!
                        //by Luiz Felipe 14/04/2020
                        movParcelaVO.setSituacao("EA");
                        //
                        getFacade().getMovParcela().validarMovParcelaPagar(movParcelaVO);
                    }
                    getFacade().getMovParcela().validarMovParcelaComTransacaoConcluidaOuPendente(movParcelaVO.getCodigo());
                    valorParcelas = Uteis.arredondarForcando2CasasDecimais(valorParcelas + movParcelaVO.getValorParcela());
                }

                //Quando dá erro na primeira tentativa... como são duas transações. o recibo foi incluido e o movpagamento ficou marcado como NovoObj (false)
                //Voltar aqui para NovoObj (true) para evitar erro ao realizar a segunda tentativa
                //by Luiz Felipe 14/04/2020
                Iterator i = movPagamentoControle.getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                    if (movPagamento.getMovPagamentoEscolhida()) {
                        movPagamento.setNovoObj(true);
                        movPagamento.setCodigo(null);
                    }
                }

                Iterator interar = movPagamentoControle.getListaSelectItemMovPagamento().iterator();
                Double valorContaCorrente = 0.0;
                // Obtem o valor em Conta Corrente
                while (interar.hasNext()) {
                    MovPagamentoVO obj = (MovPagamentoVO) interar.next();
                    if (obj.getMovPagamentoEscolhida() && obj.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                        valorContaCorrente += obj.getValorTotal();
                    }
                }
                // Soma o valor da Conta Corrente com o valor a cobrar no Transação
                Double valorTotal = Uteis.arredondarForcando2CasasDecimais(valorContaCorrente + getMovPagamentoSelecionado().getValorTotal());

                if (Uteis.arredondarForcando2CasasDecimais(valorParcelas) != valorTotal) {
                    throw new Exception("Valor da(s) parcela(s) selecionada(s) é diferente do valor da transação somado ao valor da conta corrente.");
                }

                Connection c = null;
                try {
                    c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getFacade().getRisco().getCon());
                    /*Atenção!!!
                     Metodo precisa controlar duas transações, uma para inclusão do recibo e outra para inclusão da transação, onde o recibo pode ser estornado e a transação não.
                     Isso foi necessário para evitar transações aprovadas sem recibo, onde o sistema fazia a transação e durante a inclusão do recibo acontecia algum problema.
                     Com esse erro, alunos eram bloqueado indevidamente, pois as parcelas ficavam abertas, e o fechamento de caixa ficava errado, pois não tinha recibo.
                     Agora o sistema passa a incluir primeiro o recibo e depois executar a trasação. Caso a transação não seja aprovada, recibo não é confirmado
                    */
                    movPagamentoControle.getMovPagamentoVO().getResponsavelPagamento().setUsername(this.getResponsavelPagamento().getUsername());
                    movPagamentoControle.getMovPagamentoVO().getResponsavelPagamento().setSenha(this.getResponsavelPagamento().getSenha());
                    movPagamentoControle.setControlarTransacao(false);
                    getFacade().getRisco().getCon().setAutoCommit(false);

                    //movPagamentoControle.getMovPagamentoVO().setEmpresa(getFacade().getEmpresa().consultarPorCodigo(dadosPagamento.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    movPagamentoControle.verificarUsuarioSenhaResponsavelPagamentoCupom();
                    if (movPagamentoControle.getErro()) {
                        throw new Exception(movPagamentoControle.getMensagemDetalhada());
                    }

                    if (UteisValidacao.emptyNumber(getConvenioCobrancaSelecionado())) {
                        throw new Exception("Selecione um convênio de cobrança");
                    }

                    ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(getConvenioCobrancaSelecionado());
                    if (convenioCobrancaVO == null) {
                        throw new Exception("Não foi encontrado o convênio de cobrança.");
                    }

                    //Consultar o servico de pagamento01
                    dadosPagamento.setTipoTransacaoEnum(convenioCobrancaVO.getTipo().getTipoTransacao());
                    dadosPagamento.setTransacaoPresencial(true);
                    dadosPagamento.setOrigemCobranca(OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO);
                    dadosPagamento.setAsync(false);

                    try {
                        Uteis.logarDebug("Origem: " + OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO.getDescricao() + " | Start Process | realizarCobrancaAgrupado -> Convênio: " + convenioCobrancaVO.getDescricao() + " | Tipo: " + convenioCobrancaVO.getTipo().getDescricao() + " | Pagador: " +
                                this.pessoaPagamento.getCodigo() + " - " + this.pessoaPagamento.getNome() + " | Parcelas: " + Uteis.retornarCodigos(dadosPagamento.getListaParcelas()));
                    } catch (Exception ex) {}

                    AprovacaoServiceInterface service = getServiceTransacaoOnline(convenioCobrancaVO, c);
                    new AragornService().povoarCartaoCreditoTO(dadosPagamento);
                    TransacaoVO transacao = service.tentarAprovacao(dadosPagamento);

                    if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                        try {
                            getFacade().getRisco().getCon().commit();
                            getFacade().getRisco().getCon().setAutoCommit(true);
                            transacao.setReciboPagamento(movPagamentoControle.getReciboObj().getCodigo());
                            transacao.setMovPagamento(this.getMovPagamentoSelecionado().getCodigo());
                            getFacade().getTransacao().alterar(transacao);
                            movPagamentoControle.getMovPagamentoVO().setConvenio(transacao.getConvenioCobrancaVO());
                            movPagamentoControle.setMensagemPagamento(movPagamentoControle.getMensagemPagamento() + movPagamentoControle.realizarAcoesPagamentoConfirmado());
                            PessoaVO pessoaContrato = null;
                            boolean cartaoTrocado = false;
                            boolean regimeRecorrencia = false;
                            List<MovParcelaVO> listParcelas = transacao.getListaParcelas();
                            for (MovParcelaVO movParcelaVO : listParcelas) {
                                ContratoVO contrato = movParcelaVO.getContrato();
                                pessoaContrato = movParcelaVO.getPessoa();
                                regimeRecorrencia = movParcelaVO.getRegimeRecorrencia();
                                if (contrato != null && contrato.getCodigo() != 0 && movParcelaVO.getRegimeRecorrencia()) {
                                    ContratoRecorrenciaVO contratoRecorVO = getFacade().getContratoRecorrencia().consultarPorContrato(
                                            contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, Conexao.getFromSession());
                                    contratoRecorVO.setNumeroCartao(transacao.getValorCartaoMascarado());
                                    contratoRecorVO.setUltimaTransacaoAprovada(transacao.getValorUltimaTransacaoAprovada());
                                    if (this.getTrocarCartao()) {
                                        //efetuar a troca do cartão
                                        ClienteVO cli = getFacade().getCliente().consultarPorCodigoPessoa(contratoRecorVO.getContrato().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                                        final String numeroCartaoAnterior = (String) JSFUtilities.getManagedBeanValue(TrocarCartaoRecorrenciaControle.class.getSimpleName() + ".contratoRecorrencia.numeroCartao");
                                        trocarCartao(contratoRecorVO);
                                        cartaoTrocado = getFacade().getAutorizacaoCobrancaCliente().trocarCartao(cli.getCodigo(), dadosPagamento, numeroCartaoAnterior);
                                        if (!cartaoTrocado) {
                                            getFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(numeroCartaoAnterior, cli.getCodigo());
                                        }
                                    } else {
                                        getFacade().getContratoRecorrencia().alterar(contratoRecorVO);
                                    }
                                    if (!cartaoTrocado && transacao.getTipo().equals(TipoTransacaoEnum.AprovaFacilCB)) {
                                        this.setTrocarCartao(true);
                                        this.getDadosPagamento().setTrocarCartao(true);
                                        cadastrarAutorizacaoCobranca(pessoaContrato, regimeRecorrencia, emp);
                                    }
                                }
                            }
                            this.setTrocarCartao(false);
                            this.getDadosPagamento().setTrocarCartao(false);
                            movPagamentoControle.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
                            getFacade().getMovPagamento().alterarNSU(transacao.getNSU(), this.getMovPagamentoSelecionado().getCodigo());
                            getFacade().getMovPagamento().alterarConvenioCobranca(transacao.getConvenioCobrancaVO().getCodigo(), this.getMovPagamentoSelecionado().getCodigo());
                            if (transacao.getConvenioCobrancaVO().isStoneV5()) {
                                getFacade().getMovPagamento().alterarNSUeCodAutorizacao(transacao.getCodigoNSU(), transacao.getCodigoAutorizacao(), this.getMovPagamentoSelecionado().getCodigo());
                            }
                            try {
                                if (UteisValidacao.emptyNumber(this.getMovPagamentoSelecionado().getAdquirenteVO().getCodigo())) {
                                    AdquirenteVO adquirenteVO = getFacade().getAdquirente().obterAdquirenteTransacao(transacao);
                                    getFacade().getMovPagamento().alterarAdquirente(adquirenteVO.getCodigo(), this.getMovPagamentoSelecionado().getCodigo());
                                    movPagamentoControle.getMovPagamentoVO().setAdquirenteVO(adquirenteVO);
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            JSFUtilities.storeOnSession(MovPagamentoControle.class.getSimpleName(), movPagamentoControle);

                            try {
                                if (emp.isNotificarWebhook()) {
                                    ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(movPagamentoControle.getReciboObj().getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    getFacade().getZWFacade().notificarPagamento(clienteVO, movPagamentoControle.getReciboObj());
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }

                        } catch (Exception e) {
                            getFacade().getTransacao().alterarMessagemErro(transacao, e.getMessage());
                            throw e;
                        }

                        montarSucessoGrowl("Pagamento Efetuado Com Sucesso.");
                        setOnComplete("Richfaces.hideModalPanel('panelUsuarioSenha');");
                    } else {
                        getFacade().getRisco().getCon().rollback();
                        if (adicionarParcelasJuros && (transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) || transacao.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO))) {
                            getFacade().getTransacao().excluirParcelaMultaJurosTransacao(transacao, null);
                        }
                        getFacade().getRisco().getCon().setAutoCommit(true);
                        movPagamentoControle.limparMsg();
                        movPagamentoControle.setMensagemPagamento("");
                        movPagamentoControle.setApresentarBotaoRecibo(false);
                        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                            montarErro("Pagamento enviado. Aguardando retorno da operadora. Você podera gerar o recibo após a confirmação da operadora.");
                            setOnComplete("Richfaces.hideModalPanel('panelUsuarioSenha');Richfaces.showModalPanel('panelAguardandoConfirmacaoPagamento');");
                        } else {
                            String msgExcep = "";
                            if (transacao.getTipo().equals(TipoTransacaoEnum.STONE_ONLINE)) {
                                msgExcep = "Mensagem da Stone: ";
                                if (transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) &&
                                        UteisValidacao.emptyString(transacao.getCodigoRetornoGestaoTransacaoMotivo()) &&
                                        !UteisValidacao.emptyString(transacao.getErroProcessamento())) {
                                    msgExcep += transacao.getErroProcessamento();
                                }
                            }
                            if (transacao.getTipo().equals(TipoTransacaoEnum.STRIPE)) {
                                msgExcep = "Mensagem da Stripe: ";
                            }
                            if (transacao.getTipo().equals(TipoTransacaoEnum.PAGOLIVRE)) {
                                msgExcep = "Mensagem da PagoLivre: ";
                            }
                            if (transacao.getTipo().equals(TipoTransacaoEnum.FACILITEPAY)) {
                                msgExcep = "Mensagem da FacilitePay: ";
                            }
                            if (transacao.getTipo().equals(TipoTransacaoEnum.CEOPAG)) {
                                msgExcep = "Mensagem da Ceopag: ";
                            }
                            if (transacao.getTipo().equals(TipoTransacaoEnum.PINBANK)) {
                                msgExcep = "Mensagem da PinBank: ";
                            }
                            if (transacao.getTipo().equals(TipoTransacaoEnum.CEOPAG)) {
                                msgExcep = "Mensagem da Ceopag: ";
                            }
                            throw new Exception(msgExcep + transacao.getCodigoRetornoGestaoTransacaoMotivo());
                        }
                    }
                } catch (Exception e) {
                    if (!getFacade().getRisco().getCon().getAutoCommit()) { //controle transacional  faz parte da regra de negocio, e isso é necessário para evitar exceções no rollback
                        getFacade().getRisco().getCon().rollback();
                    }
                    throw e;
                } finally {
                    if (c != null) {
                        c.close();
                    }
                    getFacade().getRisco().getCon().setAutoCommit(true);
                    movPagamentoControle.setControlarTransacao(true);
                    try {
                        //atualizar tentativa das parcelas
                        getFacade().getMovParcelaResultadoCobranca().processarParcelas(parcelas);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, PagamentoCartaoCreditoControle.class);
            setMensagemDetalhada("", e.getMessage());
            movPagamentoControle.limparMsg();
            movPagamentoControle.setMensagemPagamento("");
            movPagamentoControle.setApresentarBotaoRecibo(false);

            //não fechar o modal quando for usuário
            if (e.getMessage() != null && !e.getMessage().toLowerCase().contains("usuário e senha não confere.")) {
                doSelectCartao(acc, false);
                setOnComplete("Richfaces.hideModalPanel('panelUsuarioSenha');");
            }
            montarErro(e);
        } finally {
            if (dadosPagamento != null &&
                    !UteisValidacao.emptyString(dadosPagamento.getValidade()) &&
                    dadosPagamento.getValidade().length() == 7) {
                //formato mm/yyyy
                dadosPagamento.setValidade(dadosPagamento.getValidade().replace("/20", "/"));
            }
            setProcessandoOperacao(false);
        }
    }

    /**
     * Metodo chamado para fechar o modal alerta de pagamento aguardando retorno.
     *
     * @return
     */
    public String fecharAlertaAguardandoPagamento() {
        this.setMensagem("");
        setOnComplete("");
        return "tela8";
    }

    private AprovacaoServiceInterface getServiceTransacaoOnline(ConvenioCobrancaVO convenioCobrancaVO, Connection con) throws Exception {
        return CobrancaOnlineService.getImplementacaoAprovacaoService(convenioCobrancaVO.getTipo().getTipoTransacao(), getEmpresaLogado().getCodigo(),
                convenioCobrancaVO.getCodigo(), convenioCobrancaVO.isPactoPay(), con);
    }

    /**
     * Responsável por marcar a data de inutilização da recorrência atual e
     * gerar uma nova com os mesmos dados, exceto o cartão e a última transação
     * concluida com sucesso
     *
     * <AUTHOR> 04/08/2011
     */
    private void trocarCartao(ContratoRecorrenciaVO contratoRecorrencia) throws Exception {
        contratoRecorrencia.setDataInutilizada(Calendario.hoje());
        getFacade().getContratoRecorrencia().gravarDataInutilizada(contratoRecorrencia);
        contratoRecorrencia.setDataInutilizada(null);
        getFacade().getContratoRecorrencia().incluir(contratoRecorrencia);
    }

    public void listenerTrocarCartao(ActionEvent event) {
        this.setTrocarCartao(true);
    }

    /**
     * Valida os dados digitados pelo usuario
     *
     * <AUTHOR> 08/07/2011
     */
    public void confirmar() {
        AutorizacaoCobrancaClienteVO acc = obterAutorizacaoCobrancaClienteSelecinada();
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyNumber(getConvenioCobrancaSelecionado())) {
                throw new Exception("Selecione um convênio de cobrança.");
            }

            ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(getConvenioCobrancaSelecionado());
            if (convenioCobrancaVO == null) {
                throw new Exception("Não foi encontrado o convênio de cobrança.");
            }

            if (getParcelasPagar() != null) {
                for (MovParcelaVO listaParcela : getParcelasPagar()) {
                    MovPagamentoControle movPagamentoControle = (MovPagamentoControle) getControlador(MovPagamentoControle.class);
                    movPagamentoControle.validarParcelaRemessa(listaParcela);
                }
            }

            dadosPagamento.setNumero(dadosPagamento.getNumero().replace(" ", ""));

            if (!this.getParcelasPagar().isEmpty()) {
                dadosPagamento.setEmpresa(this.getParcelasPagar().get(0).getEmpresa().getCodigo());
            }

            if(!UteisValidacao.emptyNumber(dadosPagamento.getMesValidade()) && !UteisValidacao.emptyNumber(dadosPagamento.getAnoValidade())){
                String mesValidade = String.valueOf(dadosPagamento.getMesValidade());
                if (String.valueOf(dadosPagamento.getMesValidade()).length() == 1) {
                    mesValidade = '0' + String.valueOf(dadosPagamento.getMesValidade());
                }
                String validadeFormatada = (mesValidade + "/" + dadosPagamento.getAnoValidade()).replace("/20","/");
                dadosPagamento.setValidade(validadeFormatada);

            }

            CartaoCreditoTO.validarDados(this.getDadosPagamento());
            setOnComplete("Notifier.cleanAll();Richfaces.showModalPanel('panelUsuarioSenha');");

            setSucesso(true);
            setMensagemDetalhada("");
        } catch (Exception e) {
            doSelectCartao(acc, false);
            setSucesso(false);
            setMensagemDetalhada(e.getMessage());
            montarErro(e);
        }
    }

    /**
     * Responsável por inicializar atributos da operação de pagamento de
     * contrato recorrente
     *
     * <AUTHOR> 07/07/2011
     */
    private void validarAtributos() throws Exception {
        if (this.getParcelasPagar().isEmpty()) {
            throw new Exception(getMensagemInternalizacao("msg_erro_nenhumaParcelaRelacionada"));
        }
        for (MovParcelaVO parcela : this.getParcelasPagar()) {

            //calcular o total das parcelas somando-as
            this.setValorPagar(this.getValorPagar() + parcela.getValorParcela());
        }
        this.onComplete = "";
    }

    /**
     * retorna um comando de alerta caso a ação do botão não tenha sido efetuado
     * com sucesso
     *
     * <AUTHOR> 07/07/2011
     */
    public String getAlertValidacao() {
        if (getSucesso()) {
            return "";
        } else {
            return getMsgAlert();
        }
    }

    /**
     * Responsável por limpar os atributos referentes ao pagamento
     *
     * <AUTHOR> 07/07/2011
     */
    private void limparAtributos() {
        this.setValorPagar(0.0);
        this.setDadosPagamento(new CartaoCreditoTO());
        this.setParcelasPagar(new ArrayList<MovParcelaVO>());
        this.setResponsavelPagamento(new UsuarioVO());
    }

    /**
     * Responsável pela ação do botão cancelar
     *
     * <AUTHOR> 07/07/2011
     */
    public String cancelar() {
        limparAtributos();
        return "login";
    }

    /**
     * Responsável por validar se as configurações de gateway estão informadas
     *
     * @throws Exception
     * <AUTHOR> 05/07/2011
     */
    private void validarConfiguracoes() throws Exception {
        boolean estaConfigurado = !getMapaConvenioCobranca().isEmpty();
        if (!estaConfigurado) {
            throw new Exception(getMensagemInternalizacao("msg_erro_configuracoesRecorrenciaInexistentes"));
        }
    }

    /**
     * Responsável por formatar o valor a ser pago para exibição
     *
     * <AUTHOR> 07/07/2011
     */
    public String getValor() {
        return Formatador.formatarValorMonetario(this.getValorPagar());
    }

    public List<SelectItem> getBandeirasCartaoSelectItems() throws Exception {
        if (bandeirasCartaoSelectItems == null) {
            OperadorasExternasAprovaFacilEnum[] vet = OperadorasExternasAprovaFacilEnum.values();
            List<OperadoraCartaoVO> lista = getFacade().getOperadoraCartao().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            bandeirasCartaoSelectItems = new ArrayList();
            for (OperadoraCartaoVO operadoraCartaoVO : lista) {
                for (int i = 0; i < vet.length; i++) {
                    OperadorasExternasAprovaFacilEnum operadora = vet[i];
                    if (operadora == operadoraCartaoVO.getCodigoIntegracaoAPF() || operadora == operadoraCartaoVO.getCodigoIntegracaoVindi()) {
                        bandeirasCartaoSelectItems.add(new SelectItem(operadora.getId(), operadora.getDescricao()));
                        break;
                    }
                }
            }
            return bandeirasCartaoSelectItems;
        }
        return bandeirasCartaoSelectItems;
    }

    /**
     * Consulta o responsável pelo contrato, caso existam alteração
     */
    public void consultarResponsavel() {
        try {
            limparMsg();
            setOnComplete("");

            this.setResponsavelPagamento(
                    getFacade().getUsuario().consultarPorChavePrimaria(
                            this.getResponsavelPagamento().getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            this.setMensagemID("msg_dados_consultados");
            getMovPagamentoSelecionado().setResponsavelPagamento(this.getResponsavelPagamento());
            setOnComplete("Notifier.cleanAll();");
        } catch (Exception e) {
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
        }
    }

    public boolean verificarLoginUsuario() throws Exception {
        getFacade().getControleAcesso().verificarLoginUsuarioPIN(
                this.getResponsavelPagamento().getUsername(),
                this.getResponsavelPagamento().getSenha().toUpperCase());
        return true;
    }

    public void buscaBandeiraCartaoOperadora() {
        try {
            inicializarCard();
            String numeroCartao = dadosPagamento.getNumero().replaceAll(" ", "");
            List<SelectItem> operadoras = getOperadorasCartaoCredito();
            if (ValidaBandeira.numeroCartaoValido(numeroCartao)) {
                ValidaBandeira.Bandeira bandeiraCard = ValidaBandeira.buscarBandeira(numeroCartao);
                for (SelectItem item : operadoras) {
                    if(bandeiraCard.name().equals(item.getLabel().toUpperCase())){
                        this.operadoraCartao = Integer.valueOf(item.getValue().toString());
                    }
                }
                selecionaOperadora();
            }
        } catch (Exception ignored) {
        }
    }

    public void selecionaOperadora() {
        if (!UteisValidacao.emptyNumber(this.operadoraCartao)) {
            try {
                OperadoraCartaoVO operadoraCartaoVO = getFacade().getOperadoraCartao().consultarPorChavePrimaria(operadoraCartao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.getDadosPagamento().setBand(operadoraCartaoVO.getOperadoraIntegracao());
                this.getDadosPagamento().setBandeira(operadoraCartaoVO.getOperadoraIntegracao().getId());
                if (this.getMovPagamentoSelecionado() != null) {//recurso pode não estar sendo utilizado na tela de pagamento (quitação)
                    this.getMovPagamentoSelecionado().setOperadoraCartaoVO(operadoraCartaoVO);
                }

                MovPagamentoControle control;
                if (this.movPagamentoControle == null) {
                    control = (MovPagamentoControle) JSFUtilities.getManagedBean(MovPagamentoControle.class.getSimpleName());
                } else {
                    control = movPagamentoControle;
                }
                if (control != null) {
                    control.montarListaNrParcelasCartao(operadoraCartaoVO);
                }
                limparMsg();
            } catch (Exception ex) {
                Logger.getLogger(PagamentoCartaoCreditoControle.class.getName()).log(Level.SEVERE, null, ex);
                setMensagemDetalhada("msg_erro", ex.getMessage());
            }
        }
    }

    public void selecionaOperadoraAuto() {
        if (this.operadoraCartao != null) {
            try {
                OperadoraCartaoVO operadoraCartaoVO = getFacade().getOperadoraCartao().consultarPorChavePrimaria(operadoraCartao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.getDadosPagamento().setBand(operadoraCartaoVO.getOperadoraIntegracao());
                this.getDadosPagamento().setBandeira(operadoraCartaoVO.getOperadoraIntegracao().getId());
//                this.convenioCobrancaVO = operadoraCartaoVO.getTipoTransacaoEnum(getConfiguracaoSistema().isUsaAprovaFacil());
                if (this.getMovPagamentoSelecionado() != null) {//recurso pode não estar sendo utilizado na tela de pagamento (quitação)
                    this.getMovPagamentoSelecionado().setOperadoraCartaoVO(operadoraCartaoVO);

                }
            } catch (Exception ex) {
                Logger.getLogger(PagamentoCartaoCreditoControle.class.getName()).log(Level.SEVERE, null, ex);
                setMensagemDetalhada("msg_erro", ex.getMessage());
            }
        }
    }

    public void alterarBandeira() {
        dadosPagamento.setBand(null);
        dadosPagamento.setBandeira(0);
    }

    //--------------------------- GETTERS AND SETTERS ----------------------------------//
    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        if (this.configuracaoSistema == null) {
            this.configuracaoSistema = new ConfiguracaoSistemaVO();
        }
        return this.configuracaoSistema;

    }

    /**
     * @param parcelas the pagamentos to set
     */
    public void setParcelasPagar(List<MovParcelaVO> parcelas) {
        this.parcelasPagar = parcelas;
    }

    /**
     * @return the pagamentos
     */
    public List<MovParcelaVO> getParcelasPagar() {
        if (this.parcelasPagar == null) {
            parcelasPagar = new ArrayList<MovParcelaVO>();
        }
        return parcelasPagar;
    }

    /**
     * @param valorPagar the valorPagar to set
     */
    public void setValorPagar(Double valorPagar) {
        this.valorPagar = valorPagar;
    }

    /**
     * @return the valorPagar
     */
    public Double getValorPagar() {
        return valorPagar;
    }

    /**
     *
     */
    public void setResponsavelPagamento(UsuarioVO responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }

    /**
     * @return the pessoaPagadora
     */
    public UsuarioVO getResponsavelPagamento() {
        if (responsavelPagamento == null) {
            responsavelPagamento = new UsuarioVO();
        }
        return responsavelPagamento;
    }

    /**
     * @param dadosPagamento the dadosPagamento to set
     */
    public void setDadosPagamento(CartaoCreditoTO dadosPagamento) {
        this.dadosPagamento = dadosPagamento;
    }

    /**
     * @return the dadosPagamento
     */
    public CartaoCreditoTO getDadosPagamento() {
        if (dadosPagamento == null) {
            dadosPagamento = new CartaoCreditoTO();
        }
        return dadosPagamento;
    }

    public MovPagamentoVO getMovPagamentoSelecionado() {
        return movPagamentoSelecionado;
    }

    public void setMovPagamentoSelecionado(MovPagamentoVO movPagamentoSelecionado) {
        this.movPagamentoSelecionado = movPagamentoSelecionado;
        setarPropriedadesOperadoraCartao();
    }

    private void setarPropriedadesOperadoraCartao() {
        if (this.movPagamentoSelecionado != null && this.movPagamentoSelecionado.getOperadoraCartaoVO() != null && !UteisValidacao.emptyNumber(this.movPagamentoSelecionado.getOperadoraCartaoVO().getCodigo())) {
            this.operadoraCartao = this.movPagamentoSelecionado.getOperadoraCartaoVO().getCodigo();
            selecionaOperadora();
        }
    }

    /**
     * @param trocarCartao the trocarCartao to set
     */
    public void setTrocarCartao(boolean trocarCartao) {
        this.trocarCartao = trocarCartao;
    }

    /**
     * @return the trocarCartao
     */
    public boolean getTrocarCartao() {
        return trocarCartao;
    }

    public static void main(String... args) throws Exception {
        System.setProperty("socksProxyHost", "localhost");
        System.setProperty("socksProxyPort", "9090");
        System.out.println(ExecuteRequestHttpService.executeRequest("http://ip-api.com/json", null));
    }

    public static OperadorasExternasAprovaFacilEnum[] addElementoArray(OperadorasExternasAprovaFacilEnum elemento,
                                                                       OperadorasExternasAprovaFacilEnum[] origem) {

        //cria um novo array conforme vai add elementos
        OperadorasExternasAprovaFacilEnum[] retorno = new OperadorasExternasAprovaFacilEnum[origem.length + 1];

        System.arraycopy(origem, 0, retorno, 0, origem.length);
        retorno[origem.length] = elemento;
        return retorno;
    }

    /**
     * Método responsável por efetivar a troca de Cartão de Crédito relacionado
     * a um Contrato de Recorrência. O Sistema tentará aprovar uma transação de
     * 2,00 ( Dois reais, pois há bancos que não aceita valores abaixo disso) e
     * caso seja aprovada o sistema guardará esse ID de transação, para que este
     * seja usado nas transações de recorrência futuras para o mesmo Contrato do
     * Cliente de forma automática.
     *
     * <AUTHOR> Maciel
     */
    public void concretizarTrocaSemCobrar(ActionEvent evt) {
        limparMsg();
        AutorizacaoCobrancaClienteVO acc = obterAutorizacaoCobrancaClienteSelecinada();
        try {
            Integer codContrato = (Integer) evt.getComponent().getAttributes().get("codContrato");
            if (codContrato != null && codContrato > 0) {

                ContratoRecorrenciaVO contratoRecorVO = getFacade().getContratoRecorrencia().consultarPorContrato(
                        codContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS, Conexao.getFromSession());

                ClienteVO cli = getFacade().getCliente().consultarPorCodigoPessoa(contratoRecorVO.getContrato().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

                this.setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                if (acc != null && null != dadosPagamento.getNumero() && dadosPagamento.getNumero().contains("****")) {

                    dadosPagamento.setNumero(acc.getNazgDTO().getCard());

                }

                dadosPagamento.setUsuarioResponsavel(this.getUsuarioLogado());
                this.getDadosPagamento().setValorDocumento("2,00");
                this.getDadosPagamento().setListaParcelas(null);
                this.getDadosPagamento().setIp("RecorrENtE");
                this.getDadosPagamento().setIpClientePacto(this.getIpCliente());
                Integer codEmpresa = this.getDadosPagamento().getEmpresa();
                if (codEmpresa == null || codEmpresa == 0) {
                    codEmpresa = cli.getEmpresa().getCodigo();
                }
                EmpresaVO emp = getFacade().getEmpresa().consultarPorCodigo(codEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                String url = ConfiguracaoSistemaVO.obterURLRecorrencia(emp, this.getConfiguracaoSistema());
                this.getDadosPagamento().setUrl(url);
                this.getDadosPagamento().setValor(2.0);
                this.getDadosPagamento().setEmpresa(contratoRecorVO.getContrato().getEmpresa().getCodigo());
                this.getDadosPagamento().setTrocarCartao(true);

                CartaoCreditoTO.validarDados(this.getDadosPagamento());
                if (url != null && !url.isEmpty()) {
                    final String numeroCartaoAnterior = (String) JSFUtilities.getManagedBeanValue(TrocarCartaoRecorrenciaControle.class.getSimpleName() + ".contratoRecorrencia.numeroCartao");

                    //Consultar o servico de pagamento
                    AprovacaoServiceInterface service = new AprovaFacilService(Conexao.getFromSession());
                    new AragornService().povoarCartaoCreditoTO(dadosPagamento);
                    dadosPagamento.setTransacaoPresencial(true);
                    TransacaoVO transacao = service.tentarAprovacao(dadosPagamento);
                    if (transacao.getSituacao() == SituacaoTransacaoEnum.APROVADA) {
                        transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
                        getFacade().getTransacao().alterar(transacao);

                        contratoRecorVO.setNumeroCartao(transacao.getValorAtributoResposta(APF.CartaoMascarado));
                        contratoRecorVO.setUltimaTransacaoAprovada(transacao.getValorAtributoResposta(APF.Transacao));

                        //efetuar a troca do cartão
                        boolean cartaoTrocado = getFacade().getAutorizacaoCobrancaCliente().trocarCartao(cli.getCodigo(), dadosPagamento,
                                numeroCartaoAnterior);
                        trocarCartao(contratoRecorVO);
                        setMensagem(String.format("Troca de Cartão de Crédito realizada com sucesso pela Transação [%s]. "
                                        + "A partir de agora o novo Cartão de Crédito vinculado a este contrato possui Nº.: %s",
                                new Object[]{
                                        transacao.getCodigoExterno(),
                                        transacao.getCartaoMascarado()
                                }));
                        if (!cartaoTrocado) {
                            getFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(numeroCartaoAnterior, cli.getCodigo());
                            this.setTrocarCartao(true);
                            this.getDadosPagamento().setTrocarCartao(true);
                            cadastrarAutorizacaoCobranca(cli.getPessoa(), true, emp);
                        }
                        this.setTrocarCartao(false);
                        this.getDadosPagamento().setTrocarCartao(false);

                    } else {
                        throw new Exception("Pagamento não autorizado: "
                                + transacao.getValorAtributoResposta("ResultadoSolicitacaoAprovacao"));
                    }
                } else {
                    AutorizacaoCobrancaClienteVO auto = null;
                    List<AutorizacaoCobrancaClienteVO> autorizacoesCliente = getFacade().getAutorizacaoCobrancaCliente().consultarPorCliente(cli.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (AutorizacaoCobrancaClienteVO accVO : autorizacoesCliente) {
                        if (TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.equals(accVO.getTipoAutorizacao())) {
                            auto = accVO;
                            break;
                        }
                    }
                    String numeroCartaoAnterior = "";
                    if (auto != null) {
                        numeroCartaoAnterior = auto.getCartaoMascarado();
                    }
                    getFacade().getAutorizacaoCobrancaCliente().trocarCartao(cli.getCodigo(), dadosPagamento, numeroCartaoAnterior);
                    montarSucessoGrowl("Troca de Cartão de Crédito realizada com sucesso.");
                }
            }

        } catch (Exception e) {
            doSelectCartao(acc, true);
            setMensagemDetalhada("", e.getMessage());
        }
    }

    public void prepararControlador(List<MovParcelaVO> listaParcelas) throws Exception {
        inicializarConfiguracaoSistema();
        EmpresaVO empresa = getFacade().getEmpresa().obterEmpresaDeUmaListaParcelas(listaParcelas);
        this.getDadosPagamento().setEmpresa(empresa.getCodigo());
        this.getDadosPagamento().setUrl(ConfiguracaoSistemaVO.obterURLRecorrencia(empresa, this.getConfiguracaoSistema()));
        this.setParcelasPagar(listaParcelas);
        this.inicializarBandeiras();
    }

    public boolean isUrlTeste() {
        return urlTeste;
    }

    public void setUrlTeste(boolean urlTeste) {
        this.urlTeste = urlTeste;
    }

    private void cadastrarAutorizacaoCobranca(PessoaVO pessoaContrato,
                                              boolean regimeRecorrencia, EmpresaVO emp) {
        if (pessoaContrato != null && regimeRecorrencia) {//cadastrar automaticamente Autorização de Cobrança a partir de uma Transação Aprovada no AprovaFacil
            ClienteVO cliente;
            try {
                cliente = getFacade().getCliente().consultarPorCodigoPessoa(
                        pessoaContrato.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                if (cliente != null && cliente.getCodigo() > 0) {
                    List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPorEmpresa(
                            emp.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS,
                            new Integer[]{TipoConvenioCobrancaEnum.DCC.getCodigo()});
                    if (!convenios.isEmpty() && getFacade().getAutorizacaoCobrancaCliente().validarBandeiraConvenio(dadosPagamento.getBand(), convenios.get(0).getTipo())) {
                        AutorizacaoCobrancaClienteVO a = new AutorizacaoCobrancaClienteVO(true);
                        a.setCliente(cliente);
                        a.setConvenio(convenios.get(0));
                        a.setNumeroCartao(dadosPagamento.getNumero());
                        a.setOperadoraCartao(dadosPagamento.getBand());
                        a.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                        a.setTipoACobrar(TipoObjetosCobrarEnum.CONTRATOS_RENOVAVEIS_AUTO);
                        a.setValidadeCartao(this.getDadosPagamento().getValidadeMMYYYY(true));

                        if (this.getDadosPagamento().isTrocarCartao() || this.getTrocarCartao()) {
                            AutorizacaoCobrancaClienteVO.validarDados(a);
                            List<AutorizacaoCobrancaClienteVO> autorizacoesSemelhantes = getFacade().getAutorizacaoCobrancaCliente().obterOutrasAutorizacoesParecidasParaOMesmoTipo(a);
                            for (AutorizacaoCobrancaClienteVO autoExist : autorizacoesSemelhantes) {
                                getFacade().getAutorizacaoCobrancaCliente().alterarSituacaoAutorizacaoCobranca(false, autoExist, "" , getUsuarioLogado());
                                getFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(autoExist.getCartaoMascarado(), autoExist.getCliente().getCodigo());
                            }
                            getFacade().getAutorizacaoCobrancaCliente().incluir(a);
                        } else if (!getFacade().getAutorizacaoCobrancaCliente().existeOutraAutorizacaoParecidaParaOMesmoTipo(a)) {
                            getFacade().getAutorizacaoCobrancaCliente().incluir(a);
                        }
                    }
                }
            } catch (Exception ex) {
                Logger.getLogger(PagamentoCartaoCreditoControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public Integer getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(Integer operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public PessoaVO getPessoaPagamento() {
        return pessoaPagamento;
    }

    public void setPessoaPagamento(PessoaVO pessoaPagamento) {
        this.pessoaPagamento = pessoaPagamento;
    }

    public String getOnComplete() {
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List getListaAdquirenteMaxiPago() {
        return AdquirenteMaxiPagoEnum.getSelectListAdquirenteMaxiPago();
    }

    public boolean getConvenioStone() {
        ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(getConvenioCobrancaSelecionado());
        if (convenioCobrancaVO == null) {
            return false;
        }
        return convenioCobrancaVO.getTipo().getTipoTransacao().equals(TipoTransacaoEnum.STONE_ONLINE);
    }

    public boolean getConvenioMaxiPago() {
        ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(getConvenioCobrancaSelecionado());
        if (convenioCobrancaVO == null) {
            return false;
        }
        return convenioCobrancaVO.getTipo().getTipoTransacao().equals(TipoTransacaoEnum.MAXIPAGO);
    }

    public List<AutorizacaoCobrancaClienteVO> getAutorizacoes() {
        if (autorizacoes == null) {
            autorizacoes = new ArrayList<>();
        }
        return autorizacoes;
    }

    public void setAutorizacoes(List<AutorizacaoCobrancaClienteVO> autorizacoes) {
        this.autorizacoes = autorizacoes;
    }

    public Map<Integer, List<OperadoraCartaoVO>> getMapaConvenioBandeiras() {
        if (mapaConvenioBandeiras == null) {
            mapaConvenioBandeiras = new HashMap<>();
        }
        return mapaConvenioBandeiras;
    }

    public void setMapaConvenioBandeiras(Map<Integer, List<OperadoraCartaoVO>> mapaConvenioBandeiras) {
        this.mapaConvenioBandeiras = mapaConvenioBandeiras;
    }

    public Map<Integer, ConvenioCobrancaVO> getMapaConvenioCobranca() {
        if (mapaConvenioCobranca == null) {
            mapaConvenioCobranca = new HashMap<>();
        }
        return mapaConvenioCobranca;
    }

    public void setMapaConvenioCobranca(Map<Integer, ConvenioCobrancaVO> mapaConvenioCobranca) {
        this.mapaConvenioCobranca = mapaConvenioCobranca;
    }

    public Integer getConvenioCobrancaSelecionado() {
        if (convenioCobrancaSelecionado == null) {
            convenioCobrancaSelecionado = 0;
        }
        return convenioCobrancaSelecionado;
    }

    public void setConvenioCobrancaSelecionado(Integer convenioCobrancaSelecionado) {
        this.convenioCobrancaSelecionado = convenioCobrancaSelecionado;
    }

    public String getUrlGatewayTokenVindi() {
        try {
            if (UteisValidacao.emptyNumber(getConvenioCobrancaSelecionado())) {
                return "";
            }

            ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(getConvenioCobrancaSelecionado());
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                if (convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                    return PropsService.getPropertyValue(PropsService.urlApiVindiProducao) + "/public/payment_profiles";
                } else {
                    return PropsService.getPropertyValue(PropsService.urlApiVindiSandbox) + "/public/payment_profiles";
                }
            } else {
                return "";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String getChavePublicaGatewayTokenVindi() {
        try {
            if (UteisValidacao.emptyNumber(getConvenioCobrancaSelecionado())) {
                return "";
            }

            String chave = "";
            ConvenioCobrancaVO convenioCobrancaVO = getMapaConvenioCobranca().get(getConvenioCobrancaSelecionado());
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                chave = convenioCobrancaVO.getCodigoAutenticacao02();
            }
            return new String(new Base64().encode((chave + ":").getBytes()));
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void definirUsarOutroCartao() {
        try {
            limparMsg();
            if (getUsarOutroCartao()) {
                setAutorizacoesTemp(autorizacoes);
                setAutorizacoes(new ArrayList<>());
                limparDadosDoCartao(true);
            } else {
                setAutorizacoesTemp(new ArrayList<>());
                obterAutorizacoesCliente();
                if (autorizacoes.size() > 0) {
                    doSelectCartao(autorizacoes.get(0), true);
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void limparDadosDoCartao() {
        limparDadosDoCartao(true);
    }

    private void limparDadosDoCartao(boolean limparCvv) {
        dadosPagamento.setNumero("");
        dadosPagamento.setBand(null);
        dadosPagamento.setValidade("");
        dadosPagamento.setNomeTitular("");
        dadosPagamento.setCpfCnpjPortador("");
        dadosPagamento.setBandeira(0);
        if (limparCvv) {
            dadosPagamento.setCodigoSeguranca("");
        }
        dadosPagamento.setTokenAragorn("");
        dadosPagamento.setTokenVindi("");
        dadosPagamento.setTokenCielo("");
        dadosPagamento.setTokenPagoLivre("");
        dadosPagamento.setIdCardMundiPagg("");
        dadosPagamento.setIdCardPagarMe("");
        dadosPagamento.setMesValidade(0);
        dadosPagamento.setAnoValidade(0);
        setStyleClassCartao("white");
        setStyleClassCartaoRodape("white");
        setStyleClassCartaoTitles("titleGrey");
        operadoraCartao = null;
    }

    public Boolean getUsarOutroCartao() {
        return usarOutroCartao;
    }

    public void setUsarOutroCartao(Boolean usarOutroCartao) {
        this.usarOutroCartao = usarOutroCartao;
    }

    public String getStyleClassCartao() { return styleClassCartao; }

    public void setStyleClassCartao(String styleClassCartao) { this.styleClassCartao = styleClassCartao; }

    public String getStyleClassCartaoRodape() { return styleClassCartaoRodape; }

    public void setStyleClassCartaoRodape(String styleClassCartaoRodape) { this.styleClassCartaoRodape = styleClassCartaoRodape; }

    public String getStyleClassCartaoTitles() { return styleClassCartaoTitles; }

    public void setStyleClassCartaoTitles(String styleClassCartaoTitles) { this.styleClassCartaoTitles = styleClassCartaoTitles; }

    public List<AutorizacaoCobrancaClienteVO> getAutorizacoesTemp() {
        return autorizacoesTemp;
    }

    public void setAutorizacoesTemp(List<AutorizacaoCobrancaClienteVO> autorizacoesTemp) {
        this.autorizacoesTemp = autorizacoesTemp;
    }

    public String getTipoParcelamentoStone() {
        if (tipoParcelamentoStone == null) {
            tipoParcelamentoStone = "";
        }
        return tipoParcelamentoStone;
    }

    public void setTipoParcelamentoStone(String tipoParcelamentoStone) {
        this.tipoParcelamentoStone = tipoParcelamentoStone;
    }

    public boolean isLabelColaborador() {
        return labelColaborador;
    }

    public void setLabelColaborador(boolean labelColaborador) {
        this.labelColaborador = labelColaborador;
    }

    public void atualizarTipoPagamentoStone(ValueChangeEvent evt) {
        if (evt != null) {
            if (!UteisValidacao.emptyString(evt.getNewValue().toString())) {
                this.getDadosPagamento().setTipoParcelamentoStone(InstalmentTypeInstlmtTp.fromValue(evt.getNewValue().toString()));
            } else {
                this.getDadosPagamento().setTipoParcelamentoStone(null);
            }
        }
    }
    public void inicializarCard() {
        setStyleClassCartao("gradient");
        setStyleClassCartaoRodape("cartaoRodaPe");
        setStyleClassCartaoTitles("titleWhite");
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public List<AutorizacaoCobrancaColaboradorVO> getAutorizacoesColaborador() {
        if (autorizacoesColaborador == null) {
            autorizacoes = new ArrayList<>();
        }
        return autorizacoesColaborador;
    }

    public void irParaTelaClienteColaborador() {
        try {
            limparMsg();
            setOnComplete("");
            Integer pessoa = getMovPagamentoSelecionado().getPessoa().getCodigo();
            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Cliente ou Colaborador não Encontrado.");
            }

            Integer cliente = getFacade().getCliente().obterCodigoClientePorPessoa(pessoa);
            if (!UteisValidacao.emptyNumber(cliente)) {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(cliente);
                irParaTelaCliente(clienteVO);
                setOnComplete("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1100, 700);");
                return;
            }

            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(pessoa, getMovPagamentoSelecionado().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                irParaTelaColaborador(colaboradorVO);
                setOnComplete("abrirPopup('colaboradorForm.jsp', 'Colaborador', 1100, 700);");
                return;
            }

            throw new Exception("Cliente ou Colaborador não Encontrado.");
        } catch (Exception e) {
            montarErro(e);
        }
    }
}
