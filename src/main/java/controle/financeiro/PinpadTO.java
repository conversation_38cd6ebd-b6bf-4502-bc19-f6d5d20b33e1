package controle.financeiro;

import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import org.json.JSONObject;

/**
 * Created by alcides on 27/07/2017.
 */
public class PinpadTO {

    private Boolean pinpadCappta = false;
    private String retorno = "";
    private String cnpj = "";
    private String pdv = "";
    private String nrControle = "";
    private Double valorPinpad = 0.0;
    private Integer nrParcelas = 0;
    private Integer formaPagamento = 0;
    private Integer tipo = 1;
    private Integer codBandeiraCappta;
    private String numeroUnicoTransacao;
    private String respostaRequisicao;
    private String bandeira;
    private OpcoesPinpadEnum pinpadEnum = OpcoesPinpadEnum.CAPPTA;
    private String codigoAutorizacao;
    private String codigoNSU;
    private String adquirente;

    private Integer convenioCobranca;
    private Integer pinpadPedido;

    public String getAutorizacao() {
        try {
            if (this.getPinpadEnum().equals(OpcoesPinpadEnum.CAPPTA)) {
                JSONObject jsonObject = new JSONObject(this.getRespostaRequisicao());
                return jsonObject.optString("acquirerAuthorizationCode");
            } else {
                return this.getCodigoAutorizacao();
            }
        } catch (Exception e) {
            return "";
        }
    }

    public String getNsu(){
        try {
            if (this.getPinpadEnum().equals(OpcoesPinpadEnum.CAPPTA)) {
                JSONObject jsonObject = new JSONObject(this.getRespostaRequisicao());
                return jsonObject.optString("acquirerUniqueSequentialNumber");
            } else {
                return this.getCodigoNSU();
            }
        } catch (Exception e) {
            return "";
        }
    }


    public void setNrControleAut(MovPagamentoVO movPagamentoVO){
        try {
            if (this.getPinpadEnum().equals(OpcoesPinpadEnum.CAPPTA)) {
                JSONObject jsonObject = new JSONObject(movPagamentoVO.getRespostaRequisicaoPinpad());
                nrControle = jsonObject.optString("administrativeCode");
            } else {
                nrControle = "";
            }
        }catch (Exception e){
            nrControle = "";
        }
    }

    public Boolean getPinpadCappta() {
        return pinpadCappta;
    }

    public Boolean getParcelada() {
        return nrParcelas > 1;
    }

    public void setPinpadCappta(Boolean pinpadCappta) {
        this.pinpadCappta = pinpadCappta;
    }

    public Double getValorPinpad() {
        return valorPinpad;
    }

    public void setValorPinpad(Double valorPinpad) {
        this.valorPinpad = valorPinpad;
    }

    public Integer getNrParcelas() {
        return nrParcelas;
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public String getRetorno() {
        if(retorno != null && getPinpadEnum().equals(OpcoesPinpadEnum.CAPPTA)){
            retorno = retorno.replace("\n", "<br/>");
        }
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public Integer getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Integer formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getPdv() {
        return pdv;
    }

    public void setPdv(String pdv) {
        this.pdv = pdv;
    }

    public String getNrControle() {
        return nrControle;
    }

    public void setNrControle(String nrControle) {
        this.nrControle = nrControle;
    }

    public Integer getTipo() {
        return tipo;
    }

    public void setTipo(Integer tipo) {
        this.tipo = tipo;
    }

    public Integer getCodBandeiraCappta() {
        if (codBandeiraCappta == null) {
            codBandeiraCappta = 0;
        }
        return codBandeiraCappta;
    }

    public void setCodBandeiraCappta(Integer codBandeiraCappta) {
        this.codBandeiraCappta = codBandeiraCappta;
    }

    public String getNumeroUnicoTransacao() {
        return numeroUnicoTransacao;
    }

    public void setNumeroUnicoTransacao(String numeroUnicoTransacao) {
        this.numeroUnicoTransacao = numeroUnicoTransacao;
    }

    public String getRespostaRequisicao() {
        return respostaRequisicao;
    }

    public void setRespostaRequisicao(String respostaRequisicao) {
        this.respostaRequisicao = respostaRequisicao;
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }

    public OpcoesPinpadEnum getPinpadEnum() {
        if (pinpadEnum == null) {
            pinpadEnum = OpcoesPinpadEnum.CAPPTA;
        }
        return pinpadEnum;
    }

    public void setPinpadEnum(OpcoesPinpadEnum pinpadEnum) {
        this.pinpadEnum = pinpadEnum;
    }

    public String getCodigoAutorizacao() {
        return codigoAutorizacao;
    }

    public void setCodigoAutorizacao(String codigoAutorizacao) {
        this.codigoAutorizacao = codigoAutorizacao;
    }

    public String getCodigoNSU() {
        return codigoNSU;
    }

    public void setCodigoNSU(String codigoNSU) {
        this.codigoNSU = codigoNSU;
    }

    public OperadorasExternasAprovaFacilEnum getOperadorasExternasAprovaFacilEnum() {
        if (this.getBandeira() == null) {
            return null;
        }
        return OperadorasExternasAprovaFacilEnum.obterPorDescricao(this.getBandeira());
    }

    public String getAdquirenteGravar() {
        try {
            if (this.getPinpadEnum().equals(OpcoesPinpadEnum.CAPPTA)) {
                JSONObject jsonObject = new JSONObject(this.getRespostaRequisicao());
                return jsonObject.optString("acquirerName");
            } else if (this.getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                return "STONE";
            } else if (this.getPinpadEnum().equals(OpcoesPinpadEnum.GETCARD)) {
                return this.getAdquirente();
            }
            return "";
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(Integer convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Integer getPinpadPedido() {
        return pinpadPedido;
    }

    public void setPinpadPedido(Integer pinpadPedido) {
        this.pinpadPedido = pinpadPedido;
    }

    public String getDadosPinPadGravar() {
//        JSONObject json = new JSONObject();
//        json.put("convenioCobranca", this.convenioCobranca);
//        json.put("pinpadPedido", this.pinpadPedido);
//        json.put("pinpadTipo", this.getPinpadEnum().name());
//        json.put("respostaRequisicao", this.getRespostaRequisicao());
        PinpadDadosTO dadosTO = new PinpadDadosTO();
        dadosTO.setConvenioCobranca(this.convenioCobranca);
        dadosTO.setPinpadPedido(this.pinpadPedido);
        dadosTO.setPinpadTipo(this.getPinpadEnum().name());
        dadosTO.setRespostaRequisicao(this.getRespostaRequisicao());
        return dadosTO.toString();
    }

    public void setAdquirente(String adquirente) {
        this.adquirente = adquirente;
    }

    public String getAdquirente() {
        if (adquirente == null) {
            adquirente = "";
        }
        return adquirente;
    }
}
