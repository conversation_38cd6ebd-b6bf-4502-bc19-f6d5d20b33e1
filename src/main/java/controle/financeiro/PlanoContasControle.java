package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ReplicarRedeEmpresaEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.enumeradores.TipoRateioEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.ReplicarRedeEmpresaCallable;
import controle.plano.PlanoControle;
import negocio.comuns.arquitetura.PlanoContaRedeEmpresaVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.financeiro.PlanoConta;
import org.json.JSONArray;
import org.richfaces.model.TreeNode;
import org.richfaces.model.TreeNodeImpl;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.discovery.RedeDTO;
import servicos.impl.financeiroMs.FinanceiroMsService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.model.SelectItem;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.util.Objects.isNull;

public class PlanoContasControle extends SuperControle {

    private List<PlanoContaTO> lista;
    // navegação dos planos
    private PlanoContaTO plano;
    private PlanoContaTO planoDestino;
    private boolean disabledCampoPlanoConta = false;
    private Integer codigoTipoPadrao = 0;
    // exibição da tela
    private Boolean btExcluir;
    private Boolean btAlterarCodPlano;
    private Boolean btTrocarPlanoPai;
    private Boolean btTrocarPlanoPaiMarcado;
    // usado na suggestion box
    private String planoNome;
    private String planoNomeBoleto;
    private String planoNomePix;
    private PlanoContaTO planoEscolhidoPix;
    private PlanoContaTO planoEscolhido;
    private PlanoContaTO planoEscolhidoBoleto;
    // codigo do plano de contas para processar a seleção
    private int codigoBancoPlanoContas;
    //atributo da árvore
    private TreeNode rootNode = null;
    //atributo usado na consulta de planos especificando o tipo de consulta que será feita
    private String codigoPlanoPaiSelecionado;
    private Boolean alterarCodigo;
    private String codigoAntesAlteracao;
    private String msgExcluirTodos;
    private String fecharModalExclusao;
    private String onCompleteChangeSuggestionBoxPlanoContas = "";
    private Integer tabIndexSuggestionBoxPlanoContas;
    private boolean taxaBoleto = false;
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private boolean visaoReplicarEmpresa = false;

    public enum TipoConsulta {

        TODOS, SOMENTE_ENTRADA, SOMENTE_SAIDA
    };
    //por padrao o tipo de consulta é todos
    public static TipoConsulta tipoConsulta = TipoConsulta.TODOS;
    private Boolean mostrarComboTipoPadrao = true;
    private Boolean mostrarInputTipoPadrao = false;
    private Integer codigoEquivalenciaDRE = 0;
    private Boolean consultaEntrada;
    private Boolean consultaSaida;
    private List<PlanoContaTO> listaPlanos;
    private Integer codigoPlano;
    private EmpresaVO empresaRelatorio;
    private List<PlanoContaRedeEmpresaVO> listaPlanoContaRedeEmpresa;

    public String abrirPlanoConta() {
        try {
            configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            RateioIntegracaoControle controlRateio = (RateioIntegracaoControle) JSFUtilities.getFromSession(RateioIntegracaoControle.class.getSimpleName());
            if (controlRateio != null) {
                controlRateio.setIdTipoSelecionado(0);
            }
            this.setCodigoPlanoPaiSelecionado("");
            validarPermissaoPlanoContas();
            montarArvorePlano();
            setMensagemID("msg_dados_consultados");
            tipoConsulta = TipoConsulta.TODOS;
            setVisaoReplicarEmpresa(false);
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
            }
            return "planoConta";
        } catch (Exception e) {
            //montarMsgAlert usado para mostrar alert de permissão
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    /**
     * <AUTHOR>
     * 21/11/2011
     */
    public void montarArvorePlano() throws Exception {
        List<PlanoContaTO> todos = getFacade().getFinanceiro().getPlanoConta().consultarTodos();
        List<PlanoContaTO> arvorePlano = new ArrayList<PlanoContaTO>();

        for (PlanoContaTO plano : todos) {
            arvorePlano.add(plano);
            if (plano.getExisteRateio()) {
                PlanoContaTO planoRateio = new PlanoContaTO();
                planoRateio.setDescricao("Rateio de Centro de Custos");
                planoRateio.setRateio(Boolean.TRUE);
                planoRateio.setCodigoPlano(plano.getCodigoPlano() + ".999");
                planoRateio.setTipoPadrao(plano.getTipoPadrao());
//    			 planoRateio.setListaRateiosCentroCustos(plano.getListaRateiosCentroCustos());
                arvorePlano.add(planoRateio);
                PlanoContaTO listaRateio = new PlanoContaTO();
                listaRateio.setRateio(Boolean.TRUE);
                listaRateio.setCodigoPlano(planoRateio.getCodigoPlano() + ".999");
                listaRateio.setListaRateiosCentroCustos(plano.getListaRateiosCentroCustos());
                listaRateio.setTipoPadrao(plano.getTipoPadrao());
                arvorePlano.add(listaRateio);
            }
        }

        this.setListaPlanos(arvorePlano);
    }

    public boolean getDespesa(){
        return getPlano() != null && getCodigoTipoPadrao() != null && getCodigoTipoPadrao().equals(TipoES.SAIDA.getCodigo());
    }

    /**
     * Método que edita o plano de conta escolhido na árvore
     * @return
     */
    public void editar() throws SQLException, Exception {
        this.setAlterarCodigo(Boolean.FALSE);
        this.setCodigoAntesAlteracao("");
        PlanoContaTO obj = getFacade().getFinanceiro().getPlanoConta().obter(getCodigoPlano());
        ConfiguracaoFinanceiroVO confFinanVO = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar();
        int index = obj.getCodigoPlano().lastIndexOf(".");
        String codigoPai = index > 0 ? obj.getCodigoPlano().substring(0, index) : "";
        if (!UteisValidacao.emptyString(codigoPai)) {
            obj.setPlanoPai(getFacade().getFinanceiro().getPlanoConta().consultarPorCodigoPlano(codigoPai));
        }

        //planoPai só está preenchido caso seja um plano filho
        if (obj.getPlanoPai() != null && !UteisValidacao.emptyNumber(obj.getPlanoPai().getCodigo())) {
            setPlanoEscolhido(obj.getPlanoPai());
            //setPlanoExibicao(obj.getPlanoPai());
            setPlanoNome(obj.getPlanoPai().getDescricaoDetalhada());
        } else {
            setPlanoEscolhido(new PlanoContaTO());
            //setPlanoExibicao(new PlanoContaTO());
            setPlanoNome("");
        }
        setPlano(obj);
        setCodigoTipoPadrao(obj.getTipoPadrao().getCodigo());
        if (obj.getEquivalenciaDRE() != null) {
            setCodigoEquivalenciaDRE(obj.getEquivalenciaDRE().getCodigo());
        } else {
            setCodigoEquivalenciaDRE(0);
        }
        setDisabledCampoPlanoConta(true);
        setBtExcluir(obj.isLeaf());
        //usado no parâmetro "disabled" para renderizar o checkbox no JSP
        setBtAlterarCodPlano(obj.isLeaf());
        if (obj.isLeaf()) {
            setBtTrocarPlanoPai(true);
        } else {
            setBtTrocarPlanoPai(false);
        }
        if (obj.getCodigoPlano().length() == 3) {
            setMostrarInputTipoPadrao(false);
            setMostrarComboTipoPadrao(true);
        } else {
            setMostrarInputTipoPadrao(!confFinanVO.isPermitirTipoPlanoContaFilho());
            setMostrarComboTipoPadrao(confFinanVO.isPermitirTipoPlanoContaFilho());
        }
        setBtTrocarPlanoPaiMarcado(false);
        obj.setNovoObj(false);
        setMensagemID("msg_dados_editar");
        setSucesso(false);
        setErro(false);
        this.setCodigoAntesAlteracao(this.getPlano().getCodigoPlano());
    }

    /**
     * Método que cancela o processamento atual
     * e volta para a página de consulta
     * @return
     * @throws Exception 
     */
    public void cancelar() throws Exception {
        incluir();

    }

    public void voltar() throws Exception {
        PlanoContaTO novo = new PlanoContaTO();
        this.setPlano(novo);
        montarArvorePlano();
    }

    /**
     * Responsável por salvar a Alteracao do Codigo do Plano de contas, fazendo todas as validações necessárias 
     * e alterando em cascata os códigos dos filhos e irmãos 
     * <AUTHOR>
     * 22/11/2011
     */
    private void realizarAlteracaoCodigoPlano() throws Exception {

        String codigoPai = Uteis.obterCodigoPaiEntidadeFinanceiro(this.getPlano().getCodigoPlano());
        String codigoPaiAntesAlteracao = Uteis.obterCodigoPaiEntidadeFinanceiro(this.getCodigoAntesAlteracao());


        UteisValidacao.validaCodigoFinanceiro(this.getPlano().getCodigoPlano());
        //aqui inicializo um inteiro com o novo valor do codigo e com o valor antes da alteração
        int valorCodigoAlterar = Integer.parseInt(this.getPlano().getCodigoPlano().substring(this.getPlano().getCodigoPlano().length() - 3, this.getPlano().getCodigoPlano().length()));
        int valorCodigoAntesAlteracao = Integer.parseInt(this.getCodigoAntesAlteracao().substring(this.getCodigoAntesAlteracao().length() - 3, this.getCodigoAntesAlteracao().length()));

        //verificar se houve mudança de pai
        if (!codigoPai.equals(codigoPaiAntesAlteracao)) {
            valorCodigoAntesAlteracao = valorCodigoAlterar;
            validarMudancaCodigo(codigoPai);
            if (!codigoPai.equals("")) {
                this.getPlano().setTipoPadrao(TipoES.getTipoPadrao(getFacade().getFinanceiro().getPlanoConta().obterTipoES(codigoPai)));
            }
        }

        //verificar se o código novo existe ou está livre, caso esteja livre processar a alteração, alterando também os irmãos
        if (getFacade().getFinanceiro().getPlanoConta().verificarExistenciaPlano(this.getPlano().getCodigoPlano())) {
            //obter irmãos
            Map<Integer, String> codigosIrmaos = getFacade().getFinanceiro().getPlanoConta().obterCodigoPlanoIrmaos(codigoPai);
            //filhos dos irmãos
            Map<Integer, List<Integer>> codigosFilhos = new HashMap<Integer, List<Integer>>();
            Set<Integer> keySet = codigosIrmaos.keySet();
            //iterar nos irmãos, montando lista com códigos dos filhos
            for (Integer key : keySet) {
                String codIrmao = codigosIrmaos.get(key);
                codigosFilhos.put(key, getFacade().getFinanceiro().getPlanoConta().obterCodigoFilhos(codIrmao, this.getCodigoAntesAlteracao()));
            }
            //salvar a alteração de código de plano de contas, atualizando os filhos
            getFacade().getFinanceiro().getPlanoConta().atualizarCodigoPlanoContas(this.getPlano().getCodigo(), this.getPlano().getCodigoPlano(),
                    this.getCodigoAntesAlteracao(), getFacade().getFinanceiro().getPlanoConta().obterCodigoFilhos(this.getCodigoAntesAlteracao(), null),
                    this.getPlano().getTipoPadrao().getCodigo());
            //iterar nos planos do mesmo nivel, mudando a posição dos mesmos caso necessário
            for (Integer key : keySet) {
                String codIrmao = codigosIrmaos.get(key);
                int valorCodIrmao = Integer.parseInt(codIrmao.substring(codIrmao.length() - 3, codIrmao.length()));
                //se  valor novo é maior do que o valor antigo
                if (valorCodigoAlterar > valorCodigoAntesAlteracao) {
                    if (valorCodIrmao <= valorCodigoAlterar && valorCodIrmao > valorCodigoAntesAlteracao) {
                        //montar o novo código
                        String codigoFolha = Uteis.incrementarNumeroDoTipoString(codIrmao.substring(codIrmao.length() - 3, codIrmao.length()), -1);
                        if (codigoFolha.length() > 3) {
                            codigoFolha = Uteis.removerZeroAEsquerda(codigoFolha, codigoFolha.length() - 3);
                        }
                        String novoCodigo = codIrmao.substring(0, codIrmao.length() - 3) + codigoFolha;
                        //salvar a alteração de código de plano de contas, atualizando os filhos
                        getFacade().getFinanceiro().getPlanoConta().atualizarCodigoPlanoContas(key, novoCodigo, codIrmao, codigosFilhos.get(key), null);
                    }
                } else {
                    if ((valorCodIrmao >= valorCodigoAlterar && valorCodIrmao < valorCodigoAntesAlteracao)
                            || (valorCodIrmao >= valorCodigoAlterar && valorCodigoAlterar == valorCodigoAntesAlteracao)) {
                        //montar o novo código
                        String codigoFolha = Uteis.incrementarNumeroDoTipoString(codIrmao.substring(codIrmao.length() - 3, codIrmao.length()), 1);
                        if (codigoFolha.length() > 3) {
                            codigoFolha = Uteis.removerZeroAEsquerda(codigoFolha, codigoFolha.length() - 3);
                        }
                        String novoCodigo = codIrmao.substring(0, codIrmao.length() - 3) + codigoFolha;
                        //salvar a alteração de código de plano de contas, atualizando os filhos
                        getFacade().getFinanceiro().getPlanoConta().atualizarCodigoPlanoContas(key, novoCodigo, codIrmao, codigosFilhos.get(key), null);
                    }
                }
            }
            //caso não, salvar a alteração
        } else {
            getFacade().getFinanceiro().getPlanoConta().atualizarCodigoPlanoContas(this.getPlano().getCodigo(), this.getPlano().getCodigoPlano(),
                    this.getCodigoAntesAlteracao(), getFacade().getFinanceiro().getPlanoConta().obterCodigoFilhos(this.getCodigoAntesAlteracao(),
                    null), this.getPlano().getTipoPadrao().getCodigo());
        }
        this.setCodigoAntesAlteracao(this.getPlano().getCodigoPlano());
    }

    /**
     * <AUTHOR>
     * 24/11/2011
     * @throws Exception 
     * @throws ConsistirException 
     */
    private void validarMudancaCodigo(String pai) throws Exception {
        //se o código tiver um tamanho maior do que 3, indica que o mesmo possui pai, então verificar a existência do pai
        if (!pai.isEmpty()) {
            if (pai.equals(this.getCodigoAntesAlteracao()) || !getFacade().getFinanceiro().getPlanoConta().verificarExistenciaPlano(pai)) {
                throw new ConsistirException("Código do Plano de Contas não é válido, Plano Pai não existe.");
            }
        }
        //não pode mudar para um filho dele mesmo
        if (this.getPlano().filhoDe(this.getCodigoAntesAlteracao())) {
            throw new ConsistirException("Plano de Contas não pode se tornar um filho dele mesmo.");
        }
    }

    /**
     * Valida a permissão do usuário logado para a entidade RateioIntegracao
     * que usa a permissão "Plano de Contas"
     * @throws Exception
     */
    public void validarPermissaoPlanoContas() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "PlanoContas", "9.04 - Plano de Contas");
            }
        }
    }

    /**
     * Preenche a combo de Tipo Padrão - Entrada ou Saida
     * @return
     */
    public List<SelectItem> getListaSelectItemTipoPadrao() {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        try {
            TipoES[] listaTipos = TipoES.values();
            for (TipoES tipoES : listaTipos) {
                objs.add(new SelectItem(tipoES.getCodigo(), tipoES.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return objs;
    }

    /**
     * Preenche a combo de Tipo Padrão - Entrada ou Saida
     * @return
     */
    public List<SelectItem> getListaSelectItemTipoEquivalenciaDRE() {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        try {
            TipoEquivalenciaDRE[] listaTipos = TipoEquivalenciaDRE.values();
            for (TipoEquivalenciaDRE tipoDRE : listaTipos) {
            	if(tipoDRE.getTipo())
            		objs.add(new SelectItem(tipoDRE.getCodigo(), tipoDRE.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return objs;
    }

    /**
     * Retorna o código do plano pai
     * @param obj
     * @return
     */
    private String obterCodigoPai(PlanoContaTO obj) {
        int l = obj.getCodigoPlano().lastIndexOf(".");
        if (l == -1) {
            return "";
        } else {
            String codigoPai = obj.getCodigoPlano().substring(0, l);
            return codigoPai;
        }
    }

    /**
     * Consulta e monta a arvore novamente
     * @return
     */
    public String consultar() {
        tipoConsulta = TipoConsulta.TODOS;
        loadTree();
        return "consultar";
    }

    /**
     * Método que consulta uma lista de plano de contas e
     * monta a árvore
     */
    public void loadTree() {
        try {
            lista = getFacade().getFinanceiro().getPlanoConta().consultar(null, null,null, tipoConsulta.name());
            setarConsultaCorreta();
            if (UteisValidacao.emptyString(getMensagemID())
                    || getMensagemID().equals("msg_dados_consultados")) {
                setMensagemID("msg_dados_consultados");
                setSucesso(true);
                setErro(false);
            }
            rootNode = new TreeNodeImpl();
            addNodes(rootNode, lista);
        } catch (IOException e) {
            tratarEx(e);
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     * Responsável por verificar se consulta armazenada é a que se deseja visualizar
     *
     * <AUTHOR> 21/09/2011
     */
    public void verificarConsultaCorreta() {
        boolean consultaCorreta = true;
        if (tipoConsulta.equals(TipoConsulta.TODOS)) {
            if (!getConsultaEntrada() || !getConsultaSaida()) {
                consultaCorreta = false;
            }
        } else if (tipoConsulta.equals(TipoConsulta.SOMENTE_ENTRADA)) {
            if (!getConsultaEntrada() || getConsultaSaida()) {
                consultaCorreta = false;
            }
        } else if (tipoConsulta.equals(TipoConsulta.SOMENTE_SAIDA)) {
            if (getConsultaEntrada() || !getConsultaSaida()) {
                consultaCorreta = false;
            }
        }
        //caso a consulta armazenada não seja a que se deseja visualizar, efetuar a consulta novamente
        if (!consultaCorreta) {
            loadTree();
        }
    }
    
    public void verificarConsultaLancamento(){
        try{
            ConfiguracaoFinanceiroVO confFinanVO = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar();
            if(!tipoConsulta.equals(TipoConsulta.TODOS)){
                MovContaControle controle = (MovContaControle)getControlador(MovContaControle.class);
                TipoOperacaoLancamento tipoOperacaoLancamento = controle.getMovContaVO().getTipoOperacaoLancamento();
                    if(tipoOperacaoLancamento != null && tipoOperacaoLancamento.equals(TipoOperacaoLancamento.PAGAMENTO) ){
                        tipoConsulta = TipoConsulta.SOMENTE_SAIDA;
                    }else if(tipoOperacaoLancamento != null && tipoOperacaoLancamento.equals(TipoOperacaoLancamento.RECEBIMENTO)){
                        tipoConsulta = TipoConsulta.SOMENTE_ENTRADA;
                    }
                }
        }catch(Exception e){
            Uteis.logar(e, PlanoContasControle.class);
        }
        verificarConsultaCorreta();
    }

    public void verificarConsultaCorretaRateio() {
        tipoConsulta = TipoConsulta.TODOS;
        verificarConsultaCorreta();
    }

    /**
     * <AUTHOR>
     * 21/09/2011
     */
    private void setarConsultaCorreta() {
        if (tipoConsulta.equals(TipoConsulta.TODOS)) {
            setConsultaEntrada(true);
            setConsultaSaida(true);

        } else if (tipoConsulta.equals(TipoConsulta.SOMENTE_ENTRADA)) {
            setConsultaEntrada(true);
            setConsultaSaida(false);

        } else if (tipoConsulta.equals(TipoConsulta.SOMENTE_SAIDA)) {
            setConsultaEntrada(false);
            setConsultaSaida(true);
        }

    }

    private boolean codigoInvalido() throws ConsistirException {
        String[] codigos = this.getPlano().getCodigoPlano().split("\\.");
        for (String grupo : codigos) {
            if (grupo.equals("000")) {
                return true;
            }
        }
        return false;
    }

    public void salvar() {
        try {
            if (codigoInvalido()){
                throw new ConsistirException("Nenhum nível não pode ser 000");
            }

            if (!UteisValidacao.emptyString(this.getCodigoAntesAlteracao())
                    && !UteisValidacao.emptyString(this.getPlano().getCodigoPlano())
                    && !this.getCodigoAntesAlteracao().equals(this.getPlano().getCodigoPlano())
                    && !codigoInvalido()) {
                realizarAlteracaoCodigoPlano();

            }
            String codigoPai = "";
            if (!UteisValidacao.emptyString(this.getPlanoNome()) && UteisValidacao.emptyString(Uteis.obterCodigoPaiEntidadeFinanceiro(this.getPlano().getCodigoPlano()))) {
                throw new ConsistirException("Código do Plano de Contas não é válido, Plano Pai não existe.");
            } else {
                codigoPai = Uteis.obterCodigoPaiEntidadeFinanceiro(this.getPlano().getCodigoPlano());
            }
            if (mostrarComboTipoPadrao) {
                plano.setTipoPadrao(TipoES.getTipoPadrao(getCodigoTipoPadrao()));
            }
            if (planoEscolhido != null) {
                if (planoEscolhido != null) {
                    plano.setPlanoPai(planoEscolhido);
                }
            }
            if (this.getPlanoNome().isEmpty()) {
                if (getPlano().getCodigoPlano().isEmpty()) {
                    this.getPlano().setCodigoPlano("" + getFacade().getFinanceiro().getPlanoConta().consultarCodigoPaiContendoSomenteUmNumeroEAdicionaUm());
                }
            }
            if (getCodigoEquivalenciaDRE() == 0) {
                plano.setEquivalenciaDRE(null);
            } else {
                plano.setEquivalenciaDRE(TipoEquivalenciaDRE.getTipoEquivalenciaDRE(getCodigoEquivalenciaDRE()));
            }
            
            if(!UteisValidacao.emptyString(plano.getMetaDesc())){
            	Double meta = Formatador.obterValorNumerico(plano.getMetaDesc());
                plano.setMeta(meta);
            } else {
                plano.setMeta(0.0);
                plano.setMetaDesc("0,00");
            }

            if(!UteisValidacao.emptyString(plano.getPercGastoPretendidoDesc())){
            	Double percGas = Formatador.obterValorNumerico(plano.getPercGastoPretendidoDesc());
                plano.setPercGastoPretendido(percGas);
            } else {
                 plano.setPercGastoPretendido(0.0);
                 plano.setPercGastoPretendidoDesc("0,00");
            }

            if (plano.getMeta() > 100){
                throw new ConsistirException("Meta DRE não pode ser um valor maior que 100%");
            }

            List<PlanoContaTO> irmaosPlanoContas = getFacade().getFinanceiro().getPlanoConta().consultarPlanoFilhos(plano.getPlanoPai());

            if (irmaosPlanoContas.size() > 0) {
                double somaMetasIrmaos = 0;
                for(PlanoContaTO item: irmaosPlanoContas) {
                    if (plano.getCodigo().intValue() == item.getCodigo().intValue()){
                        item.setMeta(plano.getMeta());
                    }
                    double validarNull = (item.getMeta() == null ? 0 : item.getMeta());
                    somaMetasIrmaos = somaMetasIrmaos + validarNull;
                }
                if (somaMetasIrmaos > 100 && somaMetasIrmaos < 200){
                    throw new ConsistirException("A soma de Meta DRE dos filhos, não pode ser maior que 100%");
                } else if (somaMetasIrmaos > 200) {
                    for(PlanoContaTO item: irmaosPlanoContas) {
                        item.setMeta(0.0);
                        item.setPlanoPai(plano.getPlanoPai());
                        getFacade().getFinanceiro().getPlanoConta().alterarSemCommit(item);
                    }
                    montarArvorePlano();
                    throw new ConsistirException("A soma da Meta DRE dos filhos, ultrapassou 200%, por isso todas as metas foram editadas para 0%");
                }
            }

            if (plano.getNovoObj()) {
                getFacade().getFinanceiro().getPlanoConta().incluir(plano);
            } else {
                getFacade().getFinanceiro().getPlanoConta().alterarSemCommit(plano);
            }
            tipoConsulta = TipoConsulta.TODOS;
            this.setErro(false);
            this.setSucesso(true);
            setMensagemID("msg_dados_gravados");
            montarArvorePlano();

            if(isExibirReplicarRedeEmpresa()){
                replicarAutomaticoTodas();
            }
        } catch (Exception e) {
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            this.setErro(true);
            this.setSucesso(false);
        }
    }

    /**
     * Método que exclui o plano de conta escolhido
     * @return
     */
    public void excluir() {
        try {
            excluirSemTratar();
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     * Responsável por
     * <AUTHOR> Alcides
     * 05/03/2013
     */
    private void excluirSemTratar() throws Exception {
        this.setFecharModalExclusao("");
        try {
            if (getFacade().getFinanceiro().getPlanoConta().verificarExisteRelacionamento(this.getPlano().getCodigo())) {
                setPlanoEscolhido(new PlanoContaTO());
                setPlanoNome("");
                this.setFecharModalExclusao("Richfaces.showModalPanel('modalTrocarPlano')");

            } else {
                getFacade().getFinanceiro().getPlanoConta().excluir(this.getPlano());
                incluir();
                setMensagemID("msg_dados_excluidos");
                setSucesso(true);
                setErro(false);
                tipoConsulta = TipoConsulta.TODOS;
                montarArvorePlano();
               // this.setFecharModalExclusao("Richfaces.hideModalPanel('painelEdicaoPlanoContas');");
            }
        } catch (Exception e) {
           throw new Exception(e.getMessage());
       }

    }

    public void excluirTrocando() {
        try {
            setMsgAlert("");
            if (UteisValidacao.emptyNumber(getPlanoEscolhido().getCodigo())) {
                throw new ConsistirException("Informe o Plano de Contas.");
            }
            getFacade().getFinanceiro().getPlanoConta().trocarPlanoContas(getPlano().getCodigo(), getPlanoEscolhido().getCodigo());
            excluirSemTratar();
            setMsgAlert("Richfaces.hideModalPanel('modalTrocarPlano');");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void setarPlanoPaiVazio() {
        try {
            if (this.planoNome == null || this.planoNome.isEmpty()) {
                if (UteisValidacao.nenhumNulo(getPlano())) {
                    getPlano().setCodigoPlano("");
                }
                setPlanoEscolhido(new PlanoContaTO());
                setMostrarComboTipoPadrao(true);
                setMostrarInputTipoPadrao(false);
            }
            if(this.planoNomeBoleto == null || this.planoNomeBoleto.isEmpty()){
                if (UteisValidacao.nenhumNulo(getPlano())) {
                    getPlano().setCodigoPlano("");
                }
                setPlanoEscolhidoBoleto(new PlanoContaTO());
                setMostrarComboTipoPadrao(true);
                setMostrarInputTipoPadrao(false);
            }
            if(this.planoNomePix == null || this.planoNomePix.isEmpty()){
                if (UteisValidacao.nenhumNulo(getPlano())) {
                    getPlano().setCodigoPlano("");
                }
                setPlanoEscolhidoPix(new PlanoContaTO());
                setMostrarComboTipoPadrao(true);
                setMostrarInputTipoPadrao(false);
            }
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     * Método que exclui todos os planos do banco
     */
    public void excluirTodosPlanos() {
        try {
            getFacade().getFinanceiro().getPlanoConta().excluirTodosPlanos();
            setMensagemID("msg_dados_excluidos");
            setMsgExcluirTodos("");
            setSucesso(true);
            setErro(false);
            tipoConsulta = TipoConsulta.TODOS;
            montarArvorePlano();
            this.setPlano(new PlanoContaTO());
            this.setPlanoEscolhido(new PlanoContaTO());
            setPlanoNome("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            setMsgExcluirTodos("alert('Não foi possível excluir pois existem relacionamentos com rateios de integração e de contas.');");
        }
    }

    /**
     * Método que trata as mensagens caso ocorra erro
     * @param e
     */
    private void tratarEx(Exception e) {
        this.setLista(new ArrayList<PlanoContaTO>());
        this.setMensagemDetalhada("msg_erro", e.getMessage());
        setSucesso(false);
        setErro(true);
    }

    public void abrirReplicarEmpresa(){
        setVisaoReplicarEmpresa(true);
    }

    public void voltarPlanoContas(){
        setVisaoReplicarEmpresa(false);
    }

    /**
     * Método que monta os objetos para adicionar um plano de conta
     * @return
     */
    public void incluir() {
        try {
            this.setVisaoReplicarEmpresa(false);
            this.setCodigoAntesAlteracao("");
            //objetos usados no campo de Plano Pai
            this.setPlanoNome("");
            this.setPlanoEscolhido(new PlanoContaTO());
            // this.setPlanoExibicao(new PlanoContaTO());
            //objeto usado no cadastro de plano
            this.setPlano(new PlanoContaTO());
            getPlano().setNovoObj(true);
            //limpando campos
            setCodigoTipoPadrao(0);
            setDisabledCampoPlanoConta(false);
            setBtExcluir(false);
            setBtTrocarPlanoPai(false);
            setMostrarComboTipoPadrao(true);
            setMostrarInputTipoPadrao(false);
            setCodigoEquivalenciaDRE(0);
            RateioIntegracaoControle controlRateio = (RateioIntegracaoControle) JSFUtilities.getFromSession(RateioIntegracaoControle.class.getSimpleName());
            if (controlRateio == null) {
                controlRateio = new RateioIntegracaoControle();
            }
            controlRateio.setAdicaoRateio(true);
            controlRateio.setEdicaoRateio(false);
            controlRateio.setRateiosEdicao(new ArrayList<RateioIntegracaoTO>());
            JSFUtilities.storeOnSession(RateioIntegracaoControle.class.getSimpleName(), controlRateio);
            setMensagemID("msg_rateio_plano_contas");
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     * Monta o objeto de plano escolhido no modal de plano de contas
     */
    public void processSelection() {
        try {
            PlanoContaTO obj = getFacade().getFinanceiro().getPlanoConta().obter(this.getCodigoBancoPlanoContas());
            if(isTaxaBoleto()){
                this.setPlanoEscolhidoBoleto(obj);
                this.setPlanoNomeBoleto(obj.getDescricaoCurta());
                this.setPlanoNomePix(obj.getDescricaoCurta());
            }else {
                this.setPlanoEscolhido(obj);
                this.setPlanoNome(obj.getDescricaoCurta());
            }
            //possui filho para o pai escolhido
            if (getPlano() != null) {
                ConfiguracaoFinanceiroVO confFinanVO = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar();
                String codigoProximoFilho = getFacade().getFinanceiro().getPlanoConta().obterCodigoProximoFilho(
                        obj.getCodigoPlano());
                getPlano().setCodigoPlano(codigoProximoFilho);
                setMostrarComboTipoPadrao(confFinanVO.isPermitirTipoPlanoContaFilho());
                getPlano().setTipoPadrao(getPlanoEscolhido().getTipoPadrao());
            }
            setTaxaBoleto(false);
        } catch (Exception e) {
            tratarEx(e);
        }
    }
    
    public void processSelectionRecebiveisAvulso() {
        try {
        	RecebivelAvulsoControle recebiveis = (RecebivelAvulsoControle) getControlador(RecebivelAvulsoControle.class.getSimpleName());
            PlanoContaTO obj = getFacade().getFinanceiro().getPlanoConta().obter(this.getCodigoBancoPlanoContas());
            recebiveis.getRecebivelAvulso().setPlanoContas(obj);
            recebiveis.setDescricaoPlano(obj.getCodigoPlano() +" - "+obj.getDescricao());
            
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     * Método que consulta plano de contas quando o usuário digita no suggestionBox
     * @param suggest
     * @return
     */
    public List<PlanoContaTO> executarAutocompletePesqPlanoContas(Object suggest) {
        List<PlanoContaTO> listaPlanoContas = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
            boolean somenteNumero = UteisValidacao.somenteNumeros(nomePesq);
            Integer codigo = null;
            if (somenteNumero){
                codigo = Integer.parseInt(nomePesq);
            }
            PlanoConta pj = new PlanoConta();
            if (somenteNumerosEPontos) {
                listaPlanoContas = pj.consultar(nomePesq, null,codigo, tipoConsulta.name());
            } else {
                listaPlanoContas = pj.consultar(null, nomePesq, codigo, tipoConsulta.name());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaPlanoContas;
    }

    public List<PlanoContaTO> executarAutocompletePesqPlanoContasSomenteSaida(Object suggest) {
        List<PlanoContaTO> listaPlanoContas = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
            boolean somenteNumero = UteisValidacao.somenteNumeros(nomePesq);
            Integer codigo = null;
            if (somenteNumero){
                codigo = Integer.parseInt(nomePesq);
            }
            PlanoConta pj = new PlanoConta();
            if (somenteNumerosEPontos) {
                listaPlanoContas = pj.consultar(nomePesq, null,codigo, TipoConsulta.SOMENTE_SAIDA.name());
            } else {
                listaPlanoContas = pj.consultar(null, nomePesq, codigo, TipoConsulta.SOMENTE_SAIDA.name());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaPlanoContas;
    }

    /**
     * Método que seleciona o plano de contas escolhido no suggestionBox
     * @throws SQLException
     * @throws Exception
     */
    public void selecionarPlanoContas() throws SQLException, Exception {
        selecionarPlano((PlanoContaTO) context().getExternalContext().getRequestMap().get("result"), false);
    }

    public void selecionarPlanoContasBoleto() throws SQLException, Exception {
        selecionarPlano((PlanoContaTO) context().getExternalContext().getRequestMap().get("resultBoleto"), true);
    }

    public void selecionarPlano(PlanoContaTO obj, boolean boleto)throws Exception{
        if(boleto){
            this.setPlanoEscolhidoBoleto(obj);
        }else {
            this.setPlanoEscolhido(obj);
        }
        //possui filho para o pai escolhido
        if (getPlano() != null) {
            String codigoProximoFilho = getFacade().getFinanceiro().getPlanoConta().obterCodigoProximoFilho(
                    obj.getCodigoPlano());
            getPlano().setCodigoPlano(codigoProximoFilho);
            setMostrarComboTipoPadrao(false);
            getPlano().setTipoPadrao(boleto ? getPlanoEscolhidoBoleto().getTipoPadrao() : getPlanoEscolhido().getTipoPadrao());
        }
    }

    public void selecionarPlanoContasPix() throws SQLException, Exception {
        selecionarPlanoPix((PlanoContaTO) context().getExternalContext().getRequestMap().get("resultPix"));
    }

    public void selecionarPlanoPix(PlanoContaTO obj)throws Exception{
        this.setPlanoEscolhidoPix(obj);
        //possui filho para o pai escolhido
        if (getPlano() != null) {
            String codigoProximoFilho = getFacade().getFinanceiro().getPlanoConta().obterCodigoProximoFilho(
                    obj.getCodigoPlano());
            getPlano().setCodigoPlano(codigoProximoFilho);
            setMostrarComboTipoPadrao(false);
            getPlano().setTipoPadrao(getPlanoEscolhidoPix().getTipoPadrao());
        }
    }

    /**
     * Método que permite habilitar o campo de plano pai
     * quando o usuário marcar o checkbox
     */
    public void habilitarCampoPlanoPai() {
        disabledCampoPlanoConta = !btTrocarPlanoPaiMarcado;
    }

    /**
     * Método chamado da árvore que retorna
     * a mesma para mostrar na tela
     * @return
     */
    public TreeNode getTreeNode() {
        return rootNode;
    }

    /**
     * Método que verifica qual nó será pai e qual será filho
     * e adiciona na árvore
     * @param node
     * @param properties
     */
    private void addNodes(TreeNode node,
            List<PlanoContaTO> properties) throws Exception {
        ConfiguracaoFinanceiroVO confFinanVO = getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar();
        TreeNodeImpl temporary = new TreeNodeImpl();
        TreeNodeImpl anterior = new TreeNodeImpl();

        int i = 1;
        for (PlanoContaTO obj : properties) {
            TreeNodeImpl nodeImpl = new TreeNodeImpl();
            nodeImpl.setData(obj);

            // o plano é um dos nos raizes da arvore
            if (obj.getCodigoPlano().length() <= 3) {
                node.addChild(i++, nodeImpl);
                temporary = nodeImpl;
            } else if ((obj.getCodigoPlano().length() <= 3) == false && confFinanVO.isPermitirTipoPlanoContaFilho() && isNull(anterior.getData()) && isNull(anterior.getParent())){
                node.addChild(i++, nodeImpl);
                temporary = nodeImpl;
            } else {
                if(isNull(anterior.getData()) && isNull(anterior.getParent())) continue;

                String codigoPai = obterCodigoPai(obj);

                String codigoAnterior = ((PlanoContaTO) anterior.getData()).getCodigoPlano();
                int l2 = codigoAnterior.lastIndexOf(".");
                String codigoPaiAnterior = l2 == -1 ? codigoAnterior.substring(
                        0, 3) : codigoAnterior.substring(0, l2);

                if (codigoAnterior.equals(codigoPai)) {
                    // o plano é um subnó no nó anterior
                    obj.setPlanoPai((PlanoContaTO) anterior.getData());
                    adicionarNodos(anterior, nodeImpl, i++);
                    temporary = (TreeNodeImpl) anterior.getParent();
                    /*System.out.println("Subnó do Nó anterior: " + obj.getCodigoPlano() + " "
                    + obj.getDescricao());*/
                } else {
                    // verificar se é irmão do nó anterior
                    if (codigoPaiAnterior.equals(codigoPai)) {
                        /*System.out.println("Subnó do Nó anterior: " + obj.getCodigoPlano() + " "
                        + obj.getDescricao());*/
                        obj.setPlanoPai((PlanoContaTO) anterior.getParent().getData());
                        adicionarNodos(anterior.getParent(), nodeImpl, i++);
                        //anterior = nodeImpl;
                    } else {
                        // adicionar a um nó anterior
                        // encontrar o pai.
                        TreeNodeImpl temporaryAnterior = temporary;
                        try {
                            ACHAR_PAI:
                            while (true) {
                                String obterCodigoPai = this.obterCodigoPai(obj);
                                PlanoContaTO temporarioPlano = (PlanoContaTO) temporary.getData();
                                if (temporarioPlano != null
                                        && obterCodigoPai.equals(temporarioPlano.getCodigoPlano())) {
                                    break ACHAR_PAI;
                                } else {
                                    temporary = (TreeNodeImpl) temporary.getParent();
                                }
                            }
                            obj.setPlanoPai((PlanoContaTO) temporary.getData());
                            adicionarNodos(temporary, nodeImpl, i++);
                            /*System.out.println("Adiciona um nó após mostrar subNó do nó anterior: "
                            + obj.getCodigoPlano() + " "
                            + obj.getDescricao());*/
                        } catch (Exception e) {
                            System.out.println("Erro"
                                    + e.getMessage() + "  " + obj.getCodigoPlano());
                            // TODO Auto-generated catch block
                            temporary = temporaryAnterior;
                            e.printStackTrace();
                        }
                    }
                    // não é irmão do nó anterior
                }
            }
            anterior = nodeImpl;
        }
    }

    /**
     * Método que adiciona o nó na árvore na ordem
     * @param pai
     * @param filho
     * @param i
     */
    private void adicionarNodos(TreeNode pai, TreeNode filho, int i) {
        pai.addChild(i, filho);
    }

    public void ratearCentroCustos() throws Exception {
        RateioIntegracaoControle controlRateio = (RateioIntegracaoControle) JSFUtilities.getFromSession(RateioIntegracaoControle.class.getSimpleName());
        if (controlRateio == null) {
            controlRateio = new RateioIntegracaoControle();
        }
        controlRateio.setRateiosEdicao(getFacade().getFinanceiro().getRateioIntegracao().consultarPlanoContaRateio(plano.getCodigo()));
        controlRateio.valorOneRadio = RateioIntegracaoControle.ValorRateioOneRadio.SomenteCentroCusto;
        controlRateio.setRateio(new RateioIntegracaoTO());
        controlRateio.getRateio().setTipoRateio(TipoRateioEnum.CENTRO_CUSTOS.getCodigo());
        controlRateio.getTipoPlano();
        controlRateio.getRateio().setDescricao("Plano de Conta: " + plano.getDescricao());
        controlRateio.setGravarPlanoContasRateio(true);
        controlRateio.setAbrirModalRateio(true);
        controlRateio.setMensagemID("");
        controlRateio.setMensagemDetalhada("");
        controlRateio.setAdicaoRateio(true);
        controlRateio.setEdicaoRateio(false);
        JSFUtilities.storeOnSession(RateioIntegracaoControle.class.getSimpleName(), controlRateio);
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaPlanoContaRedeEmpresa().clear();

            Map<String, PlanoContaRedeEmpresaVO> mapaUnidadesPorChave = new HashMap<>();

            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }
                PlanoContaRedeEmpresaVO planoContaRedeEmpresaVO = getFacade().getPlanoContaRedeEmpresa().consultarPorChaveEmpresaPlanoConta(redeDTO.getChave(), redeDTO.getEmpresaZw(), 0);
                if (planoContaRedeEmpresaVO == null) {
                    planoContaRedeEmpresaVO = new PlanoContaRedeEmpresaVO();
                    planoContaRedeEmpresaVO.setMensagemSituacao("AGUARDANDO REPLICAR PLANO CONTAS");
                }
                planoContaRedeEmpresaVO.setChaveDestino(redeDTO.getChave().toLowerCase().trim());
                planoContaRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                planoContaRedeEmpresaVO.setRedeDTO(redeDTO);

                // verifica se ja existe uma unidade com a msm chave
                if (!mapaUnidadesPorChave.containsKey(planoContaRedeEmpresaVO.getChaveDestino())) {
                    mapaUnidadesPorChave.put(planoContaRedeEmpresaVO.getChaveDestino(), planoContaRedeEmpresaVO);
                } else {
                    PlanoContaRedeEmpresaVO existente = mapaUnidadesPorChave.get(planoContaRedeEmpresaVO.getChaveDestino());
                    // Apenas substitui se a nova tiver empresaZw == 1 e a existente nao
                    if (redeDTO.getEmpresaZw() == 1 && existente.getRedeDTO().getEmpresaZw() != 1) {
                        mapaUnidadesPorChave.put(planoContaRedeEmpresaVO.getChaveDestino(), planoContaRedeEmpresaVO);
                    }
                }
            }
            getListaPlanoContaRedeEmpresa().addAll(mapaUnidadesPorChave.values());
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public boolean isExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean usuarioAdministrador = false;
        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }
        try {
            integranteFranqueadoraRedeEmpresa = OamdMsService.integranteFranqueadoraRedeEmpresa(getKey());
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (integranteFranqueadoraRedeEmpresa &&  (usuarioAdminPacto || usuarioAdministrador)) {
            return true;
        } else {
            return false;
        }
    }

    public void replicarAutomaticoTodas() {
        try {
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PlanoContaRedeEmpresaVO obj : getListaPlanoContaRedeEmpresa()) {
                if (obj.getDataAtualizacaoInformada()) {
                    obj.setResponsavelOperacao(getUsuarioLogado());
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PLANO_CONTA, null, null, null, null, null, null, null, obj));
                }
            }
            if (callableTasks.isEmpty()) {
                return;
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
        }
    }

    public Integer getListaPlanoContaRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (PlanoContaRedeEmpresaVO unid : getListaPlanoContaRedeEmpresa()) {
            if (unid.getDataAtualizacaoInformada()) {
                cont++;
            }
        }
        return cont;
    }

    public Integer getListaPlanoContaRedeEmpresaSize() {
        return getListaPlanoContaRedeEmpresa().size();
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PlanoContaRedeEmpresaVO obj : getListaPlanoContaRedeEmpresa()) {
                if (!obj.getDataAtualizacaoInformada()) {
                    obj.setResponsavelOperacao(getUsuarioLogado());
                    obj.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PLANO_CONTA, null, null, null, null, null, null, null, obj));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(PlanoContaRedeEmpresaVO obj : getListaPlanoContaRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (PlanoContaRedeEmpresaVO obj : getListaPlanoContaRedeEmpresa()) {
                if (obj.isSelecionado()) {
                    obj.setResponsavelOperacao(getUsuarioLogado());
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.PLANO_CONTA, null, null, null, null, null, null, null, obj));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarPlanoContaRedeEmpresaUnica(PlanoContaRedeEmpresaVO obj) {
        try {
            replicarPlanoContaRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());
            try {
                getFacade().getPlanoContaRedeEmpresa().alterarMensagemSituacao(0, getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());
            } catch (Exception e) {
            }
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarPlanoContaRedeEmpresa(PlanoContaRedeEmpresaVO obj) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(getKey());

        PlanoContaRedeEmpresaVO planoContaRedeEmpresaVO = getFacade().getPlanoContaRedeEmpresa().consultarPorChaveEmpresaPlanoConta(obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw(), 0);
        if (planoContaRedeEmpresaVO == null) {
            String urlOrigemFinanceiroMs = clientDiscoveryDataDTO.getServiceUrls().getFinanceiroMsUrl();

            JSONArray clonePlanoContasOrigem = FinanceiroMsService.clonarPlanoContas(urlOrigemFinanceiroMs, getKey());
            planoContaRedeEmpresaVO = new PlanoContaRedeEmpresaVO(0, getKey(), obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getPlanoContaRedeEmpresa().inserir(planoContaRedeEmpresaVO);
            String planoContaReplicado = FinanceiroMsService.replicarPlanoContas(clonePlanoContasOrigem, urlOrigemFinanceiroMs, obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString(), false);
            if (planoContaReplicado.equals("sucesso")) {
                planoContaRedeEmpresaVO.setChaveDestino(obj.getRedeDTO().getChave());
                planoContaRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
                planoContaRedeEmpresaVO.setDataatualizacao(new Date());
                planoContaRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(planoContaRedeEmpresaVO.getDataatualizacao()));
                planoContaRedeEmpresaVO.setPlanoContaReplicado(0);
                obj.setChaveDestino(obj.getRedeDTO().getChave());
                obj.setDataatualizacao(new Date());
                obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
                getFacade().getPlanoContaRedeEmpresa().alterarDataAtualizacao(0, getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), 0, obj.getRedeDTO().getEmpresaZw());
                FinanceiroMsService.alterarBloqueioCriacaoPlanoConta(urlOrigemFinanceiroMs, obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString(), true);
            } else {
                throw new Exception(planoContaReplicado);
            }
        } else {
            String urlOrigemFinanceiroMs = clientDiscoveryDataDTO.getServiceUrls().getFinanceiroMsUrl();
            JSONArray clonePlanoContasOrigem = FinanceiroMsService.clonarPlanoContas(urlOrigemFinanceiroMs, getKey());
            String planoContaReplicado = FinanceiroMsService.replicarPlanoContas(clonePlanoContasOrigem, urlOrigemFinanceiroMs, obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString(),
                    (!UteisValidacao.emptyString(planoContaRedeEmpresaVO.getMensagemSituacao()) && planoContaRedeEmpresaVO.getMensagemSituacao().startsWith("REPLICADO EM")));
            if (planoContaReplicado.equals("sucesso")) {
                planoContaRedeEmpresaVO.setChaveDestino(obj.getRedeDTO().getChave());
                planoContaRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
                planoContaRedeEmpresaVO.setDataatualizacao(new Date());
                planoContaRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(planoContaRedeEmpresaVO.getDataatualizacao()));
                planoContaRedeEmpresaVO.setPlanoContaReplicado(0);
                obj.setChaveDestino(obj.getRedeDTO().getChave());
                obj.setDataatualizacao(new Date());
                obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
                getFacade().getPlanoContaRedeEmpresa().alterarDataAtualizacao(0, getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), 0, obj.getRedeDTO().getEmpresaZw());
                FinanceiroMsService.alterarBloqueioCriacaoPlanoConta(urlOrigemFinanceiroMs, obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString(), true);
            } else {
                throw new Exception(planoContaReplicado);
            }
        }


    }

    public void replicarPlanoContaRedeEmpresaGeral() {
        PlanoContaRedeEmpresaVO obj = (PlanoContaRedeEmpresaVO) context().getExternalContext().getRequestMap().get("planoContaRedeEmpresaReplicacao");
        try {
            obj.setResponsavelOperacao(getUsuarioLogado());
        } catch (Exception ex) {
        }
        replicarPlanoContaRedeEmpresaUnica(obj);
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        PlanoContaRedeEmpresaVO obj = (PlanoContaRedeEmpresaVO) context().getExternalContext().getRequestMap().get("planoContaRedeEmpresaReplicacao");
        try {
            obj.setDataatualizacao(null);
            obj.setMensagemSituacao("AGUARDANDO REPLICAR PLANO DE CONTAS");
            getFacade().getPlanoContaRedeEmpresa().limparDataAtualizacao(0, getKey(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getPlanoContaRedeEmpresa().alterarMensagemSituacao(0, getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());

            ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(getKey());
            FinanceiroMsService.alterarBloqueioCriacaoPlanoConta(clientDiscoveryDataDTO.getServiceUrls().getFinanceiroMsUrl(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString(), false);
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public boolean isBloquearCriacaoPlanoConta() {
        try {
            return getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isBloquearCriacaoPlanoConta();
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public void editandoTaxaBoleto(){
        setTaxaBoleto(true);
    }

    public Boolean getBtExcluir() {
        return btExcluir;
    }

    public void setBtExcluir(Boolean btExcluir) {
        this.btExcluir = btExcluir;
    }

    public Boolean getBtAlterarCodPlano() {
        return btAlterarCodPlano;
    }

    public void setBtAlterarCodPlano(Boolean btAlterarCodPlano) {
        this.btAlterarCodPlano = btAlterarCodPlano;
    }

    public PlanoContaTO getPlano() {
        return plano;
    }

    public void setPlano(PlanoContaTO plano) {
        this.plano = plano;
    }

    public PlanoContaTO getPlanoDestino() {
        return planoDestino;
    }

    public void setPlanoDestino(PlanoContaTO planoDestino) {
        this.planoDestino = planoDestino;
    }

    public List<PlanoContaTO> getLista() {
        tipoConsulta = tipoConsulta.TODOS;
        loadTree();
        return lista;
    }

    public void setLista(List<PlanoContaTO> lista) {
        this.lista = lista;
    }

    public String getPlanoNome() {
        return planoNome;
    }

    public void setPlanoNome(String planoNome) {
        this.planoNome = planoNome;
    }

    public PlanoContaTO getPlanoEscolhido() {
        if (planoEscolhido == null) {
            planoEscolhido = new PlanoContaTO();
        }
        return planoEscolhido;
    }

    public void setPlanoEscolhido(PlanoContaTO planoEscolhido) {
        this.planoEscolhido = planoEscolhido;
    }

    public int getCodigoBancoPlanoContas() {
        return codigoBancoPlanoContas;
    }

    public void setCodigoBancoPlanoContas(int codigoBancoPlanoContas) {
        this.codigoBancoPlanoContas = codigoBancoPlanoContas;
    }

    public boolean isDisabledCampoPlanoConta() {
        return disabledCampoPlanoConta;
    }

    public void setDisabledCampoPlanoConta(boolean disabledCampoPlanoConta) {
        this.disabledCampoPlanoConta = disabledCampoPlanoConta;
    }

    public Integer getCodigoTipoPadrao() {
        if (codigoTipoPadrao == null) {
            codigoTipoPadrao = 0;
        }
        return codigoTipoPadrao;
    }

    public void setCodigoTipoPadrao(Integer codigoTipoPadrao) {
        this.codigoTipoPadrao = codigoTipoPadrao;
    }

    public Boolean getBtTrocarPlanoPai() {
        return btTrocarPlanoPai;
    }

    public void setBtTrocarPlanoPai(Boolean btTrocarPlanoPai) {
        this.btTrocarPlanoPai = btTrocarPlanoPai;
    }

    public Boolean getBtTrocarPlanoPaiMarcado() {
        return btTrocarPlanoPaiMarcado;
    }

    public void setBtTrocarPlanoPaiMarcado(Boolean btTrocarPlanoPaiMarcado) {
        this.btTrocarPlanoPaiMarcado = btTrocarPlanoPaiMarcado;
    }

    public Boolean getMostrarComboTipoPadrao() {
        return mostrarComboTipoPadrao;
    }

    public void setMostrarComboTipoPadrao(Boolean mostrarComboTipoPadrao) {
        this.mostrarComboTipoPadrao = mostrarComboTipoPadrao;
    }

    public Boolean getMostrarInputTipoPadrao() {
        return mostrarInputTipoPadrao;
    }

    public void setMostrarInputTipoPadrao(Boolean mostrarInputTipoPadrao) {
        this.mostrarInputTipoPadrao = mostrarInputTipoPadrao;
    }

    /**
     * @return the codigoEquivalenciaDRE
     */
    public Integer getCodigoEquivalenciaDRE() {
        return codigoEquivalenciaDRE;
    }

    /**
     * @param codigoEquivalenciaDRE the codigoEquivalenciaDRE to set
     */
    public void setCodigoEquivalenciaDRE(Integer codigoEquivalenciaDRE) {
        this.codigoEquivalenciaDRE = codigoEquivalenciaDRE;
    }

    /**
     * @param consultaEntrada the consultaEntrada to set
     */
    public void setConsultaEntrada(Boolean consultaEntrada) {
        this.consultaEntrada = consultaEntrada;
    }

    /**
     * @return the consultaEntrada
     */
    public Boolean getConsultaEntrada() {
        if (consultaEntrada == null) {
            consultaEntrada = Boolean.FALSE;
        }
        return consultaEntrada;
    }

    /**
     * @param consultaSaida the consultaSaida to set
     */
    public void setConsultaSaida(Boolean consultaSaida) {
        this.consultaSaida = consultaSaida;
    }

    /**
     * @return the consultaSaida
     */
    public Boolean getConsultaSaida() {
        if (consultaSaida == null) {
            consultaSaida = Boolean.FALSE;
        }
        return consultaSaida;
    }

    /**
     * @param msgExcluirTodos the msgExcluirTodos to set
     */
    public void setMsgExcluirTodos(String msgExcluirTodos) {
        this.msgExcluirTodos = msgExcluirTodos;
    }

    /**
     * @return the msgExcluirTodos
     */
    public String getMsgExcluirTodos() {
        if (msgExcluirTodos == null) {
            msgExcluirTodos = "";
        }
        return msgExcluirTodos;
    }

    /**
     * @param listaPlanos the listaPlanos to set
     */
    public void setListaPlanos(List<PlanoContaTO> listaPlanos) {
        this.listaPlanos = listaPlanos;
    }

    /**
     * @return the listaPlanos
     */
    public List<PlanoContaTO> getListaPlanos() {
        return listaPlanos;
    }

    /**
     * @param codigoPlano the codigoPlano to set
     */
    public void setCodigoPlano(Integer codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    /**
     * @return the codigoPlano
     */
    public Integer getCodigoPlano() {
        return codigoPlano;
    }

    /**
     * @param codigoPlanoPaiSelecionado the codigoPlanoPaiSelecionado to set
     */
    public void setCodigoPlanoPaiSelecionado(String codigoPlanoPaiSelecionado) {
        this.codigoPlanoPaiSelecionado = codigoPlanoPaiSelecionado;
    }

    /**
     * @return the codigoPlanoPaiSelecionado
     */
    public String getCodigoPlanoPaiSelecionado() {
        return codigoPlanoPaiSelecionado;
    }

    /**
     * @param alterarCodigo the alterarCodigo to set
     */
    public void setAlterarCodigo(Boolean alterarCodigo) {
        this.alterarCodigo = alterarCodigo;
    }

    /**
     * @return the alterarCodigo
     */
    public Boolean getAlterarCodigo() {
        if (alterarCodigo == null) {
            alterarCodigo = Boolean.FALSE;
        }
        return alterarCodigo;
    }

    /**
     * @param codigoAntesAlteracao the codigoAntesAlteracao to set
     */
    public void setCodigoAntesAlteracao(String codigoAntesAlteracao) {
        this.codigoAntesAlteracao = codigoAntesAlteracao;
    }

    /**
     * @return the codigoAntesAlteracao
     */
    public String getCodigoAntesAlteracao() {
        return codigoAntesAlteracao;
    }

    /**
     * @param fecharModalExclusao the fecharModalExclusao to set
     */
    public void setFecharModalExclusao(String fecharModalExclusao) {
        this.fecharModalExclusao = fecharModalExclusao;
    }

    /**
     * @return the fecharModalExclusao
     */
    public String getFecharModalExclusao() {
        return fecharModalExclusao;
    }

    public String getDataAtual() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm",new Locale("pt","BR"));
        return sdf.format(Calendario.hoje());
    }

    public String getNomeUsuarioLogado() {
        String nomeUsuario = "";
        try {
            nomeUsuario = getUsuarioLogado().getNome();
        } catch (Exception e) {
        }
        return nomeUsuario;
    }

    /**
     * Metodo para capturar a foto da empresa para mostrar no relatorio de Plano de Contas
     * @throws java.io.IOException
     * @throws java.lang.Exception 
     */
    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        Integer codigoEmpresa = getEmpresaLogado().getCodigo();
        if(codigoEmpresa!=0)
           setEmpresaRelatorio(getFacade().getEmpresa().consultarPorChavePrimaria(
                codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getEmpresaRelatorio().setFotoRelatorio(getFacade().getEmpresa().obterFoto(
                getKey(), getEmpresaRelatorio().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        super.paintFotoRelatorio(out, getEmpresaRelatorio().getFotoRelatorio());
    }


    /**
     * @return the empresaRelatorio
     */
    public EmpresaVO getEmpresaRelatorio() {
        if(empresaRelatorio == null) empresaRelatorio = new EmpresaVO();
        return empresaRelatorio;
    }

    public void limpar() {
        this.setPlanoNome("");
        this.setPlanoEscolhido(new PlanoContaTO());
        this.setPlano(new PlanoContaTO());
    }

    /**
     * @param empresaRelatorio the empresaRelatorio to set
     */
    public void setEmpresaRelatorio(EmpresaVO empresaRelatorio) {
        this.empresaRelatorio = empresaRelatorio;
    }

    public Integer getTabIndexSuggestionBoxPlanoContas() {
        return tabIndexSuggestionBoxPlanoContas;
    }

    public void setTabIndexSuggestionBoxPlanoContas(Integer tabIndexSuggestionBoxPlanoContas) {
        this.tabIndexSuggestionBoxPlanoContas = tabIndexSuggestionBoxPlanoContas;
    }

    public String getOnCompleteChangeSuggestionBoxPlanoContas() {
        return onCompleteChangeSuggestionBoxPlanoContas;
    }

    public void setOnCompleteChangeSuggestionBoxPlanoContas(String onCompleteChangeSuggestionBoxPlanoContas) {
        this.onCompleteChangeSuggestionBoxPlanoContas = onCompleteChangeSuggestionBoxPlanoContas;
    }

    public PlanoContaTO getPlanoEscolhidoBoleto() {
        return planoEscolhidoBoleto;
    }

    public void setPlanoEscolhidoBoleto(PlanoContaTO planoEscolhidoBoleto) {
        this.planoEscolhidoBoleto = planoEscolhidoBoleto;
    }

    public String getPlanoNomeBoleto() {
        return planoNomeBoleto;
    }

    public void setPlanoNomeBoleto(String planoNomeBoleto) {
        this.planoNomeBoleto = planoNomeBoleto;
    }

    public String getPlanoNomePix() {
        return planoNomePix;
    }

    public void setPlanoNomePix(String planoNomePix) {
        this.planoNomePix = planoNomePix;
    }

    public PlanoContaTO getPlanoEscolhidoPix() {
        return planoEscolhidoPix;
    }

    public void setPlanoEscolhidoPix(PlanoContaTO planoEscolhidoPix) {
        this.planoEscolhidoPix = planoEscolhidoPix;
    }

    public boolean isTaxaBoleto() {
        return taxaBoleto;
    }

    public void setTaxaBoleto(boolean taxaBoleto) {
        this.taxaBoleto = taxaBoleto;
    }

    public boolean getConfiguracaoLumi() {
        return configuracaoSistemaVO != null && configuracaoSistemaVO.getLumi() != null && configuracaoSistemaVO.getLumi();
    }

    public boolean isVisaoReplicarEmpresa() {
        return visaoReplicarEmpresa;
    }

    public void setVisaoReplicarEmpresa(boolean visaoReplicarEmpresa) {
        this.visaoReplicarEmpresa = visaoReplicarEmpresa;
    }

    public List<PlanoContaRedeEmpresaVO> getListaPlanoContaRedeEmpresa() {
        if (listaPlanoContaRedeEmpresa == null) {
            listaPlanoContaRedeEmpresa = new ArrayList<>();
        }
        return listaPlanoContaRedeEmpresa;
    }

    public void setListaPlanoContaRedeEmpresa(List<PlanoContaRedeEmpresaVO> listaPlanoContaRedeEmpresa) {
        this.listaPlanoContaRedeEmpresa = listaPlanoContaRedeEmpresa;
    }
}
