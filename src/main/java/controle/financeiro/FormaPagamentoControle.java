package controle.financeiro;


import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.ce.comuns.enumerador.FormaCalculo;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoEmpresaVO;
import negocio.comuns.financeiro.FormaPagamentoPerfilAcessoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.TaxaBoletoVO;
import negocio.comuns.financeiro.TaxaCartaoVO;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoDebitoOnlineEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.FormaPagamento;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * formaPagamentoForm.jsp formaPagamentoCons.jsp) com as funcionalidades da classe <code>FormaPagamento</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see FormaPagamento
 * @see FormaPagamentoVO
 */
public class FormaPagamentoControle extends SuperControle {

    private static String NOME_ENTIDADE = "FORMA PAGAMENTO";
    private List<SelectItem> pinpads;
    private List<Integer> quantidadeNumeroParcelaNaListaSelecionadas;
    private List<SelectItem> adquirenteSelecionadas;
    private List<SelectItem> OperadoraSelecionadas;
    private List<Integer> quantidadeNumeroParcelaEmpresaNaListaSelecionadas;
    private List<SelectItem> adquirenteEmpresaSelecionadas;
    private List<SelectItem> OperadoraEmpresaSelecionadas;
    private Boolean apresentarTaxaCartaoDebito = false;
    private Boolean apresentarTaxaCartaoCredito = false;
    private Boolean apresentarTaxaBoleto = false;
    private FormaPagamentoVO formaPagamentoVO;
    private FormaPagamentoEmpresaVO formaPagamentoEmpresa;
    private FormaPagamentoPerfilAcessoVO formaPagamentoPerfilAcesso;
    private TaxaCartaoVO taxaCartaoVO = new TaxaCartaoVO();
    private TaxaCartaoVO taxaCartaoEmpresaVO = new TaxaCartaoVO();
    private TaxaBoletoVO taxaBoletoVO = new TaxaBoletoVO();
    private boolean editarSomenteDescricao = false;
    private List<TaxaCartaoVO> taxaCartaoVigencia;
    private List<TaxaCartaoVO> taxaCartaoVigenciaEmpresa;
    private List<TaxaCartaoVO> taxaCartaoAtual;
    private List<TaxaCartaoVO> taxaCartaoAtualEmpresa;
    private Integer nrMesesTaxaSelecionada;
    private Integer empresaSelecionada;
    private Integer contaSelecionada;
    private List<SelectItem> empresas;
    private List<EmpresaVO> empresasVO;
    private List<SelectItem> adquirentes;
    private List<SelectItem> operadoras;
    private List<SelectItem> contas;
    private List<TaxaCartaoVO> taxaCartaoDebito;
    private List<FormaPagamentoEmpresaVO> formasEmpresas;
    private List<FormaPagamentoPerfilAcessoVO> formasPerfilAcesso;
    private Integer codigoEmpresaRemover;
    private Integer codigoPinPadRemover;
    private String modalMensagemGenerica;
    private Map<String, List<TaxaCartaoVO>> taxasOrganizadas;
    private String agrupamento;
    private String tipo;
    private List<SelectItem> listaTipo = new ArrayList<SelectItem>();
    private Integer empSelecionadaPinPad;
    private List<SelectItem> perfisAcesso;
    private Integer codigoPerfilAcessoRemover;
    private boolean permiteAlterarTipoForma = true;
    private boolean compensacaoPorTaxa = false;
    private boolean apresentarEdicaoBoleto = false;
    private int nrDiasCompensacaoPorTaxa = 0;

    private ConfiguracaoSistemaVO configuracaoSistema;
    private PinPadVO pinPadVO;
    private List<ConvenioCobrancaVO> listaTodosConvenios;


    /**
     * Interface <code>FormaPagamentoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public FormaPagamentoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        inicializarConfiguracaoSistema();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>FormaPagamento</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        formasEmpresas = new ArrayList<FormaPagamentoEmpresaVO>();
        setFormaPagamentoPerfilAcesso(new FormaPagamentoPerfilAcessoVO());
        setFormaPagamentoVO(new FormaPagamentoVO());
        setTaxaCartaoVO(new TaxaCartaoVO());
        setTaxaBoletoVO(new TaxaBoletoVO());
        setPinPadVO(new PinPadVO());
        getTaxaCartaoVO().setVigenciaInicial(Calendario.hoje());
        setTaxaCartaoVigencia(new ArrayList<TaxaCartaoVO>());
        setTaxaCartaoAtual(new ArrayList<TaxaCartaoVO>());
        setTaxaCartaoDebito(new ArrayList<TaxaCartaoVO>());
        setNrMesesTaxaSelecionada(0);
        inicializarListasSelectItemTodosComboBox();
        setApresentarTaxaCartaoDebito(false);
        setApresentarTaxaCartaoCredito(false);
        setEditarSomenteDescricao(false);
        getFormaPagamentoVO().registrarObjetoVOAntesDaAlteracao();
        setQuantidadeNumeroParcelaNaListaSelecionadas(new ArrayList<Integer>());
        setOperadoraSelecionadas(new ArrayList<SelectItem>());
        setAdquirenteSelecionadas(new ArrayList<SelectItem>());
        apresentarEdicaoBoleto = false;
        permiteAlterarTipoForma = true;
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>FormaPagamento</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            FormaPagamentoVO obj = getFacade().getFormaPagamento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            obj.registrarTaxasAntesAlteracao();
            obj.registrarPinPadsAntesAlteracao();
            setFormaPagamentoVO(obj);
            setTaxaBoletoVO(new TaxaBoletoVO());
            setPinPadVO(new PinPadVO());
            getTaxaBoletoVO().setFormaPagamentoVO(formaPagamentoVO);
            apresentarEdicaoBoleto = false;
            setTaxaCartaoVO(new TaxaCartaoVO());
            getTaxaCartaoVO().setVigenciaInicial(Calendario.hoje());
            setTaxaCartaoVigencia(new ArrayList<TaxaCartaoVO>());
            setTaxaCartaoAtual(new ArrayList<TaxaCartaoVO>());
            setTaxaCartaoDebito(new ArrayList<TaxaCartaoVO>());
            setNrMesesTaxaSelecionada(0);
            setEditarSomenteDescricao(obj.getDefaultDCO() || obj.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla()));
            inicializarListasSelectItemTodosComboBox();
            redesenhaTela();
            montarListaTaxaAtual();
            setFormaPagamentoPerfilAcesso(new FormaPagamentoPerfilAcessoVO());
            permiteAlterarTipoForma = !getFacade().getMovPagamento().existeMovPagamentoFormaPagamento(formaPagamentoVO.getCodigo());
            if (permiteAlterarTipoForma &&
                    getFormaPagamentoVO().getDescricao().equalsIgnoreCase("CARTÃO RECORRENTE") &&
                    getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                permiteAlterarTipoForma = false;
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Forma de Pagamento",
                "Deseja excluir esta forma de pagamento?",
                this, "excluir", "", null, "", "form");
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>FormaPagamentoVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(FormaPagamentoVO obj) {
        if (obj.getConvenioCobrancaVO() == null) {
            obj.setConvenioCobrancaVO(new ConvenioCobrancaVO());
        }
    }

    public void adicionarTaxaBoleto() {
        try {
            setMsgAlert("");

            if (getTaxaBoletoVO().getTaxa() == 0) {
                throw new Exception("Informe a Taxa do Cartão.");
            }
            if(getTaxaBoletoVO().getVigenciaInicial() == null){
                throw new Exception("Informe a Vigência Inicial.");
            }else if(getTaxaBoletoVO().getVigenciaFinal() == null){
                throw new Exception("Informe a Vigência Final.");
            }
            getFacade().getTaxaBoleto().consultarPorFormaPagamento(getTaxaBoletoVO().getFormaPagamentoVO().getCodigo(),getTaxaBoletoVO().getEmpresa().getCodigo());
            getFacade().getTaxaBoleto().incluir(getTaxaBoletoVO());
            montarSucessoGrowl("Taxa adicionada com sucesso!");
            consultaTaxaBoleto();
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void alterarTaxaBoleto() {
        try {
            setMsgAlert("");

            if (getTaxaBoletoVO().getTaxa() == 0) {
                throw new Exception("Informe a Taxa do Cartão.");
            }
            if(getTaxaBoletoVO().getVigenciaInicial() == null){
                throw new Exception("Informe a Vigência Inicial.");
            }else if(getTaxaBoletoVO().getVigenciaFinal() == null){
                throw new Exception("Informe a Vigência Final.");
            }
            getFacade().getTaxaBoleto().consultarPorFormaPagamento(getTaxaBoletoVO().getFormaPagamentoVO().getCodigo(), getTaxaBoletoVO().getEmpresa().getCodigo());
            getFacade().getTaxaBoleto().alterar(getTaxaBoletoVO());
            montarSucessoGrowl("Taxa alterada com sucesso!");
            apresentarEdicaoBoleto = false;
            consultaTaxaBoleto();
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void adicionarTaxaEmpresa() {
        try {
            setMsgAlert("");

            if (!getFormaPagamentoVO().getTipoFormaPagamento().equals("CD") && getQuantidadeNumeroParcelaEmpresaNaListaSelecionadas().size() == 0) {
                throw new ConsistirException("Informe o número de parcelas.");
            }
            if (getAdquirenteEmpresaSelecionadas().size() == 0) {
                throw new ConsistirException("Informe o Adquirente");
            }
            if (getOperadoraEmpresaSelecionadas().size() == 0) {
                throw new ConsistirException("Informe a Operadora");
            }
            if (getTaxaCartaoEmpresaVO().getTaxa() == 0) {
                throw new Exception("Informe a Taxa do Cartão.");
            }
            if(getTaxaCartaoEmpresaVO().getVigenciaInicial() == null){
                throw new Exception("Informe a Vigência Inicial.");
            }else if(getTaxaCartaoEmpresaVO().getVigenciaFinal() == null){
                throw new Exception("Informe a Vigência Final.");
            }
            if (getFormaPagamentoVO().getTipoFormaPagamento().equals("CD")){
                setQuantidadeNumeroParcelaEmpresaNaListaSelecionadas(Collections.singletonList(1));
            }

            for(TaxaCartaoVO txe : taxaCartaoAtualEmpresa){
                if(getTaxaCartaoEmpresaVO().getAdquirenteVO().getCodigo().equals(txe.getAdquirenteVO().getCodigo())
                        && getTaxaCartaoEmpresaVO().getBandeira().getCodigo().equals(txe.getBandeira().getCodigo())
                        && getTaxaCartaoEmpresaVO().getNrmeses() == txe.getNrmeses()){
                    throw new Exception("Já existe uma taxa cadastrada com essa configuração.");
                }
            }

            int NumeroMesesEmpresa;
            int CodigoAdquirenteEmpresa;
            for (Integer numeroParcelaEmpresa : getQuantidadeNumeroParcelaEmpresaNaListaSelecionadas()) {
                NumeroMesesEmpresa = numeroParcelaEmpresa;
                for (SelectItem adquirenteEmpresa : getAdquirenteEmpresaSelecionadas()) {
                    CodigoAdquirenteEmpresa = (Integer) adquirenteEmpresa.getValue();
                    for (SelectItem operadoraEmpresa : getOperadoraEmpresaSelecionadas()) {
                        getTaxaCartaoEmpresaVO().setNrmeses(NumeroMesesEmpresa);
                        getTaxaCartaoEmpresaVO().getAdquirenteVO().setCodigo(CodigoAdquirenteEmpresa);
                        getTaxaCartaoEmpresaVO().getBandeira().setCodigo((Integer) operadoraEmpresa.getValue());
                        getFacade().getTaxaCartao().incluir(getTaxaCartaoEmpresaVO());
                    }
                }
            }

            taxaCartaoAtualEmpresa = getFacade().getTaxaCartao().consultarPorFormaPagamento(getFormaPagamentoVO().getCodigo(),
                    formaPagamentoEmpresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            getTaxaCartaoEmpresaVO().setCodigo(0);
            getTaxaCartaoEmpresaVO().setNrmeses(0);
            getTaxaCartaoEmpresaVO().setTaxa(null);
            setQuantidadeNumeroParcelaEmpresaNaListaSelecionadas(new ArrayList<Integer>());
            setAdquirenteEmpresaSelecionadas(new ArrayList<SelectItem>());
            setOperadoraEmpresaSelecionadas(new ArrayList<SelectItem>());
            montarListaSelectItemAdquirentes();
            montarListaSelectItemOperadoras();
            getTaxaCartaoEmpresaVO().getBandeira().setCodigo(0);
            getTaxaCartaoEmpresaVO().getAdquirenteVO().setCodigo(0);
            organizarTaxas();
            montarSucessoGrowl("Taxa adicionada com sucesso!");
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }

    }

    public void adicionarTaxa() {
        try {
            setErro(false);
            setSucesso(false);

            if(!isCompensacaoPorTaxa()){
                setNrDiasCompensacaoPorTaxa(0);
            }
            if (!getFormaPagamentoVO().getTipoFormaPagamento().equals("CD") && getQuantidadeNumeroParcelaNaListaSelecionadas().size() == 0) {
                throw new ConsistirException("Informe o número de parcelas.");
            } else if (getTaxaCartaoVO().getVigenciaInicial() == null) {
                throw new ConsistirException("Informe a vigência inicial.");
            } else if (getTaxaCartaoVO().getVigenciaFinal() == null) {
                throw new ConsistirException("Informe a vigência final.");
            } else if (getTaxaCartaoVO().getVigenciaFinal() != null) {
                if (Calendario.maior(getTaxaCartaoVO().getVigenciaInicial(), getTaxaCartaoVO().getVigenciaFinal())) {
                    throw new ConsistirException("A vigência inicial está superior a vigência final.");
                }
            }
            if (getTaxaCartaoVO().getTaxa() != null && getTaxaCartaoVO().getTaxa().doubleValue() >  100){
                throw new ConsistirException("A taxa deve ser menor que 100%.");
            }
            if (!getFormaPagamentoVO().getTipoFormaPagamento().equals("CD") && getTaxaCartaoVO().getTaxa() == 0) {
                throw new ConsistirException("A taxa deve ser informado.");
            }
            if (!getFormaPagamentoVO().getTipoFormaPagamento().equals("CD") && getAdquirenteSelecionadas().size() == 0) {
                throw new ConsistirException("Adquirente não foi informado");
            }
            if (!getFormaPagamentoVO().getTipoFormaPagamento().equals("CD") && getOperadoraSelecionadas().size() == 0) {
                throw new ConsistirException("Operadora não foi informado");
            }

            montarListaTaxaCartaoCredito();

            setOperadoraSelecionadas(new ArrayList<SelectItem>());
            setAdquirenteSelecionadas(new ArrayList<SelectItem>());
            montarListaSelectItemAdquirentes();
            montarListaSelectItemOperadoras();
            limparQuantidadeNumeroParcelaNaLista();

            setNrMesesTaxaSelecionada(getTaxaCartaoVO().getNrmeses());
            List<TaxaCartaoVO> listaVigencia = new ArrayList<TaxaCartaoVO>();
            for (TaxaCartaoVO taxaCartaoVO2 : getFormaPagamentoVO().getTaxasCartao()) {
                listaVigencia.add(taxaCartaoVO2);
            }
            setTaxaCartaoVigencia(listaVigencia);
            Ordenacao.ordenarListaReverse(getTaxaCartaoVigencia(), "vigenciaInicial");
            gravarTaxaVigencia();

            setTaxaCartaoVO(new TaxaCartaoVO());
            getTaxaCartaoVO().setVigenciaInicial(Calendario.hoje());
            getTaxaCartaoVO().getFormaPagamentoVO().setCodigo(getFormaPagamentoVO().getCodigo());
            setTaxaCartaoVigencia(new ArrayList<TaxaCartaoVO>());
            setNrMesesTaxaSelecionada(0);
            montarListaTaxaAtual();
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    private void montarListaTaxaCartaoCredito() {
        int NumeroMeses;
        int CodigoAdquirente;
        double taxaCartao = getTaxaCartaoVO().getTaxa();
        final Date vigenciaInicial = getTaxaCartaoVO().getVigenciaInicial();
        final Date vigenciaFinal = getTaxaCartaoVO().getVigenciaFinal();
        final String produtoVendaSesi = getTaxaCartaoVO().getProdutoVendaSesi();
        final String tipodocumentoSesi = getTaxaCartaoVO().getTipodocumentoSesi();
        for (Integer numeroParcela : getQuantidadeNumeroParcelaNaListaSelecionadas()) {
            NumeroMeses = numeroParcela;
            for (SelectItem adquirente : getAdquirenteSelecionadas()) {
                CodigoAdquirente = (Integer) adquirente.getValue();
                for (SelectItem operadora : getOperadoraSelecionadas()) {
                    taxaCartaoVO.setCompensacaoPorTaxa(compensacaoPorTaxa);
                    taxaCartaoVO.setNrmeses(NumeroMeses);
                    taxaCartaoVO.setTaxa(taxaCartao);
                    taxaCartaoVO.setNrDiasCompensacaoPorTaxa(nrDiasCompensacaoPorTaxa);
                    taxaCartaoVO.getAdquirenteVO().setCodigo(CodigoAdquirente);
                    taxaCartaoVO.getBandeira().setCodigo((Integer) operadora.getValue());
                    taxaCartaoVO.setVigenciaInicial(vigenciaInicial);
                    taxaCartaoVO.setVigenciaFinal(vigenciaFinal);
                    taxaCartaoVO.setProdutoVendaSesi(produtoVendaSesi);
                    taxaCartaoVO.setTipodocumentoSesi(tipodocumentoSesi);
                    getFormaPagamentoVO().getTaxasCartao().add(taxaCartaoVO);
                    taxaCartaoVO = new TaxaCartaoVO();
                }
            }
        }
    }

    public void limparAgrupamento(){
        agrupamento = "";
    }

    public void selecionarAgrupamentoTaxaEmpresaGeral(){
        agrupamento = "Geral";
    }

    public void selecionarAgrupamentoTaxaEmpresa(){
        agrupamento = (String) context().getExternalContext().getRequestMap().get("ag");
        setTaxaCartaoVigenciaEmpresa(new ArrayList<TaxaCartaoVO>());
    }

    public void removerTaxa() {
        TaxaCartaoVO taxaSelecionada = (TaxaCartaoVO) context().getExternalContext().getRequestMap().get("taxaCartao");

        List<TaxaCartaoVO> novaListaTaxaCartao = new ArrayList<TaxaCartaoVO>();
        for (TaxaCartaoVO taxaCartaoVO : getFormaPagamentoVO().getTaxasCartao()) {
            if (!(taxaCartaoVO.getNrmeses() == taxaSelecionada.getNrmeses() &&
                    taxaCartaoVO.getAdquirenteVO().getCodigo() == taxaSelecionada.getAdquirenteVO().getCodigo() &&
                    taxaCartaoVO.getBandeira().getCodigo() == taxaSelecionada.getBandeira().getCodigo())) {
                novaListaTaxaCartao.add(taxaCartaoVO);
            }
        }
        getFormaPagamentoVO().setTaxasCartao(novaListaTaxaCartao);
        montarListaTaxaAtual();
    }

    public void removerTaxaEmpresa() {
        try {
            TaxaCartaoVO taxaSelecionada = (TaxaCartaoVO) context().getExternalContext().getRequestMap().get("taxaCartaoEmpresa");
            getFacade().getTaxaCartao().excluir(taxaSelecionada);
            taxaCartaoAtualEmpresa = getFacade().getTaxaCartao().consultarPorFormaPagamento(getFormaPagamentoVO().getCodigo(),
                    formaPagamentoEmpresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getTaxaCartaoEmpresaVO().setCodigo(0);
            getTaxaCartaoEmpresaVO().setNrmeses(0);
            getTaxaCartaoEmpresaVO().setTaxa(null);
            organizarTaxas();
        }catch (Exception e){
            montarErro(e);
        }

    }

    public void removerTaxaBoleto() throws Exception {
        try {
            getFacade().getTaxaBoleto().excluir(taxaBoletoVO);
            taxaBoletoVO = new TaxaBoletoVO();
            taxaBoletoVO.setFormaPagamentoVO(formaPagamentoVO);
            taxaBoletoVO.setEmpresa(formaPagamentoEmpresa.getEmpresa());
            apresentarEdicaoBoleto = false;
            montarSucessoGrowl("Taxa removida com sucesso!");
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void selecionarTaxa() {
        TaxaCartaoVO taxaSelecionada = (TaxaCartaoVO) context().getExternalContext().getRequestMap().get("taxaCartao");
        try {
            setNrMesesTaxaSelecionada(taxaSelecionada.getNrmeses());

            List<TaxaCartaoVO> listaVigencia = new ArrayList<TaxaCartaoVO>();
            for (TaxaCartaoVO taxaCartaoVO : getFormaPagamentoVO().getTaxasCartao()) {
                listaVigencia.add(taxaCartaoVO);
            }
            setTaxaCartaoVigencia(listaVigencia);
            Ordenacao.ordenarListaReverse(getTaxaCartaoVigencia(), "vigenciaInicial");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void selecionarTaxaEmpresa() {
        TaxaCartaoVO taxaSelecionada = (TaxaCartaoVO) context().getExternalContext().getRequestMap().get("taxaCartaoEmpresa");
        try {
            int nrMeses = taxaSelecionada.getNrmeses();
            setTaxaCartaoVigenciaEmpresa(new ArrayList<TaxaCartaoVO>());
            for (TaxaCartaoVO taxaCartaoVO : taxasOrganizadas.get(agrupamento)) {
                if (taxaCartaoVO.getNrmeses() == nrMeses) {
                    getTaxaCartaoVigenciaEmpresa().add(taxaCartaoVO);
                }
            }
            Ordenacao.ordenarListaReverse(getTaxaCartaoVigenciaEmpresa(), "vigenciaInicial");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void selecionarTaxaBoleto() {
        apresentarEdicaoBoleto = true;
    }

    public void removerTaxaVigencia() {
        try {
            TaxaCartaoVO taxaSelecionada  = (TaxaCartaoVO) context().getExternalContext().getRequestMap().get("taxaCartaoVigencia");
            removerTaxaVigencia(taxaSelecionada, getTaxaCartaoVigencia(), false);
        }catch (Exception e){
            montarErro(e);
        }

    }

    public void removerTaxaVigenciaEmpresa() {
        try {
            TaxaCartaoVO taxaCartaoVigenciaEmpresa  = (TaxaCartaoVO) context().getExternalContext().getRequestMap().get("taxaCartaoVigenciaEmpresa");
            removerTaxaVigencia(taxaCartaoVigenciaEmpresa, getTaxaCartaoVigenciaEmpresa(), true);
            taxaCartaoAtualEmpresa = getFacade().getTaxaCartao().consultarPorFormaPagamento(getFormaPagamentoVO().getCodigo(),
                    formaPagamentoEmpresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            organizarTaxas();
        }catch (Exception e){
            montarErro(e);
        }

    }

    public void removerTaxaVigencia(TaxaCartaoVO taxaSelecionada, List<TaxaCartaoVO> lista, boolean empresa) throws Exception{

        lista.remove(taxaSelecionada);
        if(empresa){
            getFacade().getTaxaCartao().excluir(taxaSelecionada);
            return;
        }

        List<TaxaCartaoVO> novaListaTaxaCartao = new ArrayList<TaxaCartaoVO>();
        for (TaxaCartaoVO taxaCartaoVO : getFormaPagamentoVO().getTaxasCartao()) {
            if (taxaSelecionada.getCodigo() != taxaCartaoVO.getCodigo()) {
                novaListaTaxaCartao.add(taxaCartaoVO);
            }
        }
        getFormaPagamentoVO().setTaxasCartao(novaListaTaxaCartao);
        montarListaTaxaAtual();
        setNrMesesTaxaSelecionada(0);
    }

    public void removerTaxaDebito() {
        TaxaCartaoVO taxaSelecionada = (TaxaCartaoVO) context().getExternalContext().getRequestMap().get("taxaCartaoDebito");
        getTaxaCartaoDebito().remove(taxaSelecionada);
    }

    public void adicionarNovaTaxaVigencia() {
        adicionarNovaTaxaVigencia(getTaxaCartaoVigencia());
    }

    public void adicionarNovaTaxaVigenciaEmpresa() {
        adicionarNovaTaxaVigencia(getTaxaCartaoVigenciaEmpresa());
    }

    public void adicionarNovaTaxaVigencia(List<TaxaCartaoVO> lista) {
        try {
            setErro(false);
            setSucesso(false);
            int nrMeses = 0;
            OperadoraCartaoVO bandeira = null;
            AdquirenteVO adquirenteVO = null;
            Ordenacao.ordenarLista(lista, "vigenciaInicial");
            for (TaxaCartaoVO taxaCartaoVO : lista) {
                adquirenteVO = taxaCartaoVO.getAdquirenteVO();
                bandeira = taxaCartaoVO.getBandeira();
                nrMeses = taxaCartaoVO.getNrmeses();
                if (taxaCartaoVO.getVigenciaFinal() == null) {
                    if (Calendario.igual(Calendario.hoje(), taxaCartaoVO.getVigenciaInicial())) {
                        taxaCartaoVO.setVigenciaFinal(Calendario.hoje());
                        break;
                    } else {
                        taxaCartaoVO.setVigenciaFinal(Uteis.somarDias(Calendario.hoje(), -1));
                        break;
                    }

                }
            }

            TaxaCartaoVO novaTaxa = new TaxaCartaoVO();
            novaTaxa.getFormaPagamentoVO().setCodigo(getFormaPagamentoVO().getCodigo());
            novaTaxa.setNrmeses(nrMeses);
            novaTaxa.setVigenciaInicial(Calendario.hoje());
            lista.add(novaTaxa);

            Ordenacao.ordenarListaReverse(lista, "vigenciaInicial");

            setTaxaCartaoVO(new TaxaCartaoVO());
            getTaxaCartaoVO().setNrmeses(nrMeses);
            getTaxaCartaoVO().setVigenciaInicial(Calendario.hoje());
            setSucesso(true);
        } catch (Exception ex) {
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void adicionarNovaTaxaDebito() {
        try {
            setErro(false);
            setSucesso(false);

            Ordenacao.ordenarLista(getTaxaCartaoDebito(), "vigenciaInicial");
            for (TaxaCartaoVO taxaCartaoVO : getTaxaCartaoDebito()) {
                if (taxaCartaoVO.getVigenciaFinal() == null) {
                    if (Calendario.igual(Calendario.hoje(), taxaCartaoVO.getVigenciaInicial())) {
                        taxaCartaoVO.setVigenciaFinal(Calendario.hoje());
                        break;
                    } else {
                        taxaCartaoVO.setVigenciaFinal(Uteis.somarDias(Calendario.hoje(), -1));
                        break;
                    }

                }
            }

            TaxaCartaoVO novaTaxa = new TaxaCartaoVO();
            novaTaxa.getFormaPagamentoVO().setCodigo(getFormaPagamentoVO().getCodigo());
            novaTaxa.setNrmeses(getNrMesesTaxaSelecionada());
            novaTaxa.setVigenciaInicial(Calendario.hoje());

            getTaxaCartaoDebito().add(novaTaxa);

            Ordenacao.ordenarListaReverse(getTaxaCartaoDebito(), "vigenciaInicial");

            setTaxaCartaoVO(new TaxaCartaoVO());
            getTaxaCartaoVO().setNrmeses(getNrMesesTaxaSelecionada());
            getTaxaCartaoVO().setVigenciaInicial(Calendario.hoje());
            setSucesso(true);
        } catch (Exception ex) {
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void gravarTaxaVigenciaTela() {
        try {
            gravarTaxaVigencia();
            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("Clique em gravar para salvar as informações.");
        } catch (Exception ex) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada(ex);
        }

    }

    public void gravarTaxaVigenciaTelaEmpresa() {
        try {
            gravarTaxaVigenciaEmpresa();
        } catch (Exception ex) {
            montarErro(ex);
        }

    }

    public void gravarTaxaVigencia() throws Exception {
        gravarTaxaVigencia(taxaCartaoVigencia, false);
    }

    public void gravarTaxaVigenciaEmpresa() throws Exception {
        gravarTaxaVigencia(taxaCartaoVigenciaEmpresa, true);
        taxaCartaoVigenciaEmpresa = new ArrayList<TaxaCartaoVO>();
    }

    public void gravarTaxaVigencia(List<TaxaCartaoVO> lista, boolean empresa) throws Exception {


        //ORDENAR PARA VERIFICAR A LINHA DO TEMPO
        Ordenacao.ordenarListaReverse(lista, "vigenciaInicial");
        //VERIFICAR SE TODOS OS ITENS DA LISTA ESTÃO CORRETOS
        Integer qtdSemVigenciaFinal = 0;
        for (TaxaCartaoVO taxaCartaoVO : lista) {
            qtdSemVigenciaFinal = validarTaxaCartaoVOBasico(taxaCartaoVO, qtdSemVigenciaFinal);

            if(UteisValidacao.emptyNumber(taxaCartaoVO.getAdquirenteVO().getCodigo())){
                taxaCartaoVO.getAdquirenteVO().setNome("");
            }else{
                for(SelectItem i : getAdquirentes()){
                    if(taxaCartaoVO.getAdquirenteVO().getCodigo().equals(i.getValue())){
                        taxaCartaoVO.getAdquirenteVO().setNome(i.getLabel());
                        break;
                    }
                }
            }

            if(UteisValidacao.emptyNumber(taxaCartaoVO.getBandeira().getCodigo())){
                taxaCartaoVO.getBandeira().setDescricao("");
            }else{
                for(SelectItem i : getOperadoras()){
                    if(taxaCartaoVO.getBandeira().getCodigo().equals(i.getValue())){
                        taxaCartaoVO.getBandeira().setDescricao(i.getLabel());
                        break;
                    }
                }
            }

            //COMPARAR COM OUTROS ITENS DA LISTA PARA VERIFICAR AS VIGÊNCIAS
            validarNovaTaxa(lista, taxaCartaoVO);
        }

        if(empresa){
            for (TaxaCartaoVO taxaCartaoVO : lista) {
                taxaCartaoVO.setFormaPagamentoEmpresaVO(formaPagamentoEmpresa);
                if(UteisValidacao.emptyNumber(taxaCartaoVO.getCodigo())){
                    getFacade().getTaxaCartao().incluir(taxaCartaoVO);
                }else{
                    getFacade().getTaxaCartao().alterar(taxaCartaoVO);

                }
            }
            taxaCartaoAtualEmpresa = getFacade().getTaxaCartao().consultarPorFormaPagamento(getFormaPagamentoVO().getCodigo(),
                    formaPagamentoEmpresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            organizarTaxas();
            return;
        }


        //ORDENAR PARA VERIFICAR A LINHA DO TEMPO
        Ordenacao.ordenarListaReverse(lista, "vigenciaInicial");
        Double taxaAnt = 0.0;
        Date dataInicioAnt = null;
        Integer posicao = 0;
        int adquirenteAnt = 0;
        int bandeiraAnt = 0;
        for (TaxaCartaoVO taxa : getTaxaCartaoVigencia()) {
            if (posicao.equals(0)) {
                taxaAnt = taxa.getTaxa();
                dataInicioAnt = taxa.getVigenciaInicial();
                adquirenteAnt = taxa.getAdquirenteVO().getCodigo();
                bandeiraAnt = taxa.getBandeira().getCodigo();
            } else {
                boolean msmTaxa = Uteis.compareNullOrZero(taxa.getAdquirenteVO().getCodigo(), adquirenteAnt)
                        && Uteis.compareNullOrZero(taxa.getBandeira().getCodigo(), bandeiraAnt);

                //Verificar se a data final é um dia antes da data inicial anterior
                if (msmTaxa && !Calendario.maior(taxa.getVigenciaFinal(), dataInicioAnt)) {
                    throw new ConsistirException("A vigência final da taxa ( " + taxa.getTaxa() + " - Início: " + Uteis.getData(taxa.getVigenciaInicial()) +
                            " Final: " + Uteis.getData(taxa.getVigenciaFinal()) + " ) deve ser um dia antes da vigência inicial da taxa ( " + taxaAnt + " - Início: " + Uteis.getData(dataInicioAnt) + " )");

                }

                taxaAnt = taxa.getTaxa();
                dataInicioAnt = taxa.getVigenciaInicial();
                adquirenteAnt = taxa.getAdquirenteVO().getCodigo();
                bandeiraAnt = taxa.getBandeira().getCodigo();
            }
            posicao++;
        }

        setTaxaCartaoVO(new TaxaCartaoVO());
        getTaxaCartaoVO().getFormaPagamentoVO().setCodigo(getFormaPagamentoVO().getCodigo());
        getTaxaCartaoVO().setVigenciaInicial(Calendario.hoje());
        setTaxaCartaoVigencia(new ArrayList<TaxaCartaoVO>());
        montarListaTaxaAtual();
        getOperadoras();
        getAdquirentes();
    }

    private int validarTaxaCartaoVOBasico(TaxaCartaoVO taxaCartaoVO, int qtdSemVigenciaFinal) throws Exception{
        if (taxaCartaoVO.getVigenciaFinal() == null) {
            qtdSemVigenciaFinal++;
            if (qtdSemVigenciaFinal > 1) {
                throw new ConsistirException("Existe mais de uma taxa sem vigência final.");
            }
        }

        if (taxaCartaoVO.getVigenciaInicial() == null) {
            throw new ConsistirException("A taxa ( " + taxaCartaoVO.getTaxa() + " ) está sem vigência inicial.");
        }

        if(taxaCartaoVO.getTaxa() != null && taxaCartaoVO.getTaxa().doubleValue() > 100){
            throw new ConsistirException("A taxa deve ser menor que 100%.");
        }
        if (taxaCartaoVO.getVigenciaFinal() != null && Calendario.maior(taxaCartaoVO.getVigenciaInicial(), taxaCartaoVO.getVigenciaFinal())) {
            throw new ConsistirException("A taxa ( " + taxaCartaoVO.getTaxa() + " - Início: " + Uteis.getData(taxaCartaoVO.getVigenciaInicial()) +
                    " ) está com a vigência final ( " + Uteis.getData(taxaCartaoVO.getVigenciaFinal()) + " ) inferior a vigência inicial.");
        }

        return qtdSemVigenciaFinal;
    }

    private void validarTaxaCartaoVigenciaBasico() throws Exception{
        if(UteisValidacao.emptyList(taxaCartaoVigencia)) {
            return;
        }

        Integer qtdSemVigenciaFinal = 0;
        for (TaxaCartaoVO taxaCartaoVO : taxaCartaoVigencia) {
            qtdSemVigenciaFinal = validarTaxaCartaoVOBasico(taxaCartaoVO, qtdSemVigenciaFinal);
        }
    }

    private void validarNovaTaxa(List<TaxaCartaoVO> lista, TaxaCartaoVO taxaCartaoVO) throws ConsistirException {
        for (TaxaCartaoVO taxaCartaoVO1 : lista) {
            if (!taxaCartaoVO.equals(taxaCartaoVO1)) {

                if(!Uteis.compareNullOrZero(taxaCartaoVO1.getAdquirenteVO().getCodigo(), taxaCartaoVO.getAdquirenteVO().getCodigo())
                        || !Uteis.compareNullOrZero(taxaCartaoVO1.getBandeira().getCodigo(), taxaCartaoVO.getBandeira().getCodigo())){
                    continue;
                }
                if (taxaCartaoVO1.getNrmeses() == taxaCartaoVO.getNrmeses() &&
                        taxaCartaoVO1.getAdquirenteVO().getCodigo() == taxaCartaoVO.getAdquirenteVO().getCodigo() &&
                        taxaCartaoVO1.getBandeira().getCodigo() == taxaCartaoVO.getBandeira().getCodigo()) {

                    if (Calendario.igual(taxaCartaoVO1.getVigenciaInicial(), taxaCartaoVO.getVigenciaInicial())) {
                        throw new ConsistirException("Existem mais de uma taxa com a mesma vigência inicial.");
                    }

                    if ((taxaCartaoVO.getVigenciaFinal() != null && taxaCartaoVO1.getVigenciaFinal() != null) && (Calendario.igual(taxaCartaoVO1.getVigenciaFinal(), taxaCartaoVO.getVigenciaFinal()))) {
                        throw new ConsistirException("Existem mais de uma taxa com a mesma vigência final.");
                    }
                    if ((taxaCartaoVO.getVigenciaInicial() != null && taxaCartaoVO1.getVigenciaInicial() != null) && (taxaCartaoVO.getVigenciaFinal() != null && taxaCartaoVO1.getVigenciaFinal() != null)) {
                        if (Calendario.entre(taxaCartaoVO.getVigenciaInicial(), taxaCartaoVO1.getVigenciaInicial(), taxaCartaoVO1.getVigenciaFinal())) {
                            throw new ConsistirException("A taxa ( " + taxaCartaoVO.getTaxa() + " - Início: " + Uteis.getData(taxaCartaoVO.getVigenciaInicial()) +
                                    " ) está entre as datas da taxa ( " + taxaCartaoVO1.getTaxa() + " - Início: " + Uteis.getData(taxaCartaoVO1.getVigenciaInicial()) + " Final: " + Uteis.getData(taxaCartaoVO1.getVigenciaFinal()) + " )");
                        }
                    }

                    if ((taxaCartaoVO.getVigenciaInicial() != null && taxaCartaoVO1.getVigenciaFinal() != null)) {
                        if (Calendario.igual(taxaCartaoVO.getVigenciaInicial(), taxaCartaoVO1.getVigenciaFinal())) {
                            throw new ConsistirException("A vigência inicial ( " + Uteis.getData(taxaCartaoVO.getVigenciaInicial()) + " ) da taxa ( " + taxaCartaoVO.getTaxa() +
                                    " ) está igual a vigência final da taxa ( " + taxaCartaoVO1.getTaxa() + " - Início: " + Uteis.getData(taxaCartaoVO1.getVigenciaInicial()) + " Final: " + Uteis.getData(taxaCartaoVO1.getVigenciaFinal()) + " )");
                        }
                    }
                }
            }
        }
    }

    public void montarListaTaxaAtual() {
        if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
            setTaxaCartaoDebito(montarListaTaxaAtual(getFormaPagamentoVO().getTaxasCartao()));
        }else{
            setTaxaCartaoAtual(montarListaTaxaAtual(getFormaPagamentoVO().getTaxasCartao()));
        }
    }

    public List<TaxaCartaoVO> montarListaTaxaAtual(List<TaxaCartaoVO> taxas) {
        if(taxas == null){
            return new ArrayList<TaxaCartaoVO>();
        }

        if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
            Ordenacao.ordenarLista(taxas, "nrmeses");
            return taxas;
        }

        Map<String, TaxaCartaoVO> map = new HashMap<>();
        for (TaxaCartaoVO taxaCartaoVO : taxas) {

            String k = "nrMeses="+taxaCartaoVO.getNrmeses() +
                    ";codAdquirente="+taxaCartaoVO.getAdquirenteVO().getCodigo().toString()+
                    ";descOperadora="+taxaCartaoVO.getBandeira().getDescricao();

            if (!map.containsKey(k)) {
                map.put(k, taxaCartaoVO);
            } else {
                //TEM DATA FINAL E INICIAL
                if (taxaCartaoVO.getVigenciaInicial() != null
                        && taxaCartaoVO.getVigenciaFinal() != null) {
                    if (Calendario.maiorOuIgual(Calendario.hoje(), taxaCartaoVO.getVigenciaInicial()) && Calendario.menorOuIgual(Calendario.hoje(), taxaCartaoVO.getVigenciaFinal())) {
                        map.put(k, taxaCartaoVO);
                    }
                } else if (taxaCartaoVO.getVigenciaFinal() == null) {
                    if (Calendario.maiorOuIgual(Calendario.hoje(), taxaCartaoVO.getVigenciaInicial())) {
                        map.put(k, taxaCartaoVO);
                    }
                }
            }
        }
        List<TaxaCartaoVO> values = new ArrayList<>(map.values());
        Ordenacao.ordenarLista(values, "nrmeses");
        return values;
    }


    /**
     * author alcides
     * 24/03/2011
     */
    public String gravar() {
        return gravar(false);
    }

    /**
     * author alcides
     * 24/03/2011
     */
    public String gravarCE() {
        return gravar(true);
    }

    public void verificarTipoFormaPgto() {
        if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
            getFormaPagamentoVO().setSomenteFinanceiro(Boolean.FALSE);
            limparMsg();
            setMensagem("Formas de pagamento de tipo 'Crédito em Conta Corrente' não são usadas em lançamentos do Financeiro.");
        } else if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.LOTE.getSigla())) {
            getFormaPagamentoVO().setSomenteFinanceiro(Boolean.TRUE);
            limparMsg();
            setMensagem("Formas de pagamento de tipo 'Lote' serão usadas somente em lançamentos do Financeiro.");
        } else if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()) && getFormaPagamentoVO().isNovoObj()) {
            TaxaCartaoVO obj = new TaxaCartaoVO();
            obj.setNrmeses(0);
            obj.setTaxa(0.0);
            obj.setVigenciaInicial(Calendario.hoje());
            getTaxaCartaoDebito().add(obj);
            limparMsg();
        } else {
            limparMsg();
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>FormaPagamento</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            validarConvenioCobranca();
            validarTaxaPix();
            validarTaxaCartaoVigenciaBasico();
            validarPinpad();
            if (centralEventos) {
                this.verificarAutorizacao();
                if (formaPagamentoVO.isNovoObj()) {
                    getFacade().getFormaPagamento().incluir(formaPagamentoVO, true);
                    if(UteisValidacao.notEmptyNumber(formaPagamentoVO.getPinpad().getPinpad())  && formaPagamentoVO.getPinpad().getPinpad() == 1){
                        notificarRecursoEmpresa(RecursoSistema.ADICIONAR_CAPPTA_FORMA_PAGAMENTO);
                    }
                    getFacade().getFormaPagamentoEmpresa().incluir(formaPagamentoVO, formasEmpresas);
                    getFacade().getFormaPagamentoPerfilAcesso().incluir(formaPagamentoVO, getFormaPagamentoVO().getFormasPerfilAcesso());
                    //LOG - INICIO
                    try {
                        formaPagamentoVO.setObjetoVOAntesAlteracao(new FormaPagamentoVO());
                        formaPagamentoVO.setNovoObj(true);
                        registrarLogObjetoVO(formaPagamentoVO, formaPagamentoVO.getCodigo(), NOME_ENTIDADE, 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE FORMA PAGAMENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                } else {
                    if (formaPagamentoVO.getTipoFormaPagamento().equals("CD") && formaPagamentoVO.isExigeCodAutorizacao()) {
                        formaPagamentoVO.setExigeCodAutorizacao(false);
                    }
                    getFacade().getFormaPagamento().alterar(formaPagamentoVO, true);
                    getFacade().getFormaPagamentoEmpresa().incluir(formaPagamentoVO, formasEmpresas);
                    getFacade().getFormaPagamentoPerfilAcesso().incluir(formaPagamentoVO, getFormaPagamentoVO().getFormasPerfilAcesso());
                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(formaPagamentoVO, formaPagamentoVO.getCodigo(), NOME_ENTIDADE, 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE FORMA PAGAMENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
            } else {

                if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
                    setTaxaCartaoVigencia(getTaxaCartaoDebito());
                    gravarTaxaVigencia();
                }

                if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla())) {
                    getFormaPagamentoVO().setGerarPontos(false);
                }

                if (formaPagamentoVO.getDefaultRecorrencia() != null
                        && formaPagamentoVO.getDefaultRecorrencia()) {
                    formaPagamentoVO.setSomenteFinanceiro(Boolean.FALSE);
                }

                acoesPinPadCappta();

                if (formaPagamentoVO.isNovoObj()) {
                    getFacade().getFormaPagamento().incluir(formaPagamentoVO);
                    if(UteisValidacao.notEmptyNumber(formaPagamentoVO.getPinpad().getPinpad())  && formaPagamentoVO.getPinpad().getPinpad() == 1){
                        notificarRecursoEmpresa(RecursoSistema.ADICIONAR_CAPPTA_FORMA_PAGAMENTO);
                    }
                    getFacade().getFormaPagamentoEmpresa().incluir(formaPagamentoVO, formasEmpresas);
                    getFacade().getFormaPagamentoPerfilAcesso().incluir(formaPagamentoVO, getFormaPagamentoVO().getFormasPerfilAcesso());
                    getFacade().getPinPad().incluir(formaPagamentoVO, getFormaPagamentoVO().getListaPinPad());
                    incluirLogInclusao();
                } else {
                    if (!formaPagamentoVO.getTipoFormaPagamento().equals("CA") && !formaPagamentoVO.getTipoFormaPagamento().equals("CD")) {
                        formaPagamentoVO.setExigeCodAutorizacao(false);
                    }
                    getFacade().getFormaPagamento().alterar(formaPagamentoVO);
                    getFacade().getFormaPagamentoEmpresa().incluir(formaPagamentoVO, formasEmpresas);
                    getFacade().getFormaPagamentoPerfilAcesso().incluir(formaPagamentoVO, getFormaPagamentoVO().getFormasPerfilAcesso());
                    getFacade().getPinPad().incluir(formaPagamentoVO, getFormaPagamentoVO().getListaPinPad());
                    incluirLogAlteracao();
                }

                setTaxaCartaoVO(new TaxaCartaoVO());
                setTaxaCartaoVigencia(new ArrayList<TaxaCartaoVO>());

                setNrMesesTaxaSelecionada(0);
                montarListaTaxaAtual();
            }
//            getFormaPagamentoVO().registrarObjetoVOAntesDaAlteracao();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return null;
        }
    }

    private void acoesPinPadCappta() {
        if (!UteisValidacao.emptyList(formaPagamentoVO.getListaPinPad())) {
            boolean pinpadCappta = false;
            for (PinPadVO pinPadVO : formaPagamentoVO.getListaPinPad()) {
                if (pinPadVO.getOpcoesPinpadEnum() != null && pinPadVO.getOpcoesPinpadEnum().equals(OpcoesPinpadEnum.CAPPTA)) {
                    pinpadCappta = true;
                    break;
                }
            }
            if (pinpadCappta) {
                formaPagamentoVO.setPinpad(formaPagamentoVO.getListaPinPad().get(0));
            }
        }
    }

    private void validarTaxaPix() throws Exception {
        if (this.getFormaPagamentoVO().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.PIX.getSigla()) &&
                this.getFormaPagamentoVO().getTaxaPix() > 100.00) {
            throw new ConsistirException("A taxa do pix não pode ser maior que 100%");
        }
    }


    private void validarConvenioCobranca() throws Exception {
        if (getFormaPagamentoVO().getConvenioCobrancaVO() != null && getFormaPagamentoVO().getConvenioCobrancaVO().getCodigo() > 0) {
            ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(getFormaPagamentoVO().getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                if (!convenioCobrancaVO.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                    throw new ConsistirException("Não é permitido usar um Convênio de Cobrança que não seja do tipo Cartão de Crédito");
                }
            } else if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())) {
                if (!convenioCobrancaVO.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                    throw new ConsistirException("Não é permitido usar um Convênio de Cobrança que não seja do tipo Boleto Bancário");
                } else if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla())) {
                    if (!convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PIX)) {
                        throw new ConsistirException("Não é permitido usar um Convênio de Cobrança que não seja do tipo Pix");
                    }
                }
            } else {
                if (convenioCobrancaVO.getTipo().getTipoAutorizacao() != null) {
                    if (convenioCobrancaVO.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) ||
                            convenioCobrancaVO.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                        throw new ConsistirException("Não é permitido usar o Convênio de Cobrança selecionado para este tipo de forma de pagamento");
                    }
                }
            }
        }
    }

    private void validarPinpad() throws Exception {
        //No Caixa em Aberto, o pinpad Cappta gera um botao, o pinpad com convenio outro, os dois configurados na mesma Forma de Pagamento
        //exibe o botao do primeiro configurado e nao exibe do outro, por isso nao pode confugrar junto.
        int pinpadComConvenio = 0;
        int pinpadSemConvenio = 0;

        for (PinPadVO pinpad: getFormaPagamentoVO().getListaPinPad()) {
            if (UteisValidacao.emptyNumber(pinpad.getConvenioCobranca().getCodigo())) {
                pinpadSemConvenio += 1;
            } else {
                pinpadComConvenio += 1;
            }
        }

        if (pinpadComConvenio > 0 && pinpadSemConvenio > 0) {
            throw new ConsistirException("Cadastre uma Forma de Pagamento para os Pinpads Com Convênio e outra para os Pinpads Sem Convênio. Não pode cadastrar os dois na mesma Forma de Pagamento.");
        }

    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP FormaPagamentoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getFormaPagamento().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getFormaPagamento().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoConvenioCobranca")) {
                objs = getFacade().getFormaPagamento().consultarPorDescricaoConvenioCobranca(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoFormaPagamento")) {
                objs = getFacade().getFormaPagamento().consultarPorTipoFormaPagamento(getControleConsulta().getValorConsulta(), false, true, false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * author alcides
     * 24/03/2011
     */
    public String excluir() {
        return excluir(false);
    }

    /**
     * author alcides
     * 24/03/2011
     */
    public String excluirCE() {
        return excluir(true);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>FormaPagamentoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (isEditarSomenteDescricao()) {
                throw new ConsistirException("Essa Forma de Pagamento não pode ser excluída, pois é necessária para o funcionamento do sistema");
            }
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getFormaPagamento().excluir(formaPagamentoVO, true);
                //registrar log
                registrarLogExclusaoObjetoVO(formaPagamentoVO, formaPagamentoVO.getCodigo(), "FORMA PAGAMENTO", 0);
            } else {
                getFacade().getFormaPagamento().excluir(formaPagamentoVO);
            }

            setFormaPagamentoVO(new FormaPagamentoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoFormaPagamento</code>
     */
    public List<SelectItem> getListaSelectItemTipoFormaPagamentoFormaPagamento() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        for (TipoFormaPagto forma : TipoFormaPagto.values()) {
            itens.add(new SelectItem(forma.getSigla(), forma.getDescricao()));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort(itens, ordenador);
        return itens;
    }
    
    public List<SelectItem> getListaSelectItemTipoParceiro() throws Exception {
        return JSFUtilities.getSelectItemListFrom(Arrays.asList(TipoParceiroEnum.values()), "nome", "id", false, false);
    }

    public void montarListaSelectItemEmpresa(){
        try {
            empresasVO = getFacade().getEmpresa().consultarEmpresas();
            empresas = new ArrayList<SelectItem>();
            empresaSelecionada = null;
            contaSelecionada = null;
            empresas.add(new SelectItem(null, ""));
            for(EmpresaVO e : getEmpresasVO()){
                empresas.add(new SelectItem(e.getCodigo(), e.getNome()));
            }
            if(!UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())){
                formasEmpresas = getFacade().getFormaPagamentoEmpresa().consultarPorForma(formaPagamentoVO.getCodigo());
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public void montarListaSelectItemPinpad() {
        try {
            pinpads = new ArrayList<SelectItem>();
            pinpads.add(new SelectItem(null, ""));
            for(OpcoesPinpadEnum p : OpcoesPinpadEnum.values()){
                pinpads.add(new SelectItem(p.getCodigo(), p.getNome()));
            }
            if(!UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())){
                getFormaPagamentoVO().setListaPinPad(getFacade().getPinPad().consultarPorForma(formaPagamentoVO.getCodigo()));
                for (PinPadVO obj : getFormaPagamentoVO().getListaPinPad()) {
                    if ((obj.isStoneConnect() || obj.isGetCard()) && UteisValidacao.emptyNumber(obj.getEmpresa().getCodigo())) {
                        obj.getEmpresa().setNome("TODAS");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemPerfilAcesso() {
        try {
            setPerfisAcesso(new ArrayList<SelectItem>());
            getPerfisAcesso().add(new SelectItem(null, ""));
            List<PerfilAcessoVO> perfisAcesso = getFacade().getPerfilAcesso().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            for (PerfilAcessoVO perfilAcessoVO : perfisAcesso) {
                getPerfisAcesso().add(new SelectItem(perfilAcessoVO.getCodigo(), perfilAcessoVO.getNome()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("NOME_ENTIDADE"));
        loginControle.consultarLogObjetoSelecionado(NOME_ENTIDADE,
                getFormaPagamentoVO().getCodigo(), 0);
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaTodosConvenios();
        montarListaSelectItemPinpad();
        montarListaSelectItemEmpresa();
        montarListaSelectItemAdquirentes();
        montarListaSelectItemOperadoras();
        montarListaSelectItemContas();
        montarListaSelectItemPerfilAcesso();
    }

    public void montarListaSelectItemAdquirentes(){
        try {
            adquirentes = new ArrayList<SelectItem>();
            List<AdquirenteVO> adquirenteVOS = getFacade().getAdquirente().consultarTodos(true);
            adquirentes.add(new SelectItem(null, ""));
            for(AdquirenteVO a : adquirenteVOS){
                adquirentes.add(new SelectItem(a.getCodigo(), a.getNome()));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemContas(){
        try {
            contas = new ArrayList<SelectItem>();
            List<ContaCorrenteVO> adquirenteVOS = getFacade().getContaCorrente().consultarPorContaCorrente("", false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            contas.add(new SelectItem(null, ""));
            for(ContaCorrenteVO a : adquirenteVOS){
                contas.add(new SelectItem(a.getCodigo(), a.getDescricaoBBApresentar()));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemOperadoras(){
        try {
            operadoras = new ArrayList<SelectItem>();
            List<OperadoraCartaoVO> operadorasVOS = getFacade().getOperadoraCartao().consultarPorDescricao("", true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            operadoras.add(new SelectItem(null, ""));
            for(OperadoraCartaoVO o : operadorasVOS){
                if(UteisValidacao.emptyString(formaPagamentoVO.getTipoFormaPagamento())
                        || (formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()) && o.isCredito())
                        || (formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()) && !o.isCredito())){
                    operadoras.add(new SelectItem(o.getCodigo(), o.getDescricao()));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricaoConvenioCobranca", "Convênio"));
        itens.add(new SelectItem("tipoFormaPagamento", "Tipo Forma Pagamento"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemConvenioCobrancaApresentar() {
        if (this.getFormaPagamentoVO() != null &&
                this.getFormaPagamentoVO().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.PIX.getSigla())) {
            return this.getListaSelectItemConvenioCobrancaPix();
        } else {
            return this.getListaSelectItemConvenioCobranca();
        }
    }

    public List getListaSelectItemConvenioCobranca() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        for (ConvenioCobrancaVO obj : this.getListaTodosConvenios()) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        return objs;
    }


    public List getListaSelectItemConvenioCobrancaPix() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        for (ConvenioCobrancaVO obj : this.getListaTodosConvenios()) {
            if (obj.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.PIX)) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        return objs;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    public void redesenhaTela() {
        if ((getFormaPagamentoVO().getTipoFormaPagamento().equals(""))
                || (getFormaPagamentoVO().getTipoFormaPagamento() == null)) {
            setApresentarTaxaCartaoCredito(false);
            setApresentarTaxaCartaoDebito(false);
        }

        if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla()) ||
                getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla()) ) {
            getFormaPagamentoVO().setSomenteFinanceiro(false);
        }

        setApresentarTaxaCartaoDebito(getFormaPagamentoVO().getTipoFormaPagamento().equals("CD"));
        setApresentarTaxaCartaoCredito(getFormaPagamentoVO().getTipoFormaPagamento().equals("CA"));
        setApresentarTaxaBoleto(getFormaPagamentoVO().getTipoFormaPagamento().equals("BB"));
        montarListaSelectItemOperadoras();
        verificarTipoFormaPgto();
    }

    public boolean getApresentarSomenteFinanceiro() {
        if (getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla()) || isPix()) {
            return false;
        }
        return !getFormaPagamentoVO().getTipoFormaPagamento().equals("CC") && !getFormaPagamentoVO().getDefaultRecorrencia();
    }

    public boolean getTipoLote() {
        return getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.LOTE.getSigla());
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getFormaPagamento().consultarParaImpressao(
                tipo, filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public TaxaCartaoVO getTaxaCartaoVO() {
        return taxaCartaoVO;
    }

    public void setTaxaCartaoVO(TaxaCartaoVO taxaCartaoVO) {
        this.taxaCartaoVO = taxaCartaoVO;
    }

    public TaxaBoletoVO getTaxaBoletoVO() {
        return taxaBoletoVO;
    }

    public void setTaxaBoletoVO(TaxaBoletoVO taxaBoletoVO) {
        this.taxaBoletoVO = taxaBoletoVO;
    }

    public Boolean getApresentarTaxaCartaoDebito() {
        return apresentarTaxaCartaoDebito;
    }

    public void setApresentarTaxaCartaoDebito(Boolean apresentarTaxaCartaoDebito) {
        this.apresentarTaxaCartaoDebito = apresentarTaxaCartaoDebito;
    }

    public Boolean getApresentarTaxaCartaoCredito() {
        return apresentarTaxaCartaoCredito;
    }

    public void setApresentarTaxaCartaoCredito(Boolean apresentarTaxaCartaoCredito) {
        this.apresentarTaxaCartaoCredito = apresentarTaxaCartaoCredito;
    }

    public Boolean getApresentarTaxaBoleto() {
        return apresentarTaxaBoleto;
    }

    public void setApresentarTaxaBoleto(Boolean apresentarTaxaBoleto) {
        this.apresentarTaxaBoleto = apresentarTaxaBoleto;
    }

    public boolean isEditarSomenteDescricao() {
        return editarSomenteDescricao;
    }

    public void setEditarSomenteDescricao(boolean editarSomenteDescricao) {
        this.editarSomenteDescricao = editarSomenteDescricao;
    }

    public List<TaxaCartaoVO> getTaxaCartaoVigencia() {
        if (taxaCartaoVigencia == null) {
            taxaCartaoVigencia = new ArrayList<TaxaCartaoVO>();
        }
        return taxaCartaoVigencia;
    }

    public void setTaxaCartaoVigencia(List<TaxaCartaoVO> taxaCartaoVigencia) {
        this.taxaCartaoVigencia = taxaCartaoVigencia;
    }

    public List<TaxaCartaoVO> getTaxaCartaoAtual() {
        if (taxaCartaoAtual == null) {
            taxaCartaoAtual = new ArrayList<TaxaCartaoVO>();
        }
        return taxaCartaoAtual;
    }

    public void setTaxaCartaoAtual(List<TaxaCartaoVO> taxaCartaoAtual) {
        this.taxaCartaoAtual = taxaCartaoAtual;
    }


    public Integer getNrMesesTaxaSelecionada() {
        if (nrMesesTaxaSelecionada == null) {
            nrMesesTaxaSelecionada = 0;
        }
        return nrMesesTaxaSelecionada;
    }

    public void setNrMesesTaxaSelecionada(Integer nrMesesTaxaSelecionada) {
        this.nrMesesTaxaSelecionada = nrMesesTaxaSelecionada;
    }

    public List<TaxaCartaoVO> getTaxaCartaoDebito() {
        if (taxaCartaoDebito == null) {
            taxaCartaoDebito = new ArrayList<TaxaCartaoVO>();
        }
        return taxaCartaoDebito;
    }

    public void setTaxaCartaoDebito(List<TaxaCartaoVO> taxaCartaoDebito) {
        this.taxaCartaoDebito = taxaCartaoDebito;
    }
    
    public void incluirLogInclusao() throws Exception {
        try {
            formaPagamentoVO.setObjetoVOAntesAlteracao(new FormaPagamentoVO());
            formaPagamentoVO.setNovoObj(true);
            registrarLogObjetoVO(formaPagamentoVO, formaPagamentoVO.getCodigo(), NOME_ENTIDADE, 0);
            incluirLogAlteracoesTaxas();
            incluirLogAlteracoesPinPad();
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        formaPagamentoVO.setNovoObj(new Boolean(false));
        formaPagamentoVO.registrarObjetoVOAntesDaAlteracao();
        formaPagamentoVO.registrarTaxasAntesAlteracao();
        formaPagamentoVO.registrarPinPadsAntesAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            formaPagamentoVO.setObjetoVOAntesAlteracao(new FormaPagamentoVO());
            formaPagamentoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(formaPagamentoVO, formaPagamentoVO.getCodigo(), NOME_ENTIDADE, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(formaPagamentoVO, formaPagamentoVO.getCodigo(), NOME_ENTIDADE, 0);
            incluirLogAlteracoesTaxas();
            incluirLogAlteracoesPinPad();
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        formaPagamentoVO.registrarObjetoVOAntesDaAlteracao();
        formaPagamentoVO.registrarTaxasAntesAlteracao();
        formaPagamentoVO.registrarPinPadsAntesAlteracao();
    }
    
    public void realizarConsultaLogObjetoGeral() {
       formaPagamentoVO = new FormaPagamentoVO();
       realizarConsultaLogObjetoSelecionado();
    }
    
    private void incluirLogAlteracoesTaxas() throws Exception {
        for(TaxaCartaoVO atual : formaPagamentoVO.getTaxasCartao()){
            boolean nova = true;
            for(TaxaCartaoVO anterior : formaPagamentoVO.getTaxasCartaoAntesAlteracao()){
                if(anterior.getCodigo().equals(atual.getCodigo())){
                    if(anterior.getNrmeses() != atual.getNrmeses() 
                            || Uteis.arredondarForcando2CasasDecimais(atual.getTaxa()) != Uteis.arredondarForcando2CasasDecimais(anterior.getTaxa())
                            || anterior.getVigenciaInicial() != atual.getVigenciaInicial() 
                            || anterior.getVigenciaFinal() != atual.getVigenciaFinal()){
                        incluirLogAlteracaoTaxa(anterior ,atual);
                    }
                    nova = false;
                    break;
                }
            }
            if(nova){
                incluirLogInclusaoTaxa(atual);
            }
        }
        for(TaxaCartaoVO anterior : formaPagamentoVO.getTaxasCartaoAntesAlteracao()){
            boolean excluida = true;
            for(TaxaCartaoVO atual : formaPagamentoVO.getTaxasCartao()){
                 if(anterior.getCodigo().equals(atual.getCodigo())){
                     excluida = false;
                     break;
                 }
            }
            if(excluida){
                incluirLogExclusaoTaxa(anterior);
            }
        }
            
    }

    private void incluirLogAlteracoesPinPad() throws Exception {
        for (PinPadVO atual : formaPagamentoVO.getListaPinPad()) {
            boolean nova = true;
            for (PinPadVO anterior : formaPagamentoVO.getPinPadAntesAlteracao()) {
                if (anterior.getCodigo().equals(atual.getCodigo())) {
                    if (anterior.getPdvPinpad() != atual.getPdvPinpad()
                            || anterior.getCnpjPinpad() != atual.getCnpjPinpad()
                            || anterior.getAutoAtendimento() != atual.getAutoAtendimento())
                        incluirLogAlteracaoPinPad(anterior, atual);
                }
                nova = false;
                break;
            }

            if (nova) {
                incluirLogInclusaoPinPad(atual);
            }
        }
        for (PinPadVO anterior : formaPagamentoVO.getPinPadAntesAlteracao()) {
            boolean excluida = true;
            for (PinPadVO atual : formaPagamentoVO.getListaPinPad()) {
                if (anterior.getCodigo().equals(atual.getCodigo())) {
                    excluida = false;
                    break;
                }
            }
            if (excluida) {
                incluirLogExclusaoPinPad(anterior);
            }
        }
    }

    private void incluirLogAlteracaoPinPad(PinPadVO anterior, PinPadVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(formaPagamentoVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(atual.getOpcoesPinpadEnum().getNome());
            logVO.setNomeEntidade("FORMA PAGAMENTO");
            logVO.setNomeEntidadeDescricao("FormaPagamento - PinPad");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("PinPad = " + anterior.getOpcoesPinpadEnum().getNome() +"\n  CNPJ = "+anterior.getCnpjPinpad() +"\n  PDV = "+ anterior.getPdvPinpad() +" Autoatendimento = "+anterior.getAutoAtendimento()+" ");
            logVO.setValorCampoAlterado("PinPad = " + atual.getOpcoesPinpadEnum().getNome() +"\n  CNPJ = "+atual.getCnpjPinpad() +"\n  PDV = "+ atual.getPdvPinpad() +" Autoatendimento = "+anterior.getAutoAtendimento()+" ");

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TAXA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogAlteracaoTaxa(TaxaCartaoVO anterior, TaxaCartaoVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(formaPagamentoVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(atual.getCodigo().toString());
            logVO.setNomeEntidade("FORMA PAGAMENTO");
            logVO.setNomeEntidadeDescricao("FormaPagamento - TaxaCartao");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior(anterior.getDescricaoParaLog());
            logVO.setValorCampoAlterado(atual.getDescricaoParaLog());
            logVO.setPessoa(0);
            getFacade().getLog().incluirSemCommit(logVO);
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TAXA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void confirmarRemoverEmpresa() {
        try {
            getFacade().getFormaPagamentoEmpresa().excluirPorEmpresa(getFormaPagamentoVO().getCodigo(), codigoEmpresaRemover);
            formasEmpresas = getFacade().getFormaPagamentoEmpresa().consultarPorForma(formaPagamentoVO.getCodigo());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void confirmarRemoverPinPad() {
        try {
            if(codigoPinPadRemover == null){
                codigoPinPadRemover = 0;
            }
            getFacade().getPinPad().excluirPorPinPad(codigoPinPadRemover);
            getFormaPagamentoVO().setListaPinPad(getFacade().getPinPad().consultarPorForma(formaPagamentoVO.getCodigo()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void lancarTaxasEmpresa() throws Exception {
        try {
            limparMsg();
            if(UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())){
                montarInfo("Salve a forma de pagamento.");
                setMensagemDetalhada("Salve a forma de pagamento.");
                setMsgAlert(getMensagemNotificar());
                return;
            }
            formaPagamentoEmpresa = (FormaPagamentoEmpresaVO) context().getExternalContext().getRequestMap().get("empForma");
            if(UteisValidacao.emptyNumber(formaPagamentoEmpresa.getCodigo())){
                formaPagamentoEmpresa.setFormaPagamento(formaPagamentoVO);
                getFacade().getFormaPagamentoEmpresa().incluir(formaPagamentoEmpresa);
            }

            if (formaPagamentoVO.getTipoFormaPagamento().equals("BB")){
                taxaBoletoVO.setFormaPagamentoVO(getFormaPagamentoVO());
                taxaBoletoVO.setEmpresa(formaPagamentoEmpresa.getEmpresa());
                consultaTaxaBoleto();
                 setMsgAlert("Richfaces.showModalPanel('mdllancartaxasboleto')");
            } else {
                taxaCartaoAtualEmpresa = getFacade().getTaxaCartao().consultarPorFormaPagamento(getFormaPagamentoVO().getCodigo(),
                        formaPagamentoEmpresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                taxaCartaoEmpresaVO = new TaxaCartaoVO();
                taxaCartaoEmpresaVO.setFormaPagamentoVO(getFormaPagamentoVO());
                taxaCartaoEmpresaVO.setFormaPagamentoEmpresaVO(formaPagamentoEmpresa);
                organizarTaxas();
                setMsgAlert("Richfaces.showModalPanel('mdllancartaxas')");
            }

        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }

    }

    public void lancarTaxasBoleto() throws Exception {
        try {
            limparMsg();
            if(UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())){
                montarInfo("Salve a forma de pagamento.");
                setMensagemDetalhada("Salve a forma de pagamento.");
                setMsgAlert(getMensagemNotificar());
                return;
            }
            formaPagamentoEmpresa = (FormaPagamentoEmpresaVO) context().getExternalContext().getRequestMap().get("empForma");
            if(UteisValidacao.emptyNumber(formaPagamentoEmpresa.getCodigo())){
                formaPagamentoEmpresa.setFormaPagamento(formaPagamentoVO);
                getFacade().getFormaPagamentoEmpresa().incluir(formaPagamentoEmpresa);
            }
            taxaCartaoEmpresaVO = new TaxaCartaoVO();
            taxaCartaoEmpresaVO.setFormaPagamentoVO(getFormaPagamentoVO());
            taxaCartaoEmpresaVO.setFormaPagamentoEmpresaVO(formaPagamentoEmpresa);
            organizarTaxas();
            setMsgAlert("Richfaces.showModalPanel('mdllancartaxas')");
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }

    }

    private void organizarTaxas(){
        agrupamento = "";
        taxasOrganizadas = new HashMap<String, List<TaxaCartaoVO>>();
        for(TaxaCartaoVO taxa : taxaCartaoAtualEmpresa){

            String agrup = (UteisValidacao.emptyNumber(taxa.getAdquirenteVO().getCodigo()) ? "" : taxa.getAdquirenteVO().getNome()) +
                    (UteisValidacao.emptyNumber(taxa.getAdquirenteVO().getCodigo()) || UteisValidacao.emptyNumber(taxa.getBandeira().getCodigo()) ? "" : " - ")+
                    (UteisValidacao.emptyNumber(taxa.getBandeira().getCodigo()) ? "" : taxa.getBandeira().getDescricao());
            agrup = agrup.isEmpty() ? "Geral" : agrup;
            List<TaxaCartaoVO> taxasCartao = taxasOrganizadas.get(agrup);
            if(taxasCartao == null){
                taxasCartao = new ArrayList<TaxaCartaoVO>();
                taxasOrganizadas.put(agrup, taxasCartao);
            }
            taxasCartao.add(taxa);
        }
    }

    private void consultaTaxaBoleto() throws Exception {
        TaxaBoletoVO taxaBoletotemp = getFacade().getTaxaBoleto().consultarPorFormaPagamento(getTaxaBoletoVO().getFormaPagamentoVO().getCodigo(), getTaxaBoletoVO().getEmpresa().getCodigo());
        if (taxaBoletotemp != null) {
            this.taxaBoletoVO = taxaBoletotemp;
            this.taxaBoletoVO.setNovoObj(false);
        }
    }

    public List<String> getAgrupamentos(){
        if(taxasOrganizadas == null){
            return new ArrayList<String>();
        }
        List<String> agrups = new ArrayList<String>();
        for(String s : taxasOrganizadas.keySet()){
            if(!s.equals("Geral")){
                agrups.add(s);
            }
        }
        Collections.sort(agrups);
        return agrups;
    }

    public void removerEmpresa(){
            montarMsgGenericaPergunta("Excluir configuração de empresa",
                    "Se houverem taxas configuradas para essa empresa, elas serão removidas. Deseja realmente excluir ? ",
                    "confirmarRemoverEmpresa",
                    "Richfaces.hideModalPanel('mdlMensagemGenerica');return false;",
                    null,
                    null,
                    "empresasFormaPgto");
            FormaPagamentoEmpresaVO fpe = (FormaPagamentoEmpresaVO) context().getExternalContext().getRequestMap().get("empForma");
            codigoEmpresaRemover = fpe.getEmpresa().getCodigo();
    }

    public void removerPinPad(){
        montarMsgGenericaPergunta("Excluir configuração de pinpad",
                "Fazendo a exclusão, esse PDV não irá mais aparecer na tela de recebimento. Deseja realmente excluir ? ",
                "confirmarRemoverPinPad",
                "Richfaces.hideModalPanel('mdlMensagemGenerica');return false;",
                null,
                null,
                "pinPadFormaPgto");
        PinPadVO pp = (PinPadVO) context().getExternalContext().getRequestMap().get("pinpe");
        codigoPinPadRemover = pp.getCodigo();
    }

    public void montarMsgGenericaPergunta(String titulo, String msgInformacao, String metodoInvocarAoClicarBotaoSim, String onCompleteBotaoSim, String metodoInvocarAoClicarBotaoNao, String onCompleteBotaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("");
        control.init(titulo, msgInformacao, this, metodoInvocarAoClicarBotaoSim, onCompleteBotaoSim, metodoInvocarAoClicarBotaoNao, onCompleteBotaoNao, reRender);
    }

    public void adicionarEmpresa() throws Exception {
        if(UteisValidacao.emptyNumber(empresaSelecionada)){
            montarErro("Selecione uma empresa!");
            setMsgAlert(getMensagemNotificar());
            return;
        }
        for(FormaPagamentoEmpresaVO fpee : formasEmpresas){
            if(fpee.getEmpresa().getCodigo().equals(empresaSelecionada)){
                montarErro("Empresa já cadastrada!");
                setMsgAlert(getMensagemNotificar());
                return;
            }
        }
        FormaPagamentoEmpresaVO fpe = new FormaPagamentoEmpresaVO();
        fpe.getEmpresa().setCodigo(empresaSelecionada);

        if (!UteisValidacao.emptyNumber(contaSelecionada)) {
            fpe.setContaDestino(getFacade().getContaCorrente().consultarPorChavePrimaria(contaSelecionada, Uteis.NIVELMONTARDADOS_TELACONSULTA));
        }
        obterNomeEmpresaSelecionada(empresaSelecionada, fpe, null);
        formasEmpresas.add(fpe);
        empresaSelecionada = null;
        contaSelecionada = null;
    }

    private void obterNomeEmpresaSelecionada(Integer empSelecionada, FormaPagamentoEmpresaVO fpe, PinPadVO pp) {
        for(SelectItem se : empresas){
            if(empSelecionada != null && empSelecionada.equals(se.getValue())){
                if(pp != null){
                    pp.getEmpresa().setNome(se.getLabel());
                    break;
                }else{
                    fpe.getEmpresa().setNome(se.getLabel());
                }

            }
        }
    }

    private void incluirLogInclusaoPinPad(PinPadVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("INCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(formaPagamentoVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(atual.getOpcoesPinpadEnum().getNome());
            logVO.setNomeEntidade("FORMA PAGAMENTO");
            logVO.setNomeEntidadeDescricao("FormaPagamento - PinPad");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("PinPad = " + atual.getOpcoesPinpadEnum().getNome() +"\n  CNPJ = "+atual.getCnpjPinpad() +"\n  PDV = "+ atual.getPdvPinpad() +" Autoatendimento = "+atual.getAutoAtendimento()+" ");

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE TAXA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogInclusaoTaxa(TaxaCartaoVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("INCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(formaPagamentoVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(atual.getCodigo().toString());
            logVO.setNomeEntidade("FORMA PAGAMENTO");
            logVO.setNomeEntidadeDescricao("FormaPagamento - TaxaCartao");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado(atual.getDescricaoParaLog());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE TAXA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogExclusaoTaxa(TaxaCartaoVO anterior) throws Exception {
         try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("EXCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(formaPagamentoVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(anterior.getCodigo().toString());
            logVO.setNomeEntidade("FORMA PAGAMENTO");
            logVO.setNomeEntidadeDescricao("FormaPagamento - TaxaCartao");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado(anterior.getDescricaoParaLog());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
         } catch (Exception e) {
            registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TAXA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogExclusaoPinPad(PinPadVO anterior) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("EXCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(formaPagamentoVO.getCodigo()));
            logVO.setChavePrimariaEntidadeSubordinada(anterior.getOpcoesPinpadEnum().getNome());
            logVO.setNomeEntidade("FORMA PAGAMENTO");
            logVO.setNomeEntidadeDescricao("FormaPagamento - PinPad");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("PinPad = " + anterior.getOpcoesPinpadEnum().getNome() +"\n  CNPJ = "+anterior.getCnpjPinpad() +"\n  PDV = "+ anterior.getPdvPinpad() +" Autoatendimento = "+anterior.getAutoAtendimento()+" ");

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
         } catch (Exception e) {
            registrarLogErroObjetoVO("FORMA PAGAMENTO", formaPagamentoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TAXA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void adicionarFormaPagamentoPerfilAcesso() {
        if (UteisValidacao.emptyNumber(getFormaPagamentoPerfilAcesso().getPerfilAcessoVO().getCodigo())) {
            return;
        }

        for (FormaPagamentoPerfilAcessoVO fpaVO : getFormaPagamentoVO().getFormasPerfilAcesso()) {
            if (fpaVO.getPerfilAcessoVO().getCodigo().equals(getFormaPagamentoPerfilAcesso().getPerfilAcessoVO().getCodigo())) {
                return;
            }
        }

        for(SelectItem item : getPerfisAcesso()) {
            if (getFormaPagamentoPerfilAcesso().getPerfilAcessoVO().getCodigo().equals(item.getValue())) {
                getFormaPagamentoPerfilAcesso().getPerfilAcessoVO().setNome(item.getLabel());
                break;
            }
        }

        FormaPagamentoPerfilAcessoVO fpaVO = new FormaPagamentoPerfilAcessoVO();
        fpaVO.setFormaPagamento(getFormaPagamentoVO());
        PerfilAcessoVO perfilAcessoVO = new PerfilAcessoVO();
        perfilAcessoVO.setCodigo(getFormaPagamentoPerfilAcesso().getPerfilAcessoVO().getCodigo());
        perfilAcessoVO.setNome(getFormaPagamentoPerfilAcesso().getPerfilAcessoVO().getNome());
        fpaVO.setPerfilAcessoVO(perfilAcessoVO);
        getFormaPagamentoVO().getFormasPerfilAcesso().add(fpaVO);
    }

    public void removerFormaPagamentoPerfilAcesso(){
        montarMsgGenericaPergunta("Excluir perfil de acesso da forma de pagamento",
                "Fazendo a exclusão, essa Forma de Pagamento não irá mais aparecer na tela de recebimento para este perfil. Deseja realmente excluir? ",
                "confirmarRemoverPerfilAcesso",
                "Richfaces.hideModalPanel('mdlMensagemGenerica');return false;",
                null,
                null,
                "perfilAcessoFormaPgto");
        FormaPagamentoPerfilAcessoVO pp = (FormaPagamentoPerfilAcessoVO) context().getExternalContext().getRequestMap().get("perfilAcesso");
        codigoPerfilAcessoRemover = pp.getCodigo();
    }

    public void confirmarRemoverPerfilAcesso() {
        try {
            if (codigoPerfilAcessoRemover == null) {
                codigoPerfilAcessoRemover = 0;
            }

            FormaPagamentoPerfilAcessoVO fppaVO = new FormaPagamentoPerfilAcessoVO();
            fppaVO.setCodigo(codigoPerfilAcessoRemover);
            getFacade().getFormaPagamentoPerfilAcesso().excluir(fppaVO);
            getFormaPagamentoVO().setFormasPerfilAcesso(getFacade().getFormaPagamentoPerfilAcesso().consultarPorForma(formaPagamentoVO.getCodigo()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List getListaTipoDebitoOnlineEnum() {
        return TipoDebitoOnlineEnum.getSelectListTipo();
    }
    
    public List<SelectItem> getPinpads() {
        return pinpads;
    }

    public void setPinpads(List<SelectItem> pinpads) {
        this.pinpads = pinpads;
    }
    public List<SelectItem> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<SelectItem> empresas) {
        this.empresas = empresas;
    }

    public Integer getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(Integer empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public List<FormaPagamentoEmpresaVO> getFormasEmpresas() {
        return formasEmpresas;
    }

    public void setFormasEmpresas(List<FormaPagamentoEmpresaVO> formasEmpresas) {
        this.formasEmpresas = formasEmpresas;
    }

    public List<SelectItem> getAdquirentes() {
        return adquirentes;
    }

    public void setAdquirentes(List<SelectItem> adquirentes) {
        this.adquirentes = adquirentes;
    }

    public List<SelectItem> getOperadoras() {
        return operadoras;
    }

    public void setOperadoras(List<SelectItem> operadoras) {
        this.operadoras = operadoras;
    }

    public Integer getCodigoEmpresaRemover() {
        return codigoEmpresaRemover;
    }

    public void setCodigoEmpresaRemover(Integer codigoEmpresaRemover) {
        this.codigoEmpresaRemover = codigoEmpresaRemover;
    }

    public String getModalMensagemGenerica() {
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public FormaPagamentoEmpresaVO getFormaPagamentoEmpresa() {
        return formaPagamentoEmpresa;
    }

    public void setFormaPagamentoEmpresa(FormaPagamentoEmpresaVO formaPagamentoEmpresa) {
        this.formaPagamentoEmpresa = formaPagamentoEmpresa;
    }

    public List<TaxaCartaoVO> getTaxaCartaoAtualEmpresa() {
        return taxaCartaoAtualEmpresa;
    }

    public void setTaxaCartaoAtualEmpresa(List<TaxaCartaoVO> taxaCartaoAtualEmpresa) {
        this.taxaCartaoAtualEmpresa = taxaCartaoAtualEmpresa;
    }

    public TaxaCartaoVO getTaxaCartaoEmpresaVO() {
        return taxaCartaoEmpresaVO;
    }

    public void setTaxaCartaoEmpresaVO(TaxaCartaoVO taxaCartaoEmpresaVO) {
        this.taxaCartaoEmpresaVO = taxaCartaoEmpresaVO;
    }

    public Map<String, List<TaxaCartaoVO>> getTaxasOrganizadas() {
        return taxasOrganizadas;
    }

    public boolean getTemTaxaGeral(){
        return taxasOrganizadas != null
                && taxasOrganizadas.get("Geral") != null
                && !taxasOrganizadas.get("Geral").isEmpty();
    }

    public void setTaxasOrganizadas(Map<String, List<TaxaCartaoVO>> taxasOrganizadas) {
        this.taxasOrganizadas = taxasOrganizadas;
    }

    public String getAgrupamento() {
        return agrupamento;
    }

    public void setAgrupamento(String agrupamento) {
        this.agrupamento = agrupamento;
    }

    public String getTipo() {
        if (tipo == null) {
            tipo = "AT";
        }
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public List<SelectItem> getListaTipo() {
        if(UteisValidacao.emptyList(listaTipo)){
            listaTipo = new ArrayList<SelectItem>();
            this.listaTipo.add(new SelectItem("AT",  "Ativos"));
            this.listaTipo.add(new SelectItem("IN", "Inativos"));
            this.listaTipo.add(new SelectItem("TD",  "Todos"));
        }
        return listaTipo;
    }

    public void setListaTipo(List<SelectItem> listaTipo) {
        this.listaTipo = listaTipo;
    }

    public List<TaxaCartaoVO> getTaxaCartaoVigenciaEmpresa() {
        return taxaCartaoVigenciaEmpresa;
    }

    public void setTaxaCartaoVigenciaEmpresa(List<TaxaCartaoVO> taxaCartaoVigenciaEmpresa) {
        this.taxaCartaoVigenciaEmpresa = taxaCartaoVigenciaEmpresa;
    }

    public List<SelectItem> getContas() {
        return contas;
    }

    public void setContas(List<SelectItem> contas) {
        this.contas = contas;
    }

    public void adicionarNumeroParcelaNaLista() {
        notificarRecursoEmpresa(RecursoSistema.NUMERO_PARCELAS_ADICIONADAS_TAXA_CARTAO);

        for(Integer parcelaDaLista : getQuantidadeNumeroParcelaNaListaSelecionadas()){
            if(parcelaDaLista == getTaxaCartaoVO().getNrmeses()){
                return;
            }
        }

        if (getTaxaCartaoVO().getNrmeses() > 0) {
            getQuantidadeNumeroParcelaNaListaSelecionadas().add(getTaxaCartaoVO().getNrmeses());
            setNrMesesTaxaSelecionada(0);
        }
    }

    public List<Integer> getQuantidadeNumeroParcelaNaListaSelecionadas() {
        if (quantidadeNumeroParcelaNaListaSelecionadas == null) {
            quantidadeNumeroParcelaNaListaSelecionadas = new ArrayList<Integer>();

        }
        return quantidadeNumeroParcelaNaListaSelecionadas;
    }

    public Integer getTamanhoListaQuantidadeNumeroParcela() {
        return getQuantidadeNumeroParcelaNaListaSelecionadas().size();
    }

    public void setQuantidadeNumeroParcelaNaListaSelecionadas(List<Integer> quantidadeNumeroParcelaNaListaSelecionadas) {
        this.quantidadeNumeroParcelaNaListaSelecionadas = quantidadeNumeroParcelaNaListaSelecionadas;
    }

    public void limparQuantidadeNumeroParcelaNaLista() {
        setQuantidadeNumeroParcelaNaListaSelecionadas(new ArrayList<Integer>());
        getTaxaCartaoVO().setNrmeses(0);
    }

    public String getQuantidadeNumeroParcelaNaListaApresentar() {
        StringBuilder sb = new StringBuilder();
        for (Integer itemSelecionado : getQuantidadeNumeroParcelaNaListaSelecionadas()) {
            if (itemSelecionado >= 1) {
                sb.append("Parcela ").append(itemSelecionado).append("</br>");
            }
        }
        if (sb.length() == 0) {
            return "NENHUMA PARCELA INFORMADA";
        }
        return sb.toString();
    }

    public List<SelectItem> getAdquirenteSelecionadas() {
        if (adquirenteSelecionadas == null) {
            adquirenteSelecionadas = new ArrayList<SelectItem>();

        }
        return adquirenteSelecionadas;
    }

    public void setAdquirenteSelecionadas(List<SelectItem> adquirenteSelecionadas) {
        this.adquirenteSelecionadas = adquirenteSelecionadas;
    }
    public String getAdquirenteSelecionadasApresentar() {
        StringBuilder sb = new StringBuilder("");
        boolean nenhumSelecionado = true;
        for (SelectItem itemSelecionado : getAdquirenteSelecionadas()) {
            nenhumSelecionado = false;
            sb.append(itemSelecionado.getLabel()).append("<br/ >");
        }
        if (nenhumSelecionado) {
            sb.append("NENHUM ADQUIRENTE SELECIONADO");
        }
        return sb.toString();
    }

    public Integer getTamanhoListaAdquirentes() {
        return getAdquirenteSelecionadas().size();
    }

    public void adicionarAdquirenteNaLista() {
        notificarRecursoEmpresa(RecursoSistema.ADQUIRENTES_ADICIONADAS_TAXA_CARTAO);
        SelectItem itemSelecionado = null;
        for (SelectItem itemAdquirente : adquirentes) {
            if (getTaxaCartaoVO().getAdquirenteVO().getCodigo() > 0 && getTaxaCartaoVO().getAdquirenteVO().getCodigo() == itemAdquirente.getValue()) {
                itemSelecionado = itemAdquirente;
                getAdquirenteSelecionadas().add(itemAdquirente);
                break;
            }
        }
        if (itemSelecionado != null) {
            getAdquirentes().remove(itemSelecionado);
        }
    }

    public void limparAdquirentesNaLista() {
        montarListaSelectItemAdquirentes();
        setAdquirenteSelecionadas(new ArrayList<SelectItem>());
    }

    public List<SelectItem> getOperadoraSelecionadas() {
        if (OperadoraSelecionadas == null) {
            OperadoraSelecionadas = new ArrayList<SelectItem>();
        }
        return OperadoraSelecionadas;
    }

    public void setOperadoraSelecionadas(List<SelectItem> operadoraSelecionadas) {
        OperadoraSelecionadas = operadoraSelecionadas;
    }

    public void limparBandeiraNaLista() {
        montarListaSelectItemOperadoras();
        setOperadoraSelecionadas(new ArrayList<SelectItem>());
    }

    public String getBandeiraSelecionadasApresentar() {
        StringBuilder sb = new StringBuilder("");
        boolean nenhumSelecionado = true;
        for (SelectItem itemSelecionado : getOperadoraSelecionadas()) {
            nenhumSelecionado = false;
            sb.append(itemSelecionado.getLabel()).append("<br/>");
        }
        if (nenhumSelecionado) {
            sb.append("NENHUMA OPERADORA SELECIONADA");
        }
        return sb.toString();
    }

    public void adicionarBanceiraNaLista() {
        notificarRecursoEmpresa(RecursoSistema.OPERADORAS_ADICIONADAS_TAXA_CARTAO);
        SelectItem itemSelecionado = null;
        for (SelectItem itemOperadora : operadoras) {
            if (getTaxaCartaoVO().getBandeira().getCodigo() > 0 && getTaxaCartaoVO().getBandeira().getCodigo().equals(itemOperadora.getValue())) {
                itemSelecionado = itemOperadora;
                getOperadoraSelecionadas().add(itemOperadora);
                break;
            }
        }
        if (itemSelecionado != null) {
            getOperadoras().remove(itemSelecionado);
        }
    }
    public Integer getTamanhoListaBandeira() {
        return getOperadoraSelecionadas().size();
    }

    public List<SelectItem> getAdquirenteEmpresaSelecionadas() {
        if (adquirenteEmpresaSelecionadas == null) {
            adquirenteEmpresaSelecionadas = new ArrayList<SelectItem>();

        }
        return adquirenteEmpresaSelecionadas;
    }

    public void setAdquirenteEmpresaSelecionadas(List<SelectItem> adquirenteEmpresaSelecionadas) {
        this.adquirenteEmpresaSelecionadas = adquirenteEmpresaSelecionadas;
    }

    public String getAdquirenteEmpresaSelecionadasApresentar() {
        StringBuilder sb = new StringBuilder("");
        boolean nenhumSelecionado = true;
        for (SelectItem itemSelecionado : getAdquirenteEmpresaSelecionadas()) {
            nenhumSelecionado = false;
            sb.append(itemSelecionado.getLabel()).append("<br/>");
        }
        if (nenhumSelecionado) {
            sb.append("NENHUM ADQUIRENTE SELECIONADO");
        }
        return sb.toString();
    }

    public Integer getTamanhoListaAdquirentesEmpresa() {
        return getAdquirenteEmpresaSelecionadas().size();
    }

    public void adicionarAdquirenteEmpresaNaLista() {
        notificarRecursoEmpresa(RecursoSistema.ADQUIRENTES_ADICIONADAS_TAXA_CARTAO);
        SelectItem itemSelecionado = null;
        for (SelectItem itemAdquirente : adquirentes) {

            if (getTaxaCartaoEmpresaVO().getAdquirenteVO().getCodigo() > 0 && getTaxaCartaoEmpresaVO().getAdquirenteVO().getCodigo() == itemAdquirente.getValue()) {
                itemSelecionado = itemAdquirente;
                getAdquirenteEmpresaSelecionadas().add(itemAdquirente);
                break;
            }
        }
        if (itemSelecionado != null) {
            getAdquirentes().remove(itemSelecionado);
        }
    }

    public void limparAdquirentesEmpresaNaLista() {
        montarListaSelectItemAdquirentes();
        setAdquirenteEmpresaSelecionadas(new ArrayList<SelectItem>());
    }

    public void adicionarNumeroParcelaEmpresaNaLista() {
        notificarRecursoEmpresa(RecursoSistema.NUMERO_PARCELAS_ADICIONADAS_TAXA_CARTAO);

        for(Integer parcelaDaLista : getQuantidadeNumeroParcelaEmpresaNaListaSelecionadas()){
            if(parcelaDaLista == getTaxaCartaoEmpresaVO().getNrmeses()){
                return;
            }
        }

        if (getTaxaCartaoEmpresaVO().getNrmeses() > 0) {
            getQuantidadeNumeroParcelaEmpresaNaListaSelecionadas().add(getTaxaCartaoEmpresaVO().getNrmeses());
            setNrMesesTaxaSelecionada(0);
        }
    }

    public List<Integer> getQuantidadeNumeroParcelaEmpresaNaListaSelecionadas() {
        if (quantidadeNumeroParcelaEmpresaNaListaSelecionadas == null) {
            quantidadeNumeroParcelaEmpresaNaListaSelecionadas = new ArrayList<Integer>();

        }
        return quantidadeNumeroParcelaEmpresaNaListaSelecionadas;
    }

    public Integer getTamanhoListaQuantidadeNumeroParcelaEmpresa() {
        return getQuantidadeNumeroParcelaEmpresaNaListaSelecionadas().size();
    }

    public void setQuantidadeNumeroParcelaEmpresaNaListaSelecionadas(List<Integer> quantidadeNumeroParcelaEmpresaNaListaSelecionadas) {
        this.quantidadeNumeroParcelaEmpresaNaListaSelecionadas = quantidadeNumeroParcelaEmpresaNaListaSelecionadas;
    }

    public void limparQuantidadeNumeroParcelaEmpresaNaLista() {
        setQuantidadeNumeroParcelaEmpresaNaListaSelecionadas(new ArrayList<Integer>());
        getTaxaCartaoEmpresaVO().setNrmeses(0);
    }

    public String getQuantidadeNumeroParcelaEmpresaNaListaApresentar() {
        StringBuilder sb = new StringBuilder();
        for (Integer itemSelecionado : getQuantidadeNumeroParcelaEmpresaNaListaSelecionadas()) {
            if (itemSelecionado >= 1) {
                sb.append("Parcela ").append(itemSelecionado).append("</br>");
            }
        }
        if (sb.length() == 0) {
            return "NENHUMA PARCELA INFORMADA";
        }
        return sb.toString();
    }

    public List<SelectItem> getOperadoraEmpresaSelecionadas() {
        if (OperadoraEmpresaSelecionadas == null) {
            OperadoraEmpresaSelecionadas = new ArrayList<SelectItem>();
        }
        return OperadoraEmpresaSelecionadas;
    }

    public void setOperadoraEmpresaSelecionadas(List<SelectItem> operadoraEmpresaSelecionadas) {
        OperadoraEmpresaSelecionadas = operadoraEmpresaSelecionadas;
    }

    public void limparBandeiraEmpresaNaLista() {
        montarListaSelectItemOperadoras();
        setOperadoraEmpresaSelecionadas(new ArrayList<SelectItem>());
    }

    public String getBandeiraEmpresaSelecionadasApresentar() {
        StringBuilder sb = new StringBuilder("");
        boolean nenhumSelecionado = true;
        for (SelectItem itemSelecionado : getOperadoraEmpresaSelecionadas()) {
            nenhumSelecionado = false;
            sb.append(itemSelecionado.getLabel()).append("<br/>");
        }
        if (nenhumSelecionado) {
            sb.append("NENHUMA OPERADORA SELECIONADA");
        }
        return sb.toString();
    }

    public void adicionarBanceiraEmpresaNaLista() {
        notificarRecursoEmpresa(RecursoSistema.OPERADORAS_ADICIONADAS_TAXA_CARTAO);
        SelectItem itemSelecionado = null;
        for (SelectItem itemOperadoraEmpresa : operadoras) {
            if (getTaxaCartaoEmpresaVO().getBandeira().getCodigo() > 0 && getTaxaCartaoEmpresaVO().getBandeira().getCodigo().equals(itemOperadoraEmpresa.getValue())) {
                itemSelecionado = itemOperadoraEmpresa;
                getOperadoraEmpresaSelecionadas().add(itemOperadoraEmpresa);
                break;
            }
        }
        if (itemSelecionado != null) {
            getOperadoras().remove(itemSelecionado);
        }
    }

    public Integer getTamanhoListaBandeiraEmpresa() {
        return getOperadoraEmpresaSelecionadas().size();
    }

    public Integer getContaSelecionada() {
        return contaSelecionada;
    }

    public void setContaSelecionada(Integer contaSelecionada) {
        this.contaSelecionada = contaSelecionada;
    }

    public Integer getEmpSelecionadaPinPad() {
        return empSelecionadaPinPad;
    }

    public void setEmpSelecionadaPinPad(Integer empSelecionadaPinPad) {
        this.empSelecionadaPinPad = empSelecionadaPinPad;
    }

    public FormaPagamentoPerfilAcessoVO getFormaPagamentoPerfilAcesso() {
        if (formaPagamentoPerfilAcesso == null) {
            formaPagamentoPerfilAcesso = new FormaPagamentoPerfilAcessoVO();
        }
        return formaPagamentoPerfilAcesso;
    }

    public void setFormaPagamentoPerfilAcesso(FormaPagamentoPerfilAcessoVO formaPagamentoPerfilAcesso) {
        this.formaPagamentoPerfilAcesso = formaPagamentoPerfilAcesso;
    }

    public List<FormaPagamentoPerfilAcessoVO> getFormasPerfilAcesso() {
        return formasPerfilAcesso;
    }

    public void setFormasPerfilAcesso(List<FormaPagamentoPerfilAcessoVO> formasPerfilAcesso) {
        this.formasPerfilAcesso = formasPerfilAcesso;
    }

    public List<SelectItem> getPerfisAcesso() {
        return perfisAcesso;
    }

    public void setPerfisAcesso(List<SelectItem> perfisAcesso) {
        this.perfisAcesso = perfisAcesso;
    }

    public List<SelectItem> getListaSelectItemTipoConvenioCobranca() {
        return JSFUtilities.getSelectItemListFromEnum(TipoConvenioCobrancaEnum.class, "descricao", false);
    }

    public boolean isPermiteAlterarTipoForma() {
        return permiteAlterarTipoForma;
    }

    public boolean getExibirDescricao() {
        return !isPix();
    }

    public void setPermiteAlterarTipoForma(boolean permiteAlterarTipoForma) {
        this.permiteAlterarTipoForma = permiteAlterarTipoForma;
    }

    public boolean isCompensacaoPorTaxa() {
        return compensacaoPorTaxa;
    }

    public void setCompensacaoPorTaxa(boolean compensacaoPorTaxa) {
        this.compensacaoPorTaxa = compensacaoPorTaxa;
    }

    public int getNrDiasCompensacaoPorTaxa() {
        return nrDiasCompensacaoPorTaxa;
    }

    public void setNrDiasCompensacaoPorTaxa(int nrDiasCompensacaoPorTaxa) {
        this.nrDiasCompensacaoPorTaxa = nrDiasCompensacaoPorTaxa;
    }

    public void validarLimparCompensacaoPorTaxa(ActionEvent actionEvent) {
        TaxaCartaoVO taxa = (TaxaCartaoVO) actionEvent.getComponent().getAttributes().get("taxaCartaoCompensacao");
        if(taxa != null && !taxa.isCompensacaoPorTaxa()){
            taxa.setNrDiasCompensacaoPorTaxa(0);
        }
    }

    public boolean isPix(){
        return getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla());
    }

    public boolean getExibirDefaultDco() {
        return !isPix();
    }

    public boolean isExibirConvenio() {
        if (formaPagamentoVO.isParceiroFidelidade() && formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
            return false;
        }
        return true;
    }

    public boolean isApresentarEdicaoBoleto() {
        return apresentarEdicaoBoleto;
    }

    public void setApresentarEdicaoBoleto(boolean apresentarEdicaoBoleto) {
        this.apresentarEdicaoBoleto = apresentarEdicaoBoleto;
    }

    public List<SelectItem> getFormaCalculoEnumToSelectedItens() {
        return FormaCalculo.toSelectedItens();
    }



    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }

    public PinPadVO getPinPadVO() {
        return pinPadVO;
    }

    public void setPinPadVO(PinPadVO pinPadVO) {
        this.pinPadVO = pinPadVO;
    }

    public void adicionarPinpad() {
        try {
            limparMsg();
            if (getFormaPagamentoVO().getPinpad().isStoneConnect() ||
                    getFormaPagamentoVO().getPinpad().isGetCard()) {
                if (UteisValidacao.emptyString(getFormaPagamentoVO().getPinpad().getDescricao())) {
                    throw new Exception("Informe uma descrição para o pinpad");
                }
                if (UteisValidacao.emptyString(getFormaPagamentoVO().getPinpad().getPdvPinpad())) {
                    if (getFormaPagamentoVO().getPinpad().isGetCard()) {
                        throw new Exception("Informe o número do PDV");
                    } else {
                        throw new Exception("Informe o Serial Number do pinpad");
                    }
                }
                if (UteisValidacao.emptyNumber(getFormaPagamentoVO().getPinpad().getConvenioCobranca().getCodigo())) {
                    throw new Exception("Informe o convênio de cobrança do pinpad");
                }
            }

            if (getFormaPagamentoVO().getPinpad().isCAPPTA()) {
                if (UteisValidacao.emptyString(getFormaPagamentoVO().getPinpad().getDescricao())) {
                    throw new Exception("Informe uma descrição para o pinpad");
                }
                if (UteisValidacao.emptyString(getFormaPagamentoVO().getPinpad().getPdvPinpad())) {
                        throw new Exception("Informe o número do PDV");
                }
            }


            getFormaPagamentoVO().getPinpad().setDescricao(getFormaPagamentoVO().getPinpad().getDescricao().toUpperCase());
            for(SelectItem se : getEmpresasPinpad()){
                if (getFormaPagamentoVO().getPinpad().getEmpresa().getCodigo().equals(se.getValue())) {
                    getFormaPagamentoVO().getPinpad().getEmpresa().setNome(se.getLabel());
                    break;
                }
            }
            for(ConvenioCobrancaVO obj : this.getListaTodosConvenios()){
                if (getFormaPagamentoVO().getPinpad().getConvenioCobranca().getCodigo().equals(obj.getCodigo())) {
                    getFormaPagamentoVO().getPinpad().setConvenioCobranca(obj);
                    break;
                }
            }
            getFormaPagamentoVO().getListaPinPad().add(getFormaPagamentoVO().getPinpad());
            getFormaPagamentoVO().setPinpad(new PinPadVO());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public List<EmpresaVO> getEmpresasVO() {
        if (empresasVO == null) {
            empresasVO = new ArrayList<>();
        }
        return empresasVO;
    }

    public void setEmpresasVO(List<EmpresaVO> empresasVO) {
        this.empresasVO = empresasVO;
    }

    public List<SelectItem> getEmpresasPinpad() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, "TODAS"));
        for (EmpresaVO e : getEmpresasVO()) {
            lista.add(new SelectItem(e.getCodigo(), e.getNome()));
        }
        return lista;
    }

    public List<SelectItem> getEmpresasPinpadCappta() {
        List<SelectItem> lista = new ArrayList<>();
        for (EmpresaVO e : getEmpresasVO()) {
            lista.add(new SelectItem(e.getCodigo(), e.getNome()));
        }
        return lista;
    }

    public List<ConvenioCobrancaVO> getListaTodosConvenios() {
        if (listaTodosConvenios == null) {
            listaTodosConvenios = new ArrayList<>();
        }
        return listaTodosConvenios;
    }

    public void setListaTodosConvenios(List<ConvenioCobrancaVO> listaTodosConvenios) {
        this.listaTodosConvenios = listaTodosConvenios;
    }

    public void montarListaTodosConvenios() {
        try {
            this.setListaTodosConvenios(getFacade().getConvenioCobranca().consultarTodosGeral(false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<SelectItem> getListaSelectItemConvenioCobrancaPinPad() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        Set<Integer> adicionados = new HashSet<>();
        for (ConvenioCobrancaVO obj : this.getListaTodosConvenios()) {
            if (this.getFormaPagamentoVO() != null &&
                    this.getFormaPagamentoVO().getPinpad() != null &&
                    this.getFormaPagamentoVO().getPinpad().getOpcoesPinpadEnum() != null &&
                    obj.getTipo().getOpcoesPinpadEnum() != null &&
                    obj.getTipo().getOpcoesPinpadEnum().equals(this.getFormaPagamentoVO().getPinpad().getOpcoesPinpadEnum()) &&
                    !adicionados.contains(obj.getCodigo())) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
                adicionados.add(obj.getCodigo());
            }
        }
        return objs;
    }
}
