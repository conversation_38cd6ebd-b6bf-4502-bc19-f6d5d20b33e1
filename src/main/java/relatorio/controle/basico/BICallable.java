package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import controle.basico.LtvControle;
import controle.basico.RiscoControle;
import controle.crm.RelatorioAgendamentosControle;
import controle.financeiro.MetaFinanceiroBIControle;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.financeiro.IndiceConversaoVendaRelControle;
import relatorio.controle.financeiro.IndiceConversaoVendaSessaoRelControle;
import relatorio.controle.sad.*;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.Callable;

public class BICallable implements Callable<String> {

    private final HttpServletRequest request;
    private final HttpServletResponse response;
    private final BIEnum bi;

    public BICallable(HttpServletRequest request, HttpServletResponse response, BIEnum bi) {
        this.request = request;
        this.response = response;
        this.bi = bi;
    }

    @Override
    public String call() throws Exception {
        try {
            JSFUtilities.getFacesContext(request, response);
            switch (bi) {
                case PENDENCIA:
                    pendencias();
                    break;
                case CONTROLE_OPERACOES:
                    controleOperacoes();
                    break;
                case PROBABILIDADE_EVASAO:
                    probabilidade();
                    break;
                case CONVERSAO_VENDAS:
                    conversaoVendas();
                    break;
                case CONVERSAO_VENDAS_SS:
                    conversaoVendasSS();
                    break;
                case DCC:
                    dcc();
                    break;
                case GRUPO_RISCO:
                    risco();
                    break;
                case METAS_FINANCEIRAS:
                    metasFinan();
                    break;
                case TICKET_MEDIO:
                    ticketMedio();
                    break;
                case INDICE_RENOVACAO:
                    renovacao();
                    break;
                case ROTATIVIDADE_CONTRATO:
                    rotatividade();
                    break;
                case AULA_EXPERIMENTAL:
                    aulaExperimental();
                    break;
                case CLIENTES_VERIFICADOS:
                    clientesVerificados();
                    break;
                case GESTAO_ACESSO:
                    gestaoAcesso();
                    break;
                case INADIMPLENCIA:
                    inadimplencia();
                    break;
                case LTV:
                    ltv();
                    break;
                case GYM_PASS:
                    gymPass();
                    break;
            }
        } catch (Exception e) {
            System.out.println("nao foi possivel carregar o bi " + bi.name());
            e.printStackTrace();
        } finally {
            javax.faces.context.FacesContext context = javax.faces.context.FacesContext.getCurrentInstance();
            if (context != null) {
                context.release();
            }
        }

        return "carregado";
    }

    private void ltv() {
        System.out.println("comecei LTV ");
        long time1 = System.currentTimeMillis();
        LtvControle ltvControle = (LtvControle) JSFUtilities.getManagedBean("LtvControle");
        ltvControle.calcularMetricas();
        Uteis.logar(null, "Carregou LTV..");
        long time2 = System.currentTimeMillis();
        System.out.println("LTV: " + (time2 - time1) + "ms");
    }

    private void ticketMedio() {
        System.out.println("comecei ticketMedio ");
        long time1 = System.currentTimeMillis();
        BITicketMedioControle ticketMedioControle = (BITicketMedioControle) JSFUtilities.getManagedBean("BITicketMedioControle");
        ticketMedioControle.atualizar(false);
        Uteis.logar(null, "Carregou Ticket Medio..");
        long time2 = System.currentTimeMillis();
        System.out.println("ticketMedio: " + (time2 - time1) + "ms");
    }

    private void gymPass () throws Exception {
        System.out.println("comecei gym pass");
        long time1 = System.currentTimeMillis();
        AcessoGymPassControle acessoGymPassControle = (AcessoGymPassControle) JSFUtilities.getManagedBean("AcessoGymPassControle");
        acessoGymPassControle.prepararDadosGymPass();
        Uteis.logar(null, "Carregou Acesso Gym Pass");
        long time2 = System.currentTimeMillis();
        System.out.println("gym pass: " + (time2 - time1) + "ms");
    }

    private void pendencias() {
        System.out.println("comecei pendencias ");
        long time1 = System.currentTimeMillis();
        PendenciaControleRel pendenciaControle = getControlador(PendenciaControleRel.class);
        pendenciaControle.consultarPendenciaClienteMensagemPorColaboradores(false, false);
        Uteis.logar(null, "Carregou Pendência..");
        long time2 = System.currentTimeMillis();
        System.out.println("pendencias: " + (time2 - time1) + "ms");
    }

    private void conversaoVendasSS() throws Exception {
        System.out.println("comecei conversaoVendasSS ");
        long time1 = System.currentTimeMillis();
        IndiceConversaoVendaSessaoRelControle conversaoSessaoControle = (IndiceConversaoVendaSessaoRelControle) JSFUtilities.getManagedBean("ICVSessaoRelControle");
        if (conversaoSessaoControle == null) {
            conversaoSessaoControle = new IndiceConversaoVendaSessaoRelControle();
            JSFUtilities.setManagedBeanValue("ICVSessaoRelControle", conversaoSessaoControle);
        }
        conversaoSessaoControle.consultarICVS();
        Uteis.logar(null, "Carregou Conversão Venda SS..");
        long time2 = System.currentTimeMillis();
        System.out.println("conversaoVendasSS: " + (time2 - time1) + "ms");
    }

    private void conversaoVendas() {
        System.out.println("comecei conversaoVendas ");
        long time1 = System.currentTimeMillis();
        IndiceConversaoVendaRelControle conversaoVendaControle = getControlador(IndiceConversaoVendaRelControle.class);
        conversaoVendaControle.atualizar(false);
        Uteis.logar(null, "Carregou Conversão Venda ..");
        long time2 = System.currentTimeMillis();
        System.out.println("conversaoVendas: " + (time2 - time1) + "ms");
    }

    private void dcc() {
        System.out.println("comecei dcc ");
        long time1 = System.currentTimeMillis();
        RelContratosRecorrenciaControle contratoRecorreciaControle = getControlador(RelContratosRecorrenciaControle.class);
        contratoRecorreciaControle.atualizar();
        Uteis.logar(null, "Carregou Contrato Recorrência..");
        long time2 = System.currentTimeMillis();
        System.out.println("dcc: " + (time2 - time1) + "ms");
    }

    private void risco() {
        System.out.println("comecei risco ");
        long time1 = System.currentTimeMillis();
        RiscoControle grupoRiscoControle = getControlador(RiscoControle.class);
        grupoRiscoControle.carregar();
        Uteis.logar(null, "Carregou Grupo Risco..");
        long time2 = System.currentTimeMillis();
        System.out.println("risco: " + (time2 - time1) + "ms");
    }

    private void metasFinan() throws Exception {
        System.out.println("comecei metasFinan ");
        long time1 = System.currentTimeMillis();
        MetaFinanceiroBIControle metaFinanceiroBIControle = getControlador(MetaFinanceiroBIControle.class);
        metaFinanceiroBIControle.inicializarMeta();
        Uteis.logar(null, "Carregou Metas Finan..");
        long time2 = System.currentTimeMillis();
        System.out.println("metasFinan: " + (time2 - time1) + "ms");
    }

    private void renovacao() {
        System.out.println("comecei renovacao ");
        long time1 = System.currentTimeMillis();
        RenovacaoSinteticoControle renovacaoControle = getControlador(RenovacaoSinteticoControle.class);
        renovacaoControle.atualizar();
        Uteis.logar(null, "Carregou Indice Renovação..");
        long time2 = System.currentTimeMillis();
        System.out.println("renovacao: " + (time2 - time1) + "ms");
    }

    private void rotatividade() {
        System.out.println("comecei rotatividade ");
        long time1 = System.currentTimeMillis();
        RotatividadeAnaliticoDWControle rotatividadeControle = getControlador(RotatividadeAnaliticoDWControle.class);
        rotatividadeControle.carregar();
        Uteis.logar(null, "Carregou Rotatividade..");
        long time2 = System.currentTimeMillis();
        System.out.println("rotatividade: " + (time2 - time1) + "ms");
    }

    private void clientesVerificados() throws Exception {
        System.out.println("comecei clientesVerificados ");
        long time1 = System.currentTimeMillis();
        ClientesVerificadosRelControle clientesVerificadosRelControle = getControlador(ClientesVerificadosRelControle.class);
        clientesVerificadosRelControle.consultarIndiceVerificacao();
        Uteis.logar(null, "Carregou Clientes Verificados...");
        long time2 = System.currentTimeMillis();
        System.out.println("clientesVerificados: " + (time2 - time1) + "ms");
    }

    private void aulaExperimental() {
        System.out.println("comecei aulaExperimental ");
        long time1 = System.currentTimeMillis();
        RelatorioAgendamentosControle agendamentosControle = getControlador(RelatorioAgendamentosControle.class);
        agendamentosControle.novoBI();
        Uteis.logar(null, "Carregou Agendamento Aula Experimental ..");
        long time2 = System.currentTimeMillis();
        System.out.println("aulaExperimental: " + (time2 - time1) + "ms");
    }

    private void probabilidade() {
        System.out.println("comecei probabilidade ");
        long time1 = System.currentTimeMillis();
        RelControleProbEvas controleProbEvas = getControlador(RelControleProbEvas.class);
        controleProbEvas.filtrarPorOperacaoPorEmpresa();
        Uteis.logar(null, "Carregou Churn Rate..");
        long time2 = System.currentTimeMillis();
        System.out.println("probabilidade: " + (time2 - time1) + "ms");
    }

    private void gestaoAcesso() throws Exception {
        System.out.println("comecei gestaoAcesso ");
        long time1 = System.currentTimeMillis();
        GestaoAcessoRelControle gestaoAcessoRelControle = getControlador(GestaoAcessoRelControle.class);
        gestaoAcessoRelControle.consultarAcessos();
        Uteis.logar(null, "Carregou Gestão Acessos..");
        long time2 = System.currentTimeMillis();
        System.out.println("gestaoAcesso: " + (time2 - time1) + "ms");
    }

    private void inadimplencia() throws Exception {
        System.out.println("comecei inadimplencia ");
        long time1 = System.currentTimeMillis();
        BIInadimplenciaControle biInadimplenciaControle = getControlador(BIInadimplenciaControle.class);
        biInadimplenciaControle.consultarInadimplencia();
        Uteis.logar(null, "Carregou BI Inadimplência...");
        long time2 = System.currentTimeMillis();
        System.out.println("inadimplencia: " + (time2 - time1) + "ms");
    }

    private void controleOperacoes() {
        System.out.println("comecei controleOperacoes ");
        long time1 = System.currentTimeMillis();
        RelControleOperacoesControle controleOperacaoControle = getControlador(RelControleOperacoesControle.class);
        controleOperacaoControle.filtrarPorOperacaoPorEmpresa(false);
        Uteis.logar(null, "Carregou Controle Op..");
        long time2 = System.currentTimeMillis();
        System.out.println("controleOperacoes: " + (time2 - time1) + "ms");
    }


    public <T> T getControlador(Class<? extends T> classControlador) {
        return (T) JSFUtilities.getManagedBean(classControlador.getSimpleName());
    }
}
