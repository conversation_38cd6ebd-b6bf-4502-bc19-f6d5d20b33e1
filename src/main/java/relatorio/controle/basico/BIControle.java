package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.Modulo;
import controle.arquitetura.MenuControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.GrupoTelaControle;
import controle.basico.LtvControle;
import controle.basico.RiscoControle;
import controle.crm.RelatorioAgendamentosControle;
import controle.financeiro.MetaFinanceiroBIControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.ConfiguracaoUsuarioEnum;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.BICarregarTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConfiguracaoSistemaUsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.SerializationUtils;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.controle.financeiro.IndiceConversaoVendaRelControle;
import relatorio.controle.financeiro.IndiceConversaoVendaSessaoRelControle;
import relatorio.controle.sad.BIConviteAulaExperimentalControle;
import relatorio.controle.sad.BITicketMedioControle;
import relatorio.controle.sad.RelContratosRecorrenciaControle;
import relatorio.controle.sad.RelControleOperacoesControle;
import relatorio.controle.sad.RelControleProbEvas;
import relatorio.controle.sad.RenovacaoSinteticoControle;
import relatorio.controle.sad.RotatividadeAnaliticoDWControle;
import relatorio.negocio.comuns.basico.ResultadoBITO;
import relatorio.negocio.comuns.basico.ResultadosBITO;
import servicos.propriedades.PropsService;

import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.OutputStream;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by Rafael on 03/03/2016.
 */
public class BIControle extends SuperControleRelatorio {

    private ConfiguracaoSistemaUsuarioVO configuracaoBI;
    private boolean exibirBIs = false;
    private List listaSelectItemEmpresa;
    private List<ColaboradorVO> listaColaboradorVOs;
    private List<GrupoColaboradorVO> listaGrupoColaborador;
    private List<GrupoColaboradorVO> listaGrupoColaboradorIndividual;
    private EmpresaVO empresaFiltro;
    private UsuarioVO usuarioFiltro;
    private List<ColaboradorVO> listaColaboradoresICV = new ArrayList<ColaboradorVO>();
    private Date dataBaseFiltro;
    private boolean jsfContext = true;
    HashMap<Integer, byte[]> colaboradoresFoto = new HashMap<Integer, byte[]>();
    private boolean biCarregado = false;
    private boolean consultarPorTodosColaboradores;
    private List<BICarregarTO> bisCarregar = new ArrayList<BICarregarTO>();
    private String rotuloBICarregando = "";
    private boolean carregandoBIs = false;
    private BICarregarTO biCarregar = null;
    private String containersRenderiza = "";
    private String bisCarregarScroll = "";
    private ResultadosBITO dadosBIGerado;
    private String colaboradoresSelecionado = "";
    boolean somenteVisiveis;
    boolean exibirFaqtToid = true;
    public String containersBI;
    public Boolean exibirTutorial = Boolean.FALSE;
    private Date dataBaseInicialFiltro;// Restringe os itens a partir desta data
    private boolean permiteConsultarTodasEmpresas = false;
    private String biAtualizar;
    private String reRenderBi;
    private int quantColaboradoresSelecionados;
    private int quantTotalColaboradores;
    private int quantTotalColaboradoresMetaFin;
    private String nomeBi = "";
    private String requisicaoMobileParametro = null;
    private boolean carregarMenuWeb = false;

    public final static int NAO_ATUALIZAR = 0;
    public final static int ATUALIZAR_TODOS = 1;
    public final static int ATUALIZAR_ESPECIFICO = 2;
    private int atualizarBIs = NAO_ATUALIZAR;
    private boolean atualizandoFiltroBonecoTodos = false;
    private Map<String, Map<Integer, String>> selecionadosPorBi = new HashMap<>();
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private boolean apresentarBI_IA = false;
    private boolean apresentarBI_STUDIO = false;
    private boolean exibirModalTodosColab = false;
    private Boolean biRiscoChurn = null;

    private Date dataAtualizacao;

    public BIControle(){
        setPermiteConsultarTodasEmpresas(permissao("ConsultarInfoTodasEmpresas"));
    }

    public void initDados()  {
        try {
            Uteis.logar(null,"Preparando BI Controle");
            inicializarConfiguracaoSistema();
            setEmpresaFiltro(new EmpresaVO());
            setListaColaboradorVOs(new ArrayList<>());
            setListaSelectItemEmpresa(new ArrayList());
            montarListaSelectItemEmpresa();
            setDataBaseFiltro(Calendario.hoje());
            inicializarEmpresaLogado();
            inicializarUsuarioLogado();
            inicializarGruposColaboradores();
            setConsultarPorTodosColaboradores(!validarColaboradorLogado());
            montarConsultoresICV();
            montarFotoColaboradores();
            inicializarGruposColaboradoresIndividual();
            initBI();
            inicializarDadosCache();
            inicialzarTutorial();
            Uteis.logar(null,"Carregando BI's Controle");
            propagarDataBaseControladores();
            apresentarBI_IA =  pesquisaModuloIA();
            apresentarBI_STUDIO = apresentarBiStudio();
        }catch (Exception ex){
            montarErro(ex);
        }
    }

    private void inicializarConfiguracaoSistema() {
        try {
            setConfiguracaoSistemaVO(getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void inicializarDadosCache(){
        dadosBIGerado = new ResultadosBITO();
        dadosBIGerado.setKey(getKey());
        dadosBIGerado.setData(getDataBaseFiltro());
        dadosBIGerado.setEmpresa(getEmpresaFiltro().getCodigo());
        dadosBIGerado.setResultadosBI(new HashMap<>());
    }
    private void montarFotoColaboradores() throws Exception{
        if(!isFotosNaNuvem()) {
            for (GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
                for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                    int codigoPessoa = participante.getColaboradorParticipante().getPessoa().getCodigo();
                    if(colaboradoresFoto.get(codigoPessoa) == null) {
                        this.colaboradoresFoto.put(codigoPessoa,
                                getFacade().getPessoa().obterFotoBanco(codigoPessoa));
                    }
                }
            }
            for (ColaboradorVO participante : listaColaboradoresICV) {
                int codigoPessoa = participante.getPessoa().getCodigo();
                if(colaboradoresFoto.get(codigoPessoa) == null) {
                    this.colaboradoresFoto.put(codigoPessoa,
                            getFacade().getPessoa().obterFotoBanco(codigoPessoa));
                }
            }
        }
    }
    public void inicializarEmpresaLogado() throws Exception {
        if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo() != 0) {
            getEmpresaFiltro().setCodigo(getEmpresaLogado().getCodigo());
            getEmpresaFiltro().setNome(getEmpresaLogado().getNome());
        }

        if (getUsuarioLogado().getAdministrador() && getListaSelectItemEmpresa().size() == 2) {
            SelectItem itemUnico = (SelectItem) getListaSelectItemEmpresa().get(1);
            getEmpresaFiltro().setCodigo((Integer) itemUnico.getValue());
            getEmpresaFiltro().setNome(itemUnico.getLabel());
        }
    }

    private void inicializarUsuarioLogado() throws Exception {
        if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo() != 0) {
            setUsuarioFiltro(new UsuarioVO());
            getUsuarioFiltro().setCodigo(getUsuarioLogado().getCodigo());
            getUsuarioFiltro().setNome(getUsuarioLogado().getNome());
            getUsuarioFiltro().setAdministrador(getUsuarioLogado().getAdministrador());
        }
    }

    private Boolean validarColaboradorLogado() throws Exception {
        try {
            // marca ou desmarca todos os iguais na lista de grupos
            for (GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
                if (grupo.isSemGrupo() && getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                    continue;
                }
                for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                    if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(participante.getColaboradorParticipante().getCodigo()) && !getUsuarioLogado().getPossuiPerfilAcessoAdministrador()) {
                        participante.setGrupoColaboradorParticipanteEscolhido(true);
                    }
                }
            }
            for (ColaboradorVO co : getListaColaboradorVOs()) {
                if (getUsuarioLogado().getColaboradorVO().getCodigo() != 0 && co.getCodigo().equals(getUsuarioLogado().getColaboradorVO().getCodigo())) {
                    co.setColaboradorEscolhidoPendencia(true);
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private void inicializarGruposColaboradores() {
        GrupoTelaControle grupoTelaControle = (GrupoTelaControle) context().getExternalContext().getSessionMap().get("GrupoTelaControle");
        if (grupoTelaControle == null) {
            grupoTelaControle = new GrupoTelaControle();
        }
        setListaGrupoColaborador(new ArrayList<>());

        setListaColaboradorVOs(new ArrayList<>());
        //Adiciona os grupos de acordo com a empresa selecionada
        for (GrupoColaboradorVO grupo : grupoTelaControle.getListaGrupos()) {
            if (grupo.getEmpresa().getCodigo().equals(getEmpresaFiltro().getCodigo())
                    || UteisValidacao.emptyNumber(getEmpresaFiltro().getCodigo())
                    || UteisValidacao.emptyNumber(grupo.getCodigo())) { // usuario sem grupo
                getListaGrupoColaborador().add(grupo);
            }
        }
        //Adiciona os grupos de acordo com a empresa selecionada
        for (ColaboradorVO col : grupoTelaControle.getListaColaboradores()) {
            if (col.getEmpresa().getCodigo().equals(getEmpresaFiltro().getCodigo())) {
                getListaColaboradorVOs().add(col);
            }
        }
    }

    private void  inicializarGruposColaboradoresIndividual(){
        listaGrupoColaboradorIndividual = (List<GrupoColaboradorVO>) SerializationUtils.clone((Serializable) listaGrupoColaborador);
    }

    private void montarListaSelectItemEmpresa() {
        try {
            if ((getEmpresaFiltro().getCodigo() == 0
                        || getUsuarioLogado().getAdministrador()
                    || isPermiteConsultarTodasEmpresas()) && getListaSelectItemEmpresa().isEmpty()) {
                List<SelectItem> objs = new ArrayList<>();
                if (permiteConsultarTodasEmpresas) {
                    objs.add(new SelectItem(0, "TODAS"));
                } else {
                    objs.add(new SelectItem(0, ""));
                }

                montarListaEmpresas();
                List<SelectItem> empresas = getListaEmpresas();
                empresas.remove(0);

                objs.addAll(empresas);
                setListaSelectItemEmpresa(objs);
            }
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList());
            e.printStackTrace();
        }
    }

    public boolean pesquisaModuloIA() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        try {
            if (!UteisValidacao.emptyString(modulos)) {
                String[] arrayModulos = modulos.split(",");
                for (String modulo : arrayModulos) {
                    if (modulo.toUpperCase().equals("IA")) {
                        validarPermissao("ChurnRate", "6.32 - Pacto IA - Churn Rate", getUsuarioLogado());
                        return true;
                    }
                }
            }
        } catch (Exception ex) {
            return false;
        }
        return false;
    }

    private boolean apresentarBiStudio() {
        LoginControle loginControle = getControlador(LoginControle.class);
        return loginControle.isApresentarLinkEstudio() && loginControle.getPermissaoAcessoMenuVO().getIndiceConversao();
    }

    private boolean selecionarColaboradorListaFiltro(){
        boolean algumSelecionado = false;
        colaboradoresSelecionado = "";
        Map<Integer, Boolean> colaboradoresJaSelecionados = new HashMap();
        for (GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                for (ColaboradorVO colaborador : getListaColaboradorVOs()) {
                    if (participante.getColaboradorParticipante().getCodigo().equals(colaborador.getCodigo()) && !colaboradoresJaSelecionados.containsKey(colaborador.getCodigo())) {
                        colaborador.setColaboradorEscolhidoPendencia(participante.getGrupoColaboradorParticipanteEscolhido());
                        colaborador.setColaboradorEscolhido(participante.getGrupoColaboradorParticipanteEscolhido());
                        colaborador.setColaboradorEscolhidoOperacoes(participante.getGrupoColaboradorParticipanteEscolhido());
                        colaborador.setColaboradorEscolhidoRenovacao(participante.getGrupoColaboradorParticipanteEscolhido());
                        if (participante.getGrupoColaboradorParticipanteEscolhido()) {
                            colaboradoresJaSelecionados.put(colaborador.getCodigo(), true);
                        }
                    }else{
                        // Marcar os colaboradores do pseudo Grupo-ICV
                        colaborador.setColaboradorEscolhidoPendencia(colaborador.getColaboradorEscolhido());
                        colaborador.setColaboradorEscolhido(colaborador.getColaboradorEscolhido());
                        colaborador.setColaboradorEscolhidoOperacoes(colaborador.getColaboradorEscolhido());
                        colaborador.setColaboradorEscolhidoRenovacao(colaborador.getColaboradorEscolhido());
                    }
                }
                if(participante.getGrupoColaboradorParticipanteEscolhido()) {
                    algumSelecionado = true;
                    colaboradoresSelecionado += ","+participante.getColaboradorParticipante().getCodigo();
                }
            }
        }

        colaboradoresSelecionado = colaboradoresSelecionado.replaceFirst(",","");

        return algumSelecionado;
    }



    public void initBI()  {
        try {
            boolean algumSelecionado = selecionarColaboradorListaFiltro();

            setConsultarPorTodosColaboradores(!algumSelecionado);
            jsfContext = false;
            setConfiguracaoBI(getFacade().getOrdemItemBIInterfaceFacade().consultarUltimoPorCodigoUsuario(getUsuarioLogado().getCodigo(), ConfiguracaoUsuarioEnum.ORDEM_BI));
            getConfiguracaoBI().montarLista();
            prepararBIsCarregar(false,true,false);
        }catch (Exception ex){
            carregandoBIs = false;
            montarErro(ex);
        }
    }
    public void atualizarFiltrosBI(boolean carregarTodos){
        try {
            boolean algumSelecionado = selecionarColaboradorListaFiltro();
            setConsultarPorTodosColaboradores(!algumSelecionado);
            jsfContext = false;
            prepararBIsCarregar(false,carregarTodos,false);
        }catch (Exception ex){
            carregandoBIs = false;
            montarErro(ex);
        }
    }
    public boolean validarCarregar(boolean carregarTodos,boolean carregar){
        if(carregarTodos){
            return true;
        }
        return carregar;
    }
    private void prepararBIsCarregar(boolean somenteLixeira,boolean carregarTodos,boolean dataBase) throws Exception{
        jsfContext = false;
        bisCarregar.clear();
        biCarregado = false;
        cleanDadosCache();
        containersRenderiza = "";
        if(!getConfiguracaoBI().getClientesVerificados().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getClientesVerificados().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.CLIENTES_VERIFICADOS,"Carregando Clientes Verificados...",carregarTodos));
        }

        if(!getConfiguracaoBI().getPendencia().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getPendencia().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.PENDENCIA,"Carregando Pendências...",carregarTodos));
        }

        if(!getConfiguracaoBI().getConversaoVenda().isNaLixeira() && !somenteLixeira &&  validarCarregar(getConfiguracaoBI().getConversaoVenda().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.CONVERSAO_VENDAS,"Carregando Conversão Venda...",carregarTodos));
        }

        if (apresentarBI_STUDIO) {
            if (!getConfiguracaoBI().getConversaoVendaSS().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getConversaoVendaSS().isCarregar(), carregarTodos)) {
                bisCarregar.add(new BICarregarTO(BIEnum.CONVERSAO_VENDAS_SS, "Carregando Conversão Venda Sessão...", carregarTodos));
            }
        }

        if(!getConfiguracaoBI().getMovContrato().isNaLixeira() && !somenteLixeira &&  validarCarregar(getConfiguracaoBI().getMovContrato().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.ROTATIVIDADE_CONTRATO,"Carregando Rotatividade...",carregarTodos));
        }

        if((!getConfiguracaoBI().getTicketMedio().isNaLixeira() && !somenteLixeira) || (!getConfiguracaoBI().getTicketMedio().isNaLixeira() && (!isBiCarregado() || dataBase))  || (somenteLixeira && getConfiguracaoBI().getTicketMedio().isNaLixeira())){
            bisCarregar.add(new BICarregarTO(BIEnum.TICKET_MEDIO,"Carregando Ticket Médio...",carregarTodos));
        }

        if(!getConfiguracaoBI().getgRisco().isNaLixeira() && !somenteLixeira &&  validarCarregar(getConfiguracaoBI().getgRisco().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.GRUPO_RISCO,"Carregando Grupo de Risco...",carregarTodos));
        }

        if(!getConfiguracaoBI().getContratoR().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getContratoR().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.DCC,"Carregando Contrato Recorrência...",carregarTodos));
        }

        if((!getConfiguracaoBI().getMetasFinan().isNaLixeira() && !somenteLixeira) || (!getConfiguracaoBI().getMetasFinan().isNaLixeira() && (!isBiCarregado() || dataBase))  || (somenteLixeira && getConfiguracaoBI().getMetasFinan().isNaLixeira())){
            bisCarregar.add(new BICarregarTO(BIEnum.METAS_FINANCEIRAS,"Carregando Metas Financeiras...",carregarTodos));
        }

        if(!getConfiguracaoBI().getControleOp().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getControleOp().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.CONTROLE_OPERACOES,"Carregando Controle de Operações de Exceções...",carregarTodos));
        }

        if (apresentarBI_IA) {
            if (!getConfiguracaoBI().getProbEvas().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getProbEvas().isCarregar(), carregarTodos)) {
                bisCarregar.add(new BICarregarTO(BIEnum.PROBABILIDADE_EVASAO, "Carregando Pacto IA...", carregarTodos));
            }
        }

        if(!getConfiguracaoBI().getIndiceRenovacao().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getIndiceRenovacao().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.INDICE_RENOVACAO,"Carregando Índice Renovação...",carregarTodos));
        }

        if(!getConfiguracaoBI().getAulaExperimental().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getAulaExperimental().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.AULA_EXPERIMENTAL,"Carregando Aula Experimental...",carregarTodos));
        }


//        STANDBY
//        if(!getConfiguracaoBI().getConviteBI().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getConviteBI().isCarregar(),carregarTodos)){
//            bisCarregar.add(new BICarregarTO(BIEnum.CONVITE_BI,"Carregando BI de Convites de Aula Experimental...",carregarTodos));
//            containersRenderiza = ",containerConviteBI";
//        }

        if(!getConfiguracaoBI().getGestaoAcesso().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getGestaoAcesso().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.GESTAO_ACESSO,"Carregando Gestão de Acessos...",carregarTodos));
        }

        if(!getConfiguracaoBI().getInadimplencia().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getInadimplencia().isCarregar(),carregarTodos)){
            bisCarregar.add(new BICarregarTO(BIEnum.INADIMPLENCIA,"Carregando Inadimplência...",carregarTodos));
        }

        boolean exibirBI = getFacade().getFinanceiro().getPlanoConta().exibirBI(BIEnum.LTV.getIndice());
        if (exibirBI) {
            if (!getConfiguracaoBI().getLtv().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getLtv().isCarregar(), carregarTodos)) {
                bisCarregar.add(new BICarregarTO(BIEnum.LTV, "Carregando LTV...", carregarTodos));
            }
        }
        exibirBI = getFacade().getFinanceiro().getPlanoConta().exibirBI(BIEnum.GYM_PASS.getIndice());
        if (exibirBI) {
            if(!getConfiguracaoBI().getGymPass().isNaLixeira() && !somenteLixeira && validarCarregar(getConfiguracaoBI().getGymPass().isCarregar(), carregarTodos)) {
                bisCarregar.add(new BICarregarTO(BIEnum.GYM_PASS, "Carregando Gym Pass...", carregarTodos));
            }
        }

        jsfContext = true;
    }
    public void selecionarBIVisiveis(){
        atualizarFiltrosBI(true);
       /* try {
            atualizarFiltrosBI(false);
            if (!UteisValidacao.emptyString(getBisCarregarScroll())) {
                for (String idx : getBisCarregarScroll().split("-")) {
                    if (UteisValidacao.emptyString(idx)) {
                        continue;
                    }
                    for (BICarregarTO bi : bisCarregar) {
                        if (bi.getBi() == BIEnum.getFromIndice(Integer.parseInt(idx)) && !bi.isCarregado()) {
                            bi.setCarregar(true);
                        }
                    }
                }
            }
        }catch (Exception ignored){

        }*/
    }
    public void carregarBITodos(){
        try {
            atualizarBIs = BIControle.ATUALIZAR_TODOS;
            selecionadosPorBi = new HashMap<>();
            configuracaoBI.inicializarCarregandoParaAtualizacao();
            atualizarFiltrosBI(true);
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public void carregarBITodosDataBase(){
        try {
            boolean algumSelecionado = selecionarColaboradorListaFiltro();
            setConsultarPorTodosColaboradores(!algumSelecionado);
            configuracaoBI.inicializarCarregandoParaAtualizacao();
            propagarDataBaseControladores();
            jsfContext = false;
            prepararBIsCarregar(false,true,true);
        }catch (Exception ex){
            montarErro(ex);
        }
    }


    public void carregarAsync(){
        try{
            carregarBIAsyncPrepadado();

            long tempoTotalCarregamento = bisCarregar.stream().mapToLong(BICarregarTO::getTempoCarregamento).sum();
            notificarRecursoEmpresa(RecursoSistema.CARREGAMENTO_BI, tempoTotalCarregamento);
        }catch (Exception ex){
            montarErro(ex);
        }
    }

    public void carregar(){
        try{
            carregandoBIs = true;
            for(BICarregarTO bi : bisCarregar){
                if(bi.isCarregar() && !bi.isCarregado()) {
                    carregandoBIs = true;
                    setRotuloBICarregando(bi.getMensagem());
                    biCarregar = bi;
                    return;
                }
            }
            carregandoBIs = false;
            biCarregado = true;
            biCarregar = null;
            rotuloBICarregando = "";

            long tempoTotalCarregamento = bisCarregar.stream().mapToLong(BICarregarTO::getTempoCarregamento).sum();
            notificarRecursoEmpresa(RecursoSistema.CARREGAMENTO_BI, tempoTotalCarregamento);
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public void carregarBISelecionado(){
        try {
            if(biCarregar != null && !biCarregar.isCarregado() && biCarregar.isCarregar() && carregandoBIs) {
                carregarBIItem(biCarregar);
            }
        }catch (Exception ex){
            Uteis.logar(ex, BIControle.class);
            carregandoBIs = false;
            biCarregar = null;
            montarErro(ex);
        }
    }
    public void carregarBIItem(BICarregarTO bi) throws Exception{
        if(bi.getBi() == null){
            atualizarFiltrosBI(true);
            bi = bisCarregar.get(0);
            bi.setContainerRenderizar(",container-filtro-colaborador");
            Uteis.logar(
                    null, "Carregou Configurações Filtros...");
        } else {
            long a = System.currentTimeMillis();
            FacesContext currentInstance = FacesContext.getCurrentInstance();
            switch (bi.getBi()) {
                case PENDENCIA:
                    pendencias();
                    bi.setContainerRenderizar(",bi-container-pendencia");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case CONTROLE_OPERACOES:
                    controleOperacoes();
                    bi.setContainerRenderizar(",containerControleOp");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case PROBABILIDADE_EVASAO:
                    probabilidade();
                    bi.setContainerRenderizar(",containerProbEvas");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case CONVERSAO_VENDAS:
                    conversaoVendas();
                    bi.setContainerRenderizar(",containerConversao");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case CONVERSAO_VENDAS_SS:
                    conversaoVendasSS();
                    bi.setContainerRenderizar(",containerConversaoSessao");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case DCC:
                    dcc();
                    bi.setContainerRenderizar(",containerRecorrencia");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case GRUPO_RISCO:
                    risco();
                    bi.setContainerRenderizar(",containerRisco");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case METAS_FINANCEIRAS:
                    metasFinan();
                    bi.setContainerRenderizar(",containerMetasFinan");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case TICKET_MEDIO:
                    ticketMedio();
                    bi.setContainerRenderizar(",containerTicketMedio");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case INDICE_RENOVACAO:
                    renovacao();
                    bi.setContainerRenderizar(",containerRenovacao");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case ROTATIVIDADE_CONTRATO:
                    rotatividade();
                    bi.setContainerRenderizar(",containerRotatividade");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case AULA_EXPERIMENTAL:
                    aulaExperimental();
                    bi.setContainerRenderizar(",containerExperimental");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case CLIENTES_VERIFICADOS:
                    clientesVerificados();
                    bi.setContainerRenderizar(",containerClientesVerificados");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case GESTAO_ACESSO:
                    gestaoAcesso();
                    bi.setContainerRenderizar(",bi-container-gestaoAcesso");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case INADIMPLENCIA:
                    inadimplencia();
                    bi.setContainerRenderizar(",bi-container-inadimplencia");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
                case GYM_PASS:
                    gympass();
                    bi.setContainerRenderizar(",bi-container-gymPass");
                    containersRenderiza += bi.getContainerRenderizar();
                    break;
            }
            long b = System.currentTimeMillis();
            bi.setTempoCarregamento(b - a);
        }
        bi.setCarregado(true);
        bi.setCarregar(false);
    }

    private String conversaoVendasSS() throws Exception {
        long time1 = System.currentTimeMillis();
        carregarConversaoSessao();
        getConfiguracaoBI().getConversaoVendaSS().setCarregado(true);
        getConfiguracaoBI().getConversaoVendaSS().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("conversaoVendasSS: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String conversaoVendas() {
        long time1 = System.currentTimeMillis();
        carregarConversao();
        getConfiguracaoBI().getConversaoVenda().setCarregado(true);
        getConfiguracaoBI().getConversaoVenda().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("conversaoVendas: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String dcc() {
        long time1 = System.currentTimeMillis();
        carregarContratoRecorrencia();
        getConfiguracaoBI().getContratoR().setCarregado(true);
        getConfiguracaoBI().getContratoR().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("dcc: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String risco() {
        long time1 = System.currentTimeMillis();
        carregarGrupoRisco();
        getConfiguracaoBI().getgRisco().setCarregado(true);
        getConfiguracaoBI().getgRisco().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("risco: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String metasFinan() throws Exception {
        long time1 = System.currentTimeMillis();
        carregarMetasFinan();
        getConfiguracaoBI().getMetasFinan().setCarregado(true);
        getConfiguracaoBI().getMetasFinan().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("metasFinan: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String renovacao() {
        long time1 = System.currentTimeMillis();
        carregarRenovacao();
        getConfiguracaoBI().getIndiceRenovacao().setCarregado(true);
        getConfiguracaoBI().getIndiceRenovacao().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("renovacao: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String ticketMedio() {
        long time1 = System.currentTimeMillis();
        carregarTicketMedio();
        getConfiguracaoBI().getTicketMedio().setCarregado(true);
        getConfiguracaoBI().getTicketMedio().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("ticketMedio: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String rotatividade() {
        long time1 = System.currentTimeMillis();
        carregarRotatividade();
        getConfiguracaoBI().getMovContrato().setCarregado(true);
        getConfiguracaoBI().getMovContrato().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("rotatividade: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String clientesVerificados() throws Exception {
        long time1 = System.currentTimeMillis();
        carregarClientesVerificados();
        getConfiguracaoBI().getClientesVerificados().setCarregado(true);
        getConfiguracaoBI().getClientesVerificados().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("clientesVerificados: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String aulaExperimental() {
        long time1 = System.currentTimeMillis();
        carregarAulaExperimental();
        getConfiguracaoBI().getAulaExperimental().setCarregado(true);
        getConfiguracaoBI().getAulaExperimental().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("aulaExperimental: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String probabilidade() {
        long time1 = System.currentTimeMillis();
        carregarProbEvas();
        getConfiguracaoBI().getProbEvas().setCarregado(true);
        getConfiguracaoBI().getProbEvas().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("probabilidade: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String gestaoAcesso() throws Exception {
        long time1 = System.currentTimeMillis();
        carregarGestaoAcesso();
        getConfiguracaoBI().getGestaoAcesso().setCarregado(true);
        getConfiguracaoBI().getGestaoAcesso().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("gestaoAcesso: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String inadimplencia() throws Exception {
        long time1 = System.currentTimeMillis();
        carregarBIInadimplencia();
        getConfiguracaoBI().getInadimplencia().setCarregado(true);
        getConfiguracaoBI().getInadimplencia().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("inadimplencia: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String gympass() throws Exception {
        long time1 = System.currentTimeMillis();
        carregarBIGymPass();
        getConfiguracaoBI().getGymPass().setCarregado(true);
        getConfiguracaoBI().getGymPass().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("gympass: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String controleOperacoes() {
        long time1 = System.currentTimeMillis();
        carregarControleOp();
        getConfiguracaoBI().getControleOp().setCarregado(true);
        getConfiguracaoBI().getControleOp().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("controleOperacoes: " + (time2 - time1) + "ms");
        return "carregado";
    }

    private String pendencias() {
        long time1 = System.currentTimeMillis();
        carregarPendencia();
        getConfiguracaoBI().getPendencia().setCarregado(true);
        getConfiguracaoBI().getPendencia().setCarregar(false);
        long time2 = System.currentTimeMillis();
        System.out.println("pendencias: " + (time2 - time1) + "ms");
        return "carregado";
    }




    public String getUrlGame() {
        return getUrlGame(false);
    }

    public String getUrlGame(Boolean redirecionarMenuRelatorio) {
        String ug = PropsService.getPropertyValue(getKey(), PropsService.urlGame);
        if(UteisValidacao.emptyString(ug)){
            ug = PropsService.getPropertyValue(PropsService.urlGameGeral);
        }
        String lgn = "";
        try {
            if (!UteisValidacao.emptyString(ug)) {
                if(JSFUtilities.getRequest().getParameter("m") != null && JSFUtilities.getRequest().getParameter("m").equals("mobile")){
                    initDados();
                }
                JSONObject json = new JSONObject();
                json.put("key", getKey());
                json.put("nomeUsuario", getUsuarioLogado().getNome());
                json.put("email", getUsuarioLogado().getEmail());
                json.put("codigoUsuario", getUsuarioLogado().getCodigo());
                json.put("nomeEmpresa", getEmpresaLogado().getNome());
                json.put("codigoEmpresa", getEmpresaLogado().getCodigo());
                json.put("dataLink", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd-MM-yyyy HH:mm"));
                if(redirecionarMenuRelatorio){
                    json.put("redirecionarRelatorio", "true");
                }
                lgn = Uteis.encriptar(json.toString(), "chave_game_r35u17s");
                getRequiscaoMobile();
            }
        } catch (Exception e) {
            //ignore
        }
        return ug+"/oid?lgn="+lgn;
    }

    public String getIdentifcadorBICache(BIEnum biEnum){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String sessionID = request.getRequestedSessionId();
        return String.format("%s|%s|%s|%s",
                sessionID,
                JSFUtilities.getManagedBeanValue("BIControle.empresaFiltro.codigo"),
                Uteis.getData(getDataBaseFiltro()),
                biEnum.isCachePorColaborador() ? JSFUtilities.getManagedBeanValue("BIControle.colaboradoresSelecionado") : "");
    }

    public ResultadoBITO obterResultadoBIDiaCache(BIEnum biEnum) {
        try {
            String identificador = getIdentifcadorBICache(biEnum) + "|" + biEnum.name();
            ResultadoBITO resultadosBITO = getFacade().getMemCachedManager().ler(ResultadoBITO.class, identificador);
            if (resultadosBITO == null) {
                return null;
            }
            Uteis.logar(null,"OBTIDO DADOS MEMCACHED BI "+ biEnum.name());
            return resultadosBITO;
        }catch (Exception ex){
            return null;
        }
    }
    private void cleanDadosCache(){
       dadosBIGerado = null;
       contarColaboradoresBi("");
    }

    public void adicionarResultadoBI(BIEnum biEnum,ResultadoBITO resultado) {
        try {
            resultado.setDiaGerado(Calendario.hoje());
            if(resultado != null) {
                String identificador = getIdentifcadorBICache(biEnum) + "|" + biEnum.name();
                getFacade().getMemCachedManager().gravar(resultado, identificador);
            }
        } catch (Exception ignored) {
    }
    }
    public boolean validarResultadoBIDia(ResultadoBITO resultado){
        if(resultado == null){
            return true;
        }else if(Calendario.igual(resultado.getDiaGerado(),getDataBaseFiltroBI())){
            return false;
        }
        return false;
    }

    public void propagarDataBaseControladores() throws Exception {
        PendenciaControleRel pendenciaControle = getControlador(PendenciaControleRel.class);
        pendenciaControle.setDataBaseFiltro(getDataBaseFiltroBI());
        RiscoControle riscoControle = getControlador(RiscoControle.class);
        riscoControle.setDataBaseFiltro(getDataBaseFiltroBI());
        RenovacaoSinteticoControle renovacaoControle = getControlador(RenovacaoSinteticoControle.class);
        renovacaoControle.setDataBaseFiltro(getDataBaseFiltroBI());
        IndiceConversaoVendaRelControle conversaoVendaControle = getControlador(IndiceConversaoVendaRelControle.class);
        conversaoVendaControle.setDataBaseFiltro(getDataBaseFiltroBI());
        RotatividadeAnaliticoDWControle rotatividadeControle = getControlador(RotatividadeAnaliticoDWControle.class);
        rotatividadeControle.setDataBaseFiltro(getDataBaseFiltroBI());
        BIConviteAulaExperimentalControle conviteAulaExperimentalControle = getControlador(BIConviteAulaExperimentalControle.class);
        conviteAulaExperimentalControle.setDataBaseFiltro(getDataBaseFiltroBI());
        IndiceConversaoVendaSessaoRelControle conversaoSessaoControle = (IndiceConversaoVendaSessaoRelControle) getControlador("ICVSessaoRelControle");
        if (conversaoSessaoControle == null) {
            conversaoSessaoControle = new IndiceConversaoVendaSessaoRelControle();
        }
        conversaoSessaoControle.setDataBaseFiltro(getDataBaseFiltroBI());
        JSFUtilities.setManagedBeanValue("ICVSessaoRelControle", conversaoSessaoControle);
        RelContratosRecorrenciaControle contratoRecorreciaControle = getControlador(RelContratosRecorrenciaControle.class);
        contratoRecorreciaControle.setDataBaseFiltro(getDataBaseFiltroBI());
        RelControleOperacoesControle controleOperacaoControle = getControlador(RelControleOperacoesControle.class);
        controleOperacaoControle.setDataBaseFiltro(getDataBaseFiltroBI());
        RelControleProbEvas controleProbEvas = getControlador(RelControleProbEvas.class);
        controleProbEvas.setDataBaseFiltro(getDataBaseFiltroBI());
        BITicketMedioControle ticketMedioControle = getControlador(BITicketMedioControle.class);
        ticketMedioControle.setDataBaseFiltro(getDataBaseFiltroBI());
        ticketMedioControle.initConfig();
        RelatorioAgendamentosControle relatorioAgendamentosControle = getControlador(RelatorioAgendamentosControle.class);
        relatorioAgendamentosControle.setDataBaseFiltro(getDataBaseFiltroBI());
        relatorioAgendamentosControle.setEmpresaFiltro(getEmpresaFiltro());
        BIInadimplenciaControle biInadimplenciaControle = getControlador(BIInadimplenciaControle.class);
        biInadimplenciaControle.setDataBaseFiltro(getDataBaseFiltroBI());
        LtvControle biLtvControle = getControlador(LtvControle.class);
        biLtvControle.setDataBaseFiltro(getDataBaseFiltro());
        biLtvControle.initData();
    }

    public void carregarPendencia() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getPendencia().isNaLixeira() || jsfContext) {
            PendenciaControleRel pendenciaControle = getControlador(PendenciaControleRel.class);
            pendenciaControle.consultarPendenciaClienteMensagemPorColaboradores(false, false);
            Uteis.logar(null, "Carregou Pendência..");
        }
    }

    public void carregarRenovacao() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getIndiceRenovacao().isNaLixeira() || jsfContext) {
            RenovacaoSinteticoControle renovacaoControle = getControlador(RenovacaoSinteticoControle.class);
            renovacaoControle.atualizar();
            Uteis.logar(null, "Carregou Indice Renovação..");
        }
    }

    public void validarOrdenacao() {
        getConfiguracaoBI().montarLista();
    }

    public void carregarConversao() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getConversaoVenda().isNaLixeira() || jsfContext) {
            IndiceConversaoVendaRelControle conversaoVendaControle = getControlador(IndiceConversaoVendaRelControle.class);
            conversaoVendaControle.atualizar(false);
            Uteis.logar(null, "Carregou Conversão Venda ..");
        }
    }

    public void carregarAulaExperimental() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getAulaExperimental().isNaLixeira() || jsfContext) {
            RelatorioAgendamentosControle agendamentosControle = getControlador(RelatorioAgendamentosControle.class);
            agendamentosControle.novoBI();
            Uteis.logar(null, "Carregou Agendamento Aula Experimental ..");
        }
    }

    public void carregarRotatividade() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getMovContrato().isNaLixeira() || jsfContext) {
            RotatividadeAnaliticoDWControle rotatividadeControle = getControlador(RotatividadeAnaliticoDWControle.class);
            rotatividadeControle.carregar();
            Uteis.logar(null, "Carregou Rotatividade..");
        }
    }

    public void carregarTicketMedio() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getTicketMedio().isNaLixeira() || jsfContext) {
            BITicketMedioControle ticketMedioControle = getControlador(BITicketMedioControle.class);
            ticketMedioControle.atualizar(false);
            Uteis.logar(null, "Carregou Ticket Medio..");
        }
    }

    public void carregarConviteAulaExperimental() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getConviteBI().isNaLixeira() || jsfContext) {
            BIConviteAulaExperimentalControle conviteAulaExperimentalControle = getControlador(BIConviteAulaExperimentalControle.class);
            conviteAulaExperimentalControle.atualizar();
            Uteis.logar(null, "Carregou Convite BI...");
        }
    }

    public void carregarConversaoSessao() throws Exception {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getConversaoVendaSS().isNaLixeira() || jsfContext) {
            IndiceConversaoVendaSessaoRelControle conversaoSessaoControle = (IndiceConversaoVendaSessaoRelControle) getControlador("ICVSessaoRelControle");
            if (conversaoSessaoControle == null) {
                conversaoSessaoControle = new IndiceConversaoVendaSessaoRelControle();
                JSFUtilities.setManagedBeanValue("ICVSessaoRelControle", conversaoSessaoControle);
            }
            conversaoSessaoControle.consultarICVS();
            Uteis.logar(null, "Carregou Conversão Venda SS..");
        }
    }

    public void carregarGrupoRisco() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getgRisco().isNaLixeira() || jsfContext) {
            RiscoControle grupoRiscoControle = getControlador(RiscoControle.class);
            grupoRiscoControle.carregar();
            Uteis.logar(null, "Carregou Grupo Risco..");
        }
    }

    public void carregarContratoRecorrencia() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getContratoR().isNaLixeira() || jsfContext) {
            RelContratosRecorrenciaControle contratoRecorreciaControle = getControlador(RelContratosRecorrenciaControle.class);
            contratoRecorreciaControle.atualizar();
            Uteis.logar(null, "Carregou Contrato Recorrência..");
        }
    }

    public void carregarMetasFinan() throws Exception {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getMetasFinan().isNaLixeira() || jsfContext) {
            MetaFinanceiroBIControle metaFinanceiroBIControle = getControlador(MetaFinanceiroBIControle.class);
            metaFinanceiroBIControle.inicializarMeta();
            Uteis.logar(null, "Carregou Metas Finan..");
        }
    }

    public void carregarControleOp() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getControleOp().isNaLixeira() || jsfContext) {
            RelControleOperacoesControle controleOperacaoControle = getControlador(RelControleOperacoesControle.class);
            controleOperacaoControle.filtrarPorOperacaoPorEmpresa();
            Uteis.logar(null, "Carregou Controle Op..");
        }
    }

    public void carregarProbEvas() {
        if (jsfContext) {
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getProbEvas().isNaLixeira() || jsfContext) {
            RelControleProbEvas controleProbEvas = getControlador(RelControleProbEvas.class);
            controleProbEvas.filtrarPorOperacaoPorEmpresa();
            Uteis.logar(null, "Carregou Churn Rate..");
        }
    }

    public String inicializarRelatoriosCE() {
        try {
            initBI();
            return "telaRelatorios";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroLogin";
        }
    }

    public void carregarBI() {
        try {
            initBI();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void inicializarCarregarDados() {
        for (SelectItem filtroEmpresa:
             getListaEmpresas()) {
            if(getEmpresaFiltro().getCodigo() == filtroEmpresa.getValue()){
                getEmpresaFiltro().setNome(filtroEmpresa.getLabel());
            }
        }
        if (biCarregado) {
            montarConsultoresICV();
        }
    }

    public void montarConsultoresICV() {
        try {
            if (getUsuarioLogado().getAdministrador() || getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                adicionarColaboradorFiltro(
                        getFacade().getColaborador().consultarColaboradoresQuestionarios(
                                Uteis.obterPrimeiroDiaMes(getDataBaseFiltro()), getDataBaseFiltro(), getEmpresaFiltro().getCodigo(),
                                false, Uteis.NIVELMONTARDADOS_MINIMOS));
            } else {
                List<ColaboradorVO> colaborador = new ArrayList<ColaboradorVO>();
                colaborador.add((ColaboradorVO) getUsuarioLogado().getColaboradorVO().getClone(true));
                adicionarColaboradorFiltro(colaborador);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean getExibirGrupoConsultorICV() {
        return getListaColaboradoresICV().size() >= 1;
    }

    public void adicionarColaboradorFiltro(List<ColaboradorVO> colaboradorVOs) {

        for( int i = 0 ; i < getListaColaboradorVOs().size() ; i++){ // retira os que foram adicionados na consulta anterior
            ColaboradorVO colaboradorNalista = getListaColaboradorVOs().get(i);
            for(int e = 0 ; e < getListaColaboradoresICV().size() ; e++) {
                if(colaboradorNalista.getCodigo().equals(getListaColaboradoresICV().get(e).getCodigo()) || UteisValidacao.emptyNumber(getListaColaboradoresICV().get(e).getCodigo()) ) {
                    getListaColaboradorVOs().remove(i);
                    i--;
                    break;
                }
            }
        }
        for( int i = 0 ; i < colaboradorVOs.size() ; i++){ //adicionam o que foi encontrado na nova
            ColaboradorVO novoCol = colaboradorVOs.get(i);
            for(int e = 0 ; e < getListaColaboradorVOs().size() ; e++) {
                if(novoCol.getCodigo().equals(getListaColaboradorVOs().get(e).getCodigo()) || UteisValidacao.emptyNumber(getListaColaboradorVOs().get(e).getCodigo()) ) {
                    colaboradorVOs.remove(i);
                    i--;
                    break;
                }
            }
        }
        setListaColaboradoresICV(colaboradorVOs);
        getListaColaboradorVOs().addAll(colaboradorVOs);
    }
    public void gravarOrdemBI(){
        try {
            getConfiguracaoBI().montarLista();
            getConfiguracaoBI().setUsuario(getUsuarioLogado());
            getConfiguracaoBI().setDataRegistro(Calendario.hoje());
            getConfiguracaoBI().setTipo(ConfiguracaoUsuarioEnum.ORDEM_BI);
            getFacade().getOrdemItemBIInterfaceFacade().incluir(getConfiguracaoBI());
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public void montarOrdemBI(){
        try {
            getConfiguracaoBI().montarLista();
            getConfiguracaoBI().setUsuario(getUsuarioLogado());
            getConfiguracaoBI().setDataRegistro(Calendario.hoje());
            getConfiguracaoBI().setTipo(ConfiguracaoUsuarioEnum.ORDEM_BI);
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public void voltarParaPadrao(){
        try {
            prepararBIsCarregar(false,true,false);
            ConfiguracaoSistemaUsuarioVO config = new ConfiguracaoSistemaUsuarioVO();
            config.setOrdenacao("");
            config.montarLista();
            config.setUsuario(getUsuarioLogado());
            config.setDataRegistro(Calendario.hoje());
            config.setTipo(ConfiguracaoUsuarioEnum.ORDEM_BI);
            setConfiguracaoBI(config);
            getFacade().getOrdemItemBIInterfaceFacade().incluir(config);
            propagarDataBaseControladores();
        }catch (Exception ex){
            montarErro(ex);
        }
    }
    public void carregarBIAsyncPrepadado() throws Exception{
        long time1 = System.currentTimeMillis();

        List<BICallable> callableTasks = new ArrayList<>();
        Boolean carregarTodos = true;
        if(!getConfiguracaoBI().getClientesVerificados().isNaLixeira() && validarCarregar(getConfiguracaoBI().getClientesVerificados().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.CLIENTES_VERIFICADOS));
        }

        if(!getConfiguracaoBI().getPendencia().isNaLixeira() && validarCarregar(getConfiguracaoBI().getPendencia().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.PENDENCIA));
        }

        if(!getConfiguracaoBI().getConversaoVenda().isNaLixeira() && validarCarregar(getConfiguracaoBI().getConversaoVenda().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.CONVERSAO_VENDAS));
        }

        if (isApresentarBI_STUDIO()) {
            if (!getConfiguracaoBI().getConversaoVendaSS().isNaLixeira() && validarCarregar(getConfiguracaoBI().getConversaoVendaSS().isCarregar(), carregarTodos)) {
                callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                        JSFUtilities.getResponse(), BIEnum.CONVERSAO_VENDAS_SS));
            }
        }

        if(!getConfiguracaoBI().getMovContrato().isNaLixeira() && validarCarregar(getConfiguracaoBI().getMovContrato().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.ROTATIVIDADE_CONTRATO));
        }


        if(!getConfiguracaoBI().getgRisco().isNaLixeira() && validarCarregar(getConfiguracaoBI().getgRisco().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.GRUPO_RISCO));
        }

        if(!getConfiguracaoBI().getContratoR().isNaLixeira() && validarCarregar(getConfiguracaoBI().getContratoR().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.DCC));
        }

        if(!getConfiguracaoBI().getMetasFinan().isNaLixeira()){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.METAS_FINANCEIRAS));
        }

        if(!getConfiguracaoBI().getControleOp().isNaLixeira() && validarCarregar(getConfiguracaoBI().getControleOp().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.CONTROLE_OPERACOES));
        }

        if (isApresentarBI_IA()) {
            if (!getConfiguracaoBI().getProbEvas().isNaLixeira() && validarCarregar(getConfiguracaoBI().getProbEvas().isCarregar(), carregarTodos)) {
                callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                        JSFUtilities.getResponse(), BIEnum.PROBABILIDADE_EVASAO));
            }
        }

        if(!getConfiguracaoBI().getIndiceRenovacao().isNaLixeira() && validarCarregar(getConfiguracaoBI().getIndiceRenovacao().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.INDICE_RENOVACAO));
        }

        if(!getConfiguracaoBI().getAulaExperimental().isNaLixeira() && validarCarregar(getConfiguracaoBI().getAulaExperimental().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.AULA_EXPERIMENTAL));
        }


        boolean exibirBI = getFacade().getFinanceiro().getPlanoConta().exibirBI(BIEnum.LTV.getIndice());
        if (exibirBI) {
            if (!getConfiguracaoBI().getLtv().isNaLixeira() && validarCarregar(getConfiguracaoBI().getLtv().isCarregar(), carregarTodos)) {
                callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                        JSFUtilities.getResponse(), BIEnum.LTV));
            }
        }

        if(!getConfiguracaoBI().getGestaoAcesso().isNaLixeira() && validarCarregar(getConfiguracaoBI().getGestaoAcesso().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.GESTAO_ACESSO));
        }

        if(!getConfiguracaoBI().getInadimplencia().isNaLixeira() && validarCarregar(getConfiguracaoBI().getInadimplencia().isCarregar(),carregarTodos)){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.INADIMPLENCIA));
        }

        if(!getConfiguracaoBI().getGymPass().isNaLixeira() && validarCarregar(getConfiguracaoBI().getGymPass().isCarregar(), carregarTodos)) {
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.GYM_PASS));
        }

        if(!getConfiguracaoBI().getTicketMedio().isNaLixeira()){
            callableTasks.add(new BICallable(JSFUtilities.getRequest(),
                    JSFUtilities.getResponse(), BIEnum.TICKET_MEDIO));

        }
        final ExecutorService executorService = Executors.newFixedThreadPool(Uteis.CORES - 1);
        executorService.invokeAll(callableTasks);
        executorService.shutdown();
        long time2 = System.currentTimeMillis();
        System.out.println((time2 - time1) + "ms");
        notificarRecursoEmpresa(RecursoSistema.CARREGAMENTO_BI, (time2 - time1));
        setBiCarregado(true);
        setExibirBIs(true);
    }

    public void carregarBIAsync() throws Exception{
        carregarBITodos();
        carregarBIAsyncPrepadado();
    }

    public void irTelaBI() {
        try {
            if (getFuncionalidadeControle() != null) {
                getFuncionalidadeControle().setarFuncionalidade();
            }
            if(!biCarregado){
               initDados();
               initBI();
               carregarBIAsync();
            }
            gravarHistoricoAcessoBI(null);
            redirect("/faces/detalheBI.jsp?m=web");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isUsuarioPacto() {
        try {
            return getUsuarioLogado().getUsuarioPactoSolucoes();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return false;
    }

    public EmpresaVO getEmpresaFiltroBI(){
        if(!JSFUtilities.isJSFContext()){
            return new EmpresaVO();
        } else {
            return (EmpresaVO) JSFUtilities.getManagedBean("BIControle.empresaFiltro");
        }
    }

    public List getListaSelectItemEmpresaBI() {
        if(!JSFUtilities.isJSFContext()){
            return new ArrayList();
        } else {
            return (List) JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
        }
    }
    public List<GrupoColaboradorVO> getListaGrupoColaboradorFiltroBI(){
        if(!JSFUtilities.isJSFContext()){
            return new ArrayList<GrupoColaboradorVO>();
        } else {
            return (List) JSFUtilities.getManagedBean("BIControle.listaGrupoColaborador");
        }
    }
    public List<ColaboradorVO> getListaColaboradorFiltroBI(){
        if(!JSFUtilities.isJSFContext()){
            return new ArrayList<ColaboradorVO>();
        } else {
            return (List) JSFUtilities.getManagedBean("BIControle.listaColaboradorVOs");
        }
    }

    public List<ColaboradorVO> getListaColaboradorFiltroBI(String biSelecionado){
        if(!JSFUtilities.isJSFContext()){
            return new ArrayList<ColaboradorVO>();
        } else {
            if((( Map<String, Map<Integer, String>>) JSFUtilities.getManagedBean("BIControle.selecionadosPorBi")).containsKey(biSelecionado)){
                return obterListaColaboradoresMapaSelecionados((( Map<String, Map<Integer, String>>) JSFUtilities.getManagedBean("BIControle.selecionadosPorBi")).get(biSelecionado));
            }
            return (List) JSFUtilities.getManagedBean("BIControle.listaColaboradorVOs");
        }
    }

    private List<ColaboradorVO> obterListaColaboradoresMapaSelecionados(Map<Integer, String> mapaSelecionados) {
        List<ColaboradorVO> colaboradorVOS = new ArrayList<ColaboradorVO>();
        for (Integer codSelecionado  : mapaSelecionados.keySet()) {
            ColaboradorVO colaboradorVO  = new ColaboradorVO();
            colaboradorVO.setCodigo(codSelecionado);
            colaboradorVO.setColaboradorEscolhido(true);
            colaboradorVO.getGrupoEmQueFoiSelecionado().setDescricao(mapaSelecionados.get(codSelecionado));
            colaboradorVO.setColaboradorEscolhidoIndiceConversao(true);
            colaboradorVO.setColaboradorEscolhidoPendencia(true);
            colaboradorVO.setColaboradorEscolhidoOperacoes(true);
            colaboradorVO.setColaboradorEscolhidoRenovacao(true);
            colaboradorVOS.add(colaboradorVO);
        }
        return colaboradorVOS;
    }

    public Date getDataBaseFiltroBI(){
      return getDataBaseFiltro();
    }

    public Boolean getTodosColaboradoresSelecionadosFiltroBI(){
        return (Boolean)JSFUtilities.getManagedBean("BIControle.consultarPorTodosColaboradores");
    }

    public void inicialzarTutorial() throws Exception{
//        String habilitarTutorial = PropsService.getPropertyValue(PropsService.habilitarTutorial);
        String habilitarTutorial = "TRUE";
        if (!habilitarTutorial.toUpperCase().equals("TRUE")){
            setExibirTutorial(false);
        } else {
            String pathInfo = "/detalheBI.jsp";
            setExibirTutorial(!getUsuarioLogado().getDicasEsconder().contains(pathInfo));
        }
    }
    public ConfiguracaoSistemaUsuarioVO getConfiguracaoBI() {
        return configuracaoBI;
    }

    public void setConfiguracaoBI(ConfiguracaoSistemaUsuarioVO configuracaoBI) {
        this.configuracaoBI = configuracaoBI;
    }

    public boolean isExibirBIs() {
        return exibirBIs;
    }

    public void setExibirBIs(boolean exibirBIs) {
        this.exibirBIs = exibirBIs;
    }

    public List<ColaboradorVO> getListaColaboradorVOs() {
        return listaColaboradorVOs;
    }

    public void setListaColaboradorVOs(List<ColaboradorVO> listaColaboradorVOs) {
        this.listaColaboradorVOs = listaColaboradorVOs;
    }

    public List<GrupoColaboradorVO> getListaGrupoColaborador() {
        return listaGrupoColaborador;
    }

    public void setListaGrupoColaborador(List<GrupoColaboradorVO> listaGrupoColaborador) {
        this.listaGrupoColaborador = listaGrupoColaborador;
    }
    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public EmpresaVO getEmpresaFiltro() {
        return empresaFiltro;
    }

    public void setEmpresaFiltro(EmpresaVO empresaFiltro) {
        this.empresaFiltro = empresaFiltro;
    }

    public UsuarioVO getUsuarioFiltro() {
        return usuarioFiltro;
    }

    public void setUsuarioFiltro(UsuarioVO usuarioFiltro) {
        this.usuarioFiltro = usuarioFiltro;
    }

    public boolean isConsultarPorTodosColaboradores() {
        return consultarPorTodosColaboradores;
    }

    public void setConsultarPorTodosColaboradores(boolean consultarPorTodosColaboradores) {
        this.consultarPorTodosColaboradores = consultarPorTodosColaboradores;
    }

    public void paintFoto(OutputStream out, Object data) throws Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String pessoa = request.getParameter("pessoa");
        SuperControle.paintFoto(out, colaboradoresFoto.get(Integer.valueOf(pessoa)));
    }

    public String getUrlFotoNuvem(){
        try {
            Object obj = context().getExternalContext().getRequestMap().get("participante");
            ColaboradorVO participante = null;
            if (obj instanceof ColaboradorVO) {
                participante = (ColaboradorVO) obj;
            } else if (obj instanceof GrupoColaboradorParticipanteVO) {
                participante = ((GrupoColaboradorParticipanteVO) obj).getColaboradorParticipante();
            } else {
                return "";
            }
            return getPaintFotoDaNuvem(participante.getPessoa().getFotoKey());
        }catch (Exception ignored){
            return "";
        }
    }
    public void marcarExibirFaqToid(){
        if(exibirFaqtToid){
            setMsgAlert("startFaqtoidTour();");
        }else{
            setMsgAlert("");
        }
        exibirFaqtToid = false;
    }
    public List<ColaboradorVO> getListaColaboradoresICV() {
        return listaColaboradoresICV;
    }

    public void setListaColaboradoresICV(List<ColaboradorVO> listaColaboradoresICV) {
        this.listaColaboradoresICV = listaColaboradoresICV;
    }

    public Date getDataBaseFiltro() {
        return dataBaseFiltro;
    }

    public String getMesApresentaDataBase(){
        return String.valueOf(dataBaseFiltro.getMonth()+1);
    }

    public void acaoMudarMes() throws Exception{
        FacesContext contexto = FacesContext.getCurrentInstance();
        String mes = contexto.getExternalContext().getRequestParameterMap().get("form:dataInicioRenovacaoInputCurrentDate");
        String primeiroDia = "01/" + mes;
        Date dataAnalise = Formatador.obterData(primeiroDia, "dd/MM/yyyy");
        setDataBaseFiltro(dataAnalise);
        carregar();
    }
    public boolean isDataAlterada(){
        return !Calendario.igual(getDataBaseFiltro(), (Date) JSFUtilities.getManagedBean("BIControle.dataBaseFiltro"));
    }
    public String getDataBase_ApresentarMes(){
        DateFormat dfmt = new SimpleDateFormat(" MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataBaseFiltro());
        return dfmt.format(cal.getTime());
    }
    public String getDataBase_ApresentarMesDia(){
        if(getDataBaseFiltro() != null ) {
            return getDataComDescricaoMesDia(getDataBaseFiltro());
        } else {
            return "";
        }
    }

    public String getDataBase_ApresentarMesAno(){
        if(getDataBaseFiltro() != null ) {
            return getDataComDescricaoMesAno(getDataBaseFiltro());
        } else {
            return "";
        }
    }

    public String getDataBase_Apresentar(){
        return Uteis.getData(dataBaseFiltro);
    }

    public void setDataBaseFiltro(Date dataBaseFiltro) {
        this.dataBaseFiltro = dataBaseFiltro;
    }

    public boolean getDeveExibirPendencias() {
        return getControlador(LoginControle.class).getPermissaoAcessoMenuVO().getPendenciaCliente()
                && !getConfiguracaoBI().getPendencia().isNaLixeira();
    }

    public String getRotuloBICarregando() {
        return rotuloBICarregando;
    }

    public void setRotuloBICarregando(String rotuloBICarregando) {
        this.rotuloBICarregando = rotuloBICarregando;
    }

    public boolean isCarregandoBIs() {
        return carregandoBIs;
    }

    public void setCarregandoBIs(boolean carregandoBIs) {
        this.carregandoBIs = carregandoBIs;
    }

    public BICarregarTO getBiCarregar() {
        if(biCarregar == null){
            biCarregar = new BICarregarTO();
        }
        return biCarregar;
    }


    public void setBiCarregar(BICarregarTO biCarregar) {
        this.biCarregar = biCarregar;
    }

    public boolean isBiCarregado() {
        return biCarregado;
    }

    public void setBiCarregado(boolean biCarregado) {
        this.biCarregado = biCarregado;
    }

    public String getContainersRenderiza() {
        return containersRenderiza;
    }

    public void setContainersRenderiza(String containersRenderiza) {
        this.containersRenderiza = containersRenderiza;
    }

    public String getBisCarregarScroll() {
        return bisCarregarScroll;
    }

    public void setBisCarregarScroll(String bisCarregarScroll) {
        this.bisCarregarScroll = bisCarregarScroll;
    }

    public ResultadosBITO getDadosBIGerado() {
        return dadosBIGerado;
    }

    public void setDadosBIGerado(ResultadosBITO dadosBIGerado) {
        this.dadosBIGerado = dadosBIGerado;
    }

    public boolean isExibirFaqtToid() {
        return exibirFaqtToid;
    }

    public void setExibirFaqtToid(boolean exibirFaqtToid) {
        this.exibirFaqtToid = exibirFaqtToid;
    }

    public String getContainersBI() {
        return containersBI;
    }

    public void setContainersBI(String containersBI) {
        this.containersBI = containersBI;
    }

    public Boolean getExibirTutorial() {
        return exibirTutorial;
    }

    public void setExibirTutorial(Boolean exibirTutorial) {
        this.exibirTutorial = exibirTutorial;
    }

    public String getColaboradoresSelecionado() {
        return colaboradoresSelecionado;
    }

    public void setColaboradoresSelecionado(String colaboradoresSelecionado) {
        this.colaboradoresSelecionado = colaboradoresSelecionado;
    }

    public void carregarClientesVerificados() throws Exception {
        if(jsfContext){
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getClientesVerificados().isNaLixeira()  || jsfContext) {
            ClientesVerificadosRelControle clientesVerificadosRelControle = getControlador(ClientesVerificadosRelControle.class);
            clientesVerificadosRelControle.consultarIndiceVerificacao();
            Uteis.logar(null, "Carregou Clientes Verificados...");
        }
    }


    public Date getDataBaseInicialFiltro() {
        return dataBaseInicialFiltro;
    }

    public void setDataBaseInicialFiltro(Date dataBaseInicialFiltro) {
        this.dataBaseInicialFiltro = dataBaseInicialFiltro;
    }

    public String getDataComDescricaoMesDia(Date date){
        DateFormat dfmt = new SimpleDateFormat("dd 'de' MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(date);
        return dfmt.format(cal.getTime());
    }

    public String getDataComDescricaoMesAno(Date date){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(date);
        return dfmt.format(cal.getTime());
    }

    public String getDataBaseInicialFiltroDescricaoMesAno(){
        if(getDataBaseInicialFiltro() != null ) {
            return getDataComDescricaoMesDia(getDataBaseInicialFiltro());
        } else {
            return "";
        }
    }

    public void carregarGestaoAcesso() throws Exception {
        if(jsfContext){
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getGestaoAcesso().isNaLixeira() || jsfContext) {
            GestaoAcessoRelControle gestaoAcessoRelControle = getControlador(GestaoAcessoRelControle.class);
            gestaoAcessoRelControle.consultarAcessos();
            Uteis.logar(null, "Carregou Gestão Acessos..");
        }
    }

    public void carregarBIInadimplencia() throws Exception {
        if(jsfContext){
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getInadimplencia().isNaLixeira() || jsfContext) {
            BIInadimplenciaControle biInadimplenciaControle = getControlador(BIInadimplenciaControle.class);
            biInadimplenciaControle.consultarInadimplencia();
            Uteis.logar(
                    null, "Carregou BI Inadimplência...");
        }
    }

    public void carregarBIGymPass() throws Exception {
        if(jsfContext){
            validarOrdenacao();
        }
        if (!getConfiguracaoBI().getGymPass().isNaLixeira() || jsfContext) {
            AcessoGymPassControle acessoGymPassControle = getControlador(AcessoGymPassControle.class);
            Uteis.logar(
                    null, "Carregou BI GymPass...");
        }
    }

    public BIEnum setaEnum(String biSelecionado){
        return setaEnum(biSelecionado, false);
    }

    public BIEnum setaEnum(String biSelecionado, boolean addLista){
        contarColaboradoresBi("");
        switch (biSelecionado) {
            case "PENDENCIA":
                setNomeBi("Pendências de Clientes");
                return BIEnum.PENDENCIA;
            case "CONVERSAO_VENDAS":
                setNomeBi("Conversão de Vendas");
                return BIEnum.CONVERSAO_VENDAS;
            case "CONVERSAO_VENDAS_SS":
                setNomeBi("Conversão de vendas por Sessão");
                return BIEnum.CONVERSAO_VENDAS_SS;
            case "METAS_FINANCEIRAS":
                setNomeBi("Metas Financeiras de Venda");
                return BIEnum.METAS_FINANCEIRAS;
            case "TICKET_MEDIO":
                setNomeBi("Ticket Médio de Planos");
                return BIEnum.TICKET_MEDIO;
            case "GRUPO_RISCO":
                setNomeBi("Grupo de Risco");
                return BIEnum.GRUPO_RISCO;
            case "PROBABILIDADE_EVASAO":
                setNomeBi("Pacto IA");
                return BIEnum.PROBABILIDADE_EVASAO;
            case "INDICE_RENOVACAO":
                setNomeBi("Índice Renovação");
                return BIEnum.INDICE_RENOVACAO;
            case "ROTATIVIDADE_CONTRATO":
                setNomeBi("Movimentação de contratos");
                return BIEnum.ROTATIVIDADE_CONTRATO;
            case "DCC":
                setNomeBi("Cobranças por Convênio");
                return BIEnum.DCC;
            case "CONTROLE_OPERACOES":
                setNomeBi("Controle de Operações de Exceções");
                return BIEnum.CONTROLE_OPERACOES;
            case "CLIENTES_VERIFICADOS":
                setNomeBi("Verificação de Clientes");
                return BIEnum.CLIENTES_VERIFICADOS;
            case "AULA_EXPERIMENTAL":
                setNomeBi("Aulas experimentais ");
                return BIEnum.AULA_EXPERIMENTAL;
            case "GESTAO_ACESSO":
                setNomeBi("Gestão de Acessos");
                return BIEnum.GESTAO_ACESSO;
            case "INADIMPLENCIA":
                setNomeBi("Inadimplência");
                return BIEnum.INADIMPLENCIA;
            default:
                setNomeBi("Todos");
                marcarTodosBIs();
                return null;
        }
    }

    private void criaGrupoVazio(List<GrupoColaboradorParticipanteVO> usuarios, String descricao, String descricaoOrdenacao) {
        // cria um grupo
        GrupoColaboradorVO novoGrupo = new GrupoColaboradorVO();
        // coloca os dados no novo grupo
        novoGrupo.setDescricao(descricao);
        novoGrupo.setDescricaoOrdenacao(descricaoOrdenacao);
        novoGrupo.setSemGrupo(true);
        for (GrupoColaboradorParticipanteVO novoParticipante : usuarios) {
            novoGrupo.getGrupoColaboradorParticipanteVOs().add(novoParticipante);
        }
        // adiciona o grupo na lista
        getListaGrupoColaborador().add(novoGrupo);
    }

    public String getAtualizarColaboradoresMetaFinanceira(){
        GrupoTelaControle grupoTelaControle = (GrupoTelaControle) context().getExternalContext().getSessionMap().get("GrupoTelaControle");
        if (grupoTelaControle == null){
            grupoTelaControle = new GrupoTelaControle(null, null, false);
        }
        grupoTelaControle.getInicializarDadosMetaFinanceira();
        listaGrupoColaboradorIndividual = grupoTelaControle.getListaGrupos();
        return("Richfaces.showModalPanel('filtroConversaoColaborador')");
    }

    public void biAtualizarParam() {
        String biAtualizarAbrirConsulta = request().getParameter("biAtualizarAbrirConsulta");
        atualizandoFiltroBonecoTodos = biAtualizarAbrirConsulta.equalsIgnoreCase("carregar");
        if(request().getParameter("reRenderBi") != null) {
            setReRenderBi(request().getParameter("reRenderBi"));
        }

        if (biAtualizarAbrirConsulta != null && biAtualizarAbrirConsulta.trim().equalsIgnoreCase("biAtualizarAbrirConsulta")) {
            atualizarBIs = ATUALIZAR_ESPECIFICO;
            setBiAtualizar(request().getParameter("biAtualizar"));
            setaEnum(getBiAtualizar(), true);
            montarSelecionados();
        }

        if (getBiAtualizar() != null && !getBiAtualizar().trim().isEmpty() && !biAtualizarAbrirConsulta.trim().equals("biAtualizarAbrirConsulta")) {
            guardarSelecionados();
            carregarBISelecionado();
            biAtualizar(setaEnum(getBiAtualizar()));
            atualizarBIs = NAO_ATUALIZAR;
        }

    }

    private void guardarSelecionados(){
        List<ColaboradorVO> colaboradorVOS = ((BIControle) getSession().getAttribute(BIControle.class.getSimpleName())).getListaColaboradorVOs();
        Map<Integer, String> selecionados = new HashMap<>();
        //desmarca todos para depois marcar apenas os escolhidos
        colaboradorVOS.forEach(e->{
            e.setColaboradorEscolhido(false);
            e.setColaboradorEscolhidoRenovacao(false);
        });
        for (GrupoColaboradorVO grupo : listaGrupoColaboradorIndividual) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if(participante.getGrupoColaboradorParticipanteEscolhido()){
                        selecionados.put(participante.getColaboradorParticipante().getCodigo(), participante.getColaboradorParticipante().getGrupoEmQueFoiSelecionado().getDescricao());
                        colaboradorVOS.forEach(e->{
                            if(e.getCodigo().equals(participante.getColaboradorParticipante().getCodigo())){
                                e.setColaboradorEscolhido(true);
                                e.setColaboradorEscolhidoRenovacao(true);
                            }
                        });
                }
            }
        }
        selecionadosPorBi.put(getBiAtualizar(), selecionados);
    }
    private void montarSelecionados(){
        Map<Integer, String> selecionados = selecionadosPorBi.get(getBiAtualizar());
        for (GrupoColaboradorVO grupo : listaGrupoColaboradorIndividual) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                participante.setGrupoColaboradorParticipanteEscolhido(selecionados != null && selecionados.containsKey(participante.getColaboradorParticipante().getCodigo()));
                participante.getColaboradorParticipante().setColaboradorEscolhido(selecionados != null && selecionados.containsKey(participante.getColaboradorParticipante().getCodigo()));
                participante.getColaboradorParticipante().setColaboradorEscolhidoPendencia(selecionados != null && selecionados.containsKey(participante.getColaboradorParticipante().getCodigo()));
                participante.getColaboradorParticipante().setColaboradorEscolhidoOperacoes(selecionados != null && selecionados.containsKey(participante.getColaboradorParticipante().getCodigo()));
                participante.getColaboradorParticipante().setColaboradorEscolhidoRenovacao(selecionados != null && selecionados.containsKey(participante.getColaboradorParticipante().getCodigo()));
            }
            setaEnum(getBiAtualizar());
        }
    }


    public void biAtualizar(BIEnum biEnum) {
        try {
            if (biEnum != null) {
                getBiCarregar().setBi(biEnum);
                carregarBIItem(getBiCarregar());
            } else {
                carregarBIItem(null);
            }
            setBiAtualizar(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void marcarColaborador() {
        String codigo = request().getParameter("participante.codigo");
        Integer cogidoColaborador = 0;
        try {
            cogidoColaborador = Integer.parseInt(codigo);
            for (GrupoColaboradorVO g : listaGrupoColaboradorIndividual) {
                for (GrupoColaboradorParticipanteVO gcp : g.getGrupoColaboradorParticipanteVOs()) {
                    if(gcp.getColaboradorParticipante().getCodigo().equals(cogidoColaborador)) {
                        mudarSelecaoParticipante(gcp, !gcp.getGrupoColaboradorParticipanteEscolhido());
                    }
                    if(g.getTodosParticipantesSelecionados() && !gcp.getGrupoColaboradorParticipanteEscolhido()){
                        g.setTodosParticipantesSelecionados(false);
                    }

                }
            }
            setaEnum(getBiAtualizar());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void mudarSelecaoParticipante(GrupoColaboradorParticipanteVO gcp, boolean escolhido) {
        gcp.setGrupoColaboradorParticipanteEscolhido(escolhido);
        gcp.getColaboradorParticipante().setColaboradorEscolhido(escolhido);
        gcp.getColaboradorParticipante().setColaboradorEscolhidoPendencia( escolhido);
        gcp.getColaboradorParticipante().setColaboradorEscolhidoOperacoes( escolhido);
        gcp.getColaboradorParticipante().setColaboradorEscolhidoRenovacao( escolhido);
    }

    public void setaListaColaboradorVOs(ColaboradorVO cvo) {
        if (cvo != null) {
            if (!getListaColaboradorVOs().contains(cvo)) {
                getListaColaboradorVOs().add(cvo);
            }
        }
    }

    public void marcarGrupo() {
        String codigo = request().getParameter("grupoColaborador.codigo");
        Integer cogidoGrupo = 0;
        try {
            cogidoGrupo = Integer.parseInt(codigo);
            for (GrupoColaboradorVO g : listaGrupoColaboradorIndividual) {
                if (g.getCodigo().equals(cogidoGrupo)) {
                    g.setTodosParticipantesSelecionados(!g.getTodosParticipantesSelecionados());
                    for (GrupoColaboradorParticipanteVO gcp : g.getGrupoColaboradorParticipanteVOs()) {
                        mudarSelecaoParticipante(gcp,g.getTodosParticipantesSelecionados());
                    }
                }
            }
            setaEnum(getBiAtualizar());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void marcarDesmarcarTodos(boolean todos) {
        for (GrupoColaboradorVO g : listaGrupoColaboradorIndividual) {
            g.setTodosParticipantesSelecionados(todos);
            for (GrupoColaboradorParticipanteVO gcp : g.getGrupoColaboradorParticipanteVOs()) {
                mudarSelecaoParticipante(gcp, todos);
            }
        }
    }

    public void marcarTodosBIs() {
        marcarDesmarcarTodos(true);
    }
    public void desmarcarTodosBIs() {
        marcarDesmarcarTodos(false);

    }

    public void contarColaboradoresBi(String bi){
        List<Integer> cols = new ArrayList<>();
        for (GrupoColaboradorVO g : listaGrupoColaborador) {
            for (GrupoColaboradorParticipanteVO gcp : g.getGrupoColaboradorParticipanteVOs()) {
                cols.add(gcp.getColaboradorParticipante().getCodigo());
            }
        }
        if(bi.equals("METAS_FINANCEIRAS")){
            quantTotalColaboradoresMetaFin = cols.size();
        }else{
            setQuantTotalColaboradores(cols.size());
        }

    }

    public String getTotalCustomizado(String bi) {
        int quantidade = selecionadosPorBi.get(bi) == null || selecionadosPorBi.get(bi).size() == 0 ? 0 : selecionadosPorBi.get(bi).size();
        return quantidade == quantTotalColaboradores || quantidade == 0 ? "Todos" : quantidade + " de " +  quantTotalColaboradores;
    }

    public boolean isPermiteConsultarTodasEmpresas() {
        return permiteConsultarTodasEmpresas;
    }

    public void setPermiteConsultarTodasEmpresas(boolean permiteConsultarTodasEmpresas) {
        this.permiteConsultarTodasEmpresas = permiteConsultarTodasEmpresas;
    }

    protected Date getDataBaseInicialFiltroBI() {
        return (Date) JSFUtilities.getManagedBean("PendenciaControleRel.dataBaseInicialFiltro");
    }

    public String getBiAtualizar() {
        return biAtualizar;
    }

    public void setBiAtualizar(String biAtualizar) {
        this.biAtualizar = biAtualizar;
    }

    public String getReRenderBi() {
        return reRenderBi;
    }

    public void setReRenderBi(String reRenderBi) {
        this.reRenderBi = reRenderBi;
    }

    public int getQuantColaboradoresSelecionados() {
        return quantColaboradoresSelecionados;
    }

    public void setQuantColaboradoresSelecionados(int quantColaboradoresSelecionados) {
        this.quantColaboradoresSelecionados = quantColaboradoresSelecionados;
    }

    public int getQuantTotalColaboradores() {
        contarColaboradoresBi("");
        return quantTotalColaboradores;
    }

    public void setQuantTotalColaboradores(int quantTotalColaboradores) {
        this.quantTotalColaboradores = quantTotalColaboradores;
    }

    public String getQtdColPendencia() {
        return getTotalCustomizado(BIEnum.PENDENCIA.name());
    }

    public String getQtdColConvVendas() {
        return getTotalCustomizado(BIEnum.CONVERSAO_VENDAS.name());
    }

    public String getQtdColConvVendasSs() {
        return getTotalCustomizado(BIEnum.CONVERSAO_VENDAS_SS.name());
    }

    public String getQtdColMetasFin() {
        return getTotalCustomizado(BIEnum.METAS_FINANCEIRAS.name());
    }

    public String getQtdColGrupoRisco() {
        return getTotalCustomizado(BIEnum.GRUPO_RISCO.name());
    }

    public String getQtdColIndiceRenov() {
        return getTotalCustomizado(BIEnum.INDICE_RENOVACAO.name());
    }

    public String getQtdColContrOperacoes() {
        return getTotalCustomizado(BIEnum.CONTROLE_OPERACOES.name());
    }

    public String getNomeBi() {
        return nomeBi;
    }

    public void setNomeBi(String nomeBi) {
        this.nomeBi = nomeBi;
    }

    public boolean getRequiscaoMobile() {
        String auxilarString = JSFUtilities.getRequest().getParameter("m");

        if (auxilarString != null) {
            requisicaoMobileParametro = auxilarString;
        }
        if(requisicaoMobileParametro == null || requisicaoMobileParametro.equals("web")){
            carregarMenuWeb = true;
            return false;
        } else if (requisicaoMobileParametro != null && requisicaoMobileParametro.equals("mobile")) {
            carregarMenuWeb = false;
            return true;
        } else {
            return false;
        }
    }

    public boolean isCarregarMenuWeb() {
        return carregarMenuWeb;
    }

    public int getAtualizarBIs() {
        return atualizarBIs;
    }

    public void setAtualizarBIs(int atualizarBIs) {
        this.atualizarBIs = atualizarBIs;
    }

    public boolean isAtualizandoFiltroBonecoTodos() {
        return atualizandoFiltroBonecoTodos;
    }

    public void setAtualizandoFIltroBonecoTodos(boolean atualizandoFiltroBonecoTodos) {
        this.atualizandoFiltroBonecoTodos = atualizandoFiltroBonecoTodos;
    }

    public Map<String, Map<Integer, String>> getSelecionadosPorBi() {
        return selecionadosPorBi;
    }

    public void setSelecionadosPorBi(Map<String, Map<Integer, String>> selecionadosPorBi) {
        this.selecionadosPorBi = selecionadosPorBi;
    }

    public List<GrupoColaboradorVO> getListaGrupoColaboradorIndividual() {
        return listaGrupoColaboradorIndividual;
    }

    public void setListaGrupoColaboradorIndividual(List<GrupoColaboradorVO> listaGrupoColaboradorIndividual) {
        this.listaGrupoColaboradorIndividual = listaGrupoColaboradorIndividual;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        if (configuracaoSistemaVO == null) {
            configuracaoSistemaVO = new ConfiguracaoSistemaVO();
        }
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public boolean isApresentarBI_IA() {
        return apresentarBI_IA;
    }

    public void setApresentarBI_IA(boolean apresentarBI_IA) {
        this.apresentarBI_IA = apresentarBI_IA;
    }

    public boolean isApresentarBI_STUDIO() {
        return apresentarBI_STUDIO;
    }

    public void setApresentarBI_STUDIO(boolean apresentarBI_STUDIO) {
        this.apresentarBI_STUDIO = apresentarBI_STUDIO;
    }

    public Date getDataAtualizacao() {
        return dataAtualizacao;
    }

    public void setDataAtualizacao(Date dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }

    public String  getDataAtualizacao_Apresentar(){
        return "Indicadores gerados em "+Uteis.getDataComHora(dataAtualizacao)+". Clique para atualizar.";
    }

    public void gravarHistoricoAcessoBI(BIEnum biEnum) {
        try {
            getFacade().getUsuario().gravarHistoricoAcessoBI(getUsuarioLogado() == null ? null : getUsuarioLogado().getCodigo(), getEmpresaFiltroBI() != null ? getEmpresaFiltroBI().getCodigo() : (getEmpresaLogado() != null ? getEmpresaLogado().getCodigo() : null), biEnum);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isExibirModalTodosColab() {
        return exibirModalTodosColab;
    }

    public void setExibirModalTodosColab(boolean exibirModalTodosColab) {
        this.exibirModalTodosColab = exibirModalTodosColab;
    }

    public void abrirPanelTodosColab() {
        exibirModalTodosColab = true;
    }

    public boolean getBiRiscoChurn() {
        try {
            if(biRiscoChurn == null){
                String chaves = PropsService.getPropertyValue(PropsService.biRiscoChurn);
                biRiscoChurn = chaves.toLowerCase().contains(getKey().toLowerCase());
            }
        }catch (Exception e){
            biRiscoChurn = Boolean.FALSE;
        }
        return biRiscoChurn;
    }

    public void abrirNovoBI() {
        try {
            limparMsg();
            setMsgAlert("");
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.setUrlGoBackRedirect(null);
            notificarRecursoEmpresa(RecursoSistema.PADRAO_NOVA_TELA_BI);
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String openWindow = "window.open('"
                    + loginControle.getAbrirNovaPlataforma(Modulo.NOVO_ZW.getSiglaModulo())
                    + "&redirect=/adm/bi', '_self')";
            setMsgAlert(openWindow);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }


}
