package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MetaConsultorMesTO;
import negocio.comuns.financeiro.MetaFinanceiraConsultorVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaValoresVO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.contrato.AgrupadorConfiguracaoComissaoRel;
import relatorio.negocio.jdbc.contrato.AgrupadorPrincipalComissaoRel;
import relatorio.negocio.jdbc.contrato.ComissaoRel;

import javax.faces.model.SelectItem;
import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.faces.event.ActionEvent;
import negocio.comuns.contrato.ComissaoRelTO;

public class ComissaoControle extends SuperControleRelatorio {

    private Date dataInicioL = new Date();
    private Date dataFinalL = new Date();
    private Date dataInicioR = null;
    private Date dataContratosLancadosAPartir = null;
    private Date dataCompetencia = new Date();
    private ColaboradorVO atendente = new ColaboradorVO();
    private ColaboradorVO consultor = new ColaboradorVO();
    private String[] tipoContrato;
    private String opcaoImpressao = "CO";
    private String visualizacao = "AP";
    private String tipoValorComissoes = "PORC";
    private List<ContratoDuracaoVO> periodicidadesPossiveis = new ArrayList<ContratoDuracaoVO>();
    private List<SelectItem> tipoRelatorioDF = new ArrayList<SelectItem>();
    private Integer tipoRelatorioEscolhido = 1;
    private EmpresaVO empresa = new EmpresaVO();
    private String campoConsultarAtendente = "";
    private String valorConsultarAtendente = "";
    private String campoConsultarConsultor = "";
    private String valorConsultarConsultor = "";
    private List<ColaboradorVO> listaConsultarAtendente = new ArrayList<ColaboradorVO>();
    private List<ColaboradorVO> listaConsultarConsultor = new ArrayList<ColaboradorVO>();
    private double somatorioComissaoRelatorio = 0.0;
    private List<String> matriculas = new ArrayList<String>();
    private List<Integer> contratos = new ArrayList<Integer>();
    private String msgInformativa = "";
    private int tipoResponsavel = 1;
    private boolean retirarRecebiveisComPendecia = false;
    private boolean considerarCompensacaoOriginal = false;

    public ComissaoControle() throws Exception {
        novo();
    }

    public void novo() throws Exception {
        setPeriodicidadesPossiveis(getListaDuracoesContratos());
        setTipoRelatorioDF(getListaTipoRelatorioDF());
        montarListaEmpresas();
        setEmpresa(getEmpresaLogado());
        verificarEmpresaSelecionada();
    }

    public List<ContratoDuracaoVO> getListaDuracoesContratos() throws Exception {
        return getFacade().getContratoDuracao().consultarNumeroMeses();
    }

    public List<SelectItem> getListaTipoRelatorioDF() throws Exception {
        List<SelectItem> tipos = new ArrayList<SelectItem>();
        for (TipoRelatorioDF tipo : TipoRelatorioDF.values()) {
            if (!tipo.equals(TipoRelatorioDF.COMPETENCIA_QUITADA) && !tipo.equals(TipoRelatorioDF.COMPETENCIA_NAO_QUITADA) && !tipo.equals(TipoRelatorioDF.RECEITAPROVISAO) && !tipo.equals(TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO)) {
                tipos.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
            }
        }
        return tipos;
    }

    public void imprimirPDF() throws Exception {
        try {
            setMensagemDetalhada("", "");
            validarDados();

            setTipoRelatorio("PDF");
            imprimir();
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            montarErro(e.getMessage());
        }
    }

    public void imprimir() {
        try {

            validarDados();

            matriculas = new ArrayList<String>();
            contratos = new ArrayList<Integer>();
            TipoRelatorioDF tipoRelatorio = TipoRelatorioDF.getTipoRelatorioDF(getTipoRelatorioEscolhido());

            List listaRegistro = getFacade().getComissaoGeralConfiguracao().processarDados(
                    tipoRelatorio, getEmpresa().getCodigo(),
                    getDataInicioL(), getDataFinalL(), getDataInicioR(), getDataContratosLancadosAPartir(), getDataCompetencia(),
                    getOpcaoImpressao(), getTipoValorComissoes(),
                    getEmpresa().isComissaoMatriculaRematricula(),
                    getEmpresa().isPagarComissaoManutencaoModalidade(),
                    getEmpresa().isPagarComissaoProdutos(),
                    getEmpresaLogado().isRetirarEdicaoPagamento(),
                    isRetirarRecebiveisComPendecia(),
                    isConsiderarCompensacaoOriginal());
            listaRegistro = filtrar(listaRegistro);

            listaRegistro = ordenarDoisNiveis(listaRegistro);
            if (!listaRegistro.isEmpty()) {
                setListaRelatorio(listaRegistro);
            } else {
                throw new Exception("Não há registros comissionáveis para o período informado.");
            }
            calcularComissaoSeAtingirMetaFinanceira(listaRegistro);
            if(getTipoRelatorio().equals("EXCEL")){
                setListaRelatorio(prepararListaExcel(listaRegistro));
                setRelatorio("sim");
            } else {    
                setRelatorio("sim");
                imprimirRelatorio();
            }
            setMensagemDetalhada("", "");

            setMensagemID("msg_entre_prmrelatorio");
            
        } catch (Exception e) {
            setRelatorio("nao");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void filtrarEOrdenarListaDeRegistrosServlet(List<ComissaoRel> comissaoRelList) throws Exception {
        this.ordenarDoisNiveis(comissaoRelList);
        this.calcularComissaoSeAtingirMetaFinanceira(comissaoRelList);
    }

    public void verificarEmpresaSelecionada() {
        try {
            this.msgInformativa = "";
            if ((this.empresa.getCodigo() != null) && (this.empresa.getCodigo() > 0)) {
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(this.empresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.empresa = empresaVO;
                if (this.empresa.isPagarComissaoSeAtingirMetaFinanceira()) {
                    this.msgInformativa = "A empresa '" + this.empresa.getNome() + "' foi configurada para pagar comissão somente se atingir a Meta Financeira.  " +
                            "Como a meta financeira leva em consideração o faturamento recebido, " +
                            "o cálculo da comissão ficará correto caso seja escolhido o filtro 'Por faturamento recebido'. ";
                }
            } else {
                this.empresa = new EmpresaVO();
            }
            montarSucesso("");
        } catch (Exception e) {
            montarErro(e);

        }

    }

    private void calcularComissaoSeAtingirMetaFinanceira(List listaRegistro) throws Exception {
        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (empresaVO.isPagarComissaoSeAtingirMetaFinanceira()) {
            verificarQualMetaFoiAtingidaMesAMes(listaRegistro);
            for (Object obj : listaRegistro) {
                AgrupadorPrincipalComissaoRel agrupador = (AgrupadorPrincipalComissaoRel) obj;
                for (AgrupadorConfiguracaoComissaoRel agrupadorConfiguracaoComissaoRel : agrupador.getListaAgrupadaConfiguracao()) {
                    if (agrupadorConfiguracaoComissaoRel.verificarListaComissaoMeta()) {
                        // A configuração de comissão não foi configurada para pagar somente se atingir  meta.
                        continue;
                    }
                    for (ComissaoRel comissaoRel : agrupadorConfiguracaoComissaoRel.getComissoes()) {
                        Calendar dataBase = Calendario.getInstance();
                        if (TipoRelatorioDF.getTipoRelatorioDF(getTipoRelatorioEscolhido()) == TipoRelatorioDF.COMPETENCIA) {
                            dataBase.setTime(Calendario.getDataComHoraZerada(getDataCompetencia()));
                        } else if (TipoRelatorioDF.getTipoRelatorioDF(getTipoRelatorioEscolhido()) == TipoRelatorioDF.FATURAMENTO_DE_CAIXA ||
                                TipoRelatorioDF.getTipoRelatorioDF(getTipoRelatorioEscolhido()) == TipoRelatorioDF.FATURAMENTO) {
                            dataBase.setTime(Calendario.getDataComHoraZerada(comissaoRel.getDataPagamento()));
                        } else {
                            dataBase.setTime(Calendario.getDataComHoraZerada(comissaoRel.getDataCompensacao()));
                        }
                        dataBase.set(Calendar.DAY_OF_MONTH, 1);
                        int indiceMeta = agrupador.getListaMetaConsultorMesTO().indexOf(new MetaConsultorMesTO(Calendario.getDataComHoraZerada(dataBase.getTime()), agrupador.getCodigo()));
                        MetaConsultorMesTO metaAtingida = agrupador.getListaMetaConsultorMesTO().get(indiceMeta);
                        if ((metaAtingida.getMetaFinanceiraEmpresaValoresVO().getCodigo() == null) || (metaAtingida.getMetaFinanceiraEmpresaValoresVO().getCodigo() <= 0)) {
                            // não atingiu a meta.
                            comissaoRel.setValorDaComissao(0.0);
                            continue;
                        }
                        comissaoRel.calcularValorComissaoSeAtingirMetaFinanceira(metaAtingida, this.tipoValorComissoes);
                        agrupadorConfiguracaoComissaoRel.setComissaoMetaFinananceiraVO(comissaoRel.getComissaoMetaFinananceiraVO());
                    }

                }
            }
            this.somatorioComissaoRelatorio = 0;
            for (Object obj : listaRegistro) {
                AgrupadorPrincipalComissaoRel agrupador = (AgrupadorPrincipalComissaoRel) obj;
                setSomatorioComissaoRelatorio(getSomatorioComissaoRelatorio() + agrupador.getValorComissao());
            }

        }
    }


    private void verificarQualMetaFoiAtingidaMesAMes(List listaRegistro) throws Exception {
        //Map<Integer,List<MetaConsultorMesTO>> mapaMetaConsultor = new HashMap<Integer, List<MetaConsultorMesTO>>();
        for (Object obj : listaRegistro) {
            AgrupadorPrincipalComissaoRel agrupador = (AgrupadorPrincipalComissaoRel) obj;
            MetaConsultorMesTO metaConsultorMesTO = new MetaConsultorMesTO();
            metaConsultorMesTO.setCodigoColaborador(agrupador.getCodigo());
            ColaboradorVO colaboradorVO = new ColaboradorVO();
            colaboradorVO.setCodigo(metaConsultorMesTO.getCodigoColaborador());
            List<MetaConsultorMesTO> listaMetaConsultor = new ArrayList<MetaConsultorMesTO>();
            if (TipoRelatorioDF.getTipoRelatorioDF(getTipoRelatorioEscolhido()) == TipoRelatorioDF.COMPETENCIA) {
                Calendar mes = Calendario.getInstance();
                mes.setTime(getDataCompetencia());
                mes.set(Calendar.DAY_OF_MONTH, 1);
                metaConsultorMesTO.setMes(mes.getTime());
                consultarMetaAtingida(colaboradorVO, metaConsultorMesTO, mes);
                listaMetaConsultor.add(metaConsultorMesTO);
            } else {
                Calendar vigenciaInicial = Calendario.getInstance();
                Calendar vigenciaFinal = Calendario.getInstance();
                vigenciaInicial.setTime(Calendario.getDataComHoraZerada(this.dataInicioL));
                vigenciaFinal.setTime(Calendario.getDataComHoraZerada(this.dataFinalL));
                vigenciaInicial.set(Calendar.DAY_OF_MONTH, 1);
                vigenciaFinal.set(Calendar.DAY_OF_MONTH, 1);
                while (vigenciaInicial.compareTo(vigenciaFinal) <= 0) {
                    metaConsultorMesTO.setMes(vigenciaInicial.getTime());
                    consultarMetaAtingida(colaboradorVO, metaConsultorMesTO, vigenciaInicial);

                    listaMetaConsultor.add(metaConsultorMesTO);
                    vigenciaInicial.add(Calendar.MONTH, 1);
                }
            }
            agrupador.setListaMetaConsultorMesTO(listaMetaConsultor);
        }

    }

    private void consultarMetaAtingida(ColaboradorVO colaboradorVO, MetaConsultorMesTO metaConsultorMesTO, Calendar dataBase) throws Exception {
        metaConsultorMesTO.setTotalMetaAtingida(getFacade().getMetaFinanceiroBI().obterFaturamentoRecebido(getKey(),
                Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(metaConsultorMesTO.getMes())),
                Uteis.obterUltimoDiaMesUltimaHora(metaConsultorMesTO.getMes()), Arrays.asList(colaboradorVO), new ArrayList<>(), getEmpresa().getCodigo(), true, true, true, "CA".equals(getOpcaoImpressao()), false, false));

        List<MetaFinanceiraEmpresaValoresVO> listaMetaFinanceira = getFacade().getMetaFinanceiraEmpresaValores().consultar(getEmpresa().getCodigo(), dataBase.get(Calendar.MONTH) + 1, dataBase.get(Calendar.YEAR), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        metaConsultorMesTO.setMetaFinanceiraEmpresaValoresVO(new MetaFinanceiraEmpresaValoresVO());
        if (!listaMetaFinanceira.isEmpty()) {
            MetaFinanceiraConsultorVO metaFinanceiraConsultorVO = getFacade().getMetaFinanceiraConsultor().consultarPorColaboradorMetaDaEmpresa(colaboradorVO.getCodigo(), listaMetaFinanceira.get(0).getMetaFinanceiraEmpresa());
            if ((metaFinanceiraConsultorVO != null) && (metaFinanceiraConsultorVO.getCodigo() != null) && (metaFinanceiraConsultorVO.getCodigo() > 0)) {
                for (MetaFinanceiraEmpresaValoresVO obj : listaMetaFinanceira) {
                    obj.setValor((obj.getValor() * metaFinanceiraConsultorVO.getPercentagem()) / 100);
                }
                MetaFinanceiraEmpresaValoresVO metaAtingida = MetaFinanceiraEmpresaValoresVO.verificarValorQualMetaFoiAtingida(metaConsultorMesTO.getTotalMetaAtingida(), listaMetaFinanceira);
                metaConsultorMesTO.setMetaFinanceiraEmpresaValoresVO(metaAtingida);
            }
        }
    }

    private List filtrar(List listaRegistro) {
        List<ComissaoRel> listaFiltrada = (List<ComissaoRel>) listaRegistro;
        listaFiltrada = filtrarPeriodicidade(listaFiltrada);
        listaFiltrada = filtrarTipoPagamento(listaFiltrada);
        listaFiltrada = filtrarConsultor(listaFiltrada);
        if (getTipoRelatorioEscolhido().equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo())
                || getTipoRelatorioEscolhido().equals(TipoRelatorioDF.RECEITA.getCodigo())   ) {
            if(tipoResponsavel == 1) {  // responsavel pelo lançamento de contratos
                listaFiltrada = filtrarResponsavelLancamento(listaFiltrada);
            } else { // responsavel pelo lançamento dos pagamentos
                listaFiltrada = filtrarResponsavelRecebimento(listaFiltrada);
            }
        }
        if (getTipoValorComissoes().equals("FIXO")) {
            listaFiltrada = filtrarContratosIguaisPagamentoFixo(listaFiltrada);
        }
        if (getTipoRelatorioEscolhido().equals(TipoRelatorioDF.FATURAMENTO.getCodigo())) {
            listaFiltrada = filtrarResponsavelLancamento(listaFiltrada);
        }
        return listaFiltrada;
    }

    private List<ComissaoRel> filtrarPeriodicidade(List<ComissaoRel> lista) {
        boolean marcouPeriodicidade = false;
        List<ComissaoRel> listaFiltrada = new ArrayList<ComissaoRel>();
        for (ComissaoRel comissaoRel : lista) {
            for (ContratoDuracaoVO duracao : getPeriodicidadesPossiveis()) {
                if (duracao.getSelecionado()) {
                    marcouPeriodicidade = true;
                    if (comissaoRel.getDuracaoContrato().equals(duracao.getNumeroMeses())) {
                        listaFiltrada.add(comissaoRel);
                    }
                }
            }
        }
        if (!marcouPeriodicidade) {
            listaFiltrada.addAll(lista);
        }
        return listaFiltrada;
    }

    private List<ComissaoRel> filtrarTipoPagamento(List<ComissaoRel> lista) {
        List<ComissaoRel> listaFiltrada = new ArrayList<ComissaoRel>();
        for (ComissaoRel comissaoRel : lista) {
            //Filtro de Tipo do Pagamento
            for (String tipoPagamento : getTipoContrato()) {
                if (tipoPagamento.equals(comissaoRel.getTipoContrato())) {
                    listaFiltrada.add(comissaoRel);
                }
            }
        }
        if (getTipoContrato().length == 0) {
            listaFiltrada.addAll(lista);
        }
        return listaFiltrada;
    }

    private List<ComissaoRel> filtrarConsultor(List<ComissaoRel> lista) {
        List<ComissaoRel> listaFiltrada = new ArrayList<ComissaoRel>();
        if (getConsultor() == null || getConsultor().getCodigo() == 0) {
            listaFiltrada.addAll(lista);
        } else {
            for (ComissaoRel comissaoRel : lista) {
                if (getConsultor().getCodigo().equals(comissaoRel.getConsultorResponsavel().getCodigo())) {
                    listaFiltrada.add(comissaoRel);
                }
            }
        }
        return listaFiltrada;
    }

    private List<ComissaoRel> filtrarResponsavelRecebimento(List<ComissaoRel> lista) {
        List<ComissaoRel> listaFiltrada = new ArrayList<ComissaoRel>();
        if (getAtendente() == null || getAtendente().getCodigo() == 0) {
            listaFiltrada.addAll(lista);
        } else {
            for (ComissaoRel comissaoRel : lista) {
                if (getAtendente().getCodigo().equals(comissaoRel.getResponsavelRecebimento().getCodigo())) {
                    listaFiltrada.add(comissaoRel);
                }
            }
        }
        return listaFiltrada;
    }

    private List<ComissaoRel> filtrarResponsavelLancamento(List<ComissaoRel> lista) {
        List<ComissaoRel> listaFiltrada = new ArrayList<ComissaoRel>();
        if (getAtendente() == null || getAtendente().getCodigo() == 0) {
            listaFiltrada.addAll(lista);
        } else {
            for (ComissaoRel comissaoRel : lista) {
                if (getAtendente().getCodigo().equals(comissaoRel.getResponsavelLancamento().getColaboradorVO().getCodigo())) {
                    listaFiltrada.add(comissaoRel);
                }
            }
        }
        return listaFiltrada;
    }

    private List<ComissaoRel> filtrarContratosIguaisPagamentoFixo(List<ComissaoRel> lista) {
        ComissaoRel comissaoRel;
        ComissaoRel comissaoRel2;
        for (int x=0;x < lista.size();x++) {
            comissaoRel = lista.get(x);
            for (int y=x+1;y < lista.size();y++) {
                comissaoRel2 = lista.get(y);

                if (isNumeroNaoNulosIguais(comissaoRel.getCodigoCliente(), comissaoRel2.getCodigoCliente()) &&
                        isNumeroNaoNulosIguais(comissaoRel.getConsultorResponsavel().getCodigo(), comissaoRel2.getConsultorResponsavel().getCodigo()) &&
                        isNumeroNaoNulosIguais(comissaoRel.getCodigoContrato(), comissaoRel2.getCodigoContrato())) {
                    comissaoRel.setValor(new BigDecimal(comissaoRel.getValor()).add(new BigDecimal(comissaoRel2.getValor())).doubleValue());
                    comissaoRel.setProdutosPagos(comissaoRel.getProdutosPagos()+comissaoRel2.getProdutosPagos());
                    comissaoRel.getMapaContratoValor().put(comissaoRel.getCodigoContrato(), comissaoRel.getValor());
                    if (!comissaoRel.getFormaPagamento().contains(comissaoRel2.getFormaPagamento())) {
                        comissaoRel.setFormaPagamento(comissaoRel.getFormaPagamento()+";"+comissaoRel2.getFormaPagamento());
                    }
                    lista.remove(y);
                    y--;
                }
            }
        }
        return lista;
    }

    private boolean isNumeroNaoNulosIguais(Integer num1, Integer num2) {
        return num1 != null && num2 != null && num1.intValue() == num2.intValue();
    }

    private List ordenarDoisNiveis(List listaRegistro) {
        setSomatorioComissaoRelatorio(0.0);
        List<AgrupadorPrincipalComissaoRel> listaAgPrincipal = new ArrayList<AgrupadorPrincipalComissaoRel>();
        for (Object obj : listaRegistro) {
            ComissaoRel comissaoRel = (ComissaoRel) obj;
            AgrupadorPrincipalComissaoRel agPrincipal = existeAgPrincipal(listaAgPrincipal, comissaoRel);
            if (agPrincipal == null) {
                agPrincipal = new AgrupadorPrincipalComissaoRel();
                if (getOpcaoImpressao().equals("SO")) {
                    if ((getTipoRelatorioEscolhido().equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo())
                        || getTipoRelatorioEscolhido().equals(TipoRelatorioDF.RECEITA.getCodigo())) && tipoResponsavel == 2  ) {
                        agPrincipal.setCodigo(comissaoRel.getResponsavelRecebimento().getCodigo());
                        agPrincipal.setNome(comissaoRel.getResponsavelRecebimento().getPessoa_Apresentar());
                    } else {
                        agPrincipal.setCodigo(comissaoRel.getResponsavelLancamento().getColaboradorVO().getCodigo());
                        agPrincipal.setNome(comissaoRel.getResponsavelLancamento_apresentar());
                    }
                } else if (getOpcaoImpressao().equals("RL")) {
                    agPrincipal.setCodigo(comissaoRel.getResponsavelLancamento().getColaboradorVO().getCodigo());
                    agPrincipal.setNome(comissaoRel.getResponsavelLancamento_apresentar());
                } else {
                    agPrincipal.setCodigo(comissaoRel.getConsultorResponsavel().getCodigo());
                    agPrincipal.setNome(comissaoRel.getConsultorResponsavel().getPessoa_Apresentar());
                }
                listaAgPrincipal.add(agPrincipal);
            }
        }

        for (Object obj : listaRegistro) {
            ComissaoRel comissaoRel = (ComissaoRel) obj;
            if (comissaoRel.getConfiguracaoVO().getCodigo() != 0 || comissaoRel.getConfiguracaoProdutoVO().getCodigo() != 0) {
                AgrupadorPrincipalComissaoRel agrupadorPrinc = existeAgPrincipal(listaAgPrincipal, comissaoRel);

                AgrupadorConfiguracaoComissaoRel agrupadorConf = existeConfiguracaoNaListaAgrupada(agrupadorPrinc, comissaoRel);
                boolean existeConf = (agrupadorConf != null);
                if (!existeConf) {
                    agrupadorConf = new AgrupadorConfiguracaoComissaoRel();
                    agrupadorConf.setTipoValorComissoes(getTipoValorComissoes());
                    if (comissaoRel.getConfiguracaoVO().getCodigo() > 0) {
                        agrupadorConf.setConfiguracao(comissaoRel.getConfiguracaoVO());
                    } else {
                        agrupadorConf.setConfiguracao(comissaoRel.getConfiguracaoProdutoVO());
                    }
                    agrupadorConf.setComissaoMetaFinananceiraVO(comissaoRel.getComissaoMetaFinananceiraVO());
                }
                agrupadorConf.getComissoes().add(comissaoRel);
                if (!agrupadorPrinc.getListaAgrupadaConfiguracao().contains(agrupadorConf)) {
                    agrupadorPrinc.getListaAgrupadaConfiguracao().add(agrupadorConf);
                }
            }
        }

        for (AgrupadorPrincipalComissaoRel agrupadorPrincipalComissaoRel : listaAgPrincipal) {
            Ordenacao.ordenarLista(agrupadorPrincipalComissaoRel.getListaAgrupadaConfiguracao(), "configuracao");
            setSomatorioComissaoRelatorio(getSomatorioComissaoRelatorio() + agrupadorPrincipalComissaoRel.getValorComissao());
        }

        Ordenacao.ordenarLista(listaAgPrincipal, "nome");

        List<AgrupadorPrincipalComissaoRel> listaAgPrincipal_Apresentar = new ArrayList<AgrupadorPrincipalComissaoRel>();
        for (AgrupadorPrincipalComissaoRel agPrin : listaAgPrincipal) {
            if (agPrin.getListaAgrupadaConfiguracao().size() > 0) {
                listaAgPrincipal_Apresentar.add(agPrin);
                for (AgrupadorConfiguracaoComissaoRel agConf : agPrin.getListaAgrupadaConfiguracao()) {
                    for (ComissaoRel comissao : agConf.getComissoes()) {
                        inserirMatricula(comissao);
                        inserirContrato(comissao);
                    }
                }
            }
        }

        if (visualizacao.equals("A") || visualizacao.equals("S")) {
            listaAgPrincipal_Apresentar = agruparLista(listaAgPrincipal_Apresentar);
        } else if (visualizacao.equals("AP")) {
            listaAgPrincipal_Apresentar = agruparListaDetalhada(listaAgPrincipal_Apresentar);
        }

        return listaAgPrincipal_Apresentar;
    }

    private List<AgrupadorPrincipalComissaoRel> agruparListaDetalhada(List<AgrupadorPrincipalComissaoRel> listaAgPrincipal_apresentar) {
        for (AgrupadorPrincipalComissaoRel agPrin : listaAgPrincipal_apresentar) {
            for (AgrupadorConfiguracaoComissaoRel agConf : agPrin.getListaAgrupadaConfiguracao()) {
                Map<String, ComissaoRel> mapaUnificado = new HashMap<String, ComissaoRel>();
                for (ComissaoRel comi : agConf.getComissoes()) {
                    ComissaoRel comissaoRel = mapaUnificado.get(comi.getMatriculaCliente() + "|" + comi.getFormaPagamento() +"|"+ comi.getCodigoContrato());
                    if (comissaoRel == null) {
                        comi.setQtdParcelas(1);
                        mapaUnificado.put(comi.getMatriculaCliente() + "|" + comi.getFormaPagamento() +"|"+ comi.getCodigoContrato(), comi);
                    } else {
                        comissaoRel.setValor(comissaoRel.getValor() + comi.getValor());
                        comissaoRel.setValorDaComissao(comissaoRel.getValorDaComissao() + comi.getValorDaComissao());
                        comissaoRel.setQtdParcelas(comissaoRel.getQtdParcelas() + 1);
                        mapaUnificado.put(comi.getMatriculaCliente() + "|" + comi.getFormaPagamento() +"|"+ comi.getCodigoContrato(), comissaoRel);
                    }
                }
                List<ComissaoRel> comissaoRels = new ArrayList<ComissaoRel>();
                for (ComissaoRel comissaoMapa : mapaUnificado.values()) {
                    comissaoRels.add(comissaoMapa);
                }
                agConf.setComissoes(comissaoRels);
            }
        }
        return listaAgPrincipal_apresentar;
    }

    private List<AgrupadorPrincipalComissaoRel> agruparLista(List<AgrupadorPrincipalComissaoRel> listaAgPrincipal_apresentar) {
        for (AgrupadorPrincipalComissaoRel agPrin : listaAgPrincipal_apresentar) {
            for (AgrupadorConfiguracaoComissaoRel agConf : agPrin.getListaAgrupadaConfiguracao()) {
                Map<Integer, ComissaoRel> mapaUnificado = new HashMap<Integer, ComissaoRel>();
                for (ComissaoRel comi : agConf.getComissoes()) {
                    ComissaoRel comissaoRel = mapaUnificado.get(comi.getCodigoContrato());
                    if (comissaoRel == null) {
                        comi.setQtdParcelas(1);
                        mapaUnificado.put(comi.getCodigoContrato(), comi);
                    } else {
                        comissaoRel.setValor(comissaoRel.getValor() + comi.getValor());
                        comissaoRel.setValorDaComissao(comissaoRel.getValorDaComissao() + comi.getValorDaComissao());
                        comissaoRel.setQtdParcelas(comissaoRel.getQtdParcelas() + 1);
                        mapaUnificado.put(comi.getCodigoContrato(), comissaoRel);
                    }
                }
                List<ComissaoRel> comissaoRels = new ArrayList<ComissaoRel>();
                for (ComissaoRel comissaoMapa : mapaUnificado.values()) {
                    comissaoRels.add(comissaoMapa);
                }
                agConf.setComissoes(comissaoRels);
            }
        }
        return listaAgPrincipal_apresentar;
    }

    private void inserirMatricula(ComissaoRel obj) {
        String matricula = obj.getMatriculaCliente();

        boolean inserir = true;
        for (String matr : matriculas) {
            if (matr.equals(matricula)) {
                inserir = false;
                break;
            }
        }
        if (inserir) {
            matriculas.add(matricula);
        }
    }

    private void inserirContrato(ComissaoRel obj) {
        if (!UteisValidacao.emptyNumber(obj.getCodigoContrato()) && !contratos.contains(obj.getCodigoContrato())) {
            contratos.add(obj.getCodigoContrato());
        }
    }

    private AgrupadorPrincipalComissaoRel existeAgPrincipal(List<AgrupadorPrincipalComissaoRel> listaAgPrincipal, ComissaoRel comissaoRel) {
        for (AgrupadorPrincipalComissaoRel agPrincipal : listaAgPrincipal) {
            if (getOpcaoImpressao().equals("SO")) {
                if ((getTipoRelatorioEscolhido().equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo())
                    || getTipoRelatorioEscolhido().equals(TipoRelatorioDF.RECEITA.getCodigo())) && tipoResponsavel == 2  ) {
                    if (agPrincipal.getCodigo().equals(comissaoRel.getResponsavelRecebimento().getCodigo())) {
                        return agPrincipal;
                    }
                } else {
                    if (agPrincipal.getCodigo().equals(comissaoRel.getResponsavelLancamento().getColaboradorVO().getCodigo())) {
                        return agPrincipal;
                    }
                }
            } else if (getOpcaoImpressao().equals("RL")) {
                if (agPrincipal.getCodigo().equals(comissaoRel.getResponsavelLancamento().getColaboradorVO().getCodigo())) {
                    return agPrincipal;
                }
            } else {
                if (agPrincipal.getCodigo().equals(comissaoRel.getConsultorResponsavel().getCodigo())) {
                    return agPrincipal;
                }
            }
        }
        return null;
    }

    private AgrupadorConfiguracaoComissaoRel existeConfiguracaoNaListaAgrupada(AgrupadorPrincipalComissaoRel agPrincipal, ComissaoRel comissaoRel) {
        for (AgrupadorConfiguracaoComissaoRel agConf : agPrincipal.getListaAgrupadaConfiguracao()) {
            if (agConf.getConfiguracao().toString().equals(comissaoRel.getConfiguracao())) {
                return agConf;
            }
        }
        return null;
    }

    public void imprimirRelatorio() {
        try {
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros);
            apresentarRelatorioObjetos(parametros);

            setMensagemDetalhada("", "");
           setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void prepareParams(Map<String, Object> params) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        if (visualizacao.equals("AP")) {
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "detalhado" + File.separator);
        } else if (visualizacao.equals("A")) {
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "totalizado_aluno" + File.separator);
        } else if (visualizacao.equals("S")) {
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "totalizado_duracao" + File.separator);
        } else {
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator);
        }

        params.put("nomeRelatorio", "comissaoVariavel");
        params.put("nomeEmpresa", empre.getNome());
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());

        params.put("tituloRelatorio", "Gestão da Comissão");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());

        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());

        params.put("listaObjetos", getListaRelatorio());
        if (TipoRelatorioDF.getTipoRelatorioDF(getTipoRelatorioEscolhido()) == TipoRelatorioDF.COMPETENCIA) {
            Calendar dataIni = Calendario.getInstance();
            dataIni.setTime(getDataCompetencia());
            dataIni.set(Calendar.DAY_OF_MONTH, 1);
            Calendar dataFim = Calendario.getInstance();
            dataFim.setTime(getDataCompetencia());
            dataFim.set(Calendar.DAY_OF_MONTH, dataFim.getActualMaximum(Calendar.DAY_OF_MONTH));
            this.dataInicioL = dataIni.getTime();
            this.dataFinalL = dataFim.getTime();
        }
        params.put("descricaoPeriodo", getDescricaoPeriodo());

        params.put("modoVisualizacao", getVisualizacao());
        params.put("total", getValorComissaoRelatorio());
        params.put("filtros", getFiltros());
        params.put("qtdAlunos", matriculas.size());
        params.put("qtdContratosTotal", contratos.size());
        params.put("totalGeralPago", getTotalGeralPago());
        params.put("comissaoMatriculaRematricula", empre.isComissaoMatriculaRematricula());
        params.put("pagarComissaoProdutos", empre.isPagarComissaoProdutos());
    }

    private String getTotalGeralPago() {
        double valor = 0.0;
        for (Object obj : getListaRelatorio()) {
            AgrupadorPrincipalComissaoRel agPrin = (AgrupadorPrincipalComissaoRel) obj;
            valor += agPrin.getValorConfiguracao();
        }

        return Formatador.formatarValorMonetario(valor);
    }

    public String getFiltros() {
        StringBuilder filtros = new StringBuilder("Filtros: ");
        filtros.append(TipoRelatorioDF.getTipoRelatorioDF(getTipoRelatorioEscolhido()).getDescricao()).append(",");

        if (getOpcaoImpressao().equals("CO")) {
            filtros.append(" Somente Consultores,");
        } else if (getOpcaoImpressao().equals("CA")) {
            filtros.append(" Consultor Atual,");
        } else if (getOpcaoImpressao().equals("RL")) {
            filtros.append(" Somente Responsável Lançamento,");
        }

        if (getTipoValorComissoes().equals("PORC")) {
            filtros.append(" Por porcentagem,");
        } else if (getOpcaoImpressao().equals("FIXO")) {
            filtros.append(" Por valor fixo,");
        }

        for (String tipoPagamento : getTipoContrato()) {
            if (tipoPagamento.equals("MA")) {
                filtros.append(" Matrícula, ");
            } else if (tipoPagamento.equals("RE")) {
                filtros.append(" Rematrícula, ");
            } else if (tipoPagamento.equals("RN")) {
                filtros.append(" Renovação, ");
            }
        }

        if (getTipoContrato().length == 0) {
            filtros.append(" Matrícula,");
            filtros.append(" Rematrícula,");
            filtros.append(" Renovação,");
        }

        boolean marcouPeriodicidade = false;
        for (ContratoDuracaoVO duracao : getPeriodicidadesPossiveis()) {
            if (duracao.getSelecionado()) {
                marcouPeriodicidade = true;
                filtros.append(" ").append(duracao.getNumeroMeses()).append(",");
            }
        }
        if (marcouPeriodicidade) {
            filtros.deleteCharAt(filtros.length() - 1);
            filtros.append(" meses,");
        }
        if (!marcouPeriodicidade) {
            filtros.append(" Qualquer duração,");
        }
        if (getTipoRelatorioEscolhido().equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo())
                || getTipoRelatorioEscolhido().equals(TipoRelatorioDF.RECEITA.getCodigo())   ) {
            if(tipoResponsavel == 1) {  // responsavel pelo lançamento de contratos
                filtros.append(" Operador Contrato,");
            } else { // responsavel pelo lançamento dos pagamentos
                filtros.append(" Operador Pagamento,");
            }
        }

        if (getDataContratosLancadosAPartir() != null) {
            filtros.append(" Somente contratos lançados a partir: ").append(Uteis.getData(getDataContratosLancadosAPartir())).append(",");
        }

        if (filtros.length() > 0) {
            filtros.deleteCharAt(filtros.length() - 1);
        }

        return filtros.toString();
    }

    public String getDesignIReportRelatorio() {
        if (visualizacao.equals("AP")) {
            return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "detalhado" + File.separator + "Comissao.jrxml");
        } else if (visualizacao.equals("A")) {
            return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "totalizado_aluno" + File.separator + "Comissao.jrxml");
        } else if (visualizacao.equals("S")) {
            return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "totalizado_duracao" + File.separator + "Comissao.jrxml");
        } else {
            return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + "Comissao.jrxml");
        }
    }

    private void validarDados() throws ConsistirException {
        if (getTipoRelatorioEscolhido() == null || getTipoRelatorioEscolhido() == 0) {
            throw new ConsistirException("Escolha um Tipo dos Dados");
        }

        if (getTipoRelatorioEscolhido() != 2){
            if (getDataInicioL() == null || getDataFinalL() == null){
                throw new ConsistirException("Informe o período corretamente.");
            }
            if (Calendario.menor(getDataFinalL(), getDataInicioL())){
                throw new ConsistirException("O período final não pode ser menor que o período inicial.");
            }
        }

        if (getTipoRelatorioEscolhido() == 2){
            if (getDataCompetencia() == null){
                throw new ConsistirException("Informe a competência.");
            }
        }

        if (getEmpresa().getCodigo() == null || getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Escolha uma Empresa");
        }

        if (getTipoRelatorioEscolhido() != 2 && verificaPeriodoMeses()) {
            throw new ConsistirException("Período de busca deve ter no máximo 1 mês.");
        }
    }

    private boolean verificaPeriodoMeses() {
        return Calendario.diferencaEmMesesInteiro(getDataInicioL(), getDataFinalL()) > 1;
    }


    public void limparAtendente() {
        setAtendente(new ColaboradorVO());
    }

    public void limparConsultor() {
        setConsultor(new ColaboradorVO());
    }

    public List<SelectItem> getTipoConsultaComboConsultor() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }

    public List<SelectItem> getTipoConsultaComboAtendente() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }

    public void consultarConsultor() {
        try {
            List<ColaboradorVO> objs = new ArrayList<ColaboradorVO>();
            if (getCampoConsultarConsultor().equals("nome")) {
                objs = getFacade().getColaborador().consultarPorNomePessoa(getValorConsultarConsultor(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarConsultor(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarConsultor(new ArrayList<ColaboradorVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarAtendente() {
        try {
            List<ColaboradorVO> objs = new ArrayList<ColaboradorVO>();
            if (getCampoConsultarAtendente().equals("nome")) {
                objs = getFacade().getColaborador().consultarPorNomeOperador(getValorConsultarAtendente(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarAtendente(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarConsultor(new ArrayList<ColaboradorVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarConsultor() {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
        setConsultor(obj);
    }

    public void selecionarAtendente() {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("atendente");
        setAtendente(obj);
    }

    public Date getDataInicioL() {
        return dataInicioL;
    }

    public void setDataInicioL(Date dataInicioL) {
        this.dataInicioL = dataInicioL;
    }

    public Date getDataFinalL() {
        return dataFinalL;
    }

    public void setDataFinalL(Date dataFinalL) {
        this.dataFinalL = dataFinalL;
    }

    public Date getDataInicioR() {
        return dataInicioR;
    }

    public void setDataInicioR(Date dataInicioR) {
        this.dataInicioR = dataInicioR;
    }

    public Date getDataContratosLancadosAPartir() {
        return dataContratosLancadosAPartir;
    }

    public void setDataContratosLancadosAPartir(Date dataContratosLancadosAPartir) {
        this.dataContratosLancadosAPartir = dataContratosLancadosAPartir;
    }

    public ColaboradorVO getAtendente() {
        return atendente;
    }

    public void setAtendente(ColaboradorVO atendente) {
        this.atendente = atendente;
    }

    public ColaboradorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }

    public String[] getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String[] tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public String getOpcaoImpressao() {
        return opcaoImpressao;
    }

    public void setOpcaoImpressao(String opcaoImpressao) {
        this.opcaoImpressao = opcaoImpressao;
    }

    public String getVisualizacao() {
        return visualizacao;
    }

    public void setVisualizacao(String visualizacao) {
        this.visualizacao = visualizacao;
    }

    public String getTipoValorComissoes() {
        return tipoValorComissoes;
    }

    public void setTipoValorComissoes(String tipoValorComissoes) {
        this.tipoValorComissoes = tipoValorComissoes;
    }

    public List<ContratoDuracaoVO> getPeriodicidadesPossiveis() {
        return periodicidadesPossiveis;
    }

    public void setPeriodicidadesPossiveis(List<ContratoDuracaoVO> periodicidadesPossiveis) {
        this.periodicidadesPossiveis = periodicidadesPossiveis;
    }

    public List<SelectItem> getTipoRelatorioDF() {
        return tipoRelatorioDF;
    }

    public void setTipoRelatorioDF(List<SelectItem> tipoRelatorioDF) {
        this.tipoRelatorioDF = tipoRelatorioDF;
    }

    public Integer getTipoRelatorioEscolhido() {
        return tipoRelatorioEscolhido;
    }

    public void setTipoRelatorioEscolhido(Integer tipoRelatorioEscolhido) {
        this.tipoRelatorioEscolhido = tipoRelatorioEscolhido;
    }

    public String getCampoConsultarConsultor() {
        return campoConsultarConsultor;
    }

    public void setCampoConsultarConsultor(String campoConsultarConsultor) {
        this.campoConsultarConsultor = campoConsultarConsultor;
    }

    public String getValorConsultarConsultor() {
        return valorConsultarConsultor;
    }

    public void setValorConsultarConsultor(String valorConsultarConsultor) {
        this.valorConsultarConsultor = valorConsultarConsultor;
    }

    public List<ColaboradorVO> getListaConsultarConsultor() {
        return listaConsultarConsultor;
    }

    public void setListaConsultarConsultor(List<ColaboradorVO> listaConsultarConsultor) {
        this.listaConsultarConsultor = listaConsultarConsultor;
    }

    public String getCampoConsultarAtendente() {
        return campoConsultarAtendente;
    }

    public void setCampoConsultarAtendente(String campoConsultarAtendente) {
        this.campoConsultarAtendente = campoConsultarAtendente;
    }

    public String getValorConsultarAtendente() {
        return valorConsultarAtendente;
    }

    public void setValorConsultarAtendente(String valorConsultarAtendente) {
        this.valorConsultarAtendente = valorConsultarAtendente;
    }

    public List<ColaboradorVO> getListaConsultarAtendente() {
        return listaConsultarAtendente;
    }

    public void setListaConsultarAtendente(List<ColaboradorVO> listaConsultarAtendente) {
        this.listaConsultarAtendente = listaConsultarAtendente;
    }

    @Override
    public EmpresaVO getEmpresa() {
        return empresa;
    }

    @Override
    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public double getSomatorioComissaoRelatorio() {
        return somatorioComissaoRelatorio;
    }

    public void setSomatorioComissaoRelatorio(double somatorioComissaoRelatorio) {
        this.somatorioComissaoRelatorio = somatorioComissaoRelatorio;
    }

    public String getValorComissaoRelatorio() {
        return Formatador.formatarValorMonetario(getSomatorioComissaoRelatorio());
    }

    public Date getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(Date dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public List<String> getMatriculas() {
        return matriculas;
    }

    public void setMatriculas(List<String> matriculas) {
        this.matriculas = matriculas;
    }

    public String getNomeArquivoRelatorioGeradoAgora() {
        if (request().getAttribute("nomeArquivoRelatorioGeradoAgora") != null) {
            return request().getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
        } else {
            return "";
        }
    }

    public String getNomeRefRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"" + hRef + "\"";
        } else {
            return "";
        }
    }

    public String getNomeRefPastaRelatorioRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"relatorio/" + hRef + "\"";
        } else {
            return "";
        }
    }

    public List<Integer> getContratos() {
        return contratos;
    }

    public void setContratos(List<Integer> contratos) {
        this.contratos = contratos;
    }

    private String getDescricaoPeriodo() {
        String periodo = "";
        if (getTipoRelatorioEscolhido().equals(TipoRelatorioDF.COMPETENCIA.getCodigo())) {
            periodo = Uteis.getDataMesAnoConcatenado(getDataCompetencia());
        } else {
            periodo = Formatador.formatarDataPadrao(getDataInicioL()) + "  a  " + Formatador.formatarDataPadrao(getDataFinalL());
        }
        return periodo;
    }

    public String getMsgInformativa() {
        return msgInformativa;
    }

    public void setMsgInformativa(String msgInformativa) {
        this.msgInformativa = msgInformativa;
    }
    
    public List<SelectItem> getListaSelectItemTipoResponsavel() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        // Filtra por responsáveis dos lançamento de contratos e produtos
        objs.add(new SelectItem(1, "Contrato/Produto"));
        // Filtra por responsáveis dos recebimentos
        objs.add(new SelectItem(2, "Recebimento"));
        return objs;
    }

    public int getTipoResponsavel() {
        return tipoResponsavel;
    }

    public void setTipoResponsavel(int tipoResponsavel) {
        this.tipoResponsavel = tipoResponsavel;
    }
    public void verificarTipoSelecionado() {
        try {
            opcaoImpressao = "CO";
        } catch (Exception e) {
            montarErro(e);

        }

    }
    
    public void exportar(ActionEvent evt) throws Exception {
        setMensagemDetalhada("", "");
        try{

            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String extensao = (String) JSFUtilities.getFromActionEvent("tipo", evt);
            setTipoRelatorio("EXCEL");
            extensao = extensao.equals("xls") ? "vnd.ms-excel" : "pdf";
            imprimir();
           // validarDados();


            if (getRelatorio().equals("sim")) {
                exportadorListaControle.exportar(evt, getListaRelatorio(), "", null);
                setMsgAlert("abrirPopup('../UpdateServlet?op=downloadfile&file=" + exportadorListaControle.getFileName() + "&mimetype=application/" + extensao + "','Transacoes', 800,200);" + exportadorListaControle.getMsgAlert());
            } else {
                setMsgAlert("");
               // setMsgAlert("alert('"+getMensagemDetalhada()+"')");
            }
            setSucesso(Boolean.TRUE);
        }catch(Exception e){
            setMsgAlert("");
            montarErro(e);
        }
            
    }

    private List prepararListaExcel(List listaRegistro) throws Exception {
        List listaComissoes = new ArrayList();
        for (Object obj : listaRegistro) {
            AgrupadorPrincipalComissaoRel agrupador = (AgrupadorPrincipalComissaoRel) obj;
            for (AgrupadorConfiguracaoComissaoRel agrupadorConfiguracaoComissaoRel : agrupador.getListaAgrupadaConfiguracao()) {
                Ordenacao.ordenarLista(agrupadorConfiguracaoComissaoRel.getComissoes(), "nomePessoa");
                for(ComissaoRel comissao : agrupadorConfiguracaoComissaoRel.getComissoes()){
                    ComissaoRelTO comissaoTO = comissao.getTO();
                    comissaoTO.setConfiguracao(agrupadorConfiguracaoComissaoRel.getConfiguracao_apresentar());
                    listaComissoes.add(comissaoTO);
                }
            }
        }
        try {
            preencherDataLancamentoContrato(listaComissoes);
        } catch (Exception ignore) {}

        return listaComissoes;
    }

    public void preencherDataLancamentoContrato(List<ComissaoRelTO> listaComissoes) {
        try {
            //descobrir data de lanamento de cada plano possvel da lsita
            for (ComissaoRelTO obj : listaComissoes) {
                try {
                    if (!UteisValidacao.emptyNumber(obj.getCodigoContrato())) {
                        ContratoVO contratoVO = getFacade().getContrato().consultarPorCodigo(obj.getCodigoContrato(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        obj.setDataLancamentoContrato(contratoVO.getDataLancamento());
                    }
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ignore) {
        }
    }

    public boolean isRetirarRecebiveisComPendecia() {
        return retirarRecebiveisComPendecia;
    }

    public void setRetirarRecebiveisComPendecia(boolean retirarRecebiveisComPendecia) {
        this.retirarRecebiveisComPendecia = retirarRecebiveisComPendecia;
    }

    public String getTitleDesconsiderar(){
        return "Este filtro foi desabilitado pois criamos uma forma usual " +
                "de devolução de cheques e juntamente<br/> com ela transformamos este filtro em uma " +
                "configuração presente nas configurações do módulo Financeiro.<br/> Para mais detalhes sobre a nova forma de devolução de cheques, " +
                "<i class=\"fa-icon-question-sign\" style=\"font-size: 18px\"><a target=\"_blank\" href=\"https://pactosolucoes.com.br/ajuda/conhecimento/como-lancar-uma-devolucao-de-cheque-no-sistema-sem-o-financeiro-avancado/\">clique aqui: Devolução de cheque<a></i>";
    }

    public boolean isConsiderarCompensacaoOriginal() {
        return considerarCompensacaoOriginal;
    }

    public void setConsiderarCompensacaoOriginal(boolean considerarCompensacaoOriginal) {
        this.considerarCompensacaoOriginal = considerarCompensacaoOriginal;
    }
}
