/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.basico;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import negocio.comuns.arquitetura.RoboTransientObjectsVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.InadimplenciaGraficoTO;
import negocio.comuns.financeiro.InadimplenciaTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.financeiro.ParcelaEmAbertoControleRel;
import relatorio.negocio.comuns.basico.ResumoPessoaRelBIInadimplenciaVO;
import servicos.impl.dcc.golden.ProcessoPlanilhaTentativaCobranca;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Felipe
 */
public class BIInadimplenciaControle extends BIControle {

    private List<Totalizador> totalizador;
    private boolean incluirCanceladas = true;
    private String onComplete;
    private List<InadimplenciaGraficoTO> dadosGraficoInadimplencia;
    private Date filtroDataMatricula;
    private Integer filtroDiaPagamento;
    private List<ResumoPessoaRelBIInadimplenciaVO> listaApresentarParcelas;
    private String descricaoTitulo;
    private List<InadimplenciaGraficoTO> totalizadorTabela;
    private InadimplenciaTO inadimplenciaBi = new InadimplenciaTO();
    private InadimplenciaTO previsaoBi = new InadimplenciaTO();
    private InadimplenciaTO pagasBi = new InadimplenciaTO();
    private InadimplenciaTO canceladasBi = new InadimplenciaTO();
    private InadimplenciaTO inadimplenciaTotalBi = new InadimplenciaTO();
    private InadimplenciaTO previsaoTotalBi = new InadimplenciaTO();
    private InadimplenciaTO pagasTotalBi = new InadimplenciaTO();
    private Date dataInicioExportar;
    private Date dataFinalExportar;
    private boolean apresentarMensagemReprocessar = false;
    private String tipoParcelaDetalhado;
    private Boolean desconsiderarCanceladasBi = false;
    private String itemExportacao = "";

    public void consultarInadimplencia() throws Exception {
        processarBIInadimplencia(false);
    }

    public void atualizarInadimplencia() {
        try {
            gravarHistoricoAcessoBI(BIEnum.INADIMPLENCIA);
            processarBIInadimplencia(true);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            montarErro(ex.getMessage());
        }
    }

    private void validarEmpresa() throws Exception {
        if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
            throw new Exception("O campo empresa deve ser informado");
        }
    }

    public List<Totalizador> getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(List<Totalizador> totalizador) {
        this.totalizador = totalizador;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public List<InadimplenciaGraficoTO> getDadosGraficoInadimplencia() {
        if (dadosGraficoInadimplencia == null) {
            dadosGraficoInadimplencia = new ArrayList<>();
        }
        return dadosGraficoInadimplencia;
    }

    public void setDadosGraficoInadimplencia(List<InadimplenciaGraficoTO> dadosGraficoInadimplencia) {
        this.dadosGraficoInadimplencia = dadosGraficoInadimplencia;
    }

    public String getDadosInadimplenciaJSON() {
        return this.dadosGraficoInadimplencia != null ? new JSONArray(this.dadosGraficoInadimplencia).toString() : new JSONArray().toString();
    }

    public class Totalizador {
        private InadimplenciaTO previsao = new InadimplenciaTO();
        private InadimplenciaTO pagas = new InadimplenciaTO();
        private InadimplenciaTO inadimplencia = new InadimplenciaTO();
        private boolean media = false;

        public Totalizador() {

        }

        public Totalizador(JSONObject json) {
            previsao = new InadimplenciaTO(json.getJSONObject("previsao"));
            pagas = new InadimplenciaTO(json.getJSONObject("pagas"));
            inadimplencia = new InadimplenciaTO(json.getJSONObject("inadimplencia"));
            media = json.getBoolean("media");
        }

        public InadimplenciaTO getPrevisao() {
            return previsao;
        }

        public void setPrevisao(InadimplenciaTO previsao) {
            this.previsao = previsao;
        }

        public InadimplenciaTO getPagas() {
            return pagas;
        }

        public void setPagas(InadimplenciaTO pagas) {
            this.pagas = pagas;
        }

        public InadimplenciaTO getInadimplencia() {
            return inadimplencia;
        }

        public void setInadimplencia(InadimplenciaTO inadimplencia) {
            this.inadimplencia = inadimplencia;
        }

        public boolean isMedia() {
            return media;
        }

        public void setMedia(boolean media) {
            this.media = media;
        }
    }

    private void processarBIInadimplencia(boolean atualizarAgora) {
        try {
            validarEmpresa();
            setTotalizador(new ArrayList<>());
            setDadosGraficoInadimplencia(new ArrayList<>());
            setTotalizadorTabela(new ArrayList<>());


            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.INADIMPLENCIA, getFiltroDTO(atualizarAgora));
            JSONObject dados = new JSONObject(filtroDTO.getJsonDados());

            inadimplenciaBi = new InadimplenciaTO(dados.getJSONObject("inadimplenciaBi"));
            previsaoBi = new InadimplenciaTO(dados.getJSONObject("previsaoBi"));
            pagasBi = new InadimplenciaTO(dados.getJSONObject("pagasBi"));
            canceladasBi = new InadimplenciaTO(dados.getJSONObject("canceladasBi"));
            inadimplenciaTotalBi = new InadimplenciaTO(dados.getJSONObject("inadimplenciaTotalBi"));
            previsaoTotalBi = new InadimplenciaTO(dados.getJSONObject("previsaoTotalBi"));
            pagasTotalBi = new InadimplenciaTO(dados.getJSONObject("pagasTotalBi"));

            JSONArray totalizador = dados.getJSONArray("totalizador");
            for (int e = 0; e < totalizador.length(); e++) {
                getTotalizador().add(new Totalizador(totalizador.getJSONObject(e)));
            }

            JSONArray grafico = dados.getJSONArray("dadosGraficoInadimplencia");
            for (int e = 0; e < grafico.length(); e++) {
                getDadosGraficoInadimplencia().add(new InadimplenciaGraficoTO(grafico.getJSONObject(e)));
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarBIInadimplencia_ANTIGO() throws Exception {
        validarEmpresa();
        setTotalizador(new ArrayList<>());
        setDadosGraficoInadimplencia(new ArrayList<InadimplenciaGraficoTO>());
        setTotalizadorTabela(new ArrayList<InadimplenciaGraficoTO>());

        Double valorPrevisao = 0.0;
        Double valorPagas = 0.0;
        Double valorInadimplencia = 0.0;
        Double totalAlunosPrevisao = 0.0;
        Double totalAlunosPagas = 0.0;
        Double totalAlunosIndadimplencia = 0.0;
        Double totalAlunosCanceladas = 0.0;
        Double valorCanceladas = 0.0;

        Integer empresa = getEmpresaFiltroBI().getCodigo();

        List<Date> mesesConsultar = Uteis.getMesesEntreDatas(Uteis.somarMeses(getDataBaseFiltroBI(), -3), getDataBaseFiltroBI());

        String situacaoParcela = "";

        // CALCULO DAS QUANTIDADES DE ALUNOS NO PERIODO 4 MESES
        Date dataInicioPeriodo = Uteis.obterPrimeiroDiaMes(Uteis.somarMeses(getDataBaseFiltroBI(), -3));
        Date dataFimPeriodo = getDataBaseFiltroBI();
        //TOTAL
        InadimplenciaTO previsao = new InadimplenciaTO();
        previsao.setLabel("Previsão");
        situacaoParcela = desconsiderarCanceladasBi ? " not in ('CA')" : "";
        getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(previsao, empresa, dataInicioPeriodo, dataFimPeriodo, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());

        //PAGAS
        InadimplenciaTO pagas = new InadimplenciaTO();
        pagas.setLabel("Recebidos");
        situacaoParcela = " in ('PG')";
        getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(pagas, empresa, dataInicioPeriodo, dataFimPeriodo, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());

        //INADIMPLÊNCIA
        InadimplenciaTO inadimplencia = new InadimplenciaTO();
        inadimplencia.setLabel("Inadimplência");
        situacaoParcela = desconsiderarCanceladasBi ? " in ('EA')" : " in ('EA', 'CA')";
        getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(inadimplencia, empresa, dataInicioPeriodo, dataFimPeriodo, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());

        //CANCELADAS
        InadimplenciaTO canceladas = new InadimplenciaTO();
        canceladas.setLabel("Parcelas Canceladas");
        situacaoParcela = " in ('CA')";
        getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(canceladas, empresa, dataInicioPeriodo, dataFimPeriodo, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());

        totalAlunosIndadimplencia += inadimplencia.getQuantidadeAlunos();
        totalAlunosPrevisao += previsao.getQuantidadeAlunos();
        totalAlunosPagas += pagas.getQuantidadeAlunos();
        totalAlunosCanceladas += canceladas.getQuantidadeAlunos();

        if (!UteisValidacao.emptyList(mesesConsultar)) {
            for (Date mesPesquisar : mesesConsultar) {

                Date dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(mesPesquisar);
                Date dataFinalPesquisar = Uteis.obterUltimoDiaMes(mesPesquisar);

                situacaoParcela = "";

                //SE FOR O MES QUE ESTÁ SELECIONADO PELO USUARIO A DATA FINAL DA PESQUISA DEVE SER A DATA QUE FOI SELECIONADA NO CALENDÁRIO
                if (Uteis.getDataMesAnoConcatenado(mesPesquisar).equals(Uteis.getDataMesAnoConcatenado(getDataBaseFiltroBI()))) {
                    dataFinalPesquisar = getDataBaseFiltroBI();
                }

                //TOTAL
                previsao = new InadimplenciaTO();
                previsao.setLabel("Previsão");
                previsao.setMesReferencia(mesPesquisar);
                situacaoParcela = desconsiderarCanceladasBi ? " not in ('CA')" : "";
                getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(previsao, empresa, dataInicioPesquisar, dataFinalPesquisar, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());
                previsao.calcularPorcentagem(previsao.getValor());

                //PAGAS
                pagas = new InadimplenciaTO();
                pagas.setLabel("Recebidos");
                pagas.setMesReferencia(mesPesquisar);
                situacaoParcela = " in ('PG')";
                getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(pagas, empresa, dataInicioPesquisar, dataFinalPesquisar, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());
                pagas.calcularPorcentagem(previsao.getValor());

                //INADIMPLÊNCIA
                inadimplencia = new InadimplenciaTO();
                inadimplencia.setLabel("Inadimplência");
                inadimplencia.setMesReferencia(mesPesquisar);
                situacaoParcela = desconsiderarCanceladasBi ? " in ('EA')" : " in ('EA', 'CA')";
                getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(inadimplencia, empresa, dataInicioPesquisar, dataFinalPesquisar, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());
                inadimplencia.calcularPorcentagem(previsao.getValor());

                //CANCELADAS
                canceladas = new InadimplenciaTO();
                canceladas.setLabel("Parcelas Canceladas");
                canceladas.setMesReferencia(mesPesquisar);
                situacaoParcela = " in ('CA')";
                getFacade().getZWFacade().getMovParcela().totalizadorBIInadimplencia(canceladas, empresa, dataInicioPesquisar, dataFinalPesquisar, situacaoParcela, getFiltroDataMatricula(), getFiltroDiaPagamento());
                canceladas.calcularPorcentagem(previsao.getValor());

                valorPrevisao += previsao.getValor();
                valorPagas += pagas.getValor();
                valorInadimplencia += inadimplencia.getValor();
                valorCanceladas += canceladas.getValor();

                getDadosGraficoInadimplencia().add(new InadimplenciaGraficoTO(Uteis.getDataMesAnoConcatenado(mesPesquisar), BigDecimal.valueOf(inadimplencia.getValor()), BigDecimal.valueOf(pagas.getValor()), BigDecimal.valueOf(previsao.getValor()), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));

                Totalizador totalizador = new Totalizador();
                totalizador.setPrevisao(previsao);
                totalizador.setPagas(pagas);
                totalizador.setInadimplencia(inadimplencia);
                getTotalizador().add(totalizador);

                //INFORMAÇÕES QUE SERÃO APRESENTADAS -- SOMENTE DO MÊS QUE ESTÁ SENDO FILTRADO
                if (Uteis.getDataMesAnoConcatenado(mesPesquisar).equals(Uteis.getDataMesAnoConcatenado(getDataBaseFiltroBI()))) {
                    inadimplenciaBi = inadimplencia;
                    previsaoBi = previsao;
                    pagasBi = pagas;
                    canceladasBi = canceladas;
                }
            }

            //média totalizador tabela
            try {
                inadimplenciaTotalBi.setLabel("InadimplênciaTotal");
                inadimplenciaTotalBi.setValor(valorInadimplencia);
                inadimplenciaTotalBi.setQuantidadeAlunos(totalAlunosIndadimplencia.intValue());
                inadimplenciaTotalBi.setMesReferencia(Uteis.somarMeses(getDataBaseFiltroBI(), -3));
                inadimplenciaTotalBi.calcularPorcentagem(valorPrevisao);

                previsaoTotalBi.setLabel("PrevisãoTotal");
                previsaoTotalBi.setValor(valorPrevisao);
                previsaoTotalBi.setQuantidadeAlunos(totalAlunosPrevisao.intValue());
                previsaoTotalBi.setMesReferencia(Uteis.somarMeses(getDataBaseFiltroBI(), -3));

                pagasTotalBi.setLabel("RecebidosTotal");
                pagasTotalBi.setValor(valorPagas);
                pagasTotalBi.setQuantidadeAlunos(totalAlunosPagas.intValue());
                pagasTotalBi.setMesReferencia(Uteis.somarMeses(getDataBaseFiltroBI(), -3));

                valorPrevisao = (valorPrevisao / mesesConsultar.size());
                valorPagas = (valorPagas / mesesConsultar.size());
                valorInadimplencia = (valorInadimplencia / mesesConsultar.size());

                InadimplenciaTO previsaoMedia = new InadimplenciaTO();
                previsaoMedia.setLabel("Média");
                previsaoMedia.setValor(valorPrevisao);
                previsaoMedia.setQuantidadeAlunos(new BigDecimal(totalAlunosPrevisao).divide(new BigDecimal(mesesConsultar.size()).setScale(1, RoundingMode.HALF_UP)).intValue());

                InadimplenciaTO pagasMedia = new InadimplenciaTO();
                pagasMedia.setLabel("Média");
                pagasMedia.setValor(valorPagas);
                pagasMedia.setQuantidadeAlunos(new BigDecimal(totalAlunosPagas).divide(new BigDecimal(mesesConsultar.size()).setScale(1, RoundingMode.HALF_UP)).intValue());

                InadimplenciaTO inadimplenciaMedia = new InadimplenciaTO();
                inadimplenciaMedia.setLabel("Média");
                inadimplenciaMedia.setValor(valorInadimplencia);
                inadimplenciaMedia.setQuantidadeAlunos(new BigDecimal(totalAlunosIndadimplencia).divide(new BigDecimal(mesesConsultar.size()).setScale(1, RoundingMode.HALF_UP)).intValue());
                inadimplenciaMedia.calcularPorcentagem(valorPrevisao);

                Totalizador media = new Totalizador();
                media.setPrevisao(previsaoMedia);
                media.setPagas(pagasMedia);
                media.setInadimplencia(inadimplenciaMedia);
                media.setMedia(true);

                getTotalizador().add(media);

                //getTotalizadorTabela().add(new InadimplenciaGraficoTO("Média", BigDecimal.valueOf(valorInadimplencia), BigDecimal.valueOf(valorPagas), BigDecimal.valueOf(valorPrevisao), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));
            } catch (Exception ignored) {
            }
        }

    }

    public String getLegendaGrafico() throws Exception {
        String legenda = "[\n" +
                "        {\n" +
                "            \"balloonText\": \"[[title]]:<br/><b>" + getEmpresaLogado().getMoeda()+ " [[value]]</b>\",\n" +
                "                \"lineAlpha\": 1,\n" +
                "                \"lineThickness\": 2,\n" +
                "                \"bullet\": \"round\",\n" +
                "                \"dashLengthField\": \"dashLength\",\n" +
                "                \"type\": \"line\",\n" +
                "                \"lineColor\": \"#3E35BB\",\n" +
                "                \"valueField\": \"valorTotal\",\n" +
                "                \"title\": \"Previsão\",\n" +
                "                \"valueAxis\": \"valorTotalAxis\"\n" +
                "        }, {\n" +
                "            \"balloonText\": \"[[title]]: <b>" + getEmpresaLogado().getMoeda()+ " [[value]]</b>\",\n" +
                "                    \"lineAlpha\": 1,\n" +
                "                    \"lineThickness\": 1,\n" +
                "                    \"bullet\": \"round\",\n" +
                "                    \"dashLengthField\": \"dashLength\",\n" +
                "                    \"type\": \"line\",\n" +
                "                    \"lineColor\": \"#008000\",\n" +
                "                    \"valueField\": \"valorPago\",\n" +
                "                    \"title\": \"Recebido\",\n" +
                "                    \"valueAxis\": \"valorTotalAxis\"\n" +
                "        }, " +
                "{\n" +
                "            \"balloonText\": \"[[title]]: <b>" + getEmpresaLogado().getMoeda()+ " [[value]]</b>\",\n" +
                "                    \"lineAlpha\": 1,\n" +
                "                    \"lineThickness\": 2,\n" +
                "                    \"dashLengthField\": \"dashLength\",\n" +
                "                    \"bullet\": \"round\",\n" +
                "                    \"type\": \"line\",\n" +
                "                    \"lineColor\": \"#ED0D27\",\n" +
                "                    \"valueField\": \"valorEmAberto\",\n" +
                "                    \"title\": \"Inadimplência\",\n" +
                "                    \"valueAxis\": \"valorTotalAxis\"\n" +
                "        }, \n";
//                if (incluirCanceladas) {
//                    legenda +=
//                    "{\n" +
//                            "            \"balloonText\": \"[[title]]: <b>R$ [[value]]</b>\",\n" +
//                            "                    \"lineAlpha\": 1,\n" +
//                            "                    \"lineThickness\": 1,\n" +
//                            "                    \"bullet\": \"round\",\n" +
//                            "                    \"dashLengthField\": \"dashLength\",\n" +
//                            "                    \"type\": \"line\",\n" +
//                            "                    \"lineColor\": \"#925920\",\n" +
//                            "                    \"valueField\": \"valorCanceladas\",\n" +
//                            "                    \"title\": \"Canceladas\",\n" +
//                            "                    \"valueAxis\": \"valorTotalAxis\"\n" +
//                            "        }, ";
//                }
        legenda +=
                "    {\"alphaField\": \"alpha\",\n" +
                        "                    \"clustered\": false,\n" +
                        "                    \"balloonText\": \"[[eficiencia]]\",\n" +
                        "                    \"dashLengthField\": \"dashLength\",\n" +
                        "                    \"fillAlphas\": 0.9,\n" +
                        "                    \"lineAlpha\": 0.2,\n" +
                        "                    \"columnWidth\": 0.9,\n" +
                        "                    \"title\": \"Eficiência(%)\",\n" +
                        "                    \"type\": \"column\",\n" +
                        "                    \"fillColors\": \"#E5E5E5\",\n" +
                        "                    \"lineColor\": \"#E5E5E5\",\n" +
                        "                    \"valueField\": \"eficienciaDouble\",\n" +
                        "                    \"valueAxis\": \"inadimplenciaAxis\"\n" +
                        "        }, {\n" +
                        "            \"alphaField\": \"alpha\",\n" +
                        "                    \"clustered\": false,\n" +
                        "                    \"balloonText\": \"[[value]]% de inadimplência\",\n" +
                        "                    \"dashLengthField\": \"dashLength\",\n" +
                        "                    \"fillAlphas\": 0.9,\n" +
                        "                    \"lineAlpha\": 0.2,\n" +
                        "                    \"columnWidth\": 0.9,\n" +
                        "                    \"title\": \"Inadimplência(%)\",\n" +
                        "                    \"type\": \"column\",\n" +
                        "                    \"fillColors\": \"#B4B4B4\",\n" +
                        "                    \"lineColor\": \"#B4B4B4\",\n" +
                        "                    \"valueField\": \"inadimplencia\",\n" +
                        "                    \"valueAxis\": \"inadimplenciaAxis\"\n" +
                        "        }]";

        return legenda;
    }


    public Date getFiltroDataMatricula() {
        return filtroDataMatricula;
    }

    public void setFiltroDataMatricula(Date filtroDataMatricula) {
        this.filtroDataMatricula = filtroDataMatricula;
    }

    public Integer getFiltroDiaPagamento() {
        return filtroDiaPagamento;
    }

    public void setFiltroDiaPagamento(Integer filtroDiaPagamento) {
        this.filtroDiaPagamento = filtroDiaPagamento;
    }

    public String getDataBase_MatriculaApresentarMesDia() {
        if (getFiltroDataMatricula() != null) {
            return getDataComDescricaoMesDia(getFiltroDataMatricula());
        } else {
            return "";
        }
    }

    public void limparData() {
        setFiltroDataMatricula(null);
    }

    public String getMesAtualFormatado() {
        return Calendario.getData(getDataBaseFiltroBI(), "MMMM/YYYY");
    }

    public void abrirListaParcelas(ActionEvent evt) {
        setDescricaoTitulo("");
        setListaApresentarParcelas(new ArrayList<ResumoPessoaRelBIInadimplenciaVO>());
        InadimplenciaTO obj = (InadimplenciaTO) JSFUtilities.getFromActionEvent("totalizador", evt);
        InadimplenciaTO objAlunos = (InadimplenciaTO) JSFUtilities.getFromActionEvent("totalizadorAlunos", evt);
        String situacaoBuscar = "";
        try {
            if (obj == null && objAlunos == null) {
                throw new Exception("Totalizador não encontrado.");
            } else if (obj != null) {

                setDescricaoTitulo(obj.getLabel());
                Date dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(obj.getMesReferencia());
                Date dataFinalPesquisar = Uteis.obterUltimoDiaMes(obj.getMesReferencia());


                //SE FOR O MES QUE ESTÁ SELECIONADO PELO USUARIO A DATA FINAL DA PESQUISA DEVE SER A DATA QUE FOI SELECIONADA NO CALENDÁRIO
                if (Uteis.getDataMesAnoConcatenado(dataInicioPesquisar).equals(Uteis.getDataMesAnoConcatenado(getDataBaseFiltroBI()))) {
                    dataFinalPesquisar = getDataBaseFiltroBI();
                }
                if(obj.getLabel().equals("PrevisãoTotal") || obj.getLabel().equals("InadimplênciaTotal") || obj.getLabel().equals("RecebidosTotal")){
                    dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(Uteis.somarMeses(getDataBaseFiltroBI(), -3));
                    dataFinalPesquisar = getDataBaseFiltroBI();
                }

                ParcelaEmAbertoControleRel relParcelaControle = getControlador(ParcelaEmAbertoControleRel.class);
                if (relParcelaControle == null) {
                    relParcelaControle = new ParcelaEmAbertoControleRel();
                } else {
                    relParcelaControle.novo();
                }
                if (obj.getLabel().contains("Previsão") || obj.getLabel().contains("Inadimplência")) {
                    relParcelaControle.getParcelaEmAbertoRel().setSituacao("EA");
                    relParcelaControle.adicionarSituacaoNaLista();
                }
                if (obj.getLabel().contains("Previsão") || obj.getLabel().contains("Recebidos")){
                    relParcelaControle.getParcelaEmAbertoRel().setSituacao("PG");
                    relParcelaControle.adicionarSituacaoNaLista();
                }
                if((!desconsiderarCanceladasBi && (obj.getLabel().contains("Previsão") || obj.getLabel().contains("Inadimplência")))
                || obj.getLabel().contains("Parcelas Canceladas")){
                    relParcelaControle.getParcelaEmAbertoRel().setSituacao("CA");
                    relParcelaControle.adicionarSituacaoNaLista();

                    if (obj.getLabel().contains("Previsão") || obj.getLabel().contains("Inadimplência")) {
                        relParcelaControle.getParcelaEmAbertoRel().setParcelaCancelada("CANCELADA_APOS_VENCIMENTO");
                    }
                }

                relParcelaControle.getParcelaEmAbertoRel().setDataInicioVencimento(dataInicioPesquisar);
                relParcelaControle.getParcelaEmAbertoRel().setDataTerminoVencimento(dataFinalPesquisar);
                relParcelaControle.consultarParcelas();
                //setListaApresentarParcelas(getFacade().getMovParcela().listaBIInadimplencia(getEmpresaFiltroBI().getCodigo(), dataInicioPesquisar, dataFinalPesquisar, situacaoBuscar, getFiltroDataMatricula(), getFiltroDiaPagamento(), false));
            } else if (objAlunos != null) {

                setDescricaoTitulo(objAlunos.getLabel());
                Date dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(objAlunos.getMesReferencia());
                Date dataFinalPesquisar = Uteis.obterUltimoDiaMes(objAlunos.getMesReferencia());

                //SE FOR O MES QUE ESTÁ SELECIONADO PELO USUARIO A DATA FINAL DA PESQUISA DEVE SER A DATA QUE FOI SELECIONADA NO CALENDÁRIO
                if (Uteis.getDataMesAnoConcatenado(dataInicioPesquisar).equals(Uteis.getDataMesAnoConcatenado(getDataBaseFiltroBI()))) {
                    dataFinalPesquisar = getDataBaseFiltroBI();
                }

                situacaoBuscar = "";

                if (objAlunos.getLabel().equals("PrevisãoTotal")) {
                    situacaoBuscar = desconsiderarCanceladasBi ? " not in ('CA')" : "";
                    dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(Uteis.somarMeses(getDataBaseFiltroBI(), -3));
                    dataFinalPesquisar = getDataBaseFiltroBI();
                    setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_PREVISAO.getId());
                } else if (objAlunos.getLabel().equals("Previsão")) {
                    situacaoBuscar = desconsiderarCanceladasBi ? " not in ('CA')" : "";
                    setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_PREVISAO.getId());
                } else if (objAlunos.getLabel().equals("RecebidosTotal")) {
                    situacaoBuscar = " in ('PG')";
                    dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(Uteis.somarMeses(getDataBaseFiltroBI(), -3));
                    dataFinalPesquisar = getDataBaseFiltroBI();
                    setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_RECEBIDO.getId());
                } else if (objAlunos.getLabel().equals("Recebidos")) {
                    situacaoBuscar = " in ('PG')";
                    setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_RECEBIDO.getId());
                } else if (objAlunos.getLabel().equals("InadimplênciaTotal")) {
                    if (desconsiderarCanceladasBi) {
                        situacaoBuscar = " in ('EA')";
                    } else {
                        situacaoBuscar = " in ('EA', 'CA')";
                    }
                    dataInicioPesquisar = Uteis.obterPrimeiroDiaMes(Uteis.somarMeses(getDataBaseFiltroBI(), -3));
                    dataFinalPesquisar = getDataBaseFiltroBI();
                    setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_INADIMPLENCIA.getId());
                } else if (objAlunos.getLabel().equals("Inadimplência")) {
                    if (desconsiderarCanceladasBi) {
                        situacaoBuscar = " in ('EA')";
                    } else {
                        situacaoBuscar = " in ('EA', 'CA')";
                    }
                    setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_INADIMPLENCIA.getId());
                } else if (objAlunos.getLabel().equals("Parcelas Canceladas")) {
                    situacaoBuscar = " in ('CA')";
                } else {
                    throw new Exception("Totalizador não encontrado.");
                }

                setListaApresentarParcelas(getFacade().getMovParcela().listaBIInadimplencia(getEmpresaFiltroBI().getCodigo(), dataInicioPesquisar, dataFinalPesquisar, situacaoBuscar, getFiltroDataMatricula(), getFiltroDiaPagamento(), true));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<ResumoPessoaRelBIInadimplenciaVO> getListaApresentarParcelas() {
        if (listaApresentarParcelas == null) {
            listaApresentarParcelas = new ArrayList<>();
        }
        return listaApresentarParcelas;
    }

    public void setListaApresentarParcelas(List<ResumoPessoaRelBIInadimplenciaVO> listaApresentarParcelas) {
        this.listaApresentarParcelas = listaApresentarParcelas;
    }

    public int getQtdItensLista() {
        return getListaApresentarParcelas().size();
    }

    public void irParaTelaCliente() {
        ResumoPessoaRelBIInadimplenciaVO obj = (ResumoPessoaRelBIInadimplenciaVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Cliente não encontrado.");
            } else if (UteisValidacao.emptyNumber(obj.getCliente())) {
                throw new Exception("Cliente não encontrado.");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCliente());
                irParaTelaCliente(clienteVO);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isApresentarSituacaoDia() {
        return !UteisValidacao.emptyNumber(getFiltroDiaPagamento());
    }

    public boolean isApresentarDataMatricula() {
        return getFiltroDataMatricula() != null;
    }

    public String getAtributosExportar() {
        StringBuilder atributos = new StringBuilder();
        atributos.append("matricula=Matrícula,nome=Nome");

        if (isApresentarDataMatricula()) {
            atributos.append(",dataMatricula_Apresentar=Data Matrícula");
        }

        atributos.append(",contrato=Contrato,telefone=Telefone,email=Email,qtdParcelas=Qtd. de parcelas,situacaoParcela_Apresentar=Situação Parcela Atual");

        if (isApresentarSituacaoDia()) {
            atributos.append(",situacaoParcelaDiaPagamento_Apresentar=Situação Parcela Dia ").append(getFiltroDiaPagamento());
        }
        atributos.append(",descricaoParcela=Parcela,dataVencimento_Apresentar=Dt. Vencimento Parcela,valorParcela=Valor Parcela,empresa=Empresa");
        return atributos.toString();
    }

    public String getDescricaoTitulo() {
        if (descricaoTitulo == null) {
            descricaoTitulo = "";
        }
        return descricaoTitulo;
    }

    public void setDescricaoTitulo(String descricaoTitulo) {
        this.descricaoTitulo = descricaoTitulo;
    }

    public List<InadimplenciaGraficoTO> getTotalizadorTabela() {
        if (totalizadorTabela == null) {
            totalizadorTabela = new ArrayList<InadimplenciaGraficoTO>();
        }
        return totalizadorTabela;
    }

    public void setTotalizadorTabela(List<InadimplenciaGraficoTO> totalizadorTabela) {
        this.totalizadorTabela = totalizadorTabela;
    }

    public boolean isFiltroDataMatriculaSelecionada() {
        return getFiltroDataMatricula() != null;
    }

    public InadimplenciaTO getInadimplenciaBi() {
        return inadimplenciaBi;
    }

    public void setInadimplenciaBi(InadimplenciaTO inadimplenciaBi) {
        this.inadimplenciaBi = inadimplenciaBi;
    }

    public InadimplenciaTO getPrevisaoBi() {
        return previsaoBi;
    }

    public void setPrevisaoBi(InadimplenciaTO previsaoBi) {
        this.previsaoBi = previsaoBi;
    }

    public InadimplenciaTO getPagasBi() {
        return pagasBi;
    }

    public void setPagasBi(InadimplenciaTO pagasBi) {
        this.pagasBi = pagasBi;
    }

    public InadimplenciaTO getCanceladasBi() {
        return canceladasBi;
    }

    public void setCanceladasBi(InadimplenciaTO canceladasBi) {
        this.canceladasBi = canceladasBi;
    }

    public InadimplenciaTO getInadimplenciaTotalBi() {
        return inadimplenciaTotalBi;
    }

    public void setInadimplenciaTotalBi(InadimplenciaTO inadimplenciaTotalBi) {
        this.inadimplenciaTotalBi = inadimplenciaTotalBi;
    }

    public void abrirModalExcelPlanilhaAgrupada() {
        try {
            limparMsg();
            setOnComplete("");

            setTipoParcelaDetalhado("");
            setDataInicioExportar(Uteis.obterPrimeiroDiaMes(getDataBaseFiltroBI()));
            setDataFinalExportar(getDataBaseFiltroBI());

            alterouDataInicioExportar();

            setOnComplete("Richfaces.showModalPanel('modalExportarBiInadimplencia');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void exportarExcelPlanilhaAgrupada() {
        try {
            limparMsg();
            setOnComplete("");

            if (getDataInicioExportar() == null) {
                throw new Exception("Informa a data de início para exportar.");
            }
            if (getDataFinalExportar() == null) {
                throw new Exception("Informa a data final para exportar.");
            }
            if (Calendario.maior(getDataInicioExportar(), getDataFinalExportar())) {
                throw new Exception("A data de início não pode ser superior a data final.");
            }

            String situacaoBuscar = "";
            if (getTipoParcelaDetalhado().equals("")) {
                situacaoBuscar = "";
                setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_PREVISAO.getId());
            } else if (getTipoParcelaDetalhado().equals("REC")) {
                situacaoBuscar = " in ('PG')";
                setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_RECEBIDO.getId());
            } else if (getTipoParcelaDetalhado().equals("INA")) {
                situacaoBuscar = " in ('EA','CA')";
                setItemExportacao(ItemExportacaoEnum.BI_INADIMPLENCIA_INADIMPLENCIA.getId());
            } else {
                throw new Exception("Totalizador não encontrado.");
            }

            String parcelas = getFacade().getMovParcela().listaCodigosParcelaBIInadimplenciaDetalhado(getEmpresaFiltroBI().getCodigo(),
                    getDataInicioExportar(), getDataFinalExportar(), situacaoBuscar, getFiltroDataMatricula(), getFiltroDiaPagamento());
            //se não tem parcelas colocar como 0
            if (UteisValidacao.emptyString(parcelas)) {
                parcelas = "0";
            }
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.obterPorId(getItemExportacao()), (parcelas.length() - parcelas.replaceAll(",","").length() + 1), "", ".xls","Parcelas:" +parcelas, "");
            String arquivo = "BiInadimplencia-" + getKey() + "-" + Calendario.hoje().getTime() + ".xls";
            File file = new File(this.getServletContext().getRealPath("relatorio") + File.separator + arquivo);

            ProcessoPlanilhaTentativaCobranca processo = new ProcessoPlanilhaTentativaCobranca(getFacade().getContrato().getCon());
            processo.processarPlanilhaBIInadimplencia(getEmpresaFiltroBI().getCodigo(), getDataInicioExportar(), getDataFinalExportar(), file, parcelas);

            setOnComplete("abrirPopup('UpdateServlet?op=downloadfile&file=" + arquivo + "&mimetype=application/vnd.ms-excel','Transacoes', 800,200);Richfaces.hideModalPanel('modalExportarBiInadimplencia');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void alterouDataInicioExportar() {
        try {
            limparMsg();
            setOnComplete("");

            if (getDataInicioExportar() == null) {
                return;
            }

            Date dataLimite = Uteis.getDate("01/05/2020", "dd/MM/yyyy");

            if (Calendario.menor(getDataInicioExportar(), dataLimite)) {
                setApresentarMensagemReprocessar(true);
            } else {
                setApresentarMensagemReprocessar(false);
            }


        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public Date getDataInicioExportar() {
        return dataInicioExportar;
    }

    public void setDataInicioExportar(Date dataInicioExportar) {
        this.dataInicioExportar = dataInicioExportar;
    }

    public Date getDataFinalExportar() {
        return dataFinalExportar;
    }

    public void setDataFinalExportar(Date dataFinalExportar) {
        this.dataFinalExportar = dataFinalExportar;
    }

    public boolean isApresentarMensagemReprocessar() {
        return apresentarMensagemReprocessar;
    }

    public void setApresentarMensagemReprocessar(boolean apresentarMensagemReprocessar) {
        this.apresentarMensagemReprocessar = apresentarMensagemReprocessar;
    }

    public List<SelectItem> getSelectItemTipoParcelaDetalhado() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem("", "Previsão"));
        lista.add(new SelectItem("REC", "Recebido"));
        lista.add(new SelectItem("INA", "Inadimplência"));
        return lista;
    }

    public String getTipoParcelaDetalhado() {
        if (tipoParcelaDetalhado == null) {
            tipoParcelaDetalhado = "";
        }
        return tipoParcelaDetalhado;
    }

    public void setTipoParcelaDetalhado(String tipoParcelaDetalhado) {
        this.tipoParcelaDetalhado = tipoParcelaDetalhado;
    }

    public Boolean getDesconsiderarCanceladasBi() {
        return desconsiderarCanceladasBi;
    }

    public void setDesconsiderarCanceladasBi(Boolean desconsiderarCanceladasBi) {
        this.desconsiderarCanceladasBi = desconsiderarCanceladasBi;
    }

    public InadimplenciaTO getPrevisaoTotalBi() {
        return previsaoTotalBi;
    }

    public void setPrevisaoTotalBi(InadimplenciaTO previsaoTotalBi) {
        this.previsaoTotalBi = previsaoTotalBi;
    }

    public InadimplenciaTO getPagasTotalBi() {
        return pagasTotalBi;
    }

    public void setPagasTotalBi(InadimplenciaTO pagasTotalBi) {
        this.pagasTotalBi = pagasTotalBi;
    }

    private FiltroDTO getFiltroDTO(boolean atualizarAgora) {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.INADIMPLENCIA.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", atualizarAgora);
        filtros.put("dataBase", Calendario.getDataComHoraZerada(getDataBaseFiltroBI()).getTime());
        if (getFiltroDataMatricula() != null) {
            filtros.put("dataMatricula", Calendario.getDataComHoraZerada(getFiltroDataMatricula()).getTime());
        }
        filtros.put("diaPagamento", getFiltroDiaPagamento());
        filtros.put("desconsiderarCanceladas", this.getDesconsiderarCanceladasBi());

        List<Integer> empresas = new ArrayList<>();
        if (!UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo())) {
            empresas.add(getEmpresaFiltroBI().getCodigo());
        }
        filtros.put("empresas", empresas);

        List<Integer> colaboradores = new ArrayList<>();
        filtros.put("colaboradores", colaboradores);

        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public String getItemExportacao() {
        return itemExportacao;
    }

    public void setItemExportacao(String itemExportacao) {
        this.itemExportacao = itemExportacao;
    }
}
