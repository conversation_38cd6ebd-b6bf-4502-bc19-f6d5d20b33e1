package relatorio.controle.basico;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.AcessoGymPassTO;
import negocio.comuns.financeiro.BIAcessoGymPassTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ResumoPessoaBIAcessoGymPassVO;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.mail.FetchProfile;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AcessoGymPassControle extends BIControle {

    private Map<Integer, Integer> acessosPorCliente;
    private EmpresaVO empresaVO;
    private List<AcessoGymPassTO> totalizador = new ArrayList<>();
    private String labels;
    private Integer qtdClientesGymPass = 0;
    private Integer qtdAcessosTotais = 0;
    private List<BIAcessoGymPassTO> dadosAcessos;
    private Integer maiorQtdDeAcessoEmUmDia = 0;
    private Date dataInicio;
    private ItemExportacaoEnum itemSelecionado;

    private String descricaoTitulo;

    private List<ResumoPessoaBIAcessoGymPassVO> listaAlunosApresentar;
    private boolean acessoCheckinGympass;

    public AcessoGymPassControle(Map<Integer, Integer> acessosPorCliente, EmpresaVO empresaVO) {
        this.acessosPorCliente = acessosPorCliente;
        this.empresaVO = empresaVO;
    }

    public AcessoGymPassControle() {
    }

    public Date getDataBaseFiltroBI(){
        return (Date) JSFUtilities.getManagedBean("BIControle.dataBaseFiltro");
    }

    public void zeraVariaveisParaNovaConsulta () {
        totalizador.clear();
        qtdClientesGymPass = 0;
        qtdAcessosTotais = 0;
        maiorQtdDeAcessoEmUmDia = 35;
    }

    private FiltroDTO getFiltroDTO() throws Exception {
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.GYM_PASS.name());
        JSONObject filtros = new JSONObject();

        filtros.put("dataInicioMes", Uteis.obterPrimeiroDiaMesPrimeiraHora(dataInicio).getTime());
        filtros.put("dataFimMes", Uteis.obterUltimoDiaMesUltimaHora(dataInicio).getTime());
        filtros.put("empresa", getEmpresaFiltroBI().getCodigo());
        filtros.put("acessoCheckinGympass", isAcessoCheckinGympass());
        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    private Map<Integer, Integer> obtemDadosBIMS (JSONObject dados) {
        Map<Integer, Integer> dadosObtidos = new HashMap<>();
        String[] stringDados = dados.get("acessosPorDia").toString().replaceAll("\\{", "").replaceAll("\\}", "").split(",");

        if(!stringDados[0].isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yy");

            for (String string : stringDados) {
                Integer diaDoMes = Uteis.obterDiaData(new Date(Long.valueOf(string.replaceAll("\":.+", "").replaceAll("\"", ""))));
                Integer qtdAcessos = Integer.parseInt(string.replaceAll(".+:", ""));
                dadosObtidos.put(diaDoMes, qtdAcessos);
                if(qtdAcessos > maiorQtdDeAcessoEmUmDia) {
                    maiorQtdDeAcessoEmUmDia = qtdAcessos;
                }
            }
        }

        return dadosObtidos;
    }

    public void prepararDadosGymPass() throws Exception {
        Integer codigoEmpresaFiltrado = getEmpresaFiltroBI().getCodigo();
        if (totalizador.size() > 0) {
            zeraVariaveisParaNovaConsulta();
        }
        if (dataInicio == null) {
            setDataInicio(getDataBaseFiltroBI());
        }

        int diasDoMes = Uteis.obterNumeroDiasDoMes(dataInicio);
        dataInicio = Uteis.obterPrimeiroDiaMesPrimeiraHora(getDataInicio());
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.GYM_PASS, getFiltroDTO());
        JSONObject dados = new JSONObject(filtroDTO.getJsonDados());

        setDadosAcessos(new ArrayList<>());

        Map<Integer, Integer> dadosMap = obtemDadosBIMS(dados);

        for (Integer i = 1 ; i <= diasDoMes ; i++) {
            if (dadosMap.keySet().contains(i)) {
                getDadosAcessos().add(new BIAcessoGymPassTO(i, dadosMap.get(i)));
            } else {
                getDadosAcessos().add(new BIAcessoGymPassTO(i, 0));
            }
        }
        if(isAcessoCheckinGympass()){
            acessosPorCliente = getFacade().getAcessoCliente().consultarQtdClientesAcessoGymPassPorCheckin(dataInicio, Uteis.obterUltimoDiaMesUltimaHora(getDataInicio()), codigoEmpresaFiltrado);
        }else{
            acessosPorCliente = getFacade().getAcessoCliente().consultarQtdClientesAcessoGymPass(dataInicio, Uteis.obterUltimoDiaMesUltimaHora(getDataInicio()), codigoEmpresaFiltrado);
        }
        qtdClientesGymPass = getFacade().getCliente().consultaQtdClienteGymPass(codigoEmpresaFiltrado);

        ConfiguracaoSistemaVO configuracaoSistema = getFacade().getConfiguracaoSistema().consultarConfigsParaAtualizarDadosBI(1);

        Integer primeiroIntervalo = 0;
        Integer segundoIntervalo = 0;
        Integer terceiroIntervalo = 0;
        Integer quartoIntervalo = 0;
        if(acessosPorCliente != null) {
            for (Integer acesso : acessosPorCliente.values()) {
                qtdAcessosTotais += acesso;
                if (acesso >= configuracaoSistema.getQtdAcessoGymPassInicioFaixa1() && acesso <= configuracaoSistema.getQtdAcessoGymPassFinalFaixa1() ) {
                    primeiroIntervalo++;
                } else if (acesso >= configuracaoSistema.getQtdAcessoGymPassInicioFaixa2() && acesso <= configuracaoSistema.getQtdAcessoGymPassFinalFaixa2()) {
                    segundoIntervalo++;
                } else if (acesso >= configuracaoSistema.getQtdAcessoGymPassInicioFaixa3() && acesso <= configuracaoSistema.getQtdAcessoGymPassFinalFaixa3()) {
                    terceiroIntervalo++;
                } else if (acesso >= configuracaoSistema.getQtdAcessoGymPassInicioFaixa4() && acesso <= configuracaoSistema.getQtdAcessoGymPassFinalFaixa4()) {
                    quartoIntervalo++;
                }
            }
        }

        totalizador.add(new AcessoGymPassTO("Entre " + configuracaoSistema.getQtdAcessoGymPassInicioFaixa1() + " e " + configuracaoSistema.getQtdAcessoGymPassFinalFaixa1() + " acessos", primeiroIntervalo));
        totalizador.add(new AcessoGymPassTO("Entre " + configuracaoSistema.getQtdAcessoGymPassInicioFaixa2() + " e " + configuracaoSistema.getQtdAcessoGymPassFinalFaixa2() + " acessos", segundoIntervalo));
        totalizador.add(new AcessoGymPassTO("Entre " + configuracaoSistema.getQtdAcessoGymPassInicioFaixa3() + " e " + configuracaoSistema.getQtdAcessoGymPassFinalFaixa3() + " acessos", terceiroIntervalo));
        totalizador.add(new AcessoGymPassTO("Entre " + configuracaoSistema.getQtdAcessoGymPassInicioFaixa4() + " e " + configuracaoSistema.getQtdAcessoGymPassFinalFaixa4() + " acessos", quartoIntervalo));
    }

    public boolean isDataAlterada(){
        return !Calendario.igual(getDataBaseFiltroBI(), (Date) JSFUtilities.getManagedBean("BIControle.dataBaseFiltro"));
    }

    public Map<Integer, Integer> getAcessos() {
        return acessosPorCliente;
    }

    public void setAcessos(Map<Integer, Integer> acessosPorCliente) {
        this.acessosPorCliente = acessosPorCliente;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Map<Integer, Integer> getAcessosPorCliente() {
        return acessosPorCliente;
    }

    public void setAcessosPorCliente(Map<Integer, Integer> acessosPorCliente) {
        this.acessosPorCliente = acessosPorCliente;
    }

    public List<AcessoGymPassTO> getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(List<AcessoGymPassTO> totalizador) {
        this.totalizador = totalizador;
    }

    public String getLabels() {
        return labels;
    }

    public void setLabels(String labels) {
        this.labels = labels;
    }

    public Integer getQtdClientesGymPass() {
        return qtdClientesGymPass;
    }

    public void setQtdClientesGymPass(Integer qtdClientesGymPass) {
        this.qtdClientesGymPass = qtdClientesGymPass;
    }

    public Integer getQtdAcessosTotais() {
        return qtdAcessosTotais;
    }

    public void setQtdAcessosTotais(Integer qtdAcessosTotais) {
        this.qtdAcessosTotais = qtdAcessosTotais;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataBase_Apresentar(){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDataInicio());
        return dfmt.format(cal.getTime());
    }

    public String getDataBase_ApresentarMesAno(){
        if (getDataInicio() != null) {
            return getDataComDescricaoMesAno(getDataInicio());
        }
        if(getDataBaseFiltroBI() != null ) {
            return getDataComDescricaoMesAno(getDataBaseFiltroBI());
        } else {
            return "";
        }
    }

    public void acaoMudarMes() throws Exception{
        FacesContext contexto = FacesContext.getCurrentInstance();
        String mes = contexto.getExternalContext().getRequestParameterMap().get("form:dataInicioAcessoGymPassInputCurrentDate");
        String primeiroDia = "01/" + mes;
        Date dataAnalise = Formatador.obterData(primeiroDia, "dd/MM/yyyy");
        setDataInicio(dataAnalise);
        prepararDadosGymPass();
    }

    public String getDadosAcessosJSON(){
        return this.dadosAcessos != null ? new JSONArray(this.dadosAcessos).toString() : new JSONArray().toString();
    }

    public List<BIAcessoGymPassTO> getDadosAcessos() {
        return dadosAcessos;
    }

    public void setDadosAcessos(List<BIAcessoGymPassTO> dadosAcessos) {
        this.dadosAcessos = dadosAcessos;
    }

    public Integer getMaiorQtdDeAcessoEmUmDia() {
        if(maiorQtdDeAcessoEmUmDia > 35) {
            return maiorQtdDeAcessoEmUmDia;
        } else {
            return 35;
        }
    }

    public void setMaiorQtdDeAcessoEmUmDia(Integer maiorQtdDeAcessoEmUmDia) {
        this.maiorQtdDeAcessoEmUmDia = maiorQtdDeAcessoEmUmDia;
    }

    public String getDescricaoTitulo() {
        if (descricaoTitulo == null){
            descricaoTitulo = "";
        }
        return descricaoTitulo;
    }

    public void setDescricaoTitulo(String descricaoTitulo) {
        this.descricaoTitulo = descricaoTitulo;
    }

    public void selecionarAlunosAcessoGympass(ActionEvent evt) {
        setDescricaoTitulo("Acesso Wellhub");
        setItemSelecionado(ItemExportacaoEnum.BI_GYMPASS_ACESSOS);
        Map<Integer, Integer> obj = (Map<Integer, Integer>) JSFUtilities.getFromActionEvent("acessoGymPassAlunos", evt);
        try {
            if (obj == null) {
                throw new Exception("Não houve acesso.");
            } else {
                if (dataInicio == null) {
                    setDataInicio(getDataBaseFiltroBI());
                }
                Integer codigoEmpresaFiltrado = getEmpresaFiltroBI().getCodigo();
                if(isAcessoCheckinGympass()){
                    setListaAlunosApresentar(getFacade().getAcessoCliente().consultarClientesAcessoGymPassPorCheckin(dataInicio, Uteis.obterUltimoDiaMesUltimaHora(getDataInicio()), codigoEmpresaFiltrado));
                }else{
                    setListaAlunosApresentar(getFacade().getAcessoCliente().consultarClientesAcessoGymPass(dataInicio, Uteis.obterUltimoDiaMesUltimaHora(getDataInicio()), codigoEmpresaFiltrado));
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarAlunosGympass(ActionEvent evt) {
        setDescricaoTitulo("Alunos Wellhub");
        setItemSelecionado(ItemExportacaoEnum.BI_GYMPASS_ALUNOS);
        Integer acessoGymPassAlunos = (Integer) JSFUtilities.getFromActionEvent("acessoGymPassAlunos", evt);
        try {
            if (acessoGymPassAlunos == null || acessoGymPassAlunos == 0) {
                throw new Exception("Não há clientes com GymPass.");
            } else {
                if (dataInicio == null) {
                    setDataInicio(getDataBaseFiltroBI());
                }
                Integer codigoEmpresaFiltrado = getEmpresaFiltroBI().getCodigo();
                setListaAlunosApresentar(getFacade().getCliente().consultarClienteGymPass(codigoEmpresaFiltrado));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<ResumoPessoaBIAcessoGymPassVO> getListaAlunosApresentar() {
        if (listaAlunosApresentar == null){
            listaAlunosApresentar = new ArrayList<>();
        }
        return listaAlunosApresentar;
    }

    public void setListaAlunosApresentar(List<ResumoPessoaBIAcessoGymPassVO> listaAlunosApresentar) {
        this.listaAlunosApresentar = listaAlunosApresentar;
    }

    public int getQtdItensLista() {
        return getListaAlunosApresentar().size();
    }

    public String getAtributosExportar() {
        StringBuilder atributos = new StringBuilder();
        atributos.append("matricula=Matrícula,nome=Nome,situacaoCliente=Situação do Cliente,tokenGymPass=Token Wellhub");
        return atributos.toString();
    }

    public void irParaTelaCliente() {
        ResumoPessoaBIAcessoGymPassVO obj = (ResumoPessoaBIAcessoGymPassVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Cliente não encontrado.");
            } else if (UteisValidacao.emptyNumber(obj.getCliente())) {
                throw new Exception("Cliente não encontrado.");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCliente());
                irParaTelaCliente(clienteVO);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isAcessoCheckinGympass() {
        return acessoCheckinGympass;
    }

    public void setAcessoCheckinGympass(boolean acessoCheckinGympass) {
        this.acessoCheckinGympass = acessoCheckinGympass;
    }

    public ItemExportacaoEnum getItemSelecionado() {
        return itemSelecionado;
    }

    public void setItemSelecionado(ItemExportacaoEnum itemSelecionado) {
        this.itemSelecionado = itemSelecionado;
    }

    public String getItemSelecionadoID(){
        if(itemSelecionado == null) {
            return "";
        }
        return itemSelecionado.getId();
    }
}
