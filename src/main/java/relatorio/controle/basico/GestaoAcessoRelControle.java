/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.basico;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.acesso.AcessoClienteTO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.LiberacaoAcessoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.CaixaAbertoTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.AcessoRealizadoTO;
import negocio.comuns.plano.AcessoBloqueadoTO;
import negocio.comuns.plano.AlunoHorarioTurmaTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.collections.Predicate;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.jdbc.basico.IndicadorAcessoRel;
import relatorio.negocio.jdbc.basico.IndicadorAcessoTO;
import relatorio.negocio.jdbc.financeiro.TotalizadorFrequenciaRel;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class GestaoAcessoRelControle extends BIControle {

    private List<AcessoClienteVO> listaAcessoClienteNaAcademia = new ArrayList<>();
    private Integer quantidadeClienteNaAcademia = 0;
    private List<AcessoClienteTO> listaClienteComPendeciaFinanceira = new ArrayList<>();
    private Integer quantidadeClienteComPendencia = 0;
    private List<AcessoClienteVO> listaAcessoClienteNaAcademiaSituacaoInativo = new ArrayList<>();
    private Integer quantidadeClienteSituacaoInativo = 0;
    private Integer quantidadeClienteOutraUnidade = 0;
    private List<AcessoClienteVO> listaAcessoClienteNaAcademiaOutraUnidade = new ArrayList<>();
    private Integer quantidadeAcessoLiberado = 0;
    private List<LiberacaoAcessoVO> listaAcessoLiberado = new ArrayList<>();
    private Integer quantidadeAcessosRealizadosDia = 0;
    private List<AcessoRealizadoTO> listaAcessosRealizadosDia = new ArrayList<>();
    private Date primeiroDiaMes;
    private String listaAcessoGrafico;
    private List<IndicadorAcessoRel> listaAcessoDiaHora = new ArrayList<>();
    private List<IndicadorAcessoRel> listaAcessoDiaHoraPassado = new ArrayList<>();
    private List<IndicadorAcessoTO> listaAcessoProcessada = new ArrayList<>();
    private IndicadorAcessoRel indicadorAcessoMaiorAcesso = new IndicadorAcessoRel();
    private String diaMaiorAcesso = "";
    private IndicadorAcessoRel indicadorAcessoMaiorAcessoHora = new IndicadorAcessoRel();
    private String horaMaiorAcesso = "";
    private String tempoMedioAcademia = "";
    private Double percentualCrescimento = 0.0;
    private String onComplete;

    private Integer capacidadeSimultanea = 0;
    private Integer vagasDisponiveis = 0;
    private Integer vagasDisponiveis10min = 0;
    private Integer quantidadeClienteAulasAgendadas = 0;
    private Integer quantidadeAcessosBloqueados = 0;
    private Integer quantidadeAcessosBloqueadosMes = 0;
    private List<AlunoHorarioTurmaTO> listaAcessoQuantidadeClienteAulasAgendadas = new ArrayList<>();
    private List<AcessoBloqueadoTO> listaQuantidadeAcessosBloqueados = new ArrayList<>();
    private Date dataHoraConsulta;

    public void consultarAcessos() throws Exception {
        validarEmpresa();

        descobrirPrimeiroDiaMes();
        processarBIGestaoAcessos(false);
    }

    public void atualizarDataHoraDadosAcesso() {
        dataHoraConsulta = Calendario.hoje();
        atualizarDadosAcesso();
    }


    public void atualizarDadosAcesso() {
        try {

            if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
                throw new Exception("O campo empresa deve ser informado");
            }
            descobrirPrimeiroDiaMes();
            processarBIGestaoAcessos(true);
            gravarHistoricoAcessoBI(BIEnum.GESTAO_ACESSO);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    private void validarEmpresa() throws Exception {
        if (getEmpresaFiltroBI().getCodigo() == 0 && !isPermiteConsultarTodasEmpresas()) {
            throw new Exception("O campo empresa deve ser informado");
        }
    }

    private void processarBIGestaoAcessos(boolean atualizarAgora) throws Exception {
        try {
            validarEmpresa();

            // Se filtro empresa = 0, então é para consultar de todas as empresa.
            // Neste caso utiliza-se a empresa.codigo = 1 para obter configuração de "Tempo médio para para sair da academia."
            String tempoMedio;
            EmpresaVO empresa;
            if (getEmpresaFiltroBI().getCodigo() == 0) {
                List<EmpresaVO> listaEmpresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                empresa = listaEmpresas.get(0);
                tempoMedio = getFacade().getAcessoCliente().obterMediaTempoAcademiaPeriodo(primeiroDiaMes, Calendario.hoje(), empresa.getCodigo());
            } else {
                empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                tempoMedio = getFacade().getAcessoCliente().obterMediaTempoAcademiaPeriodo(primeiroDiaMes, Calendario.hoje(), getEmpresaFiltroBI().getCodigo());
            }

            if (tempoMedio == null || tempoMedio.equalsIgnoreCase("00:00:00")) {
                setTempoMedioAcademia(empresa.getTempoSaidaAcademiaFormatada());
            } else {
                setTempoMedioAcademia(tempoMedio.substring(0, 8));
            }

            Date dtFimClientesNaAcademia = Calendario.hoje();
            if (isUsuarioPacto()) {
                dtFimClientesNaAcademia = getDataHoraConsulta();
            }

            Date dtInicioClientesNaAcademia = Uteis.somarCampoData(dtFimClientesNaAcademia, Calendar.MINUTE, (Uteis.converteHoraParaInteiro(getTempoMedioAcademia()) * -1));
            Date dtInicio = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, (Uteis.converteHoraParaInteiro(getTempoMedioAcademia()) * -1));
            Date dtFim = Calendario.hoje();

            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.GESTAO_ACESSO,
                    getFiltroDTO(atualizarAgora,
                            dtInicioClientesNaAcademia, dtFimClientesNaAcademia,
                            dtInicio, dtFim));
            JSONObject dados = new JSONObject(filtroDTO.getJsonDados());

            setQuantidadeClienteNaAcademia(dados.getInt("alunosEmTempoReal"));
            setQuantidadeClienteComPendencia(dados.getInt("alunosPendenciasFinanceiras"));
            setQuantidadeAcessoLiberado(dados.getInt("acessosLiberados"));
            setQuantidadeClienteSituacaoInativo(dados.getInt("inativosComPermissaoAcesso"));
            setQuantidadeClienteOutraUnidade(dados.getInt("alunosOutraUnidade"));
            setQuantidadeClienteAulasAgendadas(dados.getInt("alunosAulasAgendadas"));
            setQuantidadeAcessosBloqueados(dados.getInt("acessosBloqueados"));
            setQuantidadeAcessosBloqueadosMes(dados.getInt("acessosBloqueadosMes"));
            setQuantidadeAcessosRealizadosDia(dados.getInt("acessosRealizadosDia"));

            if (dados.optLong("diaComMaisAcessoMes") != 0) {
                setDiaMaiorAcesso(Uteis.getDiaDaSemana(new Date(dados.getLong("diaComMaisAcessoMes"))));
            }
            setHoraMaiorAcesso(dados.getString("horarioComMaisAcessoMes"));

            List<IndicadorAcessoRel> indicadorAtual = new ArrayList<>();
            JSONArray arrayAtual = dados.optJSONArray("listaAcessoDiaHora");
            if (arrayAtual != null) {
                for (int i = 0; i < arrayAtual.length(); i++) {
                    JSONObject obj = arrayAtual.getJSONObject(i);

                    IndicadorAcessoRel indicador = new IndicadorAcessoRel();
                    indicador.setQuantidade(obj.getInt("quantidade"));
                    indicador.setAno(obj.getInt("ano"));
                    indicador.setMes(obj.getInt("mes"));
                    indicadorAtual.add(indicador);
                }
            }

            List<IndicadorAcessoRel> indicadorPassado = new ArrayList<>();
            JSONArray arrayPassado = dados.optJSONArray("listaAcessoDiaHoraPassado");
            if (arrayPassado != null) {
                for (int i = 0; i < arrayPassado.length(); i++) {
                    JSONObject obj = arrayPassado.getJSONObject(i);

                    IndicadorAcessoRel indicador = new IndicadorAcessoRel();
                    indicador.setQuantidade(obj.getInt("quantidade"));
                    indicador.setAno(obj.getInt("ano"));
                    indicador.setMes(obj.getInt("mes"));
                    indicadorPassado.add(indicador);
                }
            }

            if (empresa.getCapacidadeSimultanea() > 0) {
                try {
                    JSONObject objCovid19 = getFacade().getAcessoCliente().consultarAlunosNaAcademia(getEmpresaFiltroBI().getCodigo());
                    JSONObject objCovid19proximosMinutos = getFacade().getAcessoCliente().consultarAlunosNaAcademiaDaqui10minutos(getEmpresaFiltroBI().getCodigo());

                    setCapacidadeSimultanea(objCovid19.optInt("capacidade"));
                    setVagasDisponiveis(objCovid19.optInt("capacidade") - objCovid19.optInt("naAcademia"));
                    setVagasDisponiveis10min(objCovid19proximosMinutos.optInt("capacidade") - objCovid19proximosMinutos.optInt("naAcademia"));
                } catch (Exception ex) {
                    Uteis.logar(ex, GestaoAcessoRelControle.class);
                }
            }


            carregarGrafico(indicadorAtual, indicadorPassado);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void carregarGrafico(List<IndicadorAcessoRel> indicadorAtual, List<IndicadorAcessoRel> indicadorPassado) {
        Double valorMesAtual = 0.0;
        Double valorMesAnoPassado = 0.0;
        setListaAcessoGrafico("[]");

        setListaAcessoDiaHora(indicadorAtual);
        setListaAcessoDiaHoraPassado(indicadorPassado);

        processarListaIndicadorAcesso();
        if (!getListaAcessoProcessada().isEmpty()) {
            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (int i = 0; i < getListaAcessoProcessada().size(); i++) {
                dados = true;
                json.append("{").append("\"campo\":").append("\"").append("Acessos no Mês").append("\",");
                json.append("\"passado\":").append(getListaAcessoProcessada().get(i).getQuantidadePassado()).append(",");
                json.append("\"atual\":").append(getListaAcessoProcessada().get(i).getQuantidadeAtual()).append("},");
                valorMesAtual = getListaAcessoProcessada().get(i).getQuantidadeAtual().doubleValue();
                valorMesAnoPassado = getListaAcessoProcessada().get(i).getQuantidadePassado().doubleValue();
            }
            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");
            setListaAcessoGrafico(json.toString());
        }
        Double valor = valorMesAtual - valorMesAnoPassado;
        if (valorMesAnoPassado > 0) {
            valor = (valor / valorMesAnoPassado) * 100;
        }

        if (valor < 0) {
            setPercentualCrescimento(Uteis.arredondarForcando2CasasDecimais(valor) * -1);
        } else {
            setPercentualCrescimento(Uteis.arredondarForcando2CasasDecimais(valor));
        }

    }

    private void processarListaIndicadorAcesso() {
        setListaAcessoProcessada(new ArrayList<>());
        for (final IndicadorAcessoRel indicadorAcessoRel : getListaAcessoDiaHoraPassado()) {
            IndicadorAcessoRel item = (IndicadorAcessoRel) ColecaoUtils.find(getListaAcessoDiaHora(), new Predicate() {
                @Override
                public boolean evaluate(Object o) {
                    return ((IndicadorAcessoRel) o).getMes().equals(indicadorAcessoRel.getMes());
                }
            });
            IndicadorAcessoTO indicadorAcessoTO = new IndicadorAcessoTO();
            if (item != null) {
                indicadorAcessoTO.setMesPassado(indicadorAcessoRel.getMes());
                indicadorAcessoTO.setAnoPassado(indicadorAcessoRel.getAno());
                indicadorAcessoTO.setQuantidadePassado(indicadorAcessoRel.getQuantidade());
                indicadorAcessoTO.setMesAtual(item.getMes());
                indicadorAcessoTO.setAnoAtual(item.getAno());
                indicadorAcessoTO.setQuantidadeAtual(item.getQuantidade());
            } else {
                indicadorAcessoTO.setMesPassado(indicadorAcessoRel.getMes());
                indicadorAcessoTO.setAnoPassado(indicadorAcessoRel.getAno());
                indicadorAcessoTO.setQuantidadePassado(indicadorAcessoRel.getQuantidade());
                indicadorAcessoTO.setMesAtual(indicadorAcessoRel.getMes());
                indicadorAcessoTO.setAnoAtual(indicadorAcessoRel.getAno() + 1);
                indicadorAcessoTO.setQuantidadeAtual(0);
            }
            getListaAcessoProcessada().add(indicadorAcessoTO);
        }
    }

    private void descobrirPrimeiroDiaMes() throws Exception {
        if (primeiroDiaMes == null) {
            primeiroDiaMes = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
        }
    }

    private void processarListaAcessoANTIGO() throws Exception {
        String tempoMedio;
        EmpresaVO empresa;

        // Se filtro empresa = 0, então é para consultar de todas as empresa.
        // Neste caso utiliza-se a empresa.codigo = 1 para obter configuração de "Tempo médio para para sair da academia."
        if (getEmpresaFiltroBI().getCodigo() == 0) {
            List<EmpresaVO> listaEmpresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            empresa = listaEmpresas.get(0);
            tempoMedio = getFacade().getAcessoCliente().obterMediaTempoAcademiaPeriodo(primeiroDiaMes, Calendario.hoje(), empresa.getCodigo());
        } else {
            empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            tempoMedio = getFacade().getAcessoCliente().obterMediaTempoAcademiaPeriodo(primeiroDiaMes, Calendario.hoje(), getEmpresaFiltroBI().getCodigo());
        }

        if (tempoMedio == null || tempoMedio.equalsIgnoreCase("00:00:00")) {
            setTempoMedioAcademia(empresa.getTempoSaidaAcademiaFormatada());
        } else {
            setTempoMedioAcademia(tempoMedio.substring(0, 8));
        }

        Date hoje = Calendario.hoje();
        if (isUsuarioPacto()) {
            hoje = getDataHoraConsulta();
        }

        Date horarioInicial = Uteis.somarCampoData(hoje, Calendar.MINUTE, (Uteis.converteHoraParaInteiro(getTempoMedioAcademia()) * -1));
        setQuantidadeClienteNaAcademia(getFacade().getAcessoCliente().obterQuantidadeClientePelaHora(horarioInicial, hoje, getEmpresaFiltroBI().getCodigo()));

        horarioInicial = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, (Uteis.converteHoraParaInteiro(getTempoMedioAcademia()) * -1));

        setQuantidadeClienteComPendencia(getFacade().getAcessoCliente().obterClientesPelaHoraComPendeciaFinanceira(horarioInicial, Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));
        setQuantidadeAcessoLiberado(getFacade().getLiberacaoAcesso().consultarTotalLiberacao(Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));
        setQuantidadeClienteSituacaoInativo(getFacade().getAcessoCliente().obterClientesInativosPelaHora(horarioInicial, Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));
        setQuantidadeClienteOutraUnidade(getFacade().getAcessoCliente().obterClientesOutraUnidadePelaHora(horarioInicial, Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));
        setQuantidadeClienteAulasAgendadas(getFacade().getAcessoCliente().obterClientesNaAcademiaAulasAgendadas(Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));

        if (empresa.getCapacidadeSimultanea() > 0) {
            JSONObject objCovid19 = getFacade().getAcessoCliente().consultarAlunosNaAcademia(getEmpresaFiltroBI().getCodigo());
            JSONObject objCovid19proximosMinutos = getFacade().getAcessoCliente().consultarAlunosNaAcademiaDaqui10minutos(getEmpresaFiltroBI().getCodigo());

            setCapacidadeSimultanea(objCovid19.optInt("capacidade"));
            setVagasDisponiveis(objCovid19.optInt("capacidade") - objCovid19.optInt("naAcademia"));
            setVagasDisponiveis10min(objCovid19proximosMinutos.optInt("capacidade") - objCovid19proximosMinutos.optInt("naAcademia"));
        }

        carregarGrafico(new ArrayList<>(), new ArrayList<>());
    }

    public void processarListaClientesNaAcademia() {
        try {
            Date hoje = Calendario.hoje();
            if (isUsuarioPacto()) {
                hoje = getDataHoraConsulta();
            }

            EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            Date dataInicioHoraReduzida = Uteis.somarCampoData(hoje, Calendar.MINUTE, (Uteis.converteHoraParaInteiro(getTempoMedioAcademia()) * -1));
            setListaAcessoClienteNaAcademia(getFacade().getAcessoCliente().obterClientesPelaHora(dataInicioHoraReduzida, hoje, empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarClientesAulasAgendadas() {
        try {
            setListaAcessoQuantidadeClienteAulasAgendadas(getFacade().getAcessoCliente().consultarClientesNaAcademiaAulasAgendadas(Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarAlunosAcessosBloqueados(String periodo) {
        try {
            setListaQuantidadeAcessosBloqueados(getFacade().getAcessoCliente().consultarAcessosBloqueados(periodo, primeiroDiaMes, Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));
        } catch (Exception e) {
            montarErro(e);
        }

    }



    public void processarListaClientesNaAcademiaComPendencia() {
        try {
            setListaClienteComPendeciaFinanceira(new ArrayList<AcessoClienteTO>());
            EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            Date dataInicioHoraReduzida = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, (empresa.getTempoSaidaAcademia() * -1));

            List<AcessoClienteVO> listaAcessoCliente = getFacade().getAcessoCliente().obterClientesPelaHoraComPendeciaFinanceira(dataInicioHoraReduzida, Calendario.hoje(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            Map<Integer, AcessoClienteTO> mapaClientes = new HashMap<Integer, AcessoClienteTO>();
            for (AcessoClienteVO acessoClienteVO : listaAcessoCliente) {
                List<CaixaAbertoTO> caixas = getFacade().getMovParcela().consultaCaixaEmAberto(acessoClienteVO.getCliente().getMatricula(),
                        "", empresa.getCodigo(),
                        null, null, new ConfPaginacao(), false, false, new ArrayList<Integer>(), false);
                if (acessoClienteVO.getCliente().getEmpresa().getCodigo().equals(empresa.getCodigo())) {
                    if (!caixas.isEmpty()) {
                        for (CaixaAbertoTO caixa : caixas) {
                            Double valorTotal = 0.0;
                            for (MovParcelaVO parcela : caixa.getParcelas()) {
                                valorTotal += parcela.getValorParcela();
                            }
                            AcessoClienteTO acessoClienteTO = mapaClientes.get(acessoClienteVO.getCliente().getCodigo());
                            if (acessoClienteTO == null) {
                                acessoClienteTO = new AcessoClienteTO();
                                acessoClienteTO.setCliente(acessoClienteVO.getCliente());
                                acessoClienteTO.setLocalAcesso(acessoClienteVO.getLocalAcesso());
                                acessoClienteTO.setColetor(acessoClienteVO.getColetor());
                                acessoClienteTO.setCaixaAberto(caixa);
                                acessoClienteTO.setDataHoraEntrada(acessoClienteVO.getDataHoraEntrada());
                                acessoClienteTO.setDataHoraSaida(acessoClienteVO.getDataHoraSaida());
                                acessoClienteTO.setValorTotal(valorTotal);
                            }
                            mapaClientes.put(acessoClienteVO.getCliente().getCodigo(), acessoClienteTO);
                        }
                    }
                }
            }
            setListaClienteComPendeciaFinanceira(new ArrayList<AcessoClienteTO>(mapaClientes.values()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void processarAcessoLiberado() {
        try {
            setListaAcessoLiberado(new ArrayList<LiberacaoAcessoVO>());
            EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            setListaAcessoLiberado(getFacade().getLiberacaoAcesso().consultarTotalLiberacao(Calendario.hoje(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void processarListaClienteInativoComAcesso() {
        try {
            setListaAcessoClienteNaAcademiaSituacaoInativo(new ArrayList<>());
            EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            Date dataInicioHoraReduzida = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, (empresa.getTempoSaidaAcademia() * -1));
            setListaAcessoClienteNaAcademiaSituacaoInativo(getFacade().getAcessoCliente().obterClientesInativosPelaHora(dataInicioHoraReduzida, Calendario.hoje(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

            for (AcessoClienteVO acessoClienteVO : getListaAcessoClienteNaAcademiaSituacaoInativo()) {
                PeriodoAcessoClienteVO periodoAcessoClienteVO = getFacade().getPeriodoAcessoCliente().consultarPorDataPessoa(Calendario.hoje(), acessoClienteVO.getCliente().getPessoa().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
                if (periodoAcessoClienteVO != null) {
                    acessoClienteVO.setTipoAcesso(periodoAcessoClienteVO.getTipoAcesso_Apresentar());
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void processarListaClienteOutraUnidade() {
        try {
            setListaAcessoClienteNaAcademiaOutraUnidade(new ArrayList<>());
            EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltroBI().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            Date dataInicioHoraReduzida = Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, (empresa.getTempoSaidaAcademia() * -1));
            setListaAcessoClienteNaAcademiaOutraUnidade(getFacade().getAcessoCliente().obterClientesOutraUnidadePelaHora(dataInicioHoraReduzida, Calendario.hoje(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

            for (AcessoClienteVO acessoClienteVO : getListaAcessoClienteNaAcademiaOutraUnidade()) {
                EmpresaVO empresaNova = getFacade().getEmpresa().consultarPorChavePrimaria(acessoClienteVO.getCliente().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                acessoClienteVO.getCliente().setEmpresa(empresaNova);
            }

        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void processarMaioresAcessosANTIGO() throws Exception {
        setDiaMaiorAcesso("");
        setHoraMaiorAcesso("");
        TotalizadorFrequenciaRel indicadorAcessoRel = new TotalizadorFrequenciaRel();
        indicadorAcessoMaiorAcesso = indicadorAcessoRel.consultarDiaComMaiorAcesso(primeiroDiaMes, Calendario.hoje(), getEmpresaFiltroBI().getCodigo());
        if (indicadorAcessoMaiorAcesso.getData() != null) {
            setDiaMaiorAcesso(Calendario.getDiaDaSemana(indicadorAcessoMaiorAcesso.getData()));
        }
        indicadorAcessoMaiorAcessoHora = indicadorAcessoRel.consultarHoraComMaiorAcesso(primeiroDiaMes, Calendario.hoje(), getEmpresaFiltroBI().getCodigo());
        if (indicadorAcessoMaiorAcessoHora.getHora() != null) {
            setHoraMaiorAcesso(verificarHora(indicadorAcessoMaiorAcessoHora.getHora()));
        }
    }

    private String verificarHora(Double horaParametro) {
        String hora;
        int horaInteiro = horaParametro.intValue();
        if (horaInteiro <= 9) {
            hora = "0" + horaInteiro;
        } else {
            hora = Integer.toString(horaInteiro);
        }

        return hora + ":" + "00";
    }

    public List<AcessoClienteVO> getListaAcessoClienteNaAcademia() {
        return listaAcessoClienteNaAcademia;
    }

    public void setListaAcessoClienteNaAcademia(List<AcessoClienteVO> listaAcessoClienteNaAcademia) {
        this.listaAcessoClienteNaAcademia = listaAcessoClienteNaAcademia;
    }

    public Integer getQuantidadeClienteNaAcademia() {
        return quantidadeClienteNaAcademia;
    }

    public void setQuantidadeClienteNaAcademia(Integer quantidadeClienteNaAcademia) {
        this.quantidadeClienteNaAcademia = quantidadeClienteNaAcademia;
    }

    public List<AcessoClienteTO> getListaClienteComPendeciaFinanceira() {
        return listaClienteComPendeciaFinanceira;
    }

    public void setListaClienteComPendeciaFinanceira(List<AcessoClienteTO> listaClienteComPendeciaFinanceira) {
        this.listaClienteComPendeciaFinanceira = listaClienteComPendeciaFinanceira;
    }

    public Integer getQuantidadeClienteComPendencia() {
        return quantidadeClienteComPendencia;
    }

    public void setQuantidadeClienteComPendencia(Integer quantidadeClienteComPendencia) {
        this.quantidadeClienteComPendencia = quantidadeClienteComPendencia;
    }

    public List<AcessoClienteVO> getListaAcessoClienteNaAcademiaSituacaoInativo() {
        return listaAcessoClienteNaAcademiaSituacaoInativo;
    }

    public void setListaAcessoClienteNaAcademiaSituacaoInativo(List<AcessoClienteVO> listaAcessoClienteNaAcademiaSituacaoInativo) {
        this.listaAcessoClienteNaAcademiaSituacaoInativo = listaAcessoClienteNaAcademiaSituacaoInativo;
    }

    public Integer getQuantidadeClienteSituacaoInativo() {
        return quantidadeClienteSituacaoInativo;
    }

    public void setQuantidadeClienteSituacaoInativo(Integer quantidadeClienteSituacaoInativo) {
        this.quantidadeClienteSituacaoInativo = quantidadeClienteSituacaoInativo;
    }

    public Integer getQuantidadeClienteOutraUnidade() {
        return quantidadeClienteOutraUnidade;
    }

    public void setQuantidadeClienteOutraUnidade(Integer quantidadeClienteOutraUnidade) {
        this.quantidadeClienteOutraUnidade = quantidadeClienteOutraUnidade;
    }

    public List<AcessoClienteVO> getListaAcessoClienteNaAcademiaOutraUnidade() {
        return listaAcessoClienteNaAcademiaOutraUnidade;
    }

    public void setListaAcessoClienteNaAcademiaOutraUnidade(List<AcessoClienteVO> listaAcessoClienteNaAcademiaOutraUnidade) {
        this.listaAcessoClienteNaAcademiaOutraUnidade = listaAcessoClienteNaAcademiaOutraUnidade;
    }

    public Integer getQuantidadeAcessoLiberado() {
        return quantidadeAcessoLiberado;
    }

    public void setQuantidadeAcessoLiberado(Integer quantidadeAcessoLiberado) {
        this.quantidadeAcessoLiberado = quantidadeAcessoLiberado;
    }

    public Integer getQuantidadeAcessosBloqueadosMes() {
        return quantidadeAcessosBloqueadosMes;
    }

    public void setQuantidadeAcessosBloqueadosMes(Integer quantidadeAcessosBloqueadosMes) {
        this.quantidadeAcessosBloqueadosMes = quantidadeAcessosBloqueadosMes;
    }

    public List<LiberacaoAcessoVO> getListaAcessoLiberado() {
        return listaAcessoLiberado;
    }

    public void setListaAcessoLiberado(List<LiberacaoAcessoVO> listaAcessoLiberado) {
        this.listaAcessoLiberado = listaAcessoLiberado;
    }

    public Date getPrimeiroDiaMes() {
        return primeiroDiaMes;
    }

    public void setPrimeiroDiaMes(Date primeiroDiaMes) {
        this.primeiroDiaMes = primeiroDiaMes;
    }

    public String getListaAcessoGrafico() {
        return listaAcessoGrafico;
    }

    public void setListaAcessoGrafico(String listaAcessoGrafico) {
        this.listaAcessoGrafico = listaAcessoGrafico;
    }

    public List<IndicadorAcessoRel> getListaAcessoDiaHora() {
        return listaAcessoDiaHora;
    }

    public void setListaAcessoDiaHora(List<IndicadorAcessoRel> listaAcessoDiaHora) {
        this.listaAcessoDiaHora = listaAcessoDiaHora;
    }

    public List<IndicadorAcessoRel> getListaAcessoDiaHoraPassado() {
        return listaAcessoDiaHoraPassado;
    }

    public void setListaAcessoDiaHoraPassado(List<IndicadorAcessoRel> listaAcessoDiaHoraPassado) {
        this.listaAcessoDiaHoraPassado = listaAcessoDiaHoraPassado;
    }

    public List<IndicadorAcessoTO> getListaAcessoProcessada() {
        return listaAcessoProcessada;
    }

    public void setListaAcessoProcessada(List<IndicadorAcessoTO> listaAcessoProcessada) {
        this.listaAcessoProcessada = listaAcessoProcessada;
    }

    public IndicadorAcessoRel getIndicadorAcessoMaiorAcesso() {
        return indicadorAcessoMaiorAcesso;
    }

    public void setIndicadorAcessoMaiorAcesso(IndicadorAcessoRel indicadorAcessoMaiorAcesso) {
        this.indicadorAcessoMaiorAcesso = indicadorAcessoMaiorAcesso;
    }

    public String getDiaMaiorAcesso() {
        return diaMaiorAcesso;
    }

    public void setDiaMaiorAcesso(String diaMaiorAcesso) {
        this.diaMaiorAcesso = diaMaiorAcesso;
    }

    public IndicadorAcessoRel getIndicadorAcessoMaiorAcessoHora() {
        return indicadorAcessoMaiorAcessoHora;
    }

    public void setIndicadorAcessoMaiorAcessoHora(IndicadorAcessoRel indicadorAcessoMaiorAcessoHora) {
        this.indicadorAcessoMaiorAcessoHora = indicadorAcessoMaiorAcessoHora;
    }

    public String getHoraMaiorAcesso() {
        return horaMaiorAcesso;
    }

    public void setHoraMaiorAcesso(String horaMaiorAcesso) {
        this.horaMaiorAcesso = horaMaiorAcesso;
    }

    public String getTempoMedioAcademia() {
        return tempoMedioAcademia;
    }

    public void setTempoMedioAcademia(String tempoMedioAcademia) {
        this.tempoMedioAcademia = tempoMedioAcademia;
    }

    public Double getPercentualCrescimento() {
        return percentualCrescimento;
    }

    public void setPercentualCrescimento(Double percentualCrescimento) {
        this.percentualCrescimento = percentualCrescimento;
    }

    public void irParaTelaCliente() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("cliente");
        try {
            if (obj == null) {
                throw new Exception("Cliente não encontrado.");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(Integer.parseInt(obj));
                irParaTelaCliente(clienteVO);
                setOnComplete("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public Integer getCapacidadeSimultanea() {
        return capacidadeSimultanea;
    }

    public void setCapacidadeSimultanea(Integer capacidadeSimultanea) {
        this.capacidadeSimultanea = capacidadeSimultanea;
    }

    public Integer getVagasDisponiveis() {
        return vagasDisponiveis;
    }

    public void setVagasDisponiveis(Integer vagasDisponiveis) {
        this.vagasDisponiveis = vagasDisponiveis;
    }

    public Integer getVagasDisponiveis10min() {
        return vagasDisponiveis10min;
    }

    public void setVagasDisponiveis10min(Integer vagasDisponiveis10min) {
        this.vagasDisponiveis10min = vagasDisponiveis10min;
    }


    public Integer getQuantidadeClienteAulasAgendadas() {
        return quantidadeClienteAulasAgendadas;
    }

    public void setQuantidadeClienteAulasAgendadas(Integer quantidadeClienteAulasAgendadas) {
        this.quantidadeClienteAulasAgendadas = quantidadeClienteAulasAgendadas;
    }

    public Integer getQuantidadeAcessosBloqueados() {
        return quantidadeAcessosBloqueados;
    }

    public void setQuantidadeAcessosBloqueados(Integer quantidadeAcessosBloqueados) {
        this.quantidadeAcessosBloqueados = quantidadeAcessosBloqueados;
    }

    public List<AlunoHorarioTurmaTO> getListaAcessoQuantidadeClienteAulasAgendadas() {
        return listaAcessoQuantidadeClienteAulasAgendadas;
    }

    public void setListaAcessoQuantidadeClienteAulasAgendadas(List<AlunoHorarioTurmaTO> listaAcessoQuantidadeClienteAulasAgendadas) {
        this.listaAcessoQuantidadeClienteAulasAgendadas = listaAcessoQuantidadeClienteAulasAgendadas;
    }

    public List<AcessoBloqueadoTO> getListaQuantidadeAcessosBloqueados() {
        return listaQuantidadeAcessosBloqueados;
    }

    public void setListaQuantidadeAcessosBloqueados(List<AcessoBloqueadoTO> listaQuantidadeAcessosBloqueados) {
        this.listaQuantidadeAcessosBloqueados = listaQuantidadeAcessosBloqueados;
    }

    public Date getDataHoraConsulta() {
        if (dataHoraConsulta == null) {
            dataHoraConsulta = Calendario.hoje();
        }
        return dataHoraConsulta;
    }

    public void setDataHoraConsulta(Date dataHoraConsulta) {
        this.dataHoraConsulta = dataHoraConsulta;
    }

    private FiltroDTO getFiltroDTO(boolean atualizarAgora,
                                   Date dtInicioClientesNaAcademia, Date dtFimClientesNaAcademia,
                                   Date dtInicio, Date dtFim) {

        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.GESTAO_ACESSO.name());
        JSONObject filtros = new JSONObject();
        filtros.put("atualizarAgora", atualizarAgora);
        filtros.put("naAcademia_inicio", dtInicioClientesNaAcademia.getTime());
        filtros.put("naAcademia_final", dtFimClientesNaAcademia.getTime());
        Date dataInicialZerada = Calendario.getDataComHoraZerada(dtInicio);
        filtros.put("dataInicial", dataInicialZerada.getTime());
        Date dataFinalZerada = Calendario.getDataComHoraZerada(dtFim);
        filtros.put("dataFinal", dataFinalZerada.getTime());
        filtros.put("primeiroDiaMes", primeiroDiaMes.getTime());

        if (UteisValidacao.notEmptyNumber(getEmpresaFiltroBI().getCodigo())) {
            filtros.put("empresa", getEmpresaFiltroBI().getCodigo());
        }

        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public Integer getQuantidadeAcessosRealizadosDia() {
        return quantidadeAcessosRealizadosDia;
    }

    public void setQuantidadeAcessosRealizadosDia(Integer quantidadeAcessosRealizadosDia) {
        this.quantidadeAcessosRealizadosDia = quantidadeAcessosRealizadosDia;
    }

    public List<AcessoRealizadoTO> getListaAcessosRealizadosDia() {
        return listaAcessosRealizadosDia;
    }

    public void setListaAcessosRealizadosDia(List<AcessoRealizadoTO> listaAcessosRealizadosDia) {
        this.listaAcessosRealizadosDia = listaAcessosRealizadosDia;
    }

    public void consultarAlunosAcessosRealizadosDia() {
        try {
            setListaAcessosRealizadosDia(getFacade().getAcessoCliente().consultarAcessosRealizadosDia(Calendario.hoje(), getEmpresaFiltroBI().getCodigo()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

}
