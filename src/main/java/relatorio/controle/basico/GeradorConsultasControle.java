package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.servico.UpdateServico;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.basico.ConsultaTO;
import relatorio.negocio.comuns.basico.ConsultasCadastradasTO;
import relatorio.negocio.comuns.basico.GruposConsultasTO;

import javax.faces.event.ActionEvent;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GeradorConsultasControle extends SuperControle {

    private static final String selectONE = "selectONE";
    private static final String selectALL = "selectALL";
    private Boolean selectAll = false;
    private Boolean testarSQL = false;
    private ConsultaTO consultaSelecionada = new ConsultaTO();
    private Integer qtdConsulta = 0;
    private List<GruposConsultasTO> gruposConsultas = new ArrayList<GruposConsultasTO>();
    private Map<String, String> mapa = new HashMap<String, String>();
    private String consulta = "";
    private String whereDaConsulta = "";
    private String modalGeradorConsultas = "";
    private String informacaoModalConfirmar = "";
    private String fileName = "JasperGenerics.xls";
    private String hostBD = "localhost";
    private String portaBD = "5432";
    private String nomeBanco = "";
    private String userPG = "";
    private String pwdPG = "";
    private String senhaSelectALL = "";
    private String mimeType = "application/vnd.ms-excel";

    public GeradorConsultasControle() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.ENTROU_GERADOR_CONSULTA);
        gruposConsultas = ConsultasCadastradasTO.getInstance(getEmpresaLogado().getCodigo()).getConsultasCadastradas();
        Conexao conexao = Conexao.getInstance();
        Connection con = Conexao.getFromSession();

        String[] urlConexao = con.getMetaData().getURL().split("/");
        String[] servidor = urlConexao[2].split(":");
        setHostBD(servidor[0]);
        setPortaBD(servidor[1]);
        setNomeBanco(urlConexao[urlConexao.length - 1]);
        setUserPG(conexao.getUsernameBD());
        setPwdPG(conexao.getSenhaDefault());
    }

    public void selecionarConsulta(ActionEvent event) {
        limparModal();
        ConsultaTO consultaTO = (ConsultaTO) context().getExternalContext().getRequestMap().get("consulta");
        setConsultaSelecionada(consultaTO);

        if (consultaTO.isPermiteWhere()) {
            setWhereDaConsulta(consultaTO.getWhere());
        }

        setModalGeradorConsultas("Richfaces.showModalPanel('modalGeradorConsultas')");
    }

    public void selecionarConsultaTeste(ActionEvent event) {
        limparModal();
        ConsultaTO consultaTO = new ConsultaTO();
        consultaTO.setPermiteWhere(true);
        consultaTO.setPermiteSelectAll(true);
        setTestarSQL(true);
        setConsultaSelecionada(consultaTO);

        setModalGeradorConsultas("Richfaces.showModalPanel('modalGeradorConsultas')");
    }

    private void limparModal() {
        setConsulta("");
        setWhereDaConsulta("");
        setQtdConsulta(0);
        setTestarSQL(false);
        setSelectAll(false);
    }

    public void prepararParametrosRelatorio() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.GERAR_RELATORIO_GERADOR_CONSULTA);
        setMapa(new HashMap<String, String>());
        if (getSelectAll()) {
            getMapa().put("op", GeradorConsultasControle.selectALL);
        } else {
            getMapa().put("op", GeradorConsultasControle.selectONE);
            getMapa().put("bd", getNomeBanco());
        }
        setMimeType(getConsultaSelecionada().getFormatoExportar());

        getMapa().put("hostPG", getHostBD());
        getMapa().put("portaPG", getPortaBD());

        //Conexão
        getMapa().put("userPG", getUserPG());
        getMapa().put("pwdPG", getPwdPG());


        getMapa().put("sql", prepararSQL());

        String prefixoArquivo = String.format("%s-%s-%s", getConsultaSelecionada().getNomeArquivo(), getKey(), new Date().getTime());

        if(getMimeType().equals("text/plain")){
            setFileName(prefixoArquivo + ".txt");
            getMapa().put("format", "txt");
        }else {
            setFileName(prefixoArquivo + ".xls");
            getMapa().put("format", "excel");
        }




        getMapa().put("prefixoArquivo", prefixoArquivo);

        String sql = getMapa().get("sql");
        String sqlTemp = sql.toLowerCase();
        boolean temDelete = sqlTemp.contains("delete ") || sqlTemp.contains(" delete ");
        boolean temUpdate = sqlTemp.contains("update ") || sqlTemp.contains(" update ");
        boolean temDrop = sqlTemp.contains("drop ") || sqlTemp.contains(" drop ");
        boolean temAlter = sqlTemp.contains("alter ") || sqlTemp.contains(" alter ");
        boolean temInsert = sqlTemp.contains("insert ") || sqlTemp.contains(" insert ");
        try {
            if (temAlter || temDelete || temDrop || temUpdate || temInsert) {
                setInformacaoModalConfirmar("Sua consulta não pode ser realizada por conter uma das seguintes palavras: 'DELETE', 'UPDATE', 'DROP', 'ALTER', 'INSERT'");
            } else {
                if (!getSelectAll()) {
                    setQtdConsulta(SuperFacadeJDBC.contar(prepararSQLCount().replaceAll(";", ""), getFacade().getZWFacade().getCon()));
                    if (getQtdConsulta() > 0) {
                        setInformacaoModalConfirmar("Sua consulta trará " + getQtdConsulta() + " linha de dados. Deseja continuar?");
                    } else {
                        setInformacaoModalConfirmar("Sua consulta não trará nenhuma linha de dados.");
                    }
                } else {
                    //Número diferente de 0, para aparecer os botões;
                    setQtdConsulta(1);
                    setInformacaoModalConfirmar("Sua consulta poderá demorar um pouco...");
                }
            }
            if (getSelectAll() && (getHostBD().equals("********") || getHostBD().equals("********"))) {
                setMsgAlertAuxiliar("Richfaces.showModalPanel('modalConfirmarSelectALL');");
            } else {
                setMsgAlertAuxiliar("Richfaces.showModalPanel('modalConfirmar');");
            }
        }catch (Exception e){
            setInformacaoModalConfirmar("Sua cláusula WHERE está incorreta.");
            setMsgAlertAuxiliar("Richfaces.showModalPanel('modalConfirmar');");
        }
    }
    
    public void validarSenhaSelectALL() throws UnsupportedEncodingException{
         if(Uteis.encriptar(getSenhaSelectALL().toUpperCase()).equals("59d1e5c9b93e7d3b78d40392e18220c50416e4842f3cf97a3ecf790beaa9ad2a")){
             setMsgAlertAuxiliar("Richfaces.hideModalPanel('modalConfirmarSelectALL');Richfaces.showModalPanel('modalConfirmar');");
         } else {
             setMsgAlertAuxiliar("Richfaces.showModalPanel('modalConfirmarSelectALL');");
             setMensagemDetalhada("senha incorreta!");
         }
         
    }
    
    public void gerarSemSelectAll() throws Exception{
        setSelectAll(false);
        prepararParametrosRelatorio();
    }

    public void gerarRelatorioTexto() {
        try {
            String textoSalvar = "";
            if (getConsultaSelecionada().getNomeArquivo().equals("AlunoEstacionamento")) {
                textoSalvar = getFacade().getCliente().obterImportacaoClinteEstacionamento(getMapa().get("sql"), getEmpresaLogado().getCodigo());
            }
            Uteis.salvarArquivo(getFileName(), textoSalvar, request().getRealPath("relatorio") + File.separator);
        } catch (Exception ignored) {
        }
    }
    public String executarConsulta() throws IOException {
        UpdateServico updateServico;
        try {
            if(getMimeType().equals("text/plain")){
                gerarRelatorioTexto();
            }else {
                updateServico = new UpdateServico();
                if (getMapa().get("op").equals(GeradorConsultasControle.selectONE)) {
                    updateServico.executarConsultaSQL(getMapa().get("hostPG"),
                            getMapa().get("portaPG"),
                            getMapa().get("userPG"),
                            getMapa().get("pwdPG"),
                            getMapa().get("bd"),
                            getMapa().get("sql"),
                            getMapa().get("format"),
                            getMapa().get("prefixoArquivo"), null, null, response(), this.getServletContext(), true, 1);
                } else if (getMapa().get("op").equals(GeradorConsultasControle.selectALL)) {
                    updateServico.executarConsultaSQLTodosBancosDados(
                            getMapa().get("hostPG"),
                            getMapa().get("portaPG"),
                            getMapa().get("userPG"),
                            getMapa().get("pwdPG"),
                            getMapa().get("sql"),
                            getMapa().get("except"),
                            getMapa().get("format"),
                            getMapa().get("prefixoArquivo"), null, null, null, response(), this.getServletContext(), true, 1);
                }
            }
        } finally {
            updateServico = null;
        }
        return "";
    }

    private String prepararSQLCount() {
        return "SELECT count(*) FROM (" + prepararSQL() + ") as foo";
    }

    private String prepararSQL() {
        String sql;
        if (getTestarSQL()) {
            sql = getConsulta().toLowerCase().split("where")[0];
        } else {
            sql = getConsultaSelecionada().getSQLMontada();
        }
        if (!UteisValidacao.emptyString(getWhereDaConsulta())) {
            // VALIDAÇÃO CRÍTICA DE SEGURANÇA: Verificar se a cláusula WHERE contém comandos perigosos
            String whereValidado = Uteis.validarClausulaWhere(getWhereDaConsulta());
            if (whereValidado == null) {
                throw new SecurityException("Cláusula WHERE contém comandos não permitidos por motivos de segurança");
            }

            boolean existeWhere = false;
            String where = whereValidado.toLowerCase();
            if (where.contains("where ")) {
                existeWhere = true;
            }
            if (existeWhere) {
                sql = sql + "\n" + whereValidado;
            } else {
                sql = sql + "\nWHERE " + whereValidado;
            }
        }
        return sql;
    }



    private String obterUrlUpdateServlet() {
        String requestURL = JSFUtilities.getRequest().getRequestURL().toString();
        String url = requestURL.split("faces")[0];
        url = url + "UpdateServlet";
        return url;
    }

    public List<GruposConsultasTO> getGruposConsultas() {
        return gruposConsultas;
    }

    public void setGruposConsultas(List<GruposConsultasTO> gruposConsultas) {
        this.gruposConsultas = gruposConsultas;
    }

    public ConsultaTO getConsultaSelecionada() {
        return consultaSelecionada;
    }

    public void setConsultaSelecionada(ConsultaTO consultaSelecionada) {
        this.consultaSelecionada = consultaSelecionada;
    }

    public Boolean getSelectAll() {
        return selectAll;
    }

    public void setSelectAll(Boolean selectAll) {
        this.selectAll = selectAll;
    }

    public String getWhereDaConsulta() {
        return whereDaConsulta;
    }

    public String getNumberRowsWhere(){
        return UteisValidacao.emptyString(whereDaConsulta) ? "2" : (Uteis.numeroQuebraLinhasString(whereDaConsulta)+1)+"";
    }

    public void setWhereDaConsulta(String whereDaConsulta) {
        this.whereDaConsulta = whereDaConsulta;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Boolean getTestarSQL() {
        return testarSQL;
    }

    public void setTestarSQL(Boolean testarSQL) {
        this.testarSQL = testarSQL;
    }

    public String getConsulta() {
        return consulta;
    }

    public void setConsulta(String consulta) {
        this.consulta = consulta;
    }

    public Integer getQtdConsulta() {
        return qtdConsulta;
    }

    public void setQtdConsulta(Integer qtdConsulta) {
        this.qtdConsulta = qtdConsulta;
    }

    public Map<String, String> getMapa() {
        return mapa;
    }

    public void setMapa(Map<String, String> mapa) {
        this.mapa = mapa;
    }

    public String getModalGeradorConsultas() {
        return modalGeradorConsultas;
    }

    public void setModalGeradorConsultas(String modalGeradorConsultas) {
        this.modalGeradorConsultas = modalGeradorConsultas;
    }

    public String getInformacaoModalConfirmar() {
        return informacaoModalConfirmar;
    }

    public void setInformacaoModalConfirmar(String informacaoModalConfirmar) {
        this.informacaoModalConfirmar = informacaoModalConfirmar;
    }
    
    public boolean getServidorUsuarioPermiteSelectALL(){
        try {
            return getConsultaSelecionada().isPermiteSelectAll() &&
                    (getUsuarioLogado().getUsername().equals("admin") ||
                    (getUsuarioLogado().getAdministrador() && (!getHostBD().equals("********") && !getHostBD().equals("********"))));
        } catch (Exception ex) {
           return false;
        }
    }

    public String getHostBD() {
        return hostBD;
    }

    public void setHostBD(String hostBD) {
        this.hostBD = hostBD;
    }

    public String getPortaBD() {
        return portaBD;
    }

    public void setPortaBD(String portaBD) {
        this.portaBD = portaBD;
    }

    public String getNomeBanco() {
        return nomeBanco;
    }

    public void setNomeBanco(String nomeBanco) {
        this.nomeBanco = nomeBanco;
    }

    public String getUserPG() {
        return userPG;
    }

    public void setUserPG(String userPG) {
        this.userPG = userPG;
    }

    public String getPwdPG() {
        return pwdPG;
    }

    public void setPwdPG(String pwdPG) {
        this.pwdPG = pwdPG;
    }

    public String getSenhaSelectALL() {
        return senhaSelectALL;
    }

    public void setSenhaSelectALL(String senhaSelectALL) {
        this.senhaSelectALL = senhaSelectALL;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }
}
