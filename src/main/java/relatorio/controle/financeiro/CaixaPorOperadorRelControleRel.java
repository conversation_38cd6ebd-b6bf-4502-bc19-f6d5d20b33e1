/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.ReciboDevolucaoVO;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.utilitarias.*;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import org.apache.commons.lang.StringUtils;
import relatorio.controle.arquitetura.SuperControleRecibo;
import relatorio.negocio.comuns.financeiro.ReciboPagamentoRelTO;
import relatorio.negocio.jdbc.financeiro.CaixaPorOperadorDetalheSaldoLivroRel;
import relatorio.negocio.jdbc.financeiro.CaixaPorOperadorLivroRel;
import relatorio.negocio.jdbc.financeiro.CaixaPorOperadorRel;
import relatorio.negocio.jdbc.financeiro.TotalizadorFormasPagamento;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 */
public class CaixaPorOperadorRelControleRel extends SuperControleRecibo {

    private UsuarioVO usuarioFiltro;
    private CaixaPorOperadorRel caixaPorOperadorRel;
    private String campoConsultarOperador;
    private String valorConsultarOperador;
    private String dataInicioPrm;
    private String dataTerminoPrm;
    private String horaInicio;
    private String horaTermino;
    private List listaConsultarOperador;
    private List<ReciboDevolucaoVO> listaDevolucoes;
    private boolean emProcessamento = false;
    private boolean pollEnabled = true;
    private Boolean somenteSintetico = false;
    private List<SelectItem> listaConvenioCobranca = new ArrayList<SelectItem>();
    private List<String> conveniosSelecionados = new ArrayList<String>();
    private List<String> conveniosSelecionadosOnline = new ArrayList<String>();
    private List<SelectItem> listaFonteDados = new ArrayList<SelectItem>();
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private boolean layoutLivroCaixa = false;
    private boolean layoutImpressaoTermica = false;
    private boolean layoutImpressaoTermicaResumido = false;
    private boolean considerarUsuarioRecorrencia = false;
    private boolean considerarUsuarioAdmin = false;

    public CaixaPorOperadorRelControleRel(Boolean semSessao) throws Exception {

    }
    public CaixaPorOperadorRelControleRel() throws Exception {
        inicializarDados();
    }

    public void inicializarDados() throws Exception {
        setCaixaPorOperadorRel(new CaixaPorOperadorRel());
        setDataInicioPrm("");
        setDataTerminoPrm("");
        setCampoConsultarOperador("");
        setValorConsultarOperador("");
        setListaConsultarOperador(new ArrayList());
        setUsuarioFiltro(new UsuarioVO());
        setFiltroEmpresa(0);
        montarListaSelectItemEmpresa();
        montarListaSelectItemConvenio();

    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
                getListaEmpresas().get(0).setLabel(LABEL_TODAS_EMPRESAS);
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarListaSelectItemConvenio() throws Exception {
        List<ConvenioCobrancaVO> convenioCobrancaVOs;
        if (UteisValidacao.emptyNumber(getFiltroEmpresa())) {
            convenioCobrancaVOs = getFacade().getConvenioCobranca().consultarTodosGeral(false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else {
            convenioCobrancaVOs = getFacade().getConvenioCobranca().consultarPorEmpresa(getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        setListaConvenioCobranca(new ArrayList<SelectItem>());
        getListaConvenioCobranca().add(new SelectItem(0, "SEM CONVÊNIO"));
        for (ConvenioCobrancaVO convenio : convenioCobrancaVOs) {
                getListaConvenioCobranca().add(new SelectItem(convenio.getCodigo(), convenio.getDescricao()));
        }
    }

    public boolean isApresentarConvenioCobranca() {
        return getListaConvenioCobranca().size() > 1;
    }

    // Se não houver a permissao para todos os dias e necessário q seja setado um valor diferente de 1 no valor padrão.
    public boolean isRelatorioTodosDias()throws Exception{ return permissao("LiberarTodosDiasRelatorioFechamentoCaixaOperador") || getEmpresaLogado().getDiasParaRetirarRelFechamentoDeCaixa() != 1 ; }

    public void imprimirPDF() {
        try {
            if(isLayoutLivroCaixa()) {
                notificarRecursoEmpresa(RecursoSistema.LAYOUT_LIVRO_CAIXA);
            }
            setMensagemDetalhada("", "");
            //valida se a data é maior que 7 dias e se o usuário possui permissao
            validarDatasPesquisa();
            //valida se o usuário pode ver relatórios de todos os colaboradores
            validarPermissaoVerRelatorioTodosUsuarios();
            setTipoRelatorio("PDF");
            imprimir();
            setErro(false);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void getImprimirHTML() throws Exception {
        try {
            setMensagemDetalhada("", "");
            //valida se a data é maior que 7 dias e se o usuário possui permissao
            validarDatasPesquisa();
            //valida se o usuário pode ver relatórios de todos os colaboradores
            validarPermissaoVerRelatorioTodosUsuarios();
            setTipoRelatorio("HTML");
            imprimir();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void imprimir() {
        getMapaCaminhoRelatorio().put(this.getClass().getName(), new ArrayList<String>());
        setPollEnabled(true);
        if (!isEmProcessamento()) {
            setEmProcessamento(true);
            try {
//                throw new Exception("Já existe uma solicitação sendo processada! Para evitar inconsistências é indicado sair do sistema ou aguardar por um minuto antes de realizar uma nova solicitação");
                List listaRegistro = obterDadosImpressao(false);
                if (!listaRegistro.isEmpty() || !listaDevolucoes.isEmpty()) {
                    setListaRelatorio(listaRegistro);
                } else {
                    throw new Exception("Não foi encontrado nenhum recibo no período informado.");
                }
                setRelatorio("sim");
                setMensagemDetalhada("", "");
                setMensagemID("msg_entre_prmrelatorio");
                imprimirRelatorio();
                setEmProcessamento(false);
                setPollEnabled(false);
            } catch (Exception e) {
                setRelatorio("nao");
                setEmProcessamento(false);
                setPollEnabled(false);
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
        }
    }

    public String getNomeArquivoRelatorioGeradoAgora() {
        String nomeArquivoRelatorioGeradoAgora;
        if (request().getAttribute("nomeArquivoRelatorioGeradoAgora") != null) {
            nomeArquivoRelatorioGeradoAgora = request().getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
        } else {
            nomeArquivoRelatorioGeradoAgora = "";
        }

        List<String> nomesGerados = getMapaCaminhoRelatorio().get(this.getClass().getName());
        if (nomesGerados == null) {
            nomesGerados = new ArrayList<String>();
        }
        nomesGerados.add(nomeArquivoRelatorioGeradoAgora);
        getMapaCaminhoRelatorio().put(this.getClass().getName(), nomesGerados);
        return nomeArquivoRelatorioGeradoAgora;
    }

    public String getNomeRefRelatorioGeradoAgora() {
        if(getErro())
            return "";
        List<String> nomesGerados = getMapaCaminhoRelatorio().get(this.getClass().getName());
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (nomesGerados != null) {
            for (String nomeGerado : nomesGerados) {
                if (!UteisValidacao.emptyString(nomeGerado)) {
                    hRef = nomeGerado;
                }
            }
        }

        if (!hRef.isEmpty()) {
            return "location.href=\"" + hRef + "\"";
        } else {
            return "";
        }
    }

    public List obterDadosImpressao(boolean prodTipoSessao) throws Exception {
        setListaRelatorio(new ArrayList());
        setDataInicioPrm(Uteis.getData(getCaixaPorOperadorRel().getDataInicio()));
        setDataTerminoPrm(Uteis.getData(getCaixaPorOperadorRel().getDataTermino()));
        caixaPorOperadorRel.setListaReciboPagamentoRelTOs(new ArrayList<ReciboPagamentoRelTO>());
        caixaPorOperadorRel.setNomeOperador(getUsuarioFiltro().getNome());
        caixaPorOperadorRel.setTotalizador(new ArrayList<TotalizadorFormasPagamento>());
        preparaHora();
        List listaRegistro = caixaPorOperadorRel.montarDadosReciboPagamentoRelVO(getUsuarioFiltro(), prodTipoSessao, getConveniosSelecionados(), getConveniosSelecionadosOnline(), getFiltroEmpresa(), isConsiderarUsuarioRecorrencia(), isConsiderarUsuarioAdmin());

        horaInicio =  StringUtils.isNotBlank(caixaPorOperadorRel.getHoraInicio()) ? caixaPorOperadorRel.getHoraInicio() : "00:00:00.000";
        horaTermino =  StringUtils.isNotBlank(caixaPorOperadorRel.getHoraTermino()) ? caixaPorOperadorRel.getHoraTermino() : "23:59:59.999";
        if (caixaPorOperadorRel.getFonteDados() == null
                || caixaPorOperadorRel.getFonteDados().equals(TipoFonteDadosDF.TODAS.getCodigo())
                || caixaPorOperadorRel.getFonteDados().equals(TipoFonteDadosDF.ZILLYON_WEB.getCodigo())) {

            if (caixaPorOperadorRel.getTipoComprador().equals("CLI") || caixaPorOperadorRel.getTipoComprador().equals("T")) {
                listaDevolucoes = getFacade().getReciboDevolucao().consultarCaixaPorOperador(getUsuarioFiltro(), caixaPorOperadorRel.getDataInicio(), caixaPorOperadorRel.getDataTermino(),
                        horaInicio, horaTermino , getFiltroEmpresa());
            } else {
                listaDevolucoes = new ArrayList<ReciboDevolucaoVO>();
            }
            Double valorDevolucoes = 0.0;
            Double valorDevolucoesRecebiveis = 0.0;
            int qtdDevolucoes = 0;
            int qtdDevolucoesRecebiveis = 0;
            TotalizadorFormasPagamento totalEdicaoSaida= new TotalizadorFormasPagamento();
            totalEdicaoSaida.setDescricao("CHEQUE(S) DEVOLVIDO(S)");
            totalEdicaoSaida.setOrdem(TotalizadorFormasPagamento.ORDEM_EDICOES_SAIDAS);
            
            for (ReciboDevolucaoVO recibo : listaDevolucoes) {
                if (recibo.getValorDevolvidoEmDinheiro() > 0 && !recibo.getLiberacaoDevolucao()) {
                    valorDevolucoes += Uteis.arredondarForcando2CasasDecimais(recibo.getValorDevolvidoEmDinheiro());
                    qtdDevolucoes++;
                }
                double recebiveis = recibo.getValorRecebiveis();
                if (recebiveis > 0.0) {
                    valorDevolucoesRecebiveis += recebiveis;
                    qtdDevolucoesRecebiveis++;
                    if(!UteisValidacao.emptyNumber(recibo.getReciboEditado())){
                        totalEdicaoSaida.setQtd(totalEdicaoSaida.getQtd() + recibo.getChequesDevolvidos().size());
	                totalEdicaoSaida.setValor(Uteis.arredondarForcando2CasasDecimais(totalEdicaoSaida.getValor() + recibo.getValorRecebiveis()));
                    }
                }
            }
            if( totalEdicaoSaida.getQtd() > 0){
                caixaPorOperadorRel.getTotalizador().add(totalEdicaoSaida);
            }
            TotalizadorFormasPagamento.inserirDevolucoes(caixaPorOperadorRel.getTotalizador(),
                    valorDevolucoes, qtdDevolucoes, valorDevolucoesRecebiveis, qtdDevolucoesRecebiveis);
        }else{
             listaDevolucoes = new ArrayList<ReciboDevolucaoVO>();
        }

        return listaRegistro;
    }

    private void preparaHora() {
        if (caixaPorOperadorRel.getHoraInicio() == null || caixaPorOperadorRel.getHoraInicio().trim().isEmpty()) {
            caixaPorOperadorRel.setHoraInicio("00:00:00.000");
        }
        if (caixaPorOperadorRel.getHoraTermino() == null || caixaPorOperadorRel.getHoraTermino().trim().isEmpty()) {
            caixaPorOperadorRel.setHoraTermino("23:59:59.999");
        }
    }

    public void imprimirRelatorio() {
        try {

            JRDataSource jr1 = new JRBeanArrayDataSource(listaDevolucoes.toArray(), false);
            Ordenacao.ordenarLista(caixaPorOperadorRel.getTotalizador(), "tipo");
            Ordenacao.ordenarLista(caixaPorOperadorRel.getTotalizador(), "ordem");

            JRDataSource totalizadoresEspecie = new JRBeanArrayDataSource(caixaPorOperadorRel.getTotalizador().toArray(), false);

            String nomeRelatorio = caixaPorOperadorRel.getIdEntidade();
            String titulo = "Fechamento de Caixa Por Operador";
            String design = "";
            String nomeBundle = "bundleRelatorios.resourceBundleRelatorios";
            ResourceBundle bundle = ResourceBundle.getBundle(nomeBundle, new Locale(getEmpresaLogado().getLocale().getLanguage(),getEmpresaLogado().getLocale().getCountry()));
            Locale locale = new Locale(getEmpresaLogado().getLocale().getLanguage(),getEmpresaLogado().getLocale().getCountry());
            design = caixaPorOperadorRel.getDesignIReportRelatorio(isLayoutLivroCaixa(), isLayoutImpressaoTermica(), isLayoutImpressaoTermicaResumido());

            for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                for (Object obj : recibo.getListaMovProdutoVOs()) {
                    MovProdutoVO movproduto = (MovProdutoVO) obj;
                    if (movproduto.getProduto().getTipoProduto().equals("PM") && movproduto.getMulta() == 0.0 && movproduto.getJuros() == 0.0) {
//                        ContratoVO contratoVO = getFacade().getContrato().consultarPorChavePrimaria(movproduto.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        ContratoVO contratoVO = getFacade().getContrato().consultarPorChavePrimaria(movproduto.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);

                        calcularConvDescontoMovProduto(recibo, movproduto, contratoVO);
                        calcularDescontoExtraMovProduto(recibo, movproduto);

//                        movproduto.setValorPagoMovProdutoParcela(contratoVO.getValorBaseCalculo());
                    }
                }
            }

            if(isLayoutLivroCaixa() || isLayoutImpressaoTermica() || isLayoutImpressaoTermicaResumido()) {
                List<CaixaPorOperadorLivroRel> listLivroRel = new ArrayList<CaixaPorOperadorLivroRel>();
                Map<Integer, CaixaPorOperadorDetalheSaldoLivroRel> mapDetalhesSaldo = new HashMap<Integer, CaixaPorOperadorDetalheSaldoLivroRel>();

                for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                    CaixaPorOperadorLivroRel livroRel = new CaixaPorOperadorLivroRel();
                    if(StringUtils.isNotBlank(recibo.getMatricula())){
                        livroRel.setMatricula(recibo.getMatricula());
                    }else{
                        livroRel.setMatricula("CO");
                    }
                    livroRel.setNomeUsuarioOperacao(recibo.getReciboPagamentoVO().getResponsavelLancamento().getNomeAbreviado());
                    livroRel.setNomePagador(recibo.getReciboPagamentoVO().getNomePessoaPagador());
                    livroRel.setDataLancamento(Calendario.getDataComHoraZerada(recibo.getReciboPagamentoVO().getData()));

                    for (Object obj : recibo.getListaMovPagamentoVOs()) {
                        MovPagamentoVO mp = (MovPagamentoVO) obj;
                        //Não entrar pagamentos que foram feitos com credito da conta corrente
                        // pois este lançamento já entrou em outra ocasião, neste modelo não existe os "Outros detalhes" para mostrar que é de CCC.
                        if(mp.getCreditoApresentar().equals(" (CCC)")){
                            continue;
                        }

                        CaixaPorOperadorLivroRel livroRelPags = new CaixaPorOperadorLivroRel();
                        CaixaPorOperadorDetalheSaldoLivroRel detalhesSaldo = mapDetalhesSaldo.get(mp.getFormaPagamento().getCodigo());

                        if (detalhesSaldo == null) {
                            detalhesSaldo = new CaixaPorOperadorDetalheSaldoLivroRel();
                            detalhesSaldo.setCodFormaPagamento(mp.getFormaPagamento().getCodigo());
                            detalhesSaldo.setFormaPagamento(mp.getFormaPagamento().getDescricao());
                            mapDetalhesSaldo.put(mp.getFormaPagamento().getCodigo(), detalhesSaldo);
                        }
                        detalhesSaldo.adicionarValor(mp.getValorTotal());

                        livroRelPags.setFormaPagamentoDescricao(mp.getFormaPagamento().getDescricao());
                        livroRelPags.setValorTotalPagamento(mp.getValorTotal());
                        livroRelPags.setDataLancamento(livroRel.getDataLancamento());
                        livroRelPags.setMatricula(livroRel.getMatricula());
                        livroRelPags.setNomePagador(livroRel.getNomePagador());
                        livroRelPags.setValorTotalSaida(0.0);
                        livroRelPags.setNomeUsuarioOperacao(livroRel.getNomeUsuarioOperacao());
                        listLivroRel.add(livroRelPags);
                    }

                }

                for (ReciboDevolucaoVO objDevolucao : listaDevolucoes) {
                    CaixaPorOperadorLivroRel livroRelDevolucoes = new CaixaPorOperadorLivroRel();
                    if (objDevolucao.getContrato() == null || objDevolucao.getContrato().getCodigo() == 0) {
                        // Alterado para não exibir o código da pessoa, pois isso pode confundir os clientes ao ver o código da pessoa em um campo chamado "Matricula"
                        livroRelDevolucoes.setMatricula("--");
                        livroRelDevolucoes.setNomePagador(objDevolucao.getProdutoVO().getPessoa().getNome());
                    } else {
                        // Se tem contrato logo tem cliente. Então exibir a matrícula do cliente corretamente, ao invés de exibir o código da pessoa
                        ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(objDevolucao.getContrato().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        livroRelDevolucoes.setMatricula(clienteVO.getMatricula());
                        livroRelDevolucoes.setNomePagador(objDevolucao.getContrato().getPessoa().getNome());
                    }

                    if (objDevolucao.getValorDevolvidoEmDinheiro() > 0 && objDevolucao.getValorRecebiveis() > 0.0) {
                        livroRelDevolucoes.setFormaPagamentoDescricao("DEV. DINHEIRO/RECEBÍ.");
                    } else if (objDevolucao.getValorRecebiveis() > 0.0) {
                        livroRelDevolucoes.setFormaPagamentoDescricao("DEV. RECEBÍVEIS");
                    } else {
                        livroRelDevolucoes.setFormaPagamentoDescricao("DEV. DINHEIRO");
                    }

                    livroRelDevolucoes.setDataLancamento(Calendario.getDataComHoraZerada(objDevolucao.getDataDevolucao()));
                    livroRelDevolucoes.setValorTotalPagamento(0.0);
                    livroRelDevolucoes.setValorTotalSaida(objDevolucao.getValorDevolucao());
                    livroRelDevolucoes.setNomeUsuarioOperacao(objDevolucao.getResponsavelDevolucao().getNomeAbreviado());



                    listLivroRel.add(livroRelDevolucoes);

                }
                montarFiltros(bundle);
                Map<String, Object> params = new HashMap<String, Object>();

                String tituloSufixo = "";
                if(!isLayoutImpressaoTermica() && isLayoutLivroCaixa()) {
                    tituloSufixo = " - Layout Livro";
                }

                params.put(JRParameter.REPORT_LOCALE, locale);
                params.put(JRParameter.REPORT_RESOURCE_BUNDLE, bundle);
                params.put("nomeRelatorio", nomeRelatorio);
                params.put("tituloRelatorio", titulo + tituloSufixo);
                params.put("nomeEmpresa", getNomeEmpresaSelecionada(getFiltroEmpresa()));
                params.put("imagemLogo", "");
                params.put("mensagemRel", "");
                params.put("tipoRelatorio", getTipoRelatorio());
                params.put("caminhoParserXML", "/" + caixaPorOperadorRel.getIdEntidade() + "/registros");
                params.put("nomeDesignIReport", design);
                params.put("usuario", getUsuarioLogado().getNome());
                if (isLayoutImpressaoTermicaResumido()){
                    params.put("listaObjetos", agruparListaUsuarioOperacaoFormaPagamento(Ordenacao.ordenarLista(listLivroRel, "nomeUsuarioOperacao")));
                } else {
                    params.put("listaObjetos", Ordenacao.ordenarLista(listLivroRel, "dataLancamento"));
                }
                JRDataSource jrDetalheSaldo = new JRBeanArrayDataSource(mapDetalhesSaldo.values().toArray(), false);
                params.put("detalheSaldo", jrDetalheSaldo);
                if (caixaPorOperadorRel.getDescricaoFiltros().equals("")) {
                    caixaPorOperadorRel.setDescricaoFiltros("nenhum");
                }
                params.put("filtros", caixaPorOperadorRel.getDescricaoFiltros());
                params.put("dataIni", getDataInicioPrm());
                params.put("dataFim", getDataTerminoPrm());
                params.put("SUBREPORT_DIR", CaixaPorOperadorRel.getCaminhoSubRelatorio());
                params.put("SUBREPORT_DIR1", CaixaPorOperadorRel.getCaminhoSubRelatorio());
                params.put("SUBREPORT_DIR2", CaixaPorOperadorRel.getCaminhoSubRelatorio());
                params.put("totalizadores", totalizadoresEspecie);
                params.put("somenteSintetico", getSomenteSintetico());
                if (listLivroRel == null) {
                    params.put("apresentarDados", false);
                } else {
                    params.put("apresentarDados", !listLivroRel.isEmpty());
                }
                params.put("moeda", getEmpresaLogado().getMoeda());

                apresentarRelatorioObjetos(params);

            }else {
                montarFiltros(bundle);
                apresentarRelatorioObjetos(nomeRelatorio, locale, bundle,  getNomeEmpresaSelecionada(getFiltroEmpresa()), "", "", getTipoRelatorio(),
                        "/" + caixaPorOperadorRel.getIdEntidade() + "/registros", design,
                        getUsuarioLogado().getNome(), caixaPorOperadorRel.getDescricaoFiltros(),
                        getDataInicioPrm(), getDataTerminoPrm(),
                        String.valueOf(caixaPorOperadorRel.getQtdPagamentoAV()),
                        String.valueOf(caixaPorOperadorRel.getQtdPagamentoCA()),
                        String.valueOf(caixaPorOperadorRel.getQtdPagamentoCD()),
                        String.valueOf(caixaPorOperadorRel.getQtdPagamentoBB()),
                        String.valueOf(caixaPorOperadorRel.getQtdPagamentoChAvista()),
                        String.valueOf(caixaPorOperadorRel.getQtdPagamentoChPrazo()),
                        String.valueOf(caixaPorOperadorRel.getQtdPagamentoOutros()),
                        caixaPorOperadorRel.getValorPagamentoAV(),
                        caixaPorOperadorRel.getValorPagamentoCA(),
                        caixaPorOperadorRel.getValorPagamentoCD(),
                        caixaPorOperadorRel.getValorPagamentoBB(),
                        caixaPorOperadorRel.getValorPagamentoChAvista(),
                        caixaPorOperadorRel.getValorPagamentoChPrazo(),
                        caixaPorOperadorRel.getValorPagamentoOutros(),
                        CaixaPorOperadorRel.getCaminhoSubRelatorio(),
                        CaixaPorOperadorRel.getCaminhoSubRelatorio(),
                        CaixaPorOperadorRel.getCaminhoSubRelatorio(),
                        getListaRelatorio(),
                        String.valueOf(caixaPorOperadorRel.getQtdDevolucoes()),
                        caixaPorOperadorRel.getValorDevoluces(),
                        String.valueOf(caixaPorOperadorRel.getQtdDevolucoesRecebiveis()),
                        caixaPorOperadorRel.getValorDevolucesRecebiveis(),
                        jr1, totalizadoresEspecie, getSomenteSintetico(),
                        getEmpresaLogado().getMoeda());
            }
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private List agruparListaUsuarioOperacaoFormaPagamento(List<CaixaPorOperadorLivroRel> listaTotal){
        List<CaixaPorOperadorLivroRel> listaAgrupada = new ArrayList<>();
        Iterator<CaixaPorOperadorLivroRel> iterator = listaTotal.iterator();

        while (iterator.hasNext()){
            CaixaPorOperadorLivroRel obj = iterator.next();
            if (listaAgrupada.isEmpty()){
                listaAgrupada.add(obj);
            } else {
                boolean localizouItem = false;
                for (CaixaPorOperadorLivroRel item : listaAgrupada){
                    if (obj.getFormaPagamentoDescricao().equalsIgnoreCase(item.getFormaPagamentoDescricao())){
                        if (obj.getNomeUsuarioOperacao().equalsIgnoreCase(item.getNomeUsuarioOperacao())){
                            item.setValorTotalPagamento(item.getValorTotalPagamento() + obj.getValorTotalPagamento());
                            localizouItem = true;
                        }
                    }
                }
                if (!localizouItem){
                    listaAgrupada.add(obj);
                }
            }
        }
        return listaAgrupada;
    }

    /**
     * Monta os filtros mostrados no relatório
     */
    private void montarFiltros(ResourceBundle bundle) {
        String filtros = "";
        if (!caixaPorOperadorRel.getHoraInicio().trim().isEmpty() && !caixaPorOperadorRel.getHoraTermino().trim().isEmpty()) {
            filtros += bundle.getString("Horario") + " " + caixaPorOperadorRel.getHoraInicio() + " " + bundle.getString("Ate") + " " + caixaPorOperadorRel.getHoraTermino() + "  ";
        }
        if (!caixaPorOperadorRel.getNomeOperador().trim().isEmpty()) {
            filtros += bundle.getString("Operador") + " " + getUsuarioFiltro().getNome().trim().toUpperCase() + "  ";
        }
        if (caixaPorOperadorRel.getTipoComprador().equals("T")) {
            filtros += bundle.getString("Tipo_Comp_Todos") + " ";
        } else if (caixaPorOperadorRel.getTipoComprador().equals("CLI")) {
            filtros += bundle.getString("Tipo_Comp_Cliente") + " ";
        } else if (caixaPorOperadorRel.getTipoComprador().equals("COL")) {
            filtros += bundle.getString("Tipo_Comp_Colaborador") + " ";
        } else if (caixaPorOperadorRel.getTipoComprador().equals("CN")) {
            filtros += bundle.getString("Tipo_Comp_Consumidor") + " ";
        }
        if(caixaPorOperadorRel.getDataInicioFaturamento() != null || caixaPorOperadorRel.getDataTerminoFaturamento() != null){
            String descricaoFaturamento = "";
            if(caixaPorOperadorRel.getDataInicioFaturamento() != null && caixaPorOperadorRel.getDataTerminoFaturamento() == null){
                descricaoFaturamento =   " " + bundle.getString("MaiorQue") +" " +  Uteis.getData(caixaPorOperadorRel.getDataInicioFaturamento())+ "  ";
            } else if(caixaPorOperadorRel.getDataInicioFaturamento() == null && caixaPorOperadorRel.getDataTerminoFaturamento() != null){
                descricaoFaturamento = " " + bundle.getString("MenorQue")  +" " +Uteis.getData(caixaPorOperadorRel.getDataTerminoFaturamento())+ "  ";
            } else {
                descricaoFaturamento = " "+Uteis.getData(caixaPorOperadorRel.getDataInicioFaturamento())  +" " + bundle.getString("Ate") + " "+Uteis.getData(caixaPorOperadorRel.getDataTerminoFaturamento())+ "  ";
            }
            filtros += bundle.getString("FaturamentoProduto") +":"+ descricaoFaturamento;
        }
        caixaPorOperadorRel.setDescricaoFiltros(filtros);

    }

    /**
     * Valida a permissão do usuário em pesquisar o fechamento de caixa por operador
     * dentro de um período maior que 7 dias
     */
    public void validarDatasPesquisa() throws Exception {
        if(!isRelatorioTodosDias()
                && ( caixaPorOperadorRel.getDataInicio().compareTo(Calendario.hoje())==-1
                || caixaPorOperadorRel.getDataTermino().compareTo(Calendario.hoje())==-1 )){
            return;
        }
        long diasIncial = Uteis.nrDiasEntreDatas(caixaPorOperadorRel.getDataInicio(), Calendario.hoje());
        long diasTermino = Uteis.nrDiasEntreDatas(caixaPorOperadorRel.getDataTermino(), Calendario.hoje());

        if (diasIncial < getEmpresaLogado().getDiasParaRetirarRelFechamentoDeCaixa() && diasTermino < getEmpresaLogado().getDiasParaRetirarRelFechamentoDeCaixa())
        {//se o usuário escolher um intervalo de datas para pesquisa menor ou igual o sistema permite mostrar o relatório
            return;
        } else {//faz validação para permissão do usuário (LIBERAR RELATÓRIO DE FECHAMENTO DE CAIXA POR OPERADOR - TODOS OS DIAS)quando o intervalo entre as datas é maior que 7 dias
            validarPermissaoUsuarioVerRelatorioTodosDias();
        }
    }

    /**
     * Valida se precisa de permissão do usuário logado para ver o relatório de fechamento de caixa por operador de todos os colaboradores
     * ou colaboradores diferentes do usuário logado
     *
     * @throws Exception
     */
    public void validarPermissaoVerRelatorioTodosUsuarios() throws Exception {
        //valida se o usuário possui permissão para ver relatórios do colaborador escolhido quando ele escolhe um colaborador
        if (getUsuarioFiltro().getCodigo() != 0) {
            try {
                getUsuarioLogado().setColaboradorVO(getFacade().getColaborador().consultarPorCodigoUsuario(getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            } catch (Exception e) {
                getUsuarioLogado().setColaboradorVO(new ColaboradorVO());
            }
            if (getUsuarioFiltro().getCodigo().equals(getUsuarioLogado().getCodigo())) {
                return;
            } else {//se o usuário pesquisar o caixa de um colaborador que não seja ele mesmo
                validarPermissaoUsuarioVerRelatorioTodosColaboradores();
            }
        } else { //quando não é escolhido nenhum colaborador para pesquisa
            validarPermissaoUsuarioVerRelatorioTodosColaboradores();
        }
    }

      /**
     * Valida a permissão do usuário para ver o relatório de fechamento de caixa por operador
     * que usa a permissão LIBERAR RELATÓRIO DE FECHAMENTO DE CAIXA POR OPERADOR - TODOS OS DIAS
     * quando o intervalo entre as datas é maior que 7 dias
     *
     * @throws Exception
     */
    public void validarPermissaoUsuarioVerRelatorioTodosDias() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("", "");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        permissaoFuncionalidade(getUsuarioLogado(),"LiberarTodosDiasRelatorioFechamentoCaixaOperador",  "6.17 - Visualizar fechamento de caixa por operador sem limite padrão (definido nas configurações da empresa) para a pesquisa");
        setMensagemDetalhada("", "");
    }

    /**
     * Valida a permissão do usuário logado para ver o relatório de fechamento de caixa por operador
     * que usa a permissão LIBERAR RELATÓRIO DE FECHAMENTO DE CAIXA POR OPERADOR - TODOS OS COLABORADORES
     *
     * @throws Exception
     */
    public void validarPermissaoUsuarioVerRelatorioTodosColaboradores() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("", "");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        permissaoFuncionalidade(getUsuarioLogado(),"LiberarTodosColaboradoresRelatorioFechamentoCaixaOperador", "6.18 - Liberar Relatório de Fechamento de Caixa por Operador - Todos os colaboradores");
    }

    public List<UsuarioVO> executarAutocompleteFuncionalidade(Object suggest) {

        List<UsuarioVO> usuarios = new ArrayList<UsuarioVO>();

        try {
            String pref = (String) suggest;

            //Busca Funcionalidades pelo nome
            if (suggest.equals("%")) {
                usuarios = getFacade().getUsuario().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            } else {
                usuarios = getFacade().getUsuario().consultarPorNome(pref, false, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            }
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return usuarios;
    }
    /**
     * Rotina responsavel por executar as consultas disponiveis para Classe Colaborador.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public void consultarOperador() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarOperador().equals("nome")) {
                objs = getFacade().getUsuario().consultarPorNome(getValorConsultarOperador(), false, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
            }
            setListaConsultarOperador(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarOperador(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarOperador() {
        UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("colaborador");
        setUsuarioFiltro(obj);
        setConsiderarUsuarioAdmin(false);
        setConsiderarUsuarioRecorrencia(false);
    }

    public void limparCampoOperador() {
        setUsuarioFiltro(new UsuarioVO());
    }

    public boolean isExibirCheckBoxUsuarioRecorAdmin(){
        if (getUsuarioFiltro() == null || UteisValidacao.emptyNumber(getUsuarioFiltro().getCodigo())) {
            return true;
        }
        return false;
    }

    public void limparCampoFaturamentoPeriodo() {
        getCaixaPorOperadorRel().setDataInicioFaturamento(null);
        getCaixaPorOperadorRel().setDataTerminoFaturamento(null);
    }

    public List<SelectItem> getListaSelectItemOrdenacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        if(isLayoutLivroCaixa()) {
            objs.add(new SelectItem("PF", "Padrão Financeiro"));
            objs.add(new SelectItem("NA", "Nome"));
        }else{
            objs.add(new SelectItem("NR", "Número do Recibo"));
            objs.add(new SelectItem("NA", "Nome do Aluno"));
            objs.add(new SelectItem("NC", "Nome do Colaborador"));
        }
        return objs;
    }

    public List<SelectItem> getTipoConsultaComboOperador() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }

    public CaixaPorOperadorRel getCaixaPorOperadorRel() {
        return caixaPorOperadorRel;
    }

    public void setCaixaPorOperadorRel(CaixaPorOperadorRel caixaPorOperadorRel) {
        this.caixaPorOperadorRel = caixaPorOperadorRel;
    }

    public String getCampoConsultarOperador() {
        return campoConsultarOperador;
    }

    public void setCampoConsultarOperador(String campoConsultarOperador) {
        this.campoConsultarOperador = campoConsultarOperador;
    }

    public List getListaConsultarOperador() {
        return listaConsultarOperador;
    }

    public void setListaConsultarOperador(List listaConsultarOperador) {
        this.listaConsultarOperador = listaConsultarOperador;
    }

    public String getValorConsultarOperador() {
        return valorConsultarOperador;
    }

    public void setValorConsultarOperador(String valorConsultarOperador) {
        this.valorConsultarOperador = valorConsultarOperador;
    }

    public String getDataInicioPrm() {
        return dataInicioPrm;
    }

    public void setDataInicioPrm(String dataInicioPrm) {
        this.dataInicioPrm = dataInicioPrm;
    }

    public String getDataTerminoPrm() {
        return dataTerminoPrm;
    }

    public void setDataTerminoPrm(String dataTerminoPrm) {
        this.dataTerminoPrm = dataTerminoPrm;
    }

    public void novo() throws Exception {
        inicializarDados();
    }

    public List<SelectItem> getListaSelectItemTipoComprador() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("T", "Todos"));
        objs.add(new SelectItem("CLI", "Cliente"));
        objs.add(new SelectItem("COL", "Colaborador"));
        objs.add(new SelectItem("CN", "Consumidor"));
        return objs;
    }

    public boolean isEmProcessamento() {
        return emProcessamento;
    }

    public void setEmProcessamento(boolean emProcessamento) {
        this.emProcessamento = emProcessamento;
    }

    public boolean isPollEnabled() {
        return pollEnabled;
    }

    public void setPollEnabled(boolean pollEnable) {
        this.pollEnabled = pollEnable;
    }

    public Boolean getSomenteSintetico() {
        return somenteSintetico;
    }

    public void setSomenteSintetico(Boolean somenteSintetico) {
        this.somenteSintetico = somenteSintetico;
    }

    public List<SelectItem> getListaConvenioCobranca() {
        return listaConvenioCobranca;
    }

    public void setListaConvenioCobranca(List<SelectItem> listaConvenioCobranca) {
        this.listaConvenioCobranca = listaConvenioCobranca;
    }

    public List<String> getConveniosSelecionados() {
        return conveniosSelecionados;
    }

    public void setConveniosSelecionados(List<String> conveniosSelecionados) {
        this.conveniosSelecionados = conveniosSelecionados;
    }

    public List<SelectItem> getListaFonteDados() {
        if(listaFonteDados.isEmpty()){
           caixaPorOperadorRel.setFonteDados(TipoFonteDadosDF.TODAS.getCodigo());
           for(TipoFonteDadosDF tipo : TipoFonteDadosDF.values()){
               if(!tipo.equals(TipoFonteDadosDF.FINANCEIRO)){
                  listaFonteDados.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
               }
           }
        }
        return listaFonteDados;
    }

    public List<String> getConveniosSelecionadosOnline() {
        return conveniosSelecionadosOnline;
    }

    public void setConveniosSelecionadosOnline(List<String> conveniosSelecionadosOnline) {
        this.conveniosSelecionadosOnline = conveniosSelecionadosOnline;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public boolean isLayoutLivroCaixa() {
        return layoutLivroCaixa;
    }

    public void setLayoutLivroCaixa(boolean layoutLivroCaixa) {
        this.layoutLivroCaixa = layoutLivroCaixa;
    }

    public UsuarioVO getUsuarioFiltro() {
        if (usuarioFiltro == null) {
            usuarioFiltro = new UsuarioVO();
        }
        return usuarioFiltro;
    }

    public void setUsuarioFiltro(UsuarioVO usuarioFiltro) {
        this.usuarioFiltro = usuarioFiltro;
    }

    public boolean isConsiderarUsuarioRecorrencia() {
        return considerarUsuarioRecorrencia;
    }

    public void setConsiderarUsuarioRecorrencia(boolean considerarUsuarioRecorrencia) {
        this.considerarUsuarioRecorrencia = considerarUsuarioRecorrencia;
    }

    public boolean isConsiderarUsuarioAdmin() {
        return considerarUsuarioAdmin;
    }

    public void setConsiderarUsuarioAdmin(boolean considerarUsuarioAdmin) {
        this.considerarUsuarioAdmin = considerarUsuarioAdmin;
    }

    public boolean isLayoutImpressaoTermica() {
        return layoutImpressaoTermica;
    }

    public void setLayoutImpressaoTermica(boolean layoutImpressaoTermica) {
        this.layoutImpressaoTermica = layoutImpressaoTermica;
    }

    public boolean isLayoutImpressaoTermicaResumido() {
        return layoutImpressaoTermicaResumido;
    }

    public void setLayoutImpressaoTermicaResumido(boolean layoutImpressaoTermicaResumido) {
        this.layoutImpressaoTermicaResumido = layoutImpressaoTermicaResumido;
    }
}
