package relatorio.controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Colaborador;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.ParcelaEmAbertoRel;
import relatorio.negocio.jdbc.financeiro.ParcelaEmAbertoRelTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.List;
import java.util.stream.Collectors;

public class ParcelaEmAbertoControleRel extends SuperControleRelatorio {

    private int RELATORIO_RETRATO = 0;
    private int RELATORIO_PAISAGEM = 1;
    private List<SelectItem> listaSelectItemTipoSituacao;
    private List<SelectItem> listaSelectItemTipoSituacaoPeriodo;
    private List<SelectItem> listaSelectItemParcelaCancelada;
    private ParcelaEmAbertoRel parcelaEmAbertoRel;
    private String campoConsultarResponsavel;
    private String valorConsultarResponsavel;
    private List listaConsultarResponsavel;
    private String campoConsultarCliente;
    private String valorConsultarCliente;
    private List listaConsultarCliente;
    private String dataInicioPrm;
    private String dataTerminoPrm;
    private int tipoRelatorioSelecionado;
    private List<SelectItem> convenios = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemPlano;
    private List<SelectItem> listaSelectItemHorariosPlano;
    private List<SelectItem> listaTipoRelatorio;
    private String abrirRelatorio;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private boolean gerarMultasJuros = false;
    private List<ParcelaEmAbertoRelTO> listaParcelas;
    private boolean mostrarMenuFiltro = false;

    private double valorTotalPago = 0.0;
    private double valorTotalEmAberto = 0.0;
    private double valorTotalCancelado = 0.0;
    private List<String> alunosEmAberto = new ArrayList<>();
    private List<String> alunosPago = new ArrayList<>();
    private List<String> alunosCancelado = new ArrayList<>();
    private int qtdeParcelasEmAberto = 0;
    private int qtdeParcelasPago = 0;
    private int qtdeParcelasCancelado = 0;
    private int totalAlunos = 0;
    private boolean mostrarNrTentativasMovitoRetorno = false;
    private List<SelectItem> selectItemsFormaDePagamento = new ArrayList<SelectItem>();

    private boolean apresentarFormaPagamento = false;
    private boolean aprensentarDadoSensiveisCliente = false;

    private List<SelectItem> listaSelectItemModalidade;
    private List<SelectItem> listaSelectItemTurma;

    public ParcelaEmAbertoControleRel() throws Exception {
        novo();
    }

    public void consultaPredefinida() {
        try {
            limparMsg();
            novo();
            Integer codigoEmpresa = (Integer) getSession().getAttribute("BiInad_codigoEmpresa");
            Date dataInicio = (Date) getSession().getAttribute("BiInad_dataInicio");
            Date dataFim = (Date) getSession().getAttribute("BiInad_dataFim");
            String situacoesParcela = (String) getSession().getAttribute("BiInad_situacoesParcela");

            if (codigoEmpresa != null) {

                setFiltroEmpresa(codigoEmpresa);
                getParcelaEmAbertoRel().setDataInicioVencimento(dataInicio);
                getParcelaEmAbertoRel().setDataTerminoVencimento(dataFim);
                getParcelaEmAbertoRel().setPeriodo("vencimento");
                getParcelaEmAbertoRel().setSituacoesSelecionadas(null);

                SelectItem obj;
                if (UteisValidacao.emptyString(situacoesParcela)) {
                    situacoesParcela = "EA,PG,CA";
                    getParcelaEmAbertoRel().setParcelaCancelada("CANCELADA_APOS_VENCIMENTO");
                }
                if (situacoesParcela.contains("CA")) {
                    getParcelaEmAbertoRel().setParcelaCancelada("CANCELADA_APOS_VENCIMENTO");
                }
                String labelSituacao = "";
                for (String situacao : situacoesParcela.split(",")) {
                    switch (situacao) {
                        case "CA":
                            labelSituacao = "Cancelado";
                            break;
                        case "PG":
                            labelSituacao = "Pago";
                            break;
                        case "EA":
                            labelSituacao = "Em Aberto";
                            break;
                    }
                    obj = new SelectItem(situacao, labelSituacao);
                    getParcelaEmAbertoRel().getSituacoesSelecionadas().add(obj);
                }
                consultarParcelas_();

                getSession().setAttribute("BiInad_codigoEmpresa", null);
                getSession().setAttribute("BiInad_dataInicio", new Date());
                getSession().setAttribute("BiInad_dataFim", new Date());
                getSession().setAttribute("BiInad_situacoesParcela", "");
            }
        } catch (Exception e) {
            montarErro("Erro ao carregar as informações, verifique no suporte!");
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemSituacao() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        objs.add(new SelectItem("PG", "Pago"));
        objs.add(new SelectItem("EA", "Em Aberto"));
        objs.add(new SelectItem("CA", "Cancelado"));
        setListaSelectItemTipoSituacao(objs);
    }

    public final void carregarPlanoseConvenios() throws Exception{
        carregarConvenios();
        montarListaSelectItemPlano();
    }

    public final void carregarConvenios() throws Exception {
        List<ConvenioCobrancaVO> lista = new ArrayList<ConvenioCobrancaVO>();
        if (!UteisValidacao.emptyNumber(getFiltroEmpresa())) {
            lista = getFacade().getConvenioCobranca().consultarPorEmpresa(getFiltroEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else {
            lista = getFacade().getConvenioCobranca().consultarTodosGeral(false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        lista = Ordenacao.ordenarLista(lista, "descricao");
        convenios = JSFUtilities.getSelectItemListFrom(lista, "descricao", "codigo", false, true);
        convenios.add(new SelectItem(-2, "Transação ON-LINE"));
        convenios.add(new SelectItem(-1, "(Nenhum)"));
    }

    /*
   * Método responsável por inicializar List<SelectItem> de valores do
   * ComboBox correspondente ao atributo <code>situacao</code>
   */
    public List getListaSelectItemSituacaoCliente() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", ""));
        Hashtable situacaoClientes = (Hashtable) Dominios.getSituacaoCliente();
        Enumeration keys = situacaoClientes.keys();

        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoClientes.get(value);
            objs.add(new SelectItem(value, label));

        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);

        return objs;

    }

    public void montarListaSelectItemSituacaoPeriodo() {
        List<SelectItem> obj = new ArrayList<SelectItem>();
        obj.add(new SelectItem("vencimento", "Data de Vencimento"));
        obj.add(new SelectItem("registro", "Data de Faturamento"));
        obj.add(new SelectItem("pagamento", "Data de Pagamento"));
        setListaSelectItemTipoSituacaoPeriodo(obj);
    }
    public void montarListaSelectItemParcelasCanceladas() {
        List<SelectItem> obj = new ArrayList<SelectItem>();
        obj.add(new SelectItem("", "Todas"));
        obj.add(new SelectItem("CANCELADA_APOS_VENCIMENTO", "Após o vencimento"));
        obj.add(new SelectItem("CANCELADA_ANTES_VENCIMENTO", "Antes do Vencimento"));
        setListaSelectItemParcelaCancelada(obj);
    }

    public List<SelectItem> getListaSelectItemRecorrencia() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        // parcelas de recorrência e normais
        objs.add(new SelectItem(1, ""));
        //somente parcelas de recorrência
        objs.add(new SelectItem(2, "Sim"));
        //somente parcelas normais
        objs.add(new SelectItem(3, "Não"));
        return objs;
    }
    public void montaListaTipoRelatorio(){
        getListaTipoRelatorio().clear();
        getListaTipoRelatorio().add(new SelectItem(0,"Retrato"));
        getListaTipoRelatorio().add(new SelectItem(1,"Paisagem"));
    }
    public void inicializarDados() throws Exception {
        setParcelaEmAbertoRel(new ParcelaEmAbertoRel());
        setCampoConsultarResponsavel("");
        setValorConsultarResponsavel("");
        setListaConsultarResponsavel(new ArrayList());
        setCampoConsultarCliente("");
        setValorConsultarCliente("");
        setTipoRelatorioSelecionado(0);
        setListaConsultarCliente(new ArrayList());
        setListaParcelas(new ArrayList<>());
        valorTotalPago = 0.0;
        valorTotalEmAberto = 0.0;
        valorTotalCancelado = 0.0;
        alunosEmAberto = new ArrayList<>();
        alunosPago = new ArrayList<>();
        alunosCancelado = new ArrayList<>();
        qtdeParcelasEmAberto = 0;
        qtdeParcelasPago = 0;
        qtdeParcelasCancelado = 0;
        totalAlunos = 0;
        mostrarNrTentativasMovitoRetorno = false;
        gerarMultasJuros = false;
        mostrarMenuFiltro = false;
        apresentarFormaPagamento = false;
        aprensentarDadoSensiveisCliente = getFacade().getControleAcesso().verificarPermissaoFuncionalidade("RelParcelasDadosClientes");

    }

    public void imprimirHorizontal() {
        try {
            parcelaEmAbertoRel.setIdRelatorio("ParcelaEmAbertoRel-Paisagem");
            imprimirPDF();
            if(getErro()){
               throw new Exception(this.getMensagemDetalhada());
            }
            prepararAbrirRelatorio(RELATORIO_PAISAGEM);
        } catch (Exception e) {
            montarErro( e);
            abrirRelatorio = getMensagemNotificar();
        }

    }

    public void imprimirVertical() {
        try {
            parcelaEmAbertoRel.setIdRelatorio("ParcelaEmAbertoRel");
            imprimirPDF();
            if(getErro()){
                throw new Exception(this.getMensagemDetalhada());
            }
            prepararAbrirRelatorio(RELATORIO_PAISAGEM);
        } catch (Exception e) {
            montarErro( e);
            abrirRelatorio = getMensagemNotificar();
        }
    }

    public String getAtributos() {
        String colunasJurosMulta = isGerarMultasJuros() ? "juros=Juros,multas=Multa," : "";
        String colunasNrTentativasMotivoRetorno = isMostrarNrTentativasMovitoRetorno() ? "nrTentativas=Nr. Tentativas,motivoRetorno=Motivo Retorno," : "";
        String colunasInformacoesPagamento = isApresentarFormaPagamento() ? "dataPagamento=Dt.Pagamento,formasPagamento=Forma," : "";
        String colunasDadosSensiveis = parcelaEmAbertoRel.isApresentarDadosSensiveis() ? "cpf=CPF,endereco=Endereço,email=Email," : "";
        return (
                "nomeempresa=Empresa,matricula=Matrícula,nome=Nome,telefone=Telefones,contrato=Nr.Contrato,descricaoParcela=Desc.Parcela,parcela=Cod.Parcela,situacao_Apresentar=Situação,valor=Valor,dataCancelamento=Dt.Cancelamento,"
                        + colunasJurosMulta + "recorrencia_Apresentar=Recorrência,"
                        + colunasNrTentativasMotivoRetorno
                        + "dataFatura=Dt.Faturamento,dateVencimento=Dt.Vencimento," + colunasInformacoesPagamento + "nome_plano=Plano,"
                        + "convenioCobranca=Convênios,"
                        + colunasDadosSensiveis
                        + "modalidades=Modalidades,"
                        + "turma=Turma"
        );
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {
            limparMsg();
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getManagedBean("ExportadorListaControle");
            List<ParcelaEmAbertoRelTO> parcelasAberto = parcelasEmAbertas();
            if (!UteisValidacao.emptyList(parcelasAberto)) {
                exportadorListaControle.exportar(evt, parcelasAberto, "", ItemExportacaoEnum.REL_PARCELAS);
                if(exportadorListaControle.getErro()){
                    throw new Exception(exportadorListaControle.getMensagemDetalhada());
                }
            }
            prepararAbrirRelatorio(RELATORIO_RETRATO);
        } catch (Exception e){
            montarErro(e);
            abrirRelatorio = getMensagemNotificar();
        }
    }
    public void prepararAbrirRelatorio(int tipo){
        if(tipo == RELATORIO_RETRATO){
            ExportadorListaControle exp = (ExportadorListaControle)getControlador("ExportadorListaControle");
            if(UteisValidacao.emptyString(exp.getFileName())) {
                montarErro("Não foi encontrado resultados para o relatório!");
                abrirRelatorio = getMensagemNotificar();
            }else{
                abrirRelatorio = "location.href=\"../UpdateServlet?op=downloadfile&file="+exp.getFileName()+"&mimetype=application/vnd.ms-excel\"";
                abrirRelatorio += exp.getMsgAlert();
                limparMsg();
            }
        }else if(tipo == RELATORIO_PAISAGEM){
            if(UteisValidacao.emptyString(getNomeArquivoRelatorioGeradoAgora())) {
                montarErro("Não foi encontrado resultados para o relatório!");
                abrirRelatorio = getMensagemNotificar();
            }else{
                abrirRelatorio =   "window.open('" + getNomeArquivoRelatorioGeradoAgora() + "', '_blank', 'location=yes');";
                limparMsg();
            }
        }
    }
    public List<ParcelaEmAbertoRelTO> parcelasEmAbertas() throws Exception{
        List<ParcelaEmAbertoRelTO> parcelasAberto = consultarParcelasAberto();
        return parcelasAberto;
    }
    public void imprimirPDF() {
        try {
            limparMsg();
            parcelaEmAbertoRel.setDescricaoFiltros("");
            String titulo = " Relatório de Parcelas.";
            parcelaEmAbertoRel.setTotalParcelaEmAberto(0);
            parcelaEmAbertoRel.setValorTotalParcelaEmAberto(0.0);
            parcelaEmAbertoRel.setTotalParcelaPaga(0);
            parcelaEmAbertoRel.setValorTotalParcelaPaga(0.0);
            parcelaEmAbertoRel.setTotalParcelacCancelada(0);
            parcelaEmAbertoRel.setValorTotalParcelaCancelada(0.0);
            String xml = parcelaEmAbertoRel.emitirRelatorio(getFiltroEmpresa(), consultarParcelasAberto());
            parcelaEmAbertoRel.setValorTotalParcelaEmAberto(Uteis.arredondarForcando2CasasDecimais(parcelaEmAbertoRel.getValorTotalParcelaEmAberto()));
            parcelaEmAbertoRel.setValorTotalParcelaPaga(Uteis.arredondarForcando2CasasDecimais(parcelaEmAbertoRel.getValorTotalParcelaPaga()));
            parcelaEmAbertoRel.setValorTotalParcelaCancelada(Uteis.arredondarForcando2CasasDecimais(parcelaEmAbertoRel.getValorTotalParcelaCancelada()));
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_PARCELAS, listaParcelas.size(), parcelaEmAbertoRel.getDescricaoFiltros(), "PDF", "", "");

            String design = parcelaEmAbertoRel.getDesignIReportRelatorio();
            apresentarRelatorio(
                    parcelaEmAbertoRel.getIdEntidade(),
                    xml,
                    titulo,
                    getNomeEmpresaSelecionada(getFiltroEmpresa()),
                    "",
                    "PDF", "/" + parcelaEmAbertoRel.getIdEntidade() + "/registros",
                    design,
                    getUsuarioLogado().getNome(),
                    parcelaEmAbertoRel.getDescricaoFiltros(),
                    parcelaEmAbertoRel.getTotalParcelaEmAberto().toString(), parcelaEmAbertoRel.getValorTotalParcelaEmAberto().toString(),
                    parcelaEmAbertoRel.getTotalParcelaPaga().toString(), parcelaEmAbertoRel.getValorTotalParcelaPaga().toString(),
                    parcelaEmAbertoRel.getTotalParcelacCancelada().toString(), parcelaEmAbertoRel.getValorTotalParcelaCancelada().toString(),
                    parcelaEmAbertoRel.getTotalMatriculas().toString(),
                    parcelaEmAbertoRel.getTotalJuroeMulta().toString(),
                    isGerarMultasJuros(),
                    aprensentarDadoSensiveisCliente);
            setMensagemID("msg_relatorio_ok");
        } catch (ConsistirException e) {
            e.printStackTrace();
            montarErro("Nenhum Registro Encontrado!");
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }
    }

    public void irParaTelaCliente() {
        ParcelaEmAbertoRelTO obj = (ParcelaEmAbertoRelTO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        setMsgAlert("");
        limparMsg();
        try {
            if (obj == null) {
                throw new Exception("Cliente não encontrado.");
            } else if (UteisValidacao.emptyNumber(obj.getCliente())) {
                throw new Exception("Cliente não encontrado.");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCliente());
                irParaTelaCliente(clienteVO);
                setMsgAlert("abrirPopup('../clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700)");
            }
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }
    }

    public void consultarParcelas() {
        try {
            consultarParcelas_();
        } catch (Exception e) {
            montarErro(e.getMessage());
            e.printStackTrace();
        }
    }
    private void consultarParcelas_() throws Exception {
            limparMsg();
            valorTotalPago = 0;
            valorTotalEmAberto = 0;
            valorTotalCancelado = 0;
            qtdeParcelasEmAberto = 0;
            qtdeParcelasPago = 0;
            qtdeParcelasCancelado = 0;

            alunosEmAberto = new ArrayList<>();
            alunosPago = new ArrayList<>();
            alunosCancelado = new ArrayList<>();
            totalAlunos = 0;
            listaParcelas = new ArrayList<>();
            apresentarFormaPagamento = parcelaEmAbertoRel.getSituacoesSelecionadas().isEmpty() || parcelaEmAbertoRel.getCodigoSituacoesSelecionadas().contains("PG") ? true : false;
            parcelaEmAbertoRel.setApresentarDadosSensiveis(this.aprensentarDadoSensiveisCliente);
            listaParcelas = parcelaEmAbertoRel.consultarTodosParcelasEmAberto(getFiltroEmpresa());

            parcelaEmAbertoRel.setTotalJuroeMulta(0.0d);
            if(isGerarMultasJuros()){
                listaParcelas.forEach(e -> {
                    if (e.getSituacao().equals("EA") && Calendario.menor(e.getDateVencimento(), Calendario.hoje())) {
                        e.setMultas(parcelaEmAbertoRel.calcularMulta(e.getValor()));
                        e.setJuros(parcelaEmAbertoRel.calcularJuros(e.getValor(), e.getDateVencimento(), Calendario.hoje()));
                        parcelaEmAbertoRel.setTotalJuroeMulta(parcelaEmAbertoRel.getTotalJuroeMulta() + e.getMultas() + e.getJuros());
                    }
                });
            }

            for (ParcelaEmAbertoRelTO parcela : listaParcelas) {
                if (parcela.getSituacao().equals("EA")) {
                    valorTotalEmAberto += Uteis.arredondar(parcela.getValorMultasJuros(), 2 , 1);
                    qtdeParcelasEmAberto += 1;
                    if(UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
                        alunosEmAberto.add(parcela.getNome()); // venda ao consumidor, onde não existe cadastro no sistema
                    } else {
                        alunosEmAberto.add(parcela.getCodigoPessoa().toString());
                    }
                }
                if (parcela.getSituacao().equals("PG")) {
                    valorTotalPago += Uteis.arredondar(parcela.getValorMultasJuros(), 2, 1);
                    qtdeParcelasPago += 1;
                    if (UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
                        alunosPago.add(parcela.getNome());
                    } else {
                        alunosPago.add(parcela.getCodigoPessoa().toString());
                    }

                }
                if (parcela.getSituacao().equals("CA")) {
                    valorTotalCancelado += Uteis.arredondar(parcela.getValorMultasJuros(), 2 , 1);
                    qtdeParcelasCancelado += 1;
                    if(UteisValidacao.emptyNumber(parcela.getCodigoPessoa())) {
                        alunosCancelado.add(parcela.getNome());
                    } else {
                        alunosCancelado.add(parcela.getCodigoPessoa().toString());
                    }
                }
            }
            alunosEmAberto = alunosEmAberto.stream().distinct().collect(Collectors.toList());
            alunosPago = alunosPago.stream().distinct().collect(Collectors.toList());
            alunosCancelado = alunosCancelado.stream().distinct().collect(Collectors.toList());
            alunosEmAberto.addAll(alunosPago);
            alunosEmAberto.addAll(alunosCancelado);
            totalAlunos = alunosEmAberto.stream().distinct().collect(Collectors.toList()).size();

    }


    private List<ParcelaEmAbertoRelTO> consultarParcelasAberto() throws Exception{
        List<ParcelaEmAbertoRelTO> parcelasAberto = parcelaEmAbertoRel.consultarTodosParcelasEmAberto(getFiltroEmpresa());
        parcelaEmAbertoRel.setTotalJuroeMulta(0.0d);
        if(isGerarMultasJuros()){
            parcelasAberto.forEach(e -> {
                if (e.getSituacao().equals("EA") && Calendario.menor(e.getDateVencimento(), Calendario.hoje())) {
                    e.setMultas(parcelaEmAbertoRel.calcularMulta(e.getValor()));
                    e.setJuros(parcelaEmAbertoRel.calcularJuros(e.getValor(), e.getDateVencimento(), Calendario.hoje()));
                    parcelaEmAbertoRel.setTotalJuroeMulta(parcelaEmAbertoRel.getTotalJuroeMulta() + e.getMultas() + e.getJuros());
                }
            });
        }
        return parcelasAberto;
    }

    public void consultarResponsavel() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarResponsavel().equals("nome")) {
                objs = new Colaborador().consultarPorNomePessoa(getValorConsultarResponsavel(), getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarResponsavel(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarResponsavel(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarCliente() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCliente().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultarCliente(), 0, Uteis.NIVELMONTARDADOS_MINIMOS, null);
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(), 0, true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            setListaConsultarCliente(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarResponsavel() {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("responsavel");
        getParcelaEmAbertoRel().setColaboradorVO(obj);
    }

    public void selecionarCliente() {
        try {
            ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
            getParcelaEmAbertoRel().setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        } catch (Exception e) {
            getParcelaEmAbertoRel().setClienteVO(new ClienteVO());
            setMensagemDetalhada("msg_erro", e.getMessage());

        }

    }

    public List getTipoConsultaComboResponsavel() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }

    public List getTipoConsultaComboCliente() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("nome", "Nome"));
        objs.add(new SelectItem("matricula", "Matrícula"));
        return objs;
    }

    public void montarListaSelectItemEmpresa() {
        try {
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
                carregarListSelectItemsModalidade();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void limparCampoCliente() {
        getParcelaEmAbertoRel().setClienteVO(new ClienteVO());
    }

    public void limparCampoOperador() {
        getParcelaEmAbertoRel().setColaboradorVO(new ColaboradorVO());
    }

    public List getListaConsultarResponsavel() {
        return listaConsultarResponsavel;
    }

    public void setListaConsultarResponsavel(List listaConsultarResponsavel) {
        this.listaConsultarResponsavel = listaConsultarResponsavel;
    }

    public String getValorConsultarResponsavel() {
        return valorConsultarResponsavel;
    }

    public void setValorConsultarResponsavel(String valorConsultarResponsavel) {
        this.valorConsultarResponsavel = valorConsultarResponsavel;
    }

    public String getCampoConsultarResponsavel() {
        return campoConsultarResponsavel;
    }

    public void setCampoConsultarResponsavel(String campoConsultarResponsavel) {
        this.campoConsultarResponsavel = campoConsultarResponsavel;
    }

    public List<SelectItem> getListaSelectItemTipoSituacao() {
        return listaSelectItemTipoSituacao;
    }

    public void setListaSelectItemTipoSituacao(List<SelectItem> listaSelectItemTipoSituacao) {
        this.listaSelectItemTipoSituacao = listaSelectItemTipoSituacao;
    }

    public ParcelaEmAbertoRel getParcelaEmAbertoRel() {
        return parcelaEmAbertoRel;
    }

    public void setParcelaEmAbertoRel(ParcelaEmAbertoRel parcelaEmAbertoRel) {
        this.parcelaEmAbertoRel = parcelaEmAbertoRel;
    }

    public String getCampoConsultarCliente() {
        return campoConsultarCliente;
    }

    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }

    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public String getDataInicioPrm() {
        return dataInicioPrm;
    }

    public void setDataInicioPrm(String dataInicioPrm) {
        this.dataInicioPrm = dataInicioPrm;
    }

    public String getDataTerminoPrm() {
        return dataTerminoPrm;
    }

    public void setDataTerminoPrm(String dataTerminoPrm) {
        this.dataTerminoPrm = dataTerminoPrm;
    }

    public List<SelectItem> getListaSelectItemTipoSituacaoPeriodo() {
        return listaSelectItemTipoSituacaoPeriodo;
    }

    public void setListaSelectItemTipoSituacaoPeriodo(List<SelectItem> listaSelectItemTipoSituacaoPeriodo) {
        this.listaSelectItemTipoSituacaoPeriodo = listaSelectItemTipoSituacaoPeriodo;
    }

    public void novo() throws Exception {
        inicializarDados();
        montarListaSelectItemSituacao();
        montarListaSelectItemSituacaoPeriodo();
        montarListaSelectItemParcelasCanceladas();
        montarListaSelectItemEmpresa();
        montaListaTipoRelatorio();
        carregarConvenios();
        montarListaSelectItemPlano();
        montarListaSelectItemHorario();
        carregarListSelectItemsFormaDePagamento();
        carregarListSelectItemsModalidade();
        carregarListSelectItemsTurma();
    }

    private void montarListaSelectItemPlano() throws Exception {
        List<PlanoVO> planos = new ArrayList<PlanoVO>(){};
        if (UteisValidacao.emptyNumber(getFiltroEmpresa())) {
            planos = getFacade().getPlano().consultarTodos(null, Uteis.NIVELMONTARDADOS_MINIMOS);
        } else {
            if(permissaoConsultaTodasEmpresas){
                planos = getFacade().getPlano().consultarPorDescricaoDataIngresso(
                        "", Calendario.hoje(),
                         getFiltroEmpresa(), false, getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_MINIMOS).isControleAcessoMultiplasEmpresasPorPlano(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else {
                planos = getFacade().getPlano().consultarPorCodigoEmpresa(getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }
        }

        List<SelectItem> itemPlanos = new ArrayList<SelectItem>();
        itemPlanos.add(new SelectItem("", ""));
        for (PlanoVO plano : planos) {
            itemPlanos.add(new SelectItem(plano.getCodigo(), plano.getDescricao()));
        }
        setListaSelectItemPlano(itemPlanos);
    }

    public void montarListaSelectItemHorario() {
        try {
            List<SelectItem> itemHorarios = new ArrayList<SelectItem>();
            itemHorarios.add(new SelectItem("", ""));
            List<HorarioVO> horarios = getFacade().getHorario().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            for (HorarioVO horario : horarios) {
                itemHorarios.add(new SelectItem(horario.getCodigo(), horario.getDescricao()));
            }

            Ordenacao.ordenarLista(itemHorarios, "label");
            setListaSelectItemHorariosPlano(itemHorarios);
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void adicionarPlanoNaLista() {
        SelectItem itemSelecionado = null;
        for (SelectItem itemPlano : getListaSelectItemPlano()) {
            if (parcelaEmAbertoRel.getCodigoPlano() != 0 && itemPlano.getValue().equals(parcelaEmAbertoRel.getCodigoPlano())) {
                itemSelecionado = itemPlano;
                parcelaEmAbertoRel.getPlanosSelecionados().add(itemPlano);
                parcelaEmAbertoRel.setCodigoPlano(0);
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectItemPlano().remove(itemSelecionado);
            Ordenacao.ordenarLista(getListaSelectItemPlano(), "label");
        }
    }

    public void limparPlanos() {
        getListaSelectItemPlano().addAll(parcelaEmAbertoRel.getPlanosSelecionados());

        parcelaEmAbertoRel.setPlanosSelecionados(new ArrayList<SelectItem>());

        Ordenacao.ordenarLista(getListaSelectItemPlano(), "label");
    }

    public void adicionarHorarioNaLista() {
        SelectItem itemSelecionado = null;
        for (SelectItem itemHorario : getListaSelectItemHorariosPlano()) {
            if (parcelaEmAbertoRel.getCodigoHorario() != 0 && itemHorario.getValue().equals(parcelaEmAbertoRel.getCodigoHorario())) {
                itemSelecionado = itemHorario;
                parcelaEmAbertoRel.getHorariosSelecionados().add(itemHorario);
                parcelaEmAbertoRel.setCodigoHorario(0);
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectItemHorariosPlano().remove(itemSelecionado);
            Ordenacao.ordenarLista(getListaSelectItemHorariosPlano(), "label");
        }
    }

    public void adicionarModalidadeNaLista() {
        SelectItem itemSelecionado = null;
        for (SelectItem itemModalidade : getListaSelectItemModalidade()) {
            if (parcelaEmAbertoRel.getCodigoModalidade() != 0 && itemModalidade.getValue().equals(parcelaEmAbertoRel.getCodigoModalidade())) {
                itemSelecionado = itemModalidade;
                parcelaEmAbertoRel.getModalidadesSelecionadas().add(itemModalidade);
                parcelaEmAbertoRel.setCodigoModalidade(0);
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectItemModalidade().remove(itemSelecionado);
            Ordenacao.ordenarLista(getListaSelectItemModalidade(), "label");
        }
    }

    public void limparHorarios() {
        getListaSelectItemHorariosPlano().addAll(parcelaEmAbertoRel.getHorariosSelecionados());

        parcelaEmAbertoRel.setHorariosSelecionados(new ArrayList<SelectItem>());

        Ordenacao.ordenarLista(getListaSelectItemHorariosPlano(), "label");
    }

    public void adicionarSituacaoNaLista() {
        SelectItem itemSelecionado = null;
        for (SelectItem itemSituacao : getListaSelectItemTipoSituacao()) {
            if (!parcelaEmAbertoRel.getSituacao().equals("") && itemSituacao.getValue().equals(parcelaEmAbertoRel.getSituacao())) {
                itemSelecionado = itemSituacao;
                parcelaEmAbertoRel.getSituacoesSelecionadas().add(itemSituacao);
                parcelaEmAbertoRel.setSituacao("");
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectItemTipoSituacao().remove(itemSelecionado);
        }
    }

    public void limparSituacoes() {
        montarListaSelectItemSituacao();
        parcelaEmAbertoRel.setSituacoesSelecionadas(new ArrayList<SelectItem>());
    }

    public List<SelectItem> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<SelectItem> convenios) {
        this.convenios = convenios;
    }

    public List<SelectItem> getListaSelectItemPlano() {
        return listaSelectItemPlano;
    }

    public void setListaSelectItemPlano(List<SelectItem> listaSelectItemPlano) {
        this.listaSelectItemPlano = listaSelectItemPlano;
    }

    public List<SelectItem> getListaSelectItemHorariosPlano() {
        return listaSelectItemHorariosPlano;
    }

    public void setListaSelectItemHorariosPlano(List<SelectItem> listaSelectItemHorariosPlano) {
        this.listaSelectItemHorariosPlano = listaSelectItemHorariosPlano;
    }

    public List<SelectItem> getListaTipoRelatorio() {
        if(listaTipoRelatorio == null){
            listaTipoRelatorio = new ArrayList<SelectItem>();
        }
        return listaTipoRelatorio;
    }
    public boolean getRelatorioPaisagem(){
        return  getTipoRelatorioSelecionado() == 1 ?true :false;
    }
    public boolean getRelatorioRetrato(){
        return getTipoRelatorioSelecionado() == 0 ?true :false;
    }
    public void setListaTipoRelatorio(List<SelectItem> listaTipoRelatorio) {
        this.listaTipoRelatorio = listaTipoRelatorio;
    }

    public String getAbrirRelatorio() {
        return abrirRelatorio;
    }

    public void setAbrirRelatorio(String abrirRelatorio) {
        this.abrirRelatorio = abrirRelatorio;
    }

    public int getTipoRelatorioSelecionado() {
        return tipoRelatorioSelecionado;
    }

    public void setTipoRelatorioSelecionado(int tipoRelatorioSelecionado) {
        this.tipoRelatorioSelecionado = tipoRelatorioSelecionado;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public boolean isGerarMultasJuros() {
        return gerarMultasJuros;
    }

    public void setGerarMultasJuros(boolean gerarMultasJuros) {
        this.gerarMultasJuros = gerarMultasJuros;
    }

    public List<ParcelaEmAbertoRelTO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<ParcelaEmAbertoRelTO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public boolean isMostrarMenuFiltro() {
        return mostrarMenuFiltro;
    }

    public void setMostrarMenuFiltro(boolean mostrarMenuFiltro) {
        this.mostrarMenuFiltro = mostrarMenuFiltro;
    }

    public void mostrarMaisFiltros() {
        if (mostrarMenuFiltro) {
           mostrarMenuFiltro = false;
           return;
        }
        mostrarMenuFiltro = true;
    }

    public double getValorTotalPago() {
        return valorTotalPago;
    }

    public void setValorTotalPago(double valorTotalPago) {
        this.valorTotalPago = valorTotalPago;
    }

    public double getValorTotalEmAberto() {
        return valorTotalEmAberto;
    }

    public void setValorTotalEmAberto(double valorTotalEmAberto) {
        this.valorTotalEmAberto = valorTotalEmAberto;
    }

    public double getValorTotalCancelado() {
        return valorTotalCancelado;
    }

    public void setValorTotalCancelado(double valorTotalCancelado) {
        this.valorTotalCancelado = valorTotalCancelado;
    }

    public String getValorTotalPago_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalPago());
    }
    public String getValorTotalEmAberto_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalEmAberto());
    }
    public String getValorTotalCancelado_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalCancelado());
    }
    public Integer getQtdTotalParcelas() {
        return getListaParcelas().size();
    }
    public String getValorTotalParcelas_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorTotalEmAberto() + getValorTotalPago() + getValorTotalCancelado());
    }

    public List<String> getAlunosEmAberto() {
        return alunosEmAberto;
    }

    public void setAlunosEmAberto(List<String> alunosEmAberto) {
        this.alunosEmAberto = alunosEmAberto;
    }
    public Integer getTotalAlunosEmAberto() {
        return getAlunosEmAberto().size();
    }

    public List<String> getAlunosPago() {
        return alunosPago;
    }
    public Integer getTotalAlunosPago() {
        return getAlunosPago().size();
    }

    public void setAlunosPago(List<String> alunosPago) {
        this.alunosPago = alunosPago;
    }

    public List<String> getAlunosCancelado() {
        return alunosCancelado;
    }
    public Integer getTotalAlunosCancelado() {
        return getAlunosCancelado().size();
    }

    public void setAlunosCancelado(List<String> alunosCancelado) {
        this.alunosCancelado = alunosCancelado;
    }

    public int getTotalAlunos() {
        return totalAlunos;
    }

    public void setTotalAlunos(int totalAlunos) {
        this.totalAlunos = totalAlunos;
    }

    public int getQtdeParcelasEmAberto() {
        return qtdeParcelasEmAberto;
    }

    public void setQtdeParcelasEmAberto(int qtdeParcelasEmAberto) {
        this.qtdeParcelasEmAberto = qtdeParcelasEmAberto;
    }

    public int getQtdeParcelasPago() {
        return qtdeParcelasPago;
    }

    public void setQtdeParcelasPago(int qtdeParcelasPago) {
        this.qtdeParcelasPago = qtdeParcelasPago;
    }

    public int getQtdeParcelasCancelado() {
        return qtdeParcelasCancelado;
    }

    public void setQtdeParcelasCancelado(int qtdeParcelasCancelado) {
        this.qtdeParcelasCancelado = qtdeParcelasCancelado;
    }

    public List<SelectItem> getListaSelectItemParcelaCancelada() {
        return listaSelectItemParcelaCancelada;
    }

    public void setListaSelectItemParcelaCancelada(List<SelectItem> listaSelectItemParcelaCancelada) {
        this.listaSelectItemParcelaCancelada = listaSelectItemParcelaCancelada;
    }

    public boolean isMostrarNrTentativasMovitoRetorno() {
        return mostrarNrTentativasMovitoRetorno;
    }

    public void setMostrarNrTentativasMovitoRetorno(boolean mostrarNrTentativasMovitoRetorno) {
        this.mostrarNrTentativasMovitoRetorno = mostrarNrTentativasMovitoRetorno;
    }

    public List<SelectItem> getSelectItemsFormaDePagamento() {
        return selectItemsFormaDePagamento;
    }

    public void setSelectItemsFormaDePagamento(List<SelectItem> selectItemsFormaDePagamento) {
        this.selectItemsFormaDePagamento = selectItemsFormaDePagamento;
    }

    private void carregarListSelectItemsFormaDePagamento() throws Exception {
        List<FormaPagamentoVO> formasDePagamento = getFacade().getFormaPagamento().consultarPorTipoFormaPagamento("", false, false,
                false, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        Ordenacao.ordenarLista(formasDePagamento, "descricao");
        setSelectItemsFormaDePagamento(new ArrayList<SelectItem>());
        getSelectItemsFormaDePagamento().add(new SelectItem(0, ""));
        for (FormaPagamentoVO formaPagamento : formasDePagamento) {
            getSelectItemsFormaDePagamento().add(new SelectItem(formaPagamento.getCodigo(), formaPagamento.getDescricao()));
        }
    }

    public boolean isApresentarFormaPagamento() {
        return apresentarFormaPagamento;
    }

    public void setApresentarFormaPagamento(boolean apresentarFormaPagamento) {
        this.apresentarFormaPagamento = apresentarFormaPagamento;
    }

    public List<SelectItem> getListaSelectItemModalidade() {
        return listaSelectItemModalidade;
    }

    public void setListaSelectItemModalidade(List<SelectItem> listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    private void carregarListSelectItemsModalidade() throws Exception {
        List<ModalidadeVO> modalidades = getFacade().getModalidade().consultarTodasModalidades(getFiltroEmpresa(), true, true);
        Ordenacao.ordenarLista(modalidades, "descricao");
        setListaSelectItemModalidade(new ArrayList<>());
        getListaSelectItemModalidade().add(new SelectItem(0, ""));
        for (ModalidadeVO modalidade : modalidades) {
            getListaSelectItemModalidade().add(new SelectItem(modalidade.getCodigo(), modalidade.getNome()));
        }
    }

    public void limparModalidades() {
        getListaSelectItemModalidade().addAll(parcelaEmAbertoRel.getModalidadesSelecionadas());
        parcelaEmAbertoRel.setModalidadesSelecionadas(new ArrayList<>());
        Ordenacao.ordenarLista(getListaSelectItemModalidade(), "label");
    }

    public List<SelectItem> getListaSelectItemTurma() {
        return listaSelectItemTurma;
    }

    public void setListaSelectItemTurma(List<SelectItem> listaSelectItemTurma) {
        this.listaSelectItemTurma = listaSelectItemTurma;
    }

    private void carregarListSelectItemsTurma() throws Exception {
        HashSet<TurmaVO> turmas = new HashSet<>();

        for(SelectItem item : getListaSelectItemModalidade()){
            turmas.addAll(getFacade().getTurma().consultarPorCodigoModalidade(Integer.parseInt(item.getValue().toString()),
                    filtroEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        Ordenacao.ordenarLista(Arrays.asList(turmas.toArray()), "descricao");
        setListaSelectItemTurma(new ArrayList<>());
        getListaSelectItemTurma().add(new SelectItem(0, ""));
        for (TurmaVO turma : turmas) {
            getListaSelectItemTurma().add(new SelectItem(turma.getCodigo(), turma.getDescricao()));
        }
    }

    public void adicionarTurmaNaLista() {
        SelectItem itemSelecionado = null;
        for (SelectItem itemTurma : getListaSelectItemTurma()) {
            if (parcelaEmAbertoRel.getCodigoTurma() != 0 && itemTurma.getValue().equals(parcelaEmAbertoRel.getCodigoTurma())) {
                itemSelecionado = itemTurma;
                parcelaEmAbertoRel.getTurmasSelecionadas().add(itemTurma);
                parcelaEmAbertoRel.setCodigoTurma(0);
                break;
            }
        }

        if (itemSelecionado != null) {
            getListaSelectItemTurma().remove(itemSelecionado);
            Ordenacao.ordenarLista(getListaSelectItemTurma(), "label");
        }
    }

    public void limparTurmas() {
        getListaSelectItemTurma().addAll(parcelaEmAbertoRel.getTurmasSelecionadas());
        parcelaEmAbertoRel.setTurmasSelecionadas(new ArrayList<>());
        Ordenacao.ordenarLista(getListaSelectItemTurma(), "label");
    }
}
