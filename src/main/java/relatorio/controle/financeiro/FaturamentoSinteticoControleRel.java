package relatorio.controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import negocio.comuns.basico.CategoriaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.interfaces.basico.CategoriaInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import org.richfaces.component.html.HtmlDataTable;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoRelTO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoResumoPessoaVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoTipoProdutoVO;
import relatorio.negocio.jdbc.financeiro.FaturamentoSinteticoRel;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 *
 * <AUTHOR>
 */
public class FaturamentoSinteticoControleRel extends SuperControleRelatorio {

    private FaturamentoSinteticoRel faturamentoSinteticoRel;
    private String campoConsultarOperador;
    private String valorConsultarOperador;
    private String campoConsultarColaborador;
    private String valorConsultarColaborador;
    private String dataInicioPrm;
    private String dataTerminoPrm;
    private ColaboradorVO colaborador;
    private ColaboradorVO operador;
    private Boolean totalizador;
    private Boolean competencia;
    private Boolean bolsa;
    private boolean matrRenovRemat;
    private boolean produtoEstoque;
    private boolean mesReferenciaPlano;
    private boolean servico;
    private boolean trancamento;
    private boolean retornoTrancamento;
    private boolean aulaAvulsa;
    private boolean diaria;
    private boolean freePass;
    private boolean alterarHorario;
    private boolean manutencaoModalidade;
    private boolean taxaPersonal;
    private boolean creditoPersonal;
    private boolean pgtoSaldoDevedor;
    private boolean quitacaoCancelamento;
    private boolean acertoCCAluno;
    private boolean produtoSessao;
    private boolean armario;
    private boolean taxaAdesao;
    private boolean taxaAnuidade;
    private boolean atestado;
    private boolean creditoContaCorrente;
    private boolean taxaRenegociacao;
    private List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO;
    private List<PeriodoMensal> periodos;
    private FaturamentoSinteticoProdutoMesVO faturamentoSinteticoProdutoMesVO;
    private List<FaturamentoSinteticoResumoPessoaVO> listaProdutoMesVOs;
    private List<ColaboradorVO> listaConsultarOperador;
    private List<ColaboradorVO> listaConsultarColaborador;
    private String filtros;
    private FaturamentoSinteticoTipoProdutoVO resumo;
    private String agrupamento;
    private List<FaturamentoSinteticoRelTO> listaExportavel = new ArrayList<FaturamentoSinteticoRelTO>();
    private boolean somenteFaturamentoRecebido = false;
    private boolean contaCorrente = false;
    private HtmlDataTable dadosTable;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;
    private boolean incluirDevolucaoCheque = true;
    private boolean ignorarEdicaoPagamento = false;
    private String abrirRelatorio;
    private boolean desconsiderarProdutoCancelado = false;
    private boolean somenteContratoVendaAvulsaMesmoMesReferencia = false;
    private boolean desafio;
    private boolean evento;
    private boolean bioTotem;
    private boolean consultaNutricional;
    private boolean locacao;

    private List<CategoriaProdutoVO> categorias;

    private Integer modalidadeSelecionada;
    private List<SelectItem> listaModalidade;
    private Integer turmaSelecionada;
    private List<SelectItem> listaTurma;
    private Integer categoriaSelecionada;
    private List<SelectItem> listaCategorias;
    private Integer formaPagamentoSelecionada;
    private List<SelectItem> listaFormaPagamento;

    public FaturamentoSinteticoControleRel() throws Exception {
        inicializarFacades();
        inicializarDados();
    }

    public void montarListaSelectItemFormaPagamento() throws Exception {
        try {
            List<FormaPagamentoVO> formas = getFacade().getFormaPagamento().consultarSimplesFormasPagamento(false);
            listaFormaPagamento = new ArrayList<>();
            listaFormaPagamento.add(new SelectItem(0, "Selecione"));
            for(FormaPagamentoVO fp : formas){
                listaFormaPagamento.add(new SelectItem(fp.getCodigo(), fp.getDescricao()));
            }
        }catch (Exception e){
            montarErro(e);
            this.listaFormaPagamento = new ArrayList<>();
            this.listaFormaPagamento.add(new SelectItem(0, "Selecione"));
        }
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void inicializarDados() throws Exception {
        setOperador(new ColaboradorVO());
        setColaborador(new ColaboradorVO());
        setFaturamentoSinteticoRel(new FaturamentoSinteticoRel());
        setDataInicioPrm("");
        setDataTerminoPrm("");
        setCampoConsultarOperador("");
        setValorConsultarOperador("");
        setProdutoEstoque(false);
        setProdutoSessao(false);
        setTotalizador(false);
        setCompetencia(false);
        setBolsa(false);
        setManutencaoModalidade(false);
        setDiaria(false);
        setQuitacaoCancelamento(false);
        setCreditoPersonal(false);
        setMatriculaRenovacaoRematricula(false);
        setPgtoSaldoDevedor(false);
        setArmario(false);
        setQuitacaoCancelamento(false);
        setAcertoCCAluno(false);
        setTrancamento(false);
        setAulaAvulsa(false);
        setTaxaPersonal(false);
        setMesReferenciaPlano(true);
        setCreditoContaCorrente(false);
        setListaTipoProdutoVO(new ArrayList<>());
        setFaturamentoSinteticoProdutoMesVO(new FaturamentoSinteticoProdutoMesVO());
        setListaProdutoMesVOs(new ArrayList<>());
        setResumo(new FaturamentoSinteticoTipoProdutoVO());
        montarListaSelectItemEmpresa();
        montarModalidadeSelectItem();
        montarTurmas();
        montarListaSelectItemCategorias();
        montarListaSelectItemFormaPagamento();
        setAgrupamento("nomeDuracao");
        setDesafio(false);
        setBioTotem(false);
        setConsultaNutricional(false);
        setLocacao(false);
        setTaxaRenegociacao(false);
    }

    public void inicializarDadosFaturamento() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.FATURAMENTO_PERIODO.toString()));
        setSomenteFaturamentoRecebido(false);
        inicializarDados();
    }

    public void inicializarDadosFaturamentoRecebido() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.FATURAMENTO_RECEBIDO_PERIODO.toString()));
        setSomenteFaturamentoRecebido(true);
        inicializarDados();
    }

    public Map<String, Boolean> getMapaTipoProduto() {
        Map<String, Boolean> mapaTipoProduto = new HashMap<String, Boolean>();
        mapaTipoProduto.put("", true);
        mapaTipoProduto.put("MARERN", matrRenovRemat);
        mapaTipoProduto.put("PE", produtoEstoque);
        mapaTipoProduto.put("PM", mesReferenciaPlano);
        mapaTipoProduto.put("SE", servico);
        mapaTipoProduto.put("TR", trancamento);
        mapaTipoProduto.put("AA", aulaAvulsa);
        mapaTipoProduto.put("DI", diaria);
        mapaTipoProduto.put("FR", freePass);
        mapaTipoProduto.put("AH", alterarHorario);
        mapaTipoProduto.put("MM", manutencaoModalidade);
        mapaTipoProduto.put("TP", taxaPersonal);
        mapaTipoProduto.put("SS", produtoSessao);
        mapaTipoProduto.put(TipoProduto.ARMARIO.getCodigo(), armario);
        mapaTipoProduto.put("MC", pgtoSaldoDevedor);
        mapaTipoProduto.put("QU", quitacaoCancelamento);
        mapaTipoProduto.put("AC", acertoCCAluno);
        mapaTipoProduto.put("TD", taxaAdesao);
        mapaTipoProduto.put("TA", taxaAnuidade);
        mapaTipoProduto.put("AT", atestado);
        mapaTipoProduto.put(TipoProduto.DESAFIO.getCodigo(), desafio);
        mapaTipoProduto.put(TipoProduto.BIO_TOTEM.getCodigo(), bioTotem);
        mapaTipoProduto.put(TipoProduto.CONSULTA_NUTRICIONAL.getCodigo(), consultaNutricional);
        mapaTipoProduto.put(TipoProduto.TAXA_RENEGOCIACAO.getCodigo(), taxaRenegociacao);
        if(somenteFaturamentoRecebido)
            mapaTipoProduto.put("CC", creditoContaCorrente);
        mapaTipoProduto.put("CP", creditoPersonal);
        mapaTipoProduto.put(TipoProduto.LOCACAO.getCodigo(), locacao);
        return mapaTipoProduto;
    }

    // todo (Rhuan): inserir filtro para cheques e remover essa validacao
    public Map<String, Boolean> getMapaTipoProdutoFaturamento() {
        Map<String, Boolean> mapaTipoProduto = getMapaTipoProduto();
        mapaTipoProduto.put("CH", exibirCheques(mapaTipoProduto));
        return mapaTipoProduto;
    }

    // todo (Rhuan): remover esse metodo depois que o filtro para cheques devolvidos for criado
    private Boolean exibirCheques(Map<String, Boolean> mapaTipoProduto) {
        int contador = 0;
        for (String chave : mapaTipoProduto.keySet()) {
            contador = mapaTipoProduto.get(chave) ? contador + 1 : contador;
        }
        return contador < 2;
    }

    public boolean getTodos() {
        return (matrRenovRemat || produtoEstoque || mesReferenciaPlano
                || servico || trancamento || aulaAvulsa
                || diaria || freePass || alterarHorario || manutencaoModalidade
                || taxaPersonal || produtoSessao || pgtoSaldoDevedor || quitacaoCancelamento
                || acertoCCAluno || armario || desafio || creditoPersonal || taxaAdesao || taxaAnuidade || atestado
                || bioTotem || consultaNutricional || taxaRenegociacao || locacao );
    }

    public String imprimir() {
        return imprimir(true, null);
    }

    public String imprimir(boolean validarEmpresa, Integer empresa) {
        try {
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            //valida se a empresa foi informada
            if(validarEmpresa){
                validarEmpresaLogada();
            }else if(empresa != null){
                setFiltroEmpresa(empresa);
            }

            //cria objeto de total geral
            FaturamentoSinteticoProdutoVO totalGeral = new FaturamentoSinteticoProdutoVO(); // total geral de toda a consulta
            totalGeral.setDescricao("TOTAL GERAL");
            //valida se as datas estão com intervalo correto
            faturamentoSinteticoRel.setDataInicio(faturamentoSinteticoRel.getDataInicio());
            faturamentoSinteticoRel.setDataTermino(faturamentoSinteticoRel.getDataTermino());
            if (faturamentoSinteticoRel.getDataInicio().compareTo(faturamentoSinteticoRel.getDataTermino()) > 0) {
                throw new Exception("O Relatório só pode ser gerado com a Data de Início menor que a Data de Término para o período da pesquisa.");
            }
            if (isSomenteFaturamentoRecebido()) {
                obterDadosImpressaoFatRecebido(totalGeral);
                if (JSFUtilities.isJSFContext()) {
                    notificarRecursoEmpresa(RecursoSistema.RELATORIO_FATURAMENTO_RECEBIDO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(faturamentoSinteticoRel.getDataInicio(), faturamentoSinteticoRel.getDataTermino()) + 1);
                }
            } else {
                obterDadosImpressao(totalGeral);
                if (JSFUtilities.isJSFContext()) {
                    notificarRecursoEmpresa(RecursoSistema.RELATORIO_FATURAMENTO_SUCESSO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(faturamentoSinteticoRel.getDataInicio(), faturamentoSinteticoRel.getDataTermino()) + 1);
                }
            }
            setResumo(new FaturamentoSinteticoTipoProdutoVO());
            getResumo().setTipoProduto("RESUMO GERAL");
            getResumo().getListaProduto().add(totalGeral);
            if (!getResumo().getListaProduto().isEmpty()
                    && !getResumo().getListaProduto().get(0).getListaProdutoXMes().isEmpty()) {
                getResumo().setApresentarResultado(true);
            }
            // listaTipoProdutoVO.add(getResumo());
            setFiltros(getDescricaoFiltros());
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
            return "relatorio";
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
            return "consulta";
        }
    }


    public void obterDadosImpressaoFatRecebido(FaturamentoSinteticoProdutoVO totalGeral) throws Exception {
        listaTipoProdutoVO = new ArrayList();
        periodos = Uteis.getPeriodosMensaisEntreDatas(faturamentoSinteticoRel.getDataInicio(), faturamentoSinteticoRel.getDataTermino());
        getFacade().getMovProduto().consultaFaturamentoRecebidoComChequeDevolvido(getFiltroEmpresa(), listaTipoProdutoVO,
                periodos, getMapaTipoProduto(), totalGeral, agrupamento, getOperador().getCodigo(), getColaborador().getCodigo(), isContaCorrente(), isIgnorarEdicaoPagamento(), isIncluirDevolucaoCheque(), getFiltroFaturamentoRecebido());

    }

    public List<FaturamentoSinteticoProdutoVO> obterDadosImpressao(FaturamentoSinteticoProdutoVO totalGeral) throws Exception {
        //instancia a lista de produtos novamente e organiza a lista de periodos novamente
        listaTipoProdutoVO = new ArrayList<>();
        periodos = Uteis.getPeriodosMensaisEntreDatas(faturamentoSinteticoRel.getDataInicio(), faturamentoSinteticoRel.getDataTermino());
        List<FaturamentoSinteticoProdutoVO> listaLinhaProduto = new ArrayList<FaturamentoSinteticoProdutoVO>();

        Map<PeriodoMensal, Map<String, FaturamentoSinteticoProdutoMesVO>> mapaTiposContratos = new HashMap<>();

        FaturamentoSinteticoTipoProdutoVO tipoProdutoAgrupamentoTipoContrato;
        if (getAgrupamento().equals("tipoContrato")) {
            matrRenovRemat = true;
        }

        //verifica se foi escolhido algum tipo de produto ou não
        for (String tipoProduto : getMapaTipoProdutoFaturamento().keySet()) {
            if (tipoProduto.trim().isEmpty()) {
                if (getTodos()) {
                    continue;
                }
            } else if (!getMapaTipoProdutoFaturamento().get(tipoProduto)) // trazer somente os tipos produtos informados
            {
                continue;
            }
            FaturamentoSinteticoTipoProdutoVO tipoProdutoVO = new FaturamentoSinteticoTipoProdutoVO();
            if (getAgrupamento().equals("tipoContrato") &&  tipoProduto.equals("MARERN")) {
                tipoProdutoAgrupamentoTipoContrato = tipoProdutoVO;
            }
            tipoProdutoVO.setTipoProduto(getTipoProduto_Apresentar(tipoProduto));
            listaLinhaProduto = new ArrayList<>();
            listaLinhaProduto = getFacade().getMovProduto().consultarProdutosMovimentadoParaFaturaPeriodo(
                    faturamentoSinteticoRel.getDataInicio(), faturamentoSinteticoRel.getDataTermino(),
                    preencherListaTipoProduto(tipoProduto), getOperador().getCodigo(), getColaborador().getCodigo(),
                    getFiltroEmpresa(), agrupamento, false, isSomenteFaturamentoRecebido(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, getBolsa(),
                    isDesconsiderarProdutoCancelado(), isSomenteContratoVendaAvulsaMesmoMesReferencia(),isEvento() && !getTodos());

            //SUBTABLE - PRODUTO
            for (FaturamentoSinteticoProdutoVO fsProduto : listaLinhaProduto) {
                //COLUNA - MES x PRODUTO
                for (PeriodoMensal p : periodos) {
                    Map<String, FaturamentoSinteticoProdutoMesVO> mapaPeriodo = mapaTiposContratos.get(p);
                    if (mapaPeriodo == null) {
                        mapaPeriodo = new HashMap<>();
                        mapaPeriodo.put("MA", null);
                        mapaPeriodo.put("RN", null);
                        mapaPeriodo.put("RE", null);
                        mapaTiposContratos.put(p, mapaPeriodo);
                    }

                    FaturamentoSinteticoProdutoMesVO produtoMesVO;
                    produtoMesVO = getFacade().getMovProduto().consultarFaturamentoSinteticoResumoPessoa(
                            Uteis.getDataJDBC(p.getDataInicio()), Uteis.getDataJDBC(p.getDataTermino()), preencherListaTipoProduto(fsProduto.getProduto().getTipoProduto()),
                            getOperador().getCodigo(), getColaborador().getCodigo(), getFiltroEmpresa(),
                            fsProduto.getDuracao(), fsProduto.getPlano(), fsProduto.getProduto().getCodigo(),
                            !fsProduto.getNomePlano().isEmpty() ? fsProduto.getNomePlano() : null,
                            fsProduto.getProduto().getTipoProduto().equals("PM") ? null : fsProduto.getDescricao(), fsProduto.getDescricao(),
                            isSomenteFaturamentoRecebido(), getBolsa(), isDesconsiderarProdutoCancelado(), isSomenteContratoVendaAvulsaMesmoMesReferencia(),getEmpresaLogado(),isEvento() && !getTodos());

                    if (!getAgrupamento().equals("tipoContrato") || tipoProduto.equals("MARERN")) {
                        mapaPeriodo.computeIfAbsent(fsProduto.getProduto().getTipoProduto(), k -> produtoMesVO);
                        //outros???
                        fsProduto.getListaProdutoXMes().add(produtoMesVO);
                    } else if (getAgrupamento().equals("tipoContrato")) {
                        for (FaturamentoSinteticoResumoPessoaVO resumoPessoaVO : produtoMesVO.getListaResumoPessoa()) {
                            FaturamentoSinteticoProdutoMesVO item = mapaPeriodo.get(resumoPessoaVO.getContrato().getSituacaoContrato());
                            item.getListaResumoPessoa().add(resumoPessoaVO);
                            item.setValor(item.getValor() + resumoPessoaVO.getValor());
                            item.setQtd(item.getQtd() + 1);
                        }
                    }
                }
                if (!fsProduto.getListaProdutoXMes().isEmpty()) {
                    tipoProdutoVO.getListaProduto().add(fsProduto);
                }
            }

            if (getAgrupamento().equals("tipoContrato") && listaTipoProdutoVO.size() == 1) {

            }

            if (!tipoProdutoVO.getListaProduto().isEmpty()) {
                FaturamentoSinteticoProdutoVO totalizador = new FaturamentoSinteticoProdutoVO();
                totalizador.setDescricao("TOTALIZADOR");
                for (int i = 0; i < periodos.size(); i++) {
                    FaturamentoSinteticoProdutoMesVO subTotalGeral;
                    if (totalGeral.getListaProdutoXMes().size() <= i) {
                        subTotalGeral = new FaturamentoSinteticoProdutoMesVO();
                        totalGeral.getListaProdutoXMes().add(subTotalGeral);
                    } else {
                        subTotalGeral = totalGeral.getListaProdutoXMes().get(i);
                    }
                    FaturamentoSinteticoProdutoMesVO totalizadorMes = new FaturamentoSinteticoProdutoMesVO();
                    for (FaturamentoSinteticoProdutoVO fsProduto : listaLinhaProduto) {
                        if (!fsProduto.getListaProdutoXMes().isEmpty() && fsProduto.getListaProdutoXMes().size() >= (i+1)) {
                            FaturamentoSinteticoProdutoMesVO produtoMes = fsProduto.getListaProdutoXMes().get(i);
                            totalizadorMes.setQtd(totalizadorMes.getQtd() + produtoMes.getQtd());
                            totalizadorMes.setValor(totalizadorMes.getValor() + produtoMes.getValor());
                            totalizadorMes.getListaResumoPessoa().addAll(produtoMes.getListaResumoPessoa());
                            subTotalGeral.setQtd(subTotalGeral.getQtd() + produtoMes.getQtd());
                            subTotalGeral.setValor(subTotalGeral.getValor() + produtoMes.getValor());
                            subTotalGeral.getListaResumoPessoa().addAll(produtoMes.getListaResumoPessoa());
                        }
                    }
                    totalizador.getListaProdutoXMes().add(totalizadorMes);
                }
                tipoProdutoVO.getListaProduto().add(totalizador);
                tipoProdutoVO.setApresentarResultado(true);
                listaTipoProdutoVO.add(tipoProdutoVO);
            }
        }
        return listaLinhaProduto;
    }

    public List<String> preencherListaTipoProduto(String tipoProduto) {
        //analisa o tipo de produto para consulta
        //por padrão para consultar por Matricula, Rematricula, Renovacao sera adicionado os três para a pesquisa
        List<String> listaTipoProdutos = new ArrayList<String>();
        if (tipoProduto.equalsIgnoreCase("MARERN")) {
            listaTipoProdutos.add("MA");
            listaTipoProdutos.add("RE");
            listaTipoProdutos.add("RN");
        } else {
            if (!tipoProduto.isEmpty()) {
                listaTipoProdutos.add(tipoProduto);
            }
        }
        return listaTipoProdutos;
    }

    public List<String> preencheListaProdutosFiltros() {
        List listaProdutos = new ArrayList();
        if (getMatriculaRenovacaoRematricula()) {
            listaProdutos.add("Matrícula, Rematrícula , Renovação");
        }
        if (getMesReferenciaPlano()) {
            listaProdutos.add("Mês de Referência Plano");
        }
        if (getRetornoTrancamento()) {
            listaProdutos.add("Retorno Trancamento");
        }
        if (getDiaria()) {
            listaProdutos.add("Diária");
        }
        if (getAlterarHorario()) {
            listaProdutos.add("Alterar - Horário");
        }
        if (getProdutoEstoque()) {
            listaProdutos.add("Produto Estoque");
        }
        if (getServico()) {
            listaProdutos.add("Serviço");
        }
        if (getTrancamento()) {
            listaProdutos.add("Trancamento");
        }
        if (getAulaAvulsa()) {
            listaProdutos.add("Aula Avulsa");
        }
        if (getFreePass()) {
            listaProdutos.add("FreePass");
        }
        if (getManutencaoModalidade()) {
            listaProdutos.add("Manutenção Modalidade");
        }
        if (isTaxaPersonal()) {
            listaProdutos.add("Taxa de Personal");
        }
        if (isCreditoPersonal()) {
            listaProdutos.add("Crédito Personal");
        }
        if (isAcertoCCAluno()){
            listaProdutos.add("Acerto C/C Aluno");
        }
        if (isPgtoSaldoDevedor()){
            listaProdutos.add("Pgto Saldo Devedor");
        }
        if (isQuitacaoCancelamento()){
            listaProdutos.add("Quitação Cancelamento");
        }
        if (isProdutoSessao()) {
            listaProdutos.add("Produto do tipo Sessão");
        }
        if (isArmario()) {
            listaProdutos.add("Produto do Armário");
        }
        if (isCreditoContaCorrente()) {
            listaProdutos.add("Depósito Conta Corrente Aluno");
        }
        if (isTaxaAdesao()) {
            listaProdutos.add("Adesão Recorrência");
        }
        if (isTaxaAnuidade()) {
            listaProdutos.add("Anuidade Recorrência");
        }
        if (isAtestado()) {
            listaProdutos.add("Atestado Ap. Física");
        }
        if (isDesafio()) {
            listaProdutos.add("Produto do Desafio");
        }
        if (getBioTotem()) {
            listaProdutos.add("Bio Totem");
        }
        if (getConsultaNutricional()) {
            listaProdutos.add("Consulta Nutricional");
        }
        if (isLocacao()) {
            listaProdutos.add("Locação");
        }
        return listaProdutos;

    }

    private String getDescricaoFiltros() throws Exception {
        String filtros = "";
        filtros += "<b>Empresa: </b> " + getNomeEmpresaSelecionada(getFiltroEmpresa()) + "</br>";
        filtros += " <b>Período de: </b>" + Uteis.getDataAplicandoFormatacao(getFaturamentoSinteticoRel().getDataInicio(), "dd/MM/yyyy") + " <b>Até: </b>" + Uteis.getData(getFaturamentoSinteticoRel().getDataTermino()) + "</br>";
        if (!getOperador().getPessoa().getNome().equals("")) {
            if (isSomenteFaturamentoRecebido()){
                filtros += "<b>Quem lançou o pagamento: </b> " + getOperador().getPessoa().getNome() + " </br>";
            } else {
                filtros += "<b>Quem lançou contrato: </b> " + getOperador().getPessoa().getNome() + " </br>";
            }
        }
        if (!getColaborador().getPessoa().getNome().equals("")) {
            filtros += "<b>Consultor Responsável: </b> " + getColaborador().getPessoa().getNome() + " </br>";
        }
        if (getBolsa()) {
            filtros += ("<b>Somente Bolsista </b> </br>");
        }
        if (getTodos()) {
            filtros += ("<b>Tipos de Produtos: </b> </br>");
        }
        List<String> lista = preencheListaProdutosFiltros();
        if (lista.size() > 0) {
            for (String produtos : lista) {
                filtros += "<b>Produto: </b>";
                filtros += " " + produtos;
                if (produtos.equalsIgnoreCase("Mês de Referência Plano")) {
                    filtros += " <b>Agrupado por: </b>" + getNomeAgrupamento(agrupamento);
                }
                filtros += "</br>";
            }
        }
        return filtros;
    }

    private String getNomeAgrupamento(String agrupamento) {
        if (agrupamento.equalsIgnoreCase("duracao")) {
            return "Duração";
        } else if (agrupamento.equalsIgnoreCase("nomeDuracao")) {
            return "Nome e Duração";
        } else if (agrupamento.equalsIgnoreCase("nome")) {
            return "Nome";
        } else if ("tipoContrato".equalsIgnoreCase(agrupamento)) {
            return "Tipos de Contrato";
        }
        return "";
    }

    private void validarEmpresaLogada() throws Exception {
        if (UteisValidacao.emptyNumber(getFiltroEmpresa()) && !isPermissaoConsultaTodasEmpresas()) {
            throw new Exception("Por favor, informe o campo Empresa.");
        }
    }

    public void visualizarPessoas() throws Exception {
        try {
            ClienteInterfaceFacade cliente = getFacade().getCliente();
            Contrato contrato = new Contrato();
            for (FaturamentoSinteticoResumoPessoaVO resumoTotal : getFaturamentoSinteticoProdutoMesVO().getListaResumoPessoa()) {
                FaturamentoSinteticoProdutoVO fatProduto = (FaturamentoSinteticoProdutoVO) request().getAttribute("fatProduto");
                //    resumoTotal.setDescricaoProduto(fatProduto.getDescricao());
                if (!UteisValidacao.emptyNumber(resumoTotal.getCliente().getPessoa().getCodigo())) {
                    resumoTotal.setCliente(cliente.consultarPorCodigoPessoa(resumoTotal.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                    if (resumoTotal.getCliente().getPessoa().getCodigo() != 0) {
                        resumoTotal.setCliente(cliente.consultarPorCodigoPessoa(resumoTotal.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                        if (resumoTotal.getCliente().getCodigo() == null || resumoTotal.getCliente().getCodigo() == 0) {
                            resumoTotal.setColaboradorVO(getFacade().getColaborador().consultarPorCodigoPessoa(
                                    resumoTotal.getCliente().getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        }
                    }
                }
                if (!UteisValidacao.emptyNumber(resumoTotal.getContrato().getCodigo())
                        && (UteisValidacao.emptyString(resumoTotal.getModalidade_Apresentar())
                        || UteisValidacao.emptyString(resumoTotal.getDataInicio_Apresentar())
                        || UteisValidacao.emptyString(resumoTotal.getDataAte_Apresentar())
                        || UteisValidacao.emptyNumber(resumoTotal.getContrato().getDuracao_Apresentar())
                        || UteisValidacao.emptyString(resumoTotal.getPlano_Apresentar())) ) {
                    resumoTotal.setContrato(contrato.consultarPorChavePrimariaComModalidade(resumoTotal.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                }
            }
            Ordenacao.ordenarLista(getFaturamentoSinteticoProdutoMesVO().getListaResumoPessoa(), "nome_Apresentar");
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarDados() throws Exception {
        if (getFaturamentoSinteticoRel().getDataInicio() == null) {
            throw new Exception("O campo DATA INÍCIO deve ser informado.");
        }
        if (getFaturamentoSinteticoRel().getDataTermino() == null) {
            throw new Exception("O campo DATA TÉRMINO deve ser informado.");
        }
        if (getFaturamentoSinteticoRel().getOrdenacao().equals("")) {
            throw new Exception("O campo ORDENAÇÃO deve ser informado.");
        }
        if (getFaturamentoSinteticoRel().getTipoVisualizacao().equals("")) {
            throw new Exception("O campo TIPO VISUALIZAÇÃO deve ser informado.");
        }

    }

    public List getListaSelectItemOrdenacao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("MA", "Matrícula"));
        objs.add(new SelectItem("NO", "Nome do Aluno"));
        objs.add(new SelectItem("DU", "Duração"));
        return objs;
    }

    public List getListaSelectItemTipoProduto() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("MA", "Matrícula"));
        objs.add(new SelectItem("PE", "Produto Estoque"));
        objs.add(new SelectItem("PM", "Mês de Referência Plano"));
        objs.add(new SelectItem("RE", "Rematrícula"));
        objs.add(new SelectItem("RN", "Renovação"));
        objs.add(new SelectItem("SE", "Serviço"));
        objs.add(new SelectItem("TR", "Trancamento"));
        objs.add(new SelectItem("RT", "Retorno Trancamento"));
        objs.add(new SelectItem("AA", "Aula Avulsa"));
        objs.add(new SelectItem("DI", "Diária"));
        objs.add(new SelectItem("FR", "FreePass"));
        objs.add(new SelectItem("AH", "Alterar - Horário"));
        objs.add(new SelectItem("MM", "Manutenção Modalidade"));
        objs.add(new SelectItem("TP", "Taxa de Personal"));
        objs.add(new SelectItem("SS", "Produto do Tipo Sessão"));
        objs.add(new SelectItem(TipoProduto.ARMARIO.getCodigo(), "Armário"));
        objs.add(new SelectItem(TipoProduto.DESAFIO.getCodigo(), "Desafio"));
        return objs;
    }

    public List getTipoConsultaComboOperador() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }


    public List getTipoAgrupamento() throws Exception {
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem("nomeDuracao", "Nome e Duração"));
        objs.add(new SelectItem("duracao", "Duração"));
        objs.add(new SelectItem("nome", "Nome"));
        if (isSomenteFaturamentoRecebido()) {
            objs.add(new SelectItem("tipoContrato", "Tipos de contrato"));
        }
        return objs;
    }

    public void marcarTodosTipoProduto() {
        setMatriculaRenovacaoRematricula(true);
        setProdutoEstoque(true);
        setMesReferenciaPlano(true);
        setServico(true);
        setTrancamento(true);
        setRetornoTrancamento(true);
        setAulaAvulsa(true);
        setDiaria(true);
        setFreePass(true);
        setAlterarHorario(true);
        setManutencaoModalidade(true);
        setProdutoSessao(true);
        setArmario(true);
        setDesafio(true);
        setBioTotem(true);
        setConsultaNutricional(true);
        setTaxaRenegociacao(true);
    }

    public void consultarOperador() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarOperador().equals("nome")) {
                objs = getFacade().getColaborador().consultarPorNomeOperador(getValorConsultarOperador(), getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarOperador(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarOperador(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarOperador() {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("operador");
        setOperador(obj);
    }

    public void limparCampoOperador() {
        setOperador(new ColaboradorVO());
    }

    public void consultarColaborador() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarColaborador().equals("nome")) {
                objs = getFacade().getColaborador().consultarPorNomePessoa(getValorConsultarColaborador(), getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarColaborador(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarColaborador(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarColaborador() {
        ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
        setColaborador(obj);
    }

    public void limparCampoColaborador() {
        setColaborador(new ColaboradorVO());
    }

    public List getTipoConsultaComboColaborador() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("nome", "Nome"));
        return objs;
    }

    public String voltar() {
        listaTipoProdutoVO.clear();
        return "voltar";
    }

    public void irParaTelaCliente() throws Exception {
        FaturamentoSinteticoResumoPessoaVO obj = (FaturamentoSinteticoResumoPessoaVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getCliente());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public void exportarResumoPessoa(ActionEvent evt){
        try {
            String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dadosTable);
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getManagedBeanValue("ExportadorListaControle");
            List lista = getFaturamentoSinteticoProdutoMesVO().getListaResumoPessoa();
            if(!UteisValidacao.emptyString(colunaOrdenacao)) {
                String[] ordenacao = colunaOrdenacao.split(":");
                Ordenacao.ordenarLista(lista, ordenacao[0]);
                if (ordenacao[1].equalsIgnoreCase("DESC")) {
                    Collections.reverse(lista);
                }
            }

            exportadorListaControle.exportar(evt);
        }catch (Exception erro){
            System.out.println(erro);
        }
    }
    public void irParaTelaColaborador() {
        FaturamentoSinteticoResumoPessoaVO obj = (FaturamentoSinteticoResumoPessoaVO) context().getExternalContext().getRequestMap().get("resumoPessoa");
        try {
            if (obj == null) {
                throw new Exception("Colaborador Não Encontrado.");
            } else {
                irParaTelaColaborador(obj.getColaboradorVO());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarListaExportavel() throws Exception {
        setListaExportavel(new ArrayList<FaturamentoSinteticoRelTO>());

        Boolean clienteVazio=false;
        ClienteInterfaceFacade cliente = getFacade().getCliente();
        PessoaInterfaceFacade pessoa = getFacade().getPessoa();
        ContratoInterfaceFacade contrato = getFacade().getContrato();
        EmpresaInterfaceFacade empresaDAO = getFacade().getEmpresa();
        ContratoModalidade contratoModalidadeDAO = getFacade().getContratoModalidade();
        CategoriaInterfaceFacade categoriaDAO = getFacade().getCategoria();
        Map<Integer, EmpresaVO> mapEmpresas = empresaDAO.obterMapaEmpresas(Uteis.NIVELMONTARDADOS_MINIMOS);

        for (FaturamentoSinteticoTipoProdutoVO fstpVO : listaTipoProdutoVO) {
            for (FaturamentoSinteticoProdutoVO fspVO : fstpVO.getListaProduto()) {
                if (!fspVO.getDescricao().equals("TOTALIZADOR")) {
                    for (FaturamentoSinteticoProdutoMesVO fspmVO : fspVO.getListaProdutoXMes()) {
                        for (FaturamentoSinteticoResumoPessoaVO fsrpVO : fspmVO.getListaResumoPessoa()) {

                            if (!UteisValidacao.emptyNumber(fsrpVO.getCliente().getPessoa().getCodigo())) {
                                clienteVazio = false;
                                fsrpVO.setCliente(cliente.consultarPorCodigoPessoa(fsrpVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));

                                if(fsrpVO.getCliente().getPessoa().getNome().isEmpty()){
                                    clienteVazio = true;
                                    fsrpVO.setPessoa(pessoa.consultarPorChavePrimaria(fsrpVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                                }

                                fsrpVO.getCliente().setEmpresa(mapEmpresas.get(fsrpVO.getCliente().getEmpresa().getCodigo()));
                                if (fsrpVO.getCliente().getCodigo() == null || fsrpVO.getCliente().getCodigo() == 0) {
                                    fsrpVO.setColaboradorVO(getFacade().getColaborador().consultarPorCodigoPessoa(fsrpVO.getCliente().getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS));
                                    fsrpVO.getColaboradorVO().setEmpresa(mapEmpresas.get(fsrpVO.getColaboradorVO().getEmpresa().getCodigo()));

                                }
                            }
                            if (!UteisValidacao.emptyNumber(fsrpVO.getContrato().getCodigo()) && (UteisValidacao.emptyString(fsrpVO.getModalidade_Apresentar())
                                    || UteisValidacao.emptyString(fsrpVO.getDataInicio_Apresentar())
                                    || UteisValidacao.emptyString(fsrpVO.getDataAte_Apresentar())
                                    || UteisValidacao.emptyNumber(fsrpVO.getContrato().getDuracao_Apresentar())
                                    || UteisValidacao.emptyString(fsrpVO.getPlano_Apresentar())) ) {
                                fsrpVO.setContrato(contrato.consultarPorChavePrimariaComModalidade(fsrpVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                            }

                            FaturamentoSinteticoRelTO fsrTO = new FaturamentoSinteticoRelTO();
                            fsrTO.setMatriculaCliente(fsrpVO.getCliente().getMatricula());
                            fsrTO.setNomeResponsavelLancamento(fsrpVO.getNomeResponsavelLancamento());
                            fsrTO.setNomeResponsavelRecebimento(fsrpVO.getNomeResponsavelRecebimento());
                            fsrTO.setDescricaoProduto(fspVO.getDescricao());

                            fsrTO.setCodContrato(fsrpVO.getContrato_Apresentar());
                            fsrTO.setDataInicio(fsrpVO.getDataInicio_Apresentar());
                            fsrTO.setDataTermino(fsrpVO.getDataAte_Apresentar());
                            fsrTO.setDuracaoPlano(fsrpVO.getContrato().getDuracao_Apresentar());

                            for (ContratoModalidadeVO contratoModalidadeVO : fsrpVO.getContrato().getContratoModalidadeVOs()) {
                                ContratoModalidadeVO cMVO = contratoModalidadeDAO.consultarPorChavePrimaria(contratoModalidadeVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                                if (cMVO.getModalidade().getUtilizarTurma()) {
                                    for (Object contratoModalidadeTurmaVO : cMVO.getContratoModalidadeTurmaVOs()) {
                                        if (contratoModalidadeTurmaVO instanceof ContratoModalidadeTurmaVO) {
                                            ContratoModalidadeTurmaVO contratoModalidadeTurma = (ContratoModalidadeTurmaVO) contratoModalidadeTurmaVO;
                                            if (UteisValidacao.emptyString(fsrTO.getTurma())) {
                                                fsrTO.setTurma(contratoModalidadeTurma.getTurma().getDescricao());
                                            } else {
                                                fsrTO.setTurma(fsrTO.getTurma()+", " + contratoModalidadeTurma.getTurma().getDescricao());
                                            }
                                        }
                                    }
                                }
                            }

                            CategoriaVO categoria = categoriaDAO.consultarPorCodigo(fsrpVO.getCliente().getCategoria().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                            if(categoria != null){
                                fsrTO.setCategoria(categoria.getNome());
                            }

                            fsrTO.setModalidades(fsrpVO.getModalidade_Apresentar());
                            fsrTO.setNomePlano(fsrpVO.getPlano_Apresentar());
                            fsrTO.setSituacaoContrato(fsrpVO.getSituacaoContrato_Apresentar());
                            fsrTO.setDataLancamentoProduto(fsrpVO.getDataLancamentoMovProduto());
                            fsrTO.setValorCompetencia(fsrpVO.getValor());
                            fsrTO.setFormaPagApresentar(fsrpVO.getFormaPagApresentar());
                            fsrTO.setCondicaoPagamento(fsrpVO.getContrato().getContratoCondicaoPagamento().getCondicaoPagamento().getDescricao());

                            if (fsrpVO.getCliente().getPessoa() != null && !clienteVazio && !UteisValidacao.emptyString(fsrpVO.getCliente().getPessoa().getNome())) {
                                fsrTO.setCodCliente(fsrpVO.getCliente().getCodigo());
                                fsrTO.setNomeCliente(fsrpVO.getCliente().getPessoa().getNome());
                                fsrTO.setDataCadastroCliente(fsrpVO.getCliente().getPessoa().getDataCadastro());

                            } else if (clienteVazio && fsrpVO.getPessoa() != null && !UteisValidacao.emptyString(fsrpVO.getPessoa().getNome())) {
                                fsrTO.setNomeCliente(fsrpVO.getPessoa().getNome());
                                fsrTO.setCodCliente(fsrpVO.getPessoa().getCodigo());
                                fsrTO.setDataCadastroCliente(fsrpVO.getPessoa().getDataCadastro());
                            } else {
                                fsrTO.setNomeCliente(fsrpVO.getConsumidor());
                                fsrTO.setDataCadastroCliente(fsrpVO.getCliente().getPessoa().getDataCadastro());
                            }

                            fsrTO.setCodPlano(fsrpVO.getContrato().getPlano().getCodigo());
                            if (!UteisValidacao.emptyString(fsrpVO.getCliente().getEmpresa().getNome())) {
                                fsrTO.setNomeEmpresa(fsrpVO.getCliente().getEmpresa().getNome());
                            } else {
                                fsrTO.setNomeEmpresa(fsrpVO.getNomeEmpresa());
                            }

                            getListaExportavel().add(fsrTO);
                        }
                    }
                }
            }
        }
    }

    public void exportar(ActionEvent actionEvent) throws Exception {
        montarListaExportavel();
        ((ExportadorListaControle) getControlador(ExportadorListaControle.class)).exportar(actionEvent);
    }

    private Double retornaValor(Double valor) {
        if (valor == null)
            return Double.valueOf(0);

        return valor;
    }

    public void imprimirPDF()throws Exception{
        try{
            limparMsg();
            setMsgAlert("");
            Boolean imprimir = (Boolean) context().getExternalContext().getRequestMap().get("imprimir");

            EmpresaVO empre = new EmpresaVO();
            if (!UteisValidacao.emptyNumber(getFiltroEmpresa())) {
                empre = getFacade().getEmpresa().consultarPorChavePrimaria(getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            montarListaExportavel();

            if (isSomenteFaturamentoRecebido()) {
                validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_FATURAMENTO_RECEBIDO,listaExportavel.size(), getFiltros(),"pdf", "","");
            } else {
                validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_FATURAMENTO,listaExportavel.size(), getFiltros(),"pdf", "","");
            }

            setTipoRelatorio("PDF");
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nomeEmpresa", getNomeEmpresaSelecionada(getFiltroEmpresa()));
            params.put("enderecoEmpresa", empre.getEndereco());
            params.put("nomeCidade", empre.getCidade_Apresentar());
            if (isSomenteFaturamentoRecebido()) {
                params.put("nomeRelatorio", "FaturamentoRecebidoPorPeriodo");
                params.put("titulo", "Faturamento Recebido por Período");
            } else {
                params.put("nomeRelatorio", "FaturamentoPorPeriodo");
                params.put("titulo", "Faturamento por Período");
            }
            params.put("nomeDesignIReport", getDesignIReportRelatorio());
            params.put("listaObjetos",listaExportavel);

            double vlrTotal = 0;
            int qtdTotal = 0;
            String periodo = "";
            String periodoValor = "";
            Map<String, Double> totalizador = new HashMap<>();
            for (FaturamentoSinteticoTipoProdutoVO fstpVO : listaTipoProdutoVO) {
                for (FaturamentoSinteticoProdutoVO fspVO : fstpVO.getListaProduto()) {
                    if (fspVO.getDescricao().equals("TOTALIZADOR")) {
                        for (FaturamentoSinteticoProdutoMesVO fspmVO : fspVO.getListaProdutoXMes()) {
                            if (fspmVO==null)
                                continue;

                            qtdTotal += fspmVO.getQtd();
                            vlrTotal += fspmVO.getValor();

                            if (this.competencia && fspmVO.getListaResumoPessoa().size() != 0) {
                                SimpleDateFormat sdf = new SimpleDateFormat("MMMM - yyyy", Calendario.getDefaultLocale());
                                String mesAno = sdf.format(fspmVO.getListaResumoPessoa().get(0).getDataLancamentoMovProduto());

                                String valor = getEmpresaLogado().getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(fspmVO.getValor());

                                periodo += mesAno + "\n";
                                periodoValor += valor + "\n";
                            }

                            if (this.totalizador) {
                                for (FaturamentoSinteticoResumoPessoaVO fsrp : fspmVO.getListaResumoPessoa()) {
                                    totalizador.put(fsrp.getFormaPagApresentar() + "QTD", fsrp.getQuantidade() + retornaValor(totalizador.get(fsrp.getFormaPagApresentar() + "QTD")));
                                    totalizador.put(fsrp.getFormaPagApresentar() + "VLR", fsrp.getValor() + retornaValor(totalizador.get(fsrp.getFormaPagApresentar() + "VLR")));
                                }
                            }
                        }
                    }
                }
            }
            params.put("emAbertoQtd", qtdTotal);

            // Apresentar totalizador pro formas de pagamento
            if (this.totalizador) {
                params.put("totalizador", true);

                Map<String, Double> ordenaTotalizador = new TreeMap<>(totalizador);
                String texto;
                Integer quantidade = 0;
                String especie = "", especieQtd = "", especieVlr = "";

                for (Map.Entry<String, Double> totaliza : ordenaTotalizador.entrySet()) {
                    texto = totaliza.getKey();
                    if (texto.substring(texto.length() - 3).equals("VLR")) {
                        especie += texto.replace("QTD","").replace("VLR","") + "\n";
                        especieVlr += getEmpresaLogado().getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(totaliza.getValue()) + "\n";
                        especieQtd += quantidade + "\n";

                        quantidade = 0;
                    }else{
                        quantidade = totaliza.getValue().intValue();
                    }
                }

                especie += "TOTAL:";
                especieVlr += getEmpresaLogado().getMoeda() + " " + Formatador.formatarValorMonetarioSemMoeda(vlrTotal);
                especieQtd += qtdTotal;

                params.put("totalizar", true);
                params.put("especie", especie);
                params.put("especieVlr", especieVlr);
                params.put("especieQtd", especieQtd);
            }

            // Apresentar competencia de meses futuros
            if (this.competencia) {
                params.put("competencia", true);
                params.put("periodo", periodo);
                params.put("periodoValor", periodoValor);
            }

            apresentarRelatorioObjetos(params);
            setMensagemID("");
            setMensagemDetalhada("", "");
            setSucesso(false);
            setErro(false);
            ExportadorListaControle exp = (ExportadorListaControle)getControlador("ExportadorListaControle");
            if (isSomenteFaturamentoRecebido()){
                setMsgAlert("abrirPopup('../UpdateServlet?op=downloadfile&file="+ exp.getNomeArquivoRelatorioGeradoAgora()+"&mimetype=application/pdf','FaturamentoRecebidoPorPeriodo', 800,200);");
            }  else {
                setMsgAlert("abrirPopup('../UpdateServlet?op=downloadfile&file="+ exp.getNomeArquivoRelatorioGeradoAgora()+"&mimetype=application/pdf','FaturamentoPorPeriodo', 800,200);");
            }
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void montarListaSelectItemCategorias() throws Exception {
        //listaCategoriaProduto.clear();
        this.listaCategorias = new ArrayList<>();
        try {
            List<CategoriaVO> categorias = getFacade().getCategoria().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_TODOS);

            for (CategoriaVO obj : categorias) {
                this.listaCategorias.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }

            this.listaCategorias = Ordenacao.ordenarLista(listaCategorias, "label");
            this.listaCategorias.add(0, new SelectItem(0, "Selecione"));
        } catch (Exception e) {
            e.getStackTrace();
            this.listaCategorias = new ArrayList<>();
            this.listaCategorias.add(new SelectItem(0, "Selecione"));
        }
    }

    public void montarTurmas() {
        //listaTurma.clear();
        List<TurmaVO> turmas;
        this.listaTurma = new ArrayList<>();

        try {
            if (getModalidadeSelecionada() != 0) {
                turmas = getFacade().getTurma().consultarPorCodigoModalidadeEOuProfessor(false, false, this.modalidadeSelecionada, 0, this.filtroEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                turmas = getFacade().getTurma().consultar(false, false, this.modalidadeSelecionada, this.filtroEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, false);
            }

            for (TurmaVO turma : turmas) {
                this.listaTurma.add(new SelectItem(turma.getCodigo().intValue(), turma.getIdentificador()));
            }

            this.listaTurma = Ordenacao.ordenarLista(listaTurma, "label");
            this.listaTurma.add(0, new SelectItem(0, "Selecione"));
        } catch (Exception e) {
            e.getStackTrace();
            this.listaTurma = new ArrayList<>();
            this.listaTurma.add(new SelectItem(0, "Selecione"));
        }
    }

    public void montarModalidadeSelectItem() {
        List<ModalidadeVO> modalidades;
        this.listaModalidade = new ArrayList<>();

        try {
            modalidades = getFacade().getModalidade().consultarTodasModalidadesComLimite(this.filtroEmpresa, true, true);
            for (ModalidadeVO modalidade : modalidades) {
                this.listaModalidade.add(new SelectItem(modalidade.getCodigo(), modalidade.getNome()));
            }

            this.listaModalidade = Ordenacao.ordenarLista(listaModalidade, "label");
            this.listaModalidade.add(0, new SelectItem(0, "Selecione"));
        } catch (Exception e) {
            e.getStackTrace();
            this.listaModalidade = new ArrayList<>();
            this.listaModalidade.add(new SelectItem(0, "Selecione"));
        }
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "FaturamentoPorPeriodo.jrxml");
    }

    public FaturamentoSinteticoRel getFaturamentoSinteticoRel() {
        return faturamentoSinteticoRel;
    }

    public void setFaturamentoSinteticoRel(FaturamentoSinteticoRel faturamentoSinteticoRel) {
        this.faturamentoSinteticoRel = faturamentoSinteticoRel;
    }

    public String getCampoConsultarOperador() {
        return campoConsultarOperador;
    }

    public void setCampoConsultarOperador(String campoConsultarOperador) {
        this.campoConsultarOperador = campoConsultarOperador;
    }

    public String getValorConsultarOperador() {
        return valorConsultarOperador;
    }

    public void setValorConsultarOperador(String valorConsultarOperador) {
        this.valorConsultarOperador = valorConsultarOperador;
    }

    public String getDataInicioPrm() {
        return dataInicioPrm;
    }

    public void setDataInicioPrm(String dataInicioPrm) {
        this.dataInicioPrm = dataInicioPrm;
    }

    public String getDataTerminoPrm() {
        return dataTerminoPrm;
    }

    public void setDataTerminoPrm(String dataTerminoPrm) {
        this.dataTerminoPrm = dataTerminoPrm;
    }

    public boolean getAlterarHorario() {
        return alterarHorario;
    }

    public void setAlterarHorario(boolean alterarHorario) {
        this.alterarHorario = alterarHorario;
    }

    public boolean getAulaAvulsa() {
        return aulaAvulsa;
    }

    public void setAulaAvulsa(boolean aulaAvulsa) {
        this.aulaAvulsa = aulaAvulsa;
    }

    public boolean getDiaria() {
        return diaria;
    }

    public void setDiaria(boolean diaria) {
        this.diaria = diaria;
    }

    public boolean getFreePass() {
        return freePass;
    }

    public void setFreePass(boolean freePass) {
        this.freePass = freePass;
    }

    public boolean getManutencaoModalidade() {
        return manutencaoModalidade;
    }

    public void setManutencaoModalidade(boolean manutencaoModalidade) {
        this.manutencaoModalidade = manutencaoModalidade;
    }

    public boolean getMatriculaRenovacaoRematricula() {
        return matrRenovRemat;
    }

    public void setMatriculaRenovacaoRematricula(boolean matrRenovRemat) {
        this.matrRenovRemat = matrRenovRemat;
    }

    public boolean getMesReferenciaPlano() {
        return mesReferenciaPlano;
    }

    public void setMesReferenciaPlano(boolean mesReferenciaPlano) {
        this.mesReferenciaPlano = mesReferenciaPlano;
    }

    public boolean getProdutoEstoque() {
        return produtoEstoque;
    }

    public void setProdutoEstoque(boolean produtoEstoque) {
        this.produtoEstoque = produtoEstoque;
    }

    public boolean getRetornoTrancamento() {
        return retornoTrancamento;
    }

    public void setRetornoTrancamento(boolean retornoTrancamento) {
        this.retornoTrancamento = retornoTrancamento;
    }

    public boolean getServico() {
        return servico;
    }

    public void setServico(boolean servico) {
        this.servico = servico;
    }

    public boolean getTrancamento() {
        return trancamento;
    }

    public void setTrancamento(boolean trancamento) {
        this.trancamento = trancamento;
    }

    public List<FaturamentoSinteticoTipoProdutoVO> getListaTipoProdutoVO() {
        return listaTipoProdutoVO;
    }

    public void setListaTipoProdutoVO(List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO) {
        this.listaTipoProdutoVO = listaTipoProdutoVO;
    }
    public List<PeriodoMensal> getPeriodos() {
        return periodos;
    }

    public void setPeriodos(List<PeriodoMensal> periodos) {
        this.periodos = periodos;
    }

    public FaturamentoSinteticoProdutoMesVO getFaturamentoSinteticoProdutoMesVO() {
        return faturamentoSinteticoProdutoMesVO;
    }

    public void setFaturamentoSinteticoProdutoMesVO(FaturamentoSinteticoProdutoMesVO faturamentoSinteticoProdutoMesVO) {
        this.faturamentoSinteticoProdutoMesVO = faturamentoSinteticoProdutoMesVO;
    }

    public List<FaturamentoSinteticoResumoPessoaVO> getListaProdutoMesVOs() {
        return listaProdutoMesVOs;
    }

    public void setListaProdutoMesVOs(List<FaturamentoSinteticoResumoPessoaVO> listaProdutoMesVOs) {
        this.listaProdutoMesVOs = listaProdutoMesVOs;
    }

    public boolean apresentarLista() {
        FaturamentoSinteticoTipoProdutoVO obj = (FaturamentoSinteticoTipoProdutoVO) context().getExternalContext().getRequestMap().get("cidade");
        return obj.getListaProduto().size() != 0;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public List<ColaboradorVO> getListaConsultarOperador() {
        return listaConsultarOperador;
    }

    public void setListaConsultarOperador(List<ColaboradorVO> listaConsultarOperador) {
        this.listaConsultarOperador = listaConsultarOperador;
    }

    public ColaboradorVO getOperador() {
        return operador;
    }

    public void setOperador(ColaboradorVO operador) {
        this.operador = operador;
    }

    public List<ColaboradorVO> getListaConsultarColaborador() {
        return listaConsultarColaborador;
    }

    public void setListaConsultarColaborador(List<ColaboradorVO> listaConsultarColaborador) {
        this.listaConsultarColaborador = listaConsultarColaborador;
    }

    public String getCampoConsultarColaborador() {
        return campoConsultarColaborador;
    }

    public void setCampoConsultarColaborador(String campoConsultarColaborador) {
        this.campoConsultarColaborador = campoConsultarColaborador;
    }

    public String getValorConsultarColaborador() {
        return valorConsultarColaborador;
    }

    public void setValorConsultarColaborador(String valorConsultarColaborador) {
        this.valorConsultarColaborador = valorConsultarColaborador;
    }

    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public FaturamentoSinteticoTipoProdutoVO getResumo() {
        return resumo;
    }

    public void setResumo(FaturamentoSinteticoTipoProdutoVO resumo) {
        this.resumo = resumo;
    }

    public String getAgrupamento() {
        return agrupamento;
    }

    public void setAgrupamento(String agrupamento) {
        this.agrupamento = agrupamento;
    }

    public String getTipoProduto_Apresentar(String tipoProduto) {
        if (tipoProduto == null) {
            tipoProduto = "";
        }
        if (tipoProduto.equals("MARERN")) {
            return "Matrícula, Rematrícula, Renovação";
        }
        if (tipoProduto.equals("PE")) {
            return "Produto Estoque";
        }
        if (tipoProduto.equals("PM")) {
            return "Mês de Referência Plano";
        }
        if (tipoProduto.equals("SE")) {
            return "Serviço";
        }
        if (tipoProduto.equals("CD")) {
            return "Convênio de Desconto";
        }
        if (tipoProduto.equals("DE")) {
            return "Desconto";
        }
        if (tipoProduto.equals("DV")) {
            return "Devolução";
        }
        if (tipoProduto.equals("TR")) {
            return "Trancamento";
        }
        if (tipoProduto.equals("RT")) {
            return "Retorno Trancamento";
        }
        if (tipoProduto.equals("AA")) {
            return "Aula Avulsa";
        }
        if (tipoProduto.equals("DI")) {
            return "Diária";
        }
        if (tipoProduto.equals("FR")) {
            return "FreePass";
        }
        if (tipoProduto.equals("AH")) {
            return "Alterar - Horário";
        }
        if (tipoProduto.equals("MM")) {
            return "Manutenção Modalidade";
        }
        if (tipoProduto.equals("MC")) {
            return "Manutenção Conta Corrente";
        }
        if (tipoProduto.equals("DR")) {
            return "Desconto em Renovação Antecipada";
        }
        if (tipoProduto.equals("TP")) {
            return "Taxa de Personal";
        }
        if (tipoProduto.equals(TipoProduto.CREDITO_PERSONAL.getCodigo())) {
            return "Crédito Personal";
        }
        if (tipoProduto.equals("SS")) {
            return "Produto do Tipo Sessão";
        }
        if (tipoProduto.equals("QU")) {
            return "Quitação - Cancelamento";
        }
        if (tipoProduto.equals("MC")) {
            return "Pgto Saldo Devedor";
        }
        if (tipoProduto.equals("AC")) {
            return "Acerto C/C Aluno";
        }
        if (tipoProduto.equals(TipoProduto.LOCACAO.getCodigo())) {
            return "Locação";
        }

        if (tipoProduto.equals(TipoProduto.ARMARIO.getCodigo())) {
            return "Armário";
        }
        if (tipoProduto.equals(TipoProduto.DESAFIO.getCodigo())) {
            return "Desafio";
        }

        if (tipoProduto.equals(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo())) {
            return TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getDescricao();
        }

        if (tipoProduto.equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())) {
            return TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getDescricao();
        }
        if (tipoProduto.equals(TipoProduto.ATESTADO.getCodigo())) {
            return TipoProduto.ATESTADO.getDescricao();
        }
        return (tipoProduto);
    }

    public boolean isTaxaPersonal() {
        return taxaPersonal;
    }

    public void setTaxaPersonal(boolean taxaPersonal) {
        this.taxaPersonal = taxaPersonal;
    }

    public boolean isProdutoSessao() {
        return produtoSessao;
    }

    public void setProdutoSessao(boolean produtoSessao) {
        this.produtoSessao = produtoSessao;
    }

    public boolean isPgtoSaldoDevedor() {
        return pgtoSaldoDevedor;
    }

    public void setPgtoSaldoDevedor(boolean pgtoSaldoDevedor) {
        this.pgtoSaldoDevedor = pgtoSaldoDevedor;
    }

    public boolean isQuitacaoCancelamento() {
        return quitacaoCancelamento;
    }

    public void setQuitacaoCancelamento(boolean quitacaoCancelamento) {
        this.quitacaoCancelamento = quitacaoCancelamento;
    }

    public boolean isAcertoCCAluno() {
        return acertoCCAluno;
    }

    public void setAcertoCCAluno(boolean acertoCCAluno) {
        this.acertoCCAluno = acertoCCAluno;
    }

    public List<FaturamentoSinteticoRelTO> getListaExportavel() {
        return listaExportavel;
    }

    public void setListaExportavel(List<FaturamentoSinteticoRelTO> listaExportavel) {
        this.listaExportavel = listaExportavel;
    }

    public boolean isSomenteFaturamentoRecebido() {
        return somenteFaturamentoRecebido;
    }

    public void setSomenteFaturamentoRecebido(boolean somenteFaturamentoRecebido) {
        this.somenteFaturamentoRecebido = somenteFaturamentoRecebido;
    }

    public boolean isCreditoContaCorrente() {
        return creditoContaCorrente;
    }

    public void setCreditoContaCorrente(boolean creditoContaCorrente) {
        this.creditoContaCorrente = creditoContaCorrente;
    }

    public boolean isContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(boolean contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public String getFiltro() {
        StringBuilder filtro = new StringBuilder("Período ").append(Uteis.getDataAplicandoFormatacao(getFaturamentoSinteticoRel().getDataInicio(), "dd/MM/yyyy")).append(" ate ").append(Uteis.getDataAplicandoFormatacao(getFaturamentoSinteticoRel().getDataTermino(), "dd/MM/yyyy"));
        if (getOperador().getPessoa().getNome() != null && !getOperador().getPessoa().getNome().equals("")) {
            if (isSomenteFaturamentoRecebido()) {
                filtro.append(" Quem lançou o pagamento: ").append(getOperador().getPessoa().getNome());
            } else {
                filtro.append(" Quem lançou contrato: ").append(getOperador().getPessoa().getNome());
            }
        }
        if (getColaborador().getPessoa().getNome() != null && !getColaborador().getPessoa().getNome().equals("")) {
            filtro.append(" Consultor Responsável: ").append(getColaborador().getPessoa().getNome());
        }
        return filtro.toString();
    }

    public Boolean getTotalizador() {
        if (totalizador == null){
            totalizador = false;
        }
        return totalizador;
    }

    public Boolean getCompetencia() {
        if (competencia == null){
            competencia = false;
        }
        return competencia;
    }

    public Boolean getBolsa() {
        if (bolsa == null){
            bolsa = false;
        }
        return bolsa;
    }

    public HtmlDataTable getDadosTable() {
        return dadosTable;
    }

    public void setDadosTable(HtmlDataTable dadosTable) {
        this.dadosTable = dadosTable;
    }

    public void setTotalizador(Boolean totalizador) {
        this.totalizador = totalizador;
    }

    public void setCompetencia(Boolean competencia) {
        this.competencia = competencia;
    }

    public void setBolsa(Boolean bolsa) {
        this.bolsa = bolsa;
    }

    public boolean isArmario() {
        return armario;
    }

    public void setArmario(boolean armario) {
        this.armario = armario;
    }

    public boolean isCreditoPersonal() {
        return creditoPersonal;
    }

    public void setCreditoPersonal(boolean creditoPersonal) {
        this.creditoPersonal = creditoPersonal;
    }

    public boolean isTaxaAdesao() {
        return taxaAdesao;
    }

    public void setTaxaAdesao(boolean taxaAdesao) {
        this.taxaAdesao = taxaAdesao;
    }

    public boolean isTaxaAnuidade() {
        return taxaAnuidade;
    }

    public void setTaxaAnuidade(boolean taxaAnuidade) {
        this.taxaAnuidade = taxaAnuidade;
    }

    public boolean isAtestado() {
        return atestado;
    }

    public void setAtestado(boolean atestado) {
        this.atestado = atestado;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public boolean isIncluirDevolucaoCheque() {
        return incluirDevolucaoCheque;
    }

    public void setIncluirDevolucaoCheque(boolean incluirDevolucaoCheque) {
        this.incluirDevolucaoCheque = incluirDevolucaoCheque;
    }

    public boolean isIgnorarEdicaoPagamento() {
        return ignorarEdicaoPagamento;
    }

    public void setIgnorarEdicaoPagamento(boolean ignorarEdicaoPagamento) {
        this.ignorarEdicaoPagamento = ignorarEdicaoPagamento;
    }

    public String getAbrirRelatorio() {
        return abrirRelatorio;
    }

    public void setAbrirRelatorio(String abrirRelatorio) {
        this.abrirRelatorio = abrirRelatorio;
    }

    public void prepararAbrirRelatorio(int tipo){
        if(tipo == 0){
            ExportadorListaControle exp = (ExportadorListaControle)getControlador("ExportadorListaControle");

            abrirRelatorio = "abrirPopup('../UpdateServlet?op=downloadfile&file="+exp.getFileName()+"&mimetype=application/vnd.ms-excel','Transacoes', 800,200);";
            abrirRelatorio += exp.getMsgAlert();
            limparMsg();
        }else if(tipo == 1){
            abrirRelatorio =   "location.href=\"" + getNomeArquivoRelatorioGeradoAgora() + "\"";
            limparMsg();
        }
    }

    // todo Inserir filtro para consulta do relatório de faturamento recebido
    public Map<String, Integer> getFiltroFaturamentoRecebido() {
        Map<String, Integer> filtro = new HashMap<>();
        filtro.put("modalidadeSelecionada", this.modalidadeSelecionada != null ? this.modalidadeSelecionada : 0);
        filtro.put("turmaSelecionada", this.turmaSelecionada != null ? this.turmaSelecionada : 0);
        filtro.put("formaPagamentoSelecionada", this.formaPagamentoSelecionada != null ? this.formaPagamentoSelecionada : 0);
        filtro.put("categoriaSelecionada", this.categoriaSelecionada != null ? this.categoriaSelecionada : 0);

        return filtro;
    }

    public boolean isDesafio() {
        return desafio;
    }

    public void setDesafio(boolean desafio) {
        this.desafio = desafio;
    }


    public boolean isDesconsiderarProdutoCancelado() {
        return desconsiderarProdutoCancelado;
    }

    public void setDesconsiderarProdutoCancelado(boolean desconsiderarProdutoCancelado) {
        this.desconsiderarProdutoCancelado = desconsiderarProdutoCancelado;
    }

    public boolean isSomenteContratoVendaAvulsaMesmoMesReferencia() {
        return somenteContratoVendaAvulsaMesmoMesReferencia;
    }

    public void setSomenteContratoVendaAvulsaMesmoMesReferencia(boolean somenteContratoVendaAvulsaMesmoMesReferencia) {
        this.somenteContratoVendaAvulsaMesmoMesReferencia = somenteContratoVendaAvulsaMesmoMesReferencia;
    }

    public boolean isEvento() {
        return produtoEstoque ? evento : false;
    }

    public void setEvento(boolean evento) {
        this.evento = evento;
    }

    public boolean getBioTotem() {
        return bioTotem;
    }

    public void setBioTotem(boolean bioTotem) {
        this.bioTotem = bioTotem;
    }

    public boolean getConsultaNutricional() {
        return consultaNutricional;
    }

    public void setConsultaNutricional(boolean consultaNutricional) {
        this.consultaNutricional = consultaNutricional;
    }

    public boolean isTaxaRenegociacao() {
        return taxaRenegociacao;
    }

    public void setTaxaRenegociacao(boolean taxaRenegociacao) {
        this.taxaRenegociacao = taxaRenegociacao;
    }

    public boolean isLocacao() { return locacao; }

    public void setLocacao(boolean locacao) { this.locacao = locacao; }

    public Integer getModalidadeSelecionada() {
        return modalidadeSelecionada;
    }

    public void setModalidadeSelecionada(Integer modalidadeSelecionada) {
        this.modalidadeSelecionada = modalidadeSelecionada;
    }

    public void setListaModalidade(List<SelectItem> selectItensModalidade) {
        this.listaModalidade = selectItensModalidade;
    }

    public List<SelectItem> getListaModalidade() {
        return listaModalidade;
    }

    public Integer getTurmaSelecionada() {
        return turmaSelecionada;
    }

    public void setTurmaSelecionada(Integer turmaSelecionada) {
        this.turmaSelecionada = turmaSelecionada;
    }

    public List<SelectItem> getListaTurma() {
        return listaTurma;
    }

    public void setListaTurma(List<SelectItem> listaTurma) {
        this.listaTurma = listaTurma;
    }

    public Integer getCategoriaSelecionada() {
        return categoriaSelecionada;
    }

    public void setCategoriaSelecionada(Integer categoriaSelecionada) {
        this.categoriaSelecionada = categoriaSelecionada;
    }

    public List<SelectItem> getListaCategorias() {
        return listaCategorias;
    }

    public void setListaCategorias(List<SelectItem> listaCategorias) {
        this.listaCategorias = listaCategorias;
    }

    public List<CategoriaProdutoVO> getCategorias() {
        return categorias;
    }

    public void setCategorias(List<CategoriaProdutoVO> categorias) {
        this.categorias = categorias;
    }

    public Integer getFormaPagamentoSelecionada() {
        return formaPagamentoSelecionada;
    }

    public void setFormaPagamentoSelecionada(Integer formaPagamentoSelecionada) {
        this.formaPagamentoSelecionada = formaPagamentoSelecionada;
    }

    public List<SelectItem> getListaFormaPagamento() {
        return listaFormaPagamento;
    }

    public void setListaFormaPagamento(List<SelectItem> listaFormaPagamento) {
        this.listaFormaPagamento = listaFormaPagamento;
    }
}
