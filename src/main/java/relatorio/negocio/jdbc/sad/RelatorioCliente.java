package relatorio.negocio.jdbc.sad;

import br.com.pactosolucoes.comuns.to.ClientesRelConsultaTO;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.GrupoSituacaoEnum;
import br.com.pactosolucoes.enumeradores.SituacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.contrato.ConvenioDescontoConfiguracaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisSQL;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import relatorio.negocio.comuns.basico.ClienteRelatorioTO;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * 
 */
public class RelatorioCliente extends SuperEntidade {

	ClienteRelatorioTO recipiente = new ClienteRelatorioTO();
	Map<String, List<Integer>> historicosContrato = new HashMap<String, List<Integer>>();
	Map<String, List<Integer>> operacoesContrato = new HashMap<String, List<Integer>>();
	List<String> contratoModalidade = new ArrayList<String>();
	List<String> clienteModalidade = new ArrayList<String>();
	Map<String, ConvenioDescontoConfiguracaoVO> convenioDesconto = new HashMap<String, ConvenioDescontoConfiguracaoVO>();
        private String dadosAcessoVisitantes= "";

	public RelatorioCliente() throws Exception {
		super();
	}

	public void pesquisarConvenio() throws Exception{
		List<ConvenioDescontoConfiguracaoVO> lista = getFacade().getConvenioDescontoConfiguracao().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
		for(ConvenioDescontoConfiguracaoVO cdc : lista){
			convenioDesconto.put(""+cdc.getConvenioDesconto()+cdc.getDuracao(), cdc);
		}
	}
	public void pesquisarSituacoes(ClientesRelConsultaTO filtros) throws Exception{
		String sql = "SELECT tipohistorico, contrato FROM historicocontrato WHERE (? BETWEEN datainiciosituacao AND datafinalsituacao )"+
					 " OR (? BETWEEN datainiciosituacao AND datafinalsituacao ) or (? >= datainiciosituacao and ? <= datafinalsituacao)" ;
		Declaracao dc = new Declaracao(sql, con);
		dc.setDate(1, Uteis.getDataJDBC(filtros.getDataInicial()));
		dc.setDate(2, Uteis.getDataJDBC(filtros.getDataFinal()));
                dc.setDate(3, Uteis.getDataJDBC(filtros.getDataInicial()));
                dc.setDate(4, Uteis.getDataJDBC(filtros.getDataFinal()));
		ResultSet rs = dc.executeQuery();
		montarDadosHistoricoOperacao(rs,historicosContrato, "tipohistorico" );
	}
	
	public void pesquisarOperacoes(ClientesRelConsultaTO filtros) throws Exception{
		String sql = "SELECT tipooperacao, contrato FROM contratooperacao WHERE ? >= datainicioefetivacaooperacao AND ? <= datafimefetivacaooperacao"
                        + " and tipooperacao not in('AM','TS','TE','AH','EM','IM','AC','BA','BR','RT');";
                // desconsidera opera��es de transferencia de entrada(TE), transferencia de saida(TS), alteracao de hor�rio(AH), exculsao de modalidade (EM),
                // alteracao modalidade (AM), inclusao de modalidade (IM), alteracao de contrato (AC), bonus de adicao (BA), bonus de reducao(BR)
                //
		Declaracao dc = new Declaracao(sql, con);
		dc.setDate(1, Uteis.getDataJDBC(filtros.getDataInicial()));
		dc.setDate(2, Uteis.getDataJDBC(filtros.getDataFinal()));
		ResultSet rs = dc.executeQuery();
		montarDadosHistoricoOperacao(rs,operacoesContrato, "tipooperacao" );
	}
	
	private void montarDadosHistoricoOperacao(ResultSet rs, Map<String, List<Integer>> mapa, String coluna) throws Exception{
		while (rs.next()){
			String tipo = rs.getString(coluna); 
			List<Integer> lista = mapa.get(tipo);
			if(lista == null)
				lista = new ArrayList<Integer>();
			lista.add(rs.getInt("contrato"));
			mapa.put(tipo, lista);
		}
	}
	/**
	 * Respons�vel por montar a sql do relat�rio de clientes
	 * 
	 * <AUTHOR> 04/04/2011
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> montarRelatorioClientes(ClientesRelConsultaTO filtros) throws Exception {
		limparListas();
		StringBuilder sql = new StringBuilder();
                preencherDadosAcessoVisitantes(filtros);
		
		sql.append("SELECT   REL.* \n");
		//selecionar dados de cliente, pessoa, vinculos e todos relacionados ao contrato
		sql.append("FROM     ( SELECT c.matricula           , \n");
		sql.append("                 p.codigo AS pessoa     , \n");
		sql.append("                 p.nome                 , \n");
		sql.append("                 em.nome AS nomeEmpresa , \n");
		sql.append("                 c.empresa              , \n");
		sql.append("                 con.*                  , \n");
		sql.append("                 faturamento.totalfinal , \n");
                sql.append("                 pracesso.codigo as periodoacessocodigo, \n");
		sql.append(" (SELECT   max(m.totalfinal) AS competencia				\n");
		sql.append("  FROM     movproduto m \n");
		sql.append("   INNER JOIN produto p \n");
		sql.append(" ON       m.produto = p.codigo and m.contrato = con.codigo\n");
		sql.append("    WHERE    p.tipoproduto      = 'PM'  limit 1) as competencia,  \n");
		sql.append("                 vinculos.vinculo       , \n");
		sql.append("  vinculos.nome AS colaborador          , \n");
        sql.append("  vinculos.codigoColaborador          , \n");
        sql.append("  vinculos.tipovinculo          , \n");

		//definir situa��o do contrato
		sql.append(" CASE \n");
		//visitantes
		//verificar se o cliente n�o possui acesso de visitante
		sql.append("  WHEN pracesso.tipoacesso IS NOT NULL AND con.codigo IS NULL \n");
		sql.append("  THEN pracesso.tipoacesso \n");
		//verificar se o cliente n�o possui nenhum contrato relacionado
		sql.append("  WHEN con.codigo IS NULL \n");
		sql.append("  THEN 'VI' \n");
		
		//contratos ativos
		sql.append("  WHEN\n");
		//caso o usuario escolha filtrar a data de lan�amento esta condi��o n�o deve ser inserida no sql
		if(!filtros.getFiltrarDataLancamento()){
			sql.append("  ( ? BETWEEN con.vigenciaDe AND con.vigenciaAteAjustada OR ");
			sql.append("   ? BETWEEN con.vigenciaDe AND con.vigenciaAteAjustada ) AND ");
		}
			 
		
		sql.append("  not exists \n");
		sql.append("  (SELECT h.contrato FROM historicocontrato h WHERE h.contrato = con.codigo and h.tipohistorico IN ('CA','DE','VE') \n");
		
		sql.append(" AND ((? BETWEEN h.datainiciosituacao  and h.datafinalsituacao) or (? BETWEEN h.datainiciosituacao  and h.datafinalsituacao) "); 
		sql.append(" OR (h.datafinalsituacao >= ? AND h.datainiciosituacao <= ? ))) ");
		
		sql.append("  AND not exists (SELECT o.contrato FROM  contratooperacao o \n");
		sql.append("  WHERE o.contrato = con.codigo and   o.tipooperacao IN ('TR','TV') \n");
		sql.append("  AND     ? >= o.datainicioefetivacaooperacao \n");
		sql.append("  AND     ? <= o.datafimefetivacaooperacao )");
		sql.append("  THEN 'NO'");
		//contratos trancados
		//verificar se o contrato est� trancado
		sql.append("  WHEN (SELECT COUNT(codigo) FROM contratooperacao o ");
		sql.append("  WHERE   o.tipooperacao IN ('TR') \n");
		sql.append("  AND     ? >= o.datainicioefetivacaooperacao \n");
		sql.append("  AND     ? <= o.datafimefetivacaooperacao ");
		sql.append("  AND     contrato = con.codigo ) > 0");
		sql.append("  THEN 'TR'  \n");
                
                //contratos trancados
		//verificar se o contrato est� trancado
		sql.append("  WHEN (SELECT COUNT(codigo) FROM contratooperacao o ");
		sql.append("  WHERE   o.tipooperacao IN ('TV') \n");
		sql.append("  AND     ? >= o.datainicioefetivacaooperacao \n");
		sql.append("  AND     ? <= o.datafimefetivacaooperacao ");
		sql.append("  AND     contrato = con.codigo ) > 0");
		sql.append("  THEN 'TV'  \n");
		
		//caso o contrato n�o se encaixe nos ativos e n�o esteja trancado, mas possui renova��o tratar como renovado
		sql.append(" WHEN (SELECT COUNT(codigo) FROM contrato WHERE contratobaseadorenovacao = con.codigo) > 0 \n");
		sql.append(" THEN 'RN' \n");
		//contratos inativos
		//verificar se o contrato est� inativo
		sql.append("  WHEN  \n");
		sql.append("  (SELECT COUNT(codigo) FROM historicocontrato \n");  
		sql.append("   WHERE contrato = con.codigo AND tipohistorico IN ('CA','VE','DE'))>0 \n");
		sql.append("  THEN (SELECT tipohistorico FROM historicocontrato  \n");
		sql.append("  WHERE contrato = con.codigo AND tipohistorico IN ('CA','VE','DE')  and ((datainiciosituacao between ? and ?) OR (datafinalsituacao BETWEEN ? AND ? ) OR (? BETWEEN datainiciosituacao AND datafinalsituacao )) \n");
		sql.append("  ORDER BY datafinalsituacao DESC LIMIT 1) \n");
		
//		sql.append("  WHEN con.vigenciaateajustada < ? ");
//		sql.append("  THEN 'VE' ");
		
		sql.append("                 END AS situacaofinal,cc.nome categoriaCliente, \n");
		sql.append("                 e.email\n");
		sql.append("         FROM    cliente c \n");
		sql.append("                 INNER JOIN pessoa p \n");
		sql.append("                 ON      p.codigo = c.pessoa \n");
		sql.append("				 INNER JOIN empresa em \n");
		sql.append("				 ON      em.codigo = c.empresa");
		sql.append("                 LEFT JOIN email e \n");
		sql.append("                 ON      e.pessoa = p.codigo \n");
		sql.append("                 LEFT JOIN categoria cc \n");
		sql.append("                 ON      cc.codigo = c.categoria \n");
		sql.append("                 LEFT JOIN \n");
		//consultar dados de contrato
		dadosContrato(filtros, sql);
		sql.append("                         AS con \n");
		sql.append("                 ON      con.pessoa = p.codigo \n");
		
		//---------------- consultar dados de vinculo com colaboradores
		
		sql.append("                 LEFT JOIN \n");
		dadosVinculo(sql);
		sql.append("                 ON      vinculos.cliente = c.codigo \n");
		
		//DADOS DE FATURAMENTO
		sql.append("                 LEFT JOIN \n");
		dadosProduto(sql);
		sql.append("                 ON      faturamento.contrato = con.codigo \n");
		//DADOS DE ACESSO (VISITANTES)
		sql.append("                 LEFT JOIN \n");
		sql.append("                 periodoacessocliente pracesso on pracesso.pessoa =  c.pessoa and  pracesso.tipoacesso IN (").append(getDadosAcessoVisitantes().equals("")? "'nenhum'" : getDadosAcessoVisitantes()).append(") and \n");
                sql.append("        ((pracesso.datainicioacesso between ? and ?)"); 
                sql.append("  or (pracesso.datafinalacesso between ? and ?)"); 
                sql.append(" or (pracesso.datainicioacesso < ? and pracesso.datafinalacesso > ?))");
		
		if (filtros.isSemParcelasVencidas()) {
			sql.append(" where not exists (select null from movparcela m2 where m2.pessoa = c.pessoa and m2.situacao = 'EA' and m2.datavencimento::date < '" + Uteis.getDataFormatoBD(filtros.getDataInicial()) + "')");
		}

		sql.append("         )");
		sql.append("         AS REL \n");
		if(!UteisValidacao.emptyNumber(filtros.getCodigoEmpresa())){
			sql.append(" WHERE REL.empresa = ? \n");
		}else{
            sql.append(" WHERE REL.empresa IS NOT NULL \n");
        }
        filtroPorSituacaoContrato(filtros, sql, "REL.");

        informarFiltros(filtros, sql);

		// sql.append("         -- A CONSULTA DEVE SER ORDENADA POR MATRICULA PARA FACILITAR O AGRUPAMENTO NO JAVA ");
		sql.append(" ORDER BY matricula, codigo DESC");

		Declaracao dc = new Declaracao(sql.toString(), con);
		preencherFiltros(filtros, dc);
		ResultSet rs = dc.executeQuery();

		// Dados a serem retornados para a tela
		Map<String, Object> retorno = new HashMap<String, Object>();
		Map<String, Map<String, Object>> totalizadorModalidade = new HashMap<String, Map<String, Object>>();
		pesquisarOperacoes(filtros);
		pesquisarSituacoes(filtros);
		pesquisarConvenio();
		// Consulta realizada em banco
		retorno.put("consulta", rs);
		// totalizadores
		retorno.put("totalCliente", 0);
		retorno.put("totalContrato", 0);
		retorno.put("totalValor", 0.0);
		retorno.put("totalReceita", 0.0);
		retorno.put("totalCompetencia", 0.0);
		retorno.put("totalModalidade", totalizadorModalidade);
		// monta os dados da consulta
		montarDadosConsulta(retorno, filtros);
		// remove o resultSet
		retorno.remove("consulta");
		// retorna os dados
		return retorno;
	}

    private void filtroPorSituacaoContrato(ClientesRelConsultaTO filtros, StringBuilder sql, String prefixo) {
        if (!UteisValidacao.emptyString(filtros.getSituacaoContrato())) {
            if (filtros.getSituacaoContrato().split(",").length != 4) {
                sql.append(" AND ").append(prefixo).append("situacaocontrato IN (").append(filtros.getSituacaoContrato()).append(")\n");
            }
        }
    }

    /**
	 * Aqui, seleciono todos os vinculos do cliente, concatenando com seus respectivos tipos. 
	 * <AUTHOR>
	 * 26/04/2011
	 * @param sql
	 */
	private void dadosVinculo(StringBuilder sql) {
		sql.append("                         ( SELECT (v.tipovinculo \n");
		sql.append("                                         || ': ' \n");
		sql.append("                                         || pc.nome)AS vinculo, \n");
		sql.append("                                 pc.nome                      , \n");
        sql.append("                                 v.colaborador as codigoColaborador , \n");
		sql.append("                                 v.cliente , \n");
		sql.append("                                 v.tipovinculo \n");
		sql.append("                         FROM    vinculo v \n");
		sql.append("                                 INNER JOIN colaborador col \n");
		sql.append("                                 ON      v.colaborador = col.codigo \n");
		sql.append("                                 INNER JOIN pessoa pc \n");
		sql.append("                                 ON      pc.codigo = col.pessoa \n");
		sql.append("                         ) \n");
		sql.append("                         AS vinculos \n");
	}
	
	private void dadosProduto(StringBuilder sql){
		sql.append("                      (SELECT   SUM(m.totalfinal) AS totalfinal, \n");
		sql.append("                               contrato \n");
		sql.append("                      FROM     movproduto m \n");
		sql.append("                               INNER JOIN produto p \n");
		sql.append("                               ON       m.produto = p.codigo \n");
		sql.append("                      WHERE    p.tipoproduto      = 'PM' \n");
		sql.append("                      GROUP BY contrato ) AS faturamento \n");
		
	}
	/**
	 * <AUTHOR>
	 * 26/04/2011
	 * @param filtros
	 * @param sql
	 * @throws Exception
	 */
	private void dadosContrato(ClientesRelConsultaTO filtros, StringBuilder sql) throws Exception {
		sql.append("                         ( SELECT con.pessoa, con.datalancamento, \n");
		sql.append("                                 plano.descricao         AS PLANO     , \n");
                sql.append("                                 plano.codigo         AS codigoPlano     , \n");
		sql.append("                                 con.codigo                           , \n");
		sql.append("                                 m.nome                  AS modalidade, \n");
                sql.append("                                 m.codigo                 AS codigomodalidade, \n");
		sql.append("                                 nt.descricao                  AS nivelturma, \n");
		sql.append("                                 con.vigenciade                       , \n");
		sql.append("                                 con.vigenciaateajustada              , \n");
		sql.append("                                 cd.numeromeses          AS duracao   , \n");
		sql.append("                                 h.descricao             AS horario   , \n");
                sql.append("                                 h.codigo             AS codigohorario   , \n");
		sql.append("                                 (SELECT max(valor) FROM movprodutomodalidade\n" +
				"                 WHERE contrato = con.codigo and modalidade = cm.modalidade ) AS valormodalidade, \n");
        sql.append("                                 con.situacaocontrato                 , \n");
		sql.append("              (SELECT SUM(valor) FROM movprodutomodalidade \n");
		sql.append("               WHERE contrato = con.codigo ) AS totalmodalidades       \n");
		sql.append("                         FROM    contrato con \n");
		sql.append("                                 LEFT JOIN plano \n");
		sql.append("                                 ON      plano.codigo = con.plano \n");
		sql.append("                                 LEFT JOIN contratocondicaopagamento ccp \n");
		sql.append("                                 ON      ccp.contrato = con.codigo \n");
		sql.append("                                 LEFT JOIN contratomodalidade cm \n");
		sql.append("                                 ON      con.codigo = cm.contrato \n");
		sql.append(" 								 LEFT JOIN contratomodalidadeturma cmt \n");
		sql.append("                               	 ON cmt.contratomodalidade = cm.codigo \n");
		sql.append("                                 LEFT JOIN contratomodalidadehorarioturma cmht \n");
		sql.append("								 ON cmt.codigo = cmht.contratomodalidadeturma \n");
		sql.append(" 								 LEFT JOIN horarioturma ht \n");
		sql.append("								 ON cmht.horarioturma = ht.codigo\n");
		sql.append(" 								 LEFT JOIN nivelturma nt	\n");
		sql.append(" 								 ON ht.nivelturma = nt.codigo	\n");
		sql.append("                                 LEFT JOIN modalidade m \n");
		sql.append("                                 ON      m.codigo = cm.modalidade \n");
		sql.append("                                 LEFT JOIN contratoduracao cd \n");
		sql.append("                                 ON      cd.contrato = con.codigo \n");
		sql.append("                                 LEFT JOIN contratohorario ch \n");
		sql.append("                                 ON      ch.contrato = con.codigo \n");
		sql.append("                                 LEFT JOIN horario h \n");
		sql.append("                                 ON      h.codigo = ch.horario \n");
		sql.append("                                 LEFT JOIN contratomodalidadevezessemana cmv \n");
		sql.append("                                 ON      cmv.contratomodalidade = cm.codigo \n");
		
		//aqui vamos ter dois tipos de filtros por data: no caso da consulta filtrar por data de lançamento,
		if(filtros.getFiltrarDataLancamento()){
			sql.append("                         WHERE con.datalancamento BETWEEN '"+Uteis.getDataJDBC(filtros.getDataInicial())+" 00:00:00'\n");
			sql.append("		                 AND '"+Uteis.getDataJDBC(filtros.getDataFinal())+"  23:59:59' \n");
		}else{
			// ou trazendo todos os contratos com vigencia iniciada em data menor ou igual a data escolhida pra consulta
			sql.append("                         WHERE   con.vigenciade  <= ? \n");
		}
        sql.append(")");
    }

	/**
	 * Respons�vel por
	 * 
	 * <AUTHOR> 07/04/2011
	 * @param sql
	 */
	private void informarFiltros(ClientesRelConsultaTO filtros, StringBuilder sql) {
		String listaParametros = "";

		// filtrar situacao
		if (!filtros.getNomesSituacoes().isEmpty()) {
			for (String situacao : filtros.getNomesSituacoes()) {
				if(situacao.equals("AT") || situacao.equals("AV") || situacao.equals("CR") || situacao.equals("AT")  ){
					listaParametros += ",'NO'";
                                }  else if (!situacao.equals("PL") && !situacao.equals("AA") && !situacao.equals("DI")){
					listaParametros += ",'" + situacao + "'";
                                }
				
			}
			if (!listaParametros.equals("")) {
				listaParametros = listaParametros.replaceFirst(",", "");
				sql.append("AND ");
				sql.append("( rel.situacaofinal  IN (" + listaParametros + ")");
                                if(!getDadosAcessoVisitantes().equals("")){
                                    sql.append(" or periodoacessocodigo is not null ");
                                }
                                sql.append(")");

			} else {
                            if(!getDadosAcessoVisitantes().equals("")){
                                sql.append("AND ");
                                sql.append("( periodoacessocodigo is not null )");
                            }
                        }

		}
		// filtrar colaboradores
		if (!filtros.getNomesColaboradores().isEmpty()) {
			listaParametros = "";

			String colPR = "";
			String colTW = "";
			String colPT = "";
			String colOR = "";
			String colCO = "";
			String colPI = "";
			String colPE = "";
			String colTE = "";
			String colIn = "";

			for (String colSelecionados : filtros.getNomesColaboradores()) {
				String[] colaboradoresSelecionados = colSelecionados.split(",");
				for (String codColaborador : colaboradoresSelecionados) {
					String[] colaborador = codColaborador.split("\\|");

					if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.CONSULTOR.getDescricao().toUpperCase())) {
						colCO += colaborador[0] + " ,";
					} else if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.PROFESSOR_TREINO.getDescricao().toUpperCase())) {
						colTW += colaborador[0] + " ,";
					} else if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.PERSONAL_TRAINER.getDescricao().toUpperCase())) {
						colPT += colaborador[0] + " ,";
					} else if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.ORIENTADOR.getDescricao().toUpperCase())) {
						colOR += colaborador[0] + " ,";
					} else if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.PROFESSOR.getDescricao().toUpperCase())) {
						colPR += colaborador[0] + " ,";
					} else if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getDescricao().toUpperCase())) {
						colPI += colaborador[0] + " ,";
					} else if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getDescricao().toUpperCase())) {
						colPE += colaborador[0] + " ,";
					} else if (colaborador[1].toUpperCase().equals(TipoColaboradorEnum.TERCEIRIZADO.getDescricao().toUpperCase())) {
						colTE += colaborador[0] + " ,";
					} else if(colaborador[1].toUpperCase().equals("Inativos".toUpperCase())){
						colIn += colaborador[0] + " ,";
					}

				}
			}

			if (colCO.length() > 0) {
				colCO = colCO.substring(0, colCO.length() - 1);
				colCO = "(rel.codigocolaborador in (" + colCO + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.CONSULTOR.getSigla() + "' ) OR ";
			}
			if (colTW.length() > 0) {
				colTW = colTW.substring(0, colTW.length() - 1);
				colTW = "(rel.codigocolaborador in (" + colTW + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.PROFESSOR_TREINO.getSigla() + "' ) OR ";
			}
			if (colPT.length() > 0) {
				colPT = colPT.substring(0, colPT.length() - 1);
				colPT = "(rel.codigocolaborador in (" + colPT + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.PERSONAL_TRAINER.getSigla() + "' ) OR ";
			}
			if (colOR.length() > 0) {
				colOR = colOR.substring(0, colOR.length() - 1);
				colOR = "(rel.codigocolaborador in (" + colOR + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.ORIENTADOR.getSigla() + "' ) OR ";
			}
			if (colPR.length() > 0) {
				colPR = colPR.substring(0, colPR.length() - 1);
				colPR = "(rel.codigocolaborador in (" + colPR + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.PROFESSOR.getSigla() + "' ) OR ";
			}
			if (colPI.length() > 0) {
				colPI = colPI.substring(0, colPI.length() - 1);
				colPI = "(rel.codigocolaborador in (" + colPI + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.PERSONAL_INTERNO.getSigla() + "' ) OR ";
			}
			if (colPE.length() > 0) {
				colPE = colPE.substring(0, colPE.length() - 1);
				colPE = "(rel.codigocolaborador in (" + colPE + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla() + "' ) OR ";
			}
			if (colTE.length() > 0) {
				colTE = colTE.substring(0, colTE.length() - 1);
				colTE = "(rel.codigocolaborador in (" + colTE + ") AND rel.tipovinculo = '" + TipoColaboradorEnum.TERCEIRIZADO.getSigla() + "' ) OR ";
			}
			if (colIn.length() > 0) {
				colIn = colIn.substring(0, colIn.length() - 1);
				colIn = "(rel.codigocolaborador in (" + colIn + ") ) OR ";
			}
			listaParametros = colCO + colTW + colPT + colOR + colPR + colPI + colPE + colTE + colIn;
			if (!listaParametros.isEmpty()) {
				listaParametros = listaParametros.substring(0, listaParametros.length() - 3);
				sql.append("AND ( " + listaParametros + " ) ");
			}

		}

		// filtrar planos
		if (!filtros.getNomesPlanos().isEmpty()) {
			listaParametros = "";
			for (String plano : filtros.getNomesPlanos()) {
				listaParametros += "," + plano.replace("F", "") ;
			}
			listaParametros = listaParametros.replaceFirst(",", "");
			sql.append("AND rel.codigoplano IN (" + listaParametros + ") ");
		}

		// filtrar horarios
		if (!filtros.getNomesHorarios().isEmpty()) {
			listaParametros = "";
			for (String horario : filtros.getNomesHorarios()) {
				listaParametros += "," + horario.replace("F", "") ;
			}
			listaParametros = listaParametros.replaceFirst(",", "");
			sql.append("AND rel.codigohorario IN (" + listaParametros + ") ");
		}

		// filtrar modalidades
		if (!filtros.getNomesModalidades().isEmpty()) {
			listaParametros = "";
			for (String modalidade : filtros.getNomesModalidades()) {
				listaParametros += "," + modalidade.replace("F", "") ;
			}
			listaParametros = listaParametros.replaceFirst(",", "");
			sql.append("AND rel.codigomodalidade IN (" + listaParametros + ") ");
		}

		// filtrar duracao
		if (!filtros.getListaDuracao().isEmpty()) {
			listaParametros = "";
			for (String duracao : filtros.getListaDuracao()) {
				listaParametros += "," + duracao;
			}
			listaParametros = listaParametros.replaceFirst(",", "");
			sql.append(" AND rel.duracao  IN (" + listaParametros + ")");

		}

	}

	
	private void preencherFiltros(ClientesRelConsultaTO filtros, Declaracao dc) throws Exception {
		int i = 0;
                filtros.setDataInicial(Uteis.getDataComHoraZerada(filtros.getDataInicial()));
                filtros.setDataFinal(Uteis.getDataComUltimaHora(filtros.getDataFinal()));
		//para a verifica��o de ativos
		if(!filtros.getFiltrarDataLancamento()){
			dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
			dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
		}
			
		dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
		
		//para a verifica��o de trancados
		dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
                
                dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
		
		dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
		
		//para a verifica��o de inativos
		dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
                dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
                dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));

		
//		dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		
		//aqui vamos ter dois tipos de filtros por data: no caso da consulta filtrar por data de lan�amento, 
		if(!filtros.getFiltrarDataLancamento()){
			// ou trazendo todos os contratos com vigencia iniciada em data menor ou igual a data escolhida pra consulta
			dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
		}
		
		//para a verifica��o de ACESSO
		dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
                dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
                dc.setDate(++i, new java.sql.Date(filtros.getDataInicial().getTime()));
		dc.setDate(++i, new java.sql.Date(filtros.getDataFinal().getTime()));
		
		// empresa
		if (!UteisValidacao.emptyNumber(filtros.getCodigoEmpresa())) {
			dc.setInt(++i, filtros.getCodigoEmpresa());
		}

	}

	/**
	 * <AUTHOR> 05/04/2011
	 * @return
	 * @throws Exception
	 */
	public void montarDadosConsulta(final Map<String, Object> retorno, ClientesRelConsultaTO filtro) throws Exception {

		List<ClienteRelatorioTO> vetResultado = new ArrayList<ClienteRelatorioTO>();
		ResultSet tabelaResultado = (ResultSet) retorno.get("consulta");
		retorno.put("lista", vetResultado);
		while (tabelaResultado.next()) {
			this.montarDados(tabelaResultado, retorno, filtro);

		}
	}

	/**
	 * Respons�vel por montar dados do relat�rio de clientes com origem num resultset
	 * 
	 * <AUTHOR> 04/04/2011
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	private void montarDados(final ResultSet tr, Map<String, Object> retorno, ClientesRelConsultaTO filtro) throws Exception {

		Map<String, Map<String, Object>> totalModalidade = (Map<String, Map<String, Object>>) retorno.get("totalModalidade");
		List<ClienteRelatorioTO> lista = (List<ClienteRelatorioTO>) retorno.get("lista");
		// obter contadores
		Integer cliente = (Integer) retorno.get("totalCliente");
		Integer contrato = (Integer) retorno.get("totalContrato");
		Double valor = (Double) retorno.get("totalValor");
		Double receita = (Double) retorno.get("totalReceita");
		Double competencia = (Double) retorno.get("totalCompetencia");
		String matricula = tr.getString("matricula") == null ? "" : tr.getString("matricula");
		Integer pessoa = tr.getInt("pessoa");
		String nomeCategoria = tr.getString("categoriaCliente");
		Integer duracao = tr.getString("duracao") == null ? 0 : Integer.valueOf(tr.getString("duracao"));
		Double faturamento = tr.getDouble("totalfinal");
		Double comp = tr.getDouble("competencia");
		String empresa = tr.getString("nomeEmpresa");
		String situacao = verificarSituacao(tr, filtro);
		if (!UteisValidacao.emptyString(situacao)) {
			String modalidade = tr.getString("modalidade");
			Double valorModalidade = tr.getDouble("valormodalidade");
			Double valorTotalModalidade = tr.getDouble("totalmodalidades");
			String codigoContrato = tr.getString("codigo");
			// se o registro � referente ao mesmo cliente, concatenar os resultados

			if (recipiente.getPessoa().equals(pessoa)) {
				recipiente.setSituacao(concatenarAtributoContrato(recipiente.getSituacao(), situacao, tr.getString("codigo"), recipiente
						.getContrato()));
				// concatenar valor somando totaliza��o valor
				if (concatenar(codigoContrato, recipiente.getContrato())) {

					contrato++;
					competencia += comp;
					valor += faturamento;
					receita += faturamento / duracao;
					recipiente.setFaturamento(recipiente.getFaturamento() + " | " + Formatador.formatarValorMonetario(faturamento));
					if (recipiente.getFaturamentoOrdem() == null || faturamento > recipiente.getFaturamentoOrdem()) {
						recipiente.setFaturamentoOrdem(faturamento);
					}
					recipiente.setContrato(concatenarCampo(recipiente.getContrato(), tr.getString("codigo")));
					recipiente.setPlano(concatenarCampo(recipiente.getPlano(), tr.getString("plano")));
					//	recipiente.setNivelTurma(concatenarCampo(recipiente.getNivelTurma(), tr.getString("nivelturma")));
					recipiente.setDuracao(recipiente.getDuracao() + " | " + (duracao > 0 ? duracao : " "));
					recipiente.setHorario(concatenarCampo(recipiente.getHorario(), tr.getString("horario")));
					recipiente.setInicio(concatenarCampoData(recipiente.getInicio(), Uteis.getData(tr.getDate("vigenciade"))));
					recipiente.setVence(concatenarCampoData(recipiente.getVence(), Uteis.getData(tr.getDate("vigenciaateajustada"))));
					recipiente.setDataLancamento(concatenarCampoData(recipiente.getDataLancamento(), Uteis
							.getData(tr.getDate("datalancamento"))));
				}
				recipiente.setVinculo(concatenarCampo(recipiente.getVinculo(), tr.getString("vinculo")));
				recipiente.setNivelTurma(concatenarCampo(recipiente.getNivelTurma(), tr.getString("nivelturma")));
				String email = tr.getString("email");
				if (!UteisValidacao.emptyString(email) && !recipiente.getEmail().contains(tr.getString("email"))) {
					recipiente.setEmail(concatenarCampo(tr.getString("email"), recipiente.getEmail()));
				}
			} else {
				cliente++;
				recipiente = new ClienteRelatorioTO();
				contratoModalidade = new ArrayList<String>();
				recipiente.setMatricula(matricula);
				recipiente.setCategoriaCliente(nomeCategoria);
				recipiente.setPessoa(pessoa);
				recipiente.setEmpresa(empresa);
				recipiente.setNome(tr.getString("nome"));
				recipiente.setSituacao(situacao);
				recipiente.setVinculo(tr.getString("vinculo"));
				if (!situacao.equals(GrupoSituacaoEnum.VISITANTE.getCodigo()) && !situacao.startsWith(GrupoSituacaoEnum.VISITANTE.getCodigo())) {
					recipiente.setContrato(codigoContrato);
					Date datalancamento = tr.getDate("datalancamento");
					recipiente.setDataLancamento(Uteis.getData(datalancamento));
					recipiente.setPlano(tr.getString("plano"));
					recipiente.setDuracao(duracao > 0 ? duracao.toString() : " ");
					recipiente.setHorario(tr.getString("horario"));
					recipiente.setNivelTurma(tr.getString("nivelturma"));
					Date inicioContrato = tr.getDate("vigenciade");
					Date fimContrato = tr.getDate("vigenciaateajustada");
					recipiente.setEmail(concatenarCampo(tr.getString("email"),recipiente.getEmail()));
					recipiente.setInicio(Uteis.getData(inicioContrato));
					recipiente.setVence(Uteis.getData(fimContrato));
					if (!situacao.equals(GrupoSituacaoEnum.VISITANTE.getDescricao())) {
						contrato++;
						competencia += comp;
						valor += faturamento;
						receita += faturamento / duracao;
					}
					recipiente.setFaturamento(Formatador.formatarValorMonetario(faturamento));
					recipiente.setFaturamentoOrdem(faturamento);
				}
				lista.add(recipiente);

			}
			//modalidade
			if (modalidade != null && !contratoModalidade.contains(codigoContrato + modalidade)) {

				recipiente.setModalidade(recipiente.getModalidade() + modalidade + " | ");
				recipiente.setValorModalidade(recipiente.getValorModalidade() + Formatador.formatarValorMonetario(valorModalidade) + " | ");

				if (recipiente.getValorModalidadeOrdem() == null || valorModalidade > recipiente.getValorModalidadeOrdem()) {
					recipiente.setValorModalidadeOrdem(valorModalidade);
				}

				//totalizadores de modalidades
				Map<String, Object> totais = totalModalidade.get(modalidade);
				if (totais == null) {
					totais = new HashMap<String, Object>();
				}
				//valores de modalidade
				Double totalValorModalidade = (Double) totais.get("valor");
				Double totalFaturamento = (Double) totais.get("faturamento");
				if (totalValorModalidade == null && valorModalidade != null) {
					totais.put("valor", valorModalidade);
				} else if (totalValorModalidade != null && valorModalidade != null) {
					totais.put("valor", valorModalidade + totalValorModalidade);
				}
				if (totalFaturamento == null && valorTotalModalidade != null) {
					totais.put("faturamento", valorTotalModalidade);
				} else if (totalFaturamento != null && valorTotalModalidade != null) {
					totais.put("faturamento", valorTotalModalidade + totalFaturamento);
				}
				//numeros de contrato
				Integer totalContModalidade = (Integer) totais.get("contrato");
				//somar contrato
				if (totalContModalidade == null)
					totalContModalidade = 0;
				totais.put("contrato", ++totalContModalidade);
				//verificar se esse cliente j� foi contabilizado com essa modalidade
				if (!clienteModalidade.contains(matricula + modalidade)) {
					Integer totalCliModalidade = (Integer) totais.get("cliente");
					//somar cliente
					if (totalCliModalidade == null)
						totalCliModalidade = 0;
					totais.put("cliente", ++totalCliModalidade);
				}
				totalModalidade.put(modalidade, totais);
				contratoModalidade.add(codigoContrato + modalidade);
				clienteModalidade.add(matricula + modalidade);
			}
			// atualizar contadores
			retorno.put("totalCliente", cliente);
			retorno.put("totalContrato", contrato);
			retorno.put("totalValor", valor);
			retorno.put("totalReceita", receita);
			retorno.put("totalCompetencia", competencia);
		}
	}

	/**
	 * <AUTHOR> 08/04/2011
	 * @param tr
	 * @throws SQLException
	 */
	private String verificarSituacao(final ResultSet tr, ClientesRelConsultaTO filtros) throws SQLException {
		String situacao = "";
		try {
			String sf = tr.getString("situacaofinal");
			Integer contrato = tr.getInt("codigo");
			// aqui verificar se existem filtros e se a situacao � do tipo
			// "normal"
			Boolean filtrar = Boolean.FALSE;
			if (sf != null && sf.equals(SituacaoEnum.NORMAL.getCodigo())
					&& !filtros.getNomesSituacoes().isEmpty()) {
				filtrar = Boolean.TRUE;
			}
			// caso visitante
			if (sf != null && sf.equals(GrupoSituacaoEnum.VISITANTE.getCodigo())) {
				situacao = GrupoSituacaoEnum.VISITANTE.getDescricao();
			} else
			// caso renovado
			if (sf != null && sf.equals(SituacaoEnum.RENOVADO.getCodigo())) {
				situacao = SituacaoEnum.RENOVADO.getDescricao();
			} else
			// caso inativo
			if (sf != null
					&& SituacaoEnum.getSituacao(sf).getGrupo().equals(GrupoSituacaoEnum.INATIVO)) {
				situacao = concatenarSituacao(sf);
			} else {
				Set<String> keySet = operacoesContrato.keySet();
				OUT: for (String sit : keySet) {
					List<Integer> lista = operacoesContrato.get(sit);
					for (Integer cod : lista) {
						if (contrato.equals(cod)) {
							if (sit.equals("BC")) {
								sf = "";
								situacao = concatenarSituacao(sf);
							} else {
								sf = sit;
								situacao = concatenarSituacao(sf);
							}
						}
					}
				}
			}
			if (situacao.equals("")) {
				Set<String> keyLista = historicosContrato.keySet();
				OUT: for (String sit : keyLista) {
					List<Integer> lista = historicosContrato.get(sit);
					for (Integer cod : lista) {
						if (contrato.equals(cod)) {
							if (sit.equals("RN") || sit.equals("CT")) {
								sf = "NO";
								situacao = concatenarSituacao(sf);
							} else {
								sf = sit;
								situacao = concatenarSituacao(sf);
							}
							break OUT;
						}
					}
				}
			}
			if (situacao.equals("") && sf != null) {
				SituacaoEnum sitEnum = SituacaoEnum.getSituacao(sf);
				if (sitEnum != null)
					situacao = concatenarSituacao(sf);
			}
			if (SituacaoEnum.getSituacao(sf) != null) {
				if (filtrar
						&& !filtros.getNomesSituacoes().contains(
						SituacaoEnum.getSituacao(sf).getCodigo()) && UteisValidacao.emptyNumber(tr.getInt("periodoacessocodigo"))) {
					situacao = null;
				}
			}
		} catch (Exception e) {
			situacao = null;
		}
       return situacao;
	}
	/**
	 * Respons�vel por 
	 * <AUTHOR>
	 * 26/04/2011
	 * @param sf
	 * @return
	 */
	private String concatenarSituacao(String sf){
		String situacao = "";
		try{
			situacao = SituacaoEnum.getSituacao(sf).getGrupo().getCodigo()+
			"-"+SituacaoEnum.getSituacao(sf).getDescricao();	
		}catch(Exception e){
			situacao = sf;
		}
		return situacao;
		
	}
	private String concatenarCampo(String destino, String aConcat) {

		// caso a string a ser concatenada j� se encontra na string de destino, n�o concatenar
		if (!UteisValidacao.emptyString(destino) && !UteisValidacao.emptyString(aConcat) && destino.indexOf(aConcat) < 0) {
			destino +=" | "+ aConcat;
		}
		return destino;
	}
	
	private String concatenarCampoData(String destino, String aConcat) {

		// caso a string a ser concatenada j� se encontra na string de destino, n�o concatenar
		if (!UteisValidacao.emptyString(destino) && !UteisValidacao.emptyString(aConcat)) {
			destino += " | "+aConcat;
		}
		return destino;
	}
	
	

	/**
	 * Respons�vel por concatenar um campo verificando se o que vai ser concatenado pertence a um contrato que ainda n�o foi adicionado a
	 * listagem
	 * 
	 * <AUTHOR> 12/04/2011
	 */
	private String concatenarAtributoContrato(String campoConcat, String campo, String campoContrato, String contratoConcat) {
		// caso a string a ser concatenada j� se encontra na string de destino, n�o concatenar
		if (concatenar(campoContrato, contratoConcat)) {
			campoConcat +=" | "+  campo;
		}
		return campoConcat;
	}

	/**
	 * <AUTHOR> 12/04/2011
	 * @param campoContrato
	 * @param contratoConcat
	 */
	private Boolean concatenar(String campoContrato, String contratoConcat) {
		return (!UteisValidacao.emptyString(campoContrato) && !UteisValidacao.emptyString(contratoConcat) && contratoConcat
				.indexOf(campoContrato) < 0);
	}

	private void limparListas() {
		contratoModalidade = new ArrayList<String>();
		clienteModalidade = new ArrayList<String>();
		historicosContrato = new HashMap<String, List<Integer>>();
		operacoesContrato = new HashMap<String, List<Integer>>();
		recipiente = new ClienteRelatorioTO();
	}
	
	
	// ======== CALCULO DO VALOR DAS MODALIDADES COM OS DESCONTOS DADOS ==========================//
	
	/**
	 * Respons�vel por calcular Valor da Modalidade, com os descontos
	 * <AUTHOR>
	 * 02/05/2011
	 */
	private Double calcularValorModalidade(ResultSet tr) throws Exception{
		//obter valor da modalidade
		Double valorModalidade = tr.getDouble("valormodalidade");
//		Double totalModalidades = tr.getDouble("totalmodalidades");
//		Double competencia = tr.getDouble("competencia");
//		Double porcentagemModalidade = valorModalidade/(totalModalidades/100);
//		valorModalidade = competencia * (porcentagemModalidade/100);
		if(valorModalidade.isNaN()){
			valorModalidade = 0.0;
		}
		return valorModalidade;
	}

    private void preencherDadosAcessoVisitantes(ClientesRelConsultaTO filtros) {
        String retorno = "";
        if (!filtros.getNomesSituacoes().isEmpty()) {
            for (String situacao : filtros.getNomesSituacoes()) {
                    if(situacao.equals("PL") || situacao.equals("AA") || situacao.equals("DI") ){
                            retorno += ",'" + situacao + "'";
                }
            }
        }
            
        if (!retorno.equals("")) {
              setDadosAcessoVisitantes(retorno.replaceFirst(",", ""));

        } else {
            setDadosAcessoVisitantes(retorno);
        }

    }

    public String getDadosAcessoVisitantes() {
        return dadosAcessoVisitantes;
    }

    public void setDadosAcessoVisitantes(String dadosAcessoVisitantes) {
        this.dadosAcessoVisitantes = dadosAcessoVisitantes;
    }
    
}
