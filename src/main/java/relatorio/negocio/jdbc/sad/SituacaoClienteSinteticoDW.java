/*
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.sad;

import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import controle.arquitetura.SuperControle;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.DadosAcessoOfflineVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.FrequenciaAlunoWS;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ClienteOrganizadorCarteiraVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.acesso.DadosAcessoOffline;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.treino.dto.SinteticoMsDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class SituacaoClienteSinteticoDW extends SuperEntidade {

    private static final StringBuilder sqlClientes = new StringBuilder("SELECT distinct c.codigo as codigoCliente, ").append(
                    "p.codigo as codigoPessoa, p.cfp, p.datacadastro, p.rg as rgPessoa, ").append("(select array(" +
                    "select ende.endereco || '-' || ende.bairro || '-' || ende.tipoendereco " +
                    "from endereco ende " +
                    " where ende.pessoa = p.codigo)) as enderecosCliente,").append(
                    "p.sexo as sexoCliente, ").append(
                    "c.empresa as empresaCliente, ").append(
                    "c.codacesso as codacessoCliente, ").append(
                    "(SELECT array(SELECT t.tipotelefone || '-' || t.numero FROM telefone t ").append(
                    "		where c.pessoa = t.pessoa").append(
                    ")) as telefonesCliente, ").append(
                    "c.codigomatricula as matricula, ").append(
                    "p.nome as nomeCliente, ").append("p.dataNasc as dataNascimento, ").append(
                    "prof.descricao as profissao, ").append("c.situacao, ").append(
                    "c.uacodigo, ").append("ac.dthrentrada, ").append(
                    "um.codigo as um_codigo, um.nome as um_nome,").append(
                    "um.senha as um_senha, um.ativo as um_ativo,").append(
                    "(SELECT array(SELECT lv.tipovinculo || '-' || p.nome || '-' || lv.colaborador FROM vinculo lv ").append(
                    "			inner join colaborador col on lv.colaborador = col.codigo ").append(
                    "		inner join pessoa p on p.codigo = col.pessoa ").append(
                    "	WHERE lv.cliente = c.codigo)) as colaboradores, ").append(
                    "(SELECT array(SELECT t.numero FROM telefone t ").append(
                    "			inner join colaborador col on col.pessoa = t.pessoa ").append(
                    "		inner join vinculo v on v.colaborador = col.codigo ").append(
                    "	WHERE v.cliente = c.codigo and t.tipotelefone = 'CE')) as telColaboradores, ").append(
                    "cid.nome as cidade, est.sigla as estado, c.titularplanocompartilhado ").append(
                    "FROM cliente c ").append("inner join pessoa p on p.codigo = c.pessoa ").append(
                    "left outer join profissao prof on prof.codigo = p.profissao ").append(
                    "left outer join acessocliente ac on ac.codigo = c.uacodigo ").append(
                    "left outer join usuariomovel um on um.cliente = c.codigo ").append(
                    "LEFT JOIN cidade cid ON p.cidade = cid.codigo ").append(
                    "left join estado est on  p.estado = est.codigo\n");
    //
    private static final StringBuilder sqlClienteAcesso = new StringBuilder(
            " inner join acessocliente  acc on acc.cliente = c.codigo Where acc.dthrentrada > '%s'\n ");
     private static final StringBuilder sqlClienteAcessoPeriodo = new StringBuilder(
    "select distinct c.codigo from cliente c inner join acessocliente  acc on acc.cliente = c.codigo Where acc.dthrentrada between '%s' and '%s'\n ");


    public SituacaoClienteSinteticoDW() throws Exception {
        super();
    }

    public SituacaoClienteSinteticoDW(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void consultar(String idEntidade) throws Exception {
        super.consultar(idEntidade);
    }

    @Override
    public void incluir(String idEntidade) throws Exception {
        super.incluir(idEntidade);
    }

    @Override
    public void alterar(String idEntidade) throws Exception {
        super.alterar(idEntidade);
    }

    public void excluir(SituacaoClienteSinteticoDWVO obj) throws Exception {
        super.excluir(getIdEntidade());
        try {
            String sql = "DELETE FROM  SituacaoClienteSinteticoDW WHERE ((codigo = ?))";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, obj.getCodigo());
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluir(int codigoCliente) throws Exception {
        try {
            String sql = "DELETE FROM SituacaoClienteSinteticoDW WHERE codigoCliente = ?";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.setInt(1, codigoCliente);
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirTudo() throws Exception {
        try {
            String sql = "DELETE FROM  SituacaoClienteSinteticoDW";
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
                sqlExcluir.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void atualizarDadosSintetico(int clienteCodigo, ModalidadeVO modalidadeVO) throws Exception {
        try {
            String sqlUpdateModalidades = "UPDATE situacaoclientesinteticodw  \n" +
                    "SET modalidades = CASE \n" +
                    "\tWHEN modalidades like '%|" + modalidadeVO.getCodigo() + "|%' THEN replace(modalidades, '|" + modalidadeVO.getCodigo() + "|', '|')\n" +
                    "\tWHEN modalidades like '%|" + modalidadeVO.getCodigo() + "%' THEN replace(modalidades, '|" + modalidadeVO.getCodigo() + "', '')\n" +
                    "\tWHEN modalidades like '%" + modalidadeVO.getCodigo() + "|%' THEN replace(modalidades, '" + modalidadeVO.getCodigo() + "|', '')\n" +
                    "END where codigocliente  = ?";

            try (PreparedStatement sqlUpdateMod = con.prepareStatement(sqlUpdateModalidades)) {
                sqlUpdateMod.setInt(1, clienteCodigo);
                sqlUpdateMod.execute();
            }

            String sqlUpdateDescModalidades = "UPDATE situacaoclientesinteticodw \n" +
                    "SET descricoesmodalidades = CASE \n" +
                    "\tWHEN descricoesmodalidades like '%|" + modalidadeVO.getNome() + "' || '%:" + modalidadeVO.getNrVezes() + "|%' THEN replace(descricoesmodalidades, '|" + modalidadeVO.getNome() + "' || ':" + modalidadeVO.getNrVezes()+ "|', '|')\n" +
                    "\tWHEN descricoesmodalidades like '%|" + modalidadeVO.getNome() + "' || '%:" + modalidadeVO.getNrVezes() + "%' THEN replace(descricoesmodalidades, '|" + modalidadeVO.getNome() + "' || ':" + modalidadeVO.getNrVezes() + "', '')\n" +
                    "\tWHEN descricoesmodalidades like '%" + modalidadeVO.getNome() + "' || '%:" + modalidadeVO.getNrVezes() + "|%' THEN replace(descricoesmodalidades, '" + modalidadeVO.getNome() + "' || ':" + modalidadeVO.getNrVezes() + "|', '')\n" +
                    "END where codigocliente  = ?";
            try (PreparedStatement sqlUpdateDescMod = con.prepareStatement(sqlUpdateDescModalidades)) {
                sqlUpdateDescMod.setInt(1, clienteCodigo);
                sqlUpdateDescMod.execute();
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public static List<SituacaoClienteSinteticoDWVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<SituacaoClienteSinteticoDWVO> vetResultado = new ArrayList<>();
        while (tabelaResultado.next()) {
            SituacaoClienteSinteticoDWVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static SituacaoClienteSinteticoDWVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        SituacaoClienteSinteticoDWVO obj = new SituacaoClienteSinteticoDWVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDia(dadosSQL.getTimestamp("dia"));
        obj.setCodigoCliente(dadosSQL.getInt("codigoCliente"));
        obj.setCodigoPessoa(dadosSQL.getInt("codigoPessoa"));
        obj.setNomeCliente(dadosSQL.getString("nomeCliente"));
        obj.setSexoCliente(dadosSQL.getString("sexoCliente"));
        obj.setTelefonesCliente(dadosSQL.getString("telefonesCliente"));
        obj.setEmpresaCliente(dadosSQL.getInt("empresaCliente"));
        obj.setMatricula(dadosSQL.getInt("matricula"));
        obj.setCodigoContrato(dadosSQL.getInt("codigoContrato"));
        obj.setDataVigenciaDe(dadosSQL.getDate("dataVigenciaDe"));
        obj.setDataVigenciaAteAjustada(dadosSQL.getDate("dataVigenciaAteAjustada"));
        obj.setSituacao(dadosSQL.getString("situacao"));
        obj.setSituacaoContrato(dadosSQL.getString("situacaoContrato"));
        obj.setDataInicioPeriodoAcesso(dadosSQL.getDate("dataInicioPeriodoAcesso"));
        obj.setDataFimPeriodoAcesso(dadosSQL.getDate("dataFimPeriodoAcesso"));
        obj.setNomePlano(dadosSQL.getString("nomePlano"));
        obj.setPesoRisco(dadosSQL.getInt("pesoRisco"));
        obj.setSMSRisco(dadosSQL.getInt("smsrisco"));
        obj.setTelCelColVinculados(dadosSQL.getString("telCelColab"));
        obj.setDuracaoContratoMeses(dadosSQL.getInt("duracaoContratoMeses"));
        obj.setCodAcessoCliente(dadosSQL.getString("codacessoCliente"));
        obj.setFrequenciaSemanal(dadosSQL.getInt("frequenciaSemanal"));
        obj.setSaldoCreditoTreino(dadosSQL.getInt("saldoCreditoTreino"));
        obj.setValidarSaldoCreditoTreino(dadosSQL.getBoolean("validarSaldoCreditoTreino"));
        obj.setNomeConsulta(dadosSQL.getString("nomeConsulta"));

        try {
            obj.setQuantidadeDiasExtra(dadosSQL.getInt("quantidadeDiasExtra"));
            obj.setModalidades(dadosSQL.getString("modalidades"));
            obj.setDescricoesModalidades(dadosSQL.getString("descricoesModalidades"));
            obj.setSituacaoContratoOperacao(dadosSQL.getString("situacaoContratoOperacao"));
            obj.setCpf(dadosSQL.getString("cpf"));
            obj.setDataSaidaAcesso(dadosSQL.getDate("datasaidaacesso"));
            obj.setCpfConsulta(dadosSQL.getString("cpfconsulta"));
            obj.setTelefonesConsulta(dadosSQL.getString("telefonesconsulta"));
//            obj.setTelefonesConsulta(dadosSQL.getString("telefonesconsulta "));
        } catch (Exception e) {
        }
        try {
            obj.setTotalCreditoTreino(dadosSQL.getInt("totalCreditoTreino"));
        } catch (Exception e) {
        }
        try {
            obj.setCrossfit(dadosSQL.getBoolean("crossfit"));
        } catch (Exception e) {
        }

        return obj;
    }

    public static SituacaoClienteSinteticoDWVO montarDados(ResultSet dadosSQL,
            int nivelMontarDados) throws Exception {
        SituacaoClienteSinteticoDWVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            return obj;
        }
        obj.setCodigoUltimoContatoCRM(dadosSQL.getInt("codigoUltimoContatoCRM"));
        obj.setColaboradores(dadosSQL.getString("colaboradores"));
        obj.setDataLancamentoContrato(dadosSQL.getTimestamp("dataLancamentoContrato"));
        obj.setDataMatricula(dadosSQL.getTimestamp("dataMatricula"));
        obj.setDataNascimento(dadosSQL.getDate("dataNascimento"));
        obj.setDataRematriculaContrato(dadosSQL.getTimestamp("dataRematriculaContrato"));
        obj.setDataRenovacaoContrato(dadosSQL.getTimestamp("dataRenovacaoContrato"));
        obj.setCargo(dadosSQL.getString("cargo"));
        obj.setDataUltimaRematricula(dadosSQL.getTimestamp("dataUltimaRematricula"));
        obj.setDataUltimoAcesso(dadosSQL.getTimestamp("dataUltimoAcesso"));
        obj.setDataUltimoBV(dadosSQL.getTimestamp("dataUltimoBV"));
        obj.setDataUltimoContatoCRM(dadosSQL.getTimestamp("dataUltimoContatoCRM"));
        obj.setDataVigenciaAte(dadosSQL.getDate("dataVigenciaAte"));
        obj.setDiasAcessoSemanaPassada(dadosSQL.getInt("diasAcessoSemanaPassada"));
        obj.setDiasAcessoSemana2(dadosSQL.getInt("diasAcessoSemana2"));
        obj.setDiasAcessoSemana3(dadosSQL.getInt("diasAcessoSemana3"));
        obj.setDiasAcessoSemana4(dadosSQL.getInt("diasAcessoSemana4"));
        obj.setDiasAssiduidadeUltRematriculaAteHoje(dadosSQL.getInt("diasAssiduidadeUltRematriculaAteHoje"));
        obj.setFaseAtualCRM(dadosSQL.getString("faseAtualCRM"));
        obj.setIdade(dadosSQL.getInt("idade"));
        obj.setMnemonicoContrato(dadosSQL.getString("mnemonicoContrato"));
        obj.setProfissao(dadosSQL.getString("profissao"));
        obj.setResponsavelUltimoContatoCRM(dadosSQL.getString("responsavelUltimoContatoCRM"));
        obj.setSaldoContaCorrenteCliente(dadosSQL.getDouble("saldoContaCorrenteCliente"));
        obj.setValorFaturadoContrato(dadosSQL.getDouble("valorFaturadoContrato"));
        obj.setValorPagoContrato(dadosSQL.getDouble("valorPagoContrato"));
        obj.setValorParcAbertoContrato(dadosSQL.getDouble("valorParcAbertoContrato"));
        obj.setTipoPeriodoAcesso(dadosSQL.getString("tipoPeriodoAcesso"));
        obj.setVezesporsemana(dadosSQL.getInt("vezesporsemana"));
        obj.setDiasAcessoMes2(dadosSQL.getInt("diasAcessoMes2"));
        obj.setDiasAcessoMes3(dadosSQL.getInt("diasAcessoMes3"));
        obj.setDiasAcessoMes4(dadosSQL.getInt("diasAcessoMes4"));
        obj.setDiasAcessoUltimoMes(dadosSQL.getInt("diasAcessoUltimoMes"));
        obj.setMediaDiasAcesso4Meses(dadosSQL.getInt("mediaDiasAcesso4Meses"));
        obj.setEnvioSMSMarcadoClassif(dadosSQL.getBoolean("envioSMSMarcadoClassif"));
        obj.setCodigoUsuarioMovel(dadosSQL.getInt("codigoUsuarioMovel"));
        obj.setSituacaoMatriculaContrato(dadosSQL.getString("situacaoMatriculaContrato"));
        obj.setCodAcessoCliente(dadosSQL.getString("codacessoCliente"));
        obj.setFreePassInicio(dadosSQL.getTimestamp("freePassInicio"));
        obj.setFreePassFim(dadosSQL.getTimestamp("freePassFim"));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONSULTA_WS) {
            obj.setProfissao(dadosSQL.getString("profissao"));
            obj.setBairro(dadosSQL.getString("bairro"));
            obj.setEndereco(dadosSQL.getString("endereco"));
            obj.setCidade(dadosSQL.getString("nome"));
            obj.setEstadoCivil(dadosSQL.getString("estadocivil"));
            obj.setRG(dadosSQL.getString("rg"));
            obj.setUltimaVisita(dadosSQL.getDate("dataultimoacesso"));
            obj.setUF(dadosSQL.getString("uf"));
        }
        try {
            obj.setCpf(dadosSQL.getString("cpf"));
        } catch (Exception e) {
        }
        try {
            obj.setSaldoCreditoTreino(dadosSQL.getInt("saldoCreditoTreino"));
            obj.setTotalCreditoTreino(dadosSQL.getInt("totalCreditoTreino"));
            obj.setDescricoesModalidades(dadosSQL.getString("descricoesModalidades"));
            obj.setSituacaoContratoOperacao(dadosSQL.getString("situacaoContratoOperacao"));
            obj.setDataSaidaAcesso(dadosSQL.getTimestamp("datasaidaacesso"));
            obj.setDataCadastro(dadosSQL.getTimestamp("datacadastro"));
            obj.setExisteParcVencidaContrato(dadosSQL.getBoolean("existeparcvencidacontrato"));
            obj.setEmpresausafreepass(dadosSQL.getBoolean("empresausafreepass"));
        } catch (Exception e) {
        }
        obj.setNovoObj(false);

        return obj;
    }

    public void incluir(SituacaoClienteSinteticoDWVO obj) throws Exception {
        incluirSemCommit(obj, null);
    }

    public void incluirSemCommit(SituacaoClienteSinteticoDWVO obj, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        try {
            String sql = "INSERT INTO SituacaoClienteSinteticoDW("
                    + "dia,codigoCliente,codigoContrato,codigoUltimoContatoCRM,"
                    + "colaboradores,dataLancamentoContrato,"
                    + "dataMatricula,dataNascimento,"
                    + "dataRematriculaContrato,dataRenovacaoContrato,"
                    + "dataUltimaRematricula,dataUltimoAcesso,dataUltimoBV,"
                    + "dataUltimoContatoCRM,dataVigenciaAte,dataVigenciaAteAjustada,"
                    + "dataVigenciaDe,diasAcessoSemanaPassada,"
                    + "diasAssiduidadeUltRematriculaAteHoje,"
                    + "duracaoContratoMeses,faseAtualCRM,idade,matricula,"
                    + "mnemonicoContrato,nomeCliente,nomePlano,profissao,"
                    + "responsavelUltimoContatoCRM,saldoContaCorrenteCliente,"
                    + "situacao,valorFaturadoContrato,valorPagoContrato,"
                    + "valorParcAbertoContrato, situacaoContrato, tipoPeriodoAcesso, "
                    + "dataInicioPeriodoAcesso, dataFimPeriodoAcesso, diasAcessoSemana2, "
                    + "diasAcessoSemana3, diasAcessoSemana4, vezesporsemana, diasAcessoUltimoMes,"
                    + "diasAcessoMes2, diasAcessoMes3, diasAcessoMes4, mediaDiasAcesso4Meses, "
                    + "telCelColab, envioSMSMarcadoClassif, codigoPessoa,codigoUsuarioMovel, "
                    + "empresaCliente, sexoCliente, telefonesCliente, situacaoMatriculaContrato, "
                    + "cpf, codacessocliente, modalidades, descricoesModalidades, situacaoContratoOperacao,datasaidaacesso,datacadastro,existeparcvencidacontrato,empresausafreepass,\n"
                    + "ultimaVisita, cargo, freePass, endereco, cidade, bairro, estadoCivil, uf, RG, cpfconsulta,telefonesconsulta, freePassInicio, freePassFim)\n"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,"
                    + " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,"
                    + " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,\n"
                    + " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {

                int i = 1;
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDia()));
                sqlInserir.setInt(i++, obj.getCodigoCliente());
                sqlInserir.setInt(i++, obj.getCodigoContrato());
                sqlInserir.setInt(i++, obj.getCodigoUltimoContatoCRM());
                sqlInserir.setString(i++, obj.getColaboradores());
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamentoContrato()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataMatricula()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataNascimento()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRematriculaContrato()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRenovacaoContrato()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimaRematricula()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimoAcesso()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimoBV()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimoContatoCRM()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataVigenciaAte()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataVigenciaAteAjustada()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataVigenciaDe()));
                sqlInserir.setInt(i++, obj.getDiasAcessoSemanaPassada());
                sqlInserir.setInt(i++, obj.getDiasAssiduidadeUltRematriculaAteHoje());
                sqlInserir.setInt(i++, obj.getDuracaoContratoMeses());
                sqlInserir.setString(i++, obj.getFaseAtualCRM());
                sqlInserir.setInt(i++, obj.getIdade());
                sqlInserir.setInt(i++, obj.getMatricula());
                sqlInserir.setString(i++, obj.getMnemonicoContrato());
                sqlInserir.setString(i++, obj.getNomeCliente());
                sqlInserir.setString(i++, obj.getNomePlano());
                sqlInserir.setString(i++, obj.getProfissao());
                sqlInserir.setString(i++, obj.getResponsavelUltimoContatoCRM());
                sqlInserir.setDouble(i++, obj.getSaldoContaCorrenteCliente());
                sqlInserir.setString(i++, obj.getSituacao());
                sqlInserir.setDouble(i++, obj.getValorFaturadoContrato());
                sqlInserir.setDouble(i++, obj.getValorPagoContrato());
                sqlInserir.setDouble(i++, obj.getValorParcAbertoContrato());
                sqlInserir.setString(i++, obj.getSituacaoContrato());
                sqlInserir.setString(i++, obj.getTipoPeriodoAcesso());
                sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataInicioPeriodoAcesso()));
                sqlInserir.setDate(i++, Uteis.getDataJDBC(obj.getDataFimPeriodoAcesso()));
                sqlInserir.setInt(i++, obj.getDiasAcessoSemana2());
                sqlInserir.setInt(i++, obj.getDiasAcessoSemana3());
                sqlInserir.setInt(i++, obj.getDiasAcessoSemana4());
                sqlInserir.setInt(i++, obj.getVezesporsemana());
                sqlInserir.setInt(i++, obj.getDiasAcessoUltimoMes());
                sqlInserir.setInt(i++, obj.getDiasAcessoMes2());
                sqlInserir.setInt(i++, obj.getDiasAcessoMes3());
                sqlInserir.setInt(i++, obj.getDiasAcessoMes4());
                sqlInserir.setInt(i++, obj.getMediaDiasAcesso4Meses());
                sqlInserir.setString(i++, obj.getTelCelColVinculados());
                sqlInserir.setBoolean(i++, obj.isEnvioSMSMarcadoClassif());
                sqlInserir.setInt(i++, obj.getCodigoPessoa());
                sqlInserir.setInt(i++, obj.getCodigoUsuarioMovel());
                sqlInserir.setInt(i++, obj.getEmpresaCliente());
                sqlInserir.setString(i++, obj.getSexoCliente());
                sqlInserir.setString(i++, obj.getTelefonesCliente());
                sqlInserir.setString(i++, obj.getSituacaoMatriculaContrato());
                sqlInserir.setString(i++, obj.getCpf());
                sqlInserir.setString(i++, obj.getCodAcessoCliente());
                sqlInserir.setString(i++, obj.getModalidades());
                sqlInserir.setString(i++, obj.getDescricoesModalidades());
                sqlInserir.setString(i++, obj.getSituacaoContratoOperacao());
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataSaidaAcesso()));
                sqlInserir.setTimestamp(i++,Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
                sqlInserir.setBoolean(i++,obj.getExisteParcVencidaContrato());
                sqlInserir.setBoolean(i++,obj.getEmpresausafreepass());
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getUltimaVisita()));
                sqlInserir.setString(i++, obj.getCargo());
                sqlInserir.setBoolean(i++, obj.isFreePass());
                sqlInserir.setString(i++, obj.getEndereco());
                sqlInserir.setString(i++, obj.getCidade());
                sqlInserir.setString(i++, obj.getBairro());
                sqlInserir.setString(i++, obj.getEstadoCivil());
                sqlInserir.setString(i++, obj.getUF());
                sqlInserir.setString(i++, obj.getRG());
                sqlInserir.setString(i++, obj.getCpfConsulta());
                sqlInserir.setString(i++, obj.getTelefonesConsulta());
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getFreePassInicio()));
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getFreePassFim()));
                sqlInserir.execute();
            }

            obj.setCodigo(Conexao.obterUltimoCodigoGeradoTabela(con, getIdEntidade()));
            obj.setNovoObj(false);
            atualizarInformacoesCrossfit(null, false, obj.getCodigoPessoa());
            resolveNomeConsulta(obj);
            if(sinteticoSincronizar != null){
                atualizarSinteticoNew(obj, sinteticoSincronizar);
            }else {
                resolveUsuarioMovel(obj, con);
            }
//            gravarClienteAtualizar(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    private void atualizarSinteticoNew(SituacaoClienteSinteticoDWVO scDWVO, SinteticoMsDTO sinteticoSincronizar) {
        try {
            String k = DAO.resolveKeyFromConnection(con);
            if (!k.isEmpty() && scDWVO.getCodigoUsuarioMovel() > 0) {
                UsuarioMovelVO uMovel = new UsuarioMovel(con).consultarPorChavePrimaria(
                        scDWVO.getCodigoUsuarioMovel(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (uMovel != null && uMovel.getCodigo().intValue() > 0) {

                    uMovel.getCliente().setSituacaoClienteSinteticoVO(scDWVO);
                    if (scDWVO.isAtualizarTreino()) {
                        uMovel.getCliente().getPessoa().setDataCadastro(scDWVO.getDataCadastro());
                        sinteticoSincronizar.getUsuariosAtualizar().add(uMovel.toUsuarioTreinoDTO().toJSON());
                    } else if(scDWVO.isIncrementarVersaoTreino()){
                        HashMap<String, String> header = new HashMap<>();
                        header.put("key", k);
                        Uteis.executeRequestSintetico(PropsService.getPropertyValue(k, PropsService.urlTreinoWeb)+"/prest/sintetico/incrementarVersaoCliente",
                                uMovel.toUsuarioTreinoDTO().toJSON().toString(), header);
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(null, "TreinoWS -> Não foi possível adicionar atualização do Aluno " + scDWVO.getCodigoCliente() + " devido ao ERRO: " + e.getMessage());
        }
    }

    private void resolveNomeConsulta(SituacaoClienteSinteticoDWVO obj) throws Exception {
        executarConsultaUpdate("UPDATE situacaoclientesinteticodw SET nomeconsulta = remove_acento_upper(nomecliente)"
                + " WHERE codigocliente = " + obj.getCodigoCliente(), con);
    }

    public void alterarSemCommit(SituacaoClienteSinteticoDWVO obj, SinteticoMsDTO sinteticoSincronizar) throws Exception {
        try {
            String sql = "UPDATE SituacaoClienteSinteticoDW set "
                    + "dia=?,codigoCliente=?,codigoContrato=?,codigoUltimoContatoCRM=?,"
                    + "colaboradores=?,dataLancamentoContrato=?,"
                    + "dataMatricula=?,dataNascimento=?,"
                    + "dataRematriculaContrato=?,dataRenovacaoContrato=?,"
                    + "dataUltimaRematricula=?,dataUltimoAcesso=?,dataUltimoBV=?,"
                    + "dataUltimoContatoCRM=?,dataVigenciaAte=?,dataVigenciaAteAjustada=?,"
                    + "dataVigenciaDe=?,diasAcessoSemanaPassada=?,"
                    + "diasAssiduidadeUltRematriculaAteHoje=?,"
                    + "duracaoContratoMeses=?,faseAtualCRM=?,idade=?,matricula=?,"
                    + "mnemonicoContrato=?,nomeCliente=?,nomePlano=?,profissao=?,"
                    + "responsavelUltimoContatoCRM=?,saldoContaCorrenteCliente=?,"
                    + "situacao=?,valorFaturadoContrato=?,valorPagoContrato=?,"
                    + "valorParcAbertoContrato=?, situacaoContrato=?, tipoPeriodoAcesso=?, "
                    + "dataInicioPeriodoAcesso=?, dataFimPeriodoAcesso=?, diasAcessoSemana2=?, "
                    + "diasAcessoSemana3=?, diasAcessoSemana4=?, vezesporsemana=?, diasAcessoUltimoMes=?,"
                    + "diasAcessoMes2=?, diasAcessoMes3=?, diasAcessoMes4=?, "
                    + "mediaDiasAcesso4Meses=?, telCelColab =? , envioSMSMarcadoClassif =?,"
                    + "codigoPessoa =?, codigoUsuarioMovel=?, empresaCliente=?, sexoCliente = ?,"
                    + "telefonesCliente = ?,  situacaoMatriculaContrato = ?, cpf = ?, codacessocliente = ?,"
                    + "modalidades = ?, frequenciaSemanal =?, descricoesModalidades = ?, situacaoContratoOperacao = ?,datasaidaacesso = ?,datacadastro = ?,existeparcvencidacontrato = ?, empresausafreepass=?,\n "
                    + "ultimaVisita = ?, cargo = ?, freePass = ?, endereco = ?, cidade = ?, bairro = ?, estadoCivil = ?, RG = ?, uf = ?,cpfconsulta = ?,telefonesconsulta = ?,"
                    + "freePassInicio = ?, freePassFim = ? "
                    + "WHERE codigo=?";
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {

                int i = 1;
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDia()));
                sqlAlterar.setInt(i++, obj.getCodigoCliente());
                sqlAlterar.setInt(i++, obj.getCodigoContrato());
                sqlAlterar.setInt(i++, obj.getCodigoUltimoContatoCRM());
                sqlAlterar.setString(i++, obj.getColaboradores());
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamentoContrato()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataMatricula()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataNascimento()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRematriculaContrato()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataRenovacaoContrato()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimaRematricula()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimoAcesso()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimoBV()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataUltimoContatoCRM()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataVigenciaAte()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataVigenciaAteAjustada()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataVigenciaDe()));
                sqlAlterar.setInt(i++, obj.getDiasAcessoSemanaPassada());
                sqlAlterar.setInt(i++, obj.getDiasAssiduidadeUltRematriculaAteHoje());
                sqlAlterar.setInt(i++, obj.getDuracaoContratoMeses());
                sqlAlterar.setString(i++, obj.getFaseAtualCRM());
                sqlAlterar.setInt(i++, obj.getIdade());
                sqlAlterar.setInt(i++, obj.getMatricula());
                sqlAlterar.setString(i++, obj.getMnemonicoContrato());
                sqlAlterar.setString(i++, obj.getNomeCliente());
                sqlAlterar.setString(i++, obj.getNomePlano());
                sqlAlterar.setString(i++, obj.getProfissao());
                sqlAlterar.setString(i++, obj.getResponsavelUltimoContatoCRM());
                sqlAlterar.setDouble(i++, obj.getSaldoContaCorrenteCliente());
                sqlAlterar.setString(i++, obj.getSituacao());
                sqlAlterar.setDouble(i++, obj.getValorFaturadoContrato());
                sqlAlterar.setDouble(i++, obj.getValorPagoContrato());
                sqlAlterar.setDouble(i++, obj.getValorParcAbertoContrato());
                sqlAlterar.setString(i++, obj.getSituacaoContrato());
                sqlAlterar.setString(i++, obj.getTipoPeriodoAcesso());
                sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataInicioPeriodoAcesso()));
                sqlAlterar.setDate(i++, Uteis.getDataJDBC(obj.getDataFimPeriodoAcesso()));
                sqlAlterar.setInt(i++, obj.getDiasAcessoSemana2());
                sqlAlterar.setInt(i++, obj.getDiasAcessoSemana3());
                sqlAlterar.setInt(i++, obj.getDiasAcessoSemana4());
                sqlAlterar.setInt(i++, obj.getVezesporsemana());
                sqlAlterar.setInt(i++, obj.getDiasAcessoUltimoMes());
                sqlAlterar.setInt(i++, obj.getDiasAcessoMes2());
                sqlAlterar.setInt(i++, obj.getDiasAcessoMes3());
                sqlAlterar.setInt(i++, obj.getDiasAcessoMes4());
                sqlAlterar.setInt(i++, obj.getMediaDiasAcesso4Meses());
                sqlAlterar.setString(i++, obj.getTelCelColVinculados());
                sqlAlterar.setBoolean(i++, obj.isEnvioSMSMarcadoClassif());
                sqlAlterar.setInt(i++, obj.getCodigoPessoa());
                sqlAlterar.setInt(i++, obj.getCodigoUsuarioMovel());
                sqlAlterar.setInt(i++, obj.getEmpresaCliente());
                sqlAlterar.setString(i++, obj.getSexoCliente());
                sqlAlterar.setString(i++, obj.getTelefonesCliente());
                sqlAlterar.setString(i++, obj.getSituacaoMatriculaContrato());
                sqlAlterar.setString(i++, obj.getCpf());
                sqlAlterar.setString(i++, obj.getCodAcessoCliente());
                sqlAlterar.setString(i++, obj.getModalidades());
                sqlAlterar.setInt(i++, obj.getFrequenciaSemanal());
                sqlAlterar.setString(i++, obj.getDescricoesModalidades());
                sqlAlterar.setString(i++, obj.getSituacaoContratoOperacao());
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataSaidaAcesso()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataCadastro()));
                sqlAlterar.setBoolean(i++, obj.getExisteParcVencidaContrato());
                sqlAlterar.setBoolean(i++, obj.getEmpresausafreepass());
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getUltimaVisita()));
                sqlAlterar.setString(i++, obj.getCargo());
                sqlAlterar.setBoolean(i++, obj.isFreePass());
                sqlAlterar.setString(i++, obj.getEndereco());
                sqlAlterar.setString(i++, obj.getCidade());
                sqlAlterar.setString(i++, obj.getBairro());
                sqlAlterar.setString(i++, obj.getEstadoCivil());
                sqlAlterar.setString(i++, obj.getRG());
                sqlAlterar.setString(i++, obj.getUF());
                sqlAlterar.setString(i++, obj.getCpfConsulta());
                sqlAlterar.setString(i++, obj.getTelefonesConsulta());
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getFreePassInicio()));
                sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getFreePassFim()));

                sqlAlterar.setInt(i++, obj.getCodigo());

                sqlAlterar.execute();
            }
            obj.setCrossfit(atualizarInformacoesCrossfit(null, false, obj.getCodigoPessoa()));
            resolveNomeConsulta(obj);
            if(sinteticoSincronizar != null){
                atualizarSinteticoNew(obj, sinteticoSincronizar);
            }else {
                resolveUsuarioMovel(obj, con);
            }
//           gravarClienteAtualizar(obj);
        } catch (Exception e) {
            throw e;
        }
    }

    public List<SituacaoClienteSinteticoDWVO> consultarPorDia(int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM SituacaoClienteSinteticoDW "
                + "ORDER BY nomeCliente";
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<SituacaoClienteSinteticoDWVO> consultarClienteCreditoPorSituacao(List<String> listaSituacao, Boolean somenteSaldoMaiorQueZero, int creditosDe,int creditosAte, int empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("s.codigoCliente,  \n");
        sql.append("s.matricula,  \n");
        sql.append("s.nomeCliente,  \n");
        sql.append("s.saldoCreditoTreino,  \n");
        sql.append("s.dataVigenciaAte,  \n");
        sql.append("s.situacao, \n");
        sql.append("s.telefonesCliente, \n");
        sql.append("e.nome as nomeempresa \n");
        sql.append("FROM SituacaoClienteSinteticoDW s \n");
        sql.append("inner join empresa e on e.codigo = s.empresacliente \n");
        sql.append("WHERE 1 = 1  \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append( " and s.empresacliente = ").append(empresa);
        }
        if(somenteSaldoMaiorQueZero){
            sql.append(" and s.saldoCreditoTreino > 0 \n");
        } else {
            sql.append(" and s.saldoCreditoTreino >= 0 \n");
        }
        sql.append(" AND (s.saldoCreditoTreino >= ").append(creditosDe).append(" AND s.saldoCreditoTreino <= ").append(creditosAte).append(") \n");
        sql.append(" AND (");

        for (int i = 0; i <= listaSituacao.size() - 1; i++) {
            if(i > 0){
                sql.append(" OR ");
            }
            sql.append(" s.situacao = '").append(listaSituacao.get(i)).append("'");
        }
        sql.append(" ) ");
        sql.append(" ORDER BY s.nomeCliente ");

        List<SituacaoClienteSinteticoDWVO> listaSituacaoClienteSintetico;
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {

                listaSituacaoClienteSintetico = new ArrayList<>();
                SituacaoClienteSinteticoDWVO obj;
                while (rs.next()) {
                    obj = new SituacaoClienteSinteticoDWVO();
                    obj.setCodigoCliente(new Integer(rs.getInt("codigoCliente")));
                    obj.setMatricula(new Integer(rs.getInt("matricula")));
                    obj.setNomeCliente(rs.getString("nomeCliente"));
                    obj.setSaldoCreditoTreino(new Integer(rs.getInt("saldoCreditoTreino")));
                    obj.setDataVigenciaAte(rs.getDate("dataVigenciaAte"));
                    obj.setTelefonesCliente(rs.getString("telefonesCliente"));
                    obj.setNomeEmpresa(rs.getString("nomeempresa"));
                    listaSituacaoClienteSintetico.add(obj);
                }
            }
        }

        return listaSituacaoClienteSintetico;
    }

    public boolean consultarHouveProcessamentoNesteDia(Date data) throws Exception {
        String sql = "select codigo from situacaoclientesinteticodw  where dia = '"
                + Uteis.getDataJDBC(data)
                + "' limit 1";
        try (Statement stm = con.createStatement()) {
            try (ResultSet resultado = stm.executeQuery(sql)) {
                return resultado.next();
            }
        }
    }

    public SituacaoClienteSinteticoDWVO consultarCliente(int codigo, int nivelMontarDados) throws Exception {
        super.inicializar();
        String sql = "SELECT * FROM SituacaoClienteSinteticoDW WHERE codigocliente = " + codigo;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados);
                } else {
                    return new SituacaoClienteSinteticoDWVO();
                }
            }
        }
    }
    public SituacaoClienteSinteticoDWVO consultarClienteWS(int codigo, int nivelMontarDados) throws Exception {
        super.inicializar();
        String sql = "select scdw.*, t.numero, t2.numero residencial, e.bairro, e.endereco, c.nome, p.estadocivil, p.rg, p.cfp, s.sigla FROM SituacaoClienteSinteticoDW scdw\n" +
                "left join pessoa p on scdw.codigopessoa = p.codigo\n" +
                "left  join telefone t on t.pessoa = p.codigo and t.tipotelefone = 'CE'\n" +
                "left  join telefone t2 on t2.pessoa = p.codigo and t2.tipotelefone = 'RE'\n" +
                "left join endereco e on e.pessoa = p.codigo\n" +
                "left join cidade c on p.cidade = c.codigo\n" +
                "left join estado s on p.estado = s.codigo\n" +
                "WHERE scdw.codigocliente = " + codigo;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados);
                } else {
                    return new SituacaoClienteSinteticoDWVO();
                }
            }
        }
    }

    public SituacaoClienteSinteticoDWVO consultarPorCodigoPessoa(int codigoPessoa, int nivelMontarDados) throws Exception {
        super.inicializar();
        String sql = "SELECT * FROM SituacaoClienteSinteticoDW WHERE codigoPessoa = " + codigoPessoa;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado, nivelMontarDados);
                } else {
                    return new SituacaoClienteSinteticoDWVO();
                }
            }
        }
    }

    public Integer qtdCreditoContratoFuturo(Integer codigoContrato) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT (quantidadecreditocompra - (SELECT COUNT (*) FROM reposicao WHERE contrato = ")
                .append(codigoContrato).append(")) AS qtdCreditoFut FROM contratoduracao ")
                .append("  LEFT JOIN contratoduracaocreditotreino ON (contratoduracao.codigo = contratoduracaocreditotreino.contratoduracao \n" +
                        "  AND EXISTS(SELECT 1 FROM contrato WHERE contrato.codigo = contratoduracao.contrato \n" +
                        "  AND   contrato.vigenciade > now())) ")
                .append(" WHERE contratoduracao.contrato = ").append(codigoContrato);
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                return rs.next() ? rs.getInt("qtdCreditoFut") : 0;
            }
        }
    }

    public Integer qtdCreditoCompra(Integer codigoContrato) throws Exception  {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT quantidadecreditocompra FROM contratoduracao  \n")
                .append(" LEFT JOIN contratoduracaocreditotreino ON (contratoduracao.codigo = contratoduracaocreditotreino.contratoduracao) \n")
                .append(" WHERE contratoduracao.contrato = ")
                .append(codigoContrato);
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                return rs.next() ? rs.getInt("quantidadecreditocompra") : 0;
            }
        }
    }

    public boolean clienteTemVinculoComConsultor(Integer codigoCliente)throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM SituacaoClienteSinteticoDW \n");
        sql.append("WHERE codigocliente = ").append(codigoCliente).append(" \n");
        sql.append(" and colaboradores like '%CO-%' ");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                return rs.next();
            }
        }
    }

    public int existeSintetico(int codigo) throws Exception {
        String sql = "SELECT codigo FROM SituacaoClienteSinteticoDW WHERE codigocliente = " + codigo;
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("codigo");
                } else {
                    return 0;
                }
            }
        }
    }

    /**
     * Responsável por consultar no sintetico de forma enxuta os dados
     * necessários para visualização do cliente no organizador de carteiras.
     *
     * <AUTHOR> 27/06/2011
     */
    public void consultarClienteOrganizadorCarteiras(ClienteOrganizadorCarteiraVO cliente) throws Exception {
        String sql = "SELECT codigocontrato, idade, profissao, duracaocontratomeses, dataultimobv, situacao, datavigenciade,datavigenciaateajustada, mnemonicocontrato, "
                + " situacaocontrato FROM SituacaoClienteSinteticoDW WHERE codigocliente = ?";
        Declaracao dc = new Declaracao(sql, con);
        dc.setInt(1, cliente.getCliente().getCodigo());
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                cliente.getCliente().getSituacaoClienteSinteticoVO().setIdade(rs.getInt("idade"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setSituacao(rs.getString("situacao"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setProfissao(rs.getString("profissao"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setDuracaoContratoMeses(rs.getInt("duracaocontratomeses"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setDataUltimoBV(rs.getDate("dataultimobv"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setDataVigenciaDe(rs.getDate("datavigenciade"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setDataVigenciaAteAjustada(rs.getDate("datavigenciaateajustada"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setMnemonicoContrato(rs.getString("mnemonicocontrato"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setCodigoContrato(rs.getInt("codigocontrato"));
                cliente.getCliente().getSituacaoClienteSinteticoVO().setSituacaoContrato(rs.getString("situacaocontrato"));
            }
        }
    }

    public int consultarCodigoSinteticoExisteCliente(final int codigoCliente) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from "
                + "situacaoclientesinteticodw  where codigoCliente = " + codigoCliente, con)) {
            if (rs.next()) {
                return rs.getInt(1);
            } else {
                return 0;
            }
        }
    }

    public SituacaoClienteSinteticoDWVO consultarClienteSintetico(Date data, int codigoCliente) throws SQLException {
        String sql = "select dataultimoacesso, duracaocontratomeses,datavigenciaateajustada, telCelColab, datasaidaacesso "
                + " from situacaoclientesinteticodw  where codigocliente = ? ";
        if (data != null) {
            sql += " and ( datavigenciaateajustada <= ?)";
        }
        Declaracao dc = new Declaracao(sql, con);
        dc.setInt(1, codigoCliente);
        if (data != null) {
            dc.setTimestamp(2, data);
        }
        try (ResultSet rs = dc.executeQuery()) {
            if (rs.next()) {
                SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = new SituacaoClienteSinteticoDWVO();
                situacaoClienteSinteticoDWVO.setDataUltimoAcesso(rs.getDate("dataultimoacesso"));
                situacaoClienteSinteticoDWVO.setDuracaoContratoMeses(rs.getInt("duracaocontratomeses"));
                situacaoClienteSinteticoDWVO.setDataVigenciaAteAjustada(rs.getDate("datavigenciaateajustada"));
                situacaoClienteSinteticoDWVO.setTelCelColVinculados(rs.getString("telCelColab"));
                situacaoClienteSinteticoDWVO.setDataSaidaAcesso(rs.getDate("datasaidaacesso"));
                return situacaoClienteSinteticoDWVO;
            }
        }
        return null;
    }

    public void atualizarPesoRisco(Integer pesoRisco, Integer codigoCliente, Integer codigousuariomovel) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw SET pesoRisco = ?  WHERE codigocliente = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, pesoRisco);
            sqlAlterar.setInt(2, codigoCliente);
            sqlAlterar.execute();
        }
        if(!UteisValidacao.emptyNumber(codigousuariomovel)){
            resolveRiscoUsuarioMovel(codigoCliente, pesoRisco, con);
        }

    }

    public void atualizarSMSRisco(Integer smsRisco, Integer codigoCliente) throws Exception {
        String sql = "UPDATE public.situacaoclientesinteticodw SET smsrisco = ?  WHERE codigocliente = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, smsRisco);
            sqlAlterar.setInt(2, codigoCliente);
            sqlAlterar.execute();
        }
    }

    public void atualizarIdade(final Date dia) throws Exception {
        StringBuilder sql = new StringBuilder("update situacaoclientesinteticodw \n")
                .append("set idade = (select date_part('year', age(current_date, p.datanasc))) \n")
                .append("from pessoa p where situacaoclientesinteticodw.codigopessoa = p.codigo \n")
                .append("and datanasc is not null and datanasc > '1899-12-31' \n")
                .append("and date_part('month', ").append("cast('").append(Uteis.getDataFormatoBD(dia)).append("' as date)) ").append("= date_part('month', datanasc) \n")
                .append("and date_part('day', ").append("cast('").append(Uteis.getDataFormatoBD(dia)).append("' as date)) ").append(" = date_part('day', datanasc)");
        executarConsulta(sql.toString(), con);
    }

    public void atualizarFrequenciaSemana() throws Exception{
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método atualizarFrequenciaSemana ");
        Calendar dataAtual = Calendario.getInstance();
        Calendar dataInicial = Calendario.getInstance();
        dataAtual.setTime(Calendario.getDataComHoraZerada(Calendario.hoje()));
        dataInicial.setTime(Calendario.getDataComHoraZerada(Calendario.hoje()));
        if (dataAtual.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY){
            dataInicial.add(Calendar.DAY_OF_MONTH, -(dataAtual.get(Calendar.DAY_OF_WEEK) -1));
        }
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### VALORES VARIÁVEL:");
        if (dataInicial != null) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### dataInicial - " + Uteis.getData(dataInicial.getTime(), "dd/MM/yyyy"));
        } else {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### dataInicial está nula");
        }
        if (dataAtual != null) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### dataAtual - " + Uteis.getData(dataAtual.getTime(), "dd/MM/yyyy"));
        } else {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### dataAtual está nula");
        }
        StringBuilder sql = new StringBuilder();
        sql.append("        update SituacaoClienteSinteticoDW \n");
        sql.append("        set frequenciaSemanal = sql.total\n");
        sql.append("        from ( \n");
        sql.append("                select count(sql.*) as total, cliente  \n");
        sql.append("                  from( \n");
        sql.append("                       select cast(dthrEntrada as date), a.cliente \n");
        sql.append("                       from acessoCliente a \n");
        sql.append("                       where dthrEntrada BETWEEN '").append(Uteis.getDataJDBC(dataInicial.getTime())).append(" 00:00:00' and '").append(Uteis.getDataJDBC(dataAtual.getTime())).append(" 23:59:59' \n");
        sql.append("                  group by cast(dthrEntrada as date) ,cliente ) sql \n");
        sql.append("                group by cliente \n");

        sql.append("                union all  \n");

        sql.append("                select 0 as total, codigo as cliente \n");
        sql.append("                from cliente \n");
        sql.append("                where codigo not in( \n");
        sql.append("                                     select distinct(cliente) \n");
        sql.append("                                     from acessoCliente a \n");
        sql.append("                                     where dthrEntrada BETWEEN '").append(Uteis.getDataJDBC(dataInicial.getTime())).append(" 00:00:00' and '").append(Uteis.getDataJDBC(dataAtual.getTime())).append(" 23:59:59') \n");
        // fim
        sql.append("        ) sql \n");
        sql.append("        where sql.Cliente = SituacaoClienteSinteticoDW.codigoCliente         \n");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### SQL EXECUTADO - " + sql.toString());
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.execute();
        }
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Saiu do método atualizarFrequenciaSemana");
    }

    public void sincronizarFrequenciaSemanalComSistemaTreino(){
        try{
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### Entrou no método sincronizarFrequenciaSemanalComSistemaTreino ");
            atualizarFrequenciaSemana();
            Uteis.logar(null, "TreinoWSConsumer -> INICIO - Frequência Semanal dos alunos");
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("freq, \n");
            sql.append("ARRAY_TO_STRING(ARRAY(select codigoCliente from situacaoclientesinteticodw where situacao = 'AT' and frequenciaSemanal = freq), ',') as codigosClientes \n");
            sql.append("from \n");
            sql.append("(select \n");
            sql.append("distinct(frequenciaSemanal) as freq \n");
            sql.append("from situacaoclientesinteticodw \n");
            sql.append("where situacao = 'AT') as sql \n");
            JSONArray jsonArray;
            try (Statement st = con.createStatement()) {
                try (ResultSet rs = st.executeQuery(sql.toString())) {
                    jsonArray = new JSONArray();
                    while (rs.next()) {
                        JSONObject json = new JSONObject();
                        json.put("frequenciaSemanal", rs.getInt("freq"));
                        json.put("codigosClientes", rs.getString("codigosClientes"));
                        jsonArray.put(json);
                    }
                }
            }
            String key = DAO.resolveKeyFromConnection(con);
            Map<String, String> params = new HashMap<>();
            params.put("dadosJSON", jsonArray.toString());
            String retorno = ExecuteRequestHttpService.executeHttpRequest(PropsService.getPropertyValue(key, PropsService.urlTreino) + "/prest/cliente/" + key + "/atualizarFrequenciaSemanal", params);
            JSONObject jsonObject = new JSONObject(retorno);
            if (jsonObject.has("erro")) {
                throw new Exception(retorno);
            }
            Uteis.logar(null, "TreinoWSConsumer -> FIM - Frequência Semanal dos alunos atualizada");
        }catch (Exception e){
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO EXECUTAR sincronizarFrequenciaSemanalComSistemaTreino. ERRO: " + e.getMessage());
        }
    }

    public void limparInformacoesCreditoTreino(Integer pessoa, boolean verificarSeTemContratoCreditoAtivo) throws Exception{
        boolean limpar = true;
        if (verificarSeTemContratoCreditoAtivo){
            StringBuilder sql = new StringBuilder();
            sql.append("select vendaCreditoTreino \n");
            sql.append("from contrato \n");
            sql.append("where pessoa = ").append(pessoa);
            sql.append("and vigenciaAteAjustada >= current_date ");
            boolean temContratoCreditoAtivo;
            boolean temContratoAtivo;
            try (Statement st = con.createStatement()) {
                try (ResultSet rs = st.executeQuery(sql.toString())) {
                    temContratoCreditoAtivo = false;
                    temContratoAtivo = false;
                    while (rs.next()) {
                        temContratoAtivo = true;
                        if (rs.getBoolean("vendaCreditoTreino")) {
                            temContratoCreditoAtivo = true;
                            break;
                        }
                    }
                }
            }
            limpar = (temContratoAtivo) && (!temContratoCreditoAtivo);
        }
        if (limpar){
            executarConsulta("UPDATE SituacaoClienteSinteticoDW set validarSaldoCreditoTreino=false, saldoCreditoTreino = 0,"
                    + " totalCreditoTreino = 0, quantidadeDiasExtra = 0 where codigopessoa = " + pessoa, con);
        }
    }

    public void atualizarInformacoesCreditoTreino(Integer pessoa, Date dataBase) throws Exception {
        /*
            Regra: Consultar todos os contratos vigentes:
              1 - Se o aluno não tiver contrato concomitante e o contrato atual for de crédito, então atualizar as
                 informações de crédito na tabela SituacaoClienteSinteticoDW; Caso contrário zerar as informações de crédito na tabela SituacaoClienteSinteticoDW
              2 - Se o aluno  tem contrato concomitante e algum dos contratos for de crédito, então atualizar as
                 informações de crédito na tabela SituacaoClienteSinteticoDW; Caso contrário zerar as informações de crédito na tabela SituacaoClienteSinteticoDW
         */
        StringBuilder sql = new StringBuilder();
        boolean atualizarInformacoes = ((pessoa != null) && (pessoa > 0));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (!atualizarInformacoes){
            sql.append("select * from contrato where vendaCreditoTreino = true limit 1");
            try (Statement st = con.createStatement()) {
                try (ResultSet rs = st.executeQuery(sql.toString())) {
                    atualizarInformacoes = rs.next();
                }
            }
        }
        if (atualizarInformacoes){
            // Fazer a correção somente se a academia utiliza planos de crédito.
            sql.delete(0, sql.length());
            sql.append("select cli.codigo as codigoCliente, cdt.quantidadeCreditoCompra, cd.QuantidadeDiasExtra, contrato.* \n");
            sql.append("from contrato  \n");
            sql.append("inner join cliente cli on cli.pessoa = contrato.pessoa \n");
            sql.append("left join contratoDuracao cd on cd.contrato = contrato.codigo \n");
            sql.append("left join contratoDuracaoCreditoTreino cdt on cdt.contratoDuracao = cd.codigo \n");
            sql.append("where '").append(sdf.format(dataBase)).append("' >= vigenciaDe  and '").append(sdf.format(dataBase)).append("' <= vigenciaAteAjustada \n");
            if ((pessoa != null) && (pessoa >0)){
                sql.append(" and contrato.pessoa = ").append(pessoa);
            }
            sql.append(" order by contrato.pessoa, contrato.vendaCreditoTreino"); // ordenar a lista por pessoa, contrato não créditos e depois contratos de crédito.
            String key;
            boolean achouContrato;
            try (Statement st = con.createStatement()) {
                try (ResultSet rs = st.executeQuery(sql.toString())) {
                    Integer codigoPessoa = 0;
                    Integer codigoContrato = 0;
                    Integer codigoCliente = 0;
                    Integer quantidadeCreditoCompra = 0;
                    Integer quantidadeDiasExtra = 0;
                    StringBuilder sqlAlterar = new StringBuilder();
                    try (PreparedStatement pstSaldo = con.prepareStatement("select sum(quantidade) as saldoCredito from controleCreditoTreino where contrato = ?")) {
                        key = DAO.resolveKeyFromConnection(con);
                        achouContrato = false;
                        while (rs.next()) {
                            codigoPessoa = rs.getInt("pessoa");
                            codigoContrato = rs.getInt("codigo");
                            codigoCliente = rs.getInt("codigoCliente");
                            quantidadeCreditoCompra = rs.getInt("quantidadeCreditoCompra");
                            quantidadeDiasExtra = rs.getInt("quantidadeDiasExtra");
                            if (rs.getBoolean("vendaCreditoTreino") && !rs.getString("situacao").equals("CA")) {
                                pstSaldo.setInt(1, codigoContrato);
                                achouContrato = true;
                                try (ResultSet rsSaldo = pstSaldo.executeQuery()) {
                                    if (rsSaldo.next()) {

                                        // Atualizar o saldo de créditos na tabela SituacaoClienteSinteticoDW
                                        Integer saldo = rsSaldo.getInt("saldoCredito");
                                        sqlAlterar.delete(0, sqlAlterar.length());
                                        sqlAlterar.append("UPDATE SituacaoClienteSinteticoDW set validarSaldoCreditoTreino=true, ");
                                        sqlAlterar.append("saldoCreditoTreino = ").append(saldo).append(", ");
                                        sqlAlterar.append("totalCreditoTreino = ").append(quantidadeCreditoCompra).append(", ");
                                        sqlAlterar.append("quantidadeDiasExtra = ").append(quantidadeDiasExtra);
                                        sqlAlterar.append("where codigopessoa = ").append(codigoPessoa);
                                        executarConsulta(sqlAlterar.toString(), con);

                                        // Atualizar o saldo de créditos na tabela contratoDuracaoCreditoTreino
                                        StringBuilder sqlAlt = new StringBuilder();
                                        sqlAlt.append(" update contratoDuracaoCreditoTreino set quantidadeCreditoDisponivel = ").append(saldo);
                                        sqlAlt.append(" from contratoDuracao cd  \n");
                                        sqlAlt.append(" where cd.codigo = contratoDuracaoCreditoTreino.contratoDuracao \n");
                                        sqlAlt.append(" and cd.contrato = ").append(codigoContrato);
                                        executarConsulta(sqlAlt.toString(), con);

                                        TreinoWSConsumer.sincronizarCreditosAluno(key, codigoCliente, saldo, quantidadeCreditoCompra);
                                    }
                                }
                            } else if(!achouContrato || UteisValidacao.emptyNumber(pessoa)) { // pode ser contrato cancelado e se não foi processado outro, deve ser atualizado
                                limparInformacoesCreditoTreino(codigoPessoa, false);
                                TreinoWSConsumer.sincronizarCreditosAluno(key, codigoCliente, 0, 0);
                            }
                        }
                    }
                }
            }
            if ((!achouContrato) && ((pessoa != null) && (pessoa > 0))){
                limparInformacoesCreditoTreino(pessoa, false);
                SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (UtilReflection.objetoMaiorQueZero(situacaoClienteSinteticoDWVO, "getCodigoCliente()")){
                    TreinoWSConsumer.sincronizarCreditosAluno(key, situacaoClienteSinteticoDWVO.getCodigoCliente(), 0, 0);
                }
            }

        }
    }

    public boolean atualizarInformacoesCrossfit(String key, Boolean atualizarTreino, Integer codigoPessoa) throws Exception {
        if (UteisValidacao.emptyNumber(codigoPessoa)) {
            return false;
        }
        Contrato contratoDAO = new Contrato(con);
        List<ContratoVO> contratos = contratoDAO.consultarContratosVigentesPorPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        contratoDAO = null;
        if (contratos.size() > 1) { // Concomitante
            String codigoContrato = "";
            for (ContratoVO contrato : contratos) {
                if (UteisValidacao.emptyString(codigoContrato)) {
                    codigoContrato = contrato.getCodigo().toString();
                } else {
                    codigoContrato += ", " + contrato.getCodigo().toString();
                }
            }
            try (ResultSet rs1 = SuperFacadeJDBC.criarConsulta("SELECT EXISTS( SELECT cm.codigo FROM contratomodalidade cm "
                    + " INNER JOIN modalidade m ON m.codigo = cm.modalidade "
                    + " WHERE contrato in (" + codigoContrato + " ) AND m.crossfit) as possuiCrossfit ", con)) {
                if (rs1.next()) {
                    SuperFacadeJDBC.executarConsulta("UPDATE contrato con SET crossfit = "
                            + rs1.getBoolean("possuiCrossfit")
                            + " WHERE con.codigo in (" + codigoContrato + " )", con);

                    SuperFacadeJDBC.executarConsulta("UPDATE SituacaoClienteSinteticoDW sw SET crossfit = "
                            + rs1.getBoolean("possuiCrossfit")
                            + " WHERE sw.codigocontrato in (" + codigoContrato + " )", con);

                    try (ResultSet rs2 = SuperFacadeJDBC.criarConsulta("SELECT sw.crossfit, sw.matricula FROM SituacaoClienteSinteticoDW sw"
                            + " WHERE sw.codigocontrato in (" + codigoContrato + " )", con)) {
                        if (rs2.next()) {
                            if (key != null && atualizarTreino) {
                                TreinoWSConsumer.atualizarCrossfit(key, rs2.getInt("matricula"), rs1.getBoolean("possuiCrossfit"));
                            }
                            return rs1.getBoolean("possuiCrossfit");
                        }
                    }
                }
            }
        } else if (contratos.size() == 1) {
            SuperFacadeJDBC.executarConsulta("UPDATE contrato con SET crossfit = "
                    + " (SELECT EXISTS(SELECT cm.codigo FROM contratomodalidade cm "
                    + " INNER JOIN modalidade m ON m.codigo = cm.modalidade "
                    + " WHERE contrato = con.codigo AND m.crossfit) AS cmdata)"
                    + " WHERE con.codigo = " + contratos.get(0).getCodigo(), con);

            SuperFacadeJDBC.executarConsulta("UPDATE SituacaoClienteSinteticoDW sw SET crossfit = "
                    + " (SELECT crossfit FROM contrato WHERE sw.codigocontrato = codigo)"
                    + " WHERE sw.codigocontrato = " + contratos.get(0).getCodigo(), con);
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT sw.crossfit, sw.matricula FROM SituacaoClienteSinteticoDW sw"
                    + " WHERE sw.codigocontrato = " + contratos.get(0).getCodigo(), con)) {
                if (rs.next()) {
                    if (key != null && atualizarTreino) {
                        TreinoWSConsumer.atualizarCrossfit(key, rs.getInt("matricula"), rs.getBoolean("crossfit"));
                    }
                    return rs.getBoolean("crossfit");
                }
            }
        }
        return false;
    }

    public Integer consultarFrequenciaSemanaAtual(Integer codigoCliente)throws Exception{
        String sql = "select frequenciaSemanal from SituacaoClienteSinteticoDW where codigoCliente = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoCliente);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("frequenciaSemanal");
                }
            }
        }
        return 0;
    }

    public FrequenciaAlunoWS consultarFrequenciaAluno(Integer codigoCliente)throws Exception{
        String sql = "select diasAcessoUltimoMes, diasAcessoMes2, diasAcessoMes3, diasAcessoMes4 from SituacaoClienteSinteticoDW where codigoCliente = ?";
        FrequenciaAlunoWS frequenciaAlunoWS;
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoCliente);
            try (ResultSet rs = pst.executeQuery()) {
                frequenciaAlunoWS = new FrequenciaAlunoWS();
                if (rs.next()) {
                    frequenciaAlunoWS.setQuantidadeAcessosMesAtual(rs.getInt("diasAcessoUltimoMes"));
                    frequenciaAlunoWS.setQuantidadeAcessosMes2(rs.getInt("diasAcessoMes2"));
                    frequenciaAlunoWS.setQuantidadeAcessosMes3(rs.getInt("diasAcessoMes3"));
                    frequenciaAlunoWS.setQuantidadeAcessosMes4(rs.getInt("diasAcessoMes4"));
                }
            }
        }
        return frequenciaAlunoWS;

    }

    public void atualizarSaldoContaCorrente() throws Exception {
        StringBuilder sql = new StringBuilder("update situacaoclientesinteticodw ");
        sql.append("set saldocontacorrentecliente = ").
                append("coalesce((select mcc.saldoatual from movimentocontacorrentecliente mcc ").
                append("where situacaoclientesinteticodw.codigopessoa = mcc.pessoa order by mcc.codigo desc limit 1), 0)");

        executarConsulta(sql.toString(), con);
    }

    public void atualizarClassificacaoEnvioSMS() throws Exception {
        StringBuilder sql = new StringBuilder("update situacaoclientesinteticodw ");
        sql.append("set envioSMSMarcadoClassif = true ");
        sql.append("where codigocliente in (select c.codigo from cliente c ");
        sql.append("inner join clienteclassificacao cclass on cclass.cliente = c.codigo ");
        sql.append("inner join classificacao classf on classf.codigo = cclass.classificacao ");
        sql.append("where classf.enviarsmsautomatico)");

        executarConsulta(sql.toString(), con);
    }

    public void atualizarVinculo(final int codCliente,
            final String tipoVinculo, final String nomeColaboradorAntigo,
            final String nomeColaboradorNovo) throws Exception {
        String chaveAntiga = String.format("%s-%s", new Object[]{tipoVinculo, nomeColaboradorAntigo}).toUpperCase();
        String chaveNova = String.format("%s-%s", new Object[]{tipoVinculo, nomeColaboradorNovo}).toUpperCase();
        String sql = "update situacaoclientesinteticodw " + "set colaboradores = replace(colaboradores, '" + chaveAntiga + "','" + chaveNova + "') where codigoCliente = " + codCliente;
        executarConsulta(sql, con);
    }

    public void atualizarVinculos(final String tipoVinculo, final String nomeColaboradorAntigo,
            final String nomeColaboradorNovo) throws Exception {
        String chaveAntiga = String.format("%s-%s", new Object[]{tipoVinculo, nomeColaboradorAntigo}).toUpperCase();
        String chaveNova = String.format("%s-%s", new Object[]{tipoVinculo, nomeColaboradorNovo}).toUpperCase();
        String sql = "update situacaoclientesinteticodw " + "set colaboradores = replace(colaboradores, '" + chaveAntiga + "','" + chaveNova + "') where colaboradores like('%" + chaveAntiga + "%')";
        executarConsulta(sql, con);
    }

    public ClienteVO montarDadosPreparados(ResultSet rs) throws Exception {
        ClienteVO c = new ClienteVO();
        c.setCodigo(rs.getInt("codigoCliente"));
        c.getPessoa().setCodigo(rs.getInt("codigoPessoa"));
        c.getPessoa().setDataCadastro(rs.getDate("datacadastro"));
        c.setCodigoMatricula(rs.getInt("matricula"));
        c.getPessoa().setNome(rs.getString("nomeCliente"));
        c.getPessoa().setSexo(rs.getString("sexoCliente"));
        c.getPessoa().setCfp(rs.getString("cfp"));
        c.getPessoa().setDataNasc(rs.getDate("dataNascimento"));
        c.getPessoa().getProfissao().setDescricao(rs.getString("profissao"));
        c.getPessoa().getCidade().setNome(rs.getString("cidade"));
        c.setSituacao(rs.getString("situacao"));
        c.getEmpresa().setCodigo(rs.getInt("empresaCliente"));
        c.setCodAcesso(rs.getString("codacessoCliente"));
        c.getPessoa().setRg(rs.getString("rgPessoa"));
        AcessoClienteVO ac = new AcessoClienteVO();
        ac.setCodigo(rs.getInt("uacodigo"));
        ac.setDataHoraEntrada(rs.getTimestamp("dthrentrada"));
        c.setUaCliente(ac);
        String sColabs = rs.getString("colaboradores");
        if (!sColabs.isEmpty() && sColabs.length() > 2) {
            sColabs = sColabs.replace("{", "").replace("}", "");
            String[] colabs = sColabs.split(",");
            for (int i = 0; i < colabs.length; i++) {
                String colab = colabs[i].replaceAll("\"", "");
                String vinc[] = colab.split("-");
                String tipo = colab.split("-")[0];
                String nome = colab.split("-")[1];
                String codCol = colab.split("-")[vinc.length - 1];
                VinculoVO v = new VinculoVO();
                v.getColaborador().getPessoa().setNome(nome);
                v.getColaborador().setTipoColaborador(tipo);
                try {
                    v.getColaborador().setCodigo(new Integer(codCol.trim()));
                }catch (Exception ex){
                    System.out.println("WTF! "+ex);
                }
                v.setTipoVinculo(tipo);
                c.getVinculoVOs().add(v);
            }
        }
        try {
            String sTels = rs.getString("telefonesCliente");
            if (!sTels.isEmpty() && sTels.length() > 2) {
                sTels = sTels.replace("{", "").replace("}", "");
                String[] tels = sTels.split(",");
                for (int i = 0; i < tels.length; i++) {
                    String[] tel = tels[i].split("-");
                    String tipoTelefone = tel[0];
                    String numero = tel[1];
                    TelefoneVO tVO = new TelefoneVO();
                    tVO.setTipoTelefone(tipoTelefone);
                    tVO.setNumero(numero);
                    c.getPessoa().getTelefoneVOs().add(tVO);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, SituacaoClienteSinteticoDW.class);
        }
        try {
            EnderecoVO enderecoVO = new EnderecoVO();
            String endereco = rs.getString("enderecosCliente");
            if(!endereco.isEmpty()){
                endereco = endereco.replace("{","").replace("}","");
                String [] enderecos = endereco.split(",");
                for (int i = 0; i < enderecos.length; i++) {
                    String[] ends = enderecos[i].split("-");
                    String end = (ends.length > 0 ? ends[0] : "");
                    String bairro = (ends.length > 1 ? ends[1] : "");
                    String tipoEndereco = (ends.length > 2 ? ends[2] : "");
                    enderecoVO.setEndereco(end);
                    enderecoVO.setBairro(bairro);
                    enderecoVO.setTipoEndereco(tipoEndereco);
                    c.getPessoa().getEnderecoVOs().add(enderecoVO);
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, EnderecoVO.class);
        }
        UsuarioMovelVO um = new UsuarioMovelVO();
        um.setAtivo(rs.getBoolean("um_ativo"));
        um.setCodigo(rs.getInt("um_codigo"));
        um.setNome(rs.getString("um_nome"));
        um.setSenha(rs.getString("um_senha"));
        c.setUsuarioMovelVO(um);
        c.setDadosSinteticoPreparados(true);
        try {
            c.setTitularPlanoCompartilhado(rs.getInt("titularplanocompartilhado"));
        } catch (Exception ex) {
            Uteis.logar(ex, SituacaoClienteSinteticoDW.class);
        }
        return c;
    }

    public ResultSet consultarClientesPreparadosResultSet(final boolean somenteNovos) throws Exception {
        StringBuilder sb = new StringBuilder(sqlClientes);
        if (somenteNovos) {
            sb.append(" left outer join situacaoclientesinteticodw sit on sit.codigocliente = c.codigo ");
            sb.append(" where sit.codigocliente is null OR (sit.situacaocontrato in ('AE', 'CR')) ");
            sb.append(" OR (sit.situacao = 'VI' and sit.codigopessoa in (select pessoa from contrato where pessoa = sit.codigopessoa))");
            sb.append(" or (sit.situacaocontrato in ('NO', 'AV') and sit.codigocontrato in ( ");
            sb.append(" select contrato from contratooperacao where tipooperacao in ('AT','CR')");
            sb.append(" and datainicioefetivacaooperacao <= '" + Uteis.getDataJDBC(negocio.comuns.utilitarias.Calendario.hoje()) + "' ");
            sb.append(" and datafimefetivacaooperacao >= '" + Uteis.getDataJDBC(negocio.comuns.utilitarias.Calendario.hoje()) + "')) ");
            sb.append(" or (c.situacao <> 'AT' and coalesce(c.titularplanocompartilhado,0) > 0)");
        }
        sb.append("order by c.codigo");
        return criarConsulta(sb.toString(), con);

    }

    public List<ClienteVO> consultarClientesPreparadosAcesso(final Date data, String clientesAtualizados,boolean frequencimetro) throws Exception {
        Date datapesquisa = null;
        if(frequencimetro){
            Calendar dataCalendar = Calendario.getInstance();
            dataCalendar.setTime(Calendario.hoje());
            int mes = dataCalendar.get(Calendar.MONTH) - 3;
            int ano = dataCalendar.get(Calendar.YEAR);
            if (mes < 0) {
                mes += 12;
                ano--;
            }
            dataCalendar.set(Calendar.MONTH, mes);
            dataCalendar.set(Calendar.YEAR, ano);
            dataCalendar.set(Calendar.DAY_OF_MONTH, 1);
            datapesquisa = dataCalendar.getTime();
        } else {
            datapesquisa = Uteis.obterDataAnterior(Calendario.hoje(), 29);
        }
        StringBuilder sb = new StringBuilder(String.format(sqlClientes.toString()+sqlClienteAcesso.toString(),Uteis.getDataFormatoBD(datapesquisa)));
        if(!UteisValidacao.emptyString(clientesAtualizados)){
            sb.append(" and c.codigo not in ( ").append(clientesAtualizados).append(")");
        }
        List<ClienteVO> result = new ArrayList<>();
        try (ResultSet rs = criarConsulta(sb.toString(), con)) {
            while (rs.next()) {
                result.add(montarDadosPreparados(rs));
            }
        }
        return result;
    }

    public ClienteVO consultarClientePreparado(final int codigoPessoa) throws Exception {
        String sb = sqlClientes + " where c.pessoa = " + codigoPessoa;
        try (ResultSet rs = criarConsulta(sb, con)) {
            if (rs.next()) {
                return montarDadosPreparados(rs);
            }
        }
        return new ClienteVO();
    }

    public SituacaoClienteSinteticoDWVO consultarClientePorEmailPessoa(final String email,
            int nivelMontarDados) throws Exception {
        String s = "select * from situacaoclientesinteticodw " + " where codigoPessoa in (select pessoa from email where email ilike('" + email + "'))";
        try (ResultSet rs = criarConsulta(s, con)) {
            if (rs.next()) {
                return montarDados(rs, nivelMontarDados);
            } else {
                return null;
            }
        }
    }

    public void alterarDia(Date dataNova, Integer codPessoa) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw SET dia = ? WHERE codigopessoa = ?";
        try (PreparedStatement ps = getCon().prepareStatement(sql)) {
            ps.setDate(1, Uteis.getDataJDBC(dataNova));
            ps.setInt(2, codPessoa);
            ps.execute();
        }
    }

    private void resolveUsuarioMovel(SituacaoClienteSinteticoDWVO obj, Connection c) throws Exception {
        try {
            String k = DAO.resolveKeyFromConnection(c);
            UsuarioMovel usuarioMovelDAO = new UsuarioMovel(c);
            if (UteisValidacao.emptyNumber(obj.getCodigoUsuarioMovel())
                    && !UteisValidacao.emptyString(k)) {
                try {
                    usuarioMovelDAO.gerarUsuarioMovelAluno(obj.getCodigoCliente(), k, "", obj.getMatricula().toString(), null, null, null, null);
                } catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logar(null, "Erro ao gerar usuário móvel para aluno: " + obj.getCodigoCliente());
                }
            }
            if (!k.isEmpty() && obj.getCodigoUsuarioMovel() > 0) {
                    UsuarioMovelVO uMovel = new UsuarioMovel(c).consultarPorChavePrimaria(
                            obj.getCodigoUsuarioMovel(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (uMovel != null && uMovel.getCodigo().intValue() > 0) {

                        uMovel.getCliente().setSituacaoClienteSinteticoVO(obj);
                    if (obj.isAtualizarTreino()) {
                        uMovel.getCliente().getPessoa().setDataCadastro(obj.getDataCadastro());
                        TreinoWSConsumer.sincronizarUsuario(k, uMovel.toUsuarioTreino());
                    } else if(obj.isIncrementarVersaoTreino()){
                        TreinoWSConsumer.incrementarVersaoCliente(k, uMovel.toUsuarioTreino());
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(null, "TreinoWS -> Não foi possível atualizar a situação do Aluno " + obj.getCodigoCliente() + " devido ao ERRO: " + e.getMessage());
        }
    }

    private void resolveRiscoUsuarioMovel(Integer codigoCliente, Integer newRisco, Connection c) throws Exception {
        try {
            String k = DAO.resolveKeyFromConnection(c);
            if (!k.isEmpty()) {
                TreinoWSConsumer.sincronizarRiscoUsuario(k, codigoCliente, newRisco);
            }
        } catch (Exception e) {
            Uteis.logar(null, "TreinoWS -> Não foi possível atualizar a situação do Aluno " + codigoCliente + " devido ao ERRO: " + e.getMessage());
        }
    }

    public void atualizarUsuariosTreino() throws Exception {
        List<SituacaoClienteSinteticoDWVO> lista;
        try (ResultSet rs = criarConsulta("SELECT * FROM situacaoclientesinteticodw WHERE codigousuariomovel > 0", con)) {
            lista = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        for (SituacaoClienteSinteticoDWVO sw : lista) {
            sw.setAtualizarTreino(true);
            try {
                resolveUsuarioMovel(sw, con);
            } catch (Exception e) {
                Uteis.logar(e, SituacaoClienteSinteticoDW.class);
            }

        }
    }

    public void atualizarUsuariosTreino(Connection c) throws Exception {
        Uteis.logar(null, "Atualizando Codigos de Acesso no Sintetico...");
        SuperFacadeJDBC.executarConsulta("update SituacaoClienteSinteticoDW sit "
                + "set codacessocliente = (select codacesso from cliente where codigo = sit.codigocliente)", c);
        Uteis.logar(null, "Codigos de Acesso atualizados!");
        List<SituacaoClienteSinteticoDWVO> lista;
        try (ResultSet rs = criarConsulta("SELECT * FROM situacaoclientesinteticodw WHERE codigousuariomovel > 0", c)) {
            lista = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        for (SituacaoClienteSinteticoDWVO sw : lista) {
            SuperFacadeJDBC.executarConsulta(String.format("UPDATE usuariomovel SET empresa = %s where codigo = %s",
                    sw.getEmpresaCliente(), sw.getCodigoUsuarioMovel()), c);
            sw.setAtualizarTreino(true);
            try {
                resolveUsuarioMovel(sw, c);
            } catch (Exception e) {
                Uteis.logar(e, SituacaoClienteSinteticoDW.class);
            }

        }
    }

    public void atualizarUsuariosTreinoLista(Connection c, List<Integer> listaCodigos) throws Exception {
        Uteis.logar(null, "Atualizando Codigos de Acesso no Sintetico...");
        String codigos = "";
        for(Integer codigoPessoa : listaCodigos){
            SuperFacadeJDBC.executarConsulta("UPDATE situacaoclientesinteticodw " +
                    "sw SET codigousuariomovel = (select codigo from usuariomovel " +
                    "where cliente = sw.codigocliente) where codigopessoa = "+codigoPessoa, con);
            codigos += ", "+codigoPessoa;
        }
        List<SituacaoClienteSinteticoDWVO> lista;
        try (ResultSet rs = criarConsulta("SELECT * FROM situacaoclientesinteticodw WHERE codigopessoa in ("
                + codigos.replaceFirst(",", "") + ")", c)) {
            lista = montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        for (SituacaoClienteSinteticoDWVO sw : lista) {
            sw.setAtualizarTreino(true);
            try {
                resolveUsuarioMovel(sw, c);
            } catch (Exception e) {
                Uteis.logar(e, SituacaoClienteSinteticoDW.class);
            }

        }
    }

    public void atualizarUsuarioTreino(final int codPessoa) throws Exception {
        try (ResultSet rs = criarConsulta("SELECT * FROM situacaoclientesinteticodw WHERE codigopessoa = " + codPessoa, con)) {
            if (rs.next()) {
                SituacaoClienteSinteticoDWVO sw = montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (sw != null && sw.getCodigo() > 0) {
                    sw.setAtualizarTreino(true);
                    try {
                        resolveUsuarioMovel(sw, con);
                    } catch (Exception e) {
                        Uteis.logar(e, SituacaoClienteSinteticoDW.class);
                    }
                }
            }
        }
    }

    public void registrarUltimoAcesso(int codigoCliente, Date dataUltimoAcesso) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw set dataultimoacesso =? WHERE codigocliente = ?";
        if(dataUltimoAcesso != null){
            sql += " and (dataultimoacesso is null or dataultimoacesso < ?)";
        }
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (dataUltimoAcesso != null) {
                sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataUltimoAcesso));

            } else {
                sqlAlterar.setNull(1, 0);
            }
            sqlAlterar.setInt(2, codigoCliente);
            if (dataUltimoAcesso != null) {
                sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(dataUltimoAcesso));
            }

            sqlAlterar.execute();
        }
    }

    public void atualizarUltimoAcesso(int codigoCliente) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw SET dataultimoacesso = (SELECT MAX(dthrentrada) FROM acessocliente WHERE cliente = ?) WHERE codigocliente = ?";

        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoCliente);
            sqlAlterar.setInt(2, codigoCliente);
            sqlAlterar.execute();
        }
    }

    public void registrarUltimoAcessoSaida(int codigoCliente, Date dataUltimoAcessoSaida) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw set datasaidaacesso =? WHERE codigocliente = ?";
        if(dataUltimoAcessoSaida != null){
            sql += " and (datasaidaacesso is null or datasaidaacesso < ?) and (dataultimoacesso is not null)";
        }
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (dataUltimoAcessoSaida != null) {
                sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataUltimoAcessoSaida));
            } else {
                sqlAlterar.setNull(1, 0);
            }
            sqlAlterar.setInt(2, codigoCliente);
            if (dataUltimoAcessoSaida != null) {
                sqlAlterar.setTimestamp(3, Uteis.getDataJDBCTimestamp(dataUltimoAcessoSaida));
            }
            sqlAlterar.execute();
        }
    }

    private static HashMap montarMapaVinculo(String vinculosColaborador){
        HashMap map = new HashMap<Integer,String>();
        for(String cod : vinculosColaborador.split("/")){
            if(UteisValidacao.emptyString(cod)){
                continue;
            }else {
                String dados[] = cod.split("-");
                String codCol = dados[dados.length - 1];
                String tipoVin = dados[0];
                map.put(new Integer(codCol),tipoVin);
            }
        }
        return map;
    }
    public static boolean validarVinculoColaboradores(List<ColaboradorVO> cols , String vinculosColaborador) {
        HashMap map = montarMapaVinculo(vinculosColaborador);

        for(ColaboradorVO col : cols){
            if(map.get(col.getCodigo()) != null){
                return true;
            }
        }
        return false;
    }
    public static boolean validarVinculoColaboradores(String sqlCols , String vinculosColaborador) {
        try {
            HashMap map = montarMapaVinculo(vinculosColaborador);
            for (String col : sqlCols.split(",")) {
                if (map.get(new Integer(col)) != null) {
                    return true;
                }
            }
        }catch (Exception ignored){
            return false;
        }
        return false;
    }

    public static boolean validarVinculoColaboradores(String sqlCols, String vinculosColaborador, TipoColaboradorEnum tipoColaboradorEnum) {
        try {
            HashMap map = montarMapaVinculo(vinculosColaborador);
            for (String col : sqlCols.split(",")) {
                Object tipoColaborador = map.get(new Integer(col));
                if (tipoColaborador != null) {
                    if (tipoColaborador.toString().equals(tipoColaboradorEnum.getSigla())) {
                        return true;
                    }
                }
            }
        } catch (Exception ignored) {
            return false;
        }
        return false;
    }

    public ClienteVO consultarEnvioSMS(ClienteVO cliente) throws Exception{
        String sql = "select datavigenciaateajustada,"
                + "empresaCliente,"
                + "sexoCliente, "
                + "datarenovacaocontrato, "
                + "pesorisco, "
                + "smsrisco, "
                + "telCelColab, "
                + "enviosmsmarcadoclassif, "
                + "nomecliente,dataultimoacesso,datasaidaacesso "
                + "FROM situacaoclientesinteticodw  where codigocliente = "+cliente.getCodigo();
        try (PreparedStatement stm = con.prepareStatement(sql)) {
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    cliente.setSituacaoClienteSinteticoVO(new SituacaoClienteSinteticoDWVO());
                    cliente.getSituacaoClienteSinteticoVO().setDataVigenciaAteAjustada(rs.getDate("datavigenciaateajustada"));
                    cliente.getSituacaoClienteSinteticoVO().setDataRenovacaoContrato(rs.getDate("datarenovacaocontrato"));
                    cliente.getSituacaoClienteSinteticoVO().setPesoRisco(rs.getInt("pesorisco"));
                    cliente.getSituacaoClienteSinteticoVO().setSMSRisco(rs.getInt("smsrisco"));
                    cliente.getSituacaoClienteSinteticoVO().setTelCelColVinculados(rs.getString("telCelColab"));
                    cliente.getSituacaoClienteSinteticoVO().setEnvioSMSMarcadoClassif(rs.getBoolean("enviosmsmarcadoclassif"));
                    cliente.getSituacaoClienteSinteticoVO().setDataUltimoAcesso(rs.getDate("dataultimoacesso"));
                    cliente.getSituacaoClienteSinteticoVO().setDataSaidaAcesso(rs.getDate("datasaidaacesso"));
                    cliente.getSituacaoClienteSinteticoVO().setNomeCliente(rs.getString("nomecliente"));
                    cliente.setPessoa(new PessoaVO());
                    cliente.getPessoa().setNome(rs.getString("nomecliente"));
                    cliente.setEmpresa(new EmpresaVO());
                    cliente.getEmpresa().setCodigo(rs.getInt("empresaCliente"));
                    cliente.getSituacaoClienteSinteticoVO().setEmpresaCliente(rs.getInt("empresaCliente"));
                    cliente.getSituacaoClienteSinteticoVO().setSexoCliente(rs.getString("sexoCliente"));
                    cliente.getPessoa().setSexo(rs.getString("sexoCliente"));
                    return cliente;
                } else {
                    return null;
                }
            }
        }
    }

    public void inicializarDadosAcesso(Date data) throws Exception{
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana4  = -1,  diasacessosemana3= -1,   diasacessosemana2  = -1, diasacessosemanapassada  = -1", con);
        Date dataFim = Uteis.somarDias(data, -1);
        Date dataInicio = Uteis.somarDias(dataFim, -6);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemanapassada  = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

        dataInicio = Uteis.somarDias(dataInicio, -7);
        dataFim = Uteis.somarDias(dataFim, -7);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana2 = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

        dataInicio = Uteis.somarDias(dataInicio, -7);
        dataFim = Uteis.somarDias(dataFim, -7);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana3 = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

        dataInicio = Uteis.somarDias(dataInicio, -7);
        dataFim = Uteis.somarDias(dataFim, -7);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana4 = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

    }
    public void inicializarDadosAcessoProcessamentoContralado(Date data, String clientes) throws Exception{
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana4  = -1,  diasacessosemana3= -1,   diasacessosemana2  = -1, diasacessosemanapassada  = -1"
                + " where codigocliente in (" + clientes +")", con);
        Date dataFim = Uteis.somarDias(data, -1);
        Date dataInicio = Uteis.somarDias(dataFim, -6);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemanapassada  = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where cli.codigo in ("+clientes+") and tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

        dataInicio = Uteis.somarDias(dataInicio, -7);
        dataFim = Uteis.somarDias(dataFim, -7);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana2 = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where cli.codigo in ("+clientes+") and tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

        dataInicio = Uteis.somarDias(dataInicio, -7);
        dataFim = Uteis.somarDias(dataFim, -7);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana3 = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where cli.codigo in ("+clientes+") and tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

        dataInicio = Uteis.somarDias(dataInicio, -7);
        dataFim = Uteis.somarDias(dataFim, -7);
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessosemana4 = 0 where codigocliente in (select distinct cli.codigo from cliente cli left join periodoacessocliente per on per.pessoa = cli.pessoa where cli.codigo in ("+clientes+") and tipoacesso in ('CA','BO','TO','TD','PL','AA','DI','RT','RR','RA','RC') AND (('"+Uteis.getDataJDBC(dataInicio)+"' between datainicioacesso AND  datafinalacesso) OR ('"+Uteis.getDataJDBC(dataFim)+"' between datainicioacesso and  datafinalacesso)))", con);

    }
    public void inicializarFrequencimetro(String clientesAtualizados) throws Exception{
        String where = "";
        if(!clientesAtualizados.equals("")){
            where = " where codigocliente not in (" +clientesAtualizados+")";
        }
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set diasacessomes4 = 0,  diasacessomes3  = 0, diasacessomes2  = 0, diasacessoultimomes = 0, mediaDiasAcesso4Meses = 0 "
                + where , con);

    }

    public void processarDadosAcessoDia(Date data, String clientesAtualizados) throws Exception{
        Date dataFim = Uteis.getDataComUltimaHora(Uteis.somarDias(Calendario.hoje(), -1));
        Date dataInicio = Uteis.getDataComHoraZerada(Uteis.somarDias(dataFim, -6));
        String semana = "";
        for (int i = 0; i < 4; i ++){
            switch (i){
                case 0:
                    semana = "diasacessosemanapassada";
                    break;
                case 1:
                    semana = "diasacessosemana2";
                    break;
                case 2:
                    semana = "diasacessosemana3";
                    break;
                case 3:
                    semana = "diasacessosemana4";
                    break;
            }

            StringBuilder sb = new StringBuilder(String.format(sqlClienteAcessoPeriodo.toString(),Uteis.getDataJDBCTimestamp(dataInicio),Uteis.getDataJDBCTimestamp(dataFim)));
            if(!UteisValidacao.emptyString(clientesAtualizados)){
                sb.append(" and c.codigo not in ( ").append(clientesAtualizados).append(")");
            }
            try (ResultSet rs = criarConsulta(sb.toString(), con)) {
                while (rs.next()) {
                    SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set " + semana + " = (select count(distinct cast(dthrentrada as date))\n" +
                            "                    from acessocliente \n" +
                            "                    where cliente = " + rs.getInt("codigo") + " and dthrentrada between '" + Uteis.getDataJDBCTimestamp(dataInicio) + "' and '" + Uteis.getDataJDBCTimestamp(dataFim) + "') where codigocliente = " + rs.getInt("codigo"), con);
                }
            }
            dataInicio = Uteis.somarDias(dataInicio, -7);
            dataFim = Uteis.somarDias(dataFim, -7);
        }
    }

    public void atualizarBaseOffLineZillyonAcesso(String key, Integer codigoPessoa) throws Exception {
        if ((codigoPessoa == null) || (codigoPessoa <= 0)) {
            throw new ConsistirException("Para acessar o método atualizarBaseOffLineZillyonAcesso é necessário informar o codigo da pessoa.");
        }
        DadosAcessoOffline dadosAcessoOffline = new DadosAcessoOffline(con);
        dadosAcessoOffline.preencherDadosSemCommit(key, codigoPessoa);
        dadosAcessoOffline = null;
        SuperControle.notificarOuvintes(DadosAcessoOfflineVO.CHAVE_NOTIFICAR_ATUALIZAR_BASE_PESSOA + "(" + codigoPessoa + ")", PropsService.getPropertyValue("urlNotificacaoAcesso"), key);
    }

    public void atualizarFotoZillyonAcesso(String key, Integer codigoPessoa) throws Exception {
        if ((codigoPessoa == null) || (codigoPessoa <= 0)) {
            throw new ConsistirException("Para acessar o método atualizarFotoZillyonAcesso é necessário informar o codigo da pessoa.");
        }
        SuperControle.notificarOuvintes(DadosAcessoOfflineVO.CHAVE_NOTIFICAR_ATUALIZAR_FOTO_PESSOA + "(" + codigoPessoa + ")", PropsService.getPropertyValue("urlNotificacaoAcesso"), key);
    }

    public String processarFrequencimetroDia(Date data, String clientesAtualizados) throws Exception{
        String clienteProcessados = "";
        Date dataFim = Uteis.obterUltimoDiaMesUltimaHora(data);
        Date dataInicio = Uteis.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataFim));
        String mes = "";
        for (int i = 0; i < 4; i ++){
            switch (i){
                case 0:
                    mes = "diasacessoultimomes";
                    break;
                case 1:
                    mes = "diasacessomes2";
                    break;
                case 2:
                    mes = "diasacessomes3";
                    break;
                case 3:
                    mes = "diasacessomes4";
                    break;
            }

            StringBuilder sb = new StringBuilder(String.format(sqlClienteAcessoPeriodo.toString(),Uteis.getDataJDBCTimestamp(dataInicio),Uteis.getDataJDBCTimestamp(dataFim)));
            if(!UteisValidacao.emptyString(clientesAtualizados)){
                sb.append(" and c.codigo not in ( ").append(clientesAtualizados).append(")");
            }
            try (ResultSet rs = criarConsulta(sb.toString(), con)) {
                while (rs.next()) {
                    SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set " + mes + " = (select count(distinct cast(dthrentrada as date))\n" +
                            "                    from acessocliente \n" +
                            "                    where cliente = " + rs.getInt("codigo") + " and dthrentrada between '" + Uteis.getDataJDBCTimestamp(dataInicio) + "' and '" + Uteis.getDataJDBCTimestamp(dataFim) + "') where codigocliente = " + rs.getInt("codigo"), con);
                    if (!clienteProcessados.contains("," + rs.getInt("codigo") + ",") && !clienteProcessados.endsWith("," + rs.getInt("codigo"))) {
                        clienteProcessados += "," + rs.getInt("codigo");
                    }
                }
            }
            dataFim = Uteis.getDataComUltimaHora(Uteis.somarDias(dataInicio, -1));
            dataInicio = Uteis.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataFim));

        }
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw  set mediadiasacesso4meses  = ((diasacessomes4 + diasacessomes3 + diasacessomes2  + diasacessoultimomes)/4)::integer", con);
        return clienteProcessados;
    }

    public JSONArray consultarClientesModalidade(Integer empresa, Integer matricula,
            String nome, Integer modalidade, Boolean somenteAtivos, Integer horarioTurma, Date dia) throws Exception {
        StringBuilder sb = new StringBuilder();

        sb.append(" SELECT distinct coalesce(sc.telefonescliente,'')as telefonescliente, sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.totalCreditoTreino,");
        sb.append(" sc.nomecliente, sc.matricula, sc.codigopessoa,sc.codigocliente,\n");
        sb.append("sc.cidade,sc.sexocliente, coalesce((select email from email where pessoa  = sc.codigopessoa limit 1), '') as email, pes.spiviclientid, \n");
        sb.append(" sc.codigocontrato, cdt.tipohorario, sc.situacao, sc.situacaocontrato, sc.datanascimento, \n");
        sb.append(" CASE WHEN futuro.codigo > 0 THEN TRUE ELSE FALSE END AS contratoFuturo, futuro.codigo as codContratoFuturo, ");

        if (!UteisValidacao.emptyNumber(horarioTurma) && dia != null) {
            sb.append(" case when ad.codigo > 0 and (select codigo from reposicao where ad.reposicao = codigo and horarioturma = ht.codigo ) is null THEN TRUE ELSE FALSE END AS desmarcado,\n");
            sb.append(" (p.codigo) is not null as confirmado \n");
        }else{
            sb.append(" FALSE AS desmarcado,\n");
            sb.append(" FALSE as confirmado \n");
        }
        sb.append(" FROM situacaoclientesinteticodw sc \n");
        sb.append(" INNER JOIN pessoa pes on pes.codigo = sc.codigopessoa \n");
        sb.append(" LEFT JOIN contratoduracao cd ON cd.contrato = sc.codigocontrato \n");
        sb.append(" LEFT JOIN contratoduracaocreditotreino cdt ON cdt.contratoduracao = cd.codigo \n");
        sb.append(" LEFT JOIN contrato futuro ON (futuro.pessoa = codigopessoa AND sc.situacao <> 'AT' AND sc.situacao <> 'TR' AND futuro.vigenciade > '").append(Calendario.hoje()).append("') \n");
        if (!UteisValidacao.emptyNumber(modalidade)) {
            sb.append(" left JOIN contratomodalidade cm ON cm.contrato = sc.codigocontrato or cm.contrato = futuro.codigo \n");
        }
        if (!UteisValidacao.emptyNumber(horarioTurma) && dia != null) {
            sb.append(" INNER JOIN horarioturma ht on ht.codigo = ").append(horarioTurma).append(" ");
            sb.append(" LEFT JOIN reposicao rep on rep.horarioturma = ").
                    append(horarioTurma).append(" and rep.datareposicao = '").append(Uteis.getDataFormatoBD(dia)).append("' ").
                    append("and rep.cliente = sc.codigocliente \n");
            sb.append(" LEFT JOIN matriculaalunohorarioturma maht on maht.horarioturma = ").
                    append(horarioTurma).append(" and '").append(Uteis.getDataFormatoBD(dia)).append("' ").
                    append("between maht.dataInicio and maht.datafim and maht.pessoa = sc.codigopessoa \n");
            sb.append(" LEFT JOIN presenca p on p.dadosturma = maht.codigo \n");
            sb.append(" and p.datapresenca = '").append(Uteis.getDataFormatoBD(dia)).append("' ");
            sb.append(" LEFT JOIN auladesmarcada ad on  sc.codigocontrato = ad.contrato and ad.empresa = sc.empresacliente \n");
            sb.append("     and ad.horarioturma = maht.horarioturma and ad.dataorigem = '").append(Uteis.getDataFormatoBD(dia)).append("' ");

        }

        sb.append(" WHERE 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append(" and (sc.empresacliente = ").append(empresa).append(" or (select acessoSomenteComAgendamento from empresa where codigo = ").append(empresa).append(")) \n");
        }

        if (!UteisValidacao.emptyNumber(horarioTurma) && dia != null) {
            sb.append(" and ((rep.datareposicao = '").append(Uteis.getDataFormatoBD(dia)).append("')").
                    append(" or ('").append(Uteis.getDataFormatoBD(dia)).append("' between maht.dataInicio and maht.datafim and maht.pessoa = sc.codigopessoa))\n");
        }
        if(somenteAtivos){
            sb.append(" AND (sc.situacao = 'AT' OR CASE WHEN futuro.codigo > 0 THEN TRUE ELSE FALSE END) \n");
        }
        // CORREÇÃO DE SEGURANÇA: Validar entrada e escapar caracteres perigosos
        if (!UteisValidacao.emptyString(nome)) {
            // Validar entrada para prevenir SQL injection
            if (Uteis.isValidStringValue(nome)) {
                // Escapar aspas simples para prevenir SQL injection
                String nomeEscapado = nome.replace("'", "''");
                sb.append(" AND sc.nomeconsulta LIKE remove_acento_upper('%").append(nomeEscapado).append("%') ");
            } else {
                throw new SecurityException("Nome de busca contém caracteres não permitidos");
            }
        }
        if (!UteisValidacao.emptyNumber(matricula)) {
            sb.append(" AND sc.matricula = ").append(matricula).append(" ");
        }
        if (!UteisValidacao.emptyNumber(modalidade)) {
            sb.append(" AND cm.modalidade = ").append(modalidade).append(" ");
        }
        sb.append(" ORDER BY nomecliente ");

        JSONArray json;
        TurmasServiceImpl turmasService;
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sb.toString(), getCon())) {
            json = new JSONArray();
            turmasService = new TurmasServiceImpl(getCon());
            while (rs.next()) {
                JSONObject obj = new JSONObject();
                obj.put("matricula", rs.getString("matricula"));
                obj.put("codigoCliente", rs.getString("codigocliente"));
                obj.put("nome", rs.getString("nomecliente"));
                obj.put("codigoPessoa", rs.getString("codigopessoa"));
                obj.put("codigoContrato", rs.getString("codigocontrato"));
                obj.put("telefones", rs.getString("telefonescliente"));
                obj.put("validarSaldoCreditoTreino", rs.getBoolean("validarSaldoCreditoTreino"));
                obj.put("saldoCreditoTreino", rs.getInt("saldoCreditoTreino"));
                obj.put("totalCreditoTreino", rs.getInt("totalCreditoTreino"));
                obj.put("tipohorario", rs.getInt("tipohorario"));
                obj.put("situacao", rs.getString("situacao"));
                obj.put("situacaoContrato", rs.getString("situacaoContrato"));
                obj.put("desmarcado", rs.getBoolean("desmarcado"));
                obj.put("confirmado", rs.getBoolean("confirmado"));
                if (rs.getBoolean("validarSaldoCreditoTreino")) {
                    // usado para limitar a marcação de aulas acima do número de créditos para esse tipo de contrato.
                    Integer qtdReposicoesFuturas = turmasService.reposicoesFuturasParaUtilizarContratosCreditoSemTurma(rs.getInt("codigocontrato"), Calendario.hoje());
                    obj.put("qtdReposicoesFuturas", qtdReposicoesFuturas);
                } else {
                    obj.put("qtdReposicoesFuturas", 0);
                }
                obj.put("contratoFuturo", rs.getBoolean("contratoFuturo"));
                if (rs.getBoolean("contratoFuturo")) {
                    obj.put("qtdCreditoFuturo", 0);
                    obj.put("codContratoFuturo", rs.getInt("codContratoFuturo"));
                    try (ResultSet dadosCredito = SuperFacadeJDBC.criarConsulta("select cdt.tipohorario from contrato c  inner JOIN contratoduracao cd ON cd.contrato = c.codigo "
                            + "   inner JOIN contratoduracaocreditotreino cdt ON cdt.contratoduracao = cd.codigo  where c.codigo =" + rs.getInt("codContratoFuturo"), con)) {
                        while (dadosCredito.next()) {
                            obj.put("validarSaldoCreditoTreino", true);
                            obj.put("tipohorario", dadosCredito.getInt("tipohorario"));
                            Integer qtdCreditoFuturo = this.qtdCreditoCompra(rs.getInt("codContratoFuturo"));
                            obj.put("qtdCreditoFuturo", qtdCreditoFuturo);
                            Integer qtdReposicoesFuturas = turmasService.reposicoesFuturasParaUtilizarContratosCreditoSemTurma(rs.getInt("codContratoFuturo"), Calendario.hoje());
                            obj.put("qtdReposicoesFuturas", qtdReposicoesFuturas);
                        }
                    }
                    JSONArray modalidadesFuturas;
                    try (ResultSet modalidadesContratoFuturo = SuperFacadeJDBC.criarConsulta("SELECT cm.modalidade \n" +
                            "FROM contrato \n" +
                            "LEFT JOIN contratomodalidade cm ON contrato.codigo = cm.contrato \n" +
                            "WHERE contrato.codigo = " + rs.getInt("codContratoFuturo"), con)) {
                        modalidadesFuturas = new JSONArray();
                        while (modalidadesContratoFuturo.next()) {
                            modalidadesFuturas.put(modalidadesContratoFuturo.getInt("modalidade"));
                        }
                    }
                    obj.put("modalidadesContratoFuturo", modalidadesFuturas);
                }
                try {
                    obj.put("dataNascimento", rs.getString("dataNascimento"));
                }catch (Exception ignore){}
                try {
                    obj.put("email", rs.getString("email"));
                    obj.put("sexo", rs.getString("sexocliente"));
                    obj.put("cidade", rs.getString("cidade"));
                    obj.put("spiviClientID", rs.getInt("spiviclientid"));
                }catch (Exception ignore){}
                json.put(obj);
            }
        }
        turmasService = null;
        return json;
    }

    public JSONObject obterDadosSaldoAluno(String matricula) throws Exception{
        JSONObject obj = new JSONObject();
        try (ResultSet rs = criarConsulta("select \n" +
                "* ,\n" +
                "(select vendacreditotreino from plano where codigo in (select plano from contrato where codigo = codigocontrato)) as contratoCredito\n" +
                "from situacaoclientesinteticodw where matricula = " + matricula, con)) {

            if (rs.next()) {
                obj.put("totalcreditotreino", rs.getInt("totalcreditotreino"));
                obj.put("saldocreditotreino", rs.getInt("saldocreditotreino"));
                obj.put("contratoCredito", rs.getBoolean("contratoCredito"));
            } else {
                obj.put("totalcreditotreino", 0);
                obj.put("saldocreditotreino", 0);
                obj.put("contratoCredito", false);
            }
        }
        return obj;
    }

    public JSONArray obterDadosAlunosTreinoResumidos() throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT matricula, nomecliente, codigopessoa, v.colaborador, s.situacao  FROM situacaoclientesinteticodw s \n");
        sql.append("INNER JOIN usuariomovel u ON u.cliente = s.codigocliente \n");
        sql.append("LEFT JOIN vinculo v ON s.codigocliente = v.cliente AND v.tipovinculo = 'TW' \n");
        sql.append("ORDER BY nomecliente ");
        JSONArray array;
        try (ResultSet set = criarConsulta(sql.toString(), con)) {
            array = new JSONArray();
            while (set.next()) {
                JSONObject obj = new JSONObject();
                obj.put("matricula", set.getString("matricula"));
                obj.put("nome", set.getString("nomecliente"));
                obj.put("codigopessoa", set.getString("codigopessoa"));
                obj.put("situacao", set.getString("situacao"));
                obj.put("professor", set.getInt("colaborador"));
                array.put(obj);
            }
        }
        return array;
    }
    public SituacaoClienteSinteticoDWVO obterSituacaoContratoCliente(int codigoCliente) throws Exception{
        String sql = "SELECT codigocontrato, situacaoContrato FROM situacaoclientesinteticodw WHERE codigoCliente = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoCliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    SituacaoClienteSinteticoDWVO situacaoClienteSinteticoDWVO = new SituacaoClienteSinteticoDWVO();
                    situacaoClienteSinteticoDWVO.setCodigoContrato(rs.getInt("codigoContrato"));
                    situacaoClienteSinteticoDWVO.setSituacaoContrato(rs.getString("situacaoContrato"));
                    return situacaoClienteSinteticoDWVO;
                }
            }
        }
        return new SituacaoClienteSinteticoDWVO();
    }

    public String obterSituacaoCliente(int codigoCliente) throws Exception{
        String sql = "SELECT situacao FROM situacaoclientesinteticodw WHERE codigoCliente = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoCliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("situacao");
                }
            }
        }
        return "";
    }

    public List<SituacaoClienteSinteticoDWVO> consultarClienteComCPF(Integer codigoEmpresa,int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from situacaoclientesinteticodw where cpf is not null and cpf <> '' and empresacliente = "+codigoEmpresa);
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public List<SituacaoClienteSinteticoDWVO> consultarClienteComMatricula(Integer codigoEmpresa,int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from situacaoclientesinteticodw where matricula is not null and matricula <> 0 and empresacliente = "+codigoEmpresa);
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }


    public void marcarStatusBG(Integer matricula) throws Exception{
        SuperFacadeJDBC.executarConsulta("update situacaoclientesinteticodw set statusbg = 'S' where matricula = " + matricula, con);
    }

    public void gravarClienteAtualizar(SituacaoClienteSinteticoDWVO cliente) throws Exception{
        try {
            String sql = "insert into clienteatualizar(cliente, hora) values (?, ?)";
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setInt(1, cliente.getCodigoCliente());
                stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                stm.execute();
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public void atualizarNomesBaseadoCliente() throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE situacaoclientesinteticodw s");
        sql.append(" SET nomecliente = (");
        sql.append(" SELECT p.nome");
        sql.append(" FROM pessoa p");
        sql.append(" INNER JOIN cliente c ON c.pessoa = p.codigo");
        sql.append(" WHERE s.codigocliente = c.codigo);");
        SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
        sql = new StringBuilder();
        sql.append(" UPDATE situacaoclientesinteticodw s");
        sql.append(" SET nomeconsulta = remove_acento_upper(nomecliente);");
        SuperFacadeJDBC.executarConsultaUpdate(sql.toString(), con);
    }

    public List<SituacaoClienteSinteticoDWVO> consultarAlunosSemSituacaoContrato(int nivelMontarDados) throws Exception{

        String sql = "select * from situacaoclientesinteticodw  where situacao <> 'VI' and situacaocontrato=''";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(rs, nivelMontarDados);
            }
        }
    }

    public SituacaoClienteSinteticoDWVO qtdCreditoAluno(Integer codigoCliente) throws Exception  {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM situacaoclientesinteticodw    \n")
                .append(" WHERE codigocliente = ")
                .append(codigoCliente);
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDadosBasico(rs);
                }
            }
        }
        return null;
    }

    public Integer obterCodigoContratoCliente(int codigoPessoa) throws Exception {
        String sql = " select codigocontrato from situacaoclientesinteticodw where codigopessoa = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoPessoa);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("codigocontrato");
                }
            }
            return null;
        } catch (Exception e) {
            throw new Exception("Erro na consulta de do contrato do cliente");
        }
    }

    public void alterarSituacaoPorCodigoPessoa(Integer codigoPessoa) throws Exception {
        String sql = "update situacaoclientesinteticodw set situacao = 'IN', situacaocontrato = 'CA' where codigopessoa = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setInt(1, codigoPessoa);
            sqlAlterar.execute();
        }
    }

    public Date obterDataMatricula(int codigoPessoa) throws Exception {
        String sql = "select \n" +
                "c.dataMatricula\n" +
                "from contrato c\n" +
                "where c.pessoa = ?\n" +
                "and c.situacaoContrato = 'MA'\n" +
                "ORDER BY c.dataMatricula";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoPessoa);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getDate("datamatricula");
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public JSONObject consultarDadosContratoClientePorModalidade(Integer empresa, Integer matricula, Integer modalidade) throws Exception {
        StringBuilder sb = new StringBuilder();

        sb.append(" SELECT distinct coalesce(sc.telefonescliente,'')as telefonescliente, sc.validarSaldoCreditoTreino, sc.saldoCreditoTreino, sc.totalCreditoTreino, \n");
        sb.append(" sc.nomecliente, sc.matricula, sc.codigopessoa,sc.codigocliente,\n");
        sb.append(" sc.cidade,sc.sexocliente, coalesce((select email from email where pessoa  = sc.codigopessoa limit 1), '') as email, pes.spiviclientid, \n");
        sb.append(" ct.codigo as codigocontrato, cdt.tipohorario, ct.situacao, ct.situacaocontrato, sc.datanascimento, \n");
        sb.append(" CASE WHEN futuro.codigo > 0 THEN TRUE ELSE FALSE END AS contratoFuturo, futuro.codigo as codContratoFuturo\n ");
        sb.append(" FROM contrato ct \n");
        sb.append(" inner join situacaoclientesinteticodw sc on sc.codigopessoa = ct.pessoa \n");
        sb.append(" INNER JOIN pessoa pes on pes.codigo = ct.pessoa \n");
        sb.append(" inner join cliente cli on cli.pessoa = ct.pessoa \n");
        sb.append(" LEFT JOIN contratoduracao cd ON cd.contrato = ct.codigo \n");
        sb.append(" LEFT JOIN contratoduracaocreditotreino cdt ON cdt.contratoduracao = cd.codigo \n");
        sb.append(" LEFT JOIN contrato futuro ON (futuro.pessoa = ct.pessoa AND ct.situacao <> 'AT' AND ct.situacao <> 'TR' AND futuro.vigenciade > '").append(Calendario.hoje()).append("') \n");
        if (!UteisValidacao.emptyNumber(modalidade)) {
            sb.append(" left JOIN contratomodalidade cm ON cm.contrato = ct.codigo or cm.contrato = futuro.codigo \n");
        }

        sb.append(" WHERE 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sb.append(" and (sc.empresacliente = ").append(empresa).append(" or (select acessoSomenteComAgendamento from empresa where codigo = ").append(empresa).append(")) \n");
        }

        sb.append(UteisValidacao.emptyNumber(matricula) ? "" : String.format(" AND sc.matricula = %s", matricula));
        sb.append(UteisValidacao.emptyNumber(modalidade) ? "" : String.format(" AND cm.modalidade = %s", modalidade));
        sb.append(" order by ct.situacao ");
        TurmasServiceImpl turmasService;

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sb.toString(), getCon())) {
            turmasService = new TurmasServiceImpl(getCon());

            if (rs.next()) {
                JSONObject obj = new JSONObject();
                obj.put("matricula", rs.getInt("matricula"));
                obj.put("codigoCliente", rs.getInt("codigocliente"));
                obj.put("nome", rs.getString("nomecliente"));
                obj.put("codigoPessoa", rs.getInt("codigopessoa"));
                obj.put("codigoContrato", rs.getInt("codigocontrato"));
                obj.put("tipohorario", rs.getInt("tipohorario"));
                obj.put("validarSaldoCreditoTreino", rs.getBoolean("validarSaldoCreditoTreino"));
                obj.put("contratoFuturo", rs.getBoolean("contratoFuturo"));

                if (rs.getBoolean("contratoFuturo")) {
                    obj.put("qtdCreditoFuturo", 0);
                    obj.put("codContratoFuturo", rs.getInt("codContratoFuturo"));
                    try (ResultSet dadosCredito = SuperFacadeJDBC.criarConsulta("select cdt.tipohorario from contrato c  inner JOIN contratoduracao cd ON cd.contrato = c.codigo "
                            + "   inner JOIN contratoduracaocreditotreino cdt ON cdt.contratoduracao = cd.codigo  where c.codigo =" + rs.getInt("codContratoFuturo"), con)) {
                        while (dadosCredito.next()) {
                            obj.put("validarSaldoCreditoTreino", true);
                            obj.put("tipohorario", dadosCredito.getInt("tipohorario"));
                            Integer qtdCreditoFuturo = this.qtdCreditoCompra(rs.getInt("codContratoFuturo"));
                            obj.put("qtdCreditoFuturo", qtdCreditoFuturo);
                            Integer qtdReposicoesFuturas = turmasService.reposicoesFuturasParaUtilizarContratosCreditoSemTurma(rs.getInt("codContratoFuturo"), Calendario.hoje());
                            obj.put("qtdReposicoesFuturas", qtdReposicoesFuturas);
                        }
                    }

                    JSONArray modalidadesFuturas;
                    try (ResultSet modalidadesContratoFuturo = SuperFacadeJDBC.criarConsulta("SELECT cm.modalidade \n" +
                            "FROM contrato \n" +
                            "LEFT JOIN contratomodalidade cm ON contrato.codigo = cm.contrato \n" +
                            "WHERE contrato.codigo = " + rs.getInt("codContratoFuturo"), con)) {
                        modalidadesFuturas = new JSONArray();
                        while (modalidadesContratoFuturo.next()) {
                            modalidadesFuturas.put(modalidadesContratoFuturo.getInt("modalidade"));
                        }
                    }
                    obj.put("modalidadesContratoFuturo", modalidadesFuturas);
                }

                return obj;
            }
        }

        return null;
    }

    public void limparDadosChurn(int codigoCliente) throws Exception {
        String sql = "UPDATE situacaoclientesinteticodw " +
                "SET sugestaogpt = NULL, " +
                "riscochurn = NULL, " +
                "riscochurnlancamento = NULL " +
                "WHERE codigocliente = ?";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoCliente);
            ps.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



}
