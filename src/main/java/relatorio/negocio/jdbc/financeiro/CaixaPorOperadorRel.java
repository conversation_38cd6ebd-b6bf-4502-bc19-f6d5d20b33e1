/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.estudio.modelo.PacoteVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.financeiro.ReciboPagamentoRelTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.io.File;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class CaixaPorOperadorRel extends SuperRelatorio {

    protected ReciboPagamentoRelTO reciboPagamentoRelTO;
    protected ChequeVO chequeVO;
    protected Date dataInicio;
    protected Date dataTermino;
    protected String horaInicio;
    protected String horaTermino;
    private String nomeOperador;
    protected List<ReciboPagamentoRelTO> listaReciboPagamentoRelTOs;
    protected Boolean situacaoPagamentoDesbloqueado;
    protected Boolean situacaoPagamentoPagosBanco;
    protected Boolean situacaoPagamentoCancelados;
    protected String tipoVisualizacao;
    protected String ordenacao;
    protected Boolean mostraObservacaoreRecebimento;
    protected Boolean mostraContrato;
    protected Integer qtdPagamentoAV;
    protected Double valorPagamentoAV;
    protected Integer qtdPagamentoCA;
    protected Double valorPagamentoCA;
    protected Integer qtdPagamentoCD;
    protected Double valorPagamentoCD;
    private Integer qtdPagamentoBB;
    private Double valorPagamentoBB;
    protected Integer qtdPagamentoChAvista;
    protected Double valorPagamentoChAvista;
    protected Integer qtdPagamentoChPrazo;
    protected Double valorPagamentoChPrazo;
    protected Integer qtdPagamentoOutros;
    protected Double valorPagamentoOutros;
    protected String tipoComprador;
    private List<Integer> auxMpp;
    private Integer qtdDevolucoes;
    private Double valorDevoluces;
    private Integer qtdDevolucoesRecebiveis;
    private Double valorDevolucesRecebiveis;
    private List<Integer> auxMpr = new ArrayList<Integer>();
    private List<TotalizadorFormasPagamento> totalizador = new ArrayList<TotalizadorFormasPagamento>();
    private Integer fonteDados;
    private Integer codigoMov;
    private String nomeAlunoEncontrado;
    protected Date dataInicioFaturamento;
    protected Date dataTerminoFaturamento;

    public CaixaPorOperadorRel() throws Exception {
        super();
        setIdEntidade("CaixaPorOperadorRel");
        inicializarParametros();
    }

    /**
     *Metodo que inicializar os Parametros para o Relatório. 
     */
    public void inicializarParametros() {
        setReciboPagamentoRelTO(new ReciboPagamentoRelTO());
        setChequeVO(new ChequeVO());
        setListaReciboPagamentoRelTOs(new ArrayList());
        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
        setHoraInicio("");
        setHoraTermino("");
        setNomeOperador("");
        setSituacaoPagamentoCancelados(false);
        setSituacaoPagamentoDesbloqueado(false);
        setSituacaoPagamentoPagosBanco(false);
        setMostraObservacaoreRecebimento(false);
        setMostraContrato(false);
        setQtdPagamentoAV(0);
        setValorPagamentoAV(0.0);
        setQtdPagamentoCA(0);
        setQtdPagamentoBB(0);
        setQtdPagamentoCD(0);
        setQtdPagamentoChAvista(0);
        setQtdPagamentoChPrazo(0);
        setQtdPagamentoOutros(0);
        setValorPagamentoCA(0.0);
        setValorPagamentoBB(0.0);
        setValorPagamentoCD(0.0);
        setValorPagamentoChAvista(0.0);
        setValorPagamentoChPrazo(0.0);
        setValorPagamentoOutros(0.0);
        setValorDevoluces(0.0);
        setQtdDevolucoes(0);
        setValorDevolucesRecebiveis(0.0);
        setQtdDevolucoesRecebiveis(0);
        setTipoVisualizacao("AN");
        setOrdenacao("NR");
        setDataInicioFaturamento(null);
        setDataTerminoFaturamento(null);

    }

    /**
     * Valida os campos obrigatórios Para a emissão do relatório
     * @throws negocio.comuns.utilitarias.ConsistirException Caso algum campo esteja faltando é mostrado o erro.
     */
    public void validarDados() throws ConsistirException, Exception {
        if (getDataInicio() == null) {
            throw new ConsistirException("Não é possível emitir o relatório. Informe primeiro o Período de Início da pesquisa.");
        }
        if (getDataTermino() == null) {
            throw new ConsistirException("Não é possível emitir o relatório. Informe primeiro o Período de Término da pesquisa");
        }
        if (tipoVisualizacao == null || tipoVisualizacao.equals("")) {
            throw new ConsistirException("Não é possível emitir o relatório. Informe primeiro o Tipo de Visualização.");
        }
        if (getDataInicio().compareTo(getDataTermino()) > 0) {
            throw new Exception("A Data de Início deve ser menor que a Data De Término para pesquisa.");
        }
        if(getDataInicioFaturamento() != null && getDataTerminoFaturamento() != null && Calendario.menor(getDataTerminoFaturamento() , getDataInicioFaturamento()) ){
            throw new Exception("A Data de Início do Faturamento de produto deve ser menor que a Data De Término do Faturamento de produto.");
        }
        if (Uteis.nrDiasEntreDatas(getDataInicio(), getDataTermino()) > 31) {
            throw new Exception("Período consultado não pode ser superior a 31 dias.");
        }
    }

    public List<ReciboPagamentoRelTO> consultarRecibosCaixaPorOperador(UsuarioVO usuarioLogado, boolean prodTipoSessao, List<String> conveniosSelecionados, List<String> conveniosSelecionadosOnline, Integer empresa, boolean considerarUsuarioRecorrencia, boolean considerarUsuarioAdmin) throws Exception {
        inicializar();
        validarDados();
        StringBuilder sqlSelect = new StringBuilder();
        sqlSelect.append("SELECT distinct(mpd.codigo),rp.codigo as rpcodigo, rp.data as rpdata, rp.nomepessoapagador as rpnome, pes.nome as rpconsultor, \n");
        sqlSelect.append("       us.nome as rpresponsavel, " + (tipoComprador.equals("CLI") || tipoComprador.equals("T") ? "cliente.matricula as climatricula, " : "'' as climatricula, \n"));
        sqlSelect.append("       mpg.codigo as mpgcodigo, mpg.datapagamento as mpgdata, mpg.valortotal as mpgvalor, mpg.dataalteracaomanual as mpgdataalteracaomanual, mpg.credito as mpgcredito,mpg.observacao as mpgobservacao, \n");
        sqlSelect.append("       mpg.nrparcelacartaocredito as mpgnrparcela, fp.tipoformapagamento as mpgformapagamento, fp.codigo as codigoformapagamento, fp.descricao as descricaofp,  \n");
        sqlSelect.append("       ch.codigo as chcodigo, ch.agencia as chagencia, ch.banco as chbanco, ch.conta as chconta, \n");
        sqlSelect.append("       ch.numero as chnumero, ch.datacompesancao as chcompensacao, mpg.autorizacaocartao, mpg.nsu, \n");
        sqlSelect.append("       ch.cpf as chcpf, ch.cnpj as chcnpj, ch.valor as chvalor, mpp.codigo as mppcodigo,\n");
        sqlSelect.append("       ch.vistaouprazo as chvistaouprazo, ch.situacao as chsituacao, ch.dataoriginal as chdataoriginal, \n");
        sqlSelect.append("       mpr.codigo as mprcodigo, mpr.descricao as mprdescricao, mpr.valorparcela as mprvalorparcela, \n");
        sqlSelect.append("       mpd.descricao as mpddescricao, mpd.precounitario as mpdunitario, mpd.quantidade as mpdquantidade, \n");
        sqlSelect.append("       mpd.mesreferencia as mpdreferencia, mpd.codigo as mpdcodigo, mpd.valordesconto as mpddesconto, \n");
        sqlSelect.append("       mpd.multa, mpd.juros, mpp.valorpago as mpdvalorpago, \n");
        sqlSelect.append("       prd.tipoproduto as prdtipo, cdu.numeromeses as cduduracao, ppr.nome as pprnome, \n");
        sqlSelect.append("       ctr.nomemodalidades as ctrmodalidade, ctr.vigenciade as ctrvigenciade, \n");
        sqlSelect.append("       ctr.codigo as ctrcodigo, ctr.vigenciaateajustada as ctrvigenciaajustada, \n");
        sqlSelect.append("       op.descricao as opcartao ,ctr.dataalteracaomanual, ctr.responsaveldatabase, pac.titulo, prd.codigo as codigoProduto, \n");
        sqlSelect.append("       t.codigo as transacao, t.tipo as tipotransacao, mpd.empresa as mpdempresa, \n");
        sqlSelect.append("       mpd.datainiciovigencia as mpddatainiciovigencia, mpd.datafinalvigencia as mpddatafinalvigencia \n");
        sqlSelect.append("FROM recibopagamento rp \n");
        sqlSelect.append("INNER JOIN usuario us ON us.codigo = rp.responsavellancamento \n");
        sqlSelect.append("INNER JOIN movpagamento mpg ON mpg.recibopagamento = rp.codigo \n");
        sqlSelect.append("INNER JOIN formapagamento fp ON mpg.formapagamento = fp.codigo \n");
        sqlSelect.append("LEFT OUTER JOIN cheque ch ON mpg.codigo = ch.movpagamento \n");
        sqlSelect.append("LEFT OUTER JOIN operadoraCartao op ON mpg.operadoraCartao = op.codigo \n");
        sqlSelect.append("INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mpg.codigo \n");
        sqlSelect.append("INNER JOIN movparcela mpr ON mpr.codigo = pmp.movparcela \n");
        sqlSelect.append("INNER JOIN movprodutoparcela mpp ON mpp.movparcela = mpr.codigo \n");
        sqlSelect.append("INNER JOIN movproduto mpd ON mpd.codigo = mpp.movproduto ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlSelect.append(" AND mpd.empresa = " + empresa + " \n");
        }
        sqlSelect.append("INNER JOIN produto prd ON prd.codigo = mpd.produto \n");
        sqlSelect.append("LEFT OUTER JOIN pessoa ppr ON mpd.pessoa = ppr.codigo \n");
        sqlSelect.append("LEFT OUTER JOIN vendaavulsa venda ON venda.codigo = mpr.vendaavulsa  \n");
        sqlSelect.append("LEFT OUTER JOIN itemvendaavulsa it ON it.vendaavulsa = venda.codigo  \n");
        sqlSelect.append("LEFT OUTER JOIN sch_estudio.pacote pac ON pac.id_pacote = it.pacote \n");
        sqlSelect.append("LEFT OUTER JOIN contrato ctr ON ctr.codigo = mpd.contrato \n");
        sqlSelect.append("LEFT OUTER JOIN contratoduracao cdu ON cdu.contrato = mpd.contrato \n");
        if (tipoComprador.equals("CLI")) {
            sqlSelect.append("INNER JOIN cliente ON rp.pessoapagador = cliente.pessoa \n");
            sqlSelect.append("LEFT OUTER JOIN reciboclienteconsultor rcc ON (rcc.recibo = rp.codigo AND rcc.cliente = cliente.codigo AND rcc.formapagamento = mpg.formapagamento)\n");
            sqlSelect.append("LEFT OUTER JOIN colaborador col ON col.codigo = Case when coalesce(ctr.consultor,0) > 0 then ctr.consultor Else rcc.consultor END \n");
        } else if (tipoComprador.equals("COL")) {
            sqlSelect.append("INNER JOIN colaborador co2 ON rp.pessoapagador = co2.pessoa \n");
            sqlSelect.append("LEFT OUTER JOIN colaborador col ON ctr.consultor = col.codigo\n");
        } else if (tipoComprador.equals("CN")) {
            sqlSelect.append("INNER JOIN vendaavulsa ON vendaavulsa.codigo = mpr.vendaavulsa \n");
            sqlSelect.append("LEFT OUTER JOIN colaborador col ON ctr.consultor = col.codigo\n");
        } else if (tipoComprador.equals("T")) {
            sqlSelect.append("LEFT OUTER JOIN cliente ON rp.pessoapagador = cliente.pessoa \n");
            sqlSelect.append("LEFT OUTER JOIN reciboclienteconsultor rcc ON (rcc.recibo = rp.codigo AND rcc.cliente = cliente.codigo AND rcc.formapagamento = mpg.formapagamento)\n");
            sqlSelect.append("LEFT OUTER JOIN colaborador col ON col.codigo = Case when coalesce(ctr.consultor,0) > 0 then ctr.consultor Else rcc.consultor END \n");
        }
        sqlSelect.append("LEFT OUTER JOIN pessoa pes ON col.pessoa = pes.codigo \n");
        sqlSelect.append("LEFT OUTER JOIN transacao t on rp.codigo = t.recibopagamento \n");
        sqlSelect.append("WHERE ");
        sqlSelect.append("");
        sqlSelect.append(montarFiltro(usuarioLogado, prodTipoSessao, considerarUsuarioRecorrencia, considerarUsuarioAdmin));
        sqlSelect.append(montarFiltroConvenioCobranca(conveniosSelecionados));
        sqlSelect.append(OrdenacaoRelatorio());
        try (PreparedStatement sqlConsultar = con.prepareStatement(sqlSelect.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                return montarDados(tabelaResultado, false);
            }
        }
    }

    public String montarFiltroConvenioCobranca(List<String> conveniosSelecionados) throws Exception {
        if (conveniosSelecionados.size() > 0) {
            boolean apresentarSemConvenio = false;
            StringBuilder codigosConvenios = new StringBuilder("");

            for (String convenio : conveniosSelecionados) {
                if (convenio.equals("0")) {
                    apresentarSemConvenio = true;
                } else {
                    codigosConvenios.append(convenio).append(",");
                }
            }

            if (codigosConvenios.length() > 0) {
                codigosConvenios.deleteCharAt(codigosConvenios.length() - 1);
            }

            StringBuilder filtro = new StringBuilder();
            if (apresentarSemConvenio) {
                filtro.append(" AND (mpg.conveniocobranca is null ");
            }

            if (codigosConvenios.length() > 0) {
                if (filtro.length() > 0) {
                    filtro.append(" OR ");
                } else {
                    filtro.append(" AND ");
                }
                filtro.append(" mpg.conveniocobranca in (").append(codigosConvenios.toString()).append(") ");
            }

            if (apresentarSemConvenio) {
                filtro.append(")\n");
            }

            return filtro.toString();
        } else {
            return "";
        }
    }

    public String montarFiltro(UsuarioVO usuarioLogado, boolean prodTipoSessao, boolean considerarUsuarioRecorrencia, boolean considerarUsuarioAdmin) throws Exception {
        StringBuilder filtro = new StringBuilder();
        filtro.append("rp.data >= '").append(Uteis.getDataJDBC(getDataInicio())).append(" ").append(getHoraInicio()).append("' AND\n");
        filtro.append("rp.data <= '").append(Uteis.getDataJDBC(getDataTermino())).append(" ").append(getHoraTermino()).append("'\n");
        if (prodTipoSessao){
            filtro.append("AND prd.tipoproduto = 'SS'\n");
        }
        if (UteisValidacao.emptyNumber(usuarioLogado.getCodigo()) && (!considerarUsuarioAdmin || !considerarUsuarioRecorrencia)){
            String filtroUsuario = "AND us.codigo NOT IN (";
            if (!considerarUsuarioAdmin){
                filtroUsuario += ",1";
            }
            if (!considerarUsuarioRecorrencia){
                filtroUsuario +=  ",3";
            }
            filtroUsuario +=  ")";

            filtro.append(filtroUsuario.replaceFirst(",","")).append("\n");

        }

        if (!UteisValidacao.emptyNumber(usuarioLogado.getCodigo())) {
            filtro.append("AND us.codigo = ").append(usuarioLogado.getCodigo()).append("\n");
        }

        if (getTipoComprador().equals("CN")){
            filtro.append("AND rp.pessoapagador IS NULL\n");
        }
        filtro.append((getDataInicioFaturamento() == null ? "" : " AND mpd.datalancamento >= '" + Uteis.getDataJDBC(getDataInicioFaturamento()) + " 00:00:00'\n"));
        filtro.append((getDataTerminoFaturamento() == null ? "" : " AND mpd.datalancamento <= '" + Uteis.getDataJDBC(getDataTerminoFaturamento()) + " 23:59:59'\n"));

        if (tipoComprador.equals("COL")){
            filtro.append("AND prd.tipoproduto NOT IN ('PM', 'MA', 'RE', 'RN', 'TA', 'TD')\n");
            filtro.append("AND (venda.codigo IS NULL OR (venda.codigo IS NOT NULL AND venda.tipocomprador ILIKE 'CO'))\n");
        }

        if (tipoComprador.equals("CLI")){
            filtro.append("AND prd.tipoproduto NOT IN ('TP')\n");
            filtro.append("AND (venda.codigo IS NULL OR (venda.codigo IS NOT NULL AND venda.tipocomprador ILIKE 'CI'))\n");
        }

        return filtro.toString();
    }

    public String OrdenacaoRelatorio() {
        if (ordenacao.equals("NA")) {
            return " ORDER BY rp.nomepessoapagador, mpd.codigo ";
        }
        if (ordenacao.equals("NR")) {
            return " ORDER BY rp.codigo, mpd.codigo ";
        }
        if (ordenacao.equals("NC")) {
            return " ORDER BY us.nome, rp.codigo, mpd.codigo";
        }
        return "";
    }

    public List<ReciboPagamentoRelTO> montarDados(ResultSet resultado, boolean centralEventos) throws Exception {
        setAuxMpp(new ArrayList<Integer>());
        setAuxMpr(new ArrayList<Integer>());
        List<ReciboPagamentoRelTO> lista = new ArrayList<ReciboPagamentoRelTO>();
        ReciboPagamentoRelTO itemRel;
        ReciboPagamentoVO recibo;
        MovPagamentoVO pagamento;
        MovParcelaVO parcela;
        MovProdutoVO produto;
        ChequeVO cheque;
        StringBuilder descricaoProduto = new StringBuilder();
        String descricao = "";
        while (resultado.next()) {
            codigoMov = resultado.getInt("rpcodigo");
            // verifica se é um recibo novo
            itemRel = existeRecibo(lista, resultado.getInt("rpcodigo"));
            if (itemRel == null) {
                itemRel = new ReciboPagamentoRelTO();
                // dados do recibo
                recibo = new ReciboPagamentoVO();
                recibo.setCodigo(resultado.getInt("rpcodigo"));
                recibo.setData(resultado.getTimestamp("rpdata"));
                recibo.setNomePessoaPagador(resultado.getString("rpnome"));
                recibo.getResponsavelLancamento().setNome(resultado.getString("rpresponsavel"));
                // outros dados
                itemRel.setEmpresa(resultado.getInt("mpdempresa"));
                itemRel.setNomeOperador(resultado.getString("rpresponsavel"));
                itemRel.setConsultorResponsavel(resultado.getString("rpconsultor") == null ? "" : resultado.getString("rpconsultor"));
                itemRel.setMatricula(resultado.getString("climatricula") == null ? "" : resultado.getString("climatricula"));
                itemRel.setReciboPagamentoVO(recibo);
                lista.add(itemRel);
            }
            if (!centralEventos && resultado.getString("rpnome").equals(resultado.getString("pprnome"))) {
                itemRel.setConsultorResponsavel(resultado.getString("rpconsultor") == null ? "" : resultado.getString("rpconsultor"));
            }
            // verifica se é um pagamento novo
            pagamento = existePagamento(itemRel, resultado.getInt("mpgcodigo"));
            if (pagamento == null) {
                // dados do pagamento
                pagamento = new MovPagamentoVO();
                pagamento.setCodigo(resultado.getInt("mpgcodigo"));
                pagamento.setDataPagamento(resultado.getTimestamp("mpgdata"));
                pagamento.setValorTotal(resultado.getDouble("mpgvalor"));
                pagamento.setCredito(resultado.getBoolean("mpgcredito"));
                pagamento.setNrParcelaCartaoCredito(resultado.getInt("mpgnrparcela"));
                pagamento.getFormaPagamento().setDescricao(resultado.getString("descricaofp"));
                pagamento.getFormaPagamento().setCodigo(resultado.getInt("codigoformapagamento"));
                pagamento.getFormaPagamento().setTipoFormaPagamento(resultado.getString("mpgformapagamento"));
                pagamento.getOperadoraCartaoVO().setDescricao(resultado.getString("opcartao"));
                pagamento.setDataAlteracaoManual(resultado.getDate("mpgdataalteracaomanual"));
                pagamento.setObservacao(resultado.getString("mpgobservacao"));
                pagamento.setAutorizacaoCartao(resultado.getString("autorizacaocartao"));
                pagamento.setNsu(resultado.getString("nsu"));
                itemRel.getListaMovPagamentoVOs().add(pagamento);
            }
            // verifica se é um cheque novo
            if(!UteisValidacao.emptyNumber(resultado.getInt("chcodigo"))){
                cheque = existeCheque(pagamento, resultado.getInt("chcodigo"));
                if (cheque == null) {
                    // dados do cheque
                    cheque = new ChequeVO();
                    cheque.setCodigo(resultado.getInt("chcodigo"));
                    cheque.setAgencia(resultado.getString("chagencia"));
                    cheque.getBanco().setCodigoBanco(resultado.getInt("chbanco"));
                    cheque.setConta(resultado.getString("chconta"));
                    cheque.setNumero(resultado.getString("chnumero"));
                    cheque.setDataCompensacao(resultado.getDate("chcompensacao"));
                    cheque.setCpf(resultado.getString("chcpf"));
                    cheque.setCnpj(resultado.getString("chcnpj"));
                    cheque.setValor(resultado.getDouble("chvalor"));
                    cheque.setVistaOuPrazo(resultado.getString("chvistaouprazo"));
                    cheque.setSituacao(resultado.getString("chsituacao"));
                    cheque.setDataOriginal(resultado.getDate("chdataoriginal"));
                    pagamento.getChequeVOs().add(cheque);
                }
            }

            // dados da parcela
            parcela = new MovParcelaVO();
            parcela.setCodigo(resultado.getInt("mprcodigo"));
            parcela.setDescricao(resultado.getString("mprdescricao"));
            parcela.setValorParcela(resultado.getDouble("mprvalorparcela"));
            if (!centralEventos) {
                parcela.getContrato().setCodigo(resultado.getInt("ctrcodigo"));
            }
            adicionarParcela(itemRel, parcela);

            if (centralEventos) {
                produto = new MovProdutoVO();
                produto.setDescricao("CE - EVENTO : " + resultado.getString("nomeevento"));
                produto.setPrecoUnitario(resultado.getDouble("mpgvalor"));
                produto.setValorPagoMovProdutoParcela(resultado.getDouble("mpgvalor"));
                adicionarProduto(itemRel, produto, 0, descricaoProduto, descricao);
            } else {
                // dados do produto
                produto = new MovProdutoVO();
                produto.setCodigo(resultado.getInt("mpdcodigo"));
                produto.setDescricao(resultado.getString("mpddescricao"));
                produto.setPrecoUnitario(resultado.getDouble("mpdunitario"));
                produto.setQuantidade(resultado.getInt("mpdquantidade"));
                produto.setValorDesconto(resultado.getDouble("mpddesconto"));
                produto.setJuros(resultado.getDouble("juros"));
                produto.setMulta(resultado.getDouble("multa"));
                produto.setMesReferencia(resultado.getString("mpdreferencia"));
                produto.setDataInicioVigencia(resultado.getTimestamp("mpddatainiciovigencia"));
                produto.setDataFinalVigencia(resultado.getTimestamp("mpddatafinalvigencia"));
                produto.setValorPagoMovProdutoParcela(resultado.getDouble("mpdvalorpago"));
                produto.getProduto().setTipoProduto(resultado.getString("prdtipo"));
                produto.getPessoa().setNome(resultado.getString("pprnome"));
                // dados do contrato
                produto.getContrato().setCodigo(resultado.getInt("ctrcodigo"));
                produto.getContrato().setNomeModalidades(resultado.getString("ctrmodalidade"));
                produto.getContrato().setVigenciaDe(resultado.getDate("ctrvigenciade"));
                produto.getContrato().setVigenciaAteAjustada(resultado.getDate("ctrvigenciaajustada"));
                produto.getContrato().getContratoDuracao().setNumeroMeses(resultado.getInt("cduduracao"));
                produto.getContrato().setDataAlteracaoManual(resultado.getDate("dataalteracaomanual"));
                produto.getNomeAlunoPersonal();
                if (resultado.getInt("responsaveldatabase") != 0) {
                    produto.getContrato().setResponsavelDataBase(getFacade().getUsuario().
                            consultarPorChavePrimaria(resultado.getInt("responsaveldatabase"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
                int mpp = resultado.getInt("mppcodigo");
                produto.setPacoteVO(new PacoteVO());
                produto.getPacoteVO().setTitulo(getFacade().getPacote().consultarPorCodigoMovParcela(parcela.getCodigo(), resultado.getInt("codigoproduto"),0));
                adicionarProduto(itemRel, produto, mpp, descricaoProduto, descricao);

            }

        }
        return lista;
    }

    private ReciboPagamentoRelTO existeRecibo(List<ReciboPagamentoRelTO> lista, int recibo) {
        for (ReciboPagamentoRelTO aux : lista) {
            if (aux.getReciboPagamentoVO().getCodigo() == recibo) {
                return aux;
            }
        }
        return null;
    }

    private MovPagamentoVO existePagamento(ReciboPagamentoRelTO itemRel, int pagamento) {
        for (Object o : itemRel.getListaMovPagamentoVOs()) {
            MovPagamentoVO aux = (MovPagamentoVO) o;
            if (aux.getCodigo() == pagamento) {
                return aux;
            }
        }
        return null;
    }

    private ChequeVO existeCheque(MovPagamentoVO pagamento, int cheque) {
        Iterator i = pagamento.getChequeVOs().iterator();
        while (i.hasNext()) {
            ChequeVO aux = (ChequeVO) i.next();
            if (aux.getCodigo().intValue() == cheque) {
                return aux;
            }
        }
        return null;
    }

    private void adicionarParcela(ReciboPagamentoRelTO itemRel, MovParcelaVO parcela) throws Exception {


        if (getAuxMpr().contains(parcela.getCodigo())) {
            return;
        }
        Iterator i = itemRel.getListaMovParcelaVOs().iterator();
        while (i.hasNext()) {
            MovParcelaVO aux = (MovParcelaVO) i.next();

            // se o contrato é o mesmo e a parcela é a mesma
            if (parcela.getContrato().getCodigo().intValue() == aux.getContrato().getCodigo().intValue()
                    && parcela.getCodigo().intValue() == aux.getCodigo().intValue()) {
                return;
            }
            if (!UteisValidacao.emptyNumber(parcela.getContrato().getCodigo()) && parcela.getContrato().getCodigo().intValue() == aux.getContrato().getCodigo().intValue()
                    && parcela.getCodigo().intValue() != aux.getCodigo().intValue()
                    && aux.getDescricao().contains("PARCELA")) {
                // seta a nova descricao
                aux.setDescricao(aux.getDescricao() + "," + parcela.getDescricao().substring(8, parcela.getDescricao().length()));
                // soma os valores pagos até agora
                aux.setValorParcela(parcela.getValorParcela() + aux.getValorParcela());
                getAuxMpr().add(parcela.getCodigo());
                return;
            }
        }
        // se o produto ainda nao está na lista, adiciona
        itemRel.getListaMovParcelaVOs().add(parcela);
        getAuxMpr().add(parcela.getCodigo());
    }

    private void adicionarProduto(ReciboPagamentoRelTO itemRel, MovProdutoVO movproduto, int movprodutoparcela,
            StringBuilder descricaoProduto, String descricao) throws Exception {
        boolean incluiProduto = true;
        // percorre a lista de produtos deste recibo
        Iterator j = itemRel.getListaMovProdutoVOs().iterator();

        while (j.hasNext() && incluiProduto) {
            MovProdutoVO aux = (MovProdutoVO) j.next();
            // verifica se o movproduto parcela já foi adicionado a soma dos produtos
            if (getAuxMpp().contains(movprodutoparcela)) {
                return;
            }

            ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = getFacade().getContratoDuracaoCreditoTreino().consultarPorContrato(movproduto.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            // se produto sendo comparado é do tipo mensalidade
            if ((movproduto.getProduto().getTipoProduto().equals("PM") && aux.getProduto().getTipoProduto().equals("PM"))
                    && movproduto.getContrato().getCodigo().equals(aux.getContrato().getCodigo())
                    && movproduto.getDescricao().indexOf("- "+movproduto.getMesReferencia()) >= 0 && aux.getDescricao().length() >= movproduto.getDescricao().indexOf("- "+movproduto.getMesReferencia())
                    && movproduto.getDescricao().substring(0, movproduto.getDescricao().indexOf("- "+movproduto.getMesReferencia())).equals(aux.getDescricao().substring(0, movproduto.getDescricao().indexOf("- "+movproduto.getMesReferencia())))) {
                getAuxMpp().add(movprodutoparcela);
                // soma os valores pagos até agora
                aux.setValorPagoMovProdutoParcela(Uteis.arredondarForcando2CasasDecimais(movproduto.getValorPagoMovProdutoParcela() + aux.getValorPagoMovProdutoParcela()));
                // verifica se contrato do produto ainda não foi processado
                descricaoProduto = new StringBuilder("");
                descricaoProduto.append(aux.getDescricao(), 0, movproduto.getDescricao().indexOf("- " + movproduto.getMesReferencia()) + 2)
                        .append(aux.getContrato().getContratoDuracao().getNumeroMeses())
                        .append(" M - REF. ")
                        .append(aux.getMesReferencia());

                descricaoProduto.append(descricao + " - " + movproduto.getMesReferencia());

                if (contratoDuracaoCreditoTreinoVO != null) {
                    descricaoProduto.append(" - Quantidade Crédito Treino Compra: " + contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                }

                aux.setDescricao(descricaoProduto.toString());
                return;
            }
            //se não é mensalidade, e o movproduto é o mesmo
            if (!movproduto.getProduto().getTipoProduto().equals("PM")
                    && movproduto.getCodigo().equals(aux.getCodigo())) {
                getAuxMpp().add(movprodutoparcela);
                // soma os valores pagos até agora
                aux.setValorPagoMovProdutoParcela(Uteis.arredondarForcando2CasasDecimais(movproduto.getValorPagoMovProdutoParcela() + aux.getValorPagoMovProdutoParcela()));
                return;
            }
        }


        // se o produto ainda nao está na lista, adiciona
        if (incluiProduto) {
            if (movproduto.getProduto().getTipoProduto().equals("PM")) {
                ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = getFacade().getContratoDuracaoCreditoTreino().consultarPorContrato(movproduto.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                StringBuilder descricaoProduto2 = new StringBuilder();
                descricaoProduto2.append(movproduto.getDescricao(), 0, movproduto.getDescricao().indexOf("- " + movproduto.getMesReferencia()) + 2)
                        .append(movproduto.getContrato().getContratoDuracao().getNumeroMeses())
                        .append(" M - REF. ")
                        .append(movproduto.getMesReferencia());

                if (contratoDuracaoCreditoTreinoVO != null) {
                    descricaoProduto2.append(" - Quantidade Crédito Treino Compra: ").append(contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                }
                movproduto.setDescricao(descricaoProduto2.toString());
            }

            //verifica se e produto taxa personal...
            if(movproduto.getProduto().getTipoProduto().equals("TP")){

                StringBuilder sqlNomeAluno = new StringBuilder();
                StringBuilder sqlAlunos = new StringBuilder();

                sqlNomeAluno.append("SELECT p.nome FROM pessoa p INNER JOIN cliente c ON c.pessoa = p.codigo LEFT JOIN itemtaxapersonal i ON i.aluno = c.codigo WHERE i.movproduto = "+movproduto.getCodigo());
                try (PreparedStatement sqlAluno = con.prepareStatement(sqlNomeAluno.toString())) {
                    try (ResultSet nomeAlunoEncontrado = sqlAluno.executeQuery()) {
                        if (nomeAlunoEncontrado.next()) {
                            movproduto.setNomeAlunoPersonal(nomeAlunoEncontrado.getString("nome"));
                        }
                    }
                }
            }

            getAuxMpp().add(movprodutoparcela);
            //valor do desconto já vem preenchido em função da quantidade
            itemRel.getListaMovProdutoVOs().add(movproduto);
        }
    }

    public List<ReciboPagamentoRelTO> montarDadosReciboPagamentoRelVO(UsuarioVO usuarioLogado, 
            boolean prodTipoSessao, List<String> conveniosSelecionados, List<String> conveniosSelecionadosOnline, Integer empresa,
            boolean considerarUsuarioRecorrencia, boolean considerarUsuarioAdmin) throws Exception {
        if(fonteDados == null || fonteDados.equals(TipoFonteDadosDF.ZILLYON_WEB.getCodigo())
                || fonteDados.equals(TipoFonteDadosDF.TODAS.getCodigo())){
                setListaReciboPagamentoRelTOs(consultarRecibosCaixaPorOperador(usuarioLogado, prodTipoSessao, conveniosSelecionados, conveniosSelecionadosOnline, empresa, considerarUsuarioRecorrencia, considerarUsuarioAdmin));
        }
        
        if((fonteDados != null && (fonteDados.equals(TipoFonteDadosDF.CENTRAL_EVENTOS.getCodigo())
                || fonteDados.equals(TipoFonteDadosDF.TODAS.getCodigo())))
                && (tipoComprador.equals("CLI") || tipoComprador.equals("T"))){
             getListaReciboPagamentoRelTOs().addAll(consultarRecibosCaixaPorOperadorCE(usuarioLogado, prodTipoSessao, empresa, considerarUsuarioRecorrencia, considerarUsuarioAdmin));
        }
        contarMarcadoresRelatorio();
        return getListaReciboPagamentoRelTOs();
    }

    public void carregarDadosReciboPagamentoRelVO(ReciboPagamentoVO reciboPag) throws Exception {
        getReciboPagamentoRelTO().setReciboPagamentoVO(reciboPag);
        getReciboPagamentoRelTO().setNomeOperador(reciboPag.getResponsavelLancamento().getNome());
        ColaboradorVO colaborador = null;
        if(!UteisValidacao.emptyNumber(reciboPag.getContrato().getCodigo())){
            colaborador = getFacade().getColaborador().consultarConsultorDoContrato(reciboPag.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        } else {
            colaborador = getFacade().getColaborador().consultarConsultorDoReciboConsultor(reciboPag.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        }
        if (colaborador != null) {
            getReciboPagamentoRelTO().setConsultorResponsavel(colaborador.getPessoa().getNome());
        }
        if (reciboPag.getPessoaPagador().getCodigo() != 0) {
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(reciboPag.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            getReciboPagamentoRelTO().setMatricula(clienteVO.getMatricula());
            if (reciboPag.getEmpresa().isMostrarMensagemValoresRodape()){
                getReciboPagamentoRelTO().setValorParcelasAberto(consultarValorParcelasEmAberto(reciboPag.getPessoaPagador().getCodigo()));
                if (getReciboPagamentoRelTO().getValorParcelasAberto() != 0.0){
                    getReciboPagamentoRelTO().setMensagemValoresRodape(" **Resta ainda uma quantia de R$ "+getReciboPagamentoRelTO().getValorParcelasAberto()+ " para ser quitada.");
                }
            }
        }
        if (getMostraContrato()) {
            getReciboPagamentoRelTO().setMostrarNumeroContrato(true);
            getReciboPagamentoRelTO().setNumeroContrato(reciboPag.getContrato().getCodigo().toString());
        }
        montarDadosPagamentoMovParcela(getReciboPagamentoRelTO());
        montarDadosMovProdutoParcela(getReciboPagamentoRelTO());
    }

    private Double consultarValorParcelasEmAberto(Integer pessoa) throws Exception {
    	String sqlStr = "select sum(valorparcela) as valor from movparcela  " +
    			"where regimerecorrencia  = 'false' AND situacao = 'EA' and pessoa = ? ";

        Double valorParcelasEA;
        try (PreparedStatement sql = con.prepareStatement(sqlStr)) {
            sql.setInt(1, pessoa);
            try (ResultSet rs = sql.executeQuery()) {
                rs.next();
                valorParcelasEA = rs.getDouble("valor");
            }
        }
        Double saldoContaCorrente =  getFacade().getMovimentoContaCorrenteCliente().consultarSaldoAtual(pessoa);
    	if (saldoContaCorrente < 0){
    		valorParcelasEA += (saldoContaCorrente*-1);
    	}
    	return Uteis.arredondarForcando2CasasDecimais(valorParcelasEA);
    }

	public void carregarDadosReciboPagamentoEmBranco() throws Exception {
        getReciboPagamentoRelTO().setReciboPagamentoVO(new ReciboPagamentoVO());
        getReciboPagamentoRelTO().setNomeOperador("");
        getReciboPagamentoRelTO().setConsultorResponsavel("");
        getReciboPagamentoRelTO().setMatricula("");
        getReciboPagamentoRelTO().setListaMovPagamentoVOs(new ArrayList());
        getReciboPagamentoRelTO().getListaMovPagamentoVOs().add(new MovPagamentoVO());
        getReciboPagamentoRelTO().setListaMovProdutoVOs(new ArrayList());
        getReciboPagamentoRelTO().getListaMovProdutoVOs().add(new MovProdutoVO());
        getReciboPagamentoRelTO().setListaMovParcelaVOs(new ArrayList());
        getReciboPagamentoRelTO().getListaMovParcelaVOs().add(new MovParcelaVO());
    }

    public void montarDadosPagamentoMovParcela(ReciboPagamentoRelTO recibo ) throws Exception {
        List resultado = consultarPagamento(recibo);
        Iterator i = resultado.iterator();
        while (i.hasNext()) {
            PagamentoMovParcelaVO obj = (PagamentoMovParcelaVO) i.next();
            MovPagamentoVO movPagamentoVO = getFacade().getMovPagamento().consultarPorChavePrimaria(obj.getMovPagamento(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            adicionarObjMovPagamentoVOs(movPagamentoVO, recibo);
        }
    }

    public List consultarPagamento(ReciboPagamentoRelTO recibo) throws Exception {
        return getFacade().getPagamentoMovParcela().consultarPorReciboPagamneto(recibo.getReciboPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public void montarDadosMovProdutoParcela(ReciboPagamentoRelTO recibo) throws Exception {
        ContratoVO contratoAtual = new ContratoVO();
        StringBuilder descricaoProduto;
        List resultado = consultarMovProduto(recibo);
        Ordenacao.ordenarLista(resultado, "movProduto");
        Iterator i = resultado.iterator();
        String descricao = "";
        Double valorPago = 0.0;
        while (i.hasNext()) {
            boolean incluiProduto = true;
            boolean incluiParcela = true;
            // pega os dados de cada produto
            MovParcelaVO parcela;
            MovProdutoParcelaVO obj = (MovProdutoParcelaVO) i.next();
            MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(obj.getMovProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoDuracaoCreditoTreinoVO contratoDuracaoCreditoTreinoVO = getFacade().getContratoDuracaoCreditoTreino().consultarPorContrato(movProdutoVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            // se o produto é do tipo plano mensal
            if (movProdutoVO.getProduto().getTipoProduto().equals("PM") && movProdutoVO.getMulta() == 0.0 && movProdutoVO.getJuros() == 0.0) {
                    // verifica se contrato do produto ainda não foi processado
                if (contratoAtual.getCodigo() == 0 || contratoAtual.getCodigo().intValue() != movProdutoVO.getContrato().getCodigo().intValue()) {
                    valorPago = 0.0;
                    // pega os dados do contrato relativos à duracao
                    contratoAtual = getFacade().getContrato().consultarPorChavePrimaria(movProdutoVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
                }
                valorPago = Uteis.arredondarForcando2CasasDecimais(valorPago + obj.getValorPago());
                // percorre a lista de produtos deste recibo
                Iterator j = recibo.getListaMovProdutoVOs().iterator();

                while (j.hasNext() && incluiProduto) {
                    MovProdutoVO aux = (MovProdutoVO) j.next();
                    // se produto já está na lista e se o contrato é o mesmo
                    if (movProdutoVO.getProduto().getCodigo().intValue() == aux.getProduto().getCodigo().intValue()
                            && movProdutoVO.getContrato().getCodigo().intValue() == aux.getContrato().getCodigo().intValue()
                            && movProdutoVO.getDescricao().contains("- " + movProdutoVO.getMesReferencia())
                            && movProdutoVO.getDescricao().indexOf("- "+movProdutoVO.getMesReferencia()) < aux.getDescricao().length()
                            && movProdutoVO.getDescricao().substring(0, movProdutoVO.getDescricao().indexOf("- "+movProdutoVO.getMesReferencia())).equals(aux.getDescricao().substring(0, movProdutoVO.getDescricao().indexOf("- "+movProdutoVO.getMesReferencia())))) {
                        // soma os valores pagos até agora
                        aux.setValorPagoMovProdutoParcela(aux.getValorPagoMovProdutoParcela()+obj.getValorPago());
                        // seta a nova descricao
                        //deve mostrar o intervalo de meses do contrato
                        //obtem a descricao com o nome do plano a qtd de meses do contrato e o mês inicial
                        descricao = movProdutoVO.getDescricao().substring(0, movProdutoVO.getDescricao().indexOf("- "+movProdutoVO.getMesReferencia()) + 2)
                        + movProdutoVO.getContrato().getContratoDuracao().getNumeroMeses() + " M - REF. " + aux.getMesReferencia();
                        
                        descricaoProduto = new StringBuilder("");
                        descricaoProduto.append(descricao).append(" - ").append(movProdutoVO.getMesReferencia());

                        if (contratoDuracaoCreditoTreinoVO != null) {
                            descricaoProduto.append(" - Quantidade Crédito Treino Compra: ").append(contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                        }


                        aux.setDescricao(descricaoProduto.toString());
                        incluiProduto = false;
                    }
                }
            }

            if (!movProdutoVO.getProduto().getTipoProduto().equals("PM")) {
                for (MovProdutoVO prod : recibo.getListaMovProdutoVOs()) {
                    if (prod.getCodigo().equals(movProdutoVO.getCodigo())) {
                        prod.setValorPagoMovProdutoParcela(prod.getValorPagoMovProdutoParcela() + obj.getValorPago());
                        incluiProduto = false;
                    }
                }

            }
            parcela = getFacade().getMovParcela().consultarPorChavePrimaria(obj.getMovParcela(), Uteis.NIVELMONTARDADOS_RECIBOPAGAMENTO);

            if (incluiProduto && !movProdutoVO.getProduto().getTipoProduto().equals("PM")) {
                movProdutoVO.setValorPagoMovProdutoParcela(obj.getValorPago());
                //incluir descricao de pacote no recibo
                for (Object item : parcela.getVendaAvulsaVO().getItemVendaAvulsaVOs()) {
                    ItemVendaAvulsaVO item1 = (ItemVendaAvulsaVO) item;
                    if (item1.getProduto().getCodigo().intValue() == movProdutoVO.getProduto().getCodigo().intValue() && item1.getPacoteVO().getCodigo() != null && item1.getPacoteVO().getCodigo() != 0) {
                        movProdutoVO.getPacoteVO().setTitulo(item1.getPacoteVO().getTitulo());
                    }
                }
                recibo.getListaMovProdutoVOs().add(movProdutoVO);
            }

            // se o produto ainda nao está na lista, adiciona
            if (incluiProduto && movProdutoVO.getProduto().getTipoProduto().equals("PM")) {
                movProdutoVO.setValorPagoMovProdutoParcela(obj.getValorPago());
                movProdutoVO.setValorDesconto(movProdutoVO.getValorDesconto()/**produto.getQuantidade()*/
                        );//valor do desconto já vem preenchido em função da quantidade
                descricao = movProdutoVO.getDescricao().substring(0, movProdutoVO.getDescricao().indexOf("- "+movProdutoVO.getMesReferencia()) + 2)
                            + movProdutoVO.getContrato().getContratoDuracao().getNumeroMeses() + " M - REF. " + movProdutoVO.getMesReferencia();

                descricaoProduto = new StringBuilder("");
                descricaoProduto.append(descricao).append(" - ").append(movProdutoVO.getMesReferencia());

                if (contratoDuracaoCreditoTreinoVO != null) {
                    descricaoProduto.append(" - Quantidade Crédito Treino Compra: ").append(contratoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra());
                }

                movProdutoVO.setDescricao(descricaoProduto.toString());
          
                recibo.getListaMovProdutoVOs().add(movProdutoVO);
            }
            // percorre a lista de parcelas deste recibo
            Iterator j = recibo.getListaMovParcelaVOs().iterator();
            while (j.hasNext() && incluiParcela) {
                MovParcelaVO aux = (MovParcelaVO) j.next();
                // se parcela está presente na lista
                if (obj.getMovParcela().intValue() == aux.getCodigo().intValue()) {
                    incluiParcela = false;
                }
            }
            if (incluiParcela) {
                recibo.getListaMovParcelaVOs().add(parcela);
            }
        }
        if (contratoAtual.getCodigo() == 0) {
            recibo.setListaMovParcelaVOs(new ArrayList<MovParcelaVO>());
        } else {
            processaListaParcelas(recibo);
        }
    }

    private void processaListaParcelas(ReciboPagamentoRelTO recibo) {
        for (int i = 0; i < recibo.getListaMovParcelaVOs().size(); i++) {
            MovParcelaVO aux = recibo.getListaMovParcelaVOs().get(i);
            for (int j = i + 1; j < recibo.getListaMovParcelaVOs().size(); j++) {
                MovParcelaVO aux2 = recibo.getListaMovParcelaVOs().get(j);
                if (aux.getContrato().getCodigo().intValue() == aux2.getContrato().getCodigo().intValue()) {
                    if (!aux2.getDescricao().contains("PARCELA")) {
                        continue;
                    }
                    if (aux2.getDescricao().trim().length() > 9) {
                        aux.setDescricao(aux.getDescricao() + ", " + aux2.getDescricao().substring(8, 10));
                    } else {
                        aux.setDescricao(aux.getDescricao() + ", " + aux2.getDescricao().substring(8, 9));
                    }
                    aux.setValorParcela(aux.getValorParcela() + aux2.getValorParcela());
                    recibo.getListaMovParcelaVOs().remove(j--);
                }
            }
        }
    }

    public List consultarMovProduto(ReciboPagamentoRelTO recibo) throws Exception {

        return getFacade().getMovProdutoParcela().consultarPorReciboPagamneto(recibo.getReciboPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public void adicionarObjReciboPagamentoRelVOs(ReciboPagamentoRelTO obj) throws Exception {
        int index = 0;
        Iterator i = getListaReciboPagamentoRelTOs().iterator();
        while (i.hasNext()) {
            ReciboPagamentoRelTO objExistente = (ReciboPagamentoRelTO) i.next();
            if (objExistente.getReciboPagamentoVO().getCodigo().equals(obj.getReciboPagamentoVO().getCodigo().intValue())) {
                getListaReciboPagamentoRelTOs().set(index, obj);
                return;
            }
            index++;
        }
        getListaReciboPagamentoRelTOs().add(obj);
    }

    public void adicionarObjMovPagamentoVOs(MovPagamentoVO obj, ReciboPagamentoRelTO recibo) throws Exception {
        int index = 0;
        Iterator i = recibo.getListaMovPagamentoVOs().iterator();
        while (i.hasNext()) {
            MovPagamentoVO objExistente = (MovPagamentoVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo().intValue())) {
                recibo.getListaMovPagamentoVOs().set(index, obj);
                return;
            }
            index++;
        }
        recibo.getListaMovPagamentoVOs().add(obj);
    }

    public void contarMarcadoresRelatorio() throws Exception {
        setQtdPagamentoAV(0);
        setValorPagamentoAV(0.0);
        setQtdPagamentoCA(0);
        setValorPagamentoCA(0.0);
        setQtdPagamentoCD(0);
        setValorPagamentoCD(0.0);
        setQtdPagamentoBB(0);
        setValorPagamentoBB(0.0);
        setQtdPagamentoChAvista(0);
        setQtdPagamentoChPrazo(0);
        setQtdPagamentoOutros(0);
        setValorPagamentoChAvista(0.0);
        setValorPagamentoChPrazo(0.0);
        setValorPagamentoOutros(0.0);
        setValorDevoluces(0.0);
        setQtdDevolucoes(0);

        TotalizadorFormasPagamento totalEspecie = new TotalizadorFormasPagamento();
        totalEspecie.setTotal(true);
        totalEspecie.setOrdem(TotalizadorFormasPagamento.ORDEM_TOTAL_ESPECIE);
        
        TotalizadorFormasPagamento totalEspecieBoleto = new TotalizadorFormasPagamento();
        totalEspecieBoleto.setTotal(true);
        totalEspecieBoleto.setOrdem(TotalizadorFormasPagamento.ORDEM_TOTAL_ESPECIE_BOLETO);
        
        TotalizadorFormasPagamento totalBoleto = new TotalizadorFormasPagamento();
        totalBoleto.setTotal(true);
        totalBoleto.setOrdem(TotalizadorFormasPagamento.ORDEM_TOTAL_BOLETO);
        
        TotalizadorFormasPagamento totalEdicaoEntrada = new TotalizadorFormasPagamento();
        totalEdicaoEntrada.setDescricao("PARCELA(S) PAGA(S)");
        totalEdicaoEntrada.setOrdem(TotalizadorFormasPagamento.ORDEM_EDICOES_ENTRADAS);

        List <Integer> contratosJaAdicionadosModalidades = new ArrayList<Integer>(); // armazena

        Map<Integer, EmpresaVO> mapaEmpresas = getFacade().getEmpresa().obterMapaEmpresas();

        Iterator i = getListaReciboPagamentoRelTOs().iterator();
        while (i.hasNext()) {
            ReciboPagamentoRelTO movParRel = (ReciboPagamentoRelTO) i.next();
            somarPagamento(movParRel, totalEspecie, totalEspecieBoleto,totalBoleto);

            EmpresaVO empresaVO = mapaEmpresas.get(movParRel.getEmpresa());
            if (empresaVO == null) {
                empresaVO = new EmpresaVO();
            }
            if (empresaVO.isMostrarModalidade()) {
                if (!movParRel.getListaMovParcelaVOs().isEmpty()){
                    for (MovParcelaVO movParcelaVO: movParRel.getListaMovParcelaVOs()){
                        if (!contratosJaAdicionadosModalidades.contains(movParcelaVO.getContrato().getCodigo())){
                            List contratoModalidadeVO = getFacade().getContratoModalidade().consultarPorCodigoContrato(movParcelaVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
                            for (Object contrato : contratoModalidadeVO) {
                                ContratoModalidadeVO contratoMod = (ContratoModalidadeVO) contrato;
                                if (!movParRel.getModalidades().contains(contratoMod.getModalidade().getNome())) {
                                    movParRel.setModalidades(" | " + contratoMod.getModalidade().getNome() + movParRel.getModalidades());
                                }
                            }
                            contratosJaAdicionadosModalidades.add(movParRel.getContratoVO().getCodigo());
                        }
                        if(movParcelaVO.getDescricao().startsWith("PARCELA EDIÇÃO DE PAGAMENTO")){
                             totalEdicaoEntrada.setQtd(totalEdicaoEntrada.getQtd() + 1);
	                     totalEdicaoEntrada.setValor(Uteis.arredondarForcando2CasasDecimais(totalEdicaoEntrada.getValor() + movParcelaVO.getValorParcela()));
                        }
                    }
                    movParRel.setModalidades(movParRel.getModalidades().replaceFirst("\\|", ""));
                }
            }
        }
        if (totalEspecie.getQtd() > 0) {
            getTotalizador().add(totalEspecie);
        }
        if (totalBoleto.getQtd() > 0) {
            getTotalizador().add(totalBoleto);
        }
        if (totalEspecieBoleto.getQtd() > 0 && (totalBoleto.getQtd() > 0 && totalEspecie.getQtd() > 0)) {
            getTotalizador().add(totalEspecieBoleto);
        }
        if (totalEdicaoEntrada.getQtd() > 0) {
            getTotalizador().add(totalEdicaoEntrada);
        }


    }

    public void somarPagamento(ReciboPagamentoRelTO movParRel, TotalizadorFormasPagamento totEspecie, TotalizadorFormasPagamento totEspecieBoleto, TotalizadorFormasPagamento totBoleto) throws Exception {
        Iterator i = movParRel.getListaMovPagamentoVOs().iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPag = (MovPagamentoVO) i.next();
            if (movPag.getFormaPagamento().getTipoFormaPagamento().equals("CH") && !movPag.getCredito()) {
                somarCheque(movPag, totEspecie, totEspecieBoleto);
                totEspecie.setQtd(totEspecie.getQtd() + 1);
                continue;
            }
            boolean especie = ((movPag.getFormaPagamento().getTipoFormaPagamento().equals("AV")
                    || movPag.getFormaPagamento().getTipoFormaPagamento().equals("CA")
                    || movPag.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla())
                    || movPag.getFormaPagamento().getTipoFormaPagamento().equals("CD")) && !movPag.getCredito());
            boolean inserir = true;
            //verificar se o pagamento é de espécie

            for (TotalizadorFormasPagamento totalizador : getTotalizador()) {
            	if (movPag.getCredito()){
                    movPag.setChequeVOs(getFacade().getCheque().consultarPagamentoCheques(movPag.getCodigo(), null,false,false,false, Uteis.NIVELMONTARDADOS_TODOS));
            		if (totalizador.getDescricao().equals("CRÉDITO CONTA CORRENTE(CCC)")){
                            totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(totalizador.getValor() + movPag.getValorTotal()));
	                    totalizador.setQtd(totalizador.getQtd() + 1);
	                    inserir = false;
            		}
            	}else{	
            		if (totalizador.getFormaPagamento().getCodigo().equals(movPag.getFormaPagamento().getCodigo())) {
            	
	                    totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(totalizador.getValor() + movPag.getValorTotal()));
	                    totalizador.setQtd(totalizador.getQtd() + 1);
	                    inserir = false;
	                    if (especie) {
	                        totEspecie.setQtd(totEspecie.getQtd() + 1);
	                        totEspecie.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecie.getValor() + movPag.getValorTotal()));
	                        totEspecieBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecieBoleto.getValor() + movPag.getValorTotal()));
	                    }else if(movPag.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())){
	                    	totEspecieBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecieBoleto.getValor() + movPag.getValorTotal()));
	                    	totEspecieBoleto.setQtd(totEspecieBoleto.getQtd() + 1);
                                totBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totBoleto.getValor() + movPag.getValorTotal()));
                                totBoleto.setQtd(totBoleto.getQtd() + 1);
	                    }

            		}
                }
            }
            if (inserir) {
                TotalizadorFormasPagamento totalizador = new TotalizadorFormasPagamento();
                totalizador.setFormaPagamento(movPag.getFormaPagamento());
                totalizador.setValor(movPag.getValorTotal());
                totalizador.setQtd(1);
                if(movPag.getCredito()){
                	totalizador.setDescricao("CRÉDITO CONTA CORRENTE(CCC)");
                	totalizador.setFormaPagamento(new FormaPagamentoVO());
                        totalizador.setOrdem(TotalizadorFormasPagamento.ORDEM_NAO_ESPECIE);
                }else{
                	totalizador.setDescricao(movPag.getFormaPagamento().getDescricao());
                	totalizador.setFormaPagamento(movPag.getFormaPagamento());
                }
                if (especie) {
                    totEspecie.setQtd(totEspecie.getQtd() + 1);
                    totEspecie.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecie.getValor() + movPag.getValorTotal()));
                    totEspecieBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecieBoleto.getValor() + movPag.getValorTotal()));
                    totalizador.setOrdem(TotalizadorFormasPagamento.ORDEM_ESPECIE);
                } else if (!movPag.getCredito()) {
                	
                    totalizador.setOrdem(
                    		movPag.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla()) ? 
                    				TotalizadorFormasPagamento.ORDEM_BOLETO : TotalizadorFormasPagamento.ORDEM_NAO_ESPECIE);
                    if(movPag.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())){
                    	totEspecieBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecieBoleto.getValor() + movPag.getValorTotal()));
                    	totEspecieBoleto.setQtd(totEspecieBoleto.getQtd() + 1);
                        totalizador.setDescricao(totalizador.getDescricao());
                        totBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totBoleto.getValor() + movPag.getValorTotal()));
                        totBoleto.setQtd(totBoleto.getQtd() + 1);
                    }
                }
                getTotalizador().add(totalizador);
            }
        }
    }

    public void somarCheque(MovPagamentoVO movPag, TotalizadorFormasPagamento totEspecie, TotalizadorFormasPagamento totEspecieBoleto) throws Exception {
        movPag.setChequeVOs(getFacade().getCheque().consultarPagamentoCheques(movPag.getCodigo(), null,false,false,false, Uteis.NIVELMONTARDADOS_TODOS));
        Iterator i = movPag.getChequeVOs().iterator();
        while (i.hasNext()) {
            boolean inserir = true;
            ChequeVO cheque = (ChequeVO) i.next();
            if (cheque.getVistaOuPrazo().isEmpty()) {
            	cheque.setVistaOuPrazo(cheque.getDataCompensacao().compareTo(Uteis.somarDias(movPag.getDataLancamento(), movPag.getEmpresa().getNrDiasChequeAVista())) == 1 ? "PR" : "AV");
            }
            for (TotalizadorFormasPagamento totalizador : getTotalizador()) {
                if (totalizador.getFormaPagamento().getCodigo().equals(movPag.getFormaPagamento().getCodigo())) {
                    if ((cheque.getVistaOuPrazo().equals("AV") && totalizador.getChequeAVista())
                            || (cheque.getVistaOuPrazo().equals("PR") && totalizador.getChequeAPrazo())) {
                        totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(totalizador.getValor() + cheque.getValor()));
                        totalizador.setQtd(totalizador.getQtd() + 1);
                        inserir = false;
                        totEspecie.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecie.getValor() + cheque.getValor()));
                        totEspecieBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecieBoleto.getValor() + cheque.getValor()));
                    }
                }
            }
            if (inserir) {
                TotalizadorFormasPagamento totalizador = new TotalizadorFormasPagamento();
                totalizador.setFormaPagamento(movPag.getFormaPagamento());
                totalizador.setValor(cheque.getValor());
                totalizador.setQtd(1);
                if (cheque.getVistaOuPrazo().equals("PR")) {
                    totalizador.setDescricao(movPag.getFormaPagamento().getDescricao() + " (A prazo)");
                    totalizador.setChequeAPrazo(true);
                } else {
                    totalizador.setDescricao(movPag.getFormaPagamento().getDescricao() + " (À vista)");
                    totalizador.setChequeAVista(true);
                }
                totEspecie.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecie.getValor() + cheque.getValor()));
                totEspecieBoleto.setValor(Uteis.arredondarForcando2CasasDecimais(totEspecieBoleto.getValor() + cheque.getValor()));
                totalizador.setOrdem(TotalizadorFormasPagamento.ORDEM_ESPECIE);
                getTotalizador().add(totalizador);
            }
        }
    }

    public List<ReciboPagamentoRelTO> consultarRecibosCaixaPorOperadorCE(UsuarioVO usuarioLogado, boolean prodTipoSessao, Integer empresa, boolean considerarUsuarioRecorrencia, boolean considerarUsuarioAdmin) throws Exception {
        try {
            inicializar();
            validarDados();
            StringBuilder sqlSelect = new StringBuilder();
            // consulta recibos com os dados necessarios
            sqlSelect.append("SELECT rp.codigo as rpcodigo, rp.data as rpdata, rp.nomepessoapagador as rpnome, \n");
            sqlSelect.append("us.nome as rpresponsavel, cliente.matricula as climatricula,         \n");
            sqlSelect.append("mpg.codigo as mpgcodigo, mpg.datapagamento as mpgdata, mpg.valor as mpgvalor, mpg.credito as mpgcredito, mpg.dataalteracaomanual as mpgdataalteracaomanual,mpg.observacao as mpgobservacao, \n");
            sqlSelect.append("mpg.nrparcelacartaocredito as mpgnrparcela, fp.tipoformapagamento as mpgformapagamento, fp.codigo as codigoformapagamento, fp.descricao as descricaofp,         \n");
            sqlSelect.append("ch.codigo as chcodigo, ch.agencia as chagencia, ch.banco as chbanco, ch.conta as chconta,         \n");
            sqlSelect.append("ch.numero as chnumero, ch.datacompesancao as chcompensacao, ch.dataoriginal as chdataoriginal,       \n");
            sqlSelect.append("ch.cpf as chcpf, ch.cnpj as chcnpj, ch.valor as chvalor,  mpg.autorizacaocartao, mpg.nsu, \n");
            sqlSelect.append("ch.vistaouprazo as chvistaouprazo, ch.situacao as chsituacao,   \n");
            sqlSelect.append("mpr.codigo as mprcodigo, mpr.descricao as mprdescricao, mpr.valorparcela as mprvalorparcela, \n");
            sqlSelect.append("nec.nomeevento, nec.valorfinal, usc.nome as rpconsultor, \n");
            sqlSelect.append("op.descricao as opcartao, mov.empresa as mpdempresa FROM recibopagamento rp  \n");
            sqlSelect.append("INNER JOIN usuario us ON us.codigo = rp.responsavellancamento \n");
            sqlSelect.append("INNER JOIN movpagamento mpg ON mpg.recibopagamento = rp.codigo  \n");
            sqlSelect.append("INNER JOIN formapagamento fp ON mpg.formapagamento = fp.codigo  \n");
            sqlSelect.append("LEFT OUTER JOIN cheque ch ON mpg.codigo = ch.movpagamento  \n");
            sqlSelect.append("LEFT OUTER JOIN operadoraCartao op ON mpg.operadoraCartao = op.codigo \n");
            sqlSelect.append("INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mpg.codigo  \n");
            sqlSelect.append("INNER JOIN movparcela mpr ON mpr.codigo = pmp.movparcela  \n");
            sqlSelect.append("INNER JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = mpr.codigo \n");
            sqlSelect.append("INNER JOIN negociacaoeventocontrato nec ON nec.codigo = necp.contrato \n");
            sqlSelect.append("LEFT OUTER JOIN cliente ON rp.pessoapagador = cliente.pessoa \n");
            sqlSelect.append("LEFT OUTER JOIN usuario usc ON nec.responsavelcontrato = usc.codigo \n");
            sqlSelect.append("left outer join movprodutoparcela movpp on movpp.movparcela = mpr.codigo \n");
            sqlSelect.append("left outer join movproduto mov on mov.codigo = movpp.movproduto \n");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sqlSelect.append(" AND mov.empresa = " + empresa + " \n");
            }

            sqlSelect.append("left outer join produto prd on prd.codigo = mov.produto  \n");
            sqlSelect.append("WHERE ");
            sqlSelect.append(montarFiltro(usuarioLogado, prodTipoSessao, considerarUsuarioRecorrencia, considerarUsuarioAdmin));
            sqlSelect.append("\nORDER BY rp.nomepessoapagador");
            try (PreparedStatement sqlConsultar = con.prepareStatement(sqlSelect.toString())) {
                try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                    return montarDados(tabelaResultado, true);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public String getDesignIReportRelatorio(boolean isLayoutCaixa, boolean isLayoutImpressaoTermica, boolean isLayoutImpressaoTermicaResumido) {

        String caminhoBase = "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator;
        if (isLayoutImpressaoTermica) {
            return caminhoBase + "ReciboTermico" + File.separator + "FechamentoCaixaModeloTermico.jrxml";
        } else if (isLayoutImpressaoTermicaResumido) {
            return caminhoBase + "ReciboTermico" + File.separator + "FechamentoCaixaModeloTermicoResumido.jrxml";
        } else if (isLayoutCaixa) {
            return (caminhoBase + "CaixaPorOperadorLivroRel" + ".jrxml");
        } else {
            return (caminhoBase + getIdEntidade() + ".jrxml");
        }

    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraTermino() {
        return horaTermino;
    }

    public void setHoraTermino(String horaTermino) {
        this.horaTermino = horaTermino;
    }

    public Boolean getMostraObservacaoreRecebimento() {
        return mostraObservacaoreRecebimento;
    }

    public void setMostraObservacaoreRecebimento(Boolean mostraObservacaoreRecebimento) {
        this.mostraObservacaoreRecebimento = mostraObservacaoreRecebimento;
    }

    public Boolean getMostraContrato() {
        return mostraContrato;
    }

    public void setMostraContrato(Boolean mostraContrato) {
        this.mostraContrato = mostraContrato;
    }

    public Boolean getSituacaoPagamentoCancelados() {
        return situacaoPagamentoCancelados;
    }

    public void setSituacaoPagamentoCancelados(Boolean situacaoPagamentoCancelados) {
        this.situacaoPagamentoCancelados = situacaoPagamentoCancelados;
    }

    public Boolean getSituacaoPagamentoDesbloqueado() {
        return situacaoPagamentoDesbloqueado;
    }

    public void setSituacaoPagamentoDesbloqueado(Boolean situacaoPagamentoDesbloqueado) {
        this.situacaoPagamentoDesbloqueado = situacaoPagamentoDesbloqueado;
    }

    public Boolean getSituacaoPagamentoPagosBanco() {
        return situacaoPagamentoPagosBanco;
    }

    public void setSituacaoPagamentoPagosBanco(Boolean situacaoPagamentoPagosBanco) {
        this.situacaoPagamentoPagosBanco = situacaoPagamentoPagosBanco;
    }

    public String getTipoVisualizacao() {
        return tipoVisualizacao;
    }

    public void setTipoVisualizacao(String tipoVisualizacao) {
        this.tipoVisualizacao = tipoVisualizacao;
    }

    public List<ReciboPagamentoRelTO> getListaReciboPagamentoRelTOs() {
        return listaReciboPagamentoRelTOs;
    }

    public void setListaReciboPagamentoRelTOs(List<ReciboPagamentoRelTO> listaReciboPagamentoRelTOs) {
        this.listaReciboPagamentoRelTOs = listaReciboPagamentoRelTOs;
    }

    public Integer getQtdPagamentoAV() {
        return qtdPagamentoAV;
    }

    public void setQtdPagamentoAV(Integer qtdPagamentoAV) {
        this.qtdPagamentoAV = qtdPagamentoAV;
    }

    public Integer getQtdPagamentoCA() {
        return qtdPagamentoCA;
    }

    public void setQtdPagamentoCA(Integer qtdPagamentoCA) {
        this.qtdPagamentoCA = qtdPagamentoCA;
    }

    public Double getValorPagamentoAV() {
        return valorPagamentoAV;
    }

    public void setValorPagamentoAV(Double valorPagamentoAV) {
        this.valorPagamentoAV = valorPagamentoAV;
    }

    public Integer getQtdPagamentoCD() {
        return qtdPagamentoCD;
    }

    public void setQtdPagamentoCD(Integer qtdPagamentoCD) {
        this.qtdPagamentoCD = qtdPagamentoCD;
    }

    public Double getValorPagamentoCA() {
        return valorPagamentoCA;
    }

    public void setValorPagamentoCA(Double valorPagamentoCA) {
        this.valorPagamentoCA = valorPagamentoCA;
    }

    public Double getValorPagamentoCD() {
        return valorPagamentoCD;
    }

    public void setValorPagamentoCD(Double valorPagamentoCD) {
        this.valorPagamentoCD = valorPagamentoCD;
    }

    public Integer getQtdPagamentoChAvista() {
        return qtdPagamentoChAvista;
    }

    public void setQtdPagamentoChAvista(Integer qtdPagamentoChAvista) {
        this.qtdPagamentoChAvista = qtdPagamentoChAvista;
    }

    public Integer getQtdPagamentoChPrazo() {
        return qtdPagamentoChPrazo;
    }

    public void setQtdPagamentoChPrazo(Integer qtdPagamentoChPrazo) {
        this.qtdPagamentoChPrazo = qtdPagamentoChPrazo;
    }

    public Double getValorPagamentoChAvista() {
        return valorPagamentoChAvista;
    }

    public void setValorPagamentoChAvista(Double valorPagamentoChAvista) {
        this.valorPagamentoChAvista = valorPagamentoChAvista;
    }

    public Double getValorPagamentoChPrazo() {
        return valorPagamentoChPrazo;
    }

    public void setValorPagamentoChPrazo(Double valorPagamentoChPrazo) {
        this.valorPagamentoChPrazo = valorPagamentoChPrazo;
    }

    public Integer getQtdPagamentoOutros() {
        return qtdPagamentoOutros;
    }

    public void setQtdPagamentoOutros(Integer qtdPagamentoOutros) {
        this.qtdPagamentoOutros = qtdPagamentoOutros;
    }

    public Double getValorPagamentoOutros() {
        return valorPagamentoOutros;
    }

    public void setValorPagamentoOutros(Double valorPagamentoOutros) {
        this.valorPagamentoOutros = valorPagamentoOutros;
    }

    public String getTipoVisualizacao_Apresentar() {
        if (tipoVisualizacao == null) {
            return "";
        }
        if (tipoVisualizacao.equals("SI")) {
            return "Sintético";
        }
        if (tipoVisualizacao.equals("AN")) {
            return "Analítico";
        }
        return (tipoVisualizacao);
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public String getOrdenacao_Apresentar() {
        if (ordenacao == null) {
            return "";
        }
        if (ordenacao.equals("NR")) {
            return "Número Recibo";
        }
        if (ordenacao.equals("NA")) {
            return "Nome do Aluno";
        }
        return (ordenacao);
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    public ReciboPagamentoRelTO getReciboPagamentoRelTO() {
        return reciboPagamentoRelTO;
    }

    public void setReciboPagamentoRelTO(ReciboPagamentoRelTO reciboPagamentoRelTO) {
        this.reciboPagamentoRelTO = reciboPagamentoRelTO;
    }

    public ChequeVO getChequeVO() {
        return chequeVO;
    }

    public void setChequeVO(ChequeVO chequeVO) {
        this.chequeVO = chequeVO;
    }

    public String getNomeOperador() {
        return nomeOperador;
    }

    public void setNomeOperador(String nomeOperador) {
        this.nomeOperador = nomeOperador;
    }

    public Integer getQtdPagamentoBB() {
        return qtdPagamentoBB;
    }

    public void setQtdPagamentoBB(Integer qtdPagamentoBB) {
        this.qtdPagamentoBB = qtdPagamentoBB;
    }

    public Double getValorPagamentoBB() {
        return valorPagamentoBB;
    }

    public void setValorPagamentoBB(Double valorPagamentoBB) {
        this.valorPagamentoBB = valorPagamentoBB;
    }

    public String getTipoComprador() {
        return tipoComprador;
    }

    public void setTipoComprador(String tipoComprador) {
        this.tipoComprador = tipoComprador;
    }

    public void setAuxMpp(List<Integer> auxMpp) {
        this.auxMpp = auxMpp;
    }

    public List<Integer> getAuxMpp() {
        if (auxMpp == null) {
            auxMpp = new ArrayList<Integer>();
        }
        return auxMpp;
    }

    public void setQtdDevolucoes(Integer qtdDevolucoes) {
        this.qtdDevolucoes = qtdDevolucoes;
    }

    public Integer getQtdDevolucoes() {
        return qtdDevolucoes;
    }

    public void setValorDevoluces(Double valorDevoluces) {
        this.valorDevoluces = valorDevoluces;
    }

    public Double getValorDevoluces() {
        return valorDevoluces;
    }

    public void setQtdDevolucoesRecebiveis(Integer qtdDevolucoesRecebiveis) {
        this.qtdDevolucoesRecebiveis = qtdDevolucoesRecebiveis;
    }

    public Integer getQtdDevolucoesRecebiveis() {
        return qtdDevolucoesRecebiveis;
    }

    public void setValorDevolucesRecebiveis(Double valorDevolucesRecebiveis) {
        this.valorDevolucesRecebiveis = valorDevolucesRecebiveis;
    }

    public Double getValorDevolucesRecebiveis() {
        return valorDevolucesRecebiveis;
    }

    public void setTotalizador(List<TotalizadorFormasPagamento> totalizador) {
        this.totalizador = totalizador;
    }

    public List<TotalizadorFormasPagamento> getTotalizador() {
        return totalizador;
    }

    public void setAuxMpr(List<Integer> auxMpr) {
        this.auxMpr = auxMpr;
    }

    public List<Integer> getAuxMpr() {
        return auxMpr;
    }

    public Integer getFonteDados() {
        return fonteDados;
    }

    public void setFonteDados(Integer fonteDados) {
        this.fonteDados = fonteDados;
    }

    public String getNomeAlunoEncontrado() {
        return nomeAlunoEncontrado;
    }

    public void setNomeAlunoEncontrado(String nomeAlunoEncontrado) {
        this.nomeAlunoEncontrado = nomeAlunoEncontrado;
    }

    public Date getDataInicioFaturamento() {
        return dataInicioFaturamento;
    }

    public void setDataInicioFaturamento(Date dataInicioFaturamento) {
        this.dataInicioFaturamento = dataInicioFaturamento;
    }

    public Date getDataTerminoFaturamento() {
        return dataTerminoFaturamento;
    }

    public void setDataTerminoFaturamento(Date dataTerminoFaturamento) {
        this.dataTerminoFaturamento = dataTerminoFaturamento;
    }
}
