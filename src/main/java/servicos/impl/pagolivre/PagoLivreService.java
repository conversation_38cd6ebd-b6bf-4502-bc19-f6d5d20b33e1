package servicos.impl.pagolivre;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.GeradorTelefone;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.gson.Gson;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.DetalhesRequestEnviadaVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.DetalhesRequestEnviada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.impl.gatewaypagamento.RecebedorDTO;
import servicos.interfaces.PagoLivreServiceInterface;
import servicos.propriedades.PropsService;

import java.io.File;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/*
 * Created by Rodrigo Estulano on 23/09/2021.
 */

public class PagoLivreService extends AbstractCobrancaOnlineServiceComum implements PagoLivreServiceInterface {

    private String URL_API_PAGO_LIVRE = "";
    private String URL_GATEWAY_API_PAGO_LIVRE = "";
    private String TOKEN_PAGO_LIVRE = "";
    private String TOKEN_GATEWAY_PAGO_LIVRE = "";
    private ConvenioCobranca convenioCobrancaDAO;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private ConvenioCobrancaVO convenioPagoLivre;
    private Transacao transacaoDAO;
    private TipoTransacaoEnum tipoTransacaoEnum;

    public PagoLivreService(Connection con, AmbienteEnum ambienteEnum, TipoTransacaoEnum tipoTransacaoEnum) throws Exception {
        //usado nas requisições de Merchant onde não tem vínculo nenhum com convênio para preencher os properties
        super(con);
        this.tipoTransacaoEnum = tipoTransacaoEnum;
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        popularToken(ambienteEnum);
    }

    public PagoLivreService(Connection con, Integer empresa, Integer convenioCobranca, TipoTransacaoEnum tipoTransacaoEnum) throws Exception {
        super(con);
        this.tipoTransacaoEnum = tipoTransacaoEnum;
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioPagoLivre = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    public RespostaHttpDTO cadastrarMerchant(AmbienteEnum ambienteEnum,
                                             String cnpj, String nomeEmpresa, String emailEmpresa,
                                             String dddTelefone, String telefone,
                                             ContaCorrenteVO contaCorrenteVO,
                                             String emailUsuario, String nomeUsuario) throws Exception {
        String codBanco = contaCorrenteVO.getBanco().getCodigoBanco().toString();
        String agencia = contaCorrenteVO.getAgencia();
        String digAgencia = (UteisValidacao.emptyString(contaCorrenteVO.getAgenciaDV()) ? "" : (contaCorrenteVO.getAgenciaDV()));
        String conta = Uteis.removerZeroAEsquerda(contaCorrenteVO.getContaCorrente(), 10);
        String digConta = (UteisValidacao.emptyString(contaCorrenteVO.getContaCorrenteDV()) ? "" : (contaCorrenteVO.getContaCorrenteDV()));

        MerchantPagoLivreDto merchantPagoLivreDto = new MerchantPagoLivreDto();
        merchantPagoLivreDto.setEmail(StringUtils.lowerCase(emailEmpresa));
        merchantPagoLivreDto.setCnpj(Uteis.formatarCpfCnpj(cnpj, true));
        merchantPagoLivreDto.setCompanyName(StringUtilities.doRemoverAcentos(nomeEmpresa));
        merchantPagoLivreDto.setPhoneNumber(dddTelefone + telefone);

        BankAccountPagoLivreDto bankDto = new BankAccountPagoLivreDto();
        bankDto.setBankNumber(codBanco);
        bankDto.setAgencyNumber(agencia);
        bankDto.setAgencyDigit(digAgencia);
        bankDto.setAccountNumber(conta);
        bankDto.setAccountDigit(digConta);
        bankDto.setType(TipoContaMerchantPagoLivre.CONTA_CORRENTE.getCodigo().toString());
        bankDto.setCnpj(Uteis.formatarCpfCnpj(cnpj, true));
        merchantPagoLivreDto.setBankAccount(bankDto);

        PortalUsersPagoLivreDto portalUsers = new PortalUsersPagoLivreDto();
        List<PortalUsersPagoLivreDto> listaPortalUsers = new ArrayList<PortalUsersPagoLivreDto>();
        portalUsers.setRole(TipoRolePagoLivreEnum.MERCHANT_FINANCIAL.getDescricao());
        portalUsers.setEmail(StringUtils.lowerCase(emailUsuario));
        portalUsers.setName(Uteis.retirarAcentuacao(nomeUsuario));
        listaPortalUsers.add(portalUsers);
        merchantPagoLivreDto.setPortalUsers(listaPortalUsers);

        PagoLivreRequisicaoDto requisicaoDto = new PagoLivreRequisicaoDto();
        String url = apiUrl(ambienteEnum) + "/merchant";
        requisicaoDto.setEnvio(body(merchantPagoLivreDto));
        return executarRequestPagoLivre(url, requisicaoDto.getEnvio(), MetodoHttpEnum.POST);
    }

    public RespostaHttpDTO alterarMerchant(String merchantIdPagoLivre,
                                           String nomeEmpresa, String emailEmpresa,
                                           String dddTelefone, String telefone) throws Exception {
        MerchantPagoLivreDto merchantPagoLivreDto = new MerchantPagoLivreDto();
        merchantPagoLivreDto.setMerchantId(merchantIdPagoLivre);
        merchantPagoLivreDto.setEmail(StringUtils.lowerCase(emailEmpresa));
        merchantPagoLivreDto.setCompanyName(Uteis.removerCaracteresNaoAscii(nomeEmpresa));
        merchantPagoLivreDto.setPhoneNumber(dddTelefone + telefone);
        merchantPagoLivreDto.setPortalUsers(null);

        PagoLivreRequisicaoDto requisicaoDto = new PagoLivreRequisicaoDto();
        String url = apiUrl(this.convenioPagoLivre.getAmbiente()) + "/merchant";
        requisicaoDto.setEnvio(body(merchantPagoLivreDto));
        return executarRequestPagoLivre(url, requisicaoDto.getEnvio(), MetodoHttpEnum.PUT);
    }

    public RespostaHttpDTO consultarMerchant() throws Exception {
        String endpoint = "/merchant/" + this.convenioPagoLivre.getCodigoAutenticacao01();
        return executarRequestPagoLivre(endpoint, null, MetodoHttpEnum.GET);
    }

    public RespostaHttpDTO consultarMerchant(String merchantId) throws Exception {
        String endpoint = "/merchant/" + merchantId;
        return executarRequestPagoLivre(endpoint, null, MetodoHttpEnum.GET);
    }

    public RespostaHttpDTO alterarContaBancariaMerchant(AmbienteEnum ambienteEnum, JSONObject json) throws Exception {
        String url = apiUrl(ambienteEnum) + "/merchant/bankaccount";
        return executarRequestPagoLivre(url, json.toString(), MetodoHttpEnum.PUT);
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    private String apiUrl(AmbienteEnum ambienteEnum) {
        if (ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)) {
            return PropsService.getPropertyValue(PropsService.urlApiPagoLivreSandbox);
        } else {
            return PropsService.getPropertyValue(PropsService.urlApiPagoLivreProducao);
        }
    }

    private void popularInformacoes() {
        if (this.convenioPagoLivre != null) {
            if (this.convenioPagoLivre.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.URL_API_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.urlApiPagoLivreProducao);
                this.URL_GATEWAY_API_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.urlApiGatewayPagoLivreProducao);
                this.TOKEN_GATEWAY_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenGatewayPagoLivreProducao);
            } else {
                this.URL_API_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.urlApiPagoLivreSandbox);
                this.URL_GATEWAY_API_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.urlApiGatewayPagoLivreSandbox);
                this.TOKEN_GATEWAY_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenGatewayPagoLivreSandbox);
            }
            popularToken(this.convenioPagoLivre.getAmbiente());
        }
    }

    private void popularToken(AmbienteEnum ambienteEnum) {
        if (ambienteEnum.equals(AmbienteEnum.PRODUCAO)) {
            if (this.convenioPagoLivre != null && //sempre verificar se é null pois tem caso que não tem no momento do cadastro por exemplo
                    !UteisValidacao.emptyString(this.convenioPagoLivre.getCodigoAutenticacao03().trim())) {
                this.TOKEN_PAGO_LIVRE = this.convenioPagoLivre.getCodigoAutenticacao03().trim();
            } else if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.PAGOLIVRE)) {
                this.TOKEN_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenPagoLivreProducao);
            } else if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.FACILITEPAY)) {
                this.TOKEN_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenPagoLivreFacilitePayProducao);
            } else if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.AFINZ)) {
                this.TOKEN_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenPagoLivreProducao);
            }
        } else {
            if (this.convenioPagoLivre != null && //sempre verificar se é null pois tem caso que não tem no momento do cadastro por exemplo
                    !UteisValidacao.emptyString(this.convenioPagoLivre.getCodigoAutenticacao03().trim())) {
                this.TOKEN_PAGO_LIVRE = this.convenioPagoLivre.getCodigoAutenticacao03().trim();
            } else if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.PAGOLIVRE)) {
                this.TOKEN_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenPagoLivreSandbox);
            } else if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.FACILITEPAY)) {
                this.TOKEN_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenPagoLivreFacilitePaySandbox);
            } else if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.AFINZ)) {
                this.TOKEN_PAGO_LIVRE = PropsService.getPropertyValue(PropsService.tokenPagoLivreSandbox);
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoPagoLivreVO(), ((this.tipoTransacaoEnum != null) ? this.tipoTransacaoEnum : TipoTransacaoEnum.PAGOLIVRE), this.convenioPagoLivre);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);

            if (UteisValidacao.emptyString(pessoaVO.getCustomerIdPagoLivre())) {
                ResponsavelPagamentoTO responsavelPagamentoTO = obterPessoaResponsavelPagamento(pessoaVO, clienteVO);
                incluirPessoaPagoLivre(pessoaVO, transacao, responsavelPagamentoTO);
            }

            JSONObject parametrosPagamento = criarTransacaoJSON(transacao, dadosCartao, pessoaVO.getCustomerIdPagoLivre());

            transacao.setParamsEnvio(parametrosPagamento.toString());
            transacaoDAO.alterar(transacao);

            if (UteisValidacao.emptyString(pessoaVO.getCustomerIdPagoLivre())) {
                throw new Exception("Não foi possível incluir o cliente na " + this.getNomeAdquirente());
            }

            validarDadosTransacao(transacao, dadosCartao);

            RespostaHttpDTO retorno = executarRequestPagoLivre("/payment", parametrosPagamento.toString(), MetodoHttpEnum.POST,
                    true, "transacao", transacao.getCodigo());
            processarRetorno(transacao, retorno.getResponse(), parametrosPagamento);

            //consultar para verificar se já foi Concluída com sucesso...
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                realizarConsultaSituacao(3, transacao, parametrosPagamento);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
            return transacao;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return null;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        // atualiza situação da transação, caso ela esteja aguardando
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            retransmitirTransacao(transacaoVO, null, null);
        }
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    @Override
    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            RespostaHttpDTO retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetorno(transacaoVO, retorno.getResponse(), null);
        }
    }

    @Override
    public String consultarTransacao(Integer id) throws Exception {
        return null;
    }

    @Override
    public void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO) throws Exception {
    }

    private void incluirPessoaPagoLivre(PessoaVO pessoaVO, TransacaoVO transacaoVO,
                                        ResponsavelPagamentoTO responsavelPagamentoTO) throws Exception {
        Pessoa pessoaDAO;
        try {
            pessoaDAO = new Pessoa(getCon());

            validarDadosObrigatorios(responsavelPagamentoTO);

            //incluir Customer PagoLivre
            RespostaHttpDTO resposta = incluirPessoaRequest(pessoaVO, responsavelPagamentoTO, transacaoVO, false, false);

            if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.FACILITEPAY) &&
                    resposta.getHttpStatus() != 200) {

                //telefone vazio ou já existe para outro usuário
                if (UteisValidacao.emptyString(responsavelPagamentoTO.getTelefoneValido()) ||
                        resposta.getResponse().toLowerCase().contains("celular pertence a outro")) {

                    //tentar incluir forçando celular fictício
                    resposta = incluirPessoaRequest(pessoaVO, responsavelPagamentoTO, transacaoVO, false, true);

                    //mesmo forçando celular na requisição acima pode ser que dê erro de email já sendo usado, então neste caso forçar celular e email juntos.
                    if (emailInvalido(responsavelPagamentoTO, resposta)) {
                        //tentar incluir forçando celular e email fictício
                        resposta = incluirPessoaRequest(pessoaVO, responsavelPagamentoTO, transacaoVO, true, true);
                    }
                }
                //email vazio ou já existe para outro usuário
                else if (emailInvalido(responsavelPagamentoTO, resposta)) {
                    resposta = incluirPessoaRequest(pessoaVO, responsavelPagamentoTO, transacaoVO, true, false);
                }
            }

            if (resposta.getHttpStatus() != 200) {
                String respostaStr = resposta.getResponse();
                if (respostaStr.contains("The MobileNumber field is required")) {
                    respostaStr = "O Celular é obrigatório";
                }

                throw new ConsistirException("Falha ao inserir o cliente na " + getNomeAdquirente() + ": " + respostaStr);
            } else {
                JSONObject jsonObject = new JSONObject(resposta.getResponse());
                if (jsonObject.has("customerId")) {
                    String customerId = jsonObject.optString("customerId");
                    if (!UteisValidacao.emptyString(customerId)) {
                        pessoaVO.setCustomerIdPagoLivre(customerId);
                        pessoaDAO.alterarCustomerIdPagoLivre(pessoaVO);
                    }
                }
            }
        } catch (Exception e) {
            incluirHistoricoRetornoTransacao(transacaoVO, e.getMessage(), "incluirPessoaErro");
            throw e;
        } finally {
            pessoaDAO = null;
        }
    }

    private boolean emailInvalido(ResponsavelPagamentoTO responsavelPagamentoTO, RespostaHttpDTO resposta) {
        return (UteisValidacao.emptyString(responsavelPagamentoTO.getEmailValido()) ||
                resposta.getResponse().toLowerCase().contains("email field is required") ||
                resposta.getResponse().toLowerCase().contains("e-mail field is required") ||
                resposta.getResponse().toLowerCase().contains("cs002") || //O e-mail \<EMAIL>\u0027 j\u00E1 est\u00E1 em uso.
                resposta.getResponse().toLowerCase().contains("cs003") || //E-mail pertence a outro usu\u00E1rio cadastrado.
                (resposta.getResponse().toLowerCase().contains("e-mail") &&
                        (resposta.getResponse().toLowerCase().contains("sendo utilizado") ||
                                resposta.getResponse().toLowerCase().contains("j\\u00e1 est\\u00e1") ||
                                resposta.getResponse().toLowerCase().contains("em uso") ||
                                resposta.getResponse().toLowerCase().contains("cadastrado") ||
                                resposta.getResponse().toLowerCase().contains("pertence"))
                ));
    }

    private String getEmailForcado(Integer codigoPessoa) {
        String key = "";
        try {
            key = DAO.resolveKeyFromConnection(this.getCon());
        } catch (Exception ignored) {
        }
        return ("fk_pessoa_" + codigoPessoa + (!UteisValidacao.emptyString(key) ? ("_" + key.substring(0, 4)) : "") + "@pactofk.com");
    }

    private RespostaHttpDTO incluirPessoaRequest(PessoaVO pessoaVO, ResponsavelPagamentoTO responsavelPagamentoTO,
                                                 TransacaoVO transacaoVO, boolean forcarEmail, boolean forcarTelefone) throws Exception {

        StringBuilder identificadorOperacao = new StringBuilder();
        if (forcarEmail) {
            identificadorOperacao.append("-Email-Forcado");
        }
        if (forcarTelefone) {
            identificadorOperacao.append("-Telefone-Forcado");
        }

        JSONObject customerJSON = criarCustomerJSON(pessoaVO, responsavelPagamentoTO, forcarEmail, forcarTelefone);
        incluirHistoricoRetornoTransacao(transacaoVO, customerJSON.toString(), ("incluirPessoa-Envio" + identificadorOperacao));
        RespostaHttpDTO resposta = executarRequestPagoLivre("/customer", customerJSON.toString(), MetodoHttpEnum.POST,
                true, "pessoa.customeridpagolivre", pessoaVO.getCodigo());
        incluirHistoricoRetornoTransacao(transacaoVO, resposta.getResponse(), ("incluirPessoa-Retorno" + identificadorOperacao));
        return resposta;
    }

    public RespostaHttpDTO consultarTransacao(String codigoExterno) throws Exception {
        return executarRequestPagoLivre("/payment/paymentid/" + codigoExterno, null, MetodoHttpEnum.GET);
    }

    private JSONObject criarTransacaoJSON(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO, String customerIdPagoLivre) throws Exception {

        JSONObject payment = new JSONObject();

        //merchantId PagoLivre da empresa ao qual está sendo realizado o pagamento
        payment.put("merchantId", this.convenioPagoLivre.getCodigoAutenticacao01());

        //CustomerId Previamente cadastrado na pagolivre
        payment.put("customerId", customerIdPagoLivre);

        //valor do pagamento
        payment.put("amount", transacaoVO.getValor());

        //Quantidade de parcelas do pagamento
        payment.put("installments", cartaoCreditoTO.getParcelas());

        //Identificador da transação
        String identificador = "TRA" + transacaoVO.getCodigo();
        payment.put("orderKey", identificador);
        Uteis.logarDebug("IDENTIFICADOR " + getNomeAdquirente().toUpperCase() + ": " + identificador);

        JSONObject card = new JSONObject();
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getTokenPagoLivre())) {
            gravarOutrasInformacoes("", cartaoCreditoTO, transacaoVO);

//            card.put("cardMask", "");
            card.put("cardToken", cartaoCreditoTO.getTokenPagoLivre());
            card.put("cardHolder", !UteisValidacao.emptyString(cartaoCreditoTO.getNomeTitular()) ? cartaoCreditoTO.getNomeTitular() : "");
            card.put("cardBrand", cartaoCreditoTO.getBand() != null ? cartaoCreditoTO.getBand() : OperadorasExternasAprovaFacilEnum.MASTERCARD);
            card.put("validThru", criarParametrosValidadeCardUsandoToken(cartaoCreditoTO));
        } else {
             card = gerarTokenCartao(cartaoCreditoTO, transacaoVO);
        }
        payment.put("card", card);

        //verifica se existe regra de recebedores
        //Regras de divisão da transação
        verificarSplit(transacaoVO, payment);

        return payment;
    }

    private JSONObject criarParametrosValidadeCardUsandoToken(CartaoCreditoTO cartaoCreditoTO) {
        JSONObject validThru = new JSONObject();
        String month = cartaoCreditoTO.getValidade().substring(0, 2);
        String year = cartaoCreditoTO.getAnoValidadeYYYY();
        validThru.put("month", Integer.parseInt(month));
        validThru.put("year", Integer.parseInt(year));
        return validThru;
    }

    private void processarRetorno(TransacaoVO transacao, String retorno, JSONObject parametrosPagamento) {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        transacao.setParamsResposta(retorno);
        JSONObject retornoJSON = new JSONObject(retorno);

        if (retornoJSON.has("paymentId")) {

            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                String paymentId = retornoJSON.optString("paymentId");
                if (!UteisValidacao.emptyString(paymentId)) {
                    transacao.setCodigoExterno(paymentId);
                }
            }

            try {
                //IDENTIFICAR A OPERADORA DO CARTAO
                if (parametrosPagamento != null) {
                    transacao.setBandeiraPagamento(null);
                    OperadorasExternasAprovaFacilEnum operadora = OperadorasExternasAprovaFacilEnum.valueOf(parametrosPagamento.getJSONObject("card").optInt("cardBrand"));
                    if (operadora != null && !UteisValidacao.emptyString(operadora.getDescricao())) {
                        for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                            String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                            String operadoraPagamento = operadora.getDescricao().toUpperCase().replaceAll(" ", "");
                            if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                                transacao.setBandeiraPagamento(ope);
                                break;
                            }
                        }
                    }
                }
            } catch (Exception ignored) {
            }


            //DOC PAGOLIVRE https://documenter.getpostman.com/view/11754280/Tzm6nwxG#7f836e4d-901e-42c8-aae8-43af1d438398
            //Valores possíveis: created, finalized, denied, error, canceled, aborted, rejected, authorizing, authorized, captured, authenticating, canceling
            String status = retornoJSON.getString("status");
            if (status.equalsIgnoreCase("finalized")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if (status.equalsIgnoreCase("created") || status.equalsIgnoreCase("authorized") || status.equalsIgnoreCase("authorizing")
                    || status.equalsIgnoreCase("authenticating") || status.equalsIgnoreCase("captured")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("canceled")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equalsIgnoreCase("denied") || status.equalsIgnoreCase("rejected") ||
                    (status.equalsIgnoreCase("error") && retorno.contains("Transação não autorizada"))) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }

        } else if (retornoJSON.has("errors")) {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
        }
    }

    public void realizarConsultaSituacao(int qtd, TransacaoVO transacaoVO, JSONObject parametrospagamento) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            if (qtd > 0) {
                RespostaHttpDTO retorno = consultarTransacao(transacaoVO.getCodigoExterno());
                processarRetorno(transacaoVO, retorno.getResponse(), parametrospagamento);
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    Thread.sleep(1000);
                    realizarConsultaSituacao(qtd - 1, transacaoVO, parametrospagamento);
                }
            }
        }
    }

    private JSONObject gerarTokenCartao(CartaoCreditoTO cartaoCreditoTO, TransacaoVO transacaoVO) throws Exception {
        try {
            String numeroCartao = "";
            if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
                numeroCartao = cartaoCreditoTO.getNumero();
            }
            gravarOutrasInformacoes(numeroCartao, cartaoCreditoTO, transacaoVO);

            JSONObject card = new JSONObject();
            //monta o objeto para fazer a chamada do endpoint de tokenizar no método gerarTokenCartao
            card.put("cardBrand", cartaoCreditoTO.getBand());
            card.put("cardNumber", numeroCartao);
            card.put("cvv", cartaoCreditoTO.getCodigoSeguranca());
            card.put("cardHolder", cartaoCreditoTO.getNomeTitular());
            card.put("validThru", criarParametrosValidadeCard(cartaoCreditoTO));

            RespostaHttpDTO retorno = executarRequestGatewayPagoLivre(card.toString());
            JSONObject retornoJSON = new JSONObject(retorno.getResponse());
            JSONObject cardTokenizado = new JSONObject();

            if (retorno.getHttpStatus() == 200) {
                if (retornoJSON.has("cardToken")) {
                    //seta cada item da resposta Json de retorno
                    cardTokenizado.put("cardToken", retornoJSON.optString("cardToken"));
                    cardTokenizado.put("cardMask", retornoJSON.optString("cardMask"));
                    cardTokenizado.put("cardHolder", retornoJSON.optString("cardHolder"));
                    cardTokenizado.put("cardBrand", retornoJSON.optInt("cardBrand"));
                    cardTokenizado.put("validThru", setarParametrosValidadeCardJson(retornoJSON));
                    return cardTokenizado;
                }
            } else {
                throw new Exception("Não foi possível gerar o token do cartão na " + getNomeAdquirente() + ": " + retorno.getResponse());
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível gerar o token do cartão na " + getNomeAdquirente() + ": " + ex.getMessage());
        }
    }

    private RespostaHttpDTO executarRequestPagoLivre(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        return executarRequestPagoLivre(endPoint, body, metodoHttpEnum, false, null, null);
    }

    private RespostaHttpDTO executarRequestPagoLivre(String endPoint, String body, MetodoHttpEnum metodoHttpEnum, boolean gravarDetalhesRequestEnviada, String nomeTabelaForeignKey,
                                                     Integer codigoTabelaForeignKey) throws Exception {
        long inicio = 0;
        long tempoDecorrido = 0;
        String path = this.URL_API_PAGO_LIVRE + endPoint;
        Uteis.logarDebug("Iniciando requisição na Pagolivre | URL: " + path);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + this.TOKEN_PAGO_LIVRE);

        RequestHttpService service = new RequestHttpService();
        inicio = System.currentTimeMillis();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        tempoDecorrido = System.currentTimeMillis() - inicio;
        Uteis.logarDebug("Tempo de resposta da Pagolivre: " + tempoDecorrido + "ms");
        if (gravarDetalhesRequestEnviada) {
            DetalhesRequestEnviada detalhesRequestEnviadaDAO;
            Connection connection;
            try {
                connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon());
                detalhesRequestEnviadaDAO = new DetalhesRequestEnviada(connection);
                detalhesRequestEnviadaDAO.incluir(montarObjDetalhesRequestEnviada(path, respostaHttpDTO, tempoDecorrido, nomeTabelaForeignKey, codigoTabelaForeignKey));
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                connection = null;
                detalhesRequestEnviadaDAO = null;
            }
        }
        respostaHttpDTO.setRequestBody(body);
        return respostaHttpDTO;
    }

    public DetalhesRequestEnviadaVO montarObjDetalhesRequestEnviada(String endpoint, RespostaHttpDTO respostaHttpDTO,
                                                                    long tempoRequisicao, String nomeTabelaForeignKey, Integer codigoTabelaForeignKey) {
        DetalhesRequestEnviadaVO obj = new DetalhesRequestEnviadaVO();
        obj.setDataRegistro(Calendario.hoje());
        obj.setUrl(endpoint);
        obj.setStatusResponse(respostaHttpDTO.getHttpStatus());
        obj.setTempoRequisicaoMs(tempoRequisicao);
        if (obj.getStatusResponse() == 200 || obj.getStatusResponse() == 201) {
            obj.setSucesso(true);
        } else {
            obj.setSucesso(false);
        }
        obj.setNomeTabelaForeignKey(nomeTabelaForeignKey);
        obj.setCodigoTabelaForeignKey(codigoTabelaForeignKey);
        return obj;
    }

    //Utilizado somente para tokenizar o cartão
    private RespostaHttpDTO executarRequestGatewayPagoLivre(String body) throws Exception {

        String path = this.URL_GATEWAY_API_PAGO_LIVRE + "/creditcard/token";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "X-API-Key " + this.TOKEN_GATEWAY_PAGO_LIVRE);

        RequestHttpService service = new RequestHttpService();
        return service.executeRequest(path, headers, null, body, MetodoHttpEnum.POST);
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("cancellationRequestId", transacaoVO.getCodigoExterno());
        jsonEnvio.put("reason", "Cancelamento do pagamento");

        RespostaHttpDTO respCancelamento = executarRequestPagoLivre("/payment/cancel/" + transacaoVO.getCodigoExterno(), jsonEnvio.toString(), MetodoHttpEnum.PUT);
        processarRetornoCancelamento(transacaoVO, respCancelamento.getResponse());
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retornoCancelamento) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoCancelamento, "processarRetornoCancelamento");
        transacaoVO.setResultadoCancelamento(retornoCancelamento);
        try {
            JSONObject cancelamentoJSON = new JSONObject(retornoCancelamento);
            String status = cancelamentoJSON.getString("status");
            if (status.equalsIgnoreCase("canceled")) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacaoVO.setDataHoraCancelamento(Calendario.hoje());
            } else {
                consultarSituacaoCobrancaTransacao(transacaoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            consultarSituacaoCobrancaTransacao(transacaoVO);
        }
    }

    private JSONObject criarParametrosValidadeCard(CartaoCreditoTO cartaoCreditoTO) {
        JSONObject validThru = new JSONObject();
        validThru.put("month", cartaoCreditoTO.getValidade().substring(0, 2));
        validThru.put("year", cartaoCreditoTO.getAnoValidadeYYYY());
        return validThru;
    }

    private JSONObject setarParametrosValidadeCardJson(JSONObject jsonObject) {
        JSONObject validThru = new JSONObject();
        validThru.put("month", jsonObject.getJSONObject("validThru").optInt("month"));
        validThru.put("year", jsonObject.getJSONObject("validThru").optInt("year"));
        return validThru;
    }

    private JSONObject criarCustomerJSON(PessoaVO pessoaVO, ResponsavelPagamentoTO responsavelPagamentoTO, boolean forcarEmail, boolean forcarTelefone) {
        JSONObject customer = new JSONObject();
        customer.put("name", Uteis.retirarAcentuacao(responsavelPagamentoTO.getNome()));
        customer.put("cpf", Uteis.removerCaracteresNaoAscii(Uteis.formatarCpfCnpj(responsavelPagamentoTO.getCpf(), true)));

        if (forcarEmail) {
            String emailForcado = getEmailForcado(pessoaVO.getCodigo());
            customer.put("email", emailForcado);
        } else {
            if (!responsavelPagamentoTO.getEmails().isEmpty()) {
                for (EmailVO emailVO : responsavelPagamentoTO.getEmails()) {
                    customer.put("email", emailVO.getEmail());
                    break;
                }
            }
        }

        if (forcarTelefone) {
            String telefoneForcado = GeradorTelefone.gerarNumeroTelefone(9);
            customer.put("mobileNumber", UteisTelefone.removerCaracteresEspeciais(telefoneForcado));
        } else {
            if (!responsavelPagamentoTO.getTelefones().isEmpty()) {
                for (TelefoneVO telefoneVO : responsavelPagamentoTO.getTelefones()) {
                    customer.put("mobileNumber", UteisTelefone.removerCaracteresEspeciais(telefoneVO.getNumero()));
                    break;
                }
            }
        }
        return customer;
    }

    public boolean enviarEmailUserPagoLivre(String emailEnviar, String nomeEmpresa, String cnpj, PortalUsersPagoLivreDto obj) throws Exception {
        try {

            if (UteisValidacao.emptyString(emailEnviar)) {
                throw new Exception("Informe um e-mail.");
            }
            if (!UteisEmail.getValidEmail(emailEnviar)) {
                throw new Exception("O e-mail " + emailEnviar.toUpperCase() + " é inválido.");
            }

            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
            UteisEmail uteis = new UteisEmail();
            String assunto = "Dados de acesso ao portal " + tipoTransacaoEnum.getDescricao();
            String nomeRemetente = tipoTransacaoEnum.getDescricao();

            uteis.novo(assunto, configuracaoSistemaCRMVO);
            uteis.enviarEmail(emailEnviar, "", gerarCorpoEmailAcessoPortal(obj, nomeEmpresa, cnpj).toString(), nomeRemetente,
                    configuracaoSistemaCRMVO.getIntegracaoPacto(), configuracaoSistemaCRMVO.preparaEnvioSendy());

            return true;
        } catch (Exception ex) {
            if (ex.getMessage().toLowerCase().contains("daily message limit")) {
                throw new Exception("Infelizmente seu limite de mensagens diário foi atingido, por favor troque de conta ou espere até amanhã.");
            } else {
                throw new Exception("Erro no Envio de Email. Verifique as Configurações e Tente Novamente: " + ex.getMessage());
            }
        }
    }

    private StringBuilder gerarCorpoEmailAcessoPortal(PortalUsersPagoLivreDto obj, String nomeEmpresa, String cnpj) throws Exception {
        File arq = null;
        if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.PAGOLIVRE)) {
            arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailAcessoPortalPagoLivre.txt").toURI());
        } else if (this.tipoTransacaoEnum.equals(TipoTransacaoEnum.FACILITEPAY)) {
            arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailAcessoPortalFacilitePay.txt").toURI());
        }
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        String aux = texto.toString()
                .replaceAll("#EMPRESA_RAZAO_SOCIAL", nomeEmpresa)
                .replaceAll("#EMPRESA_CNPJ", Uteis.formatarCpfCnpj(cnpj, false))
                .replaceAll("#USUARIO", obj.getEmail())
                .replaceAll("#SENHA", obj.getPassword());
        return new StringBuilder(aux);
    }

    public List<MerchantPagoLivreDto> obterMerchantsByParentId(String merchantId) throws Exception {
        List<MerchantPagoLivreDto> lista = new ArrayList<>();

        String path = this.URL_API_PAGO_LIVRE + "/merchant/byparentid/" + merchantId;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + this.TOKEN_PAGO_LIVRE);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, null, MetodoHttpEnum.GET);

        if (respostaHttpDTO.getHttpStatus() != 200) {
            throw new Exception("Não foi possível consultar os merchants by parentId:" + respostaHttpDTO.getResponse());
        }

        JSONArray jsonArray = new JSONArray(respostaHttpDTO.getResponse());
        for (int e = 0; e < jsonArray.length(); e++) {
            JSONObject obj = jsonArray.getJSONObject(e);

            if (obj.has("bankAccount")) {
                MerchantPagoLivreDto recebedorDTO = new MerchantPagoLivreDto(obj);
                BankAccountPagoLivreDto bankAccountPagoLivreDto = new BankAccountPagoLivreDto(obj.getJSONObject("bankAccount"));
                recebedorDTO.setBankAccount(bankAccountPagoLivreDto);

                if (!UteisValidacao.emptyString(recebedorDTO.getMerchantId())) {
                    lista.add(recebedorDTO);
                }
            }
        }
        return lista;
    }

    private void verificarSplit(TransacaoVO transacaoVO, JSONObject payment) throws Exception {
        List<RecebedorDTO> lista = obterRecebedoresSplitPagamentoDoConvenio(transacaoVO, this.convenioPagoLivre);

        if (!UteisValidacao.emptyList(lista)) {
            JSONArray splitPayment = new JSONArray();
            for (RecebedorDTO dto : lista) {
                JSONObject rules = new JSONObject();
                rules.put("merchantId", dto.getId());
                rules.put("amount", dto.getValorCentavosDouble());
                rules.put("amountType", "VALUE");
                splitPayment.put(rules);
            }

            if (splitPayment.length() > 0) {
                payment.put("splitPayment", splitPayment);
            }
        }
    }

    public void validarDadosObrigatorios(ResponsavelPagamentoTO responsavelPagamentoTO) throws ConsistirException {
        List<String> camposInvalidos = new ArrayList<>();

        if (!SuperVO.verificaCPF(responsavelPagamentoTO.getCpf())) {
            if (responsavelPagamentoTO.isUsandoDoResponsavel()) {
                camposInvalidos.add(" CPF DO RESPONSAVEL");
            } else {
                camposInvalidos.add(" CPF");
            }
        }

        //validar email e telefone somente se não for FacilitePay
        if (this.tipoTransacaoEnum != null &&
                !this.tipoTransacaoEnum.equals(TipoTransacaoEnum.FACILITEPAY)) {

            if (UteisValidacao.emptyString(responsavelPagamentoTO.getEmailValido())) {
                camposInvalidos.add(" EMAIL");
            }

            if (UteisValidacao.emptyString(responsavelPagamentoTO.getTelefoneValido())) {
                camposInvalidos.add(" TELEFONE");
            }
        }

        String campos = "";
        if (!UteisValidacao.emptyList(camposInvalidos)) {
            for (String campo : camposInvalidos) {
                campos += campo + ",";
            }
            if (camposInvalidos.size() > 1) {
                String mensagem = "Alguns campos são obrigatórios para o Gateway de pagamentos. Os campos: " + Uteis.removerUltimoCaractere(campos) + " estão inválidos ou não foram informados!";
                throw new ConsistirException(mensagem);
            } else {
                String mensagem = "Alguns campos são obrigatórios para o Gateway de pagamentos. O campo: " + Uteis.removerUltimoCaractere(campos) + " está inválido ou não foi informado!";
                throw new ConsistirException(mensagem);
            }
        }
    }

    private String getNomeAdquirente() {
        return (this.tipoTransacaoEnum != null) ? this.tipoTransacaoEnum.getDescricao() : "PagoLivre";
    }
}
