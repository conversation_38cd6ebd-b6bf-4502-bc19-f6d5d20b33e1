package servicos.impl.gatewaypagamento;

import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.to.TotalizadorRetentativaTO;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import controle.arquitetura.exceptions.CobrancaException;
import controle.financeiro.EstornoReciboControle;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.PactoPayConfig;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONObject;
import org.json.XML;
import servicos.SuperServico;
import servicos.impl.apf.APF;
import servicos.impl.apf.AprovaFacilService;
import servicos.impl.ceopag.CeopagService;
import servicos.impl.cieloecommerce.CieloeCommerceService;
import servicos.impl.dcccaixaonline.DccCaixaOnlineService;
import servicos.impl.fitnessCard.FitnessCardService;
import servicos.impl.getnet.GetNetECommerceService;
import servicos.impl.maxiPago.MaxiPagoService;
import servicos.impl.mundiPagg.MundiPaggService;
import servicos.impl.onepayment.OnePaymentService;
import servicos.impl.pactoPay.PactoPayTransacaoService;
import servicos.impl.pagarMe.PagarMeService;
import servicos.impl.pagbank.PagBankService;
import servicos.impl.pagolivre.PagoLivreRetornoEnum;
import servicos.impl.pagolivre.PagoLivreService;
import servicos.impl.redepay.ERedeService;
import servicos.impl.stone.StoneOnlineService;
import servicos.impl.stone.StoneRetornoEnum;
import servicos.impl.stoneV5.StoneOnlineV5RetornoPSPEnum;
import servicos.impl.stoneV5.StoneOnlineV5Service;
import servicos.impl.stripe.StripeService;
import servicos.impl.pinbank.PinBankService;
import servicos.impl.vindi.VindiService;
import servicos.interfaces.AprovacaoServiceInterface;

import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by johnys on 23/03/2017.
 */
public class CobrancaOnlineService extends SuperServico {

    protected TransacaoInterfaceFacade transacaoFacade;

    public CobrancaOnlineService(Connection con) throws Exception {
        super(con);
        this.transacaoFacade = new Transacao(con);
    }

    public static AprovacaoServiceInterface getImplementacaoAprovacaoService(TipoTransacaoEnum tipo, Integer empresa,
                                                                             Integer convenioCobranca, boolean usarPactoPay, Connection con) throws Exception {

        if (tipo.equals(TipoTransacaoEnum.PACTO_PAY) || usarPactoPay) {
            return new PactoPayTransacaoService(con, empresa, convenioCobranca);
        }

        ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
        ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        convenioCobrancaDAO = null;

        if (!convenioCobrancaVO.getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
            throw new Exception("Convênio de cobrança \"" + convenioCobrancaVO.getDescricao() + "\" não está ativo!");
        }

        if (tipo.equals(TipoTransacaoEnum.AprovaFacilCB)) {
            return new AprovaFacilService(con);
        } else if (tipo.equals(TipoTransacaoEnum.MAXIPAGO)) {
            return new MaxiPagoService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.FITNESS_CARD)) {
            return new FitnessCardService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.E_REDE)) {
            return new ERedeService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.GETNET_ONLINE)) {
            return new GetNetECommerceService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.VINDI)) {
            return new VindiService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.CIELO_ONLINE)) {
            return new CieloeCommerceService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.STONE_ONLINE)) {
            return new StoneOnlineService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5)) {
            return new StoneOnlineV5Service(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.MUNDIPAGG)) {
            return new MundiPaggService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.PAGAR_ME)) {
            return new PagarMeService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.PAGBANK)) {
            return new PagBankService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.STRIPE)) {
            return new StripeService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.PAGOLIVRE)) {
            return new PagoLivreService(con, empresa, convenioCobranca, TipoTransacaoEnum.PAGOLIVRE);
        } else if (tipo.equals(TipoTransacaoEnum.FACILITEPAY)) {
            return new PagoLivreService(con, empresa, convenioCobranca, TipoTransacaoEnum.FACILITEPAY);
        } else if (tipo.equals(TipoTransacaoEnum.CEOPAG)) {
            return new CeopagService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.PINBANK)) {
            return new PinBankService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.ONE_PAYMENT)) {
            return new OnePaymentService(con, empresa, convenioCobranca);
        } else if (tipo.equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE)) {
            return new DccCaixaOnlineService(con, empresa, convenioCobranca);
        } else {
            return null;
        }
    }

    public void estornarRecibo(TransacaoVO transacaoOriginal, Boolean estornarNaOperadora) throws Exception {
        if (transacaoOriginal.getTipo().equals(TipoTransacaoEnum.AprovaFacilCB)) {
            String url = transacaoOriginal.getUrlTransiente();
            init(url);
        }
        ReciboPagamento reciboFacade = new ReciboPagamento(transacaoFacade.getCon());
        MovPagamento movPagamentoFacade = new MovPagamento(transacaoFacade.getCon());
        MovProdutoParcela movProdutoParcelaFacade = new MovProdutoParcela(transacaoFacade.getCon());

        EstornoReciboControle estorno = new EstornoReciboControle();
        EstornoReciboVO estornoVO = new EstornoReciboVO();
        estornoVO.setExcluirNFSe(true);

        //usuario responsável cancelamento
        if (UteisValidacao.emptyNumber(transacaoOriginal.getUsuarioResponsavelCancelamento().getCodigo())) {
            Usuario usuarioDAO = new Usuario(transacaoFacade.getCon());
            transacaoOriginal.setUsuarioResponsavelCancelamento(usuarioDAO.getUsuarioRecorrencia());
            usuarioDAO = null;
        }

        estornoVO.setResponsavelEstornoRecivo(transacaoOriginal.getUsuarioResponsavelCancelamento());

        List<MovPagamentoVO> listaMovPagamento = new ArrayList();
        listaMovPagamento = movPagamentoFacade.consultarPorCodigoRecibo(transacaoOriginal.getReciboPagamento(), false, Uteis.NIVELMONTARDADOS_TODOS);

        estornoVO.setListaMovPagamento(listaMovPagamento);
        estornoVO.getListaTransacoes().add(transacaoOriginal);

        if (UteisValidacao.emptyList(transacaoOriginal.getListaParcelas())) {
            Uteis.logarDebug("CobrancaOnlineService - Lista de parcelas transação vazia | Transação " + transacaoOriginal.getCodigo());
            Transacao transacaoDAO = new Transacao(transacaoFacade.getCon());
            transacaoOriginal.setListaParcelas(transacaoDAO.obterParcelasDaTransacao(transacaoOriginal));
        }

        estornoVO.setListaMovParcela(transacaoOriginal.getListaParcelas());
        estornoVO.setEstornarOperadora(estornarNaOperadora);
        estornoVO.setReciboPagamentoVO(reciboFacade.consultarPorChavePrimaria(transacaoOriginal.getReciboPagamento(), Uteis.NIVELMONTARDADOS_TODOS));

        estorno.setEstornoReciboVO(estornoVO);

        reciboFacade.estornarReciboPagamento(estornoVO, movPagamentoFacade, movProdutoParcelaFacade, null, null, null, getCon().getAutoCommit());
    }

    public void gravarTentativaCartao(TransacaoVO transacaoVO) {
        CartaoTentativa cartaoDAO = null;
        try {
            if (transacaoVO == null) {
                Uteis.logar(null, "Transacao null...");
                return;
            }
            if (UteisValidacao.emptyString(transacaoVO.getCartaoMascarado())) {
                Uteis.logar(null, "Cartão em branco...");
                return;
            }
            if (transacaoVO.isTransacaoVerificarCartao()) {
                Uteis.logar(null, "Transação de verificação...");
                return;
            }

            //marcar a autorização de cobrança como verificado
            processarCartaoVerificado(transacaoVO);

            //enviar resultado da cobrança régua de cobrança PactoPay
            processarResultadoCobrancaReguaCobranca(transacaoVO);

            cartaoDAO = new CartaoTentativa(getCon());

            CartaoTentativaVO tentativaVO = new CartaoTentativaVO();
            tentativaVO.setTransacaoVO(transacaoVO);
            tentativaVO.setTransacao(transacaoVO.getCodigo());
            tentativaVO.setCartao(transacaoVO.getCartaoMascarado());
            tentativaVO.setCodigoRetorno(transacaoVO.getCodigoRetornoGestaoTransacao());
            tentativaVO.setConvenioCobrancaVO(transacaoVO.getConvenioCobrancaVO());
            tentativaVO.setTipoConvenioCobranca(transacaoVO.getConvenioCobrancaVO().getTipo());
            tentativaVO.setUsuario(transacaoVO.getUsuarioResponsavel().getCodigo());
            tentativaVO.setOperacaoRetornoCobranca(OperacaoRetornoCobrancaEnum.obter(transacaoVO.getCodigoRetornoGestaoTransacao(), transacaoVO.getConvenioCobrancaVO().getTipo()));

            if (transacaoVO.getCartaoCreditoTO() != null) {
                tentativaVO.setTransacaoPresencial(transacaoVO.getCartaoCreditoTO().isTransacaoPresencial());
            }

            cartaoDAO.incluir(tentativaVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            cartaoDAO = null;
        }
    }

    private void processarCartaoVerificado(TransacaoVO transacaoVO) {
        AutorizacaoCobrancaCliente autoCliDAO = null;
        try {
            //marcar a autorização de cobrança como verificad
            if (transacaoVO != null &&
                    !UteisValidacao.emptyString(transacaoVO.getTokenAragorn()) &&
                    !UteisValidacao.emptyNumber(transacaoVO.getValor())) {

                autoCliDAO = new AutorizacaoCobrancaCliente(this.getCon());
                autoCliDAO.processarCartaoVerificado(transacaoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            autoCliDAO = null;
        }
    }

    public NazgDTO obterNazgTO(String tokenAragorn) throws Exception {
        return new AragornService().obterNazg(tokenAragorn);
    }

    public void incluirHistoricoRetornoTransacao(TransacaoVO transacao, final String retorno, final String metodo) {
        Transacao transacaoDAO = null;
        Connection con = null;
        try {
            con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon());
            transacaoDAO = new Transacao(con);
            transacaoDAO.incluirHistoricoRetorno(transacao, retorno, metodo, (transacao != null ? transacao.getOrigemSincronizacao() : ""));
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, CobrancaOnlineService.class);
        } finally {
            transacaoDAO = null;
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    public void incluirHistoricoRetornoTransacao(TransacaoVO transacao, final String retorno, final String metodo, int statusServer, long tempoRequisicao) {
        Transacao transacaoDAO = null;
        Connection con = null;
        try {
            con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon());
            transacaoDAO = new Transacao(con);
            transacaoDAO.incluirHistoricoRetorno(transacao, retorno, metodo, (transacao != null ? transacao.getOrigemSincronizacao() : ""), statusServer, tempoRequisicao);
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, CobrancaOnlineService.class);
        } finally {
            transacaoDAO = null;
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    public void verificarException(TransacaoVO transacaoVO, Exception ex) throws Exception {
        if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
            throw ex;
        }
    }

    public void marcarTransacaoComErro(TransacaoVO transacaoVO, CobrancaException ex) {
        marcarTransacaoComErroGeral(transacaoVO, ex);
    }

    public void marcarTransacaoComErro(TransacaoVO transacaoVO, String msgErro) {
        marcarTransacaoComErroGeral(transacaoVO, new CobrancaException(CodigoRetornoPactoEnum.OUTRO, msgErro));
    }

    private void marcarTransacaoComErroGeral(TransacaoVO transacaoVO, CobrancaException cobrancaException) {
        if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
            Transacao transacaoDAO = null;
            try {
                transacaoDAO = new Transacao(getCon());

                //padrão para ser utilizado para apresentar no gestão de transação
                transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoRetornoPacto, cobrancaException.getCodigoRetornoPactoEnum().getCodigo());
                transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.msgErro, cobrancaException.getMensagemComplementar());

                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NENHUMA)) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
                }

                transacaoDAO.alterar(transacaoVO);
            } catch (Exception ex) {
                Uteis.logar(ex, CobrancaOnlineService.class);
            } finally {
                transacaoDAO = null;
            }
        } else {
            cobrancaException.printStackTrace();
            Uteis.logarDebug("Transação null " + cobrancaException.getMensagemComplementar());
        }
    }

    public void gravarOutrasInformacoes(String numeroCartaoPassando, CartaoCreditoTO dadosCartaoTO, TransacaoVO transacaoVO) {
        try {
            transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado, APF.getCartaoMascarado(numeroCartaoPassando));
            transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoTitular, dadosCartaoTO.getNomeTitular());
            if (dadosCartaoTO.getBand() != null) {
                transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira, dadosCartaoTO.getBand().getDescricao());
            }

            String cvv = "";
            if (!UteisValidacao.emptyString(dadosCartaoTO.getCodigoSeguranca())) {
                try {
                    cvv = dadosCartaoTO.getCodigoSeguranca().replaceAll(".", "*");
                } catch (Exception ex) {
                    cvv = "***";
                }
            }
            transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoCvv, cvv);

            if (dadosCartaoTO.getParcelas() != null) {
                transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas, dadosCartaoTO.getParcelas().toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void gerarLogErroTentativaCobranca(CartaoCreditoTO dadosCartaoTO, TransacaoVO transacaoVO, final String msgException) {
        Transacao transacaoDAO = null;
        try {
            Integer codPessoa = dadosCartaoTO.getIdPessoaCartao();
            if (transacaoVO != null && UteisValidacao.emptyNumber(codPessoa)) {
                codPessoa = transacaoVO.getPessoaPagador().getCodigo();
            }

            if (UteisValidacao.emptyNumber(codPessoa)) {
                throw new Exception("Pessoa não informada... " + codPessoa);
            }

            transacaoDAO = new Transacao(getCon());
            transacaoDAO.gerarLogErroTentativaCobranca(codPessoa, msgException);
        } catch (Exception ex) {
            Uteis.logar(ex, CobrancaOnlineService.class);
        } finally {
            transacaoDAO = null;
        }
    }

    public void validarDadosTransacao(TransacaoVO transacaoVO, CartaoCreditoTO cartaoTO) throws Exception {

        //se tiver o ambiente vazio preencher com a do convenio de cobrança
        if (transacaoVO.getAmbiente().equals(AmbienteEnum.NENHUM)) {
            transacaoVO.setAmbiente(transacaoVO.getConvenioCobrancaVO().getAmbiente());
        }

        //validar o ambiente no convenio de cobrança
        if (transacaoVO.getAmbiente().equals(AmbienteEnum.NENHUM)) {
            throw new Exception("Ambiente convênio de cobrança não informado.");
        }

        //validar validade do cartão
        if (!UteisValidacao.emptyNumber(cartaoTO.getMesValidade()) &&
                !UteisValidacao.emptyNumber(cartaoTO.getAnoValidade())) {
            try {
                UteisValidacao.validarVencimentoCartao(cartaoTO.getValidadeMMYYYY(true));
            } catch (Exception ex) {
                throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_VENCIDO, ex.getMessage());
            }
        }

        //caso seja de verificacao não precisa realizar validações
        if (cartaoTO.isTransacaoVerificarCartao()) {
            return;
        }

        CartaoTentativaVO ultimaTentativaCartao = obterUltimaTentativa(transacaoVO, false, cartaoTO);
        CartaoTentativaVO ultimaTentativaCartaoSemPAC = obterUltimaTentativa(transacaoVO, true, cartaoTO);
        if (ultimaTentativaCartao != null) {

            //verificar se houve outra tentativa em menos de 5 horas
            if (!cartaoTO.isTransacaoPresencial() && cartaoTO.isSomenteUmEnvioCartaoTentativa() &&
                    cartaoTO.getTipoTransacaoEnum().equals(ultimaTentativaCartao.getTipoConvenioCobranca().getTipoTransacao())) {
                if (ultimaTentativaCartaoSemPAC != null) {
                    //se for menor que 5 horas desde a última tentativa, não tentar
                    Integer difHoras = Calendario.diferencaEmHoras(ultimaTentativaCartaoSemPAC.getData(), Calendario.hoje()).intValue();
                    if (difHoras < 5) {
                        Uteis.logarDebug(CodigoRetornoPactoEnum.BLOQUEIO_AGUARDAR.getDescricao());
                        throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_AGUARDAR);
                    }
                }
            }

            String descricaoErroComplementada = null;
            ConvenioCobranca convenioCobrancaDAO;
            try {
                if (ultimaTentativaCartaoSemPAC != null) {
                    TipoCredencialStoneEnum tipoCredencialStoneEnum = null;
                    OperadorasExternasAprovaFacilEnum bandeira = null;
                    if (ultimaTentativaCartaoSemPAC != null && ultimaTentativaCartaoSemPAC.getTipoConvenioCobranca().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) {
                        convenioCobrancaDAO = new ConvenioCobranca(getCon());
                        tipoCredencialStoneEnum = convenioCobrancaDAO.obterTipoCredenciamentoStoneByCodigoConvenio(ultimaTentativaCartaoSemPAC.getConvenioCobrancaVO().getCodigo());
                        bandeira = transacaoVO.getBandeiraPagamento() != null ? transacaoVO.getBandeiraPagamento() : transacaoVO.getBandeiraEnum();
                    }
                    String msgAdqui = CartaoTentativaVO.obterDescricaoRetornoAdquirente(
                            ultimaTentativaCartaoSemPAC.getTipoConvenioCobranca(), ultimaTentativaCartaoSemPAC.getCodigoRetorno(), tipoCredencialStoneEnum, bandeira);
                    descricaoErroComplementada = (ultimaTentativaCartaoSemPAC.getCodigoRetorno() + " - " + msgAdqui);
                }
            } catch (Exception ignored) {
            } finally {
                convenioCobrancaDAO = null;
            }

            if (!cartaoTO.isTransacaoPresencial() &&
                    (ultimaTentativaCartao.getCodigoRetornoPacto().equals(CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA) ||
                            ultimaTentativaCartao.getCodigoRetornoPacto().equals(CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE) ||
                            ultimaTentativaCartao.getCodigoRetornoPacto().equals(CodigoRetornoPactoEnum.CARTAO_CANCELADO))) {
                Uteis.logar(null, "Bloqueio devido a ultima tentativa... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                if (!UteisValidacao.emptyString(descricaoErroComplementada)) {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), descricaoErroComplementada);
                } else {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
                }
            }

            //se a ultima tentativa não tinha saldo...
            //tentar novamente só no outro dia..
            //Não validar STONE v1 nem v5 aqui
            if (transacaoVO.getTipo() != null && !transacaoVO.getTipo().equals(TipoTransacaoEnum.STONE_ONLINE)
                    && !transacaoVO.getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5)
                    && !cartaoTO.isTransacaoPresencial() &&
                    ultimaTentativaCartao.getCodigoRetornoPacto().equals(CodigoRetornoPactoEnum.SALDO_INSUFICIENTE) &&
                    Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) {
                Uteis.logar(null, "Bloqueio devido a última tentativa (Sem saldo)... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                if (!UteisValidacao.emptyString(descricaoErroComplementada)) {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), descricaoErroComplementada);
                } else {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
                }
            }

            if (transacaoVO.getTipo() != null && !cartaoTO.isTransacaoPresencial() &&
                    (transacaoVO.getTipo().equals(TipoTransacaoEnum.FACILITEPAY) || transacaoVO.getTipo().equals(TipoTransacaoEnum.PAGOLIVRE)) &&
                    ultimaTentativaCartao.getCodigoRetorno().equals(PagoLivreRetornoEnum.RetornoLR_51.getCodigo())) {

                Integer diasUltima = Calendario.diferencaEmDias(ultimaTentativaCartao.getData(), Calendario.hoje());
                if (diasUltima <= 3) {
                    if (diasUltima == 0) {
                        diasUltima = 1;
                    }
                    Integer diasTentarNovamente = (4 - diasUltima);
                    Uteis.logar(null, "Bloqueio devido a última tentativa (Sem saldo)... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                    throw new CobrancaException(CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, "Tentar novamente em " + diasTentarNovamente + " dia(s).");
                }
            }

            //VALIDAÇÕES DE LIMITE DE RETENTATIVAS DA STONE V1 E V5
            if (transacaoVO.getTipo() != null) {
                if (transacaoVO.getTipo().equals(TipoTransacaoEnum.STONE_ONLINE) && !cartaoTO.isTransacaoPresencial()) {
                    validarRegrasLimiteRetentativaStoneV1(transacaoVO, ultimaTentativaCartao, ultimaTentativaCartaoSemPAC, descricaoErroComplementada);
                } else if (transacaoVO.getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5)
                        && transacaoVO.getConvenioCobrancaVO() != null
                        && transacaoVO.getConvenioCobrancaVO().getTipoCredencialStoneEnum() != null) {
                    //VALIDAÇÕES RETENTATIVAS DA (stone v5) PAGARME V5
                    if (transacaoVO.getConvenioCobrancaVO().getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.PSP)) {
                        validarSeUltimaTentativaEIrreversivelStoneV5PSP(ultimaTentativaCartaoSemPAC, transacaoVO);
                    } else if (transacaoVO.getConvenioCobrancaVO().getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.GATEWAY)) {
                        validarSeUltimaTentativaEIrreversivelStoneV5Gateway(ultimaTentativaCartaoSemPAC, transacaoVO);
                    }
                    validarRegrasLimiteRetentativaPorBandeira(cartaoTO, transacaoVO);
                }
            }
        }
    }

    private void validarRegrasLimiteRetentativaPorBandeira(CartaoCreditoTO cartaoTO, TransacaoVO transacaoVO) throws CobrancaException {
        ValidaBandeira.Bandeira bandeira = buscaBandeiraCartao(cartaoTO.getNumero());
        if (bandeira == null) {
            return;
        }
        if (bandeira.equals(ValidaBandeira.Bandeira.MASTERCARD)) {
            validarRegrasLimiteRetentativaMASTERCARD(cartaoTO);
        } else if (bandeira.equals(ValidaBandeira.Bandeira.VISA)) {
            validarRegrasLimiteRetentativaVISA(cartaoTO, transacaoVO);
        } else if (bandeira.equals(ValidaBandeira.Bandeira.ELO)) {
            validarRegrasLimiteRetentativaELO(cartaoTO, transacaoVO);
        } else if (bandeira.equals(ValidaBandeira.Bandeira.HIPERCARD)) {
            validarRegrasLimiteRetentativaHIPERCARD(cartaoTO, transacaoVO);
        }
    }

    private void validarSeUltimaTentativaEIrreversivelStoneV5PSP(CartaoTentativaVO ultimaTentativaCartaoSemPAC, TransacaoVO transacaoVO) throws CobrancaException {
        Transacao transacaoDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            transacaoDAO = new Transacao(getCon());
            convenioCobrancaDAO = new ConvenioCobranca(getCon());

            //ESSE RETORNO DE 1016 SALDO INSUFICIENTE É REVERSÍVEL
            if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1016.getId()) &&
                    Calendario.igual(ultimaTentativaCartaoSemPAC.getData(), Calendario.hoje())) {
                Uteis.logar(null, "Bloqueio devido a última tentativa (Sem saldo)... " + ultimaTentativaCartaoSemPAC.getCodigo());
                throw new CobrancaException(CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, " Já houve uma tentativa de saldo insuficiente hoje, então só será possível Tentar novamente no dia seguinte.");
            }

            //(MODELO PSP) RETORNOS IRREVERSÍVEIS RELACIONADOS A ULTIMA TENTATIVA SEM PAC COM O MESMO NÚMERO DO CARTÃO
            if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1004.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1011.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1015.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1019.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1020.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1022.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1024.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1028.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1029.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1030.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1032.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1035.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1036.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1037.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1039.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1041.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1042.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1047.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1061.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1062.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status2004.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status2008.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status2009.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status2010.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status9102.getId())
            ) {
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
            }

            //RETORNOS IRREVERSÍVEIS RELACIONADOS AO ESTABLECIMENTO
            if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1005.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1009.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status1060.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status2005.getId()) ||
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneOnlineV5RetornoPSPEnum.Status9350.getId())) {
                ConvenioCobrancaVO convenioUltTentativa = convenioCobrancaDAO.consultarPorChavePrimaria(ultimaTentativaCartaoSemPAC.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CREDENCIAIS_CONVENIO);
                ConvenioCobrancaVO convenioAgora = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CREDENCIAIS_CONVENIO);

                if (convenioUltTentativa != null && convenioAgora != null) {
                    if (convenioUltTentativa.isUsaSplitPagamentoStoneV5()) {
                        if (!convenioUltTentativa.getCodigoAutenticacao04().equals(convenioAgora.getCodigoAutenticacao04())) {
                            throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
                        }
                    } else {
                        if (!convenioUltTentativa.getCodigoAutenticacao03().equals(convenioAgora.getCodigoAutenticacao03())) {
                            throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
                        }
                    }
                }
            }

            if (ultimaTentativaCartaoSemPAC != null && !UteisValidacao.emptyNumber(ultimaTentativaCartaoSemPAC.getTransacao())) {
                String paramsRespostaUltTransacaoCartao = transacaoDAO.consultarParamsRespostaPorChavePrimaria(ultimaTentativaCartaoSemPAC.getTransacao());
                if (!UteisValidacao.emptyString(paramsRespostaUltTransacaoCartao)) {
                    if (paramsRespostaUltTransacaoCartao.toUpperCase().contains("NÃO TENTE NOVAMENTE") ||
                            paramsRespostaUltTransacaoCartao.toUpperCase().contains("NAO TENTE NOVAMENTE") ||
                            paramsRespostaUltTransacaoCartao.toUpperCase().contains("NÃO TENTAR NOVAMENTE") ||
                            paramsRespostaUltTransacaoCartao.toUpperCase().contains("NAO TENTAR NOVAMENTE") ||
                            paramsRespostaUltTransacaoCartao.toUpperCase().contains("NÃO RE-TENTAR") ||
                            paramsRespostaUltTransacaoCartao.toUpperCase().contains("NAO RE-TENTAR") ||
                                    paramsRespostaUltTransacaoCartao.toUpperCase().contains("NÃO RETENTAR") ||
                                    paramsRespostaUltTransacaoCartao.toUpperCase().contains("NAO RETENTAR")) {
                        Uteis.logar(null, "Bloqueio devido última tentativa do cartão ter sido irreversível."
                                + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL.getDescricao());
                        throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
                    }
                }
            }
        } catch (CobrancaException cob) {
            throw cob;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            transacaoDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    private void validarSeUltimaTentativaEIrreversivelStoneV5Gateway(CartaoTentativaVO ultimaTentativaCartaoSemPAC, TransacaoVO transacaoVO) throws CobrancaException {
        ConvenioCobranca convenioCobrancaDAO;
        Transacao transacaoDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(getCon());
            transacaoDAO = new Transacao(getCon());
            OperadorasExternasAprovaFacilEnum bandeira = transacaoVO.getBandeiraPagamento() != null ? transacaoVO.getBandeiraPagamento() : transacaoVO.getBandeiraEnum();
            RetornoAbecsBandeirasEnum retornoUltTentativa = RetornoAbecsBandeirasEnum.obterPorCodigoEBandeira(
                    ultimaTentativaCartaoSemPAC.getCodigoRetorno(), bandeira);

            //NÃO ENCONTROU O RETORNO DA ÚLTIMA TENTATIVA
            if (retornoUltTentativa == null) {
                return;
            }

            boolean retornoPorFaltaDeLimite = retornoUltTentativa.isRetornoPorFaltaDeLimite(retornoUltTentativa);
            if (retornoPorFaltaDeLimite && Calendario.igual(ultimaTentativaCartaoSemPAC.getData(), Calendario.hoje())) {
                Uteis.logar(null, "Bloqueio devido a última tentativa (Sem saldo)... " + ultimaTentativaCartaoSemPAC.getCodigo());
                throw new CobrancaException(CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, " Já houve uma tentativa de saldo insuficiente hoje, então só será possível Tentar novamente no dia seguinte.");
            }

            //É REVERSÍVEL
            //TODO Não alterar a ordem desse if
            if (retornoUltTentativa.isReversivel()) {
                return;
            }

            //RETORNOS IRREVERSÍVEIS RELACIONADOS AO ESTABLECIMENTO QUE PODEM SER REVERSÍVEIS SE TROCAR A CREDENCIAL
            boolean retornoPorErroEstabelecimentoOuCredencialPodeRetentarSeTiverAlteradoACredencialDoConvenio = retornoUltTentativa.isRetornoPorErroEstabelecimentoOuCredencial(retornoUltTentativa);
            if (retornoPorErroEstabelecimentoOuCredencialPodeRetentarSeTiverAlteradoACredencialDoConvenio) {
                ConvenioCobrancaVO convenioUltTentativa = convenioCobrancaDAO.consultarPorChavePrimaria(ultimaTentativaCartaoSemPAC.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CREDENCIAIS_CONVENIO);
                ConvenioCobrancaVO convenioAgora = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CREDENCIAIS_CONVENIO);

                //verificar se foi alterado as credenciais do convênio desde a última tentativa
                if (convenioUltTentativa != null && convenioAgora != null) {
                    if (convenioUltTentativa.isUsaSplitPagamentoStoneV5()) {
                        if (!convenioUltTentativa.getCodigoAutenticacao04().equals(convenioAgora.getCodigoAutenticacao04())) {
                            throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
                        }
                    } else {
                        if (!convenioUltTentativa.getCodigoAutenticacao03().equals(convenioAgora.getCodigoAutenticacao03())) {
                            throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
                        }
                    }
                }
            }


            boolean retornoPorCartaoVencido = retornoUltTentativa.isRetornoPorCartaoVencido(retornoUltTentativa);
            if (retornoPorCartaoVencido && !UteisValidacao.emptyNumber(ultimaTentativaCartaoSemPAC.getTransacao())) {
                TransacaoVO transacaoVOAnteriorCompleta = transacaoDAO.consultarPorChavePrimaria(ultimaTentativaCartaoSemPAC.getTransacao(), Uteis.NIVELMONTARDADOS_DADOSMINIMOS_TRANSACAO);
                String validadeCartaoTransacaoAnterior = "";
                String validadeCartaoTransacaoAtual = "";

                //transação anterior
                if (transacaoVOAnteriorCompleta != null && !UteisValidacao.emptyString(transacaoVOAnteriorCompleta.getParamsEnvio())) {
                    try {
                        JSONObject obj = (JSONObject) new JSONObject(transacaoVOAnteriorCompleta.getParamsEnvio()).getJSONArray("payments").get(0);
                        JSONObject card = obj.getJSONObject("credit_card").getJSONObject("card");
                        validadeCartaoTransacaoAnterior = card.getString("exp_month") + "/" + card.getString("exp_year");
                    } catch (Exception ex) {
                        validadeCartaoTransacaoAnterior = "";
                    }
                }

                //transação atual
                if (transacaoVO != null && !UteisValidacao.emptyString(transacaoVO.getParamsEnvio())) {
                    try {
                        JSONObject obj = (JSONObject) new JSONObject(transacaoVO.getParamsEnvio()).getJSONArray("payments").get(0);
                        JSONObject card = obj.getJSONObject("credit_card").getJSONObject("card");
                        validadeCartaoTransacaoAtual = card.getString("exp_month") + "/" + card.getString("exp_year");
                    } catch (Exception ex) {
                        validadeCartaoTransacaoAtual = "";
                    }
                }

                if (!UteisValidacao.emptyString(validadeCartaoTransacaoAnterior)
                        && !UteisValidacao.emptyString(validadeCartaoTransacaoAtual)
                        && validadeCartaoTransacaoAnterior.equals(validadeCartaoTransacaoAtual)) { //se o vencimento ainda é o mesmo, então bloquear
                    Uteis.logar(null, "Bloqueio devido a última tentativa ser de cartão vencido e não alterou o vencimento do cartão... " + ultimaTentativaCartaoSemPAC.getCodigo());
                    throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
                }
            }

            //CHEGOU ATÉ AQUI, É PORQUE É UMA IRREVERSÍVEL QUE NÃO PODE RETENTAR MAIS NO MESMO CARTÃO...
            // retornoPorCartaoVencido valida um pouco mais acima
            if (!retornoUltTentativa.isReversivel() && !retornoPorCartaoVencido) {
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_ULT_TENTATIVA_E_IRREVERSIVEL);
            }

        } catch (CobrancaException cob) {
            throw cob;
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            convenioCobrancaDAO = null;
            transacaoDAO = null;
        }
    }

    private void validarRegrasLimiteRetentativaMASTERCARD(CartaoCreditoTO cartaoTO) throws CobrancaException {
        // ##REGRAS DE RETENTATIVA PARA MASTERCARD##
        // --> 7 tentativas negadas em 24 horas no mesmo cartão
        // --> 35 tentativas negadas em 30 dias no mesmo cartão

        CartaoTentativa cartaoTentativaDAO;
        String cartaoExibirLog = APF.getCartaoMascarado(cartaoTO.getNumero());
        try {
            cartaoTentativaDAO = new CartaoTentativa(getCon());

            //Buscar informações para validação de retentativa
            TotalizadorRetentativaTO totalizadorRetentativaTO = cartaoTentativaDAO.buscarTotalizadoresUlt24HorasEUlt30Dias(cartaoTO);

            if (totalizadorRetentativaTO.getTotalTentativasUlt24HorasMesmoCartao() >= RegrasRetentativaPorBandeiraEnum.MASTERCARD.getQtdTentativaLimite24Horas()) {
                Uteis.logar(null, "Bloqueio devido exceder limite de tentativas do cartão na bandeira Mastercard nas últimas 24 horas... (" + cartaoExibirLog + ") - "
                        + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_24_HORAS.getDescricao());
                String msgRetorno = "A bandeira MASTERCARD só permite no máx. <b>" + RegrasRetentativaPorBandeiraEnum.MASTERCARD.getQtdTentativaLimite24Horas()
                        + "</b> tentativas negadas no mesmo cartão no período de 24 horas. Tente novamente depois.";
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_24_HORAS, msgRetorno);
            } else if (totalizadorRetentativaTO.getTotalTentativasUlt30DiasMesmoCartao() >= RegrasRetentativaPorBandeiraEnum.MASTERCARD.getQtdTentativaLimite30Dias()) {
                Uteis.logar(null, "Bloqueio devido exceder limite de tentativas do cartão na bandeira Mastercard nos últimos 30 dias... (" + cartaoExibirLog + ") - "
                        + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_30_DIAS.getDescricao());
                String msgRetorno = "A bandeira MASTERCARD só permite no máx. <b>" + RegrasRetentativaPorBandeiraEnum.MASTERCARD.getQtdTentativaLimite30Dias()
                        + "</b> tentativas negadas no mesmo cartão no período de 30 dias. Tente novamente depois.";
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_30_DIAS, msgRetorno);
            }
        } catch (CobrancaException ex) {
            throw ex;
        } catch (Exception ex) {
        } finally {
            cartaoTentativaDAO = null;
        }
    }

    private void validarRegrasLimiteRetentativaVISA(CartaoCreditoTO cartaoTO, TransacaoVO transacaoVO) throws CobrancaException {
        // ##REGRAS DE RETENTATIVA PARA VISA##
        // --> 15 tentativas negadas em 30 dias no mesmo cartão e do mesmo valor

        CartaoTentativa cartaoTentativaDAO;
        try {
            cartaoTentativaDAO = new CartaoTentativa(getCon());

            //Buscar informações para validação de retentativa
            TotalizadorRetentativaTO totalizadorRetentativaTO = cartaoTentativaDAO.buscarTotalizadoresUlt24HorasEUlt30DiasEMesmoValor(cartaoTO, transacaoVO.getValor());

            if (totalizadorRetentativaTO.getTotalTentativasUlt30DiasMesmoCartao() >= RegrasRetentativaPorBandeiraEnum.VISA.getQtdTentativaLimite30Dias()) {
                Uteis.logar(null, "Bloqueio devido exceder limite de tentativas do cartão na bandeira VISA nos últimas 30 dias... (" + APF.getCartaoMascarado(cartaoTO.getNumero()) + ") - "
                        + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_30_DIAS.getDescricao());
                String msgRetorno = "A bandeira VISA só permite no máx. <b>" + RegrasRetentativaPorBandeiraEnum.VISA.getQtdTentativaLimite30Dias()
                        + "</b> tentativas negadas no mesmo cartão e no mesmo valor no período de 30 dias. Tente novamente depois.";
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_30_DIAS, msgRetorno);
            }
        } catch (CobrancaException ex) {
            throw ex;
        } catch (Exception ex) {
        } finally {
            cartaoTentativaDAO = null;
        }
    }

    private void validarRegrasLimiteRetentativaELO(CartaoCreditoTO cartaoTO, TransacaoVO transacaoVO) throws CobrancaException {
        // ##REGRAS DE RETENTATIVA PARA ELO##
        // --> 15 tentativas negadas no mÊs vigente no mesmo cartão e do mesmo valor

        CartaoTentativa cartaoTentativaDAO;
        try {
            cartaoTentativaDAO = new CartaoTentativa(getCon());

            //Buscar informações para validação de retentativa
            TotalizadorRetentativaTO totalizadorRetentativaTO = cartaoTentativaDAO.buscarTotalizadoresMesVigenteEMesmoValor(cartaoTO, transacaoVO.getValor());

            if (totalizadorRetentativaTO.getTotalTentativasMesVigenteMesmoCartao() >= RegrasRetentativaPorBandeiraEnum.ELO.getQtdTentativaLimiteMesVigente()) {
                Uteis.logar(null, "Bloqueio devido exceder limite de tentativas do cartão na bandeira ELO no mês atual... (" + APF.getCartaoMascarado(cartaoTO.getNumero()) + ") - "
                        + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_MES_VIGENTE.getDescricao());
                String msgRetorno = "A bandeira ELO só permite no máx. <b>" + RegrasRetentativaPorBandeiraEnum.VISA.getQtdTentativaLimiteMesVigente()
                        + "</b> tentativas negadas no mesmo cartão e no mesmo valor no mês vigente. Tente novamente depois.";
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_MES_VIGENTE, msgRetorno);
            }
        } catch (CobrancaException ex) {
            throw ex;
        } catch (Exception ex) {
        } finally {
            cartaoTentativaDAO = null;
        }
    }

    private void validarRegrasLimiteRetentativaHIPERCARD(CartaoCreditoTO cartaoTO, TransacaoVO transacaoVO) throws CobrancaException {
        // ##REGRAS DE RETENTATIVA PARA HIPERCARD##
        // --> 8 tentativas negadas em 24 horas no mesmo cartão e mesmo valor
        // --> 8 tentativas negadas em 30 dias no mesmo cartão e mesmo valor

        CartaoTentativa cartaoTentativaDAO;
        String cartaoExibirLog = APF.getCartaoMascarado(cartaoTO.getNumero());
        try {
            cartaoTentativaDAO = new CartaoTentativa(getCon());

            //Buscar informações para validação de retentativa
            TotalizadorRetentativaTO totalizadorRetentativaTO = cartaoTentativaDAO.buscarTotalizadoresUlt24HorasEUlt30DiasEMesmoValor(cartaoTO, transacaoVO.getValor());

            if (totalizadorRetentativaTO.getTotalTentativasUlt24HorasMesmoCartao() >= RegrasRetentativaPorBandeiraEnum.HIPERCARD.getQtdTentativaLimite24Horas()) {
                Uteis.logar(null, "Bloqueio devido exceder limite de tentativas do cartão na bandeira HIPERCARD nas últimas 24 horas... (" + cartaoExibirLog + ") - "
                        + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_24_HORAS.getDescricao());
                String msgRetorno = "A bandeira HIPERCARD só permite no máx. <b>" + RegrasRetentativaPorBandeiraEnum.HIPERCARD.getQtdTentativaLimite24Horas()
                        + "</b> tentativas negadas no mesmo cartão no período de 24 horas. Tente novamente depois.";
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_24_HORAS, msgRetorno);
            } else if (totalizadorRetentativaTO.getTotalTentativasUlt30DiasMesmoCartao() >= RegrasRetentativaPorBandeiraEnum.HIPERCARD.getQtdTentativaLimite30Dias()) {
                Uteis.logar(null, "Bloqueio devido exceder limite de tentativas do cartão na bandeira HIPERCARD nos últimos 30 dias... (" + cartaoExibirLog + ") - "
                        + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_30_DIAS.getDescricao());
                String msgRetorno = "A bandeira HIPERCARD só permite no máx. <b>" + RegrasRetentativaPorBandeiraEnum.HIPERCARD.getQtdTentativaLimite30Dias()
                        + "</b> tentativas negadas no mesmo cartão no período de 30 dias. Tente novamente depois.";
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA_ULT_30_DIAS, msgRetorno);
            }
        } catch (CobrancaException ex) {
            throw ex;
        } catch (Exception ex) {
        } finally {
            cartaoTentativaDAO = null;
        }
    }

    private void validarRegrasLimiteRetentativaStoneV1(TransacaoVO transacaoVO, CartaoTentativaVO ultimaTentativaCartao, CartaoTentativaVO ultimaTentativaCartaoSemPAC, String erroDeOrigem) throws CobrancaException {

        boolean cobrancaAutomaticaSistema = (transacaoVO.getOrigem().equals(OrigemCobrancaEnum.ZW_AUTOMATICO) ||
                transacaoVO.getOrigem().equals(OrigemCobrancaEnum.ZW_AUTOMATICO_RETENTATIVA) ||
                transacaoVO.getOrigem().equals(OrigemCobrancaEnum.ZW_AUTOMATICO_RENOVA_FACIL_CIELO) ||
                transacaoVO.getOrigem().equals(OrigemCobrancaEnum.ZW_AUTOMATICO_RENOVA_FACIL_FACILITE_PAY));

        //######REGRAS ESPECÍFICAS PELO CÓD. DE RETORNO PAC#####
        if (ultimaTentativaCartao.getCodigoRetornoPacto().equals(CodigoRetornoPactoEnum.SALDO_INSUFICIENTE)) {
            if (Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) { //se já tiver uma no mesmo dia, não retentar na adquirente
                Uteis.logar(null, "Bloqueio devido a última tentativa (Sem saldo)... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                if (!UteisValidacao.emptyString(erroDeOrigem)) {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), erroDeOrigem);
                } else {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
                }
            } else {
                //######VALIDAR LIMITE DE RETENTATIVAS PARA O CARTÃO PELA VARIÁVEL QUE VEM NA RESPOSTA DA ÚLT. TRANSAÇÃO#####
                validarRegrasLimiteRetentativaPorBandeira(transacaoVO, ultimaTentativaCartao, ultimaTentativaCartaoSemPAC);
                return;
            }
        }

        //######REGRAS ESPECÍFICAS PELO CÓD. DE RETORNO#####

        //Bad Debt - Estabelecimento Entrar em contato com a Stone
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1040.getId()) &&
                !cobrancaAutomaticaSistema) {

            String stoneCodeAnterior = "";
            Transacao transacaoDAO;
            boolean teveCobrancaAprovadaUltimos2Dias = false;
            try {
                transacaoDAO = new Transacao(getCon());
                TransacaoVO transacaoVOAnterior = transacaoDAO.consultarPorChavePrimaria(ultimaTentativaCartaoSemPAC.getTransacao(), Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
                stoneCodeAnterior = XML.toJSONObject(transacaoVOAnterior.getParamsEnvio()).optJSONObject("Document").optJSONObject("AccptrAuthstnReq").
                        optJSONObject("AuthstnReq").optJSONObject("Envt").optJSONObject("POI").optJSONObject("Id").optString("Id");
                teveCobrancaAprovadaUltimos2Dias = transacaoDAO.possuiTransacaoAprovadaCodigoAutenticacao01NosUltimos2Dias(stoneCodeAnterior);
            } catch (Exception ex) {
            } finally {
                transacaoDAO = null;
            }

            if (!teveCobrancaAprovadaUltimos2Dias) {
                Uteis.logar(null, "Bloqueio devido a ultima tentativa ser Bad Debit - Cliente deve Entrar em contato com a Stone ... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                if (!UteisValidacao.emptyString(erroDeOrigem)) {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), erroDeOrigem);
                } else {
                    throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
                }
            }
        }

        //Contate a central do seu cartão
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1828.getId()) &&
                Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) {

            Uteis.logar(null, "Bloqueio devido a ultima tentativa ser Contate a central do seu cartão... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
            if (!UteisValidacao.emptyString(erroDeOrigem)) {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), erroDeOrigem);
            } else {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
            }
        }

        //Verifique os dados do cartão
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1011.getId())) {
            if (ultimaTentativaCartaoSemPAC.getCartao().equals(transacaoVO.getCartaoMascarado()) && !teveCobrancaAprovadaRecente(transacaoVO) && !teveVerificacaoAprovadaRecente(transacaoVO)) {
                Uteis.logar(null, "Bloqueio devido dados inválidos do cartão... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                if (!UteisValidacao.emptyString(erroDeOrigem)) {
                    throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_INVALIDO, erroDeOrigem);
                } else {
                    throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_INVALIDO);
                }
            }
        }

        //Contate a central do seu cartão
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status3002.getId()) &&
                Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) {
            Uteis.logar(null, "Bloqueio devido Contate a central do seu cartão - não tente novamente... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
            if (!UteisValidacao.emptyString(erroDeOrigem)) {
                throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_INVALIDO, erroDeOrigem);
            } else {
                throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_INVALIDO);
            }
        }

        //Cartão não permite transações internacionais
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1813.getId()) &&
                Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) {

            Uteis.logar(null, "Bloqueio devido cartão não permitir transações internacionais... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
            if (!UteisValidacao.emptyString(erroDeOrigem)) {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), erroDeOrigem);
            } else {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
            }
        }

        //Transação não permitida para o cartão
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1019.getId()) &&
                Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) {

            Uteis.logar(null, "Bloqueio devido Transação não permitida para o cartão... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
            if (!UteisValidacao.emptyString(erroDeOrigem)) {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), erroDeOrigem);
            } else {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
            }
        }

        //Tente novamente mais tarde
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1841.getId()) &&
                Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) {

            Uteis.logar(null, "Bloqueio devido Tente novamente mais tarde no cartão... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
            if (!UteisValidacao.emptyString(erroDeOrigem)) {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto(), erroDeOrigem);
            } else {
                throw new CobrancaException(ultimaTentativaCartao.getCodigoRetornoPacto());
            }
        }

        //Conta encerrada
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1035.getId())) {

            if (ultimaTentativaCartaoSemPAC.getCartao().equals(transacaoVO.getCartaoMascarado())) {
                Uteis.logar(null, "Bloqueio devido conta encerrada... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                if (!UteisValidacao.emptyString(erroDeOrigem)) {
                    throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA, erroDeOrigem);
                } else {
                    throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA);
                }
            }
        }

        //transação não permitida
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1020.getId()) &&
                Calendario.igual(ultimaTentativaCartao.getData(), Calendario.hoje())) {
            Uteis.logar(null, "Bloqueio devido transação não permitida... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
            if (!UteisValidacao.emptyString(erroDeOrigem)) {
                throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA, erroDeOrigem);
            } else {
                throw new CobrancaException(CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA);
            }
        }

        //Estabelecimento Inválido Stone
        if (ultimaTentativaCartaoSemPAC.getCodigoRetorno().equals(StoneRetornoEnum.Status1009.getId())) {
            String stoneCodeAnterior = "";
            Transacao transacaoDAO;
            boolean teveCobrancaAprovadaUltimos2Dias = false;
            try {
                transacaoDAO = new Transacao(getCon());
                TransacaoVO transacaoVOAnterior = transacaoDAO.consultarPorChavePrimaria(ultimaTentativaCartaoSemPAC.getTransacao(), Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
                stoneCodeAnterior = XML.toJSONObject(transacaoVOAnterior.getParamsEnvio()).optJSONObject("Document").optJSONObject("AccptrAuthstnReq").
                        optJSONObject("AuthstnReq").optJSONObject("Envt").optJSONObject("POI").optJSONObject("Id").optString("Id");
                teveCobrancaAprovadaUltimos2Dias = transacaoDAO.possuiTransacaoAprovadaCodigoAutenticacao01NosUltimos2Dias(stoneCodeAnterior);
            } catch (Exception ex) {
            } finally {
                transacaoDAO = null;
            }

            //se tiver alguma aprovada no StoneCode nos últimos 2 dias, então deixar enviar mesmo assim, ignorando a regra.
            //Isso porque na Stone já aconteceu deles devolverem o 1019 para algumas transações, porém aprovaram outras e então
            // o aluno que tinha a 1019 a primeira vez nunca mais enviava cobrança
            if (!teveCobrancaAprovadaUltimos2Dias && transacaoVO.getConvenioCobrancaVO().getCodigoAutenticacao01().equals(stoneCodeAnterior)) {
                Uteis.logar(null, "Bloqueio devido a última tentativa ser de Estabelecimento inválido, não tem nenhuma aprovada neste convênio para nenhum aluno nos últimos 3 dias e cliente ainda não alterou stone code... " + ultimaTentativaCartao.getCodigo() + " - " + ultimaTentativaCartao.getCodigoRetornoPacto().getDescricao());
                throw new CobrancaException(ultimaTentativaCartaoSemPAC.getCodigoRetornoPacto(), erroDeOrigem);
            }
        }

        //SE NÃO CAIU EM NENHUMA DAS REGRAS ANTERIORES, ENTÃO AGORA VALIDAR LIMITE DE RETENTATIVAS PARA O CARTÃO PELA VARIÁVEL QUE VEM NA RESPOSTA DA ÚLT. TRANSAÇÃO#####
        validarRegrasLimiteRetentativaPorBandeira(transacaoVO, ultimaTentativaCartao, ultimaTentativaCartaoSemPAC);

    }

    private boolean teveCobrancaAprovadaRecente(TransacaoVO transacaoVO) {
        // A Stone as vezes retorna um cartão como negado cartão inválido e outras vezes o mesmo cartão é aprovado sem nenhuma alteração.
        // Criado essa validação, para ter um caso semelhante a esse, que o sistema faça a tentativa de cobrança
        boolean teveCobrancaAprovadaRecente = false;
        CartaoTentativa cartaoTentativaDAO = null;
        try {
            cartaoTentativaDAO = new CartaoTentativa(getCon());
            teveCobrancaAprovadaRecente = cartaoTentativaDAO.existeTentativaAprovada(transacaoVO.getCartaoMascarado());
        } catch (Exception ex) {
            Uteis.logar(ex, CobrancaOnlineService.class);
        } finally {
            cartaoTentativaDAO = null;
        }
        return teveCobrancaAprovadaRecente;
    }

    private boolean teveVerificacaoAprovadaRecente(TransacaoVO transacaoVO) {
        // A Stone as vezes retorna um cartão como negado cartão inválido, onde o aluno pode tentar resolver com o banco e retirar o bloqueio.
        // Criado essa validação, para se o cartão for incluindo novamente e passou na tentativa de verificação, que a próxima de cobrança aconteça, na esperança de que o aluno conseguiu resolver o problema com o banco.
        boolean teveVerificacaoAprovadaRecente = false;
        CartaoTentativa cartaoTentativaDAO = null;
        try {
            cartaoTentativaDAO = new CartaoTentativa(getCon());
            teveVerificacaoAprovadaRecente = cartaoTentativaDAO.existeVerificacaoPosteriorUltimaTentaviva(transacaoVO.getCartaoMascarado());
        } catch (Exception ex) {
            Uteis.logar(ex, CobrancaOnlineService.class);
        } finally {
            cartaoTentativaDAO = null;
        }
        return teveVerificacaoAprovadaRecente;
    }

    private void validarRegrasLimiteRetentativaPorBandeira(TransacaoVO transacaoVO, CartaoTentativaVO cartaoTentativaVO, CartaoTentativaVO cartaoTentativaSemPacto) throws CobrancaException {
        String qtdLimiteRetentativas = null;
        Date dataLimiteRetentativas = null;
        boolean validarRegras = false;
        ValidaBandeira.Bandeira bandeira = null;
        try {
            Transacao transacaoDAO = new Transacao(getCon());
            TransacaoVO transacaoVOAnterior = transacaoDAO.consultarPorChavePrimaria(cartaoTentativaSemPacto.getTransacao(), Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
            transacaoDAO = null;
            if (!UteisValidacao.emptyString(transacaoVOAnterior.getParamsResposta())) {
                qtdLimiteRetentativas = transacaoVOAnterior.obterValorParametroXML(transacaoVOAnterior.getParamsResposta(),"RtryTxLmt");
                String dataLimiteRetentativasString = transacaoVOAnterior.obterValorParametroXML(transacaoVOAnterior.getParamsResposta(),"RtryTxDtLmt");
                if (!UteisValidacao.emptyString(qtdLimiteRetentativas) && !UteisValidacao.emptyString(dataLimiteRetentativasString)) {

                    //se vier ... é porque a transação é sem limite de retentativas, não precisa validar mais regras
                    if (qtdLimiteRetentativas.equals("...")) {
                        return;
                    }
                    //se vier 0 no retorno do xml é pq já não aceita nenhuma retentativa
                    if (qtdLimiteRetentativas != null && qtdLimiteRetentativas.equals("0")) {
                        Uteis.logar(null, "Bloqueio devido adquirente não permitir mais tentativas no cartão... " + cartaoTentativaVO.getCodigo() + " - " + CodigoRetornoPactoEnum.BLOQUEIO_OPERADORA_NAO_PERMITE_RETENTATIVA_NO_CARTAO.getDescricao());
                        String msgRetorno = "A bandeira " + bandeira.name() + " nos informou que não permite mais retentativas de cobranças neste cartão. </br>Faça a troca do cartão do aluno ou entre em contato com a Adquirente para mais detalhes.";
                        throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_OPERADORA_NAO_PERMITE_RETENTATIVA_NO_CARTAO, msgRetorno);
                    }

                    //seguir para validar
                    dataLimiteRetentativas = Uteis.getDate(dataLimiteRetentativasString, "yyyy-MM-dd'T'HH:mm:ss");
                    validarRegras = true;
                    bandeira = buscaBandeiraCartao(transacaoVO.getCartaoCreditoTO().getNumero());
                }
            }
        } catch (Exception ex) {
        }

        //validar limite por bandeira
        if (bandeira != null && validarRegras && !UteisValidacao.emptyString(qtdLimiteRetentativas)) {
            RegrasRetentativaPorBandeiraEnum regrasRetentativaPorBandeiraEnum = RegrasRetentativaPorBandeiraEnum.obterPorDescricao(bandeira.name());
            int tentativasRealizadasPeriodoRegraBandeira = obterQtdTentativasUltDias(transacaoVO.getCartaoMascarado(), regrasRetentativaPorBandeiraEnum.getQtdDiasPeriodoValidar());
            if (tentativasRealizadasPeriodoRegraBandeira > Integer.valueOf(qtdLimiteRetentativas)) {
                Uteis.logar(null, "Bloqueio devido exceder limite de tentativas do cartão na adquirente... " + cartaoTentativaVO.getCodigo() + " - " + CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA.getDescricao());
                String msgRetorno = "A bandeira " + bandeira.name() + " permite no máx. <b>" + qtdLimiteRetentativas + "</b> tentativas no período de "
                        + regrasRetentativaPorBandeiraEnum.getQtdDiasPeriodoValidar() + " dias. </br> Você só poderá tentar novamente a partir do dia " + Uteis.getData(dataLimiteRetentativas, "dd-MM-yyyy");
                throw new CobrancaException(CodigoRetornoPactoEnum.BLOQUEIO_LIMITE_TENTATIVA_CARTAO_EXCEDIDO_BANDEIRA, msgRetorno);
            }
        }
    }

    public ValidaBandeira.Bandeira buscaBandeiraCartao(String numCartao) {
        String numeroCartao = "";
        ValidaBandeira.Bandeira bandeiraCard = null;
        try {
            numeroCartao = numeroCartao.replaceAll(" ", "");
            if (ValidaBandeira.numeroCartaoValido(numCartao)) {
                bandeiraCard = ValidaBandeira.buscarBandeira(numCartao);
                return bandeiraCard;
            }
        } catch (Exception ignored) {
        }
        return bandeiraCard;
    }

    private CartaoTentativaVO obterUltimaTentativa(TransacaoVO transacaoVO, boolean semRetornoPacto, CartaoCreditoTO cartaoCreditoTO) {
        CartaoTentativa cartaoTentativaDAO = null;
        try {
            if (transacaoVO == null) {
                return null;
            }
            if (UteisValidacao.emptyString(transacaoVO.getCartaoMascarado()) && UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
                return null;
            }

            cartaoTentativaDAO = new CartaoTentativa(getCon());
            if (transacaoVO.getTipo() != null && transacaoVO.getTipo().equals(TipoTransacaoEnum.DCC_STONE_ONLINE_V5)) {
                return cartaoTentativaDAO.obterUltimaTentativaCartao(cartaoCreditoTO.getNumero(), semRetornoPacto, transacaoVO.getConvenioCobrancaVO().getCodigo());
            } else {
                String cartaoMascarado = UteisValidacao.emptyString(transacaoVO.getCartaoMascarado()) ? APF.getCartaoMascarado(cartaoCreditoTO.getNumero()) : transacaoVO.getCartaoMascarado();
                return cartaoTentativaDAO.obterUltimaTentativa(cartaoMascarado, semRetornoPacto, transacaoVO.getConvenioCobrancaVO().getCodigo());
            }
        } catch (Exception ex) {
            Uteis.logar(ex, CobrancaOnlineService.class);
            return null;
        } finally {
            cartaoTentativaDAO = null;
        }
    }

    private int obterQtdTentativasUltDias(String cartaoMascarado, int qtdDias) {
        CartaoTentativa cartaoTentativaDAO = null;
        try {
            if (UteisValidacao.emptyString(cartaoMascarado)) {
                return 0;
            }

            cartaoTentativaDAO = new CartaoTentativa(getCon());
            return cartaoTentativaDAO.obterQtdTentativasUltDias(cartaoMascarado, qtdDias);
        } catch (Exception ex) {
            Uteis.logar(ex, CobrancaOnlineService.class);
            return 0;
        } finally {
            cartaoTentativaDAO = null;
        }
    }

    public void verificarAlteracoesTransacao(TransacaoVO transacaoVO) throws Exception {
        VerificadorTransacaoService service = null;
        try {
            service = new VerificadorTransacaoService(getCon());
            service.verificarAlteracoesTransacao(transacaoVO, true);
        } catch (Exception ex) {
            Uteis.logar(ex, CobrancaOnlineService.class);
            throw ex;
        } finally {
            service = null;
        }
    }

    public List<RecebedorDTO> obterRecebedoresSplitPagamentoDoConvenio(TransacaoVO transacaoVO, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        //verificar recebedores para Split de pagamentos
        ConvenioCobrancaRateio convenioCobrancaRateioDAO = null;
        try {
            convenioCobrancaRateioDAO = new ConvenioCobrancaRateio(this.getCon());

            List<ConvenioCobrancaRateioVO> listaRateio = convenioCobrancaRateioDAO.consultarPorConvenioCobranca(convenioCobrancaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (UteisValidacao.emptyList(listaRateio)) {
                //não existe regra definida
                return null;
            }

            Map<String, Double> mapaRecebedorPercentual = new HashMap<>();

            Double percentualTotal = 0.0;
            for (MovParcelaVO movParcelaVO : transacaoVO.getListaParcelas()) {

                StringBuilder sql = new StringBuilder();
                sql.append("select  \n");
                sql.append("mp.valorparcela, \n");
                sql.append("mp.empresa, \n");
                sql.append("con.plano, \n");
                sql.append("mov.produto, \n");
                sql.append("pro.tipoproduto \n");
                sql.append("from movparcela mp \n");
                sql.append("left join contrato con on con.codigo = mp.contrato \n");
                sql.append("inner join movprodutoparcela mpp on mpp.movparcela = mp.codigo \n");
                sql.append("inner join movproduto mov on mpp.movproduto = mov.codigo \n");
                sql.append("inner join produto pro on pro.codigo = mov.produto \n");
                sql.append("where mp.codigo = ").append(movParcelaVO.getCodigo());

                try (Statement stm = getCon().createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sql.toString())) {
                        while (rs.next()) {

                            Integer empresa = rs.getInt("empresa");
                            Integer plano = rs.getInt("plano");
                            Integer produto = rs.getInt("produto");
                            String tipoproduto = rs.getString("tipoproduto");

                            ConvenioCobrancaRateioVO rateioUtilizarVO = null;
                            for (ConvenioCobrancaRateioVO rateioVO : listaRateio) {

                                //verificar se é da mesma empresa da parcela
                                if (!rateioVO.getEmpresaVO().getCodigo().equals(empresa)) {
                                    continue;
                                }

                                //Valida pelo plano
                                if (rateioVO.getPlanoVO().getCodigo().equals(plano) && !UteisValidacao.emptyNumber(plano)) {
                                    rateioUtilizarVO = rateioVO;
                                    break;
                                }

                                //Valida pelo produto
                                if (rateioVO.getProdutoVO().getCodigo().equals(produto) && !UteisValidacao.emptyNumber(produto)) {
                                    rateioUtilizarVO = rateioVO;
                                    break;
                                }

                                //Valida pelo Tipo do Produto
                                if (rateioVO.getTipoProduto().equals(tipoproduto) && !UteisValidacao.emptyString(tipoproduto)) {
                                    rateioUtilizarVO = rateioVO;
                                    break;
                                }
                            }

                            //verificar se existe um padrão para ser utilizado
                            if (rateioUtilizarVO == null) {
                                for (ConvenioCobrancaRateioVO rateioVO : listaRateio) {
                                    if (rateioVO.isPadrao()) {
                                        rateioUtilizarVO = rateioVO;
                                        break;
                                    }
                                }
                            }

                            //calcular valor pago
                            if (rateioUtilizarVO != null) {
                                for (ConvenioCobrancaRateioItemVO itemVO : rateioUtilizarVO.getItens()) {
                                    Double percentualAcumulado = mapaRecebedorPercentual.get(itemVO.getIdRecebedor());
                                    if (percentualAcumulado == null) {
                                        percentualAcumulado = 0.0;
                                    }
                                    // Adiciona ao mapa, mas ainda não soma ao percentualTotal
                                    if (convenioCobrancaVO.isStoneV5()) {
                                        //concatena com o "recebedor principal" true ou false
                                        String labelIdItem = itemVO.getIdRecebedor() + "|" + itemVO.isRecebedorPrincipal();
                                        mapaRecebedorPercentual.put(labelIdItem, (percentualAcumulado + itemVO.getPorcentagem()));
                                    } else {
                                        //só adiciona o id ao mapa sem concatenar
                                        mapaRecebedorPercentual.put(itemVO.getIdRecebedor(), (percentualAcumulado + itemVO.getPorcentagem()));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            percentualTotal = mapaRecebedorPercentual.values().stream().mapToDouble(Double::doubleValue).sum();

            if (mapaRecebedorPercentual.size() == 0) {
                return null;
            }


            //Definir o valor de acordo com o percentual
            List<RecebedorDTO> lista = new ArrayList<>();
            for (String key : mapaRecebedorPercentual.keySet()) {
                RecebedorDTO recebedorDTO = new RecebedorDTO();

                if (convenioCobrancaVO.isStoneV5()) {
                    String[] id = key.split("\\|");
                    recebedorDTO.setId(id[0]);
                    recebedorDTO.setRecebedorPrincipal(Boolean.valueOf(id[1]));
                } else {
                    recebedorDTO.setId(key);
                }


                Double percentualRecebedor = mapaRecebedorPercentual.get(key);
                recebedorDTO.setPercentual(percentualRecebedor);

                //descobrir percentual sobre o percentual total
                Double percentage = ((recebedorDTO.getPercentual() * 100) / percentualTotal);

                //descobrir o valor que representa
                Double valor = ((transacaoVO.getValor() * percentage) / 100);

                DecimalFormat df = new DecimalFormat("0.00");
                df.setRoundingMode(RoundingMode.DOWN);
                Double valorArredondado = new Double(df.format(valor).replaceAll(",", "."));

                recebedorDTO.setValor(valorArredondado);

                int valorEmCentavos = (int) (Uteis.arredondarForcando2CasasDecimais(recebedorDTO.getValor() * 100));
                recebedorDTO.setValorCentavos(recebedorDTO.getValorCentavos() + valorEmCentavos);

                lista.add(recebedorDTO);
            }

            Ordenacao.ordenarListaReverse(lista, "valor");

            //verificar se a soma do rateio está igual ao valor total (Arredondamento)
            Integer valorTotalRateio = 0;
            for (RecebedorDTO dto : lista) {
                valorTotalRateio += dto.getValorCentavos();
            }
            int valorTotalTransacao = (int) (transacaoVO.getValor() * 100);


            //caso haja diferença colocar a diferença no primeiro item
            // O item que tem o maior valor
            Integer diferenca = (valorTotalTransacao - valorTotalRateio);
            if (diferenca != 0) {
                lista.get(0).setValorCentavos(lista.get(0).getValorCentavos() + diferenca);
            }

            //ordenar pra vir o recebedor principal em primeiro da lista
            if (convenioCobrancaVO.isStoneV5()) {
                return lista.stream()
                        .sorted(Comparator.comparing(RecebedorDTO::isRecebedorPrincipal).reversed())
                        .collect(Collectors.toList());
            } else {
                //retornar sem ordenação
                return lista;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            convenioCobrancaRateioDAO = null;
        }
    }

    private void processarResultadoCobrancaReguaCobranca(TransacaoVO transacaoVO) {
        PactoPayConfig pactoPayConfigDAO;
        try {
            pactoPayConfigDAO = new PactoPayConfig(this.getCon());
            pactoPayConfigDAO.processarResultadoCobranca(transacaoVO, false, null, false);
        } catch (Exception ex) {
//            ex.printStackTrace();
            Uteis.logarDebug("CobrancaOnlineService | processarResultadoCobrancaReguaCobranca: " + ex.getMessage());
        } finally {
            pactoPayConfigDAO = null;
        }
    }
}
