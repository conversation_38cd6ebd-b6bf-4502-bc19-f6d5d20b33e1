package servicos.impl.pix;

import br.com.pactosolucoes.pjbank.WebhookPJBankJSON;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.ConvenioCobrancaArquivo;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.facade.jdbc.financeiro.PixWebhook;
import negocio.facade.jdbc.financeiro.PixWebhookDetalhe;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.oamd.OAMDService;
import servicos.pix.*;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Rodrigo Estulano on 21/11/2023.
 */

public class PixWebhookService extends SuperEntidade {

    private static final Integer TIMEOUT_REQUEST = 10000; //milisegundos

    private static Map<String, RedeEmpresaVO> redeEmpresaVosJaConsultados = new HashMap<>();

    public PixWebhookService() throws Exception {
    }

    public PixWebhookService(Connection con) throws Exception {
        super(con);
    }

    public void processar(String chaveZW, TipoConvenioCobrancaEnum tipoConvenio, String dados, int codigoPixWebhookOamd) throws Exception {
        if (tipoConvenio.equals(TipoConvenioCobrancaEnum.PIX_PJBANK)) {
            processarWebhookPJBank(chaveZW, tipoConvenio.getCodigo(), dados, codigoPixWebhookOamd);
        }else if (tipoConvenio.equals(TipoConvenioCobrancaEnum.PIX_AFINZ)) {
            processarWebhookAfinz(chaveZW, tipoConvenio.getCodigo(), dados, codigoPixWebhookOamd);
        } else {
            //BB, SANTANDER, BRADESCO, INTER, ITAU
            processarWebhook(chaveZW, tipoConvenio.getCodigo(), dados, codigoPixWebhookOamd);
        }
    }

    private void processarWebhook(String chaveZW, int tipoConvenio, String dados, int codigoPixWebhookOamd) throws Exception {

        try {
            if (isRedePratique(chaveZW) && tipoConvenio == (TipoConvenioCobrancaEnum.PIX_BB.getCodigo())) {
                Uteis.logarDebug("Webhook de pix da Pratique, não vou processar!");
                return;
            }
        } catch (Exception ex) {
        }

        Pix pixDAO = null;
        try {
            pixDAO = new Pix(getCon());

            try {
                //PixWebhookVO é a tabela responsável por gravar os dados crus que vieram da requisição do banco BB, Bradesco, etc...
                PixWebhookVO pixWebhookVO = gravarPixWebhook(tipoConvenio, dados, codigoPixWebhookOamd);

                //INSTANCIAR LISTA QUE VEIO LÁ DO BANCO
                JSONObject body = null;
                JSONArray arrayPixProcessar = null;
                try {
                    body = new JSONObject(dados);
                    arrayPixProcessar = new JSONArray(body.optJSONArray("pix").toString());
                } catch (Exception ex) {
                    throw new Exception("Erro ao obter Body/JsonArray da lista de pix");
                }

                boolean todosPixProcessadosComSucesso = true;

                //PROCESSAR OS PIX DA LISTA QUE VEIO DO BANCO
                if (arrayPixProcessar != null && arrayPixProcessar.length() > 0) {

                    Uteis.logarDebug("########### INICIO DE PROCESSAMENTO WEBHOOK DE PIX | " + arrayPixProcessar.length() + " PIX PAGO(S) PARA PROCESSAR | " + Calendario.hoje());

                    for (int i = 0; i < arrayPixProcessar.length(); i++) {
                        try {
                            Object obj = arrayPixProcessar.get(i);

                            String txIdPix = ((JSONObject) obj).optString("txid");
                            String dataPagamento = ((JSONObject) obj).optString("horario");

                            PixVO pixVO = pixDAO.consultarPorTxId(txIdPix);

                            //PixWebhookDetalhe tabela responsável pelo log da "fila" de pix já processados
                            gravarPixWebhookDetalhe(pixWebhookVO, pixVO);

                            if (pixVO != null && !pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                                pixVO.setDataPagamento(obterDateFromString(dataPagamento, tipoConvenio));
                                pixVO.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());

                                PixPagamentoService pixPagamentoService = null;
                                try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon())) {
                                    pixPagamentoService = new PixPagamentoService(connection);
                                    pixPagamentoService.processarPagamentoPixWebhookControlandoTransacao(pixVO);
                                    Uteis.logarDebug("Pix processado com sucesso | Pix: " + pixVO.getCodigo() + " | txId: " + pixVO.getTxid());
                                    colocarPixWebhookDetalheJaProcessado(pixWebhookVO.getCodigo(), pixVO.getCodigo(), pixVO.getTxid());
                                    colocarPixProcessadoOrigemPeloWebhook(pixVO.getCodigo());
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    Uteis.logarDebug("ERRO ProcessamentoWebhookPix | Pix: " + pixVO.getCodigo() + " | txId: " + pixVO.getTxid() + " | " + ex.getMessage());
                                }
                            } else {
                                Uteis.logarDebug("Não processar Pix via webhook pois já está PAGO: Txid: " + pixVO.getTxid() + " | ChaveZW: " + chaveZW);
                            }
                        } catch (Exception ex) {
                            todosPixProcessadosComSucesso = false;
                            Uteis.logarDebug("Não foi possível processar o pix do webhook: Chave: " + chaveZW + " | tipoConvenio: " +
                                    tipoConvenio + " | Dados: " + dados + " | pixWebhookVO: " + pixWebhookVO.getCodigo() + " | Exception: " + ex.getMessage());
                        }
                    }
                    Uteis.logarDebug("########### FIM DE PROCESSAMENTO WEBHOOK DE PIX " + Calendario.hoje());

                    //Se tiver processado todos os pix da lista do webhook com sucesso, então ir lá no OAMD e atualizar o registro da tabela pixWebhook
                    if (todosPixProcessadosComSucesso) {
                        try {
                            String dataFinalizouProcessamentoZW = Uteis.getDataJDBCTimestamp(Calendario.hoje()).toString();
                            atualizarInfosPixWebhookOAMD(chaveZW, codigoPixWebhookOamd, dataFinalizouProcessamentoZW, pixWebhookVO.getCodigo());
                        } catch (Exception ex) {
                            Uteis.logarDebug("Não foi possivel gravar o PixWebhookOAMD: " + ex.getMessage());
                        }
                    }

                }
            } catch (Exception ex) {
                Uteis.logarDebug("Não foi possível processar o pixWebhook: Chave: " + chaveZW + " | tipoConvenio: " +
                        tipoConvenio + " | Dados: " + dados + " | Exception: " + ex.getMessage());
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
        }
    }

    private boolean isRedePratique(String key) {
        //Como todas as unidades da pratique usam a mesma conta bancária, então não é possível configurar o webhook pois o webhook é uma URL por chave pix.
        //Momentaneamente quando for rede pratique continuará sem webhook até que a integração do Pix Inter fique pronta.
        OAMDService oamdService;
        RedeEmpresaVO rede;
        try {
            oamdService = new OAMDService();

            //Ver se já existe no mapa de já consultados anteriormente
            rede = redeEmpresaVosJaConsultados.getOrDefault(key, null);

            if (rede == null) {
                rede = oamdService.consultarRedeEmpresa(key);
            }

            if (rede != null) {
                redeEmpresaVosJaConsultados.put(key, rede);
                //CHAVEREDE DA PRATIQUE
                if (rede.getChaverede().equals("341b908afd7637c1d5b09f248d3498f1")) {
                    return true;
                }
            }

        } catch (Exception ex) {
            return false;
        } finally {
            oamdService = null;
        }
        return false;
    }

    private void processarWebhookPJBank(String chaveZW, int tipoConvenio, String dados, int codigoPixWebhookOamd) throws Exception {
        Pix pixDAO = null;
        try {
            pixDAO = new Pix(getCon());

            //PixWebhookVO é a tabela responsável por gravar os dados crus que vieram da requisição do banco BB, Bradesco, etc...
            PixWebhookVO pixWebhookVO = gravarPixWebhook(tipoConvenio, dados, codigoPixWebhookOamd);

            WebhookPJBankJSON webhookPJBankJSON = null;
            try {
                webhookPJBankJSON = new WebhookPJBankJSON(new JSONObject(dados));
            } catch (Exception ex) {
                throw new Exception("Erro ao obter WebhookPJBankJSON do pix PJBank");
            }

            boolean pixProcessadoComSucesso = true;
            //PROCESSAR O PIX QUE VEIO DO BANCO
            try {
                Uteis.logarDebug("########### INICIO DE PROCESSAMENTO WEBHOOK DE PIX PAGO PJBANK | " + Calendario.hoje());

                String txIdPix = webhookPJBankJSON.getId_unico();
                String dataPagamento = webhookPJBankJSON.getData_pagamento();
                String dataCredito = webhookPJBankJSON.getData_credito();

                PixVO pixVO = pixDAO.consultarPorTxId(txIdPix);

                //PixWebhookDetalhe tabela responsável pelo log da "fila" de pix já processados
                gravarPixWebhookDetalhe(pixWebhookVO, pixVO);

                if (pixVO != null && !pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                    pixVO.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());
                    pixVO.setDataPagamento(Uteis.getDate(dataPagamento, "MM/dd/yyyy"));
                    if (!UteisValidacao.emptyString(dataCredito)) {
                        pixVO.setDataCredito(Uteis.getDate(dataCredito, "MM/dd/yyyy"));
                    }

                    PixPagamentoService pixPagamentoService = null;
                    try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon())) {
                        pixPagamentoService = new PixPagamentoService(connection);
                        pixPagamentoService.processarPagamentoPixWebhookControlandoTransacao(pixVO);
                        Uteis.logarDebug("Pix processado com sucesso | Pix: " + pixVO.getCodigo() + " | txId: " + pixVO.getTxid());
                        colocarPixWebhookDetalheJaProcessado(pixWebhookVO.getCodigo(), pixVO.getCodigo(), pixVO.getTxid());
                        colocarPixProcessadoOrigemPeloWebhook(pixVO.getCodigo());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        Uteis.logarDebug("ERRO ProcessamentoWebhookPix | Pix: " + pixVO.getCodigo() + " | txId: " + pixVO.getTxid() + " | " + ex.getMessage());
                    }
                } else {
                    Uteis.logarDebug("Não processar Pix via webhook pois já está PAGO: Txid: " + pixVO.getTxid() + " | ChaveZW: " + chaveZW);
                }
            } catch (Exception ex) {
                pixProcessadoComSucesso = false;
                Uteis.logarDebug("Não foi possível processar o pix do webhook: Chave: " + chaveZW + " | tipoConvenio: " +
                        tipoConvenio + " | Dados: " + dados + " | pixWebhookVO: " + pixWebhookVO.getCodigo() + " | Exception: " + ex.getMessage());
            }

            Uteis.logarDebug("########### FIM DE PROCESSAMENTO WEBHOOK DE PIX " + Calendario.hoje());

            //Se tiver processado com sucesso, então ir lá no OAMD e atualizar o registro da tabela pixWebhook
            if (pixProcessadoComSucesso) {
                try {
                    String dataFinalizouProcessamentoZW = Uteis.getDataJDBCTimestamp(Calendario.hoje()).toString();
                    atualizarInfosPixWebhookOAMD(chaveZW, codigoPixWebhookOamd, dataFinalizouProcessamentoZW, pixWebhookVO.getCodigo());
                } catch (Exception ex) {
                    Uteis.logarDebug("Não foi possivel gravar o PixWebhookOAMD: " + ex.getMessage());
                }
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
        }
    }

    public PixDto preencherObtejoPJBank(JSONObject jsonObject) throws Exception {
        PixDto pixDto = new PixDto();
        pixDto.setTxid(jsonObject.optString("nosso_numero"));
        if (UteisValidacao.emptyString(pixDto.getTxid())) {
            pixDto.setTxid(jsonObject.optString("id_unico"));
        }
        if (!UteisValidacao.emptyString(jsonObject.optString("data_pagamento"))) {
            pixDto.setDataPagamento(Uteis.getDate(jsonObject.optString("data_pagamento"), "MM/dd/yyyy"));
            pixDto.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());
        } else {
            pixDto.setStatus(PixStatusEnum.ATIVA.getDescricao());
        }
        if (!UteisValidacao.emptyString(jsonObject.optString("data_credito"))) {
            pixDto.setDataCredito(Uteis.getDate(jsonObject.optString("data_credito"), "MM/dd/yyyy"));
        }

        pixDto.setPedidoNumero(jsonObject.optString("pedido_numero"));
        pixDto.setVencimento(Uteis.getDate(jsonObject.optString("data_vencimento"), "MM/dd/yyyy"));
        pixDto.setUrlQRcode(jsonObject.optString("link"));
        pixDto.setTextoImagemQRcode(jsonObject.optString("qrcode"));
        pixDto.setLinkInfo(jsonObject.optString("link_info"));

        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(jsonObject.optString("valor"));
        pixDto.setValor(pixValorDto);

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setNome(jsonObject.optString("pagador"));
        pixDto.setDevedor(pixDevedorDto);

        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto();
        pixDto.setCalendario(pixCalendarioDto);

        return pixDto;
    }

    private PixWebhookVO gravarPixWebhook(int tipoConvenio, String dados, int codigoPixWebhookOamd) throws Exception {
        PixWebhook pixWebhookDAO = null;
        PixWebhookVO pixWebhookVO = null;
        try {
            pixWebhookDAO = new PixWebhook(getCon());
            pixWebhookVO = new PixWebhookVO();
            pixWebhookVO.setTipoConveio(tipoConvenio);
            pixWebhookVO.setDados(dados);
            pixWebhookVO.setCodigoPixWebhookOamd(codigoPixWebhookOamd);
            pixWebhookDAO.incluir(pixWebhookVO);
            return pixWebhookVO;
        } catch (Exception ex) {
            throw new Exception("Erro ao incluir o pixWebhook: " + ex.getMessage() + " | PixWebhookVO: " + pixWebhookVO.toString());
        } finally {
            pixWebhookDAO = null;
        }
    }

    private PixWebhookDetalheVO gravarPixWebhookDetalhe(PixWebhookVO pixWebhookVO, PixVO pixVO) throws Exception {
        PixWebhookDetalhe pixWebhookDetalheDAO = null;
        PixWebhookDetalheVO pixWebhookDetalheVO = null;
        try {
            pixWebhookDetalheDAO = new PixWebhookDetalhe(getCon());

            pixWebhookDetalheVO = new PixWebhookDetalheVO();
            pixWebhookDetalheVO.setPixWebhook(pixWebhookVO.getCodigo());
            pixWebhookDetalheVO.setTxid(pixVO.getTxid());
            pixWebhookDetalheVO.setPix(pixVO.getCodigo());
            pixWebhookDetalheVO.setProcessado(false);
            pixWebhookDetalheVO.setDataProcessamento(null);
            pixWebhookDetalheDAO.incluir(pixWebhookDetalheVO);
            return pixWebhookDetalheVO;
        } catch (Exception ex) {
            throw new Exception("Erro ao incluir o pixWebhookDetalhe: " + ex.getMessage() + " | PixWebhookDetalheVO: " + pixWebhookDetalheVO.toString());
        } finally {
            pixWebhookDetalheDAO = null;
        }
    }

    private void colocarPixWebhookDetalheJaProcessado(int codPixWebhook, int pix, String txid) throws Exception {
        PixWebhookDetalhe pixWebhookDetalheDAO = null;
        try {
            pixWebhookDetalheDAO = new PixWebhookDetalhe(getCon());
            pixWebhookDetalheDAO.colocarComoJaProcessado(codPixWebhook, pix, txid);
        } catch (Exception ex) {
            throw new Exception("Erro ao setar o pixWebhookDetalhe como JaProcessado: " + ex.getMessage() + " | Pix: " + pix + " | tdid: " + txid);
        } finally {
            pixWebhookDetalheDAO = null;
        }
    }

    private void colocarPixProcessadoOrigemPeloWebhook(int pix) throws Exception {
        Pix pixDAO = null;
        try {
            pixDAO = new Pix(getCon());
            pixDAO.colocarPixProcessadoOrigemPeloWebhook(pix);
        } catch (Exception ex) {
            throw new Exception("Erro ao setar o pix como pagoOrigemWebhook: " + ex.getMessage() + " | Pix: " + pix);
        } finally {
            pixDAO = null;
        }
    }

    private void atualizarInfosPixWebhookOAMD(String chaveZW, int codigoPixWebhookOamdAtualizar, String dataFinalizouProcessamentoZW, int codPixWebhookZW) throws Exception {
        OAMDService oamdService;
        try {
            Uteis.logarDebug("Vou atualizar informações do PixWebhook do OAMD");
            oamdService = new OAMDService();
            boolean sucesso = oamdService.atualizarInfosPixWebhookOAMD(chaveZW, codigoPixWebhookOamdAtualizar, dataFinalizouProcessamentoZW, codPixWebhookZW);
            if (sucesso) {
                Uteis.logarDebug("Informações do PixWebhook do OAMD atualizadas com sucesso");
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao atualizar informações do pixWebhook no OAMD: Chave: " + chaveZW + " | codigoPixWebhookOamd"
                    + codigoPixWebhookOamdAtualizar + " | msg: " + ex.getMessage());
        } finally {
            oamdService = null;
        }
    }

    private Date obterDateFromString(String data, int tipoConvenio) {
        try {
            //BB já vem a data correta, não precisa alterar.
            if (tipoConvenio == TipoConvenioCobrancaEnum.PIX_BRADESCO.getCodigo() ||
                    tipoConvenio == TipoConvenioCobrancaEnum.PIX_INTER.getCodigo() ||
                    tipoConvenio == TipoConvenioCobrancaEnum.PIX_SANTANDER.getCodigo() ||
                    tipoConvenio == TipoConvenioCobrancaEnum.PIX_ITAU.getCodigo()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                Date dataPagamento = sdf.parse(data);
                return dataPagamento;
            } else {
                Date dataPagamento = Uteis.getDate(data, "yyyy-MM-dd'T'HH:mm:ss");
                return dataPagamento;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return Calendario.hoje();
        }
    }

    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        PixService pixService = null;
        try {
            pixService = new PixService(getCon());
            PixServiceInterfaceFacade service = pixService.getPixService(convenioCobrancaVO, getCon());

            if (convenioCobrancaVO.isPixInter()) {
                montarConvenioCobrancaArquivo(convenioCobrancaVO);
            }

            return service.consultarWebhookAtivo(convenioCobrancaVO);

        } catch (CobrancaException ex) {
            ex.printStackTrace();
        }
        return new JSONObject();
    }

    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String chaveZW) throws Exception {
        PixService pixService = null;
        try {
            pixService = new PixService(getCon());
            PixServiceInterfaceFacade service = pixService.getPixService(convenioCobrancaVO, getCon());

            if (convenioCobrancaVO.isPixInter()) {
                montarConvenioCobrancaArquivo(convenioCobrancaVO);
            }

            String urlCallback = montarUrlCallbackWebhook(convenioCobrancaVO, chaveZW);

            return service.configurarUrlCallback(convenioCobrancaVO, urlCallback);
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String montarUrlCallbackWebhook(ConvenioCobrancaVO convenioCobrancaVO, String chaveZW) {
//            IMPORTANTE SOBRE O PIX ITAU
//            Depois dessa etapa, as notificações de recebimento serão enviadas na URL cadastrada (call back)
//            acrescentando o sufixo "/pix". Sendo assim, no momento do cadastro,
//            não é necessário enviar a URL com o sufixo "/pix". Mas na publicação da url de call back deve ser feita com o sufixo /pix.

        String tipoBancoPath = "";
        switch (convenioCobrancaVO.getTipo()) {
            case PIX_BB:
                tipoBancoPath = "bb";
                break;
            case PIX_BRADESCO:
                tipoBancoPath = "bradesco";
                break;
            case PIX_SANTANDER:
                tipoBancoPath = "santander";
                break;
            case PIX_PJBANK:
                tipoBancoPath = "pjbank";
                break;
            case PIX_INTER:
                tipoBancoPath = "inter";
                break;
            case PIX_ITAU:
                tipoBancoPath = "itau";
                break;
            case PIX_AFINZ:
                tipoBancoPath = "afinz";
                break;
        }
        return (obterUrlAPI() + "/prest/pix/webhook/" + tipoBancoPath + "/" + chaveZW);
    }

    public String montarUrlCallbackWebhookV2(ConvenioCobrancaVO convenioCobrancaVO, String chaveZW) {
       return (obterUrlAPI() + "/prest/pix/v2/webhook/" + convenioCobrancaVO.getTipo().getCodigo() + "/" + chaveZW);
    }

    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        PixService pixService = null;
        try {
            pixService = new PixService(getCon());
            PixServiceInterfaceFacade service = pixService.getPixService(convenioCobrancaVO, getCon());

            if (convenioCobrancaVO.isPixInter()) {
                montarConvenioCobrancaArquivo(convenioCobrancaVO);
            }

            return service.excluirUrlCallback(convenioCobrancaVO);

        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String consultarPixWebhook(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        PixWebhook pixWebhookDAO = null;
        try {
            pixWebhookDAO = new PixWebhook(getCon());

            List<PixWebhookVO> lista = pixWebhookDAO.consultarPorTipoConvenio(convenioCobrancaVO);

            StringBuilder sb = new StringBuilder();
            for (PixWebhookVO obj : lista) {
                sb.append(obj + "\n");
            }

            return sb.toString();
        } catch (CobrancaException ex) {
            ex.printStackTrace();
        }
        return "";
    }

    public String obterUrlAPI() {
        return PropsService.getPropertyValue(PropsService.urlZWAPI);

        //descomentar abaixo e comentar o de cima para testar em ambiente local
        //testar local é necessário usar o Ngrok para rotear o localhost para a Internet(Web)
//        return "https://fab2-170-82-78-195.ngrok-free.app/API-ZillyonWeb";
    }

    private void montarConvenioCobrancaArquivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        ConvenioCobrancaArquivo convenioCobrancaArquivoDAO;
        try {
            convenioCobrancaArquivoDAO = new ConvenioCobrancaArquivo(con);
            List<ConvenioCobrancaArquivoVO> listaConvenioCobrancaArquivoVO = new ArrayList<>();
            listaConvenioCobrancaArquivoVO.addAll(convenioCobrancaArquivoDAO.consultarListaPorConvenioCobranca(convenioCobrancaVO.getCodigo(), false));

            if (!UteisValidacao.emptyList(listaConvenioCobrancaArquivoVO)) {
                for (ConvenioCobrancaArquivoVO item : listaConvenioCobrancaArquivoVO) {

                    if (convenioCobrancaVO.isPixInter()) {
                        item.setSenha(null);
                    }

                    //sempre montar o arquivo só onde precisa para evitar vazamento de memória. NÃO colocar lá no montar dados
                    item.setArquivo(getFacade().getConvenioCobrancaArquivo().consultarArquivoBytePorChavePrimaria(item.getCodigo()));
                }
            }

            convenioCobrancaVO.setListaConvenioCobrancaArquivoVO(listaConvenioCobrancaArquivoVO);
        } catch (Exception ex) {
        } finally {
            convenioCobrancaArquivoDAO = null;
        }
    }

    private void processarWebhookAfinz(String chaveZW, int tipoConvenio, String dados, int codigoPixWebhookOamd) throws Exception {
        Pix pixDAO = null;
        try {
            pixDAO = new Pix(getCon());

            //PixWebhookVO é a tabela responsável por gravar os dados crus que vieram da requisição do banco Afinz, BB, Bradesco, etc...
            PixWebhookVO pixWebhookVO = gravarPixWebhook(tipoConvenio, dados, codigoPixWebhookOamd);

            JSONObject webhookJSON = null;
            try {
                webhookJSON = new JSONObject(dados);
            } catch (Exception ex) {
                throw new Exception("Erro ao obter JSON do pix Afinz");
            }

            if(!webhookJSON.has("orderId")) {
                throw new Exception("Erro ao obter orderId e installments do pix Afinz");
            }

            // Pegar detalhes do pagamento
            String url = PropsService.getPropertyValue(PropsService.urlApiPagoLivreConciliacaoProducao) + "/orders/" + webhookJSON.optString("orderId");

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            RequestHttpService service = new RequestHttpService();

            RespostaHttpDTO resposta = service.executeRequest(url, headers, null, null, MetodoHttpEnum.GET);
            JSONObject json = new JSONObject(resposta.getResponse());

            boolean pixProcessadoComSucesso = true;
            //PROCESSAR O PIX QUE VEIO DO BANCO
            try {
                Uteis.logarDebug("########### INICIO DE PROCESSAMENTO WEBHOOK DE PIX AFINZ | " + Calendario.hoje());

                if(!json.has("orderId") || !json.has("installments")) {
                    throw new Exception("Erro ao obter orderId e installments do pix Afinz");
                }

                JSONArray pagamentos = json.optJSONArray("installments");
                if(pagamentos.length() == 0) {
                    throw new Exception("Erro ao obter os pagamentos do pix Afinz");
                }
                JSONObject pagamento = pagamentos.getJSONObject(0);
                if(!pagamento.has("paymentDate") || !pagamento.has("expectedWithdrawDate")) {
                    throw new Exception("Erro ao obter paymentDate e expectedWithdrawDate do pix Afinz");
                }

                // Revalidar se foi mesmo pago para evitar que só de chamar o webhook
                // passando os dados certos o Pix seja dado como pago
                if(!pagamento.optString("status").equals("paid")) {
                    throw new Exception("Erro, Pix não está com o status pago na Afinz");
                }

                if(!json.optString("id").equals(webhookJSON.optString("orderId"))) {
                    throw new Exception("Erro, orderId/txId enviado não corresponde a informação retornada pela Afinz");
                }

                String txIdPix = json.optString("id");

                PixVO pixVO = pixDAO.consultarPorTxId(txIdPix);

                //PixWebhookDetalhe tabela responsável pelo log da "fila" de pix já processados
                gravarPixWebhookDetalhe(pixWebhookVO, pixVO);

                if (pixVO != null && !pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                    pixVO.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());

                    Calendar calendar = Calendar.getInstance();

                    String paymentDate = pagamento.optString("paymentDate");
                    Date dataPagamento = Uteis.getDate(paymentDate.split("T")[0] + " " + paymentDate.split("T")[1].split("\\.")[0], "yyyy-MM-dd hh:mm:ss");
                    calendar.setTime(dataPagamento);
                    calendar.add(Calendar.HOUR_OF_DAY, -3);
                    dataPagamento = calendar.getTime();
                    pixVO.setDataPagamento(dataPagamento);

                    String expectedWithdrawDate = pagamento.optString("expectedWithdrawDate");

                    if (!UteisValidacao.emptyString(expectedWithdrawDate)) {
                        Date dataCredito = Uteis.getDate(expectedWithdrawDate.split("T")[0] + " " + expectedWithdrawDate.split("T")[1].split("\\.")[0], "yyyy-MM-dd hh:mm:ss");
                        calendar.setTime(dataCredito);
                        calendar.add(Calendar.HOUR_OF_DAY, -3);
                        dataCredito = calendar.getTime();
                        pixVO.setDataCredito(dataCredito);
                    }

                    PixPagamentoService pixPagamentoService = null;
                    try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon())) {
                        pixPagamentoService = new PixPagamentoService(connection);
                        pixPagamentoService.processarPagamentoPixWebhookControlandoTransacao(pixVO);
                        Uteis.logarDebug("Pix processado com sucesso | Pix: " + pixVO.getCodigo() + " | txId: " + pixVO.getTxid());
                        colocarPixWebhookDetalheJaProcessado(pixWebhookVO.getCodigo(), pixVO.getCodigo(), pixVO.getTxid());
                        colocarPixProcessadoOrigemPeloWebhook(pixVO.getCodigo());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        Uteis.logarDebug("ERRO ProcessamentoWebhookPix | Pix: " + pixVO.getCodigo() + " | txId: " + pixVO.getTxid() + " | " + ex.getMessage());
                    }
                } else {
                    Uteis.logarDebug("Não processar Pix via webhook pois já está PAGO: Txid: " + pixVO.getTxid() + " | ChaveZW: " + chaveZW);
                }
            } catch (Exception ex) {
                pixProcessadoComSucesso = false;
                Uteis.logarDebug("Não foi possível processar o pix do webhook: Chave: " + chaveZW + " | tipoConvenio: " +
                        tipoConvenio + " | Dados: " + dados + " | pixWebhookVO: " + pixWebhookVO.getCodigo() + " | Exception: " + ex.getMessage());
            }

            Uteis.logarDebug("########### FIM DE PROCESSAMENTO WEBHOOK DE PIX AFINZ " + Calendario.hoje());

            //Se tiver processado com sucesso, então ir lá no OAMD e atualizar o registro da tabela pixWebhook
            if (pixProcessadoComSucesso) {
                try {
                    String dataFinalizouProcessamentoZW = Uteis.getDataJDBCTimestamp(Calendario.hoje()).toString();
                    atualizarInfosPixWebhookOAMD(chaveZW, codigoPixWebhookOamd, dataFinalizouProcessamentoZW, pixWebhookVO.getCodigo());
                } catch (Exception ex) {
                    Uteis.logarDebug("Não foi possivel gravar o PixWebhookOAMD: " + ex.getMessage());
                }
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
        }
    }
}
