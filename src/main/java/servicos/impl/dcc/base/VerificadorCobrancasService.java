package servicos.impl.dcc.base;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Remessa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Lu<PERSON>
 * Date: 09/05/2020
 */
public class VerificadorCobrancasService {

    private Connection con;
    private List<String> problemasEncontrados;

    public VerificadorCobrancasService(Connection con) {
        this.con = con;
        this.problemasEncontrados = new ArrayList<>();
    }

    public void processar() {
        try {
            verificarRemessasSemStatusHoje();
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            enviarEmailNotificarErrosCobranca();
        }
    }

    private void verificarRemessasSemStatusHoje() {
        try {
            Remessa remessaDAO = new Remessa(this.con);
            List<RemessaVO> lista = remessaDAO.consultarRemessasSemSitucao(Calendario.hoje());
            remessaDAO = null;

            if (!UteisValidacao.emptyList(lista)) {
                this.adicionarMsg("Remessas sem situação - Data " + Uteis.getData(Calendario.hoje()) + ". Total " + lista.size());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void adicionarMsg(String msg) {
        if (this.problemasEncontrados == null) {
            this.problemasEncontrados = new ArrayList<>();
        }
        this.problemasEncontrados.add(msg);
        Uteis.logar(null, msg);
    }

    private void enviarEmailNotificarErrosCobranca() {
        try {
            if (UteisValidacao.emptyList(this.problemasEncontrados)) {
                return;
            }

            String chave = DAO.resolveKeyFromConnection(this.con);

            UteisEmail email = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
            String assunto = "Problemas Cobranças: " + chave;
            email.novo(assunto, config);

            String[] emailEnviar = new String[]{"<EMAIL>"};

            StringBuilder emailTexto = new StringBuilder();
            emailTexto.append("<h2>Erros encontrados nas cobranças</h2>");
            emailTexto.append("<h4>Chave: ").append(chave).append("</h4>");
            for (String erro : this.problemasEncontrados) {
                emailTexto.append("<p> - ").append(erro).append("</p>");
            }

            email.enviarEmailN(emailEnviar, emailTexto.toString(), assunto, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro enviarEmailBloqueioEnvioGetNet: " + ex.getMessage());
        }
    }

    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"teste"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);

            VerificadorCobrancasService service = new VerificadorCobrancasService(con);
            service.processar();
            service = null;
        } catch (Exception ex) {
            Logger.getLogger(VerificadorCobrancasService.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ignored){
                }
            }
        }
    }
}
