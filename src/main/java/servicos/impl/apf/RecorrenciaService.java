/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.apf;

import br.com.pactosolucoes.ecf.cupomfiscal.comuns.servico.interfaces.CupomFiscalServiceFacade;
import br.com.pactosolucoes.enumeradores.RegraCancelamentoAutomaticoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.plano.PlanoCategoriaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.arquitetura.ResultadoServicosInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.ConfiguracaoSistemaInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.contrato.ContratoRecorrenciaInterfaceFacade;
import negocio.interfaces.contrato.MovProdutoInterfaceFacade;
import negocio.interfaces.financeiro.ControleTaxaPersonalInterfaceFacade;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaTentativaConvenioInterfaceFacade;
import negocio.interfaces.financeiro.OperadoraCartaoInterfaceFacade;
import negocio.interfaces.notaFiscal.NotaFiscalInterfaceFacade;
import negocio.interfaces.plano.PlanoInterfaceFacade;
import servicos.EmailNotificacao;
import servicos.SuperServico;
import servicos.operacoes.CancelamentoContratoAutomaticoService;
import servicos.operacoes.ThreadCancelamentoAutomatico;
import servicos.operacoes.ThreadRenovacaoAutomatica;
import servicos.operacoes.ThreadRenovacaoAutomaticaContratoNormal;
import servicos.operacoes.ThreadRenovacaoAutomaticaProduto;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Classe responsável por encapsular lógica de negócio de cobrança através do
 * AprovaFácil em regime de Lote (RemessaVO) de transações, recebendo uma lista
 * de MovParcelaVO e efetuando uma Transacao (TransacaoVO) para cada parcela da
 * lista. Cada transacao ocorrerá dentro uma Thread (ThreadAprovaFacilParcela)
 * para processá-la.
 *
 * <AUTHOR>
 */
public class RecorrenciaService extends SuperServico {

    private ZillyonWebFacade zwFacade;
    private FormaPagamentoVO formaPagamento;
    private StringBuffer sb = new StringBuffer();
    private ConfiguracaoSistemaVO configSistema;
    private Map<TransacaoVO, ContratoRecorrenciaVO> mapaParcelasComErro = new HashMap<>();
    private List<MovParcelaVO> listaParcelas = new ArrayList<>();
    private RemessaVO remessaVO = new RemessaVO();
    private UsuarioVO usuario;
    private String ip;
    private int nrThreadsProcessadas = 0;
    private List<ContratoRecorrenciaVO> listaContratosVencendo = new ArrayList<>();
    private List<MovProdutoVO> listaProdutosVencendo = new ArrayList<>();
    ConfiguracaoSistemaInterfaceFacade configDao;
    private boolean verificarRepescagem = false;
    private ProdutoVO produtoAnuidade;
    private ClienteInterfaceFacade clienteDAO;

    public static void main(String[] args) {
        String key = "bdzillyonengenhariadocorpomatriz-2022-12-03";
        Connection con;
        try {
            con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(key, con);
            RecorrenciaService rs = new RecorrenciaService(con);
            rs.processarDia(new Date());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public RecorrenciaService(Connection con) throws Exception {
        super(con);
        Uteis.logar(sb, "Conexão utilizada: " + con);

        this.zwFacade = new ZillyonWebFacade(con);

        this.usuario = zwFacade.getUsuarioRecorrencia();

        this.ip = "RecorrENtE";

        this.clienteDAO = new Cliente(con);

        configDao = new ConfiguracaoSistema(con);
        this.configSistema = configDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);

        FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
        formaPagamento = formaPagamentoDAO.obterFormaPagamentoCartaoRecorrente();
        formaPagamentoDAO = null;
    }

    public void registrarParcelaErro(TransacaoVO transacao, ContratoRecorrenciaVO contratoRecorrencia) {
        this.mapaParcelasComErro.put(transacao, contratoRecorrencia);
    }

    public ContratoInterfaceFacade getContratoDAO() throws Exception {
        return zwFacade.getContrato();
    }

    private ControleTaxaPersonalInterfaceFacade getControleTaxaPersonalDAO() throws Exception {
        return zwFacade.getControleTaxaPersonal();
    }

    public MovParcelaInterfaceFacade getMovParcelaDAO() throws Exception {
        return zwFacade.getMovParcela();
    }

    public MovParcelaTentativaConvenioInterfaceFacade getMovParcelaTentativaConvenioDAO() throws Exception {
        return zwFacade.getMovParcelaTentativaConvenio();
    }

    public void logar(String mensagem) {
        Uteis.logar(this.sb, mensagem);
    }

    public ConfiguracaoSistemaVO getConfigSistema() {
        return configSistema;
    }

    public ContratoRecorrenciaInterfaceFacade getContratoRecorrenciaDAO() throws Exception {
        return zwFacade.getContratoRecorrencia();
    }

    public MovPagamentoInterfaceFacade getMovPagamentoDAO() throws Exception {
        return zwFacade.getMovPagamento();
    }

    public OperadoraCartaoInterfaceFacade getOperadoraCartaoDAO() throws Exception {
        return zwFacade.getOperadoraCartao();
    }

    public EmpresaInterfaceFacade getEmpresaDAO() throws Exception {
        return zwFacade.getEmpresa();
    }

    public FormaPagamentoVO getFormaPagamento() {
        return formaPagamento;
    }

    public TransacaoVO obterUltimaTransacao(String codigoUltimaTransacao) throws Exception {
        return zwFacade.getTransacao().consultarPorCodigoExterno(codigoUltimaTransacao);
    }

    public OperadoraCartaoVO obterOperadoraUltimaTransacao(TransacaoVO transacao) throws Exception {
        String nomeBandeira = transacao.getValorAtributoEnvio(APF.Bandeira);
        if (!nomeBandeira.isEmpty()) {
            OperadorasExternasAprovaFacilEnum oper = OperadorasExternasAprovaFacilEnum.valueOf(nomeBandeira);
            return obterOperadoraPorEnum(oper);
        } else {
            return null;
        }
    }

    public CupomFiscalServiceFacade obterServicoCupom() throws Exception {
        return zwFacade.getCupomFiscalService();
    }

    public NotaFiscalInterfaceFacade obterServicoNotaFiscal() throws Exception {
        return zwFacade.getNotaFiscal();
    }

    public LogInterfaceFacade getLogDAO() throws Exception {
        return zwFacade.getLog();
    }

    public ClienteInterfaceFacade getClienteDAO() {
        return clienteDAO;
    }

    public OperadoraCartaoVO obterOperadoraPorEnum(OperadorasExternasAprovaFacilEnum oper) throws Exception {
        List<OperadoraCartaoVO> lista = getOperadoraCartaoDAO().consultarPorCodigoIntegracaoAPF(
                oper.getId(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!lista.isEmpty()) {
            return lista.get(0);
        } else {
            return null;
        }
    }

    public void atualizarRemessa(TransacaoVO transacao) {
        remessaVO.getListaTransacoes().add(transacao);
    }

    public String getIP() {
        return ip;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void processarDia(Date dia) throws SQLException {
        ResultadoServicosVO resultadoCancelamento = new ResultadoServicosVO(ServicoEnum.CANCELAMENTO);

        ////////Cancelamento automático dos contratos recorrência que não são de transação de Cartão-Crédito On-line
        try {
            List<MovParcelaVO> listaParcelasRecorrenciaOff = this.getMovParcelaDAO().consultarParcelasRecorrenciaParaCancelamentoAutomatico(dia);
            if (!listaParcelasRecorrenciaOff.isEmpty()) {
                int seq = 1;
                for (MovParcelaVO parcelaVO : listaParcelasRecorrenciaOff) {
                    boolean parcelaOff = true;
                    if (!listaParcelas.isEmpty()) {
                        for (MovParcelaVO obj : listaParcelas) {
                            if (obj.getCodigo().equals(parcelaVO.getCodigo())) {
                                parcelaOff = false;
                                break;
                            }
                        }
                    }

                    if (parcelaOff) {
                        ContratoRecorrenciaVO contratoRecor = getContratoRecorrenciaDAO().consultarPorContrato(parcelaVO.getContrato().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.getCon());
                        try {
                            logar(seq + ". Processando Parc.: " + parcelaVO.getCodigo());
                            ThreadCancelamentoAutomatico threadCancelAuto = new ThreadCancelamentoAutomatico(contratoRecor, this.getCon());
                            if (contratoRecor.getContrato().getSituacao().equals("AT") && threadCancelAuto.validarParcelasParaCancelamento()) {
                                threadCancelAuto.run();
                                notificarThreadProcessada();
                            }
                            seq++;
                        } catch (Exception e) {
                            notificarThreadProcessada();
                            StringBuffer excecao = new StringBuffer(String.format("Erro ao processar  Empresa: \"%s\", parcela \"%s\", do contrato: \"%s\" de \"%s\": %s",
                                    parcelaVO.getEmpresa().getNome(),
                                    parcelaVO.getCodigo(),
                                    contratoRecor.getContrato().getCodigo(),
                                    parcelaVO.getContrato().getPessoa().getNome(),
                                    e.getMessage()));
                            UteisValidacao.enfileirarEmail(new MsgTO(parcelaVO.getEmpresa().getNome(), "Recorrência: Erro Aprovação Parcela", excecao));
                            resultadoCancelamento.getResultado().put(excecao.toString());
                        }
                    }

                }
            }

            //Cancelar parcelas anteriores e gerar débito na conta corrente que ficaram em aberto dos contratos de recorrência renovados
            CancelamentoContratoAutomaticoService cancelamentoService = new CancelamentoContratoAutomaticoService(getCon());
            cancelamentoService.cancelarParcelasAntigasDeContratosRenovadosCancelados();

        } catch (Exception e) {
            this.logar("EXC.: Erro em RecorrenciaService.processarDia ");
            this.logar(Uteis.getData(dia));
            try {
                this.logar("URL: " + getCon().getMetaData().getURL());
                this.logar("Exceção que ocorreu: " + e.getMessage());
                UteisValidacao.enfileirarEmail(new MsgTO("RECORRENCIA SERVICE",
                        "Recorrência erro ao processar dia " + dia + " - " + getCon().getMetaData().getURL(), sb));
            } catch (SQLException ex1) {
                Logger.getLogger(RecorrenciaService.class.getName()).log(Level.SEVERE, null, ex1);
            }
        } finally {
            ThreadEnviarEmail.finalmente();
            logar("Terminando CANCELAMENTO AUTOMÁTICO do dia -> " + Uteis.getDataComHora(dia));
        }

        // Cancelar parcelas de plano personal
        logar("Iniciando CANCELAMENTO AUTOMÁTICO de parcelas de plano personal. Data: " + Uteis.getDataComHora(dia));

        try {
            getCon().setAutoCommit(false);

            List<EmpresaVO> empresas = getEmpresaDAO().consultarEmpresas();

            for (EmpresaVO empresa : empresas) {

                if (empresa.getQuantidadeParcelasSeguidasCancelamento() == 0) {
                    continue;
                }

                List<ControleTaxaPersonalVO> controlesTaxaPersonal = this.getControleTaxaPersonalDAO()
                        .consultarComParcelasEmAtrasoParaCancelamento(empresa.getQuantidadeParcelasSeguidasCancelamento());

                for (ControleTaxaPersonalVO controleTaxaPersonal : controlesTaxaPersonal) {

                    PlanoVO plano = this.getPlanoDAO().consultarPorChavePrimaria(controleTaxaPersonal.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    List<MovParcelaVO> parcelasAVencer = this.getMovParcelaDAO()
                            .consultarParcelasPlanoPersonalAVencerParaCancelamento(controleTaxaPersonal.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);

                    cancelarParcelasPlanoRecorrencia(parcelasAVencer,
                            plano,
                            controleTaxaPersonal.getDataInicioVigenciaPlano(),
                            controleTaxaPersonal.getDataFimVigenciaPlano(),
                            " - CANCELADA AUTOMATICAMENTE ");
                    controleTaxaPersonal.setDataCancelamento(Calendario.hoje());
                    controleTaxaPersonal.setDataFimVigenciaPlano(Calendario.hoje());
                    controleTaxaPersonal.setResponsavelCancelamento(usuario.getCodigo());
                    getControleTaxaPersonalDAO().cancelar(controleTaxaPersonal);
                    logar("CANCELADO CONTROLETAXAPERSONAL: " + controleTaxaPersonal.getCodigo() + ", DO COLABORADOR: " + controleTaxaPersonal.getPersonal().getPessoa_Apresentar());
                }
            }

            getCon().commit();
        } catch (Exception e) {
            getCon().rollback();
            getCon().setAutoCommit(true);
            Uteis.logar("EXC.: Erro em RecorrenciaService.processarDia(" + Uteis.getData(dia) + ") durante o cancelamento de planos personal");
            Uteis.logar(e, RecorrenciaService.class);
        } finally {
            getCon().setAutoCommit(true);
        }

        logar("Fim do CANCELAMENTO AUTOMÁTICO de parcelas de plano personal");

        try {
            getResultadoServicosDAO().gravarResultado(resultadoCancelamento);
        } catch (Exception ex) {
            logar(resultadoCancelamento.getMensagemErro());
        }
    }

    public List<MovParcelaVO> simularCancelamentoPlanoPersonal(ControleTaxaPersonalVO controleTaxaPersonal, String descricaoParcelasCanceladas) throws Exception {

        PlanoVO plano = this.getPlanoDAO().consultarPorChavePrimaria(controleTaxaPersonal.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        List<MovParcelaVO> parcelasAVencer = this.getMovParcelaDAO()
                .consultarParcelasPlanoPersonalAVencerParaCancelamento(controleTaxaPersonal.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);

        return cancelarParcelasPlanoRecorrencia(parcelasAVencer,
                plano,
                controleTaxaPersonal.getDataInicioVigenciaPlano(),
                controleTaxaPersonal.getDataFimVigenciaPlano(),
                descricaoParcelasCanceladas,
                true);
    }

    public List<MovParcelaVO> cancelarPlanoPersonal(ControleTaxaPersonalVO controleTaxaPersonal, String descricaoParcelasCanceladas, UsuarioVO responsavel) throws Exception {

        try{
            getCon().setAutoCommit(false);

            PlanoVO plano = this.getPlanoDAO().consultarPorChavePrimaria(controleTaxaPersonal.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            List<MovParcelaVO> parcelasAVencer = this.getMovParcelaDAO()
                    .consultarParcelasPlanoPersonalAVencerParaCancelamento(controleTaxaPersonal.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);


            List<MovParcelaVO> parcelasCancelamento = cancelarParcelasPlanoRecorrencia(parcelasAVencer,
                    plano,
                    controleTaxaPersonal.getDataInicioVigenciaPlano(),
                    controleTaxaPersonal.getDataFimVigenciaPlano(),
                    descricaoParcelasCanceladas,
                    false);

            parcelasCancelamento = Ordenacao.ordenarLista(parcelasCancelamento, "dataVencimento");

            Date novaDataVigenciaPlano = parcelasCancelamento.size() > 0
                    ? parcelasCancelamento.get(0).getDataVencimento()
                    : controleTaxaPersonal.getDataFimVigenciaPlano();

            controleTaxaPersonal.setDataFimVigenciaPlano(novaDataVigenciaPlano);
            controleTaxaPersonal.setResponsavelCancelamento(responsavel.getCodigo());
            controleTaxaPersonal.setCancelado(true);
            controleTaxaPersonal.setDataCancelamento(Calendario.hoje());
            getControleTaxaPersonalDAO().cancelar(controleTaxaPersonal);

            getCon().commit();

            return parcelasCancelamento;
        }catch (Exception e){
            getCon().rollback();
            getCon().setAutoCommit(true);
            throw e;
        }
    }

    public List<MovParcelaVO> cancelarParcelasPlanoRecorrencia(List<MovParcelaVO> parcelas,
                                                           PlanoVO plano,
                                                           Date dataInicioVigenciaContrato,
                                                           Date dataFimVigenciaContrato,
                                                           String descricaoParaIncluirParcelasCanceladas) throws Exception {
        return cancelarParcelasPlanoRecorrencia(parcelas, plano, dataInicioVigenciaContrato, dataFimVigenciaContrato, descricaoParaIncluirParcelasCanceladas, false);
    }

    public List<MovParcelaVO> cancelarParcelasPlanoRecorrencia(List<MovParcelaVO> parcelas,
                                                               PlanoVO plano,
                                                               Date dataInicioVigenciaContrato,
                                                               Date dataFimVigenciaContrato,
                                                               String descricaoParaIncluirParcelasCanceladas,
                                                               boolean somenteCalcular) throws Exception {



        descricaoParaIncluirParcelasCanceladas = descricaoParaIncluirParcelasCanceladas != null && descricaoParaIncluirParcelasCanceladas.length() > 0
                ? " - "+descricaoParaIncluirParcelasCanceladas
                : "";
        Date hoje = Calendario.hoje();
        List<MovParcelaVO> novasParcelasCancelamento = new ArrayList<>();

        for (MovParcelaVO parcela: parcelas) {

            for (MovProdutoParcelaVO produtoParcela:parcela.getMovProdutoParcelaVOs()) {
                parcela.getTipoProdutos().add(produtoParcela.getMovProdutoVO().getProduto().getTipoProduto());
            }

            if(parcela.getTipoProdutos().contains(TipoProduto.MES_REFERENCIA_PLANO.getCodigo()) ||
                    parcela.getTipoProdutos().contains(TipoProduto.TAXA_PERSONAL.getCodigo())){

                if(plano.getPlanoRecorrencia().isCancelamentoProporcional()){

                    int diasAteVencimentoParcela = Calendario.diferencaEmDias(hoje, parcela.getDataVencimento());
                    if( diasAteVencimentoParcela <= plano.getPlanoRecorrencia().getQtdDiasCobrarProximaParcela()){
                        continue;
                    }
                }
            }

            if(parcela.getTipoProdutos().contains(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())){

                int diasAteVigenciaFinalPlano = Calendario.diferencaEmDias(hoje, dataFimVigenciaContrato);
                if(plano.getPlanoRecorrencia().isCancelamentoProporcional() &&
                        diasAteVigenciaFinalPlano <= plano.getPlanoRecorrencia().getQtdDiasCobrarAnuidadeTotal()){

                    // Configuração do plano: Quantidade de dias até a data de término do contrato para cobrar valor total da anuidade
                    // Neste caso, o valor total da anuidade deve ser cobrada
                    // Veja mais detalhes no cadastro do plano na aba recorrencia
                    continue;
                }else{

                    // Cancela a parcela atual e cria uma parcela de anuidade com valor proporcional ao que foi utilizado pelo personal

                    int diasVigenciaPlano = Calendario.diferencaEmDias(dataInicioVigenciaContrato, dataFimVigenciaContrato);
                    int diasUtilizacaoPlano = Calendario.diferencaEmDias(dataInicioVigenciaContrato, hoje);
                    Double valorPorDiaAnuidade = parcela.getValorParcela() / diasVigenciaPlano;
                    Double valorProporcionalAnuidade = valorPorDiaAnuidade * diasUtilizacaoPlano;

                    String descricao = " - CANCELAMENTO - "+Calendario.getData(hoje, "dd/MM/yyyy") ;
                    MovParcelaVO novaParcelaAnuidade = (MovParcelaVO) parcela.getClone(true);

                    novaParcelaAnuidade.setDescricao(parcela.getDescricao() + descricao);
                    novaParcelaAnuidade.setValorParcela(valorProporcionalAnuidade);
                    novaParcelaAnuidade.setSituacao("EA");

                    if(!somenteCalcular){
                        getMovParcelaDAO().incluirParcelaSemCommit(novaParcelaAnuidade);
                    }

                    int totalProdutosDaParcela = parcela.getMovProdutoParcelaVOs().size();
                    for (MovProdutoParcelaVO produtoParcela:parcela.getMovProdutoParcelaVOs()) {

                        // Podem existir vários produtos para uma parcela, então o valor do produto é o "valor da parcela / quantidade de produtos da parcela"
                        Double valorPorProduto = novaParcelaAnuidade.getValorParcela() / totalProdutosDaParcela;

                        MovProdutoVO novoProdutoAnuidade = (MovProdutoVO) produtoParcela.getMovProdutoVO().getClone(true);
                        novoProdutoAnuidade.setSituacao("EA");
                        novoProdutoAnuidade.setDescricao(novoProdutoAnuidade.getDescricao() + descricao);
                        novoProdutoAnuidade.setDataFinalVigencia(hoje);
                        novoProdutoAnuidade.setTotalFinal(valorPorProduto);
                        novoProdutoAnuidade.setDataLancamento(hoje);
                        novoProdutoAnuidade.setPrecoUnitario(valorPorProduto);
                        novoProdutoAnuidade.setQuantidade(1);
                        novoProdutoAnuidade.setMesReferencia(Calendario.getMesAno(hoje));
                        novoProdutoAnuidade.setAnoReferencia(Calendario.getAno(hoje));

                        if(!somenteCalcular){
                            getMovProdutoDAO().incluirSemCommit(novoProdutoAnuidade);
                            getMovProdutoParcela().incluir(new MovProdutoParcelaVO(novoProdutoAnuidade.getCodigo(), novaParcelaAnuidade.getCodigo()));
                        }
                    }

                    novasParcelasCancelamento.add(novaParcelaAnuidade);
                }
            }


            parcela.setSituacao("CA");
            parcela.setDescricao(parcela.getDescricao() + descricaoParaIncluirParcelasCanceladas);
            if(!somenteCalcular){
                this.getMovParcelaDAO().alterarSemCommit(parcela);
            }

            for (MovProdutoParcelaVO produtoParcela:parcela.getMovProdutoParcelaVOs()) {

                produtoParcela.getMovProdutoVO().setSituacao("CA");
                produtoParcela.getMovProdutoVO().setDescricao(produtoParcela.getMovProdutoVO().getDescricao() + descricaoParaIncluirParcelasCanceladas);
                if(!somenteCalcular){
                    this.getMovProdutoDAO().alterarSemCommit(produtoParcela.getMovProdutoVO());
                }
            }
        }

        parcelas.addAll(novasParcelasCancelamento);

        if(!somenteCalcular) {
            getCon().commit();
        }


        return parcelas;
    }

    private MovProdutoParcela getMovProdutoParcela() throws Exception {
        return zwFacade.getMovProdutoParcela();
    }

    private MovProdutoInterfaceFacade getMovProdutoDAO() throws Exception {
            return zwFacade.getMovProduto();
    }

    private PlanoInterfaceFacade getPlanoDAO() throws Exception {
        return zwFacade.getPlano();
    }


    private void enviarEmailParcelasErro() throws Exception {
        if (!mapaParcelasComErro.isEmpty() || verificarRepescagem) {
            EmailNotificacao emailService = new EmailNotificacao();
            emailService.enviarEmailInconsistencias(mapaParcelasComErro, null, verificarRepescagem);
        }
    }

    private Map<Integer, EmpresaVO> mapaEmpresas() throws Exception {
        Map<Integer, EmpresaVO> mapa = new HashMap<>();
        List<EmpresaVO> empresas = getEmpresaDAO().consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (EmpresaVO empresa : empresas) {
            mapa.put(empresa.getCodigo(), empresa);
        }
        return mapa;
    }

    public void gerarParcelaAnuidade(ContratoVO contratoVO, UsuarioVO usuarioVO,
            Double valorAnuidade, Integer codEmpresa, Date dataRegistro, Date dataVencimento) throws Exception {
        MovParcelaVO parcela = new MovParcelaVO();
        parcela.setContrato(contratoVO);
        parcela.setDataRegistro(dataRegistro);
        parcela.setDataVencimento(dataVencimento);
        if(Calendario.menor(dataVencimento, Calendario.hoje())){
            parcela.setDataVencimento(Calendario.hoje());
        }
        parcela.setDescricao("ANUIDADE PLANO RECORRENTE");
        parcela.setResponsavel(usuarioVO);
        if (valorAnuidade == 0) {
            parcela.setSituacao("PG");
        } else {
            parcela.setSituacao("EA");
        }
        parcela.setValorBaseCalculo(valorAnuidade);
        parcela.setValorParcela(valorAnuidade);
        parcela.setEmpresa(contratoVO.getEmpresa());
        parcela.setPessoa(contratoVO.getPessoa());
        gerarMovProdutoAnuidade(parcela, contratoVO, usuarioVO, valorAnuidade, dataRegistro, codEmpresa);
        zwFacade.incluirMovParcelaSemCommit(parcela);
    }

    public void gerarMovProdutoAnuidade(MovParcelaVO parcelaVO, ContratoVO contratoVO, UsuarioVO usuarioVO,
            Double valorAnuidade, Date dataRegistro, Integer codEmpresa) throws Exception {
        MovProdutoVO movProdutoVO = zwFacade.gerarMovProdutoAnuidade(parcelaVO, contratoVO, usuarioVO, valorAnuidade, 0.0, valorAnuidade, dataRegistro, codEmpresa, getProdutoAnuidade());
        zwFacade.getMovProduto().incluirSemCommit(movProdutoVO);
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProdutoVO.getCodigo());
        movProdutoParcela.setValorPago(movProdutoVO.getTotalFinal());
        movProdutoParcela.setMovProdutoVO(movProdutoVO);
        parcelaVO.getMovProdutoParcelaVOs().add(movProdutoParcela);
    }

    private ProdutoVO getProdutoAnuidade() throws Exception {
        if (produtoAnuidade == null) {
            Produto produtoDAO = new Produto(zwFacade.getCon());
            produtoAnuidade = produtoDAO.criarOuConsultarExisteProdutoPorTipo(
                    "ANUIDADE PLANO RECORRENTE", "TA",
                    0.0);
            produtoDAO = null;
        }
        return produtoAnuidade;
    }

    public void processarAnuidadeContratoRecorrencia(final Date dia) {
        try {
            Map<Integer, EmpresaVO> mapaEmpresas = mapaEmpresas();
            ResultadoServicosVO resultadoAnuidade = new ResultadoServicosVO(ServicoEnum.ANUIDADE);
            for (Integer empresa : mapaEmpresas.keySet()) {

                List<ContratoRecorrenciaVO> listaContratosAnuidade =
                        getContratoRecorrenciaDAO().consultarContratosPrevistosAnuidade(
                        dia, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.getCon());
                EmpresaVO emp = mapaEmpresas.get(empresa);
                logar("Iniciando processamento Anuidades da empresa " + emp.getNome());
                for (ContratoRecorrenciaVO contratoRecorrenciaVO : listaContratosAnuidade) {
                    try {
                        if (contratoRecorrenciaVO.getMesVencimentoAnuidade() > 0
                                && contratoRecorrenciaVO.getDiaVencimentoAnuidade() > 0) {
                            //
                            Date dataVencimento = Calendario.getInstance(Calendario.getInstance().get(Calendar.YEAR),
                                    contratoRecorrenciaVO.getMesVencimentoAnuidade(),
                                    contratoRecorrenciaVO.getDiaVencimentoAnuidade()).getTime();
                            //
                            try {
                                zwFacade.getCon().setAutoCommit(false);
                                gerarParcelaAnuidade(
                                        contratoRecorrenciaVO.getContrato(), getUsuario(),
                                        contratoRecorrenciaVO.getValorAnuidade(),
                                        empresa, Calendario.hoje(), dataVencimento);
                                zwFacade.getCon().commit();
                            } catch (Exception e) {
                                zwFacade.getCon().rollback();
                                throw e; 
                            }  finally{
                                zwFacade.getCon().setAutoCommit(true);
                            }
                            logar(String.format("Processou Anuidade Contrato: %s, venc.: %s, valor: R$ %s, Aluno: %s ",
                                    contratoRecorrenciaVO.getContrato().getCodigo(),
                                    Calendario.getData(dataVencimento, "dd/MM/yyyy"),
                                    contratoRecorrenciaVO.getValorAnuidade(),
                                    contratoRecorrenciaVO.getContrato().getPessoa().getNome()));
                        }
                    } catch (Exception e) {
                        notificarThreadProcessada();
                        StringBuffer excecao = new StringBuffer(String.format("Erro ao processar anuidade do contrato: \"%s\" de \"%s\": %s",
                                contratoRecorrenciaVO.getContrato().getCodigo(),
                                contratoRecorrenciaVO.getContrato().getPessoa().getNome(),
                                e.getMessage()));
                        UteisValidacao.enfileirarEmail(new MsgTO(contratoRecorrenciaVO.getContrato().getEmpresa().getNome(),
                                "Anuidade Automática: Erro ao processar", excecao));
                        resultadoAnuidade.getResultado().put(excecao.toString());
                    }
                }
                logar("Terminou processamento Anuidades da empresa " + emp.getNome());
            }
            getResultadoServicosDAO().gravarResultado(resultadoAnuidade);
        } catch (Exception e) {
            logar("ERRO ao processar Anuidades: " + e.getMessage());
        } finally {
            ThreadEnviarEmail.finalmente();
        }
    }

    public void processarRenovacoesAutomaticas(final Date dia) {
        String key = "";
        Uteis.logar(null, "Iniciando RENOVAÇÃO AUTOMÁTICA do dia DO DIA -> " + Uteis.getDataComHora(dia));
        ResultadoServicosVO resultadoRenovacao = new ResultadoServicosVO(ServicoEnum.RENOVACAO);
        try {

            key = DAO.resolveKeyFromConnection(getCon());

            listaContratosVencendo = new ArrayList<>();
            List<Integer> idContratosVencendoRecorrente = new ArrayList<>();

            Map<Integer, EmpresaVO> mapaEmpresas = mapaEmpresas();
            Uteis.logar(null, " iniciando RENOVAÇÃO AUTOMÁTICA dos planos de recorrencia  do dia DO DIA -> " + Uteis.getDataComHora(dia));
            for (Integer empresa : mapaEmpresas.keySet()) {
                EmpresaVO empresaVO = mapaEmpresas.get(empresa);
                Integer carenciaRenovacao = UteisValidacao.emptyNumber(empresaVO.getCarenciaRenovacao())
                        ? this.configSistema.getCarenciaRenovacao()
                        : empresaVO.getCarenciaRenovacao();
                idContratosVencendoRecorrente.addAll(getContratoRecorrenciaDAO().consultarIdContratosVencendoRecorrente(dia, carenciaRenovacao,
                        empresaVO.getDiasRenovacaoAutomaticaAntecipada(),
                        empresaVO.getCodigo(),
                        this.getCon()));
            }
            if (!idContratosVencendoRecorrente.isEmpty()) {
                for (Integer idContratoRecorrencia : idContratosVencendoRecorrente) {
                    ContratoRecorrenciaVO contratoRecorrenciaVO = getContratoRecorrenciaDAO().consultarPorContrato(idContratoRecorrencia, Uteis.NIVELMONTARDADOS_TODOS, getCon());
                    try {
                        ThreadRenovacaoAutomatica threadRenovacaoAuto = new ThreadRenovacaoAutomatica(contratoRecorrenciaVO, this.getCon());
                        EmpresaVO empresaVO = mapaEmpresas.get(contratoRecorrenciaVO.getContrato().getEmpresa().getCodigo());
                        if (empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCarenciaRenovacao())) {
                            threadRenovacaoAuto.setCarenciaRenovacao(empresaVO.getCarenciaRenovacao());
                        } else {
                            threadRenovacaoAuto.setCarenciaRenovacao(this.configSistema.getCarenciaRenovacao());
                        }

                        if (empresaVO != null && (Calendario.menor(contratoRecorrenciaVO.getContrato().getPlano().getVigenciaAte(), Calendario.hoje()) && empresaVO.isBloquearRenovacaoAutomaticaPlanosForaDaVigencia())){
                            throw new Exception("O plano está fora de vigência e a empresa não permite renovação automática de planos fora de vigência.");
                        }

                        bloquearRenovacaoValidandoCategoria(empresaVO, contratoRecorrenciaVO.getContrato());

                        //se possui renovacao automatica pra acontecer nesse contrato
                        if (threadRenovacaoAuto.verificarDeveRenovar(empresaVO == null ? null : empresaVO.getDiasRenovacaoAutomaticaAntecipada())) {
                           Integer parcelasVencidas = 0;
                            if (contratoRecorrenciaVO.getContrato().getPlano().getPlanoRecorrencia().getNaoRenovarParcelaVencida()) {
                                parcelasVencidas = getMovParcelaDAO().consultarParcelasVencidasContratosRecorrencia(dia, contratoRecorrenciaVO.getContrato().getCodigo());
                            }
                            if (UteisValidacao.emptyNumber(parcelasVencidas)) {
                                threadRenovacaoAuto.run();
                                notificarThreadProcessada();
                            }
                        }

                    } catch (Exception e) {
                        notificarThreadProcessada();
                        StringBuffer excecao = new StringBuffer(String.format("Erro ao processar renovacão automática do contrato: \"%s\" de \"%s\": %s",
                                contratoRecorrenciaVO.getContrato().getCodigo(),
                                contratoRecorrenciaVO.getContrato().getPessoa().getNome(),
                                e.getMessage()));
                        UteisValidacao.enfileirarEmail(new MsgTO(contratoRecorrenciaVO.getContrato().getEmpresa().getNome(),
                                "Renovação Automática: Erro ao processar", excecao));
                        resultadoRenovacao.getResultado().put(excecao.toString());
                    }
                }
            }
            Uteis.logar(null, " Terminando RENOVAÇÃO AUTOMÁTICA dos planos de recorrencia  do dia DO DIA -> " + Uteis.getDataComHora(dia));
            Uteis.logar(null, " iniciando RENOVAÇÃO AUTOMÁTICA dos planos normais do dia DO DIA -> " + Uteis.getDataComHora(dia));

            //CONTRATOS QUE NÃO SÃO DE RECORRENCIA
            List<Integer> idContratosVencendoNaoRecorrente = new ArrayList<>();
            for (Integer empresa : mapaEmpresas.keySet()) {
                EmpresaVO empresaVO = mapaEmpresas.get(empresa);
                Integer carenciaRenovacao = UteisValidacao.emptyNumber(empresaVO.getCarenciaRenovacao())
                        ? this.configSistema.getCarenciaRenovacao()
                        : empresaVO.getCarenciaRenovacao();
                idContratosVencendoNaoRecorrente.addAll(getContratoDAO().consultarIdContratosVencendoNaoRecorrente(dia,
                        carenciaRenovacao,
                        empresaVO.getDiasRenovacaoAutomaticaAntecipada(),
                        empresaVO.getCodigo(),
                        this.getCon()));
            }
            if (!idContratosVencendoNaoRecorrente.isEmpty()) {
                for (Integer idContratoVO : idContratosVencendoNaoRecorrente) {
                    ContratoVO contratoVO = getContratoDAO().consultarPorCodigo(idContratoVO, Uteis.NIVELMONTARDADOS_TODOS);
                    try {
                        if(contratoVO.getPlano().isRenovarAutomaticamenteApenasCondicaoPagamentoRecorrencia() && !contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().isRecorrencia()){
                            continue;
                        }

                        List<LogVO> logs = getLogDAO().consultarPorNomeentidadeNomecampoChaveprimariaOperacao("CONTRATO",
                                "Erro", contratoVO.getCodigo().toString(), "RENOVAÇÃO AUTOMÁTICA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (logs.isEmpty()) {
                            ThreadRenovacaoAutomaticaContratoNormal threadRenovacaoAutoNormal = new ThreadRenovacaoAutomaticaContratoNormal(contratoVO,
                                    this.getCon());
                            EmpresaVO empresaVO = mapaEmpresas.get(contratoVO.getEmpresa().getCodigo());
                            if (empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCarenciaRenovacao())) {
                                threadRenovacaoAutoNormal.setCarenciaRenovacao(empresaVO.getCarenciaRenovacao());
                            } else {
                                threadRenovacaoAutoNormal.setCarenciaRenovacao(this.configSistema.getCarenciaRenovacao());
                            }
                            if (empresaVO != null && (Calendario.menor(contratoVO.getPlano().getVigenciaAte(), Calendario.hoje()) && empresaVO.isBloquearRenovacaoAutomaticaPlanosForaDaVigencia())){
                                throw new Exception("O plano está fora de vigência e a empresa não permite renovação automática de planos fora de vigência.");
                            }

                            bloquearRenovacaoValidandoCategoria(empresaVO, contratoVO);

                            //se possui renovacao automatica pra acontecer nesse contrato
                            if (threadRenovacaoAutoNormal.verificarDeveRenovar(empresaVO == null ? null : empresaVO.getDiasRenovacaoAutomaticaAntecipada())) {
                                Integer parcelasVencidas = 0;
                                if (threadRenovacaoAutoNormal.getContrato().getPlano().isNaoRenovarContratoParcelaVencidaAberto()) {
                                    parcelasVencidas = getMovParcelaDAO().consultarParcelasVencidasContrato(dia, threadRenovacaoAutoNormal.getContrato().getCodigo());
                                }
                                if (UteisValidacao.emptyNumber(parcelasVencidas)) {
                                    threadRenovacaoAutoNormal.run();
                                    notificarThreadProcessada();
                                }
                            }
                        }
                    } catch (Exception e) {
                        notificarThreadProcessada();
                        StringBuffer excecao = new StringBuffer(String.format("Erro ao processar renovacão automática do contrato: \"%s\" de \"%s\": %s",
                                new Object[]{
                            contratoVO.getCodigo(),
                            contratoVO.getPessoa().getNome(),
                            e.getMessage()}));
                        Uteis.logar(excecao, "erro");
                        resultadoRenovacao.getResultado().put(excecao.toString());
                    }
                }
            }
            Uteis.logar(null, " Terminando RENOVAÇÃO AUTOMÁTICA dos planos normais do dia DO DIA -> " + Uteis.getDataComHora(dia));
        } catch (Exception e) {
            Uteis.logar(null, "# Renovação Automática: Erro ao processar dia "+dia.toString()+" : " + e.getMessage());
            e.printStackTrace();
            StringBuffer excecao = new StringBuffer(String.format("Renovação Automática: Erro ao processar dia \"%s\": %s",
                    new Object[]{
                            dia.toString(),
                            e.getMessage()}));
            resultadoRenovacao.getResultado().put(excecao.toString());
            UteisValidacao.enfileirarEmail(new MsgTO(key, "Renovação Automática: Erro ao processar dia", new StringBuffer(e.getMessage()).append(" dia: ").append(dia.toString())));
        } finally {
            ThreadEnviarEmail.finalmente();
        }
        try {
            getResultadoServicosDAO().gravarResultado(resultadoRenovacao);
        } catch (Exception e){
        }
        Uteis.logar(null, "Terminando RENOVAÇÃO AUTOMÁTICA do dia DO DIA -> " + Uteis.getDataComHora(dia));
    }

    private void bloquearRenovacaoValidandoCategoria(EmpresaVO empresaVO, ContratoVO contrato) throws Exception {
        if (empresaVO != null && empresaVO.isBloquearRenovacaoAutomaticaPlanosForaCategoriaCliente()) {
            ClienteVO cliente = getClienteDAO().consultarPorCodigoPessoa(contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (contrato.getPlano().getRestringeVendaPorCategoria() && contrato.getPlano().getPlanoCategoriaVOs() != null && !contrato.getPlano().getPlanoCategoriaVOs().isEmpty()) {
                boolean possuiCategoria = false;
                if (cliente != null && cliente.getCategoria() != null && !UteisValidacao.emptyNumber(cliente.getCategoria().getCodigo())) {
                    for (PlanoCategoriaVO planoCategoriaVO : contrato.getPlano().getPlanoCategoriaVOs()) {
                        if (planoCategoriaVO.getCategoria().getCodigo().equals(cliente.getCategoria().getCodigo())) {
                            possuiCategoria = true;
                            break;
                        }
                    }
                }
                if (!possuiCategoria) {
                    String msgLog = "Parece que este plano é exclusivo para uma categoria específica de alunos, e este cliente não se enquadra nesses critérios. Não será possivel realizar a renovação automatica para o contrato: " + contrato.getCodigo();
                    LogVO logVO = getLogDAO().novo();
                    logVO.setPessoa(contrato.getPessoa().getCodigo());
                    logVO.setNomePessoa(contrato.getPessoa().getNome());
                    logVO.setDataAlteracao(Calendario.hoje());
                    logVO.setNomeEntidade("CONTRATO");
                    logVO.setNomeEntidadeDescricao("CONTRATO");
                    logVO.setResponsavelAlteracao("RECORRENCIA");
                    logVO.setNomeCampo("RENOVAÇÃO AUTOMÁTICA");
                    logVO.setCliente(cliente.getCodigo());
                    logVO.setOperacao("INCLUSÃO DE CONTRATO");
                    logVO.setValorCampoAlterado(msgLog);
                    getLogDAO().incluir(logVO);

                    throw new ServiceException(msgLog);
                }
            }
        }
    }

    public void setListaContratosVencendo(List<ContratoRecorrenciaVO> listaContratosVencendo) {
        this.listaContratosVencendo = listaContratosVencendo;
    }

    public void notificarThreadProcessada() {
        nrThreadsProcessadas++;
    }

    public void processarCancelamentoAutomaticoContratosForaRegimeRecorrencia(final Date dia) {
        String key = "";
        try {
            key = DAO.resolveKeyFromConnection(getCon());

            final List<MovParcelaVO> parcelasVencidas = getMovParcelaDAO().consultarParcelasParaCancelamentoAutomatico(dia);
            final Map<Integer, EmpresaVO> mapEmpresas = new HashMap<>();

            if (!parcelasVencidas.isEmpty()) {
                final CancelamentoContratoAutomaticoService cancelamentoAutomatico = new CancelamentoContratoAutomaticoService(getCon());

                for (MovParcelaVO movParcelaVO : parcelasVencidas) {
                    final ContratoVO contratoVO = getContratoDAO().consultarPorChavePrimaria(movParcelaVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_PARCELA);

                    EmpresaVO empresaContrato = mapEmpresas.get(contratoVO.getEmpresa().getCodigo());
                    if (empresaContrato == null) {
                        empresaContrato = getEmpresaDAO().consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        mapEmpresas.put(contratoVO.getEmpresa().getCodigo(), empresaContrato);
                    }
                    contratoVO.setEmpresa(empresaContrato);

                    boolean ehRenovavelAutomaticamente = (contratoVO.getRenovavelAutomaticamente() ||
                            (contratoVO.getPlano() != null && contratoVO.getPlano().getRenovavelAutomaticamente() != null && contratoVO.getPlano().getRenovavelAutomaticamente()));
                    boolean permiteCancelarContratosNaoRenovaveisForaRegimeRecorrencia = contratoVO.getEmpresa().isCancelarContratosNaoRenovaveisForaRecorrencia();

                    if (!ehRenovavelAutomaticamente && !permiteCancelarContratosNaoRenovaveisForaRegimeRecorrencia) {
                        continue;
                    }

                    try {
                        if (Calendario.menor(contratoVO.getVigenciaDe(), dia) && cancelamentoAutomatico.contratoPossuiParcelasParaCancelamentoAutomatico(contratoVO)) {
                            final int qtdParcelasSeguidas = contratoVO.getEmpresa().getQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia();
                            contratoVO.setDescricaoRegraCancelamento(RegraCancelamentoAutomaticoEnum.PARCELAS_VENCIDAS_SEGUIDAS.getDescricao().replace("XXX", String.valueOf(qtdParcelasSeguidas)));
                            cancelamentoAutomatico.cancelarAutomaticamente(contratoVO, 0.0);
                        }

                        if (Calendario.menor(contratoVO.getVigenciaDe(), dia) && cancelamentoAutomatico.contratoPossuiParcelaVencidaAposQtdDiasUteisTolerado(contratoVO, dia)) {
                            final int qtdDiasUteisAposVencimentoParaCancelar = contratoVO.getEmpresa().getQuantidadeDiasUteisAposVencimentoParaCancelarContrato();
                            contratoVO.setDescricaoRegraCancelamento(RegraCancelamentoAutomaticoEnum.PARCELA_VENCIDA_APOS_QTD_DIAS_UTEIS_TOLERADOS.getDescricao().replace("XXX", String.valueOf(qtdDiasUteisAposVencimentoParaCancelar)));
                            cancelamentoAutomatico.cancelarAutomaticamente(contratoVO, 0.0);
                        }

                    } catch (Exception e) {
                        StringBuffer excecao = new StringBuffer(String.format("Erro ao processar  Empresa: \"%s\", parcela \"%s\", do contrato: \"%s\" de \"%s\": %s",
                                        movParcelaVO.getEmpresa().getNome(),
                                        movParcelaVO.getCodigo(),
                                        contratoVO.getCodigo(),
                                        movParcelaVO.getContrato().getPessoa().getNome(),
                                        e.getMessage()));
                        UteisValidacao.enfileirarEmail(new MsgTO(movParcelaVO.getEmpresa().getNome(), "Contrato: Cancelamento automático", excecao));
                    }
                }
            }
        } catch (Exception e) {
            UteisValidacao.enfileirarEmail(new MsgTO(key, "Cancelamento Automático: Erro ao processar dia", new StringBuffer(e.getMessage()).append(" dia: ").append(dia.toString())));
        } finally {
            ThreadEnviarEmail.finalmente();
        }
    }

    public void processarCancelamentoAutomaticoContratosSemAssinatura(final Date dia) {
        String key = "";
        try {
            key = DAO.resolveKeyFromConnection(getCon());
            List<ContratoVO> contratosSemAssinatura = getContratoDAO().consultarContratosAtivosNaoAssinados(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Map<Integer, EmpresaVO> mapEmpresas = new HashMap<>();

            if (!contratosSemAssinatura.isEmpty()) {
                CancelamentoContratoAutomaticoService cancelamentoAutomatico = new CancelamentoContratoAutomaticoService(getCon());
                for (ContratoVO contratoVO : contratosSemAssinatura) {
                    EmpresaVO empresaContrato = mapEmpresas.get(contratoVO.getEmpresa().getCodigo());
                    if (empresaContrato == null) {
                        empresaContrato = getEmpresaDAO().consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        mapEmpresas.put(contratoVO.getEmpresa().getCodigo(), empresaContrato);
                    }
                    contratoVO.setEmpresa(empresaContrato);

                    try {
                        if (!UteisValidacao.emptyNumber(empresaContrato.getToleranciaCancelarContratosNaoAssinados())) {
                            Date dataTolerancia = Calendario.somarDias(contratoVO.getVigenciaDe(), empresaContrato.getToleranciaCancelarContratosNaoAssinados());
                            if (Calendario.maiorComHora(dia, dataTolerancia)) {
                                contratoVO.setDescricaoRegraCancelamento(RegraCancelamentoAutomaticoEnum.TOLERANCIA_NAO_ASSINOU_CONTRATO.getDescricao().replace("XXX", String.valueOf(empresaContrato.getToleranciaCancelarContratosNaoAssinados())));
                                cancelamentoAutomatico.cancelarAutomaticamente(contratoVO, 0.0);
                            }
                        }
                    } catch (Exception e) {
                        StringBuffer excecao = new StringBuffer(String.format("Erro ao processar  Empresa: \"%s\", contrato: \"%s\" de \"%s\": %s",
                                contratoVO.getEmpresa().getNome(),
                                contratoVO.getCodigo(),
                                contratoVO.getPessoa().getNome(),
                                e.getMessage()));
                        UteisValidacao.enfileirarEmail(new MsgTO(contratoVO.getEmpresa().getNome(), "Contrato: Cancelamento automático", excecao));
                    }
                }
            }
        } catch (Exception e) {
            UteisValidacao.enfileirarEmail(new MsgTO(key, "Cancelamento Automático: Erro ao processar dia", new StringBuffer(e.getMessage()).append(" dia: ").append(dia.toString())));
        } finally {
            ThreadEnviarEmail.finalmente();
        }
    }

    public ResultadoServicosInterfaceFacade getResultadoServicosDAO() throws Exception {
        return zwFacade.getResultadoServicos();
    }


    public void processarRenovacoesAutomaticasProduto(final Date dia) {
        String key = "";
        Uteis.logar(null, "Iniciando RENOVAÇÃO AUTOMÁTICA de PRODUTOS do dia DO DIA -> " + Uteis.getDataComHora(dia));
        ResultadoServicosVO resultadoRenovacao = new ResultadoServicosVO(ServicoEnum.RENOVACAO_PRODUTO);
        try {
            key = DAO.resolveKeyFromConnection(getCon());
            listaProdutosVencendo = new ArrayList<>();
            Map<Integer, EmpresaVO> mapaEmpresas = mapaEmpresas();

            for (Integer empresa : mapaEmpresas.keySet()) {
                EmpresaVO empresaVO = mapaEmpresas.get(empresa);
                listaProdutosVencendo.addAll(getMovProdutoDAO().consultarProdutoParaRenovacaoAutomatica(dia, empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_RENOVACAO_AUTOMATICA));
            }
            for (MovProdutoVO movProdutoVO: listaProdutosVencendo) {
                try {
                    ThreadRenovacaoAutomaticaProduto threadRenovacaoAuto = new ThreadRenovacaoAutomaticaProduto(movProdutoVO, this.getCon());
                    threadRenovacaoAuto.run();
                    notificarThreadProcessada();
                } catch (Exception e) {
                    notificarThreadProcessada();
                    StringBuffer excecao = new StringBuffer(String.format("Erro ao processar renovacão automática do  PRODUTO: \"%s\" de \"%s\": %s",
                            movProdutoVO.getCodigo(),
                            movProdutoVO.getPessoa().getNome(),
                            e.getMessage()));
                    UteisValidacao.enfileirarEmail(new MsgTO(movProdutoVO.getEmpresa().getNome(),
                            "Renovação Automática de PRODUTOS: Erro ao processar", excecao));
                    resultadoRenovacao.getResultado().put(excecao.toString());
                }
            }
        } catch (Exception e) {
            Uteis.logar(null, "# Renovação Automática de PRODUTOS: Erro ao processar dia "+dia.toString()+" : " + e.getMessage());
            e.printStackTrace();
            StringBuffer excecao = new StringBuffer(String.format("Renovação Automática de PRODUTOS: Erro ao processar dia \"%s\": %s",
                    new Object[]{
                            dia.toString(),
                            e.getMessage()}));
            resultadoRenovacao.getResultado().put(excecao.toString());
            UteisValidacao.enfileirarEmail(new MsgTO(key, "Renovação Automática de PRODUTOS: Erro ao processar dia", new StringBuffer(e.getMessage()).append(" dia: ").append(dia.toString())));
        } finally {
            ThreadEnviarEmail.finalmente();
        }
        try {
            getResultadoServicosDAO().gravarResultado(resultadoRenovacao);
        } catch (Exception e){
        }
        Uteis.logar(null, "Terminando RENOVAÇÃO AUTOMÁTICA de PRODUTOS do dia DO DIA -> " + Uteis.getDataComHora(dia));
    }


}
