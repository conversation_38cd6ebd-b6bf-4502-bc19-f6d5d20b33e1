package servicos.impl.vindi;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.VindiServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by johnys on 17/02/2017.
 */
public class VindiService extends AbstractCobrancaOnlineServiceComum implements VindiServiceInterface {
    /**
     * URL da api da vindi.
     */
    private String urlAPI;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Usuario usuarioDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioVindi;
    private String chaveAPI;

    public VindiService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.usuarioDAO = new Usuario(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioVindi = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoVindiVO(), TipoTransacaoEnum.VINDI, this.convenioVindi);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);

            if (clienteVO.isUtilizarResponsavelPagamento() &&
                    !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
                PessoaCPFTO pessoaCPFTO = transacaoDAO.obterDadosPessoaPagador(pessoa, false, true);

                //usar CPF do responsável
                pessoa.setCfp(pessoaCPFTO.getCpfResponsavel());
            }

            verificarAlteracaoPessoa(pessoa, transacao);
            verificarAutorizacaoCobranca(pessoa, transacao);

            JSONObject parametrosPagamento = criarParametrosPagamento(transacao, dadosCartao, pessoa);
            transacao.setParamsEnvio(removerDadosSigilososEnvio(parametrosPagamento));
            transacaoDAO.alterar(transacao);

            validarDadosTransacao(transacao, dadosCartao);

            String retorno = executarRequestVindi(parametrosPagamento.toString(), "/bills", ExecuteRequestHttpService.METODO_POST);
            processarRetorno(transacao, retorno, pessoa);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                repetirConsultarTransacao(3, transacao);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
            return transacao;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private void popularInformacoes() {
        if (this.convenioVindi != null) {
            this.chaveAPI = this.convenioVindi.getCodigoAutenticacao01();

            if (this.convenioVindi.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiVindiProducao);
            } else {
                this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiVindiSandbox);
            }
        }
    }

    private String removerDadosSigilososEnvio(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        if (parametrosPagamento.has("payment_profile")) {
            JSONObject paymentProfile = parametrosPagamento.getJSONObject("payment_profile");
            if (paymentProfile.has("card_number")) {
                paymentProfile.put("card_number", APF.getCartaoMascarado(paymentProfile.getString("card_number")));
            }
            if (paymentProfile.has("card_cvv")) {
                paymentProfile.put("card_cvv", "***");
            }
        }
        return parametrosPagamento.toString();
    }

    public void repetirConsultarTransacao(int qtd, TransacaoVO transacao) throws Exception {
        if (qtd > 0) {
            consultarSituacaoCobrancaTransacao(transacao);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                Thread.sleep(1000);
                repetirConsultarTransacao(qtd - 1, transacao);
            }
        }
    }

    @Override
    public String consultarTransacao(Integer id) throws Exception {
        return executarRequestVindi(null, "/bills/" + id, ExecuteRequestHttpService.METODO_GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para Vindi.");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        repetirConsultarTransacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {

        // atualiza situação da transação, caso ela esteja aguardando
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            verificarAlteracoesTransacao(transacao);

            //se não houve alterações então pode cancelar a fatura
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                realizarCancelamentoTransacaoPendente(transacao);
                new Transacao(getCon()).alterar(transacao);
                return transacao;
            }
        }

        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) || transacao.isIgnorarSituacaoCancelamento()) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao) throws Exception {
        // tentar cancelar uma transação pendente (azul)
        realizarCancelamentoTransacaoPendente(transacao);
        return transacao;
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        SituacaoTransacaoEnum situacaoTransacaoEnumAntesCancelar = transacaoVO.getSituacao();
        estornarCharge(transacaoVO);
        String retorno = executarRequestVindi(null, "/bills/" + transacaoVO.getCodigoExterno(), ExecuteRequestHttpService.METODO_DELETE);
        processarRetornoCancelamento(transacaoVO, retorno);

        if (estornarRecibo && !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento()) &&
                (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) || transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA))) {

            estornarRecibo(transacaoVO, estornarRecibo);

            //caso tenha realizado o estorno e a transação ficou como ESTORNADA.
            //se ESTORNOU O Recibo fica como cancelada
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA) &&
                    UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);

        cancelarParcelaMultaJuros(transacaoVO, situacaoTransacaoEnumAntesCancelar);
    }

    private void realizarCancelamentoTransacaoPendente(TransacaoVO transacaoVO) throws Exception {
        //cancelar a fatura somente, não precisa estornar a charge (cobrança) pois ela ainda não ocorreu
        String retorno = executarRequestVindi(null, "/bills/" + transacaoVO.getCodigoExterno(), ExecuteRequestHttpService.METODO_DELETE);
        processarRetornoCancelamentoTransacaoPendente(transacaoVO, retorno);
    }

    private void cancelarParcelaMultaJuros(TransacaoVO transacaoVO, SituacaoTransacaoEnum situacaoTransacaoEnumAntesCancelar) {
        //Cancelar MovProduto e MovParcela de Multa e Juros da Transação VINDI, apenas de Status APROVADA (AZUL ESCURO)
        MovProdutoParcela movProdutoParcelaDAO = null;
        MovParcela movParcelaDAO = null;
        MovProduto movProdutoDAO = null;
        Log logDAO = null;
        try {
            if(situacaoTransacaoEnumAntesCancelar.equals(SituacaoTransacaoEnum.APROVADA)){
                movProdutoParcelaDAO = new MovProdutoParcela(getCon());
                movParcelaDAO = new MovParcela(getCon());
                movProdutoDAO = new MovProduto(getCon());
                logDAO = new Log(getCon());

                for(MovParcelaVO movParcelaVO: transacaoVO.getListaParcelas()){
                    List<MovProdutoParcelaVO> listaMovProdutoParcelaVO = movProdutoParcelaDAO.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for(MovProdutoParcelaVO movProdutoParcelaVO: listaMovProdutoParcelaVO){
                        if(!UteisValidacao.emptyNumber(movProdutoParcelaVO.getMovParcelaOriginalMultaJuros().getCodigo())){
                            movParcelaDAO.excluir(movParcelaVO);
                            MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(movProdutoParcelaVO.getMovProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            movProdutoDAO.excluir(movProdutoVO);
                            movProdutoParcelaDAO.excluir(movProdutoParcelaVO);

                            gravarLogCancelamentoTransacao(logDAO, movParcelaVO, transacaoVO);
                        }
                    }
                }
            }
        } catch (Exception ex){
            Uteis.logarDebug("Não foi possível excluir MovProduto ou MovParcela no Cancelamento da Transação APROVADA VINDI, com o ID: " + transacaoVO.getCodigo());
        } finally {
            movProdutoParcelaDAO = null;
            movParcelaDAO = null;
            movProdutoDAO = null;
            logDAO = null;
        }
    }

    private static void gravarLogCancelamentoTransacao(Log logDAO, MovParcelaVO movParcelaVO, TransacaoVO transacaoVO) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(movParcelaVO.getCodigo().toString());
        obj.setNomeEntidade("MOVPARCELA");
        obj.setNomeEntidadeDescricao("parcela");
        obj.setOperacao("CANCELAMENTO - PARCELA");
        obj.setResponsavelAlteracao("ADMINISTRADOR");
        obj.setNomeCampo("TODOS");

        String pessoaPagador = "NULL";
        if (movParcelaVO.getPessoa() != null && movParcelaVO.getPessoa().getCodigo() != 0) {
            pessoaPagador = movParcelaVO.getPessoa().getCodigo().toString();
            obj.setPessoa(movParcelaVO.getPessoa().getCodigo());
        } else {
            obj.setPessoa(0);
        }

        obj.setValorCampoAnterior("Situacao= " + "Em Aberto" + "\n\r ");
        obj.setValorCampoAlterado("--------------------------------------\n\r");
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + " \n\rCódigo da Parcela = " + movParcelaVO.getCodigo() + "\n\r" + ", Data Vencimento = " + movParcelaVO.getDataVencimento_Apresentar()
                + "\n\r" + ", Valor Total = R$ " + Uteis.getDoubleFormatado(movParcelaVO.getValorParcela()) + "\n\r"
                + ", Codigo Pessoa do Cliente = " + pessoaPagador + "\n\r" + ", Responsável Lançamento = " + "ADMINISTRADOR - VIA TRANSACAO-CANCELAMENTO" + "\n\r"
                + ", Código Transação Origem Cancelamento = " + transacaoVO.getCodigo() + ", Situacao Parcela= Cancelado\n\r ");

        logDAO.incluirSemCommit(obj);
    }

    private void estornarCharge(TransacaoVO transacaoVO) {
        try {
            JSONObject jsonObject = new JSONObject(transacaoVO.getParamsResposta());
            JSONObject bill = jsonObject.getJSONObject("bill");
            JSONArray charges = bill.getJSONArray("charges");
            JSONObject charge = charges.getJSONObject(0);
            Integer idCharge = charge.optInt("id");

            if (UteisValidacao.emptyNumber(idCharge)) {
                return;
            }

            JSONObject body = new JSONObject();
            body.put("cancel_bill", true);

            String retorno = executarRequestVindi(body.toString(), "/charges/" + idCharge + "/refund", ExecuteRequestHttpService.METODO_POST);
            incluirHistoricoRetornoTransacao(transacaoVO, retorno, "estornarCharge");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retorno) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retorno, "processarRetornoCancelamento");
        transacaoVO.setResultadoCancelamento(retorno);
        try {
            JSONObject retornoCancelamento = new JSONObject(retorno);
            JSONObject charge = retornoCancelamento.getJSONObject("charge");
            JSONObject lastTransaction = charge.getJSONObject("last_transaction");
            String status = lastTransaction.getString("status");
            if (status.equalsIgnoreCase("waiting") || status.equalsIgnoreCase("processing")) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.ESTORNADA);
                transacaoVO.setAguardandoConfirmacao(true);
            } else {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacaoVO.setAguardandoConfirmacao(false);
            }
            realizarCancelamentoFatura(transacaoVO, true);
        } catch (Exception ex) {
            ex.printStackTrace();
            consultarSituacaoCobrancaTransacao(transacaoVO);
        }
    }

    private void processarRetornoCancelamentoTransacaoPendente(TransacaoVO transacaoVO, String retorno) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retorno, "processarRetornoCancelamentoTransacaoPendente");
        transacaoVO.setResultadoCancelamento(retorno);
        try {
            JSONObject retornoCancelamento = new JSONObject(retorno);
            JSONObject bill = retornoCancelamento.getJSONObject("bill");
            String status = bill.getString("status");
            if (status.equalsIgnoreCase("waiting") || status.equalsIgnoreCase("processing")) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.ESTORNADA);
                transacaoVO.setAguardandoConfirmacao(true);
            } else {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacaoVO.setAguardandoConfirmacao(false);
                transacaoVO.setProximaTentativa(null);
                new Transacao(getCon()).alterarProximaTentativaNull(transacaoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno, PessoaVO pessoa) throws Exception {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        JSONObject retornoJSON = new JSONObject(retorno);
        if (retornoJSON.has("errors")) {

            if (UteisValidacao.emptyString(transacao.getParamsResposta())) {
                transacao.setParamsResposta(retorno);
                transacao.setAtualizarParamsResposta(true);
            }


            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.NENHUMA)) {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }

            String msg = retornoJSON.getJSONArray("errors").getJSONObject(0).optString("message");
            if (!UteisValidacao.emptyString(msg)) {
                throw new Exception(msg);
            }


            try {
                if (transacao.getResultadoRequisicao().contains("Recurso não encontrado: Customer")
                        && transacao.getResultadoRequisicao().contains("\"id\":\"not_found\"")
                        && pessoa != null) { //isso pode acontecer com troca de empresas
                    pessoa.setIdVindi(null);
                    new Pessoa(getCon()).alterarIdVindi(pessoa);
                }
            } catch (Exception ignored) {
            }
        } else {
            transacao.setParamsResposta(retorno);
            transacao.setAtualizarParamsResposta(true);
            JSONObject bill = retornoJSON.getJSONObject("bill");

            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                transacao.setCodigoExterno(Integer.valueOf(bill.getInt("id")).toString());
            }

            JSONArray charges = bill.getJSONArray("charges");
            JSONObject charge = charges.getJSONObject(0);


            try {
                String dataCobrancaString = charge.getString("paid_at");
                Date dataCobranca = Uteis.getDate(dataCobrancaString, "yyyy-MM-dd'T'HH:mm:ss");
                transacao.setDataCobranca(dataCobranca);
            } catch (Exception ignored) {//fatura fica pendente e nao tem nenhuma tentativa de cobrança
            }

            try { //IDENTIFICAR SE O PAGAMENTO FOI EM DINHEIRO PELO PORTAL DA VINDI
                JSONObject payment_method = charge.getJSONObject("payment_method");
                String cash = payment_method.getString("code");
                if (cash.toLowerCase().equals("cash")) {
                    transacao.setPagamentoDinheiro(true);
                }
            } catch (Exception ignored) {
            }

            String statusLastTransaction = "";
            String statusLastTransactionType = "";
            try { //IDENTIFICAR A OPERADORA DO CARTAO
                JSONObject lastTransaction = charge.getJSONObject("last_transaction");
                statusLastTransaction = lastTransaction.optString("status");
                statusLastTransactionType = lastTransaction.optString("transaction_type");
                JSONObject payment_profile = lastTransaction.getJSONObject("payment_profile");
                JSONObject payment_company = payment_profile.getJSONObject("payment_company");
                String name = payment_company.getString("name");
                if (!UteisValidacao.emptyString(name)) {
                    for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                        String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                        String operadoraPagamento = name.toUpperCase().replaceAll(" ", "");
                        if (operadoraEnum.equals(operadoraPagamento)) {
                            transacao.setBandeiraPagamento(ope);
                            break;
                        }
                    }
                } else {
                    transacao.setBandeiraPagamento(null);
                }
            } catch (Exception ignored) {
                transacao.setBandeiraPagamento(null);
            }

            boolean temRetentativa = true;
            try {
                //Data da próxima tentativa automática de cobrança
//                Integer nrTentativasAutomaticas = charge.getInt("attempt_count");
                String next_attempt = charge.optString("next_attempt");

                boolean next_attempt_null = false;
                try {
                    charge.get("next_attempt");
                    next_attempt_null = charge.isNull("next_attempt");
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                if (next_attempt_null) {
                    next_attempt = null;
                } else {
                    if (!UteisValidacao.emptyString(next_attempt)) {
                        Date dataProximaTentativa = Uteis.getDate(next_attempt, "yyyy-MM-dd'T'HH:mm:ss");
                        //se a data da proxima tentativa for anterior a data atual não tem mais próxima tentativa.
                        if (Calendario.menor(dataProximaTentativa, Calendario.hoje())) {
                            next_attempt = null;
                        }
                    }
                }

                if (!charge.isNull("last_transaction")) {
                    JSONObject lastTransactionJSON = charge.getJSONObject("last_transaction");
                    String lastTransactionStatus = lastTransactionJSON.optString("status");
                    if (lastTransactionStatus.equalsIgnoreCase("rejected") &&
                            UteisValidacao.emptyString(next_attempt)) {
                        temRetentativa = false;
                    }

                } else if (next_attempt == null) {
                    //não tem próxima tentativa e nã otem ultima tentativa
                    temRetentativa = false;
                }
            } catch (Exception ignored) {
            }

            String status = bill.optString("status");

            if (statusLastTransactionType.equalsIgnoreCase("refund") &&
                    (statusLastTransaction.equalsIgnoreCase("waiting") ||
                            statusLastTransaction.equalsIgnoreCase("processing"))) {
                transacao.setSituacao(SituacaoTransacaoEnum.ESTORNADA);
                transacao.setAguardandoConfirmacao(true);
            } else if (status.equalsIgnoreCase("paid")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if ((status.equalsIgnoreCase("pending") || status.equalsIgnoreCase("scheduled")) && temRetentativa) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("pending") && !temRetentativa) {
                //pode acontecer da vindi mandar status pending e mandar sem a data da próxima cobrança, então neste caso pra evitar erros, devemos cancelar a transação lá e colocar a transação como não aprovada (vermelha)
                try {
                    cancelarTransacao(transacao);
                    //quando chega aqui a situação da transação está como cancelada, mas mudar ela para não aprovada.
                    transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                } catch (Exception ex) {
                    transacao.setSituacao(SituacaoTransacaoEnum.APROVADA); //se der erro no cancelamento, continuar azul
                }
            } else if (status.equalsIgnoreCase("refund") && statusLastTransaction.equalsIgnoreCase("success")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacao.setAguardandoConfirmacao(false);
            } else if (status.equalsIgnoreCase("canceled")) {

                if (statusLastTransaction.equalsIgnoreCase("waiting") || statusLastTransaction.equalsIgnoreCase("processing")) {
                    transacao.setSituacao(SituacaoTransacaoEnum.ESTORNADA);
                    transacao.setAguardandoConfirmacao(true);
                } else {
                    transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                    transacao.setAguardandoConfirmacao(false);
                }
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            }
        }
    }

    private JSONObject criarParametrosPagamento(TransacaoVO transacaoVO, CartaoCreditoTO dadosCartao, PessoaVO pessoa) throws Exception {

        String identificador = "TRA" + transacaoVO.getCodigo();
        Uteis.logarDebug("IDENTIFICADOR VINDI: " + identificador);

        JSONObject pagamento = new JSONObject();
        pagamento.put("code", identificador);
        pagamento.put("customer_id", pessoa.getIdVindi());
        pagamento.put("payment_method_code", "credit_card");
        if (!UteisValidacao.emptyNumber(dadosCartao.getParcelas())) {
            pagamento.put("installments", dadosCartao.getParcelas());
        }

        pagamento.put("bill_items", criarParamentosItensPagamento(transacaoVO, dadosCartao));

        //enviar cartão para o cadastro do cliente na Vindi
        if (!UteisValidacao.emptyString(dadosCartao.getGatewayTokenVindi())) {
            Integer idPaymentProfile = vincularTokenClienteVindi(pessoa, dadosCartao.getGatewayTokenVindi());
            JSONObject payment = new JSONObject();
            payment.put("id", idPaymentProfile);
            pagamento.put("payment_profile", payment);
        } else if (!UteisValidacao.emptyString(dadosCartao.getNumero())) {
            pagamento.put("payment_profile", criarParametrosCartao(transacaoVO, dadosCartao, pessoa));
        }
        return pagamento;
    }

    public Integer vincularTokenClienteVindi(PessoaVO pessoaVO, String gatewayTokenVindi) throws Exception {
        try {
            JSONObject payments = new JSONObject();
            payments.put("gateway_token", gatewayTokenVindi);
            payments.put("customer_id", pessoaVO.getIdVindi());
            payments.put("payment_method_code", "credit_card");

            String retornoEnvio = executarRequestVindi(payments.toString(), "payment_profiles", ExecuteRequestHttpService.METODO_POST);
            JSONObject jsonResp = new JSONObject(retornoEnvio);
            Integer idVindiPaymentProfile = jsonResp.getJSONObject("payment_profile").optInt("id");
            if (UteisValidacao.emptyNumber(idVindiPaymentProfile)) {
                throw new Exception(retornoEnvio);
            }
            return idVindiPaymentProfile;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro cadastrar cartao Vindi: " + ex.getMessage());
        }
    }

    private JSONObject criarParametrosCartao(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoa) throws Exception {
        JSONObject dadosCartao = new JSONObject();

        String numeroCartaoPassando = "";
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            numeroCartaoPassando = cartaoCreditoTO.getNumero();
        }

        //salvar para ser apresentado no gestão de transação
        gravarOutrasInformacoes(numeroCartaoPassando, cartaoCreditoTO, transacaoVO);

        //verificar se o profile payment está ativo
        validarPaymentProfile(cartaoCreditoTO, pessoa);

        String idPaymentProfile;
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getTokenVindi())) {
            //caso já esteja informado o token.. utilizar..
            idPaymentProfile = cartaoCreditoTO.getTokenVindi();
        } else {
            idPaymentProfile = obterIdPaymentProfile(numeroCartaoPassando, cartaoCreditoTO, pessoa);
        }

        if (!UteisValidacao.emptyString(idPaymentProfile)) {

            dadosCartao.put("id", Integer.parseInt(idPaymentProfile));

        } else {

            String nomeCartao = Uteis.retirarAcentuacao(cartaoCreditoTO.getNomeTitular());
            if (!UteisValidacao.emptyString(nomeCartao)) {
                dadosCartao.put("holder_name", nomeCartao);
            }

            dadosCartao.put("card_expiration", cartaoCreditoTO.getValidadeMMYY(true));
            dadosCartao.put("payment_company_code", obterPayment_company_code(cartaoCreditoTO));
            dadosCartao.put("card_number", numeroCartaoPassando);
            dadosCartao.put("payment_method_code", "credit_card");

            if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
                dadosCartao.put("card_cvv", cartaoCreditoTO.getCodigoSeguranca());
            }

            try {
                String registry_code = Uteis.formatarCpfCnpj(cartaoCreditoTO.getCpfCnpjSomenteNumeros(), true);
                if (!UteisValidacao.emptyString(registry_code) && SuperVO.verificaCPF(registry_code)) {
                    dadosCartao.put("registry_code", registry_code);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return dadosCartao;
    }

    private void validarPaymentProfile(CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoa) {
        AutorizacaoCobrancaCliente autoCliDAO;
        try {
            //TokenVindi é o cód. externo da aut. de cobrança. Se o aluno tiver, então consultar na vindi pra saber se esse perfil ainda está ativo
            //Necessário verificar, pois o perfil de pagamento pode ser alterado direto no portal da Vindi
            if (!UteisValidacao.emptyString(cartaoCreditoTO.getTokenVindi())) {
                String retorno = executarRequestVindi(null, "/payment_profiles/" + cartaoCreditoTO.getTokenVindi(), ExecuteRequestHttpService.METODO_GET);
                JSONObject jsonRetorno = new JSONObject(retorno);
                String status = jsonRetorno.getJSONObject("payment_profile").optString("status");
                String idCustomer = jsonRetorno.getJSONObject("payment_profile").getJSONObject("customer").optString("id");
                if (idCustomer.equals(pessoa.getIdVindi().toString()) &&
                        !status.equalsIgnoreCase("active")) {
                    autoCliDAO = new AutorizacaoCobrancaCliente(getCon());
                    autoCliDAO.alterarCodigoExterno("", this.convenioVindi.getCodigo(), pessoa.getCodigo(), cartaoCreditoTO.getTokenVindi());
                    cartaoCreditoTO.setTokenVindi(null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            autoCliDAO = null;
        }
    }

    private String obterPayment_company_code(CartaoCreditoTO cartaoCreditoTO) {
        if (cartaoCreditoTO.getBand().equals(OperadorasExternasAprovaFacilEnum.AMEX)) {
            return "american_express";
        } else if (cartaoCreditoTO.getBand().equals(OperadorasExternasAprovaFacilEnum.DISCOVER)) {
            return OperadorasExternasAprovaFacilEnum.ELO.name().toLowerCase();
        } else {
            return cartaoCreditoTO.getBand().name().toLowerCase();
        }
    }

    private String obterIdPaymentProfile(String numeroCartaoPassando, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoa) {
        AutorizacaoCobrancaCliente autoDAO = null;
        try {
            autoDAO = new AutorizacaoCobrancaCliente(getCon());

            List<AutorizacaoCobrancaClienteVO> autorizacoesCadastradas = autoDAO.consultarPorPessoaTipoAutorizacao(pessoa.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            String numeroCartaoPassandoMascarado = APF.getCartaoMascarado(numeroCartaoPassando);

            //buscar a autorização de cobrança referente ao cartão que está sendo utilizado.
            AutorizacaoCobrancaClienteVO auto = null;
            if (!UteisValidacao.emptyList(autorizacoesCadastradas)) {
                for (AutorizacaoCobrancaClienteVO obj : autorizacoesCadastradas) {
                    if (obj.getCartaoMascarado().equalsIgnoreCase(numeroCartaoPassandoMascarado)) {
                        if (!UteisValidacao.emptyString(obj.getCodigoExterno())) {
                            return obj.getCodigoExterno();
                        } else {
                            auto = obj;
                            break;
                        }
                    }
                }
            }


            JSONObject payments = new JSONObject();
            payments.put("card_number", numeroCartaoPassando);
            payments.put("holder_name", Uteis.retirarAcentuacao(cartaoCreditoTO.getNomeTitular()));
            if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
                payments.put("card_cvv", cartaoCreditoTO.getCodigoSeguranca());
            }
            payments.put("card_expiration", cartaoCreditoTO.getValidadeMMYY(true));
            payments.put("payment_company_code", obterPayment_company_code(cartaoCreditoTO));
            payments.put("payment_method_code", "credit_card");
            payments.put("customer_id", pessoa.getIdVindi());

            Uteis.logar(null, "Vou cadastrar cartão vindi... " + numeroCartaoPassandoMascarado);

            String retornoEnvio = executarRequestVindi(payments.toString(), "payment_profiles", ExecuteRequestHttpService.METODO_POST);
            JSONObject jsonResp = new JSONObject(retornoEnvio);
            String idVindiPagamento = jsonResp.getJSONObject("payment_profile").optString("id");

            //tem autorização de cobrança.. então vou enviar o cartão para a vindi..
            if (auto != null && !UteisValidacao.emptyString(idVindiPagamento)) {
                auto.setCodigoExterno(idVindiPagamento);
                autoDAO.alterarCodigoExterno(auto);
                return auto.getCodigoExterno();
            } else {
                return idVindiPagamento;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            autoDAO = null;
        }
    }

    private JSONArray criarParamentosItensPagamento(TransacaoVO transacaoVO, CartaoCreditoTO dadosCartao) throws Exception {
        JSONArray itensPagamento = new JSONArray();

        String nomeEmpresa = this.convenioVindi.getEmpresa().getNome();
        if (UteisValidacao.emptyString(nomeEmpresa)) {
            Empresa empresaDAO = new Empresa(getCon());
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(dadosCartao.getEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            empresaDAO = null;
            nomeEmpresa = empresaVO.getNome();
        }

        Double valorTotal = dadosCartao.getValor();
        Double valorParcela = 0.0;
        for (MovParcelaVO mov : dadosCartao.getListaParcelas()) {
            JSONObject itemPagamento = new JSONObject();
            if (Uteis.arredondarForcando2CasasDecimais(valorTotal) > Uteis.arredondarForcando2CasasDecimais(mov.getValorParcela())) {
                valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal - mov.getValorParcela());
                valorParcela = mov.getValorParcela();
            } else {
                valorParcela = valorTotal;
                valorTotal = 0.0;
            }
            itemPagamento.put("amount", valorParcela);
            itemPagamento.put("description", Uteis.retirarAcentuacao(mov.getDescricao()));
            itemPagamento.put("product_id", getProductIdVindi(transacaoVO, nomeEmpresa));
            itensPagamento.put(itemPagamento);
            if (Uteis.arredondarForcando2CasasDecimais(valorTotal) <= 0.00) {
                break;
            }
        }
        return itensPagamento;
    }

    private void verificarAlteracaoPessoa(PessoaVO pessoa, TransacaoVO transacaoVO) throws Exception {
        if (dadosDesatualizados(pessoa)) {
            incluirPessoa(pessoa, transacaoVO);
        }
    }

    private void verificarAutorizacaoCobranca(PessoaVO pessoa, TransacaoVO transacaoVO) throws Exception {
        List<AutorizacaoCobrancaClienteVO> auto = new AutorizacaoCobrancaCliente(getCon()).consultarPorPessoaTipoAutorizacao(pessoa.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (AutorizacaoCobrancaClienteVO obj : auto) {
            verificarAutorizacaoCobranca(obj, pessoa, transacaoVO);
        }
    }

    private void verificarAutorizacaoCobranca(AutorizacaoCobrancaClienteVO auto, PessoaVO pessoa, TransacaoVO transacaoVO) throws Exception {
        if (auto != null && auto.getOperadoraCartao() == null && auto.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
            Uteis.logar(null, "Vou verificarAutorizacaoCobranca VINDI... Auto " + auto.getCodigo());
            StringBuilder url = new StringBuilder();
            url.append("customer_id=").append(pessoa.getIdVindi());
            url.append("&status=active");
            url.append("&type=PaymentProfile::CreditCard");
            String retorno = executarRequestVindi(null, "payment_profiles?" + url.toString(), ExecuteRequestHttpService.METODO_GET);
            incluirHistoricoRetornoTransacao(transacaoVO, retorno, "verificarAutorizacaoCobranca");
            JSONObject retornoJSON = new JSONObject(retorno);
            if (retornoJSON.has("payment_profiles") && retornoJSON.getJSONArray("payment_profiles").length() > 0) {
                String operadora = retornoJSON.getJSONArray("payment_profiles").getJSONObject(0).getJSONObject("payment_company").getString("code");
                auto.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(operadora.toUpperCase()));
                new AutorizacaoCobrancaCliente(getCon()).alterarOperadoraCartao(auto);
            }
        }
    }

    private Boolean dadosDesatualizados(PessoaVO pessoa) {
        try {
            Boolean dadosDesatualizados = UteisValidacao.emptyNumber(pessoa.getIdVindi());
            if (!dadosDesatualizados) {
                LogVO ultimoLog = this.logDAO.consultarUltimoLogPessoa(pessoa.getCodigo(), "CLIENTE", "PESSOA", "CLIENTE - EMPRESA");
                if (ultimoLog != null && pessoa.getDataAlteracaoVindi() != null) {
                    dadosDesatualizados = pessoa.getDataAlteracaoVindi().getTime() < ultimoLog.getDataAlteracao().getTime();
                } else {
                    dadosDesatualizados = true;
                }
            }
            return dadosDesatualizados;
        } catch (Exception ex) {
            ex.printStackTrace();
            return true;
        }
    }

    public void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO) throws Exception {
        if (!SuperVO.verificaCPF(pessoa.getCfp())) {
            throw new Exception("O CPF cadastrado para essa pessoa é inválido");
        }
        JSONObject pessoaJSON = criarPessoaJSON(pessoa, true);
        JSONObject resposta = null;
        if (UteisValidacao.emptyNumber(pessoa.getIdVindi())) {
            resposta = new JSONObject(executarRequestVindi(pessoaJSON.toString(), METODO_CUSTOMERS, ExecuteRequestHttpService.METODO_POST));
            incluirHistoricoRetornoTransacao(transacaoVO, resposta.toString(), "incluirPessoa1");
        } else {
            resposta = new JSONObject(executarRequestVindi(null, METODO_CUSTOMERS + "/" + pessoa.getIdVindi(), ExecuteRequestHttpService.METODO_GET));
            incluirHistoricoRetornoTransacao(transacaoVO, resposta.toString(), "incluirPessoa2");
            if (resposta.has("errors")) {
                pessoa.setIdVindi(null);
                pessoaJSON = criarPessoaJSON(pessoa, true);
                resposta = new JSONObject(executarRequestVindi(pessoaJSON.toString(), METODO_CUSTOMERS, ExecuteRequestHttpService.METODO_POST));
                incluirHistoricoRetornoTransacao(transacaoVO, resposta.toString(), "incluirPessoa3");
            }
        }
        if (resposta.has("errors") || isDadosDiferentes(resposta, pessoa)) {
            try {
                JSONObject respostaBusca = tentarBuscarPessoaParaAtualizar(pessoa, pessoaJSON, transacaoVO, false);
                if (respostaBusca == null) {
                    respostaBusca = tentarBuscarPessoaParaAtualizar(pessoa, pessoaJSON, transacaoVO, true);
                }

                if (respostaBusca == null) {
                    throw new ConsistirException("Falha ao inserir a pessoa na vindi");
                } else {
                    resposta = respostaBusca;
                }
            } catch (Exception e) {
                String msg = e.getMessage();
                Object errors = resposta.opt("errors");
                if (errors != null) {
                    msg = msg + " " + errors.toString();
                }
                throw new ConsistirException(msg);
            }

        }
        if (resposta.has("errors")) {
            throw new ConsistirException("Falha ao inserir a pessoa na vindi. " + resposta.get("errors").toString());
        } else {
            pessoa.setIdVindi(getIdVindi(resposta));
            new Pessoa(getCon()).alterarIdVindi(pessoa);
        }
    }

    private Integer getIdVindi(JSONObject resposta) {
        Integer id = null;
        if (resposta.has("id")) {
            id = resposta.getInt("id");
        } else if (resposta.has("customer")) {
            JSONObject customer = resposta.getJSONObject("customer");
            id = customer.getInt("id");
        }
        return id;
    }

    private JSONObject tentarBuscarPessoaParaAtualizar(PessoaVO pessoa, JSONObject pessoaJSON, TransacaoVO transacaoVO, boolean buscarUtilizandoCodigo) throws Exception {
        StringBuilder url = new StringBuilder(METODO_CUSTOMERS).append("?");
        if (!UteisValidacao.emptyNumber(pessoa.getIdVindi())) {
            url.append("query=id:").append(pessoa.getIdVindi());
        } else if (!UteisValidacao.emptyString(pessoa.getCfp()) && !buscarUtilizandoCodigo) {
            url.append("query=registry_code:").append(pessoa.getCfp().replace(".", "").replace("-", ""));
        } else {
            url.append("query=code:P").append(pessoa.getCodigo());
        }
        boolean encontrou = false;
        JSONObject retorno = new JSONObject(executarRequestVindi(null, url.toString(), ExecuteRequestHttpService.METODO_GET));
        incluirHistoricoRetornoTransacao(transacaoVO, retorno.toString(), "tentarBuscarPessoaParaAtualizar1");
        if (retorno.has("customers")) {
            JSONArray customers = retorno.getJSONArray("customers");
            if (customers.length() > 0) {

                if (buscarUtilizandoCodigo) {
                    //será validado se o nome é o mesmo
                    for (int i = 0; i < customers.length(); i++) {
                        JSONObject customer = customers.getJSONObject(i);
                        if (!UteisValidacao.emptyString(customer.optString("code")) && customer.optString("code").equals(pessoaJSON.optString("code")) &&
                                customer.optString("name").equalsIgnoreCase(pessoa.getNome())) {
                            JSONObject pessoaParaAtualizar = criarPessoaJSON(pessoa, false);
                            retorno = new JSONObject(executarRequestVindi(pessoaParaAtualizar.toString(), METODO_CUSTOMERS + "/" + customer.getInt("id"), ExecuteRequestHttpService.METODO_PUT));
                            incluirHistoricoRetornoTransacao(transacaoVO, retorno.toString(), "tentarBuscarPessoaParaAtualizarCode2");
                            encontrou = true;
                            break;
                        }
                    }
                } else {
                    for (int i = 0; i < customers.length(); i++) {
                        JSONObject customer = customers.getJSONObject(i);
                        if (!UteisValidacao.emptyString(customer.optString("code")) && customer.optString("code").equals(pessoaJSON.optString("code"))) {
                            JSONObject pessoaParaAtualizar = criarPessoaJSON(pessoa, false);
                            retorno = new JSONObject(executarRequestVindi(pessoaParaAtualizar.toString(), METODO_CUSTOMERS + "/" + customer.getInt("id"), ExecuteRequestHttpService.METODO_PUT));
                            incluirHistoricoRetornoTransacao(transacaoVO, retorno.toString(), "tentarBuscarPessoaParaAtualizar2");
                            encontrou = true;
                            break;
                        }
                    }
                }

                if (!encontrou) {//caso de importação onde o code pode não ser o informado pelo zw.
                    JSONObject customer = customers.getJSONObject(0);
                    JSONObject pessoaParaAtualizar = criarPessoaJSON(pessoa, false);
                    retorno = new JSONObject(executarRequestVindi(pessoaParaAtualizar.toString(), METODO_CUSTOMERS + "/" + customer.getInt("id"), ExecuteRequestHttpService.METODO_PUT));
                    incluirHistoricoRetornoTransacao(transacaoVO, retorno.toString(), "tentarBuscarPessoaParaAtualizar3");
                    encontrou = true;
                }
            }
        }
        if (!encontrou) {
            return null;
        }

        return retorno;
    }

    private String executarRequestVindi(String parametros, String metodo, String metodoHTTP) throws Exception {
        validarDadosConvenio();

        String path = this.urlAPI + "/" + metodo;
        Map<String, String> maps = new HashMap<String, String>();
        String credential = chaveAPI + ":"; //senha é vazia
        maps.put("Content-Type", "application/json");
        maps.put("Authorization", "Basic " + new String(new Base64().encode(credential.getBytes())));

        // Para não permitir que a quantidade de requisições por segundo seja atingida.
        // Para mais detalhes veja: https://atendimento.vindi.com.br/hc/pt-br/articles/204075034-Qual-o-limite-de-requisi%C3%A7%C3%B5es-da-plataforma-Vindi-
        Integer limiteRequisicoesPorMinuto = 120;
        Thread.sleep((limiteRequisicoesPorMinuto / 60) * 1000);

        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, maps, metodoHTTP, "UTF-8");
    }

    private void validarDadosConvenio() throws Exception {
        if (this.convenioVindi == null || UteisValidacao.emptyNumber(this.convenioVindi.getCodigo())) {
            throw new Exception("Convênio de cobrança não encontrado ou inativo.");
        }
        if (UteisValidacao.emptyString(this.chaveAPI)) {
            throw new Exception("Chave da API no convênio de cobrança não informada.");
        }
    }

    private JSONObject criarPessoaJSON(PessoaVO pessoa, boolean incluirPhones) {
        JSONObject pes = new JSONObject();
        pes.put("name", Uteis.retirarAcentuacao(pessoa.getNome().trim()));
        pes.put("code", "P" + pessoa.getCodigo());
        if (!UteisValidacao.emptyString(pessoa.getEmail())) {
            pes.put("email", pessoa.getEmail().trim());
        }
        if (!UteisValidacao.emptyString(pessoa.getCfp())) {
            pes.put("registry_code", pessoa.getCfp());
        }
        if (incluirPhones) {
            JSONArray phones = getTelefonesCelularesPessoa(pessoa);
            if (phones.length() > 0) {
                pes.put("phones", phones);
            }
        }
        if (!UteisValidacao.emptyNumber(pessoa.getIdVindi())) {
            pes.put("id", pessoa.getIdVindi());
        }
        return pes;
    }

    private JSONArray getTelefonesCelularesPessoa(PessoaVO pessoa) {
        JSONArray telefones = new JSONArray();
        if (pessoa.getTelefoneVOs() != null && !pessoa.getTelefoneVOs().isEmpty()) {
            for (TelefoneVO telefone : pessoa.getTelefoneVOs()) {
                if (!UteisValidacao.emptyString(telefone.getTipoTelefone()) && telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo())) {
                    telefones.put(criarTelefoneJSON(telefone));
                }
            }
        }
        return telefones;
    }

    private JSONObject criarTelefoneJSON(TelefoneVO telefone) {
        JSONObject tel = new JSONObject();
        tel.put("phone_type", telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo()) ? "mobile" : "landline");
        String numero = telefone.getNumero();
        numero = numero.replaceAll("[()]", "");
        tel.put("number", "55" + numero);
        return tel;
    }

    public Integer getProductIdVindi(TransacaoVO transacaoVO, String nomeEmpresa) throws Exception {
        Integer productIdVindi = null;
        if (this.convenioVindi != null) {
            if (!this.convenioVindi.getProps().containsKey(ConvenioCobrancaAttrEnum.ID_PRODUTO.name())) {
                cadastrarProdutoVindi(transacaoVO, nomeEmpresa);
            } else {
                alterarProdutoVindi(transacaoVO, nomeEmpresa);
            }
            productIdVindi = this.convenioVindi.getProps().containsKey(ConvenioCobrancaAttrEnum.ID_PRODUTO.name()) ? Integer.valueOf(this.convenioVindi.getProps().get(ConvenioCobrancaAttrEnum.ID_PRODUTO.name())) : null;
        }
        return productIdVindi;
    }

    private void cadastrarProdutoVindi(TransacaoVO transacaoVO, String nomeEmpresa) throws Exception {
        JSONObject produto = montarJSONProdutoVindi(nomeEmpresa);
        String retornoConsulta = executarRequestVindi(produto.toString(), "products/", ExecuteRequestHttpService.METODO_POST);
        incluirHistoricoRetornoTransacao(transacaoVO, retornoConsulta, "cadastrarProdutoVindi");
        JSONObject retorno = new JSONObject(retornoConsulta);
        if (retorno.has("errors")) {
            throw new ConsistirException("Falha ao cadastrar o produto na vindi.");
        } else {
            Integer codigo = retorno.getJSONObject("product").getInt("id");
            this.convenioVindi.getProps().put(ConvenioCobrancaAttrEnum.ID_PRODUTO.name(), codigo.toString());
            this.convenioVindi.setUsuarioVO(this.usuarioDAO.getUsuarioRecorrencia());
            this.convenioVindi.setValidarDados(false);
            this.convenioCobrancaDAO.alterar(this.convenioVindi);
        }
    }

    private void alterarProdutoVindi(TransacaoVO transacaoVO, String nomeEmpresa) throws Exception {
        String codigo = this.convenioVindi.getProps().get(ConvenioCobrancaAttrEnum.ID_PRODUTO.name());
        Integer codigoInt = Integer.parseInt(codigo);
        String retorno1 = executarRequestVindi(null, "products/" + codigoInt, ExecuteRequestHttpService.METODO_GET);
        incluirHistoricoRetornoTransacao(transacaoVO, retorno1, "alterarProdutoVindi1");
        JSONObject retorno = new JSONObject(retorno1);
        if (retorno.has("message") || retorno.has("errors")) {
            JSONArray lista = new JSONArray(retorno.get("errors").toString());
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                String id = obj.optString("id");
                String message = obj.optString("message");
                if (id.contains("not_found")) {
                    //Produto que temos não foi encontrado na vindi, então deve ser cadastrado um novo.
                    cadastrarProdutoVindi(transacaoVO, nomeEmpresa);
                    break;
                } else {
                    throw new ConsistirException("Falha ao buscar o produto na Vindi. <br/>Mensagem Vindi: " + message);
                }
            }
        } else {
            String name = retorno.getJSONObject("product").getString("name");
            String nomeEmpresaComparar = "";
            if (name.length() > 17) {
                nomeEmpresaComparar = name.substring(17);
            }
            if (name.contains("PACTO") || !nomeEmpresaComparar.equals(Uteis.retirarAcentuacao(nomeEmpresa))) {
                JSONObject produtoNovo = new JSONObject();
                produtoNovo.put("name", PropsService.getPropertyValue(PropsService.nomeProdutoPadraoVindi) + " " + Uteis.retirarAcentuacao(nomeEmpresa));
                produtoNovo.put("status", "active");
                produtoNovo.put("invoice", "always");
                String retorno2 = executarRequestVindi(produtoNovo.toString(), "products/" + codigoInt, ExecuteRequestHttpService.METODO_PUT);
                incluirHistoricoRetornoTransacao(transacaoVO, retorno2, "alterarProdutoVindi2");
                JSONObject retornoNovo = new JSONObject(retorno2);
                if (retornoNovo.has("errors") || retornoNovo.has("message")) {
                    throw new ConsistirException("Falha ao alterar o produto na vindi.");
                }
            }
        }
    }

    private JSONObject montarJSONProdutoVindi(String nomeEmpresa) {
        JSONObject produto = new JSONObject();
        produto.put("name", PropsService.getPropertyValue(PropsService.nomeProdutoPadraoVindi) + " " + Uteis.retirarAcentuacao(nomeEmpresa));
        produto.put("status", "active");
        JSONObject pricingSchema = new JSONObject();
        pricingSchema.put("price", 0.0);
        pricingSchema.put("schema_type", "per_unit");
        produto.put("pricing_schema", pricingSchema);
        return produto;
    }

    public String getChaveAPI() {
        return chaveAPI;
    }

    public void setChaveAPI(String chaveAPI) {
        this.chaveAPI = chaveAPI;
    }

    private void realizarCancelamentoFatura(TransacaoVO transacao, Boolean transacaoAceita) throws Exception {
        String retorno = executarRequestVindi(null, "/bills/" + transacao.getCodigoExterno(), ExecuteRequestHttpService.METODO_DELETE);
        processarRetornoCancelamentoFatura(transacao, retorno, transacaoAceita);
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoCancelamentoFatura(TransacaoVO transacao, String retorno, Boolean transacaoAceita) throws Exception {
        JSONObject retornoCancelamentoFatura = new JSONObject(retorno);
        if (transacaoAceita) {
            JSONObject retornoCancelamentoTransacao = new JSONObject(transacao.getResultadoCancelamento());
            retornoCancelamentoTransacao.remove("canFatura");
            retornoCancelamentoTransacao.append("canFatura", retornoCancelamentoFatura);
            transacao.setResultadoCancelamento(retornoCancelamentoTransacao.toString());
        } else {
            transacao.setResultadoCancelamento(retorno);
        }
        try {
            JSONObject bill = retornoCancelamentoFatura.getJSONObject("bill");
            String status = bill.getString("status");
            if (status.equalsIgnoreCase("waiting") || status.equalsIgnoreCase("processing")) {
                transacao.setSituacao(SituacaoTransacaoEnum.ESTORNADA);
                transacao.setAguardandoConfirmacao(true);
            } else if (status.equalsIgnoreCase("canceled")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacao.setAguardandoConfirmacao(false);
            }
        } catch (Exception e) {

        }
    }

    public void excluirPerfilPagamento(final int idProfile) throws Exception {
        try {
            executarRequestVindi(null, "/payment_profiles/" + idProfile, ExecuteRequestHttpService.METODO_DELETE);
        } catch (Exception e) {
            Uteis.logar(e, VindiService.class);
        }
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            String retorno = executarRequestVindi(null, "/bills/" + transacaoVO.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
            processarRetorno(transacaoVO, retorno, null);
        }
    }

    private boolean isDadosDiferentes(JSONObject resposta, PessoaVO pessoa) {
        try {
            if (resposta.has("errors")) {
                return false;
            }

            JSONObject dadosVindi = resposta;
            if (resposta.has("customer")) {
                dadosVindi = resposta.getJSONObject("customer");
            }

            String nomeVindi = dadosVindi.optString("name", "").trim();
            String emailVindi = dadosVindi.optString("email", "").trim();
            String cpfVindi = dadosVindi.optString("registry_code", "").trim();

            String nomeLocal = Uteis.retirarAcentuacao(pessoa.getNome()).trim();
            String emailLocal = (pessoa.getEmail() != null ? pessoa.getEmail() : "").trim();
            String cpfLocal = (pessoa.getCfp() != null ? pessoa.getCfp() : "").trim();

            boolean nomeDiferente = !nomeVindi.equalsIgnoreCase(nomeLocal);

            boolean emailDiferente = false;
            if (!UteisValidacao.emptyString(emailVindi) && !UteisValidacao.emptyString(emailLocal)) {
                emailDiferente = !emailVindi.equalsIgnoreCase(emailLocal);
            }

            boolean cpfDiferente = false;
            if (!UteisValidacao.emptyString(cpfVindi) && !UteisValidacao.emptyString(cpfLocal)) {
                String cpfVindiLimpo = cpfVindi.replaceAll("[^0-9]", "");
                String cpfLocalLimpo = cpfLocal.replaceAll("[^0-9]", "");
                cpfDiferente = !cpfVindiLimpo.equals(cpfLocalLimpo);
            }

            if (nomeDiferente || emailDiferente || cpfDiferente) {
                Uteis.logarDebug("Dados diferentes encontrados na VINDI:");
                Uteis.logarDebug("Nome VINDI: '" + nomeVindi + "' vs Local: '" + nomeLocal + "' - Diferente: " + nomeDiferente);
                Uteis.logarDebug("Email VINDI: '" + emailVindi + "' vs Local: '" + emailLocal + "' - Diferente: " + emailDiferente);
                Uteis.logarDebug("CPF VINDI: '" + cpfVindi + "' vs Local: '" + cpfLocal + "' - Diferente: " + cpfDiferente);
            }

            return nomeDiferente || emailDiferente || cpfDiferente;

        } catch (Exception e) {
            Uteis.logarDebug("Erro ao comparar dados VINDI: " + e.getMessage());
            return false;
        }
    }
}
