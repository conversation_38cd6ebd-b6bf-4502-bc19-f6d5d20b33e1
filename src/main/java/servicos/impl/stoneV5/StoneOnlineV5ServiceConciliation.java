package servicos.impl.stoneV5;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import negocio.facade.jdbc.financeiro.Transacao;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.stone.bean.StoneConciliationFinancialTransaction;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static negocio.comuns.utilitarias.Calendario.somarDias;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 20/01/2025
 */

public class StoneOnlineV5ServiceConciliation {
    ;

    public static void processarExtratosStoneV5(EmpresaVO empresa, ConvenioCobrancaVO convenio, Date reprocessarAPartirDe, Date reprocessarAte, boolean convenioPossuiVariasEmpresasConfiguradas,
                                                String chaveDaApi, Connection con) throws Exception {
        int dia = 1;
        int i = 0;
        if (reprocessarAPartirDe != null && Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte)) >= 0)
            dia = Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte));
        for (; i <= dia; i++) {
            Date dataConsulta = somarDias((reprocessarAPartirDe != null ? reprocessarAPartirDe : Calendario.ontem()), i);
            String arquivoProcessamentoTemp = "StoneOnlineServiceV5Conciliation:" + new SimpleDateFormat("yyyyMMdd").format(dataConsulta) + "-" + chaveDaApi;
            String retornoVendas = "";
            String retornoPagamentos = "";
            try {
                boolean arquivoJaProcessado = getFacade().getExtratoDiarioItem().arquivoProcessado(arquivoProcessamentoTemp);
                if (arquivoJaProcessado) {
                    Uteis.logarDebug("Arquivo já processado: " + arquivoProcessamentoTemp);
                    continue;
                }

                int tentativa = 0;
                boolean possuiDadosDeVendas = false;
                boolean possuiDadosDePagamentos = false;

                //faz no máximo 10 tentativas em caso de erro de request
                while (tentativa <= 10) {
                    try {
                        ++tentativa;
                        Uteis.logarDebug("Tentativa: " + tentativa + " | Buscando conciliação do dia: " +
                                Uteis.getData(dataConsulta) + " utilizando Chave da API: " + chaveDaApi +
                                (!UteisValidacao.emptyString(convenio.getDescricao()) ? " | CONVÊNIO: " + convenio.getDescricao() : ""));

                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

                        // Lá na API, existe alguns problemas nos filtros de data PARA CONSULTA DE VENDAS. Para contornar é necessário algumas ações:
                        // se você quer consultar o dia de hoje, então sempre precisa passar o date_since sendo o dia desejado e o data_until sempre sendo 2 dias seguintes em relação ao dia desejado.
                        // 2 dias seguintes pois os recebimentos que acontecem hoje dàs 21:00 às 23:59, são jogados para o dia seguinte por causa do horário timezone do servidor deles e os resultados do dia seguinte só sã filtrados se colocar + 1 dia também
                        // EX: data da consulta é dia 26/01, então ele pesquisa do dia 26/01 até o dia 28/01, e a API traz dados do dia 26 e 27.
                        // preciso do dia seguinte (27) no exemplo porque tem os registros que ficam errado mencionado no comentário anterior.
                        Date data_Ate_para_filtro_vendas = somarDias(dataConsulta, 2);
                        String endPointVendas = "/payables?created_since=" + sdf.format(dataConsulta) + "&created_until=" + sdf.format(data_Ate_para_filtro_vendas) + "&size=1000&page=1";


                        //POR PAGAMENTOS a pesquisa funciona somando somente o dia seguinte em relação a data de consulta
                        // EX: data da consulta é dia 26/01, então ele pesquisa do dia 26/01 até o dia 27/01, e a API traz dados somente do dia 26.
                        Date data_Ate_para_filtro_pagamentos = somarDias(dataConsulta, 1);
                        String endPointPagamentos = "/payables?payment_date_since=" + sdf.format(dataConsulta) + "&payment_date_until=" + sdf.format(data_Ate_para_filtro_pagamentos) + "&size=1000&page=1"; //não precisa do pagesize, ele buga a consulta

                        //Consulta de conciliação de Vendas (Faturamento)
                        retornoVendas = executarRequestStone_V5(endPointVendas, chaveDaApi, MetodoHttpEnum.GET);

                        //Consulta de conciliação de Pagamentos (Compensação)
                        retornoPagamentos = executarRequestStone_V5(endPointPagamentos, chaveDaApi, MetodoHttpEnum.GET);

                        possuiDadosDeVendas = validarSeRetornoPossuiDadosValidos(retornoVendas);
                        possuiDadosDePagamentos = validarSeRetornoPossuiDadosValidos(retornoPagamentos);

                        // chegou até aqui não deu erro na requisição
                        break;

                    } catch (Exception ex) {
                        ex.printStackTrace();
                        if (reprocessarAPartirDe == null) { //só aguardar quando é o processo automatico, reprocessamento não precisa aguardar
                            Uteis.logarDebug("Erro... Aguardar 5 segundos e tentar novamente extrato | " + ex.getMessage());
                            Thread.sleep(5000);
                        }
                    }
                }
                //VENDAS
                if (possuiDadosDeVendas && !UteisValidacao.emptyString(retornoVendas)) {
                    processarRetornoVendas(retornoVendas, convenio, empresa, arquivoProcessamentoTemp,
                            dataConsulta, convenioPossuiVariasEmpresasConfiguradas, chaveDaApi, con);
                }
                //PAGAMENTOS
                if (possuiDadosDePagamentos && !UteisValidacao.emptyString(retornoPagamentos)) {
                    processarRetornoPagamentos(retornoPagamentos, convenio, empresa, arquivoProcessamentoTemp,
                            dataConsulta, convenioPossuiVariasEmpresasConfiguradas, chaveDaApi, con);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao processar '" + arquivoProcessamentoTemp + "' | " + ex.getMessage());
                try {
                    notificarErro(convenio, empresa, ex, getFacade().getCliente().getCon());
                } catch (Exception e) {
                    ex.printStackTrace();
                }
            }
        }
    }

    private static String executarRequestStone_V5(String endPoint, String chaveDaApi, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlApiStone_V5) + endPoint;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("ServiceRefererName", PropsService.getPropertyValue(PropsService.serviceRefererNamePagarMePacto));
        //A chave da API é combinada com os dois pontos : para formar um base64-encoded string:
        String chaveAPI = chaveDaApi + ":";
        headers.put("Authorization", "Basic " + new String(new Base64().encode(chaveAPI.getBytes())));

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, null, metodoHttpEnum);
        return respostaHttpDTO.getResponse();

//        return retornoMockRecebiveis();
    }

    private static void processarRetornoVendas(String retornoVendas, ConvenioCobrancaVO convenio, EmpresaVO empresa,
                                               String arquivoProcessamentoTemp, Date dataConsulta, boolean convenioPossuiVariasEmpresasConfiguradas,
                                               String chaveDaApi, Connection con) throws Exception {

        JSONArray arrayItemsVendas = new JSONArray();
        if (UteisValidacao.emptyString(retornoVendas)) {
            return;
        } else {
            arrayItemsVendas = new JSONObject(retornoVendas).optJSONArray("data");
        }

        List<PayableStoneTO> listPayablesStoneTOVendas = new ArrayList<>();

        //Converter items Json para TO
        for (int i = 0; i < arrayItemsVendas.length(); i++) {
            JSONObject item = arrayItemsVendas.getJSONObject(i);
            if (itemEValidoEmRelacaoADataDaConsulta(item, dataConsulta)) {
                PayableStoneTO payableStoneTO = new PayableStoneTO(item, TipoConsultaPayablesStoneEnum.DATA_VENDA);
                listPayablesStoneTOVendas.add(payableStoneTO);
            }

        }

        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        boolean configuracaoConcilicarSemNumeroParcela = getFacade().getConfiguracaoSistema().isConciliarSemNumeroParcela();

        //Processamento por data de VENDA (CRIAÇÃO) -- > (VENDAS, CANCELAMENTO, CHARGEBACK)
        if (!UteisValidacao.emptyList(listPayablesStoneTOVendas)) {
            extratoDiarioItens.addAll(
                    processarPayablesDeVenda(listPayablesStoneTOVendas, convenio, chaveDaApi, empresa, arquivoProcessamentoTemp,
                            convenioPossuiVariasEmpresasConfiguradas, dataConsulta, configuracaoConcilicarSemNumeroParcela, retornoVendas, con));
        }

        Uteis.logarDebug("Conciliação Stone V5 - Capturou " + extratoDiarioItens.size() + " Itens");
        if (extratoDiarioItens.size() > 0) {
            getFacade().getExtratoDiarioItem().processarListaExtratoDiario(extratoDiarioItens, false, convenio);
        }
        try {
            preencherPessoaItemExtrato(extratoDiarioItens, getFacade().getCliente().getCon());
        } catch (Exception ignored) {
        }
    }

    private static List<ExtratoDiarioItemVO> processarPayablesDeVenda(List<PayableStoneTO> payables, ConvenioCobrancaVO convenio, String chaveDaApi, EmpresaVO empresa,
                                                                      String arquivoProcessamentoTemp, boolean convenioPossuiVariasEmpresasConfiguradas,
                                                                      Date dataConsulta, boolean isConciliarSemNumeroParcela, String props, Connection con) throws Exception {
        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();

        if (UteisValidacao.emptyList(payables)) {
            return new ArrayList<>();
        }

        //##AGRUPAMENTOS##
        List<PayableStoneTO> listaPayablesVendaTratadaAgrupada = processarAgrupamentoPayablesVenda(payables);

        List<PayableStoneTO> listaPayablesVendaProcessar = new ArrayList<>();
        List<PayableStoneTO> listaPayablesCancelamentoProcessar = new ArrayList<>();
        List<PayableStoneTO> listaPayablesChargebackProcessar = new ArrayList<>();

        //Distribuir nas listas corretas
        for (PayableStoneTO payable : listaPayablesVendaTratadaAgrupada) {
            //originator_model: Valores possíveis: refund e chargeback.
            if (payable.getOriginator_model() == null || payable.getOriginator_model().equals(OriginatorModelPayableStoneEnum.NENHUM)) {
                listaPayablesVendaProcessar.add(payable);
                continue;
            }
            if (payable.getOriginator_model() != null && payable.getOriginator_model().equals(OriginatorModelPayableStoneEnum.REFUND)) {
                listaPayablesCancelamentoProcessar.add(payable);
                continue;
            }
            if (payable.getOriginator_model() != null && payable.getOriginator_model().equals(OriginatorModelPayableStoneEnum.CHARGEBACK)) {
                listaPayablesChargebackProcessar.add(payable);
                continue;
            }
        }

        //VENDAS DA LISTA DE VENDA (POR FATURAMENTO)
        if (!UteisValidacao.emptyList(listaPayablesVendaProcessar)) {
            for (PayableStoneTO payable : listaPayablesVendaProcessar) {
                extratoDiarioItens.add(criarExtratoDiarioItem(convenio, chaveDaApi, empresa, arquivoProcessamentoTemp,
                        convenioPossuiVariasEmpresasConfiguradas, isConciliarSemNumeroParcela, props, con, payable, dataConsulta));
            }
        }
        //CANCELAMENTOS DA LISTA DE VENDA (POR FATURAMENTO)
        if (!UteisValidacao.emptyList(listaPayablesCancelamentoProcessar)) {
            for (PayableStoneTO payable : listaPayablesCancelamentoProcessar) {
                extratoDiarioItens.add(criarExtratoDiarioItemCancelamento(convenio, chaveDaApi, empresa, arquivoProcessamentoTemp,
                        convenioPossuiVariasEmpresasConfiguradas, props, con, payable, dataConsulta));
            }
        }

        return extratoDiarioItens;
    }

    //TODO muito cuidado ao mexer nesse método aqui | By: Rodrigo Estulano
    public static List<PayableStoneTO> processarAgrupamentoPayablesVenda(List<PayableStoneTO> payablesVenda) throws
            IllegalAccessException, InstantiationException {
        // Agrupar payables por gateway_id
        Map<String, List<PayableStoneTO>> mapa_gateway_id = new HashMap<>();
        for (PayableStoneTO payable : payablesVenda) {
            String gatewayId = String.valueOf(payable.getGateway_id());

            List<PayableStoneTO> payablesDoGatewayId = mapa_gateway_id.get(gatewayId);
            if (payablesDoGatewayId == null) {
                payablesDoGatewayId = new ArrayList<>();
                mapa_gateway_id.put(gatewayId, payablesDoGatewayId);
            }
            payablesDoGatewayId.add(payable);
        }

        // Lista final com os payables processados corretamente
        List<PayableStoneTO> listaFinal = new ArrayList<>();

        // Processar os grupos encontrados
        for (List<PayableStoneTO> payablesDoGatewayId : mapa_gateway_id.values()) {

            // **Ordena pelo número da parcela (installment)**, do menor para o maior
            payablesDoGatewayId.sort(Comparator.comparingInt(PayableStoneTO::getInstallment));

            // Contar o parcelamento
            Set<Integer> installments = new HashSet<>();
            boolean vendaSplitParcelada = false;
            for (PayableStoneTO payable : payablesDoGatewayId) {
                installments.add(payable.getInstallment());
                if (!UteisValidacao.emptyString(payable.getSplit_id()))
                    vendaSplitParcelada = true;
            }

            int totalParcelas = installments.size(); // Descobre em quantas vezes foi parcelado

            if (totalParcelas > 1 && !vendaSplitParcelada) { //Venda Parcelada sem aplit entra aqui...
                // Parcelamento ? NÃO AGRUPA, apenas adiciona a informação de totalParcelas e adiciona na lista
                for (PayableStoneTO payable : payablesDoGatewayId) {
                    payable.setTipoAgrupamento(TipoAgrupamentoPayableStoneEnum.VENDA_PARCELADA);
                    payable.setTotalInstallments(totalParcelas);
                    listaFinal.add(payable);
                }
            } else if (payablesDoGatewayId.size() > 1) { //Venda sem ser parcelada ou Venda Parcelada com split entra aqui...
                // **Filtrar recebimentos e estornos**
                List<PayableStoneTO> recebimentos = new ArrayList<>();
                List<PayableStoneTO> estornos = new ArrayList<>();

                for (PayableStoneTO payableStoneTO : payablesDoGatewayId) {
                    if (payableStoneTO.getAmount() >= 0) {
                        recebimentos.add(payableStoneTO);
                    } else {
                        estornos.add(payableStoneTO);
                    }
                }

                // **Agrupa os recebimentos positivos, se houver mais de um**
                if (recebimentos.size() > 1) {
                    PayableStoneTO agrupadoRecebimentos = (PayableStoneTO) recebimentos.get(0).getClone(true);
                    agrupadoRecebimentos.setAmount(0);
                    agrupadoRecebimentos.setAnticipation_fee(0);
                    agrupadoRecebimentos.setFee(0);
                    agrupadoRecebimentos.setInstallment(1);
                    agrupadoRecebimentos.setTotalInstallments(1);
                    agrupadoRecebimentos.setTipoAgrupamento(TipoAgrupamentoPayableStoneEnum.SPLIT);

                    for (PayableStoneTO payableStoneTO : recebimentos) {
                        agrupadoRecebimentos.setAmount(agrupadoRecebimentos.getAmount() + payableStoneTO.getAmount());
                        if (!UteisValidacao.emptyNumber(payableStoneTO.getAnticipation_fee()) && payableStoneTO.getAnticipation_fee() > -1) {
                            agrupadoRecebimentos.setAnticipation_fee(agrupadoRecebimentos.getAnticipation_fee() + payableStoneTO.getAnticipation_fee());
                        }
                        if (!UteisValidacao.emptyNumber(payableStoneTO.getFee()) && payableStoneTO.getFee() > -1) {
                            agrupadoRecebimentos.setFee(agrupadoRecebimentos.getFee() + payableStoneTO.getFee());
                        }
                    }
                    listaFinal.add(agrupadoRecebimentos);
                } else if (!UteisValidacao.emptyList(recebimentos) && recebimentos.size() == 1) { //tem só uma venda split e não é parcelada
                    recebimentos.get(0).setTotalInstallments(1);
                    listaFinal.add(recebimentos.get(0));
                }

                // **Agrupa os estornos negativos, se houver mais de um (SPLIT)**
                if (estornos.size() > 1) {
                    PayableStoneTO agrupadoEstornos = (PayableStoneTO) estornos.get(0).getClone(true);
                    agrupadoEstornos.setAmount(0);
                    agrupadoEstornos.setAnticipation_fee(0);
                    agrupadoEstornos.setFee(0);
                    agrupadoEstornos.setInstallment(1);
                    agrupadoEstornos.setTotalInstallments(1);
                    agrupadoEstornos.setTipoAgrupamento(TipoAgrupamentoPayableStoneEnum.SPLIT);

                    for (PayableStoneTO payableStoneTO : estornos) {
                        agrupadoEstornos.setAmount(agrupadoEstornos.getAmount() + payableStoneTO.getAmount());
                        if (!UteisValidacao.emptyNumber(payableStoneTO.getAnticipation_fee()) && payableStoneTO.getAnticipation_fee() > -1) {
                            agrupadoEstornos.setAnticipation_fee(agrupadoEstornos.getAnticipation_fee() + payableStoneTO.getAnticipation_fee());
                        }
                        if (!UteisValidacao.emptyNumber(payableStoneTO.getFee()) && payableStoneTO.getFee() > -1) {
                            agrupadoEstornos.setFee(agrupadoEstornos.getFee() + payableStoneTO.getFee());
                        }
                    }
                    listaFinal.add(agrupadoEstornos);
                } else if (!estornos.isEmpty()) {
                    listaFinal.add(estornos.get(0));
                }
            } else {
                // Venda SIMPLES ? Mantém como está
                PayableStoneTO unico = payablesDoGatewayId.get(0);
                unico.setTotalInstallments(1);
                unico.setTipoAgrupamento(TipoAgrupamentoPayableStoneEnum.VENDA_NORMAL);
                listaFinal.add(unico);
            }
        }
        return listaFinal;
    }

    private static void processarRetornoPagamentos(String retornoPagamentos, ConvenioCobrancaVO convenio, EmpresaVO empresa,
                                                   String arquivoProcessamentoTemp, Date dataConsulta, boolean convenioPossuiVariasEmpresasConfiguradas,
                                                   String chaveDaApi, Connection con) throws Exception {

        JSONArray arrayItemsPagamentos = new JSONArray();
        if (UteisValidacao.emptyString(retornoPagamentos)) {
            return;
        } else {
            arrayItemsPagamentos = new JSONObject(retornoPagamentos).optJSONArray("data");
        }

        List<PayableStoneTO> listPayablesStoneTOPagamentos = new ArrayList<>();

        //Converter items Json para TO
        for (int i = 0; i < arrayItemsPagamentos.length(); i++) {
            JSONObject item = arrayItemsPagamentos.getJSONObject(i);
            PayableStoneTO payableStoneTO = new PayableStoneTO(item, TipoConsultaPayablesStoneEnum.DATA_PAGAMENTO);
            listPayablesStoneTOPagamentos.add(payableStoneTO);
        }

        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        boolean configuracaoConcilicarSemNumeroParcela = getFacade().getConfiguracaoSistema().isConciliarSemNumeroParcela();

        //Processamento por data de PAGAMENTO -- > (VENDAS, CANCELAMENTO, CHARGEBACK)
        if (!UteisValidacao.emptyList(listPayablesStoneTOPagamentos)) {
            extratoDiarioItens.addAll(
                    processarPayablesDePagamento(listPayablesStoneTOPagamentos, convenio, chaveDaApi, empresa, arquivoProcessamentoTemp,
                            convenioPossuiVariasEmpresasConfiguradas, dataConsulta, configuracaoConcilicarSemNumeroParcela, retornoPagamentos, con));
        }

        Uteis.logarDebug("Conciliação Stone V5 - Capturou " + extratoDiarioItens.size() + " Itens");
        if (extratoDiarioItens.size() > 0) {
            getFacade().getExtratoDiarioItem().processarListaExtratoDiario(extratoDiarioItens, false, convenio);
        }
        try {
            preencherPessoaItemExtrato(extratoDiarioItens, getFacade().getCliente().getCon());
        } catch (Exception ignored) {
        }
    }

    private static List<ExtratoDiarioItemVO> processarPayablesDePagamento(List<PayableStoneTO> payables, ConvenioCobrancaVO convenio, String chaveDaApi, EmpresaVO empresa,
                                                                          String arquivoProcessamentoTemp, boolean convenioPossuiVariasEmpresasConfiguradas,
                                                                          Date dataConsulta, boolean isConciliarSemNumeroParcela, String props, Connection con) throws Exception {
        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();

        if (UteisValidacao.emptyList(payables)) {
            return new ArrayList<>();
        }

        //##AGRUPAMENTOS##
        List<PayableStoneTO> listaPayablesPagamentoTratadaAgrupada = processarAgrupamentoPayablesPagamento(payables);

        List<PayableStoneTO> listaPayablesPagamentoProcessar = new ArrayList<>();
        List<PayableStoneTO> listaPayablesCancelamentoProcessar = new ArrayList<>();
        List<PayableStoneTO> listaPayablesChargebackProcessar = new ArrayList<>();

        //Distribuir nas listas corretas
        for (PayableStoneTO payable : listaPayablesPagamentoTratadaAgrupada) {
            //originator_model: Valores possíveis: refund e chargeback.
            if (payable.getOriginator_model() == null || payable.getOriginator_model().equals(OriginatorModelPayableStoneEnum.NENHUM)) {
                listaPayablesPagamentoProcessar.add(payable);
                continue;
            }
            if (payable.getOriginator_model() != null && payable.getOriginator_model().equals(OriginatorModelPayableStoneEnum.REFUND)) {
                listaPayablesCancelamentoProcessar.add(payable);
                continue;
            }
            if (payable.getOriginator_model() != null && payable.getOriginator_model().equals(OriginatorModelPayableStoneEnum.CHARGEBACK)) {
                listaPayablesChargebackProcessar.add(payable);
                continue;
            }
        }

        //PAGAMENTOS DA LISTA DE PAGAMENTOS (POR COMPENSAÇÃO)
        if (!UteisValidacao.emptyList(listaPayablesPagamentoProcessar)) {
            for (PayableStoneTO payable : listaPayablesPagamentoProcessar) {
                extratoDiarioItens.add(criarExtratoDiarioItem(convenio, chaveDaApi, empresa, arquivoProcessamentoTemp,
                        convenioPossuiVariasEmpresasConfiguradas, isConciliarSemNumeroParcela, props, con, payable, dataConsulta));
            }
        }
        //CANCELAMENTOS DA LISTA DE PAGAMENTOS (POR COMPENSAÇÃO)
        if (!UteisValidacao.emptyList(listaPayablesCancelamentoProcessar)) {
            for (PayableStoneTO payable : listaPayablesCancelamentoProcessar) {
                extratoDiarioItens.add(criarExtratoDiarioItemCancelamento(convenio, chaveDaApi, empresa, arquivoProcessamentoTemp,
                        convenioPossuiVariasEmpresasConfiguradas, props, con, payable, dataConsulta));
            }
        }

        //CHARGEBACKS DA LISTA DE PAGAMENTOS (POR COMPENSAÇÃO)
        if (!UteisValidacao.emptyList(listaPayablesChargebackProcessar)) {
            List<ExtratoDiarioItemVO> itensChargeback = new ArrayList<>();
            for (PayableStoneTO payable : listaPayablesChargebackProcessar) {
                itensChargeback.add(criarExtratoDiarioItemChargeBack(convenio, chaveDaApi, empresa, arquivoProcessamentoTemp,
                        props, con, payable));
            }
            lancarContaAPagarOuReceberAutomaticaFinanceiro(convenio, empresa, itensChargeback, dataConsulta);
            extratoDiarioItens.addAll(itensChargeback);
        }

        return extratoDiarioItens;
    }

    //TODO muito cuidado ao mexer nesse método aqui | By: Rodrigo Estulano
    public static List<PayableStoneTO> processarAgrupamentoPayablesPagamento(List<PayableStoneTO> payablesPagamento) throws
            IllegalAccessException, InstantiationException {

        // Agrupar payables por gateway_id
        Map<String, List<PayableStoneTO>> mapa_gateway_id = new HashMap<>();

        for (PayableStoneTO payable : payablesPagamento) {
            String gatewayId = String.valueOf(payable.getGateway_id());

            List<PayableStoneTO> payablesDoGatewayId = mapa_gateway_id.get(gatewayId);
            if (payablesDoGatewayId == null) {
                payablesDoGatewayId = new ArrayList<>();
                mapa_gateway_id.put(gatewayId, payablesDoGatewayId);
            }
            payablesDoGatewayId.add(payable);
        }

        // Lista final com os payables processados corretamente
        List<PayableStoneTO> listaFinal = new ArrayList<>();

        // Processar os grupos encontrados
        for (List<PayableStoneTO> payablesDoGatewayId : mapa_gateway_id.values()) {
            // **Ordena pelo número da parcela (installment)**, do menor para o maior
            payablesDoGatewayId.sort(Comparator.comparingInt(PayableStoneTO::getInstallment));

            List<PayableStoneTO> recebimentos = new ArrayList<>();
            List<PayableStoneTO> estornos = new ArrayList<>();
            // **Separar recebimentos e estornos pelo Gateway_id**
            for (PayableStoneTO payableStoneTO : payablesDoGatewayId) {
                if (payableStoneTO.getAmount() >= 0) {
                    recebimentos.add(payableStoneTO);
                } else {
                    estornos.add(payableStoneTO);
                }
            }

            if (!UteisValidacao.emptyList(recebimentos)) {
                // Criar um novo mapa para armazenar os recebimentos por número da parcela
                Map<Integer, List<PayableStoneTO>> recebimentosPorParcela = new HashMap<>();

                // Agrupar os recebimentos pelo número da parcela
                for (PayableStoneTO recebimento : recebimentos) {
                    int installment = recebimento.getInstallment();
                    recebimentosPorParcela.computeIfAbsent(installment, k -> new ArrayList<>()).add(recebimento);
                }

                // **Agrupa os recebimentos positivos
                // Processar cada grupo de parcela separadamente pois pode vir parcelas antecipadas no mesmo gateway_id
                for (Map.Entry<Integer, List<PayableStoneTO>> entry : recebimentosPorParcela.entrySet()) {
                    int installment = entry.getKey();
                    List<PayableStoneTO> recebimentosDessaParcela = entry.getValue();

                    if (recebimentosDessaParcela.size() > 1) {
                        PayableStoneTO agrupadoRecebimentos = (PayableStoneTO) recebimentosDessaParcela.get(0).getClone(true);

                        agrupadoRecebimentos.setInstallment(installment);
                        agrupadoRecebimentos.setAmount(0);
                        agrupadoRecebimentos.setAnticipation_fee(0);
                        agrupadoRecebimentos.setFee(0);
                        agrupadoRecebimentos.setTotalInstallments(1);
                        agrupadoRecebimentos.setTipoAgrupamento(TipoAgrupamentoPayableStoneEnum.SPLIT);

                        for (PayableStoneTO payableStoneTO : recebimentosDessaParcela) {
                            agrupadoRecebimentos.setAmount(agrupadoRecebimentos.getAmount() + payableStoneTO.getAmount());
                            if (!UteisValidacao.emptyNumber(payableStoneTO.getAnticipation_fee()) && payableStoneTO.getAnticipation_fee() > 1) {
                                agrupadoRecebimentos.setAnticipation_fee(agrupadoRecebimentos.getAnticipation_fee() + payableStoneTO.getAnticipation_fee());
                            }
                            if (!UteisValidacao.emptyNumber(payableStoneTO.getFee()) && payableStoneTO.getFee() > -1) {
                                agrupadoRecebimentos.setFee(agrupadoRecebimentos.getFee() + payableStoneTO.getFee());
                            }
                        }
                        listaFinal.add(agrupadoRecebimentos);
                    } else {
                        // Caso haja apenas um recebimento para a parcela, adiciona diretamente
                        listaFinal.add(recebimentosDessaParcela.get(0));
                    }
                }
            }

                // **Agrupa os estornos negativos, se houver mais de um (SPLIT)**
                if (estornos.size() > 1) {
                    PayableStoneTO agrupadoEstornos = (PayableStoneTO) estornos.get(0).getClone(true);
                    agrupadoEstornos.setAmount(0);
                    agrupadoEstornos.setAnticipation_fee(0);
                    agrupadoEstornos.setFee(0);
                    agrupadoEstornos.setInstallment(1);
                    agrupadoEstornos.setTotalInstallments(1);
                    agrupadoEstornos.setTipoAgrupamento(TipoAgrupamentoPayableStoneEnum.SPLIT);

                for (PayableStoneTO payableStoneTO : estornos) {
                    agrupadoEstornos.setAmount(agrupadoEstornos.getAmount() + payableStoneTO.getAmount());
                    if (!UteisValidacao.emptyNumber(payableStoneTO.getAnticipation_fee()) && payableStoneTO.getAnticipation_fee() > -1) {
                        agrupadoEstornos.setAnticipation_fee(agrupadoEstornos.getAnticipation_fee() + payableStoneTO.getAnticipation_fee());
                    }
                    if (!UteisValidacao.emptyNumber(payableStoneTO.getFee()) && payableStoneTO.getFee() > -1) {
                        agrupadoEstornos.setFee(agrupadoEstornos.getFee() + payableStoneTO.getFee());
                    }
                }
                listaFinal.add(agrupadoEstornos);
            } else if (!estornos.isEmpty()) {
                listaFinal.add(estornos.get(0));
            }
        }
        return listaFinal;
    }

    public static boolean itemEValidoEmRelacaoADataDaConsulta(JSONObject jsonItem, Date dataConsulta) throws ParseException {
        //SOMENTE PARA TIPO CONCILIAÇÃO POR VENDAS (FATURAMENTO RECEBIDO)
        // Lá na API, existe alguns problemas nos filtros de data. Para contornar é necessário algumas ações:
        // se você quer consultar o dia de hoje, então sempre precisa passar o date_since sendo o dia desejado e o data_until sempre sendo 2 dias seguintes em relação ao dia desejado.
        // 2 dias seguintes pois os recebimentos que acontecem hoje dàs 21:00 às 23:59, são jogados para o dia seguinte por causa do horário timezone do servidor deles e os resultados do dia seguinte só sã filtrados se colocar + 1 dia também
        // Esse método aqui é pra garantir que entre os resulados corretamente com base na data consulta
        if (jsonItem.has("created_at") && !jsonItem.isNull("created_at")) {
            // Não remover o timezone daqui de jeito nenhum pois a data do servidor deles lá sempre fica +3 horas
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataPagamento = sdf.parse(jsonItem.getString("created_at"));
            if (Calendario.igual(dataPagamento, dataConsulta)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isCancelamentoOrChargeback(StoneConciliationFinancialTransaction transaction) {
        if (transaction.getStoneConciliationEvents().getTipoConciliacao() != null &&
                (transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.CHARGEBACK ||
                        transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.CANCELAMENTO ||
                        transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.ESTORNO_CHARGEBACK ||
                        transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.TAXA_CANCELAMENTO)) {
            return true;
        }
        return false;
    }

    private static void processarJSONCancellations(JSONObject conciliacaoJSON, String strFinTransA) {
        try {
            for (int i = 0; i < conciliacaoJSON.getJSONArray(strFinTransA).length(); i++) {
                JSONArray array = new JSONArray();
                if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).has("Cancellations") &&
                        conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).get("Cancellations") instanceof JSONObject) {
                    if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").get("Cancellation") instanceof JSONObject) {
                        JSONObject installmentSingle = conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").getJSONObject("Cancellation");
                        array = new JSONArray().put(installmentSingle);
                    } else if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").get("Cancellation") instanceof JSONArray) {
                        array = conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").getJSONArray("Cancellation");
                    }
                    conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).remove("Cancellations");
                    conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).put("Cancellations", array);
                } else {
                    conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).put("Cancellations", (Object) null);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void lancarContaAPagarOuReceberAutomaticaFinanceiro(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresa,
                                                                       List<ExtratoDiarioItemVO> itensChargeback, Date dataArquivo) {
        try {
            if (!convenioCobrancaVO.isStoneV5()) {
                return;
            }
            ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = getFacade().getConfiguracaoFinanceiro().consultar();
            if (!configuracaoFinanceiroVO.isCriarContaPagarAutomatico()) {
                return;
            }
            for (ExtratoDiarioItemVO itemVO : itensChargeback) {
                itemVO.setDataArquivo(dataArquivo);
                getFacade().getExtratoDiarioItem().criarContaPagarOuReceberCancelamentoStone(convenioCobrancaVO, itemVO, empresa, configuracaoFinanceiroVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void preencherPessoaItemExtrato(List<ExtratoDiarioItemVO> extratos, Connection con) throws Exception {
        ExtratoDiarioItem extratoDiarioItemDAO;
        try {
            extratoDiarioItemDAO = new ExtratoDiarioItem(con);

            for (ExtratoDiarioItemVO extrato : extratos) {
                try {
                    //buscar por movpagamento que já existe no item do extrato
                    if (extrato.getMovPagamento() != null && extrato.getMovPagamento().getPessoa() != null) {
                        if (!UteisValidacao.emptyNumber(extrato.getMovPagamento().getPessoa().getCodigo())) {
                            extrato.getPessoa().setCodigo(extrato.getMovPagamento().getPessoa().getCodigo());
                        }
                    }
                    if (UteisValidacao.emptyNumber(extrato.getPessoa().getCodigo())) {
                        //buscar no sistema e nas transações a pessoa do extrato
                        extratoDiarioItemDAO.obterPessoaExtrato(extrato);
                    } else {
                        extratoDiarioItemDAO.incluirInfoCodPessoa(extrato);
                    }
                } catch (Exception ignore) {
                    if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao() + " e nem pelo nsu: " + extrato.getNsu());
                    } else if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao());
                    } else if (UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pelo nsu: " + extrato.getNsu());
                    }
                }
            }
        } finally {
            extratoDiarioItemDAO = null;
        }
    }

    private static ExtratoDiarioItemVO criarExtratoDiarioItem(ConvenioCobrancaVO convenio, String chaveDaApi, EmpresaVO empresa, String arquivoProcessamentoTemp,
                                                              boolean convenioPossuiVariasEmpresasConfiguradas, boolean isConciliarSemNumeroParcela, String props, Connection con,
                                                              PayableStoneTO payable, Date dataConsulta) throws Exception {
        ExtratoDiarioItemVO extratoDiarioItemVO;
        extratoDiarioItemVO = new ExtratoDiarioItemVO();

        //Preencher NSU e Autorização
        if (!UteisValidacao.emptyNumber(payable.getGateway_id())) {
            Transacao transacaoDAO;
            try {
                transacaoDAO = new Transacao(con);
                TransacaoVO transacao = transacaoDAO.consultarPorGatewayId(payable.getGateway_id());
                if (transacao != null) {
                    extratoDiarioItemVO.setAutorizacao(transacao.getCodigoAutorizacao());
                    if (!UteisValidacao.emptyString(transacao.getOutrasInformacoes())) {
                        try {
                            JSONObject outrasInformacoes = new JSONObject(transacao.getOutrasInformacoes());
                            if (outrasInformacoes.has("cartaoMascarado")) {
                                extratoDiarioItemVO.setNrCartao(outrasInformacoes.getString("cartaoMascarado"));
                            }
                        } catch (Exception ex) {
                        }
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro ao tentar encontrar o nsu/auitorização da transação para conciliar o recebível. Payable Charge_id: " + payable.getCharge_id());
            } finally {
                transacaoDAO = null;
            }
        }

        //NSU = payable.getGateway_id()
        extratoDiarioItemVO.setNsu(payable.getGateway_id().toString());
        extratoDiarioItemVO.setRo(payable.getGateway_id().toString());

        extratoDiarioItemVO.setIdExterno(payable.getId());
        extratoDiarioItemVO.setIdExterno2(payable.getCharge_id());
        extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
        extratoDiarioItemVO.setProps(new HashMap<>());
        extratoDiarioItemVO.setPropsString(props);
        extratoDiarioItemVO.setDataLancamento(payable.getCreated_at());
        extratoDiarioItemVO.setTipoConciliacao(payable.getTipoConciliacaoEnum().getCodigo());
        extratoDiarioItemVO.setEstabelecimento(chaveDaApi);
        extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
        extratoDiarioItemVO.setNrParcela(payable.getInstallment());
        extratoDiarioItemVO.setNrTotalParcelas(payable.getTotalInstallments());
        extratoDiarioItemVO.setApresentarExtrato(true);
        extratoDiarioItemVO.setConvenio(convenio);
        extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
        extratoDiarioItemVO.setEmpresa(empresa);
        extratoDiarioItemVO.setEstorno(false);

        //SETAR ANTECIPACAO
        boolean isAntecipacao = !UteisValidacao.emptyNumber(payable.getAnticipation_fee()) && !UteisValidacao.emptyString(payable.getAnticipation_id());
        if (isAntecipacao) {
            extratoDiarioItemVO.setAntecipacao(true);
        }

        if (payable.getTipoConciliacaoEnum().equals(TipoConciliacaoEnum.PAGAMENTOS)) {
            if (extratoDiarioItemVO.isAntecipacao()) {
                if (payable.getOriginal_payment_date() != null) {
                    extratoDiarioItemVO.setDataPgtoOriginalAntesDaAntecipacao(payable.getOriginal_payment_date());
                } else {
                    extratoDiarioItemVO.setDataPgtoOriginalAntesDaAntecipacao(payable.getPayment_date());
                }
            }
            extratoDiarioItemVO.setDataPrevistaPagamento(payable.getPayment_date());
        } else {
            if (payable.getOriginal_payment_date() != null && payable.getPayment_date() != null) {
                //acontece de um recebível normal, sem antecipação ter a data original maior que o dia de pagamento de fato,
                // geralmente um dia maior, por isso necessário as validações abaixo.
                if (Calendario.igual(payable.getOriginal_payment_date(), payable.getPayment_date())) {
                    extratoDiarioItemVO.setDataPrevistaPagamento(payable.getOriginal_payment_date());
                } else if (Calendario.maior(payable.getOriginal_payment_date(), payable.getPayment_date())) {
                    extratoDiarioItemVO.setDataPrevistaPagamento(payable.getPayment_date());
                } else {
                    extratoDiarioItemVO.setDataPrevistaPagamento(payable.getOriginal_payment_date());
                }
            } else if (payable.getOriginal_payment_date() != null) {
                extratoDiarioItemVO.setDataPrevistaPagamento(payable.getOriginal_payment_date());
            } else {
                extratoDiarioItemVO.setDataPrevistaPagamento(payable.getPayment_date());
            }
        }

        extratoDiarioItemVO.setCredito(true);

        if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
            getFacade().getExtratoDiarioItem().preencherMovPagamento(extratoDiarioItemVO);
        } else if (extratoDiarioItemVO.getCredito()) {
            getFacade().getExtratoDiarioItem().preencherCartaoCredito(extratoDiarioItemVO, isConciliarSemNumeroParcela);
        }

        try {
            if (convenioPossuiVariasEmpresasConfiguradas && !UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
            }
        } catch (Exception ex) {
        }


        double valorBruto = payable.getAmount() / 100.0;
        double valorTaxaCartao = payable.getFee() / 100.0;
        double valorTaxaAntecipacao = 0.0;
        if (extratoDiarioItemVO.isAntecipacao()) {
            valorTaxaAntecipacao = payable.getAnticipation_fee() / 100.0;
            extratoDiarioItemVO.setValorDescontadoAntecipacao(valorTaxaAntecipacao);
        }
        double valorLiquido = ((double) payable.getAmount() / 100.0) - (valorTaxaCartao + valorTaxaAntecipacao);
        double valorComissao = valorBruto - valorLiquido;

        extratoDiarioItemVO.setValorBruto(valorBruto);
        extratoDiarioItemVO.setValorLiquido(valorLiquido);
        extratoDiarioItemVO.setValorComissao(valorComissao);

        //CALCULAR TAXA PERCENTUAL DA ANTECIPAÇÃO COM BASE NO VALOR
        if (extratoDiarioItemVO.isAntecipacao()) {
            double taxaCalculadaAntecipacao = 0.0;
            extratoDiarioItemVO.setValorDescontadoAntecipacao((double) payable.getAnticipation_fee() / 100);  //valor em R$ descontado pela taxa de antecipação (double).
            taxaCalculadaAntecipacao = Uteis.arredondarForcando2CasasDecimais((valorTaxaAntecipacao / valorBruto) * 100);
            extratoDiarioItemVO.setTaxaCalculadaAntecipacao(taxaCalculadaAntecipacao);
        } else {
            extratoDiarioItemVO.setTaxaCalculadaAntecipacao(0.0);
        }

        //CALCULAR TAXA PERCENTUAL DO CARTÃO COM BASE NO VALOR
        double taxaCalculadaCartao = 0.0;
        taxaCalculadaCartao = Uteis.arredondarForcando2CasasDecimais((valorTaxaCartao / valorBruto) * 100);
        extratoDiarioItemVO.setTaxa(taxaCalculadaCartao);

        //para antecipação de recebíveis, mudar a data do recebimento no zw também automaticamente;
        if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()) && extratoDiarioItemVO.isAntecipacao()) {
            Uteis.logarDebug("Encontrei um pagamento que foi antecipado, vou mudar a data de pagamento dentro do ZW automaticamente..." +
                    (!UteisValidacao.emptyString(extratoDiarioItemVO.getAutorizacao()) ? " | AUT: " + extratoDiarioItemVO.getAutorizacao() : "") +
                    (!UteisValidacao.emptyString(extratoDiarioItemVO.getNsu()) ? " | NSU: " + extratoDiarioItemVO.getNsu() : ""));
            try {
                getFacade().getExtratoDiarioItem().alterarDatasPagamento(extratoDiarioItemVO);
            } catch (Exception ex) {
                ex.printStackTrace();
                try {
                    Uteis.logarDebug("Não foi possível mudar a data de pagamento dentro do ZW automaticamente... | ID Payable (recebível): " + extratoDiarioItemVO.getIdExterno());
                } catch (Exception ignored) {
                }
            }
        }
        return extratoDiarioItemVO;
    }

    private static ExtratoDiarioItemVO criarExtratoDiarioItemCancelamento(ConvenioCobrancaVO convenio, String chaveDaApi, EmpresaVO empresa, String arquivoProcessamentoTemp,
                                                                          boolean convenioPossuiVariasEmpresasConfiguradas, String props, Connection con,
                                                                          PayableStoneTO payable, Date dataConsulta) throws Exception {
        ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO();

        //Preencher NSU e Autorização
        if (!UteisValidacao.emptyNumber(payable.getGateway_id())) {
            Transacao transacaoDAO;
            try {
                transacaoDAO = new Transacao(con);
                TransacaoVO transacao = transacaoDAO.consultarPorGatewayId(payable.getGateway_id());
                if (transacao != null) {
                    extratoDiarioItemVO.setNsu(transacao.getCodigoNSU());
                    extratoDiarioItemVO.setRo(transacao.getCodigoNSU());
                    extratoDiarioItemVO.setAutorizacao(transacao.getCodigoAutorizacao());
                    try {
                        if (!UteisValidacao.emptyString(transacao.getOutrasInformacoes())) {
                            JSONObject outrasInformacoes = new JSONObject(transacao.getOutrasInformacoes());
                            if (outrasInformacoes.has("cartaoMascarado")) {
                                extratoDiarioItemVO.setNrCartao(outrasInformacoes.getString("cartaoMascarado"));
                            }
                        }
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro ao tentar encontrar o nsu/auitorização da transação para conciliar o recebível. Payable Charge_id: " + payable.getCharge_id());
            } finally {
                transacaoDAO = null;
            }
        }

        extratoDiarioItemVO.setIdExterno(payable.getId());
        extratoDiarioItemVO.setIdExterno2(payable.getCharge_id());
        extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
        extratoDiarioItemVO.setProps(new HashMap<>());
        extratoDiarioItemVO.setPropsString(props);
        extratoDiarioItemVO.setDataLancamento(payable.getCreated_at());
        extratoDiarioItemVO.setDataCancelamento(payable.getCreated_at());
        extratoDiarioItemVO.setTipoConciliacao(payable.getTipoConciliacaoEnum().getCodigo());
        extratoDiarioItemVO.setEstabelecimento(chaveDaApi);
        extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
        extratoDiarioItemVO.setDataPrevistaPagamento(dataConsulta); //Usar sempre a data que está processando o arquivo aqui. Não usar a data do evento.
        extratoDiarioItemVO.setNrParcela(0);
        extratoDiarioItemVO.setNrTotalParcelas(0);
        extratoDiarioItemVO.setApresentarExtrato(true);
        extratoDiarioItemVO.setConvenio(convenio);
        extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
        extratoDiarioItemVO.setEmpresa(empresa);
        extratoDiarioItemVO.setEstorno(true);
        extratoDiarioItemVO.setValorBruto(payable.getAmount() / 100.0);
        extratoDiarioItemVO.setCredito(true);

        getFacade().getExtratoDiarioItem().preencherMovPagamento(extratoDiarioItemVO);

        try {
            if (convenioPossuiVariasEmpresasConfiguradas && !UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
            }
        } catch (Exception ex) {
        }

        return extratoDiarioItemVO;
    }

    private static ExtratoDiarioItemVO criarExtratoDiarioItemChargeBack(ConvenioCobrancaVO convenio, String chaveDaApi, EmpresaVO empresa, String arquivoProcessamentoTemp,
                                                                        String props, Connection con, PayableStoneTO payable) throws Exception {
        ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO();

        //Preencher NSU e Autorização
        if (!UteisValidacao.emptyNumber(payable.getGateway_id())) {
            Transacao transacaoDAO;
            try {
                transacaoDAO = new Transacao(con);
                TransacaoVO transacao = transacaoDAO.consultarPorGatewayId(payable.getGateway_id());
                if (transacao != null) {
                    extratoDiarioItemVO.setNsu(transacao.getCodigoNSU());
                    extratoDiarioItemVO.setRo(transacao.getCodigoNSU());
                    extratoDiarioItemVO.setAutorizacao(transacao.getCodigoAutorizacao());
                    try {
                        if (!UteisValidacao.emptyString(transacao.getOutrasInformacoes())) {
                            JSONObject outrasInformacoes = new JSONObject(transacao.getOutrasInformacoes());
                            if (outrasInformacoes.has("cartaoMascarado")) {
                                extratoDiarioItemVO.setNrCartao(outrasInformacoes.getString("cartaoMascarado"));
                            }
                        }
                    } catch (Exception ex) {
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro ao tentar encontrar o nsu/auitorização da transação para conciliar o recebível. Payable Charge_id: " + payable.getCharge_id());
            } finally {
                transacaoDAO = null;
            }
        }

        extratoDiarioItemVO.setIdExterno(payable.getId());
        extratoDiarioItemVO.setIdExterno2(payable.getCharge_id());
        extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
        extratoDiarioItemVO.setProps(new HashMap<>());
        extratoDiarioItemVO.setPropsString(props);
        extratoDiarioItemVO.setDataLancamento(payable.getCreated_at());
        extratoDiarioItemVO.setDataCancelamento(payable.getCreated_at());
        extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.CHARGEBACK.getCodigo());
        extratoDiarioItemVO.setEstabelecimento(chaveDaApi);
        extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
        try {
            extratoDiarioItemVO.setNrParcela(payable.getInstallment());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        extratoDiarioItemVO.setNrTotalParcelas(0);
        extratoDiarioItemVO.setApresentarExtrato(false);
        extratoDiarioItemVO.setConvenio(convenio);
        extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
        extratoDiarioItemVO.setEmpresa(empresa);
        extratoDiarioItemVO.setEstorno(false);
        extratoDiarioItemVO.setValorBruto(payable.getAmount() / 100.0);

        return extratoDiarioItemVO;
    }

    private static void notificarErro(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO,
                                      Exception exception, Connection con) {

        ZillyonWebFacade zillyonWebFacadeDAO;
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);
            zillyonWebFacadeDAO = new ZillyonWebFacade(con);

            String chave = DAO.resolveKeyFromConnection(con);

            try {
                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                zillyonWebFacadeDAO.notificarRecursoSistema(chave, RecursoSistema.ERRO_EXTRATO_STONE, usuarioVO, empresaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            String mensagem = exception.getMessage();
            try {
                StringBuilder result = new StringBuilder(exception.toString() + "\n");
                StackTraceElement[] trace = exception.getStackTrace();
                for (StackTraceElement stackTraceElement : trace) {
                    result.append(stackTraceElement.toString()).append("\n");
                }
                mensagem = result.toString();
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            UteisEmail email = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
            String assunto = "Erro processar Stone Conciliação V5: " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao() + " | Chave " + chave;
            email.novo(assunto, config);

            String[] emailEnviar = new String[]{"<EMAIL>","<EMAIL>"};

            StringBuilder emailTexto = new StringBuilder();
            emailTexto.append("<h2>Erro ao processar extrato Stone </h2>");
            emailTexto.append("<h3>Chave: ").append(chave).append("</h3>");
            emailTexto.append("<h3></h3>");
            emailTexto.append("<h3>Convênio: </h3>");
            emailTexto.append("<h3>Código: ").append(convenioCobrancaVO.getCodigo()).append(" </h3>");
            emailTexto.append("<h3>Descrição: ").append(convenioCobrancaVO.getDescricao()).append(" </h3>");
            emailTexto.append("<h3>Chave da API Gateway: ").append(convenioCobrancaVO.getCodigoAutenticacao03()).append(" </h3>");
            emailTexto.append("<h3>Chave da API PSP: ").append(convenioCobrancaVO.getCodigoAutenticacao04()).append(" </h3>");
            emailTexto.append("<h2></h2>");
            emailTexto.append("<h2>Erro: </h2>");
            emailTexto.append("<h2></h2>");
            emailTexto.append("<p> - ").append(mensagem).append("</p>");

            email.enviarEmailN(emailEnviar, emailTexto.toString(), assunto, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro notificarErro: " + ex.getMessage());
        } finally {
            zillyonWebFacadeDAO = null;
            usuarioDAO = null;
        }
    }

    public static boolean validarSeRetornoPossuiDadosValidos(String retorno) {
        boolean possuiDadosvalidos = false;
        try {
            possuiDadosvalidos = new JSONObject(retorno).optJSONArray("data").length() > 0;
            return possuiDadosvalidos;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("ERRO Converter JSON StoneConciliation.class");
            Uteis.logarDebug("JSON: " + retorno.toString());
            Uteis.logarDebug("ERRO: " + ex.getMessage());
        }
        return false;
    }
}
