package servicos.impl.financeiroMs;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.impl.microsservice.SuperMSService;
import servicos.impl.planoMs.PlanoMsException;
import servicos.util.ExecuteRequestHttpService;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class FinanceiroMsService extends SuperMSService {

    public static String replicarPlanoContas(JSONArray array, String financeiroMsUrl, String chaveDestino, String empresa, boolean replicado) throws Exception {
        String url = financeiroMsUrl + "/v1/planoconta/replicar/" + replicado;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chaveDestino));
        headers.put("empresaId", empresa);

        try {
            String response = ExecuteRequestHttpService.post(url, array.toString(), headers);

            return new JSONObject(response).getString("content");
        } catch (Exception e) {
            Uteis.logar(e, FinanceiroMsService.class);
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    public static String alterarBloqueioCriacaoPlanoConta(String financeiroMsUrl, String chaveDestino, String empresa, boolean bloqueado) throws Exception {
        String url = financeiroMsUrl + "/v1/planoconta/alterarBloqueioCriacaoPlanoConta/" + bloqueado;
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chaveDestino));
        headers.put("empresaId", empresa);

        try {
            String response = ExecuteRequestHttpService.post(url, "", headers);

            return new JSONObject(response).getString("content");
        } catch (Exception e) {
            Uteis.logar(e, FinanceiroMsService.class);
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    public static JSONArray clonarPlanoContas(String financeiroMsUrl, String chaveOrigem) throws Exception {
        String url = financeiroMsUrl + "/v1/planoconta/clonar";
        return getArray(chaveOrigem, url);
    }

    public static JSONObject obterUsuario(Integer codigo, String pessoaMsUrl, String chave) throws Exception {
        String url = pessoaMsUrl + "/usuario/" + codigo;
        return get(chave, url);
    }

    public static JSONArray obterUsuarioPorNomeUsuario(String nomeUsuario, String pessoaMsUrl, String chave) throws Exception {
        String nomeCodificado = URLEncoder.encode(nomeUsuario, StandardCharsets.UTF_8.toString());
        String url = pessoaMsUrl + "/usuario/findByName?nome=" + nomeCodificado;

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONArray(response);
        } catch (Exception e) {
            Uteis.logar(e, FinanceiroMsService.class);
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    private static JSONArray getArray(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONArray("content");
        } catch (Exception e) {
            Uteis.logar(e, FinanceiroMsService.class);
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    private static JSONObject get(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            Uteis.logar(e, FinanceiroMsService.class);
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }
}
