package servicos.impl.pactoPay;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pactopay.PactoPaySuperService;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PactoPayEnvioEmailVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.enumerador.PactoPayEnvioEmailStatusEnum;
import negocio.comuns.financeiro.enumerador.StatusPactoPayComunicacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoEnvioPactoPayEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.basico.PactoPayConfig;
import negocio.facade.jdbc.basico.PactoPayEnvioEmail;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.crm.HistoricoContato;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.botconversa.BotConversaController;
import servicos.integracao.gymbotpro.GymbotProController;
import servicos.integracao.push.PushController;
import servicos.integracao.sms.Message;
import servicos.operacoes.MailingItensController;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 25/08/2022
 */
public class PactoPayComunicacaoService {


    private Connection con;
    private String key;
    private PactoPayConfig pactoPayConfigDAO;
    private PactoPayComunicacao pactoPayComunicacaoDAO;
    private PactoPayEnvioEmail pactoPayEnvioEmailDAO;
    private Cliente clienteDAO;
    private Pessoa pessoaDAO;
    private Empresa empresaDAO;
    private HistoricoContato historicoContatoDAO;
    private Usuario usuarioDAO;
    private PactoPaySuperService pactoPaySuperService;

    public PactoPayComunicacaoService(Connection con) throws Exception {
        this.con = con;
        this.key = DAO.resolveKeyFromConnection(this.con);
        pactoPayConfigDAO = new PactoPayConfig(con);
        pactoPayComunicacaoDAO = new PactoPayComunicacao(con);
        pactoPayEnvioEmailDAO = new PactoPayEnvioEmail(con);
        clienteDAO = new Cliente(con);
        pessoaDAO = new Pessoa(con);
        empresaDAO = new Empresa(con);
        historicoContatoDAO = new HistoricoContato(con);
        usuarioDAO = new Usuario(con);
        pactoPaySuperService = new PactoPaySuperService(con);
    }

    public PactoPayComunicacaoVO enviarPactoPayComunicacao(Integer codigo) throws Exception {
        PactoPayComunicacaoVO obj = null;
        try {
            obj = pactoPayComunicacaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (obj == null) {
                throw new Exception("PactoPayComunicacao não encontrado com o código: " + codigo);
            }

            if (obj.getPactoPayComunicacaoDTO().getDestinatario().isEmpty()) {
                throw new Exception("Nenhum destinatário encontrado");
            }

            PactoPayConfigVO configVO = this.pactoPayConfigDAO.consultarPorEmpresa(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean envioWagi = false;
            String resultado = "";
            if (obj.getMeioEnvio().equals(MeioEnvio.SMS)) {
                //realizar o envio do sms
                EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                resultado = this.enviarSMS(empresaVO, configVO, obj);
            } else if (obj.getMeioEnvio().equals(MeioEnvio.EMAIL)) {
//                ConfiguracaoSistemaCRMVO configCRMVO = getConfiguracaoSMTPNoReply();
                ConfiguracaoSistemaCRMVO configCRMVO = this.pactoPaySuperService.obterConfiguracaoEmail(configVO);
                envioWagi = configCRMVO.getIntegracaoPacto();
                resultado = this.enviarEmailLista(obj, configCRMVO);
            } else if (obj.getMeioEnvio().equals(MeioEnvio.GYMBOT)) {
                if (!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
                    obj.setPessoaVO(this.pessoaDAO.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                }
                resultado = this.enviarGymBot(obj);
            } else if (obj.getMeioEnvio().equals(MeioEnvio.GYMBOT_PRO)) {
                if (!UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
                    obj.setPessoaVO(this.pessoaDAO.consultarPorChavePrimaria(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                }
                resultado = this.enviarGymBotPro(obj);
            } else if (obj.getMeioEnvio().equals(MeioEnvio.APP)) {
                resultado = this.enviarApp(obj);
            } else {
                throw new Exception("Meio comunicação não implementado");
            }

            //exemplo já deixar como jobexcluido
            //ou caso seja envio direto então já marcar como jobexcluido
            if (obj.getPactoPayComunicacaoDTO().isExemplo() ||
                    obj.getPactoPayComunicacaoDTO().isEnvioSemJenkins()) {
                pactoPayComunicacaoDAO.alteraJobExcluido(true, obj.getCodigo());
            }

            //gravar o histório de contato
            if (!obj.getPactoPayComunicacaoDTO().isExemplo()) {
                //criar o histórico de contato
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                HistoricoContatoVO histVO = gerarHistoricoContato(envioWagi, obj.getTipoEnvioPactoPay(),
                        obj.getPactoPayComunicacaoDTO().getCodigoOrigem(),
                        obj.getPactoPayComunicacaoDTO().getObservacaoHistorico(),
                        clienteVO, obj.getMeioEnvio(), usuarioVO);
                histVO.setDia(Calendario.hoje());
                historicoContatoDAO.incluirSemCommit(histVO);
                obj.setHistoricoContatoVO(histVO);
            }

            obj.setSucesso(true);
            obj.setResultado(resultado);
        } catch (Exception ex) {
            if (obj == null) {
                obj = new PactoPayComunicacaoVO();
            }

            Uteis.logar(null, "ERRO PactoPayComunicacao | " + obj.getPactoPayComunicacaoDTO().getIdentificador() + " | " + ex.getMessage());

            StringBuilder result = null;
            try {
                result = new StringBuilder(ex + "\n");
                StackTraceElement[] trace = ex.getStackTrace();
                for (StackTraceElement stackTraceElement : trace) {
                    result.append(stackTraceElement.toString()).append("\n");
                }
            } catch (Exception ex1) {
                ex1.printStackTrace();
                if (result == null) {
                    result = new StringBuilder();
                }
                result.append(ex1.getMessage());
            }
            obj.setSucesso(false);
            obj.setResultado(result.toString());
            throw ex;
        } finally {
            if (obj == null) {
                obj = new PactoPayComunicacaoVO();
            }
            obj.setDataExecucao(Calendario.hoje());
            obj.setStatus(StatusPactoPayComunicacaoEnum.PROCESSADO);
            pactoPayComunicacaoDAO.gravarSemCommit(obj);
        }
        return obj;
    }

    public PactoPayEnvioEmailVO enviarPactoPayEnvioEmail(Integer codigo) throws Exception {
        PactoPayEnvioEmailVO objEnvio = null;
        try {
            objEnvio = pactoPayEnvioEmailDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            if (objEnvio == null) {
                throw new Exception("PactoPayEnvioEmail não encontrado com o código: " + codigo);
            }

            if (UteisValidacao.emptyList(objEnvio.getListaPactoPayComunicacao())) {
                throw new Exception("Nenhum destinatário encontrado");
            }

            PactoPayConfigVO configVO = this.pactoPayConfigDAO.consultarPorChavePrimaria(objEnvio.getPactoPayConfigVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConfiguracaoSistemaCRMVO configCRMVO = this.pactoPaySuperService.obterConfiguracaoEmail(configVO);
            boolean envioWagi = configCRMVO.getIntegracaoPacto();
            String resultado = this.enviarEmailLista(objEnvio, configCRMVO);

            //exemplo já deixar como jobexcluido
            //ou caso seja envio direto então já marcar como jobexcluido
            if (objEnvio.getPactoPayEnvioEmailDadosDTO().isEnvioSemJenkins()) {
                pactoPayEnvioEmailDAO.alteraJobExcluido(true, objEnvio.getCodigo());
            }

            //gravar histórico de contato
            gravarHistoricoContatoLista(objEnvio, envioWagi, resultado);

            objEnvio.setResposta(resultado);
            objEnvio.setStatus(PactoPayEnvioEmailStatusEnum.ENVIADO_SENDY);
        } catch (Exception ex) {
            if (objEnvio == null) {
                objEnvio = new PactoPayEnvioEmailVO();
            }

            Uteis.logar(null, "ERRO PactoPayEnvioEmail | " + objEnvio.getCodigo() + " | " + ex.getMessage());

            StringBuilder result = null;
            try {
                result = new StringBuilder(ex + "\n");
                StackTraceElement[] trace = ex.getStackTrace();
                for (StackTraceElement stackTraceElement : trace) {
                    result.append(stackTraceElement.toString()).append("\n");
                }
            } catch (Exception ex1) {
                ex1.printStackTrace();
                if (result == null) {
                    result = new StringBuilder();
                }
                result.append(ex1.getMessage());
            }
            objEnvio.setStatus(PactoPayEnvioEmailStatusEnum.ERRO);
            objEnvio.setResposta(result.toString());
            throw ex;
        } finally {
            if (objEnvio == null) {
                objEnvio = new PactoPayEnvioEmailVO();
            }
            objEnvio.setDataExecucao(Calendario.hoje());
            pactoPayEnvioEmailDAO.gravarSemCommit(objEnvio);
        }
        return objEnvio;
    }

    private void gravarHistoricoContatoLista(PactoPayEnvioEmailVO objEnvio, boolean envioWagi, String resultado) throws Exception {
        //gravar o histório de contato
        for (PactoPayComunicacaoVO obj : objEnvio.getListaPactoPayComunicacao()) {
            try {
                if (!obj.getPactoPayComunicacaoDTO().isExemplo()) {
                    //criar o histórico de contato
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
                    UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                    HistoricoContatoVO histVO = gerarHistoricoContato(envioWagi, obj.getTipoEnvioPactoPay(),
                            obj.getPactoPayComunicacaoDTO().getCodigoOrigem(), obj.getPactoPayComunicacaoDTO().getObservacaoHistorico(),
                            clienteVO, obj.getMeioEnvio(), usuarioVO);
                    histVO.setDia(Calendario.hoje());
                    historicoContatoDAO.incluirSemCommit(histVO);
                    obj.setHistoricoContatoVO(histVO);
                }
                obj.setSucesso(true);
                obj.setResultado(resultado);
            } catch (Exception ex) {
                if (obj == null) {
                    obj = new PactoPayComunicacaoVO();
                }

                Uteis.logar(null, "ERRO PactoPayComunicacao | " + obj.getPactoPayComunicacaoDTO().getIdentificador() + " | " + ex.getMessage());

                StringBuilder result = null;
                try {
                    result = new StringBuilder(ex + "\n");
                    StackTraceElement[] trace = ex.getStackTrace();
                    for (StackTraceElement stackTraceElement : trace) {
                        result.append(stackTraceElement.toString()).append("\n");
                    }
                } catch (Exception ex1) {
                    ex1.printStackTrace();
                    if (result == null) {
                        result = new StringBuilder();
                    }
                    result.append(ex1.getMessage());
                }
                obj.setSucesso(false);
                obj.setResultado(result.toString());
            } finally {
                if (obj == null) {
                    obj = new PactoPayComunicacaoVO();
                }
                obj.setDataExecucao(Calendario.hoje());
                obj.setStatus(StatusPactoPayComunicacaoEnum.PROCESSADO);
                pactoPayComunicacaoDAO.gravarSemCommit(obj);
            }
        }
    }

    private HistoricoContatoVO gerarHistoricoContato(boolean envioWagi, TipoEnvioPactoPayEnum tipoEnvioPactoPayEnum,
                                                     Integer codigoOrigem, String observacao, ClienteVO clienteVO,
                                                     MeioEnvio meioEnvio, UsuarioVO usuarioVO) throws Exception {
        HistoricoContatoVO hist = new HistoricoContatoVO();
        hist.setDia(Calendario.hoje());
        hist.setResponsavelCadastro(usuarioVO);
        hist.setClienteVO(clienteVO);
        hist.setWagienvi(envioWagi);
        hist.setContatoAvulso(true);
        hist.setResultado("Envio " + meioEnvio.getDescricao());
        hist.setTipoContato(meioEnvio.getTipoContatoCRM().getSigla());
        hist.setObservacao(observacao);
        hist.setOrigem(tipoEnvioPactoPayEnum.getIdentificador());
        hist.setOrigemCodigo(codigoOrigem);
        HistoricoContatoVO.validarDados(hist);
        return hist;
    }

    public String enviarEmail(PactoPayComunicacaoVO pactoPayComunicacaoVO, ConfiguracaoSistemaCRMVO configCRMVO) throws Exception {
        try {
            String remetente = configCRMVO.getRemetentePadrao();
            configCRMVO.setRemetentePadrao(""); //limpar para não ficar errado o remetente
            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getAssunto(), configCRMVO);
            uteisEmail.setChave(this.key);
            uteisEmail.setPactoPayComunicacaoVO(pactoPayComunicacaoVO);
            String[] emailEnviar = new String[pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario().size()];
            emailEnviar = pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario().toArray(emailEnviar);
            String appKey = this.pactoPaySuperService.obterAppKey(this.key, pactoPayComunicacaoVO.getEmpresaVO().getCodigo());
            uteisEmail.enviarEmailN(emailEnviar, pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getMensagem(),
                    pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getAssunto(),
                    remetente, configCRMVO, appKey, pactoPayComunicacaoVO.getCodigo());
            return "E-mail enviado";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO ENVIO EMAIL: " + ex.getMessage());
        }
    }

    public String enviarEmailLista(PactoPayComunicacaoVO pactoPayComunicacaoVO, ConfiguracaoSistemaCRMVO configCRMVO) throws Exception {
        try {
            String remetente = configCRMVO.getRemetentePadrao();
            configCRMVO.setRemetentePadrao(""); //limpar para não ficar errado o remetente
            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getAssunto(), configCRMVO);
            uteisEmail.setChave(this.key);
            uteisEmail.setPactoPayComunicacaoVO(pactoPayComunicacaoVO);
            String appKey = this.pactoPaySuperService.obterAppKey(this.key, pactoPayComunicacaoVO.getEmpresaVO().getCodigo());

            List<MailingItensController.MailingItem> mailingItems = new ArrayList<>();
            for (String emailEnviar : pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario()) {
                MailingItensController.MailingItem dto = new MailingItensController.MailingItem(0, emailEnviar, 0);
                dto.setCustom_fields(pactoPayComunicacaoVO.getCustom_fields());
                mailingItems.add(dto);
            }

            String resposta = uteisEmail.enviarEmailPactoPay(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getMensagem(),
                    pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getAssunto(), remetente,
                    configCRMVO, appKey, pactoPayComunicacaoVO.getCodigo(), mailingItems,
                    pactoPayComunicacaoVO.getEmpresaVO().getCodigo());
            try {
                this.pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(pactoPayComunicacaoVO.getCodigo(), "ENVIAR_EMAIL_RESPOSTA", resposta);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "E-mail enviado";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO ENVIO EMAIL: " + ex.getMessage());
        }
    }

    public String enviarEmailLista(PactoPayEnvioEmailVO pactoPayEnvioEmailVO, ConfiguracaoSistemaCRMVO configCRMVO) throws Exception {
        try {
            String remetente = configCRMVO.getRemetentePadrao();
            configCRMVO.setRemetentePadrao(""); //limpar para não ficar errado o remetente
            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(pactoPayEnvioEmailVO.getAssunto(), configCRMVO);
            uteisEmail.setChave(this.key);
            uteisEmail.setPactoPayEnvioEmailVO(pactoPayEnvioEmailVO);
            String appKey = this.pactoPaySuperService.obterAppKey(this.key, pactoPayEnvioEmailVO.getEmpresaVO().getCodigo());

            List<MailingItensController.MailingItem> mailingItems = new ArrayList<>();
            for (PactoPayComunicacaoVO pactoPayComunicacaoVO : pactoPayEnvioEmailVO.getListaPactoPayComunicacao()) {
                for (String emailEnviar : pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario()) {
                    MailingItensController.MailingItem dto = new MailingItensController.MailingItem(0, emailEnviar, 0);
                    dto.setCustom_fields(pactoPayComunicacaoVO.getCustom_fields());
                    mailingItems.add(dto);
                }
            }

            String resposta = uteisEmail.enviarEmailPactoPay(pactoPayEnvioEmailVO.getMensagem(),
                    pactoPayEnvioEmailVO.getAssunto(), remetente,
                    configCRMVO, appKey, pactoPayEnvioEmailVO.getCodigo(), mailingItems,
                    pactoPayEnvioEmailVO.getEmpresaVO().getCodigo());
            try {
                this.pactoPayComunicacaoDAO.incluirPactoPayEnvioEmailLog(pactoPayEnvioEmailVO.getCodigo(), "ENVIAR_EMAIL_RESPOSTA", resposta);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "E-mail enviado";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO ENVIO EMAIL: " + ex.getMessage());
        }
    }

    public String enviarEmailNovo(PactoPayComunicacaoVO pactoPayComunicacaoVO, ConfiguracaoSistemaCRMVO configCRMVO) throws Exception {
        try {
            String remetente = configCRMVO.getRemetentePadrao();
            configCRMVO.setRemetentePadrao(""); //limpar para não ficar errado o remetente
            UteisEmail uteisEmail = new UteisEmail();
            uteisEmail.novo(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getAssunto(), configCRMVO);
            uteisEmail.setChave(this.key);
            uteisEmail.setPactoPayComunicacaoVO(pactoPayComunicacaoVO);
            String[] emailsEnviar = new String[pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario().size()];
            emailsEnviar = pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario().toArray(emailsEnviar);
            String appKey = this.pactoPaySuperService.obterAppKey(this.key, pactoPayComunicacaoVO.getEmpresaVO().getCodigo());

            List<MailingItensController.MailingItem> mailingItems = new ArrayList<>();
            for (String emailEnviar : emailsEnviar) {
                MailingItensController.MailingItem dto = new MailingItensController.MailingItem(0, emailEnviar, 0);
                dto.setCustom_fields(pactoPayComunicacaoVO.getCustom_fields());
                mailingItems.add(dto);
            }

            String resposta = uteisEmail.enviarEmailPactoPay(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getMensagem(),
                    pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getAssunto(), remetente,
                    configCRMVO, appKey, pactoPayComunicacaoVO.getCodigo(), mailingItems,
                    pactoPayComunicacaoVO.getEmpresaVO().getCodigo());
            try {
                this.pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(pactoPayComunicacaoVO.getCodigo(), "ENVIAR_EMAIL_RESPOSTA", resposta);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "E-mail enviado";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("ERRO ENVIO EMAIL: " + ex.getMessage());
        }
    }

    public String enviarSMS(EmpresaVO empresaVO,
                            PactoPayConfigVO configVO,
                            PactoPayComunicacaoVO pactoPayComunicacaoVO) throws Exception {

//        teste
//        tokenEnvio = "v91ajK4hHXVsKj7fMXEpcJX6ujn2uFYYEET1xOkt0Dc=";
//        getComunicacaoEnviarTO().setChave("ef104c66579de336c8fc6dff69c5e006");

        String chaveEnvioSMS = Uteis.getSMSChaveFacilitePay();
        String tokenEnvioSMS = Uteis.getSMSTokenFacilitePay();
        String remetenteSMS = "ACADEMIA";
        if (configVO != null &&
                !UteisValidacao.emptyString(configVO.getConfiguracaoEmail().getRemetenteSMS())) {
            remetenteSMS = configVO.getConfiguracaoEmail().getRemetenteSMS();
        }

        String resposta = "";
        Exception exception = null;
        boolean enviou = false;
        for (String numero : pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario()) {
            try {
                Map<String, String> params = new HashMap<>();
                params.put("key", chaveEnvioSMS);
                params.put("token", tokenEnvioSMS);
                params.put("msgs", montarMensagem(numero, pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getMensagem()));
                params.put("idRemetente", remetenteSMS);

                RequestHttpService requestHttpService = new RequestHttpService();
                requestHttpService.connectionRequestTimeout = 15000;
                RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest("https://s.smsup.com.br/smsservice/api/sender/do", null, params, null, MetodoHttpEnum.POST);
                requestHttpService = null;
                JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse());
                if (!jsonResp.optString("return").startsWith("ok - enviados: 1")) {
                    throw new Exception(respostaHttpDTO.getResponse());
                }

                //se for só uma mensagem para enviar então já retorna..
                if (pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario().size() > 1) {
                    return resposta;
                }

                resposta = respostaHttpDTO.getResponse();
                enviou = true;

                try {
                    this.pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(pactoPayComunicacaoVO.getCodigo(), "ENVIAR_SMS_ENVIO", params.toString());
                    this.pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(pactoPayComunicacaoVO.getCodigo(), "ENVIAR_SMS_RESPOSTA", resposta);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                exception = ex;
            }
        }

        if (!enviou && exception != null) {
            throw exception;
        }
        return resposta;
    }

    private String montarMensagem(String telefone, String mensagem) {
        telefone = telefone.replaceAll("[[a-z][A-Z]\\ \\+()-.]", "");
        if (telefone.length() == 11) {
            telefone = "55" + telefone;
        }
        if (telefone.length() == 13) {
            return telefone + "|" + mensagem;
        }
        return null;
    }

    public String enviarGymBot(PactoPayComunicacaoVO pactoPayComunicacaoVO) throws Exception {
        try {
            List<Message> enviarMessage = montarListaBeans(pactoPayComunicacaoVO, MeioEnvio.GYMBOT);
            BotConversaController service = new BotConversaController(key, pactoPayComunicacaoVO, this.con);
            String retorno = service.sendMessage(key,
                    pactoPayComunicacaoVO.getEmpresaVO().getCodigo(),
                    pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getUrlWebhookGymBot(),
                    enviarMessage);
            try {
                this.pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(pactoPayComunicacaoVO.getCodigo(), "ENVIAR_GYMBOT_RESPOSTA", retorno);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (retorno.equals("ok") || retorno.toLowerCase().contains("realizada com sucesso")) {
                return "Enviado para fila GymBot | Resp: " + retorno;
            } else {
                throw new Exception("ERRO ENVIO GYMBOT: " + retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String enviarGymBotPro(PactoPayComunicacaoVO pactoPayComunicacaoVO) throws Exception {
        try {
            List<Message> enviarMessage = montarListaBeans(pactoPayComunicacaoVO, MeioEnvio.GYMBOT_PRO);

            GymbotProController service = new GymbotProController(key, pactoPayComunicacaoVO, this.con);
            String retorno = service.sendMessage(key, pactoPayComunicacaoVO.getEmpresaVO().getCodigo(),
                    pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getTokenGymBotPro(),
                    pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getIdFluxoGymBotPro(),
                    enviarMessage);
            try {
                this.pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(pactoPayComunicacaoVO.getCodigo(), "ENVIAR_GYMBOTPRO_RESPOSTA", retorno);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (retorno.equals("ok") || retorno.toLowerCase().contains("realizada com sucesso")) {
                return "Enviado para fila GymBotPro | Resp: " + retorno;
            } else {
                throw new Exception("ERRO ENVIO GYMBOTPRO: " + retorno);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private List<Message> montarListaBeans(PactoPayComunicacaoVO pactoPayComunicacaoVO, MeioEnvio meioEnvio) throws Exception {
        List<Message> listaBeans = new ArrayList<Message>();
        for (String numero : pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getDestinatario()) {
            //personalizar a tag de nome
            Message beanMessage = new Message();
            beanMessage.setTipo(meioEnvio.getDescricao());
            beanMessage.setCodigoPessoa(pactoPayComunicacaoVO.getPessoaVO().getCodigo());
            beanMessage.setNome(pactoPayComunicacaoVO.getPessoaVO().getNome());
            beanMessage.setCodigoCliente(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getCliente());
//            beanMessage.setMsg(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getMensagem());

            String telefone = numero.replaceAll("[[a-z][A-Z]\\ \\+()-.]", "");
            if (telefone.length() == 11) {
                telefone = "55" + telefone;
            }
            beanMessage.setNumero(telefone);
            listaBeans.add(beanMessage);
        }
        return listaBeans;
    }

    public String enviarApp(PactoPayComunicacaoVO pactoPayComunicacaoVO) throws Exception {
        String mensagem = Uteis.retiraTags(pactoPayComunicacaoVO.getPactoPayComunicacaoDTO().getMensagem(), false);
        String retorno = new PushController(null, this.key, mensagem, null, pactoPayComunicacaoVO).send();
        try {
            this.pactoPayComunicacaoDAO.incluirPactoPayComunicacaoLog(pactoPayComunicacaoVO.getCodigo(), "ENVIAR_APP_RESPOSTA", retorno);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return retorno;
    }
}


