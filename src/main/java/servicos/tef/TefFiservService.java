package servicos.tef;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class TefFiservService extends SuperServico {


    public TefFiservService(Connection con) throws Exception {
        super(con);
    }

    public static String getIpSitef() {
        return PropsService.getPropertyValue(PropsService.ipSiTef);
    }

    private static String formatarValorFiserv(Double valor) {
        String valorFormatado = Uteis.formatarValorEmReal(valor);
        return valorFormatado;
    }

    private static Integer obterFuncaoFiserv(String tipoFormaPagto) {
        //Documentação: Interface Simplificada com a aplicação(VRS-254).pdf, página 21, título: 5.2.2 Tabela de códigos de funções
        if (tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            return 3;
        } else if (tipoFormaPagto.equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
            return 2;
        } else if (tipoFormaPagto.equals(TipoFormaPagto.PIX.getSigla())) {
            return 122;
        }
        return null;
    }

    public static JSONObject montarObjetoVerificacaoCobrancaFiserv(String codigoEmpresaFiserv, UsuarioVO usuarioLogado) throws Exception {
        JSONObject jsonObject = new JSONObject();
        montarBaseCobrancaFiserv(jsonObject, usuarioLogado);
        jsonObject.put("empresa", codigoEmpresaFiserv);
        return jsonObject;
    }

    public static JSONObject montarObjetoCobrancaFiserv(MovPagamentoVO obj, UsuarioVO usuarioLogado) throws Exception {
        JSONObject jsonObject = new JSONObject();
        montarBaseCobrancaFiserv(jsonObject, usuarioLogado);
        jsonObject.put("empresa", obterEmpresaFiserv(obj));
        jsonObject.put("valor", formatarValorFiserv(obj.getValorTotal()));

        String cnpj = obj.getEmpresa().getCNPJ().replace(".", "").replace("/", "").replace("-", "");
        jsonObject.put("trn_init_parameters", "[ParmsClient=1=" + cnpj + ";2=05870588000158]");

        jsonObject.put("funcao", obterFuncaoFiserv(obj.getFormaPagamento().getTipoFormaPagamento()));
        jsonObject.put("nrParcelasCartaoCredito", obj.getNrParcelaCartaoCredito());

        return jsonObject;
    }

    private static void montarBaseCobrancaFiserv(JSONObject jsonObject, UsuarioVO usuarioLogado) throws Exception {
        String dataFiscal = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        jsonObject.put("datafiscal", dataFiscal);
        String horaFiscal = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        jsonObject.put("horafiscal", horaFiscal);
        jsonObject.put("cupomfiscal", dataFiscal + horaFiscal); //Número de controle da Transação com a Fiserv. Recomendado ser a concatenação da data e hora fiscal.
        jsonObject.put("ipsitef", getIpSitef());
        jsonObject.put("terminal", "");

        if (!UteisValidacao.emptyString(usuarioLogado.getNome()) && usuarioLogado.getNome().length() > 14) {
            jsonObject.put("operador", usuarioLogado.getNome().toUpperCase().substring(0, 15));
        } else if (!UteisValidacao.emptyString(usuarioLogado.getNome()) && usuarioLogado.getNome().length() < 14) {
            jsonObject.put("operador", usuarioLogado.getNome().toUpperCase());
        }
    }

    private static String obterEmpresaFiserv(MovPagamentoVO obj) {
        String empresaFiserv = "";
        for (PinPadVO pinPadVO : obj.getFormaPagamento().getListaPinPad()) {
            if (!UteisValidacao.emptyString(pinPadVO.getPdvPinpad())) {
                empresaFiserv = pinPadVO.getPdvPinpad();
                break;
            }
        }
        return empresaFiserv;
    }

    public static Set<String> obterListEmpresasFiserv(Connection con, Integer empresaLogado) throws Exception {
        Set<String> codigosEmpresasFiserv = new HashSet<>();

        FormaPagamento formaPagamentoDAO;
        try {
            formaPagamentoDAO = new FormaPagamento(con);
            List<FormaPagamentoVO> formaPagamentoVOList = formaPagamentoDAO.consultarPorDescricaoTipoFormaPagamento("", false, false, true, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (FormaPagamentoVO formaPagamentoVO : formaPagamentoVOList) {
                for (PinPadVO pinPadVO : formaPagamentoVO.getListaPinPad()) {
                    if (pinPadVO.isFiserv() && pinPadVO.getEmpresa().getCodigo() == empresaLogado) {
                        codigosEmpresasFiserv.add(pinPadVO.getPdvPinpad());
                    }
                }
            }
        } catch (Exception ex) {
            System.out.println("ERRO: " + ex.getMessage());
        } finally {
            formaPagamentoDAO = null;
        }
        return codigosEmpresasFiserv;
    }

}
