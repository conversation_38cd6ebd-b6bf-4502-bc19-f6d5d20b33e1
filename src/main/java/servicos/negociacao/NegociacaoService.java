package servicos.negociacao;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import controle.contrato.ContratoControle;
import controle.contrato.ParcelasEditarNegociacaoNovo;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.plano.*;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.oamd.OAMDService;
import servicos.vendasonline.VendasOnlineService;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaDTO;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class NegociacaoService {

    private Connection con;

    public NegociacaoService(String key, Connection con) throws Exception {
        this.con = con;
        if (!UteisValidacao.emptyString(key)) {
            Conexao.guardarConexaoForJ2SE(key, this.con);
        }
    }

    public NegociacaoService() throws Exception {
    }


    public JSONObject simular(String key, JSONObject negociacao, AcessoControle acessoControle) {
        try {
            acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            UsuarioVO usuarioVO = acessoControle.getUsuarioDao().consultarPorChavePrimaria(negociacao.getInt("usuario"), Uteis.NIVELMONTARDADOS_MINIMOS);
            ContratoControle contratoControle = obterContratoControle(usuarioVO, negociacao, acessoControle, false);
            ContratoWS contratoWS = contratoControle.getContratoVO().toWS(false);
            if (negociacao.has("vencimentoCartao") && negociacao.get("vencimentoCartao") != null) {
                contratoWS.setDiasCartao(negociacao.getInt("vencimentoCartao"));
            }
            if(!UteisValidacao.emptyNumber(contratoControle.getContratoVO().getValorProRata())){
                contratoWS.setValorProRata(contratoControle.getContratoVO().getValorProRata());
            }
            if (!contratoWS.getSituacao().equalsIgnoreCase("CA")
                    && contratoControle.getContratoVO().getSituacaoContrato().equalsIgnoreCase("RN")
                    && UteisValidacao.emptyNumber(contratoWS.getDiasCartao())
                    && negociacao.has("contratoBase")
                    && negociacao.getInt("contratoBase") > 0) {
                ContratoRecorrencia contratoRecorrenciaDAO = new ContratoRecorrencia(acessoControle.getCon());
                ContratoRecorrenciaVO contratoReco = contratoRecorrenciaDAO.consultarPorContrato(negociacao.getInt("contratoBase"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (contratoReco != null && !UteisValidacao.emptyNumber(contratoReco.getDiaVencimentoCartao())) {
                    contratoWS.setDiasCartao(contratoReco.getDiaVencimentoCartao());
                }
                contratoRecorrenciaDAO = null;
            }
            JSONObject simulado = new JSONObject(contratoWS);
            simulado.put("inicio", contratoControle.getContratoVO().getVigenciaDe().getTime());
            simulado.put("fim", contratoControle.getContratoVO().getVigenciaAteAjustada().getTime());
            simulado.put("lancamento", contratoControle.getContratoVO().getDataLancamento().getTime());
            ajustarParcelas(contratoControle, simulado);
            Double descontos = 0.0;
            if (!UteisValidacao.emptyNumber(contratoControle.getContratoVO().getValorTemporarioDescontoPorcentagem())) {
                descontos = contratoControle.getContratoVO().getValorDescontoManual();
            }
            if (!UteisValidacao.emptyNumber(contratoControle.getContratoVO().getValorDescontoEspecifico())) {
                descontos = contratoControle.getContratoVO().getValorDescontoManual();
            }
            simulado.put("descontosExtra", descontos);
            descontos += contratoControle.getContratoVO().getValorConvenioDesconto();
            if(contratoControle.isMostrarDescontoRenovacaoAntecipada()
                    && contratoControle.getDescontoAntecipado() != null
                    && !UteisValidacao.emptyNumber(contratoControle.getDescontoAntecipado().getCodigo())){
                JSONObject descontoAntecipado = new JSONObject();
                descontoAntecipado.put("codigo", contratoControle.getDescontoAntecipado().getCodigo());
                descontoAntecipado.put("descricao", contratoControle.getDescontoAntecipado().getDescricao());
                descontoAntecipado.put("tipo", contratoControle.getDescontoAntecipado().getTipoDesconto_Apresentar());
                descontoAntecipado.put("valor", contratoControle.getDescontoAntecipado().getValor());
                descontoAntecipado.put("nrDiasAntecipado", contratoControle.getDescontoAntecipado().getNrDiasAntecipado());
                descontoAntecipado.put("selecionado", contratoControle.getDescontoAntecipado().isDescontoEscolhido());
                descontoAntecipado.put("apresentarDiasAtrasoRenovacao", contratoControle.getContratoVO().getApresentarDiasAtrasoRenovacao());
                Double valorReal = 0.0;
                if(contratoControle.getDescontoAntecipado().isDescontoEscolhido()){
                    if(TipoDesconto.PE.equals(contratoControle.getDescontoAntecipado().getTipoDesconto())){
                        valorReal = contratoControle.getContratoVO().getValorTemporarioDescontoAntecipadoTotal();
                    } else {
                        valorReal = contratoControle.getDescontoAntecipado().getValor();
                    }
                    descontos += valorReal;
                }
                descontoAntecipado.put("valorReal", valorReal);
                simulado.put("descontoRenovacaoAntecipada", descontoAntecipado);
            }
            simulado.put("descontosConvenio", contratoControle.getContratoVO().getValorConvenioDesconto());

            Double valorAcrescimos = valorAcrescimos(contratoControle);
            if (contratoControle.getContratoVO().getPlanoDuracao() != null &&
                    !UteisValidacao.emptyNumber(contratoControle.getContratoVO().getPlanoDuracao().getNumeroMeses())) {
                valorAcrescimos = valorAcrescimos * contratoControle.getContratoVO().getPlanoDuracao().getNumeroMeses().doubleValue();
            }
            simulado.put("valorPlano", (contratoControle.getContratoVO().getValorBaseCalculo() + descontos) - valorAcrescimos);
            simulado.put("valorBase", contratoControle.getContratoVO().getValorBaseCalculo() + descontos);
            simulado.put("valorAcrescimos", valorAcrescimos);

            Double descontoCupomParcelas = cupomParcelas(key, negociacao.optString("cupom"), contratoControle, simulado);
            simulado.put("valorFinal", simulado.getDouble("valorFinal") - descontoCupomParcelas);
            simulado.put("descontos", descontos + descontoCupomParcelas);

            JSONArray valoresModalidades = new JSONArray();
            for (ContratoModalidadeVO cm : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
                JSONObject obj = new JSONObject();
                checkPacote(cm, contratoControle, obj);
                obj.put("modalidade", cm.getModalidade().getCodigo());
                if (cm.getContratoModalidadeCredito() != null && !UteisValidacao.emptyNumber(cm.getContratoModalidadeCredito().getQtdCreditoCompra())) {
                    obj.put("valor", cm.getContratoModalidadeCredito().getValorTotal());
                    obj.put("totalCreditos", cm.getContratoModalidadeCredito().getQtdCreditoCompra());
                } else if (cm.getExcecao() == null || UteisValidacao.emptyNumber(cm.getExcecao().getValor())) {
                    obj.put("valor", cm.getModalidade().getValorMensal());
                } else {
                    obj.put("valor", cm.getExcecao().getValor());
                }
                valoresModalidades.put(obj);
            }
            simulado.put("valoresModalidades", valoresModalidades);
            GrupoVO grupo = contratoControle.getContratoVO().getGrupo();
            if(grupo != null && !UteisValidacao.emptyNumber(grupo.getCodigo())) {
                JSONObject grupoJSON = new JSONObject();
                grupoJSON.put("descricao", grupo.getDescricao());
                grupoJSON.put("percentualDescontoGrupo", grupo.getPercentualDescontoGrupo());
                grupoJSON.put("valorDescontoGrupo", grupo.getValorDescontoGrupo());

                TipoDesconto tipoDesconto = TipoDesconto.getTipoDesconto(grupo.getTipo());
                grupoJSON.put("tipoDesconto", tipoDesconto == null ? TipoDesconto.NA.getCodigo() : tipoDesconto.getCodigo());

                grupoJSON.put("tipo", grupo.getTipo());
                simulado.put("grupo", grupoJSON);
            }
            if(!UteisValidacao.emptyString(contratoControle.getContratoVO().getInformacaoRenovacaoCreditoTreino())){
                simulado.put("informacaoRenovacaoCreditoTreino", contratoControle.getContratoVO().getInformacaoRenovacaoCreditoTreino());
            }
            if (negociacao.optBoolean("arredondar", false)) {
                contratoControle.apresentarArrendondamento();
                simulado.put("valoresArredondados", contratoControle.getValoresArredondados());
            }

            return simulado;
        } catch (Exception e) {
            e.printStackTrace();
            return new JSONObject().put("erro", e.getMessage());
        }
    }

    private Double cupomParcelas(String chave, String cupom, ContratoControle contratoControle, JSONObject simulado) throws Exception {
        if (cupom == null || cupom.trim().equals("")) {
            return 0.0;
        }
        Double totalDescontos = 0.0;
        JSONArray parcelas = simulado.getJSONArray("parcelas");
        List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorCupom =
                (new OAMDService()).consultarPremiosPortadorCupomDesconto(contratoControle.getContratoVO().getIdCampanhaCupomDesconto(),
                        contratoControle.getContratoVO().getPlano().getDescricao(), chave);
        for (CampanhaCupomDescontoPremioPortadorVO premio : listaPremioPortadorCupom) {
            if (premio.getTipoPremio().equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE)) {
                Integer nrParcela = premio.getNumeroParcelaDescontoPortadorCupom();
                if (nrParcela != null) {
                    for (int i = 0; i < parcelas.length(); i++) {
                        JSONObject parcela = parcelas.getJSONObject(i);
                        if (parcela.getInt("nrParcela") == nrParcela && parcela.getString("descricao").contains("Parcela")) {
                            double desc = premio.calcularDescontoPremio(parcela.getDouble("valorParcela"));
                            parcela.put("valorParcela", parcela.getDouble("valorParcela") - desc);
                            parcela.put("cupom", cupom);
                            totalDescontos += desc;
                            break;
                        }
                    }

                }
            }
        }
        return totalDescontos;
    }


    private void ajustarParcelas(ContratoControle contratoControle, JSONObject simulado) throws Exception {
        if (contratoControle.getContratoVO().getPlano().getContratosEncerramDia() != null) {
            boolean acabaAntes = Calendario.menor(contratoControle.getContratoVO().getVigenciaAteAjustada(), contratoControle.getContratoVO().getPlano().getContratosEncerramDia());
            if (acabaAntes) {
                contratoControle.getTotalContrato();
            }
        } else {
            contratoControle.getTotalContrato();
        }
        List<ParcelasEditarNegociacaoNovo> parcelas = new ArrayList<>();
        parcelaAdesao(contratoControle, parcelas);
        parcelaProduto(contratoControle, parcelas);
        ParcelasEditarNegociacaoNovo parcela1 = new ParcelasEditarNegociacaoNovo();
        parcela1.setNrParcela(1);
        parcela1.setValorParcela(contratoControle.getValorParcelaComProdutoContrato());
        parcela1.setDescricao("Parcela 1");
        parcelas.add(parcela1);
        parcelas.addAll(contratoControle.getContratoVO().getListParcelasEditadas());
        parcelaAnuidade(contratoControle, parcelas);
        ContratoVO contratoVO = contratoControle.getContratoVO();
        if (contratoVO.isDeveGerarParcelasComValorDiferente()) {
            for(ParcelasEditarNegociacaoNovo parcela : parcelas){
                if((parcela.getNrParcela() == 1)
                        && !(isPrimeiraParcelaValorDiferente(contratoVO))){
                    continue;
                }
                for(PlanoRecorrenciaParcelaVO pr : contratoVO.getPlano().getPlanoRecorrencia().getParcelas()){
                    if((parcela.getNrParcela() == pr.getNumero())
                            && !(isParcelaAnuidadeOuAdesao(parcela))) {
                        parcela.setValorParcela(pr.getValorFinal());
                        break;
                    }
                }
            }
        }

        simularValoresEncerramentoContratoDia(contratoControle.getContratoVO(), parcelas, simulado);

        simulado.put("parcelas", Ordenacao.ordenarLista(parcelas, "nrParcela"));
    }

    private boolean isParcelaAnuidadeOuAdesao(ParcelasEditarNegociacaoNovo parcela) {
        boolean parcelaAnuidade = parcela.getDescricao().toUpperCase().contains("ANUIDADE");

        boolean parcelaAdesao = (parcela.getDescricao().trim().equals("Adesão")
                || parcela.getDescricao().trim().equals("Adesao"));

        return (parcelaAnuidade || parcelaAdesao);
    }

    private boolean isPrimeiraParcelaValorDiferente(ContratoVO contratoVO) {
        for(PlanoRecorrenciaParcelaVO pr : contratoVO.getPlano().getPlanoRecorrencia().getParcelas()){
            if(pr.getNumero() == 1)
                return true;
        }
        return false;
    }

    private void simularValoresEncerramentoContratoDia(ContratoVO contratoVO, List<ParcelasEditarNegociacaoNovo> parcelas, JSONObject simulado) {
        if (contratoVO.getPlano().getContratosEncerramDia() != null) {
            boolean acabaAntes = Calendario.menor(contratoVO.getVigenciaAteAjustada(), contratoVO.getPlano().getContratosEncerramDia());
            if (!acabaAntes) {
                Integer parcelasRemover = Calendario.diferencaEmMeses(contratoVO.getVigenciaAteAjustada(), contratoVO.getPlano().getContratosEncerramDia());
                if (!contratoVO.contratoIniciaDia1AcabaDia30()) {
                    parcelasRemover += 1; // ajuste no intervalo da diferença
                }
                Ordenacao.ordenarListaReverse(parcelas, "nrParcela");
                while (parcelasRemover < 0) {
                    parcelas.remove(0);
                    parcelasRemover++;
                }
                long count = parcelas.stream().filter(o -> o.getDescricao().startsWith("Parcela")).count();
                if (count == 1) {
                    double valorParcela = parcelas.stream().filter(o ->
                            o.getDescricao().startsWith("Parcela 1")).findFirst().get().getValorParcela();
                    parcelas.stream().filter(o ->
                            o.getDescricao().startsWith("Parcela 1")).findFirst().get().setValorParcela(
                            Uteis.calcularProRataEncerramentoContratoDia(contratoVO, valorParcela));
                } else {
                    Ordenacao.ordenarListaReverse(parcelas, "nrParcela");
                    for (ParcelasEditarNegociacaoNovo parcela : parcelas) {
                        if (parcela.getDescricao().startsWith("Parcela")) {
                            parcela.setValorParcela(Uteis.calcularProRataEncerramentoContratoDia(contratoVO, parcela.getValorParcela()));
                            break;
                        }
                    }
                }
                contratoVO.setVigenciaAteAjustada(contratoVO.getPlano().getContratosEncerramDia());
                contratoVO.setValorBaseCalculo(parcelas.stream()
                        .filter(o -> o.getDescricao() != null && o.getDescricao().startsWith("Parcela"))
                        .mapToDouble(ParcelasEditarNegociacaoNovo::getValorParcela)
                        .sum());
                contratoVO.setValorFinal(parcelas.stream()
                        .mapToDouble(ParcelasEditarNegociacaoNovo::getValorParcela)
                        .sum());

                simulado.put("valorFinal", contratoVO.getValorFinal());
                simulado.put("fim", contratoVO.getVigenciaAteAjustada().getTime());
                simulado.put("vigenciaAteAjustada", contratoVO.getVigenciaAteAjustada_Apresentar());
            }
        }
    }

    private void parcelaAdesao(ContratoControle contratoControle, List<ParcelasEditarNegociacaoNovo> parcelas) {
        Boolean separado = contratoControle.getContratoVO().isCobrarMatriculaSeparada();
        if (separado && (!UteisValidacao.emptyNumber(contratoControle.getContratoVO().getValorParcelasAdesao())
                || !UteisValidacao.emptyNumber(contratoControle.getContratoVO().getValorParcelasMatricula()))) {
            for (int i = 1; i <= contratoControle.getContratoVO().getNrParcelasAdesao(); i++) {
                ParcelasEditarNegociacaoNovo adesao = new ParcelasEditarNegociacaoNovo();
                adesao.setNrParcela(i);
                adesao.setValorParcela(contratoControle.getContratoVO().getPlano().getRecorrencia() ?
                        contratoControle.getContratoVO().getValorParcelasAdesao() :
                        contratoControle.getContratoVO().getValorParcelasMatricula());
                adesao.setDescricao((contratoControle.getContratoVO().getPlano().getRecorrencia() ? "Adesão " : "Matrícula ") +
                        (contratoControle.getContratoVO().getNrParcelasAdesao() == 1 ? "" : i));
                if(!UteisValidacao.emptyNumber(adesao.getValorParcela())){
                    parcelas.add(adesao);
                }
            }
        }
    }

    private void parcelaProduto(ContratoControle contratoControle, List<ParcelasEditarNegociacaoNovo> parcelas) {
        Boolean separado = contratoControle.getContratoVO().isCobrarProdutoSeparado();
        if (separado) {
            for (int i = 1; i <= contratoControle.getContratoVO().getNrVezesParcelarProduto(); i++) {
                ParcelasEditarNegociacaoNovo produto = new ParcelasEditarNegociacaoNovo();
                produto.setNrParcela(i);
                produto.setValorParcela(contratoControle.getContratoVO().getValorParcelasProduto());
                produto.setDescricao("Produtos " +
                        (contratoControle.getContratoVO().getValorParcelasProduto() == 1 ? "" : i));
                if(!UteisValidacao.emptyNumber(produto.getValorParcela())){
                    parcelas.add(produto);
                }
            }
        }
    }

    private void parcelaAnuidade(ContratoControle contratoControle, List<ParcelasEditarNegociacaoNovo> parcelas) {

        if (!contratoControle.getContratoVO().getPlano().getRecorrencia()) {
            return;
        }

        if (contratoControle.getContratoVO().getPlano().getPlanoRecorrencia().isParcelarAnuidade()) {
            for (PlanoAnuidadeParcelaVO parc : contratoControle.getContratoVO().getPlano().getPlanoRecorrencia().getParcelasAnuidade()) {
                ParcelasEditarNegociacaoNovo anuidade = new ParcelasEditarNegociacaoNovo();
                anuidade.setNrParcela(parc.getParcela());
                anuidade.setValorParcela(parc.getValorTotal());
                anuidade.setDescricao("Anuidade " + parc.getNumero());
                if(!UteisValidacao.emptyNumber(anuidade.getValorParcela())){
                    parcelas.add(anuidade);
                }
            }
            return;
        }

        if (contratoControle.getContratoVO().getPlano().getPlanoRecorrencia().getMesAnuidade() > 0) {
            int mesData = Uteis.getMesData(Calendario.hoje());
            ParcelasEditarNegociacaoNovo anuidade = new ParcelasEditarNegociacaoNovo();
            if (contratoControle.getContratoVO().getPlano().getPlanoRecorrencia().getMesAnuidade() < mesData) {
                anuidade.setNrParcela(((contratoControle.getContratoVO().getPlano().getPlanoRecorrencia().getMesAnuidade() + 12)
                        - mesData) + 1);
            } else {
                anuidade.setNrParcela((contratoControle.getContratoVO().getPlano().getPlanoRecorrencia().getMesAnuidade()
                        - mesData) + 1);
            }
            anuidade.setValorParcela(contratoControle.getContratoVO().getPlano().getPlanoRecorrencia().getValorAnuidade());
            anuidade.setDescricao("Anuidade");
            if(!UteisValidacao.emptyNumber(anuidade.getValorParcela())){
                parcelas.add(anuidade);
            }
        }
    }

    private ContratoControle obterContratoControle(UsuarioVO usuarioVO, JSONObject negociacao,
                                                   AcessoControle acessoControle, Boolean gravar) throws Exception {
        ClienteVO clienteVO = new ClienteVO();
        if (!UteisValidacao.emptyNumber(negociacao.optInt("cliente"))) {
            clienteVO = acessoControle.getClienteDao().consultarPorChavePrimaria(negociacao.optInt("cliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        PlanoVO planoVO = acessoControle.getPlanoDao().consultarPorChavePrimaria(negociacao.optInt("plano"), false, Uteis.NIVELMONTARDADOS_TODOS);
        Map<Integer, Double> valoresOriginaisModalidades = new HashMap<>();
        for (PlanoModalidadeVO planoModalidadeVO : planoVO.getPlanoModalidadeVOs()) {
            try {
                valoresOriginaisModalidades.put(planoModalidadeVO.getModalidade().getCodigo(), planoModalidadeVO.getModalidade().getValorMensal());
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        planoVO.filtrarPlanoProdutoSugeridoAtivoPlano();
        ContratoVO novoContrato = new ContratoVO();
        novoContrato.setPlano(planoVO);
        EmpresaVO empresaLogada = acessoControle.getEmpresaDao().consultarPorChavePrimaria(negociacao.optInt("empresa"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        novoContrato.setEmpresa(empresaLogada);
        novoContrato.setCliente(clienteVO);
        novoContrato.setPessoa(clienteVO.getPessoa());


        ContratoControle contratoControle;
        if (negociacao.has("contratoBase") && negociacao.getInt("contratoBase") > 0) {
            ContratoVO contratocontratoBase = acessoControle.getContratoDao().consultarPorCodigo(negociacao.getInt("contratoBase"),
                    Uteis.NIVELMONTARDADOS_TODOS);
            if (!contratocontratoBase.getEmpresa().getCodigo().equals(negociacao.optInt("empresa"))) {
               throw new Exception("Renovações e rematrículas só podem ser realizadas para a mesma unidade do contrato de origem (" +
                       contratocontratoBase.getEmpresa().getNome() +
                       ").");
            }
            contratocontratoBase.obterSituacaoContratoRenovacaoRematricula();
            contratocontratoBase.setPlano(planoVO);
            contratocontratoBase.setEmpresa(novoContrato.getEmpresa());
            contratocontratoBase.setPessoa(clienteVO.getPessoa());
            contratocontratoBase.setObservacao("");
            contratoControle = new ContratoControle(clienteVO, contratocontratoBase);
        } else {
            contratoControle = new ContratoControle(clienteVO);
            contratoControle.setContratoVO(novoContrato);
        }
        contratoControle.setEmpresa(empresaLogada);
        contratoControle.inicializarDadosReferenteAoPlano();
        //limpar a data da primeira parcela para evitar problema de parcela com data retroativa
        contratoControle.getContratoVO().setDataPrimeiraParcela(null);
        contratoControle.getContratoVO().setConvenioDesconto(new ConvenioDescontoVO());
        contratoControle.getContratoVO().setMovProdutoVOs(new ArrayList());

        if (negociacao.has("dataLancamento")) {
            long dataLancamento = negociacao.getLong("dataLancamento");
            contratoControle.setResponsavelDataBase(usuarioVO);
            contratoControle.atualizarDatas(new Date(dataLancamento));
        }

        if (negociacao.has("observacao")) {
            contratoControle.getContratoVO().setObservacao(negociacao.getString("observacao"));
        }

        if (negociacao.has("inicio")) {
            Date inicio = new Date(negociacao.getLong("inicio"));
            if (Calendario.menor(inicio, contratoControle.getContratoVO().getDataLancamento()) && !contratoControle.getContratoVO().isContratoRenovacao()) {
                throw new Exception("Início do contrato não pode ser menor do que a data de lançamento.");
            }
            contratoControle.setDataInicioContrato(inicio);
        }

        acaoSelecionarPlanoTelaNova(contratoControle, usuarioVO, planoVO, acessoControle);

        boolean cobrarProdutosSeparados = false;

        if (negociacao.has("configuracoesAvancadas")) {
            JSONObject configuracoesAvancadas = negociacao.getJSONObject("configuracoesAvancadas");

            if (configuracoesAvancadas.has("diaPrimeiraParcela") && configuracoesAvancadas.optLong("diaPrimeiraParcela") > 0L) {
                long dataPrimeiraParcela = configuracoesAvancadas.getLong("diaPrimeiraParcela");
                contratoControle.getContratoVO().setDataPrimeiraParcela(new Date(dataPrimeiraParcela));
                if (Calendario.menor(contratoControle.getContratoVO().getDataPrimeiraParcela(), contratoControle.getContratoVO().getDataLancamento())) {
                    throw new Exception("A data da primeira parcela não pode ser anterior a data de lançamento do contrato.");
                }
            }
            contratoControle.getContratoVO().setNrParcelasAdesao(configuracoesAvancadas.getInt("vezesCobrarMatricula"));
            contratoControle.getContratoVO().setNrVezesParcelarProduto(configuracoesAvancadas.getInt("vezesCobrarProdutosSeparados"));
            contratoControle.getContratoVO().setNrVezesParcelarMatricula(configuracoesAvancadas.getInt("vezesCobrarMatricula"));
            contratoControle.getContratoVO().setGerarParcelaParaProdutos(!configuracoesAvancadas.getBoolean("dividirProdutoParcela"));
            contratoControle.getContratoVO().setCobrarProdutoSeparado(configuracoesAvancadas.getBoolean("cobrarProdutosSeparados"));
            cobrarProdutosSeparados = configuracoesAvancadas.getBoolean("cobrarProdutosSeparados");
            contratoControle.getContratoVO().setCobrarMatriculaSeparada(configuracoesAvancadas.getBoolean("cobrarMatricula"));
            contratoControle.getContratoVO().setDividirProdutosNasParcelas(configuracoesAvancadas.getBoolean("dividirProdutoParcela"));
            contratoControle.setIndiceDiaVencimento(Uteis.getParamJsonInt(configuracoesAvancadas, "diaProrata"));
        }

        for (PlanoDuracaoVO obj : contratoControle.getContratoVO().getPlano().getPlanoDuracaoVOs()) {
            if (obj.getCodigo() == negociacao.optInt("duracao")) {
                obj.setDuracaoEscolhida(true);
                contratoControle.setPlanoDuracaoSelecionada(obj);
                contratoControle.selecionarDuracao(obj);
            }
        }
        if(planoVO.isVendaCreditoTreino()){
            selecionarHorarioPlanoCreditoTreino(contratoControle, negociacao.optInt("horario"));
        } else {
            for (PlanoHorarioVO obj : contratoControle.getContratoVO().getPlano().getPlanoHorarioVOs()) {
                if (obj.getCodigo() == negociacao.optInt("horario")) {
                    obj.getHorario().setHorarioEscolhida(true);
                    contratoControle.selecionarHorario(obj);
                }
            }
        }

        if (negociacao.has("credito")
                && !contratoControle.getContratoVO().getPlano().isCreditoSessao()
                && contratoControle.getContratoVO().getPlano().isVendaCreditoTreino()) {
            contratoControle.setPlanoDuracaoCreditoTreinoSelecionado(new PlanoDuracaoCreditoTreinoVO(
                    negociacao.getJSONObject("credito"), contratoControle.getContratoVO().getPlano(), true));
            contratoControle.selecionarPlanoDuracaoCreditoTreino(contratoControle.getPlanoDuracaoCreditoTreinoSelecionado());
        }

        if (negociacao.has("produtos") && negociacao.getJSONArray("produtos").length() > 0) {
            for (int i = 0; i < negociacao.getJSONArray("produtos").length(); i++) {
                JSONObject produtoimento = negociacao.getJSONArray("produtos").getJSONObject(i);
                boolean adicionado = false;
                int quantidade = 0;
                try {
                    quantidade = produtoimento.optInt("quantidade");
                } catch (Exception ex){
                    quantidade = 0;
                }
                for (Object obj : contratoControle.getContratoVO().getContratoPlanoProdutoSugeridoVOs()) {
                    ContratoPlanoProdutoSugeridoVO produtoSugeridoVO = (ContratoPlanoProdutoSugeridoVO) obj;
                    if (produtoSugeridoVO.getPlanoProdutoSugerido().getProduto().getCodigo() ==
                            produtoimento.optInt("codigo")) {
                        produtoSugeridoVO.getPlanoProdutoSugerido().setProdutoSugeridoEscolhida(true);
                        produtoSugeridoVO.getPlanoProdutoSugerido().setQuantidade(produtoimento.getInt("quantidade"));

                        if(produtoimento.optDouble("desconto", 0.0) == 0.0){
                            contratoControle.setDescontoManualProduto(0.0);
                            contratoControle.setContratoPlanoProdutoSugerido(produtoSugeridoVO);
                            contratoControle.salvarDescontoManual();
                        }

                        if(produtoimento.optDouble("desconto", 0.0) > 0.0){
                            contratoControle.setDescontoManualProduto(produtoimento.optDouble("desconto", 0.0));
                            contratoControle.setContratoPlanoProdutoSugerido(produtoSugeridoVO);
                            contratoControle.salvarDescontoManual();
                        } else {
                            JSONObject desconto = produtoimento.optJSONObject("descontoPadrao");
                            if (desconto == null && (produtoimento.getString("descricao").equalsIgnoreCase("MATRICULA") ||
                                    produtoimento.getString("descricao").equalsIgnoreCase("ADESAO") ||
                                    produtoimento.getString("descricao").equalsIgnoreCase("ADESÃO") ||
                                    produtoimento.getString("descricao").equalsIgnoreCase("MATRÍCULA")) && contratoControle.getContratoVO().isCobrarMatriculaSeparada()) {
                                contratoControle.getContratoVO().setValorMatricula(contratoControle.getContratoVO().getValorMatricula() - produtoimento.optDouble("desconto", 0.0));
                            }
                            if (desconto != null && desconto.optDouble("valor", 0.0) > 0.0) {
                                produtoSugeridoVO.getPlanoProdutoSugerido().getProduto().setDesconto(new DescontoVO());
                                produtoSugeridoVO.getPlanoProdutoSugerido().getProduto().getDesconto().setCodigo(desconto.getInt("codigo"));
                                produtoSugeridoVO.getPlanoProdutoSugerido().getProduto().getDesconto().setDescricao(desconto.getString("descricao"));
                                produtoSugeridoVO.getPlanoProdutoSugerido().getProduto().getDesconto().setValor(desconto.getDouble("valor"));
                                produtoSugeridoVO.getPlanoProdutoSugerido().getProduto().getDesconto().setTipoDesconto(
                                        TipoDesconto.getTipoDesconto(desconto.getString("tipo")));


                                if ((produtoimento.getString("descricao").equalsIgnoreCase("MATRICULA") ||
                                        produtoimento.getString("descricao").equalsIgnoreCase("ADESAO") ||
                                        produtoimento.getString("descricao").equalsIgnoreCase("ADESÃO") ||
                                        produtoimento.getString("descricao").equalsIgnoreCase("MATRÍCULA")) && contratoControle.getContratoVO().isCobrarMatriculaSeparada()) {
                                    if (TipoDesconto.PE.equals(TipoDesconto.getTipoDesconto(desconto.getString("tipo")))) {
                                        double percentual = (desconto.getDouble("valor") / 100.0);
                                        contratoControle.getContratoVO().setValorMatricula(contratoControle.getContratoVO().getValorMatricula() - (percentual * contratoControle.getContratoVO().getValorMatricula()));
                                    } else {
                                        contratoControle.getContratoVO().setValorMatricula(contratoControle.getContratoVO().getValorMatricula() - desconto.getDouble("valor"));
                                    }
                                }

                            }
                        }
                        adicionado = true;
                        contratoControle.calcularTotalProdutosContrato();
                        contratoControle.consultarValorMatriculaEProdutosPlanoSelecionado();
                    }
                }
                try {
                    if (!adicionado) {
                        ProdutoVO produtoVO = acessoControle.getProdutoDao().consultarPorChavePrimaria(produtoimento.optInt("codigo"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        try {
                             produtoVO.setValorFinal(produtoimento.optDouble("valor"));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        JSONObject desconto = produtoimento.optJSONObject("descontoPadrao");
                        produtoVO.setDesconto(new DescontoVO());
                        if (desconto != null && desconto.optDouble("valor", 0.0) > 0.0) {
                            produtoVO.getDesconto().setCodigo(desconto.getInt("codigo"));
                            produtoVO.getDesconto().setDescricao(desconto.getString("descricao"));
                            produtoVO.getDesconto().setValor(desconto.getDouble("valor"));
                            produtoVO.getDesconto().setTipoDesconto(
                                    TipoDesconto.getTipoDesconto(desconto.getString("tipo")));
                        } else {
                            produtoVO.getDesconto().setValor(produtoimento.optDouble("desconto", 0.0));
                        }
                        contratoControle.selecionarProduto(produtoVO, quantidade);
                        contratoControle.consultarValorMatriculaEProdutosPlanoSelecionado();
                    }
                } catch (Exception e) {
                }
            }
        }

        if(negociacao.has("descontoRenovacaoAntecipada")
                && contratoControle.getDescontoAntecipado() != null
                && !UteisValidacao.emptyNumber(contratoControle.getDescontoAntecipado().getCodigo())
                && negociacao.getBoolean("descontoRenovacaoAntecipada")){
            contratoControle.getDescontoAntecipado().setDescontoEscolhido(true);
            contratoControle.getContratoVO().getPlano().setDescontoAntecipado(contratoControle.getDescontoAntecipado());
        }

        addPacote(negociacao, contratoControle, false);
        if (cobrarProdutosSeparados && contratoControle.getContratoVO().getTotalProdutosCobrarSeparado() > 0) {
            contratoControle.getContratoVO().setCobrarProdutoSeparado(true);
        }
        if (negociacao.has("modalidades") && negociacao.getJSONArray("modalidades").length() > 0) {
            for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
                contratoModalidadeVO.getModalidade().setModalidadeEscolhida(false);
            }
            for (int i = 0; i < negociacao.getJSONArray("modalidades").length(); i++) {
                JSONObject modalidade = negociacao.getJSONArray("modalidades").getJSONObject(i);
                for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
                    if (contratoModalidadeVO.getModalidade().getCodigo() == modalidade.optInt("codigo")) {
                        Double valorModalidade = modalidade.optDouble("valorModalidade", 0.0);
                        if(UteisValidacao.emptyNumber(valorModalidade) && valoresOriginaisModalidades.containsKey(modalidade.optInt("codigo"))){
                            valorModalidade = valoresOriginaisModalidades.get(modalidade.optInt("codigo"));
                        }
                        contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);
                        contratoModalidadeVO.setValorFinalModalidade(valorModalidade);
                        contratoModalidadeVO.setValorModalidade(valorModalidade);
                        contratoControle.marcarModalidade(contratoModalidadeVO);
                        contratoModalidadeVO.setNrVezesSemana(modalidade.optInt("nrvezes"));
                        if (modalidade.optJSONArray("configsVezes") != null
                                && modalidade.getJSONArray("configsVezes").length() == 1) {
                            JSONObject configsVezes = modalidade.getJSONArray("configsVezes").getJSONObject(0);
                            contratoModalidadeVO.setNrVezesSemana(configsVezes.getInt("vezes"));
                        }
                        obterPlanoNrVezes(contratoModalidadeVO, contratoControle);
                        contratoControle.montarValorContratoPlanoDuracao();
                        if (contratoModalidadeVO.getModalidade().getUtilizarTurma()
                            && modalidade.has("turmas") && modalidade.getJSONArray("turmas").length() > 0) {
                            for (int j = 0; j < modalidade.getJSONArray("turmas").length(); j++) {
                                HorarioTurmaVO horarioTurmaVO = acessoControle.getHorarioTurmaDao().consultarPorCodigo(
                                        modalidade.getJSONArray("turmas").getJSONObject(j).getInt("codigo"),
                                        Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                horarioTurmaVO.setHorarioTurmaEscolhida(true);
                                TurmaVO turmaVO = acessoControle.getTurmaDao().consultarPorChavePrimaria(horarioTurmaVO.getTurma(),
                                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                                turmaVO.setTurmaEscolhida(true);
                                adicionaNaModalidade(turmaVO, horarioTurmaVO, contratoModalidadeVO);
                            }
                            contratoControle.setContratoModalidade(contratoModalidadeVO);
                            contratoControle.salvarHorarios();
                        } else {
                            contratoControle.setContratoModalidade(contratoModalidadeVO);
                        }
                    }
                }
            }
        }
        addPacote(negociacao, contratoControle, true);

        if (negociacao.has("tipoContrato")) {
            if (negociacao.has("alterouTipoContrato")) {
                contratoControle.getContratoVO().setAlterouTipoDoContrato(negociacao.getBoolean("alterouTipoContrato"));
            }
            contratoControle.getContratoVO().setContratoAgendadoEspontaneo(TipoContratoEnum.valueOf(negociacao.getString("tipoContrato")));
        }

        if (negociacao.optInt("convenioDesconto") != 0) {
            contratoControle.setAutorizacaoDescontoConvenio(true);
            contratoControle.getContratoVO().getConvenioDesconto().setCodigo(negociacao.getInt("convenioDesconto"));
            contratoControle.selecionarConvenioDescontoAtualizandoValores();
        }
        if (negociacao.optDouble("descontoExtraValor") > 0.0
                || negociacao.optDouble("descontoExtraPercentual") > 0.0) {
            try {
                contratoControle.getContratoVO().setDesconto(new ProdutoVO());
                for (Object obj : contratoControle.getListaConsultaProdutoDesconto()) {
                    SelectItem item = (SelectItem) obj;
                    if (!UteisValidacao.emptyNumber((Number) item.getValue())) {
                        contratoControle.getContratoVO().getDesconto().setCodigo((Integer) item.getValue());
                        contratoControle.gerenciarApresentarCamposDesconto();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (negociacao.optDouble("descontoExtraPercentual") > 0.0) {
                contratoControle.getContratoVO().setTipoDesconto("PE");
                contratoControle.getContratoVO().setValorDesconto(negociacao.optDouble("descontoExtraPercentual"));
                contratoControle.getContratoVO().setValorDescontoPorcentagem(negociacao.optDouble("descontoExtraPercentual"));
            }
            if (negociacao.optDouble("descontoExtraValor") > 0.0) {
                contratoControle.getContratoVO().setTipoDesconto("VA");
                contratoControle.getContratoVO().setValorDesconto(negociacao.optDouble("descontoExtraValor"));
                contratoControle.getContratoVO().setValorDescontoEspecifico(negociacao.optDouble("descontoExtraValor"));
                contratoControle.setValorDescontoPorcentagem(0.0);
                contratoControle.getContratoVO().setValorDescontoPorcentagem(0.0);
                contratoControle.calcularContrato();
            }
            contratoControle.setApresentarValorDescontoEspecifico(contratoControle.getContratoVO().getTipoDesconto().equals("VA") ? true : false);
            contratoControle.setApresentarValorDescontoPorcentagem(contratoControle.getContratoVO().getTipoDesconto().equals("PE") ? true : false);
            validarLimiteDescontoPorUsuario(usuarioVO, negociacao, acessoControle, contratoControle, novoContrato);
        }

        if (negociacao.has("vencimentoCartao")) {
            int diaVencimentoCartao = negociacao.getInt("vencimentoCartao");
            if (contratoControle.getContratoVO().isDeveGerarParcelasComValorDiferente()) {
                diaVencimentoCartao = Uteis.getDiaMesData(contratoControle.getContratoVO().getVigenciaDe());
            }
            contratoControle.getContratoVO().setDiaVencimentoCartaoRecorrencia(diaVencimentoCartao);
            contratoControle.setDiaVencimentoCartaoRecorrencia(diaVencimentoCartao);
            contratoControle.setIndiceDiaVencimento(diaVencimentoCartao);
        }

        if (negociacao.has("condicao") && !UteisValidacao.emptyNumber(negociacao.optInt("condicao"))) {
            for (PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO : contratoControle.getContratoVO().getPlanoDuracao().getPlanoCondicaoPagamentoVOs()) {
                if (planoCondicaoPagamentoVO.getCodigo() == negociacao.optInt("condicao")) {
                    planoCondicaoPagamentoVO.getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
                    contratoControle.selecionarCondicaoPagamento(planoCondicaoPagamentoVO);
                }
            }
            contratoControle.mostraValoresDeComoFicouCondicaoPagamento();
            contratoControle.montarModalEdicaoParcelasNegociacao(false);
            if (!gravar && !UteisValidacao.emptyNumber(contratoControle.getIndiceDiaVencimento())) {
                contratoControle.selecionarProrata();
            }
            contratoControle.getContratoVO().setCodigo(999);
        }

        if (negociacao.optJSONObject("arredondamento") != null
                && negociacao.getJSONObject("arredondamento").optDouble("valorParcelas", 0.0) > 0.0) {
            double valorEntrada = negociacao.getJSONObject("arredondamento").optDouble("valorEntrada", 0.0);
            ArredondamentoParcelaTO obj = new ArredondamentoParcelaTO(
                    valorEntrada > 0.0 ? valorEntrada : null,
                    negociacao.getJSONObject("arredondamento").getDouble("valorParcelas"));
            contratoControle.selecionarArredondamento(obj);
            contratoControle.getContratoVO().setListParcelasEditadas(new ArrayList<>());
            contratoControle.mostraValoresDeComoFicouCondicaoPagamento();
            contratoControle.montarModalEdicaoParcelasNegociacao(false);
            if (!gravar && !UteisValidacao.emptyNumber(contratoControle.getIndiceDiaVencimento())) {
                contratoControle.selecionarProrata();
            }
        }

        String cupom = "";
        try {
            cupom = negociacao.getString("cupom");
        } catch (Exception ignore) {
        }

        if (!cupom.trim().equals("")) {
            contratoControle.setNumeroCupom(cupom);
            contratoControle.getContratoVO().setChave(acessoControle.getKey());
            contratoControle.validarCupomCupom(acessoControle.getKey());
            contratoControle.getContratoVO().setPlano(planoVO);
            contratoControle.verificarCampanhaCupomDescontoVigente();
            if (!contratoControle.isUtilizaCupomDesconto()) {
                throw new Exception("Cupom não habilitado para este plano.");
            }

        }

        if (negociacao.has("origemSistema")) {
            contratoControle.getContratoVO().setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(negociacao.optInt("origemSistema")));
        }

        return contratoControle;
    }

    private void validarLimiteDescontoPorUsuario(UsuarioVO usuarioVO, JSONObject negociacao,
                                                 AcessoControle acessoControle,
                                                 ContratoControle contratoControle, ContratoVO novoContrato) throws Exception {
        UsuarioVO usuarioDesconto = usuarioVO;
        if(negociacao.has("usuarioAutorizouDesconto")
                && negociacao.optInt("usuarioAutorizouDesconto") > 0
                && negociacao.optInt("usuarioAutorizouDesconto") != usuarioVO.getCodigo()){
            usuarioDesconto = acessoControle.getUsuarioDao().consultarPorChavePrimaria(negociacao.optInt("usuarioAutorizouDesconto"), Uteis.NIVELMONTARDADOS_MINIMOS);
        }

        if (contratoControle.getContratoVO().getEmpresa() != null && contratoControle.getContratoVO().getEmpresa().isLimitarDescontosPorPerfil()) {
            if (negociacao.optDouble("descontoExtraPercentual") > 0.0 || negociacao.optDouble("descontoExtraValor") > 0.0) {
                contratoControle.getContratoVO().setResponsavelAutorizacaoDesconto(usuarioDesconto);
                contratoControle.getContratoVO().getResponsavelAutorizacaoDesconto().setPorcetagemDescontoContrato(
                        acessoControle.getPerfilAcessoDao().consultarMaximoDescontoPorUsuarioEmpresa(usuarioDesconto.getCodigo(), novoContrato.getEmpresa().getCodigo()));
                String mensagemDescontoExcedido = contratoControle.calcularDescontoContratoRet(true);
                if (!UteisValidacao.emptyString(mensagemDescontoExcedido)) {
                    contratoControle.getContratoVO().setValorDescontoPorcentagem(0.0);
                    contratoControle.getContratoVO().setValorDescontoEspecifico(0.0);
                    contratoControle.getContratoVO().setValorDesconto(0.0);
                    contratoControle.setApresentarValorDescontoPorcentagem(false);
                    contratoControle.setApresentarValorDescontoEspecifico(false);
                    contratoControle.setValorDescontoPorcentagem(0.0);
                    contratoControle.setValorDescontoEspecifico(0.0);
                    throw new Exception(mensagemDescontoExcedido.replace("<br>", " "));
                }
            }
        }
    }

    private void obterPlanoNrVezes(ContratoModalidadeVO contratoModalidadeVO, ContratoControle contratoControle){
        try {
            List<PlanoModalidadeVO> planoModalidadeVOs = contratoControle.getContratoVO().getPlano().getPlanoModalidadeVOs();
            for (PlanoModalidadeVO planoModalidade : planoModalidadeVOs) {
                if (planoModalidade.getModalidade().getCodigo().intValue() == contratoModalidadeVO.getModalidade().getCodigo()) {
                    if (contratoModalidadeVO.getNrVezesSemana() != 0) {
                        Iterator j = planoModalidade.getPlanoModalidadeVezesSemanaVOs().iterator();
                        while (j.hasNext()) {
                            PlanoModalidadeVezesSemanaVO planoVezesSemana = (PlanoModalidadeVezesSemanaVO) j.next();
                            if (planoVezesSemana.getNrVezes().equals(contratoModalidadeVO.getNrVezesSemana())) {
                                planoVezesSemana.setVezeSemanaEscolhida(true);
                                contratoModalidadeVO.setPlanoVezesSemanaVO(planoVezesSemana);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            Uteis.logar(e, NegociacaoService.class);
        }

    }

    private void addPacote(JSONObject negociacao, ContratoControle contratoControle, boolean modalidadesEspecificas) throws Exception {
        if (negociacao.has("pacotes") && negociacao.getJSONArray("pacotes").length() > 0) {
            for (int i = 0; i < negociacao.getJSONArray("pacotes").length(); i++) {
                JSONObject pacote = negociacao.getJSONArray("pacotes").getJSONObject(i);
                PlanoComposicaoVO pacoteEscolhido = null;
                for (Object obj : contratoControle.getContratoVO().getPlano().getPlanoComposicaoVOs()) {
                    PlanoComposicaoVO pmVO = (PlanoComposicaoVO) obj;
                    if (pmVO.getComposicao().getCodigo() == pacote.optInt("codigo") && pmVO.getComposicao().isModalidadesEspecificas() == modalidadesEspecificas) {
                        pacoteEscolhido = pmVO;
                        pmVO.getComposicao().setComposicaoEscolhida(true);
                        if(pacoteEscolhido.getComposicao().isModalidadesEspecificas()){
                            for (ComposicaoModalidadeVO pcmVO : pmVO.getComposicao().getComposicaoModalidadeVOs()) {
                                for (ContratoModalidadeVO cmVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
                                    if (Objects.equals(cmVO.getModalidade().getCodigo(), pcmVO.getModalidade().getCodigo())) {
                                        cmVO.getModalidade().setComposicao(true);
                                    }
                                }
                            }
                        }
                        break;
                    }
                }

                if (pacoteEscolhido != null) {
                    contratoControle.setContratoComposicaoVO(new ContratoComposicaoVO());
                    contratoControle.marcarComposicao(pacoteEscolhido);
                    contratoControle.setUltimaComposicaoEscolhida(pacoteEscolhido);
                }
            }
        }
    }

    public JSONObject gravarContrato(String key, JSONObject negociacao) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            Conexao.guardarConexaoForJ2SE(key, acessoControle.getCon());
            UsuarioVO usuarioVO = acessoControle.getUsuarioDao().consultarPorChavePrimaria(negociacao.getInt("usuario"), Uteis.NIVELMONTARDADOS_VALIDACAOACESSO);
            ContratoControle contratoControle = obterContratoControle(usuarioVO, negociacao, acessoControle, true);
            Calendario.setDateThread(contratoControle.getContratoVO().getDataLancamento());
            String validacao = contratoControle.fecharNegociacao(null, true);
            if (validacao.equals("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            contratoControle.getContratoVO().setListParcelasEditadas(new ArrayList<>());
            if (negociacao.has("parcelas")) {
                JSONArray parcelas = negociacao.getJSONArray("parcelas");
                for (int i = 0; i < parcelas.length(); i++) {
                    JSONObject parcela = parcelas.getJSONObject(i);

                    String descricao = parcela.getString("descricao");
                    if (descricao.equals("Parcela 1")) {
                        contratoControle.getContratoVO().setValorPrimeiraParcelaEdicao(parcela.getDouble("valorParcela"));
                    } else if (descricao.startsWith("Parcela")) {
                        ParcelasEditarNegociacaoNovo parcelaEditada = new ParcelasEditarNegociacaoNovo();
                        parcelaEditada.setDescricao(descricao);
                        parcelaEditada.setValorParcela(parcela.getDouble("valorParcela"));
                        parcelaEditada.setNrParcela(parcela.getInt("nrParcela"));
                        contratoControle.getContratoVO().getListParcelasEditadas().add(parcelaEditada);
                    }
                }
            }

            if (negociacao.has("configuracoesAvancadas")) {
                JSONObject configuracoesAvancadas = negociacao.getJSONObject("configuracoesAvancadas");

                if (configuracoesAvancadas.has("diaPrimeiraParcela") && configuracoesAvancadas.optLong("diaPrimeiraParcela") > 0L) {
                    long dataPrimeiraParcela = configuracoesAvancadas.getLong("diaPrimeiraParcela");
                    contratoControle.getContratoVO().setDataPrimeiraParcela(new Date(dataPrimeiraParcela));
                }
            }

            contratoControle.getContratoVO().povoarInformacoesLancarContratoEncerramentoContratoDia(contratoControle.getContratoVO());

            if (contratoControle.getContratoVO().getPlano().isVendaCreditoTreino() && contratoControle.getContratoVO().getPlano().isCreditoSessao()) {
                if (contratoControle.getContratoVO().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == null) {
                    throw new Exception("Não foi encontrada configuração de crédito para esse plano e duração, por favor verifique o cadastro do plano.");
                }
            }

            if (contratoControle.getContratoVO().getPlano() != null && contratoControle.getContratoVO().getPlano().getPlanoTipo() != null &&
                    !UteisValidacao.emptyNumber(contratoControle.getContratoVO().getPlano().getPlanoTipo().getLimiteVendas())) {
                Integer qtdContratos = acessoControle.getContratoDao().contarContratosAtivosTipoPlano(contratoControle.getContratoVO().getPlano().getCodigo(),
                        contratoControle.getContratoVO().getPlano().getPlanoTipo().getCodigo());
                if (qtdContratos >= contratoControle.getContratoVO().getPlano().getPlanoTipo().getLimiteVendas()) {
                    throw new Exception("Não é possível realizar a negociação desse contrato, pois já chegou no limite de vendas pelo tipo de plano " + contratoControle.getContratoVO().getPlano().getPlanoTipo().getNome());
                }
            }

            if (negociacao.has("alterouDataInicioContrato")) {
                contratoControle.getContratoVO().setAlterouDataInicioContrato(negociacao.optBoolean("alterouDataInicioContrato"));
            }
            if(contratoControle.getContratoVO().isContratoRenovacao()){
                contratoControle.getContratoVO().setCobrarMatriculaSeparada(false);
            }
            contratoControle.getContratoVO().setVendaContrato(true);
            String resultadoGravarContrato = contratoControle.gravar(usuarioVO, true);
            if(UteisValidacao.emptyNumber(contratoControle.getContratoVO().getCodigo())
                    || !verificarContratoConsistencia(contratoControle.getContratoVO().getCodigo(), acessoControle)){
                throw new Exception("Erro ao gravar contrato. Tente novamente.");
            }

            JSONObject contrato = new JSONObject().put("contrato", contratoControle.getContratoVO().getCodigo());

            boolean cobrancaAutomaticaHabilitada = getFacade().getEmpresa().isHabilitarCobrancaAutomaticaNaVenda(contratoControle.getContratoVO().getEmpresa().getCodigo());
            if (resultadoGravarContrato != null && resultadoGravarContrato.equals("concluirNegociacao") && cobrancaAutomaticaHabilitada) {
                if (!UteisValidacao.emptyNumber(negociacao.optInt("codigoConvenio"))) {
                    realizarCobrancaAutomatica(contratoControle.getContratoVO(), negociacao.optInt("codigoConvenio"), key);
                }
            }


            if (negociacao.has("gerarLink") && negociacao.optBoolean("gerarLink")) {
                try {
                    ClienteVO clienteVO = new ClienteVO();
                    if (!UteisValidacao.emptyNumber(negociacao.optInt("cliente"))) {
                        clienteVO = acessoControle.getClienteDao().consultarPorChavePrimaria(negociacao.optInt("cliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                    VendasOnlineService vendasOnlineService = new VendasOnlineService(null, Conexao.getFromSession());
                    String link = vendasOnlineService.obterLinkPagamentoVendasOnline(key, clienteVO, clienteVO.getEmpresa().getCodigo(),
                            true, OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO, null, usuarioVO, null, null, null, 1);
                    contrato.put("link", link);
                } catch (Exception e) {
                    contrato.put("link", "-");
                }
            }
            if (contrato.has("contrato")) {
                if (contratoControle.getContratoVO().getPlano().getContratosEncerramDia() != null) {
                    boolean acabaAntes = Calendario.menor(contratoControle.getContratoVO().getVigenciaAteAjustada(), contratoControle.getContratoVO().getPlano().getContratosEncerramDia());
                    if (!acabaAntes) {
                        acessoControle.getMovParcelaDao().alterarSomenteVencimentoUltimaParcelaContratoSemCommit(contratoControle.getContratoVO().getCodigo(), contratoControle.getContratoVO().getPlano().getContratosEncerramDia());
                    }
                }
                ServicoNotificacaoPush.enviaNotificacaoContrato(key, contratoControle.getContratoVO().getEmpresa().getCodigo(), false, contratoControle.getContratoVO().getOrigemSistema().getDescricao(),
                        contratoControle.getContratoVO().getValorFinal(), contratoControle.getContratoVO().getPlano().getDescricao(), contratoControle.getContratoVO().getContratoDuracao().getNumeroMeses(), contratoControle.getContratoVO().getEmpresa().getNome(), contratoControle.getContratoVO().getCliente().getCodigo(), acessoControle.getCon(), "");
            }
            return contrato;
        } catch (Exception e) {
            Uteis.logar(e, NegociacaoService.class);
            return new JSONObject().put("erro", tratarErroGravarNegociacao(e.getMessage()));
        } finally {
            Calendario.setDateThread(null);
        }
    }

    private boolean verificarContratoConsistencia(Integer codigo, AcessoControle acessoControle) throws Exception{
        return acessoControle.getContratoDao().verificarContratoEmBanco(codigo);
    }

    private String tratarErroGravarNegociacao(String msg){
        if(msg != null && msg.contains("null value in column \"consultor\"")){
            return "O aluno não tem consultor. Volte a tela do aluno e adicione um vínculo de consultor pra ele.";
        }
        return msg;
    }

    // GC-461: Ao habilitar a config "habilitarCobrancaAutomaticaNaVenda", serÃ¡ realizado a cobranÃ§a automÃ¡tica do sistema no ato da negociaÃ§Ã£o do contrato
    private void realizarCobrancaAutomatica(ContratoVO contratoGravado, Integer codigoConvenio, String chave) throws Exception {
        Connection con = Conexao.getFromSession();

        try {
            boolean possuiVencimentoNaDataLancamento = Uteis.getData(contratoGravado.getDataLancamento(), "DD/MM/YYYY").equals(Uteis.getData(contratoGravado.getDataPrimeiraParcela(), "DD/MM/YYYY"));
            if(possuiVencimentoNaDataLancamento) {
                VendasOnlineService vendasOnlineService = new VendasOnlineService(chave, con);
                VendaDTO venda = new VendaDTO();

                venda.setOrigemCobranca(OrigemCobrancaEnum.ZW_AUTOMATICO.getCodigo());
                venda.setCodigoEvento(contratoGravado.getEventoVO().getCodigo());
                venda.setCodigoColaborador(contratoGravado.getConsultor().getCodigo());
                venda.setUnidade(contratoGravado.getEmpresa().getCodigo());
                venda.setCpf(contratoGravado.getPessoa().getCfp());
                venda.setCpfMae(contratoGravado.getPessoa().getCpfMae());
                venda.setCpfPai(contratoGravado.getPessoa().getCpfPai());
                venda.setNome(contratoGravado.getPessoa().getNome());
                venda.setDataNascimento(contratoGravado.getPessoa().getDataNasc_Apresentar());
                if(!UteisValidacao.emptyList(contratoGravado.getPessoa().getEmailVOs())) {
                    venda.setEmail(contratoGravado.getPessoa().getEmailVOs().get(0).getEmail());
                }
                venda.setTelefone(contratoGravado.getPessoa().getTelefonesCelular());
                venda.setSexo(contratoGravado.getPessoa().getSexo());
                venda.setPlano(contratoGravado.getPlano().getCodigo());

                List<MovParcelaVO> listaParcelas = getFacade().getMovParcela().consultarPorContrato(contratoGravado.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<MovParcelaVO> parcelasVencendoMsmData = listaParcelas.stream()
                        .filter(parcela -> Uteis.getData(parcela.getDataVencimento(), "DD/MM/YYYY")
                                .equals(Uteis.getData(contratoGravado.getDataLancamento(), "DD/MM/YYYY"))
                                && "EA".equals(parcela.getSituacao()))
                        .collect(Collectors.toList());

                List<RetornoVendaTO> retornoVendaTOList = new ArrayList<>();

                if(!UteisValidacao.emptyList(parcelasVencendoMsmData)) {
                    vendasOnlineService.cobrarParcelas(
                            venda,
                            parcelasVencendoMsmData,
                            parcelasVencendoMsmData.size(),
                            contratoGravado.getCliente(),
                            contratoGravado.getUsuarioVO(),
                            codigoConvenio,
                            contratoGravado.getEmpresa().getCodigo(),
                            false,
                            null,
                            OrigemCobrancaEnum.ZW_AUTOMATICO,
                            0.0,
                            retornoVendaTOList
                    );
                }
            }

        } catch (Exception e) {
            throw new ConsistirException("Erro ao realizar cobrança automática: " + e.getMessage());
        }
    }

    private Double valorAcrescimos(ContratoControle contratoControle) {
        try {
            double pacrescimo = 0.00;
            double vacrescimo = 0.00;

            if (!contratoControle.getContratoVO().getPlanoDuracao().getTipoOperacao().equals("")) {
                if (contratoControle.getContratoVO().getPlanoDuracao().getTipoOperacao().equals("AC")) {
                    if (contratoControle.getContratoVO().getPlanoDuracao().getTipoValor().equals("PD")) {
                        pacrescimo = contratoControle.getContratoVO().getPlanoDuracao().getPercentualDesconto();
                    } else {
                        vacrescimo = contratoControle.getContratoVO().getPlanoDuracao().getValorEspecifico();
                    }
                }
            }

            if (!contratoControle.getContratoVO().getPlanoHorario().getTipoOperacao().equals("")) {
                if (contratoControle.getContratoVO().getPlanoHorario().getTipoOperacao().equals("AC")) {
                    if (contratoControle.getContratoVO().getPlanoHorario().getTipoValor().equals("PD")) {
                        pacrescimo = (pacrescimo + contratoControle.getContratoVO().getPlanoHorario().getPercentualDesconto());
                    } else {
                        vacrescimo = (vacrescimo + contratoControle.getContratoVO().getPlanoHorario().getValorEspecifico());
                        if (contratoControle.getContratoVO().getPlanoDuracao() != null &&
                                !UteisValidacao.emptyNumber(contratoControle.getContratoVO().getPlanoDuracao().getNumeroMeses())) {
                            vacrescimo = vacrescimo / contratoControle.getContratoVO().getPlanoDuracao().getNumeroMeses().doubleValue();
                        }
                    }
                }
            }

            if (!contratoControle.getContratoVO().getPlanoCondicaoPagamento().getTipoOperacao().equals("")) {
                if (contratoControle.getContratoVO().getPlanoCondicaoPagamento().getTipoOperacao().equals("AC")) {
                    if (contratoControle.getContratoVO().getPlanoCondicaoPagamento().getTipoValor().equals("PD")) {
                        pacrescimo = (pacrescimo + contratoControle.getContratoVO().getPlanoCondicaoPagamento().getPercentualDesconto());
                    } else {
                        vacrescimo = (vacrescimo + contratoControle.getContratoVO().getPlanoCondicaoPagamento().getValorEspecifico());
                    }
                }
            }
            return ((contratoControle.getTotalContrato() / 100) * pacrescimo) + vacrescimo;
        } catch (Exception e) {
            e.printStackTrace();
            return 0.0;
        }
    }

    public void adicionaNaModalidade(TurmaVO turma, HorarioTurmaVO horario, ContratoModalidadeVO contratoModalidade) {
        boolean incluirTurma = true;
        boolean incluirHorario = true;
        Iterator i = contratoModalidade.getContratoModalidadeTurmaVOs().iterator();
        // percorre a lista de turmas da modalidade
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO cm = (ContratoModalidadeTurmaVO) i.next();
            // verifica se turma ja existe
            if (cm.getTurma().getCodigo().intValue() == turma.getCodigo()) {
                Iterator j = cm.getContratoModalidadeHorarioTurmaVOs().iterator();
                // percorre a lista de horarios da turma
                while (j.hasNext()) {
                    ContratoModalidadeHorarioTurmaVO ht = (ContratoModalidadeHorarioTurmaVO) j.next();
                    // verifica se horario ja existe
                    if (ht.getHorarioTurma().getCodigo().intValue() == horario.getCodigo()) {
                        // se ja existir nao precisa incluir
                        incluirHorario = false;
                        break;
                    }
                }
                if (incluirHorario) {
                    // cria um novo contrato modalidade horario turma e prepara os dados
                    ContratoModalidadeHorarioTurmaVO cmht = new ContratoModalidadeHorarioTurmaVO();
                    cmht.setHorarioTurma(horario);
                    cmht.setContratoModalidadeTurma(cm.getCodigo());
                    cm.getContratoModalidadeHorarioTurmaVOs().add(cmht);
                }
                incluirTurma = false;
                break;
            }
        }
        if (incluirTurma) {
            turma.setTurmaEscolhida(true);
            // cria um novo contrato modalidade horario turma e prepara os dados
            ContratoModalidadeHorarioTurmaVO cmht = new ContratoModalidadeHorarioTurmaVO();
            cmht.setHorarioTurma(horario);
            // cria um novo contrato modalidade turma e prepara os dados
            ContratoModalidadeTurmaVO cmt = new ContratoModalidadeTurmaVO();
            cmt.setTurma(turma);
            cmt.setContratoModalidade(contratoModalidade.getCodigo());
            cmt.getContratoModalidadeHorarioTurmaVOs().add(cmht);
            // adiciona ? modalidade
            contratoModalidade.getContratoModalidadeTurmaVOs().add(cmt);
        }
    }

    public void acaoSelecionarPlanoTelaNova(ContratoControle contratoControle,
                                            UsuarioVO usuario,
                                            PlanoVO planoSelecionado,
                                            AcessoControle acessoControle) {
        try {
            contratoControle.setPlanoDuracaoSelecionada(null);
            planoSelecionado.filtrarPlanoProdutoSugeridoAtivoPlano();
            planoSelecionado.setPlanoExcecaoVOs(acessoControle.getPlanoExcecaoDao().consultarPorPlano(planoSelecionado.getCodigo()));
            planoSelecionado.filtrarPlanoHorarioHorarioAtivo();
            contratoControle.inicializarDadosReferenteAoPlano();
            contratoControle.validarPlanoExisteComposicaoAdicionar();
            contratoControle.selecionarPlano(usuario);
            Ordenacao.ordenarLista(contratoControle.getContratoVO().getPlano().getPlanoDuracaoVOs(), "numeroMeses");
            contratoControle.selecionarTodosProdutosSugeridos();
            contratoControle.selecionarPlanoModalidadeUnica();
            contratoControle.inicializarValoresDuracaoPlanoCreditoTreino(false);
            contratoControle.selecionarPlanoDuracaoUnicaCreditoSessao();
            contratoControle.verificarRenovacaoAntecipadaPlanoCreditoParaNaoCredito();
            contratoControle.consultarValorMatriculaEProdutosPlanoSelecionado();
            if (planoSelecionado.getCodigo() > 0) {
                contratoControle.montarListaParcelasMatricula();
                contratoControle.montarListaParcelasProduto();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    private void checkPacote(ContratoModalidadeVO cm, ContratoControle contratoControle, JSONObject obj) {
        if (!UteisValidacao.emptyList(contratoControle.getContratoVO().getContratoComposicaoVOs())) {
            for (Object objCC : contratoControle.getContratoVO().getContratoComposicaoVOs()) {
                ContratoComposicaoVO cc = (ContratoComposicaoVO) objCC;
                if (cc.getComposicaoVO().getComposicaoEscolhida()) {
                    for (ComposicaoModalidadeVO cpm : cc.getComposicaoVO().getComposicaoModalidadeVOs()) {
                        if (cpm.getModalidade().getCodigo().equals(cm.getModalidade().getCodigo())) {
                            obj.put("pacote", cc.getComposicaoVO().getCodigo());
                        }
                    }
                }
            }
        }
    }

    private void selecionarHorarioPlanoCreditoTreino(ContratoControle contratoControle, Integer codigoHorarioCreditoTreinoSelecionado) throws Exception {
        for (PlanoHorarioVO planoHorarioVO : contratoControle.getContratoVO().getPlano().getPlanoHorarioVOs()) {
            boolean encontrou = false;
            if (codigoHorarioCreditoTreinoSelecionado.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getCodigo())) {
                if (!planoHorarioVO.getHorario().isLivre()) {
                    encontrou = true;
                }
            } else {
                if (planoHorarioVO.getHorario().isLivre()) {
                    encontrou = true;
                }
            }
            if (encontrou) {
                contratoControle.getContratoVO().getPlano().getPlanoHorarioVOs().add(planoHorarioVO);
                planoHorarioVO.getHorario().setHorarioEscolhida(true);
                contratoControle.selecionarHorario(planoHorarioVO);
                break;
            }
        }
    }

}
