package servicos.pix;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.*;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.impl.gatewaypagamento.RecebedorDTO;
import servicos.impl.pix.PixWebhookService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.util.*;

/**
 * Created by Rafael Moris on 20/05/2025.
 */

public class PixAfinz extends SuperServico implements PixServiceInterfaceFacade {
    public PixAfinz(Connection con) throws Exception {
        super(con);
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO, String chave, List<MovParcelaVO> listaParcelas) throws Exception {
        PixDto pixDto = new PixDto();

        String celular = pessoaVO.getTelefonesCelular();
        if ((celular == null || celular.isEmpty())) {
            celular = "11912341234";
        }

        String idConsumidor = criarConsumidor(pixDto, celular, pessoaVO);
        List<RecebedorDTO> splits = obterSplits(convenioCobrancaVO, listaParcelas);
        JSONObject pagamento = criarPagamento(pixDto, idConsumidor, splits, convenioCobrancaVO, empresaVO, valor, chave);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto(expiracao);

        pixDto.setCalendario(pixCalendarioDto);

        pixDevedorDto.setCpf(Uteis.removerCaracteresNaoAscii(Uteis.formatarCpfCnpj(pessoaVO.getCfp(), true)));
        pixDevedorDto.setNome(Uteis.retirarAcentuacao(pessoaVO.getNome()).replace("-", " ").replace("_", " "));
        pixDto.setDevedor(pixDevedorDto);

        pixDto.setSolicitacaoPagador(descricao);

        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(Uteis.formatarValorEmRealSemAlterarPontuacao(valor));
        pixDto.setValor(pixValorDto);

        pixRequisicaoDto.setPixDto(pixDto);
        pixRequisicaoDto.setEnvio(body(pixDto));

        String pixCopiaECola = pagamento.optJSONObject("order").optString("pix");
        pixRequisicaoDto.getPixDto().setTextoImagemQRcode(pixCopiaECola);
        pixRequisicaoDto.getPixDto().getDevedor().setTelefone(UteisTelefone.removerCaracteresEspeciais(celular));

        pixDto.setTxid(pagamento.optString("orderId"));

        pixRequisicaoDto.setResposta(body(pagamento));

        pixDto.setStatus(PixStatusEnum.ATIVA.getDescricao());

        return pixRequisicaoDto;
    }

    List<RecebedorDTO> obterSplits(ConvenioCobrancaVO convenioCobrancaVO, List<MovParcelaVO> listaParcelas)  throws Exception {
        try {
            TipoTransacaoEnum tipoTransacaoEnum = convenioCobrancaVO.getTipo().getTipoTransacao();
            TransacaoVO transacaoVO = tipoTransacaoEnum.getTransacaoVO();
            transacaoVO.setListaParcelas(listaParcelas);

            CobrancaOnlineService cobrancaOnlineService = new CobrancaOnlineService(getCon());
            return cobrancaOnlineService.obterRecebedoresSplitPagamentoDoConvenio(transacaoVO, convenioCobrancaVO);
        } catch (Exception ex) {
            throw ex;
        }
    }

    String criarConsumidor(PixDto pixDto, String celular, PessoaVO pessoaVO) throws Exception{
        JSONObject consumidor = new JSONObject();
        consumidor.put("cpf", Uteis.removerCaracteresNaoAscii(Uteis.formatarCpfCnpj(pessoaVO.getCfp(), true)));
        consumidor.put("email", pessoaVO.getEmail());
        consumidor.put("name", Uteis.retirarAcentuacao(pessoaVO.getNome()));
        consumidor.put("mobileNumber", UteisTelefone.removerCaracteresEspeciais(celular));

        String url = PropsService.getPropertyValue(PropsService.urlApiPagoLivreProducao) + "/customer";
        String token = PropsService.getPropertyValue(PropsService.tokenPagoLivreProducao);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + token);

        RequestHttpService service = new RequestHttpService();

        RespostaHttpDTO resposta = service.executeRequest(url, headers, null, consumidor.toString(), MetodoHttpEnum.POST);
        JSONObject json = new JSONObject(resposta.getResponse());

        if(!json.has("customerId")) {
            throw new Exception("Não foi possível cadastrar o aluno por erro/bloqueio na Afinz.");
        }

        pixDto.setCpf(consumidor.getString("cpf"));
        pixDto.setEmail(consumidor.getString("email"));
        pixDto.setName(consumidor.getString("name"));
        pixDto.setMobileNumber(consumidor.getString("mobileNumber"));

        return json.optString("customerId");
    }

    JSONObject criarPagamento(PixDto pixDto, String customerId, List<RecebedorDTO> splits, ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO, Double valor, String chave) throws Exception {
        PixWebhookService pixWebhookService = new PixWebhookService(this.getCon());
        String urlCallback = pixWebhookService.montarUrlCallbackWebhook(convenioCobrancaVO, chave);
        if (UteisValidacao.emptyString(urlCallback)) {
            throw new Exception("Não foi possível criar URLCallback do pix Afinz");
        }

        JSONObject pagamento = new JSONObject();
        pagamento.put("merchantId", convenioCobrancaVO.getCodigoAutenticacao01());
        pagamento.put("customerId", customerId);
        pagamento.put("amount", valor);
        pagamento.put("installments", 1);
        pagamento.put("orderKey", "PIX" + empresaVO.getCodigo() + "-" + System.currentTimeMillis());
        pagamento.put("receivingOptionType", "pix");
        pagamento.put("callBack", urlCallback);

        if (!UteisValidacao.emptyList(splits)) {
            JSONArray splitPayment = new JSONArray();
            for (RecebedorDTO dto : splits) {
                JSONObject rules = new JSONObject();
                rules.put("merchantId", dto.getId());
                rules.put("amount", dto.getPercentual());
                rules.put("amountType", "PERCENTAGE");
                pixDto.addSplit(rules);
                splitPayment.put(rules);
            }

            if (splitPayment.length() > 0) {
                pagamento.put("splitPayment", splitPayment);
            }
        }

        String url = PropsService.getPropertyValue(PropsService.urlApiPagoLivreProducao) + "/payment";
        String token = PropsService.getPropertyValue(PropsService.tokenPagoLivreProducao);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + token);

        RequestHttpService service = new RequestHttpService();

        RespostaHttpDTO resposta = service.executeRequest(url, headers, null, pagamento.toString(), MetodoHttpEnum.POST);
        JSONObject json = new JSONObject(resposta.getResponse());

        if(!Objects.equals(json.optString("status"), "authorized")) {
            throw new Exception("Não foi possível gerar a cobrança na Afinz.");
        }

        if(!json.has("order") || !json.optJSONObject("order").has("pix")) {
            throw new Exception("Não foi possível gerar o Pix na Afinz.");
        }

        pixDto.setMerchantId(pagamento.getString("merchantId"));
        pixDto.setCustomerId(pagamento.getString("customerId"));
        pixDto.setInstallments(pagamento.getInt("installments"));
        pixDto.setOrderKey(pagamento.getString("orderKey"));
        pixDto.setReceivingOptionType(pagamento.getString("receivingOptionType"));
        pixDto.setCallBack(pagamento.getString("callBack"));

        return json;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws Exception {
        // Não é possível cancelar Pix na Afinz, caso seja necessário estornar tem que
        // ser feito fisicamente pelo lojista
        return null;
    }

    @Override
        public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlApiPagoLivreConciliacaoProducao) + "/orders/" + pixVO.getTxid();
        String token = PropsService.getPropertyValue(PropsService.tokenPagoLivreProducao);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + token);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(url, headers, null, null, MetodoHttpEnum.GET);

        JSONObject json = new JSONObject(resposta.getResponse());

        if(resposta.getHttpStatus() != 200 || !json.has("id")) {
            throw new Exception("Cobrança Pix não encontrada na Afinz.");
        }

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setResposta(resposta.getResponse());
        pixRequisicaoDto.setPixDto(preencherObjetoRetorno(json));

        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {
        return consultarCobranca(pixVO);
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String txId, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        Pix pixDAO = null;
        try {
            pixDAO = new Pix(getCon());
            PixVO pixVO = pixDAO.consultarPorTxId(txId);
            return consultarCobranca(pixVO);
        } catch (Exception ex) {
            throw new Exception("Erro ao tentar consultar Pix Afinz.");
        } finally {
            pixDAO = null;
        }
    }

    public PixDto preencherObjetoRetorno(JSONObject json) throws Exception {
        PixDto pixDto = new PixDto();

        pixDto.setTxid(json.optString("id"));
        pixDto.setStatus(PixStatusEnum.ATIVA.getDescricao());

        if(json.has("installments")) {
            JSONArray pagamentos = json.optJSONArray("installments");
            if(pagamentos.length() > 0) {
                JSONObject pagamento = pagamentos.getJSONObject(0);

                if(pagamento.optString("status").equals("paid")) {
                    pixDto.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());

                    Calendar calendar = Calendar.getInstance();

                    String paymentDate = pagamento.optString("paymentDate");
                    Date dataPagamento = Uteis.getDate(paymentDate.split("T")[0] + " " + paymentDate.split("T")[1].split("\\.")[0], "yyyy-MM-dd hh:mm:ss");
                    calendar.setTime(dataPagamento);
                    calendar.add(Calendar.HOUR_OF_DAY, -3);
                    dataPagamento = calendar.getTime();
                    pixDto.setDataPagamento(dataPagamento);

                    String expectedWithdrawDate = pagamento.optString("expectedWithdrawDate");

                    if (!UteisValidacao.emptyString(expectedWithdrawDate)) {
                        Date dataCredito = Uteis.getDate(expectedWithdrawDate.split("T")[0] + " " + expectedWithdrawDate.split("T")[1].split("\\.")[0], "yyyy-MM-dd hh:mm:ss");
                        calendar.setTime(dataCredito);
                        calendar.add(Calendar.HOUR_OF_DAY, -3);
                        dataCredito = calendar.getTime();
                        pixDto.setDataCredito(dataCredito);
                    }
                }
            }
        }

        pixDto.setDeleted(json.optString("status").equals("canceled")); //cancelamento
        if (pixDto.isDeleted()) {
            pixDto.setStatus(PixStatusEnum.CANCELADA.toString());
        }

        return pixDto;
    }

    @Override
    public HttpClient createConnector() {
        return null;
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return null;
    }

    private HttpClient createConnector(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return ExecuteRequestHttpService.createConnector();
    }

    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {

    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorDto.class);
    }

    @Override
    public String translateMessages(String message) {
        return message;
    }

    @Override
    public void validateResponseError(String responseJsonString, int status) throws PixRequestException {

    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws Exception {
        return null;
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    private String apiUrl() {
        return PropsService.getPropertyValue(PropsService.urlApiPagoLivreProducao);
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        return false;
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return false;
    }

    @Override
    public String token(PixVO pixVO) {
        return null;
    }

    @Override
    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        return null;
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String gerarTextoQrCode(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO) {
        return "";
    }

    @Override
    public String fixResponseErros(String responseJson) {
        return null;
    }
}
