package servicos.pix;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.facade.jdbc.financeiro.PixMovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.vendas.VendasOnlineVenda;
import org.json.JSONObject;

import java.sql.Connection;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class PixPagamentoService {

    private Connection connection;
    private ReciboPagamento reciboPagamento;
    private Log log;
    private Usuario usuario;
    private Pix pix;
    private MovParcela movParcela;
    private MovProdutoParcela movProdutoParcela;
    private MovPagamento movPagamento;
    private PixMovParcela pixMovParcela;
    private MovProduto movProduto;
    private ConvenioCobranca convenioCobranca;
    private Empresa empresa;
    private PixService pixService;
    private FormaPagamento formaPagamento;

    private ZillyonWebFacade zwFacade;

    public PixPagamentoService(Connection connection) {
        this.connection = connection;
    }

    public PixVO processarPixControlandoTransacao(Integer codigoPix) throws Exception {
        return processarPixControlandoTransacao(getPix().consultarPorCodigo(codigoPix, true));
    }

    public PixVO processarPixControlandoTransacao(PixVO pixVO) throws Exception {
        try {
            pixVO.setStatusAnterior(pixVO.getStatus());

            if (pixVO.getAmbienteEnum().equals(AmbienteEnum.PRODUCAO)) {
                PixRequisicaoDto pixRequisicaoDto = getPixService().consultarCobranca(pixVO);
                if (pixVO.getConveniocobranca().isPixSantander()){
                    obterSituacaoSantander(pixRequisicaoDto);
                }
                pixVO.setStatus(pixRequisicaoDto.getPixDto().getStatus());

                if (pixRequisicaoDto.getPixDto().getDataPagamento() != null) {
                    pixVO.setDataPagamento(pixRequisicaoDto.getPixDto().getDataPagamento());
                }

                PixStatusEnum pixStatusEnum = null;
                try {
                    pixStatusEnum = pixVO.getStatusEnum();
                } catch (Exception ignored) {
                }

                //para pix pjbank e asaas
                if (pixRequisicaoDto.getPixDto().getDataCredito() != null) {
                    pixVO.setDataCredito(pixRequisicaoDto.getPixDto().getDataCredito());
                }

                //PjBank
                if (pixVO.getConveniocobranca().isPixPjBank()) {
                    if (pixVO.expiradoPJBank() && pixStatusEnum != null && !pixStatusEnum.equals(PixStatusEnum.CONCLUIDA) && !pixStatusEnum.equals(PixStatusEnum.CANCELADA)) {
                        pixVO.setStatus(PixStatusEnum.EXPIRADA.toString());
                        try {
                            getPixService().cancelar(pixVO);
                        } catch (Exception ignore) {
                            Uteis.logarDebug("Encontrei uma cobrança expirada mas não consegui cancelar na PjBank");
                        }
                    } else if (pixVO.expiradoPJBank() && pixStatusEnum != null && !pixStatusEnum.equals(PixStatusEnum.CONCLUIDA) && pixStatusEnum.equals(PixStatusEnum.CANCELADA)) {
                        pixVO.setStatus(PixStatusEnum.CANCELADA.toString());
                    }
                }
                //Asaas
                else if (pixVO.getConveniocobranca().isPixAsaas()) {
                    if (pixVO.expiradoPJBank() && pixStatusEnum != null && !pixStatusEnum.equals(PixStatusEnum.CONCLUIDA) && !pixStatusEnum.equals(PixStatusEnum.CANCELADA)) {
                        pixVO.setStatus(PixStatusEnum.EXPIRADA.toString());
                        try {
                            getPixService().cancelar(pixVO);
                        } catch (Exception ignore) {
                            Uteis.logarDebug("Encontrei uma cobrança expirada mas não consegui cancelar no Asaas");
                        }
                    } else if (pixVO.expiradoPJBank() && pixStatusEnum != null && !pixStatusEnum.equals(PixStatusEnum.CONCLUIDA) && pixStatusEnum.equals(PixStatusEnum.CANCELADA)) {
                        pixVO.setStatus(PixStatusEnum.CANCELADA.toString());
                    }
                    pixVO.setTransactionReceiptUrl(pixRequisicaoDto.getPixDto().getTransactionReceiptUrl());
                }
                //Outros
                else if (pixVO.expirado() && pixStatusEnum != null && !pixStatusEnum.equals(PixStatusEnum.CONCLUIDA)) {
                    pixVO.setStatus(PixStatusEnum.EXPIRADA.toString());
                }
            }

            if (pixVO.getAmbienteEnum().equals(AmbienteEnum.HOMOLOGACAO) && !pixVO.expirado()) {
                pixVO.setStatus(PixStatusEnum.CONCLUIDA.toString());
                pixVO.setDataPagamento(Calendario.hoje());
            }

            //controlar a transação para depois da consulta evitar lock em caso de demora na resposta da consulta no banco
            //antes desse ponto não deve ser alterado nada em banco.
            getConnection().setAutoCommit(false);

            if (!pixVO.getStatusAnterior().equals(pixVO.getStatus())) {
                getPix().alterarStatus(pixVO);
                getPix().gerarLogAlterarStatus(pixVO);
                if (!UteisValidacao.emptyString(pixVO.getTransactionReceiptUrl())) {
                    getPix().alterarTransactionReceiptUrl(pixVO);
                }
                boolean gerouRecibo = false;
                try {
                    if (pixVO.getStatus().equals(PixStatusEnum.CONCLUIDA.toString())) {
                        ConvenioCobrancaVO convenioCobrancaVO = getConvenioCobranca().consultarDadosPix(pixVO.getConveniocobranca().getCodigo());
                        EmpresaVO empresaVO = getEmpresa().consultarPorCodigo(pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        FormaPagamentoVO formaPagamentoVO = getPix().obterFormaPagamentoPix(pixVO, convenioCobrancaVO);
                        if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                            throw new Exception("Não foi encontrado uma forma de pagamento PIX!");
                        }
                        convenioCobrancaVO.setEmpresa(empresaVO);
                        processarPagamento(pixVO, convenioCobrancaVO, formaPagamentoVO);
                        if(!UteisValidacao.emptyNumber(pixVO.getReciboPagamento())) {
                            gerouRecibo = true;
                        }
                    } else {
                        //verificar se pode excluir multa e juros caso o pix tenha ficado inválido
                        getPix().verificarExcluirParcelaMultaJurosPix(pixVO);
                    }
                }catch (Exception e) {
                    if(gerouRecibo) {
                        try {
                            if (!UteisValidacao.emptyNumber(pixVO.getReciboPagamento()) && getZwFacade().getConfiguracaoSistema().realizarEnvioSesiSC()) {
                                List<Integer> listaRecibo = new ArrayList<>();
                                listaRecibo.add(pixVO.getReciboPagamento());
                                getZwFacade().startThreadIntegracaoFiesc(pixVO.getEmpresa(), listaRecibo, "incluirPix");
                            }
                        } catch (Exception ignored){
                        }
                    }
                    throw e;
                }

            }

            getConnection().commit();
            try {
                if (!UteisValidacao.emptyNumber(pixVO.getReciboPagamento()) && getZwFacade().getConfiguracaoSistema().realizarEnvioSesiSC()) {
                    List<Integer> listaRecibo = new ArrayList<>();
                    listaRecibo.add(pixVO.getReciboPagamento());
                    getZwFacade().startThreadIntegracaoFiesc(pixVO.getEmpresa(), listaRecibo, "incluirPix");
                }
            } catch (Exception ignored){
            }
            return pixVO;
        } catch (Exception e) {
            if (!getConnection().getAutoCommit()) {
                getConnection().rollback();
            }
            Uteis.logarDebug("Erro ao processar pix de cód.: " + pixVO.getCodigo() + " | Chave: " +
                    pixVO.getRecebedorChave() + " | " + e.getMessage());
            e.printStackTrace();
            throw e;
        } finally {
            getConnection().setAutoCommit(true);
            acoesPixVendasOnline(pixVO, "processarPagamentoPixWebhookControlandoTransacao");
        }
    }

    public PixVO processarPagamentoPixWebhookControlandoTransacao(PixVO pixVO) throws Exception {
        try {
            getConnection().setAutoCommit(false);
            getPix().alterarStatus(pixVO);
            getPix().gerarLogAlterarStatus(pixVO);
            boolean gerouRecibo = false;
            try {

                ConvenioCobrancaVO convenioCobrancaVO = getConvenioCobranca().consultarDadosPix(pixVO.getConveniocobranca().getCodigo());
                EmpresaVO empresaVO = getEmpresa().consultarPorCodigo(pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                FormaPagamentoVO formaPagamentoVO = getPix().obterFormaPagamentoPix(pixVO, convenioCobrancaVO);
                if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                    throw new Exception("Não foi encontrado uma forma de pagamento PIX!");
                }
                convenioCobrancaVO.setEmpresa(empresaVO);
                processarPagamento(pixVO, convenioCobrancaVO, formaPagamentoVO);

                if(!UteisValidacao.emptyNumber(pixVO.getReciboPagamento())) {
                    gerouRecibo = true;
                }

            } catch (Exception e) {
                if(gerouRecibo) {
                    try {
                        if (!UteisValidacao.emptyNumber(pixVO.getReciboPagamento()) && getZwFacade().getConfiguracaoSistema().realizarEnvioSesiSC()) {
                            List<Integer> listaRecibo = new ArrayList<>();
                            listaRecibo.add(pixVO.getReciboPagamento());
                            getZwFacade().startThreadIntegracaoFiesc(pixVO.getEmpresa(), listaRecibo, "incluirPix");
                        }
                    } catch (Exception ignored) {
                    }
                }
                throw e;
            }

            getConnection().commit();
            try {
                if (!UteisValidacao.emptyNumber(pixVO.getReciboPagamento()) && getZwFacade().getConfiguracaoSistema().realizarEnvioSesiSC()) {
                    List<Integer> listaRecibo = new ArrayList<>();
                    listaRecibo.add(pixVO.getReciboPagamento());
                    getZwFacade().startThreadIntegracaoFiesc(pixVO.getEmpresa(), listaRecibo, "incluirPix");
                }
            } catch (Exception ignored) {
            }
            return pixVO;
        } catch (Exception e) {
            if (!getConnection().getAutoCommit()) {
                getConnection().rollback();
            }
            Uteis.logarDebug("Erro ao processar pix de cód.: " + pixVO.getCodigo() + " | Chave: " +
                    pixVO.getRecebedorChave() + " | " + e.getMessage());
            e.printStackTrace();
            throw e;
        } finally {
            getConnection().setAutoCommit(true);
            acoesPixVendasOnline(pixVO, "processarPagamentoPixWebhookControlandoTransacao");
        }
    }

    public void processarPagamento(PixVO pixVO, ConvenioCobrancaVO convenioCobrancaVO, FormaPagamentoVO formaPagamentoVO) throws Exception {
        MovParcela movParcelaDAO;
        PixMovParcela pixMovParcelaDAO;
        Cliente clienteDAO;
        Colaborador colaboradorDAO;
        MovimentoContaCorrenteCliente movContaCorrenteDAO;
        MovPagamento movPagamentoDAO;
        PagamentoMovParcela pagamentoMovParcelaDAO;

        try {
        movParcelaDAO = new MovParcela(this.getConnection());
        pixMovParcelaDAO = new PixMovParcela(this.getConnection());
        pagamentoMovParcelaDAO = new PagamentoMovParcela(this.getConnection());

        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        MovPagamentoVO movPagamentoVO = montarMovPagamento(pixVO, convenioCobrancaVO, formaPagamentoVO);
        listaPagamento.add(movPagamentoVO);

        List<MovParcelaVO> listaParcelas = new ArrayList<>();
        boolean existeParcelaPaga = false;
        Double valorEmAberto = 0.0;
        for (PixMovParcelaVO pixMovParcelaVO : pixVO.getPixMovParcelas()) {
            if (!pixMovParcelaVO.getMovParcelaVO().getSituacao().equalsIgnoreCase("EA")) {
                if (pagamentoMovParcelaDAO.existeReciboPagoPorPix(pixMovParcelaVO.getMovParcelaVO().getCodigo())){
                    throw new Exception("Existe Recibo para esse Pix");
                }
                existeParcelaPaga = true;
            } else {
                listaParcelas.add(pixMovParcelaVO.getMovParcelaVO());
                valorEmAberto += pixMovParcelaVO.getMovParcelaVO().getValorParcela();
            }

            //criar parcela de multa e juros
            if (!UteisValidacao.emptyNumber(pixMovParcelaVO.getValorMulta()) ||
                    !UteisValidacao.emptyNumber(pixMovParcelaVO.getValorJuros())) {
                //esse valor já está incluso no valor do PIX porem ainda não foi criado a parcela
                //criar a parcela e adicionar na lista de parcelas
                MovParcelaVO movParcelaMultaJuros = movParcelaDAO.criarParcelaMultaJuros(pixMovParcelaVO.getMovParcelaVO(),
                        pixMovParcelaVO.getValorMulta(), pixMovParcelaVO.getValorJuros(), getUsuario().getUsuarioRecorrencia(), false);
                valorEmAberto += movParcelaMultaJuros.getValorParcela();
                pixMovParcelaDAO.incluir(pixVO, movParcelaMultaJuros);
                listaParcelas.add(movParcelaMultaJuros);
            }
        }

        //se a parcela não estiver em aberto então gerar depósito na conta corrente
        //gerar pagamento conta corrente
        if (existeParcelaPaga || UteisValidacao.emptyList(pixVO.getPixMovParcelas())) {

            clienteDAO = new Cliente(getConnection());
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pixVO.getPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteDAO = null;

            ColaboradorVO colaboradorVO = null;
            if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                clienteVO = null;
                colaboradorDAO = new Colaborador(getConnection());
                colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(pixVO.getPessoa(), pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                colaboradorDAO = null;
            }

            movContaCorrenteDAO = new MovimentoContaCorrenteCliente(getConnection());
            VendaAvulsaVO vendaAvulsaVO = movContaCorrenteDAO.gerarProdutoPagamentoCredito((pixVO.getValor() - valorEmAberto), clienteVO, colaboradorVO, null, pixVO.getUsuarioResponsavel(),
                    MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_PIX, pixVO.getDataPagamento(), pixVO.getDataPagamento(), new EmpresaVO(pixVO.getEmpresa()));
            movContaCorrenteDAO = null;
            MovParcelaVO movParcelaVO = getMovParcela().consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            movPagamentoVO.setObservacao("Foi gerado crédito pois a parcela " + movParcelaVO.getCodigo() + " - " + movParcelaVO.getDescricao() + " estava com a situação: " + movParcelaVO.getSituacao_Apresentar());
            listaParcelas.add(movParcelaVO);

            //Se pagou o pix a mais do que o valor da parcela gerar a diferença para a Conta corrente do cliente
        } else if (pixVO.getValor() != null && pixVO.getValor() > Uteis.arredondarForcando2CasasDecimais(valorEmAberto)) {
            double valorDepositarCC = pixVO.getValor() - valorEmAberto;

            clienteDAO = new Cliente(getConnection());
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(pixVO.getPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteDAO = null;

            ColaboradorVO colaboradorVO = null;
            if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                clienteVO = null;
                colaboradorDAO = new Colaborador(getConnection());
                colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(pixVO.getPessoa(), pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                colaboradorDAO = null;
            }

            movContaCorrenteDAO = new MovimentoContaCorrenteCliente(getConnection());
            VendaAvulsaVO vendaAvulsaVO = movContaCorrenteDAO.gerarProdutoPagamentoCredito(valorDepositarCC, clienteVO, colaboradorVO, null, pixVO.getUsuarioResponsavel(),
                    MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_PIX_EXCEDENTE, pixVO.getDataPagamento(), pixVO.getDataPagamento(), new EmpresaVO(pixVO.getEmpresa()));
            movContaCorrenteDAO = null;
            MovParcelaVO movParcelaVO = getMovParcela().consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            movPagamentoVO.setObservacao("Foi gerado crédito pois o valor do pix pago é maior do que o valor das parcelas vinculadas à ele");
            listaParcelas.add(movParcelaVO);
        }

        Integer codigoContrato = obterCodigoContrato(listaParcelas);
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(codigoContrato);

        movPagamentoDAO = new MovPagamento(getConnection());
        ReciboPagamentoVO reciboPagamentoVO = movPagamentoDAO.incluirListaPagamento(
                listaPagamento, listaParcelas, null,
                contratoVO, false, 0.0, false, null);

        pixVO.setReciboPagamento(reciboPagamentoVO.getCodigo());
        getPix().alterarReciboPagamento(pixVO);
        getPix().incluirDataPagamento(pixVO);
        if (reciboPagamentoVO.getPagamentosDesteRecibo() != null &&
                reciboPagamentoVO.getPagamentosDesteRecibo().size() > 0) {
            pixVO.setMovPagamentoVO(reciboPagamentoVO.getPagamentosDesteRecibo().get(0));
        }
    }  catch (Exception ex) {
            throw ex;
        } finally {
            movParcelaDAO = null;
            pixMovParcelaDAO = null;
            clienteDAO = null;
            colaboradorDAO = null;
            movContaCorrenteDAO = null;
            movPagamentoDAO = null;
        }
    }

    public MovPagamentoVO montarMovPagamento(PixVO pixVO, ConvenioCobrancaVO convenioCobrancaVO, FormaPagamentoVO formaPagamentoVO) throws Exception {
        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setCpfPagador(pixVO.getDevedorCpf());
        movPagamentoVO.setConvenio(convenioCobrancaVO);
        movPagamentoVO.setDataCobrancaTransacao(pixVO.getDataPagamento());
        movPagamentoVO.setDataLancamento(pixVO.getDataPagamento());
        movPagamentoVO.setDataMovimento(pixVO.getDataPagamento());
        movPagamentoVO.setDataQuitacao(pixVO.getDataPagamento());
        if (pixVO.getDataCredito() != null) {
            movPagamentoVO.setDataPagamento(pixVO.getDataCredito());
            movPagamentoVO.setDataPagamentoOriginal(pixVO.getDataCredito());
        }
        movPagamentoVO.setEmpresa(new EmpresaVO(pixVO.getEmpresa()));
        if(convenioCobrancaVO.getEmpresa() != null
                && !UteisValidacao.emptyString(convenioCobrancaVO.getEmpresa().getSenhaSpc())
                && !UteisValidacao.emptyString(convenioCobrancaVO.getEmpresa().getOperadorSpc())){
            movPagamentoVO.getEmpresa().setSenhaSpc(convenioCobrancaVO.getEmpresa().getSenhaSpc());
            movPagamentoVO.getEmpresa().setOperadorSpc(convenioCobrancaVO.getEmpresa().getOperadorSpc());
        }
        movPagamentoVO.setFormaPagamento(formaPagamentoVO);
        movPagamentoVO.setPessoa(new PessoaVO(pixVO.getPessoa()));
        movPagamentoVO.setValor(pixVO.getValor());
        movPagamentoVO.setValorTotal(pixVO.getValor());
        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setNomePagador(pixVO.getDevedorNome());
        if (UteisValidacao.emptyNumber(pixVO.getUsuarioResponsavel().getCodigo())) {
            movPagamentoVO.setResponsavelPagamento(getUsuario().getUsuarioRecorrencia());
        } else {
            movPagamentoVO.setResponsavelPagamento(pixVO.getUsuarioResponsavel());
        }
        return movPagamentoVO;
    }

    public Integer obterCodigoContrato(List<MovParcelaVO> movParcelaVOS){
        if (UteisValidacao.emptyList(movParcelaVOS)) {
            return 0;
        }
        Integer contrato = movParcelaVOS.get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : movParcelaVOS) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                contrato = 0;
                break;
            }
        }
        return contrato;
    }

    public void obterSituacaoSantander(PixRequisicaoDto pixRequisicaoDto) {
        //Santander retorna status CONCLUIDA para pix pagos e também expirados.
        //Se array de pix no retorno estiver vazia a cobrança está expirada, se não estiver vazia está pago.
        try {
            JSONObject retornoJSON = null;
            retornoJSON = new JSONObject(pixRequisicaoDto.getResposta());

            try {
                //verifica se a array está preenchida, se não tiver vai cair no catch
                retornoJSON.getJSONArray("pix").getJSONObject(0);

                //se a array não está vazia e status = CONCLUIDA, o pix está pago
                if (pixRequisicaoDto.getPixDto().getStatus().equals(PixStatusEnum.CONCLUIDA.toString())) {
                    pixRequisicaoDto.getPixDto().setStatus(PixStatusEnum.CONCLUIDA.toString());
                }
            } catch (Exception e) {
                //se caiu no catch é porque a lista está vazia e o pix com a situação CONCLUIDA está EXPIRADO na verdade
                if (pixRequisicaoDto.getPixDto().getStatus().equals(PixStatusEnum.CONCLUIDA.toString())) {
                    pixRequisicaoDto.getPixDto().setStatus(PixStatusEnum.EXPIRADA.toString());
                }
            }

        } catch (Exception ignore) {
        }
    }

    private Long discoveryOffset(ZonedDateTime zonedDateTime) {
        return TimeUnit.SECONDS.toHours(zonedDateTime.getOffset().getTotalSeconds());
    }

    public ReciboPagamento getReciboPagamento() throws Exception {
        if (reciboPagamento == null) {
            setReciboPagamento(new ReciboPagamento(getConnection()));
        }
        return reciboPagamento;
    }

    public void setReciboPagamento(ReciboPagamento reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public MovPagamento getMovPagamento() throws Exception {
        if (movPagamento == null) {
            setMovPagamento(new MovPagamento(getConnection()));
        }
        return movPagamento;
    }

    public void setMovPagamento(MovPagamento movPagamento) {
        this.movPagamento = movPagamento;
    }

    public MovProdutoParcela getMovProdutoParcela() throws Exception {
        if (movProdutoParcela == null) {
            setMovProdutoParcela(new MovProdutoParcela(getConnection()));
        }
        return movProdutoParcela;
    }

    public void setMovProdutoParcela(MovProdutoParcela movProdutoParcela) {
        this.movProdutoParcela = movProdutoParcela;
    }

    public MovParcela getMovParcela() throws Exception {
        if (movParcela == null) {
            setMovParcela(new MovParcela(getConnection()));
        }
        return movParcela;
    }

    public void setMovParcela(MovParcela movParcela) {
        this.movParcela = movParcela;
    }


    public Pix getPix() throws Exception {
        if (pix == null) {
            setPix(new Pix(getConnection()));
        }
        return pix;
    }

    public void setPix(Pix pix) {
        this.pix = pix;
    }

    public Usuario getUsuario() throws Exception {
        if (usuario == null) {
            setUsuario(new Usuario(getConnection()));
        }
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }


    public Log getLog() throws Exception {
        if (log == null) {
            setLog(new Log(getConnection()));
        }
        return log;
    }

    public void setLog(Log log) {
        this.log = log;
    }

    public Connection getConnection() {
        return connection;
    }

    public void setConnection(Connection connection) {
        this.connection = connection;
    }

    public PixMovParcela getPixMovParcela() throws Exception {
        if (pixMovParcela == null) {
            setPixMovParcela(new PixMovParcela(getConnection()));
        }
        return pixMovParcela;
    }

    public void setPixMovParcela(PixMovParcela pixMovParcela) {
        this.pixMovParcela = pixMovParcela;
    }

    public MovProduto getMovProduto() throws Exception {
        if (movPagamento == null) {
            setMovProduto(new MovProduto(getConnection()));
        }
        return movProduto;
    }

    public void setMovProduto(MovProduto movProduto) {
        this.movProduto = movProduto;
    }

    public ConvenioCobranca getConvenioCobranca() throws Exception {
        if (convenioCobranca == null) {
            setConvenioCobranca(new ConvenioCobranca(getConnection()));
        }
        return convenioCobranca;
    }

    public void setConvenioCobranca(ConvenioCobranca convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Empresa getEmpresa() throws Exception {
        if (empresa == null) {
            setEmpresa(new Empresa(getConnection()));
        }
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public PixService getPixService() throws Exception {
        if (pixService == null) {
            setPixService(new PixService(getConnection()));
        }
        return pixService;
    }

    public void setPixService(PixService pixService) {
        this.pixService = pixService;
    }

    public FormaPagamento getFormaPagamento() throws Exception {
        if (formaPagamento == null) {
            setFormaPagamento(new FormaPagamento(getConnection()));
        }
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamento formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    private void acoesPixVendasOnline(PixVO pixVO, String origemProcesso) {
        VendasOnlineVenda vendasOnlineVendaDAO;
        try {
            if (!UteisValidacao.emptyNumber(pixVO.getCodigo())) {
                Uteis.logarDebug("PixPagamentoService.acoesPixVendasOnline - Codigo Pix: " + pixVO.getCodigo());
            }
            Uteis.logarDebug("PixPagamentoService.acoesPixVendasOnline - Status Pix: " + pixVO.getStatus());
            Uteis.logarDebug("PixPagamentoService.acoesPixVendasOnline - Origem Processo Pix: " + origemProcesso);

            vendasOnlineVendaDAO = new VendasOnlineVenda(this.getConnection());
            if (pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                //marcar a aula do aluno depois que o pix for pago
                marcarAulaPixPagoVendasOnline(pixVO, vendasOnlineVendaDAO);
            }
            if (pixVO.getStatusEnum().equals(PixStatusEnum.ATIVA) ||
                    pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                //não precisa fazer mais nada, só retornar
                return;
            }
            if (pixVO.getStatusEnum().equals(PixStatusEnum.EXPIRADA)
                    || pixVO.getStatusEnum().equals(PixStatusEnum.CANCELADA)
                    || pixVO.getStatusEnum().equals(PixStatusEnum.REMOVIDA_PELO_USUARIO_RECEBEDOR)
                    || pixVO.getStatusEnum().equals(PixStatusEnum.REMOVIDA_PELO_PSP)) {
                boolean estornarContratoVendasOnline = isParcelasDoPixEstaoEmAberto(pixVO); //só estornar o contrato se todas as parcelas vinculadas ao pix ainda estiverem em aberto
                if (estornarContratoVendasOnline) {
                    estornarContratoVendasOnlinePixNaoPago(pixVO, vendasOnlineVendaDAO);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            vendasOnlineVendaDAO = null;
        }
    }

    private boolean isParcelasDoPixEstaoEmAberto(PixVO pixVO) {
        Pix pixDao;
        try {
            if (UteisValidacao.emptyList(pixVO.getPixMovParcelas())) {
                //se não tiver parcelas preenchidas, consultar novamente
                pixDao = new Pix(this.getConnection());
                pixVO.setPixMovParcelas(pixDao.consultarPorCodigo(pixVO.getCodigo(), true).getPixMovParcelas());
            }
            if (UteisValidacao.emptyList(pixVO.getPixMovParcelas())) {
                //se ainda estiver vazio, assumir que as parcelas do pix não foram pagas e retornar true;
                return true;
            }
            for (PixMovParcelaVO pixMovParcelaVO : pixVO.getPixMovParcelas()) {
                if (pixMovParcelaVO.getMovParcelaVO() != null && pixMovParcelaVO.getMovParcelaVO().getSituacao().equalsIgnoreCase("PG")) {
                    //SE ENCONTRAR UMA JÁ PAGA, JÁ RETORNA FALSE
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            return true;
        } finally {
            pixDao = null;
        }
    }

    private void marcarAulaPixPagoVendasOnline(PixVO pixVO, VendasOnlineVenda vendasOnlineVendaDAO) {
        try {
            vendasOnlineVendaDAO.marcarAulaVendasOnline(pixVO, null);
            vendasOnlineVendaDAO.incluirCategoriaClientePix(pixVO, pixVO.getPessoa(), pixVO.getEmpresa(), pixVO.getUsuarioVO());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void estornarContratoVendasOnlinePixNaoPago(PixVO pixVO, VendasOnlineVenda vendasOnlineVendaDAO) {
        try {
            vendasOnlineVendaDAO.estornarVendaVendasOnline(pixVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public ZillyonWebFacade getZwFacade() throws Exception {
        if (zwFacade == null) {
            setZwFacade(new ZillyonWebFacade(getConnection()));
        }
        return zwFacade;
    }

    public void setZwFacade(ZillyonWebFacade zwFacade) {
        this.zwFacade = zwFacade;
    }
}
