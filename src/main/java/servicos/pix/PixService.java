package servicos.pix;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaArquivoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.ConvenioCobrancaArquivo;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;

import java.sql.Connection;
import java.util.Array<PERSON>;
import java.util.List;

/**
 *  Created by <PERSON> on 24/01/2024.
 */

public class PixService  extends SuperEntidad<PERSON> {

    public PixService() throws Exception {
    }

    public PixService(Connection conexao) throws Exception {
        super(conexao);
    }

    public PixRequisicaoDto cancelar(PixVO pixVO) throws Exception {
        PixServiceInterfaceFacade service = null;
        try {
            ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobranca(pixVO.getConveniocobranca().getCodigo());
            if (!UteisValidacao.emptyList(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO())) {
                pixVO.getConveniocobranca().setListaConvenioCobrancaArquivoVO(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO());
            }
            service = pixServiceInterfaceFacade(convenioCobrancaVO, this.con);
            return service.cancelar(pixVO);
        } finally {
            service = null;
        }
    }

    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception {
        PixServiceInterfaceFacade service = null;
        try {
            ConvenioCobrancaVO convenioCobrancaVO = obterConvenioCobranca(pixVO.getConveniocobranca().getCodigo());
            if (!UteisValidacao.emptyList(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO())) {
                pixVO.getConveniocobranca().setListaConvenioCobrancaArquivoVO(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO());
            }
            service = pixServiceInterfaceFacade(convenioCobrancaVO, this.con);
            if (convenioCobrancaVO.isPixPjBank()) {
                return service.consultarCobranca(pixVO.getTxid(), convenioCobrancaVO);
            } else {
                return service.consultarCobranca(pixVO);
            }
        } finally {
            service = null;
        }
    }

    public PixRequisicaoDto consultarCobranca(String txIdOrE2EId, ConvenioCobrancaVO convenioCobrancaVO, String tipoIdConsultar) throws Exception {
        PixServiceInterfaceFacade service = null;
        try {
            if (convenioCobrancaVO.isPixBradesco() || convenioCobrancaVO.isPixInter()) {
                montarConvenioCobrancaArquivo(convenioCobrancaVO);
            }
            PixVO pixVO = new PixVO();
            montarPixVo(pixVO, convenioCobrancaVO);
            pixVO.setConveniocobranca(convenioCobrancaVO);

            service = pixServiceInterfaceFacade(convenioCobrancaVO, this.con);
            if (tipoIdConsultar.equals("txId")) {
                pixVO.setTxid(txIdOrE2EId);
                return service.consultarCobranca(pixVO);
            } else if (tipoIdConsultar.contains("e2eid")) {
                pixVO.setE2eId(txIdOrE2EId);
                return service.consultarCobrancaE2EId(pixVO);
            } else {
                throw new Exception("Não foi possível obter txId ou e2eId");
            }
        } finally {
            service = null;
        }
    }

    private PixVO montarPixVo(PixVO pixVO, ConvenioCobrancaVO convenioCobrancaVO) {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            if (!UteisValidacao.emptyList(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO())) {
                pixVO.getConveniocobranca().setListaConvenioCobrancaArquivoVO(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO());
            }
            pixVO.setAmbiente(convenioCobrancaVO.getAmbiente().toString());
            if (convenioCobrancaVO.isPixBB()) {
                pixVO.setBasicAuth(convenioCobrancaVO.getPixBasicAuth());
            }
            if (convenioCobrancaVO.isPixBradesco() || convenioCobrancaVO.isPixSantander()) {
                convenioCobrancaDAO = new ConvenioCobranca(getCon());
                pixVO.setConveniocobranca(convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobrancaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception e) {
            //ignore
        } finally {
            convenioCobrancaDAO = null;
        }
        return pixVO;
    }

    private ConvenioCobrancaVO obterConvenioCobranca(Integer codigo) throws Exception {
        ConvenioCobranca convenioCobrancaDAO = null;
        ConvenioCobrancaArquivo convenioCobrancaArquivoDAO = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            convenioCobrancaArquivoDAO = new ConvenioCobrancaArquivo(con);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (convenioCobrancaVO.isPixBradesco() || convenioCobrancaVO.isPixInter()) {
                montarConvenioCobrancaArquivo(convenioCobrancaVO);
            }
            return convenioCobrancaVO;
        } finally {
            convenioCobrancaDAO = null;
            convenioCobrancaArquivoDAO = null;
        }
    }

    public PixRequisicaoDto criarCobranca(
            ConvenioCobrancaVO convenioCobrancaVO,
            String cpfDevedor,
            PessoaVO pessoaVO,
            String telefoneDevedor,
            Double valor,
            String descricao,
            Integer expiracao,
            EmpresaVO empresaVO,
            String chave,
            List<MovParcelaVO> listaParcelas
    ) throws Exception {
        PixServiceInterfaceFacade service = null;
        ConvenioCobrancaArquivo convenioCobrancaArquivoDAO = null;
        try {
            if (convenioCobrancaVO.isPixBradesco() || convenioCobrancaVO.isPixInter()) {
                montarConvenioCobrancaArquivo(convenioCobrancaVO);
            }
            service = pixServiceInterfaceFacade(convenioCobrancaVO, this.con);

            if (service == null) {
                throw new Exception("Não foi possível obter o service para o tipo de pix");
            }

            if (convenioCobrancaVO.isPixPjBank() || convenioCobrancaVO.isPixAsaas()) {
                if (UteisValidacao.emptyNumber(convenioCobrancaVO.getDiasExpirarPix())) {
                    expiracao = 1;

                } else {
                    expiracao = convenioCobrancaVO.getDiasExpirarPix();
                }
            } else {
                if (UteisValidacao.emptyNumber(expiracao)) {
                    if (convenioCobrancaVO.getPixExpiracao() != null && convenioCobrancaVO.getPixExpiracao() > 0) {
                        expiracao = convenioCobrancaVO.getPixExpiracao();
                    } else {
                        expiracao = 86400;
                    }
                }
            }

            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_ASAAS)) {
                pessoaVO.setCfp(cpfDevedor);
                return service.criarCobranca(convenioCobrancaVO, pessoaVO, valor, Uteis.retirarAcentuacao(descricao), expiracao, empresaVO);
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_PJBANK)) {
                return service.criarCobranca(convenioCobrancaVO, cpfDevedor, pessoaVO.getNome(), telefoneDevedor, valor, Uteis.retirarAcentuacao(descricao), expiracao, chave);
            } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_AFINZ)) {
                return service.criarCobranca(convenioCobrancaVO, pessoaVO, valor, Uteis.retirarAcentuacao(descricao), expiracao, empresaVO, chave, listaParcelas);
            } else {
                return service.criarCobranca(convenioCobrancaVO, cpfDevedor, pessoaVO.getNome(), telefoneDevedor, valor, Uteis.retirarAcentuacao(descricao), expiracao);
            }
        } finally {
            service = null;
            convenioCobrancaArquivoDAO = null;
        }
    }

    public void processarExpirarPix() throws Exception {
        Uteis.logarDebug("PROCESSAMENTO EXPIRAÇÃO DE PIX | INÍCIO...");
        Pix pixDAO;
        try {
            pixDAO = new Pix(this.con);
            List<PixVO> pixAtivos = pixDAO.consultarAtivos();

            if (UteisValidacao.emptyList(pixAtivos)) {
                Uteis.logarDebug("Nenhum pix ativo para verificar a expiração...");
                return;
            }

            Uteis.logarDebug("Encontrei " + pixAtivos.size() + " pix ativo(s) para verificar");

            int i = 1;
            for (PixVO pixVO : pixAtivos) {
                Uteis.logarDebug("Verificando pix " + i + "/" + pixAtivos.size() + " | Cód. " + pixVO.getCodigo() + " | TxId: " + pixVO.getTxid() + " | Tipo: " + pixVO.getConveniocobranca().getTipo().getDescricao());
                i++;

                boolean pixPJBank = pixVO.getConveniocobranca().getTipo().equals(TipoConvenioCobrancaEnum.PIX_PJBANK);
                if ((!pixPJBank && pixVO.expirado()) || (pixPJBank && pixVO.expiradoPJBank())) {
                    if (pixPJBank) {
                        //PJBank deve cancelar lá na API, pois não é automático o cancelamento igual aos demais pix
                        cancelarEExpirarPixPJBank(pixDAO, pixVO);
                    } else {
                        //demais pix altera só em banco, pois lá na API já expira automaticamente
                        cancelarEExpirarPix(pixDAO, pixVO);
                    }

                } else {
                    Uteis.logarDebug("Pix ainda não está expirado");
                }
            }

            Uteis.logarDebug("PROCESSAMENTO EXPIRAÇÃO DE PIX | FIM...");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("ERRO EXPIRAÇÃO DE PIX: " + ex.getMessage());
        } finally {
            pixDAO = null;
        }
    }

    private void cancelarEExpirarPix(Pix pixDAO, PixVO pixVO) {
        try {
            pixVO.setStatus(PixStatusEnum.EXPIRADA.getDescricao());
            pixDAO.alterarStatus(pixVO);
            Uteis.logarDebug("Pix expirado com sucesso");
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível alterar o pix para expirado: " + ex.getMessage());
        }
    }

    private void cancelarEExpirarPixPJBank(Pix pixDAO, PixVO pixVO) {
        PixServiceInterfaceFacade pixService;
        try {
            pixService = getPixService(pixVO.getConveniocobranca(), this.con);
            boolean canceladoComSucessoNaAPIPJBank = false;

            try {
                Uteis.logarDebug("Vou tentar cancelar o pix na API da PJBank");
                pixService.cancelar(pixVO);
                Uteis.logarDebug("Pix PJBank cancelado com sucesso na API");
                canceladoComSucessoNaAPIPJBank = true;
            } catch (Exception ex) {
                Uteis.logarDebug("Não consegui efetuar cancelamento do pix na API da PJBank: " + ex.getMessage());
            }

            if (canceladoComSucessoNaAPIPJBank) {
                pixVO.setStatus(PixStatusEnum.EXPIRADA.getDescricao());
                pixDAO.alterarStatus(pixVO);
                Uteis.logarDebug("Pix expirado com sucesso");
            }
        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível alterar o pix PJBank para expirado: " + ex.getMessage());
        } finally {
            pixService = null;
        }
    }


    private void montarConvenioCobrancaArquivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        ConvenioCobrancaArquivo convenioCobrancaArquivoDAO;
        try {
            convenioCobrancaArquivoDAO = new ConvenioCobrancaArquivo(con);
            List<ConvenioCobrancaArquivoVO> listaConvenioCobrancaArquivoVO = new ArrayList<>();
            listaConvenioCobrancaArquivoVO.addAll(convenioCobrancaArquivoDAO.consultarListaPorConvenioCobranca(convenioCobrancaVO.getCodigo(), false));

            if (convenioCobrancaVO.isPixInter()) {
                listaConvenioCobrancaArquivoVO.get(0).setSenha(null);
            }

            if (!UteisValidacao.emptyList(listaConvenioCobrancaArquivoVO)) {
                for (ConvenioCobrancaArquivoVO item : listaConvenioCobrancaArquivoVO) {
                      //sempre montar o arquivo só onde precisa para evitar vazamento de memória. NÃO colocar lá no montar dados
                    item.setArquivo(convenioCobrancaArquivoDAO.consultarArquivoBytePorChavePrimaria(item.getCodigo()));
                }
            }

            convenioCobrancaVO.setListaConvenioCobrancaArquivoVO(listaConvenioCobrancaArquivoVO);
        } catch (Exception ex) {
        } finally {
            convenioCobrancaArquivoDAO = null;
        }
    }

    private PixServiceInterfaceFacade pixServiceInterfaceFacade(ConvenioCobrancaVO convenioCobrancaVO, Connection con) throws Exception {
        return getPixService(convenioCobrancaVO, con);
    }

    public static PixServiceInterfaceFacade getPixService(ConvenioCobrancaVO convenioCobrancaVO, Connection con) throws Exception {

        if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_BB)) {
            return new PixBB();
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_BRADESCO)) {
            return new PixBradesco();
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_SANTANDER)) {
            return new PixSantander();
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_PJBANK) && con != null) {
            return new PixPjBank(con);
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_INTER) && con != null) {
            return new PixInter(con);
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_ASAAS) && con != null) {
            return new PixAsaas(con, convenioCobrancaVO);
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_ITAU) && con != null) {
            return new PixItau(con, convenioCobrancaVO);
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PIX_AFINZ) && con != null) {
            return new PixAfinz(con);
        }
        return null;
    }
}
