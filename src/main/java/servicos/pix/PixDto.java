package servicos.pix;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;

public class PixDto {

    private PixCalendarioDto calendario;
    private String status;
    private String location;
    private String textoImagemQRcode;
    private String urlQRcode;
    private String txid;
    private Integer revisao;
    private PixDevedorDto devedor;
    private PixValorDto valor;
    private String chave;
    private String solicitacaoPagador;
    private Date dataPagamento;
    private String transactionReceiptUrl;

    //Atributos PjBank
    private Date vencimento;
    private String pedidoNumero;
    private String linkInfo;
    private Date dataCredito;
    private boolean deleted = false; //cancelado asaas
    private String pixCopiaECola = "";

    // Atributos Afinz
    private String cpf;
    private String email;
    private String name;
    private String mobileNumber;
    private String merchantId;
    private String customerId;
    private String amount;
    private Integer installments;
    private String orderKey;
    private String receivingOptionType;
    private String callBack;
    private JSONArray splitPayment;

    public String getUrlQRcode() {
        return urlQRcode;
    }

    public void setUrlQRcode(String urlQRcode) {
        this.urlQRcode = urlQRcode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTextoImagemQRcode() {
        return textoImagemQRcode;
    }

    public void setTextoImagemQRcode(String textoImagemQRcode) {
        this.textoImagemQRcode = textoImagemQRcode;
    }

    public PixCalendarioDto getCalendario() {
        return calendario;
    }

    public void setCalendario(PixCalendarioDto calendario) {
        this.calendario = calendario;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getTxid() {
        return txid;
    }

    public void setTxid(String txid) {
        this.txid = txid;
    }

    public PixDevedorDto getDevedor() {
        return devedor;
    }

    public void setDevedor(PixDevedorDto devedor) {
        this.devedor = devedor;
    }

    public PixValorDto getValor() {
        return valor;
    }

    public void setValor(PixValorDto valor) {
        this.valor = valor;
    }


    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getSolicitacaoPagador() {
        return solicitacaoPagador;
    }

    public void setSolicitacaoPagador(String solicitacaoPagador) {
        this.solicitacaoPagador = solicitacaoPagador;
    }

    public Integer getRevisao() {
        return revisao;
    }

    public void setRevisao(Integer revisao) {
        this.revisao = revisao;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Date getVencimento() {
        return vencimento;
    }

    public void setVencimento(Date vencimento) {
        this.vencimento = vencimento;
    }

    public String getPedidoNumero() {
        if (UteisValidacao.emptyString(pedidoNumero)) {
            return "";
        }
        return pedidoNumero;
    }
    public void setPedidoNumero(String pedidoNumero) {
        this.pedidoNumero = pedidoNumero;
    }

    public String getLinkInfo() {
        if (UteisValidacao.emptyString(linkInfo)) {
            return "";
        }
        return linkInfo;
    }

    public void setLinkInfo(String linkInfo) {
        this.linkInfo = linkInfo;
    }

    public Date getDataCredito() {
        return dataCredito;
    }

    public void setDataCredito(Date dataCredito) {
        this.dataCredito = dataCredito;
    }

    public String getTransactionReceiptUrl() {
        if (UteisValidacao.emptyString(transactionReceiptUrl)) {
            return "";
        }
        return transactionReceiptUrl;
    }

    public void setTransactionReceiptUrl(String transactionReceiptUrl) {
        this.transactionReceiptUrl = transactionReceiptUrl;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public String getPixCopiaECola() {
        return pixCopiaECola;
    }

    public void setPixCopiaECola(String pixCopiaECola) {
        this.pixCopiaECola = pixCopiaECola;
    }

    // Metodos Afinz
    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public Integer getInstallments() {
        return installments;
    }

    public void setInstallments(Integer installments) {
        this.installments = installments;
    }

    public String getOrderKey() {
        return orderKey;
    }

    public void setOrderKey(String orderKey) {
        this.orderKey = orderKey;
    }

    public String getReceivingOptionType() {
        return receivingOptionType;
    }

    public void setReceivingOptionType(String receivingOptionType) {
        this.receivingOptionType = receivingOptionType;
    }

    public String getCallBack() {
        return callBack;
    }

    public void setCallBack(String callBack) {
        this.callBack = callBack;
    }

    public void addSplit(JSONObject split) {
        if(splitPayment == null) {
            splitPayment = new JSONArray();
        }
        this.splitPayment.put(split);
    }
}
