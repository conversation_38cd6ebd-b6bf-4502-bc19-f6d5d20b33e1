package servicos.pix;

import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import controle.arquitetura.threads.ThreadRobo;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaAsaasEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Endereco;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.boleto.asaas.ClienteAsaasDTO;
import servicos.impl.boleto.asaas.CobrancaAsaasRetornoDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 15/05/2023
 */

public class PixAsaas extends SuperServico implements PixServiceInterfaceFacade {
    private ConvenioCobrancaVO convenioCobrancaVO;
    private String URL_API_ASAAS = "";


    public PixAsaas(Connection con, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        super(con);
        this.convenioCobrancaVO = convenioCobrancaVO;
        popularInformacoes();
    }


    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO,
                                          PessoaVO pessoaVO, Double valor,
                                          String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {

        tratarCamposNulosConvenioCobranca(convenioCobrancaVO);
        ClienteAsaasDTO clienteAsaasDTO = obterClienteAsaas(pessoaVO);

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setNome(clienteAsaasDTO.getNome());
        pixDevedorDto.setCpf(clienteAsaasDTO.getCpfCnpj());

        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(String.valueOf(valor));

        PixDto pixDto = new PixDto();
        pixDto.setDevedor(pixDevedorDto);
        pixDto.setVencimento(Uteis.somarDias(Calendario.hoje(), expiracao));
        pixDto.setValor(pixValorDto);
        pixDto.setPedidoNumero(String.valueOf(gerarNumeroAleatorio()));

        pixDto.setCalendario(new PixCalendarioDto(expiracao));

        //CRIAR PIX
        PixRequisicaoDto pixRequisicaoDto;
        try {
            pixRequisicaoDto = criarCobranca(clienteAsaasDTO, pixDto, empresaVO);
            preencherObtejoRetornoCriacao(pixRequisicaoDto.getResposta(), pixDto);
        } catch (Exception ex) {
            throw new Exception("Não foi possível gerar o pix: " + ex.getMessage());
        }

        int tentativas = 1;
        boolean conseguiuConsultarPayload = false;

        //CONSULTAR PIX para obter payload (consultar no máx 4 vezes em caso de erros)
        while (tentativas <= 4) {
            tentativas++;

            //aguardar 1 segundo pelo menos antes de consultar o pix a primeira vez.
            ThreadRobo.sleep(1000);
            try {
                String payload = obterQRCode(pixDto.getTxid(), convenioCobrancaVO);
                if (!UteisValidacao.emptyString(payload)) {
                    pixDto.setTextoImagemQRcode(payload);
                    conseguiuConsultarPayload = true;
                    break;
                }
            } catch (Exception ignore) {
            }
        }

        if (UteisValidacao.emptyString(pixDto.getTextoImagemQRcode())) {
            throw new Exception("Não foi possível obter o QrCode para exibir");
        }

        //se não consultar o payload com sucesso nas 4 tentativas, deve cancelar o que foi gerado
        if (!conseguiuConsultarPayload) {
            PixVO pixVO = new PixVO();
            pixVO.setTxid(pixDto.getTxid());
            pixVO.setConveniocobranca(convenioCobrancaVO);
            try {
                cancelar(pixVO);
            } catch (Exception ignore) {
                throw new Exception("Não foi possível gerar o pix por instabilidade no ASAAS. Gerou mas não conseguiu consultar nem cancelar. Tente novamente");
            }
            //conseguiu cancelar, lança essa exceção aqui
            throw new Exception("Não foi possível gerar o pix por instabilidade no ASAAS. Gerou mas não conseguiu consultar. Tente novamente");
        }

        //chegou até aqui, tudo ok!
        pixDto.setStatus(PixStatusEnum.ATIVA.getDescricao());
        pixRequisicaoDto.setPixDto(pixDto);
        return pixRequisicaoDto;
    }

    private PixRequisicaoDto criarCobranca(ClienteAsaasDTO clienteAsaasDTO, PixDto pixDto, EmpresaVO empresaVO) throws Exception {

        //Montar Objeto JSON Cobranca Asaas
        JSONObject jsonEnvio = montarCobrancaJSON(clienteAsaasDTO, pixDto, empresaVO);

        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/payments";
        RequestHttpService service = new RequestHttpService();

        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, jsonEnvio.toString(), MetodoHttpEnum.POST);

        if (resposta.getHttpStatus() == 401) {
            throw new ConsistirException("Chave da API informada no convênio de cobrança é inválida.");
        }

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        }
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(jsonEnvio.toString());
        pixRequisicaoDto.setResposta(resposta.getResponse());

        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception {

        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", pixVO.getAppKey());

        String endpoint = URL_API_ASAAS + "api/v3/payments/" + pixVO.getTxid();
        RequestHttpService service = new RequestHttpService();

        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);
        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        }
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setResposta(resposta.getResponse());

        JSONObject json = new JSONObject(resposta.getResponse());
        pixRequisicaoDto.setPixDto(preencherObjetoRetorno(json));

        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", pixVO.getAppKey());

        String endpoint = URL_API_ASAAS + "api/v3/payments/" + pixVO.getTxid();
        RequestHttpService service = new RequestHttpService();

        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.DELETE);
        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
        }

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setResposta(resposta.getResponse());
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

            String endpoint = URL_API_ASAAS + "api/v3/payments/" + idExterno + "/pixQrCode";
            RequestHttpService service = new RequestHttpService();

            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.POST);
            if (resposta.getHttpStatus() != 200) {
                throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
            }
            JSONObject retorno = new JSONObject(resposta.getResponse());
            if (!UteisValidacao.emptyString(retorno.optString("payload"))) {
                return retorno.optString("payload");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        return false;
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return false;
    }

    @Override
    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public PixDto preencherObjetoRetorno(JSONObject resposta) throws Exception {

        PixDto pixDto = new PixDto();
        CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTO = new CobrancaAsaasRetornoDTO(resposta);

        pixDto.setTxid(cobrancaAsaasRetornoDTO.getId());

        if (!UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getPaymentDate())) {
            pixDto.setDataPagamento(Uteis.getDate(cobrancaAsaasRetornoDTO.getPaymentDate()));
            pixDto.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());
        } else {
            pixDto.setStatus(PixStatusEnum.ATIVA.getDescricao());
        }
        if (!UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getCreditDate())) {
            pixDto.setDataCredito(Uteis.getDate(cobrancaAsaasRetornoDTO.getCreditDate()));
        }
        pixDto.setTransactionReceiptUrl(cobrancaAsaasRetornoDTO.getTransactionReceiptUrl());

        pixDto.setPedidoNumero(cobrancaAsaasRetornoDTO.getExternalReference());
        pixDto.setVencimento(Uteis.getDate(cobrancaAsaasRetornoDTO.getDueDate()));

        pixDto.setDeleted(cobrancaAsaasRetornoDTO.isDeleted()); //cancelamento
        if (pixDto.isDeleted()) {
            pixDto.setStatus(PixStatusEnum.CANCELADA.toString());
        }

        return pixDto;
    }

    public void preencherObtejoRetornoCriacao(String resposta, PixDto pixDto) throws Exception {
        CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTO = new CobrancaAsaasRetornoDTO(new JSONObject(resposta));
        pixDto.setTxid(cobrancaAsaasRetornoDTO.getId());
    }

    public CobrancaAsaasRetornoDTO obterDadosCobranca(String idCobranca) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/payments/" + idCobranca;
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() == 200) {
            JSONObject json = new JSONObject(resposta.getResponse());
            return new CobrancaAsaasRetornoDTO(json);
        }
        return new CobrancaAsaasRetornoDTO();
    }

    public Integer obterExpiracaoEmSegundos(Integer expiracao) {
        return expiracao * 86400; //86400 = 1 dia
    }

    public void tratarCamposNulosConvenioCobranca(ConvenioCobrancaVO convenioCobrancaVO) {
        //Necessário pois na hora de incluir o PixVO não pode ser nulo
        convenioCobrancaVO.setPixClientId("");
        convenioCobrancaVO.setPixClientSecret("");
        convenioCobrancaVO.setPixAppKey(convenioCobrancaVO.getCodigoAutenticacao01());
    }

    public JSONObject montarCobrancaJSON(ClienteAsaasDTO clienteAsaasDTO, PixDto pixDto, EmpresaVO empresaVO) {
        try {
            JSONObject params = new JSONObject();
            params.put("customer", clienteAsaasDTO.getId()); //Identificador único do cliente no Asaas
            params.put("billingType", TipoCobrancaAsaasEnum.PIX.getDescricao()); //tipo da cobrança (Boleto, Cartão ou Pix)
            params.put("value", Double.valueOf(pixDto.getValor().getOriginal())); //Valor da cobrança

            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            params.put("dueDate", dateFormat.format(pixDto.getVencimento())); //Data de vencimento da cobrança
            params.put("externalReference", gerarNumeroAleatorio()); //Campo livre para busca

            //MULTA/JUROS
            if (empresaVO.getCobrarAutomaticamenteMultaJuros() && empresaVO.isCobrarMultaJurosAsaas()) {
                if (!UteisValidacao.emptyNumber(empresaVO.getValorMultaAsaas())) {
                    JSONObject multa = new JSONObject();
                    multa.put("value", Uteis.arredondarForcando2CasasDecimais(empresaVO.getValorMultaAsaas()));
                    params.put("fine", multa);
                }
                if (!UteisValidacao.emptyNumber(empresaVO.getValorJurosAsaas())) {
                    JSONObject multa = new JSONObject();
                    multa.put("value", Uteis.arredondarForcando2CasasDecimais(empresaVO.getValorJurosAsaas() * 30)); //juros mensal calculado com base no juros por dia
                    params.put("interest", multa);
                }
            }
            return params;

        } catch (Exception ex) {
        }

        return new JSONObject();
    }


    public String incluirClienteAsaas(ClienteAsaasDTO clienteAsaasDTO, PessoaVO pessoaVO) throws Exception {

        JSONObject jsonCliente = montarClienteJSON(clienteAsaasDTO);

        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/customers";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, jsonCliente.toString(), MetodoHttpEnum.POST);

        if (resposta.getHttpStatus() == 401) {
            throw new ConsistirException("Chave da API informada no convênio de cobrança é inválida.");
        }

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(resposta.getResponse());
        } else {
            JSONObject jsonObject = new JSONObject(resposta.getResponse());
            if (jsonObject.has("id") && !UteisValidacao.emptyString(jsonObject.optString("id"))) {
                String clientId = jsonObject.optString("id");
                pessoaVO.setIdAsaas(clientId);
                new Pessoa(getCon()).alterarIdAsaas(pessoaVO);
                return clientId;
            }
        }
        return "";
    }

    public JSONObject montarClienteJSON(ClienteAsaasDTO clienteAsaasDTO) {
        JSONObject params = new JSONObject();

        //parâmetros obrigatórios
        params.put("name", clienteAsaasDTO.getNome());
        params.put("cpfCnpj", clienteAsaasDTO.getCpfCnpj());
        params.put("notificationDisabled", !this.convenioCobrancaVO.isEnviarNotificacoes());

        //parâmetros opcionais - Importante enviar pois o aluno fica com o cadastro completo lá no portal facilitando conferências e recebendo notificações do Asaas.
        if (!UteisValidacao.emptyString(clienteAsaasDTO.getEmail())) {
            params.put("email", clienteAsaasDTO.getEmail());
        }
        if (!UteisValidacao.emptyString(clienteAsaasDTO.getTelefone())) {
            params.put("mobilePhone", clienteAsaasDTO.getTelefone());
        }

        if (clienteAsaasDTO.getEnderecoVO() != null) {
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getEndereco())) {
                params.put("address", clienteAsaasDTO.getEnderecoVO().getEndereco());
            }
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getNumero())) {
                params.put("addressNumber", clienteAsaasDTO.getEnderecoVO().getNumero());
            }
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getComplemento())) {
                params.put("complement", clienteAsaasDTO.getEnderecoVO().getComplemento());
            }
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getCep())) {
                params.put("postalCode", clienteAsaasDTO.getEnderecoVO().getCep());
            }
        }
        return params;
    }

    private void validarCamposObrigatorioPagador(PessoaVO pessoaVO) throws Exception {

        List<String> camposInvalidos = new ArrayList<>();
        if (!SuperVO.verificaCPF(pessoaVO.getCfp())) {
            camposInvalidos.add(" CPF");
        }
        String campos = "";
        if (!UteisValidacao.emptyList(camposInvalidos)) {
            for (String campo : camposInvalidos) {
                campos += campo + ",";
            }
            if (camposInvalidos.size() > 1) {
                String mensagem = "Os campos: " + Uteis.removerUltimoCaractere(campos) + " do aluno são obrigatórios!";
                throw new ConsistirException(mensagem);
            } else {
                String mensagem = "O " + Uteis.removerUltimoCaractere(campos) + " do aluno é obrigatório!";
                throw new ConsistirException(mensagem);
            }
        }
    }

    private String obterEmailPessoa(PessoaVO pessoaVO) {
        for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return "";
    }

    private String obterTelefonePessoa(PessoaVO pessoaVO) {
        for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
            if (Uteis.validarTelefoneCelular(UteisTelefone.removerCaracteresEspeciais(telefoneVO.getNumero())))
                return UteisTelefone.removerCaracteresEspeciais(telefoneVO.getNumero());
        }
        return "";
    }

    public EnderecoVO obterEnderecoVO(PessoaVO pessoaVO) throws Exception {
        Endereco enderecoDAO;
        try {
            enderecoDAO = new Endereco(getCon());
            List<EnderecoVO> listaEnderecos = enderecoDAO.consultarEnderecos(pessoaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(listaEnderecos)) {
                return new EnderecoVO();
            }

            Ordenacao.ordenarListaReverse(listaEnderecos, "codigo");

            for (EnderecoVO enderecoVO : listaEnderecos) {
                if (enderecoVO.getEnderecoCorrespondencia()) {
                    return enderecoVO;
                }
            }

            for (EnderecoVO enderecoVO : listaEnderecos) {
                if (enderecoVO.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    return enderecoVO;
                }
            }

            return listaEnderecos.get(0);
        } finally {
            enderecoDAO = null;
        }
    }

    public ClienteAsaasDTO obterClienteAsaas(PessoaVO pessoaVO) throws Exception {

        ClienteAsaasDTO clienteAsaas = new ClienteAsaasDTO();
        clienteAsaas.setNome(pessoaVO.getNome());
        clienteAsaas.setCpfCnpj(pessoaVO.getCfp().replaceAll("[^0-9]", ""));
        clienteAsaas.setEmail(obterEmailPessoa(pessoaVO));
        clienteAsaas.setTelefone(obterTelefonePessoa(pessoaVO));

        if (UteisValidacao.emptyString(pessoaVO.getIdAsaas())) {
            try {
                EnderecoVO enderecoVO = obterEnderecoVO(pessoaVO);
                clienteAsaas.setEnderecoVO(enderecoVO);
                validarCamposObrigatorioPagador(pessoaVO);
                pessoaVO.setIdAsaas(incluirClienteAsaas(clienteAsaas, pessoaVO));
            } catch (Exception ex) {
                throw new ConsistirException("Falha ao inserir o cliente no portal Asaas: " + ex.getMessage());
            }
        }

        if (UteisValidacao.emptyString(pessoaVO.getIdAsaas())) {
            throw new ConsistirException("Falha ao inserir o cliente no portal Asaas");
        }
        clienteAsaas.setId(pessoaVO.getIdAsaas());
        return clienteAsaas;
    }

    public String tratarMensagemErro(String resposta) {
        if (!UteisValidacao.emptyString(resposta)) {
            JSONObject jsonObject = new JSONObject(resposta);
            if (jsonObject.has("errors") && jsonObject.optJSONArray("errors") != null) {
                JSONObject jsonErrors = new JSONObject(jsonObject.getJSONArray("errors").get(0).toString());
                if (!UteisValidacao.emptyString(jsonErrors.optString("description"))) {
                    return jsonErrors.optString("description");
                }
            }
            return resposta;
        }
        return "Erro ao realizar operação no Asaas";
    }

    private void popularInformacoes() {
        if (this.convenioCobrancaVO != null) {
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.URL_API_ASAAS = PropsService.getPropertyValue(PropsService.urlApiAsaasProducao);
            } else {
                this.URL_API_ASAAS = PropsService.getPropertyValue(PropsService.urlApiAsaasSandbox);

            }
        }
    }

    @Override
    public void validateResponseError(String resposta, int status) throws PixRequestException {
    }

    private Integer gerarNumeroAleatorio() {
        SimpleDateFormat sdf = new SimpleDateFormat("ddHHmmss");
        Double vlrRandom = 0.0;
        vlrRandom = Math.random() * Double.valueOf(sdf.format(Calendario.hoje()));
        return vlrRandom.intValue();
    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        return null;
    }

    @Override
    public String fixResponseErros(String responseJson) {
        return null;
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {
    }

    @Override
    public String translateMessages(String message) {
        return null;
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws IOException, PixRequestException {
        return null;
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public String token(PixVO pixVO) throws Exception {
        return null;
    }

    @Override
    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {
        return null;
    }

    @Override
    public HttpClient createConnector() {
        return ExecuteRequestHttpService.createConnector();
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO, String chave, List<MovParcelaVO> listaParcelas) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao, String chave) throws Exception {
        return null;
    }
}
