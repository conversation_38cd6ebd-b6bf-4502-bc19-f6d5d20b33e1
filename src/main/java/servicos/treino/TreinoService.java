package servicos.treino;

import br.com.pactosolucoes.contrato.servico.impl.ContratoAssinaturaDigitalServiceImpl;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.AditivoVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import negocio.comuns.contrato.ContratoTextoPadraoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoAssinaturaDigital;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.financeiro.MovParcela;
import org.json.JSONArray;
import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class TreinoService implements AutoCloseable {

    private final Connection con;

    public TreinoService(Connection con) throws Exception {
        this.con = con;
    }

    @Override
    public void close() throws Exception {
        if (con != null && !con.isClosed()) {
            con.close();
        }
    }

    public JSONObject ativosEmpresa(Integer empresa) throws Exception {
        JSONObject json = new JSONObject();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "select count(c.codigo) as total from cliente c \n" +
                        "inner join pessoa p on c.pessoa = p.codigo \n" +
                        "WHERE (c.situacao = 'AT') \n" +
                        "AND c.empresa = " + empresa, con)) {
            if (rs.next()) {
                json.put("ativos", rs.getInt("total"));
            }
        }
        return json;
    }

    public JSONObject ativosEmpresaProfessores(Integer empresa, Boolean ativos) throws Exception {
        JSONObject json = new JSONObject();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                " select colaborador, COUNT(v.cliente) as clientes from vinculo v \n" +
                        " inner join cliente c on c.codigo = v.cliente \n" +
                        " where tipovinculo = 'TW' " + (ativos ? "and situacao = 'AT'" : " ") + "and empresa = " + empresa +
                        " group by colaborador ", con)) {
            while (rs.next()) {
                json.put(String.valueOf(rs.getInt("colaborador")), rs.getInt("clientes"));
            }
        }
        return json;
    }

    public JSONObject matriculasAtivosEmpresa(Integer empresa) throws Exception {
        JSONObject json = new JSONObject();
        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "select c.codigomatricula  from cliente c \n" +
                        " inner join pessoa p on c.pessoa = p.codigo \n" +
                        " WHERE (c.situacao = 'AT') \n" +
                        " AND c.empresa = " + empresa, con)) {
            while (rs.next()) {
                array.put(rs.getInt("codigomatricula"));
            }
        }
        json.put("matriculas", array);
        return json;
    }

    public Map<Integer, Integer> matriculasExternas() throws Exception {
        Map<Integer, Integer> maps = new HashMap<>();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "select matriculaexterna, matricula from cliente c where matriculaexterna > 0", con)) {
            while (rs.next()) {
                maps.put(rs.getInt("matriculaexterna"), rs.getInt("matricula"));
            }
        }
        return maps;
    }

    public Map<Integer, Integer> matriculasExternasProspects(Integer empresa) throws Exception {
        Map<Integer, Integer> maps = new HashMap<>();
        String sql = "SELECT pes.idexterno, cli.codigomatricula FROM cliente cli \n" +
                " INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n" +
                " WHERE pes.idexterno::varchar ILIKE 'P%' \n" +
                " OR (pes.idexterno IS NOT NULL AND COALESCE(cli.matriculaexterna,0) = 0) \n";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql += " AND cli.empresa = " + empresa;
        }
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            while (rs.next()) {
                try {
                    Integer idExterno = Integer.parseInt(rs.getString("idexterno").replace("P", ""));
                    maps.put(idExterno, rs.getInt("codigomatricula"));
                } catch (Exception e) {
                }
            }
        }
        return maps;
    }

    public JSONObject matriculasNaoImportadas(Integer empresa) throws Exception {
        JSONObject json = new JSONObject();
        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "select matricula from situacaoclientesinteticodw s \n" +
                        "left join usuariomovel u on u.cliente = s.codigocliente \n" +
                        "where datacadastro > '2021-09-01' and datacadastro  < '2021-09-07' \n" +
                        "and u.codigo is null and empresacliente  = " + empresa
                        + " limit 50", con)) {
            while (rs.next()) {
                array.put(rs.getInt("matricula"));
            }
        }
        json.put("matriculas", array);
        return json;
    }

    public JSONObject matriculasAtivosEmpresaProfessor(Integer empresa, Integer professor, Boolean ativos) throws Exception {
        JSONObject json = new JSONObject();
        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                " select c.codigomatricula from vinculo v \n" +
                        " inner join cliente c on c.codigo = v.cliente \n" +
                        " where tipovinculo = 'TW' " + (ativos ? "and situacao = 'AT'" : " ") + " and empresa = " + empresa +
                        " and colaborador = " + professor
                , con)) {
            while (rs.next()) {
                array.put(rs.getInt("codigomatricula"));
            }
        }
        json.put("matriculas", array);
        return json;
    }

    public JSONArray codigosColaboradores(Integer empresa, String situacao, String tipos, String cargas, String modalidades) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct t.colaborador from tipocolaborador t \n");
        sql.append(" inner join colaborador c on c.codigo = t.colaborador \n");
        if (!UteisValidacao.emptyString(modalidades)) {
            sql.append(" inner join colaboradormodalidade cm on c.codigo = cm.colaborador \n");
        }
        sql.append(" where t.descricao in (").append(tipos).append(") \n");
        if (!UteisValidacao.emptyString(situacao)) {
            sql.append(" and c.situacao = '").append(situacao.equals("ativo") ? "AT" : "NA").append("' \n");
        }
        if (!UteisValidacao.emptyString(cargas)) {
            sql.append(" and c.cargahoraria in (").append(cargas).append(") \n");
        }
        if (!UteisValidacao.emptyString(modalidades)) {
            sql.append(" and cm.modalidade in (").append(modalidades).append(") \n");
        }
        sql.append(" and c.empresa = ").append(empresa);
        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                array.put(rs.getInt("colaborador"));
            }
        }
        return array;
    }

    public void colaboradorProgramaData(Integer codigo, Date termino) throws Exception {
        try (PreparedStatement stm = con.prepareStatement("update colaborador set terminoultimoprogramatreino = ? where codigo = ?")) {
            stm.setDate(1, Uteis.getDataJDBC(termino));
            stm.setInt(2, codigo);
            stm.execute();
        }
    }

    private void montarFiltroDataTerminoPrograma(StringBuilder sql, String tipos, Date limitAvencer) {
        if (tipos != null) {
            String[] split = tipos.split(",");
            StringBuilder secundario = new StringBuilder();
            for (String s : split) {
                if (!s.startsWith("TIPO_COL_") && !s.isEmpty()) {
                    switch (s) {
                        case "SEM_TREINO":
                            secundario.append(" or c.terminoultimoprogramatreino is null ");
                            break;
                        case "EM_DIA":
                            secundario.append(" or c.terminoultimoprogramatreino::date > '");
                            secundario.append(Calendario.getDataAplicandoFormatacao(limitAvencer, "yyyy-MM-dd")).append("'");
                            break;
                        case "A_VENCER":
                            secundario.append(" or (c.terminoultimoprogramatreino::date >= '");
                            secundario.append(Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
                            secundario.append(" and c.terminoultimoprogramatreino::date <= '");
                            secundario.append(Calendario.getDataAplicandoFormatacao(limitAvencer, "yyyy-MM-dd")).append("')");
                            break;
                        case "VENCIDO":
                            secundario.append(" or c.terminoultimoprogramatreino::date < '");
                            secundario.append(Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("'");
                            break;
                    }
                }
            }
            if (!secundario.toString().isEmpty()) {
                sql.append(" and (").append(secundario.toString().replaceFirst(" or ", " ")).append(") ");
            }
        }
    }

    public JSONObject colaboradoresPrescricao(Integer empresa, String search, String tipos, Integer page, Integer size, Date limitAvencer) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select count(distinct c.codigo) as cont from colaborador c \n");
        sql.append(" inner join pessoa p on p.codigo = c.pessoa \n");
        if (tipos != null && tipos.contains("TIPO_COL_")) {
            sql.append(" inner join tipocolaborador t on t.colaborador = c.codigo \n");
        }
        sql.append(" where situacao = 'AT' and empresa = ").append(empresa);
        if (!UteisValidacao.emptyString(search)) {
            if (search.matches("\\d+")) {
                sql.append(" and c.codigo = ").append(search).append(" ");
            } else {
                sql.append(" and p.nome ilike '").append(search).append("%'");
            }
        }
        montarFiltroDataTerminoPrograma(sql, tipos, limitAvencer);
        if (tipos != null && tipos.contains("TIPO_COL_")) {
            String[] split = tipos.split(",");
            String filtrosTipos = "";
            for (String s : split) {
                filtrosTipos += s.startsWith("TIPO_COL_") ? (",'" + s.replace("TIPO_COL_", "") + "'") : "";
            }
            sql.append(" and t.descricao in (");
            sql.append(filtrosTipos.replaceFirst(",", "")).append(")");

        }
        JSONObject retorno = new JSONObject();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            retorno.put("total", rs.next() ? rs.getInt("cont") : 0);
        }
        sql.append(" order by terminoultimoprogramatreino is null desc, terminoultimoprogramatreino, p.nome ");
        sql.append(" limit ").append(size);
        sql.append(" offset ").append(page);

        JSONArray array = new JSONArray();
        String codigos = "";
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString().replace("count(distinct c.codigo) as cont", "distinct p.nome, p.fotokey, c.codigo, " +
                "c.terminoultimoprogramatreino, (terminoultimoprogramatreino is null) "), con)) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("id", rs.getInt("codigo"));
                codigos += "," + rs.getInt("codigo");
                json.put("nome", rs.getString("nome"));
                json.put("foto", UteisValidacao.emptyString(rs.getString("fotokey")) ? "" : Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                array.put(json);
            }
        }
        retorno.put("lista", povoarTiposColaborador(array, codigos));
        return retorno;
    }

    private JSONArray povoarTiposColaborador(JSONArray array, String codigos) throws Exception {
        if (codigos == null || codigos.isEmpty()) {
            return array;
        }
        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("select colaborador, descricao " +
                "from tipocolaborador t where colaborador in (" +
                codigos.replaceFirst(",", "") + ")", con
        );

        Map<Integer, String> descricoesTipo = new HashMap<>();
        while (resultSet.next()) {
            TipoColaboradorEnum tipo = TipoColaboradorEnum.getTipo(resultSet.getString("descricao"));
            if (tipo != null) {
                descricoesTipo.put(resultSet.getInt("colaborador"), tipo.getDescricao());
            }
        }
        JSONArray arrayTipos = new JSONArray();
        for (int i = 0; i < array.length(); i++) {
            JSONObject json = array.getJSONObject(i);
            int id = json.getInt("id");
            json.put("tipo", descricoesTipo.get(id));
            arrayTipos.put(json);
        }
        return arrayTipos;
    }

    public JSONObject clientesPrescricao(String matriculas) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select cli.codigo, p.fotokey, c.mensagem  from cliente cli \n");
        sql.append(" inner join pessoa p on p.codigo = cli.pessoa \n");
        sql.append(" left join clientemensagem c on c.cliente = cli.codigo and c.tipomensagem = 'AM' \n");
        sql.append(" where cli.codigo in (");
        sql.append(matriculas).append(") ");

        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("codigo", rs.getInt("codigo"));
                json.put("mensagem",
                        UteisValidacao.emptyString(rs.getString("mensagem")) ? "" :
                                Jsoup.clean(rs.getString("mensagem").replace("<title>Untitled document</title>", ""), Whitelist.none()));
                json.put("foto", UteisValidacao.emptyString(rs.getString("fotokey")) ? "" : Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                array.put(json);
            }
        }
        JSONObject json = new JSONObject();
        json.put("lista", array);
        return json;
    }

    public JSONArray ativosProfessorEmData(Integer professor, Integer empresa, Date dataBase) throws Exception {
        StringBuilder sql = new StringBuilder();
        String dataBaseStr = Calendario.getDataAplicandoFormatacao(dataBase, "yyyy-MM-dd HH:mm:ss");
        sql.append(" select cli.matricula from historicovinculo h \n");
        sql.append(" inner join cliente cli on cli.codigo = h.cliente \n");
        sql.append(" inner join contrato c on cli.pessoa = c.pessoa \n");
        sql.append(" where h.colaborador = ").append(professor);
        sql.append(" and h.tipohistoricovinculo = 'EN' \n");
        sql.append(" and h.tipocolaborador = 'TW' \n");
        sql.append(" and h.dataregistro < '");
        sql.append(dataBaseStr).append("' \n");
        sql.append(" and not exists (select codigo from historicovinculo hs where hs.colaborador = ").append(professor);
        sql.append(" and hs.cliente = h.cliente \n");
        sql.append(" and hs.tipohistoricovinculo = 'SD' and hs.tipocolaborador = 'TW' and h.codigo < hs.codigo \n");
        sql.append(" and hs.dataregistro < '");
        sql.append(dataBaseStr).append("') \n");
        sql.append(" and '").append(dataBaseStr).append("' between c.vigenciade and c.vigenciaateajustada \n");
        sql.append(" and c.codigo not in (select distinct h.contrato from historicocontrato h where h.tipohistorico in ('CA','DE') and '");
        sql.append(dataBaseStr).append("' between h.datainiciosituacao and h.datafinalsituacao) \n");
        sql.append(" and c.codigo not in (select distinct o.contrato from contratooperacao  o where o.tipooperacao in ('TR','TV') and '");
        sql.append(dataBaseStr).append("' >= o.datainicioefetivacaooperacao and '");
        sql.append(dataBaseStr).append("' <= o.datafimefetivacaooperacao) \n");
        sql.append(" and c.codigo not in (select distinct contratobaseadorenovacao from contrato ct where ct.pessoa = c.pessoa and '");
        sql.append(dataBaseStr).append("' between ct.vigenciaDe and ct.vigenciaAteAjustada) \n");
        sql.append(" and cli.empresa = ").append(empresa);
        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                array.put(rs.getInt("matricula"));
            }
        }
        return array;
    }

    public JSONObject existeParcelaVencida(final Integer empresa, final Integer codPessoa) {
        JSONObject json = new JSONObject();
        ConfiguracaoSistema configuracaoSistemaDAO = null;
        Empresa empresaDAO = null;
        MovParcela movParcelaDAO = null;
        try {
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.buscarPorCodigo(1, false,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            empresaDAO = new Empresa(con);
            EmpresaVO empresaVo = empresaDAO.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            movParcelaDAO = new MovParcela(con);
            boolean parcelaVencida = (movParcelaDAO.existeParcelaVencidaSegundoConfiguracoes(
                    codPessoa, empresaVo, configuracaoSistemaVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            json.put("parcelaVencida", parcelaVencida);
            return json;
        } catch (Exception ex) {
            json.put("parcelaVencida", false);
            return json;
        } finally {
            configuracaoSistemaDAO = null;
            empresaDAO = null;
            movParcelaDAO = null;
        }
    }

    public JSONArray consultarContratosAssinaturaDigital(HttpServletRequest request, String chave, String matricula, Integer empresa, boolean validarContratoAssinado) {
        ContratoAssinaturaDigitalServiceImpl cadServiceContrato;
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT p.datanasc as datanascimento, (p.nomemae is not null and p.nomemae != '') as possuiMae, (p.nomepai is not null and p.nomepai != '') as possuiPai, c.codigo as contrato," +
                    " (cad.assinatura is not null and cad.assinatura <> '' OR c.dataassinaturacontrato is not null) as assinado, c.situacao, false AS ehAditivo, null AS aditivo from contrato c ");
            sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa ");
            sql.append(" INNER JOIN cliente cli ON p.codigo = cli.pessoa ");
            sql.append(" LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo and cad.ehaditivo is false ");
            sql.append(" WHERE c.situacao = 'AT' ");
            sql.append(" AND cli.empresa = ").append(empresa);
            sql.append(" AND cli.codigomatricula::varchar = '").append(matricula).append("' ");

            sql = adicionarAditivosQuery(sql, matricula, empresa);

            final JSONArray jsonArray = new JSONArray();
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("datanascimento", rs.getString("datanascimento"));
                    json.put("possuiResponsavel", (rs.getBoolean("possuiMae") || rs.getBoolean("possuiPai")));
                    json.put("contrato", rs.getInt("contrato"));
                    json.put("assinado", rs.getBoolean("assinado"));
                    json.put("situacaocontrato", rs.getString("situacao"));
                    json.put("ehAditivo", rs.getBoolean("ehAditivo"));
                    if (rs.getInt("aditivo") != 0) {
                        json.put("aditivo", rs.getInt("aditivo"));
                    }

                    jsonArray.put(json);
                }
            }
            if (validarContratoAssinado) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject obj = (JSONObject) jsonArray.get(i);
                    if (!obj.getBoolean("assinado")) {
                        return jsonArray;
                    }
                }
            }
            cadServiceContrato = new ContratoAssinaturaDigitalServiceImpl(con);
            cadServiceContrato.setServletContext(request.getSession().getServletContext());
            ContratoTextoPadrao contratoTextoPadraoDao = new ContratoTextoPadrao(con);
            JSONArray jsonArrayReturn = new JSONArray();

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);
                ContratoVO c = cadServiceContrato.contratoPreparado(chave, obj.getInt("contrato"));

                final int aditivo = obj.optInt("aditivo");
                String texto;
                if (aditivo == 0) {
                    texto = contratoTextoPadraoDao.consultarHtmlContrato(c.getCodigo(), false);
                } else {
                    texto = contratoTextoPadraoDao.consultarHtmlContratoAditivo(c.getCodigo(), false, aditivo, true);
                }

                texto = texto.replaceAll("__4ssin4tur4digit4l__", "<b>A assinatura digital ser colocada aqui.</b>");
                texto = texto.replaceAll("__4ssin4tur4digit4l2__", "<b>A assinatura digital resp. financeiro será colocada aqui.</b>");
                texto = cadServiceContrato.arranjarImagens(chave, texto, c, true);

                obj.put("contratotextopadrao", texto);
                jsonArrayReturn.put(obj);
            }

            return jsonArrayReturn;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new JSONArray();
        } finally {
            cadServiceContrato = null;
        }
    }

    StringBuilder adicionarAditivosQuery(StringBuilder sqlRoot, String matricula, Integer empresa) {
        final StringBuilder sqlAditivoAssinados = new StringBuilder();
        sqlAditivoAssinados.append("SELECT p.datanasc as datanascimento, (p.nomemae is not null and p.nomemae != '') as possuiMae, (p.nomepai is not null and p.nomepai != '') as possuiPai, c.codigo as contrato, true as assinado, c.situacao, true AS ehAditivo, ad.codigo AS aditivo\n");
        sqlAditivoAssinados.append("from contrato c\n");
        sqlAditivoAssinados.append("JOIN pessoa p ON p.codigo = c.pessoa\n");
        sqlAditivoAssinados.append("JOIN cliente cli ON p.codigo = cli.pessoa\n");
        sqlAditivoAssinados.append("JOIN plano pl ON (pl.codigo = c.plano)\n");
        sqlAditivoAssinados.append("JOIN planotextopadrao p2 ON (p2.codigo = pl.planotextopadrao)\n");
        sqlAditivoAssinados.append("JOIN aditivo ad ON (ad.plano = p2.codigo)\n");
        sqlAditivoAssinados.append("LEFT JOIN contratoassinaturadigital cad ON cad.contratoaditivo = c.codigo\n");
        sqlAditivoAssinados.append("WHERE c.situacao = 'AT' AND pl.vigenciaate >= current_date AND ad.dataprocessamento >= c.datalancamento\n");
        sqlAditivoAssinados.append("AND cad.ehAditivo is true AND cad.aditivo = ad.codigo AND (cad.assinatura is not null AND cad.assinatura <> '')\n");
        sqlAditivoAssinados.append("AND cli.empresa = ").append(empresa).append("\n");
        sqlAditivoAssinados.append("AND cli.codigomatricula::varchar = '").append(matricula).append("'");

        final StringBuilder sqlAditivosNaoAssinados = new StringBuilder();
        sqlAditivosNaoAssinados.append("SELECT p.datanasc as datanascimento, (p.nomemae is not null and p.nomemae != '') as possuiMae, (p.nomepai is not null and p.nomepai != '') as possuiPai, c.codigo as contrato, false as assinado, c.situacao, true AS ehAditivo, ad.codigo AS aditivo\n");
        sqlAditivosNaoAssinados.append("from contrato c\n");
        sqlAditivosNaoAssinados.append("JOIN pessoa p ON p.codigo = c.pessoa\n");
        sqlAditivosNaoAssinados.append("JOIN cliente cli ON p.codigo = cli.pessoa\n");
        sqlAditivosNaoAssinados.append("JOIN plano pl ON (pl.codigo = c.plano)\n");
        sqlAditivosNaoAssinados.append("JOIN planotextopadrao p2 ON (p2.codigo = pl.planotextopadrao)\n");
        sqlAditivosNaoAssinados.append("JOIN aditivo ad ON (ad.plano = p2.codigo)\n");
        sqlAditivosNaoAssinados.append("LEFT JOIN contratoassinaturadigital cad ON cad.contratoaditivo = c.codigo and cad.aditivo = ad.codigo\n");
        sqlAditivosNaoAssinados.append("WHERE c.situacao = 'AT' AND pl.vigenciaate >= current_date AND ad.dataprocessamento >= c.datalancamento\n");
        sqlAditivosNaoAssinados.append("AND (cad.ehaditivo is null or cad.ehaditivo is true) AND (cad.assinatura is null OR cad.assinatura = '')\n");
        sqlAditivosNaoAssinados.append("AND cli.empresa = ").append(empresa).append("\n");
        sqlAditivosNaoAssinados.append("AND cli.codigomatricula::varchar = '").append(matricula).append("'");

        sqlRoot.append("\nUNION ALL\n");
        sqlRoot.append(sqlAditivoAssinados);
        sqlRoot.append("\nUNION ALL\n");
        sqlRoot.append(sqlAditivosNaoAssinados);

        return sqlRoot;
    }

    public JSONArray consultarContratosAssinaturaDigitalByContrato(HttpServletRequest request, String chave, Integer contrato, Integer empresa) {
        ContratoAssinaturaDigitalServiceImpl cadServiceContrato;
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT c.codigo as contrato, (cad.assinatura is not null) as assinado, c.situacao from contrato c ");
            sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa ");
            sql.append(" INNER JOIN cliente cli ON p.codigo = cli.pessoa ");
            sql.append(" LEFT JOIN contratoassinaturadigital cad ON cad.contrato = c.codigo ");
            sql.append(" WHERE cli.empresa = ").append(empresa);
            sql.append(" AND c.codigo = '").append(contrato).append("' ");

            JSONArray jsonArray = new JSONArray();
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("contrato", rs.getInt("contrato"));
                    json.put("assinado", rs.getBoolean("assinado"));
                    json.put("situacaocontrato", rs.getString("situacao"));
                    jsonArray.put(json);
                }
            }
            cadServiceContrato = new ContratoAssinaturaDigitalServiceImpl(con);
            cadServiceContrato.setServletContext(request.getSession().getServletContext());
            ContratoTextoPadrao contratoTextoPadraoDao = new ContratoTextoPadrao(con);
            JSONArray jsonArrayReturn = new JSONArray();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);
                ContratoVO c = cadServiceContrato.contratoPreparado(chave, obj.getInt("contrato"));
                String texto = contratoTextoPadraoDao.consultarHtmlContrato(c.getCodigo(), false);
                texto = texto.replaceAll("__4ssin4tur4digit4l__", "<b>A assinatura digital ser colocada aqui.</b>");
                texto = texto.replaceAll("__4ssin4tur4digit4l2__", "<b>A assinatura digital resp. financeiro será colocada aqui.</b>");
                texto = cadServiceContrato.arranjarImagens(chave, texto, c, true);
                obj.put("contratotextopadrao", texto);
                jsonArrayReturn.put(obj);
            }
            return jsonArrayReturn;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new JSONArray();
        } finally {
            cadServiceContrato = null;
        }
    }

    public String incluirAssinaturaContrato(String chave, Integer contrato, String assinatura, Integer aditivo, String documentos, String ip, String tipoAutenticacao, String dadosAutenticacao) {
        ContratoAssinaturaDigital cadDao;
        ContratoAssinaturaDigitalServiceImpl cadServiceContrato;
        Usuario usuarioDao;
        Contrato contratoDao;

        try {
            cadDao = new ContratoAssinaturaDigital(con);
            cadServiceContrato = new ContratoAssinaturaDigitalServiceImpl(con);

            final ContratoAssinaturaDigitalVO assinaturaAnterior = cadDao.consultarPorContratoEAditivo(contrato, aditivo);
            final ContratoAssinaturaDigitalVO cad = new ContratoAssinaturaDigitalVO();

            if (aditivo == null) {
                cad.setContrato(new ContratoVO());
                cad.getContrato().setCodigo(contrato);
            } else {
                cad.setContratoAditivo(new ContratoVO());
                cad.getContratoAditivo().setCodigo(contrato);

                cad.setEhAditivo(true);
                cad.setAditivo(new AditivoVO());
                cad.getAditivo().setCodigo(aditivo);
            }

            usuarioDao = new Usuario(con);
            UsuarioVO usuarioVO = usuarioDao.consultarPorCodigo(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            cad.setUsuarioResponsavel(new UsuarioVO());
            cad.getUsuarioResponsavel().setCodigo(usuarioVO.getCodigo());

            cad.setContratoTextoPadrao(new ContratoTextoPadraoVO());

            final String sql = aditivo == null
                    ? "select codigo from contratotextopadrao where contrato = " + contrato
                    : "select codigo from contratotextopadrao where contrato = " + contrato + " AND aditivo = " + aditivo;

            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
                if (rs.next()) {
                    cad.getContratoTextoPadrao().setCodigo(rs.getInt("codigo"));
                }
            }

            cad.setIp(ip);
            cad.setTipoAutenticacao(tipoAutenticacao);
            cad.setDadosAutenticacao(dadosAutenticacao);

            cad.setAssinatura(cadServiceContrato.uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO, assinaturaAnterior.getAssinatura(), assinatura, true));
            if (!UteisValidacao.emptyString(documentos)) {
                cad.setDocumentos(cadServiceContrato.uploadImagem(contrato, MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO, assinaturaAnterior.getDocumentos(), documentos, true));
            }

            if (assinaturaAnterior == null || UteisValidacao.emptyNumber(assinaturaAnterior.getCodigo())) {
                cadDao.incluir(cad);
            } else {
                cad.setCodigo(assinaturaAnterior.getCodigo());
                cadDao.alterarSomenteAssinaturaContrato(cad);
            }

            contratoDao = new Contrato(con);
            ContratoVO contratoVO = contratoDao.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_MINIMOS);

            //Atualizar impressão do contrato
            ContratoTextoPadrao contTexPadraoDao = new ContratoTextoPadrao(con);
            contTexPadraoDao.gravarHtmlContrato(contrato, usuarioVO, aditivo);

            cadServiceContrato.registrarLogApp(cad, usuarioVO, contratoVO.getPessoa().getCodigo());
            cadServiceContrato.notificarAssinaturaParaAcesso(chave, contratoVO.getPessoa().getCodigo());
            return "sucesso";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "Falha ao gravar assinatura do contrato: " + contrato;
        } finally {
            cadDao = null;
            cadServiceContrato = null;
            usuarioDao = null;
            contratoDao = null;
        }
    }

    public JSONArray alunosAvisoMedico(Integer empresa, boolean contar, String colaborador) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select ");
        if (contar) {
            sql.append(" cb.codigo as colaborador, count(cm.codigo)as qtdAlunosMsg ");
        } else {
            sql.append(" scs.matricula, scs.nomecliente as nome, scs.situacao, scs.datavigenciaate as terminoContrato, cm.mensagem as avisoMedico ");
        }
        sql.append(" from clientemensagem cm ");
        sql.append(" inner join vinculo v on v.cliente = cm.cliente ");
        sql.append(" inner join situacaoclientesinteticodw scs on scs.codigocliente = cm.cliente ");
        sql.append(" inner join colaborador cb on cb.codigo  = v.colaborador ");
        sql.append(" where cm.tipomensagem = 'AM' ");
        sql.append(" and scs.empresacliente = ").append(empresa);
        sql.append(" and v.tipovinculo = 'TW' ");
        if (isNotBlank(colaborador)) {
            sql.append(" and cb.codigo in (").append(colaborador.replace(";", ",")).append(") ");
        }
        sql.append("group by cb.codigo");
        if (!contar) {
            sql.append(", scs.matricula, scs.nomecliente, scs.situacao, scs.datavigenciaate, cm.mensagem ");
        }
        JSONArray array = new JSONArray();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                JSONObject obj = new JSONObject();
                if (contar) {
                    obj.put("colaborador", rs.getInt("colaborador"));
                    obj.put("qtdAlunosMsg", rs.getInt("qtdAlunosMsg"));
                } else {
                    obj.put("nome", rs.getString("nome"));
                    obj.put("matricula", rs.getString("matricula"));
                    obj.put("nome", rs.getString("nome"));
                    obj.put("situacao", rs.getString("situacao"));
                    obj.put("terminoContrato", rs.getString("terminoContrato"));
                    obj.put("avisoMedico", Jsoup.clean(rs.getString("avisoMedico").replace("<title>Untitled document</title>", ""), Whitelist.none()));
                }
                array.put(obj);
            }
        }
        return array;
    }

    public String clienteMensagem(Integer codCliente, String tipoMensagem) throws Exception {
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "SELECT ClienteMensagem.mensagem FROM ClienteMensagem, Cliente WHERE" +
                        " ClienteMensagem.cliente = Cliente.codigo  and" +
                        " Cliente.codigo = " + codCliente + " and" +
                        " ClienteMensagem.tipomensagem = '" + tipoMensagem + "' ORDER BY Cliente.codigo "
                , con)) {
            while (rs.next()) {
                return Jsoup.clean(rs.getString("mensagem").replace("<title>Untitled document</title>", ""), Whitelist.none());
            }
        }
        return "";
    }

    public JSONArray consultarAutorizados(String parametro) throws Exception {
        JSONArray autorizados = new JSONArray();
        if (UteisValidacao.emptyString(parametro)) {
            return autorizados;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT nomepessoa, codigomatricula, cpf, i.nomeempresa, i.chave ");
        sb.append("FROM autorizacaoacessogrupoempresarial a ");
        sb.append("INNER JOIN integracaoacessogrupoempresarial i ON i.codigo = a.integracaoacessogrupoempresarial ");
        if (parametro.matches("\\d+")) {
            sb.append(" where codigomatricula = ").append(parametro);
        } else {
            sb.append(" where nomepessoa ILIKE '").append(parametro.toUpperCase()).append("%' ");
        }
        sb.append(" ORDER BY nomepessoa ");
        sb.append(" LIMIT 10");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sb.toString(), con)) {
            while (rs.next()) {
                JSONObject json = new JSONObject();
                json.put("codigomatricula", rs.getInt("codigomatricula"));
                json.put("nomepessoa", rs.getString("nomepessoa"));
                json.put("nomeempresa", rs.getString("nomeempresa"));
                json.put("chave", rs.getString("chave"));
                autorizados.put(json);
            }
        }
        return autorizados;
    }
}
