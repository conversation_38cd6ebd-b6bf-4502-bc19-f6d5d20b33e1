package servicos.vendasonline;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaColaborador;
import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaClienteInterfaceFacade;
import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaColaboradorInterfaceFacade;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.turmas.servico.dto.HorarioTurmaDTO;
import controle.contrato.ContratoControle;
import controle.contrato.ParcelasEditarNegociacaoNovo;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoComposicaoVO;
import negocio.comuns.contrato.ContratoDuracaoCreditoTreinoVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeProdutoSugeridoVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoPlanoProdutoSugeridoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.contrato.Prorata;
import negocio.comuns.crm.*;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ItemTaxaPersonalVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaCupomDescontoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.plano.*;
import negocio.comuns.plano.enumerador.ReferenciaValorModalidadeEnum;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.Atestado;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoComposicao;
import negocio.facade.jdbc.contrato.ContratoCondicaoPagamento;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import negocio.facade.jdbc.contrato.ContratoDuracaoCreditoTreino;
import negocio.facade.jdbc.contrato.ContratoHorario;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.contrato.ContratoModalidadeCredito;
import negocio.facade.jdbc.contrato.ContratoModalidadeHorarioTurma;
import negocio.facade.jdbc.contrato.ContratoModalidadeProdutoSugerido;
import negocio.facade.jdbc.contrato.ContratoModalidadeTurma;
import negocio.facade.jdbc.contrato.ContratoModalidadeVezesSemana;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.facade.jdbc.contrato.ContratoPlanoProdutoSugerido;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.contrato.HistoricoContrato;
import negocio.facade.jdbc.contrato.JustificativaOperacao;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.crm.AberturaMeta;
import negocio.facade.jdbc.crm.Agenda;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.crm.ConversaoLead;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.Indicado;
import negocio.facade.jdbc.crm.Passivo;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.Cheque;
import negocio.facade.jdbc.financeiro.ControleTaxaPersonal;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.ItemTaxaPersonal;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovParcelaCupomDesconto;
import negocio.facade.jdbc.financeiro.MovParcelaTentativaConvenio;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Remessa;
import negocio.facade.jdbc.financeiro.RemessaCancelamentoItem;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.TransacaoMovParcela;
import negocio.facade.jdbc.plano.*;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import negocio.interfaces.acesso.AcessoClienteInterfaceFacade;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.ClienteMensagemInterfaceFacade;
import negocio.interfaces.basico.ColaboradorInterfaceFacade;
import negocio.interfaces.basico.ConfiguracaoSistemaInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.basico.MovimentoContaCorrenteClienteInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.interfaces.basico.ProfissaoInterfaceFacade;
import negocio.interfaces.basico.QuestionarioClienteInterfaceFacade;
import negocio.interfaces.contrato.AtestadoInterfaceFacade;
import negocio.interfaces.contrato.ContratoDuracaoCreditoTreinoInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.contrato.ContratoModalidadeCreditoInterfaceFacade;
import negocio.interfaces.contrato.ContratoModalidadeTurmaInterfaceFacade;
import negocio.interfaces.contrato.ContratoOperacaoInterfaceFacade;
import negocio.interfaces.contrato.ContratoRecorrenciaInterfaceFacade;
import negocio.interfaces.contrato.ContratoTextoPadraoInterfaceFacade;
import negocio.interfaces.contrato.HistoricoContratoInterfaceFacade;
import negocio.interfaces.contrato.JustificativaOperacaoInterfaceFacade;
import negocio.interfaces.contrato.MatriculaAlunoHorarioTurmaInterfaceFacade;
import negocio.interfaces.contrato.MovProdutoInterfaceFacade;
import negocio.interfaces.contrato.PeriodoAcessoClienteInterfaceFacade;
import negocio.interfaces.crm.AberturaMetaInterfaceFacade;
import negocio.interfaces.crm.AgendaInterfaceFacade;
import negocio.interfaces.crm.ConfiguracaoSistemaCRMInterfaceFacade;
import negocio.interfaces.crm.ConversaoLeadInterfaceFacade;
import negocio.interfaces.crm.FecharMetaDetalhadoInterfaceFacade;
import negocio.interfaces.crm.FecharMetaInterfaceFacade;
import negocio.interfaces.crm.HistoricoContatoInterfaceFacade;
import negocio.interfaces.crm.PassivoInterfaceFacade;
import negocio.interfaces.financeiro.ControleTaxaPersonalInterfaceFacade;
import negocio.interfaces.financeiro.FormaPagamentoInterfaceFacade;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaTentativaConvenioInterfaceFacade;
import negocio.interfaces.financeiro.OperadoraCartaoInterfaceFacade;
import negocio.interfaces.financeiro.RemessaCancelamentoItemInterfaceFacade;
import negocio.interfaces.financeiro.RemessaInterfaceFacade;
import negocio.interfaces.financeiro.RemessaItemInterfaceFacade;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import negocio.interfaces.financeiro.TransacaoMovParcelaInterfaceFacade;
import negocio.interfaces.plano.*;
import negocio.interfaces.vendas.VendasConfigInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import negocio.oamd.CupomDescontoVO;
import org.apache.commons.collections.Predicate;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.impl.IntegracaoLeadGenericaServiceImpl;
import servicos.integracao.impl.buzzlead.IntegracaoBuzzLeadServiceImpl;
import servicos.integracao.impl.hubspot.IntegracaoHubspostLeadServiceImpl;
import servicos.integracao.impl.rd.IntegracaoRDServiceImpl;
import servicos.operacoes.LancamentoProdutoColetivoService;
import servicos.operacoes.MailingService;
import servicos.vendasonline.dto.ModalidadeSelecionadaDTO;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaDTO;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class ContratoSiteService {
    private Connection con;
    private Colaborador colaborador;
    private ContratoDuracao contratoDuracao;
    private ContratoHorario contratoHorario;
    private ContratoCondicaoPagamento contratoCondicaoPagamento;
    private ContratoComposicao contratoComposicao;
    private ContratoTextoPadrao contratoTextoPadrao;
    private ContratoModalidade contratoModalidade;
    private ContratoPlanoProdutoSugerido contratoPlanoProdutoSugerido;
    private HistoricoContrato historicoContrato;
    private PeriodoAcessoCliente periodoAcessoCliente;
    private MovProduto movProduto;
    private Plano plano;
    private PlanoEmpresa planoempresa;
    private Contrato contrato;
    private ContratoRecorrencia contratoRecorrencia;
    private ReciboPagamento reciboPagamento;
    private MovParcela movParcela;
    private MovParcelaTentativaConvenio movParcelaTentativaConvenio;
    private MovProdutoParcela movProdutoParcela;
    private MovPagamento movPagamento;
    private List<MovPagamentoVO> listaSelectItemMovPagamentoVOs;
    private Cliente cliente;
    private UsuarioMovelInterfaceFacade usuarioMovel;
    private LogInterfaceFacade log;
    private Agenda agenda;
    private FecharMeta fecharMeta;
    private MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurma;
    private HorarioTurma horarioTurma;
    private ContratoOperacao contratoOperacao;
    private FormaPagamento formaPagamento;
    private Usuario usuario;
    private JustificativaOperacao justificativaOperacao;
    private MovimentoContaCorrenteCliente movimentoContaCorrenteCliente;
    private Cheque cheque;
    private SituacaoClienteSinteticoDW situacaoClienteSinteticoDW;
    private Profissao profissao;
    private QuestionarioCliente questionarioCliente;
    private HistoricoContato historicoContato;
    private AcessoCliente acessoCliente;
    private ClienteMensagem clienteMensagem;
    private PlanoRecorrencia planoRecorrencia;
    private ContratoModalidadeTurma contratoModalidadeTurma;
    private ContratoModalidadeProdutoSugerido contratoModalidadeProdutoSugerido;
    private ContratoModalidadeVezesSemana contratoModalidadeVezesSemana;
    private ContratoModalidadeHorarioTurma contratoModalidadeHorarioTurma;
    private Pessoa pessoa;
    private Empresa empresa;
    private PlanoDuracao planoDuracao;
    private PlanoCondicaoPagamento planoCondicaoPagamento;
    private PlanoComposicao planoComposicao;
    private ConfiguracaoSistema configuracaoSistema;
    private PlanoModalidade planoModalidade;
    private PlanoHorario planoHorario;
    private PlanoProdutoSugerido planoProdutoSugerido;
    private ControleTaxaPersonal controleTaxaPersonal;
    private Remessa remessa;
    private RemessaItem remessaItem;
    private RemessaCancelamentoItem remessaCancelamentoItem;
    private Transacao transacao;
    private Produto produto;
    private TransacaoMovParcela transacaoMovParcela;
    private OperadoraCartao operadoraCartao;
    private FecharMetaDetalhadoInterfaceFacade fecharMetaDetalhado;
    private CartaoCredito cartao;
    private AberturaMeta aberturaMeta;
    private NotaFiscalConsumidorEletronica notaFiscalConsumidorEletronica;
    private AutorizacaoCobrancaCliente autorizacaoCobrancaCliente;
    private AutorizacaoCobrancaColaborador autorizacaoCobrancaColaborador;
    private Atestado atestado;
    private ContratoDuracaoCreditoTreino contratoDuracaoCreditoTreino;
    private ContratoModalidadeCredito contratoModalidadeCredito;
    private PlanoTipo planoTipo;
    private ConversaoLead conversaoLead;
    private ConfiguracaoSistemaCRM configuracaoSistemaCRM;
    private Passivo passivo;
    private Date dataInicioContrato;
    private Date dataBaseSegundaParcela;
    private ContratoPlanoProdutoSugeridoVO contratoPlanoProdutoSugeridoo;
    private Date vigenciaAteAjustadaDoUltimoContrato;
    private List listaApresentarTurmaMarcadas;
    private List listaDiasVencimento;
    private int diaVencimentoCartaoRecorrencia;
    private DescontoVO descontoAntecipado;
    private Desconto desconto;
    private ContratoModalidadeVO contratoModalidadeVO;
    private Modalidade modalidade;
    private Turma turma;
    private ContratoModalidadeProdutoSugeridoVO contratoModalidadeProdutoSugeridoo;
    private List listaProdutoApresentar;
    private Double valorMensalModalidade;
    private List listaApresentarModalidadesMarcadas;
    private MovProdutoVO movProdutoVO;
    private PlanoRecorrenciaParcela planorecorrenciaParcela;
    private PlanoDuracaoVO planoDuracaoSelecionada;
    private Double valorDescontoCondicaoPagamento;
    private Double valorParcelaContrato;
    private int quantidadeParcelasValorContrato;
    private String parcelasValorDiferente = "";
    private Double valorParcelaComProdutoContrato;
    private ZillyonWebFacade zwFacade;
    private int codigoPlanoDuracaoSelecionado;
    private PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoSelecionado;
    private ArrayList<SelectItem> listaSelectItemDuracaoCreditoTreino;
    private Integer codigoHorarioCreditoTreinoSelecionado;
    private Iterable<? extends PlanoDuracaoCreditoTreinoVO> listaPlanoDuracaoCreditoTreino;
    private PlanoDuracaoCreditoTreino planoDuracaoCreditoTreino;
    private List<SelectItem>  listaSelectItemTipoHorarioCreditoTreino;
    private Prorata prorata;
    private double valorPrimeiraParcela = 0.0;
    private Integer numeroParcelaContrato;
    private Boolean parcelaAvistaContrato;
    private int quantidadeParcelasValorDiferente = 0;
    private VendasConfig vendas;
    private boolean fluxoContratoEncerramentoDia = false;

    public ContratoSiteService() {
    }

    public ContratoSiteService(Connection con) {
        this.con = con;
    }

    public ContratoVO incluirContratoSite(String key,
                                          int plano,
                                          int cliente,
                                          int nrParcelasAdesao,
                                          int nrParcelasProduto,
                                          boolean gerarAdesao,
                                          boolean gerarAnuidade,
                                          Integer diaVencimentoCartaoRecorrencia,
                                          Date dataPrimeiraParcela,
                                          boolean simular,
                                          int empresacod,
                                          String numeroCupomDesconto,
                                          Date dataInicioContrato,
                                          Date dataLancamento,
                                          ContratoVO contratoASerRenovado,
                                          ContratoVO contratoASerRematriculado, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas, List<HorarioTurmaDTO> horariosSelecionados) throws Exception {
        return incluirContratoSite(key, plano, cliente, nrParcelasAdesao, nrParcelasProduto, 1, gerarAdesao,
                gerarAnuidade, diaVencimentoCartaoRecorrencia, dataPrimeiraParcela, simular, empresacod,
                numeroCupomDesconto, dataInicioContrato, dataLancamento, contratoASerRenovado, contratoASerRematriculado, null,null, null, modalidadesSelecionadas, null, horariosSelecionados);
    }

    public ContratoVO incluirContratoSite(String key,
                                          int plano,
                                          int cliente,
                                          int nrParcelasAdesao,
                                          int nrParcelasProduto,
                                          int nrParcelasMatricula,
                                          boolean gerarAdesao,
                                          boolean gerarAnuidade,
                                          Integer diaVencimentoCartaoRecorrencia,
                                          Date dataPrimeiraParcela,
                                          boolean simular,
                                          int empresacod,
                                          String numeroCupomDesconto,
                                          Date dataInicioContrato,
                                          Date dataLancamento,
                                          ContratoVO contratoASerRenovado,
                                          ContratoVO contratoASerRematriculado,
                                          ColaboradorVO colaboradorVO,
                                          Integer codigoEvento, VendaDTO vendaDTO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas, RetornoVendaTO retDTO, List<HorarioTurmaDTO> horariosSelecionados) throws Exception {
        try {
            Usuario usuarioDao = new Usuario(this.con);
            Cliente clienteDao = new Cliente(this.con);
            Plano planoDao = new Plano(this.con);
            Empresa empresaDao = new Empresa(this.con);
            Contrato contratoDao = new Contrato(this.con);
            ZillyonWebFacade zillyonWebFacadeDao = new ZillyonWebFacade(this.con);
            Indicado indicadoDAO = new Indicado(this.con);
            HorarioTurma horarioTurmaDao = new HorarioTurma(this.con);
            ContratoVO novoContrato = new ContratoVO();
            setFluxoContratoEncerramentoDia(false);
            try {
                UsuarioVO usuarioVO = null;
                if (vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel())) {
                    try {
                        usuarioVO = usuarioDao.consultarPorChavePrimaria(vendaDTO.getUsuarioResponsavel(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

                if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                    usuarioVO = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
                ClienteVO clienteVO;
                VendasConfigVO config = null;
                try {
                    clienteVO = clienteDao.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                    config = getVendasOnline().config(clienteVO.getEmpresa().getCodigo());
                    if (!clienteVO.getEmpresa().getPermiteContratosConcomintante()) {
                        if (contratoDao.possuiContratoVigentePessoa(clienteVO.getPessoa().getCodigo(), 0) && !config.isRenovarContratoAntigo()) {
                            throw new Exception("Cliente já possui um plano ativo!");
                        }
                    }
                } catch (Exception e) {
                    if(simular){
                        clienteVO = new ClienteVO();
                        clienteVO.setEmpresa(new EmpresaVO());
                        clienteVO.getEmpresa().setCodigo(empresacod);
                        config = getVendasOnline().config(empresacod);
                    }else{
                        throw e;
                    }
                }

                if (clienteVO.getPessoa() != null && !UteisValidacao.emptyNumber(clienteVO.getPessoa().getCodigo()) &&
                       horariosSelecionados != null && !horariosSelecionados.isEmpty()) {
                    String horarios = horariosSelecionados.stream()
                            .map(HorarioTurmaDTO::getCodigo)
                            .filter(codigo -> codigo != null && !UteisValidacao.emptyNumber(codigo))
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    if (!horariosSelecionados.isEmpty()) {
                        horarioTurmaDao.validarHorarioTurmaPossuiVagasPorCapacidadeCategorias(horarios,
                                clienteVO.getPessoa().getCodigo());
                    }
                }

                PlanoVO planoVO = planoDao.consultarPorChavePrimaria(plano, false, Uteis.NIVELMONTARDADOS_TODOS);

                if (!simular && planoVO != null && planoVO.getPlanoTipo() != null &&
                        !UteisValidacao.emptyNumber(planoVO.getPlanoTipo().getLimiteVendas())) {
                    Integer qtdContratos = contratoDao.contarContratosAtivosTipoPlano(planoVO.getCodigo(),
                            planoVO.getPlanoTipo().getCodigo());
                    if (qtdContratos >= planoVO.getPlanoTipo().getLimiteVendas()) {
                        throw new Exception("Não é possível realizar a negociação desse contrato, pois já chegou no limite de vendas pelo tipo de plano " + planoVO.getPlanoTipo().getNome());
                    }
                }

                planoVO.filtrarPlanoProdutoSugeridoAtivoPlano();

                if (!UteisValidacao.emptyNumber(planoVO.getDiasBloquearCompraMesmoPlano()) && clienteVO.getPessoa() != null &&
                        !UteisValidacao.emptyNumber(clienteVO.getPessoa().getCodigo())) {
                    List<ContratoVO> contratos = contratoDao.consultarListaTela(clienteVO.getPessoa().getCodigo(), 10);
                    if (contratos != null) {
                        for (ContratoVO contrato : contratos) {
                            Date dataLimite = Uteis.somarDias(contrato.getDataLancamento(), planoVO.getDiasBloquearCompraMesmoPlano());
                            if (Objects.equals(contrato.getPlano().getCodigo(), planoVO.getCodigo()) && Calendario.menor(Calendario.hoje(), dataLimite)) {
                                throw new Exception("Você comprou este plano no dia " +
                                        Uteis.getData(contrato.getDataLancamento()) + " e só será permitido recomprá-lo à partir de " +
                                        Uteis.getData(dataLimite));
                            }
                        }
                    }
                }

                if(dataLancamento != null){
                    novoContrato.setDataLancamento(dataLancamento);
                }
                if(contratoASerRenovado != null) {
                    if(dataInicioContrato == null && contratoASerRenovado.getVigenciaAteAjustada() != null){
                        Date dataInicial = Uteis.obterDataFutura2(contratoASerRenovado.getVigenciaAteAjustada(), 1);
                        Date hoje = Uteis.getDataComHoraZerada(Calendario.hoje());

                        dataInicioContrato = dataInicial;
                        this.dataInicioContrato = dataInicial;

                        if(dataInicial.getTime() < hoje.getTime()) {
                            dataPrimeiraParcela = hoje;
                        } else {
                            dataPrimeiraParcela = dataInicial;
                        }

                        //setar dados iniciais que podem ser alterados
                        novoContrato.setConvenioDesconto(new ConvenioDescontoVO(dataInicial));
                    }

                    if(dataPrimeiraParcela != null) {
                        novoContrato.setDataBaseSegundaParcela(Uteis.obterDataFutura2(contratoASerRenovado.getVigenciaAteAjustada(), 1));

                        if(config.isCobrarPrimeiraParcelaCompraRenovacao()) {
                            if(config.isCobrarParcelasMesSeguinteRenovacao()) {
                                novoContrato.setDataBaseSegundaParcela(Uteis.getDataComHoraZerada(Calendario.hoje()));
                            }
                        }

                    }

                    if(contratoASerRenovado.getDataMatricula() != null) {
                        novoContrato.setDataMatricula(contratoASerRenovado.getDataMatricula());
                    }

                    novoContrato.setSituacaoContrato(SituacaoContratoEnum.RENOVACAO.getCodigo());
                    novoContrato.setContratoBaseadoRenovacao(contratoASerRenovado.getCodigo());
                    novoContrato.setContratoOrigemRenovacao(contratoASerRenovado);
                } else if (contratoASerRematriculado != null) {
                    if(contratoASerRematriculado.getDataMatricula() != null) {
                        novoContrato.setDataMatricula(contratoASerRematriculado.getDataMatricula());
                    }
                    novoContrato.setSituacaoContrato(SituacaoContratoEnum.REMATRICULA.getCodigo());
                    novoContrato.setContratoBaseadoRematricula(contratoASerRematriculado.getCodigo());
                    setVigenciaAteAjustadaDoUltimoContrato(contratoASerRematriculado.getVigenciaAteAjustada());
                }

                try {
                    if (vendaDTO != null && dataInicioContrato == null && vendaDTO.getDataInicioContrato() == null && novoContrato.getDataBaseSegundaParcela() == null) {
                        Date dataInicioFuturaConfiguradaNoPlano = planoDao.consultarDataInicioFutura(plano);
                        if (dataInicioFuturaConfiguradaNoPlano != null) {
                            Date hoje = Uteis.getDate(Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd"), "yyyy-MM-dd");
                            Date dtInicioFuturaComparar = Uteis.getDate(Calendario.getDataAplicandoFormatacao(dataInicioFuturaConfiguradaNoPlano, "yyyy-MM-dd"), "yyyy-MM-dd");
                            if (Calendario.menor(hoje, dtInicioFuturaComparar)) { //validar se a data configurada no plano Ã© maior que hoje (futura)
                                novoContrato.setDataBaseSegundaParcela(dataInicioFuturaConfiguradaNoPlano);
                            }
                        }
                    }
                } catch (Exception ex) {
                }

                novoContrato.setChave(key);
                novoContrato.setPlano(planoVO);
                novoContrato.setEmpresa(clienteVO.getEmpresa());
                novoContrato.setPessoa(clienteVO.getPessoa());
                novoContrato.setOrigemSistema(OrigemSistemaEnum.VENDAS_ONLINE_2);
                if(!UteisValidacao.emptyString(numeroCupomDesconto)) {
                    validarCupomDesconto(key, novoContrato, numeroCupomDesconto);
                }

                int parcelasAdesao = (nrParcelasAdesao > 0) ? nrParcelasAdesao : 1;
                int parcelasProduto = (nrParcelasProduto > 0) ? nrParcelasProduto : 1;

                novoContrato.setCobrarMatriculaSeparada(nrParcelasMatricula > 1);
                novoContrato.setNrVezesParcelarMatricula(nrParcelasMatricula);
                novoContrato.setNrParcelasAdesao(parcelasAdesao);
                novoContrato.setNrVezesParcelarProduto(parcelasProduto);

                novoContrato.setGerarAdesao(gerarAdesao);
                novoContrato.setGerarAnuidade(gerarAnuidade);
                novoContrato.setDataPrimeiraParcela(dataPrimeiraParcela);
                novoContrato.setSituacao("AT");

                if(novoContrato.getDataBaseSegundaParcela() == null) {
                    novoContrato.setDiaVencimentoCartaoRecorrencia(diaVencimentoCartaoRecorrencia == 0 ?
                            Calendario.getInstance(dataPrimeiraParcela != null ? dataPrimeiraParcela : Calendario.hoje()).get(Calendar.DAY_OF_MONTH) : diaVencimentoCartaoRecorrencia);
                } else {
                    novoContrato.setDiaVencimentoCartaoRecorrencia(diaVencimentoCartaoRecorrencia == 0 ?
                            Calendario.getInstance(novoContrato.getDataBaseSegundaParcela()).get(Calendar.DAY_OF_MONTH) : diaVencimentoCartaoRecorrencia);
                }

                if(simular) {
                    this.dataBaseSegundaParcela = novoContrato.getDataBaseSegundaParcela();
                } else {
                    this.dataBaseSegundaParcela = null;
                }
                selecionarPlano(usuarioVO, novoContrato, dataInicioContrato, modalidadesSelecionadas, (vendaDTO != null ? vendaDTO.getHorariosSelecionados() : null));
                if (vendaDTO != null
                        && !UteisValidacao.emptyString(vendaDTO.getTipoParcelamentoCredito())
                        && "RECORRENTE".equalsIgnoreCase(vendaDTO.getTipoParcelamentoCredito())
                        && novoContrato.getPlano().getParcelamentoOperadora()) {
                    novoContrato.getPlano().setParcelamentoOperadora(false);
                }

                if(!novoContrato.getPlano().getPermitirTurmasVendasOnline()) {
                    for (ContratoModalidadeVO contratoModalidadeVO : novoContrato.getContratoModalidadeVOs()) {
                        contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);
                    }
                }

                for (Object pcpVO : novoContrato.getPlanoDuracao().getPlanoCondicaoPagamentoVOs()) {
                    PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = (PlanoCondicaoPagamentoVO) pcpVO;
                    planoCondicaoPagamentoVO.getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
                }

//                obterDataPagamentoAnuidade(novoContrato);

                gerarAnuidadeOuAdesao(gerarAdesao, gerarAnuidade, novoContrato);
                if(colaboradorVO != null){
                    novoContrato.setUsuarioVO(colaboradorVO.getUsuarioVO());
                    novoContrato.setConsultor(colaboradorVO);
                }else{
                    try {
                        adicionarConsultorSite(usuarioDao, empresaDao, clienteVO, novoContrato);
                    } catch (Exception ex) {
                        //em caso de erro, setar usuÃ¡rio ADMINISTRADOR
                        usuarioVO = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
                        novoContrato.setUsuarioVO(usuarioVO);
                    }
                }

                selecionarTodosProdutosSugeridos(novoContrato);
                consultarValorMatriculaEProdutosPlanoSelecionado(novoContrato);
                verificarContratoProdutoSugerido(novoContrato);

                if (novoContrato.getPlano().getRegimeRecorrencia()) {
                    novoContrato.setRegimeRecorrencia(true);
                    novoContrato.setForcarDatasParcelasDiferentes(true);
                } else {
                    novoContrato.setRegimeRecorrencia(false);
                    novoContrato.setForcarDatasParcelasDiferentes(true);
                }

                if(dataInicioContrato != null){
                    novoContrato.obterDataFinalContrato(dataInicioContrato);
                }
                novoContrato.getEventoVO().setCodigo((vendaDTO != null && vendaDTO.getCodigoEvento() != null) ? vendaDTO.getCodigoEvento() : null);
                verificarContratoAgendado(novoContrato);
                setProrata(new Prorata());
                if(!(novoContrato.getPlano() != null && novoContrato.getPlano().getInicioMinimoContrato() != null && Calendario.maior(novoContrato.getPlano().getInicioMinimoContrato(), Calendario.hoje()))) { // Contrato Inicio Futuro
                    selecionarProrata(novoContrato, novoContrato.getDiaVencimentoCartaoRecorrencia());
                } else if (config.getCobrarPrimeiraParcelaCompra() && Calendario.maior(novoContrato.getPlano().getInicioMinimoContrato(), Calendario.hoje())) { // Contrato Inicio Futuro
                    selecionarDivisaoProdutoParcela(novoContrato);
                }
                novoContrato.setValorPrimeiraParcela(this.getValorPrimeiraParcela());

                gerarPeriodoAcesso(usuarioVO, clienteVO, novoContrato);

                if(simular){
                    return novoContrato;
                }

                if (novoContrato.getPlano().getContratosEncerramDia() != null) {
                    boolean acabaAntes = Calendario.menor(novoContrato.getVigenciaAteAjustada(), novoContrato.getPlano().getContratosEncerramDia());
                    if (!acabaAntes) {
                        setFluxoContratoEncerramentoDia(true);
                        novoContrato.povoarInformacoesLancarContratoEncerramentoContratoDia(novoContrato);
                    }
                }

                try {
                    this.con.setAutoCommit(false);

                    incluirSemCommit(novoContrato, clienteVO);

                    if (!UteisValidacao.emptyNumber(novoContrato.getCodigo()) && novoContrato.getPlano().getContratosEncerramDia() != null) {
                        boolean acabaAntes = Calendario.menor(novoContrato.getVigenciaAteAjustada(), novoContrato.getPlano().getContratosEncerramDia());
                        if (!acabaAntes) {
                            getMovParcela().alterarSomenteVencimentoUltimaParcelaContratoSemCommit(novoContrato.getCodigo(), novoContrato.getPlano().getContratosEncerramDia());
                        }
                    }

                    if (novoContrato.getPlano().getRegimeRecorrencia()) {
                        getContratoRecorrencia().incluirContratoRecorrencia(
                                novoContrato.getPlano().getPlanoRecorrencia(), novoContrato,
                                novoContrato.getDiaVencimentoCartaoRecorrencia());
                    }

                    getHistoricoContrato().incluirSemCommit(
                            montarHistoricoContrato(novoContrato), false);

                    montarLogInclusaoContrato(usuarioVO, novoContrato);

                    this.con.commit();
                } catch (Exception e) {
                    this.con.rollback();
                    throw e;
                } finally {
                    this.con.setAutoCommit(true);
                }

                try {
                    getZwFacade().atualizarSintetico(clienteVO,
                            Calendario.hoje(),
                            SituacaoClienteSinteticoEnum.GRUPO_TODOS,
                            true);
                } catch (SinteticoException se) {
                    Uteis.logar(null, "Problema ao processar sint?tico do cliente: " + clienteVO.getCodigo() + " - Chave: " + key);
                }


                //Identificar se aluno veio por Indica??o, utilizando CPF cadastrado na Indica??o do CRM e atualiza referencia para utilizar no pr?ximo m?todo de PontuacaoCliente para Indica??o
                List indicado = indicadoDAO.consultarIndicadoPorCpf(vendaDTO.getCpf(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyList(indicado)) {
                    IndicadoVO indicadoVO = (IndicadoVO) indicado.get(0);
                    if (UteisValidacao.emptyNumber(indicadoVO.getClienteVO().getCodigo())) {
                        indicadoDAO.executarAlteracaoPorCadastroCliente(indicadoVO.getCodigo(), novoContrato.getCliente().getCodigo());
                    }
                }

                //Gravar pontos do Clube de Vantagens
                try {
                    zillyonWebFacadeDao.gravarPontuacaoCliente(novoContrato);
                } catch (Exception ex) {
                    Uteis.logarDebug("###Erro ao incluir pontos do clube de vantagens: " + ex.getMessage());
                    ex.printStackTrace();
                }

                return novoContrato;
            } catch (Exception ex) {
                if (retDTO != null) {
                    retDTO.setContrato(novoContrato.getCodigo());
                }
                ex.printStackTrace();
                Uteis.logar(ex, this.getClass());
                throw ex;
            } finally {
                usuarioDao = null;
                clienteDao = null;
                planoDao = null;
                empresaDao = null;
                zillyonWebFacadeDao = null;
                indicadoDAO = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, this.getClass());
            throw e;
        }
    }

    private void obterDataPagamentoAnuidade(ContratoVO novoContrato) {
        List<MovProdutoVO> movProdutoVOs = novoContrato.getMovProdutoVOs();

        if(movProdutoVOs != null){
            for(MovProdutoVO mop : new ArrayList<MovProdutoVO>(movProdutoVOs)){
                if(mop.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())){
                    try {
                        int ano = Uteis.getAnoData(novoContrato.getVigenciaDe());
                        Date cobranca = Uteis.getDate(novoContrato.getPlano().getPlanoRecorrencia().getDiaAnuidade(),
                                novoContrato.getPlano().getPlanoRecorrencia().getMesAnuidade() - 1,
                                ano);
                        if(Calendario.menor(cobranca, novoContrato.getVigenciaDe())){
                            cobranca = Uteis.somarCampoData(cobranca, Calendar.YEAR, 1);
                        }
                        novoContrato.setDataAnuidade(cobranca);
                    }catch (Exception e){
                    }
                }
            }
        }
    }

    private void montarLogInclusaoContrato(UsuarioVO usuarioVO, ContratoVO novoContrato) throws Exception {
        //LOG - INICIO
        try {
            LogVO logVO = new LogVO();
            novoContrato.montarLogDadosContrato(logVO, null, null);
            if(!UteisValidacao.emptyString(usuarioVO.getNome()))
                logVO.setResponsavelAlteracao(usuarioVO.getNome());
            logVO.setOperacao("INCLUSÃO DE CONTRATO");
            registrarLogObjetoVO(logVO, novoContrato.getPessoa().getCodigo());
        } catch (Exception e) {
            if (JSFUtilities.isJSFContext()) {
                registrarLogErroObjetoVO("CONTRATO", novoContrato.getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO CONTRATO", usuarioVO.getNome(), "");
                Uteis.logar(e, ContratoControle.class);
            }
        }
        //LOG - FIM
    }

    private void registrarLogObjetoVO(LogVO logVO, int codPessoa) throws Exception {
        if (logVO != null) {
            logVO.setPessoa(codPessoa);
            getLog().incluirSemCommit(logVO);
        }
    }

    private void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg, String responsavel, String userOamd) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            getLog().incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void selecionarProrata(ContratoVO contratoVO, int diaVencimentoSite) {
        try {

            try {
                prorata.verificarConfiguracoesSistema(contratoVO.getEmpresa(), con);
            } catch (Exception e) {
                return;
            }

            String diaVencimento = diaVencimento = String.valueOf(diaVencimentoSite);
            if (diaVencimento.trim().isEmpty() ||  Integer.parseInt(diaVencimento) == Uteis.getDiaMesData(contratoVO.getVigenciaDe())) {
                prorata.setDiaReferenciaProrata(0);
            } else {
                prorata.setDiaReferenciaProrata(Integer.valueOf(diaVencimento));
            }
            contratoVO = prorata.processarContrato(contratoVO);
            if(contratoVO.getDiaPrimeiraParcela() != null) {
                diaVencimento = contratoVO.getDiaPrimeiraParcela();
            }
            selecionarDataPrimeiraParcela(diaVencimento, contratoVO);
            selecionarDivisaoProdutoParcela(contratoVO);
        } catch (Exception e) {
            e.getStackTrace();
        }
    }

    private void mostraValoresDeComoFicouCondicaoPagamento(ContratoVO contratoVO) {
        if (contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() == 1) {
            setParcelaAvistaContrato(true);
            setNumeroParcelaContrato(0);
        } else {
            setParcelaAvistaContrato(false);
            setNumeroParcelaContrato(contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() - 1);
        }
    }

    private static HistoricoContratoVO montarHistoricoContrato(ContratoVO contratoVO){
        HistoricoContratoVO historicoContrato = new HistoricoContratoVO();
        historicoContrato.setContrato(contratoVO.getCodigo());
        historicoContrato.setDescricao("MATRICULADO");
        historicoContrato.setResponsavelRegistro(contratoVO.getResponsavelContrato());
        historicoContrato.setDataFinalSituacao(contratoVO.getVigenciaAteAjustada());
        historicoContrato.setDataRegistro(contratoVO.getDataLancamento());
        historicoContrato.setDataInicioSituacao(contratoVO.getVigenciaDe());
        historicoContrato.setTipoHistorico("MA");
        if (contratoVO.isContratoRenovacao()) {
            historicoContrato.setDescricao("RENOVADO");
            historicoContrato.setTipoHistorico("RN");
        } else if (contratoVO.isContratoRematricula()) {
            historicoContrato.setDescricao("REMATRICULADO");
            historicoContrato.setTipoHistorico("RE");
        }
        return historicoContrato;
    }

    private void selecionarDivisaoProdutoParcela(ContratoVO contratoVO) throws Exception {
        setQuantidadeParcelasValorDiferente(0);
        double valorPacelas;
        int numeroMeses;
        setValorPrimeiraParcela(0);
        if (contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getEntrada()) {
            valorPacelas = contratoVO.obterValorParcelas(contratoVO, true);
            setValorParcelaComProdutoContrato(contratoVO.obterValorPrimeiraParcelas(contratoVO, valorPacelas));
            setValorParcelaContrato(valorPacelas);
            numeroMeses = contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();

            Double valorParcelasContrato;
            Double valorContrato;

            Double valorParcelaComProduto = getValorParcelaComProdutoContrato();
            Double valorProduto = 0.0;
            if (contratoVO.getGerarParcelaParaProdutos()) {
                valorProduto = contratoVO.getSomaAdesao();
            }
            if (contratoVO.getPlano().getRegimeRecorrencia() && contratoVO.getGerarParcelaAnuidadeSeparada()) {
                valorProduto += contratoVO.getSomaAnuidade();
            }
            if (contratoVO.isCobrarMatriculaSeparada()){
                valorProduto = valorProduto + contratoVO.getValorMatricula();
            }
            if (contratoVO.isCobrarProdutoSeparado()){
                valorProduto = valorProduto + contratoVO.getTotalProdutosCobrarSeparado();
            }
            if (numeroMeses > 0) {

                // Parcelas com valor diferente das outras, para planos recorrentes
                // DefiniÃ§Ãµes da parcela no cadastro de plano, aba recorrÃªncia, campo "Cobrar anuidade junto com a parcela"
                if (contratoVO.isDeveGerarParcelasComValorDiferente()) {

                    PlanoRecorrenciaVO planoRecorrenciaVO = getPlanoRecorrencia().consultarTotalParcelasValorDiferente(contratoVO.getPlano().getPlanoRecorrencia().getCodigo());
                    valorContrato = planoRecorrenciaVO.getValorTotalParcelasValorDirefente() + ((numeroMeses - contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size()) * getValorParcelaContrato());
                    setQuantidadeParcelasValorDiferente(planoRecorrenciaVO.getQuantidadeParcelasValorDiferente());
                    //int quantidadeParcelasIguais =  /*numeroMeses - */planoRecorrenciaVO.getQuantidadeParcelasValorDiferente();// + 1;
                    //valorContrato = planoRecorrenciaVO.getValorTotalParcelasValorDirefente() + (quantidadeParcelasIguais * getValorParcelaContrato());
                    montarParcelasDiferentes(numeroMeses, getValorParcelaContrato(), contratoVO);
                    contratoVO.setValorFinal(Uteis.arredondarForcando2CasasDecimais(valorContrato + contratoVO.getSomaProduto()));
                    // montarListaParcelasComParcelasDiferentes();

                } else {
                    contratoVO.setValorFinal(Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorContrato() + contratoVO.getSomaProduto() + contratoVO.getValorProRata()));
                }

                calculoPrimeiraParcela(contratoVO);
            }
        } else {
            valorPacelas = contratoVO.obterValorParcelas(contratoVO, false);
            numeroMeses = contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();

            setValorParcelaContrato(valorPacelas);
            if (contratoVO.getDividirProdutosNasParcelas()) {
                setValorParcelaComProdutoContrato(valorPacelas);
                if (numeroMeses > 0) {
                    contratoVO.setValorFinal(contratoVO.getValorFinal());
                }
            } else {
                double valorDiferencaPrimeiraParcela = 0.0;
                if (contratoVO.getPlano().getPlanoRecorrencia() != null && !contratoVO.getPlano().getPlanoRecorrencia().getParcelas().isEmpty()) {
                    valorDiferencaPrimeiraParcela = (valorPacelas - contratoVO.getPlano().getPlanoRecorrencia().getParcelas().get(0).getValor());
                }
                setValorParcelaComProdutoContrato(valorPacelas + contratoVO.getSomaProduto() + contratoVO.getValorProRata() - valorDiferencaPrimeiraParcela);
                double valorParcComProd = getValorParcelaComProdutoContrato();
                if (contratoVO.isCobrarMatriculaSeparada()){
                    setValorParcelaComProdutoContrato(getValorParcelaComProdutoContrato() - contratoVO.getValorMatricula());
                }
                if (contratoVO.isCobrarProdutoSeparado()){
                    setValorParcelaComProdutoContrato(getValorParcelaComProdutoContrato() - contratoVO.getTotalProdutosCobrarSeparado());
                }
                if (numeroMeses > 0) {
                    contratoVO.setValorFinal(Uteis.arredondarForcando2CasasDecimais((getValorParcelaContrato() * (numeroMeses - 1)) + valorParcComProd));
                }
            }
            calculoPrimeiraParcela(contratoVO);
        }
        mostraValoresDeComoFicouCondicaoPagamento(contratoVO);

    }

    private void calculoPrimeiraParcela(ContratoVO contratoVO) throws Exception {
        double valorProdutos = contratoVO.getSomaProduto();
        double percEntradaCondPagamento = contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getPercentualValorEntrada();

        /* Calculo valor mensal do contrato */
        double valorMensal = 0;
        if (contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas().equals(1)) {
            valorMensal = contratoVO.getValorBaseCalculo();
        }else {
            if (contratoVO.getPlano().getPlanoRecorrencia().getCodigo().equals(0)) {
                if (contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() > 0) {
                    valorMensal = contratoVO.getValorContrato() / contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();
                }else {
                    valorMensal = contratoVO.getValorBaseCalculo();
                }

            }else {
                valorMensal = getValorParcelaContrato();
            }
        }

        boolean primeiraParcelaFixa = false;
        boolean gerarParcelaAnuidadeSeparada = contratoVO.getGerarParcelaAnuidadeSeparada();
        if (contratoVO.isDeveGerarParcelasComValorDiferente()) {
            for (PlanoRecorrenciaParcelaVO prp : contratoVO.getPlano().getPlanoRecorrencia().getParcelas()) {
                if (prp.getNumero() == 1) {
                    valorMensal = prp.getValor();
                    primeiraParcelaFixa = true;
                    contratoVO.setGerarParcelaParaProdutos(true);
                    contratoVO.setCobrarProdutoSeparado(true);
                    /* Se primeira parcela tem valor configurado, a anuidade Ã© separada */
                    gerarParcelaAnuidadeSeparada = true;
                    /* Se cobra anuidade proporcional, essa deve ser cobrada na primeira parcela, caso nÃ£o cobra valor cheio na data configurada no plano */
                    if (!contratoVO.getPlano().getPlanoRecorrencia().getNaoCobrarAnuidadeProporcional()) {
                        contratoVO.setDataAnuidade(Calendario.hoje());
                    }
                    break;
                }
            }
        }
        /* Calculo da primeira parcela quando tem percentual de entrada na condiÃ§Ã£o de pagamento */
        double valorParcelas = 0;
        try {
            valorParcelas = contratoVO.obterValorParcelas(contratoVO, true, 0);
        } catch (Exception e) {
            throw new Exception("Erro ao calcular primeira parcela, verifique no suporte!");
        }
        if (percEntradaCondPagamento > 0) {
            valorMensal = (contratoVO.getValorContrato() - (valorParcelas * (contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas() - 1)));
        }

        valorMensal = valorMensal + contratoVO.getValorProRata();
        if (contratoVO.getPlano().getRecorrencia()) {
            setValorPrimeiraParcela(valorProdutos + valorMensal);
        } else {
            setValorPrimeiraParcela(valorMensal);
        }
        if (primeiraParcelaFixa) {
            setValorPrimeiraParcela(valorMensal);
        } //Se Plano Recorrencia, mantém regra antiga se Plano Turma, não faz nada.
        else if (contratoVO.getPlano().getRecorrencia()) {
            if (contratoVO.isCobrarMatriculaSeparada()) {
                setValorPrimeiraParcela(getValorPrimeiraParcela() - contratoVO.getValorMatricula());
            }
            if (contratoVO.getGerarParcelaParaProdutos()) { // Cobrar adesao separado?
                setValorPrimeiraParcela(getValorPrimeiraParcela() - contratoVO.getSomaAdesao());
            }
            if (contratoVO.isCobrarProdutoSeparado()) {
                setValorPrimeiraParcela(getValorPrimeiraParcela() - contratoVO.getValorParcelasProduto());
            }
            if (gerarParcelaAnuidadeSeparada) {
                double valorAnuidade = contratoVO.getPlano().getPlanoRecorrencia().getValorAnuidade();
                PlanoEmpresaVO planoEmpresaVO = contratoVO.getPlano().obterPlanoEmpresa(contratoVO.getEmpresa().getCodigo());
                if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getValorAnuidade())) {
                    valorAnuidade =planoEmpresaVO.getValorAnuidade();
                }

                setValorPrimeiraParcela(getValorPrimeiraParcela() - valorAnuidade);
            }
            if (contratoVO.getDividirProdutosNasParcelas()) {
                if (valorMensal > contratoVO.getPlano().getPlanoRecorrencia().getValorMensal()) {
                    double valorDivididoNasParcelas =  (valorMensal - contratoVO.getValorProRata() - contratoVO.getPlano().getPlanoRecorrencia().getValorMensal()) * contratoVO.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();
                    setValorPrimeiraParcela(getValorPrimeiraParcela() - valorDivididoNasParcelas);
                }
            }
        }

    }

    private void selecionarDataPrimeiraParcela(String diaV, ContratoVO contratoVO) throws Exception {
        if (getConfiguracaoSistema().isDefinirDataInicioPlanosRecorrencia()&&
                contratoVO.getPlano().getInicioMinimoContrato() != null &&
                Calendario.maiorOuIgual(contratoVO.getPlano().getInicioMinimoContrato(), Calendario.hoje())) {
            Calendar aux = Calendario.getInstance();
            aux.setTime(contratoVO.getPlano().getInicioMinimoContrato());
            aux.set(Calendar.DAY_OF_MONTH, getDiaVencimentoCartaoRecorrencia());
            contratoVO.setDataPrimeiraParcela(aux.getTime());
            return;
        }
        if (diaV.trim().isEmpty()) {
            contratoVO.setDataPrimeiraParcela(Calendario.hoje());
            return;
        }
        int diaVencimento = Integer.valueOf(diaV.trim());
        Calendar aux = Calendario.getInstance();
        aux.set(Calendar.DAY_OF_MONTH, diaVencimento);

        boolean contratoRecorrenteCondicaoPagamentoFuturo = false;
        try {
            if(contratoVO.getPlanoDuracao() != null
                    && !UteisValidacao.emptyList(contratoVO.getPlanoDuracao().getPlanoCondicaoPagamentoVOs())
                    && !UteisValidacao.emptyNumber(contratoVO.getPlanoDuracao().getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getIntervaloEntreParcela())
                    && contratoVO.getPlanoDuracao().getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getEntrada() != null
                    && !contratoVO.getPlanoDuracao().getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getEntrada() ) {
                int intervaloCobracaCondicaoPagamento = contratoVO.getPlanoDuracao().getPlanoCondicaoPagamentoVOs().get(0).getCondicaoPagamento().getIntervaloEntreParcela();
                aux.add(Calendar.DAY_OF_MONTH, intervaloCobracaCondicaoPagamento + 1);

                contratoRecorrenteCondicaoPagamentoFuturo = true;
            }
        } catch (Exception e){

        }

        if (contratoVO.isForcarDatasParcelasDiferentes()) {
            if (Calendario.menor(aux.getTime(), contratoVO.getVigenciaDe()) && !contratoVO.getPlano().getSite()) {
                aux.setTime(Calendario.hoje());
            }

            if(contratoVO.getPlano().getSite() && !contratoRecorrenteCondicaoPagamentoFuturo) { //Manter Dia vencimento para parcela 1 informado no vendas online
                acionarConfigVendasOnlinePrimeiraParcela(aux, diaVencimento, contratoVO);
            }

        } else {
            // enquanto o vencimento da primeira parcela for anterior a data de inicio do contrato.
            // observe q a data com hora da variavel aux sera comparada com uma data com hora zerada,
            // portanto qdo as datas forem iguais a diferenÃ§a de horario irÃ¡ considera-las diferentes.
            while (aux.getTime().before(contratoVO.getVigenciaDe())) {
                aux.add(Calendar.MONTH, 1);
            }
        }
        contratoVO.setDataPrimeiraParcela(aux.getTime());
    }

    private void acionarConfigVendasOnlinePrimeiraParcela(Calendar aux, int diaVencimento, ContratoVO contratoVO) {
        try {
            VendasConfigVO config = getVendasOnline().config(contratoVO.getEmpresa().getCodigo());
            SituacaoContratoEnum situacaoEnum = SituacaoContratoEnum.obterPorCodigo(contratoVO.getSituacaoContrato());
            if ((config.getCobrarPrimeiraParcelaCompra() && situacaoEnum != SituacaoContratoEnum.RENOVACAO)
                    || (config.isCobrarPrimeiraParcelaCompraRenovacao() && situacaoEnum == SituacaoContratoEnum.RENOVACAO)) {
                aux.setTime(Calendario.hoje());
            }else if(Calendario.maior(contratoVO.getVigenciaDe(), Calendario.hoje())){
                if(Calendario.menor(Uteis.getDate(diaVencimento+"/"+Calendario.getMesAno(contratoVO.getVigenciaDe())), contratoVO.getVigenciaDe())){
                    aux.setTime(Calendario.somarMeses(Uteis.getDate(diaVencimento+"/"+Calendario.getMesAno(contratoVO.getVigenciaDe())), 1));
                }else{
                    aux.setTime(Uteis.getDate(diaVencimento+"/"+Calendario.getMesAno(contratoVO.getVigenciaDe())));
                }
            }else if (Calendario.menor(aux.getTime(), Calendario.hoje())) {
                aux.setTime(Calendario.somarMeses(aux.getTime(), 1));
            }
        }
        catch(Exception e){
            aux.setTime(Calendario.hoje());
            Uteis.logar("Falha ao obter data para primeira parcela vendas online, "+e.getMessage());
        }
    }


    private void consultarValorMatriculaEProdutosPlanoSelecionado(ContratoVO contratoVO)throws Exception{
        contratoVO.setValorMatricula(0.0);
        contratoVO.setTotalProdutosCobrarSeparado(0);
        for (Object obj: contratoVO.getPlano().getPlanoProdutoSugeridoVOs()){
            PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO)obj;
            if (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo())){
                contratoVO.setValorMatricula(planoProdutoSugeridoVO.getValorTotalFinal());
            }else if (ProdutoVO.isProdutoPodeCobrarSeparado(planoProdutoSugeridoVO.getProduto().getTipoProduto())){
                contratoVO.setTotalProdutosCobrarSeparado(contratoVO.getTotalProdutosCobrarSeparado() + planoProdutoSugeridoVO.getValorProdutoQtdDesconto());
            }
        }
        if (contratoVO.getValorMatricula() <= 0){
            contratoVO.setCobrarMatriculaSeparada(false);
        }
        if (contratoVO.getTotalProdutosCobrarSeparado() <= 0){
            contratoVO.setCobrarProdutoSeparado(false);
        }
    }

    private void verificarContratoProdutoSugerido(ContratoVO contratoVO) {
        if (contratoVO.getPlano().getRegimeRecorrencia() && contratoVO.getPlano().getPlanoRecorrencia().getNaoCobrarAnuidadeProporcional()
                && (!contratoVO.getPlano().isRenovarAnuidadeAutomaticamente() || !contratoVO.isContratoRenovacao())) {

            for (Object obj : contratoVO.getContratoPlanoProdutoSugeridoVOs()) {
                ContratoPlanoProdutoSugeridoVO contratoPlanoProdutoSugeridoVO = (ContratoPlanoProdutoSugeridoVO) obj;
                if (contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())) {
                    int qtdAnuidadesGerar = contratoVO.getPlano().getPlanoRecorrencia().calcularQuantidadeDeAnuidades(contratoVO.getVigenciaDe(), contratoVO.getVigenciaAte(), contratoVO.getGerarParcelaAnuidadeSeparada(), contratoVO.getPlano().getPlanoRecorrencia().isAnuidadeNaParcela());
                    if (contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().getQuantidade() != qtdAnuidadesGerar) {
                        double valorAntesAlteracao = contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().getValorProdutoQtdDesconto();

                        int dif = qtdAnuidadesGerar - contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().getQuantidade();
                        contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().setQuantidade(qtdAnuidadesGerar);

                        double valorAposAlteracao = contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().getValorProdutoQtdDesconto();
                        contratoPlanoProdutoSugeridoVO.setValorFinalProduto(valorAposAlteracao / qtdAnuidadesGerar);

                        double difValor = valorAposAlteracao - valorAntesAlteracao;
                        if (dif != 0) {
                            contratoVO.setSomaProduto(Uteis.arredondarForcando2CasasDecimais(contratoVO.getSomaProduto() + (difValor)));
                            contratoVO.setSomaAnuidade(contratoVO.getSomaAnuidade() + (difValor));
                            contratoVO.setValorFinal(contratoVO.getValorFinal() + (difValor));
                        }
                    }
                }
            }

        }
    }

    private void verificarContratoAgendado(ContratoVO contratoVO) throws Exception {
        //obter configuraÃ§oes
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        //obter a data futura limite para verificar se existe agendamento para a pessoa
        //pois se existir o contrato Ã© considerado agendado
        Date dataInicial = getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(contratoVO.getDataLancamento(), true,
                configuracaoSistemaCRM.getNrDiasPosterioresAgendamento(),
                contratoVO.getEmpresa());
        Date dataFinal = getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(contratoVO.getDataLancamento(), false,
                configuracaoSistemaCRM.getNrDiasAnterioresAgendamento(),
                contratoVO.getEmpresa());
        contratoVO.setAgendamentoOrigem(getAgenda().verificaAgendamento(dataInicial, dataFinal, contratoVO.getPessoa().getCodigo()));
        if (UteisValidacao.emptyNumber(contratoVO.getAgendamentoOrigem().getCodigo())) {
            contratoVO.setContratoAgendadoEspontaneo(TipoContratoEnum.ESPONTANEO);
        } else {
            contratoVO.setContratoAgendadoEspontaneo(TipoContratoEnum.AGENDADO);
        }
    }

    private void gerarPeriodoAcesso(UsuarioVO usuarioVO, ClienteVO clienteVO, ContratoVO novoContrato) {
        PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
        periodoAcesso.setDataInicioAcesso(novoContrato.getVigenciaDe());
        periodoAcesso.setDataFinalAcesso(novoContrato.getVigenciaAteAjustada());
        periodoAcesso.setPessoa(clienteVO.getPessoa().getCodigo());
        periodoAcesso.setTipoAcesso("CA");
        periodoAcesso.setResponsavel(usuarioVO.getCodigo());
        periodoAcesso.setDataLancamento(Calendario.hoje());
        novoContrato.setPeriodoAcessoClienteVOs(Arrays.asList(periodoAcesso));
    }

    private void selecionarPlano(UsuarioVO usuario, ContratoVO contratoVO, Date dataInicioContratoManual, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas, List<HorarioTurmaDTO> horariosSelecionados) throws Exception {
        if(dataInicioContratoManual == null) {
            dataInicioContratoManual = Calendario.hoje();
        }
        if (contratoVO.getPlano().getRegimeRecorrencia()) {
            contratoVO.setRegimeRecorrencia(true);
        }
        if (!contratoVO.getPlano().getCodigo().equals(0)) {
            contratoVO.setContratoComposicaoVOs(new ArrayList<ContratoComposicaoVO>());
            contratoVO.getPlano().setPlanoDuracaoVOs(getPlanoDuracao().consultarPlanoDuracaos(contratoVO.getPlano().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS));
            contratoVO.setResponsavelContrato(usuario);
            montarContratoPlanoProdutoSugerido(contratoVO);
            adicionarPlanoProdutoSugeridoVOsObrigatorios(contratoVO);
            setListaApresentarTurmaMarcadas(new ArrayList());
            montarListaDiasVencimento(contratoVO.getPlano().getListaPreenchidaVencimentos());
            montarDadosDescontoAntecipado(contratoVO);

            if ((contratoVO.getPlano().getSite() || getConfiguracaoSistema().isDefinirDataInicioPlanosRecorrencia())&&
                    contratoVO.getPlano().getInicioMinimoContrato() != null &&
                    Calendario.maiorOuIgual(contratoVO.getPlano().getInicioMinimoContrato(), dataInicioContratoManual)) {
                contratoVO.setDataPrimeiraParcela(getVendasOnline().config(contratoVO.getEmpresa().getCodigo()).getCobrarPrimeiraParcelaCompra() ?
                        Calendario.hoje() : contratoVO.getPlano().getInicioMinimoContrato());
                dataInicioContrato = (Date)contratoVO.getPlano().getInicioMinimoContrato().clone();
                contratoVO.setVigenciaDe((Date)dataInicioContrato.clone());
            }
            /*
             * Para contratos de RECORRÃNCIA jÃ¡ devem vir marcados:
             * 1 - As modalidades do plano, pois o valor nÃ£o faz diferenÃ§a;
             * 2 - A duraÃ§Ã£o Ãºnica (a quantidade de mezes da fidelidade da recorrÃªncia);
             * 3 - O horÃ¡rio se for Ãºnico
             * 4 - A condicaÃ§Ã£o de pagamento tambÃ©m Ãºnica, CartÃ£o RecorrÃªncia
             */
            montarContratoModalidade(contratoVO, modalidadesSelecionadas, horariosSelecionados);
            contratoVO.setGerarParcelaParaProdutos(contratoVO.getPlano().getCobrarAdesaoSeparada());
            contratoVO.setCobrarProdutoSeparado(contratoVO.getPlano().getCobrarProdutoSeparado() || (!contratoVO.getPlano().getCobrarProdutoSeparado() && getVendasOnline().config(contratoVO.getEmpresa().getCodigo()).getCobrarProdutosJuntoAdesao()));
            contratoVO.setCobrarMatriculaSeparada(contratoVO.getPlano().getCobrarAdesaoSeparada());
            if (contratoVO.getPlano().getRegimeRecorrencia()) {
                contratoVO.setConvenioDesconto(new ConvenioDescontoVO(dataInicioContratoManual));
                List<ContratoModalidadeVO> listaMod = contratoVO.getContratoModalidadeVOs();
                for (ContratoModalidadeVO ctModalidade : listaMod) {
                    ctModalidade.getModalidade().setModalidadeEscolhida(true);
                    incluiProdutosModalidade(ctModalidade);
                }
            }
            selecionarAutomaticamenteHorarioDuracaoCondicao(contratoVO, modalidadesSelecionadas);

        } else {
            setListaApresentarTurmaMarcadas(new ArrayList());
            contratoVO.setPlano(new PlanoVO());
            contratoVO.setPlanoDuracao(new PlanoDuracaoVO());
            contratoVO.getDesconto().setCodigo(0);
            contratoVO.setContratoComposicaoVOs(new ArrayList<ContratoComposicaoVO>());
            contratoVO.setContratoModalidadeVOs(new ArrayList<ContratoModalidadeVO>());
            montarListaDiasVencimento(new ArrayList<String>());
        }
        selecionarCondicaoPagamento(contratoVO, modalidadesSelecionadas);
        montarValorContratoPlanoDuracao(contratoVO, modalidadesSelecionadas);
    }

    private void removerContratoPlanoProdutoSugeridoVOsDuplicado(ContratoVO contratoVO) {
        boolean jaAdicionouAnuidade = false;
        List<ContratoPlanoProdutoSugeridoVO> listaContratoPlanoProdutoSugeridosDuplicados = new ArrayList<>();

        for (Object item : contratoVO.getContratoPlanoProdutoSugeridoVOs()) {
            ContratoPlanoProdutoSugeridoVO contratoPlanoProdutoSugeridoVO = (ContratoPlanoProdutoSugeridoVO) item;
            if (validarProdutoAnuidadeJaAdicionada(jaAdicionouAnuidade, contratoPlanoProdutoSugeridoVO, listaContratoPlanoProdutoSugeridosDuplicados)) {
                continue;
            }
            if (contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().getProduto().getDescricao().toLowerCase().contains("anuidade")
                    && contratoPlanoProdutoSugeridoVO.getPlanoProdutoSugerido().getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())) {
                jaAdicionouAnuidade = true;
            }
        }

        listaContratoPlanoProdutoSugeridosDuplicados.forEach(p -> {
            contratoVO.getContratoPlanoProdutoSugeridoVOs().remove(p);
        });
    }

    private void selecionarCondicaoPagamento(ContratoVO contrato, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) throws Exception {
        List listaCondicaoPagamento = getPlanoCondicaoPagamento().consultarPorDescricaoPlano(contrato.getPlano().getDescricao(),
                (contrato.getEmpresa().getCodigo().equals(contrato.getPlano().getEmpresa().getCodigo()) ? contrato.getEmpresa().getCodigo() : contrato.getPlano().getEmpresa().getCodigo()),
                Uteis.NIVELMONTARDADOS_TODOS);
        if(listaCondicaoPagamento != null && listaCondicaoPagamento.size() > 0){
            PlanoCondicaoPagamentoVO obj = (PlanoCondicaoPagamentoVO) listaCondicaoPagamento.get(0);
            selecionarCondicaoPagamento(obj, contrato, modalidadesSelecionadas);
        }
    }

    private Double getValorDescontoCondicaoPagamento() {
        return valorDescontoCondicaoPagamento;
    }

    private void setValorDescontoCondicaoPagamento(Double valorDescontoCondicaoPagamento) {
        this.valorDescontoCondicaoPagamento = valorDescontoCondicaoPagamento;
    }

    private void calculoCondicaoPagamento(PlanoCondicaoPagamentoVO obj, ContratoVO contratoVO) {
        if (obj.getTipoOperacao() == null) {
            obj.setTipoOperacao("");
        }
        if (obj.getTipoOperacao().equals("RE")) {
            if (obj.getTipoValor().equals("PD")) {
                setValorDescontoCondicaoPagamento(Uteis.arredondarForcando2CasasDecimais((contratoVO.getValorFinal() * (obj.getPercentualDesconto() / 100))));
                //contratoVO.setValorFinal(Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorFinal() - (contratoVO.getValorFinal() * (obj.getPercentualDesconto() / 100))));

            } else {
                // contratoVO.setValorFinal(contratoVO.getValorFinal() - obj.getValorEspecifico());
                setValorDescontoCondicaoPagamento(obj.getValorEspecifico());
            }
        } else if (obj.getTipoOperacao().equals("AC")) {
            if (obj.getTipoValor().equals("PD")) {
                setValorDescontoCondicaoPagamento(Uteis.arredondarForcando2CasasDecimais((contratoVO.getValorFinal() * (obj.getPercentualDesconto() / 100))));
                // contratoVO.setValorFinal(Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorFinal() + (contratoVO.getValorFinal() * (obj.getPercentualDesconto() / 100))));

            } else {
                // contratoVO.setValorFinal(contratoVO.getValorFinal() + obj.getValorEspecifico());
                setValorDescontoCondicaoPagamento(obj.getValorEspecifico());
            }
        }
    }

    private int getQuantidadeParcelasValorContrato() {
        return quantidadeParcelasValorContrato;
    }

    private void setQuantidadeParcelasValorContrato(int quantidadeParcelasValorContrato) {
        this.quantidadeParcelasValorContrato = quantidadeParcelasValorContrato;
    }

    private void montarParcelasDiferentes(int totalMeses, double valorParcela, ContratoVO contratoVO) {
        int vezes = 0;
        setQuantidadeParcelasValorContrato(0);
        String valorIgual = "";//contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size() > 0 ? contratoVO.getPlano().getPlanoRecorrencia().getParcelas().get(0).getValorFormatado() : "";
        for (int y = 0; y < contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size(); y++) {
            PlanoRecorrenciaParcelaVO p = contratoVO.getPlano().getPlanoRecorrencia().getParcelas().get(y);
            if (p.getNumero() != 1) {
                valorIgual = contratoVO.getPlano().getPlanoRecorrencia().getParcelas().get(y).getValorFormatado();
                for (int x = 0; x < contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size(); x++) {
                    PlanoRecorrenciaParcelaVO XxX = contratoVO.getPlano().getPlanoRecorrencia().getParcelas().get(x);
                    if (XxX.getNumero() != 1) {
                        if (contratoVO.getPlano().getPlanoRecorrencia().getParcelas().get(x).getValorFormatado().equals(valorIgual) && !parcelasValorDiferente.contains(valorIgual)) {
                            vezes++;
                        }
                    }
                }
                if (vezes > 0 && p.getValor() > 0) {
                    parcelasValorDiferente += " " + vezes + "X R$ " + p.getValorFormatado();
                    vezes = 0;
                }
            }
        }
        if (contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size() == 1) {
            PlanoRecorrenciaParcelaVO p = contratoVO.getPlano().getPlanoRecorrencia().getParcelas().get(0);
            if (p.getNumero() == 1) {
                setQuantidadeParcelasValorContrato(totalMeses - 1);
            }else {
                setQuantidadeParcelasValorContrato(totalMeses - 2);
            }
        } else {
            boolean temEntrada = false;
            for (PlanoRecorrenciaParcelaVO prp : contratoVO.getPlano().getPlanoRecorrencia().getParcelas()) {
                if (prp.getNumero() == 1) {
                    temEntrada = true;
                    break;
                }
            }
            if (temEntrada) {
                setQuantidadeParcelasValorContrato(totalMeses - contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size());
            }else {
                setQuantidadeParcelasValorContrato(totalMeses - contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size() - 1);
            }

        }
    }

    private void validarCondicaoPagamentoMaiorDuracao(PlanoCondicaoPagamentoVO obj, ContratoVO contratoVO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) throws Exception {
        contratoVO.setPlanoCondicaoPagamento(obj);
        calcularContrato(contratoVO, modalidadesSelecionadas);
        geraMovProdutoComCondicaoPagamento(contratoVO);
        contratoVO.setValorFinal(contratoVO.getValorBaseCalculo());
        calculoCondicaoPagamento(obj, contratoVO);
        if (obj.getCondicaoPagamento().getNrParcelas() == 0) {
            setValorParcelaContrato(Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorFinal()));
        } else {
            setValorParcelaContrato(Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorFinal() / obj.getCondicaoPagamento().getNrParcelas()));
            if  (contratoVO.isDeveGerarParcelasComValorDiferente()) {
                setValorParcelaContrato(Uteis.arredondarForcando2CasasDecimais(contratoVO.getValorFinal() / (obj.getCondicaoPagamento().getNrParcelas() - contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size())));
            }
            montarParcelasDiferentes(obj.getCondicaoPagamento().getNrParcelas(), (contratoVO.getValorFinal() / obj.getCondicaoPagamento().getNrParcelas()), contratoVO);
        }
        if (contratoVO.isDeveGerarParcelasComValorDiferente()) {
            boolean entrarIf = true;
            for (PlanoRecorrenciaParcelaVO p : contratoVO.getPlano().getPlanoRecorrencia().getParcelas()) {
                if (p.getNumero() == 1) {
                    setValorParcelaComProdutoContrato(Uteis.arredondarForcando2CasasDecimais(p.getValor() + contratoVO.getSomaProduto()));
                    if (contratoVO.getSomaAnuidade() > 0) {
                        setValorParcelaComProdutoContrato(getValorParcelaComProdutoContrato() - contratoVO.getSomaAnuidade());
                    }
                    entrarIf = false;
                    break;
                }
            }
            if (entrarIf) {
                setValorParcelaComProdutoContrato(Uteis.arredondarForcando2CasasDecimais(getValorParcelaContrato() + contratoVO.getSomaProduto()));
            }
        } else {
            setValorParcelaComProdutoContrato(Uteis.arredondarForcando2CasasDecimais(getValorParcelaContrato() + contratoVO.getSomaProduto()));
        }
    }

    private Double getValorParcelaContrato() {
        return valorParcelaContrato;
    }

    private void setValorParcelaContrato(Double valorParcelaContrato) {
        this.valorParcelaContrato = valorParcelaContrato;
    }

    private Double getValorParcelaComProdutoContrato() {
        return valorParcelaComProdutoContrato;
    }

    private void setValorParcelaComProdutoContrato(Double valorParcelaComProdutoContrato) {
        this.valorParcelaComProdutoContrato = valorParcelaComProdutoContrato;
    }

    private void selecionarCondicaoPagamento(PlanoCondicaoPagamentoVO obj, ContratoVO contratoVO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) throws Exception {
        try {
            if (obj == null) {
                calcularContrato(contratoVO, modalidadesSelecionadas);
                geraMovProdutoComCondicaoPagamento(contratoVO);
                contratoVO.setValorFinal(contratoVO.getValorBaseCalculo());
            } else {
                boolean estaPresente = false;
                Iterator i = contratoVO.getPlanoDuracao().getPlanoCondicaoPagamentoVOs().iterator();
                while (i.hasNext()) {
                    PlanoCondicaoPagamentoVO planoCondicaoPagamento = (PlanoCondicaoPagamentoVO) i.next();
                    if (!planoCondicaoPagamento.getCondicaoPagamento().getCodigo().equals(obj.getCondicaoPagamento().getCodigo())) {
                        planoCondicaoPagamento.getCondicaoPagamento().setCondicaoPagamentoEscolhida(false);
                    } else {
                        estaPresente = true;
                        if (planoCondicaoPagamento.getCondicaoPagamento().getCondicaoPagamentoEscolhida()) {
                            validarCondicaoPagamentoMaiorDuracao(obj, contratoVO, modalidadesSelecionadas);
                        } else {
                            contratoVO.setPlanoCondicaoPagamento(new PlanoCondicaoPagamentoVO());
                            calcularContrato(contratoVO);
                            geraMovProdutoComCondicaoPagamento(contratoVO);
                            contratoVO.setValorFinal(contratoVO.getValorBaseCalculo());
                        }
                    }
                }
                if (!estaPresente) {
                    contratoVO.setPlanoCondicaoPagamento(new PlanoCondicaoPagamentoVO());
                    calcularContrato(contratoVO);
                    geraMovProdutoComCondicaoPagamento(contratoVO);
                    contratoVO.setValorFinal(contratoVO.getValorBaseCalculo());
                }
            }
            contratoVO.setValorFinal(Uteis.arredondarForcando2CasasDecimais(contratoVO.getSomaProduto() + contratoVO.getValorFinal()));
        } catch (Exception e) {
            e.getStackTrace();
        }
    }

    private void inicializarMovProdutoContrato(ContratoVO contratoVO) throws Exception {
        try {
            int i = 0;
            Date mesInicioReferencia = dataBaseSegundaParcela == null ? (dataInicioContrato == null ? (Date)contratoVO.getDataLancamento() : dataInicioContrato) : dataBaseSegundaParcela;
            if ((contratoVO.getSaldoCreditoContratoOrigemRenovacao() > 0) && (contratoVO.getDiasRestanteContratoOrigemRenovacao() > 0)){
                mesInicioReferencia = Uteis.somarDias(contratoVO.getContratoOrigemRenovacao().getVigenciaAte(), 1);
            }
            Date dataAtual = mesInicioReferencia;
            int mes = 1;
            Date data = mesInicioReferencia;
            Double somaParcelasDif = 0.0;
            Double somaTotal = 0.0; // usado para evitar problemas com arredondamento
            while (i < contratoVO.getPlanoDuracao().getNumeroMeses()) {
                getMovProdutoVO().setNumeroParcela(mes);
                getMovProdutoVO().setProduto(contratoVO.getPlano().getProdutoPadraoGerarParcelasContrato());
                getMovProdutoVO().setApresentarMovProduto(true);
                getMovProdutoVO().setContrato(contratoVO);
                getMovProdutoVO().setMesReferencia(Uteis.getMesReferenciaData(data));
                getMovProdutoVO().setAnoReferencia(Uteis.getAnoData(data));
                data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                getMovProdutoVO().setDescricao(contratoVO.getPlano().getDescricao() + " - " + getMovProdutoVO().getMesReferencia());
                getMovProdutoVO().setSituacao("EA");
                getMovProdutoVO().setEmpresa(contratoVO.getEmpresa());
                getMovProdutoVO().setPessoa(contratoVO.getPessoa());
                getMovProdutoVO().setQuantidade(1);
                getMovProdutoVO().setDataInicioVigencia(dataAtual);
                getMovProdutoVO().setDataFinalVigencia(contratoVO.getVigenciaAteAjustada());
                getMovProdutoVO().setDataLancamento(contratoVO.getDataLancamento());
                getMovProdutoVO().getResponsavelLancamento().setCodigo(contratoVO.getResponsavelContrato().getCodigo());

                // Gerar parcela com valor diferente das outras, para plano recorrentes
                // DefiniÃ§Ãµes da parcela no cadastro de plano, aba recorrÃªncia, campo "Cobrar anuidade junto com a parcela"
                Double valorFinal = 0.0;
                if(getMovProdutoVO().getTipoProduto().equals("PM") && contratoVO.isDeveGerarParcelasComValorDiferente()){
                    Double valor = getPlanorecorrenciaParcela().consultarValorPorNumeroParcela(mes, contratoVO.getPlano().getPlanoRecorrencia().getCodigo());
                    if(valor != null){
                        valorFinal = valor;
                        somaParcelasDif += valor;
                    }else{
                        if(contratoVO.getPlano().getRecorrencia() && contratoVO.getPlano().getPlanoRecorrencia().getQuantidadeParcelasValorDiferente() > 0) {
                            valorFinal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal((contratoVO.getValorBaseCalculo() - contratoVO.getPlano().getPlanoRecorrencia().getValorTotalParcelasValorDirefente()) / (contratoVO.getPlanoDuracao().getNumeroMeses() - contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size()));
                        } else {
                            valorFinal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal((contratoVO.getValorBaseCalculo() - somaParcelasDif) / (contratoVO.getPlanoDuracao().getNumeroMeses() - contratoVO.getPlano().getPlanoRecorrencia().getParcelas().size()));
                        }
                    }
                }else {
                    valorFinal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(contratoVO.getValorBaseCalculo() / contratoVO.getPlanoDuracao().getNumeroMeses());
                }
                if(getMovProdutoVO().getNumeroParcela() == 1 && !contratoVO.getDividirProdutosNasParcelas()){
                    getMovProdutoVO().setTotalFinal(valorFinal +  contratoVO.getTotalFinalProdutos());
                } else {
                    getMovProdutoVO().setTotalFinal(valorFinal);
                }
                if (contratoVO.getValorConvenioDesconto() != 0.0) {
                    getMovProdutoVO().setValorDesconto(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(contratoVO.getValorConvenioDesconto() / contratoVO.getPlanoDuracao().getNumeroMeses()));
                } else {
                    getMovProdutoVO().setValorDesconto(0.0);
                }
                if ((contratoVO.getDesconto() != null || contratoVO.getDesconto().getCodigo() != 0)) {
                    if (contratoVO.getTipoDesconto().equals("VA")) {
                        getMovProdutoVO().setValorDesconto((getMovProdutoVO().getValorDesconto() + (contratoVO.getValorDescontoEspecifico() / contratoVO.getPlanoDuracao().getNumeroMeses())));
                    } else {
                        getMovProdutoVO().setValorDesconto(Uteis.arredondarForcando2CasasDecimais(getMovProdutoVO().getValorDesconto()
                                + contratoVO.getValorTemporarioDescontoPorcentagem()
                                + contratoVO.getValorTemporarioDescontoAntecipado()));
                    }
                }
                getMovProdutoVO().setPrecoUnitario((getMovProdutoVO().getTotalFinal() + getMovProdutoVO().getValorDesconto()));
                somaTotal = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(somaTotal + getMovProdutoVO().getTotalFinal());
                adicionarMovProduto(false, contratoVO);
                mes++;
                i++;

            }
            if (somaTotal.doubleValue() != contratoVO.getValorBaseCalculo() && (contratoVO.getMovProdutoVOs() != null && !contratoVO.getMovProdutoVOs().isEmpty())) { // ajusta problemas de arredondamento
                Double diferenca = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(somaTotal - contratoVO.getValorBaseCalculo());
                MovProdutoVO primeiro = (MovProdutoVO) contratoVO.getMovProdutoVOs().get(0);
                Double precoFinal;
                if(primeiro.getTipoProduto().equals("PM") && contratoVO.isDeveGerarParcelasComValorDiferente()) {
                    Double valor = getPlanorecorrenciaParcela().consultarValorPorNumeroParcela(1, contratoVO.getPlano().getPlanoRecorrencia().getCodigo());
                    if (valor != null) {
                        precoFinal = valor;
                    } else {
                        precoFinal = primeiro.getPrecoUnitario() - diferenca;
                    }
                }else{
                    precoFinal = primeiro.getTotalFinal() - diferenca;
                }
                primeiro.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(precoFinal));
                primeiro.setTotalFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(precoFinal));
            }
        } catch (Exception e) {
            throw e;
        }

    }

    public double obterValorMensalPlanoSiteNaoRecorrente(ContratoVO contratoVO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) {
        double valorMensal = 0.0;

        if(contratoVO.getPlano().getPermitirTurmasVendasOnline() && modalidadesSelecionadas != null && !modalidadesSelecionadas.isEmpty()){
            AtomicReference<Double> valorMensalTurma = new AtomicReference<>(0.0);
            contratoVO.getPlano().getPlanoExcecaoVOs().forEach(pE -> {
                contratoVO.getPlano().getPlanoModalidadeVOs().forEach(pM ->{
                    if (pE.getModalidade().getCodigo().equals(pM.getModalidade().getCodigo())) {
                        modalidadesSelecionadas.forEach(mS -> {
                            if ((pE.getModalidade().getCodigo().equals(mS.getCodigo()) && pE.getVezesSemana().equals(mS.getSelectedTimesPerWeek()))) {
                                valorMensalTurma.updateAndGet(v -> v + pE.getValor());
                            }
                        });
                    }
                });
            });
            valorMensal = valorMensalTurma.get();
        } else if(contratoVO.getPlano().getPermitirTurmasVendasOnline() && UteisValidacao.emptyList(modalidadesSelecionadas) &&
                !UteisValidacao.emptyList(contratoVO.getPlano().getPlanoExcecaoVOs())) {
            PlanoExcecaoVO planoExcecaoVOMenorValor = null;
            for (PlanoExcecaoVO planoExcecaoVO: contratoVO.getPlano().getPlanoExcecaoVOs()) {
                if (planoExcecaoVOMenorValor == null || planoExcecaoVO.getValor() < planoExcecaoVOMenorValor.getValor()) {
                    planoExcecaoVOMenorValor = planoExcecaoVO;
                }
            }
            valorMensal = Uteis.arredondarForcando2CasasDecimais(planoExcecaoVOMenorValor.getValor());
        } else if (!UteisValidacao.emptyList(contratoVO.getPlano().getPlanoDuracaoVOs()) && contratoVO.getPlano().getPlanoDuracaoVOs().get(0).getValorTotal() != 0) {
            valorMensal = contratoVO.getPlano().getPlanoDuracaoVOs().get(0).getValorTotal() / contratoVO.getPlano()
                    .getPlanoDuracaoVOs().get(0).getNumeroMeses();
        } else if (!UteisValidacao.emptyList(contratoVO.getPlano().getPlanoExcecaoVOs())) {
            valorMensal = contratoVO.getPlano().getPlanoExcecaoVOs().get(0).getValorTotal() / contratoVO.getPlano()
                    .getPlanoExcecaoVOs().get(0).getDuracao();
        } else {
            for (PlanoModalidadeVO planoModalidadeVO : contratoVO.getPlano().getPlanoModalidadeVOs()) {
                List<PlanoModalidadeVezesSemanaVO> planoModalidadeVezesSemanaVOS = planoModalidadeVO.getPlanoModalidadeVezesSemanaVOs();
                if (planoModalidadeVO.getPlanoModalidadeVezesSemanaVOs().size() > 1) {
                    Ordenacao.ordenarListaReverse(planoModalidadeVezesSemanaVOS, "nrVezes");
                    if (planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico() != 0
                            && planoModalidadeVezesSemanaVOS.get(0).getTipoOperacao().equalsIgnoreCase("EX")) {
                        valorMensal += planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico();
                    } else if (planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico() != 0
                            && planoModalidadeVezesSemanaVOS.get(0).getTipoOperacao().equalsIgnoreCase("AC")) {
                        valorMensal +=
                                planoModalidadeVO.getModalidade().getValorMensal() + planoModalidadeVezesSemanaVOS.get(
                                        0).getValorEspecifico();
                    } else if (planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico() != 0
                            && planoModalidadeVezesSemanaVOS.get(0).getTipoOperacao().equalsIgnoreCase("RE")) {
                        valorMensal +=
                                planoModalidadeVO.getModalidade().getValorMensal() - planoModalidadeVezesSemanaVOS.get(
                                        0).getValorEspecifico();
                    } else {
                        valorMensal += planoModalidadeVO.getModalidade().getValorMensal();
                    }
                } else if (planoModalidadeVO.getPlanoModalidadeVezesSemanaVOs().size() == 1) {
                    if (planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico() != 0
                            && planoModalidadeVezesSemanaVOS.get(0).getTipoOperacao().equalsIgnoreCase("EX")) {
                        valorMensal += planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico();
                    } else if (planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico() != 0
                            && planoModalidadeVezesSemanaVOS.get(0).getTipoOperacao().equalsIgnoreCase("AC")) {
                        valorMensal +=
                                planoModalidadeVO.getModalidade().getValorMensal() + planoModalidadeVezesSemanaVOS.get(
                                        0).getValorEspecifico();
                    } else if (planoModalidadeVezesSemanaVOS.get(0).getValorEspecifico() != 0
                            && planoModalidadeVezesSemanaVOS.get(0).getTipoOperacao().equalsIgnoreCase("RE")) {
                        valorMensal +=
                                planoModalidadeVO.getModalidade().getValorMensal() - planoModalidadeVezesSemanaVOS.get(
                                        0).getValorEspecifico();
                    } else {
                        valorMensal += planoModalidadeVO.getModalidade().getValorMensal();
                    }
                }
            }
        }
        return valorMensal;
    }

    private void geraMovProdutoComCondicaoPagamento(ContratoVO contratoVO) throws Exception {
        contratoVO.setSomaProduto(0.0);
        contratoVO.setMovProdutoVOs(new ArrayList());
        inicializarMovProdutoContrato(contratoVO);
        inicializarMovProduto(false, contratoVO);
    }

    private void inicializarMovProduto(boolean arredondamento, ContratoVO contratoVO) throws Exception {
        try {
            contratoVO.setSomaProduto(0D);
            contratoVO.setSomaAdesao(0D);
            contratoVO.setSomaAnuidade(0D);
            contratoVO.setSomaDescontoAdesao(0D);
            Date mesInicioReferencia = dataBaseSegundaParcela == null ? (dataInicioContrato == null ? (Date)contratoVO.getDataLancamento() : dataInicioContrato) : dataBaseSegundaParcela;
            if ((contratoVO.getDiasRestanteContratoOrigemRenovacao() > 0) && (contratoVO.getSaldoCreditoContratoOrigemRenovacao() > 0)){
                mesInicioReferencia = Uteis.somarDias(contratoVO.getContratoOrigemRenovacao().getVigenciaAte(), 1);
            }
            if (contratoVO.getContratoPlanoProdutoSugeridoVOs().size() > 0) {
                Iterator j = contratoVO.getContratoPlanoProdutoSugeridoVOs().iterator();
                while (j.hasNext()) {
                    ContratoPlanoProdutoSugeridoVO produto = (ContratoPlanoProdutoSugeridoVO) j.next();
                    if (!produto.getPlanoProdutoSugerido().getProdutoSugeridoEscolhida()) {
                        continue;
                    }
                    getMovProdutoVO().setProduto(produto.getPlanoProdutoSugerido().getProduto());
                    getMovProdutoVO().setRenovavelAutomaticamente(produto.getPlanoProdutoSugerido().getProduto().getRenovavelAutomaticamente());
                    getMovProdutoVO().setApresentarMovProduto(false);
                    getMovProdutoVO().setContrato(contratoVO);
                    getMovProdutoVO().setDescricao(produto.getPlanoProdutoSugerido().getProduto().getDescricao());
                    getMovProdutoVO().setSituacao("EA");
                    getMovProdutoVO().setEmpresa(contratoVO.getEmpresa());
                    getMovProdutoVO().setPessoa(contratoVO.getPessoa());
                    getMovProdutoVO().setMesReferencia(Uteis.getMesReferenciaData(mesInicioReferencia));
                    getMovProdutoVO().setQuantidade(produto.getPlanoProdutoSugerido().getQuantidade());
                    getMovProdutoVO().setAnoReferencia(Uteis.getAnoData(mesInicioReferencia));
                    if (!produto.getPlanoProdutoSugerido().getProduto().getTipoVigencia().equals("")) {
                        getMovProdutoVO().setDataInicioVigencia(produto.getPlanoProdutoSugerido().getDataVenda());
                        getMovProdutoVO().setDataFinalVigencia(produto.getPlanoProdutoSugerido().getDataValidade());
                    } else {
                        getMovProdutoVO().setDataInicioVigencia(null);
                        getMovProdutoVO().setDataFinalVigencia(null);
                    }
                    getMovProdutoVO().setDataLancamento(contratoVO.getDataLancamento());
                    getMovProdutoVO().setResponsavelLancamento(contratoVO.getResponsavelContrato());
                    getMovProdutoVO().setPrecoUnitario(produto.getPlanoProdutoSugerido().getValorProduto());
                    if (produto.getPlanoProdutoSugerido().getProduto().getDesconto().getValor() != 0.0) {
                        if (produto.getPlanoProdutoSugerido().getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
                            getMovProdutoVO().setValorDesconto(Uteis.arredondarForcando2CasasDecimais(produto.getPlanoProdutoSugerido().getProduto().getDesconto().getValorTemporarioProdudoDoDesconto()));
                        } else {
                            getMovProdutoVO().setValorDesconto(Uteis.arredondarForcando2CasasDecimais(produto.getPlanoProdutoSugerido().getProduto().getDesconto().getValor()));
                        }
                    } else {
                        getMovProdutoVO().setValorDesconto(0.0);
                    }
                    getMovProdutoVO().setTotalFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(produto.getPlanoProdutoSugerido().getValorProdutoQtdDesconto()));
                    contratoVO.setSomaProduto(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getMovProdutoVO().getTotalFinal() + contratoVO.getSomaProduto()));

                    if (produto.getPlanoProdutoSugerido().getProduto().getTipoProduto().equals("TA")) {
                        contratoVO.setSomaAnuidade(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(
                                getMovProdutoVO().getTotalFinal() + contratoVO.getSomaAnuidade()));
                    }

                    if (produto.getPlanoProdutoSugerido().getProduto().getTipoProduto().equals("TD")) {
                        contratoVO.setSomaAdesao(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(
                                getMovProdutoVO().getTotalFinal() + contratoVO.getSomaAdesao()));
                        contratoVO.setSomaDescontoAdesao(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(
                                produto.getPlanoProdutoSugerido().getValorDescontoCupom() + contratoVO.getSomaDescontoAdesao()));
                    }

                    adicionarMovProduto(!arredondamento, contratoVO);
                }
            }
            if (contratoVO.getContratoModalidadeVOs().size() > 0) {
                for (ContratoModalidadeVO cm : contratoVO.getContratoModalidadeVOs()) {
                    if (cm.getContratoModalidadeProdutoSugeridoVOs().size() > 0) {
                        Iterator k = cm.getContratoModalidadeProdutoSugeridoVOs().iterator();
                        while (k.hasNext()) {
                            ContratoModalidadeProdutoSugeridoVO produto = (ContratoModalidadeProdutoSugeridoVO) k.next();
                            if (!produto.getProdutoSugerido().getProdutoSugeridoEscolhida()) {
                                continue;
                            }
                            getMovProdutoVO().setProduto(produto.getProdutoSugerido().getProduto());
                            getMovProdutoVO().setContrato(contratoVO);
                            getMovProdutoVO().setDescricao(produto.getProdutoSugerido().getProduto().getDescricao());
                            getMovProdutoVO().setSituacao("EA");
                            getMovProdutoVO().setEmpresa(contratoVO.getEmpresa());
                            getMovProdutoVO().setPessoa(contratoVO.getPessoa());
                            getMovProdutoVO().setMesReferencia(Uteis.getMesReferenciaData(mesInicioReferencia));
                            getMovProdutoVO().setQuantidade(1);
                            getMovProdutoVO().setAnoReferencia(Uteis.getAnoData(mesInicioReferencia));
                            if (!produto.getProdutoSugerido().getProduto().getTipoVigencia().equals("")) {
                                getMovProdutoVO().setDataInicioVigencia(produto.getProdutoSugerido().getDataVenda());
                                getMovProdutoVO().setDataFinalVigencia(produto.getProdutoSugerido().getDataValidade());
                            } else {
                                getMovProdutoVO().setDataInicioVigencia(null);
                                getMovProdutoVO().setDataFinalVigencia(null);
                            }
                            getMovProdutoVO().setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
                            getMovProdutoVO().setResponsavelLancamento(contratoVO.getResponsavelContrato());
                            if (produto.getProdutoSugerido().getProduto().getDesconto().getValor() != 0.0) {
                                if (produto.getProdutoSugerido().getProduto().getDesconto().getTipoDesconto().equals(TipoDesconto.PE)) {
                                    getMovProdutoVO().setValorDesconto(Uteis.arredondarForcando2CasasDecimais(produto.getProdutoSugerido().getProduto().getDesconto().getValorTemporarioProdudoDoDesconto()));
                                } else {
                                    getMovProdutoVO().setValorDesconto(produto.getProdutoSugerido().getProduto().getDesconto().getValor());
                                }

                            } else {
                                getMovProdutoVO().setValorDesconto(0.0);
                            }
                            getMovProdutoVO().setPrecoUnitario(produto.getProdutoSugerido().getProduto().getValorFinal());
                            getMovProdutoVO().setTotalFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(produto.getValorFinalProduto()));
                            contratoVO.setSomaProduto(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getMovProdutoVO().getTotalFinal() + contratoVO.getSomaProduto()));
                            adicionarMovProduto(!arredondamento, contratoVO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    private void adicionarMovProduto(boolean produtoSugerido, ContratoVO contratoVO) throws Exception {
        try {
            if (!contratoVO.getCodigo().equals(0)) {
                movProdutoVO.setContrato(contratoVO);
            }
            contratoVO.adicionarObjMovProdutoVOs(getMovProdutoVO(), produtoSugerido);
            this.setMovProdutoVO(new MovProdutoVO());
        } catch (Exception e) {
            throw e;
        }
    }

    private void calcularContrato(ContratoVO contratoVO) {
        calcularContrato(contratoVO, null);
    }

    private void calcularContrato(ContratoVO contratoVO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) {
        try {
            // Contratos comuns
            if(!contratoVO.getPlano().getRegimeRecorrencia() && !contratoVO.getPlano().isVendaCreditoTreino()){
                double valorContrato = Uteis.arredondarForcando2CasasDecimais(obterValorMensalPlanoSiteNaoRecorrente(contratoVO, modalidadesSelecionadas) * contratoVO.getPlano().getPlanoDuracaoVOs().get(0).getNumeroMeses());
                double valorContratoComProdutos = Uteis.arredondarForcando2CasasDecimais(contratoVO.getSomaProduto() + valorContrato);
                contratoVO.setValorFinal(valorContratoComProdutos);
                contratoVO.setValorBaseCalculo(valorContrato);
                contratoVO.setValorContrato(valorContrato);
            }else {
                setValorMensalModalidade(getContrato().calcularValorContrato(contratoVO, true, true));
            }
        } catch (Exception e) {
            e.getMessage();
        }
    }

    private void montarValorContratoPlanoDuracao(ContratoVO contratoVO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) throws Exception {
        try {
            if (!contratoVO.getPlano().isVendaCreditoTreino()) {
                for (PlanoDuracaoVO planoDuracaoVO : contratoVO.getPlano().getPlanoDuracaoVOs()) {
                    planoDuracaoVO.setValorFinalContrato(0.0);
                    planoDuracaoVO.setDuracaoEscolhida(true);
                    selecionarDuracao(planoDuracaoVO, contratoVO, modalidadesSelecionadas);
                }
                if (getPlanoDuracaoSelecionada() != null) {
                    getPlanoDuracaoSelecionada().setDuracaoEscolhida(true);
                    selecionarDuracao(getPlanoDuracaoSelecionada(), contratoVO, null);
                } else {
                    selecionarDuracao(new PlanoDuracaoVO(), contratoVO, modalidadesSelecionadas);
                }
            }
        } catch (Exception e) {
            for (PlanoDuracaoVO planoDuracaoVO : contratoVO.getPlano().getPlanoDuracaoVOs()) {
                planoDuracaoVO.setValorFinalContrato(0.0);
                planoDuracaoVO.setDuracaoEscolhida(false);
            }
            throw e;
        }
    }

    private void selecionarDuracao(PlanoDuracaoVO obj, ContratoVO contratoVO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) throws Exception {
        Iterator i = contratoVO.getPlano().getPlanoDuracaoVOs().iterator();
        while (i.hasNext()) {
            PlanoDuracaoVO planoDuracao = (PlanoDuracaoVO) i.next();
            if (!planoDuracao.getCodigo().equals(obj.getCodigo())) {
                planoDuracao.setDuracaoEscolhida(false);
            } else {
                if (planoDuracao.getDuracaoEscolhida()) {
                    contratoVO.setPlanoDuracao(obj);
                    contratoVO.getPlanoDuracao().setPlanoCondicaoPagamentoVOs(Ordenacao.ordenarLista(contratoVO.getPlanoDuracao().getPlanoCondicaoPagamentoVOs(), "qtdParcela"));
                    boolean pagamentoEscolhido = true;
                    if (planoDuracao.getPlanoCondicaoPagamentoVOs().size() == 1 || contratoVO.getPlano().getBolsa()) {
                        PlanoCondicaoPagamentoVO planoCondicaoPagamento = planoDuracao.getPlanoCondicaoPagamentoVOs().get(0);
                        planoCondicaoPagamento.getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
                    } else {
                        Iterator j = planoDuracao.getPlanoCondicaoPagamentoVOs().iterator();
                        while (j.hasNext() && pagamentoEscolhido) {
                            PlanoCondicaoPagamentoVO aux = (PlanoCondicaoPagamentoVO) j.next();
                            if (aux.getCondicaoPagamento().getCondicaoPagamentoEscolhida()) {
//                                validarCondicaoPagamentoMaiorDuracao(aux);
                                pagamentoEscolhido = false;
                            }
                        }
                    }
                } else {
                    contratoVO.setPlanoDuracao(new PlanoDuracaoVO());
                }
            }
        }
        contratoVO.obterDataFinalContrato(dataInicioContrato == null ? (Date)contratoVO.getDataLancamento().clone() : (Date)dataInicioContrato.clone());
        selecionarCondicaoPagamento(contratoVO, modalidadesSelecionadas);
        obj.setValorFinalContrato(contratoVO.getValorFinal());
    }

    private void selecionarAutomaticamenteHorarioDuracaoCondicao(ContratoVO contratoVO, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) throws Exception {
        if (contratoVO.getPlano().getPlanoHorarioVOs().size() == 1) {
            PlanoHorarioVO planoHorario = contratoVO.getPlano().getPlanoHorarioVOs().get(0);
            planoHorario.getHorario().setHorarioEscolhida(true);
            selecionarHorario(planoHorario, contratoVO);
        }
        if (contratoVO.getPlano().getPlanoDuracaoVOs().size() == 1) {
            PlanoDuracaoVO duracao = contratoVO.getPlano().getPlanoDuracaoVOs().get(0);
            duracao.setDuracaoEscolhida(true);
            contratoVO.setPlanoDuracao(duracao);
            selecionarDuracao(duracao, contratoVO, modalidadesSelecionadas);

            if (contratoVO.getPlano().isVendaCreditoTreino() && contratoVO.getPlano().getSite() && !contratoVO.getPlano().isCreditoSessao()) {
                if (contratoVO.getPlanoDuracao().getListaPlanoDuracaoCreditoTreino().size() == 1) {
                    inicializarValoresDuracaoPlanoCreditoTreino(false, contratoVO);
                    PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO = contratoVO.getPlanoDuracao().getListaPlanoDuracaoCreditoTreino().get(0);
                    planoDuracaoCreditoTreinoVO.setSelecionado(true);
                    selecionarPlanoDuracaoCreditoTreino(planoDuracaoCreditoTreinoVO, contratoVO);
                }
            }
        }
    }

    private void selecionarPlanoDuracaoCreditoTreino(PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoSelecionado, ContratoVO contratoVO) {
        try {
            contratoVO.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(null);
            this.planoDuracaoCreditoTreinoSelecionado  = planoDuracaoCreditoTreinoSelecionado;
            //this.planoDuracaoCreditoTreinoSelecionado = getFacade().getPlanoDuracaoCreditoTreino().consultarPorCodigo(codigoPlanoDuracaoCreditoTreinoSelecionado,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            // desmarcar todas as duraÃ§Ãµes
            for (PlanoDuracaoCreditoTreinoVO obj : listaPlanoDuracaoCreditoTreino) {
                if (!obj.getCodigo().equals(planoDuracaoCreditoTreinoSelecionado.getCodigo())) {
                    obj.setSelecionado(false);
                }
            }
            // calcular o valor mensal.
            planoDuracaoCreditoTreinoSelecionado.setPlanoDuracaoVO(getPlanoDuracao().consultarPorChavePrimaria(planoDuracaoCreditoTreinoSelecionado.getPlanoDuracaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            double valorMensal = 0;
            if (planoDuracaoCreditoTreinoSelecionado.getTipoHorarioCreditoTreinoEnum() != TipoHorarioCreditoTreinoEnum.HORARIO_TURMA){
                valorMensal = (planoDuracaoCreditoTreinoSelecionado.getValorTotal() / planoDuracaoCreditoTreinoSelecionado.getPlanoDuracaoVO().getNumeroMeses()) / contratoVO.getContratoModalidadeVOs().size();
            }

            // Alterar os valores de(x semana e se exige turma) de acordo com o tipo de horÃ¡rio da duraÃ§Ã£o do crÃ©dito
            for (ContratoModalidadeVO obj : contratoVO.getContratoModalidadeVOs()) {
                obj.setValorFinalModalidade(0.0);
                obj.getModalidade().setValorMensal(valorMensal);
                obj.getModalidade().setModalidadeEscolhida(true);
                //adicionarListaApresentarModalidadeMarcada(obj);

                if (planoDuracaoCreditoTreinoSelecionado.getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA){
                    //obj.setNrVezesSemana(planoDuracaoCreditoTreinoSelecionado.getNumeroVezesSemana());
                    obj.getModalidade().setModalidadeEscolhida(false);
                    obj.setNrVezesSemana(0);
                    obj.getModalidade().setNrVezes(planoDuracaoCreditoTreinoSelecionado.getNumeroVezesSemana());
                    obj.getModalidade().setUtilizarTurma(true);
                    obj.getPlanoVezesSemanaVO().setNrVezes(planoDuracaoCreditoTreinoSelecionado.getNumeroVezesSemana());
                }else{
                    obj.getModalidade().setModalidadeEscolhida(true);
                    obj.setNrVezesSemana(7);
                    obj.getPlanoVezesSemanaVO().setNrVezes(7);
                    obj.getModalidade().setUtilizarTurma(false);
                }
            }
            PlanoDuracaoVO planoDuracaoVO = null;
            // escolher a duraÃ§Ã£o do plano de acordo com a "duraÃ§Ã£o do crÃ©dito de treino selecionado."
            for (PlanoDuracaoVO obj: contratoVO.getPlano().getPlanoDuracaoVOs()){
                if ((obj.getCodigo().equals(planoDuracaoCreditoTreinoSelecionado.getPlanoDuracaoVO().getCodigo())) &&
                        (planoDuracaoCreditoTreinoSelecionado.isSelecionado())){
                    obj.setDuracaoEscolhida(true);
                    contratoVO.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(ContratoDuracaoCreditoTreinoVO.criarInstancia(planoDuracaoCreditoTreinoSelecionado,contratoVO.getContratoDuracao(), contratoVO.getPlano()));
                    planoDuracaoVO = obj;
                }
            }
            if (planoDuracaoVO != null)
                selecionarDuracao(planoDuracaoVO, contratoVO, null);
        }catch (Exception e){
            e.getStackTrace();
        }
    }

    private void inicializarValoresDuracaoPlanoCreditoTreino(boolean renovacao, ContratoVO contratoVO) throws Exception{
        if (contratoVO.getPlano().isVendaCreditoTreino()){
            if (renovacao) {
                if (Calendario.igual(contratoVO.getContratoOrigemRenovacao().getVigenciaDe(), Calendario.hoje())){
                    throw new ConsistirException("OperaÃ§Ã£o nÃ£o permitida. Devido o contrato atual jÃ¡ ter sido renovado hoje, sÃ³ Ã© possÃ­vel renovar novamente a partir do dia '"  + Uteis.getData(Uteis.somarDias(dataInicioContrato,1)) + "'.");
                }
                contratoVO.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(contratoVO.getContratoDuracao().getCodigo(),Uteis.NIVELMONTARDADOS_TODOS));
            }else{
                contratoVO.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(null);
            }
            this.codigoPlanoDuracaoSelecionado = 0;
            if (!renovacao && !contratoVO.getPlano().isCreditoSessao()){
                for(ContratoModalidadeVO obj: contratoVO.getContratoModalidadeVOs()){
                    obj.getModalidade().setUtilizarTurma(false);
                    obj.getModalidade().setValorMensal(0.0);
                }
            }

            this.planoDuracaoCreditoTreinoSelecionado = new PlanoDuracaoCreditoTreinoVO();

            // montar lista duraÃ§Ã£o
            listaSelectItemDuracaoCreditoTreino = new ArrayList<SelectItem>();
            this.listaSelectItemDuracaoCreditoTreino.add(new SelectItem(0,""));
            for (PlanoDuracaoVO planoDuracaoVO: contratoVO.getPlano().getPlanoDuracaoVOs()){
                if (planoDuracaoVO.getNumeroMeses() == 1){
                    if (planoDuracaoVO.getQuantidadeDiasExtra() > 0){
                        this.listaSelectItemDuracaoCreditoTreino.add(new SelectItem(planoDuracaoVO.getCodigo(), planoDuracaoVO.getTotalDias() + " DIAS"));
                    }else{
                        this.listaSelectItemDuracaoCreditoTreino.add(new SelectItem(planoDuracaoVO.getCodigo(), planoDuracaoVO.getNumeroMeses() + " MÃS"));
                    }

                }else{
                    if (planoDuracaoVO.getQuantidadeDiasExtra() > 0){
                        this.listaSelectItemDuracaoCreditoTreino.add(new SelectItem(planoDuracaoVO.getCodigo(), planoDuracaoVO.getTotalDias() + " DIAS"));
                    }else{
                        this.listaSelectItemDuracaoCreditoTreino.add(new SelectItem(planoDuracaoVO.getCodigo(), planoDuracaoVO.getNumeroMeses() + " MESES"));
                    }
                }
            }
            if (renovacao){
                codigoPlanoDuracaoSelecionado = contratoVO.getPlanoDuracao().getCodigo();
                if(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO() != null) {
                    codigoHorarioCreditoTreinoSelecionado = contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum().getCodigo();
                }
            }
            // montar lista horÃ¡rio Credito Treino
            if (contratoVO.getPlano().isCreditoSessao()) {
                montarListaHorarioCreditoTreinoSessao();
            } else {
                montarListaHorarioCreditoTreino(codigoPlanoDuracaoSelecionado);
            }
            this.listaPlanoDuracaoCreditoTreino = getPlanoDuracaoCreditoTreino().consultar(codigoPlanoDuracaoSelecionado,codigoHorarioCreditoTreinoSelecionado,Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (renovacao){
                if(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO() != null) {
                    for (PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO : this.listaPlanoDuracaoCreditoTreino) {
                        if ((planoDuracaoCreditoTreinoVO.getTipoHorarioCreditoTreinoEnum() == contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum()) &&
                                (planoDuracaoCreditoTreinoVO.getNumeroVezesSemana().equals(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana())) &&
                                (planoDuracaoCreditoTreinoVO.getQuantidadeCreditoCompra().equals(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoCompra()))) {
                            contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().setValorUnitario(planoDuracaoCreditoTreinoVO.getValorUnitario());

                            planoDuracaoCreditoTreinoVO.setSelecionado(true);
                            planoDuracaoCreditoTreinoSelecionado = planoDuracaoCreditoTreinoVO;
                            planoDuracaoCreditoTreinoSelecionado.getPlanoDuracaoVO().setNumeroMeses(contratoVO.getContratoDuracao().getNumeroMeses());
                            break;
                        }
                    }
                }
                // montar os valores de vezes por semana.
                for (ContratoModalidadeVO contratoModalidadeVO: contratoVO.getContratoModalidadeVOs()){
                    if (contratoModalidadeVO.getModalidade().getModalidadeEscolhida()){
                        contratoModalidadeVO.getPlanoVezesSemanaVO().setNrVezes(contratoModalidadeVO.getNrVezesSemana());
//                        selecionarModalidade(contratoModalidadeVO);
                    }
                    if(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO() != null) {
                        contratoModalidadeVO.getModalidade().setUtilizarTurma(contratoVO.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA);
                    }
                }

            }
        }
    }

    private void montarListaHorarioCreditoTreino(Integer codigoPlanoDuracao) throws Exception{
        this.listaSelectItemTipoHorarioCreditoTreino = new ArrayList<SelectItem>();
        if ((codigoPlanoDuracao != null) && (codigoPlanoDuracao > 0)){
            List<PlanoDuracaoCreditoTreinoVO> lista = getPlanoDuracaoCreditoTreino().consultar(codigoPlanoDuracao,null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Map<Integer, String> mapaCodigo = new HashMap<Integer, String>();
            for (PlanoDuracaoCreditoTreinoVO obj: lista){
                TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(obj.getTipoHorarioCreditoTreino());
                if (tipo != null) {
                    mapaCodigo.put(tipo.getCodigo(), tipo.getDescricao());
                }
            }
            for (Map.Entry<Integer, String> mapa : mapaCodigo.entrySet()) {
                this.listaSelectItemTipoHorarioCreditoTreino.add(new SelectItem(mapa.getKey(), mapa.getValue()));
                if ((this.codigoHorarioCreditoTreinoSelecionado == null) || (this.codigoHorarioCreditoTreinoSelecionado == 0))
                    this.codigoHorarioCreditoTreinoSelecionado = mapa.getKey();
            }

        }
    }

    private void montarListaHorarioCreditoTreinoSessao() {
        TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.HORARIO_TURMA;
        this.listaSelectItemTipoHorarioCreditoTreino.add(new SelectItem(tipo.getCodigo(), tipo.getDescricao()));
        this.codigoHorarioCreditoTreinoSelecionado = tipo.getCodigo();
    }

    private void selecionarHorario(PlanoHorarioVO obj, ContratoVO contratoVO) throws Exception {
        try {
            Iterator i = contratoVO.getPlano().getPlanoHorarioVOs().iterator();
            while (i.hasNext()) {
                PlanoHorarioVO planoHorario = (PlanoHorarioVO) i.next();
                if (!planoHorario.getCodigo().equals(obj.getCodigo())) {
                    planoHorario.getHorario().setHorarioEscolhida(false);
                } else {
                    if (planoHorario.getHorario().getHorarioEscolhida()) {
                        contratoVO.setPlanoHorario(obj);
                    } else {
                        contratoVO.setPlanoHorario(new PlanoHorarioVO());
                    }
                }
            }
//            setMensagemDetalhada("msg_dados_selecionados", "");
            setValorMensalModalidade(getContrato().calcularValorContrato(contratoVO, true, true));
        } catch (Exception e) {
            e.getMessage();
        }


    }

    private PlanoDuracaoVO getPlanoDuracaoSelecionada() {
        return planoDuracaoSelecionada;
    }

    private void setPlanoDuracaoSelecionada(PlanoDuracaoVO planoDuracaoSelecionada) {
        this.planoDuracaoSelecionada = planoDuracaoSelecionada;
    }

    private void incluiProdutosModalidade(ContratoModalidadeVO obj) throws Exception {
        Iterator i = obj.getModalidade().getProdutoSugeridoVOs().iterator();
        while (i.hasNext()) {
            ProdutoSugeridoVO produtoSugerido = (ProdutoSugeridoVO) i.next();
            if (produtoSugerido.getObrigatorio()) {
                produtoSugerido.setProdutoSugeridoEscolhida(true);
                getContratoModalidadeProdutoSugeridoo().setProdutoSugerido(produtoSugerido);
                obj.adicionarObjContratoModalidadeProdutoSugeridoVOs(getContratoModalidadeProdutoSugeridoo());
                if (!UteisValidacao.emptyList(getListaProdutoApresentar())) {
                    adicionarListaProdutoApresentar(getContratoModalidadeProdutoSugeridoo());
                }
                setContratoModalidadeProdutoSugeridoo(new ContratoModalidadeProdutoSugeridoVO());
            }
        }
        adicionarListaApresentarModalidadeMarcada(obj);
    }

    private void adicionarListaApresentarModalidadeMarcada(ContratoModalidadeVO obj) throws Exception {
        int index = 0;
        Iterator i = getListaApresentarModalidadesMarcadas().iterator();
        while (i.hasNext()) {
            ContratoModalidadeVO objExistente = (ContratoModalidadeVO) i.next();
            if (objExistente.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo())) {
                getListaApresentarModalidadesMarcadas().set(index, obj);
                return;
            }
            index++;
        }
        getListaApresentarModalidadesMarcadas().add(obj);
    }

    private void adicionarListaProdutoApresentar(ContratoModalidadeProdutoSugeridoVO obj) throws Exception {
        int index = 0;
        Iterator i = getListaProdutoApresentar().iterator();
        while (i.hasNext()) {
            ContratoModalidadeProdutoSugeridoVO objExistente = (ContratoModalidadeProdutoSugeridoVO) i.next();
            if (objExistente.getProdutoSugerido().getProduto().getCodigo().equals(obj.getProdutoSugerido().getProduto().getCodigo().intValue())) {
                getListaProdutoApresentar().set(index, obj);
                return;
            }
            index++;
        }
        getListaProdutoApresentar().add(obj);
    }

    private void montarDadosDescontoAntecipado(ContratoVO contratoVO) throws Exception {
        // se o plano possui desconto antecipado
        if (contratoVO.getPlano().getDescontoAntecipado() != null
                && contratoVO.getPlano().getDescontoAntecipado().getCodigo() != 0) {
            descontoAntecipado = getDesconto().consultarPorChavePrimaria(contratoVO.getPlano().getDescontoAntecipado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
    }

    private void montarContratoModalidade(ContratoVO contrato, List<ModalidadeSelecionadaDTO> modalidadesSelecionadas, List<HorarioTurmaDTO> horariosSelecionados) throws Exception {
        Set<TurmaVO> turmasVO = new HashSet<>();
        Set<HorarioTurmaVO> horariosVO = new HashSet<>();

        Iterator i = contrato.getPlano().getPlanoModalidadeVOs().iterator();
        while (i.hasNext()) {
            PlanoModalidadeVO obj = (PlanoModalidadeVO) i.next();
            if (obj.getModalidade().getAtivo() != null && obj.getModalidade().getAtivo()) {
                //Verificar se alguma Vezes/Semana do Plano eh para ser Referencia do Mensal
                PlanoModalidadeVezesSemanaVO planoVezesReferencia = (PlanoModalidadeVezesSemanaVO) ColecaoUtils.find(
                        obj.getPlanoModalidadeVezesSemanaVOs(), new Predicate() {
                            @Override
                            public boolean evaluate(Object o) {
                                PlanoModalidadeVezesSemanaVO vezesSemana = (PlanoModalidadeVezesSemanaVO) o;
                                return vezesSemana.isReferencia();
                            }
                        });

                if (!JSFUtilities.isJSFContext() && planoVezesReferencia == null && !UteisValidacao.emptyList(obj.getPlanoModalidadeVezesSemanaVOs())) {
                    planoVezesReferencia = (PlanoModalidadeVezesSemanaVO) obj.getPlanoModalidadeVezesSemanaVOs().get(0);
                }

                if (contrato.getOrigemSistema() != null && contrato.getOrigemSistema().equals(OrigemSistemaEnum.VENDAS_ONLINE_2) &&
                        contrato.getPlano().getPermitirTurmasVendasOnline() && modalidadesSelecionadas != null && !modalidadesSelecionadas.isEmpty()) {
                    ModalidadeVO m = obj.getModalidade();
                    if(contrato.getPlano().getRegimeRecorrencia()){
                        obj.getPlanoModalidadeVezesSemanaVOs().forEach(pVS -> {
                            PlanoModalidadeVezesSemanaVO pVSO = (PlanoModalidadeVezesSemanaVO) pVS;
                                modalidadesSelecionadas.forEach(mS -> {
                                    if (m.getCodigo().equals(mS.getCodigo()) && pVSO.getNrVezes().equals(mS.getSelectedTimesPerWeek())) {
                                        getContratoModalidadeVO().setPlanoVezesSemanaVO(pVSO);
                                        getContratoModalidadeVO().setNrVezesSemana(pVSO.getNrVezes());
                                        m.setValorOriginal(pVSO.getValorEspecifico() != 0 ? pVSO.getValorEspecifico() : m.getValorOriginal());
                                        m.setValorMensal(pVSO.getValorEspecifico() != 0 ? pVSO.getValorEspecifico() : m.getValorMensal());
                                        m.setNrVezes(pVSO.getNrVezes());
                                        m.setModalidadeEscolhida(true);
                                        pVSO.setVezeSemanaEscolhida(true);
                                        pVSO.setReferencia(true);
                                        pVSO.setOrigem(ReferenciaValorModalidadeEnum.PLANO);
                                        pVSO.setValorReferencia(pVSO.getValorEspecifico());
                                    }
                                });
                        });
                    }else {
                        contrato.getPlano().getPlanoExcecaoVOs().forEach(pE -> {
                            if (pE.getModalidade().getCodigo().equals(m.getCodigo())) {
                                modalidadesSelecionadas.forEach(mS -> {
                                    if ((pE.getModalidade().getCodigo().equals(mS.getCodigo()) && pE.getVezesSemana().equals(mS.getSelectedTimesPerWeek()))) {
                                        m.setValorMensal(pE.getValor());
                                        m.setNrVezes(pE.getVezesSemana());
                                        m.setModalidadeEscolhida(true);
                                    }
                                });
                            }
                        });
                    }
                } else if (planoVezesReferencia != null) {
                    if (obj.getModalidade().getUtilizarTurma() != null && !obj.getModalidade().getUtilizarTurma()) {
                        getContratoModalidadeVO().setPlanoVezesSemanaVO(planoVezesReferencia);
                        getContratoModalidadeVO().setNrVezesSemana(planoVezesReferencia.getNrVezes());
                    }
                    ModalidadeVO m = obj.getModalidade();
                    m.setValorOriginal(planoVezesReferencia.getValorEspecifico() != 0 ? planoVezesReferencia.getValorEspecifico() : m.getValorOriginal());
                    m.setValorMensal(planoVezesReferencia.getValorEspecifico() != 0 ? planoVezesReferencia.getValorEspecifico() : m.getValorMensal());
                    m.setNrVezes(planoVezesReferencia.getNrVezes());
                    if (contrato.getOrigemSistema() != null && contrato.getOrigemSistema().equals(OrigemSistemaEnum.VENDAS_ONLINE_2)) {
                        m.setModalidadeEscolhida(true);
                    }
                    planoVezesReferencia.setReferencia(true);
                    planoVezesReferencia.setOrigem(ReferenciaValorModalidadeEnum.PLANO);
                    planoVezesReferencia.setValorReferencia(planoVezesReferencia.getValorEspecifico());
                } else {
                    obj.getModalidade().setValorOriginal(obj.getModalidade().getValorMensal());
                }
                if (contrato.getPlano().getPermitirTurmasVendasOnline()) {
                    if (!obj.getModalidade().getModalidadeEscolhida()) {
                        continue;
                    }
                }
                if ((contrato.getPlano().getPermitirTurmasVendasOnline() && !contrato.getPlano().getRegimeRecorrencia()) || (obj.getModalidade().getUtilizarTurma() != null && !obj.getModalidade().getUtilizarTurma())) {
                    Iterator k = obj.getPlanoModalidadeVezesSemanaVOs().iterator();
                    while (k.hasNext()) {
                        PlanoModalidadeVezesSemanaVO planoVezes = (PlanoModalidadeVezesSemanaVO) k.next();
                        if (obj.getModalidade().getNrVezes().equals(planoVezes.getNrVezes())
                                || (planoVezes.getTipoOperacao().equals("EX") && obj.getPlanoModalidadeVezesSemanaVOs().size() == 1)) {
                            planoVezes.setVezeSemanaEscolhida(true);
                            getContratoModalidadeVO().setPlanoVezesSemanaVO(planoVezes);
                            getContratoModalidadeVO().setNrVezesSemana(planoVezes.getNrVezes());
                        }
                    }
                }
                if (contrato.getPlano().getPermitirTurmasVendasOnline() && horariosSelecionados != null && !horariosSelecionados.isEmpty()) {
                    if (turmasVO.isEmpty() && horariosVO.isEmpty()) {
                        for (HorarioTurmaDTO tS : horariosSelecionados) {
                            TurmaVO turma = getTurma().consultarPorChavePrimaria(tS.getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            AtomicBoolean podeAdd = new AtomicBoolean(true);
                            turmasVO.forEach(tVO -> {
                                if (tVO.getCodigo().equals(turma.getCodigo())) {
                                    podeAdd.set(false);
                                }
                            });
                            if (podeAdd.get()) {
                                turmasVO.add(turma);
                            }
                        }
                        for (HorarioTurmaDTO hS : horariosSelecionados) {
                            HorarioTurmaVO horario = getHorarioTurma().consultarPorChavePrimaria(hS.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            horariosVO.add(horario);
                        }
                    }

                    List<ContratoModalidadeTurmaVO> turmas = new ArrayList<>();
                    turmasVO.forEach(turma -> {
                        ContratoModalidadeTurmaVO t = new ContratoModalidadeTurmaVO();
                        t.setContratoModalidade(getContratoModalidadeVO().getCodigo());
                        t.setTurma(turma);
                        t.getTurma().setTurmaEscolhida(true);

                        if (obj.getModalidade().getModalidadeEscolhida() && obj.getModalidade().getCodigo().equals(turma.getModalidade().getCodigo())) {
                            List<ContratoModalidadeHorarioTurmaVO> horarios = new ArrayList<>();
                            horariosVO.forEach(hS -> {
                                if (hS.getTurma().equals(turma.getCodigo())) {
                                    ContratoModalidadeHorarioTurmaVO h = new ContratoModalidadeHorarioTurmaVO();
                                    h.setContratoModalidadeTurma(t.getCodigo());
                                    h.setHorarioTurma(hS);
                                    h.getHorarioTurma().setHorarioTurmaEscolhida(true);
                                    horarios.add(h);
                                }
                            });
                            t.setContratoModalidadeHorarioTurmaVOs(horarios);
                            turmas.add(t);
                        }
                    });
                    getContratoModalidadeVO().setContratoModalidadeTurmaVOs(turmas);
                }
                getContratoModalidadeVO().setModalidade(obj.getModalidade());
                adicionarContratoModalidade(contrato);
            }
        }
    }

    private void adicionarContratoModalidade(ContratoVO contrato) throws Exception {
        try {
            if (!contrato.getCodigo().equals(0)) {
                contratoModalidadeVO.setContrato(contrato.getCodigo());
            }
            if (getContratoModalidadeVO().getModalidade().getCodigo().equals(0)) {
                Integer campoConsulta = getContratoModalidadeVO().getModalidade().getCodigo();
                ModalidadeVO modalidade = getModalidade().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getContratoModalidadeVO().setModalidade(modalidade);
            }
            contrato.adicionarObjContratoModalidadeVOs(getContratoModalidadeVO());
            this.setContratoModalidadeVO(new ContratoModalidadeVO());
        } catch (Exception e) {
        }
    }

    private PlanoRecorrenciaParcela getPlanorecorrenciaParcela() throws Exception {
        if( planorecorrenciaParcela == null){
            planorecorrenciaParcela = new PlanoRecorrenciaParcela(this.con);
        }
        return planorecorrenciaParcela;
    }

    private void setPlanorecorrenciaParcela(PlanoRecorrenciaParcela planorecorrenciaParcela) {
        this.planorecorrenciaParcela = planorecorrenciaParcela;
    }

    private MovProdutoVO getMovProdutoVO() {
        if(movProdutoVO == null){
            movProdutoVO = new MovProdutoVO();
        }
        return movProdutoVO;
    }

    private void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    private List getListaApresentarModalidadesMarcadas() {
        if(listaApresentarModalidadesMarcadas == null){
            listaApresentarModalidadesMarcadas = new ArrayList();
        }
        return listaApresentarModalidadesMarcadas;
    }

    private void setListaApresentarModalidadesMarcadas(List listaApresentarModalidadesMarcadas) {
        this.listaApresentarModalidadesMarcadas = listaApresentarModalidadesMarcadas;
    }

    private Double getValorMensalModalidade() {
        if (valorMensalModalidade == null) {
            valorMensalModalidade = 0.0;
        }
        return valorMensalModalidade;
    }

    private void setValorMensalModalidade(Double valorMensalModalidade) {
        this.valorMensalModalidade = valorMensalModalidade;
    }

    private List getListaProdutoApresentar() {
        return listaProdutoApresentar;
    }

    private void setListaProdutoApresentar(List listaProdutoApresentar) {
        this.listaProdutoApresentar = listaProdutoApresentar;
    }

    private ContratoModalidadeProdutoSugeridoVO getContratoModalidadeProdutoSugeridoo() {
        if(contratoModalidadeProdutoSugeridoo == null){
            contratoModalidadeProdutoSugeridoo = new ContratoModalidadeProdutoSugeridoVO();
        }
        return contratoModalidadeProdutoSugeridoo;
    }

    private void setContratoModalidadeProdutoSugeridoo(ContratoModalidadeProdutoSugeridoVO contratoModalidadeProdutoSugerido) {
        this.contratoModalidadeProdutoSugeridoo = contratoModalidadeProdutoSugerido;
    }

    private ContratoModalidadeVO getContratoModalidadeVO() {
        if(contratoModalidadeVO == null){
            contratoModalidadeVO = new ContratoModalidadeVO();
        }
        return contratoModalidadeVO;
    }

    private void setContratoModalidadeVO(ContratoModalidadeVO contratoModalidadeVO) {
        this.contratoModalidadeVO = contratoModalidadeVO;
    }

    private int getDiaVencimentoCartaoRecorrencia() {
        return diaVencimentoCartaoRecorrencia;
    }

    private void setDiaVencimentoCartaoRecorrencia(int diaVencimentoCartaoRecorrencia) {
        this.diaVencimentoCartaoRecorrencia = diaVencimentoCartaoRecorrencia;
    }

    private List getListaDiasVencimento() {
        return listaDiasVencimento;
    }

    private void setListaDiasVencimento(List listaDiasVencimento) {
        this.listaDiasVencimento = listaDiasVencimento;
    }

    private void montarListaDiasVencimento(List<String> prm) {
        setDiaVencimentoCartaoRecorrencia(0);
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (String obj : prm) {
            objs.add(new SelectItem(obj.equals("") ? "0" : obj, obj));
        }
        Integer diaAtual = Uteis.getDiaMesData(Calendario.hoje());
        SelectItem itemHoje = new SelectItem(diaAtual, "HOJE");
        if (!objs.isEmpty()){
            objs.add(1, itemHoje);
        }
        setListaDiasVencimento(objs);
    }

    private List getListaApresentarTurmaMarcadas() {
        return listaApresentarTurmaMarcadas;
    }

    private void setListaApresentarTurmaMarcadas(List listaApresentarTurmaMarcadas) {
        this.listaApresentarTurmaMarcadas = listaApresentarTurmaMarcadas;
    }

    private void adicionarPlanoProdutoSugeridoVOsObrigatorios(ContratoVO contratoVO) throws Exception {
        Iterator j = contratoVO.getPlano().getPlanoProdutoSugeridoVOs().iterator();
        while (j.hasNext()) {
            PlanoProdutoSugeridoVO planoProdutoSugerido = (PlanoProdutoSugeridoVO) j.next();
            if (planoProdutoSugerido.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo()) && contratoVO.isContratoRenovacao() && contratoVO.getRegimeRecorrencia()) {
                continue;
            }
            if (planoProdutoSugerido.getObrigatorio()) {
                planoProdutoSugerido.setProdutoSugeridoEscolhida(true);
                getContratoPlanoProdutoSugeridoo().setPlanoProdutoSugerido(planoProdutoSugerido);
                adicionarContratoPlanoProdutoSugerido(contratoVO);
            }
        }
        try {
            removerContratoPlanoProdutoSugeridoVOsDuplicado(contratoVO);
        } catch (Exception ex) {}
    }

    private void montarContratoPlanoProdutoSugerido(ContratoVO contratoVO) throws Exception {
        Iterator j = contratoVO.getPlano().getPlanoProdutoSugeridoVOs().iterator();
        while (j.hasNext()) {
            PlanoProdutoSugeridoVO planoProdutoSugerido = (PlanoProdutoSugeridoVO) j.next();
            if(!planoProdutoSugerido.getAtivoPlano()){
                continue;
            }
            if (contratoVO.isContratoMatricula() && (planoProdutoSugerido.getProduto().getTipoProduto().equals("RE") || planoProdutoSugerido.getProduto().getTipoProduto().equals("RN"))) {
                planoProdutoSugerido.setApresentaProdutoMaRenoRema(false);
            }
            if (contratoVO.isContratoRematricula() && (planoProdutoSugerido.getProduto().getTipoProduto().equals("MA") || planoProdutoSugerido.getProduto().getTipoProduto().equals("RN"))) {
                planoProdutoSugerido.setApresentaProdutoMaRenoRema(false);
            }
            if (contratoVO.isContratoRenovacao() && (planoProdutoSugerido.getProduto().getTipoProduto().equals("MA") || planoProdutoSugerido.getProduto().getTipoProduto().equals("RE"))) {
                planoProdutoSugerido.setApresentaProdutoMaRenoRema(false);
            }
            if (contratoVO.isContratoMatricula() && planoProdutoSugerido.getProduto().getTipoProduto().equals("MA")) {
                planoProdutoSugerido.setProdutoSugeridoEscolhida(true);
                planoProdutoSugerido.setApresentaProdutoMaRenoRema(true);
                planoProdutoSugerido.setObrigatorio(true);
                getContratoPlanoProdutoSugeridoo().setPlanoProdutoSugerido(planoProdutoSugerido);
                adicionarContratoPlanoProdutoSugerido(contratoVO);
            } else if (contratoVO.isContratoRematricula() && planoProdutoSugerido.getProduto().getTipoProduto().equals("RE")) {
                planoProdutoSugerido.setProdutoSugeridoEscolhida(true);
                planoProdutoSugerido.setApresentaProdutoMaRenoRema(true);
                planoProdutoSugerido.setObrigatorio(true);
                getContratoPlanoProdutoSugeridoo().setPlanoProdutoSugerido(planoProdutoSugerido);
                long qtdDias = Uteis.nrDiasEntreDatasSemHoraZerada(getVigenciaAteAjustadaDoUltimoContrato(), Calendario.hoje());
                if (qtdDias <= contratoVO.getEmpresa().getQtdDiasCobrarRematricula()) {
                    planoProdutoSugerido.getProduto().getDesconto().setValor(planoProdutoSugerido.getValorProduto());
                }
                adicionarContratoPlanoProdutoSugerido(contratoVO);
            } else if (contratoVO.isContratoRenovacao() && planoProdutoSugerido.getProduto().getTipoProduto().equals("RN")) {
                planoProdutoSugerido.setProdutoSugeridoEscolhida(true);
                planoProdutoSugerido.setApresentaProdutoMaRenoRema(true);
                planoProdutoSugerido.setObrigatorio(true);
                getContratoPlanoProdutoSugeridoo().setPlanoProdutoSugerido(planoProdutoSugerido);
                adicionarContratoPlanoProdutoSugerido(contratoVO);
            } else if (!planoProdutoSugerido.getProduto().getTipoProduto().equals("RE")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("RN")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("MA")
                    && !(planoProdutoSugerido.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo()) && contratoVO.isContratoRenovacao() && contratoVO.getRegimeRecorrencia())) {
                planoProdutoSugerido.setApresentaProdutoMaRenoRema(true);
                getContratoPlanoProdutoSugeridoo().setPlanoProdutoSugerido(planoProdutoSugerido);
                adicionarContratoPlanoProdutoSugerido(contratoVO);
            } else {
                j.remove();
            }
        }
    }

    private void adicionarContratoPlanoProdutoSugerido(ContratoVO contrato) throws Exception {
        try {
            contrato.adicionarObjContratoPlanoProdutoSugeridoVOs(getContratoPlanoProdutoSugeridoo());
            setContratoPlanoProdutoSugeridoo(new ContratoPlanoProdutoSugeridoVO());
        } catch (Exception e) {
        }
    }

    private ContratoPlanoProdutoSugeridoVO getContratoPlanoProdutoSugeridoo() {
        if(this.contratoPlanoProdutoSugeridoo == null){
            this.contratoPlanoProdutoSugeridoo = new ContratoPlanoProdutoSugeridoVO();
        }
        return this.contratoPlanoProdutoSugeridoo;
    }

    private void setContratoPlanoProdutoSugeridoo(ContratoPlanoProdutoSugeridoVO contratoPlanoProdutoSugerido) {
        this.contratoPlanoProdutoSugeridoo = contratoPlanoProdutoSugerido;
    }

    private Date getVigenciaAteAjustadaDoUltimoContrato() {
        return vigenciaAteAjustadaDoUltimoContrato;
    }

    public void setVigenciaAteAjustadaDoUltimoContrato(Date vigenciaAteAjustadaDoUltimoContrato) {
        this.vigenciaAteAjustadaDoUltimoContrato = vigenciaAteAjustadaDoUltimoContrato;
    }

    private String getVigenciaAteAjustadaDoUltimoContrato_Apresentar() {
        return Uteis.getData(vigenciaAteAjustadaDoUltimoContrato);
    }

    private void adicionarConsultorSite(Usuario usuarioDao, Empresa empresaDao, ClienteVO clienteVO, ContratoVO novoContrato) throws Exception {
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(novoContrato.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        UsuarioVO usuarioVO = null;
        if (!UteisValidacao.emptyNumber(empresaVO.getConsultorSite().getCodigo())) {
            usuarioVO = usuarioDao.consultarPorCodigoColaborador(empresaVO.getConsultorSite().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        } else {
            UsuarioVO usuarioConsultor = usuarioDao.consultarColaboradorResponsavelPeloCliente(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            if(usuarioConsultor == null){
                usuarioVO = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            }else{
                usuarioVO = usuarioConsultor;
            }
        }
        novoContrato.setUsuarioVO(usuarioVO);
    }

    private void gerarAnuidadeOuAdesao(boolean gerarAdesao, boolean gerarAnuidade, ContratoVO novoContrato) {
        if ((!gerarAdesao) || (!gerarAnuidade)){
            for (Object obj: novoContrato.getPlano().getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO) obj;
                if (!gerarAnuidade){
                    if (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())) {
                        planoProdutoSugeridoVO.setValorProduto(0.0);
                        planoProdutoSugeridoVO.getProduto().setValorFinal(0.0);
                    }
                }
                if (!gerarAdesao){
                    if (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo())) {
                        planoProdutoSugeridoVO.setValorProduto(0.0);
                        planoProdutoSugeridoVO.getProduto().setValorFinal(0.0);
                    }
                }
            }
        }
    }

    private void inicializarDadosConsultor(ContratoVO obj, ClienteVO clienteVO) throws Exception {
        ColaboradorVO colaboradorVO = getColaborador().consultarColaboradorPorPessoaVinculada(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (colaboradorVO != null && !UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
            obj.setConsultor(colaboradorVO);
        } else if (colaboradorVO == null || UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
            Vinculo vinculoDAO;
            try {
                obj.setConsultor(buscarColaboradorSiteOuCriarSeNaoExistir(obj.getEmpresa()));
                vinculoDAO = new Vinculo(this.con);
                List<VinculoVO> vinculosExistentes = vinculoDAO.consultarPorClienteTipoVinculo(clienteVO.getCodigo(), "CO", Uteis.NIVELMONTARDADOS_MINIMOS);
                // Se aluno chegar aqui sem nenhum vínculo em seu cadastro, então incluir
                if (UteisValidacao.emptyList(vinculosExistentes)) {
                    VinculoVO vinculoVO = new VinculoVO();
                    vinculoVO.setColaborador(obj.getConsultor());
                    vinculoVO.setTipoVinculo("CO");
                    vinculoVO.setCliente(clienteVO);
                    vinculoDAO.incluir(vinculoVO, Calendario.hoje(), "VENDAS", true, null, null);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                vinculoDAO = null;
            }
        }
    }

    private ColaboradorVO buscarColaboradorSiteOuCriarSeNaoExistir(EmpresaVO empresaVO) throws Exception {
        ColaboradorVO colaboradorVOSite = new ColaboradorVO();
        Colaborador colaboradorDAO;
        VendasConfig vendasConfigDAO;

        try {
            vendasConfigDAO = new VendasConfig(this.con);
            //Tentar onbter o consultor padrão configurador lá nas configurações gerais do vendas online.
            colaboradorVOSite = vendasConfigDAO.config(empresaVO.getCodigo()).getConsultorSite();
            if (colaboradorVOSite != null && !UteisValidacao.emptyNumber(colaboradorVOSite.getCodigo())) {
                return colaboradorVOSite;
            } else { //Se lá não tiver nada definido então buscar ou criar o COLABORADOR SITE no banco
                colaboradorDAO = new Colaborador(this.con);
                TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
                tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());
                colaboradorVOSite.getPessoa().setNome("COLABORADOR SITE");
                colaboradorVOSite.getPessoa().setDataNasc(Calendario.ontem());
                colaboradorVOSite.setEmpresa(empresaVO);
                colaboradorVOSite.setSituacao("AT");
                colaboradorVOSite.setDiaVencimento(1);
                colaboradorVOSite.setPorcComissao(1.0);
                colaboradorVOSite.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
                colaboradorVOSite = colaboradorDAO.criarOuConsultarSeExistePorNome(colaboradorVOSite, true);
                return colaboradorVOSite;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            vendasConfigDAO = null;
            colaboradorDAO = null;
        }
        return colaboradorVOSite;
    }

    private void incluirSemCommit(ContratoVO obj, ClienteVO cliente) throws Exception {

        if(obj.getConsultor() == null || obj.getConsultor().getCodigo() == 0){
            inicializarDadosConsultor(obj, cliente);
        }

        Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### INICIOU A INCLUSAO DO CONTRATO =" + obj.getCodigo() + " URL:" + getContrato().getCon().getMetaData().getURL() + " HASHCODE:" + getContrato().getCon().toString());
        //incluir o contrato
        getContrato().incluirContrato(obj);
        Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### FINALIZOU A INCLUSAO DO CONTRATO =" + obj.getCodigo() + " URL:" + getContrato().getCon().getMetaData().getURL()+ " HASHCODE:" + getContrato().getCon().toString());
        if (obj.getEmpresa().isGerarLoginAPIAoIncluirContrato()) {
            Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### INICIOU CONSULTAR CLIENTE =" + " URL:" + getCliente().getCon().getMetaData().getURL() + " HASHCODE:" + getCliente().getCon().toString());
            ClienteVO clienteVO = getCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### FINALIZOU CONSULTAR CLIENTE =" + " URL:" + getCliente().getCon().getMetaData().getURL() + " HASHCODE:" + getCliente().getCon().toString());
            getUsuarioMovel().gerarUsuarioMovelViaContextoZW(obj.getEmpresa(), clienteVO, obj.getPessoa().getEmail(), obj.getPessoa().getNome(), obj.getPessoa().getCfp());
            Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### FINALIZOU INCLUIR USUARIO MOVEL =" + " URL:" + getUsuarioMovel().getCon().getMetaData().getURL() + " HASHCODE:" + getUsuarioMovel().getCon().toString());
        }
        if (((obj.getCodigo() == null) || (obj.getCodigo() == 0)) || (!getContrato().contratoExiste(obj.getCodigo()))) {
            /*  **** Vai cai neste bloco quando a operaÃ§Ã£o "getUsuarioMovel().gerarUsuarioMovelViaContextoZW"  demorar muito, e o usuÃ¡rio pressiona F5 e conclui a operaÃ§Ã£o novamente. **********
             * Motivo da ValidaÃ§Ã£o: Como a selfit utiliza a configuraÃ§Ã£o de incluir usuario movel ao incluir um novo contrato,
             * o processo de incluir usuario movel envia email e tem sincronizaÃ§Ã£o com o Treino, desta forma, em alguns momentos essa operaÃ§Ã£o
             * pode demorar mais que o normal, e nessa demora o usuÃ¡rio pressiona a tecla F5 e clica no botÃ£o concluir novamente, e ao concluir
             * a operaÃ§Ã£o novamente, o sistema valida que jÃ¡ tem um contrato em andamento e exclui o contrato original, entÃ£o nÃ£o permitir seguir
             * seguir o fluxo, pois terÃ­amos erro de FK ao tentar incluir contratoDuracao.
             */
            throw new ConsistirException("Houve um erro durante a gravaÃ§Ã£o do contrato. Acesse a tela do Cliente e verifique se o Contrato foi criado corretamente.");
        }
        Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### INICIOU A INCLUSAO DO CONTRATO_DURACAO =" + obj.getCodigo() + " URL:" + getContratoDuracao().getCon().getMetaData().getURL()+ " HASHCODE:" + getContratoDuracao().getCon().toString());
        //incluir a duracao
        obj.getContratoDuracao().setContratoVO(obj);
        Logger.getLogger(ZillyonWebFacade.class.getName()).log(Level.SEVERE, "#### FINALIZOU A INCLUSAO DO CONTRATO_DURACAO =" + obj.getCodigo() + " URL:" + getContratoDuracao().getCon().getMetaData().getURL()+ " HASHCODE:" + getContratoDuracao().getCon().toString());
        getContratoDuracao().incluir(obj.getContratoDuracao());
        getZwFacade().adicionarAulaDesmarcadaContratoAnterior(obj, obj.getEmpresa());


        //incluir horario
        if (obj.getContratoHorario().getHorario().getCodigo() > 0) {
            getContratoHorario().incluir(obj.getContratoHorario());
        }
        //condicao de pagamento
        getContratoCondicaoPagamento().incluir(obj.getContratoCondicaoPagamento());
        //contrato composicao
        getContratoComposicao().incluirContratoComposicaos(obj.getCodigo(), obj.getContratoComposicaoVOs());
        //texto padrao
        getContratoTextoPadrao().incluir(obj.getContratoTextoPadrao());
        //modalidades
        incluirContratoModalidades(obj.getCodigo(), obj.getContratoModalidadeVOs());

        //CONTRATO DE CREDITO POR SESSÃO DEPENDE DO CONTRATO MODALIDADE
        if (obj.getPlano().isVendaCreditoTreino() && obj.getPlano().isCreditoSessao()){
            ContratoDuracaoCreditoTreino cdctDAO = new ContratoDuracaoCreditoTreino(this.con);
            obj.getContratoDuracaoCreditoTreinoVO().setDataUltimoCreditoMensal(obj.getVigenciaDe());
            cdctDAO.incluir(obj.getContratoDuracaoCreditoTreinoVO());
            cdctDAO = null;
        }

        if (!UteisValidacao.emptyNumber(obj.getPlano().getQuantidadeCompartilhamentos())) {
            ContratoDependente contratoDependenteDAO = new ContratoDependente(con);
            contratoDependenteDAO.incluir(obj);
            contratoDependenteDAO = null;
        }

        //produtos sugeridos
        getContratoPlanoProdutoSugerido().incluirContratoPlanoProdutoSugerido(obj.getCodigo(), obj.getContratoPlanoProdutoSugeridoVOs());
        //historico do contrato
        getHistoricoContrato().incluirHistoricoContratos(obj.getCodigo(), obj.getHistoricoContratoVOs());
        //periodo de acesso
        getPeriodoAcessoCliente().incluirPeriodoAcessoClienteContrato(obj, obj.getPeriodoAcessoClienteVOs());
        //gerar movprodutomodalidade
        for (Object object : obj.getMovProdutoVOs()) {
            MovProdutoVO mp = (MovProdutoVO) object;
            if (mp.getProduto().getTipoProduto().equals("PM")) {
                mp.setMovProdutoModalidades(gerarMovProdutoModalidade(MovProdutoModalidade.getValorSomaContratoModalidade(obj.getContratoModalidadeVOs()),
                        obj.getVigenciaDe(), obj.getVigenciaAte(), obj.getContratoModalidadeVOs(), mp.getTotalFinal()));
            }
        }
        atribuirCodigoOperacaoFinanceira(obj.getMovProdutoVOs(), obj);
        //produtos
        getMovProduto().incluirMovProdutoContratos(obj, obj.getMovProdutoVOs());

        //Nova regra de negocio quando o contrato estiver com valor zero entao gerar o fluxo inteiro
        //movproduto,movparcela,movpagamento,recibo tudo quitado.
        if(obj.isGerarParcelasAPartirDeMovParcelaVOs()){
            incluirParcelas(obj);
            if(obj.getRegimeRecorrencia()){
                obj.getContratoRecorrenciaVO().getContrato().setCodigo(obj.getCodigo());
                getContratoRecorrencia().incluir(obj.getContratoRecorrenciaVO());
            }
        }else if (obj.getValorFinal().doubleValue() <= 0.0) {
            setListaSelectItemMovPagamentoVOs(new ArrayList<MovPagamentoVO>());
            ReciboPagamentoVO recibo = gerarRecibo(obj);
            gerarParcelas(obj, "PG", recibo);
            gerarMovPagamento(obj, recibo, "AV");
            getMovPagamento().incluirMovPagamentos(getListaSelectItemMovPagamentoVOs());
        } else {
            gerarParcelas(obj, "EA", new ReciboPagamentoVO());
            if (obj.getPagarComBoleto()) {
                gerarMovPagamento(obj, new ReciboPagamentoVO(), "BB");
                getMovPagamento().incluirMovPagamentos(getListaSelectItemMovPagamentoVOs());
            }
        }
        if (obj.isContratoMatricula()) {
            getContrato().gerarMatriculaAlunoTurma(obj);
            if (obj.getDataAlteracaoManual() == null) {
                if (Uteis.getCompareData(obj.getVigenciaDe(), negocio.comuns.utilitarias.Calendario.hoje()) <= 0) {
                    alterarSituacaoClienteMatricula(obj);
                }
            } else {
                if (Uteis.getCompareData(obj.getVigenciaDe(), obj.getDataAlteracaoManual()) <= 0) {
                    alterarSituacaoClienteMatricula(obj);
                }
            }
        } else {
            processarDadosReMatriculaAlunoTurma(obj);
            processarContratoMatricula(obj);
        }

        AgendaVO agendaMarcadaPresenca = getAgenda().marcarComparecimentoPessoa(obj.getPessoa().getCodigo(), obj.getResponsavelContrato(), false);

        if(null != agendaMarcadaPresenca){
            agendaMarcadaPresenca.setCliente(new Cliente(this.con).consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_ABERTURAMETA));
            getFecharMeta().executarValidacaoAgendadoComprouContrato(obj, agendaMarcadaPresenca);
        }
        getFecharMeta().executarAtualizacaoMetaFaturamentoAndVenda(obj);
        getFecharMetaDetalhado().baterMetaPorFase(null, obj.getPessoa().getCodigo(), Calendario.hoje(), FasesCRMEnum.VENCIDOS.getSigla(), 0, 0, 0);
        if(obj.getEmpresa().utilizaAlgumaIntegracaoLeadsCrm()){
            try{
                verificarConversaoLead(obj);
            } catch(Exception ingnored){
            }
        }


        if (obj.getContratoAgendado()) {
            //bater meta conversÃ£o de agendados
            baterMetaConversaoAgendados(obj);
        }

        baterMetaConversao(obj, FasesCRMEnum.INDICACOES, FasesCRMEnum.CONVERSAO_INDICADOS, "Indicado");
        baterMetaConversao(obj, FasesCRMEnum.VISITANTES_ANTIGOS, FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS, "Visitante Antigo");
        baterMetaConversao(obj, FasesCRMEnum.EX_ALUNOS, FasesCRMEnum.CONVERSAO_EX_ALUNOS, "Ex-Aluno");
        baterMetaConversao(obj, FasesCRMEnum.DESISTENTES, FasesCRMEnum.CONVERSAO_DESISTENTES, "Desistente");
        baterMetaConversaoPassivo(obj);

        if (obj.isContratoRenovacao()) {
            baterMetaRenovacao(obj);
        }

        LancamentoProdutoColetivoService servicoLancProduto = new LancamentoProdutoColetivoService(this.con);
        servicoLancProduto.lancarProdutosParaContrato(obj);
        if ((obj.getNumeroCupomDesconto() != null) && (!obj.getNumeroCupomDesconto().trim().equals(""))){
            // validar o cupom novamente antes de incluir o contrato.
            CupomDescontoVO cupomDescontoVO = (new OAMDService()).validarConcederPremioCupomPortadorCupom(this.con, obj.getEmpresa().getCodEmpresaFinanceiro(), obj.getNumeroCupomDesconto(), obj);
            if (!cupomDescontoVO.getMsgValidacao().equals("")){
                List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorCupom =  (new OAMDService()).consultarPremiosPortadorCupomDesconto(cupomDescontoVO.getCampanhaCupomDescontoVO().getId(), obj.getPlano().getDescricao(), obj.getChave());
                if (listaPremioPortadorCupom.size() >0 ){
                    // retirar o desconto do cupom
                    for (Object objPlanoProdutoSugerido: obj.getPlano().getPlanoProdutoSugeridoVOs()){
                        PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO)objPlanoProdutoSugerido;
                        for(CampanhaCupomDescontoPremioPortadorVO premio: listaPremioPortadorCupom){
                            if ((premio.getTipoPremio().equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_PRODUTO)) &&
                                    (planoProdutoSugeridoVO.getProduto().getDescricao().toUpperCase().equals(premio.getDescricaoPremio().toUpperCase()))){

                                PlanoProdutoSugeridoVO planoProdutoSugeridoBd = getPlanoProdutoSugerido().consultarPorChavePrimaria(planoProdutoSugeridoVO.getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                planoProdutoSugeridoVO.setValorProduto(planoProdutoSugeridoBd.getValorProduto());
                                obj.setPremioProdutosCupomDesconto(0.0);
                            }
                        }
                    }

                }
                throw new ConsistirException(cupomDescontoVO.getMsgValidacao());
            }
        }
        //validaÃ§Ã£o da inclusÃ£o do convenio de cobranÃ§a BOLETO automaticamente.
        validarInclusaoConvenioCobrancaBoleto(obj);
        getPessoa().definirCPFComoSenhaAcesso(obj.getPessoa().getCodigo(), obj.getEmpresa().getCodigo());

        enviarMalaDiretaAssincrono(obj, false);
    }

    private void baterMetaRenovacao(ContratoVO obj) throws Exception {
        getFecharMetaDetalhado().baterMetaPorFase(null, obj.getPessoa().getCodigo(), obj.getDataLancamento(), FasesCRMEnum.RENOVACAO.getSigla(), 0, 0, 0);
    }

    private void validarCupomDesconto(String chave, ContratoVO contratoVO, String numeroCupom)throws Exception{
        contratoVO.setNumeroCupomDesconto("");
        contratoVO.setPremioProdutosCupomDesconto(0.0);
        OAMDService oamdService = new OAMDService();
        CupomDescontoVO cupomDescontoVO = oamdService.validarConcederPremioCupomPortadorCupom(this.con, contratoVO.getEmpresa().getCodEmpresaFinanceiro(), numeroCupom, null);
        if (!cupomDescontoVO.getMsgValidacao().equals("")){
            throw new ConsistirException(cupomDescontoVO.getMsgValidacao());
        }
        contratoVO.setIdCampanhaCupomDesconto(cupomDescontoVO.getCampanhaCupomDescontoVO().getId());
        boolean encontrouProduto = false;
        List<CampanhaCupomDescontoPremioPortadorVO> listaPremioPortadorCupom =  (new OAMDService()).consultarPremiosPortadorCupomDesconto(cupomDescontoVO.getCampanhaCupomDescontoVO().getId(), contratoVO.getPlano().getDescricao(), chave);
        double totalDescontoPremioProdutoCupom = 0;
        for (Object obj: contratoVO.getPlano().getPlanoProdutoSugeridoVOs()){
            PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO)obj;
            for(CampanhaCupomDescontoPremioPortadorVO premio: listaPremioPortadorCupom) {
                if (premio.getTipoPremio().equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_PRODUTO)) {
                    if (planoProdutoSugeridoVO.getProduto().getDescricao().toUpperCase().equals(premio.getDescricaoPremio().toUpperCase()) ||
                            (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("TD") && premio.getDescricaoPremio().toUpperCase().equals("ADESÃO PLANO RECORRENTE")) ||
                            (planoProdutoSugeridoVO.getProduto().getTipoProduto().equals("TA") && premio.getDescricaoPremio().toUpperCase().equals("ANUIDADE PLANO RECORRENTE"))){
                        double desconto = premio.calcularDescontoPremio(planoProdutoSugeridoVO.getValorProduto());
                        totalDescontoPremioProdutoCupom = totalDescontoPremioProdutoCupom + desconto;
                        double novoValor = br.com.pacto.priv.utils.Uteis.arredondarForcando2CasasDecimais(planoProdutoSugeridoVO.getValorProduto() - desconto);
                        planoProdutoSugeridoVO.setValorProduto(novoValor);
                        planoProdutoSugeridoVO.getProduto().setValorFinal(novoValor);
                        planoProdutoSugeridoVO.setNumeroCupomDesconto(numeroCupom);
                        planoProdutoSugeridoVO.setValorDescontoCupom(desconto);
                        encontrouProduto = true;
                    }
                }
            }
        }

        contratoVO.setPremioProdutosCupomDesconto(totalDescontoPremioProdutoCupom);
        boolean encontrouDescontoMensalidade = false;
        for (CampanhaCupomDescontoPremioPortadorVO premio: listaPremioPortadorCupom){
            if (premio.getTipoPremio().equals(CampanhaCupomDescontoPremioPortadorVO.TIPO_PREMIO_MENSALIDADE)) {
                encontrouDescontoMensalidade = true;
                break;
            }
        }

        if  ((!encontrouDescontoMensalidade) && (!encontrouProduto)){
            throw new ConsistirException("NÃ£o Ã© possÃ­vel conceder o desconto do cupom, pois nenhum produto do contrato bate com a descriÃ§Ã£o do prÃªmio do cupom. Este cupom tambÃ©m nÃ£o concede desconto em nenhuma mensalidade. " );
        }
        contratoVO.setNumeroCupomDesconto(numeroCupom);
        contratoVO.setCalcularAnuidade(false);
        selecionarCondicaoPagamento(contratoVO, null);
        oamdService = null;
    }

    private void baterMetaConversaoPassivo(ContratoVO obj) throws Exception {
        obj.setCliente(getCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

        ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Date cadastradoAte = getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(obj.getDataLancamento(), true, configuracaoSistemaCRM.getNrDiasContarResultado(), obj.getEmpresa());

        Integer codPassivo = getPassivo().consultarPorCodigoCliente(obj.getCliente().getCodigo(), false);

        if (!codPassivo.equals(0)) {
            PassivoVO passivoVO = getPassivo().consultarPorChavePrimaria(codPassivo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            boolean considerarConversao = Calendario.maior(passivoVO.getDia(), cadastradoAte);

            if (considerarConversao) {
                getPassivo().executarAlteracaoContrato(passivoVO.getCodigo(), obj.getCodigo());
            }
        }
    }

    private void baterMetaConversao(ContratoVO obj, FasesCRMEnum faseInicial, FasesCRMEnum faseConversao, String origem) throws Exception {
        //a partir daqui vamos precisar de dados do cliente
        obj.setCliente(getCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        // obter configuraÃ§oes
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Date dataInicial = getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(obj.getDataLancamento(), true, configuracaoSistemaCRM.getNrDiasContarResultado(), obj.getEmpresa());

        Date dataFinal = obj.getDataLancamento();

        FecharMetaDetalhadoVO fmdOrigem = getFecharMetaDetalhado().consultarPorFaseIntervaloDataClienteColaborador(dataInicial, dataFinal, faseInicial.getSigla(), obj.getCliente().getCodigo());

        if (fmdOrigem != null && fmdOrigem.isObteveSucesso()) {
            AberturaMetaVO aberturaMeta = getAberturaMeta().consultarAberturaPorCodigoUsuario(
                    fmdOrigem.getFecharMeta().getAberturaMetaVO().getColaboradorResponsavel().getCodigo(),
                    fmdOrigem.getFecharMeta().getAberturaMetaVO().getDia(), obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            for (FecharMetaVO fecharMeta : aberturaMeta.getFecharMetaVosVenda()) {
                if (fecharMeta.getIdentificadorMeta().equals(faseConversao.getSigla())) {
                    //incrementar a meta
                    fecharMeta.setMeta(fecharMeta.getMeta() + 1);
                    fecharMeta.calcularPorcentagem();
                    getFecharMeta().alteraSemSubordinada(fecharMeta);
                    //gerar detalhe de meta
                    FecharMetaDetalhadoVO fmd = new FecharMetaDetalhadoVO();
                    fmd.setOrigem(origem);
                    fmd.setCliente(obj.getCliente());
                    fmd.setFecharMeta(fecharMeta);
//                    fmd.setCodigoOrigem(agendamentoGerado.getCodigo());
                    fmd.setContratoVO(obj);
                    getFecharMetaDetalhado().incluir(fmd);
                    // atualizar o valor da meta atingida
                    getFecharMeta().executarAtualizacaoMetaAtingida(fmd, obj.getCodigo(), 1L, true,true);
                }
            }
        }
    }

    private void baterMetaConversaoAgendados(ContratoVO obj) throws Exception {
        //a partir daqui vamos precisar de dados do cliente
        obj.setCliente(getCliente().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        // obter configuraÃ§oes
        ConfiguracaoSistemaCRMVO configuracaoSistemaCRM = getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        // obter a data passada limite para verificar se existe agendamento para a pessoa pois se existir o contrato Ã© considerado agendado
        Date dataInicial = getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(obj.getDataLancamento(), true, configuracaoSistemaCRM.getNrDiasPosterioresAgendamento(), obj.getEmpresa());

        Date dataFinal = getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(obj.getDataLancamento(), false, configuracaoSistemaCRM.getNrDiasAnterioresAgendamento(), obj.getEmpresa());

        AberturaMetaVO aberturaMeta = getAberturaMeta().consultarAberturaPorCodigoUsuario(
                obj.getAgendamentoOrigem().getColaboradorResponsavel().getCodigo(),
                obj.getDataLancamento(),
                obj.getAgendamentoOrigem().getEmpresa(),
                Uteis.NIVELMONTARDADOS_TODOS);

        AgendaVO agendamentoGerado = getAgenda().verificaAgendamento(dataInicial, dataFinal, obj.getPessoa().getCodigo());

        for (FecharMetaVO fecharMeta : aberturaMeta.getFecharMetaVosVenda()) {
            if (fecharMeta.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                //incrementar a meta
                fecharMeta.setMeta(fecharMeta.getMeta() + 1);
                fecharMeta.calcularPorcentagem();
                getFecharMeta().alteraSemSubordinada(fecharMeta);
                //gerar detalhe de meta
                FecharMetaDetalhadoVO fmd = new FecharMetaDetalhadoVO();
                fmd.setOrigem("AGENDA");
                fmd.setCliente(obj.getCliente());
                fmd.setFecharMeta(fecharMeta);
                fmd.setCodigoOrigem(agendamentoGerado.getCodigo());
                fmd.setContratoVO(obj);
                getFecharMetaDetalhado().incluir(fmd);
                // atualizar o valor da meta atingida
                getFecharMeta().executarAtualizacaoMetaAtingida(fmd, obj.getCodigo(), 1L, true,true);
            }
        }
    }

    private void verificarConversaoLead(ContratoVO obj) throws Exception {
        ConversaoLeadVO conversaoleadVO = getConversaoLead().consultarUltimaConversao(0,obj.getPessoa().getCodigo(), obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if(!UteisValidacao.emptyNumber(conversaoleadVO.getLead().getCliente().getCodigo())){
            getFecharMetaDetalhado().baterMetaPorFase(conversaoleadVO.getLead().getCliente().getCodigo(), 0, Calendario.hoje(), FasesCRMEnum.LEADS_HOJE.getSigla() , 0, conversaoleadVO.getLead().getPassivo().getCodigo(), 0);
            getFecharMetaDetalhado().baterMetaPorFase(conversaoleadVO.getLead().getCliente().getCodigo(), 0, Calendario.hoje(), FasesCRMEnum.LEADS_ACUMULADAS.getSigla() , 0, conversaoleadVO.getLead().getPassivo().getCodigo(), 0);
        }
        if(!UteisValidacao.emptyNumber(conversaoleadVO.getLead().getPassivo().getCodigo())){
            getFecharMetaDetalhado().baterMetaPorFase(0, 0, Calendario.hoje(), FasesCRMEnum.LEADS_HOJE.getSigla() , 0, conversaoleadVO.getLead().getPassivo().getCodigo(), 0);
            getFecharMetaDetalhado().baterMetaPorFase(0, 0, Calendario.hoje(), FasesCRMEnum.LEADS_ACUMULADAS.getSigla() , 0, conversaoleadVO.getLead().getPassivo().getCodigo(), 0);
        }
        if(!UteisValidacao.emptyNumber(conversaoleadVO.getLead().getIndicado().getCodigo())){
            getFecharMetaDetalhado().baterMetaPorFase(0, 0, Calendario.hoje(), FasesCRMEnum.LEADS_HOJE.getSigla() , 0, 0, conversaoleadVO.getLead().getIndicado().getCodigo());
            getFecharMetaDetalhado().baterMetaPorFase(0, 0, Calendario.hoje(), FasesCRMEnum.LEADS_ACUMULADAS.getSigla() , 0, 0, conversaoleadVO.getLead().getIndicado().getCodigo());
        }
        if(!UteisValidacao.emptyNumber(conversaoleadVO.getCodigo()) && !UteisValidacao.emptyNumber(conversaoleadVO.getContrato().getCodigo())){
            conversaoleadVO.setContrato(obj);
            conversaoleadVO.setTratada(true);
            getConversaoLead().alterar(conversaoleadVO);
            if(conversaoleadVO.getLead().getTipo().equals(TipoLeadEnum.RDSTATION)){
                IntegracaoRDServiceImpl rdService = new IntegracaoRDServiceImpl(this.con);
                String chave = (String) JSFUtilities.getFromSession("key");
                rdService.alterarStatusLead(conversaoleadVO, true, obj.getValorFinal(), "",chave);
            }
            if(conversaoleadVO.getLead().getTipo().equals(TipoLeadEnum.BUZZLEAD)){
                IntegracaoBuzzLeadServiceImpl buzzService = new IntegracaoBuzzLeadServiceImpl(this.con);
                buzzService.alterarStatusLead(conversaoleadVO, true, obj.getValorFinal(), "");
            }
            if(conversaoleadVO.getLead().getTipo().equals(TipoLeadEnum.HUBSPOT)){
                IntegracaoHubspostLeadServiceImpl hubspost = new IntegracaoHubspostLeadServiceImpl(this.con);
                String chave = (String) JSFUtilities.getFromSession("key");
                hubspost.alterarStatusLead(conversaoleadVO, true, obj.getValorFinal(), "",chave);
            }
            if(conversaoleadVO.getLead().getTipo().equals(TipoLeadEnum.BITIRX24)){
                try {
                    String chave = (String) JSFUtilities.getFromSession("key");
                    List<ConfiguracaoEmpresaBitrixVO> listbitrix = getConfiguracaoSistemaCRM().consultarConfiguracaoEmpresaBitrix24(chave);
                    ConfiguracaoEmpresaBitrixVO config = listbitrix.get(0);
                    IntegracaoLeadGenericaServiceImpl bitrix = new IntegracaoLeadGenericaServiceImpl(this.con);

                    String jsonString = config.getAcao();
                    // Remova as aspas simples e substitua por aspas duplas para tornar a string vÃ¡lida no formato JSON
                    jsonString = jsonString.replace("'", "\"");
                    // Carrega a lista de campos customizados para recuperar o nome do objeto pelo label
                    String jsonFields = bitrix.leadFieldBitrix(config.getUrl(), "l");
                    JSONObject jsonObject = new JSONObject(jsonString);
                    JSONObject json = Uteis.extractField(jsonFields, "Status Pacto");

                    JSONObject fields =  new JSONObject();
                    JSONObject rg = Uteis.extractField(jsonFields,"RG");
                    JSONObject cpf = Uteis.extractField(jsonFields,"CPF");
                    JSONObject statuspacto = Uteis.extractField(jsonFields,"Status Pacto");

                    fields.put(rg.get("title").toString(), conversaoleadVO.getContrato().getPessoa().getRg());
                    fields.put("NAME", conversaoleadVO.getContrato().getPessoa().getNome());
                    fields.put(cpf.get("title").toString(), conversaoleadVO.getContrato().getPessoa().getCfp());
                    fields.put("STATUS_ID", "CONVERTED");
                    fields.put(statuspacto.get("title").toString(), "CLIENTE");

                    bitrix.updateStausBitrix(config.getUrl() + jsonObject.getString("updatelead"), (int) conversaoleadVO.getLead().getIdLead(), fields);

                    jsonFields = bitrix.leadFieldBitrix(config.getUrl(), "n");
                    JSONObject Plano = Uteis.extractField(jsonFields,"Plano");
                    JSONObject Modalidades = Uteis.extractField(jsonFields,"Modalidades");
                    JSONObject Unidade = Uteis.extractField(jsonFields,"Unidade (lista)");
                    JSONObject vezes_na_semana = Uteis.extractField(jsonFields,"Vezes na semana");

                    String title = "'Lead #"+ conversaoleadVO.getLead().getIdLead()+"'";
                    fields =  new JSONObject();
                    fields.put("filter",  new JSONObject( "{ \"TITLE\": "+ title +"}"));
                    fields.put("select",  new JSONArray( "[ \"ID\", \"TITLE\", \"STAGE_ID\", \"PROBABILITY\", \"OPPORTUNITY\", \"CURRENCY_ID\" ]"));
                    Object jsonNegocio = bitrix.listNegociacaoBitrix(config.getUrl() + "/crm.deal.list", fields);
                    JSONObject JSONObject = new JSONObject(jsonNegocio.toString());

                    if ( UteisValidacao.emptyNumber(Integer.parseInt(JSONObject.get("total").toString()))){
                        createOrUpdateNegocioacao(obj, conversaoleadVO, config, bitrix, Plano, Modalidades, Unidade, vezes_na_semana, "");
                    }
                    else {
                        JSONArray jsonArray = (JSONArray)JSONObject.get("result");
                        JSONObject jsOb = new JSONObject(jsonArray.get(0).toString());

                        createOrUpdateNegocioacao(obj, conversaoleadVO, config, bitrix, Plano, Modalidades, Unidade, vezes_na_semana, jsOb.get("ID").toString());
                    }
                }
                catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
    }
    private static void createOrUpdateNegocioacao(ContratoVO obj, ConversaoLeadVO conversaoleadVO, ConfiguracaoEmpresaBitrixVO config, IntegracaoLeadGenericaServiceImpl bitrix, JSONObject Plano,
                                                  JSONObject Modalidades, JSONObject Unidade, JSONObject vezes_na_semana, String id) throws Exception {
        JSONObject fields;
        fields =  new JSONObject();
        fields.put("TITLE","Lead #" +  conversaoleadVO.getLead().getIdLead());
        fields.put("TYPE_ID", "SALE");
        fields.put("STAGE_ID", "NEW");
        fields.put("LEAD_ID", conversaoleadVO.getLead().getIdLead());

        JSONArray JsArr = (JSONArray) Unidade.get("items");
        if(JsArr.length() > 0){
            for (int i = 0; i < JsArr.length(); i++) {
                JSONObject item = JsArr.getJSONObject(i);
                String ID = item.getString("ID");
                String values = item.getString("VALUE");
                if (values.toUpperCase().contains(obj.getEmpresa().getNome())) {
                    fields.put(Unidade.get("title").toString(), ID);
                }
            }
        }
        StringBuilder modalidadesPlano = new StringBuilder();
        StringBuilder vezes = new StringBuilder();
        obj.getPlano().getPlanoModalidadeVOs().forEach(item -> modalidadesPlano.append( "-" + item .getModalidade().getNome()) ) ;
        obj.getPlano().getPlanoModalidadeVOs().forEach(item -> vezes.append( " " + item .getModalidade().getNrVezes() + "x " ) ) ;
        fields.put(Modalidades.get("title").toString(), modalidadesPlano.toString());
        fields.put(Plano.get("title").toString(), obj.getPlano().getDescricao());
        fields.put(vezes_na_semana.get("title").toString(), vezes.toString());
        if(UteisValidacao.emptyString(id)){
            bitrix.createNegociacaoBitrix(config.getUrl() + "/crm.deal.add", fields,id);
        }
        else{
            bitrix.createNegociacaoBitrix(config.getUrl() + "/crm.deal.update", fields, id);
        }

    }
    private void processarContratoMatricula(ContratoVO obj) throws Exception {
        try {
            ContratoVO contrato = new ContratoVO();
            if (obj.isContratoRenovacao()) {
                contrato = getContrato().consultarPorChavePrimaria(obj.getContratoBaseadoRenovacao().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS);
                contrato.setContratoResponsavelRenovacaoMatricula(obj.getCodigo());
                contrato.setDataRenovarRealizada(negocio.comuns.utilitarias.Calendario.hoje());
                contrato.obterSituacaoRenovacao(obj.getDiasRestanteContratoOrigemRenovacao());
                if (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(), contrato.getVigenciaAteAjustada()) > 0) {
                    contrato.setSituacao("IN");
                }
                if (Uteis.getCompareData(obj.getVigenciaDe(), negocio.comuns.utilitarias.Calendario.hoje()) <= 0) {
                    alterarSituacaoClienteRenovacaoOuRematricula(obj, contrato);
                }

            } else {
                contrato = getContrato().consultarPorChavePrimaria(obj.getContratoBaseadoRematricula().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS);
                contrato.setContratoResponsavelRematriculaMatricula(obj.getCodigo());
                contrato.setDataRematriculaRealizada(negocio.comuns.utilitarias.Calendario.hoje());
                if (Uteis.getCompareData(obj.getVigenciaDe(), negocio.comuns.utilitarias.Calendario.hoje()) <= 0) {
                    alterarSituacaoClienteRenovacaoOuRematricula(obj, contrato);
                }
            }
            getContrato().alterarContratoResponsavelRenovacaoMatriculaSemCommit(contrato);
        } catch (Exception e) {
            throw e;
        }
    }

    private void alterarSituacaoClienteRenovacaoOuRematricula(ContratoVO obj, ContratoVO contratoResponsavel) throws Exception {
        try {
            ClienteVO cliente = getCliente().consultarPorCodigoPessoa(
                    obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            cliente.setSituacao("AT");
            getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
        } catch (Exception e) {
            throw e;
        }

    }

    private void processarDadosReMatriculaAlunoTurma(ContratoVO obj) throws Exception {
        try {
            if ((obj.getPlano().isVendaCreditoTreino() && obj.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)){
                Integer saldo = ControleCreditoTreino.consultarSaldoCredito(con, obj.getCodigo());
                obj.gerarVigenciaMatriculaPlanoCreditoTreino(obj.getVigenciaDe(), saldo);
            }

            List listaMatriculaContrato = new ArrayList();
            Integer contratoAnterior = UteisValidacao.emptyNumber(obj.getContratoBaseadoRenovacao()) ? obj.getContratoBaseadoRematricula() : obj.getContratoBaseadoRenovacao();
            listaMatriculaContrato = getMatriculaAlunoHorarioTurma().consultarPorCodigoContrato(contratoAnterior, obj.getVigenciaDe(),obj.getVigenciaDe(),  Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, null);
            Iterator j = listaMatriculaContrato.iterator();
            while (j.hasNext()) {
                MatriculaAlunoHorarioTurmaVO matriculaAluno = (MatriculaAlunoHorarioTurmaVO) j.next();
                if (Calendario.maiorOuIgual(matriculaAluno.getDataFim(), obj.getVigenciaDe())) { //ajusta a data da matricula anterior do aluno, para que ele nÃ£o fique em duas turmas durante o periodo de vencido
                    matriculaAluno.setDataFim(Uteis.somarDias(obj.getVigenciaDe(), -1));
                    if (matriculaAluno.getDataInicio().compareTo(matriculaAluno.getDataFim()) > 0) {
                        getMatriculaAlunoHorarioTurma().excluirSemCommit(matriculaAluno);
                    } else {
                        getMatriculaAlunoHorarioTurma().alterarSemCommit(matriculaAluno);
                    }
                }
            }
            Iterator i = obj.getContratoModalidadeVOs().iterator();
            while (i.hasNext()) {
                ContratoModalidadeVO cont = (ContratoModalidadeVO) i.next();
                if (cont.getModalidade().getModalidadeEscolhida() && cont.getModalidade().getUtilizarTurma()) {
                    validarQuaisTurmasForamEscolhidas(obj, cont, listaMatriculaContrato);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    private void validarQuaisTurmasForamEscolhidas(ContratoVO contratoVO, ContratoModalidadeVO cont, List listaMatriculaContrato) throws Exception {
        Iterator i = cont.getContratoModalidadeTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeTurmaVO contModTurma = (ContratoModalidadeTurmaVO) i.next();
            if (contModTurma.getTurma().getTurmaEscolhida()) {
                validarQuaisHorarioTurmaForamEscolhidos(contratoVO, cont, contModTurma, listaMatriculaContrato);
            }
        }
    }

    private void validarQuaisHorarioTurmaForamEscolhidos(ContratoVO contratoVO, ContratoModalidadeVO cont, ContratoModalidadeTurmaVO contModTurma, List listaMatriculaContrato) throws Exception {
        Iterator i = contModTurma.getContratoModalidadeHorarioTurmaVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeHorarioTurmaVO contModHorarioTurma = (ContratoModalidadeHorarioTurmaVO) i.next();
            if (contModHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()) {
                processarDadosListaMatriculaContratoSaoIguaisTurmaEHorarioTurmaEscolhidos(contratoVO, cont, contModHorarioTurma, contModTurma, listaMatriculaContrato);
            }
        }
    }

    private void processarDadosListaMatriculaContratoSaoIguaisTurmaEHorarioTurmaEscolhidos(ContratoVO contratoVO, ContratoModalidadeVO cont, ContratoModalidadeHorarioTurmaVO contModHorarioTurma, ContratoModalidadeTurmaVO contModTurma, List listaMatriculaContrato) throws Exception {
        boolean existeMatricula = false;
        Iterator i = listaMatriculaContrato.iterator();
        while (i.hasNext()) {
            MatriculaAlunoHorarioTurmaVO matriculaAluno = (MatriculaAlunoHorarioTurmaVO) i.next();
            if (matriculaAluno.getHorarioTurma().getTurma().equals(contModTurma.getTurma().getCodigo().intValue()) && matriculaAluno.getHorarioTurma().getCodigo().intValue() == contModHorarioTurma.getHorarioTurma().getCodigo().intValue()) {
                // dados principais
                matriculaAluno.setEmpresa(contratoVO.getEmpresa().getCodigo());
                matriculaAluno.setPessoa(contratoVO.getPessoa());
                matriculaAluno.setContrato(contratoVO);
                matriculaAluno.setDataInicio(contratoVO.getVigenciaDe());
                if (contratoVO.getVigenciaTurmaCreditoTreinoAte() != null){
                    matriculaAluno.setDataFim(contratoVO.getVigenciaTurmaCreditoTreinoAte());
                }else{
                    matriculaAluno.setDataFim(Uteis.somarDias(contratoVO.getVigenciaAte(), contratoVO.getEmpresa().getToleranciaOcupacaoTurma()));
                }

                matriculaAluno.setHorarioTurma(contModHorarioTurma.getHorarioTurma());
                getMatriculaAlunoHorarioTurma().incluirSemComit(matriculaAluno);
                existeMatricula = true;
                break;
            }
        }
        if (!existeMatricula) {
            gravarMatriculaAlunoTurma(contratoVO, cont, contModTurma, contModHorarioTurma);
        }
    }

    private void gravarMatriculaAlunoTurma(ContratoVO contrato, ContratoModalidadeVO cont, ContratoModalidadeTurmaVO modTurma, ContratoModalidadeHorarioTurmaVO modalidadeHorarioTurma) throws Exception {
        if (modalidadeHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida().booleanValue()
                && (!modTurma.getTurma().getBloquearMatriculasAcimaLimite() || contrato.getEmpresa().isPermiteRenovarContratosEmTurmasLotadas()
                || modalidadeHorarioTurma.getHorarioTurma().getNrAlunoMatriculado() < modalidadeHorarioTurma.getHorarioTurma().getNrMaximoAluno()
        )) {
            matricularNaTurma(contrato, modalidadeHorarioTurma);
        }else if (modalidadeHorarioTurma.getHorarioTurma().getHorarioTurmaEscolhida()
                && modalidadeHorarioTurma.getHorarioTurma().getNrAlunoMatriculado() >= modalidadeHorarioTurma.getHorarioTurma().getNrMaximoAluno()) {
            throw new Exception("NÃ£o foi possÃ­vel matricular o  aluno nesse HorÃ¡rio ("
                    + modalidadeHorarioTurma.getHorarioTurma().getIdentificadorTurma() + " - "
                    + modalidadeHorarioTurma.getHorarioTurma().getDiaSemana_Apresentar() + " "
                    + modalidadeHorarioTurma.getHorarioTurma().getHoraInicial() + " - "
                    + modalidadeHorarioTurma.getHorarioTurma().getHoraFinal()
                    + "), pois o mesmo estÃ¡ cheio.");
        }
    }

    private void matricularNaTurma(ContratoVO contrato, ContratoModalidadeHorarioTurmaVO modalidadeHorarioTurma) throws Exception {
        MatriculaAlunoHorarioTurmaVO mat = new MatriculaAlunoHorarioTurmaVO();
        // dados principais
        mat.setEmpresa(contrato.getEmpresa().getCodigo());
        mat.setPessoa(contrato.getPessoa());
        mat.setContrato(contrato);
        mat.setDataInicio(contrato.getVigenciaDe());
        if (contrato.getVigenciaTurmaCreditoTreinoAte() != null){
            mat.setDataFim(contrato.getVigenciaTurmaCreditoTreinoAte());
        }else{
            mat.setDataFim(Uteis.somarDias(contrato.getVigenciaAte(), contrato.getEmpresa().getToleranciaOcupacaoTurma()));
        }
        mat.setHorarioTurma(modalidadeHorarioTurma.getHorarioTurma());
        getMatriculaAlunoHorarioTurma().incluirSemComit(mat);
    }

    private void alterarSituacaoClienteMatricula(ContratoVO obj) throws Exception {
        try {
            ClienteVO cliente = getCliente().consultarPorCodigoPessoa(
                    obj.getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            cliente.setSituacao("AT");
            getCliente().alterarSituacaoClienteSemPessoaCommit(cliente);
        } catch (Exception e) {
            throw e;
        }
    }

    private void inicializarPagamentoMovParcelaVO(MovPagamentoVO movPagamento, MovParcelaVO movParcela, ReciboPagamentoVO recibo) {
        PagamentoMovParcelaVO pagamentoMovParcela = new PagamentoMovParcelaVO();
        pagamentoMovParcela.setMovParcela(movParcela);
        pagamentoMovParcela.setValorPago(movParcela.getValorParcela());
        if (recibo.getCodigo().intValue() != 0) {
            pagamentoMovParcela.setReciboPagamento(recibo);
        }
        movPagamento.getPagamentoMovParcelaVOs().add(pagamentoMovParcela);
    }

    private void inicializarMovPagamentoVOAvista(FormaPagamentoVO formaPagamento, ContratoVO obj, ReciboPagamentoVO recibo) {
        MovPagamentoVO movPagamento = new MovPagamentoVO();
        movPagamento.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
        movPagamento.setFormaPagamento(formaPagamento);
        movPagamento.setNomePagador(obj.getPessoa().getNome());
        movPagamento.setMovPagamentoEscolhida(true);
        movPagamento.setPessoa(obj.getPessoa());
        movPagamento.setValor(0.0);
        movPagamento.setDataPagamento(negocio.comuns.utilitarias.Calendario.hoje());
        movPagamento.setResponsavelPagamento(obj.getResponsavelContrato());
        movPagamento.setEmpresa(obj.getEmpresa());
        if (recibo.getCodigo().intValue() != 0) {
            movPagamento.setReciboPagamento(recibo);
        }
        Iterator<MovParcelaVO> i = obj.getMovParcelaVOs().iterator();
        while (i.hasNext()) {
            MovParcelaVO movParcela = i.next();
            inicializarPagamentoMovParcelaVO(movPagamento, movParcela, recibo);
        }
        getListaSelectItemMovPagamentoVOs().add(movPagamento);
    }

    private void inicializarMovPagamentoVOBoleto(FormaPagamentoVO formaPagamento, ContratoVO obj, ReciboPagamentoVO recibo) {
        Iterator<MovParcelaVO> i = obj.getMovParcelaVOs().iterator();
        while (i.hasNext()) {
            MovParcelaVO movParcela = i.next();
            MovPagamentoVO movPagamento = new MovPagamentoVO();
            movPagamento.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
            movPagamento.setFormaPagamento(formaPagamento);
            movPagamento.setNomePagador(obj.getPessoa().getNome());
            movPagamento.setMovPagamentoEscolhida(true);
            movPagamento.setPessoa(obj.getPessoa());
            movPagamento.setValor(movParcela.getValorParcela());
            movPagamento.setDataPagamento(movParcela.getDataVencimento());
            movPagamento.setResponsavelPagamento(obj.getResponsavelContrato());
            movPagamento.setEmpresa(obj.getEmpresa());
            if (recibo.getCodigo().intValue() != 0) {
                movPagamento.setReciboPagamento(recibo);
            }
            inicializarPagamentoMovParcelaVO(movPagamento, movParcela, recibo);
            getListaSelectItemMovPagamentoVOs().add(movPagamento);
        }
    }

    private void gerarMovPagamento(ContratoVO obj, ReciboPagamentoVO recibo, String formaPagamento) throws Exception {
        if (formaPagamento.equals("AV")) {
            List listaSelectItemFormaPagamento = (getFormaPagamento().consultarPorTipoFormaPagamento("AV", true, false, false, Uteis.NIVELMONTARDADOS_TODOS));
            Iterator i = listaSelectItemFormaPagamento.iterator();
            while (i.hasNext()) {
                FormaPagamentoVO avista = (FormaPagamentoVO) i.next();
                if (avista.getTipoFormaPagamento().equals("AV")) {
                    inicializarMovPagamentoVOAvista(avista, obj, recibo);
                }
            }
        } else {
            List listaSelectItemFormaPagamento = getFormaPagamento().consultarPorTipoFormaPagamento("BB", true, false, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = listaSelectItemFormaPagamento.iterator();
            while (i.hasNext()) {
                FormaPagamentoVO boleto = (FormaPagamentoVO) i.next();
                if (boleto.getTipoFormaPagamento().equals("BB")) {
                    inicializarMovPagamentoVOBoleto(boleto, obj, recibo);
                }
            }
        }
    }

    private void gerarParcelaAnuidade(ContratoVO obj, ReciboPagamentoVO recibo) throws Exception {

        if (!obj.isGerarAnuidade()){
            return ;
        }
        PlanoRecorrenciaVO planoRecorrenciaVO = obj.getPlano().getPlanoRecorrencia();
        Integer anoVigenciaDe = Uteis.getAnoData(obj.getVigenciaDe());
        Integer diaAnuidade = planoRecorrenciaVO.getDiaAnuidade();
        Integer mesAnuidade = planoRecorrenciaVO.getMesAnuidade();

        if (obj.getPlano().getPlanoRecorrencia().isAnuidadeNaParcela()) {
            diaAnuidade = Uteis.getDiaMesData(obj.getVigenciaAte());
            mesAnuidade = Uteis.getMesData(obj.getVigenciaAte());
        }

        Calendar dataAnuidade = Calendario.getInstance(anoVigenciaDe, mesAnuidade, diaAnuidade);
        Date dataParcela = dataAnuidade.getTime();

        ProdutoVO produtoVO = null;
        MovProdutoVO movProdutoAnuidade = null;
        boolean existeAnuidade = false;
        String numeroCupomAnuidade = null;
        for (Object object : obj.getMovProdutoVOs()) {
            movProdutoAnuidade = (MovProdutoVO) object;
            if (movProdutoAnuidade.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())) {
                produtoVO = movProdutoAnuidade.getProduto();
                movProduto.excluirSemCommit(movProdutoAnuidade);
                existeAnuidade = true;
                break;
            }
        }
        for(Object object : obj.getPlano().getPlanoProdutoSugeridoVOs()){
            PlanoProdutoSugeridoVO prodSugerido = (PlanoProdutoSugeridoVO) object;
            if(prodSugerido.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo()))
                numeroCupomAnuidade = prodSugerido.getNumeroCupomDesconto();
        }

        if(existeAnuidade){
            obj.getMovProdutoVOs().remove(movProdutoAnuidade);
        }

        Date dataInicioContrato = obj.getVigenciaDe();
        Date dataFinalContrato = obj.getVigenciaAteAjustada();
        int anoParcelaAnuidade = Calendario.getInstance(dataInicioContrato).get(Calendar.YEAR);;
        int anoFimContrato = Calendario.getInstance(dataFinalContrato).get(Calendar.YEAR);
        if (obj.getPlano().getPlanoRecorrencia().isAnuidadeNaParcela() && obj.getPlano().getPlanoRecorrencia().getDuracaoPlano() == 12) {
            anoFimContrato = anoParcelaAnuidade;
            dataParcela = obj.getVigenciaDe();
        }
        while (anoParcelaAnuidade <=  anoFimContrato) {
            if (Calendario.maiorOuIgual(dataParcela, obj.getVigenciaDe()) && Calendario.menorOuIgual(dataParcela, obj.getVigenciaAte())) {
                Double valorAnuidade = obj.getPlano().getPlanoRecorrencia().getValorAnuidade();
                PlanoEmpresaVO planoEmpresaVO = obj.getPlano().obterPlanoEmpresa(obj.getEmpresa().getCodigo());
                if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getValorAnuidade())) {
                    valorAnuidade =planoEmpresaVO.getValorAnuidade();
                }

                Double valorParcela = obj.getValorPorAnuidade();
                Double valorDesconto = Uteis.arredondarForcando2CasasDecimais(valorAnuidade - valorParcela);

                MovParcelaVO movParc = new MovParcelaVO();
                movParc.setValorBaseCalculo(valorParcela);
                movParc.setValorParcela(movParc.getValorBaseCalculo());

                movParc.setDescricao("ANUIDADE PLANO RECORRENTE - " + Uteis.getAnoData(dataParcela));
                movParc.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());

                if(obj.getDataAnuidade() != null){
                    movParc.setDataVencimento(obj.getDataAnuidade());
                }else{
                    movParc.setDataVencimento(dataParcela);
                }

                if(Calendario.menor(movParc.getDataVencimento(), Calendario.hoje())){
                    movParc.setDataVencimento(Calendario.hoje());
                }

                movParc.setPercentualJuro(obj.getEmpresa().getJuroParcela());
                movParc.setPercentualMulta(obj.getEmpresa().getMulta());
                movParc.setResponsavel(obj.getResponsavelContrato());
                movParc.setSituacao(UteisValidacao.emptyNumber(movParc.getValorParcela()) ? "PG" : "EA");


                if (produtoVO == null) {
                    return;
                }
                //gerar relacionamento produto --- parcela
                MovProdutoVO movProdutoVO = gerarMovProdutoAnuidade(movParc, obj, obj.getResponsavelContrato(), valorAnuidade, valorDesconto, valorParcela, Calendario.hoje(), obj.getEmpresa().getCodigo(), produtoVO);
                movProdutoVO.setNumeroCupomDesconto(numeroCupomAnuidade);
                movProduto.incluirSemCommit(movProdutoVO);

                movProdutoPago(movParc, movProdutoVO, recibo);

                //informar que esta foi a entrada
                movParc.setContrato(obj);
                movParc.setEmpresa(obj.getEmpresa());
                movParc.setPessoa(obj.getPessoa());
                movParc.setCupomDesconto(numeroCupomAnuidade);
                incluirMovParcelaSemCommit(movParc);
                obj.getMovParcelaVOs().add(movParc);

                gerarMovParcelaCupomDesconto(numeroCupomAnuidade, movParc, valorDesconto);

            }
            dataParcela = Uteis.somarCampoData(dataParcela, Calendar.YEAR, 1);
            anoParcelaAnuidade++;
        }
    }

    private void movProdutoPago(MovParcelaVO movParcela, MovProdutoVO movProduto, ReciboPagamentoVO recibo, Double valorParcela) throws Exception {
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProduto.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProduto);
        Double valor = valorParcela == null ? Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal()) : valorParcela;
        movProdutoParcela.setValorPago(valor);
        if (recibo.getCodigo() != 0) {
            movProdutoParcela.setReciboPagamento(recibo);
        }
        movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
        movProduto.getMovProdutoParcelaVOs().add(movProdutoParcela);
        movProduto.setQuitado(true);
        if(UteisValidacao.emptyNumber(movParcela.getValorParcela())){
            movProduto.setSituacao("PG");
        }
        Double valorBase = Uteis.arredondarForcando2CasasDecimais(movParcela.getValorBaseCalculo());
        movParcela.setValorBaseCalculo(valorBase - valor);
    }

    private void gerarMovParcelaCupomDesconto(String numeroCupom, MovParcelaVO movParcelaVO, Double valorDesconto) {
        try {
            if (!UteisValidacao.emptyString(numeroCupom)) {
                MovParcelaCupomDescontoVO movParcelaCupomDescontoVO = new MovParcelaCupomDescontoVO();
                movParcelaCupomDescontoVO.setMovParcelaVO(movParcelaVO);
                movParcelaCupomDescontoVO.setCupomDesconto(numeroCupom);
                String usuario = movParcelaVO.getResponsavel().getUsername();
                String usuarioOAMD = movParcelaVO.getResponsavel().getUserOamd();
                usuario = ((usuarioOAMD != null) && (!usuarioOAMD.trim().equals(""))) ? usuario + " - UserOAMD:" + usuarioOAMD : usuario;
                movParcelaCupomDescontoVO.setResponsavelDesconto(usuario);
                movParcelaCupomDescontoVO.setValorDesconto(valorDesconto);
                movParcelaCupomDescontoVO.setDescontoContratoNovo(true);
                MovParcelaCupomDesconto movParcelaCupomDescontoDAO = new MovParcelaCupomDesconto(this.con);
                movParcelaCupomDescontoDAO.incluir(movParcelaCupomDescontoVO);
                movParcelaCupomDescontoDAO = null;
            }
        } catch (Exception ex) {
        }
    }

    private void movProdutoPago(MovParcelaVO movParcela, MovProdutoVO movProduto, ReciboPagamentoVO recibo) throws Exception {
        movProdutoPago( movParcela, movProduto, recibo, null);
    }

    private MovProdutoVO gerarMovProdutoAnuidade(MovParcelaVO parcelaVO, ContratoVO contratoVO, UsuarioVO usuarioVO,
                                                 Double valorAnuidade, Double valorDesconto, Double totalFinal, Date dataRegistro, Integer codEmpresa, ProdutoVO produtoAnuidade) throws Exception {
        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(parcelaVO.getDataVencimento()));
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(parcelaVO.getDataVencimento()));
        movProdutoVO.setDataLancamento(Calendario.getDataComHoraZerada(dataRegistro));
        movProdutoVO.setContrato(contratoVO);
        movProdutoVO.setProduto(produtoAnuidade);
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(produtoAnuidade.getDescricao());
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setResponsavelLancamento(usuarioVO);
        movProdutoVO.setPrecoUnitario(valorAnuidade);
        movProdutoVO.setValorDesconto(valorDesconto);
        movProdutoVO.setTotalFinal(totalFinal);
        movProdutoVO.getEmpresa().setCodigo(codEmpresa);
        movProdutoVO.setPessoa(contratoVO.getPessoa());
        if (valorAnuidade == 0) {
            movProdutoVO.setQuitado(true);
            movProdutoVO.setSituacao("PG");
        } else {
            movProdutoVO.setQuitado(false);
            movProdutoVO.setSituacao("EA");
        }
        return movProdutoVO;
    }

    private void gerarParcelaAdesao(ContratoVO obj, ReciboPagamentoVO recibo, Date data, Date dataAtual) throws Exception {
        if (!obj.isGerarAdesao()){
            return;
        }
        // AdesÃ£o sempre Ã© cobrada no ato.
        if (obj.isForcarDatasParcelasDiferentes() && obj.getPlano().getSite()) {
            data = Calendario.hoje();
        }

        Date dataParcela = new Date(data.getTime());
        int mes = 1;
        Integer nrParcelasAdesao = UteisValidacao.emptyNumber(obj.getNrParcelasAdesao()) ? 1 : obj.getNrParcelasAdesao();
        Double valorParcela = (obj.getSomaAdesao() / nrParcelasAdesao);
        Double valorPrimeiraParcela = ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasAdesao) == obj.getSomaAdesao())
                ? valorParcela : ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasAdesao) > obj.getSomaAdesao())
                ? (valorParcela - ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasAdesao) - obj.getSomaAdesao()))
                : (valorParcela + (obj.getSomaAdesao() - (Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasAdesao)));
        Double valorDescontoParcela = (obj.getSomaDescontoAdesao() / nrParcelasAdesao);

        String numeroCupom = null;
        for(Object object : obj.getPlano().getPlanoProdutoSugeridoVOs()){
            PlanoProdutoSugeridoVO prodSugerido = (PlanoProdutoSugeridoVO) object;
            if(prodSugerido.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo()))
                numeroCupom = prodSugerido.getNumeroCupomDesconto();
        }

        for (int i = 1; i <= nrParcelasAdesao; i++) {
            MovParcelaVO movParc = new MovParcelaVO();
            if (obj.getRegimeRecorrencia() && i != 1 && obj.isForcarDatasParcelasDiferentes()) {
                Calendar c = Calendario.getInstance(dataParcela);
                c.set(Calendar.DATE, obj.getDiaVencimentoCartaoRecorrencia());
                dataParcela = c.getTime();
            }

            movParc.setValorBaseCalculo(i == 1 ? valorPrimeiraParcela : valorParcela);
            movParc.setValorParcela(movParc.getValorBaseCalculo());

            movParc.setDescricao("ADESÃO PARCELA " + i);
            movParc.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            if (obj.getDataAlteracaoManual() != null) {
                movParc.setDataAlteracaoManual(Calendario.hojeSemRequest());
            }
            movParc.setDataVencimento(dataParcela);
            movParc.setPercentualJuro(obj.getEmpresa().getJuroParcela());
            movParc.setPercentualMulta(obj.getEmpresa().getMulta());
            movParc.setResponsavel(obj.getResponsavelContrato());
            movParc.setSituacao(UteisValidacao.emptyNumber(movParc.getValorParcela()) ? "PG" : "EA");

            //gerar relacionamento produto --- parcela
            parcelaTaxaAdesao(obj, movParc, recibo, numeroCupom, valorDescontoParcela);
            //informar que esta foi a entrada
            movParc.setContrato(obj);
            movParc.setEmpresa(obj.getEmpresa());
            movParc.setPessoa(obj.getPessoa());
            movParc.setCupomDesconto(numeroCupom);
            incluirMovParcelaSemCommit(movParc);
            obj.getMovParcelaVOs().add(movParc);

            gerarMovParcelaCupomDesconto(numeroCupom, movParc, valorDescontoParcela);

            if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                dataParcela = Uteis.obterDataFuturaParcela(dataParcela, (mes));
            } else {
                dataParcela = Uteis.obterDataFutura2(dataParcela, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
            }
        }
    }

    private void parcelaTaxaAdesao(ContratoVO obj, MovParcelaVO movParcela, ReciboPagamentoVO recibo, String numeroCupom, Double valorDescontoParcela) throws Exception {
        if (obj.getConvenioDesconto().getCodigo() != 0) {
            movParcela.setUtilizaConvenio(true);
        }
        PlanoEmpresaVO planoEmpresaVO = obj.getPlano().obterPlanoEmpresa(obj.getEmpresa().getCodigo());

        Iterator<MovProdutoVO> j = obj.getMovProdutoVOs().iterator();
        while (j.hasNext()) {
            MovProdutoVO movProduto = j.next();
            if (movProduto.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo())) {
                if(!UteisValidacao.emptyString(numeroCupom)){
                    movProduto.setNumeroCupomDesconto(numeroCupom);
                    movProduto.setValorDesconto(valorDescontoParcela);
                    movProduto.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(obj.getPlano().getPlanoRecorrencia().getTaxaAdesao()));
                    if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getTaxaAdesao())) {
                        movProduto.setPrecoUnitario(planoEmpresaVO.getTaxaAdesao());
                    }
                }
                if (movParcela.getValorBaseCalculo() >= movProduto.getTotalFinal()) {
                    movProdutoPago(movParcela, movProduto, recibo);
                } else if (movParcela.getValorBaseCalculo().intValue() > 0) {
                    movProdutoNaoPago(movParcela, movProduto, recibo);
                }
                movProduto.setQuitado(true);
            }
        }
    }

    private void movProdutoNaoPago(MovParcelaVO movParcela, MovProdutoVO movProduto, ReciboPagamentoVO recibo) throws Exception {
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProduto.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProduto);
        Double valor = Uteis.arredondarForcando2CasasDecimais(movParcela.getValorBaseCalculo());
        movProdutoParcela.setValorPago(valor);
        if (recibo.getCodigo() != 0) {
            movProdutoParcela.setReciboPagamento(recibo);
        }
        movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
        movProduto.getMovProdutoParcelaVOs().add(movProdutoParcela);
        movProduto.setQuitado(false);
        movParcela.setValorBaseCalculo(0.0);
    }

    private void gerarParcelaMatriculaSeparada(ContratoVO obj, ReciboPagamentoVO recibo, Date data, Date dataAtual) throws Exception {
        // Matricula sempre Ã© cobrada no ato.
        if (obj.isForcarDatasParcelasDiferentes() && obj.getPlano().getSite()) {
            data = Calendario.hoje();
        }
        Date dataParcela = new Date(data.getTime());
        int mes = 1;
        Integer nrParcelasMatricula = UteisValidacao.emptyNumber(obj.getNrVezesParcelarMatricula()) ? 1 : obj.getNrVezesParcelarMatricula();
        Double valorParcela = (obj.getValorMatricula() / nrParcelasMatricula);
        Double valorPrimeiraParcela = ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasMatricula) == obj.getValorMatricula())
                ? valorParcela : ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasMatricula) > obj.getValorMatricula())
                ? (valorParcela - ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasMatricula) - obj.getValorMatricula()))
                : (valorParcela + ( obj.getValorMatricula() - (Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasMatricula)));

        for (int i = 1; i <= nrParcelasMatricula; i++) {
            MovParcelaVO movParc = new MovParcelaVO();

            movParc.setValorBaseCalculo(i == 1 ? valorPrimeiraParcela : valorParcela);
            movParc.setValorParcela(movParc.getValorBaseCalculo());

            movParc.setDescricao("MATRÃCULA PARCELA " + i);
            movParc.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            if (obj.getDataAlteracaoManual() != null) {
                movParc.setDataAlteracaoManual(Calendario.hojeSemRequest());
            }
            movParc.setDataVencimento(dataParcela);
            movParc.setPercentualJuro(obj.getEmpresa().getJuroParcela());
            movParc.setPercentualMulta(obj.getEmpresa().getMulta());
            movParc.setResponsavel(obj.getResponsavelContrato());
            movParc.setSituacao(UteisValidacao.emptyNumber(movParc.getValorParcela()) ? "PG" : "EA");

            //gerar relacionamento produto --- parcela
            parcelaMatricula(obj, movParc, recibo);
            //informar que esta foi a entrada
            movParc.setContrato(obj);
            movParc.setEmpresa(obj.getEmpresa());
            movParc.setPessoa(obj.getPessoa());
            incluirMovParcelaSemCommit(movParc);
            obj.getMovParcelaVOs().add(movParc);

            if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                dataParcela = Uteis.obterDataFuturaParcela(dataParcela, (mes));
            } else {
                dataParcela = Uteis.obterDataFutura2(dataParcela, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
            }
        }
    }

    private void parcelaMatricula(ContratoVO obj, MovParcelaVO movParcela, ReciboPagamentoVO recibo) throws Exception {
        if (obj.getConvenioDesconto().getCodigo().intValue() != 0) {
            movParcela.setUtilizaConvenio(true);
        }
        Iterator<MovProdutoVO> j = obj.getMovProdutoVOs().iterator();
        while (j.hasNext()) {
            MovProdutoVO movProduto = j.next();
            if (movProduto.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo())) {
                /*if (movParcela.getValorBaseCalculo() >= movProduto.getTotalFinal()) {
                    movProdutoPago(movParcela, movProduto, recibo);
                } else if (movParcela.getValorBaseCalculo().intValue() > 0) {
                    movProdutoNaoPago(movParcela, movProduto, recibo);
                }
                movProduto.setQuitado(true);*/
                movProdutoNaoPago(movParcela, movProduto, recibo);
            }
        }
    }

    private void selecionarTodosProdutosSugeridos(ContratoVO contratoVO){
        Double sum = 0.0;
        boolean jaAdicionouAdesao = false;
        boolean jaAdicionouAnuidade = false;
        boolean jaAdicionouMatricula = false;
        boolean jaAdicionouRematricula = false;
        boolean jaAdicionouRenovacao = false;

        for(PlanoProdutoSugeridoVO produto : (List<PlanoProdutoSugeridoVO>)contratoVO.getPlano().getPlanoProdutoSugeridoVOs()){
            if (validarProdutoJaAdicionado(jaAdicionouAdesao, jaAdicionouAnuidade, jaAdicionouMatricula, jaAdicionouRematricula, jaAdicionouRenovacao, produto))
                continue;

            if (!produto.getObrigatorio()) {
                produto.setQuantidade(0);
                produto.setProdutoSugeridoEscolhida(false);
                contratoVO.excluirObjContratoPlanoProdutoSugeridoVOs(produto.getProduto().getCodigo());
            }

            sum += produto.getValorProdutoQtd() - produto.getValorDesconto();

            if(produto.getProduto().getDescricao().toLowerCase().contains("anuidade")){
                jaAdicionouAnuidade = true;
            }else if((produto.getProduto().getDescricao().toLowerCase().contains("adesÃ£o") || produto.getProduto().getDescricao().toLowerCase().contains("adesao"))){
                jaAdicionouAdesao = true;
            }else if((produto.getProduto().getDescricao().toLowerCase().contains("matricula") || produto.getProduto().getDescricao().toLowerCase().contains("matrÃ­cula"))){
                jaAdicionouMatricula = true;
            }else if((produto.getProduto().getDescricao().toLowerCase().contains("rematricula") || produto.getProduto().getDescricao().toLowerCase().contains("rematrÃ­cula"))){
                jaAdicionouRematricula = true;
            }else if((produto.getProduto().getDescricao().toLowerCase().contains("renovacao") || produto.getProduto().getDescricao().toLowerCase().contains("renovaÃ§Ã£o"))){
                jaAdicionouRenovacao = true;
            }
        }
        contratoVO.setTotalFinalProdutos(sum);
    }

    private boolean validarProdutoJaAdicionado(boolean jaAdicionouAdesao, boolean jaAdicionouAnuidade, boolean jaAdicionouMatricula, boolean jaAdicionouRematricula, boolean jaAdicionouRenovacao, PlanoProdutoSugeridoVO produto) {
        if(produto.getProduto().getDescricao().toLowerCase().contains("anuidade") && jaAdicionouAnuidade){
            return true;
        }else if((produto.getProduto().getDescricao().toLowerCase().contains("adesao") || produto.getProduto().getDescricao().toLowerCase().contains("adesÃ£o")) && jaAdicionouAdesao){
            return true;
        }else if((produto.getProduto().getDescricao().toLowerCase().contains("matricula") || produto.getProduto().getDescricao().toLowerCase().contains("matrÃ­cula")) && jaAdicionouMatricula){
            return true;
        }else if((produto.getProduto().getDescricao().toLowerCase().contains("rematricula") || produto.getProduto().getDescricao().toLowerCase().contains("rematrÃ­cula")) && jaAdicionouRematricula){
            return true;
        }else if((produto.getProduto().getDescricao().toLowerCase().contains("renovacao") || produto.getProduto().getDescricao().toLowerCase().contains("renovaÃ§Ã£o")) && jaAdicionouRenovacao){
            return true;
        }
        return false;
    }

    private boolean validarProdutoAnuidadeJaAdicionada(boolean jaAdicionouAnuidade, ContratoPlanoProdutoSugeridoVO produto, List<ContratoPlanoProdutoSugeridoVO> listaContratoPlanoProdutoSugeridosDuplicados) {
        if (produto.getPlanoProdutoSugerido().getProduto().getDescricao().toLowerCase().contains("anuidade")
                && produto.getPlanoProdutoSugerido().getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())
                && jaAdicionouAnuidade) {
            listaContratoPlanoProdutoSugeridosDuplicados.add(produto);
            return true;
        }
        return false;
    }

    private void gerarParcelasV2(ContratoVO obj, String situacaoParcela, ReciboPagamentoVO recibo) throws Exception {
        try {
            obj.setEmpresa(getEmpresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            boolean parcelamentoOperadora = obj.getPlano().getParcelamentoOperadora();
            //inicializar produtos para divisão correta nas parcelas
            for (MovProdutoVO movProdutoVO: obj.getMovProdutoVOs()) {
                movProdutoVO.setQuitado(false);
            }

            //inicializar lista de parcelas
            MovParcelaVO movParcela = new MovParcelaVO();
            movParcela.setDataRegistro(obj.getDataLancamento());
            obj.setMovParcelaVOs(new ArrayList());
            //contador
            int i = 1;
            //nr de parcelas
            int nrParcelas = obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();
            if (obj.getPlano().getParcelamentoOperadora()) {
                nrParcelas = 1;
                obj.getPlanoCondicaoPagamento().getCondicaoPagamento().setNrParcelas(1);
            }

            int entrada = 0;
            double valorParcelas = 0;
            double valorPrimeiraParcelas = 0;
            Date dataAtual = (obj.getDataPrimeiraParcela() == null ? negocio.comuns.utilitarias.Calendario.hoje() : obj.getDataPrimeiraParcela());
            Date data = (obj.getDataPrimeiraParcela() == null ? negocio.comuns.utilitarias.Calendario.hoje() : obj.getDataPrimeiraParcela());
            int mes = 1;
            boolean validarVencimentoAnterior = false; //para que a segunda parcela não fiquei com data anterior a primeira, em casos de recorrência.
            if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago()
                    && !obj.getSituacaoContrato().equals("RN")) {
                if (obj.getDataPrimeiraParcela() == null) {
                    dataAtual = movParcela.getDataRegistro();
                    data = movParcela.getDataRegistro();
                }
                validarVencimentoAnterior = true;
                mes--;
            }

            double parcelaAdesaoSeparada = 0.0;
            double parcelaProdutoSeparada = 0.0;
            double parcelaMatriculaSeparada = 0.0;
            double parcelaAnuidadeSeparada = 0.0;

            String numeroCupomDescontoAnuidade = "";
            if (obj.getPlano().getPlanoRecorrencia().isParcelarAnuidade()) {
                numeroCupomDescontoAnuidade = removerAnuidadeGerarParcelada(obj);
            }

            if (obj.getGerarParcelaAnuidadeSeparada() || obj.getDataAnuidade() != null && obj.getTotalFinalProdutos() > 0.0) {
                gerarParcelaAnuidade(obj, recibo);
                parcelaAnuidadeSeparada = obj.getSomaAnuidade();
            }
            boolean segunda = false;
            if (obj.getGerarParcelaParaProdutos() && obj.getSomaAdesao() > 0) {
                gerarParcelaAdesao(obj, recibo, data, dataAtual);
                parcelaAdesaoSeparada = obj.getSomaAdesao();
            }
            if (obj.isCobrarMatriculaSeparada()) {
                gerarParcelaMatriculaSeparada(obj, recibo, data, dataAtual);
                parcelaMatriculaSeparada = obj.getSituacaoContrato().equals("MA") ? obj.getValorMatricula() : obj.getValorRematricula();
            }
            if (obj.isCobrarProdutoSeparado()) {
                gerarParcelaProdutosSeparado(obj, recibo, data, dataAtual);
                parcelaProdutoSeparada = obj.getTotalProdutosCobrarSeparado();
            }

            boolean fluxoParcelasEditadas = !UteisValidacao.emptyList(obj.getListParcelasEditadas());

            Integer keyParcelaGerada = 2;
            HashMap<Integer, String> parcelaGerada = new HashMap<>();
            parcelaGerada.put(keyParcelaGerada, "Parcela 2");
            boolean isParcela1 = true;
            double parcelaEditadaValorGerado = 0.0;
            //enquanto o contador for menor ou igual ao nr de parcelas, gere as parcelas
            while (i <= nrParcelas) {

                // EDIÇÃO DE PARCELAS NA NEGOCIAÇÃO DO CONTRATO
                if (fluxoParcelasEditadas) {
                    if (isParcela1) {
                        //Para parcela 1 segue o fluxo
                        isParcela1 = false;
                        // Para demais parcelas usar o for
                    } else {
                        for (ParcelasEditarNegociacaoNovo parcelaEditada : obj.getListParcelasEditadas()) {
                            String par = parcelaGerada.get(keyParcelaGerada);
                            if (parcelaEditada.getDescricao().equals(par)) {
                                parcelaEditadaValorGerado = parcelaEditada.getValorParcela();
                                keyParcelaGerada++;
                                parcelaGerada.put(keyParcelaGerada, "Parcela " + keyParcelaGerada);
                                break;
                            }
                        }
                    }
                }

                //se a condição de pagamento exige entrada e ainda não foi feita a entrada
                if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getEntrada() && entrada == 0) {
                    //obter valor da parcela
                    if (fluxoParcelasEditadas) {
                        valorPrimeiraParcelas = obj.getValorPrimeiraParcelaEdicao();
                    } else {
                        if (obj.isDeveGerarParcelasComValorDiferente()) {
                            double valorDivisaoParcela = 0;
                            if (obj.getDividirProdutosNasParcelas()) {
                                valorDivisaoParcela = obj.getTotalFinalProdutos() / obj.getPlano().getPlanoRecorrencia().getDuracaoPlano();
                            }
                            if(obj.getPlano().getParcelamentoOperadora()) {
                                for (int j = 0; j < obj.getContratoDuracao().getNumeroMeses(); j++) {
                                    if(j == 0) { // primeira parcela
                                        valorParcelas += Uteis.arredondarForcando2CasasDecimais(obj.getMovProdutoVOs().get(j).getTotalFinal() + obj.getTotalFinalProdutos() + valorDivisaoParcela - parcelaAnuidadeSeparada - parcelaAdesaoSeparada - parcelaProdutoSeparada - parcelaMatriculaSeparada);
                                    } else {
                                        valorParcelas += obj.getMovProdutoVOs().get(j).getTotalFinal() + valorDivisaoParcela;
                                    }
                                }
                                valorPrimeiraParcelas = valorParcelas;
                            } else {
                                valorParcelas = obj.getMovProdutoVOs().get(i - 1).getTotalFinal() + valorDivisaoParcela;
                                valorPrimeiraParcelas = Uteis.arredondarForcando2CasasDecimais(obj.getMovProdutoVOs().get(i - 1).getTotalFinal()
                                        + obj.getTotalFinalProdutos()
                                        + valorDivisaoParcela
                                        - parcelaAnuidadeSeparada
                                        - parcelaAdesaoSeparada
                                        - parcelaProdutoSeparada
                                        - parcelaMatriculaSeparada);
                            }
                        } else {
                            valorParcelas = obj.obterValorParcelas(obj, true, i);
                            valorPrimeiraParcelas = obj.obterValorPrimeiraParcelas(obj, valorParcelas);
                        }
                    }
                    //setar valor da parcela
                    movParcela.setValorBaseCalculo(valorPrimeiraParcelas);
                    movParcela.setValorParcela(valorPrimeiraParcelas);

                    //preenche dados das parcelas
                    inicializarValoresParcelas(obj, movParcela, i, data, situacaoParcela);
                    movParcela.setSituacao(UteisValidacao.emptyNumber(movParcela.getValorParcela()) ? "PG" : situacaoParcela);

                    if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                        data = Uteis.obterDataFuturaParcela(dataAtual, (mes));
                    } else {
                        data = Uteis.obterDataFutura2(data, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
                    }
                    //gerar relacionamento produto --- parcela
                    if (!obj.getDividirProdutosNasParcelas()) {
                        parcelaComEntrada(obj, movParcela, recibo);
                    }
                    //informar que esta foi a entrada
                    entrada = 1;
                    segunda = true;
                    //se a entrada já foi informada
                } else {
                    //trata o dia do vencimento da primeira parcela e demais dias de vencimento,
                    //quando contrato é de Recorrência
                    if (fluxoParcelasEditadas) {
                        valorParcelas = parcelaEditadaValorGerado;
                    } else {
                        if (obj.isDeveGerarParcelasComValorDiferente()) {
                            double valorDivisaoParcela = 0;
                            if (obj.getDividirProdutosNasParcelas()) {
                                valorDivisaoParcela = obj.getTotalFinalProdutos() / obj.getPlano().getPlanoRecorrencia().getDuracaoPlano();
                            }
                            valorParcelas = obj.getMovProdutoVOs().get(i - 1).getTotalFinal() + valorDivisaoParcela;
                        } else {
                            valorParcelas = obj.obterValorParcelas(obj, false, i);
                        }
                    }
                    if (obj.getRegimeRecorrencia() && i != 1 && obj.isForcarDatasParcelasDiferentes()) {
                        Calendar c = Calendario.getInstance(data);
                        if ((movParcela.getDataVencimento().getMonth() == 1 && obj.getDiaVencimentoCartaoRecorrencia() > 28) // melhores dias 29,30 e 31 no mes fevereiro
                                || obj.getDiaVencimentoCartaoRecorrencia() > 30) { // melhor dia 31 em meses com 30 dias
                            data = Uteis.obterUltimoDiaMes(data); // isso para não pular um mês sem cobrança, pois se apenas adicionarmos o dia como abaixo, o sistema passaria para dias do mês seguinte, caso não tenha o dia no mês atual
                        } else {
                            c.set(Calendar.DATE, obj.getDiaVencimentoCartaoRecorrencia());
                            if (validarVencimentoAnterior) {
                                if (Calendario.menor(data, c.getTime())) { // validacao feita apenas na segunda parcela, para condição de pagamento pré-paga. As demais seguem o fluxo normal.
                                    data = c.getTime();
                                }
                                validarVencimentoAnterior = false;
                            } else {
                                data = c.getTime();
                            }
                        }
                        //se a diferenca entre a primeira parcela e a segunda for de menos de 30 dias
                        //a configuração da condição de pagamento é 30 dias
                        //é a segunda parcela
                        // e a configuração da empresa diz que deve forçar a diferença de 30 dias
                        Integer nrDiasComparar = obj.getEmpresa().getNrDiasProrata();
                        if (movParcela.getDataVencimento().getMonth() == 1 && nrDiasComparar > 28) {
                            nrDiasComparar = 1 + new Long(Uteis.nrDiasEntreDatas(Uteis.obterPrimeiroDiaMes(movParcela.getDataVencimento()), Uteis.obterUltimoDiaMes(movParcela.getDataVencimento()))).intValue();
                        }
                        if (Uteis.nrDiasEntreDatas(movParcela.getDataVencimento(), data) < nrDiasComparar
                                && obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30
                                && segunda
                                && obj.getEmpresa().isForcarMinimoVencimento2parcela()
                                && !(obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago()
                                && !obj.getSituacaoContrato().equals("RN"))) {
                            data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                            mes++;
                            segunda = false;
                            Calendar ca = Calendario.getInstance(data);
                            ca.set(Calendar.DATE, obj.getDiaVencimentoCartaoRecorrencia());
                            data = ca.getTime();
                        }

                    }

                    if (entrada == 1) {
                        //preencher valores da parcela
                        if ((obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago()
                                && !obj.getSituacaoContrato().equals("RN"))
                                && i == 2) {
                            if (obj.getDataPrimeiraParcela() == null) {
                                data = negocio.comuns.utilitarias.Calendario.hoje();
                            }
                        }

                        inicializarValoresParcelas(obj, movParcela, i, data, situacaoParcela);
                        if (segunda && !fluxoParcelasEditadas) {
                            movParcela.setValorParcela(obj.obterValorParcelas(obj, true, i));
                            movParcela.setValorBaseCalculo(obj.obterValorParcelas(obj, true, i));
                            if (obj.isDeveGerarParcelasComValorDiferente()) {
                                double valorDivisaoParcela = 0;
                                if (obj.getDividirProdutosNasParcelas()) {
                                    valorDivisaoParcela = obj.getTotalFinalProdutos() / obj.getPlano().getPlanoRecorrencia().getDuracaoPlano();
                                }
                                movParcela.setValorParcela(valorParcelas + valorDivisaoParcela);
                                movParcela.setValorBaseCalculo(valorParcelas + valorDivisaoParcela);
                            } else {
                                movParcela.setValorParcela(obj.obterValorParcelas(obj, true, i));
                                movParcela.setValorBaseCalculo(obj.obterValorParcelas(obj, true, i));
                            }
                        } else {
                            movParcela.setValorParcela(valorParcelas);
                            movParcela.setValorBaseCalculo(valorParcelas);
                        }

                        movParcela.setValorBaseCalculo(valorParcelas);
                        movParcela.setSituacao(UteisValidacao.emptyNumber(movParcela.getValorParcela()) ? "PG" : situacaoParcela);

                        //setar data
                        if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                            data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                        } else {
                            data = Uteis.obterDataFutura2(data, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
                        }

                        //gerar relacionamento produto --- parcela
                        if (!obj.getDividirProdutosNasParcelas()) {
                            parcela(obj, movParcela, recibo);
                        }
                        //se não contempla exige entrada
                    } else {
                        //verificar a primeira parcela com data futura
                        if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                            data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                        } else if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() != 0) {
                            data = Uteis.obterDataFutura2(data, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
                        }
                        //preencher valores da parcela
                        inicializarValoresParcelas(obj, movParcela, i, data, situacaoParcela);
                        //se chegou aqui sem entrar na parcela, quer dizer que não tem entrada e é a primeira parcela
                        if (entrada == 0) {

                            if (fluxoParcelasEditadas) {
                                valorParcelas = obj.getValorPrimeiraParcelaEdicao();
                                movParcela.setValorBaseCalculo(valorParcelas);
                                movParcela.setValorParcela(valorParcelas);
                            } else {
                                if (obj.isDeveGerarParcelasComValorDiferente()) {
                                    double valorDivisaoParcela = 0;
                                    if (obj.getDividirProdutosNasParcelas()) {
                                        valorDivisaoParcela = obj.getTotalFinalProdutos() / obj.getPlano().getPlanoRecorrencia().getDuracaoPlano();
                                    }
                                    valorPrimeiraParcelas = Uteis.arredondarForcando2CasasDecimais(obj.getMovProdutoVOs().get(i - 1).getTotalFinal() + obj.getTotalFinalProdutos() + valorDivisaoParcela - parcelaAnuidadeSeparada - parcelaAdesaoSeparada - parcelaProdutoSeparada - parcelaMatriculaSeparada);
                                } else {
                                    valorParcelas = obj.obterValorParcelas(obj, true, i);
                                    valorPrimeiraParcelas = obj.obterValorPrimeiraParcelas(obj, valorParcelas);
                                }
                                double valorParc = Uteis.arredondarForcando2CasasDecimais(valorPrimeiraParcelas);
                                movParcela.setValorBaseCalculo(valorParc);
                                movParcela.setValorParcela(valorParc);
                            }
                            //setar para não entrar aqui mais
                            entrada = 3;
                        } else {
                            movParcela.setValorBaseCalculo(valorParcelas);
                            movParcela.setValorParcela(valorParcelas);
                        }
                        movParcela.setSituacao(UteisValidacao.emptyNumber(movParcela.getValorParcela()) ? "PG" : situacaoParcela);
                        //gerar relacionamento produto --- parcela
                        if (!obj.getDividirProdutosNasParcelas()) {
                            parcela(obj, movParcela, recibo);
                        }
                    }
                }

                if (obj.getPlano().getParcelamentoOperadora()) {
                    int nrVezes = obj.getNrParcelasPagamento();
                    if (nrVezes == 0) {
                        nrVezes = 1;
                    }
                    movParcela.setNumeroParcelasOperadora(nrVezes);
                }
                movParcela.setContrato(obj);
                movParcela.setEmpresa(obj.getEmpresa());
                movParcela.setPessoa(obj.getPessoa());
                if (obj.getDividirProdutosNasParcelas()) {
                    dividirProdutosNasParcelas(movParcela, obj.getMovProdutoVOs(), recibo, nrParcelas, true, true, i == 1, obj.getGerarParcelaParaProdutos());
                }

                if (fluxoParcelasEditadas) {
                    tratarParcelasComValorZeradoFluxoParcelasEditadasNegociacao(movParcela);
                }
                incluirMovParcelaSemCommit(movParcela);
                obj.getMovParcelaVOs().add(movParcela);
                movParcela = new MovParcelaVO();
                mes++;
                i++;
            }

            if (obj.getRegimeRecorrencia() && obj.getDataAnuidade() == null) {
                gerarParcelaAnuidadeNaParcela(obj);
            }

            gerarAnuidadeParcelada(obj, numeroCupomDescontoAnuidade);

        } catch (Exception e) {
            throw e;
        }
    }

    private void tratarParcelasComValorZeradoFluxoParcelasEditadasNegociacao(MovParcelaVO movParcela) {
        //Antes do commit, caso a parcela tenha sido editada para um valor zerado, sua situação é setada como PG.
        if (movParcela.getValorParcela() <= 0.0) {
            movParcela.setSituacao("PG");
        }
    }

    private void gerarParcelas(ContratoVO obj, String situacaoParcela, ReciboPagamentoVO recibo) throws Exception {
        if (fluxoContratoEncerramentoDia) {
            gerarParcelasV2(obj, situacaoParcela, recibo);
        } else {
            gerarParcelasV1(obj, situacaoParcela, recibo);
        }
    }

    private void gerarParcelasV1(ContratoVO obj, String situacaoParcela, ReciboPagamentoVO recibo) throws Exception {
        try {
            boolean parcelamentoOperadora = obj.getPlano().getParcelamentoOperadora();
            //inicializar lista de parcelas
            MovParcelaVO movParcela = new MovParcelaVO();
            movParcela.setDataRegistro(obj.getDataLancamento());
            obj.setMovParcelaVOs(new ArrayList<>());
            //contador
            int i = 1;
            //nr de parcelas
            int nrParcelas = obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getNrParcelas();
            if (parcelamentoOperadora) {
                nrParcelas = 1;
                obj.getPlanoCondicaoPagamento().getCondicaoPagamento().setNrParcelas(1);
            }

            int entrada = 0;
            double valorParcelas = 0;
            double valorPrimeiraParcelas = 0;
            Date dataAtual = (obj.getDataPrimeiraParcela() == null ? negocio.comuns.utilitarias.Calendario.hoje() : obj.getDataPrimeiraParcela());
            Date data = (obj.getDataPrimeiraParcela() == null ? negocio.comuns.utilitarias.Calendario.hoje() : obj.getDataPrimeiraParcela());
            int mes = 1;
            boolean validarVencimentoAnterior = false; //para que a segunda parcela nÃ£o fiquei com data anterior a primeira, em casos de recorrÃªncia.
            if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago()
                    && !obj.getSituacaoContrato().equals("RN")){
                if (obj.getDataPrimeiraParcela() == null){
                    dataAtual = movParcela.getDataRegistro();
                    data = movParcela.getDataRegistro();
                }
                validarVencimentoAnterior = true;
                mes--;
            }

            double parcelaAdesaoSeparada = 0.0;
            double parcelaProdutoSeparada = 0.0;
            double parcelaMatriculaSeparada = 0.0;
            double parcelaAnuidadeSeparada = 0.0;

            String numeroCupomDescontoAnuidade = "";
            if (obj.getPlano().getPlanoRecorrencia().isParcelarAnuidade()) {
                numeroCupomDescontoAnuidade = removerAnuidadeGerarParcelada(obj);
            }

            if (obj.getGerarParcelaAnuidadeSeparada() || obj.getDataAnuidade() != null || obj.isDeveGerarParcelasComValorDiferente()) {
                gerarParcelaAnuidade(obj, recibo);
                parcelaAnuidadeSeparada = obj.getSomaAnuidade();
            }
            boolean segunda = false;
            if (obj.getGerarParcelaParaProdutos()) {
                gerarParcelaAdesao(obj, recibo, data, dataAtual);
                parcelaAdesaoSeparada = obj.getSomaAdesao();
            }
            if (obj.isCobrarMatriculaSeparada()){
                gerarParcelaMatriculaSeparada(obj, recibo, data, dataAtual);
                parcelaMatriculaSeparada = obj.getValorMatricula();
            }

            if (obj.isCobrarProdutoSeparado()){
                gerarParcelaProdutosSeparado(obj, recibo, data, dataAtual);
                parcelaProdutoSeparada = obj.getTotalProdutosCobrarSeparado();
            }
            if (obj.getGerarParcelaAnuidadeSeparada() && obj.getTotalFinalProdutos() > 0.0) {
                parcelaAnuidadeSeparada = obj.getSomaAnuidade();
            }

            //enquanto o contador for menor ou igual ao nr de parcelas, gere as parcelas
            while (i <= nrParcelas) {

                //se a condiÃ§Ã£o de pagamento exige entrada e ainda nÃ£o foi feita a entrada
                if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getEntrada() && entrada == 0) {
                    if(obj.isDeveGerarParcelasComValorDiferente()) {

                        double valorDivisaoParcela = 0;
                        if (obj.getDividirProdutosNasParcelas()) {
                            valorDivisaoParcela = obj.getTotalFinalProdutos() / obj.getPlano().getPlanoRecorrencia().getDuracaoPlano();
                        }
                        if (parcelamentoOperadora) {
                            //Se for parcelado pela Operadora, a primeira parcela será o valor total de todas as parcelas
                            obj.getPlano().getPlanoRecorrencia().setGerarParcelasValorDiferente(false);
                            valorParcelas = obj.obterValorParcelas(obj, true, i);
                            valorPrimeiraParcelas = valorParcelas;
                            obj.getPlano().getPlanoRecorrencia().setGerarParcelasValorDiferente(true);
                        } else {
                            //Se não for parcelado pela Operadora, a primeira parcela será o valor configurado no Plano.
                            valorPrimeiraParcelas = calcularValorPrimeiraParcela(i, obj, valorDivisaoParcela, parcelaAnuidadeSeparada, parcelaAdesaoSeparada, parcelaProdutoSeparada);
                        }
                    }else{
                        valorParcelas = obj.obterValorParcelas(obj, true, i);
                        valorPrimeiraParcelas = obj.obterValorPrimeiraParcelas(obj, valorParcelas);
                    }
                    //setar valor da parcela
                    movParcela.setValorBaseCalculo(valorPrimeiraParcelas);
                    movParcela.setValorParcela(valorPrimeiraParcelas);

                    //preenche dados das parcelas
                    inicializarValoresParcelas(obj, movParcela, i, data, situacaoParcela);
                    movParcela.setSituacao(UteisValidacao.emptyNumber(movParcela.getValorParcela()) ? "PG" : situacaoParcela);

                    if(obj.getDataBaseSegundaParcela() != null) {
                        data = obj.getDataBaseSegundaParcela();
                        dataAtual = obj.getDataBaseSegundaParcela();
                    }

                    if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                        data = Uteis.obterDataFuturaParcela(dataAtual, (mes));
                    } else {
                        data = Uteis.obterDataFutura2(data, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
                    }
                    //gerar relacionamento produto --- parcela
                    if (!obj.getDividirProdutosNasParcelas()) {
                        parcelaComEntrada(obj, movParcela, recibo);
                    }

                    if (!obj.getDividirProdutosNasParcelas()) {
                        parcela(obj, movParcela, recibo);
                    }

                    //informar que esta foi a entrada
                    entrada = 1;
                    segunda = true;
                    //se a entrada jÃ¡ foi informada
                } else {
                    //trata o dia do vencimento da primeira parcela e demais dias de vencimento,
                    //quando contrato Ã© de RecorrÃªncia
                    double valorDivisaoParcela = 0;
                    if (obj.getDividirProdutosNasParcelas()) {
                        valorDivisaoParcela = obj.getTotalFinalProdutos() / obj.getPlano().getPlanoRecorrencia().getDuracaoPlano();
                    }

                    //PAY-1197 - Alterado para usar o método obterValorParcelas quando vier do vendas online, pois dessa forma, contratos com duração superior à condição de pagamento geravam as parcelas
                    // com valor do MovProduto, ficando a menor do que o valor de fato do plano
                    if(obj.getOrigemSistema() != null && obj.getOrigemSistema().equals(OrigemSistemaEnum.VENDAS_ONLINE_2)
                        && obj.getPlano() != null && obj.getPlano().getSite()) {
                        valorParcelas = obj.obterValorParcelas(obj, true) + valorDivisaoParcela;
                    }else{
                        valorParcelas = obj.getMovProdutoVOs().get(i - 1).getTotalFinal() + valorDivisaoParcela;
                    }


                    if (!obj.getPlano().isVendaCreditoTreino() && i != 1 && obj.isForcarDatasParcelasDiferentes()) {
                        Calendar c = Calendario.getInstance(data);
                        if ((movParcela.getDataVencimento().getMonth() == 1 && obj.getDiaVencimentoCartaoRecorrencia() > 28) // melhores dias 29,30 e 31 no mes fevereiro
                                || obj.getDiaVencimentoCartaoRecorrencia() > 30) { // melhor dia 31 em meses com 30 dias
                            data = Uteis.obterUltimoDiaMes(data); // isso para nÃ£o pular um mÃªs sem cobranÃ§a, pois se apenas adicionarmos o dia como abaixo, o sistema passaria para dias do mÃªs seguinte, caso nÃ£o tenha o dia no mÃªs atual
                        } else {
                            c.set(Calendar.DATE, obj.getDiaVencimentoCartaoRecorrencia());
                            if (validarVencimentoAnterior) {
                                if (Calendario.menor(data, c.getTime())) { // validacao feita apenas na segunda parcela, para condiÃ§Ã£o de pagamento prÃ©-paga. As demais seguem o fluxo normal.
                                    data = c.getTime();
                                }
                                validarVencimentoAnterior = false;
                            } else {
                                data = c.getTime();
                            }
                        }
                        //se a diferenca entre a primeira parcela e a segunda for de menos de 30 dias
                        //a configuraÃ§Ã£o da condiÃ§Ã£o de pagamento Ã© 30 dias
                        //Ã© a segunda parcela
                        // e a configuraÃ§Ã£o da empresa diz que deve forÃ§ar a diferenÃ§a de 30 dias
                        Integer nrDiasComparar = obj.getEmpresa().getNrDiasProrata();
                        if (movParcela.getDataVencimento().getMonth() == 1 && nrDiasComparar > 28) {
                            nrDiasComparar = 1 + new Long(Uteis.nrDiasEntreDatas(Uteis.obterPrimeiroDiaMes(movParcela.getDataVencimento()), Uteis.obterUltimoDiaMes(movParcela.getDataVencimento()))).intValue();
                        }
                        if (Uteis.nrDiasEntreDatas(movParcela.getDataVencimento(), data) < nrDiasComparar
                                && obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30
                                && segunda
                                && obj.getEmpresa().isForcarMinimoVencimento2parcela()
                                && !(obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago()
                                && !obj.getSituacaoContrato().equals("RN"))) {
                            data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                            mes++;
                            segunda = false;
                            Calendar ca = Calendario.getInstance(data);
                            ca.set(Calendar.DATE, obj.getDiaVencimentoCartaoRecorrencia());
                            data = ca.getTime();
                        }

                    }

                    if (entrada == 1) {
                        //preencher valores da parcela
                        if ((obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago()
                                && !obj.getSituacaoContrato().equals("RN"))
                                && i == 2) {
                            if (obj.getDataPrimeiraParcela() == null) {
                                data = negocio.comuns.utilitarias.Calendario.hoje();
                            }
                        }

                        inicializarValoresParcelas(obj, movParcela, i, data, situacaoParcela);
                        if (segunda) {
                            movParcela.setValorParcela(contrato.obterValorParcelas(obj, true, i));
                            movParcela.setValorBaseCalculo(contrato.obterValorParcelas(obj, true, i));
                            valorDivisaoParcela = 0;
                            if (obj.getDividirProdutosNasParcelas()) {
                                valorDivisaoParcela = obj.getTotalFinalProdutos() / obj.getPlano().getPlanoRecorrencia().getDuracaoPlano();
                            }
                            movParcela.setValorParcela(valorParcelas + valorDivisaoParcela);
                            movParcela.setValorBaseCalculo(valorParcelas + valorDivisaoParcela);
                        } else {
                            movParcela.setValorParcela(valorParcelas);
                            movParcela.setValorBaseCalculo(valorParcelas);
                        }

                        movParcela.setValorBaseCalculo(valorParcelas);
                        movParcela.setSituacao(UteisValidacao.emptyNumber(movParcela.getValorParcela()) ? "PG" : situacaoParcela);

                        //setar data
                        if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                            data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                        } else {
                            data = Uteis.obterDataFutura2(data, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
                        }

                        //gerar relacionamento produto --- parcela
                        if (!obj.getDividirProdutosNasParcelas()) {
                            parcela(obj, movParcela, recibo);
                        }
                        //se nÃ£o contempla exige entrada
                    } else {

                        //preencher valores da parcela
                        inicializarValoresParcelas(obj, movParcela, i, data, situacaoParcela);
                        //se chegou aqui sem entrar na parcela, quer dizer que nÃ£o tem entrada e Ã© a primeira parcela
                        if (entrada == 0) {
                            valorParcelas = obj.obterValorParcelas(obj, false, i);

                            //verificar se vai dividir os produtos nas parcelas
                            if (obj.getDividirProdutosNasParcelas()) {
                                movParcela.setValorBaseCalculo(valorParcelas);
                                movParcela.setValorParcela(valorParcelas);
                            } else {
                                double valorParc = valorParcelas + obj.getSomaProduto() + obj.getValorProRata() - parcelaAdesaoSeparada - parcelaMatriculaSeparada - parcelaProdutoSeparada - parcelaAnuidadeSeparada;
                                movParcela.setValorBaseCalculo(valorParc);
                                movParcela.setValorParcela(valorParc);
                            }
                            //setar para nÃ£o entrar aqui mais
                            entrada = 3;
                        } else {
                            movParcela.setValorBaseCalculo(valorParcelas);
                            movParcela.setValorParcela(valorParcelas);
                        }
                        movParcela.setSituacao(UteisValidacao.emptyNumber(movParcela.getValorParcela()) ? "PG" : situacaoParcela);
                        //gerar relacionamento produto --- parcela
                        if (!obj.getDividirProdutosNasParcelas()) {
                            parcela(obj, movParcela, recibo);
                        }
                        //gerar vencimento para proxima parcela
                        if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                            data = Uteis.obterDataFuturaParcela(dataAtual, mes);
                        } else if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() != 0) {
                            data = Uteis.obterDataFutura2(data, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
                        }
                    }
                }

                if (obj.getPlano().getParcelamentoOperadora()) {
                    int nrVezes = obj.getNrParcelasPagamento();
                    if (nrVezes == 0) {
                        nrVezes = 1;
                    }
                    movParcela.setNumeroParcelasOperadora(nrVezes);
                }
                movParcela.setContrato(obj);
                movParcela.setEmpresa(obj.getEmpresa());
                movParcela.setPessoa(obj.getPessoa());
                if (obj.getDividirProdutosNasParcelas()) {
                    dividirProdutosNasParcelas(movParcela, obj.getMovProdutoVOs(), recibo, nrParcelas, true, true, i == 1,obj.getGerarParcelaParaProdutos());
                }


                incluirMovParcelaSemCommit(movParcela);
                obj.getMovParcelaVOs().add(movParcela);
                movParcela = new MovParcelaVO();
                mes++;
                i++;
            }

            if (obj.getRegimeRecorrencia() && obj.getDataAnuidade() == null) {
                gerarParcelaAnuidadeNaParcela(obj);
            }

            gerarAnuidadeParcelada(obj, numeroCupomDescontoAnuidade);

        } catch (Exception e) {
            throw e;
        }
    }

    private static double calcularValorPrimeiraParcela(int posicaoMovProduto, ContratoVO obj, double valorDivisaoParcela, double parcelaAnuidadeSeparada, double parcelaAdesaoSeparada, double parcelaProdutoSeparada) {
        Double valorParcela = obj.getMovProdutoVOs().get(posicaoMovProduto - 1).getTotalFinal() + obj.getTotalFinalProdutos() + valorDivisaoParcela;
        if ((valorParcela - parcelaAnuidadeSeparada) >= 0) {
            valorParcela = valorParcela - parcelaAnuidadeSeparada;
        }
        if ((valorParcela - parcelaAdesaoSeparada) >= 0) {
            valorParcela = valorParcela - parcelaAdesaoSeparada;
        }
        if ((valorParcela - parcelaProdutoSeparada) >= 0) {
            valorParcela = valorParcela - parcelaProdutoSeparada;
        }
        return Uteis.arredondarForcando2CasasDecimais(valorParcela);
    }

    private void dividirProdutosNasParcelas(MovParcelaVO parcela,
                                            List<MovProdutoVO> produtos,
                                            ReciboPagamentoVO recibo,
                                            int nrParcelas, boolean produto,
                                            boolean dividir,
                                            boolean primeira, boolean adesaoSeparada) throws Exception {

        Double valorParcela = parcela.getValorParcela();
        parcela.setMovProdutoParcelaVOs(new ArrayList<MovProdutoParcelaVO>());
        if (produto) {
            for (MovProdutoVO movProduto : produtos) {
                //se nÃ£o plano mensal
                if (!movProduto.getProduto().getTipoProduto().equals("PM")
                        //nem desconto extra
                        && !movProduto.getProduto().getTipoProduto().equals("DE")
                        // nem devoluÃ§Ã£o
                        && !movProduto.getProduto().getTipoProduto().equals("DV")
                        //nem desconto renovaÃ§Ã£o
                        && !movProduto.getProduto().getTipoProduto().equals("DR")
                        && !(movProduto.getProduto().getTipoProduto().equals("TD") &&  adesaoSeparada )) {
                    //aqui os produtos sÃ£o realmente divididos nas parcelas
                    Double valorProduto = movProduto.getTotalFinal();
                    if (dividir && primeira) {
                        Double valorDividido = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal() / nrParcelas);
                        valorProduto = valorDividido - ((valorDividido * nrParcelas) - movProduto.getTotalFinal());
                    } else if (dividir) {
                        if(valorProduto <= 0.0){
                            continue;
                        }
                        valorProduto = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal() / nrParcelas);
                    }
                    //gerar movprodutoparcela para relacionamento entre produto - parcela
                    gerarMovProdParcela(recibo, parcela, movProduto, valorProduto, true,false);
                    valorParcela = Uteis.arredondarForcando2CasasDecimais(valorParcela - valorProduto);
                }
            }
        }
        Ordenacao.ordenarLista(produtos, "mesReferenciaOrdenacao");
        for (MovProdutoVO movProduto : produtos) {
            //para plano mensal agora
            if (movProduto.getProduto().getTipoProduto().equals("PM")
                    && !movProduto.getQuitado()) {
                Double valorJaQuitado = 0.0;
                for (Object obj : movProduto.getMovProdutoParcelaVOs()) {
                    MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                    valorJaQuitado = Uteis.arredondarForcando2CasasDecimais(valorJaQuitado + mpp.getValorPago());
                }
                Double valorProduto = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal() - valorJaQuitado);
                if (Uteis.arredondarForcando2CasasDecimais(valorParcela) >= Uteis.arredondarForcando2CasasDecimais(valorProduto)) {
                    gerarMovProdParcela(recibo, parcela, movProduto, valorProduto, true,false);
                    valorParcela = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorParcela - valorProduto);
                    if (valorParcela == 0) {
                        break;
                    }
                } else {
                    gerarMovProdParcela(recibo, parcela, movProduto, valorParcela, false,false);
                    break;
                }
            }
        }

    }

    private void gerarMovProdParcela(ReciboPagamentoVO recibo, MovParcelaVO parcela,
                                     MovProdutoVO movProduto, Double valorProduto, boolean quitado,boolean vendaArmario) {
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProduto.getCodigo());
        movProdutoParcela.setValorPago(valorProduto);
        movProdutoParcela.setMovProdutoVO(movProduto);
        if (recibo.getCodigo() != 0) {
            movProdutoParcela.setReciboPagamento(recibo);
        }
        if(vendaArmario && parcela.getDescricao().contains("PRO-RATA") && movProduto.getDescricao().contains("PRO-RATA")) {
            parcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
        }
        if(!vendaArmario || (!parcela.getDescricao().contains("PRO-RATA") && !movProduto.getDescricao().contains("PRO-RATA") )) {
            parcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
        }
        movProduto.getMovProdutoParcelaVOs().add(movProdutoParcela);
        movProduto.setQuitado(quitado);
    }

    private void parcela(ContratoVO obj, MovParcelaVO movParcela, ReciboPagamentoVO recibo) throws Exception {
        int parcialmentePago = 0;
        Double valorMovProdutoParcelaParcialmentePago = 0.0;
        if (obj.getConvenioDesconto().getCodigo().intValue() != 0) {
            movParcela.setUtilizaConvenio(true);
        }

        //iterar nos produtos do contrato
        Iterator<MovProdutoVO> j = obj.getMovProdutoVOs().iterator();
        while (j.hasNext()) {
            MovProdutoVO movProduto = j.next();

            //se nÃ£o plano mensal
            if (!movProduto.getProduto().getTipoProduto().equals("PM")
                    //nem desconto extra
                    && !movProduto.getProduto().getTipoProduto().equals("DE")
                    // nem devoluÃ§Ã£o
                    && !movProduto.getProduto().getTipoProduto().equals("DV")
                    //nem desconto renovaÃ§Ã£o
                    && !movProduto.getProduto().getTipoProduto().equals("DR")
                    //                    nem esteja quitado
                    && !movProduto.getQuitado()) {

                if ((obj.isCobrarMatriculaSeparada()) && (movProduto.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo()))){
                    continue;
                }
                if ((obj.isCobrarProdutoSeparado()) && (ProdutoVO.isProdutoPodeCobrarSeparado(movProduto.getProduto().getTipoProduto()))){
                    continue;
                }
                List objs = getMovProdutoParcela().consultarPorCodigoMovProdutos(movProduto.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Iterator l = objs.iterator();
                while (l.hasNext()) {
                    MovProdutoParcelaVO movProdutoParcelaParciamentePago = (MovProdutoParcelaVO) l.next();
                    valorMovProdutoParcelaParcialmentePago = movProdutoParcelaParciamentePago.getValorPago() + valorMovProdutoParcelaParcialmentePago;
                    parcialmentePago = 1;
                }
                if (parcialmentePago == 1) {
                    valorMovProdutoParcelaParcialmentePago = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal() - valorMovProdutoParcelaParcialmentePago);
                    if (movParcela.getValorBaseCalculo() >= valorMovProdutoParcelaParcialmentePago) {
                        movProdutoParcialmente(movParcela, movProduto, valorMovProdutoParcelaParcialmentePago, recibo);
                    } else {
                        movProdutoNaoPago(movParcela, movProduto, recibo);
                    }
                    parcialmentePago = 0;
                    valorMovProdutoParcelaParcialmentePago = 0.0;
                } else if (movParcela.getValorBaseCalculo() >= movProduto.getTotalFinal()) {
                    movProdutoPago(movParcela, movProduto, recibo);
                } else if (movParcela.getValorBaseCalculo().intValue() > 0) {
                    movProdutoNaoPago(movParcela, movProduto, recibo);
                }
            }
        }
        if (movParcela.getValorBaseCalculo().intValue() >= 0) {
            Iterator<MovProdutoVO> k = obj.getMovProdutoVOs().iterator();
            while (k.hasNext()) {
                MovProdutoVO movProduto = k.next();
                if (movProduto.getProduto().getTipoProduto().equals("PM")
                        && !movProduto.getProduto().getTipoProduto().equals("DV")
                        && !movProduto.getQuitado()) {
                    List objs = getMovProdutoParcela().consultarPorCodigoMovProdutos(movProduto.getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    Iterator l = objs.iterator();
                    while (l.hasNext()) {
                        MovProdutoParcelaVO movProdutoParcelaParciamentePago = (MovProdutoParcelaVO) l.next();
                        valorMovProdutoParcelaParcialmentePago = movProdutoParcelaParciamentePago.getValorPago() + valorMovProdutoParcelaParcialmentePago;
                        parcialmentePago = 1;
                    }
                    if (parcialmentePago == 1) {
                        if (movParcela.getValorBaseCalculo() >= (movProduto.getTotalFinal() - valorMovProdutoParcelaParcialmentePago)) {
                            movProdutoParcialmente(movParcela, movProduto, valorMovProdutoParcelaParcialmentePago, recibo);
                        } else {
                            movProdutoNaoPago(movParcela, movProduto, recibo);
                        }
                        parcialmentePago = 0;
                        valorMovProdutoParcelaParcialmentePago = 0.0;
                    } else if (movParcela.getValorBaseCalculo() >= movProduto.getTotalFinal()) {
                        movProdutoPago(movParcela, movProduto, recibo);
                    } else if (movParcela.getValorBaseCalculo() < 1.0) {
                        movParcela.setValorBaseCalculo(0.0);
                    } else {
                        movProdutoNaoPago(movParcela, movProduto, recibo);
                    }
                }
            }
        }

    }

    private void movProdutoParcialmente(MovParcelaVO movParcela, MovProdutoVO movProduto, Double valorMovProdutoParcelaParcialmentePago, ReciboPagamentoVO recibo) throws Exception {
        double valor = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal() - valorMovProdutoParcelaParcialmentePago);
        if (valor > 0.0) {
            MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
            movProdutoParcela.setMovProduto(movProduto.getCodigo().intValue());
            movProdutoParcela.setMovProdutoVO(movProduto);
            movProdutoParcela.setValorPago(valor);
            if (recibo.getCodigo().intValue() != 0) {
                movProdutoParcela.setReciboPagamento(recibo);
            }
            movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
            movProduto.getMovProdutoParcelaVOs().add(movProdutoParcela);
            movProduto.setQuitado(true);
            movParcela.setValorBaseCalculo(movParcela.getValorBaseCalculo() - Uteis.arredondarForcando2CasasDecimais(movProdutoParcela.getValorPago()));
        }
    }

    private void parcelaComEntrada(ContratoVO obj, MovParcelaVO movParcela, ReciboPagamentoVO recibo) throws Exception {
        if (obj.getConvenioDesconto().getCodigo().intValue() != 0) {
            movParcela.setUtilizaConvenio(true);
        }
        Iterator<MovProdutoVO> j = obj.getMovProdutoVOs().iterator();
        while (j.hasNext()) {
            MovProdutoVO movProduto = j.next();
            if (!movProduto.getProduto().getTipoProduto().equals("PM")
                    && !movProduto.getProduto().getTipoProduto().equals("DE")
                    && !movProduto.getProduto().getTipoProduto().equals("DR") && !movProduto.getQuitado()) {
                if (obj.isCobrarMatriculaSeparada() && movProduto.getProduto().getTipoProduto().equals(TipoProduto.MATRICULA.getCodigo())){
                    continue;
                }
                if  ((obj.isCobrarProdutoSeparado()) && (ProdutoVO.isProdutoPodeCobrarSeparado(movProduto.getProduto().getTipoProduto()))){
                    continue;
                }

                if (movParcela.getValorBaseCalculo() >= movProduto.getTotalFinal()) {
                    movProdutoPago(movParcela, movProduto, recibo);
                } else if (movParcela.getValorBaseCalculo().intValue() > 0) {
                    movProdutoNaoPago(movParcela, movProduto, recibo);
                }
            }
        }
        if (movParcela.getValorBaseCalculo().intValue() > 0 || obj.getValorBaseCalculo() == 0.00) {
            Iterator<MovProdutoVO> k = obj.getMovProdutoVOs().iterator();
            while (k.hasNext()) {
                MovProdutoVO movProduto = k.next();
                if (movProduto.getProduto().getTipoProduto().equals("PM") && !movProduto.getQuitado()) {
                    if (movParcela.getValorBaseCalculo() >= movProduto.getTotalFinal()) {
                        movProdutoPago(movParcela, movProduto, recibo);
                    } else if (movParcela.getValorBaseCalculo() > 0) {
                        movProdutoNaoPago(movParcela, movProduto, recibo);
                    }
                }
            }
        }
    }

    private void inicializarValoresParcelas(ContratoVO obj, MovParcelaVO movParcela, int nrParcela, Date dataAtual, String situacaoParcela) throws Exception {
        movParcela.setDescricao("Parcela " + nrParcela);
        movParcela.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        if (obj.getDataAlteracaoManual() != null) {
            movParcela.setDataAlteracaoManual(Calendario.hojeSemRequest());
        }
        movParcela.setDataVencimento(dataAtual);
        movParcela.setPercentualJuro(obj.getEmpresa().getJuroParcela());
        movParcela.setPercentualMulta(obj.getEmpresa().getMulta());
        movParcela.setResponsavel(obj.getResponsavelContrato());
        movParcela.setSituacao(situacaoParcela);
    }

    private String removerAnuidadeGerarParcelada(ContratoVO obj) throws Exception {
        MovProdutoVO movProdutoAnuidade = null;
        boolean existeAnuidade = false;
        String numeroCupomAnuidade = null;
        for (Object object : obj.getMovProdutoVOs()) {
            movProdutoAnuidade = (MovProdutoVO) object;
            if (movProdutoAnuidade.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo())) {
                movProduto.excluirSemCommit(movProdutoAnuidade);
                existeAnuidade = true;
                numeroCupomAnuidade = movProdutoAnuidade.getNumeroCupomDesconto();
                break;
            }
        }
        if(existeAnuidade){
            obj.getMovProdutoVOs().remove(movProdutoAnuidade);
        }
        return numeroCupomAnuidade;
    }

    private void gerarAnuidadeParcelada(ContratoVO obj, String numeroCupomDescontoAnuidade) throws Exception {

        if (!obj.getPlano().getPlanoRecorrencia().isParcelarAnuidade()) {
            return;
        }

        Integer anoVigenciaDe = Uteis.getAnoData(obj.getVigenciaDe());
        Integer diaAnuidade = Uteis.getDiaMesData(obj.getVigenciaAte());
        Integer mesAnuidade = Uteis.getMesData(obj.getVigenciaAte());
        Calendar dataAnuidade = Calendario.getInstance(anoVigenciaDe, mesAnuidade, diaAnuidade);
        Date dataReferenciaAnuidade = dataAnuidade.getTime();

        Double valorAnuidade = obj.getPlano().getPlanoRecorrencia().getValorAnuidadeTotalParcelada();

        //criar produto anuidade
        MovProdutoVO movProdutoVO = gerarMovProdutoAnuidadeParcelada(obj, valorAnuidade, Calendario.hoje());
        movProdutoVO.setNumeroCupomDesconto(numeroCupomDescontoAnuidade);
        movProduto.incluirSemCommit(movProdutoVO);

        for (PlanoAnuidadeParcelaVO planoAnuidadeParcelaVO : obj.getPlano().getPlanoRecorrencia().getParcelasAnuidade()) {

            MovParcelaVO parcelaReferencia = obterParcelaAnuidade(obj.getMovParcelaVOs(), planoAnuidadeParcelaVO.getParcela());
            if (parcelaReferencia == null) {
                throw new Exception("Erro ao gerar parcela de anuidade!");
            }

            MovParcelaVO movParc = new MovParcelaVO();
            movParc.setValorBaseCalculo(Uteis.arredondarForcando2CasasDecimais(planoAnuidadeParcelaVO.getValor()));
            movParc.setValorParcela(movParc.getValorBaseCalculo());

            movParc.setDescricao("ANUIDADE PLANO RECORRENTE - " + Uteis.getAnoData(dataReferenciaAnuidade) + " - PARCELA " + planoAnuidadeParcelaVO.getNumero());
            movParc.setDataRegistro(Calendario.hoje());
            movParc.setDataVencimento(parcelaReferencia.getDataVencimento());
            movParc.setDataCobranca(parcelaReferencia.getDataVencimento());

            movParc.setPercentualJuro(obj.getEmpresa().getJuroParcela());
            movParc.setPercentualMulta(obj.getEmpresa().getMulta());
            movParc.setResponsavel(obj.getResponsavelContrato());
            movParc.setSituacao(UteisValidacao.emptyNumber(movParc.getValorParcela()) ? "PG" : "EA");

            movProdutoPagoAnuidadeParcelada(movParc, movProdutoVO);

            //informar que esta foi a entrada
            movParc.setContrato(obj);
            movParc.setEmpresa(obj.getEmpresa());
            movParc.setPessoa(obj.getPessoa());
            incluirMovParcelaSemCommit(movParc);
            obj.getMovParcelaVOs().add(movParc);
            gerarMovParcelaCupomDesconto(numeroCupomDescontoAnuidade, movParc, 0.0);
        }
    }

    private void movProdutoPagoAnuidadeParcelada(MovParcelaVO movParcela, MovProdutoVO movProduto) {
        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
        movProdutoParcela.setMovProduto(movProduto.getCodigo());
        movProdutoParcela.setMovProdutoVO(movProduto);
        movProdutoParcela.setValorPago(movParcela.getValorParcela());
        movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
        movProduto.getMovProdutoParcelaVOs().add(movProdutoParcela);
        movProduto.setQuitado(true);
    }

    public MovParcelaVO obterParcelaAnuidade(List<MovParcelaVO> listaParcelas, Integer numeroParcela) {
        for (MovParcelaVO movParcelaVO : listaParcelas) {
            if (movParcelaVO.getDescricao().toUpperCase().equals("PARCELA " + numeroParcela)) {
                return movParcelaVO;
            }
        }
        return null;
    }

    private MovProdutoVO gerarMovProdutoAnuidadeParcelada(ContratoVO contratoVO, Double valorAnuidade, Date dataRegistro) throws Exception {

        ProdutoVO produtoAnuidade = getProduto().consultarPorTipoProduto(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (UteisValidacao.emptyNumber(produtoAnuidade.getCodigo())) {
            throw new Exception("Produto \"Taxa de Anuidade Plano Recorrncia\" no foi encontrado!");
        }

        MovProdutoVO movProdutoVO = new MovProdutoVO();
        movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(contratoVO.getVigenciaDe()));
        movProdutoVO.setAnoReferencia(Uteis.getAnoData(contratoVO.getVigenciaDe()));
        movProdutoVO.setDataLancamento(Calendario.getDataComHoraZerada(dataRegistro));
        movProdutoVO.setContrato(contratoVO);
        movProdutoVO.setProduto(produtoAnuidade);
        movProdutoVO.setApresentarMovProduto(false);
        movProdutoVO.setDescricao(produtoAnuidade.getDescricao());
        movProdutoVO.setQuantidade(1);
        movProdutoVO.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProdutoVO.setPrecoUnitario(valorAnuidade);
        movProdutoVO.setValorDesconto(0.0);
        movProdutoVO.setTotalFinal(valorAnuidade);
        movProdutoVO.getEmpresa().setCodigo(contratoVO.getEmpresa().getCodigo());
        movProdutoVO.setPessoa(contratoVO.getPessoa());
        if (valorAnuidade == 0) {
            movProdutoVO.setQuitado(true);
            movProdutoVO.setSituacao("PG");
        } else {
            movProdutoVO.setQuitado(false);
            movProdutoVO.setSituacao("EA");
        }
        return movProdutoVO;
    }


    private void gerarParcelaAnuidadeNaParcela(ContratoVO obj) throws Exception {

        if (!obj.getPlano().getPlanoRecorrencia().isAnuidadeNaParcela()) {
            return;
        }


        Integer diaVencimento = 0;
        Integer mesVencimento = 0;
        Integer anoVencimento = 0;
        boolean temParcelaIgualAnuidadeSelecionada = false;
        List<MovParcelaVO> listaAnuidade = new ArrayList<MovParcelaVO>();
        MovParcelaVO movParcelaVO = null;
        for (Object object : obj.getMovParcelaVOs()) {
            movParcelaVO = (MovParcelaVO) object;
            if (movParcelaVO.getDescricao().toUpperCase().contains("ANUIDADE PLANO")) {
                listaAnuidade.add(movParcelaVO);
            }

            if (movParcelaVO.getDescricao().toUpperCase().equals("PARCELA " + obj.getPlano().getPlanoRecorrencia().getParcelaAnuidade())) {
                diaVencimento = Uteis.obterDiaData(movParcelaVO.getDataVencimento());
                mesVencimento = Uteis.getMesData(movParcelaVO.getDataVencimento());
                anoVencimento = Uteis.getAnoData(movParcelaVO.getDataVencimento());
                temParcelaIgualAnuidadeSelecionada = true;
            }
        }

        if(!temParcelaIgualAnuidadeSelecionada){
            Date dataAnuidade = Uteis.somarMeses(Calendario.hoje(), obj.getPlano().getPlanoRecorrencia().getParcelaAnuidade() - 1);
            diaVencimento = Uteis.obterDiaData(dataAnuidade);
            mesVencimento = Uteis.getMesData(dataAnuidade);
            anoVencimento = Uteis.getAnoData(dataAnuidade);
        }

        for (MovParcelaVO movp : listaAnuidade) {
            Calendar novaData = Calendario.getInstance();
            novaData.setTime(movp.getDataVencimento());
            novaData.set(Calendar.DAY_OF_MONTH, diaVencimento);
            novaData.set(Calendar.MONTH, mesVencimento - 1);
            if (listaAnuidade.size() == 1) {
                novaData.set(Calendar.YEAR, anoVencimento);
                movp.setDescricao("ANUIDADE PLANO RECORRENTE - " + anoVencimento);
            }
            movp.setDataVencimento(novaData.getTime());
            MovParcela movParcelaDAO = new MovParcela(this.con);
            movParcelaDAO.alterarSemCommit(movp);
            MovProduto movProdutoDAO = new MovProduto(this.con);
            for(MovProdutoParcelaVO movProdutoParcelaVO: movp.getMovProdutoParcelaVOs()){
                movProdutoParcelaVO.getMovProdutoVO().setMesReferencia(Uteis.getMesReferenciaData(movp.getDataVencimento()));
                movProdutoParcelaVO.getMovProdutoVO().setAnoReferencia(Uteis.getAnoData(movp.getDataVencimento()));
                movProdutoDAO.alterarSemCommit(movProdutoParcelaVO.getMovProdutoVO());
            }
        }
    }

    private void gerarParcelaProdutosSeparado(ContratoVO obj, ReciboPagamentoVO recibo, Date data, Date dataAtual) throws Exception {

        // Somente se nÃ£o for contrato futuro.
        // Produto e Adesao nÃ£o acompanha o dia de vencimento da mensalidade informado no vendas online, segue o padrÃ£o.
        if (obj.isForcarDatasParcelasDiferentes() && Calendario.igual(obj.getVigenciaDe(), Calendario.hoje())) {
            if (obj.getPlano().getSite() && Calendario.maior(data, obj.getVigenciaDe())) {
                data = Calendario.hoje();
            }
        }


        Date dataParcela = new Date(data.getTime());
        int mes = 1;

        Integer nrParcelasProdutos = UteisValidacao.emptyNumber(obj.getNrVezesParcelarProduto()) ? 1 : obj.getNrVezesParcelarProduto();
        Double valorParcela = (obj.getTotalProdutosCobrarSeparado() / nrParcelasProdutos);
        Double valorPrimeiraParcela = ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasProdutos) == obj.getTotalProdutosCobrarSeparado())
                ? valorParcela : ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasProdutos) > obj.getTotalProdutosCobrarSeparado())
                ? (valorParcela - ((Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasProdutos) - obj.getTotalProdutosCobrarSeparado()))
                : (valorParcela + (obj.getTotalProdutosCobrarSeparado() - (Uteis.arredondarForcando2CasasDecimais(valorParcela) * nrParcelasProdutos)));

        for (int i = 1; i <= nrParcelasProdutos; i++) {
            MovParcelaVO movParc = new MovParcelaVO();

            movParc.setValorBaseCalculo(i == 1 ? valorPrimeiraParcela : valorParcela);
            movParc.setValorParcela(movParc.getValorBaseCalculo());

            movParc.setDescricao("PRODUTOS PARCELA " + i);
            movParc.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
            if (obj.getDataAlteracaoManual() != null) {
                movParc.setDataAlteracaoManual(Calendario.hojeSemRequest());
            }
            movParc.setDataVencimento(dataParcela);
            movParc.setPercentualJuro(obj.getEmpresa().getJuroParcela());
            movParc.setPercentualMulta(obj.getEmpresa().getMulta());
            movParc.setResponsavel(obj.getResponsavelContrato());
            movParc.setSituacao(UteisValidacao.emptyNumber(movParc.getValorParcela()) ? "PG" : "EA");

            //gerar relacionamento produto --- parcela
            parcelaProdutos(obj, movParc, recibo, movParc.getValorParcela());
            //informar que esta foi a entrada
            movParc.setContrato(obj);
            movParc.setEmpresa(obj.getEmpresa());
            movParc.setPessoa(obj.getPessoa());
            incluirMovParcelaSemCommit(movParc);
            obj.getMovParcelaVOs().add(movParc);

            if (obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela() == 30) {
                dataParcela = Uteis.obterDataFuturaParcela(dataParcela, (mes));
            } else {
                dataParcela = Uteis.obterDataFutura2(dataParcela, obj.getPlanoCondicaoPagamento().getCondicaoPagamento().getIntervaloEntreParcela());
            }
        }
    }

    private void parcelaProdutos(ContratoVO obj, MovParcelaVO movParcela, ReciboPagamentoVO recibo, Double valorParcela) throws Exception {
        if (obj.getConvenioDesconto().getCodigo().intValue() != 0) {
            movParcela.setUtilizaConvenio(true);
        }
        Iterator<MovProdutoVO> j = obj.getMovProdutoVOs().iterator();
        while (j.hasNext()) {
            MovProdutoVO movProduto = j.next();
            if (ProdutoVO.isProdutoPodeCobrarSeparado(movProduto.getProduto().getTipoProduto())){
                movProdutoPago(movParcela, movProduto, recibo, valorParcela);
            }
        }
    }

    private ReciboPagamentoVO gerarRecibo(ContratoVO obj) throws Exception {
        ReciboPagamentoVO recibo = new ReciboPagamentoVO();
        recibo.setContrato(obj);
        recibo.setData(negocio.comuns.utilitarias.Calendario.hoje());
        recibo.setNomePessoaPagador(obj.getPessoa().getNome());
        recibo.setPessoaPagador(obj.getPessoa());
        recibo.setResponsavelLancamento(obj.getResponsavelContrato());
        recibo.setValorTotal(obj.getValorFinal());
        recibo.setEmpresa(obj.getEmpresa());
        getReciboPagamento().incluir(recibo);
        return recibo;
    }

    private void incluirParcelas(ContratoVO contrato) throws Exception {
        List<MovParcelaVO> parcelas = contrato.getMovParcelaVOs();
        for (MovParcelaVO parcela: parcelas) {
            parcela.getContrato().setCodigo(contrato.getCodigo());
            parcela.getEmpresa().setCodigo(contrato.getEmpresa().getCodigo());
            incluirMovParcelaSemCommit(parcela);
        }
    }

    private void incluirMovParcelaSemCommit(MovParcelaVO obj) throws Exception {
        MovParcelaVO.validarDados(obj);
        incluirMovParcelaSemValidar(obj);
    }

    private void incluirMovParcelaSemValidar(MovParcelaVO obj) throws Exception {
        if (obj.getPersonal().getPersonal().getCodigo() > 0) {
            // inclui o historico do personal e dos alunos
            getControleTaxaPersonal().incluirSemCommit(obj.getPersonal());
        }
        getMovParcela().incluirParcelaSemValidar(obj);
        getMovProdutoParcela().incluirMovProdutoParcelas(obj.getCodigo(), obj.getMovProdutoParcelaVOs());
        Iterator<MovProdutoVO> i = obj.getContrato().getMovProdutoVOs().iterator();
        while (i.hasNext()) {
            MovProdutoVO movProduto = i.next();
            if (movProduto.getQuitado()) {
                getMovProduto().alterarSemCommit(movProduto);
            }
        }
        // inclui os produtos relativos aos alunos do personal trainer
        for (ItemTaxaPersonalVO item : obj.getPersonal().getAlunos()) {
            if (obj.getValorParcela() == 0.0 && obj.getSituacao().equals("PG")) {
                item.getMovProduto().setSituacao("PG");
            }
            getMovProduto().incluirSemCommit(item.getMovProduto());

            //pega codigo do produto e codigo itemTaxaPersonal...
            int codigoMovProduto = item.getMovProduto().getCodigo();
            int codigoItemTaxaPersonal = item.getCodigo();
            //faz update na itemTaxaPersonal e insere o codigo do movProduto onde codigoItemTaxaPersonal igual a codigo...
            ItemTaxaPersonal itemTaxaPersonalDAO = new ItemTaxaPersonal(this.con);
            itemTaxaPersonalDAO.alterarItemTaxaPersonal(codigoMovProduto, codigoItemTaxaPersonal);
            itemTaxaPersonalDAO= null;

            MovProdutoParcelaVO mpp = new MovProdutoParcelaVO();
            mpp.setMovParcela(obj.getCodigo());
            mpp.setMovProduto(item.getMovProduto().getCodigo());
            mpp.setMovProdutoVO(item.getMovProduto());
            mpp.setValorPago(item.getMovProduto().getTotalFinal());
            getMovProdutoParcela().incluir(mpp);
        }
        obj.setNovoObj(false);
    }

    private void enviarMalaDiretaAssincrono(ContratoVO contratoVO, boolean cancelamento) {
        enviarMalaDiretaAssincronoGeral(null, null, contratoVO, cancelamento);
    }

    private void enviarMalaDiretaAssincronoGeral(OcorrenciaEnum ocorrenciaEnum, Integer cliente, ContratoVO contratoVO, boolean cancelamento) {
        try {
            MailingService mailingService = new MailingService(this.con);

            if (ocorrenciaEnum != null) {

                mailingService.enviarMalaDiretaComOcorrencia(ocorrenciaEnum, cliente);

            } else {

                ClienteVO clienteVO = getCliente().consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                try {
                    if (contratoVO != null) {
                        //independente se é Rematrícula, renovação ou novo contrato
                        mailingService.enviarMalaDiretaComOcorrencia(OcorrenciaEnum.VENDA_CONTRATO, clienteVO.getCodigo());
                    }
                } catch (Exception ex) {
                }
                if (cancelamento) {
                    mailingService.enviarMalaDiretaComOcorrencia(OcorrenciaEnum.APOS_CANCELAMENTO, clienteVO.getCodigo());
                } else if (contratoVO.isContratoMatricula()) {
                    mailingService.enviarMalaDiretaComOcorrencia(OcorrenciaEnum.APOS_MATRICULA, clienteVO.getCodigo());
                } else if (contratoVO.isContratoRematricula()) {
                    mailingService.enviarMalaDiretaComOcorrencia(OcorrenciaEnum.APOS_REMATRICULA, clienteVO.getCodigo());
                } else if (contratoVO.isContratoRenovacao()) {
                    mailingService.enviarMalaDiretaComOcorrencia(OcorrenciaEnum.APOS_RENOVACAO, clienteVO.getCodigo());
                }
            }
        } catch (Exception ignored) {
        }
    }

    /**
     * Valida se Ã© necessÃ¡rio incluir a {@link AutorizacaoCobrancaClienteVO} do tipo BOLETO para o cliente.
     * @param obj
     * @throws Exception
     */
    private void validarInclusaoConvenioCobrancaBoleto(ContratoVO obj) throws Exception{
        EmpresaVO empresa = getEmpresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(empresa.getConvenioBoletoPadrao() != null && !UteisValidacao.emptyNumber(empresa.getConvenioBoletoPadrao().getCodigo())){
            AutorizacaoCobrancaClienteVO autorizacao = getAutorizacaoCobrancaCliente().consultar(obj.getCliente().getCodigo(), TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
            if(autorizacao == null || UteisValidacao.emptyNumber(autorizacao.getCodigo())){
                AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = new AutorizacaoCobrancaClienteVO();
                autorizacaoCobrancaClienteVO.setConvenio(empresa.getConvenioBoletoPadrao());
                autorizacaoCobrancaClienteVO.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
                autorizacaoCobrancaClienteVO.setCliente(obj.getCliente());
                autorizacaoCobrancaClienteVO.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                getAutorizacaoCobrancaCliente().incluir(autorizacaoCobrancaClienteVO);
            }
        }
    }

    private void atribuirCodigoOperacaoFinanceira(List<MovProdutoVO> movProdutos, ContratoVO contrato){
        try{
            if(UtilReflection.objetoMaiorQueZero(contrato, "getPlano().getCodigo()")){
                PlanoVO plano = getPlano().consultarPorChavePrimaria(contrato.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(UtilReflection.objetoMaiorQueZero(plano, "getPlanoTipo().getCodigo()")){
                    PlanoTipoVO planoTipo = getPlanoTipo().consultarPorCodigo(plano.getPlanoTipo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    for (MovProdutoVO movProdutoVO: movProdutos) {
                        String codigoOperacao =
                                getPlanoTipo().consultarCodigoOperacaoFinanceiraParaParcelas(planoTipo, movProdutoVO.getTipoProduto());
                        movProdutoVO.setCodigoOperacaoFinanceira(codigoOperacao);
                    }
                }

            }

        }catch (Exception e){
            Uteis.logar("Erro ao definir cÃ³digo de operaÃ§Ã£o financeira do produto");
            Uteis.logar(e, ZillyonWebFacade.class);
        }

    }

    private List<MovProdutoModalidadeVO> gerarMovProdutoModalidade(Double valorSomaModalidades, Date inicio,
                                                                   Date fim, List<ContratoModalidadeVO> modalidades, Double valorADividir) {
        List<MovProdutoModalidadeVO> movProdModalidades = new ArrayList<MovProdutoModalidadeVO>();
        for (ContratoModalidadeVO cm : modalidades) {
            if (cm.getModalidade().getModalidadeEscolhida()) {
                MovProdutoModalidadeVO movProdutoModalidade = new MovProdutoModalidadeVO();
                if (valorSomaModalidades > 0) {
                    movProdutoModalidade.setValor(
                            Uteis.arredondarForcando2CasasDecimais(valorADividir * (cm.getValorFinalModalidade() / valorSomaModalidades)));
                } else {
                    movProdutoModalidade.setValor(0.0);
                }
                movProdutoModalidade.setModalidadeVO(cm.getModalidade());
                movProdutoModalidade.setDataInicio(inicio);
                movProdutoModalidade.setDataFim(fim);
                ContratoVO c = new ContratoVO();
                c.setCodigo(cm.getContrato());
                movProdutoModalidade.setContrato(c);
                movProdModalidades.add(movProdutoModalidade);
            }
        }
        return movProdModalidades;
    }

    private void incluirContratoModalidades(Integer contratoPrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoModalidadeVO obj = (ContratoModalidadeVO) e.next();
            if (obj.getModalidade().getModalidadeEscolhida()) {
                obj.setContrato(contratoPrm);
                incluirContratoModalidade(obj);
            }
        }
    }

    private void incluirContratoModalidadesTurma(Integer contratoModalidadePrm, List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ContratoModalidadeTurmaVO obj = (ContratoModalidadeTurmaVO) e.next();
            if (obj.getTurma().getTurmaEscolhida()) {
                obj.setContratoModalidade(contratoModalidadePrm);
                incluirContratoModalidadeTurma(obj);
            }

        }
    }

    private void incluirContratoModalidadeTurma(ContratoModalidadeTurmaVO obj) throws Exception {
        getContratoModalidadeTurma().incluirContratoModalidadeTurma(obj);
        getContratoModalidadeHorarioTurma().incluirContratoModalidadeHorarioTurmas(obj.getCodigo(), obj.getContratoModalidadeHorarioTurmaVOs());
    }

    private void incluirContratoModalidade(ContratoModalidadeVO obj) throws Exception {
        getContratoModalidade().incluirContratoModalidade(obj);
        incluirContratoModalidadesTurma(obj.getCodigo(), obj.getContratoModalidadeTurmaVOs());
        getContratoModalidadeProdutoSugerido().incluirContratoModalidadesProdutoSugerido(obj.getCodigo(), obj.getContratoModalidadeProdutoSugeridoVOs());
        obj.gerarContratoModalidade();
        getContratoModalidadeVezesSemana().incluirContratoModalidadeVezesSemana(obj.getCodigo(), obj.getContratoModalidadeVezesSemanaVO());
        getContratoModalidadeCredito().incluirContratoModalidadeCredito(obj);
    }



    // -------------------------------- TODOS OS GETTERS devem inicializar as DAOs passando a conexÃ£o
    private VendasConfigInterfaceFacade getVendasOnline() throws Exception {
        if (vendas == null) {
            vendas = new VendasConfig(this.con);
        }
        return vendas;
    }

    private PlanoDuracaoCreditoTreinoInterfaceFacade getPlanoDuracaoCreditoTreino() throws Exception{
        if (planoDuracaoCreditoTreino == null){
            planoDuracaoCreditoTreino = new PlanoDuracaoCreditoTreino(this.con);
        }
        return planoDuracaoCreditoTreino;
    }

    private ModalidadeInterfaceFacade getModalidade() throws Exception {
        if (modalidade == null) {
            modalidade = new Modalidade(this.con);
        }
        return modalidade;
    }

    private TurmaInterfaceFacade getTurma() throws Exception {
        if (turma == null) {
            turma = new Turma(this.con);
        }
        return turma;
    }

    private DescontoInterfaceFacade getDesconto() throws Exception {
        if (desconto == null) {
            desconto = new Desconto(this.con);
        }
        return desconto;
    }

    private PassivoInterfaceFacade getPassivo() throws Exception {
        if (passivo == null) {
            passivo = new Passivo(this.con);
        }
        return passivo;
    }

    private ConfiguracaoSistemaCRMInterfaceFacade getConfiguracaoSistemaCRM() throws Exception {
        if (configuracaoSistemaCRM == null) {
            configuracaoSistemaCRM = new ConfiguracaoSistemaCRM(this.con);
        }
        return configuracaoSistemaCRM;
    }

    private ConversaoLeadInterfaceFacade getConversaoLead() throws Exception {
        if (conversaoLead == null) {
            conversaoLead  = new ConversaoLead(this.con);
        }
        return conversaoLead;
    }

    private PlanoTipo getPlanoTipo() throws Exception {
        if( planoTipo == null){
            planoTipo = new PlanoTipo(this.con);
        }
        return planoTipo;
    }

    private ContratoModalidadeCreditoInterfaceFacade getContratoModalidadeCredito() throws Exception {
        if (contratoModalidadeCredito == null) {
            contratoModalidadeCredito = new ContratoModalidadeCredito(this.con);
        }
        return contratoModalidadeCredito;
    }

    /**
     * @return the colaborador
     */
    private ColaboradorInterfaceFacade getColaborador() throws Exception {
        if (colaborador == null) {
            colaborador = new Colaborador(this.con);
        }
        return colaborador;
    }

    /**
     * @return the contratoDuracao
     */
    private ContratoDuracao getContratoDuracao() throws Exception {
        if (contratoDuracao == null) {
            contratoDuracao = new ContratoDuracao(this.con);
        }
        return contratoDuracao;
    }

    /**
     * @return the contratoHorario
     */
    private ContratoHorario getContratoHorario() throws Exception {
        if (contratoHorario == null) {
            contratoHorario = new ContratoHorario(this.con);
        }
        return contratoHorario;
    }

    /**
     * @return the contratoCondicaoPagamento
     */
    private ContratoCondicaoPagamento getContratoCondicaoPagamento() throws Exception {
        if (contratoCondicaoPagamento == null) {
            contratoCondicaoPagamento = new ContratoCondicaoPagamento(this.con);
        }
        return contratoCondicaoPagamento;
    }

    /**
     * @return the contratoComposicao
     */
    private ContratoComposicao getContratoComposicao() throws Exception {
        if (contratoComposicao == null) {
            contratoComposicao = new ContratoComposicao(this.con);
        }
        return contratoComposicao;
    }

    /**
     * @return the contratoTextoPadrao
     */
    private ContratoTextoPadraoInterfaceFacade getContratoTextoPadrao() throws Exception {
        if (contratoTextoPadrao == null) {
            contratoTextoPadrao = new ContratoTextoPadrao(this.con);
        }
        return contratoTextoPadrao;
    }

    /**
     * @return the contratoModalidade
     */
    private ContratoModalidade getContratoModalidade() throws Exception {
        if (contratoModalidade == null) {
            contratoModalidade = new ContratoModalidade(this.con);
        }
        return contratoModalidade;
    }

    /**
     * @return the contratoPlanoProdutoSugerido
     */
    private ContratoPlanoProdutoSugerido getContratoPlanoProdutoSugerido() throws Exception {
        if (contratoPlanoProdutoSugerido == null) {
            contratoPlanoProdutoSugerido = new ContratoPlanoProdutoSugerido(this.con);
        }
        return contratoPlanoProdutoSugerido;
    }

    /**
     * @return the historicoContrato
     */
    private HistoricoContratoInterfaceFacade getHistoricoContrato() throws Exception {
        if (historicoContrato == null) {
            historicoContrato = new HistoricoContrato(this.con);
        }
        return historicoContrato;
    }

    /**
     * @return the periodoAcessoCliente
     */
    private PeriodoAcessoClienteInterfaceFacade getPeriodoAcessoCliente() throws Exception {
        if (periodoAcessoCliente == null) {
            periodoAcessoCliente = new PeriodoAcessoCliente(this.con);
        }
        return periodoAcessoCliente;
    }

    /**
     * @return the movProduto
     * @throws Exception
     */
    private MovProdutoInterfaceFacade getMovProduto() throws Exception {
        if (movProduto == null) {
            movProduto = new MovProduto(this.con);
        }
        return movProduto;
    }

    /**
     * @return the plano
     * @throws Exception
     */
    private PlanoInterfaceFacade getPlano() throws Exception {
        if (plano == null) {
            plano = new Plano(this.con);
        }
        return plano;
    }

    private PlanoEmpresa getPlanoEmpresa() throws Exception {
        if (planoempresa == null) {
            planoempresa = new PlanoEmpresa(this.con);
        }
        return planoempresa;
    }

    /**
     * @return the contrato
     * @throws Exception
     */
    private ContratoInterfaceFacade getContrato() throws Exception {
        if (contrato == null) {
            contrato = new Contrato(this.con);
        }
        return contrato;
    }

    /**
     * @return the contratoRecorrencia
     * @throws Exception
     */
    private ContratoRecorrenciaInterfaceFacade getContratoRecorrencia() throws Exception {
        if (contratoRecorrencia == null) {
            contratoRecorrencia = new ContratoRecorrencia(this.con);
        }
        return contratoRecorrencia;
    }

    /**
     * @return the reciboPagamento
     * @throws Exception
     */
    private ReciboPagamento getReciboPagamento() throws Exception {
        if (reciboPagamento == null) {
            reciboPagamento = new ReciboPagamento(this.con);
        }
        return reciboPagamento;
    }

    /**
     * @return the movParcela
     * @throws Exception
     */
    private MovParcelaInterfaceFacade getMovParcela() throws Exception {
        if (movParcela == null) {
            movParcela = new MovParcela(this.con);
        }
        return movParcela;
    }

    private MovParcelaTentativaConvenioInterfaceFacade getMovParcelaTentativaConvenio() throws Exception {
        if (movParcelaTentativaConvenio == null) {
            movParcelaTentativaConvenio = new MovParcelaTentativaConvenio(this.con);
        }
        return movParcelaTentativaConvenio;
    }

    /**
     * @return the movProdutoParcela
     * @throws Exception
     */
    private MovProdutoParcela getMovProdutoParcela() throws Exception {
        if (movProdutoParcela == null) {
            movProdutoParcela = new MovProdutoParcela(this.con);
        }
        return movProdutoParcela;
    }

    /**
     * @return the movPagamento
     * @throws Exception
     */
    private MovPagamentoInterfaceFacade getMovPagamento() throws Exception {
        if (movPagamento == null) {
            movPagamento = new MovPagamento(this.con);
        }
        return movPagamento;
    }

    /**
     * @return the listaSelectItemMovPagamentoVOs
     */
    private List<MovPagamentoVO> getListaSelectItemMovPagamentoVOs() {
        if (listaSelectItemMovPagamentoVOs == null) {
            listaSelectItemMovPagamentoVOs = new ArrayList<MovPagamentoVO>();
        }
        return listaSelectItemMovPagamentoVOs;
    }

    /**
     * @param listaSelectItemMovPagamentoVOs the listaSelectItemMovPagamentoVOs
     * to set
     */
    private void setListaSelectItemMovPagamentoVOs(List<MovPagamentoVO> listaSelectItemMovPagamentoVOs) {
        this.listaSelectItemMovPagamentoVOs = listaSelectItemMovPagamentoVOs;
    }

    /**
     * @return the movPagamento
     * @throws Exception
     */
    private ClienteInterfaceFacade getCliente() throws Exception {
        if (cliente == null) {
            cliente = new Cliente(this.con);
        }
        return cliente;
    }

    private UsuarioMovelInterfaceFacade getUsuarioMovel() throws Exception{
        if (usuarioMovel == null) {
            usuarioMovel = new UsuarioMovel(this.con);
        }
        return usuarioMovel;
    }

    private void setUsuarioMovel(UsuarioMovelInterfaceFacade usuarioMovel) {
        this.usuarioMovel = usuarioMovel;
    }

    private LogInterfaceFacade getLog() throws Exception {
        if (log == null) {
            log = new Log(this.con);
        }
        return log;
    }

    private void setLog(LogInterfaceFacade log) {
        this.log = log;
    }

    /**
     * @return the movPagamento
     * @throws Exception
     */
    private AgendaInterfaceFacade getAgenda() throws Exception {
        if (agenda == null) {
            agenda = new Agenda(this.con);
        }
        return agenda;
    }

    /**
     * @return the movPagamento
     * @throws Exception
     */
    private FecharMetaInterfaceFacade getFecharMeta() throws Exception {
        if (fecharMeta == null) {
            fecharMeta = new FecharMeta(this.con);
        }
        return fecharMeta;
    }

    /**
     * @return the matriculaAlunoHorarioTurma
     * @throws Exception
     */
    private MatriculaAlunoHorarioTurmaInterfaceFacade getMatriculaAlunoHorarioTurma() throws Exception {
        if (matriculaAlunoHorarioTurma == null) {
            matriculaAlunoHorarioTurma = new MatriculaAlunoHorarioTurma(this.con);
        }
        return matriculaAlunoHorarioTurma;
    }

    private HorarioTurmaInterfaceFacade getHorarioTurma() throws Exception {
        if (horarioTurma == null) {
            horarioTurma = new HorarioTurma(this.con);
        }
        return horarioTurma;
    }

    /**
     * @return the contratoOperacao
     * @throws Exception
     */
    private ContratoOperacaoInterfaceFacade getContratoOperacao() throws Exception {
        if (contratoOperacao == null) {
            contratoOperacao = new ContratoOperacao(this.con);
        }
        return contratoOperacao;
    }

    /**
     * @return the formaPagamento
     * @throws Exception
     */
    private FormaPagamentoInterfaceFacade getFormaPagamento() throws Exception {
        if (formaPagamento == null) {
            formaPagamento = new FormaPagamento(this.con);
        }
        return formaPagamento;
    }

    /**
     * @return the matriculaAlunoHorarioTurma
     * @throws Exception
     */
    private UsuarioInterfaceFacade getUsuario() throws Exception {
        if (usuario == null) {
            usuario = new Usuario(this.con);
        }
        return usuario;
    }

    /**
     * @return the justificativaOperacao
     * @throws Exception
     */
    private JustificativaOperacaoInterfaceFacade getJustificativaOperacao() throws Exception {
        if (justificativaOperacao == null) {
            justificativaOperacao = new JustificativaOperacao(this.con);
        }
        return justificativaOperacao;
    }

    /**
     * @return the movimentoContaCorrenteCliente
     * @throws Exception
     */
    private MovimentoContaCorrenteClienteInterfaceFacade getMovimentoContaCorrenteCliente() throws Exception {
        if (movimentoContaCorrenteCliente == null) {
            movimentoContaCorrenteCliente = new MovimentoContaCorrenteCliente(this.con);
        }
        return movimentoContaCorrenteCliente;
    }

    /**
     * @return the cheque
     * @throws Exception
     */
    private Cheque getCheque() throws Exception {
        if (cheque == null) {
            cheque = new Cheque(this.con);
        }
        return cheque;
    }

    /**
     * @return the situacaoClienteSinteticoDW
     * @throws Exception
     */
    private SituacaoClienteSinteticoDW getSituacaoClienteSinteticoDW() throws Exception {
        if (situacaoClienteSinteticoDW == null) {
            situacaoClienteSinteticoDW = new SituacaoClienteSinteticoDW(this.con);
        }
        return situacaoClienteSinteticoDW;
    }

    /**
     * @return the profissao
     * @throws Exception
     */
    private ProfissaoInterfaceFacade getProfissao() throws Exception {
        if (profissao == null) {
            profissao = new Profissao(this.con);
        }
        return profissao;
    }

    /**
     * @return the questionarioCliente
     * @throws Exception
     */
    private QuestionarioClienteInterfaceFacade getQuestionarioCliente() throws Exception {
        if (questionarioCliente == null) {
            questionarioCliente = new QuestionarioCliente(this.con);
        }
        return questionarioCliente;
    }

    /**
     * @return the historicoContato
     * @throws Exception
     */
    private HistoricoContatoInterfaceFacade getHistoricoContato() throws Exception {
        if (historicoContato == null) {
            historicoContato = new HistoricoContato(this.con);
        }
        return historicoContato;
    }

    /**
     * @return the acessoCliente
     * @throws Exception
     */
    private AcessoClienteInterfaceFacade getAcessoCliente() throws Exception {
        if (acessoCliente == null) {
            acessoCliente = new AcessoCliente(this.con);
        }
        return acessoCliente;
    }

    /**
     * @return the clienteMensagem
     * @throws Exception
     */
    private ClienteMensagemInterfaceFacade getClienteMensagem() throws Exception {
        if (clienteMensagem == null) {
            clienteMensagem = new ClienteMensagem(this.con);
        }
        return clienteMensagem;
    }

    /**
     * @return the planoRecorrencia
     * @throws Exception
     */
    private PlanoRecorrencia getPlanoRecorrencia() throws Exception {
        if (planoRecorrencia == null) {
            planoRecorrencia = new PlanoRecorrencia(this.con);
        }
        return planoRecorrencia;
    }

    /**
     * @return the contratoModalidadeTurma
     * @throws Exception
     */
    private ContratoModalidadeTurmaInterfaceFacade getContratoModalidadeTurma() throws Exception {
        if (contratoModalidadeTurma == null) {
            contratoModalidadeTurma = new ContratoModalidadeTurma(this.con);
        }
        return contratoModalidadeTurma;
    }

    /**
     * @return the ContratoModalidadeProdutoSugerido
     * @throws Exception
     */
    private ContratoModalidadeProdutoSugerido getContratoModalidadeProdutoSugerido() throws Exception {
        if (contratoModalidadeProdutoSugerido == null) {
            contratoModalidadeProdutoSugerido = new ContratoModalidadeProdutoSugerido(this.con);
        }
        return contratoModalidadeProdutoSugerido;
    }

    /**
     * @return the ContratoModalidadeProdutoSugerido
     * @throws Exception
     */
    private ContratoModalidadeVezesSemana getContratoModalidadeVezesSemana() throws Exception {
        if (contratoModalidadeVezesSemana == null) {
            contratoModalidadeVezesSemana = new ContratoModalidadeVezesSemana(this.con);
        }
        return contratoModalidadeVezesSemana;
    }

    /**
     * @return the contratoModalidadeHorarioTurma
     * @throws Exception
     */
    private ContratoModalidadeHorarioTurma getContratoModalidadeHorarioTurma() throws Exception {
        if (contratoModalidadeHorarioTurma == null) {
            contratoModalidadeHorarioTurma = new ContratoModalidadeHorarioTurma(this.con);
        }
        return contratoModalidadeHorarioTurma;
    }

    /**
     * @return the pessoa
     * @throws Exception
     */
    private PessoaInterfaceFacade getPessoa() throws Exception {
        if (pessoa == null) {
            pessoa = new Pessoa(this.con);
        }
        return pessoa;
    }

    /**
     * @return the empresa
     * @throws Exception
     */
    private EmpresaInterfaceFacade getEmpresa() throws Exception {
        if (empresa == null) {
            empresa = new Empresa(this.con);
        }
        return empresa;
    }

    /**
     * @return the planoDuracao
     * @throws Exception
     */
    private PlanoDuracao getPlanoDuracao() throws Exception {
        if (planoDuracao == null) {
            planoDuracao = new PlanoDuracao(this.con);
        }
        return planoDuracao;
    }

    /**
     * @return the planoDuracao
     * @throws Exception
     */
    private PlanoCondicaoPagamento getPlanoCondicaoPagamento() throws Exception {
        if (planoCondicaoPagamento == null) {
            planoCondicaoPagamento = new PlanoCondicaoPagamento(this.con);
        }
        return planoCondicaoPagamento;
    }

    /**
     * @return the planoComposicao
     * @throws Exception
     */
    private PlanoComposicao getPlanoComposicao() throws Exception {
        if (planoComposicao == null) {
            planoComposicao = new PlanoComposicao(this.con);
        }
        return planoComposicao;
    }

    private ConfiguracaoSistemaInterfaceFacade getConfiguracaoSistema() throws Exception {
        if (configuracaoSistema == null) {
            configuracaoSistema = new ConfiguracaoSistema(this.con);
        }
        return configuracaoSistema;
    }

    /**
     * @return the planoModalidade
     * @throws Exception
     */
    private PlanoModalidade getPlanoModalidade() throws Exception {
        if (planoModalidade == null) {
            planoModalidade = new PlanoModalidade(this.con);
        }
        return planoModalidade;
    }

    /**
     * @return the planoHorario
     * @throws Exception
     */
    private PlanoHorario getPlanoHorario() throws Exception {
        if (planoHorario == null) {
            planoHorario = new PlanoHorario(this.con);
        }
        return planoHorario;
    }

    /**
     * @return the planoProdutoSugerido
     * @throws Exception
     */
    private PlanoProdutoSugeridoInterfaceFacade getPlanoProdutoSugerido() throws Exception {
        if (planoProdutoSugerido == null) {
            planoProdutoSugerido = new PlanoProdutoSugerido(this.con);
        }
        return planoProdutoSugerido;
    }

    private ControleTaxaPersonalInterfaceFacade getControleTaxaPersonal() throws Exception {
        if (controleTaxaPersonal == null) {
            controleTaxaPersonal = new ControleTaxaPersonal(this.con);
        }
        return controleTaxaPersonal;
    }

    private RemessaInterfaceFacade getRemessa() throws Exception {
        if (remessa == null) {
            remessa = new Remessa(this.con);
        }
        return remessa;
    }

    private RemessaItemInterfaceFacade getRemessaItem() throws Exception {
        if (remessaItem == null) {
            remessaItem = new RemessaItem(this.con);
        }
        return remessaItem;
    }

    private RemessaCancelamentoItemInterfaceFacade getRemessaCancelamentoItem() throws Exception {
        if (remessaCancelamentoItem == null) {
            remessaCancelamentoItem = new RemessaCancelamentoItem(this.con);
        }
        return remessaCancelamentoItem;
    }

    private TransacaoInterfaceFacade getTransacao() throws Exception {
        if (transacao == null) {
            transacao = new Transacao(this.con);
        }
        return transacao;
    }

    private ProdutoInterfaceFacade getProduto() throws Exception {
        if (produto == null) {
            produto = new Produto(this.con);
        }
        return produto;
    }

    private TransacaoMovParcelaInterfaceFacade getTransacaoMovParcela() throws Exception {
        if (transacaoMovParcela == null) {
            transacaoMovParcela = new TransacaoMovParcela(this.con);
        }
        return transacaoMovParcela;
    }

    private OperadoraCartaoInterfaceFacade getOperadoraCartao() throws Exception {
        if (operadoraCartao == null) {
            operadoraCartao = new OperadoraCartao(this.con);
        }
        return operadoraCartao;
    }

    private FecharMetaDetalhadoInterfaceFacade getFecharMetaDetalhado() throws Exception {
        if (fecharMetaDetalhado == null) {
            fecharMetaDetalhado = new FecharMetaDetalhado(this.con);
        }
        return fecharMetaDetalhado;
    }

    private void setFecharMetaDetalhado(FecharMetaDetalhadoInterfaceFacade fecharMetaDetalhado) {
        this.fecharMetaDetalhado = fecharMetaDetalhado;
    }

    private CartaoCredito getCartao() throws Exception {
        if (cartao == null) {
            cartao = new CartaoCredito(this.con);
        }
        return cartao;
    }

    private AberturaMetaInterfaceFacade getAberturaMeta() throws Exception {
        if (aberturaMeta == null) {
            aberturaMeta = new AberturaMeta(this.con);
        }
        return aberturaMeta;
    }

    private AutorizacaoCobrancaClienteInterfaceFacade getAutorizacaoCobrancaCliente() throws Exception {
        if (autorizacaoCobrancaCliente == null) {
            autorizacaoCobrancaCliente = new AutorizacaoCobrancaCliente(this.con);
        }
        return autorizacaoCobrancaCliente;
    }

    private AutorizacaoCobrancaColaboradorInterfaceFacade getAutorizacaoCobrancaColaborador() throws Exception {
        if (autorizacaoCobrancaColaborador == null) {
            autorizacaoCobrancaColaborador = new AutorizacaoCobrancaColaborador(this.con);
        }
        return autorizacaoCobrancaColaborador;
    }

    private AtestadoInterfaceFacade getAtestado() throws Exception {
        if (atestado == null) {
            atestado = new Atestado(this.con);
        }
        return atestado;
    }

    private ContratoDuracaoCreditoTreinoInterfaceFacade getContratoDuracaoCreditoTreino() throws Exception {
        if (contratoDuracaoCreditoTreino == null) {
            contratoDuracaoCreditoTreino = new ContratoDuracaoCreditoTreino(this.con);
        }
        return contratoDuracaoCreditoTreino;
    }

    private ZillyonWebFacade getZwFacade() throws Exception {
        if(zwFacade == null){
            zwFacade = new ZillyonWebFacade(this.con);
        }
        return zwFacade;
    }

    private Prorata getProrata() {
        return prorata;
    }

    private void setProrata(Prorata prorata) {
        this.prorata = prorata;
    }

    private double getValorPrimeiraParcela() {
        return valorPrimeiraParcela;
    }

    private void setValorPrimeiraParcela(double valorPrimeiraParcela) {
        this.valorPrimeiraParcela = valorPrimeiraParcela;
    }

    private Integer getNumeroParcelaContrato() {
        return numeroParcelaContrato;
    }

    private void setNumeroParcelaContrato(Integer numeroParcelaContrato) {
        this.numeroParcelaContrato = numeroParcelaContrato;
    }

    private Boolean getParcelaAvistaContrato() {
        return parcelaAvistaContrato;
    }

    private void setParcelaAvistaContrato(Boolean parcelaAvistaContrato) {
        this.parcelaAvistaContrato = parcelaAvistaContrato;
    }

    private int getQuantidadeParcelasValorDiferente() {
        return quantidadeParcelasValorDiferente;
    }

    private void setQuantidadeParcelasValorDiferente(int quantidadeParcelasValorDiferente) {
        this.quantidadeParcelasValorDiferente = quantidadeParcelasValorDiferente;
    }

    public boolean isFluxoContratoEncerramentoDia() {
        return fluxoContratoEncerramentoDia;
    }

    public void setFluxoContratoEncerramentoDia(boolean fluxoContratoEncerramentoDia) {
        this.fluxoContratoEncerramentoDia = fluxoContratoEncerramentoDia;
    }
}
