package servicos.vendasonline;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.atualizadb.processo.GerarReciboTransacao;
import br.com.pactosolucoes.autorizacaocobranca.controle.util.ValidaBandeira;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayCobrancaAntecipadaDTO;
import br.com.pactosolucoes.notificacao.ServicoNotificacaoPush;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.dto.ParamAlunoAulaCheiaTO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.ServiceException;
import controle.basico.VinculoControle;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteObservacaoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.ConviteVO;
import negocio.comuns.basico.EmailPagamentoTO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.PactoPayCobrancaAntecipadaVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayConfigVO;
import negocio.comuns.basico.PerguntaClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaClienteVO;
import negocio.comuns.basico.QuestionarioPerguntaVO;
import negocio.comuns.basico.QuestionarioVO;
import negocio.comuns.basico.RespostaPergClienteVO;
import negocio.comuns.basico.RespostaPerguntaVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.FreePassVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.TaxaCartaoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoBloqueioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoOperacaoContaCorrenteEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.plano.PlanoCategoriaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.comuns.utilitarias.validator.CreditCardValidator;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.CampanhaDuracao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.ClienteObservacao;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Convite;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.HistoricoPontos;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.basico.PactoPayCobrancaAntecipada;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.basico.PactoPayConfig;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Questionario;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.PeriodoAcessoCliente;
import negocio.facade.jdbc.crm.*;
import negocio.facade.jdbc.crm.optin.Optin;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.facade.jdbc.financeiro.TaxaCartao;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.oamd.CampanhaCupomDesconto;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Produto;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.vendas.ImagensAcademiaVendasVO;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import negocio.facade.jdbc.vendas.VendasOnlineCampanhaIcv;
import negocio.facade.jdbc.vendas.VendasOnlineConvenio;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioTentativa;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioTentativaVO;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioVO;
import negocio.facade.jdbc.vendas.VendasOnlineVenda;
import negocio.interfaces.crm.AgendaInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoVO;
import negocio.oamd.RedeEmpresaVO;
import negocio.vendasOnline.TokenVendasOnline;
import negocio.vendasOnline.TokenVendasOnlineVO;
import org.apache.commons.lang.SerializationUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.email.EmailVoucherClienteIndicacao;
import servicos.impl.gatewaypagamento.PagamentoService;
import servicos.impl.oamd.OAMDService;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;
import servicos.integracao.impl.integracaoBotConversaService.IntegracaoBotConversaService;
import servicos.integracao.impl.integracaoSistema.IntegracaoSistemaService;
import servicos.pix.PixEmailService;
import servicos.pix.PixPagamentoService;
import servicos.pix.PixStatusEnum;
import servicos.propriedades.PropsService;
import servicos.treino.TreinoService;
import servicos.util.ExecuteRequestHttpService;
import servicos.vendasonline.dto.ConvenioPagamentoVendasOnlineDTO;
import servicos.vendasonline.dto.DadosTokenDTO;
import servicos.vendasonline.dto.InclusaoAutorizacaoVendasDTO;
import servicos.vendasonline.dto.NowLocationIpVendaDTO;
import servicos.vendasonline.dto.ParcelaDTO;
import servicos.vendasonline.dto.RetornoVendaDTO;
import servicos.vendasonline.dto.RetornoVendaTO;
import servicos.vendasonline.dto.VendaDTO;
import servicos.vendasonline.dto.VendaProdutoDTO;
import servicos.vendasonline.dto.VendasOnlineIcvDTO;
import servlet.cobranca.OperacaoVendasOnlineDTO;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import static controle.arquitetura.SuperControle.registrarLogObjetoVO;
import static java.util.Arrays.asList;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class VendasOnlineService {

    private static final Map<String, RedeEmpresaVO> redeEmpresaVosJaConsultados = new HashMap<>();
    private static final boolean ENVIAR_EMAIL_VENDAS_ONLINE = PropsService.isTrue(PropsService.enviarEmailVendasOnline);
    private final Connection con;
    private final Usuario usuarioDAO;
    private final Empresa empresaDAO;
    private final Cliente clienteDAO;
    private final Pessoa pessoaDAO;
    private final AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO;
    private final MovParcela movParcelaDAO;
    private final Transacao transacaoDAO;
    private final Contrato contratoDAO;
    private final Produto produtoDAO;
    private final VendaAvulsa vendaAvulsaDAO;
    private final AulaAvulsaDiaria aulaAvulsaDiariaDAO;
    private final ConvenioCobranca convenioCobrancaDAO;
    private final FormaPagamento formaPagamentoDAO;
    private final VendasOnlineConvenioTentativa vendasOnlineConvenioTentativaDAO;
    private final VendasConfig vendasConfigDAO;
    private final Log logDAO;
    private final IntegracaoCadastros integracaoCadastrosDAO;
    private final ZillyonWebFacade zwFacade;
    private final PixPagamentoService pixPagamentoService;
    private final Pix pixDAO;
    private final MovPagamento movPagamentoDAO;
    private final ConfiguracaoSistema configuracaoSistemaDAO;
    private final Boleto boletoDAO;
    private final PactoPayConfig pactoPayConfigDAO;
    private final PactoPayCobrancaAntecipada cobrancaAntecipadaDAO;
    private final PactoPayComunicacao pactoPayComunicacaoDAO;
    private final TokenVendasOnline tokenVendasOnlineDAO;
    private final VendasOnlineVenda vendasOnlineVendaDAO;
    private final Questionario questionarioDao;
    private final QuestionarioCliente questionarioClienteDao;
    private final Colaborador colaboradorDao;
    private final VendasOnlineCampanhaIcv vendasOnlineCampanhaIcvDao;
    private final TaxaCartao taxaCartaoDAO;
    private final OperadoraCartao operadoraCartaoDAO;
    private final Adquirente adquirenteDAO;
    private final VendasOnlineConvenio vendasOnlineConvenioDAO;
    private final Email emailDAO;
    private final ConfiguracaoSistemaCRM configuracaoSistemaCRMDAO;
    private final MalaDireta malaDiretaDAO;
    protected ClienteVO copiaClienteVO;
    private final Plano planoDAO;

    public VendasOnlineService(String key, Connection con) throws Exception {
        this.con = con;
        this.usuarioDAO = new Usuario(con);
        this.empresaDAO = new Empresa(con);
        this.clienteDAO = new Cliente(con);
        this.pessoaDAO = new Pessoa(con);
        this.zwFacade = new ZillyonWebFacade(con);
        this.autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);
        this.movParcelaDAO = new MovParcela(con);
        this.transacaoDAO = new Transacao(con);
        this.contratoDAO = new Contrato(con);
        this.vendaAvulsaDAO = new VendaAvulsa(con);
        this.aulaAvulsaDiariaDAO = new AulaAvulsaDiaria(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.formaPagamentoDAO = new FormaPagamento(con);
        this.vendasOnlineConvenioTentativaDAO = new VendasOnlineConvenioTentativa(con);
        this.vendasConfigDAO = new VendasConfig(con);
        this.logDAO = new Log(con);
        this.integracaoCadastrosDAO = new IntegracaoCadastros(con);
        this.produtoDAO = new Produto(con);
        this.pixPagamentoService = new PixPagamentoService(con);
        this.pixDAO = new Pix(con);
        this.movPagamentoDAO = new MovPagamento(con);
        this.configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        this.boletoDAO = new Boleto(con);
        this.pactoPayConfigDAO = new PactoPayConfig(con);
        this.cobrancaAntecipadaDAO = new PactoPayCobrancaAntecipada(con);
        this.pactoPayComunicacaoDAO = new PactoPayComunicacao(con);
        this.tokenVendasOnlineDAO = new TokenVendasOnline(con);
        this.vendasOnlineVendaDAO = new VendasOnlineVenda(con);
        this.questionarioDao = new Questionario(con);
        this.questionarioClienteDao = new QuestionarioCliente(con);
        this.colaboradorDao = new Colaborador(con);
        this.vendasOnlineCampanhaIcvDao = new VendasOnlineCampanhaIcv(con);
        this.taxaCartaoDAO = new TaxaCartao(con);
        this.operadoraCartaoDAO = new OperadoraCartao(con);
        this.adquirenteDAO = new Adquirente(con);
        this.vendasOnlineConvenioDAO = new VendasOnlineConvenio(con);
        this.emailDAO = new Email(con);
        this.configuracaoSistemaCRMDAO = new ConfiguracaoSistemaCRM(con);
        this.malaDiretaDAO = new MalaDireta(con);
        this.planoDAO = new Plano(con);
    }

    public RetornoVendaDTO incluirAlunoVendaApp(String key, VendaDTO vendaDTO) throws Exception {
        List<VendaDTO> listaVendaDTO = new ArrayList<>();
        listaVendaDTO.add(vendaDTO);

        List<RetornoVendaTO> lista = incluirAlunoVenda(key, listaVendaDTO, false, null, false);
        enviaNotificacaoVendaProdutoEDiaria(key, vendaDTO, lista);
        return new RetornoVendaDTO(lista.get(0));
    }

    public String incluirAlunoEVendaOnline(String key, VendaDTO vendaDTO, Connection con) throws Exception {
        List<VendaDTO> listaVendaDTO = new ArrayList<>();
        listaVendaDTO.add(vendaDTO);
        List<RetornoVendaTO> lista = incluirAlunoVenda(key, listaVendaDTO, false, null, true);
        try {
            ServicoNotificacaoPush.enviaNotificacaoContrato(key, lista.get(0).getVendaAvulsaVO().getEmpresa().getCodigo(), false,
                    "Vendas Online", lista.get(0).getVendaAvulsaVO().getValorTotal(), lista.get(0).getContratoVO().getPlano().getDescricao(),
                    lista.get(0).getContratoVO().getContratoDuracao().getNumeroMeses(), lista.get(0).getVendaAvulsaVO().getEmpresa().getNome(), lista.get(0).getClienteVO().getCodigo(), con, "Vendas Online");

        } catch (Exception ignore) {
        }
        enviaNotificacaoVendaProdutoEDiaria(key, vendaDTO, lista);

        try{
            Integer codigoCliente = lista !=  null && !lista.isEmpty() && lista.get(0).getClienteVO() != null ? lista.get(0).getClienteVO().getCodigo() : 0;
            Integer codigoEmpresa =  lista !=  null && !lista.isEmpty() && lista.get(0).getVendaAvulsaVO() != null && lista.get(0).getVendaAvulsaVO().getEmpresa() != null ? lista.get(0).getVendaAvulsaVO().getEmpresa().getCodigo() : 0;
            codigoEmpresa = codigoEmpresa > 0 ? codigoEmpresa : vendaDTO.getUnidade();
            ServicoNotificacaoPush.enviarMensagemConversasAi(key, codigoCliente, codigoEmpresa, con);
            ServicoNotificacaoPush.enviarNotificacaoBiConversas(key,codigoCliente, codigoEmpresa, con, lista.get(0));
        }catch (Exception ignore) {}

        return lista.get(0).getMsgRetorno();
    }

    private void enviaNotificacaoVendaProdutoEDiaria(String key, VendaDTO vendaDTO, List<RetornoVendaTO> lista) {
        try {
            if (!UteisValidacao.emptyNumber(vendaDTO.getOrigemSistema()) && vendaDTO.getOrigemSistema().equals(OrigemSistemaEnum.APP_FLOW.getCodigo())) {
                for (RetornoVendaTO retornoVendaTO : lista) {
                    if (retornoVendaTO.getVendaDTO() != null) {
                        for (VendaProdutoDTO vendaProdutoDTO : retornoVendaTO.getVendaDTO().getProdutos()) {
                            vendaProdutoDTO.setDescricao(!UteisValidacao.emptyString(vendaProdutoDTO.getDescricao()) ? vendaProdutoDTO.getDescricao() :
                                    produtoDAO.consultarPorCodigo(vendaProdutoDTO.getProduto(), Uteis.NIVELMONTARDADOS_MINIMOS).getDescricao());
                            if (!UteisValidacao.emptyString(vendaProdutoDTO.getDescricao())) {
                                ServicoNotificacaoPush.enviaNotificacaoVendaProdutoAppDoGestor(key, vendaProdutoDTO, retornoVendaTO.getClienteVO());
                            }
                        }
                    }
                }

                for (RetornoVendaTO retornoVendaTO : lista) {
                    if (retornoVendaTO.getDiariaVO() != null) {
                        AulaAvulsaDiariaVO aulaAvulsaDiariaVO = retornoVendaTO.getDiariaVO();
                        ServicoNotificacaoPush.enviaNotificacaoVendaDiariaAppDoGestor(key, aulaAvulsaDiariaVO, retornoVendaTO.getClienteVO());
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, VendasOnlineService.class);
        }

    }

    public List<RetornoVendaTO> incluirAlunoEVendaOnlineRetornaLista(String key, VendaDTO vendaDTO) throws Exception {
        List<VendaDTO> listaVendaDTO = new ArrayList<>();
        listaVendaDTO.add(vendaDTO);
        List<RetornoVendaTO> retornoVendaTOES = incluirAlunoVenda(key, listaVendaDTO, false, null, false);

        enviaNotificacaoVendaProdutoEDiaria(key, vendaDTO, retornoVendaTOES);
        return retornoVendaTOES;
    }

    public List<RetornoVendaTO> incluirAlunoVenda(String key, List<VendaDTO> listaVendaDTO,
                                                  boolean pactoStore, Integer codigoFinanceiroPagador, Boolean atualizarSintetico) throws Exception {

        if (UteisValidacao.emptyList(listaVendaDTO)) {
            throw new Exception("Nenhuma venda informada.");
        }

        if (listaVendaDTO.get(0).getPlano() <= 0 && listaVendaDTO.get(0).getProdutos().isEmpty()) {
            throw new Exception("Nenhum produto ou plano selecionado!");
        }

        Integer empresa = listaVendaDTO.get(0).getUnidade();
        for (VendaDTO vendaDTO : listaVendaDTO) {
            if (!empresa.equals(vendaDTO.getUnidade())) {
                throw new Exception("As vendas devem ser da mesma unidade.");
            }
        }

        if (pactoStore && UteisValidacao.emptyNumber(codigoFinanceiroPagador)) {
            throw new Exception("Código Financeiro pagador não informado ZillyonWeb.");
        }

        String msgRetorno = "";
        List<RetornoVendaTO> retornoVendaTOList = new ArrayList<>();
        UsuarioVO usuarioVO = obterUsuarioVendasOnline(listaVendaDTO.get(0));
        TransacaoVO transacaoVO = null;
        EmpresaVO empresaVO = null;
        ClienteVO clienteVO = null;
        PlanoVO planoVO = null;
        Boolean usaConfigPrimeiraCobrancaPixEGuardarCartao = obterConfigPrimeiraCobrancaPixEGuardarCartao(empresa);
        try {
            boolean cobrarPrimeiraParcela = obterConfigPrimeiraParcela(empresa);
            boolean incluirAutoBoleto = obterConfigCriarAutorizacaoCobrancaBoleto(empresa);
            boolean freePass = false;
            ConvenioPagamentoVendasOnlineDTO convenioPadraoVendas = obterConvenioPadrao(listaVendaDTO.get(0), empresa, false);
            empresaVO = this.empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            InclusaoAutorizacaoVendasDTO inclusaoAutorizacaoDTO = new InclusaoAutorizacaoVendasDTO();
            OrigemCobrancaEnum origemCobrancaEnum = (pactoStore ? OrigemCobrancaEnum.PACTO_STORE : OrigemCobrancaEnum.VENDAS_ONLINE_VENDA);

            //realiza todas as vendas.. Contrato e Venda Avulsa
            ConvenioPagamentoVendasOnlineDTO convenioCobrancaUtilizarCobranca = null;
            boolean vendaConsultor = false;
            boolean vendaContratoPix = false;
            boolean vendaContratoBoleto = false;
            List<VendasOnlineIcvDTO> listaIcv = new ArrayList<>();
            ContratoVO contratoASerRenovado = null;
            ContratoVO contratoASerRematriculado = null;
            for (VendaDTO vendaDTO : listaVendaDTO) {

                vendaConsultor = vendaDTO.isVendaConsultor() ? true : vendaConsultor;
                vendaContratoPix = vendaDTO.isPix() ? true : vendaContratoPix;
                vendaContratoBoleto = vendaDTO.isBoleto() ? true : vendaContratoBoleto;

                RetornoVendaTO retDTO = new RetornoVendaTO(vendaDTO);
                String situacaoContrato = null;
                try {
                    //incluir o cliente
                    clienteVO = incluirObterCliente(vendaDTO, usuarioVO, pactoStore, null, atualizarSintetico);
                    if (clienteVO != null && !Util.isEmptyString(vendaDTO.getRg()) &&
                            (clienteVO.getPessoa() != null && clienteVO.getPessoa().getRg() != null && !clienteVO.getPessoa().getRg().equals(vendaDTO.getRg()))) {
                        atualizarRGCliente(vendaDTO, clienteVO);
                    }
                    retDTO.setMatricula(clienteVO.getMatricula());
                    retDTO.setCodigoCliente(clienteVO.getCodigo());
                    retDTO.setClienteVO(clienteVO);

                    // incluir Resposta ParQ
                    try {
                        if (isNotBlank(vendaDTO.getRespostaParqJson())) {
                            String urlTreino = PropsService.getPropertyValue(key, PropsService.urlTreino);
                            ExecuteRequestHttpService.post(urlTreino + "/prest/avaliacao/"
                                    + key + "/responderParq?matricula=" + clienteVO.getMatricula() +
                                    "&assinatura=consentimento-pelo-vendas-online2.0", vendaDTO.getRespostaParqJson(), new HashMap<>());
                        }
                    } catch (Exception ex) {
                        Uteis.logar(null, String.format("ParqVendasOnline -> erro ao realizar chamada %s", ex.getMessage()));
                    }

                    // incluir Localizacao e Ip Cliente
                    try {
                        if (isNotBlank(vendaDTO.getNowLocationIp())) {
                            clienteDAO.incluirIpLocalizacaoCliente(clienteVO, vendaDTO.getNowLocationIp());
                        }
                    } catch (Exception ex) {
                    }

                    //incluir FreePass
                    if (!UteisValidacao.emptyList(vendaDTO.getProdutos()) && vendaDTO.getProdutos().size() > 0) {
                        Produto produto = new Produto(con);
                        for (VendaProdutoDTO vendaProdutoDTO : vendaDTO.getProdutos()) {
                            ProdutoVO produtoVO = produto.consultarPorCodigoProduto(vendaProdutoDTO.getProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            if (produtoVO.getTipoProduto().equals(TipoProduto.FREEPASS.getCodigo())) {
                                if (vendaProdutoDTO.getDataInicio().before(Uteis.getDataComHoraZerada(Calendario.hoje()))) {
                                    throw new Exception("A data de início do Free Pass não pode ser menor que a data atual.");
                                }
                                Contrato contrato = new Contrato(con);
                                if (contrato.consultarDiasVigenciaContratoAtivo(clienteVO) > 0) {
                                    throw new Exception("O aluno possui um contrato ativo. Não é possível incluir um Free Pass.");
                                }
                                PeriodoAcessoCliente periodoAcessoCliente = new PeriodoAcessoCliente(con);
                                PeriodoAcessoClienteVO periodoFreePass = periodoAcessoCliente.consultarUltimoPorDataPessoaTipoAcesso(Calendario.hoje(), clienteVO.getPessoa().getCodigo(),
                                        SituacaoEnum.FREE_PASS.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                                if (periodoFreePass != null) {
                                    throw new Exception("Existe um Free Pass para este aluno para o período de " + Uteis.getData(periodoFreePass.getDataInicioAcesso()) + " até " + Uteis.getData(periodoFreePass.getDataFinalAcesso()) + ". É necessário excluir esse Free Pass para lançar outro.");
                                }
                                Cliente cliente = new Cliente(con);
                                clienteVO.setResponsavelFreePass(vendaDTO.getCodigoColaborador());
                                clienteVO.getFreePass().setCodigo(vendaProdutoDTO.getProduto());
                                cliente.incluirFreePass(clienteVO, vendaProdutoDTO.getDataInicio(), null, null, null);
                                freePass = true;
                                cliente = null;
                            }
                        }
                    }
                    //validar se já existe um pix criado para o mesmo cliente e venda
                    if (vendaContratoPix) {
                        PixVO pixVO = obterPixAtivoVendaContrato(clienteVO, vendaDTO);
                        if (pixVO != null && !UteisValidacao.emptyNumber(pixVO.getCodigo())) {
                            retDTO.setPixVO(pixVO);
                            return retornoVendaTOList;
                        }
                    }

                    if (vendaContratoBoleto) {
                        List<BoletoVO> listaBoletos = obterBoletoAtivoVendaContrato(clienteVO, vendaDTO);
                        if (!UteisValidacao.emptyList(listaBoletos)) {
                            retDTO.setBoletoVO(listaBoletos.get(0));
                            retDTO.setBoletosGerados(listaBoletos);
                            return retornoVendaTOList;
                        }
                    }

                    //incluir observação
                    incluirObservacao(clienteVO, usuarioVO, vendaDTO);

                    //envia email de confirmação de recebimento de comunicações e promoções
                    if (!clienteVO.getPessoa().getEmailVOs().isEmpty()) {
                        clienteVO.getPessoa().setEmail(clienteVO.getPessoa().getEmailVOs().get(0).getEmail());
                        ConfiguracaoSistemaVO config = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                        Optin optinDAO;
                        try {
                            optinDAO = new Optin(con);
                            optinDAO.enviarEmailOptin(empresaVO, clienteVO, key, config.getSesc());
                        } catch (Exception ex) {
                            Uteis.logarDebug("Erro ao enviar email optin: " + ex.getMessage() + " | Chave: " + key + " | CodCliente: " + clienteVO.getCodigo());
                        } finally {
                            optinDAO = null;
                        }
                    }

                    //operações para Pacto Store
                    if (pactoStore) {
                        incluirPactoStoreCliente(vendaDTO, clienteVO);
                    }

                    //obter o convênio de cobrança
                    ConvenioPagamentoVendasOnlineDTO convenioAutorizacao = null;
                    ConvenioPagamentoVendasOnlineDTO convenioConfiguracaoConvenio = obterConvenioConfigPlanoProduto(vendaDTO, clienteVO.getCodigo());
                    if (convenioConfiguracaoConvenio != null) {
                        convenioAutorizacao = convenioConfiguracaoConvenio;
                    } else {
                        convenioAutorizacao = convenioPadraoVendas;
                    }

                    if (!vendaDTO.isVendaConsultor() && convenioAutorizacao == null && !vendaContratoBoleto && !vendaContratoPix) {
                        throw new Exception("Convênio de cobrança não encontrado, verifique as configurações do Vendas Online!");
                    }

                    if (!UteisValidacao.emptyNumber(vendaDTO.getPlano())) {
                        planoVO = this.planoDAO.consultarPorChavePrimaria(vendaDTO.getPlano(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                    if (!clienteVO.getSituacao().equalsIgnoreCase("VI")
                            && planoVO != null && planoVO.getBloquearRecompra()) {
                        throw new Exception("Este plano só pode ser vendido para novos alunos.");
                    }
                    //incluir a autorizacao de cobranca
                    if (!vendaDTO.isVendaConsultor() && (!vendaContratoPix || (vendaContratoBoleto && incluirAutoBoleto)) ||
                            (vendaContratoPix && usaConfigPrimeiraCobrancaPixEGuardarCartao)) {
                        if (vendaContratoBoleto) {
                            Integer convenioBoletoVendaContrato = obterConvenioBoletoVendaContrato(empresaVO.getCodigo());
                            inclusaoAutorizacaoDTO = incluirAutorizacao(clienteVO, vendaDTO, convenioBoletoVendaContrato, usuarioVO, empresaVO, origemCobrancaEnum, TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
                        } else {
                            inclusaoAutorizacaoDTO = incluirAutorizacao(clienteVO, vendaDTO, convenioAutorizacao.getConvenioCobranca(), usuarioVO, empresaVO, origemCobrancaEnum, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                        }
                    }

                    //convenio que será utilizado para cobrar
                    if (convenioCobrancaUtilizarCobranca == null) {
                        convenioCobrancaUtilizarCobranca = convenioAutorizacao;
                    }

                    //incluir contrato
                    if (!UteisValidacao.emptyNumber(vendaDTO.getPlano())) {
                        if (this.vendasConfigDAO.config(empresaVO.getCodigo()).isRenovarContratoAntigo()) {
                            contratoASerRenovado = obterContratoASerRenovadoVendasOnline(clienteVO, pactoStore);
                        }

                        if (contratoASerRenovado != null) {
                            //caso seja uma renovação deve se levar em consideração esse parametro para cobrar ou não a 1º parcela
                            cobrarPrimeiraParcela = obterConfigPrimeiraParcelaRenovacao(empresa);
                        } else {
                            contratoASerRematriculado = obterContratoASerRematriculadoVendasOnline(clienteVO, pactoStore);
                            //tem casos que a rematrícula já foi feita, mas o cliente quer lançar um concomitante.
                            //a seguencia abaixo vai avaliar esse contratoResponsavelRematriculaMatricual tem registro e concomitante está ativo
                            //se sim irá lançar uma matricula
                            //se não, segue o fluxo atual, onde se tiver contratoResponsavelRematriculaMatricual vai dar erro no banco de dados
                            if (contratoASerRematriculado != null && !UteisValidacao.emptyNumber(contratoASerRematriculado.getContratoResponsavelRematriculaMatricula()) &&
                                    contratoASerRematriculado.getEmpresa().getPermiteContratosConcomintante()) {
                                contratoASerRematriculado = null;
                            }
                        }

                        //INCLUSÃO CONTRATO
                        ContratoVO contratoVO = incluirContrato(key, clienteVO, vendaDTO, usuarioVO, contratoASerRenovado, contratoASerRematriculado, empresaVO, retDTO);

                        situacaoContrato = contratoVO.getSituacaoContrato();
                        gravarConclusaoAcessoPaginaInfoPessoa(listaIcv, vendaDTO.getCodigoRegistroAcessoPagina(), contratoVO.getPessoa().getCodigo(), vendaDTO, clienteVO, contratoVO.getCodigo(), null, null);
                        retDTO.setContrato(contratoVO.getCodigo());
                        retDTO.setContratoVO(contratoVO);
                        if (vendaDTO.isVendaConsultor()) {
                            List<MovParcelaVO> parcelasPagar = movParcelaDAO.consultarParcelasPagarContrato(contratoVO);
                            retDTO.setParcelasPagarContrato(ParcelaDTO.movParcelasToDTOList(parcelasPagar));
                            retDTO.setValorPagarContrato(parcelasPagar.stream().mapToDouble(parcelaVO -> parcelaVO.getValorParcela().doubleValue()).sum());
                        }

                        ZillyonWebFacade zwDAO = new ZillyonWebFacade(this.con);
                        if (contratoVO.isContratoRematricula() || contratoVO.isContratoRenovacao()) {
                            try {
                                zwDAO.vincularDependentesContratoRenovadoOuRematriculado(contratoVO);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                        if (contratoVO.getPlano().getQuantidadeCompartilhamentos() > 0 && !UteisValidacao.emptyList(vendaDTO.getClientesCadastradosComoDependentesPlanoCompartilhado())) {
                            try {
                                zwDAO.vincularNovosDependentes(vendaDTO, contratoVO, clienteVO);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                        zwDAO = null;

                        //Inclusão de assinatura digital (origem Pacto Flow da Pacto)
                        try {
                            if (!UteisValidacao.emptyString(vendaDTO.getAssinaturaDigital())) {
                                incluirAssinaturaDigital(key, contratoVO.getCodigo(), vendaDTO);
                            }
                        } catch (Exception ex) {
                        }
                    }

                    //incluir venda avulsa
                    if (!UteisValidacao.emptyList(vendaDTO.getProdutos()) && !freePass) {
                        VendaAvulsaVO vendaAvulsaVO = incluirVendaAvulsa(clienteVO, vendaDTO, usuarioVO);
                        retDTO.setVendaAvulsa(vendaAvulsaVO.getCodigo());
                        retDTO.setVendaAvulsaVO(vendaAvulsaVO);
                        gravarConclusaoAcessoPaginaInfoPessoa(listaIcv, vendaDTO.getCodigoRegistroAcessoPagina(), vendaAvulsaVO.getPessoaVO().getCodigo(), vendaDTO, clienteVO, null, vendaAvulsaVO.getCodigo(), null);
                        if (vendaAvulsaVO.getDiariaVendaOnline() != null) {
                            retDTO.setDiariaVO(vendaAvulsaVO.getDiariaVendaOnline());
                        }
                    }
                    gerarBV(vendaDTO, clienteVO, (situacaoContrato != null && situacaoContrato.equals("RE")));
                } catch (Exception ex) {
                    ex.printStackTrace();
                    retDTO.setMsgRetorno(ex.getMessage());
                    retDTO.setErro(false);
                    incluirLogVendasOnline(retDTO.getCodigoCliente(), vendaDTO, "ERRO_REALIZAR_VENDA", ex.getMessage());
                    throw ex;
                } finally {
                    retDTO.setErro(false);
                    retDTO.setMsgRetorno("Venda realizada com sucesso.");
                    retornoVendaTOList.add(retDTO);
                }
            }

            ClienteVO clientePagadorVO = null;

            if (pactoStore) {
                //identificar o cliente que será o responsável pelo pagamento
                clientePagadorVO = obterClientePagadorPactoStore(codigoFinanceiroPagador);
            } else {
                clientePagadorVO = (ClienteVO) retornoVendaTOList.get(0).getClienteVO().getClone(true);
            }

            if (clientePagadorVO == null || UteisValidacao.emptyNumber(clientePagadorVO.getCodigo())) {
                throw new Exception("Cliente pagador não encontrado");
            }

            //Consultar as parcelas para realizar cobrança
            List<MovParcelaVO> listaParcelas = new ArrayList<>();
            for (RetornoVendaTO retDTO : retornoVendaTOList) {
                listaParcelas.addAll(consultarParcelasParaCobrar(retDTO.getContrato(),
                        retDTO.getVendaAvulsa(),
                        retDTO.getDiariaVO() == null ? null : retDTO.getDiariaVO().getCodigo(),
                        cobrarPrimeiraParcela, vendaContratoBoleto));
            }

            if (UteisValidacao.emptyList(listaParcelas) && (!freePass && contratoASerRenovado == null) && cobrarPrimeiraParcela) {
                throw new Exception("A cobrança não pode ser processada com o valor zerado. Por favor, verifique o valor do plano ou produto ao qual está tentando vender e faça os ajustes necessários.");
            }

            //Condições especificas de um cliente que trabalha com vendas online e pré-pago, sendo que uma parcela paga pix e outra no cartão automático
            //Deve retirar as parcelas de plano do pré-pago da lista de parcelas a gerar o pix
            if (vendaContratoPix && usaConfigPrimeiraCobrancaPixEGuardarCartao) {
                List<MovParcelaVO> listaParcelasSemParcela2PrePago = new ArrayList<>();
                for (MovParcelaVO item : listaParcelas) {
                    if (!item.getDescricao().equals("PARCELA 2")) {
                        listaParcelasSemParcela2PrePago.add(item);
                    }
                }
                listaParcelas = listaParcelasSemParcela2PrePago;
            }

            PixVO pixVO = null;
            List<BoletoVO> boletosGerados = new ArrayList<>();
            //não tem parcelas para cobrar
            if (!vendaConsultor) {
                if (vendaContratoBoleto) {
                    Integer convenioBoletoVendaContrato = obterConvenioBoletoVendaContrato(empresaVO.getCodigo());
                    Integer diasVencimentoBoleto = obterConfigDiasVencimentoBoleto(empresaVO.getCodigo());
                    ConvenioCobrancaVO convenioCobrancaBoleto = this.convenioCobrancaDAO.consultarPorChavePrimaria(convenioBoletoVendaContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    if (obterConfigGerarBoletoTodasParcelas(empresa)) {

                        for (MovParcelaVO movParcelaVO : listaParcelas) {
                            if (movParcelaVO.getSituacao().equals("EA") &&
                                    Calendario.menorOuIgual(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                                movParcelaVO.setDataVencimento(Calendario.somarDias(Calendario.hoje(), diasVencimentoBoleto));
                            }
                        }

                        boletosGerados = gerarBoleto(listaParcelas, convenioCobrancaBoleto, empresa,
                                clientePagadorVO.getPessoa(), usuarioVO, origemCobrancaEnum, UteisValidacao.emptyList(listaVendaDTO) ? null : listaVendaDTO.get(0));
                    } else {

                        List<MovParcelaVO> listaParcelasGerarBoleto = new ArrayList<>();
                        for (MovParcelaVO movParcelaVO : listaParcelas) {
                            if (movParcelaVO.getSituacao().equals("EA") &&
                                    Calendario.menorOuIgual(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                                listaParcelasGerarBoleto.add(movParcelaVO);
                            }
                        }
                        Date vencimentoBoleto = Calendario.somarDias(Calendario.hoje(), diasVencimentoBoleto);
                        BoletoVO boletoVO = this.boletoDAO.gerarBoleto(clientePagadorVO.getPessoa(), convenioCobrancaBoleto, listaParcelasGerarBoleto, vencimentoBoleto, usuarioVO,
                                origemCobrancaEnum, false, null, null, true, true);

                        if (!UteisValidacao.emptyList(listaVendaDTO)) {
                            gravarPactoPayComunicacao(listaVendaDTO.get(0), null, null, boletoVO, null);
                        }

                        boletosGerados.add(boletoVO);
                    }

                } else if (vendaContratoPix) {

                    // Caso esteja marcado no Gestão de Vendas Online a configuração PrimeiraCobrancaPixEGuardarCartao
                    // Tem de validar o cartão antes de gerar o pix
                    // Se o cartão não for aprovado, deve estornar tudo.
                    if (usaConfigPrimeiraCobrancaPixEGuardarCartao) {
                        transacaoVO = realizarTransacaoDeVerificacao(empresaVO, clientePagadorVO, inclusaoAutorizacaoDTO, usuarioVO, UteisValidacao.emptyList(listaVendaDTO) ? null : listaVendaDTO.get(0));
                        transacaoNaoAprovadaDeveRetornarCartaoAntigo(transacaoVO, empresaVO, inclusaoAutorizacaoDTO);
                        marcarCartaoVerificado(transacaoVO, inclusaoAutorizacaoDTO.getNovaAutorizacao());

                        boolean transacaoAprovada = isTransacaoAprovada(transacaoVO);
                        if (!transacaoAprovada) {
                            String msgRetornoTransacao = "Falha ao tentar Verificar o Cartão de Crédito. Retorno Adquirente: ";
                            msgRetornoTransacao = msgRetornoTransacao + msgRetornoTransacao(transacaoVO);
                            //não precisa mostrar mensagem redundante aqui
                            if (msgRetornoTransacao.contains("PAC001 - Verifique o retorno da adquirente.")) {
                                msgRetornoTransacao = msgRetornoTransacao.replace("PAC001 - Verifique o retorno da adquirente.", "");
                            }
                            throw new Exception(msgRetornoTransacao);
                        }
                    }

                    if (listaParcelas.size() > 0) {
                        //pix valido por 2 horas
                        Integer convenioPixVendaContrato = obterConvenioPixVendaContrato(empresaVO.getCodigo());
                        pixVO = gerarPix(key, empresaVO, convenioPixVendaContrato,
                                clientePagadorVO, listaParcelas, origemCobrancaEnum, 0.0, 0.0, listaVendaDTO.get(0),
                                7200);
                    }
                } else if (UteisValidacao.emptyList(listaParcelas)) {
                    //se não tem parcelas para ser cobrado então vamos validar o cartão
                    transacaoVO = realizarTransacaoDeVerificacao(empresaVO, clientePagadorVO, inclusaoAutorizacaoDTO, usuarioVO, UteisValidacao.emptyList(listaVendaDTO) ? null : listaVendaDTO.get(0));
                } else {
                    Integer nrVezesDividir = UteisValidacao.emptyNumber(listaVendaDTO.get(0).getNrVezesDividir()) ? 0 : listaVendaDTO.get(0).getNrVezesDividir();
                    transacaoVO = cobrarParcelasAgrupando(listaVendaDTO.get(0), listaParcelas, nrVezesDividir, clientePagadorVO, usuarioVO,
                            convenioCobrancaUtilizarCobranca.getConvenioCobranca(), empresa, convenioCobrancaUtilizarCobranca.getTipoParcelamentoStone(),
                            origemCobrancaEnum, 0.0, retornoVendaTOList);

                    transacaoNaoAprovadaDeveRetornarCartaoAntigo(transacaoVO, empresaVO, inclusaoAutorizacaoDTO);
                }
            }

            //marcar a autorização de cobrança como verificada
            if (transacaoVO != null) {
                marcarCartaoVerificado(transacaoVO, inclusaoAutorizacaoDTO.getNovaAutorizacao());
            }

            notificarRecursoOAMD(retornoVendaTOList, transacaoVO, key, usuarioVO, empresaVO, pactoStore);

            boolean transacaoAprovada = isTransacaoAprovada(transacaoVO);

            if (!vendaConsultor && !vendaContratoBoleto) {
                if (!UteisValidacao.emptyList(listaVendaDTO)) {
                    enviarAcoesBotConversa(listaVendaDTO.get(0), transacaoVO, empresaVO, transacaoAprovada);
                }
                if (transacaoAprovada && !vendaContratoPix) { //Pagamento cartão com sucesso
                    processosPosSucesso(key, transacaoVO, empresaVO, retornoVendaTOList, usuarioVO);
                    //Aulas da agenda, quando o pagamento for pix, é marcada após a aprovação do pix lá no PixPagamentoService dentro do verificarEstornarVendaVendasOnline
                    marcarAulasAluno(key, retornoVendaTOList, listaVendaDTO, usuarioVO, false);
                    msgRetorno = "APROVADA";
                } else if (!transacaoAprovada && !vendaContratoPix) { //Pagamento cartão com negacao
                    String msgRetornoTransacao = msgRetornoTransacao(transacaoVO);
                    //não precisa mostrar mensagem redundante aqui
                    if (msgRetornoTransacao.contains("PAC001 - Verifique o retorno da adquirente.")) {
                        msgRetornoTransacao = msgRetornoTransacao.replace("PAC001 - Verifique o retorno da adquirente.", "");
                    }
                    throw new Exception(msgRetornoTransacao);
                } else if (vendaContratoPix) { //Pagamento pix, adicionado aqui para não fazer a marcação de aulas antes de aprovar o pix
                    processosPosSucesso(key, transacaoVO, empresaVO, retornoVendaTOList, usuarioVO);
                }
            }
            for (VendasOnlineIcvDTO obj : listaIcv) {
                // Após o pagamento ser aprovado, atualizar as informações do contrato e vendaavulsa.
                gravarConclusaoAcessoPaginaInfoVenda(obj.getCodigoVendasOnlineCampanhaIcv(), obj.getContrato(), obj.getVendaAvulsa(), obj.getVendaDTO(), obj.getClienteVO(), obj.getClienteVO().getPessoa().getCodigo());
            }


            for (RetornoVendaTO retDTO : retornoVendaTOList) {
                try {
                    retDTO.setPixVO(pixVO);
                    retDTO.setTransacaoVO(transacaoVO);
                    if (!UteisValidacao.emptyList(boletosGerados)) {
                        retDTO.setBoletoVO(boletosGerados.get(0));
                    }
                    retDTO.setBoletosGerados(boletosGerados);
                    this.vendasOnlineVendaDAO.incluirVendasOnlineVenda(retDTO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return retornoVendaTOList;
        } catch (Exception ex) {
            //estornar vendas, cancelar transacao e excluir convenio aluno novo em caso de erro
            ex.printStackTrace();
            msgRetorno = (ex.getMessage() != null) ? ex.getMessage() : "";
            processosPosFalha(key, transacaoVO, empresaVO, retornoVendaTOList, usuarioVO, clienteVO, msgRetorno);

            incluirLogVendasOnline(retornoVendaTOList, "ERRO_VENDAS_ONLINE", ex.getMessage());
            throw ex;
        } finally {
            for (RetornoVendaTO retDTO : retornoVendaTOList) {
                retDTO.setMsgRetorno(msgRetorno);
                incluirRegistroVenda(retDTO.getCodigoCliente(), retDTO.getVendaDTO(), retDTO, msgRetorno);
            }
            incluirLogVendasOnline(retornoVendaTOList, pactoStore ? "RESULTADO_VENDAS_ONLINE_PACTO_STORE" : "RESULTADO_VENDAS_ONLINE", msgRetorno);
        }
    }

    private boolean isTransacaoAprovada(TransacaoVO transacaoVO) {
        return transacaoVO != null && (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) ||
                transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.PENDENTE));
    }

    private void transacaoNaoAprovadaDeveRetornarCartaoAntigo(TransacaoVO transacaoVO, EmpresaVO empresaVO, InclusaoAutorizacaoVendasDTO inclusaoAutorizacaoDTO) throws Exception {
        if (transacaoVO != null && inclusaoAutorizacaoDTO.getNovaAutorizacao() != null &&
                (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) ||
                        transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) &&
                !empresaVO.isPermiteCadastrarCartaoMesmoAssim()) {
            //inativar a autorizacao
            this.autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(false, inclusaoAutorizacaoDTO.getNovaAutorizacao(), "Cobrança de verificação não autorizada");

            //colocar as autorizações que foram inativadas como ativas
            for (AutorizacaoCobrancaClienteVO auto : inclusaoAutorizacaoDTO.getAnteriores()) {
                this.autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(true, auto, "Reativar autorização devido inclusão do novo cartão não foi autorizado");
            }
        }
    }

    private void atualizarRGCliente(VendaDTO vendaDTO, ClienteVO clienteVO) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE pessoa SET rg = '").append(vendaDTO.getRg()).append("' WHERE codigo = (SELECT pessoa FROM cliente WHERE codigo = ").append(clienteVO.getCodigo()).append(")");

            SuperFacadeJDBC.executarConsulta(sql.toString(), this.con);
        } catch (Exception e) {
            System.out.println("Erro ao atualizar RG do cliente: " + e.getMessage());
        }
    }

    public void enviarAcoesBotConversa(VendaDTO vendaDTO, TransacaoVO transacaoVO, EmpresaVO empresaVO, boolean transacaoAprovada) {
        try {
            //Integração enviar informações de Vendas e tentativas via API BotConversa;
            VendasConfigVO vendasConfigVO = obterConfigsIntegracaoBotConversa(empresaVO.getCodigo());
            if (vendasConfigVO != null && vendasConfigVO.isIntegracaoBotConversa() && !UteisValidacao.emptyString(vendasConfigVO.getEnderecoEnviarAcoesBotConversa())) {
                if (transacaoAprovada) {
                    notificarWebhookBotConversa(vendaDTO, transacaoVO, "VENDA", empresaVO, vendasConfigVO);
                } else {
                    notificarWebhookBotConversa(vendaDTO, transacaoVO, "CARRINHO", empresaVO, vendasConfigVO);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void incluirAssinaturaDigital(String chave, int codContrato, VendaDTO vendaDTO) {
        try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con)) {
            try (TreinoService service = new TreinoService(connection)) {
                service.incluirAssinaturaContrato(chave, codContrato, vendaDTO.getAssinaturaDigital(), null, null, null, null, null);
                Uteis.logarDebug("Assinatura digital incluída com sucesso para o contrato " + codContrato + " | chave: " + vendaDTO.getChave());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("ERRO incluir Assinatura Digital pelo Vendas Online para o contrato " + codContrato + " | chave: " + vendaDTO.getChave() + " | erro: " + ex.getMessage());
        }
    }

    public void notificarWebhookBotConversa(VendaDTO vendaDTO, TransacaoVO transacaoVO, String operacao, EmpresaVO empresaVO, VendasConfigVO vendasConfigVO) {
        IntegracaoBotConversaService service;
        try {
            service = new IntegracaoBotConversaService(this.con);
            service.notificarWebhookBotConversa(vendaDTO, transacaoVO, operacao, empresaVO, vendasConfigVO);

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            service = null;
        }
    }

    public VendasConfigVO obterConfigsIntegracaoBotConversa(Integer codEmpresa) {
        try {
            VendasConfigVO vendasConfigVO = new VendasConfigVO();
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select integracaobotconversa, enderecoenviaracoesbotconversa from vendasonlineconfig " +
                    "where empresa = " + codEmpresa, this.con);
            if (rs.next()) {
                vendasConfigVO.setIntegracaoBotConversa(rs.getBoolean("integracaobotconversa"));
                vendasConfigVO.setEnderecoEnviarAcoesBotConversa(rs.getString("enderecoenviaracoesbotconversa"));
                return vendasConfigVO;
            }
        } catch (Exception ex) {
        }
        return null;
    }

    private void povoarListaIcvVenda(List<VendasOnlineIcvDTO> listaIcv, Integer contrato, Integer vendaAvulsa, VendaDTO vendaDTO, ClienteVO clienteVO) {
        VendasOnlineIcvDTO vendasOnlineIcvDTO = null;
        for (VendasOnlineIcvDTO obj : listaIcv) {
            if (obj.getCodigoVendasOnlineCampanhaIcv().equals(vendaDTO.getCodigoRegistroAcessoPagina())) {
                vendasOnlineIcvDTO = obj;
                break;
            }
        }
        if (vendasOnlineIcvDTO == null) {
            vendasOnlineIcvDTO = new VendasOnlineIcvDTO();
            vendasOnlineIcvDTO.setCodigoVendasOnlineCampanhaIcv(vendaDTO.getCodigoRegistroAcessoPagina());
            vendasOnlineIcvDTO.setContrato(contrato);
            vendasOnlineIcvDTO.setVendaAvulsa(vendaAvulsa);
            vendasOnlineIcvDTO.setVendaDTO(vendaDTO);
            vendasOnlineIcvDTO.setClienteVO(clienteVO);
            listaIcv.add(vendasOnlineIcvDTO);
        }
        if (contrato != null) {
            vendasOnlineIcvDTO.setContrato(contrato);
        }
        if (vendaAvulsa != null) {
            vendasOnlineIcvDTO.setVendaAvulsa(vendaAvulsa);
        }
    }


    private void gerarBV(VendaDTO vendaDTO, ClienteVO clienteVO, boolean rematriculaContrato) throws Exception {
        try {
            boolean usarUsuarioResponsavelLink = !UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel());
            QuestionarioClienteVO questionarioClienteVO = montarQuestionarioCliente(
                    clienteVO, clienteVO.getEmpresa(), vendaDTO, rematriculaContrato, usarUsuarioResponsavelLink);
            if (questionarioClienteVO != null) {
                questionarioClienteVO.setOrigemSistemaEnum(OrigemSistemaEnum.VENDAS_ONLINE_2);
                questionarioClienteVO.getEventoVO().setCodigo(vendaDTO.getCodigoEvento());
                questionarioClienteVO.setQuestionarioPerguntaClienteVOs(new ArrayList());
                for (QuestionarioPerguntaVO questionarioPerguntaVO : questionarioClienteVO.getQuestionario().getQuestionarioPerguntaVOs()) {
                    // criar as perguntas do questionario do cliente
                    PerguntaClienteVO perguntaClienteVO = new PerguntaClienteVO();
                    perguntaClienteVO.setTipoPergunta(questionarioPerguntaVO.getPergunta().getTipoPergunta());
                    perguntaClienteVO.setDescricao(questionarioPerguntaVO.getPergunta().getDescricao());
                    perguntaClienteVO.setMultipla(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("ME"));
                    perguntaClienteVO.setSimples(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("SE") || questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("SN"));
                    perguntaClienteVO.setTextual(questionarioPerguntaVO.getPergunta().getTipoPergunta().equals("TE"));

                    perguntaClienteVO.setRespostaPergClienteVOs(new ArrayList());
                    if (questionarioPerguntaVO.getPergunta().getRespostaPerguntaVOs().size() == 0) {
                        if (perguntaClienteVO.getTextual()) {
                            // criar resposta vazia para o texto
                            RespostaPergClienteVO respostaPergClienteVO = new RespostaPergClienteVO();
                            respostaPergClienteVO.setRespostaOpcao(false);
                            respostaPergClienteVO.setDescricaoRespota("");
                            perguntaClienteVO.getRespostaPergClienteVOs().add(respostaPergClienteVO);
                        }
                    } else {
                        for (RespostaPerguntaVO respostaPerguntaVO : questionarioPerguntaVO.getPergunta().getRespostaPerguntaVOs()) {
                            // criar as respostas das perguntas do questionario do cliente
                            RespostaPergClienteVO respostaPergClienteVO = new RespostaPergClienteVO();
                            respostaPergClienteVO.setRespostaOpcao(false);
                            respostaPergClienteVO.setDescricaoRespota(respostaPerguntaVO.getDescricaoRespota());
                            perguntaClienteVO.getRespostaPergClienteVOs().add(respostaPergClienteVO);
                        }
                    }
                    // criar o questionario do cliente.
                    QuestionarioPerguntaClienteVO questionarioPerguntaClienteVO = new QuestionarioPerguntaClienteVO();
                    questionarioPerguntaClienteVO.setQuestionarioCliente(questionarioClienteVO.getCodigo());
                    questionarioPerguntaClienteVO.setPerguntaCliente(perguntaClienteVO);
                    questionarioClienteVO.getQuestionarioPerguntaClienteVOs().add(questionarioPerguntaClienteVO);
                }
                questionarioClienteDao.incluirSemComit(questionarioClienteVO);
            }
        } catch (Exception e) {
            // anular a exceção e gravar o log... isso é necessário para garantir que esta alteração não impacte na venda de contratos/produtos no vendas online.
            incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_GERAR_BV", e.getMessage());
        }


    }

    private QuestionarioClienteVO montarQuestionarioCliente(ClienteVO clienteVO, EmpresaVO empresaVO, VendaDTO vendaDTO,
                                                            boolean rematriculaContrato, boolean usarUsuarioResponsavelLink) throws Exception {
        ColaboradorVO colaboradorSite = null;

        //obter colaborador do link do site que vem no parâmetro "us=" do link de cadastro de visitante
        if (usarUsuarioResponsavelLink && !UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel())) {
            try {
                colaboradorSite = buscarColaboradorSite(empresaVO, vendaDTO.getUsuarioResponsavel(), true);
            } catch (Exception e) {
                colaboradorSite = buscarColaboradorSite(empresaVO, vendaDTO.getCodigoColaborador(), false);
            }
        } else {
            //obter colaborador padrão site definido nas configurações do vendas online
            colaboradorSite = buscarColaboradorSite(empresaVO, vendaDTO.getCodigoColaborador(), false);
        }

        QuestionarioClienteVO questionarioClienteVO = new QuestionarioClienteVO();
        questionarioClienteVO.setCliente(clienteVO);
        questionarioClienteVO.setConsultor(colaboradorSite);
        questionarioClienteVO.setData(Calendario.hoje());
        TipoBVEnum tipoBVEnum = null;
        if (!vendaDTO.isClienteJaCadastrado()) {
            tipoBVEnum = TipoBVEnum.MA;
        }
        List lista = questionarioClienteDao.consultarPorCodigoCliente(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (rematriculaContrato) {
            if (lista.isEmpty()) {
                tipoBVEnum = TipoBVEnum.RE;
            } else {
                int ultimaPosicao = lista.size();
                ultimaPosicao--;
                QuestionarioClienteVO quest = (QuestionarioClienteVO) lista.get(ultimaPosicao);
                long nrdias = Uteis.nrDiasEntreDatas(quest.getData(),
                        Calendario.hoje());

                int mesValidade = Uteis.getMesData(quest.getData());

                int anoValidade = Uteis.getAnoData(quest.getData());

                int mesCorrente = Uteis.getMesData(Calendario.hoje());

                int anoCorrente = Uteis.getAnoData(Calendario.hoje());

                if (mesValidade != mesCorrente || anoValidade != anoCorrente) {
                    if (Uteis.getCompareData(quest.getData(),
                            Calendario.hoje()) < 0) {
                        tipoBVEnum = TipoBVEnum.RE;
                    }
                }
                if (nrdias > empresaVO.getNrDiasVigenteQuestionarioRematricula()) {
                    tipoBVEnum = TipoBVEnum.RE;
                }
            }
        }
        if ((tipoBVEnum == null) && (lista.isEmpty())) {
            tipoBVEnum = TipoBVEnum.MA;
        }
        if ((tipoBVEnum == null) && (empresaVO.getQuestionarioRetorno().getCodigo() != 0 && lista.size() >= 1)) {
            // validando um retorno
            int ultimaPosicao = lista.size();
            ultimaPosicao--;

            QuestionarioClienteVO quest = (QuestionarioClienteVO) lista.get(ultimaPosicao);
            long nrdias = Uteis.nrDiasEntreDatas(quest.getData(),
                    Calendario.hoje());

            int mesValidade = Uteis.getMesData(quest.getData());

            int anoValidade = Uteis.getAnoData(quest.getData());

            int mesCorrente = Uteis.getMesData(Calendario.hoje());

            int anoCorrente = Uteis.getAnoData(Calendario.hoje());

            if (mesValidade != mesCorrente || anoValidade != anoCorrente) {
                if (Uteis.getCompareData(quest.getData(),
                        Calendario.hoje()) < 0) {
                    tipoBVEnum = TipoBVEnum.RT;
                }
            }
            if (nrdias > empresaVO.getNrDiasVigenteQuestionarioRetorno()) {
                tipoBVEnum = TipoBVEnum.RT;
            }
        }
        if (tipoBVEnum == null) {
            // Não gerar bv, pois não se enquadrou nas regras de geração de BV(Rematricula, Retorno)
            return null;
        }
        QuestionarioVO questionarioVO = null;
        if (tipoBVEnum == TipoBVEnum.MA) {
            if (empresaVO.getQuestionarioPrimeiraVisita().getCodigo() > 0) {
                questionarioVO = questionarioDao.consultarPorChavePrimaria(empresaVO.getQuestionarioPrimeiraVisita().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                questionarioVO = questionarioDao.consultarPorDescricao("BV MATRICULA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        } else if (tipoBVEnum == TipoBVEnum.RE) {
            if (empresaVO.getQuestionarioReMatricula().getCodigo() > 0) {
                questionarioVO = questionarioDao.consultarPorChavePrimaria(empresaVO.getQuestionarioReMatricula().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                questionarioVO = questionarioDao.consultarPorDescricao("BV REMATRICULA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        } else {
            if (empresaVO.getQuestionarioRetorno().getCodigo() > 0) {
                questionarioVO = questionarioDao.consultarPorChavePrimaria(empresaVO.getQuestionarioRetorno().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                questionarioVO = questionarioDao.consultarPorDescricao("BV RETORNO", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        }
        questionarioClienteVO.setTipoBV(tipoBVEnum);
        questionarioClienteVO.setQuestionario(questionarioVO);
        return questionarioClienteVO;
    }

    private ColaboradorVO buscarColaboradorSite(EmpresaVO empresaVO, Integer codigoColaborador, boolean usarUsuarioResponsavelLink) throws Exception {
        ColaboradorVO colaboradorSite = null;

        if (usarUsuarioResponsavelLink && !UteisValidacao.emptyNumber(codigoColaborador)) {
            colaboradorSite = this.colaboradorDao.consultarPorCodigoUsuario(codigoColaborador, 0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (colaboradorSite != null && !UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                return colaboradorSite;
            }
        }

        if (!usarUsuarioResponsavelLink && !UteisValidacao.emptyNumber(codigoColaborador)) {
            colaboradorSite = this.colaboradorDao.consultarPorCodigo(codigoColaborador, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (((colaboradorSite != null) && (colaboradorSite.getCodigo() > 0)) && (!colaboradorSite.isInativo())) {
                return colaboradorSite;
            }
        }

        colaboradorSite = this.vendasConfigDAO.config(empresaVO.getCodigo()).getConsultorSite();
        if ((colaboradorSite != null) && (colaboradorSite.getCodigo() > 0)) {
            return colaboradorSite;
        } else {
            colaboradorSite = new ColaboradorVO();
            TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
            tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());
            colaboradorSite.getPessoa().setNome("COLABORADOR SITE");
            colaboradorSite.getPessoa().setDataNasc(Calendario.ontem());
            colaboradorSite.setEmpresa(empresaVO);
            colaboradorSite.setSituacao("AT");
            colaboradorSite.setDiaVencimento(1);
            colaboradorSite.setPorcComissao(1.0);
            colaboradorSite.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
            colaboradorSite = this.colaboradorDao.criarOuConsultarSeExistePorNome(colaboradorSite, true);
            return colaboradorSite;
        }
    }


    public void marcarAulasAluno(String key, List<RetornoVendaTO> retornoVendaTOList, List<VendaDTO> vendas,
                                 UsuarioVO usuario, boolean aulaExperimental) {
        // Método responsável por incluir o aluno no alunohorarioturma (ou agendamento individual)
        // e, agora, também incluir o registro correspondente na agenda (para que os dados apareçam no BI/relatórios).
        try {
            ClienteVO cliente = retornoVendaTOList.get(0).getClienteVO();
            TurmasServiceInterface turmaService = new TurmasServiceImpl(con);
            AgendaInterfaceFacade agendaDAO = new Agenda(con);

            for (VendaDTO vendaDTO : vendas) {
                if (UteisValidacao.emptyList(vendaDTO.getAulasMarcadas())) {
                    continue;
                }

                Integer produtoFreePass = null;
                if (!UteisValidacao.emptyString(vendaDTO.getFreepass())) {
                    try {
                        produtoFreePass = Integer.valueOf(vendaDTO.getFreepass());
                    } catch (Exception e) {
                        Uteis.logar(e, getClass());
                    }
                }

                ProdutoVO produtoDiaria = null;
                for (VendaProdutoDTO prod : vendaDTO.getProdutos()) {
                    ProdutoVO produtoVO = produtoDAO.consultarPorCodigo(prod.getProduto(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (produtoVO.getTipoProduto().equals(TipoProduto.DIARIA.getCodigo())) {
                        produtoDiaria = produtoVO;
                    }
                }

                for (String codAula : vendaDTO.getAulasMarcadas()) {
                    Integer codigoHorarioTurmas = Integer.valueOf(codAula.split("_")[0]);
                    Boolean aulaColetiva = turmaService.aulaColetiva(codigoHorarioTurmas);
                    Date data = Uteis.getDate(codAula.split("_")[1], "dd/MM/yyyy");
                    try {
                        if (aulaColetiva) {
                            ParamAlunoAulaCheiaTO param = new ParamAlunoAulaCheiaTO();
                            param.setCodigoCliente(cliente.getCodigo());
                            param.setCodigoHorarioTurma(codigoHorarioTurmas);
                            param.setAulaExperimental(aulaExperimental);
                            param.setData(data);
                            param.setCodigoUsuario(usuario.getCodigo());
                            param.setOrigemSistema(OrigemSistemaEnum.SITE.getCodigo());
                            turmaService.inserirAlunoAulaCheiaBooking(param, "");
                        } else {
                            // Marcação para aula individual
                            turmaService.marcarDesmarcarAlunoApp(key, codigoHorarioTurmas, data,
                                    cliente.getCodigoMatricula(), true, false, false, null);
                        }
                        // Após a marcação, também registra o agendamento na agenda
                        try {
                            // Recupera os dados da turma para obter hora de início e modalidade
                            AgendaTotalJSON json = turmaService.consultarUmaTurma(codigoHorarioTurmas, data);
                            String horaInicio = json.getInicio();
                            // Se o valor contiver data/hora, extrai somente a hora
                            if (horaInicio != null && horaInicio.contains(" ")) {
                                horaInicio = horaInicio.split(" ")[1];
                            }
                            Integer modalidade = json.getCodigoTipo(); // Modalidade da turma
                            Integer empresa = (cliente.getEmpresa() != null) ? cliente.getEmpresa().getCodigo() : null;
                            Integer idAlunoHorarioTurma = aulaColetiva ? turmaService.consultarUltimoAlunoHorarioTurmaInserido() : null;

                            agendaDAO.gravarAgendamentoAulaColetivaExperimental(
                                    data,
                                    horaInicio,
                                    cliente.getCodigo(),
                                    modalidade,
                                    empresa,
                                    produtoFreePass,
                                    usuario.getCodigo(),
                                    idAlunoHorarioTurma,
                                    TipoAgendaEnum.AULA_EXPERIMENTAL
                            );
                        } catch (Exception ex) {
                            Uteis.logar(ex, VendasOnlineService.class);
                        }
                        if (produtoDiaria != null) {
                            inserirDiaria(produtoDiaria, cliente, data, usuario);
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, VendasOnlineService.class);
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, VendasOnlineService.class);
        }
    }

    private void inserirDiaria(ProdutoVO produtoVO, ClienteVO clienteVO, Date dia, UsuarioVO usuario) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### INCLUSÃO DE DIÁRIA VIA inserirDiaria - VendasOnlineService.java ");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Matrícula " + clienteVO.getMatricula());
        AulaAvulsaDiariaVO diariaVO = new AulaAvulsaDiariaVO();
        diariaVO.setEmpresa(clienteVO.getEmpresa());
        diariaVO.setCliente(clienteVO);
        diariaVO.setDataInicio(dia);
        diariaVO.setDataLancamento(Calendario.hoje());
        diariaVO.setDataRegistro(dia);
        diariaVO.getModalidade().setCodigo(produtoVO.getModalidadeVendasOnline());
        diariaVO.setNomeComprador(clienteVO.getPessoa().getNome());
        diariaVO.setProduto(produtoVO);
        diariaVO.setResponsavel(usuario);
        diariaVO.setValor(produtoVO.getValorFinal());
        aulaAvulsaDiariaDAO.incluirSemCommit(diariaVO, OrigemSistemaEnum.VENDAS_ONLINE_2, false);
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### FINALIZOU INCLUSÃO DE DIÁRIA VIA inserirDiaria - VendasOnlineService.java ");
    }

    private void marcarCartaoVerificado(TransacaoVO transacaoVO, AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO) {
        try {
            //marcar a autorização de cobrança como verificad
            if (transacaoVO != null &&
                    autorizacaoCobrancaClienteVO != null &&
                    !UteisValidacao.emptyNumber(transacaoVO.getCodigo()) &&
                    !UteisValidacao.emptyNumber(transacaoVO.getValor()) &&
                    (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                            (!transacaoVO.getTipo().equals(TipoTransacaoEnum.VINDI) && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)))) {
                autorizacaoCobrancaClienteVO.setCartaoVerificado(true);
                this.autorizacaoCobrancaClienteDAO.alterarSomenteCartaoVerificado(autorizacaoCobrancaClienteVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private TransacaoVO realizarTransacaoDeVerificacao(EmpresaVO empresaVO, ClienteVO clienteVO,
                                                       InclusaoAutorizacaoVendasDTO inclusaoAutorizacaoDTO,
                                                       UsuarioVO usuarioVO, VendaDTO vendaDTO) throws Exception {

        TransacaoVO transacaoVO = new TransacaoVO();
        JSONObject jsonRetorno = transacaoDAO.realizaCobrancaVerificarCartao(clienteVO, null, inclusaoAutorizacaoDTO.getNovaAutorizacao(), usuarioVO, "");
        boolean erro = jsonRetorno.getBoolean("erro");
        if (erro) {
            //se deu erro exclui a autorizacao de cobranca
            if (!empresaVO.isPermiteCadastrarCartaoMesmoAssim()) {
                //inativar a autorizacao
                this.autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(false, inclusaoAutorizacaoDTO.getNovaAutorizacao(), "Cobrança de verificação não autorizada");

                //colocar as autorizações que foram inativadas como ativas
                for (AutorizacaoCobrancaClienteVO auto : inclusaoAutorizacaoDTO.getAnteriores()) {
                    this.autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(true, auto, "Reativar autorização devido inclusão do novo cartão não foi autorizado");
                }
            }
            transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao, jsonRetorno.optString("message"));
            transacaoVO.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        } else {
            transacaoVO = new TransacaoVO();
            transacaoVO.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        }
        if (transacaoVO != null) {
            if (UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                transacaoVO.setCodigo(jsonRetorno.optInt("transacao"));
            }
            gravarPactoPayComunicacao(vendaDTO, transacaoVO, null, null, null);
        }
        if (!UteisValidacao.emptyNumber(jsonRetorno.optInt("autorizacaoCobrancaCliente"))) {
            AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = new AutorizacaoCobrancaClienteVO();
            autorizacaoCobrancaClienteVO.setCodigo(jsonRetorno.optInt("autorizacaoCobrancaCliente"));
            gravarPactoPayComunicacao(vendaDTO, null, null, null, autorizacaoCobrancaClienteVO);
        }
        return transacaoVO;
    }

    public String incluirAlunoOnline(String key, VendaDTO vendaDTO, Boolean ignoraCpf) throws Exception {
        RetornoVendaTO lista = incluirCadastroAlunoVenda(key, vendaDTO, false, ignoraCpf);
        return lista.getMsgRetorno();
    }

    public RetornoVendaTO incluirCadastroAlunoVenda(String key, VendaDTO vendaDTO,
                                                    boolean pactoStore, Boolean ignoraCpf) throws Exception {

        boolean temDadosDoResponsavelPai = false;
        boolean temDadosDoResponsavelMae = false;
        if (!UteisValidacao.emptyString(vendaDTO.getResponsavelPai()) && !UteisValidacao.emptyString(vendaDTO.getCpfPai())) {
            temDadosDoResponsavelPai = true;
        }
        if (!UteisValidacao.emptyString(vendaDTO.getResponsavelMae()) && !UteisValidacao.emptyString(vendaDTO.getCpfMae())) {
            temDadosDoResponsavelMae = true;
        }

        if ((ignoraCpf == null || !ignoraCpf) && UteisValidacao.emptyString(vendaDTO.getCpf()) && !isPactoFlowVendaInternacional(vendaDTO) &&
                !this.configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS).isUsarSistemaInternacional()) {
            boolean cpfValido = UteisValidacao.isValidCPF(vendaDTO.getCpf());
            if (!cpfValido && !temDadosDoResponsavelPai && !temDadosDoResponsavelMae) {
                throw new Exception("CPF inválido.");
            }
        }

        String msgRetorno = "";
        UsuarioVO usuarioVO = obterUsuarioVendasOnline(vendaDTO);
        RetornoVendaTO retDTO = new RetornoVendaTO(vendaDTO);
        try {
            //incluir o cliente
            ClienteVO clienteVO = incluirObterCliente(vendaDTO, usuarioVO, pactoStore, ignoraCpf, false);
            retDTO.setMatricula(clienteVO.getMatricula());
            retDTO.setCodigoCliente(clienteVO.getCodigo());
            retDTO.setClienteVO(clienteVO);

            //incluir observação
            incluirObservacao(clienteVO, usuarioVO, vendaDTO);

            //marcar aula experimental link visitante
            if (vendasConfigDAO.config(vendaDTO.getUnidade()).isHabilitarAgendamentoAulaExperimentalLinkVisitante() &&
                    vendaDTO.getAulasMarcadas() != null && !vendaDTO.getAulasMarcadas().isEmpty()) {
                Boolean marcou = clienteDAO.validarAulaExperimentalConsumidaLinkVisitante(clienteVO.getCodigo());
                if (marcou != null && !marcou) {
                    RetornoVendaTO aluno = new RetornoVendaTO();
                    aluno.setClienteVO(clienteVO);
                    marcarAulasAluno(key, asList(aluno), asList(vendaDTO), usuarioDAO.getUsuarioRecorrencia(), true);
                    ProdutoVO diaria = produtoDAO.criarOuConsultarProdutoPorTipoNrDiasVigencia(
                            TipoProduto.FREEPASS.getCodigo(), 1, "1 DIA DE AULA EXPERIMENTAL", Uteis.NIVELMONTARDADOS_MINIMOS);
                    vendaDTO.setFreepass(diaria.getCodigo().toString());
                    int usuarioResponsavel = UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel()) ? usuarioDAO.getUsuarioRecorrencia().getCodigo() : vendaDTO.getUsuarioResponsavel();
                    vendaDTO.setUsuarioResponsavel(usuarioResponsavel);
                    clienteDAO.marcarAulaExperimentalConsumidaLinkVisitante(clienteVO.getCodigo());
                }
            }

            ConfiguracaoSistemaCRM crmDAO = new ConfiguracaoSistemaCRM(con);
            boolean gerarIndicacaoParaCadastroConvidadosVendasOnline = crmDAO.isGerarIndicacaoParaCadastroConvidadosVendasOnline();
            Indicado indicadoDAO = new Indicado(con);
            List indicado = !UteisValidacao.emptyString(vendaDTO.getCpf()) ? indicadoDAO.consultarIndicadoPorCpf(vendaDTO.getCpf(), Uteis.NIVELMONTARDADOS_DADOSBASICOS) : new ArrayList();
            gerarIndicacaoParaCadastroConvidadosVendasOnline = indicado.isEmpty();
            crmDAO = null;

            ClienteVO clienteVOConvidou = null;
            boolean alunoQueConvidouOuIndicouExiste = false;
            if (isNotBlank(vendaDTO.getRemetenteConviteMatricula())) {
                clienteVOConvidou = zwFacade.getCliente().consultarPorMatricula(vendaDTO.getRemetenteConviteMatricula(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                alunoQueConvidouOuIndicouExiste = clienteVOConvidou != null && !UteisValidacao.emptyNumber(clienteVOConvidou.getCodigo());
            }

            String voucherIndicacao = "";
            if (gerarIndicacaoParaCadastroConvidadosVendasOnline && alunoQueConvidouOuIndicouExiste) {
                voucherIndicacao = gerarVoucherIndicacao(vendaDTO.getUnidade(), key, usuarioVO);
            }

            //envia email do "mailing após cadastro do visitante", caso exista criado
            try {
                boolean enviarMalaDiretaVisitante = !UteisValidacao.emptyList(this.malaDiretaDAO.consultarCodigosPorOcorrencia(
                        OcorrenciaEnum.INCLUSAO_VISITANTE, clienteVO.getEmpresa().getCodigo()));
                if (enviarMalaDiretaVisitante) {
                    enviarMalaDiretaVisitante(clienteVO.getCodigo(), key);
                }
            } catch (Exception ex) {
                Uteis.logarDebug("Erro ao enviar email de mala direta após cadastro de visitante: " + ex.getMessage() + " | Chave: " + vendaDTO.getChave());
            }

            //envia email de confirmação de recebimento de comunicações e promoções
            if (!clienteVO.getPessoa().getEmailVOs().isEmpty()) {
                clienteVO.getPessoa().setEmail(clienteVO.getPessoa().getEmailVOs().get(0).getEmail());
                ConfiguracaoSistemaVO config = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                Optin optinDAO;
                Empresa empresaDAO;
                try {
                    optinDAO = new Optin(con);
                    empresaDAO = new Empresa(con);

                    EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    optinDAO.enviarEmailOptin(empresaVO, clienteVO, key, config.getSesc());
                } catch (Exception ex) {
                    Uteis.logarDebug("Erro ao enviar email optin: " + ex.getMessage() + " | Chave: " + key + " | CodCliente: " + clienteVO.getCodigo());
                } finally {
                    optinDAO = null;
                    empresaDAO = null;
                }
            }

            //envia email de voucher
            if (!clienteVO.getPessoa().getEmailVOs().isEmpty()) {
                String email = clienteVO.getPessoa().getEmailVOs().get(0).getEmail();
                clienteVO.getPessoa().setEmail(email);
                if (gerarIndicacaoParaCadastroConvidadosVendasOnline && alunoQueConvidouOuIndicouExiste && !UteisValidacao.emptyString(voucherIndicacao)) {
                    // enviar email com voucher
                    EmailVoucherClienteIndicacao emailVoucherClienteIndicacao = new EmailVoucherClienteIndicacao();
                    emailVoucherClienteIndicacao.enviarEmailVoucherIndicacao(email, clienteVO, voucherIndicacao, key, vendaDTO.getUnidade());
                }
            }

            msgRetorno = "ALUNO CADASTRADO COM SUCESSO";
            if (gerarIndicacaoParaCadastroConvidadosVendasOnline && alunoQueConvidouOuIndicouExiste && !UteisValidacao.emptyString(voucherIndicacao)) {
                msgRetorno += ";" + voucherIndicacao;
            }
            if (!indicado.isEmpty()) {
                msgRetorno += ";" + "Convite já cadastrado.";
            }
            retDTO.setMsgRetorno(msgRetorno);

            try {
                zwFacade.atualizarSintetico(zwFacade.getCliente().consultarPorCodigoPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                zwFacade.getUsuarioMovel().verificarEnvioAlunoParaTW(key, clienteVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            try {
                if (alunoQueConvidouOuIndicouExiste) {
                    ConviteVO convite = new ConviteVO();
                    convite.setConvidado(clienteVO);
                    convite.setConvidou(clienteVOConvidou);
                    convite.setPlano(new PlanoVO());
                    convite.setDia(Calendario.hoje());
                    convite.setFaltalancarfreepass(true);
                    Convite conviteDAO = new Convite(con);
                    conviteDAO.incluir(convite);
                    conviteDAO = null;
                    if (gerarIndicacaoParaCadastroConvidadosVendasOnline) {
                        gerarIndicacao(clienteVO, clienteVOConvidou, usuarioVO, vendaDTO);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            boolean lancadoComSucesso = lancaFreepass(vendaDTO, clienteVO);
            if (lancadoComSucesso && clienteVO.getNovoObj()) {
                retDTO.setMsgRetorno("FREEPASS LANÇADO COM SUCESSO");
            } else if (!lancadoComSucesso && !clienteVO.getNovoObj()) {
                retDTO.setMsgRetorno("ALUNO ATUALIZADO COM SUCESSO");
            }

            gerarBV(vendaDTO, clienteVO, false);
            try {
                gravarConclusaoAcessoPaginaInfoPessoa(null, vendaDTO.getCodigoRegistroAcessoPagina(), clienteVO.getPessoa().getCodigo(),
                        vendaDTO, clienteVO, null, null, Calendario.hoje());
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("Erro no método gravarConclusaoAcessoPaginaInfoPessoa no fluxo de cadastro de visitante | chave: " + key);
                incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_gravarConclusaoAcessoPaginaFluxoCadastroVisitante", ex.getMessage());
            }

            return retDTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            retDTO.setMsgRetorno("Erro: " + ex.getMessage());
            incluirLogVendasOnline(retDTO.getCodigoCliente(), vendaDTO, "ERRO_REALIZAR_VENDA", ex.getMessage());
            throw ex;
        }

    }

    public void enviarMalaDiretaVisitante(Integer cliente, String chave) throws Exception {
        String urlZW = PropsService.getPropertyValue(chave, PropsService.roboControle);
        if (urlZW != null && urlZW.startsWith("http")) {
            String urlMailing = urlZW + "/ms" +
                    "?chave=" + chave +
                    "&cliente=" + cliente +
                    "&ocorrencia=" + OcorrenciaEnum.INCLUSAO_VISITANTE.getCodigo();

            // É enviado por requisição para API do ZW para resolver problemas de deadlock
            // causado por complicações no compatilhamento de conexão no contexto do JSF
            ExecuteRequestHttpService.executeRequestGET(urlMailing, null);
        }
    }

    private boolean lancaFreepass(VendaDTO vendaDTO, ClienteVO clienteVO) throws Exception {
        boolean lancadoComSucesso = false;
        if ((clienteVO.getSituacao().equals("VI")) && !UteisValidacao.emptyString(vendaDTO.getFreepass())) {
            PeriodoAcessoCliente periodoAcessoClienteDAO;
            Cliente clienteDAO;
            ZillyonWebFacade zillyonWebFacadeDAO;
            try {
                periodoAcessoClienteDAO = new PeriodoAcessoCliente(con);
                clienteDAO = new Cliente(con);
                zillyonWebFacadeDAO = new ZillyonWebFacade(con);

                UsuarioVO usuarioVO = usuarioDAO.consultarPorCodigoUsuario(vendaDTO.getUsuarioResponsavel(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                ProdutoVO produtoVO = produtoDAO.consultarPorCodigoProdutoAtivo(Integer.parseInt(vendaDTO.getFreepass()), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                FreePassVO freePassVO = new FreePassVO();
                freePassVO.setClienteVO(clienteVO);
                freePassVO.setProdutoFreePass(produtoVO);
                freePassVO.setUsuarioResponsavel(usuarioVO);

                Date dataInicio = new Date();
                if (vendasConfigDAO.config(vendaDTO.getUnidade()).isHabilitarAgendamentoAulaExperimentalLinkVisitante() &&
                        vendaDTO.getAulasMarcadas() != null && !vendaDTO.getAulasMarcadas().isEmpty()) {
                    dataInicio = Calendario.getDate("dd/MM/yyyy", vendaDTO.getAulasMarcadas().get(0).split("_")[1]);
                } else {
                    dataInicio = Calendario.hoje();
                }
                Date dataFim = Uteis.somarDias(dataInicio, (freePassVO.getProdutoFreePass().getNrDiasVigencia() - 1));

                Boolean possuiFreePass = periodoAcessoClienteDAO.possuiPeriodoAcesso(freePassVO.getClienteVO().getPessoa().getCodigo(), "PL", null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (possuiFreePass) {
                    //False, pois se já tem, não deve lançar
                    return false;
                }

                freePassVO.getClienteVO().setResponsavelFreePass(usuarioVO.getCodigo());
                freePassVO.getClienteVO().setFreePass(freePassVO.getProdutoFreePass());

                clienteDAO.incluirFreePass(freePassVO.getClienteVO(), dataInicio, null, null, null);
                gravarPontosCliente(freePassVO);
                incluirLogInclusao(freePassVO, usuarioVO, dataInicio, dataFim);

                zillyonWebFacadeDAO.atualizarSintetico(freePassVO.getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

                lancadoComSucesso = true;
            } catch (Exception e) {
                throw new Exception("Erro ao lançar freepass: " + e.getMessage());
            } finally {
                periodoAcessoClienteDAO = null;
                clienteDAO = null;
                zillyonWebFacadeDAO = null;
            }
        }
        return lancadoComSucesso;
    }

    //Tem um método similar no FreePassControle
    //Se alterar algo aqui, precisa validar para alterar lá também
    private void gravarPontosCliente(FreePassVO freePassVO) throws Exception {
        CampanhaDuracao campanhaDuracaoDAO;
        HistoricoPontos historicoPontosDAO;
        try {
            campanhaDuracaoDAO = new CampanhaDuracao(con);
            historicoPontosDAO = new HistoricoPontos(con);

            CampanhaDuracaoVO maiorCampanhaAtiva = campanhaDuracaoDAO.campanhaVigenteMultiplicador(Calendario.hoje(), TipoItemCampanhaEnum.PRODUTO, freePassVO.getEmpresaVO().getCodigo());
            if (freePassVO.getClienteVO().getEmpresa().isTrabalharComPontuacao() && freePassVO.getProdutoFreePass().getPontos() > 0 &&
                    (!freePassVO.getEmpresaVO().isPontuarApenasCategoriasEmCampanhasAtivas() ||
                            (freePassVO.getEmpresaVO().isPontuarApenasCategoriasEmCampanhasAtivas() && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())))) {
                HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
                historicoPontos.setCliente(freePassVO.getClienteVO());
                historicoPontos.setDataConfirmacao(Calendario.hoje());
                historicoPontos.setDataaula(Calendario.hoje());
                historicoPontos.setEntrada(true);
                Integer pontos = (maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * freePassVO.getProdutoFreePass().getPontos() : freePassVO.getProdutoFreePass().getPontos());
                historicoPontos.setPontos(pontos);
                historicoPontos.setDescricao("Lançamento (FreePass) - " + freePassVO.getProdutoFreePass().getDescricao() + maiorCampanhaAtiva.getTextoCampanhaApresentar());
                historicoPontos.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
                historicoPontos.setTipoPonto(TipoItemCampanhaEnum.PRODUTO);
                historicoPontos.setCodigoVenda(freePassVO.getCodigo());
                historicoPontos.setProduto(freePassVO.getProdutoFreePass().getCodigo());
                historicoPontosDAO.incluir(historicoPontos);
            }
        } catch (Exception e) {
            throw new Exception("Erro ao gravar pontos do cliente: " + e.getMessage());
        } finally {
            campanhaDuracaoDAO = null;
            historicoPontosDAO = null;
        }
    }

    //Tem um método similar no FreePassControle
    //Se alterar algo aqui, precisa validar para alterar lá também
    public void incluirLogInclusao(FreePassVO freePassVO, UsuarioVO usuarioVO, Date dataInicio, Date dataFim) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO obj = new LogVO();
            String nomeUsuario;
            obj.setPessoa(freePassVO.getClienteVO().getPessoa().getCodigo());
            obj.setNomeEntidade("FREEPASS" + "");
            obj.setChavePrimaria(freePassVO.getClienteVO().getPessoa().getCodigo().toString());
            obj.setNomeEntidadeDescricao("FreePass");
            obj.setOperacao("INCLUSÃO " + freePassVO.getUsuarioResponsavel().getNome());
            try {
                nomeUsuario = usuarioVO.getNome();
            } catch (Exception e) {
                nomeUsuario = freePassVO.getUsuarioResponsavel().getNome();
            }
            obj.setResponsavelAlteracao(nomeUsuario);
            obj.setUserOAMD(freePassVO.getUsuarioResponsavel().getUserOamd());
            obj.setNomeCampo("FREEPASS");
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\rNome do Cliente = " + freePassVO.getClienteVO().getPessoa().getNome() + "\n\rData do inicio do FreePass = " + (Uteis.getData(dataInicio)) + "\n\rData Final do FreePass= " + (Uteis.getData(dataFim)) + "\n\rProduto = " + freePassVO.getProdutoFreePass().getDescricao() + "\n\r");
            logDAO.incluirSemCommit(obj);
        } catch (Exception e) {
            registrarLogErroObjetoVO("FREEPASS", freePassVO.getClienteVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE FREEPASS", usuarioVO.getNome(), usuarioVO.getUserOamd());
        } finally {
            logDAO = null;
        }
    }

    private String gerarVoucherIndicacao(Integer codigoEmpresa, String key, UsuarioVO usuarioVO) {
        String voucher = "";
        try {
            Integer idCampanhaCupomDesctonto = vendasConfigDAO.consultarCampanhaCupomDescontoIndicacoes(codigoEmpresa);
            CampanhaCupomDesconto campanhaCupomDescontoOAMDDao = new CampanhaCupomDesconto();
            // validar campanha ativa
            CampanhaCupomDescontoVO campanhaCupomDescontoVO = campanhaCupomDescontoOAMDDao.consultarPorId(idCampanhaCupomDesctonto);
            if (Calendario.menorOuIgual(campanhaCupomDescontoVO.getVigenciaInicial(), Calendario.getDataComHoraZerada(Calendario.hoje()))
                    && Calendario.maiorOuIgual(campanhaCupomDescontoVO.getVigenciaFinal(), Calendario.getDataComHoraZerada(Calendario.hoje()))) {
                OAMDService oamdService = new OAMDService();
                voucher = oamdService.gerarNovoCupomDescontoIndicacao(campanhaCupomDescontoVO, key, usuarioVO, getFacade().getLog());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return voucher;
    }

    private void gerarIndicacao(ClienteVO clienteVO, ClienteVO clienteVOConvidou, UsuarioVO usuarioVO, VendaDTO vendaDTO) {
        try {
            ColaboradorVO colaboradorSite = buscarColaboradorSite(clienteVO.getEmpresa(), 0, false);
            colaboradorSite.setUsuarioVO(zwFacade.getUsuario().consultarPorCodigoColaborador(colaboradorSite.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            UsuarioVO usuarioResponsavel = colaboradorSite.getUsuarioVO() != null && colaboradorSite.getUsuarioVO().getCodigo() > 0
                    ? colaboradorSite.getUsuarioVO() : usuarioVO;

            Indicacao indicacaoDAO = new Indicacao(con);

            IndicacaoVO indicacaoVO = new IndicacaoVO();
            indicacaoVO.setOrigemSistemaEnum(OrigemSistemaEnum.VENDAS_ONLINE_2);
            indicacaoVO.setClienteQueIndicou(clienteVOConvidou);
            indicacaoVO.setEmpresa(clienteVO.getEmpresa());
            indicacaoVO.setColaboradorResponsavel(usuarioResponsavel);
            indicacaoVO.setResponsavelCadastro(usuarioResponsavel);
            indicacaoVO.setDia(Calendario.hoje());
            indicacaoVO.setDiaAbertura(Calendario.hoje());

            IndicadoVO indicadoVO = new IndicadoVO();
            indicadoVO.setClienteVO(clienteVO);
            indicadoVO.setEmpresaVO(clienteVO.getEmpresa());
            indicadoVO.setNomeIndicado(vendaDTO.getNome());
            indicadoVO.setCpf(vendaDTO.getCpf());
            indicadoVO.setEmail(vendaDTO.getEmail());
            indicadoVO.setTelefoneIndicado(Uteis.removerMascara(vendaDTO.getTelefone().replace(" ", "")));
            indicadoVO.setTelefone("");
            indicadoVO.setLead(true);
            indicadoVO.setIndicacaoVO(indicacaoVO);
            indicacaoVO.getIndicadoVOs().add(indicadoVO);

            indicacaoDAO.incluir(indicacaoVO);
            indicacaoDAO = null;

            clienteDAO.alterarIndicacao(indicacaoVO.getClienteQueIndicou().getCodigo(), clienteVO.getCodigo());
        } catch (Exception ex) {
            incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_INCLUIR_INDICADO", ex.getMessage());
        }
    }

    private void gravarConclusaoAcessoPaginaInfoVenda(Integer codigoVendasOnlineCampanhaIcv, Integer contrato, Integer vendaAvulsa, VendaDTO vendaDTO, ClienteVO clienteVO, Integer pessoa) {
        try {
            this.vendasOnlineCampanhaIcvDao.alterarInfoVenda(codigoVendasOnlineCampanhaIcv, contrato, vendaAvulsa, pessoa);
        } catch (Exception e) {
            // anular a exceção e gravar o log... isso é necessário para garantir que esta alteração não impacte na venda de contratos/produtos no vendas online.
            incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_gravarConclusaoAcessoPagina", e.getMessage());
        }
    }

    private void gravarConclusaoAcessoPaginaInfoPessoa(List<VendasOnlineIcvDTO> listaIcv, Integer codigoVendasOnlineCampanhaIcv, Integer pessoa, VendaDTO vendaDTO, ClienteVO clienteVO, Integer contrato, Integer vendaAvulsa, Date dataConclusao) {
        try {
            if (!UteisValidacao.emptyNumber(codigoVendasOnlineCampanhaIcv)) {
                this.vendasOnlineCampanhaIcvDao.alterarInfoPessoa(vendaDTO, codigoVendasOnlineCampanhaIcv, pessoa, dataConclusao);
                povoarListaIcvVenda(listaIcv, contrato, vendaAvulsa, vendaDTO, clienteVO);
            }
        } catch (Exception e) {
            // anular a exceção e gravar o log... isso é necessário para garantir que esta alteração não impacte na venda de contratos/produtos no vendas online.
            incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_gravarConclusaoAcessoPagina", e.getMessage());
        }
    }


    public void notificarWebhook(RetornoVendaTO retornoVendaTO) {
        IntegracaoSistemaService service;
        try {
            boolean notificarContrato = (retornoVendaTO.getContratoVO() != null && retornoVendaTO.getContratoVO().getEmpresa().isNotificarWebhook());
            boolean notificarVendaAvulsa = (retornoVendaTO.getVendaAvulsaVO() != null && retornoVendaTO.getVendaAvulsaVO().getEmpresa().isNotificarWebhook());

            if (notificarContrato || notificarVendaAvulsa) {
                service = new IntegracaoSistemaService(this.con);
                ClienteVO clienteVO = this.clienteDAO.consultarPorChavePrimaria(retornoVendaTO.getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
                if (notificarContrato) {
                    service.notificarContrato(clienteVO, retornoVendaTO.getContratoVO(), false, retornoVendaTO.getContratoVO().getEmpresa(),
                            retornoVendaTO.getContratoVO().getResponsavelContrato());
                }
                if (notificarVendaAvulsa) {
                    service.notificarVendaAvulsa(clienteVO, retornoVendaTO.getVendaAvulsaVO(), retornoVendaTO.getVendaAvulsaVO().getEmpresa(),
                            retornoVendaTO.getVendaAvulsaVO().getResponsavel());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            service = null;
        }
    }

    private ClienteVO obterClientePagadorPactoStore(Integer codigoFinanceiroPagador) throws Exception {
        try {
            Integer codigoCliente = consultarClientePactoStore(codigoFinanceiroPagador);
            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                throw new Exception("Cliente Pagador não encontrado");
            }
            return clienteVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirLogVendasOnline(0, null, "ERRO_OBTER_CLIENTE_PAGADOR_PACTO_STORE", ex.getMessage());
            throw ex;
        }
    }

    private void notificarRecursoOAMD(List<RetornoVendaTO> retornoVendaTOList, TransacaoVO transacaoVO,
                                      String key, UsuarioVO usuarioVO, EmpresaVO empresaVO, boolean pactoStore) {
        for (RetornoVendaTO retDTO : retornoVendaTOList) {

            if (retDTO.getVendaDTO().isVendaConsultor()) {
                notificarRecursoEmpresa(key, RecursoSistema.VENDA_APP_CONSULTOR,
                        usuarioVO, retDTO.getVendaDTO(), empresaVO);
                continue;
            }

            if (transacaoVO != null && (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) ||
                    transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                    transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.PENDENTE))) {

                notificarRecursoEmpresa(key, pactoStore ? RecursoSistema.VENDA_APROVADA_PACTO_STORE : RecursoSistema.VENDA_APROVADA_VENDAS_ONLINE,
                        usuarioVO, retDTO.getVendaDTO(), empresaVO);
            } else if (transacaoVO != null) {
                notificarRecursoEmpresa(key, pactoStore ? RecursoSistema.VENDA_NEGADA_PACTO_STORE : RecursoSistema.VENDA_NEGADA_VENDAS_ONLINE,
                        usuarioVO, retDTO.getVendaDTO(), empresaVO);
            } else {
                notificarRecursoEmpresa(key, pactoStore ? RecursoSistema.VENDA_ERRO_PACTO_STORE : RecursoSistema.VENDA_ERRO_VENDAS_ONLINE,
                        usuarioVO, retDTO.getVendaDTO(), empresaVO);
            }
        }
    }

    private void notificarRecursoEmpresa(String key, RecursoSistema recurso, UsuarioVO usuario,
                                         VendaDTO vendaDTO, EmpresaVO empresaVO) {
        try {
            if (!vendaDTO.getNome().contains("TESTE AUTOMATIZADO")) {
                SuperControle.notificarRecursoEmpresa(key, recurso, usuario, empresaVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar("Não foi possivel gerar recurso empresa: ".concat(e.getMessage()));
        }
    }

    private Date primeiraParcela(Integer diaVencimento, Boolean cobrarPrimeiraParcela, Integer plano) throws Exception {
        if (cobrarPrimeiraParcela && UteisValidacao.emptyNumber(diaVencimento)) {
            return null;
        }

        if (!cobrarPrimeiraParcela && UteisValidacao.emptyNumber(diaVencimento)) {
            Date dia = null;
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select inicioMinimoContrato from plano where codigo = " + plano, this.con);
            if (rs.next()) {
                dia = rs.getDate("inicioMinimoContrato") != null && Calendario.maior(rs.getDate("inicioMinimoContrato"), Calendario.hoje()) ?
                        rs.getDate("inicioMinimoContrato") : null;
            }
            return dia;
        }

        Date dia = Uteis.getDate((diaVencimento > 9 ? diaVencimento.toString() : ("0".concat(diaVencimento.toString()))) +
                "/" +
                Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "MM/yyyy"));
        if (Calendario.menor(dia, Calendario.hoje())) {
            dia = Uteis.somarCampoData(dia, Calendar.MONTH, 1);
        }
        return dia;
    }

    private void estornarContrato(VendaDTO vendaDTO, String key, Integer contrato, Integer codigoCliente, UsuarioVO usuarioVO, String msg) {
        Contrato contratoDAO;
        Cliente clienteDAO;
        Contrato contratoDAOBaseadaOutra;
        Empresa empresaDAO;
        ContratoVO contratoCompleto = null;
        ClienteVO clienteVO = null;
        ZillyonWebFacade zwDAO = null;
        Log logDAO = null;

        try {
            if (UteisValidacao.emptyNumber(contrato)) {
                incluirLogVendaNaoFinalizada(codigoCliente, usuarioVO, msg, vendaDTO);
            } else {
                contratoDAO = new Contrato(this.con);
                clienteDAO = new Cliente(this.con);
                empresaDAO = new Empresa(this.con);
                zwDAO = new ZillyonWebFacade(this.con);
                logDAO = new Log(this.con);

                contratoCompleto = contratoDAO.consultarPorChavePrimaria(contrato, Uteis.NIVELMONTARDADOS_PARCELA);
                clienteVO = clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                contratoCompleto.setUsuarioVO(usuarioVO);
                EmpresaVO emp = empresaDAO.consultarPorChavePrimaria(contratoCompleto.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                clienteVO.setEmpresa(emp);
                contratoCompleto.setEmpresa(emp);
                contratoDAO.montarListasParaEstorno(contratoCompleto);

//                //tentar estornar pela thread
//                ThreadEstornoContrato threadEstornoContrato = new ThreadEstornoContrato(this.con, contratoCompleto, clienteVO, usuarioVO, msg, key);
//                threadEstornoContrato.run();

                //ESTORNAR CONTRATO
                try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con)) {
                    contratoDAOBaseadaOutra = new Contrato(connection);
                    contratoDAOBaseadaOutra.estornoContrato(contratoCompleto, clienteVO, null, "Estorno Automático - Vendas Online");
                }

                zwDAO.atualizarSintetico(clienteDAO.consultarPorCodigo(clienteVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                logDAO.incluirLogEstornoContratoSite(clienteVO.getCodigo(), usuarioVO, msg);
                if (!UteisValidacao.emptyString(contratoCompleto.getNumeroCupomDesconto())) {
                    OAMDService oamdService = new OAMDService();
                    oamdService.cancelarUtilizacaoCupomDesconto(contratoCompleto.getNumeroCupomDesconto(), key);
                    oamdService.informarContratoEstornadoHistoricoUtilizacaoCupom(contratoCompleto.getCodigo(), key);
                    oamdService = null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_ESTORNAR_CONTRATO", e.getMessage());
            Uteis.logarDebug(e.getMessage());
        } finally {
            contratoDAO = null;
            clienteDAO = null;
            empresaDAO = null;
            zwDAO = null;
            logDAO = null;
            contratoDAOBaseadaOutra = null;
        }
    }

    private void estornarVendaAvulsa(VendaDTO vendaDTO, Integer vendaAvulsa, Integer codigoCliente,
                                     UsuarioVO usuarioVO, String msg) {
        try {
            if (UteisValidacao.emptyNumber(vendaAvulsa)) {
                incluirLogVendaNaoFinalizada(codigoCliente, usuarioVO, msg, vendaDTO);
            } else {
                VendaAvulsaVO vendaAvulsaVO = this.vendaAvulsaDAO.consultarPorChavePrimaria(vendaAvulsa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.vendaAvulsaDAO.excluirVendasOnline(vendaAvulsaVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_ESTORNAR_VENDA_AVULSA", e.getMessage());
            Uteis.logar(e, VendasOnlineService.class);
        }
    }

    private void estornarDiaria(VendaDTO vendaDTO, Integer diaria, Integer codigoCliente,
                                UsuarioVO usuarioVO, String msg) {
        try {
            this.aulaAvulsaDiariaDAO.excluirVendasOnline(diaria);
            incluirLogVendaNaoFinalizada(codigoCliente, usuarioVO, "DIARIA_VENDA_ESTORNADA - " + msg, vendaDTO);
        } catch (Exception e) {
            e.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_ESTORNAR_VENDA_DIARIA", e.getMessage());
            Uteis.logar(e, VendasOnlineService.class);
        }
    }

    public void incluirLogVendaNaoFinalizada(Integer cliente, UsuarioVO usuarioVO, String msg, VendaDTO vendaDTO) {
        incluirLog(cliente, usuarioVO, msg, "ESTORNO_COBRANCA_NAO_AUTORIZADA");
        incluirLogVendasOnline(cliente, vendaDTO, "LOG_VENDA_NAO_FINALIZADA", msg);
    }

    public void incluirLog(Integer cliente, UsuarioVO usuarioVO, String msg, String op) {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(cliente.toString());
            obj.setNomeEntidade("CLIENTE");
            obj.setNomeEntidadeDescricao("Cliente");
            obj.setOperacao(op);
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("TODOS");
            obj.setDataAlteracao(Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado(msg);
            this.logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean forcarConvenioLinkPagamento() {
        try {
            String chave = DAO.resolveKeyFromConnection(con);
            String modulos = PropsService.getPropertyValue(chave, PropsService.modulos);
            return modulos != null && !modulos.contains("NVO");
        } catch (Exception e) {
            Uteis.logar(e, VendasOnlineService.class);
            return false;
        }
    }

    public Double preencherValor(List<MovParcelaVO> lst) {
        Double soma = 0.0;
        for (MovParcelaVO parcela : lst) {
            soma += parcela.getValorParcela();
        }
        return soma;
    }


    public JSONObject consultarCobrancaPix(int codigo, String key) throws Exception {
        JSONObject jsonRetorno = new JSONObject();
        Pix pixDAO;
        try {
            pixDAO = new Pix(con);

            //Agora basta consultar a situação atual do Pix pois quem dá baixa no Pix é o Webhook
            PixVO pixVO = pixDAO.consultarPorCodigo(codigo, true);

            boolean consultarPixNaApiDoBanco = (isRedePratique(key) && pixVO.getConveniocobranca() != null && pixVO.getConveniocobranca().isPixBB());

            if (consultarPixNaApiDoBanco) {
                pixVO = this.pixPagamentoService.processarPixControlandoTransacao(codigo);
            }

            if (pixVO.getStatusEnum().equals(PixStatusEnum.EXPIRADA)) {
                jsonRetorno.put("codigo", codigo);
                jsonRetorno.put("status", "EXPIRADO");
            }
            if (pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                jsonRetorno.put("codigo", codigo);
                jsonRetorno.put("status", "CONCLUIDA");
                enviarEmailSucessoPix(codigo, jsonRetorno);
            }
            if (pixVO.getStatusEnum().equals(PixStatusEnum.ATIVA)) {
                jsonRetorno.put("codigo", codigo);
                jsonRetorno.put("status", "ATIVA");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pixDAO = null;
        }
        return jsonRetorno;
    }

    private boolean isRedePratique(String key) {
        //Como todas as unidades da pratique usam a mesma conta bancária, então não é possível configurar o webhook pois o webhook é uma URL por chave pix.
        //Momentaneamente quando for rede pratique continuará sem webhook até que a integração do Pix Inter fique pronta.
        OAMDService oamdService;
        RedeEmpresaVO rede;
        try {
            oamdService = new OAMDService();

            //Ver se já existe no mapa de já consultados anteriormente
            rede = redeEmpresaVosJaConsultados.getOrDefault(key, null);

            if (rede == null) {
                rede = oamdService.consultarRedeEmpresa(key);
            }

            if (rede != null) {
                redeEmpresaVosJaConsultados.put(key, rede);
                //CHAVEREDE DA PRATIQUE
                if (rede.getChaverede().equals("341b908afd7637c1d5b09f248d3498f1")) {
                    return true;
                }
            }

        } catch (Exception ex) {
            return false;
        } finally {
            oamdService = null;
        }
        return false;
    }

    public JSONObject cobrarParcelasAbertasPix(String key, String matricula, VendaDTO vendaDTO) {
        Integer codigoCliente = 0;
        JSONObject jsonRetorno = new JSONObject();
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, pessoa, empresa from cliente where codigomatricula = " + Integer.valueOf(matricula), this.con);
            if (rs.next()) {
                EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteVO = this.clienteDAO.consultarPorCodigo(rs.getInt("codigo"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                codigoCliente = clienteVO.getCodigo();

                OrigemCobrancaEnum origemCobrancaEnum = OrigemCobrancaEnum.obterPorCodigo(vendaDTO.getOrigemCobranca());
                if (origemCobrancaEnum == null || origemCobrancaEnum.equals(OrigemCobrancaEnum.NENHUM)) {
                    origemCobrancaEnum = OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO;
                }

                Double descontoValorFixo = 0.0;
                Double descontoPercentual = 0.0;
                List<MovParcelaVO> movParcelasEmAberto;

                Integer convenioCobranca = null;
                boolean isLinkDePagamentoDaReguaDeCobranca = vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getPactoPayComunicacao());
                if (isLinkDePagamentoDaReguaDeCobranca) {
                    convenioCobranca = empresaVO.getConvenioCobrancaPixRegua().getCodigo();
                } else {
                    convenioCobranca = empresaVO.getConvenioCobrancaPix().getCodigo();
                }

                if (UteisValidacao.emptyNumber(convenioCobranca)) {
                    throw new Exception("Convênio de cobrança não selecionado nas configurações da empresa (Link de pagamento)!");
                }

                if (!UteisValidacao.emptyNumber(vendaDTO.getCobrancaAntecipada())) {
                    PactoPayCobrancaAntecipadaDTO antecipadaDTO = obterValidarCobrancaAntecipadaDTO(vendaDTO.getCobrancaAntecipada());
                    PactoPayConfigVO pactoPayConfigVO = this.pactoPayConfigDAO.consultarPorEmpresa(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (antecipadaDTO.isAplicarDesconto()) {
                        if (pactoPayConfigVO.isCobrancaAntecipadaValorFixo()) {
                            descontoValorFixo = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                        } else {
                            descontoPercentual = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                        }
                    }

                    movParcelasEmAberto = this.movParcelaDAO.consultarPorCodigos(antecipadaDTO.getParcelas(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    if (UteisValidacao.emptyList(movParcelasEmAberto)) {
                        throw new Exception("Nenhuma parcela encontrada para pagamento antecipado");
                    }
                } else if (origemCobrancaEnum.equals(OrigemCobrancaEnum.TOTEM) && !UteisValidacao.emptyString(vendaDTO.getParcelasSelecionadas())) {
                    movParcelasEmAberto = new ArrayList<>();
                    List<String> parcelas = asList(vendaDTO.getParcelasSelecionadas().split("_"));
                    parcelas.forEach(p -> {
                        try {
                            movParcelasEmAberto.add(this.movParcelaDAO.consultarPorCodigo(Integer.parseInt(p), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                } else if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) && vendaDTO.getTodasEmAberto() != null && vendaDTO.getTodasEmAberto()) {
                    movParcelasEmAberto = this.movParcelaDAO.consultarParcelasEmAbertoPessoa(rs.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else if ((origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) && (vendaDTO.getTodasEmAberto() == null || !vendaDTO.getTodasEmAberto()) && !UteisValidacao.emptyString(vendaDTO.getParcelasSelecionadas())) ||
                        (origemCobrancaEnum.equals(OrigemCobrancaEnum.APP_ALUNO) && !UteisValidacao.emptyString(vendaDTO.getParcelasSelecionadas()))) {
                    List<String> parcelas = asList(vendaDTO.getParcelasSelecionadas().split(";"));
                    List<Integer> parcelasInt = parcelas.stream().map(Integer::parseInt).collect(Collectors.toList());
                    movParcelasEmAberto = this.movParcelaDAO.consultarPorCodigos(parcelasInt, true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else {
                    movParcelasEmAberto = this.movParcelaDAO.consultarParcelasVencidasPessoaVendasOnline(rs.getInt("pessoa"), rs.getInt("empresa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                }

                if (UteisValidacao.emptyList(movParcelasEmAberto)) {
                    throw new Exception("Nenhuma parcela encontrada para pagamento");
                }

                PixVO pixVO = gerarPix(key, empresaVO, convenioCobranca, clienteVO, movParcelasEmAberto, origemCobrancaEnum, descontoValorFixo, descontoPercentual, vendaDTO, null);

                jsonRetorno.put("Codigo", pixVO.getCodigo());
                jsonRetorno.put("UrlQRcode", pixVO.obterUrlSomenteImagemQRcode(key));
                jsonRetorno.put("qrtext", pixVO.getTextoImagemQrcode());
                incluirLogVendasOnline(codigoCliente, vendaDTO, "COBRAR_PARCELAS_ABERTAS", "PIX - " + pixVO.obterUrlCompartilharQRcode(key));

                if (vendaDTO.getEnviarEmail() != null && vendaDTO.getEnviarEmail()) {
                    try {
                        List<EmailVO> emails = this.emailDAO.consultarEmails(rs.getInt("pessoa"), true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (emails != null && !emails.isEmpty() && !UteisValidacao.emptyString(emails.get(0).getEmail())) {
                            PixEmailService.enviarEmail(emails.get(0).getEmail(), pixVO, vendaDTO.getUrlZw(), empresaVO, key,
                                    this.configuracaoSistemaCRMDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS), usuarioDAO.getUsuarioRecorrencia());
                            jsonRetorno.put("emailEnviado", true);
                        } else {
                            jsonRetorno.put("emailEnviado", false);
                        }
                    } catch (Exception ex) {
                        jsonRetorno.put("emailEnviado", false);
                    }
                }
            } else {
                throw new Exception("Cliente não encontrado");
            }
        } catch (Exception ex) {
            jsonRetorno.put("ERRO", ex.getMessage());
            ex.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_COBRAR_PARCELAS_ABERTAS", ex.getMessage());
            return jsonRetorno;
        }
        return jsonRetorno;
    }

    private PixVO gerarPix(String key, EmpresaVO empresaVO, Integer convenioCobranca, ClienteVO clienteVO,
                           List<MovParcelaVO> movParcelasEmAberto, OrigemCobrancaEnum origemCobrancaEnum,
                           Double descontoValorFixo, Double descontoPercentual, VendaDTO vendaDTO,
                           Integer expiracao) throws Exception {
        try {
            ConvenioCobrancaVO convenioCobrancaPIX = this.convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(convenioCobrancaPIX.getEmpresa().getCodigo()) || UteisValidacao.emptyString(convenioCobrancaPIX.getEmpresa().getNome())) {
                convenioCobrancaPIX.setEmpresa(this.empresaDAO.consultarPorChavePrimaria(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
            FormaPagamentoVO formaPagamentoVO = this.pixDAO.obterFormaPagamentoPix(null, convenioCobrancaPIX);
            if (formaPagamentoVO == null) {
                formaPagamentoVO = this.formaPagamentoDAO.consultarPorTipo(TipoFormaPagto.PIX);
            }
            if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                throw new Exception("Não foi encontrado uma forma de pagamento PIX!");
            }

            //validar crédito pacto
            this.pixDAO.validarCreditosPacto(empresaVO);

            //setar valores de multa e juros nas parcelas
            this.movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, TipoCobrancaEnum.PIX, movParcelasEmAberto, Calendario.hoje());

            UsuarioVO usuarioVO = obterUsuarioVendasOnline(vendaDTO);

            PixVO pixVO = this.pixDAO.gerarObterPix(key, clienteVO.getPessoa(), convenioCobrancaPIX, empresaVO, movParcelasEmAberto, usuarioVO,
                    formaPagamentoVO.getCodigo(), origemCobrancaEnum, descontoValorFixo, descontoPercentual, expiracao, true);
            gravarPactoPayComunicacao(vendaDTO, null, pixVO, null, null);
            return pixVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_COBRAR_PARCELAS_ABERTAS", ex.getMessage());
            throw ex;
        }
    }

    public JSONObject cobrarParcelasAbertas(String matricula, VendaDTO vendaDTO) throws Exception {
        OrigemCobrancaEnum origemCobranca = null;
        boolean permiteCadastrarCartaoMesmoAssim = false;
        int codAutIncluida = 0;
        Integer codigoCliente = 0;
        List<MovParcelaVO> parcelasMultaCriadas = new ArrayList<>();
        TransacaoVO transacaoVO = null;
        JSONObject jsonRetorno = new JSONObject();

        TokenVendasOnline tokenVendasOnlineDao = new TokenVendasOnline(con);
        if (!UteisValidacao.emptyString(vendaDTO.getToken())) {
            try {
                if (tokenVendasOnlineDao.cobrancaEmProcessamento(vendaDTO.getToken())) {
                    tokenVendasOnlineDao = null;
                    jsonRetorno.put("ERRO", "Cobrança em processamento. Confirme com a academia se a cobrança foi efetuada.");
                    return jsonRetorno;
                }
            } catch (Exception e) {
                tokenVendasOnlineDao = null;
            }
        }

        try {
            tokenVendasOnlineDao.mudarCobrancaEmProcessamento(vendaDTO.getToken(), true);
            SituacaoTransacaoEnum situacao = null;
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, pessoa, empresa from cliente where codigomatricula = " + Integer.valueOf(matricula), this.con);
            if (rs.next()) {
                UsuarioVO usuarioVO = obterUsuarioVendasOnline(vendaDTO);
                ClienteVO clienteVO = this.clienteDAO.consultarPorCodigo(rs.getInt("codigo"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                permiteCadastrarCartaoMesmoAssim = empresaVO.isPermiteCadastrarCartaoMesmoAssim();
                if (!vendaDTO.getCpftitularcard().isEmpty() && !verificaCPF(vendaDTO.getCpftitularcard())) {
                    throw new Exception("CPF titular do cartão inválido!");
                }
                codigoCliente = clienteVO.getCodigo();

                ConvenioPagamentoVendasOnlineDTO convenio = null;

                boolean isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca = vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getPactoPayComunicacao());
                if (isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca) {
                    convenio = obterConvenioPadraoRegua(vendaDTO.getUnidade());
                } else {
                    convenio = obterConvenioPadrao(vendaDTO, vendaDTO.getUnidade(), true);
                }

                boolean forcarConvenioLinkPagamento = forcarConvenioLinkPagamento();
                if (convenio == null || forcarConvenioLinkPagamento) {
                    ConvenioPagamentoVendasOnlineDTO convenioParaLink = null;

                    if (isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca) {
                        convenioParaLink = obterConvenioPadraoRegua(vendaDTO.getUnidade());
                    } else {
                        convenioParaLink = obterConvenioPadrao(vendaDTO, vendaDTO.getUnidade(), true);
                    }

                    convenio = (convenioParaLink != null) ? convenioParaLink : convenio;
                    if (convenio == null) {
                        throw new Exception("Convênio de cobrança não configurado!");
                    }
                }

                OrigemCobrancaEnum origemCobrancaEnum = OrigemCobrancaEnum.obterPorCodigo(vendaDTO.getOrigemCobranca());
                if (origemCobrancaEnum == null || origemCobrancaEnum.equals(OrigemCobrancaEnum.NENHUM)) {
                    origemCobrancaEnum = vendaDTO.isCobrarParcelasEmAberto() ? OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO : OrigemCobrancaEnum.VENDAS_ONLINE_LINK_CADASTRAR;
                }

                InclusaoAutorizacaoVendasDTO inclusaoAutorizacaoDTO = incluirAutorizacao(clienteVO, vendaDTO, convenio.getConvenioCobranca(),
                        usuarioVO, empresaVO, origemCobrancaEnum, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);

                origemCobranca = origemCobrancaEnum;
                codAutIncluida = inclusaoAutorizacaoDTO.getNovaAutorizacao().getCodigo();

                if (vendaDTO.isCobrarParcelasEmAberto()) {

                    List<MovParcelaVO> movParcelasEmAberto;
                    Double descontoValorFixo = 0.0;
                    Double descontoPercentual = 0.0;
                    if (!UteisValidacao.emptyNumber(vendaDTO.getCobrancaAntecipada())) {
                        PactoPayCobrancaAntecipadaDTO antecipadaDTO = obterValidarCobrancaAntecipadaDTO(vendaDTO.getCobrancaAntecipada());
                        PactoPayConfigVO pactoPayConfigVO = this.pactoPayConfigDAO.consultarPorEmpresa(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        if (antecipadaDTO.isAplicarDesconto()) {
                            if (pactoPayConfigVO.isCobrancaAntecipadaValorFixo()) {
                                descontoValorFixo = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                            } else {
                                descontoPercentual = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                            }
                        }

                        movParcelasEmAberto = this.movParcelaDAO.consultarPorCodigos(antecipadaDTO.getParcelas(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                        if (UteisValidacao.emptyList(movParcelasEmAberto)) {
                            throw new Exception("Nenhuma parcela encontrada para pagamento antecipado");
                        }
                    } else if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) && vendaDTO.getTodasEmAberto() != null && vendaDTO.getTodasEmAberto()) {
                        movParcelasEmAberto = this.movParcelaDAO.consultarParcelasEmAbertoPessoa(rs.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    } else if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) && (vendaDTO.getTodasEmAberto() == null || !vendaDTO.getTodasEmAberto()) && !UteisValidacao.emptyString(vendaDTO.getParcelasSelecionadas())) {
                        List<String> parcelas = asList(vendaDTO.getParcelasSelecionadas().split(";"));
                        List<Integer> parcelasInt = parcelas.stream().map(Integer::parseInt).collect(Collectors.toList());
                        movParcelasEmAberto = this.movParcelaDAO.consultarPorCodigos(parcelasInt, true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    } else {
                        movParcelasEmAberto = this.movParcelaDAO.consultarParcelasVencidasPessoaVendasOnline(rs.getInt("pessoa"), rs.getInt("empresa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                        if (!UteisValidacao.emptyNumber(vendaDTO.getOrigemSistema()) && vendaDTO.getOrigemSistema().equals(OrigemSistemaEnum.VENDAS_ONLINE_2.getCodigo()) && UteisValidacao.emptyList(movParcelasEmAberto)) {
                            movParcelasEmAberto = this.movParcelaDAO.consultarParcelasVencidasPessoaVendasOnline(rs.getInt("pessoa"), null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        }
                    }

                    if (UteisValidacao.emptyList(movParcelasEmAberto)) {
                        transacaoVO = realizarTransacaoDeVerificacao(empresaVO, clienteVO, inclusaoAutorizacaoDTO, usuarioVO, vendaDTO);
                        situacao = transacaoVO.getSituacao();
                        jsonRetorno.put("motivo", transacaoVO.getCodigoRetornoGestaoTransacaoMotivo());
                    } else {

                        //MONTAR MULTA E JUROS
                        this.movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, TipoCobrancaEnum.ONLINE, movParcelasEmAberto, Calendario.hoje());

                        List<MovParcelaVO> movParcelasCobrar = new ArrayList<>();
                        movParcelasCobrar.addAll(movParcelasEmAberto);

                        // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
                        // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
                        // Por isso a inclusão desses logs
                        if (!UteisValidacao.emptyList(movParcelasEmAberto)) {
                            String codigosMovParcelas = movParcelasEmAberto.stream()
                                    .map(p -> String.valueOf(p.getCodigo()))
                                    .collect(Collectors.joining(","));
                            Uteis.logarDebug("VendasOnlineService - cobrarParcelasAbertas - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
                        }

                        //criar parcela de multa e juros
                        for (MovParcelaVO movParcelaVO : movParcelasEmAberto) {
                            if (!UteisValidacao.emptyNumber(movParcelaVO.getValorMulta()) || !UteisValidacao.emptyNumber(movParcelaVO.getValorJuros())) {
                                MovParcelaVO parcelaMultaJuros = this.movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, movParcelaVO.getValorMulta(), movParcelaVO.getValorJuros(), usuarioVO);
                                movParcelasCobrar.add(parcelaMultaJuros);
                                parcelasMultaCriadas.add(parcelaMultaJuros);
                            }
                        }

                        //calcular a soma das parcelas para poder aplicar o desconto
                        Double valorDesconto = 0.0;
                        if (!UteisValidacao.emptyNumber(descontoValorFixo) ||
                                !UteisValidacao.emptyNumber(descontoPercentual)) {

                            Double valorTotal = 0.0;
                            for (MovParcelaVO parcela : movParcelasCobrar) {
                                valorTotal += parcela.getValorParcela();
                            }
                            valorDesconto = calcularValorDesconto(valorTotal, descontoValorFixo, descontoPercentual);
                        }

                        transacaoVO = cobrarParcelasAgrupando(vendaDTO, movParcelasCobrar, vendaDTO.getNrVezesDividir(), clienteVO, usuarioVO,
                                convenio.getConvenioCobranca(), vendaDTO.getUnidade(), convenio.getTipoParcelamentoStone(),
                                origemCobrancaEnum, valorDesconto, null);
                        if (transacaoVO != null) {
                            situacao = transacaoVO.getSituacao();
                            jsonRetorno.put("motivo", transacaoVO.getCodigoRetornoGestaoTransacaoMotivo());
                        } else {
                            situacao = SituacaoTransacaoEnum.NAO_APROVADA;
                        }
                        if (situacao != null &&
                                inclusaoAutorizacaoDTO != null &&
                                (situacao.equals(SituacaoTransacaoEnum.NAO_APROVADA) || situacao.equals(SituacaoTransacaoEnum.COM_ERRO)) &&
                                !empresaVO.isPermiteCadastrarCartaoMesmoAssim()) {
                            //inativar a autorizacao
                            this.autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(false, inclusaoAutorizacaoDTO.getNovaAutorizacao(), "Cobrança de verificação não autorizada");

                            //colocar as autorizações que foram inativadas como ativas
                            for (AutorizacaoCobrancaClienteVO auto : inclusaoAutorizacaoDTO.getAnteriores()) {
                                this.autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(true, auto, "Reativar autorização devido inclusão do novo cartão não foi autorizado");
                            }
                        }

                        //enviar email de pagamento Sucesso;
                        enviarEmailSucessoPagamento(clienteVO.getPessoa().getCodigo(), jsonRetorno, transacaoVO, null, empresaVO, isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca);

                        //Verifica se tem recibo gerado e envia para integração da Benner caso a mesma esteja ativa
                        if(transacaoVO != null && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)
                                && !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())){
                            if(zwFacade.getConfiguracaoSistema().realizarEnvioSesiSC()){
                                List<Integer> listaRecibo = new ArrayList<>();
                                listaRecibo.add(transacaoVO.getReciboPagamento());
                                zwFacade.startThreadIntegracaoFiesc(transacaoVO.getEmpresa(), listaRecibo, "incluirCartao");
                            }
                        }
                    }
                } else {
                    transacaoVO = realizarTransacaoDeVerificacao(empresaVO, clienteVO, inclusaoAutorizacaoDTO, usuarioVO, vendaDTO);
                    situacao = transacaoVO.getSituacao();

                    if (!UteisValidacao.emptyString(transacaoVO.getCodigoRetornoGestaoTransacaoMotivo()) &&
                            transacaoVO.getCodigoRetornoGestaoTransacaoMotivo().contains("Não foi possível verificar este cartão") &&
                            empresaVO.isPermiteCadastrarCartaoMesmoAssim()) {

                        String msgCartaoNaoVerificadoMasAdicionado = "";
                        msgCartaoNaoVerificadoMasAdicionado = transacaoVO.getCodigoRetornoGestaoTransacaoMotivo() + " mas o mesmo foi adicionado";

                        jsonRetorno.put("motivo", msgCartaoNaoVerificadoMasAdicionado);
                    } else {
                        jsonRetorno.put("motivo", transacaoVO.getCodigoRetornoGestaoTransacaoMotivo());
                    }
                }

                //marcar a autorização de cobrança como verificada
                marcarCartaoVerificado(transacaoVO, inclusaoAutorizacaoDTO.getNovaAutorizacao());
            }
            String retorno = "ERRO";
            switch (situacao) {
                case APROVADA:
                case CONCLUIDA_COM_SUCESSO:
                case PENDENTE:
                    retorno = "APROVADA";
                    break;
                case NENHUMA:
                case COM_ERRO:
                case NAO_APROVADA:
                case ERRO_CAPTURA:
                case CANCELADA:
                case DESCARTADA:
                case ESTORNADA:
                    retorno = "NAO_APROVADA";
                    break;
                default:
                    retorno = "APROVADA";
                    break;
            }

            incluirLogVendasOnline(codigoCliente, vendaDTO, "COBRAR_PARCELAS_ABERTAS", retorno);

            //Aqui inclui o log no aluno quando ele cadastrar um cartão pelo link de cadastro de cartão ou realizar um pagamento pelo link de pagamento
            if (retorno.equals("APROVADA") || (!retorno.equals("APROVADA") && permiteCadastrarCartaoMesmoAssim)) {
                incluirLogOperacaoLink(origemCobranca, codigoCliente, vendaDTO.getResponsavelLink(), codAutIncluida);
            }
            if (!UteisValidacao.emptyString(vendaDTO.getToken()) && retorno.equals("APROVADA")) {
                //depois de um pagamento aprovado ou cartão incluído com sucesso, setar token do link como expirado.
                this.tokenVendasOnlineDAO.inutilizarToken(vendaDTO.getToken());
            }

            jsonRetorno.put("status", retorno);
            return jsonRetorno;
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_COBRAR_PARCELAS_ABERTAS", ex.getMessage());
            throw ex;
        } finally {
            excluirParcelasMultaJuros(transacaoVO, parcelasMultaCriadas);
            tokenVendasOnlineDao.mudarCobrancaEmProcessamento(vendaDTO.getToken(), false);
            tokenVendasOnlineDao = null;
        }
    }

    public JSONObject cobrarParcelasAbertasSaldo(String key, String matricula, VendaDTO vendaDTO, Integer usuarioResponsavel) throws Exception {
        Integer codigoCliente = 0;
        List<MovParcelaVO> parcelasMultaCriadas = new ArrayList<>();
        JSONObject jsonRetorno = new JSONObject();
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, pessoa, empresa from cliente where codigomatricula = " + Integer.valueOf(matricula), this.con);
            if (rs.next()) {
                EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteVO = this.clienteDAO.consultarPorCodigo(rs.getInt("codigo"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                codigoCliente = clienteVO.getCodigo();

                //ConvenioCobrancaVO convenioCobrancaPIX = this.convenioCobrancaDAO.consultarPorChavePrimaria(empresaVO.getConvenioCobrancaPix().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                //FormaPagamentoVO formaPagamentoVO = this.pixDAO.obterFormaPagamentoPix(null, convenioCobrancaPIX);
                FormaPagamentoVO formaPagamentoVO = this.formaPagamentoDAO.consultarPorTipo(TipoFormaPagto.CREDITOCONTACORRENTE);
                if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                    throw new Exception("Não foi encontrado uma forma de pagamento por saldo da conta corrente do cliente!");
                }

                UsuarioVO usuarioVO = null;
                if (!UteisValidacao.emptyNumber(usuarioResponsavel)) {
                    usuarioVO = this.usuarioDAO.consultarPorChavePrimaria(usuarioResponsavel, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
                if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                    usuarioVO = this.usuarioDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
                }

                List<MovParcelaVO> movParcelasEmAberto = this.movParcelaDAO.consultarParcelasVencidasPessoaVendasOnline(rs.getInt("pessoa"), rs.getInt("empresa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                //MONTAR MULTA E JUROS
                this.movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, TipoCobrancaEnum.EDI_DCC, movParcelasEmAberto, Calendario.hoje());

                BigDecimal valorTotal = BigDecimal.ZERO;

                // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
                // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
                // Por isso a inclusão desses logs
                if (!UteisValidacao.emptyList(movParcelasEmAberto)) {
                    String codigosMovParcelas = movParcelasEmAberto.stream()
                            .map(p -> String.valueOf(p.getCodigo()))
                            .collect(Collectors.joining(","));
                    Uteis.logarDebug("VendasOnlineService - cobrarParcelasAbertasSaldo - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
                }

                List<MovParcelaVO> movParcelasCobrar = new ArrayList<>(movParcelasEmAberto);
                ContratoVO contratoVO = new ContratoVO();
                for (MovParcelaVO movParcelaVO : movParcelasEmAberto) {
                    valorTotal = valorTotal.add(movParcelaVO.getValorParcela() != null ? new BigDecimal(movParcelaVO.getValorParcela()) : BigDecimal.ZERO);
                    if (UteisValidacao.emptyNumber(contratoVO.getCodigo()) && !UteisValidacao.emptyNumber(movParcelaVO.getContrato().getCodigo())) {
                        contratoVO = movParcelaVO.getContrato();
                    }
                    if (!UteisValidacao.emptyNumber(movParcelaVO.getValorMulta()) || !UteisValidacao.emptyNumber(movParcelaVO.getValorJuros())) {
                        //criar parcela de multa e juros
                        MovParcelaVO parcelaMultaJuros = this.movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, movParcelaVO.getValorMulta(), movParcelaVO.getValorJuros(), usuarioVO);
                        movParcelasCobrar.add(parcelaMultaJuros);
                        parcelasMultaCriadas.add(parcelaMultaJuros);
                        valorTotal = valorTotal.add(movParcelaVO.getValorParcela() != null ? new BigDecimal(parcelaMultaJuros.getValorParcela()) : BigDecimal.ZERO);
                    }
                }

                if (movParcelasCobrar.isEmpty()) {
                    throw new Exception("Não foi encontrado nenhuma divida em aberto para esse cliente!");
                }

                MovimentoContaCorrenteClienteVO movimentoContaCorrenteClienteVO = consultarMovimentoContaCorrenteClienteVOComValidacao(clienteVO);

                if (valorTotal.compareTo(new BigDecimal(movimentoContaCorrenteClienteVO.getSaldoAtual())) > 0) {
                    throw new Exception("Saldo do cliente insuficiente para realizar esse pagamento!");
                }

                /**
                 * Preencher dados MovPagamentoVO
                 */
                MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                movPagamentoVO.setValor(valorTotal.doubleValue());
                movPagamentoVO.setValorTotal(valorTotal.doubleValue());
                movPagamentoVO.setNomePagador(clienteVO.getPessoa().getNome());
                movPagamentoVO.setTipoPagador("CI");
                movPagamentoVO.setPessoa(clienteVO.getPessoa());
                movPagamentoVO.setResponsavelPagamento(usuarioVO);
                movPagamentoVO.setFormaPagamento(formaPagamentoVO);
                movPagamentoVO.setOpcaoPagamentoContaCorrenteCliente(true);
                movPagamentoVO.setMovPagamentoEscolhida(true);
                movPagamentoVO.setEmpresa(empresaVO);
                movPagamentoVO.setPagamentoAberto(true);

                /**
                 * Preencher dados MovimentoContaCorrenteClienteVO
                 */
                NumberFormat nf = NumberFormat.getInstance();
                nf.setMinimumFractionDigits(2);
                double saldo = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movimentoContaCorrenteClienteVO.getSaldoAtual() - valorTotal.doubleValue());
                movPagamentoVO.setSaldoContaCorrenteCliente("Será retirado da sua conta o VALOR: " + nf.format(valorTotal.doubleValue()) + ". Restando um Saldo de: " + nf.format(saldo));

                movimentoContaCorrenteClienteVO.setCodigo(0);
                movimentoContaCorrenteClienteVO.setNovoObj(true);
                movimentoContaCorrenteClienteVO.setDescricao("Pagar Parcelas");
                movimentoContaCorrenteClienteVO.setPessoa(movPagamentoVO.getPessoa());
                movimentoContaCorrenteClienteVO.setDataRegistro(Calendario.hoje());
                movimentoContaCorrenteClienteVO.setTipoMovimentacao("DE");
                movimentoContaCorrenteClienteVO.setSaldoAnterior(movimentoContaCorrenteClienteVO.getSaldoAtual());
                movimentoContaCorrenteClienteVO.setValor(movPagamentoVO.getValorTotal());
                movimentoContaCorrenteClienteVO.setSaldoAtual(saldo);
                /*****/

                this.movPagamentoDAO.incluirListaPagamento(asList(movPagamentoVO), movParcelasCobrar, movimentoContaCorrenteClienteVO, contratoVO, null, 0.0, true, null, TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);

                incluirLogVendasOnline(codigoCliente, vendaDTO, "COBRAR_PARCELAS_ABERTAS", "APROVADA");
                jsonRetorno.put("status", "APROVADA");
            } else {
                throw new Exception("Cliente não encontrado");
            }
        } catch (Exception ex) {
            //caso tenha algum erro então exclui as parcelas criadas de multa e juros
            excluirParcelasMultaJuros(null, parcelasMultaCriadas);
            jsonRetorno.put("ERRO", ex.getMessage());
            ex.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_COBRAR_PARCELAS_ABERTAS", ex.getMessage());
            return jsonRetorno;
        }
        return jsonRetorno;
    }

    private MovimentoContaCorrenteClienteVO consultarMovimentoContaCorrenteClienteVOComValidacao(ClienteVO clienteVO) throws Exception {
        boolean isPagarUsandoSaldoCliente = true;

        if (!UteisValidacao.emptyNumber(clienteVO.getEmpresa().getCodigo())) {
            VendasConfigVO config = this.vendasConfigDAO.config(clienteVO.getEmpresa().getCodigo());
            isPagarUsandoSaldoCliente = config != null ? config.isPagarUsandoSaldoCliente() : true;
        }

        if (!isPagarUsandoSaldoCliente) {
            throw new Exception("Sistema não está habilitado para usar esse tipo de pagamento!");
        }

        MovimentoContaCorrenteClienteVO obj = new MovimentoContaCorrenteCliente(this.con).consultarPorCodigoPessoa(
                clienteVO.getPessoa().getCodigo(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (obj == null || obj.getSaldoAtual() == null || obj.getSaldoAtual() == 0.0) {
            throw new Exception("Não foi encontrado saldo para esse cliente!");
        }

        return obj;
    }

    private void excluirParcelasMultaJuros(TransacaoVO transacaoVO, List<MovParcelaVO> parcelasMultaCriadas) {
        for (MovParcelaVO movParcelaVO : parcelasMultaCriadas) {
            this.transacaoDAO.excluirParcelaMultaJurosTransacao(transacaoVO, movParcelaVO);
        }
    }

    private void incluirObservacao(ClienteVO clienteVO, UsuarioVO usuarioVO, VendaDTO vendaDTO) throws Exception {
        if (UteisValidacao.emptyString(vendaDTO.getObservacaoCliente().trim())) {
            return;
        }

        ClienteObservacao clienteObservacao = new ClienteObservacao(this.con);
        ClienteObservacaoVO obj = new ClienteObservacaoVO();
        obj.setDataCadastro(Calendario.hoje());
        obj.setClienteVO(clienteVO);
        obj.setUsuarioVO(usuarioVO);
        obj.setObservacao(vendaDTO.getObservacaoCliente());
        clienteObservacao.incluir(obj);
    }

    public InclusaoAutorizacaoVendasDTO incluirAutorizacao(ClienteVO clienteVO, VendaDTO vendaDTO, Integer convenioCobranca,
                                                           UsuarioVO usuarioVO, EmpresaVO empresaVO, OrigemCobrancaEnum origemAut,
                                                           TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum) throws Exception {
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO;
        ClienteMensagem clienteMensagemDAO;
        InclusaoAutorizacaoVendasDTO inclusaoDTO = new InclusaoAutorizacaoVendasDTO();
        try {
            con.setAutoCommit(false);
            //controla a transação para evitar problema de excluir o cartão e caso o cartão seja inválido o aluno irá ficar sem cartão.

            autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);
            clienteMensagemDAO = new ClienteMensagem(con);

            List<AutorizacaoCobrancaClienteVO> autorizacaoCobrancaClienteVOS = autorizacaoCobrancaClienteDAO.consultarPorPessoaTipoAutorizacao(clienteVO.getPessoa().getCodigo(),
                    tipoAutorizacaoCobrancaEnum, Uteis.NIVELMONTARDADOS_MINIMOS);
            boolean possuiMaisDeUmCartaoCadastrado = false;
            if (!UteisValidacao.emptyList(autorizacaoCobrancaClienteVOS) && autorizacaoCobrancaClienteVOS.size() > 1) {
                possuiMaisDeUmCartaoCadastrado = true;
            }
            for (AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO : autorizacaoCobrancaClienteVOS) {
                String msgLogExclusao = "Exclusão de autorização de cobrança devido a inclusão de novo cartão.";
                if (origemAut != null) {
                    msgLogExclusao += " | Origem: " + origemAut.getDescricao();
                } else {
                    msgLogExclusao += " | Origem: Não foi possível identificar a origem";
                }
                autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(false, autorizacaoCobrancaClienteVO, msgLogExclusao);
                inclusaoDTO.getAnteriores().add(autorizacaoCobrancaClienteVO);
            }
            if (autorizacaoCobrancaClienteVOS.size() > 0) {
                clienteMensagemDAO.processarMensagensCartaoVencidoCliente(clienteVO.getCodigo(), usuarioVO);
            }

            AutorizacaoCobrancaClienteVO autorizacao = new AutorizacaoCobrancaClienteVO();
            autorizacao.setCliente(clienteVO);
            autorizacao.setTipoAutorizacao(tipoAutorizacaoCobrancaEnum);
            autorizacao.getConvenio().setCodigo(convenioCobranca);
            autorizacao.setOrigemCobrancaEnum(origemAut);
            if (!possuiMaisDeUmCartaoCadastrado) {
                autorizacao.setOrdem(1);
            }

            boolean isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca = vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getPactoPayComunicacao());

            if (tipoAutorizacaoCobrancaEnum.equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                if (origemAut != null && (origemAut.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) ||
                        origemAut.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_CADASTRAR) || origemAut.isReguaCobranca())) {
                    if (isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca) {
                        autorizacao.setTipoACobrar(empresaVO.getTipoParcelasCobrarVendaSiteRegua());
                    } else {
                        autorizacao.setTipoACobrar(empresaVO.getTipoParcelasCobrarVendaSite());
                    }
                } else {
                    autorizacao.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                }
                autorizacao.setNomeTitularCartao(vendaDTO.getNomeCartao());
                autorizacao.setCvv(vendaDTO.getCvv());
                vendaDTO.setNumeroCartao(vendaDTO.getNumeroCartao().replace("_", ""));

                CreditCardValidator cartaoCredito = new CreditCardValidator();
                autorizacao.setOperadoraCartao(cartaoCredito.operadora(
                        vendaDTO.getNumeroCartao()) == null ? null : OperadorasExternasAprovaFacilEnum.valueOf(cartaoCredito.operadora(vendaDTO.getNumeroCartao())));
                if (autorizacao.getOperadoraCartao() == null) {
                    try {
                        UteisValidacao.validarNumeroCartaoCreditoElo(vendaDTO.getNumeroCartao());
                        autorizacao.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.ELO);
                    } catch (Exception ignored) {
                    }
                }
                if (autorizacao.getOperadoraCartao() == null) {
                    throw new Exception("Cartão inválido, revise os dados do cartão e tente novamente.");
                }
                autorizacao.setNumeroCartao(vendaDTO.getNumeroCartao());
                autorizacao.setValidadeCartao(vendaDTO.getValidade());
                autorizacao.setVencimentoFatura(vendaDTO.getVencimentoFatura());
                if (!UteisValidacao.emptyString(vendaDTO.getCpftitularcard())) {
                    autorizacao.setCpfTitular(vendaDTO.getCpftitularcard());
                }

            } else if (tipoAutorizacaoCobrancaEnum.equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)) {
                autorizacao.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
            }

            autorizacaoCobrancaClienteDAO.incluir(autorizacao);
            con.commit();
            if (isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca) {
                if (!UteisValidacao.emptyNumber(clienteVO.getPessoa().getCodigo()) && empresaVO.isGerarAutCobrancaComCobAutBloqueadaRegua() && origemAut != null && origemAut.isReguaCobranca()) {
                    clienteVO.getPessoa().setDataBloqueioCobrancaAutomatica(new Date());
                    clienteVO.getPessoa().setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum.TODAS_PARCELAS);
                    pessoaDAO.alterarDataBloqueioCobrancaAutomatica(clienteVO.getPessoa().getDataBloqueioCobrancaAutomatica(),
                            clienteVO.getPessoa().getTipoBloqueioCobrancaAutomatica(), clienteVO.getPessoa().getCodigo(), usuarioVO, false, "");
                }
            } else {
                if (!UteisValidacao.emptyNumber(clienteVO.getPessoa().getCodigo()) && empresaVO.isGerarAutCobrancaComCobAutBloqueada() && origemAut != null
                        && origemAut.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_CADASTRAR)) {
                    clienteVO.getPessoa().setDataBloqueioCobrancaAutomatica(new Date());
                    clienteVO.getPessoa().setTipoBloqueioCobrancaAutomatica(TipoBloqueioCobrancaEnum.TODAS_PARCELAS);
                    pessoaDAO.alterarDataBloqueioCobrancaAutomatica(clienteVO.getPessoa().getDataBloqueioCobrancaAutomatica(),
                            clienteVO.getPessoa().getTipoBloqueioCobrancaAutomatica(), clienteVO.getPessoa().getCodigo(), usuarioVO, false, "");
                }
            }
            inclusaoDTO.setNovaAutorizacao(autorizacao);
            gravarPactoPayComunicacao(vendaDTO, null, null, null, autorizacao);
        } catch (Exception ex) {
            ex.printStackTrace();
            //if(clienteVO.getSituacaoClienteSinteticoVO() == null || UteisValidacao.emptyNumber(clienteVO.getSituacaoClienteSinteticoVO().getCodigo())) {
            //    //cria cliente sintetico
            //    clienteDAO.montarDadosSituacaoClienteSintetico(clienteVO, Uteis.NIVELMONTARDADOS_MINIMOS, con);
            //    if(clienteVO.getSituacaoClienteSinteticoVO() == null || UteisValidacao.emptyNumber(clienteVO.getSituacaoClienteSinteticoVO().getCodigo())) {
            //        clienteDAO.alterarSituacaoClienteSintetico(clienteVO.getSituacaoClienteSinteticoVO().getCodigo(), clienteVO.getPessoa().getCodigo(), usuarioVO);
            //    }
            //}
            con.rollback();
            throw ex;
        } finally {
            autorizacaoCobrancaClienteDAO = null;
            clienteMensagemDAO = null;
            con.setAutoCommit(true);
        }
        return inclusaoDTO;
    }

    public ConvenioPagamentoVendasOnlineDTO obterConvenioPadrao(VendaDTO vendaDTO, Integer empresa, boolean apenasLinkPagamentoOnline) throws Exception {
        ResultSet rs;
        if (vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getConvenioCobranca())) {
            rs = SuperFacadeJDBC.criarConsulta("select \n" +
                    "c.codigo as convenio, \n" +
                    "c.tipoParcelamentoStone \n" +
                    "from conveniocobranca c \n" +
                    "where c.codigo = " + vendaDTO.getConvenioCobranca(), con);
        } else if (apenasLinkPagamentoOnline) {
            rs = SuperFacadeJDBC.criarConsulta("select \n" +
                    "c.codigo as convenio, \n" +
                    "c.tipoParcelamentoStone \n" +
                    "from empresa e \n" +
                    "inner join conveniocobranca c on c.codigo = e.convenioCobrancaCartao \n" +
                    "where e.codigo = " + empresa, con);
        } else {
            rs = SuperFacadeJDBC.criarConsulta("select \n" +
                    "conveniocobranca as convenio, \n" +
                    "tipoparcelamentostone as tipoParcelamentoStone \n" +
                    "from vendasonlineconveniotentativa \n" +
                    "where empresa = " + empresa + " \n" +
                    "order by ordem limit 1", con);
        }
        if (rs.next()) {
            ConvenioPagamentoVendasOnlineDTO convenioPagamentoVendasOnlineDTO = new ConvenioPagamentoVendasOnlineDTO();
            convenioPagamentoVendasOnlineDTO.setConvenioCobranca(rs.getInt("convenio"));
            convenioPagamentoVendasOnlineDTO.setTipoParcelamentoStone(rs.getString("tipoParcelamentoStone"));
            return convenioPagamentoVendasOnlineDTO;
        }
        return null;
    }

    public ConvenioPagamentoVendasOnlineDTO obterConvenioPadraoRegua(Integer empresa) throws Exception {
        ResultSet rs;
        rs = SuperFacadeJDBC.criarConsulta("select \n" +
                "c.codigo as convenio, \n" +
                "c.tipoParcelamentoStone \n" +
                "from empresa e \n" +
                "inner join conveniocobranca c on c.codigo = e.convenioCobrancaCartaoRegua \n" +
                "where e.codigo = " + empresa, con);

        if (rs.next()) {
            ConvenioPagamentoVendasOnlineDTO convenioPagamentoVendasOnlineDTO = new ConvenioPagamentoVendasOnlineDTO();
            convenioPagamentoVendasOnlineDTO.setConvenioCobranca(rs.getInt("convenio"));
            convenioPagamentoVendasOnlineDTO.setTipoParcelamentoStone(rs.getString("tipoParcelamentoStone"));
            return convenioPagamentoVendasOnlineDTO;
        }
        return null;
    }

    public JSONArray obterImagens(Integer empresa) throws Exception {
        JSONArray imagens = new JSONArray();
        List<ImagensAcademiaVendasVO> imagensvo = this.vendasConfigDAO.imagens(empresa);
        for (ImagensAcademiaVendasVO i : imagensvo) {
            imagens.put(i.getUrlFoto());
        }
        return imagens;
    }

    public JSONObject obterConfigsVendas(Integer empresa) throws Exception {
        JSONObject retorno = new JSONObject();
        ResultSet rs = SuperFacadeJDBC.criarConsulta(
                "select \n" +
                        "v.redirecionarapp, \n" +
                        "v.camposadicionais,\n" +
                        "v.paginaredirecionar,\n" +
                        "v.cor, \n" +
                        "v.CobrarPrimeiraParcelaCompra,\n" +
                        "v.detalharParcelaTelaCheckout, \n" +
                        "v.titulocheckout, \n" +
                        "v.cobrarProdutosJuntoAdesao, \n" +
                        "v.apresentarvaloranuidade,\n" +
                        "v.apresentarcpflinkpag,\n" +
                        "v.apresentardtfaturalinkpag,\n" +
                        "v.apresentartermoaceitelinkpag, \n" +
                        "v.analyticsId, \n" +
                        "v.pixelId, \n" +
                        "v.tokenApiConversao, \n" +
                        "v.googleTagId, \n" +
                        "v.googleTagIdHotsite, \n" +
                        "v.temaclaro, \n" +
                        "v.temaescuro,\n" +
                        "v.tema,\n" +
                        "e.permitecontratosconcomintante, \n" +
                        "v.permitirMudarTipoParcelamento, \n" +
                        "v.apresentarvalortotaldoplanonateladeselecaodoplano, \n" +
                        "v.permitevendaprodutoalunooutraunidade, \n" +
                        "v.exibirTipoDocumentoTelaVendasOnline, \n" +
                        "v.camposadicionaisproduto, \n" +
                        "v.habilitarAgendamentoAulaExperimentalLinkVisitante, \n" +
                        "v.habilitarprecadastro, \n" +
                        "v.renovarcontratoantigo,  \n" +
                        "v.usarformapagamentoplanoproduto,  \n" +
                        "v.primeiraCobrancaPixEGuardarCartao,  \n" +
                        "v.modalidadesIniciarSelecionadasContratoTurma,  \n" +
                        "v.ativarLinksGooglePlayEAppleStore,  \n" +
                        "v.urlLinkGooglePlay,  \n" +
                        "v.urlLinkAppleStore , \n" +
                        "v.permiteProsseguirMesmoCpfOuEmailCadastroVisitante, \n" +
                        "v.camposadicionaisplanoflow, \n" +
                        "v.camposadicionaisprodutoflow,  \n" +
                        "v.exibedatautilizacao, \n" +
                        "v.igopassUrl, \n" +
                        "v.igopassQrCodeUrl, \n" +
                        "v.igopassEstabelecimento, \n" +
                        "v.igopassUsuario \n" +
                        "from vendasonlineconfig v\n" +
                        "inner join empresa e on e.codigo = v.empresa \n" +
                        "where v.empresa = " + empresa, this.con);

        if (rs.next()) {
            retorno.put("url", rs.getBoolean("redirecionarapp") ? "" : rs.getString("paginaredirecionar"));
            retorno.put("cobrarPrimeiraParcelaCompra", rs.getBoolean("CobrarPrimeiraParcelaCompra"));
            retorno.put("cor", rs.getString("cor"));
            retorno.put("camposAdicionais", rs.getString("camposadicionais"));
            retorno.put("detalharParcelaTelaCheckout", rs.getBoolean("detalharParcelaTelaCheckout"));
            retorno.put("titulocheckout", rs.getString("titulocheckout"));
            retorno.put("cobrarProdutoJuntoAdesaoMatricula", rs.getBoolean("cobrarProdutosJuntoAdesao"));
            retorno.put("apresentarvaloranuidade", rs.getBoolean("apresentarvaloranuidade"));
            retorno.put("apresentarcpflinkpag", rs.getBoolean("apresentarcpflinkpag"));
            retorno.put("apresentardtfaturalinkpag", rs.getBoolean("apresentardtfaturalinkpag"));
            retorno.put("apresentarTermoAceitelinkpag", rs.getBoolean("apresentartermoaceitelinkpag"));
            retorno.put("analyticsId", rs.getString("analyticsId"));
            retorno.put("pixelId", rs.getString("pixelId"));
            retorno.put("tokenApiConversao", rs.getString("tokenApiConversao"));
            retorno.put("googleTagId", rs.getString("googleTagId"));
            retorno.put("googleTagIdHotsite", rs.getString("googleTagIdHotsite"));
            retorno.put("temaclaro", rs.getBoolean("temaclaro"));
            retorno.put("temaescuro", rs.getBoolean("temaescuro"));
            retorno.put("tema", rs.getString("tema"));
            retorno.put("permitecontratosconcomintante", rs.getBoolean("permitecontratosconcomintante"));
            retorno.put("permitirMudarTipoParcelamento", rs.getBoolean("permitirMudarTipoParcelamento"));
            retorno.put("apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano", rs.getBoolean("apresentarvalortotaldoplanonateladeselecaodoplano"));
            retorno.put("permiteVendaProdutoAlunoOutraUnidade", rs.getBoolean("permiteVendaProdutoAlunoOutraUnidade"));
            retorno.put("exibirTipoDocumentoTelaVendasOnline", rs.getBoolean("exibirTipoDocumentoTelaVendasOnline"));
            retorno.put("camposAdicionaisProduto", rs.getString("camposadicionaisproduto"));
            retorno.put("camposAdicionaisProdutoPlano", "");
            retorno.put("habilitarAgendamentoAulaExperimentalLinkVisitante", rs.getBoolean("habilitarAgendamentoAulaExperimentalLinkVisitante"));
            retorno.put("habilitarPreCadastro", rs.getBoolean("habilitarPreCadastro"));
            retorno.put("renovarcontratoantigo", rs.getBoolean("renovarcontratoantigo"));
            retorno.put("usarFormaPagamentoPlanoProduto", rs.getBoolean("usarformapagamentoplanoproduto"));
            retorno.put("configSescHabilitada", configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS).getSesc());
            retorno.put("primeiraCobrancaPixEGuardarCartao", rs.getBoolean("primeiraCobrancaPixEGuardarCartao"));
            retorno.put("modalidadesIniciarSelecionadasContratoTurma", rs.getBoolean("modalidadesIniciarSelecionadasContratoTurma"));
            retorno.put("ativarLinksGooglePlayEAppleStore", rs.getBoolean("ativarLinksGooglePlayEAppleStore"));
            retorno.put("urlLinkGooglePlay", rs.getString("urlLinkGooglePlay"));
            retorno.put("urlLinkAppleStore", rs.getString("urlLinkAppleStore"));
            retorno.put("permiteProsseguirMesmoCpfOuEmailCadastroVisitante", rs.getBoolean("permiteProsseguirMesmoCpfOuEmailCadastroVisitante"));
            retorno.put("camposAdicionaisProdutoFlow", rs.getString("camposadicionaisprodutoflow"));
            retorno.put("camposAdicionaisPlanoFlow", rs.getString("camposadicionaisplanoflow"));
            retorno.put("exibedatautilizacao", rs.getBoolean("exibedatautilizacao"));
            retorno.put("igopassUrl", rs.getString("igopassUrl"));
            retorno.put("igopassQrCodeUrl", rs.getString("igopassQrCodeUrl"));
            retorno.put("igopassEstabelecimento", rs.getString("igopassEstabelecimento"));
            retorno.put("igopassUsuario", rs.getString("igopassUsuario"));

            String camposAdicionaisProdutoPlano = "";

            if (rs.getString("camposadicionais") != null) {
                retorno.put("camposAdicionaisProdutoPlano", rs.getString("camposadicionais"));
                if (rs.getString("camposadicionaisproduto") != null) {
                    String[] textoSeparado = rs.getString("camposadicionais").split(";");
                    String[] textoSeparadoproduto = rs.getString("camposadicionaisproduto").split(";");

                    for (String x : textoSeparadoproduto) {
                        if (!Arrays.stream(textoSeparado).anyMatch(x::equals)) {
                            camposAdicionaisProdutoPlano += ";" + x;
                        }
                    }

                    camposAdicionaisProdutoPlano = rs.getString("camposadicionais") + camposAdicionaisProdutoPlano;
                    retorno.put("camposAdicionaisProdutoPlano", camposAdicionaisProdutoPlano);
                }
            } else if (rs.getString("camposadicionaisproduto") != null) {
                retorno.put("camposAdicionaisProdutoPlano", rs.getString("camposadicionaisproduto"));
            }
        } else {
            retorno.put("url", PropsService.getPropertyValue(PropsService.urlSiteApp));
            retorno.put("cor", "#1998fc");
            retorno.put("cobrarPrimeiraParcelaCompra", true);
            retorno.put("camposAdicionais", "");
            retorno.put("detalharParcelaTelaCheckout", false);
            retorno.put("titulocheckout", "Bora Treinar?");
            retorno.put("cobrarProdutoJuntoAdesaoMatricula", false);
            retorno.put("apresentarvaloranuidade", false);
            retorno.put("apresentarcpflinkpag", false);
            retorno.put("apresentardtfaturalinkpag", true);
            retorno.put("apresentartermoaceitelinkpag", false);
            retorno.put("analyticsId", "");
            retorno.put("pixelId", "");
            retorno.put("tokenApiConversao", "");
            retorno.put("googleTagId", "");
            retorno.put("googleTagIdHotsite", "");
            retorno.put("temaclaro", false);
            retorno.put("temaescuro", false);
            retorno.put("tema", "");
            retorno.put("permitecontratosconcomintante", false);
            retorno.put("permitirMudarTipoParcelamento", false);
            retorno.put("apresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano", false);
            retorno.put("permiteVendaProdutoAlunoOutraUnidade", false);
            retorno.put("exibirTipoDocumentoTelaVendasOnline", false);
            retorno.put("habilitarAgendamentoAulaExperimentalLinkVisitante", false);
            retorno.put("usarFormaPagamentoPlanoProduto", false);
            retorno.put("habilitarPreCadastro", false);
            retorno.put("primeiraCobrancaPixEGuardarCartao", false);
            retorno.put("modalidadesIniciarSelecionadasContratoTurma", false);
            retorno.put("ativarLinksGooglePlayEAppleStore", false);
            retorno.put("urlLinkGooglePlay", "");
            retorno.put("urlLinkAppleStore", "");
            retorno.put("permiteProsseguirMesmoCpfOuEmailCadastroVisitante", false);
            retorno.put("igopassUrl", "");
            retorno.put("igopassQrCodeUrl", "");
            retorno.put("igopassEstabelecimento", "");
            retorno.put("igopassUsuario", "");
        }

        //link de pagamento
        retorno.put("apresentarCartao", !UteisValidacao.emptyNumber(this.empresaDAO.obterConvenioCobrancaCartao(empresa)));
        retorno.put("apresentarPix", !UteisValidacao.emptyNumber(this.empresaDAO.obterConvenioCobrancaPix(empresa)));
        retorno.put("apresentarBoleto", !UteisValidacao.emptyNumber(this.empresaDAO.obterConvenioCobrancaBoleto(empresa)));

        //link de pagamento gerado pela régua de cobrança
        retorno.put("apresentarCartaoRegua", !UteisValidacao.emptyNumber(this.empresaDAO.obterConvenioCobrancaCartaoRegua(empresa)));
        retorno.put("apresentarPixRegua", !UteisValidacao.emptyNumber(this.empresaDAO.obterConvenioCobrancaPixRegua(empresa)));
        retorno.put("apresentarBoletoRegua", !UteisValidacao.emptyNumber(this.empresaDAO.obterConvenioCobrancaBoletoRegua(empresa)));

        //vendas online
        retorno.put("apresentarPixVenda", !UteisValidacao.emptyNumber(obterConvenioPixVendaContrato(empresa)));
        retorno.put("apresentarBoletoVenda", !UteisValidacao.emptyNumber(obterConvenioBoletoVendaContrato(empresa)));
        retorno.put("apresentarCartaoVenda", !UteisValidacao.emptyNumber(obterConvenioCartaoVendaContrato(empresa, false)));
        return retorno;
    }

    public Integer obterConvenioPixVendaContrato(Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("v.empresa, \n");
        sql.append("v.conveniocobrancapix \n");
        sql.append("from vendasonlineconfig v \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = v.conveniocobrancapix \n");
        sql.append("where v.empresa = ").append(empresa).append(" \n");
        sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con);
        if (rs.next()) {
            return rs.getInt("conveniocobrancapix");
        }
        return null;
    }

    private Integer obterConvenioBoletoVendaContrato(Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("v.empresa, \n");
        sql.append("v.conveniocobrancaboleto \n");
        sql.append("from vendasonlineconfig v \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = v.conveniocobrancaboleto \n");
        sql.append("where v.empresa = ").append(empresa).append(" \n");
        sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con);
        if (rs.next()) {
            return rs.getInt("conveniocobrancaboleto");
        }
        return null;
    }

    public Integer consultarClienteCadastrado(VendaDTO dados, boolean permiteVendaProdutoAlunoOutraUnidade) throws Exception {
        if (dados.isVendaConsultor() && UteisValidacao.emptyString(dados.getCpf()) && UteisValidacao.emptyString(dados.getCnpj())) {
            throw new ServiceException("CPF não informado.");
        }
        //Removida validação de e-mail PAY-943
        if (UteisValidacao.emptyString(dados.getCpf()) && UteisValidacao.emptyString(dados.getCnpj()) && !isPactoFlowVendaInternacional(dados)) {
            return null;
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select codigocliente, empresacliente from situacaoclientesinteticodw s \n");
        sql.append("inner join cliente c on c.codigo = s.codigocliente  \n");
        sql.append("inner join pessoa p on p.codigo = s.codigopessoa \n");
        sql.append("left join email e on e.pessoa = p.codigo \n");
        sql.append("where 1=1 \n");

        if (!UteisValidacao.emptyString(dados.getCpf())) {
            sql.append("and (p.cfp = '").append(Uteis.formatarCpfCnpj(dados.getCpf(), true)).append("' or p.cfp = '").append(Uteis.formatarCpfCnpj(dados.getCpf(), false)).append("') \n");
        }
        if (!UteisValidacao.emptyString(dados.getCnpj())) {
            sql.append("and (p.cnpj = '").append(Uteis.formatarCpfCnpj(dados.getCnpj(), true)).append("' or p.cnpj = '").append(Uteis.formatarCpfCnpj(dados.getCnpj(), false)).append("') \n");
        }
        if (!UteisValidacao.emptyString(dados.getPassaporte())) {
            sql.append("and (p.passaporte = '").append(dados.getPassaporte()).append("' or p.passaporte = '").append(dados.getPassaporte().toUpperCase()).append("') \n");
        }

        boolean encontrouPorCpfCnpj = false;
        if (!UteisValidacao.emptyString(dados.getCpf()) || !UteisValidacao.emptyString(dados.getCnpj())) {
            encontrouPorCpfCnpj = SuperFacadeJDBC.existe(sql.toString(), con);
        }

        /* Removido consulta por e-mail PAY-943
        if (!encontrouPorCpfCnpj) {
            if (UteisValidacao.emptyString(dados.getCpf()) && UteisValidacao.emptyString(dados.getCnpj()) && !UteisValidacao.emptyString(dados.getEmail())) {
                sql.append("and e.email ilike '").append(dados.getEmail()).append("' \n");
            } else if (!UteisValidacao.emptyString(dados.getEmail())) {
                sql.append("or e.email ilike '").append(dados.getEmail()).append("' \n");
            }
        }*/

        ResultSet rs = SuperFacadeJDBC.criarConsultaRolavel(sql.toString(), con);
        if (rs.next()) {
            Integer codCliente = rs.getInt("codigocliente");
            while (rs.next()) {
                if (rs.getInt("codigocliente") != codCliente) {
                    throw new Exception("Mais de um cadastro encontrado com este CPF ou passaporte.");
                }
            }
            rs.beforeFirst();
        }
        if (rs.next()) {
            Integer codCliente = rs.getInt("codigocliente");

            if (!permiteVendaProdutoAlunoOutraUnidade && rs.getInt("empresacliente") != dados.getUnidade()) {

                ClienteVO clienteVO = this.clienteDAO.consultarPorCodigo(codCliente, false, Uteis.NIVELMONTARDADOS_GESTAO_PERSONAL);

                //Não permite transferir o aluno se tiver alguma parcela em aberto VENCIDA
                if (clienteVO.getPessoa() != null && !UteisValidacao.emptyNumber(clienteVO.getPessoa().getCodigo())) {
                    List<MovParcelaVO> parcelasVencidas = movParcelaDAO.consultarParcelasVencidasPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyList(parcelasVencidas)) {
                        throw new Exception("O Cadastro existente já possui pendências financeiras. Procure a empresa " + clienteVO.getEmpresa().getNome() + " e resolva antes de lançar um novo contrato.");
                    }
                }

                gravarClienteTrocandoEmpresa(rs.getInt("codigocliente"), dados);
            }
            return codCliente;
        }
        return null;
    }

    private boolean isPactoFlowVendaInternacional(VendaDTO dados) {
        return dados.getOrigemSistema() == OrigemSistemaEnum.APP_FLOW.getCodigo() &&
                !UteisValidacao.emptyString(dados.getPassaporte());
    }

    private Integer consultarClientePactoStore(Integer codigoFinanceiro) throws Exception {
        if (UteisValidacao.emptyNumber(codigoFinanceiro)) {
            throw new Exception("Código financeiro não informado.");
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select cliente from pactostorecliente where codigofinanceiro = " + codigoFinanceiro, con);
        if (rs.next()) {
            return rs.getInt("cliente");
        }
        return null;
    }

    private void incluirPactoStoreCliente(VendaDTO vendaDTO, ClienteVO clienteVO) throws Exception {
        ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("select codigo from pactostorecliente where codigofinanceiro = " + vendaDTO.getCodigoFinanceiro(), con);
        if (!rsExiste.next()) {
            SuperFacadeJDBC.executarConsulta("INSERT INTO pactostorecliente(codigofinanceiro) VALUES (" + vendaDTO.getCodigoFinanceiro() + ");", con);
        }

        try (PreparedStatement stm = con.prepareStatement("UPDATE pactostorecliente SET cliente = ?, codigoempresazw = ?, chave = ? WHERE codigofinanceiro = ?;")) {
            int i = 0;
            stm.setInt(++i, clienteVO.getCodigo());
            stm.setInt(++i, vendaDTO.getCodigoEmpresaZW());
            stm.setString(++i, vendaDTO.getChave());
            stm.setInt(++i, vendaDTO.getCodigoFinanceiro());
            stm.execute();
        }
    }

    private void gravarClienteTrocandoEmpresa(int codCliente, VendaDTO dados) throws SQLException {
        try {
            con.setAutoCommit(false);
            ClienteVO cliente = clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_TODOS);
            setCopiaClienteVO(cliente);
            ClienteVO clienteTemp = (ClienteVO) SerializationUtils.clone(cliente);
            EmpresaVO empresaAntiga = clienteTemp.getEmpresa();
            getCopiaClienteVO().registrarObjetoVOAntesDaAlteracao();
            EmpresaVO empresaDestino = empresaDAO.consultarPorCodigo(dados.getUnidade(), Uteis.NIVELMONTARDADOS_TODOS);
            cliente.setEmpresa(empresaDestino);
            VinculoControle.processarVinculosTrocaEmpresa(cliente, usuarioDAO.getUsuarioRecorrencia());
            clienteDAO.alterarClienteEmpresa(cliente, empresaAntiga);

            ConfiguracaoSistemaVO configuracaoSistemaVO = zwFacade.getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (configuracaoSistemaVO.isCancelarContratoNaUnidadeOrigemAoTransferirAluno()) {

                // Porque este recurso só deve ser aplicado a contratos de recorrencia.
                // Transferir contratos não recorrentes podem gerar problemas como deste ticket: #16553
                int quantidadeContratosNaoRecorrentesVigentes = zwFacade.getContrato().quantidadeContratosNaoRecorrentesVigentes(codCliente);
                if (quantidadeContratosNaoRecorrentesVigentes == 0) {
                    zwFacade.transferirContratosDoClienteParaOutraEmpresa(cliente, empresaDestino, usuarioDAO.getUsuarioRecorrencia());
                }
            }

            zwFacade.getAutorizacaoCobrancaCliente().atualizarAutorizacaoCobrancaClienteCartaoCreditoUsandoConfigReenvioCobrancaAutomatica(empresaDestino.getCodigo(), cliente.getCodigo());

            try {
                LogVO obj = new LogVO();
                obj.setChavePrimaria(getCopiaClienteVO().getPessoa().getCodigo().toString());
                obj.setNomeEntidade("VENDAS ONLINE CLIENTE - EMPRESA");
                obj.setNomeEntidadeDescricao("Vendas Online Cliente - Empresa");
                obj.setOperacao("VENDAS ONLINE - TRANSFERÊNCIA EMPRESA");
                obj.setResponsavelAlteracao(usuarioDAO.getUsuarioRecorrencia().getNome());
                obj.setUserOAMD(usuarioDAO.getUsuarioRecorrencia().getUserOamd());
                obj.setNomeCampo("Transferência de Cliente");
                obj.setValorCampoAnterior("CÓDIGO: " + empresaAntiga.getCodigo() + " - EMPRESA: " + empresaAntiga.getNome().toUpperCase() + "");
                obj.setValorCampoAlterado("CÓDIGO: " + cliente.getEmpresa().getCodigo().toString() + " - EMPRESA: " + cliente.getEmpresa().getNome().toUpperCase() + "");
                obj.setDataAlteracao(Calendario.hoje());
                registrarLogObjetoVO(obj, getCopiaClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("VENDAS ONLINE CARTEIRAS", getCopiaClienteVO().getPessoa().getCodigo(), "ERRO AO TRANSFERIR CLIENTE DE EMPRESA", usuarioDAO.getUsuarioRecorrencia().getNome(), usuarioDAO.getUsuarioRecorrencia().getUserOamd());
                e.printStackTrace();
            }
            cliente.registrarObjetoVOAntesDaAlteracao();
            cliente.getPessoa().registrarObjetoVOAntesDaAlteracao();

            con.commit();
        } catch (Exception e) {
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void atualizarAluno(VendaDTO dados, Integer codigopessoa) throws Exception {
        //Pessoa antes da alteração
        PessoaVO pessoaAntesAlteracao = this.pessoaDAO.consultarPorChavePrimaria(codigopessoa, Uteis.NIVELMONTARDADOS_TELACONSULTA);

        //NOME
        String nome = "update pessoa set nome = '" + dados.getNome().toUpperCase() + "' where codigo = " + codigopessoa;
        SuperFacadeJDBC.executarConsulta(nome, this.con);
        //CPF
        String cpf = "update pessoa set cfp = '" + dados.getCpf() + "' where codigo = " + codigopessoa;
        SuperFacadeJDBC.executarConsulta(cpf, this.con);

        String sexo = "update pessoa set sexo = '" + dados.getSexo() + "' where codigo = " + codigopessoa;
        SuperFacadeJDBC.executarConsulta(sexo, this.con);
        //ENDERECO
        if (!UteisValidacao.emptyString(dados.getEndereco()) || !UteisValidacao.emptyString(dados.getBairro()) ||
                !UteisValidacao.emptyString(dados.getCep()) || !UteisValidacao.emptyString(dados.getCep())
                || !UteisValidacao.emptyString(dados.getNumero())) {
            String deleteEndereco = "delete from endereco where pessoa = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(deleteEndereco, this.con);
            String endereco = "INSERT into endereco (bairro, tipoendereco, endereco, cep, numero, complemento, pessoa) values ('"
                    + (!UteisValidacao.emptyString(dados.getBairro()) ? dados.getBairro() : "-") +
                    "', 'RE" +
                    "', '" + (!UteisValidacao.emptyString(dados.getEndereco()) ? dados.getEndereco() : "-") +
                    "', '" + (!UteisValidacao.emptyString(dados.getCep()) ? dados.getCep() : "-") +
                    "', '" + (!UteisValidacao.emptyString(dados.getNumero()) ? dados.getNumero() : "-") +
                    "', '" + (!UteisValidacao.emptyString(dados.getComplemento()) ? dados.getComplemento() : "-") +
                    "', " + codigopessoa + ") ";
            SuperFacadeJDBC.executarConsulta(endereco, this.con);
        }
        //EMAIL
        String deleteEmail = "delete from email where pessoa = " + codigopessoa;
        SuperFacadeJDBC.executarConsulta(deleteEmail, this.con);
        String email = "insert into email (pessoa, emailcorrespondencia, email, bloqueadobounce, receberemailnovidades) " +
                "values(" + codigopessoa + ", true, '" + dados.getEmail() + "', false, true)";
        SuperFacadeJDBC.executarConsulta(email, this.con);

        //NOME RESPONSAVEL EMPRESA
        String nomeResponsavelEmpresa = "UPDATE pessoa SET nomeResponsavelEmpresa = '" + dados.getNomeResponsavelEmpresa().toUpperCase() + "' WHERE codigo = " + codigopessoa;
        SuperFacadeJDBC.executarConsulta(nomeResponsavelEmpresa, this.con);

        //CPF RESPONSAVEL EMPRESA
        String cpfResponsavelEmpresa = "UPDATE pessoa SET cpfResponsavelEmpresa = '" + dados.getCpfResponsavelEmpresa() + "' WHERE codigo = " + codigopessoa;
        SuperFacadeJDBC.executarConsulta(cpfResponsavelEmpresa, this.con);

        //TELEFONE
        if (!UteisValidacao.emptyString(dados.getTelefone())) {

            if (!UteisValidacao.validaTelefone(dados.getTelefone())) {
                throw new Exception("O telefone informado " + dados.getTelefone() + " é inválido. Por favor, verifique as informações e tente novamente.");
            }

            String apagarTelefones = "DELETE FROM telefone WHERE pessoa = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(apagarTelefones, this.con);

            String inserirTelefone = "INSERT INTO telefone (pessoa, tipotelefone, numero) \n" +
                    "VALUES (" + codigopessoa + ", '" + TipoTelefoneEnum.CELULAR.getCodigo() + "', '" + dados.getTelefone() + "')";
            SuperFacadeJDBC.executarConsulta(inserirTelefone, this.con);
        }

        if (!UteisValidacao.emptyString(dados.getRg())) {
            String rg = "update pessoa set rg = '" + dados.getRg() + "' where codigo = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(rg, this.con);
        }

        if (!UteisValidacao.emptyString(dados.getRg())) {
            String nascimento = "update pessoa set datanasc = '" + Uteis.getDataJDBC(Calendario.getDate("dd/MM/yyyy", dados.getDataNascimento())) + "' where codigo = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(nascimento, this.con);
        }

        //No momento foi construido apenas para a tela de Cadastro de Visitante.
        //No futuro, se necessário, pode ser ajustado para outras telas.
        String operacao = "";
        try {
            operacao = obterOperacaoUrl(dados.getUrlZw());
        } catch (Exception e) {
            System.err.println("Erro ao obter a operação da URL: " + e.getMessage());
        }
        if (!UteisValidacao.emptyString(operacao) && operacao.equals("cadastrarClienteVendasOnline")) {
            //RG
            String rg = "update pessoa set rg = '" + dados.getRg() + "' where codigo = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(rg, this.con);
            //DATA NASCIMENTO
            try {
                // Converter a string para java.sql.Date
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
                java.util.Date parsedDate = dateFormat.parse(dados.getDataNascimento());
                Date sqlDate = new Date(parsedDate.getTime());

                // Formatar a data no formato aceito pelo banco (yyyy-MM-dd)
                String formattedDate = sqlDate.toString();

                // Concatenar na consulta
                String dataNascimento = "update pessoa set datanasc = '" + formattedDate + "' where codigo = " + codigopessoa;
                SuperFacadeJDBC.executarConsulta(dataNascimento, this.con);
            } catch (Exception e) {
                System.err.println("Erro ao converter a data de nascimento: " + e.getMessage());
            }
            //RESPONSAVEL PAI
            String nomePai = "update pessoa set nomepai = '" + dados.getResponsavelPai() + "' where codigo = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(nomePai, this.con);
            //CPF RESPONSAVEL PAI
            String cpfPai = "update pessoa set cpfpai = '" + dados.getCpfPai() + "' where codigo = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(cpfPai, this.con);
            //RESPONSAVEL MAE
            String nomeMae = "update pessoa set nomemae = '" + dados.getResponsavelMae() + "' where codigo = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(nomeMae, this.con);
            //CPF RESPONSAVEL MAE
            String cpfMae = "update pessoa set cpfmae = '" + dados.getCpfMae() + "' where codigo = " + codigopessoa;
            SuperFacadeJDBC.executarConsulta(cpfMae, this.con);
        }

        UsuarioVO usuarioVO = obterUsuarioVendasOnline(dados);
        Log logDao = new Log(con);
        logDao.incluirLogEditarClienteSite(dados, pessoaAntesAlteracao, usuarioVO);
        logDao = null;
    }

    public String obterOperacaoUrl(String url) {
        String urlRetornar = "";
        try {
            URI uri = new URI(url);
            String query = uri.getQuery();
            String operacao = null;

            if (query != null) {
                String[] parametros = query.split("&");
                for (String parametro : parametros) {
                    if (parametro.startsWith("operacao=")) {
                        operacao = parametro.split("=")[1];
                        break;
                    }
                }
            }

            if (!UteisValidacao.emptyString(operacao)) {
                urlRetornar = operacao;
            }
        } catch (URISyntaxException e) {
            System.out.println("URL inválida: " + e.getMessage());
        }
        return urlRetornar;
    }

    public void enviarEmailAviso(VendaDTO vendaDTO, ContratoVO contratoVO,
                                 VendaAvulsaVO vendaAvulsaVO,
                                 ClienteVO clienteVO,
                                 AulaAvulsaDiariaVO diariaVO,
                                 EmpresaVO empresaVO, Boolean sucesso, String retorno) throws Exception {

        if (!ENVIAR_EMAIL_VENDAS_ONLINE) {
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta("select emailavisar from vendasonlineconfig " +
                "where empresa = " + empresaVO.getCodigo(), this.con);

        if (rs.next()) {
            String emailavisar = rs.getString("emailavisar");
            if (UteisValidacao.emptyString(emailavisar)) {
                return;
            }

            String[] emailsarray = emailavisar.split("\\;");
            for (String email : emailsarray) {
                Uteis.logar(null, "Enviando email confirmação de venda online para " + email + " da empresa " + empresaVO.getNome());
                String[] emails = new String[1];
                emails[0] = email;
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = SuperControle.getConfiguracaoSMTPNoReply();

                String plano = "";
                String produtos = "";
                Double valor = 0.0;
                if (contratoVO != null) {
                    plano = contratoVO.getPlano().getDescricao();
                    valor = contratoVO.getValorFinal();
                }
                if (vendaAvulsaVO != null) {
                    String listaProdutos = "";
                    for (MovProdutoVO movProdutoVO : vendaAvulsaVO.getMovProdutoVOs()) {
                        listaProdutos += "," + movProdutoVO.getProduto().getDescricao() + " ";
                    }
                    produtos = listaProdutos.replaceFirst(",", "");
                    valor = vendaAvulsaVO.getValorTotal();
                }

                if (diariaVO != null) {
                    produtos = "DIARIA";
                    valor = diariaVO.getValor();
                }

                MsgTO msg = new MsgTO(textoEmail(empresaVO.getNome(),
                        clienteVO.getMatricula(),
                        vendaDTO.getNome(),
                        Calendario.hoje(),
                        plano,
                        produtos,
                        valor,
                        retorno,
                        sucesso),
                        (sucesso ? "[Sistema Pacto - Venda realizada] - " : "[Sistema Pacto - Tentativa malsucedida] - ") + clienteVO.getMatricula(),
                        empresaVO.getNome(), true,
                        configuracaoSistemaCRMVO,
                        false, emails);

                UteisValidacao.enfileirarEmail(msg);
            }
        }
    }

    private StringBuffer textoEmail(String nomeEmpresa,
                                    String matricula,
                                    String nomeCliente,
                                    Date dataCadastro,
                                    String plano,
                                    String produtos,
                                    Double valor,
                                    String status,
                                    boolean sucesso) {

        String dataEnvio = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy");
        String horaEnvio = Uteis.getDataAtualAplicandoFormatacao("HH:mm");
        StringBuffer email = new StringBuffer();
        email.append("<html>\n");
        email.append("<head>\n");
        email.append("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n");
        if (sucesso) {
            email.append("    <title><b>SUCESSO</b> - Nova venda pelo site</title>\n");
        } else {
            email.append("    <title><b>TENTATIVA MALSUCEDIDA</b> - Venda pelo site</title>\n");

        }

        email.append("</head>\n");
        email.append("\n");
        email.append("<body style=\"padding: 40px 30px; font-family: Arial;\">\n");
        email.append("<div id=\"cabecalho\">\n");
        if (sucesso) {
            email.append("    <h1 style=\"font-size: 16px; text-transform: uppercase; display: inline-block; width: 50%;\">Venda concluída pelo site</h1>\n");
        } else {
            email.append("    <h1 style=\"font-size: 16px; text-transform: uppercase; display: inline-block; width: 50%;\">Tentativa de Venda pelo site</h1>\n");

        }

        email.append("    <div style=\"display: inline-block; width: 40%; float: right; font-size: 16px; text-align: right; padding-top: 10px;\">").append(dataEnvio).append(" <span style=\"font-size:12px\">").append(horaEnvio).append("</span></div>\n");
        email.append("    <hr>\n");
        email.append("    <div id=\"dadosRel\">\n");
        email.append("        <div id=\"colunaA\" style=\"width:49.5%; display: inline-block; font-weight:bold\">\n");
        if (!UteisValidacao.emptyString(plano)) {
            email.append("            <div>Plano: <span style=\"font-weight: normal\">").append(plano).append("</span></div>\n");
        }
        if (!UteisValidacao.emptyString(produtos)) {
            email.append("            <div>Produtos: <span style=\"font-weight: normal\">").append(produtos).append("</span></div>\n");
        }
        email.append("            <div>Empresa: <span style=\"font-weight: normal\">").append(nomeEmpresa).append("</span></div>\n");
        email.append("            <div>Matrícula: <span style=\"font-weight: normal\">").append(matricula).append("</span></div>\n");
        email.append("            <div>Nome do cliente: <span style=\"font-weight: normal\">").append(nomeCliente).append("</span></div>\n");
        email.append("            <div>Data do cadastro: <span style=\"font-weight: normal\">").append(Uteis.getDataAplicandoFormatacao(dataCadastro, "dd/MM/yyyy HH:mm")).append("</span></div>\n");

        email.append("            <br/><div>Valor: <span style=\"font-weight: normal\">").append(Formatador.formatarValorMonetario(valor)).append("</span></div>\n");
        email.append("            <div>Retorno: <span style=\"font-weight: normal\">").append(status).append("</span></div>\n");
        email.append("        </div>\n");
        email.append("    </div>\n");
        email.append("    <hr>\n");
        email.append("</div>\n");
        email.append("</body>\n");
        email.append("</html>");
        return email;
    }

    private boolean obterConfigCriarAutorizacaoCobrancaBoleto(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select criarAutorizacaoCobrancaBoleto from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean(1) : false;
    }

    private boolean obterConfigPrimeiraCobrancaPixEGuardarCartao(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select primeiraCobrancaPixEGuardarCartao from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean(1) : false;
    }

    private boolean obterConfigGerarBoletoTodasParcelas(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select gerarBoletoTodasParcelas from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean(1) : false;
    }

    private Integer obterConfigDiasVencimentoBoleto(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select diasVencimentoBoleto from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getInt(1) : 0;
    }

    public boolean obterConfigPrimeiraParcela(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select CobrarPrimeiraParcelaCompra from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean("cobrarPrimeiraParcelaCompra") : true;
    }

    public boolean obterConfigPermiteContratoConcomitanteComParcelaEmAberto(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select permiteContratoConcomitanteComParcelaEmAberto from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean("permiteContratoConcomitanteComParcelaEmAberto") : true;
    }

    public boolean obterConfigPermiteVendaProdutoAlunoOutraUnidade(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select permiteVendaProdutoAlunoOutraUnidade from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean("permiteVendaProdutoAlunoOutraUnidade") : true;
    }

    public boolean obterConfigApresentarValorTotalDoPlano(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select apresentarvalortotaldoplanonateladeselecaodoplano from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean("apresentarvalortotaldoplanonateladeselecaodoplano") : false;
    }

    public boolean obterConfigPrimeiraParcelaRenovacao(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select cobrarPrimeiraParcelaCompraRenovacao from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean("cobrarPrimeiraParcelaCompraRenovacao") : false;
    }

    public boolean obterConfigIncluirCategoriaPix(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select incluirCategoriaPix from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean(1) : false;
    }

    public boolean obterConfigRenovarContratoAntigo(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select renovarContratoAntigo from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean("renovarContratoAntigo") : false;
    }

    public ContratoVO simular(String key, VendaDTO dados, Integer unidade) throws ServiceException {
        try {
            Integer codigoCliente = null;
            boolean pactoStore = false;

            if (pactoStore) {
                codigoCliente = consultarClientePactoStore(dados.getCodigoFinanceiro());
            }

            if (UteisValidacao.emptyNumber(codigoCliente)) {
                codigoCliente = consultarClienteCadastrado(dados, false);
                codigoCliente = codigoCliente == null ? 0 : codigoCliente;
            }

            ContratoVO contratoASerRenovado = null;
            ContratoVO contratoASerRematriculado = null;
            ClienteVO clienteVO = null;
            if (!UteisValidacao.emptyNumber(codigoCliente)) {
                clienteVO = this.clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (dados.isPermitirRenovacao()) {
                    contratoASerRenovado = obterContratoASerRenovadoVendasOnline(clienteVO, pactoStore);
                }
                if (contratoASerRenovado == null) {
                    contratoASerRematriculado = obterContratoASerRematriculadoVendasOnline(clienteVO, pactoStore);
                }
            }

            ContratoSiteService contratoSiteService = new ContratoSiteService(this.con);
            ContratoVO contratoVO = contratoSiteService.incluirContratoSite(key, dados.getPlano(), codigoCliente, 1,
                    1, true, true, dados.getDiaVencimento(), null, true,
                    unidade, "", null, null, contratoASerRenovado, contratoASerRematriculado,
                    dados.getModalidadesSelecionadas(), dados.getHorariosSelecionados());
            if (!UteisValidacao.emptyString(dados.getCpf()) && contratoVO.getPlano().getRestringeVendaPorCategoria() && contratoVO.getPlano().getPlanoCategoriaVOs() != null && !contratoVO.getPlano().getPlanoCategoriaVOs().isEmpty()) {
                boolean possuiCategoria = false;
                if (clienteVO != null && clienteVO.getCategoria() != null && !UteisValidacao.emptyNumber(clienteVO.getCategoria().getCodigo())) {
                    for (PlanoCategoriaVO planoCategoriaVO : contratoVO.getPlano().getPlanoCategoriaVOs()) {
                        if (planoCategoriaVO.getCategoria().getCodigo().equals(clienteVO.getCategoria().getCodigo())) {
                            possuiCategoria = true;
                            break;
                        }
                    }
                }
                if (!possuiCategoria) {
                    throw new ServiceException("Parece que este plano é exclusivo para uma categoria específica de alunos, e você não se enquadra nesses critérios. Por favor, escolha outro plano adequado á sua categoria.");
                }
            }
            return contratoVO;
        } catch (Exception e) {
            if (!UteisValidacao.emptyString(e.getMessage()) && (e.getMessage().startsWith("Parece") || e.getMessage().startsWith("O horário"))) {
                throw new ServiceException(e.getMessage());
            }
            e.printStackTrace();
        }
        return new ContratoVO();
    }

    public ContratoVO simular(String key, Integer plano, Integer unidade) {
        try {
            ContratoSiteService contratoSiteService = new ContratoSiteService(this.con);
            ContratoVO contratoVO = contratoSiteService.incluirContratoSite(key, plano, 0, 1, 1, true, true, 0, null, true, unidade, "", null, null, null, null, null, null);
            contratoSiteService = null;
            return contratoVO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ContratoVO();
    }

    private void incluirLogVendasOnline(List<RetornoVendaTO> retornoVendaTOList, String operacao, String msgLog) {
        for (RetornoVendaTO retDTO : retornoVendaTOList) {
            try {
                incluirLogVendasOnline(retDTO.getCodigoCliente(), retDTO.getVendaDTO(), operacao, msgLog);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void incluirLogVendasOnline(Integer cliente, VendaDTO vendaDTO, String operacao, String msgLog) {
        try {
            String sql = "INSERT INTO vendasonlinelog(dataregistro, nomeCliente, cliente, plano, produtos, operacao, log, utm_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?);";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                int i = 0;
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));

                //nomeCliente
                if (vendaDTO != null && !UteisValidacao.emptyString(vendaDTO.getNome())) {
                    pst.setString(++i, vendaDTO.getNome());
                } else {
                    pst.setString(++i, "");
                }

                //cliente
                if (UteisValidacao.emptyNumber(cliente)) {
                    pst.setNull(++i, Types.NULL);
                } else {
                    pst.setInt(++i, cliente);
                }
                //plano
                if (vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getPlano())) {
                    pst.setInt(++i, vendaDTO.getPlano());
                } else {
                    pst.setNull(++i, Types.NULL);
                }
                //produtos
                if (vendaDTO != null) {
                    pst.setString(++i, new JSONArray(vendaDTO.getProdutos()).toString());
                } else {
                    pst.setString(++i, "");
                }

                pst.setString(++i, operacao);
                pst.setString(++i, msgLog);
                pst.setString(++i, vendaDTO.getUtm_data());
                pst.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void incluirRegistroVenda(Integer cliente, VendaDTO vendaDTO, RetornoVendaTO retornoVendaTO, String retorno) {
        try {

            /**
             * Esse deve ser um registro único com o resultado da operação de venda
             * Seja sucesso ou seja falha deve ser criado somente 1 registro.
             * Pois esse é utilizado para retirar os indicadores de SUCESSO e FALHAS.
             * by Luiz Felipe 26/06/2020
             **/

            JSONObject clienteJSON = new JSONObject();
            clienteJSON.put("unidade", vendaDTO.getUnidade());
            clienteJSON.put("nome", vendaDTO.getNome());
            if (!UteisValidacao.emptyString(vendaDTO.getCpf())) {
                clienteJSON.put("cpf", vendaDTO.getCpf());
            }
            if (!UteisValidacao.emptyString(vendaDTO.getCnpj())) {
                clienteJSON.put("cnpj", vendaDTO.getCnpj());
            }
            clienteJSON.put("email", vendaDTO.getEmail());
            clienteJSON.put("telefone", vendaDTO.getTelefone());
            clienteJSON.put("plano", vendaDTO.getPlano());
            clienteJSON.put("produtos", vendaDTO.getProdutos());


            //verificar se o dado realmente existe
            Integer vendaAvulsa = 0;
            if (retornoVendaTO != null && !UteisValidacao.emptyNumber(retornoVendaTO.getVendaAvulsa())) {
                boolean existe = SuperFacadeJDBC.existe("select codigo from vendaavulsa where codigo = " + retornoVendaTO.getVendaAvulsa(), this.con);
                if (existe) {
                    vendaAvulsa = retornoVendaTO.getVendaAvulsa();
                }
            }

            //verificar se o dado realmente existe
            Integer contrato = 0;
            if (retornoVendaTO != null && !UteisValidacao.emptyNumber(retornoVendaTO.getContrato())) {
                boolean existe = SuperFacadeJDBC.existe("select codigo from contrato where codigo = " + retornoVendaTO.getContrato(), this.con);
                if (existe) {
                    contrato = retornoVendaTO.getContrato();
                }
            }


            String sql = "INSERT INTO vendasonline(dataRegistro, cliente, contrato, vendaavulsa, dadoscliente, retorno,vendasonlinecampanhaicv) VALUES (?, ?, ?, ?, ?, ?,?);";
            try (PreparedStatement pst = this.con.prepareStatement(sql)) {
                int i = 0;
                pst.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));

                //cliente
                if (UteisValidacao.emptyNumber(cliente)) {
                    pst.setNull(++i, Types.NULL);
                } else {
                    pst.setInt(++i, cliente);
                }

                //contrato
                if (UteisValidacao.emptyNumber(contrato)) {
                    pst.setNull(++i, Types.NULL);
                } else {
                    pst.setInt(++i, contrato);
                }

                //vendaavulsa
                if (UteisValidacao.emptyNumber(vendaAvulsa)) {
                    pst.setNull(++i, Types.NULL);
                } else {
                    pst.setInt(++i, vendaAvulsa);
                }
                pst.setString(++i, clienteJSON.toString());
                pst.setString(++i, retorno);
                if (UteisValidacao.emptyNumber(vendaDTO.getCodigoRegistroAcessoPagina())) {
                    pst.setNull(++i, Types.NULL);
                } else {
                    pst.setInt(++i, vendaDTO.getCodigoRegistroAcessoPagina());
                }
                pst.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private ClienteVO incluirObterCliente(VendaDTO vendaDTO, UsuarioVO usuarioResponsavel,
                                          boolean pactoStore, Boolean ignoraCpf, Boolean atualizarSintetico) throws Exception {
        Integer codigoCliente = null;
        try {

            //Decisão do time, se for para trocar, chamar time para discutir
            //No Vendas Online a prioridade é da pessoa Física. Foi tratado no front para enviar apenas um, mas se por erro vir os dois, limpar o cnpj e priorizar o cpf
            if (!UteisValidacao.emptyString(vendaDTO.getCpf()) && !UteisValidacao.emptyString(vendaDTO.getCnpj())) {
                vendaDTO.setCnpj("");
            }

            if(!UteisValidacao.emptyString(vendaDTO.getPassaporte()) && (vendaDTO.getPassaporte().length() > 20 || vendaDTO.getPassaporte().length() < 6)) {
                throw new Exception("O passaporte informado é inválido. Por favor, verifique as informações e tente novamente.");
            }

            if(!UteisValidacao.emptyString(vendaDTO.getPassaporte()) && !UteisValidacao.emptyString(vendaDTO.getCpf())) {
                throw new Exception("Não é possível realizar a venda com passaporte e CPF preenchidos ao mesmo tempo.");
            }

            if (pactoStore) {
                codigoCliente = consultarClientePactoStore(vendaDTO.getCodigoFinanceiro());
            }

            //permitir venda de produto para aluno de outra unidade não deve transferir o aluno de unidade
            boolean permiteVendaProdutoAlunoOutraUnidade = false;
            if (vendaDTO != null && UteisValidacao.emptyNumber(vendaDTO.getPlano()) &&
                    !UteisValidacao.emptyList(vendaDTO.getProdutos())) {
                permiteVendaProdutoAlunoOutraUnidade = obterConfigPermiteVendaProdutoAlunoOutraUnidade(vendaDTO.getUnidade());
            }

            if (UteisValidacao.emptyNumber(codigoCliente)) {
                codigoCliente = consultarClienteCadastrado(vendaDTO, permiteVendaProdutoAlunoOutraUnidade);
            }
            Empresa empresaDAO = new Empresa(con);
            Indicado indicadoDAO = new Indicado(con);
            List<EmpresaVO> empresaList = empresaDAO.consultarPorCodigo(usuarioResponsavel.getCodEmpresaLogada(), true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresaList.forEach(empresaItem -> {
                try {
                    List indicado = indicadoDAO.consultarPorCpf(vendaDTO.getCpf(), empresaItem, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!indicado.isEmpty()) {
                        throw new ServiceException("Convite já cadastrado.");
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    throw new RuntimeException(ex);
                }
            });

            if (vendaDTO.isVendaConsultor() && vendaDTO.getNome().isEmpty() && codigoCliente == null) {
                throw new ServiceException("O cliente com cpf " + vendaDTO.getCpf() + " não está cadastrado.");
            }

            ConfiguracaoSistemaVO configuracaoSistemaVO = zwFacade.getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            String cpfValidar = vendaDTO.getCpf().trim();
            if (UteisValidacao.emptyString(vendaDTO.getCnpj()) && configuracaoSistemaVO.isCpfValidar() && !isPactoFlowVendaInternacional(vendaDTO) &&
                    !UteisValidacao.emptyString(cpfValidar) && !SuperVO.verificaCPF(cpfValidar)) {
                throw new ServiceException("O CPF não é válido.");
            }

            boolean atualizarcliente = false;
            if (UteisValidacao.emptyNumber(codigoCliente)) {
                vendaDTO.setClienteJaCadastrado(false);

                ClienteSiteService clienteSiteService = new ClienteSiteService(this.con);

                if (!UteisValidacao.emptyString(vendaDTO.getCnpj())) {
                    //inclusão de cliente por CNPJ
                    //Utilizado pelo PactoStore e pelo Vendas Online
                    try {
                        ClienteVO clienteVO = clienteSiteService.incluirClienteCNPJ(vendaDTO, usuarioResponsavel, pactoStore);
                        codigoCliente = clienteVO.getCodigo();
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_INCLUIR_CLIENTE_CNPJ", ex.getMessage());
                        throw ex;
                    }
                } else {
                    //inclusão de cliente por CPF ou passaporte
                    try {
                        String resultadoPersistirCliente = clienteSiteService.incluirClienteSite(vendaDTO, usuarioResponsavel, ignoraCpf, atualizarSintetico, isPactoFlowVendaInternacional(vendaDTO));
                        if (resultadoPersistirCliente.startsWith("ERRO")) {
                            throw new Exception(resultadoPersistirCliente);
                        }
                        String[] codCliente_s = resultadoPersistirCliente.split("CodCliente ");
                        codigoCliente = Integer.valueOf(codCliente_s[1]);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_INCLUIR_CLIENTE_CPF", ex.getMessage());
                        throw ex;
                    } finally {
                        if (!UteisValidacao.emptyList(vendaDTO.getClientesCadastradosComoDependentesPlanoCompartilhado())) {
                            clienteSiteService.incluirClientesDependentesSite(vendaDTO, usuarioResponsavel);
                        }
                        clienteSiteService = null;
                    }
                }
            } else {
                atualizarcliente = true;
            }

            ClienteSiteService clienteSiteService = new ClienteSiteService(this.con);
            Integer codigoPessoa = this.pessoaDAO.obterPessoaCliente(codigoCliente);
            atualizarcliente = vendaDTO.isVendaConsultor() && vendaDTO.getNome().isEmpty() ? false : atualizarcliente;
            if (atualizarcliente) {
                atualizarAluno(vendaDTO, codigoPessoa);
                if (!UteisValidacao.emptyList(vendaDTO.getClientesCadastradosComoDependentesPlanoCompartilhado())) {
                    clienteSiteService.incluirClientesDependentesSite(vendaDTO, usuarioResponsavel);

                }

            }
            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (atualizarcliente) {
                clienteVO.setNovoObj(false);
            } else {
                clienteVO.setNovoObj(true);
            }

            try {
                zwFacade.atualizarSintetico(zwFacade.getCliente().consultarPorCodigoPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            return clienteVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setCodigo(codigoCliente);
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_INCLUIR_CLIENTE", ex.getMessage());
            throw ex;
        }
    }

    private ContratoVO incluirContrato(String key, ClienteVO clienteVO, VendaDTO vendaDTO,
                                       UsuarioVO usuarioVO, ContratoVO contratoASerRenovado, ContratoVO contratoASerRematriculado, EmpresaVO empresaVO, RetornoVendaTO retDTO) throws Exception {
        ContratoSiteService service = null;
        try {
            service = new ContratoSiteService(this.con);

            //não permite venda de contrato concomitante se o cliente já tiver alguma parcela em aberto VENCIDA
            if (!UteisValidacao.emptyNumber(vendaDTO.getUnidade()) && !obterConfigPermiteContratoConcomitanteComParcelaEmAberto(vendaDTO.getUnidade())
                    && clienteVO.getPessoa() != null && !UteisValidacao.emptyNumber(clienteVO.getPessoa().getCodigo())) {
                List<MovParcelaVO> parcelasVencidas = movParcelaDAO.consultarParcelasVencidasPessoa(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_REGISTRAR_JUSTIFICATIVA);
                if (!UteisValidacao.emptyList(parcelasVencidas)) {
                    throw new Exception("O Cadastro existente já possui pendências financeiras. Procure a empresa " + empresaVO.getNome() + " e resolva antes de lançar um novo contrato.");
                }
            }

            boolean cobrarPrimeiraParcela = (contratoASerRenovado == null && obterConfigPrimeiraParcela(vendaDTO.getUnidade())) || contratoASerRenovado != null && obterConfigPrimeiraParcelaRenovacao(vendaDTO.getUnidade());
            Date primeiraParcela = primeiraParcela(vendaDTO.getDiaVencimento(), cobrarPrimeiraParcela, vendaDTO.getPlano());

            ColaboradorVO colaboradorVO = null;
            if (!UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel())) {
                Colaborador colaboradorDAO = new Colaborador(con);
                colaboradorVO = colaboradorDAO.consultarPorCodigoUsuario(vendaDTO.getUsuarioResponsavel(), vendaDTO.getUnidade(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                    //se não encontrou com a empresa, buscar sem a empresa
                    colaboradorVO = colaboradorDAO.consultarPorCodigoUsuario(vendaDTO.getUsuarioResponsavel(), 0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                        colaboradorVO = null;
                    }
                }
                colaboradorDAO = null;
                if (colaboradorVO != null && colaboradorVO.getUsuarioVO() == null) {
                    //tentar descorbrir o usuário do colaborador, é usado em alguns métodos mais pra frente
                    Usuario userDAO;
                    try {
                        userDAO = new Usuario(con);
                        UsuarioVO userCol = userDAO.consultarPorCodigoColaborador(colaboradorVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        colaboradorVO.setUsuarioVO(userCol);
                    } catch (Exception ignore) {
                    } finally {
                        userDAO = null;
                    }
                }
            }
            if (!UteisValidacao.emptyNumber(vendaDTO.getCodigoColaborador())) {
                Colaborador colaborador = new Colaborador(this.con);
                colaboradorVO = colaborador.consultarPorCodigo(vendaDTO.getCodigoColaborador(), 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                colaborador = null;
                if (vendaDTO.isVendaConsultor() && (colaboradorVO == null || colaboradorVO.getCodigo() == 0)) {
                    throw new ServiceException("O colaborador de código " + vendaDTO.getCodigoColaborador() + " não existe");
                }
            }

            return service.incluirContratoSite(key, vendaDTO.getPlano(), clienteVO.getCodigo(), 1, 1,
                    vendaDTO.getNrVezesDividirMatricula(), true, true, vendaDTO.getDiaVencimento(), primeiraParcela, false, 0,
                    vendaDTO.getNumeroCupomDesconto(), vendaDTO.getDataInicioContrato(), vendaDTO.getDataLancamento(), contratoASerRenovado, contratoASerRematriculado,
                    colaboradorVO, vendaDTO.getCodigoEvento(), vendaDTO, vendaDTO.getModalidadesSelecionadas(), retDTO, null);
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_INCLUIR_CONTRATO", ex.getMessage());
            this.logDAO.incluirLogErroInclusaoContrato(clienteVO.getCodigo(), usuarioVO, ex.getMessage());
            throw ex;
        } finally {
            service = null;
        }
    }

    private VendaAvulsaVO incluirVendaAvulsa(ClienteVO clienteVO, VendaDTO vendaDTO, UsuarioVO usuarioVO) throws Exception {
        VendaAvulsaSiteService service = null;
        try {
            service = new VendaAvulsaSiteService(this.con);
            return service.incluirVendaAvulsaSite(vendaDTO, clienteVO, usuarioVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirLogVendasOnline(clienteVO.getCodigo(), vendaDTO, "ERRO_INCLUIR_VENDA_AVULSA", ex.getMessage());
            this.logDAO.incluirLogErroInclusaoContrato(clienteVO.getCodigo(), usuarioVO, ex.getMessage());
            throw ex;
        } finally {
            service = null;
        }
    }

    private List<MovParcelaVO> consultarParcelasParaCobrar(Integer contrato, Integer vendaAvulsa,
                                                           Integer diaria, Boolean cobrarPrimeiraParcela,
                                                           boolean consultarTodasParcelasGeradasEmAberto) throws Exception {

        List<MovParcelaVO> movParcelas = new ArrayList<>();

        //contrato
        if (!UteisValidacao.emptyNumber(contrato)) {
            List<MovParcelaVO> listaParcelasContrato = new ArrayList<>();
            if (consultarTodasParcelasGeradasEmAberto) {
                listaParcelasContrato = this.movParcelaDAO.consultarEmAbertoPorContrato(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else {
                listaParcelasContrato = this.movParcelaDAO.consultarPrimeirasParcelasContrato(contrato, cobrarPrimeiraParcela);
            }
            movParcelas.addAll(listaParcelasContrato);
        }

        if (cobrarPrimeiraParcela) {
            obterPrimeiraParcelaFutura(contrato, movParcelas);
        }

        //venda avulsa
        if (!UteisValidacao.emptyNumber(vendaAvulsa)) {
            List<MovParcelaVO> listaParcelasVendaAvulsa = this.movParcelaDAO.consultarPorCodigoVendaAvulsaLista(vendaAvulsa, null, null, "EA", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            movParcelas.addAll(listaParcelasVendaAvulsa);
        }

        //diaria
        if (!UteisValidacao.emptyNumber(diaria)) {
            List<MovParcelaVO> listaParcelasVendaAvulsa = this.movParcelaDAO.consultarPorCodigoAulaAvulsaDiariaLista(diaria, "EA",
                    false,
                    null, null,
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            movParcelas.addAll(listaParcelasVendaAvulsa);
        }

        List<MovParcelaVO> movParcelasCobrar = new ArrayList<>();
        for (MovParcelaVO obj : movParcelas) {
            if (UteisValidacao.emptyNumber(obj.getValorParcela()) || !obj.getSituacao().equalsIgnoreCase("EA")) {
                Uteis.logarDebug("Parcela ignorar vendas online | Parcela " + obj.getCodigo());
                continue;
            }
            movParcelasCobrar.add(obj);
        }
        return movParcelasCobrar;
    }

    private void obterPrimeiraParcelaFutura(Integer contrato, List<MovParcelaVO> movParcelas) throws Exception {
        boolean parcela1EstaNaLista = false;
        for (MovParcelaVO item : movParcelas) {
            if (item.getDescricao().equals("PARCELA 1")) {
                parcela1EstaNaLista = true;
            }
        }
        if (!parcela1EstaNaLista) {
            List<MovParcelaVO> listaParcelasContrato = new ArrayList<>();
            listaParcelasContrato = this.movParcelaDAO.consultarPrimeirasParcelasContrato(contrato, 1);
            if (!UteisValidacao.emptyList(listaParcelasContrato) && listaParcelasContrato.get(0).getDescricao().equals("PARCELA 1")) {
                movParcelas.addAll(listaParcelasContrato);
            }
        }
    }

    private TransacaoVO cobrarParcelasAgrupando(VendaDTO vendaDTO, List<MovParcelaVO> listaParcelas, Integer nrVezesDividir, ClienteVO clientePagadorVO,
                                                UsuarioVO usuarioVO, Integer convenio, Integer empresa, String tipoParcelamentoStoneString,
                                                OrigemCobrancaEnum origemCobrancaEnum, Double desconto, List<RetornoVendaTO> retornoVendaTOList) throws Exception {


        InstalmentTypeInstlmtTp tipoParcelamentoStone = null;
        if (!UteisValidacao.emptyString(tipoParcelamentoStoneString)) {
            tipoParcelamentoStone = InstalmentTypeInstlmtTp.fromValue(tipoParcelamentoStoneString);
        }

        //primeira tentativa de cobrança
        TransacaoVO transacaoVO = cobrarParcelas(vendaDTO, listaParcelas, nrVezesDividir, clientePagadorVO, usuarioVO,
                convenio, empresa, false, tipoParcelamentoStone, origemCobrancaEnum, desconto, retornoVendaTOList);

        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) ||
                transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) {

            List<VendasOnlineConvenioTentativaVO> lista = this.vendasOnlineConvenioTentativaDAO.consultarPorEmpresa(empresa, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lista.size() > 1) {
                for (VendasOnlineConvenioTentativaVO tentativaVO : lista) {

                    //ignorar o convenio que já foi realizado uma tentativa
                    if (tentativaVO.getConvenioCobrancaVO().getCodigo().equals(convenio)) {
                        continue;
                    }

                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) ||
                            transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) {
                        tipoParcelamentoStone = null;
                        if (!UteisValidacao.emptyString(tentativaVO.getTipoParcelamentoStone())) {
                            tipoParcelamentoStone = InstalmentTypeInstlmtTp.fromValue(tentativaVO.getTipoParcelamentoStone());
                        }
                        transacaoVO = cobrarParcelas(vendaDTO, listaParcelas, nrVezesDividir, clientePagadorVO, usuarioVO,
                                tentativaVO.getConvenioCobrancaVO().getCodigo(), empresa, true, tipoParcelamentoStone, origemCobrancaEnum, desconto, retornoVendaTOList);
                    } else {
                        return transacaoVO;
                    }
                }
            }
        }
        return transacaoVO;
    }

    public TransacaoVO cobrarParcelas(VendaDTO vendaDTO, List<MovParcelaVO> listaParcelas, Integer nrVezesDividir, ClienteVO clientePagadorVO,
                                      UsuarioVO usuarioVO, Integer convenio, Integer empresa, boolean forcarValidarAutorizacaoCobranca,
                                      InstalmentTypeInstlmtTp tipoParcelamentoStone, OrigemCobrancaEnum origemCobrancaEnum, Double desconto, List<RetornoVendaTO> retornoVendaTOList) throws Exception {
        PagamentoService pagamentoService = null;
        try {
            ConvenioCobrancaVO convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenio, empresa, Uteis.NIVELMONTARDADOS_TODOS);
            if (!convenioCobrancaVO.getTipo().isTransacaoOnline()) {
                throw new Exception("Convênio não é de Transação Online");
            }

            TransacaoVO transacaoVO = new TransacaoVO();

            //cartão de teste
            if (verificarCartaoNaoCobrar(vendaDTO)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.APROVADA);
                return transacaoVO;
            }

            String ipCliente = obterIpCliente(vendaDTO);

            pagamentoService = new PagamentoService(this.con, convenioCobrancaVO);
            pagamentoService.setForcarValidarAutorizacaoCobranca(forcarValidarAutorizacaoCobranca);
            transacaoVO = pagamentoService.realizarCobrancaAgrupadoVendasOnline(listaParcelas, clientePagadorVO.getPessoa(), usuarioVO,
                    nrVezesDividir, tipoParcelamentoStone, vendaDTO.getCvv(),
                    origemCobrancaEnum, desconto, retornoVendaTOList, ipCliente);
            if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                throw new Exception("Erro ao processar pagamento.");
            }

            //teste automatizado
            if (verificarTesteAutomatizadoAceitar(vendaDTO)) {
                Integer reciboDaTransacao = GerarReciboTransacao.gerarReciboDaTransacao(this.con, transacaoVO.getCodigo(), false);
                incluirLog(clientePagadorVO.getCodigo(), usuarioVO, "RECIBO GERADO ATRAVES DO TESTE AUTOMATIZADO DO VENDAS ONLINE: " + reciboDaTransacao,
                        "RECIBO_TESTE_VENDAS");
                transacaoVO.setSituacao(SituacaoTransacaoEnum.APROVADA);
            }

            gravarPactoPayComunicacao(vendaDTO, transacaoVO, null, null, null);

            return transacaoVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirLogVendasOnline(clientePagadorVO.getCodigo(), null, "ERRO_REALIZAR_COBRANCA_AGRUPADA", ex.getMessage());
            throw ex;
        } finally {
            pagamentoService = null;
        }
    }

    private String obterIpCliente(VendaDTO vendaDTO) {
        try {
            if (UteisValidacao.emptyString(vendaDTO.getNowLocationIp())) {
                return "";
            }
            NowLocationIpVendaDTO dto = JSONMapper.getObject(new JSONObject(vendaDTO.getNowLocationIp()), NowLocationIpVendaDTO.class);
            return dto.getIp();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private boolean verificarTesteAutomatizadoAceitar(VendaDTO vendaDTO) throws Exception {
        return vendaDTO != null &&
                !UteisValidacao.emptyString(vendaDTO.getCpf()) &&
                vendaDTO.getCpf().equals(PropsService.getPropertyValue(PropsService.cpftestevendasonline)) &&
                Uteis.encriptar(vendaDTO.getNumeroCartao()).equals("35b9f15749946a94573b3124fe3dd88d7a694d1964a82918f041203ef76b524b");
    }

    private Boolean verificarCartaoNaoCobrar(VendaDTO vendaDTO) {
        return vendaDTO != null &&
                vendaDTO.getNomeCartao() != null &&
                vendaDTO.getNomeCartao().equals("CARTAO A B PACTO TESTE");
    }

    private ConvenioPagamentoVendasOnlineDTO obterConvenioConfigPlanoProduto(VendaDTO vendaDTO, Integer codigoCliente) {
        try {
            Map<Integer, ConvenioPagamentoVendasOnlineDTO> listaConveniosEncontrados = new HashMap<>();
            int i = 1;

            if (!UteisValidacao.emptyNumber(vendaDTO.getConvenioCobranca())) {
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select \n" +
                        "c.codigo as convenio, \n" +
                        "c.tipoParcelamentoStone \n" +
                        "from conveniocobranca c \n" +
                        "where c.codigo = " + vendaDTO.getConvenioCobranca(), con);
                if (rs.next()) {
                    ConvenioPagamentoVendasOnlineDTO convenioPagamentoVendasOnlineDTO = new ConvenioPagamentoVendasOnlineDTO();
                    convenioPagamentoVendasOnlineDTO.setConvenioCobranca(rs.getInt("convenio"));
                    convenioPagamentoVendasOnlineDTO.setTipoParcelamentoStone(rs.getString("tipoParcelamentoStone"));
                    return convenioPagamentoVendasOnlineDTO;
                }
            }

            if (!UteisValidacao.emptyNumber(vendaDTO.getPlano())) {
                StringBuilder sql = new StringBuilder();
                sql.append("select \n");
                sql.append("c.codigo as convenio, \n");
                sql.append("cp.tipoParcelamentoStone \n");
                sql.append("from conveniocobranca c \n");
                sql.append("inner join vendasonlineconvenio cp on cp.conveniocobranca = c.codigo \n");
                sql.append("inner join vendasonlineconfig cg on cg.empresa = cp.empresa \n");
                sql.append("where cg.usarConvenioPlanoProduto \n");
                sql.append("and c.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
                sql.append("and cp.empresa = ").append(vendaDTO.getUnidade()).append(" \n");
                sql.append("and cp.plano = ").append(vendaDTO.getPlano()).append(" \n");

                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rs.next()) {
                    ConvenioPagamentoVendasOnlineDTO convenioPagamentoVendasOnlineDTO = new ConvenioPagamentoVendasOnlineDTO();
                    convenioPagamentoVendasOnlineDTO.setConvenioCobranca(rs.getInt("convenio"));
                    convenioPagamentoVendasOnlineDTO.setTipoParcelamentoStone(rs.getString("tipoParcelamentoStone"));
                    listaConveniosEncontrados.put(i++, convenioPagamentoVendasOnlineDTO);
                }
            }

            if (!UteisValidacao.emptyList(vendaDTO.getProdutos())) {
                for (VendaProdutoDTO vendaProdutoDTO : vendaDTO.getProdutos()) {
                    if (UteisValidacao.emptyNumber(vendaProdutoDTO.getProduto())) {
                        continue;
                    }

                    StringBuilder sql = new StringBuilder();
                    sql.append("select \n");
                    sql.append("c.codigo as convenio, \n");
                    sql.append("cp.tipoParcelamentoStone \n");
                    sql.append("from conveniocobranca c \n");
                    sql.append("inner join vendasonlineconvenio cp on cp.conveniocobranca = c.codigo \n");
                    sql.append("inner join vendasonlineconfig cg on cg.empresa = cp.empresa \n");
                    sql.append("where cg.usarConvenioPlanoProduto \n");
                    sql.append("and c.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
                    sql.append("and cp.empresa = ").append(vendaDTO.getUnidade()).append(" \n");
                    sql.append("and cp.produto = ").append(vendaProdutoDTO.getProduto()).append(" \n");

                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                    if (rs.next()) {
                        ConvenioPagamentoVendasOnlineDTO convenioPagamentoVendasOnlineDTO = new ConvenioPagamentoVendasOnlineDTO();
                        convenioPagamentoVendasOnlineDTO.setConvenioCobranca(rs.getInt("convenio"));
                        convenioPagamentoVendasOnlineDTO.setTipoParcelamentoStone(rs.getString("tipoParcelamentoStone"));
                        listaConveniosEncontrados.put(i++, convenioPagamentoVendasOnlineDTO);
                    }
                }
            }

            if (listaConveniosEncontrados.size() == 1) {
                return listaConveniosEncontrados.get(1);
            } else if (listaConveniosEncontrados.size() > 1) {
                //se encontrou mais de 1 lançar exceção para registrar no log e usar o padrão.
                throw new Exception("Encontrado mais de um convênio com configuração de plano produto.");
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "OBTER_CONFIG_CONVENIO_COBRANCA_PLANO_PRODUTO", ex.getMessage());
            return null;
        }
    }

    private void processosPosFalha(String key, TransacaoVO transacaoVO, EmpresaVO empresaVO,
                                   List<RetornoVendaTO> retornoVendaTOList, UsuarioVO usuarioVO, ClienteVO clienteVO, String motivoFalha) {
        for (RetornoVendaTO retDTO : retornoVendaTOList) {
            try {
                estornarPontosDoContratoNoClubeVantagens(retornoVendaTOList, "Pontos do Clube de Vantagens Estornados devido a falha no processo de Venda do Vendas Online");

                estornarDevidoErro(key, retornoVendaTOList, null, "RETORNO DE TRANSAÇÃO: ".concat(msgRetornoTransacao(transacaoVO)));

                if (transacaoVO != null) {
                    cancelarTransacaoDevidoErro(key, transacaoVO, null);
                }

                excluirAutorizacaoCobrancaAlunoNovo(clienteVO, usuarioVO, motivoFalha);

                enviarEmailAviso(retDTO.getVendaDTO(), retDTO.getContratoVO(), retDTO.getVendaAvulsaVO(),
                        retDTO.getClienteVO(),
                        retDTO.getDiariaVO(),
                        empresaVO, false, msgRetornoTransacao(transacaoVO));

            } catch (Exception e) {
                //se der erro nessa parte, o contrato já foi lancado, a cobrança feita com sucesso, não existe a necessidade de
                //estornos
                incluirLog(retDTO.getCodigoCliente(), usuarioVO, "ERRO EM OPERAÇÃO PÓS VENDA - " + e.getMessage(), "VENDAS_ON_LINE");
            }
        }
    }

    private void excluirAutorizacaoCobrancaAlunoNovo(ClienteVO clienteVO, UsuarioVO usuarioVO, String msgErro) throws Exception {
        try {
            if (Calendario.igual(clienteVO.getPessoa().getDataCadastro(), Calendario.hoje()) && !UteisValidacao.emptyString(msgErro) &&
                    !msgErro.contains("Cliente já possui um plano ativo!")) {
                List<AutorizacaoCobrancaClienteVO> autorizacaoCobrancaClienteVOS = this.autorizacaoCobrancaClienteDAO.consultarPorClienteTipoAutorizacao(clienteVO.getCodigo(),
                        TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_MINIMOS);
                for (AutorizacaoCobrancaClienteVO autorizacaoVO : autorizacaoCobrancaClienteVOS) {
                    if (autorizacaoVO.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) && autorizacaoVO.getTipoACobrar().equals(TipoObjetosCobrarEnum.TUDO)) {
                        String msgLogExclusao = "Exclusão de autorização de cobrança devido erro: " + msgErro;
                        msgLogExclusao += " | Origem: " + OrigemCobrancaEnum.VENDAS_ONLINE_VENDA.getDescricao();
                        autorizacaoCobrancaClienteDAO.alterarSituacaoAutorizacaoCobranca(false, autorizacaoVO, msgLogExclusao);
                    }
                }
            }
        } catch (Exception ex) {
            incluirLog(clienteVO.getCodigo(), usuarioVO, "ERRO EM OPERAÇÃO PÓS VENDA (excluirAutorizacaoCobrancaAlunoNovo) - " + ex.getMessage(), "VENDAS_ON_LINE");
        }
    }

    private void processosPosSucesso(String key, TransacaoVO transacaoVO, EmpresaVO empresaVO,
                                     List<RetornoVendaTO> retornoVendaTOList, UsuarioVO usuarioVO) {

        for (RetornoVendaTO retDTO : retornoVendaTOList) {
            notificarWebhook(retDTO);
            try {
                try {
                    String uuid = UUID.randomUUID().toString();
                    String senhaRandomica = uuid.substring(0, uuid.indexOf("-"));

                    //variável booleana para definir se o usuário irá receber o email da criação do usuário móvel.
                    // Independente do email, o usuário móvel sempre será criado normalmente
                    boolean isEnviarEmailUsuarioMovelAutomaticamente = this.vendasConfigDAO.config(empresaVO.getCodigo()).isEnviarEmailUsuarioMovelAutomaticamente();

                    String retornoEnvioEmail = this.integracaoCadastrosDAO.incluirUsuarioMovel(key,
                            empresaVO, retDTO.getClienteVO(), retDTO.getVendaDTO().getEmail(), senhaRandomica,
                            retDTO.getVendaDTO().getNome(), null, 0, OrigemSistemaEnum.VENDAS_ONLINE_2, isEnviarEmailUsuarioMovelAutomaticamente);
                    if (retornoEnvioEmail.toUpperCase().contains("EMAIL DE ACESSO ENVIADO")) {
                        HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
                        historicoContatoVO.setDia(Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                        historicoContatoVO.setObservacao("E-mail de 1ª compra - Vendas Online");
                        historicoContatoVO.setResponsavelCadastro(usuarioVO);
                        historicoContatoVO.setResultado("Envio E-mail");
                        historicoContatoVO.setContatoAvulso(false);
                        historicoContatoVO.setTipoContato("EM");
                        historicoContatoVO.setClienteVO(retDTO.getClienteVO());
                        HistoricoContato historicoContato = new HistoricoContato(con);
                        historicoContato.incluirSemCommit(historicoContatoVO);
                    }
                } catch (Exception e) {
                    incluirLog(retDTO.getCodigoCliente(), usuarioVO, "ERRO AO ADICIONAR USUARIO MOVEL - " + e.getMessage(), "VENDAS_ON_LINE");
                    incluirLogVendasOnline(retDTO.getCodigoCliente(), retDTO.getVendaDTO(), "ERRO_INCLUIR_USUARIO_MOVEL", e.getMessage());
                }

                enviarEmailAviso(retDTO.getVendaDTO(), retDTO.getContratoVO(), retDTO.getVendaAvulsaVO(), retDTO.getClienteVO(),
                        retDTO.getDiariaVO(),
                        empresaVO, true, msgRetornoTransacao(transacaoVO));

            } catch (Exception e) {
                //se der erro nessa parte, o contrato já foi lancado, a cobrança feita com sucesso, não existe a necessidade de
                //estornos
                incluirLog(retDTO.getCodigoCliente(), usuarioVO, "ERRO EM OPERAÇÃO PÓS VENDA - " + e.getMessage(), "VENDAS_ON_LINE");
            }
        }
    }

    private String msgRetornoTransacao(TransacaoVO transacaoVO) {
        if (transacaoVO == null) {
            return "99 - Desconhecido";
        }
        return transacaoVO.getCodigoRetornoGestaoTransacao().concat(" - ").concat(transacaoVO.getCodigoRetornoGestaoTransacaoMotivo());
    }

    public void estornarDevidoErro(String key, List<RetornoVendaTO> retornoVendaTOList, UsuarioVO usuarioVO, String msg) throws Exception {
        if (usuarioVO == null) {
            UsuarioVO usuarioRecorrencia = usuarioDAO.getUsuarioRecorrencia();
            usuarioVO = usuarioRecorrencia != null && !UteisValidacao.emptyNumber(usuarioRecorrencia.getCodigo()) ?
                    usuarioRecorrencia :
                    obterUsuarioVendasOnline(null); //enviar como null para pegar o usuario admin
        }
        for (RetornoVendaTO retDTO : retornoVendaTOList) {
            try {
                VendaDTO vendaDTO = retDTO.getVendaDTO();

                //contrato
                if (!UteisValidacao.emptyNumber(vendaDTO.getPlano())) {
                    estornarContrato(vendaDTO, key, retDTO.getContrato(), retDTO.getCodigoCliente(), usuarioVO, msg);
                }

                //venda avulsa
                if (!UteisValidacao.emptyList(vendaDTO.getProdutos())) {
                    estornarVendaAvulsa(vendaDTO, retDTO.getVendaAvulsa(), retDTO.getCodigoCliente(), usuarioVO, msg);
                }

                //diaria
                if (retDTO.getDiariaVO() != null && !UteisValidacao.emptyNumber(retDTO.getDiariaVO().getCodigo())) {
                    estornarDiaria(vendaDTO, retDTO.getDiariaVO().getCodigo(), retDTO.getCodigoCliente(), usuarioVO, msg);
                }
            } finally {
                incluirLogVendasOnline(retDTO.getCodigoCliente(), retDTO.getVendaDTO(), "ESTORNAR_DEVIDO_ERRO", msg);
            }
        }
    }

    public void estornarPontosDoContratoNoClubeVantagens(List<RetornoVendaTO> retornoVendaTOList, String msg) throws Exception {
        for (RetornoVendaTO retDTO : retornoVendaTOList) {
            HistoricoPontos historicoPontosDao;
            try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
                VendaDTO vendaDTO = retDTO.getVendaDTO();
                if (!UteisValidacao.emptyNumber(vendaDTO.getPlano()) && !UteisValidacao.emptyNumber(retDTO.getContrato())) {
                    historicoPontosDao = new HistoricoPontos(connection);
                    historicoPontosDao.excluirPorContrato(retDTO.getContrato());
                }
            } catch (Exception e) {
                incluirLogVendasOnline(retDTO.getCodigoCliente(), retDTO.getVendaDTO(), "ESTORNAR_DEVIDO_ERRO", msg);
            } finally {
                historicoPontosDao = null;
            }
        }
    }

    public void cancelarTransacaoDevidoErro(String key, TransacaoVO transacaoVO, UsuarioVO usuarioVO) throws Exception {
        try {
            if (usuarioVO == null) {
                usuarioVO = obterUsuarioVendasOnline(null); //enviar como null para pegar o usuario admin
            }
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) || transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                this.transacaoDAO.cancelarTransacao(transacaoVO, false, usuarioVO, key);
            }
        } catch (Exception ex) {
            incluirLog(transacaoVO.getClienteVO().getCodigo(), usuarioVO, "ERRO EM OPERAÇÃO PÓS VENDA (cancelarTransacaoDevidoErro)- " + ex.getMessage(), "VENDAS_ON_LINE");
        }
    }

    public ClienteVO getCopiaClienteVO() {
        return copiaClienteVO;
    }

    public void setCopiaClienteVO(ClienteVO copiaClienteVO) {
        this.copiaClienteVO = copiaClienteVO;
    }

    private boolean verificaCPF(String cpf) {
        try {
            cpf = Uteis.removerMascara(cpf);
            if (Uteis.digitosIguais(cpf)) {
                return false;
            }
            if (cpf.equals("")) {
                return false;
            }
            // WM -> precisamos dos testes
        /*if ((cpf.equals("00000000000")) || (cpf.equals("11111111111")) || (cpf.equals("22222222222")) || (cpf.equals("33333333333")) || (cpf.equals("44444444444")) || (cpf.equals("55555555555")) || (cpf.equals("66666666666")) || (cpf.equals("77777777777")) || (cpf.equals("88888888888")) || (cpf.equals("99999999999"))) {
         return false;
         }*/

            int count, Soma, x, y, CPF[] = new int[11];

            if ((cpf.length() != 11) && (cpf.length() != 0)) {
                return false;
            }

            Soma = 0;
            for (count = 0; count < 11; count++) {
                CPF[count] = 0;
            }

            char vetorChar[] = new char[11];
            String temp, CPFvalido = "";

            for (count = 0; count < 11; count++) {
                // Transformar String em vetor de caracteres
                vetorChar = cpf.toCharArray();
                // Transformar cada caractere em String
                temp = String.valueOf(vetorChar[count]);
                // Transformar String em inteiro e jogar no vetor
                CPF[count] = Integer.parseInt(temp);
            }
            // Método da árvore para obter o x
            for (count = 0; count < 9; count++) {
                // Pegar soma da permutação dos dígitos do CPF
                Soma += CPF[count] * (10 - count);
            }

            // se o resto da divisão der 0 ou 1, x terá dois dígitos
            if (Soma % 11 < 2) {
                x = 0;
            } // x não pode ter dois dígitos
            else {
                x = 11 - (Soma % 11);
            } // obtendo o penúltimo dígito do CPF

            CPF[9] = x;

            // Método da árvore para obter o y
            Soma = 0;
            for (count = 0; count < 10; count++) {
                // Pegar soma da permutação dos dígitos do CPF
                Soma += CPF[count] * (11 - count);
            }

            // se o resto da divisão der 0 ou 1, y terá dois dígitos
            if (Soma % 11 < 2) {
                y = 0;
            } // y não pode ter dois dígitos
            else {
                y = 11 - (Soma % 11);
            } // obtendo o último dígito do CPF

            CPF[10] = y;
            Soma = 0;

            // Verificando se o cpf informado é válido para retornar resultado ao
            // programa
            for (count = 0; count < 11; count++) {
                CPFvalido += String.valueOf(CPF[count]);
            }
            if (cpf.compareTo(CPFvalido) == 0) {
                return true;
            } else {
                return false;
            }
            // return true;
        } catch (Exception ex) {
            return false;
        }
    }

    private void enviarEmailSucessoPix(Integer codigoPix, JSONObject jsonRetorno) {
        try {
            PixVO pixVO = this.pixDAO.consultarPorCodigo(codigoPix, false);
            EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            enviarEmailSucessoPagamento(pixVO.getPessoa(), jsonRetorno, null, pixVO, empresaVO, false);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void enviarEmailSucessoPagamento(Integer pessoa, JSONObject jsonRetorno, TransacaoVO transacaoVO, PixVO pixVO, EmpresaVO empresaVO, boolean isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca) {
        boolean emailEnviado = false;
        String emailMsg = "";
        ConfiguracaoSistemaCRM crmDAO;
        try {
            if (UteisValidacao.emptyNumber(pessoa)) {
                throw new Exception("Pessoa não informada");
            }
            if (isLinkDePagamentoOuLinkCadastroDeCartaoDaReguaDeCobranca) {
                if (!empresaVO.isEnviarEmailPagamentoRegua()) {
                    throw new Exception("Config EnviarEmailPagamentoRegua não habilitada");
                }
            } else if (!empresaVO.isEnviarEmailPagamento()) {
                throw new Exception("Config EnviarEmailPagamento não habilitada");
            }
            if (transacaoVO != null &&
                    (UteisValidacao.emptyNumber(transacaoVO.getValor()) || !transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO))) {
                throw new Exception("Transação não CONCLUIDA_COM_SUCESSO");
            }
            if (pixVO != null &&
                    (UteisValidacao.emptyNumber(pixVO.getValor()) || !pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA))) {
                throw new Exception("PIX não CONCLUIDA");
            }

            //enviar email
            crmDAO = new ConfiguracaoSistemaCRM(con);
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = crmDAO.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!configuracaoSistemaCRMVO.isConfiguracaoEmailValida()) {
                throw new Exception("ConfiguracaoSistemaCRMVO não é válida");
            }

            EmailPagamentoTO emailPagamentoTO = vendasConfigDAO.obterDados(transacaoVO != null ? transacaoVO.getCodigo() : null, pixVO != null ? pixVO.getCodigo() : null);
            if (emailPagamentoTO == null) {
                throw new Exception("Não foi possível obter dados para envio do email");
            }

            if (UteisValidacao.emptyString(emailPagamentoTO.getEmail())) {
                throw new Exception("Pessoa sem email para correspondência");
            }

            UteisEmail uteis = new UteisEmail();
            uteis.novo("Pagamento aprovado! \\o/", configuracaoSistemaCRMVO);
            uteis.enviarEmail(emailPagamentoTO.getEmail(), "", vendasConfigDAO.gerarCorpoEmailPagamento(emailPagamentoTO).toString(), "", configuracaoSistemaCRMVO.getIntegracaoPacto(), configuracaoSistemaCRMVO.preparaEnvioSendy());

            emailEnviado = true;
            emailMsg = "Email enviado com sucesso para: " + emailPagamentoTO.getEmail();
        } catch (Exception ex) {
            emailMsg = ex.getMessage();
        } finally {
            crmDAO = null;
            jsonRetorno.put("emailEnviado", emailEnviado);
            jsonRetorno.put("emailMsg", emailMsg);
        }
    }

    private ContratoVO obterContratoASerRenovadoVendasOnline(ClienteVO clienteVO, boolean pactoStore) throws Exception {
        ContratoVO contratoASerRenovado = null;

        if (!pactoStore && obterConfigRenovarContratoAntigo(clienteVO.getEmpresa().getCodigo())) {
            //obter contrato que pode ser renovado
            List<ContratoVO> listaContratos = contratoDAO.consultarContratosRenovar(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

            if (listaContratos != null && !listaContratos.isEmpty()) {
                contratoASerRenovado = listaContratos.get(0);
            }
        }

        return contratoASerRenovado;
    }

    private ContratoVO obterContratoASerRematriculadoVendasOnline(ClienteVO clienteVO, boolean pactoStore) {
        if (!pactoStore) {
            try {
                List<ContratoVO> listaContratos = contratoDAO.consultarContratosRematricular(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL);
                return UteisValidacao.emptyList(listaContratos) ? null : listaContratos.get(0);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public Integer obterConvenioCartaoVendaContrato(Integer empresa, boolean linkPagamento) {
        VendasOnlineConvenioTentativa vendasOnlineConvenioTentativaDAO;
        try {
            vendasOnlineConvenioTentativaDAO = new VendasOnlineConvenioTentativa(this.con);

            Integer codConvenio = 0;
            List<VendasOnlineConvenioTentativaVO> lista = vendasOnlineConvenioTentativaDAO.consultarPorEmpresa(empresa, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyList(lista)) {
                codConvenio = lista.get(0).getConvenioCobrancaVO().getCodigo();
            }

            if (UteisValidacao.emptyNumber(codConvenio) && linkPagamento) {
                codConvenio = this.empresaDAO.obterConvenioCobrancaCartao(empresa);
            }
            return codConvenio;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            vendasOnlineConvenioTentativaDAO = null;
        }
    }

    public String obterLinkPagamentoVendasOnlineSms(String key, ClienteVO clienteVO, int codEmpresa,
                                                    boolean cobrarParcelasEmAberto, OrigemCobrancaEnum origemCobrancaEnum,
                                                    PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO, UsuarioVO usuarioVO) throws Exception {
        try {
            TokenVendasOnlineVO tokenVendasOnlineVO = new TokenVendasOnlineVO();
            tokenVendasOnlineVO.setToken(UUID.randomUUID().toString().substring(0, 10) + key.substring(0, 5));
            tokenVendasOnlineVO.setDataRegistro(Calendario.hoje());
            tokenVendasOnlineVO.setDados(montarValorDados(codEmpresa, usuarioVO, clienteVO, cobrarParcelasEmAberto, cobrancaAntecipadaVO,
                    origemCobrancaEnum, key, null, null, null, 1));
            TokenVendasOnlineVO token = null;
            do {
                token = this.tokenVendasOnlineDAO.consultarPorTokenShort(tokenVendasOnlineVO.getToken());
                tokenVendasOnlineVO.setToken(UUID.randomUUID().toString().substring(0, 10) + key.substring(0, 5));
            } while (token != null);

            this.tokenVendasOnlineDAO.incluir(tokenVendasOnlineVO);

            //Estrutura do Link: URL_VENDAS_ONLINE + /p/chave/token
            return PropsService.getPropertyValue(PropsService.urlVendasOnline) + "/p/" + key + "/" + tokenVendasOnlineVO.getToken();

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String obterLinkPagamentoVendasOnline(String key, ClienteVO clienteVO, int codEmpresa,
                                                 boolean cobrarParcelasEmAberto, OrigemCobrancaEnum origemCobrancaEnum,
                                                 PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO, UsuarioVO usuarioVO,
                                                 PactoPayComunicacaoVO comunicacaoVO, Boolean todasEmAberto,
                                                 String parcelasSelecionadas, int numeroVezesParcelamentoOperadora) throws Exception {
        try {
            TokenVendasOnlineVO tokenVendasOnlineVO = new TokenVendasOnlineVO();
            tokenVendasOnlineVO.setToken(UUID.randomUUID().toString());
            tokenVendasOnlineVO.setDataRegistro(Calendario.hoje());
            tokenVendasOnlineVO.setDados(montarValorDados(codEmpresa, usuarioVO, clienteVO, cobrarParcelasEmAberto, cobrancaAntecipadaVO, origemCobrancaEnum,
                    key, comunicacaoVO, todasEmAberto, parcelasSelecionadas, numeroVezesParcelamentoOperadora));

            this.tokenVendasOnlineDAO.incluir(tokenVendasOnlineVO);

            //Estrutura do Link: URL_VENDAS_ONLINE + /pagamento/chave/token
            return PropsService.getPropertyValue(PropsService.urlVendasOnline) + "/pagamento/" + key + "/" + tokenVendasOnlineVO.getToken();

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String montarValorDados(int codEmpresa, UsuarioVO usuarioVO, ClienteVO clienteVO,
                                   boolean cobrarParcelasEmAberto,
                                   PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO,
                                   OrigemCobrancaEnum origemCobrancaEnum, String key,
                                   PactoPayComunicacaoVO comunicacaoVO, Boolean todasEmAberto, String parcelasSelecionadas,
                                   int numeroVezesParcelamentoOperadora) throws Exception {
        DadosTokenDTO dadosTokenDTO = new DadosTokenDTO();
        //Chave da empresa (key)
        if (!UteisValidacao.emptyString(key)) {
            dadosTokenDTO.setChave(key);
        }
        //Código da empresa
        if (!UteisValidacao.emptyNumber(codEmpresa)) {
            dadosTokenDTO.setCodEmpresa(codEmpresa);
        }

        //Responsável pelo link gerado
        if (usuarioVO != null && !UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
            dadosTokenDTO.setResponsavel(usuarioVO.getCodigo());
        }
        //matrícula do cliente
        if (!UteisValidacao.emptyString(clienteVO.getMatricula())) {
            dadosTokenDTO.setCliente(clienteVO.getMatricula());
        }
        //Parâmetro que define se é link de pagamento ou de cadastro de cartão
        dadosTokenDTO.setCobrarParcelasEmAberto(cobrarParcelasEmAberto);

        //Usado na régua de cobrança
        if (cobrancaAntecipadaVO != null && !UteisValidacao.emptyNumber(cobrancaAntecipadaVO.getCodigo())) {
            dadosTokenDTO.setCodCobrancaAntecipada(cobrancaAntecipadaVO.getCodigo());
        }
        //Por onde a cobrança será realizada
        if (origemCobrancaEnum != null) {
            dadosTokenDTO.setOrigemCobranca(origemCobrancaEnum.getCodigo());
        }
        //Usado na régua de cobrança
        if (comunicacaoVO != null && !UteisValidacao.emptyNumber(comunicacaoVO.getCodigo())) {
            dadosTokenDTO.setCodPactoPayComunicacao(comunicacaoVO.getCodigo());
        }
        //Usado no link de cobrança
        if (todasEmAberto != null) {
            dadosTokenDTO.setTodasEmAberto(todasEmAberto);
        }
        //Usado no link de cobrança
        if (!UteisValidacao.emptyString(parcelasSelecionadas)) {
            dadosTokenDTO.setParcelasSelecionadas(parcelasSelecionadas);
        }
        //Usado no link de cobrança
        if (!UteisValidacao.emptyNumber(numeroVezesParcelamentoOperadora)) {
            dadosTokenDTO.setNumeroVezesParcelamentoOperadora(numeroVezesParcelamentoOperadora);
        }
        return new JSONObject(dadosTokenDTO).toString();
    }

    public JSONObject cobrarParcelasAbertasBoleto(String key, String matricula, VendaDTO vendaDTO) {
        Integer codigoCliente = 0;
        JSONObject jsonRetorno = new JSONObject();
        try {
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo, pessoa, empresa from cliente where codigomatricula = " + Integer.valueOf(matricula), this.con);
            if (rs.next()) {
                EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ClienteVO clienteVO = this.clienteDAO.consultarPorCodigo(rs.getInt("codigo"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                codigoCliente = clienteVO.getCodigo();
                UsuarioVO usuarioVO = obterUsuarioVendasOnline(vendaDTO);

                OrigemCobrancaEnum origemCobrancaEnum = OrigemCobrancaEnum.obterPorCodigo(vendaDTO.getOrigemCobranca());
                if (origemCobrancaEnum == null || origemCobrancaEnum.equals(OrigemCobrancaEnum.NENHUM)) {
                    origemCobrancaEnum = OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO;
                }

                Double descontoValorFixo = 0.0;
                Double descontoPercentual = 0.0;
                Integer convenioCobranca = null;

                boolean isLinkDePagamentoDaReguaDeCobranca = vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getPactoPayComunicacao());
                if (isLinkDePagamentoDaReguaDeCobranca) {
                    convenioCobranca = this.empresaDAO.obterConvenioCobrancaBoletoRegua(empresaVO.getCodigo());
                } else {
                    convenioCobranca = this.empresaDAO.obterConvenioCobrancaBoleto(empresaVO.getCodigo());
                }
                if (UteisValidacao.emptyNumber(convenioCobranca)) {
                    throw new Exception("Convênio de cobrança não selecionado nas configurações da empresa (Link de pagamento)!");
                }
                List<MovParcelaVO> movParcelasEmAberto;
                Date dataVencimentoBoleto = Calendario.somarDias(Calendario.hoje(), empresaVO.getQtdDiasVencimentoBoleto());

                if (!UteisValidacao.emptyNumber(vendaDTO.getCobrancaAntecipada())) {
                    PactoPayCobrancaAntecipadaDTO antecipadaDTO = obterValidarCobrancaAntecipadaDTO(vendaDTO.getCobrancaAntecipada());
                    PactoPayConfigVO pactoPayConfigVO = this.pactoPayConfigDAO.consultarPorEmpresa(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    if (antecipadaDTO.isAplicarDesconto()) {
                        if (pactoPayConfigVO.isCobrancaAntecipadaValorFixo()) {
                            descontoValorFixo = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                        } else {
                            descontoPercentual = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                        }
                    }

                    dataVencimentoBoleto = antecipadaDTO.getDataLimitePagamento_Date();
                    movParcelasEmAberto = this.movParcelaDAO.consultarPorCodigos(antecipadaDTO.getParcelas(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    if (UteisValidacao.emptyList(movParcelasEmAberto)) {
                        throw new Exception("Nenhuma parcela encontrada para pagamento antecipado");
                    }
                } else if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) && vendaDTO.getTodasEmAberto() != null && vendaDTO.getTodasEmAberto()) {
                    movParcelasEmAberto = this.movParcelaDAO.consultarParcelasEmAbertoPessoa(rs.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO) && (vendaDTO.getTodasEmAberto() == null || !vendaDTO.getTodasEmAberto()) && !UteisValidacao.emptyString(vendaDTO.getParcelasSelecionadas())) {
                    List<String> parcelas = asList(vendaDTO.getParcelasSelecionadas().split(";"));
                    List<Integer> parcelasInt = parcelas.stream().map(Integer::parseInt).collect(Collectors.toList());
                    movParcelasEmAberto = this.movParcelaDAO.consultarPorCodigos(parcelasInt, true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else {
                    movParcelasEmAberto = this.movParcelaDAO.consultarParcelasVencidasPessoaVendasOnline(rs.getInt("pessoa"), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                }

                if (UteisValidacao.emptyList(movParcelasEmAberto)) {
                    throw new Exception("Nenhuma parcela encontrada");
                }

                if (empresaVO.getCobrarAutomaticamenteMultaJuros()) {
                    this.movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, movParcelasEmAberto, Calendario.hoje(), false, 1.0, null);
                }

                ConvenioCobrancaVO convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                BoletoVO boletoVO = boletoDAO.gerarBoleto(pessoaVO, convenioCobrancaVO, movParcelasEmAberto, dataVencimentoBoleto, usuarioVO,
                        origemCobrancaEnum, true, descontoValorFixo, descontoPercentual, true, true);

                gravarPactoPayComunicacao(vendaDTO, null, null, boletoVO, null);

                jsonRetorno.put("boleto_codigo", boletoVO.getCodigo());
                jsonRetorno.put("boleto_linha_digitavel", boletoVO.getLinhaDigitavel());
                jsonRetorno.put("boleto_url", boletoVO.getLinkBoleto());
                jsonRetorno.put("boleto_valor", boletoVO.getValorApresentar());
                jsonRetorno.put("boleto_vencimento", boletoVO.getDataVencimentoApresentar());
                jsonRetorno.put("sucesso", true);
                incluirLogVendasOnline(codigoCliente, vendaDTO, "COBRAR_PARCELAS_ABERTAS_BOLETO", "BOLETO - " + boletoVO.getCodigo());
            } else {
                throw new Exception("Cliente não encontrado");
            }
        } catch (Exception ex) {
            jsonRetorno.put("sucesso", false);
            jsonRetorno.put("msg", ex.getMessage());
            ex.printStackTrace();
            incluirLogVendasOnline(codigoCliente, vendaDTO, "ERRO_COBRAR_PARCELAS_ABERTAS_BOLETO", ex.getMessage());
        }
        return jsonRetorno;
    }

    private UsuarioVO obterUsuarioVendasOnline(VendaDTO vendaDTO) throws Exception {
        if (vendaDTO != null && !UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel())) {
            try {
                return this.usuarioDAO.consultarPorChavePrimaria(vendaDTO.getUsuarioResponsavel(), Uteis.NIVELMONTARDADOS_MINIMOS);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return this.usuarioDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
    }

    public List<MovParcelaVO> consultarParcelasVencidas(Integer codigoCliente, Integer codigoPessoa,
                                                        Integer codigoEmpresa, Integer tipoCobranca,
                                                        Integer cobrancaAntecipada, Integer origem) throws Exception {
        try {

            if (UteisValidacao.emptyNumber(codigoPessoa)) {
                codigoPessoa = this.pessoaDAO.descobrirCodigoPessoa(codigoCliente, null, null, null);
            }

            if (UteisValidacao.emptyNumber(codigoEmpresa)) {
                codigoEmpresa = this.empresaDAO.obterEmpresaClientePessoa(null, codigoPessoa);
            }

            if (UteisValidacao.emptyNumber(codigoEmpresa)) {
                throw new Exception("Empresa não encontrada");
            }

            if (UteisValidacao.emptyNumber(codigoPessoa)) {
                throw new Exception("Pessoa não encontrada");
            }

            List<MovParcelaVO> movParcelasEmAberto;
            if (!UteisValidacao.emptyNumber(cobrancaAntecipada)) {
                PactoPayCobrancaAntecipadaDTO antecipadaDTO = obterValidarCobrancaAntecipadaDTO(cobrancaAntecipada);

                movParcelasEmAberto = this.movParcelaDAO.consultarPorCodigos(antecipadaDTO.getParcelas(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                if (UteisValidacao.emptyList(movParcelasEmAberto)) {
                    throw new Exception("Nenhuma parcela encontrada para pagamento antecipado");
                }
            } else {
                movParcelasEmAberto = this.movParcelaDAO.consultarParcelasVencidasPessoaVendasOnline(codigoPessoa, codigoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            if (UteisValidacao.emptyList(movParcelasEmAberto) && origem != null && origem.equals(OrigemSistemaEnum.VENDAS_ONLINE_2.getCodigo())) {
                movParcelasEmAberto = this.movParcelaDAO.consultarParcelasVencidasPessoaVendasOnline(codigoPessoa, null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }

            //montar valor multa e juros
            if (!UteisValidacao.emptyList(movParcelasEmAberto)) {
                TipoCobrancaEnum tipoCobrancaEnum = TipoCobrancaEnum.obterPorId(tipoCobranca);
                if (tipoCobrancaEnum == null || tipoCobrancaEnum.equals(TipoCobrancaEnum.NENHUM)) {
                    tipoCobrancaEnum = TipoCobrancaEnum.ONLINE;
                }
                EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, tipoCobrancaEnum, movParcelasEmAberto, Calendario.hoje());
            }
            return movParcelasEmAberto;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private PactoPayCobrancaAntecipadaDTO obterValidarCobrancaAntecipadaDTO(Integer cobrancaAntecipada) throws Exception {
        PactoPayCobrancaAntecipadaVO cobrancaAntecipadaVO = this.cobrancaAntecipadaDAO.consultarPorChavePrimaria(cobrancaAntecipada, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (cobrancaAntecipadaVO == null) {
            throw new Exception("Link inválido");
        }
        PactoPayCobrancaAntecipadaDTO antecipadaDTO = cobrancaAntecipadaVO.getDTO();
        if (!Calendario.menorOuIgual(Calendario.hoje(), antecipadaDTO.getDataLimitePagamento_Date())) {
            throw new Exception("Link expirado");
        }
        return antecipadaDTO;
    }

    public Double consultarDescontoCobrancaAntecipada(Double valorCobrar, Integer codigoEmpresa,
                                                      Integer cobrancaAntecipada) throws Exception {
        try {
            Double desconto = 0.0;
            if (!UteisValidacao.emptyNumber(cobrancaAntecipada)) {
                PactoPayCobrancaAntecipadaDTO antecipadaDTO = obterValidarCobrancaAntecipadaDTO(cobrancaAntecipada);
                PactoPayConfigVO pactoPayConfigVO = this.pactoPayConfigDAO.consultarPorEmpresa(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (antecipadaDTO.isAplicarDesconto()) {
                    Double descontoValorFixo = 0.0;
                    Double descontoPercentual = 0.0;
                    if (pactoPayConfigVO.isCobrancaAntecipadaValorFixo()) {
                        descontoValorFixo = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                    } else {
                        descontoPercentual = pactoPayConfigVO.getCobrancaAntecipadaDesconto();
                    }
                    return calcularValorDesconto(valorCobrar, descontoValorFixo, descontoPercentual);
                }
            }
            return desconto;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private Double calcularValorDesconto(Double valorTotal, Double descontoValorFixo, Double descontoPercentual) {
        Double valorDesconto = 0.0;
        if (!UteisValidacao.emptyNumber(descontoValorFixo)) {
            valorDesconto = descontoValorFixo;
        } else if (!UteisValidacao.emptyNumber(descontoPercentual)) {
            valorDesconto = Uteis.arredondarForcando2CasasDecimais((valorTotal * descontoPercentual) / 100);
        }

        if (Uteis.arredondarForcando2CasasDecimais(valorDesconto) > Uteis.arredondarForcando2CasasDecimais(valorTotal)) {
            return valorTotal;
        } else {
            return Uteis.arredondarForcando2CasasDecimais(valorDesconto);
        }
    }

    public void incluirLogOperacaoLink(OrigemCobrancaEnum origemCobrancaEnum, int codCliente, int usuario, int codAutIncluida) throws Exception {
        try {
            UsuarioVO usuarioVO;
            ClienteVO clienteVO;
            //Usuário responsável por ter gerado o link
            usuarioVO = this.usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_MINIMOS);
            clienteVO = this.clienteDAO.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_MINIMOS);

            LogVO obj = new LogVO();
            obj.setChavePrimaria(clienteVO.getPessoa().getCodigo().toString());

            if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_CADASTRAR)) {
                obj.setNomeEntidade("LINK CADASTRAR CARTAO");
                obj.setNomeEntidadeDescricao("LINK CADASTRAR CARTAO");
                obj.setOperacao("INCLUSÃO - CARTÃO PELO LINK DE CADASTRO");
                obj.setResponsavelAlteracao("Vendas Online");
                obj.setNomeCampo("Usuário responsável pelo link gerado");
                obj.setValorCampoAnterior("");
                obj.setValorCampoAlterado(usuarioVO.getNome() + " (cód: " + usuario + ")" +
                        " | ID. Autorização de cobrança incluída: " + codAutIncluida);
                obj.setDataAlteracao(Calendario.hoje());
            }
            if (origemCobrancaEnum.equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO)) {
                obj.setNomeEntidade("LINK DE PAGAMENTO");
                obj.setNomeEntidadeDescricao("LINK DE PAGAMENTO");
                obj.setOperacao("INCLUSÃO - CARTÃO PELO LINK DE PAGAMENTO");
                obj.setResponsavelAlteracao("Vendas Online");
                obj.setNomeCampo("Usuário responsável pelo link gerado");
                obj.setValorCampoAnterior("");
                obj.setValorCampoAlterado(usuarioVO.getNome() + " (cód: " + usuario + ")" +
                        " | ID. Autorização de cobrança incluída: " + codAutIncluida);
                obj.setDataAlteracao(Calendario.hoje());
            }
            registrarLogObjetoVO(obj, clienteVO.getPessoa().getCodigo());
        } catch (Exception e) {
        }
    }

    private PixVO obterPixAtivoVendaContrato(ClienteVO clienteVO, VendaDTO vendaDTO) {
        try {
            return this.vendasOnlineVendaDAO.obterPixAtivoVendaContrato(clienteVO, vendaDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public String obterValorComTaxaParcelamentoStone(OperacaoVendasOnlineDTO operacaoDTO) throws Exception {
        double taxa = 0;
        ConvenioCobrancaVO convenio = obterConvenioVendasOnline(operacaoDTO);

        if (convenio.getTipo() == TipoConvenioCobrancaEnum.DCC_STONE_ONLINE && convenio.getTipoParcelamentoStone().equals("ISSR")) {

            OperadoraCartaoVO operadora = obterOperadoraCartaoConvenioVendasOnline(operacaoDTO);
            int codFormaPagamentoConvenio = obterIdFormaPagamentoConvenioVendasOnline(convenio);
            AdquirenteVO adquirente = adquirenteDAO.obterAdquirentePeloTipoConvenio(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE);

            Date data = Uteis.getDateTime(new Date(), 0, 0, 0);
            Calendar cal = Calendar.getInstance(Calendario.getDefaultLocale());
            cal.setTime(data);
            cal.set(Calendar.MILLISECOND, 0);
            java.sql.Date dataHoje = Uteis.getDataJDBC(cal.getTime());

            List<TaxaCartaoVO> taxaCartaoVO = taxaCartaoDAO.obterPorBandeiraEFormaPagamentoEParcelamento(operadora.getCodigo(), codFormaPagamentoConvenio, operacaoDTO.getVenda().getNrVezesDividir(), adquirente.getCodigo(), dataHoje, Uteis.NIVELMONTARDADOS_TODOS);

            if (taxaCartaoVO.size() != 0) {
                if (!UteisValidacao.emptyNumber(taxaCartaoVO.get(0).getTaxa())) {
                    taxa = taxaCartaoVO.get(0).getTaxa();
                }
            }
        }
        return String.valueOf(taxa);
    }

    private ConvenioCobrancaVO obterConvenioVendasOnline(OperacaoVendasOnlineDTO operacaoDTO) throws Exception {
        ConvenioCobrancaVO convenio = null;

        //Verifica se tem Convênio Especifico por Produto ou Plano, nas configurações do Gestão do Vendas Online
        int quantItensVenda = (operacaoDTO.getVenda().getPlano() == 0 ? 0 : 1) + operacaoDTO.getVenda().getProdutos().size();
        if (quantItensVenda == 1) {
            VendasOnlineConvenioVO objConvenioEspecifico = null;
            if (operacaoDTO.getVenda().getPlano() == 0) {
                objConvenioEspecifico = vendasOnlineConvenioDAO.consultarPorProdutoOuPlano(operacaoDTO.getEmpresa(), true, operacaoDTO.getVenda().getProdutos().get(0).getProduto(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, false);
            } else {
                objConvenioEspecifico = vendasOnlineConvenioDAO.consultarPorProdutoOuPlano(operacaoDTO.getEmpresa(), false, operacaoDTO.getVenda().getPlano(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, false);
            }
            if (objConvenioEspecifico != null) {
                convenio = objConvenioEspecifico.getConvenioCobrancaVO();
                convenio.setTipoParcelamentoStone(objConvenioEspecifico.getTipoParcelamentoStone());
            }
        }

        //Verifica se tem Convênio Especifico Geral, nas configurações do Gestão do Vendas Online
        if (convenio == null) {
            List<VendasOnlineConvenioTentativaVO> vendasOnlineConvenioTentativaVOList = vendasOnlineConvenioTentativaDAO.consultarPorEmpresa(operacaoDTO.getEmpresa(), true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (vendasOnlineConvenioTentativaVOList.size() > 0) {
                convenio = vendasOnlineConvenioTentativaVOList.get(0).getConvenioCobrancaVO();
                convenio.setTipoParcelamentoStone(vendasOnlineConvenioTentativaVOList.get(0).getTipoParcelamentoStone());
            }
        }

        //Verifica se cliente não tem ativado Gestão do Vendas Online, mas usa Link de Pagamento
        if (convenio == null) {
            Integer idConveioCobranca = empresaDAO.obterConvenioCobrancaCartao(operacaoDTO.getEmpresa());
            convenio = convenioCobrancaDAO.consultarPorChavePrimaria(idConveioCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        return convenio;
    }

    private OperadoraCartaoVO obterOperadoraCartaoConvenioVendasOnline(OperacaoVendasOnlineDTO operacaoDTO) throws Exception {
        OperadorasExternasAprovaFacilEnum bandeiraEnum = null;
        OperadoraCartaoVO operadora = null;
        if (ValidaBandeira.numeroCartaoValido(operacaoDTO.getVenda().getNumeroCartao())) {
            ValidaBandeira.Bandeira bandeira = ValidaBandeira.buscarBandeira(operacaoDTO.getVenda().getNumeroCartao());
            String bandeiraCartao = String.valueOf(bandeira);
            bandeiraEnum = Enum.valueOf(OperadorasExternasAprovaFacilEnum.class, bandeiraCartao.toUpperCase());
            operadora = operadoraCartaoDAO.consultarPorCodigoIntegracaoStoneOnline(bandeiraEnum, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true);
        } else {
            throw new Exception("Cartão inválido");
        }
        return operadora;
    }

    private int obterIdFormaPagamentoConvenioVendasOnline(ConvenioCobrancaVO convenio) throws Exception {
        //obter forma de pagamento do convênio do vendas online
        int codFormaPagamentoConvenio = 0;
        boolean temFormaPagamento = false;
        List<FormaPagamentoVO> formasPgtConvenio = formaPagamentoDAO.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        for (FormaPagamentoVO forma : formasPgtConvenio) {
            if (forma.getConvenioCobrancaVO() != null && forma.getConvenioCobrancaVO().getCodigo().equals(convenio.getCodigo())) {
                codFormaPagamentoConvenio = forma.getCodigo();
                temFormaPagamento = true;
                break;
            }
        }
        //obtem a padrão dafault recorrência se não tem nenhuma forma de pagamento que veio do vendas online preenchido
        if (!temFormaPagamento) {
            codFormaPagamentoConvenio = formaPagamentoDAO.obterFormaPagamentoCartaoRecorrente().getCodigo();
        }
        return codFormaPagamentoConvenio;
    }


    private List<BoletoVO> obterBoletoAtivoVendaContrato(ClienteVO clienteVO, VendaDTO vendaDTO) {
        try {
            return this.vendasOnlineVendaDAO.obterBoletoAtivoVendaContrato(clienteVO, vendaDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    private List<BoletoVO> gerarBoleto(List<MovParcelaVO> listaParcelas, ConvenioCobrancaVO convenioCobrancaVO,
                                       Integer empresa, PessoaVO pessoaVO, UsuarioVO usuarioVO,
                                       OrigemCobrancaEnum origemCobrancaEnum, VendaDTO vendaDTO) throws Exception {

        PessoaCPFTO pessoaCPFTO = this.boletoDAO.obterDadosPessoaPagador(empresa, pessoaVO, true, true);
        List<BoletoVO> listaBoleto = this.boletoDAO.gerarBoletoPorParcela(new PessoaVO(pessoaCPFTO.getCodigo()),
                convenioCobrancaVO, listaParcelas, usuarioVO, origemCobrancaEnum, false, true);
        if (!UteisValidacao.emptyList(listaBoleto)) {
            for (BoletoVO boletoVO : listaBoleto) {
                gravarPactoPayComunicacao(vendaDTO, null, null, boletoVO, null);
            }
        }
        return listaBoleto;
    }

    private void gravarPactoPayComunicacao(VendaDTO vendaDTO, TransacaoVO transacaoVO, PixVO pixVO, BoletoVO boletoVO,
                                           AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO) {
        try {
            if (vendaDTO == null ||
                    UteisValidacao.emptyNumber(vendaDTO.getPactoPayComunicacao())) {
                return;
            }
            if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                pactoPayComunicacaoDAO.alterarTransacao(transacaoVO.getCodigo(), vendaDTO.getPactoPayComunicacao(), false);
                if (!UteisValidacao.emptyNumber(transacaoVO.getDesconto())) {
                    pactoPayComunicacaoDAO.alterarDesconto(transacaoVO.getDesconto(), vendaDTO.getPactoPayComunicacao(), false);
                }
            }
            if (pixVO != null && !UteisValidacao.emptyNumber(pixVO.getCodigo())) {
                pactoPayComunicacaoDAO.alterarPix(pixVO.getCodigo(), vendaDTO.getPactoPayComunicacao(), false);
                if (!UteisValidacao.emptyNumber(pixVO.getDesconto())) {
                    pactoPayComunicacaoDAO.alterarDesconto(pixVO.getDesconto(), vendaDTO.getPactoPayComunicacao(), false);
                }
            }
            if (boletoVO != null && !UteisValidacao.emptyNumber(boletoVO.getCodigo())) {
                pactoPayComunicacaoDAO.alterarBoleto(boletoVO.getCodigo(), vendaDTO.getPactoPayComunicacao(), false);
                if (!UteisValidacao.emptyNumber(boletoVO.getValorDescontoPagAntecipado())) {
                    pactoPayComunicacaoDAO.alterarDesconto(boletoVO.getValorDescontoPagAntecipado(), vendaDTO.getPactoPayComunicacao(), false);
                }
            }
            if (autorizacaoCobrancaClienteVO != null && !UteisValidacao.emptyNumber(autorizacaoCobrancaClienteVO.getCodigo())) {
                pactoPayComunicacaoDAO.alterarAutorizacaoCobrancaCliente(autorizacaoCobrancaClienteVO.getCodigo(), vendaDTO.getPactoPayComunicacao(), false);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
