package servicos.vendasonline.dto;

import br.com.pactosolucoes.turmas.servico.dto.HorarioTurmaDTO;
import com.google.gson.Gson;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VendaDTO {
    private Integer unidade;
    private Integer nrVezesDividir;
    private Integer nrVezesDividirMatricula;
    private Integer plano;
    private String nome;
    private String cpf;
    private String passaporte;
    private String sexo;
    private String dataNascimento;
    private String responsavelPai;
    private String responsavelMae;
    private String cpfPai;
    private String cpfMae;
    private String email;
    private String nomeCartao;
    private String numeroCartao;
    private String validade;
    private String cvv;
    private String telefone;
    private String endereco;
    private String numero;
    private String bairro;
    private String complemento;
    private String cep;
    private Integer diaVencimento;
    private String numeroCupomDesconto;
    private String cpftitularcard;
    private Integer vencimentoFatura;
    private List<VendaProdutoDTO> produtos;
    private List<String> aulasMarcadas;
    private boolean cobrarParcelasEmAberto = true;
    private String observacaoCliente;
    private Date dataInicioContrato;
    private Date dataLancamento;
    private Integer codigoCategoriaPlano;
    private boolean permitirRenovacao;
    private Date dataInicioVendaProdutos;

    // INICIO - Pacto Store
    private String cnpj;
    private Integer codigoFinanceiro;
    //não é a chave da venda..
    private String chave;
    //não é a empresa da venda..
    private Integer codigoEmpresaZW;
    // FIM - Pacto Store
    private boolean vendaConsultor = false;
    private Integer codigoColaborador;
    private Integer convenioCobranca;
    private Integer origemCobranca;
    private Integer cobrancaAntecipada;
    private Integer pactoPayComunicacao;
    private int responsavelLink;
    //cód usuário resp. por gerar o link
    private String token;
    private boolean pix = false;
    private boolean boleto = false;
    private Integer codigoEvento;
    private String remetenteConviteMatricula;
    private String rg;
    private boolean clienteJaCadastrado = true;

    //usuario responsável
    //caso informado esse será o usuário do consultor do cliente, responsável pelo contrato e pagamento.
    private Integer usuarioResponsavel;
    private Integer codigoRegistroAcessoPagina;

    private String tipoParcelamentoCredito;
    private String respostaParqJson;
    private String nowLocationIp;
    private String nomeResponsavelEmpresa;
    private String cpfResponsavelEmpresa;
    private String assinaturaDigital;
    private String parcelasSelecionadas;
    private String freepass;
    private Boolean enviarEmail;
    private String urlZw;
    private String utm_data;
    private String userAgent;
    private String ip;
    private List<ModalidadeSelecionadaDTO> modalidadesSelecionadas;
    private List<HorarioTurmaDTO> horariosSelecionados;
    private Boolean todasEmAberto;
    private Integer origemSistema;
    private Date dataUtilizacao;
    private boolean permiteInformarDataUtilizacao;

    //Plano compartilhado
    private List<ClienteDependenteDTO> clientesCadastradosComoDependentesPlanoCompartilhado;

    public VendaDTO() {
    }


    public VendaDTO(String dados) {
        this(new JSONObject(dados));
    }

    public VendaDTO(JSONObject json) {
        this.diaVencimento = json.optInt("diaVencimento");
        this.unidade = json.optInt("unidade");
        this.nrVezesDividir = json.optInt("nrVezesDividir");
        this.nrVezesDividirMatricula = json.optInt("nrVezesDividirMatricula");
        this.plano = json.optInt("plano");
        this.nome = json.optString("nome");
        this.cpf = json.optString("cpf");
        this.passaporte = json.optString("passaporte");
        this.responsavelPai = json.optString("responsavelPai");
        this.responsavelMae = json.optString("responsavelMae");
        this.cpfMae = json.optString("cpfMae");
        this.cpfPai = json.optString("cpfPai");
        if (json.optString("sexo").equalsIgnoreCase("Masculino") || json.optString("sexo").equalsIgnoreCase("M")) {
            this.sexo = "M";
        } else if (json.optString("sexo").equalsIgnoreCase("Feminino") || json.optString("sexo").equalsIgnoreCase("F")) {
            this.sexo = "F";
        } else {
            this.sexo = "";
        }
        this.dataNascimento = json.optString("dataNascimento");
        this.email = json.optString("email");
        this.nomeCartao = json.optString("nomeCartao");
        this.numeroCartao = json.optString("numeroCartao");
        this.validade = json.optString("validade");
        this.cvv = json.optString("cvv");
        this.cep = json.optString("cep");
        this.endereco = json.optString("endereco");
        this.telefone = json.optString("telefone");
        this.numero = json.optString("numero");
        this.complemento = json.optString("complemento");
        this.bairro = json.optString("bairro");
        this.numeroCupomDesconto = json.optString("numeroCupomDesconto");
        this.cpftitularcard = json.optString("cpftitularcard");
        this.vencimentoFatura = json.optInt("vencimentoFatura");
        this.cobrarParcelasEmAberto = json.optBoolean("cobrarParcelasEmAberto", true);
        this.observacaoCliente = json.optString("observacaoCliente");
        this.cnpj = json.optString("cnpj");
        this.codigoFinanceiro = json.optInt("codigoFinanceiro");
        this.chave = json.optString("chave");
        this.codigoEmpresaZW = json.optInt("codigoEmpresaZW");
        this.origemCobranca = json.optInt("origemCobranca");
        this.cobrancaAntecipada = json.optInt("cobrancaAntecipada");
        this.pactoPayComunicacao = json.optInt("pactoPayComunicacao");
        try {
            this.dataInicioContrato = new SimpleDateFormat("yyyy-MM-dd").parse(json.optString("dataInicioContrato"));
        }catch (Exception e){
            this.dataInicioContrato = null;
        }
        try {
            this.dataLancamento = new SimpleDateFormat("yyyy-MM-dd").parse(json.optString("dataLancamento"));
        }catch (Exception e){
            this.dataLancamento = null;
        }
        try{
            this.permitirRenovacao = json.getBoolean("permitirRenovacao");
        }catch (Exception e){
            this.permitirRenovacao = false;
        }
        try {
            this.dataInicioVendaProdutos = new SimpleDateFormat("yyyy-MM-dd").parse(json.optString("dataInicioVigenciaVenda"));
        }catch (Exception e){
            this.dataInicioVendaProdutos = null;
        }
        try {
            this.aulasMarcadas = new ArrayList(){{
                for(int i = 0; i < json.getJSONArray("aulasMarcadas").length(); i++){
                    add(json.getJSONArray("aulasMarcadas").getString(i));
                }
            }};
        }catch (Exception e){
            this.aulasMarcadas = new ArrayList<>();
        }

        this.produtos = new ArrayList<>();

        try {
            JSONArray array = json.optJSONArray("produtos");
            for (int e = 0; e < array.length(); e++) {
                JSONObject obj = array.getJSONObject(e);
                this.produtos.add(new VendaProdutoDTO(obj));
            }
        } catch (Exception ignored) {
        }

        try {
            this.codigoCategoriaPlano = json.optInt("codigoCategoriaPlano");
        } catch (Exception e) {
            this.codigoCategoriaPlano = null;
        }

        try {
            this.vendaConsultor = json.getBoolean("vendaConsultor");
        } catch (Exception inored) {
        }

        this.codigoColaborador = json.optInt("codigoColaborador");
        this.convenioCobranca = json.optInt("convenioCobranca");
        this.responsavelLink = json.optInt("responsavelLink");
        this.token = json.optString("token");
        this.remetenteConviteMatricula = json.optString("remetenteConviteMatricula");
        this.rg = json.optString("rg");

        if (json.has("codigoEvento")){
            this.codigoEvento = json.optInt("codigoEvento");
        }
        this.usuarioResponsavel = json.optInt("usuarioResponsavel");
        this.codigoRegistroAcessoPagina= json.optInt("codigoRegistroAcessoPagina");
        this.tipoParcelamentoCredito = json.optString("tipoParcelamentoCredito");
        this.respostaParqJson = json.optString("respostaParqJson");
        this.nowLocationIp = json.optString("nowLocationIp");
        this.nomeResponsavelEmpresa = json.optString("nomeResponsavelEmpresa");
        this.cpfResponsavelEmpresa = json.optString("cpfResponsavelEmpresa");
        this.assinaturaDigital = json.optString("assinaturaDigital");
        if(json.has("freepass")) {
            this.setFreepass(json.optString("freepass"));
        }
        if(json.has("parcelasSelecionadas")) {
            this.parcelasSelecionadas = json.optString("parcelasSelecionadas");
        }
        if(json.has("enviarEmail")) {
            this.enviarEmail = json.optBoolean("enviarEmail");
        }
        this.utm_data = json.optString("utm_data");
        // montando lista de clientes q sao dependentes do titular e q devem ser vinculados
        if (json.has("clientesCadastradosComoDependentesPlanoCompartilhado")) {
            JSONArray dependentesArray = json.optJSONArray("clientesCadastradosComoDependentesPlanoCompartilhado");

            if (dependentesArray != null) {
                this.clientesCadastradosComoDependentesPlanoCompartilhado = new ArrayList<>();
                for (int i = 0; i < dependentesArray.length(); i++) {
                    JSONObject clienteJson = dependentesArray.getJSONObject(i);
                    ClienteDependenteDTO cliente = new ClienteDependenteDTO(clienteJson);
                    this.clientesCadastradosComoDependentesPlanoCompartilhado.add(cliente);
                }
            }
        }


        this.modalidadesSelecionadas = new ArrayList<>();

        try {
            JSONArray array = json.optJSONArray("modalidadesSelecionadas");
            for (int e = 0; e < array.length(); e++) {
                JSONObject obj = array.getJSONObject(e);
                if (!UteisValidacao.emptyNumber(Uteis.getParamJsonInt(obj, "selectedTimesPerWeek"))) {
                    this.modalidadesSelecionadas.add(new ModalidadeSelecionadaDTO(obj));
                }
            }
        } catch (Exception ignored) {
        }

        this.horariosSelecionados = new ArrayList<>();

        try {
            JSONArray array = json.optJSONArray("horariosSelecionados");
            for (int e = 0; e < array.length(); e++) {
                JSONObject obj = array.getJSONObject(e);
                this.horariosSelecionados.add(new HorarioTurmaDTO(obj));
            }
        } catch (Exception ignored) {
        }

        try {
            this.ip = json.optString("ip");
        } catch (Exception ex) {
        }

        try {
            this.userAgent = json.optString("userAgent");
        } catch (Exception ex) {
        }

        try {
            this.urlZw = json.optString("urlZw");
        } catch (Exception ex) {
        }

        try {
            this.todasEmAberto = json.optBoolean("todasEmAberto");
        } catch (Exception ex) {
        }

        this.origemSistema = json.optInt("origemSistema");

        try {
            this.dataUtilizacao = new SimpleDateFormat("dd/MM/yyyy").parse(json.optString("dataUtilizacao"));
        }catch (Exception e){
            this.dataUtilizacao = null;
        }

        this.permiteInformarDataUtilizacao = json.optBoolean("permiteInformarDataUtilizacao");
    }

    public Integer getNrVezesDividir() {
        return nrVezesDividir;
    }

    public void setNrVezesDividir(Integer nrVezesDividir) {
        this.nrVezesDividir = nrVezesDividir;
    }

    public Integer getNrVezesDividirMatricula() {
        return nrVezesDividirMatricula;
    }

    public void setNrVezesDividirMatricula(Integer nrVezesDividirMatricula) {
        this.nrVezesDividirMatricula = nrVezesDividirMatricula;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public Integer getUnidade() {
        return unidade;
    }

    public void setUnidade(Integer unidade) {
        this.unidade = unidade;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNomeCartao() {
        return nomeCartao;
    }

    public void setNomeCartao(String nomeCartao) {
        this.nomeCartao = nomeCartao;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public String getValidade() {
        return validade;
    }

    public void setValidade(String validade) {
        this.validade = validade;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public Integer getDiaVencimento() {
        return diaVencimento;
    }

    public void setDiaVencimento(Integer diaVencimento) {
        this.diaVencimento = diaVencimento;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getNumeroCupomDesconto() {
        return numeroCupomDesconto == null ? "" : numeroCupomDesconto;
    }

    public void setNumeroCupomDesconto(String numeroCupomDesconto) {
        this.numeroCupomDesconto = numeroCupomDesconto;
    }

    public String getCpftitularcard() {
        return cpftitularcard;
    }

    public void setCpftitularcard(String cpftitularcard) {
        this.cpftitularcard = cpftitularcard;
    }

    public Integer getVencimentoFatura() {
        return vencimentoFatura;
    }

    public void setVencimentoFatura(Integer vencimentoFatura) {
        this.vencimentoFatura = vencimentoFatura;
    }

    public List<VendaProdutoDTO> getProdutos() {
        if (produtos == null) {
            produtos = new ArrayList<>();
        }
        return produtos;
    }

    public void setProdutos(List<VendaProdutoDTO> produtos) {
        this.produtos = produtos;
    }

    public boolean isCobrarParcelasEmAberto() {
        return cobrarParcelasEmAberto;
    }

    public void setCobrarParcelasEmAberto(boolean cobrarParcelasEmAberto) {
        this.cobrarParcelasEmAberto = cobrarParcelasEmAberto;
    }

    public String getObservacaoCliente() {
        if (observacaoCliente == null) {
            observacaoCliente = "";
        }
        return observacaoCliente;
    }

    public void setObservacaoCliente(String observacaoCliente) {
        this.observacaoCliente = observacaoCliente;
    }

    public String getCnpj() {
        if (cnpj == null) {
            cnpj = "";
        }
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodigoEmpresaZW() {
        //utilizado para Pacto Store..
        //não é a empresa da venda..
        return codigoEmpresaZW;
    }

    public void setCodigoEmpresaZW(Integer codigoEmpresaZW) {
        this.codigoEmpresaZW = codigoEmpresaZW;
    }

    public Date getDataInicioContrato() {
        return dataInicioContrato;
    }

    public void setDataInicioContrato(Date dataInicioContrato) {
        this.dataInicioContrato = dataInicioContrato;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public boolean isPermitirRenovacao() {
        return permitirRenovacao;
    }

    public void setPermitirRenovacao(boolean permitirRenovacao) {
        this.permitirRenovacao = permitirRenovacao;
    }

    public String getResponsavelPai() {
        return responsavelPai;
    }

    public void setResponsavelPai(String responsavelPai) {
        this.responsavelPai = responsavelPai;
    }

    public String getResponsavelMae() {
        return responsavelMae;
    }

    public void setResponsavelMae(String responsavelMae) {
        this.responsavelMae = responsavelMae;
    }

    public List<String> getAulasMarcadas() {
        return aulasMarcadas;
    }

    public void setAulasMarcadas(List<String> aulasMarcadas) {
        this.aulasMarcadas = aulasMarcadas;
    }

    public Integer getCodigoCategoriaPlano() {
        return codigoCategoriaPlano;
    }

    public void setCodigoCategoriaPlano(Integer codigoCategoriaPlano) {
        this.codigoCategoriaPlano = codigoCategoriaPlano;
    }

    public boolean isVendaConsultor() {
        return vendaConsultor;
    }

    public void setVendaConsultor(boolean vendaConsultor) {
        this.vendaConsultor = vendaConsultor;
    }

    public Date getDataInicioVendaProdutos() {
        return dataInicioVendaProdutos;
    }

    public void setDataInicioVendaProdutos(Date dataInicioVendaProdutos) {
        this.dataInicioVendaProdutos = dataInicioVendaProdutos;
    }

    public JSONObject toJSON() {
        return new JSONObject(new Gson().toJson(this));
    }

    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    public void setCodigoColaborador(Integer codigoColaborador) {
        this.codigoColaborador = codigoColaborador;
    }

    public String getCpfPai() {
        return cpfPai;
    }

    public void setCpfPai(String cpfPai) {
        this.cpfPai = cpfPai;
    }

    public String getCpfMae() {
        return cpfMae;
    }

    public void setCpfMae(String cpfMae) {
        this.cpfMae = cpfMae;
    }

    public Integer getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(Integer convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Integer getOrigemCobranca() {
        return origemCobranca;
    }

    public void setOrigemCobranca(Integer origemCobranca) {
        this.origemCobranca = origemCobranca;
    }

    public Integer getCobrancaAntecipada() {
        return cobrancaAntecipada;
    }

    public void setCobrancaAntecipada(Integer cobrancaAntecipada) {
        this.cobrancaAntecipada = cobrancaAntecipada;
    }

    public int getResponsavelLink() {
        return responsavelLink;
    }

    public void setResponsavelLink(int responsavelLink) {
        this.responsavelLink = responsavelLink;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isPix() {
        return pix;
    }

    public void setPix(boolean pix) {
        this.pix = pix;
    }

    public String getRemetenteConviteMatricula() {
        return remetenteConviteMatricula;
    }

    public void setRemetenteConviteMatricula(String remetenteConviteMatricula) {
        this.remetenteConviteMatricula = remetenteConviteMatricula;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public Integer getCodigoEvento() {
        return codigoEvento;
    }

    public void setCodigoEvento(Integer codigoEvento) {
        this.codigoEvento = codigoEvento;
    }

    public boolean isClienteJaCadastrado() {
        return clienteJaCadastrado;
    }

    public void setClienteJaCadastrado(boolean clienteJaCadastrado) {
        this.clienteJaCadastrado = clienteJaCadastrado;
    }

    public Integer getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(Integer usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public Integer getCodigoRegistroAcessoPagina() {
        return codigoRegistroAcessoPagina;
    }

    public void setCodigoRegistroAcessoPagina(Integer codigoRegistroAcessoPagina) {
        this.codigoRegistroAcessoPagina = codigoRegistroAcessoPagina;
    }

    public boolean isBoleto() {
        return boleto;
    }

    public void setBoleto(boolean boleto) {
        this.boleto = boleto;
    }

    public String getTipoParcelamentoCredito() {
        return tipoParcelamentoCredito;
    }

    public void setTipoParcelamentoCredito(String tipoParcelamentoCredito) {
        this.tipoParcelamentoCredito = tipoParcelamentoCredito;
    }

    public String getRespostaParqJson() {
        return respostaParqJson;
    }

    public void setRespostaParqJson(String respostaParqJson) {
        this.respostaParqJson = respostaParqJson;
    }

    public String getNowLocationIp() {
        return nowLocationIp;
    }

    public void setNowLocationIp(String nowLocationIp) {
        this.nowLocationIp = nowLocationIp;
    }

    public String getNomeResponsavelEmpresa() {
        return nomeResponsavelEmpresa;
    }

    public void setNomeResponsavelEmpresa(String nomeResponsavelEmpresa) {
        this.nomeResponsavelEmpresa = nomeResponsavelEmpresa;
    }

    public String getCpfResponsavelEmpresa() {
        return cpfResponsavelEmpresa;
    }

    public void setCpfResponsavelEmpresa(String cpfResponsavelEmpresa) {
        this.cpfResponsavelEmpresa = cpfResponsavelEmpresa;
    }

    public String getAssinaturaDigital() {
        if (UteisValidacao.emptyString(assinaturaDigital)) {
            return "";
        }
        return assinaturaDigital;
    }

    public void setAssinaturaDigital(String assinaturaDigital) {
        this.assinaturaDigital = assinaturaDigital;
    }

    public String getFreepass() {
        return freepass;
    }

    public void setFreepass(String freepass) {
        this.freepass = freepass;
    }

    public String getParcelasSelecionadas() {
        return parcelasSelecionadas;
    }

    public void setParcelasSelecionadas(String parcelasSelecionadas) {
        this.parcelasSelecionadas = parcelasSelecionadas;
    }

    public Boolean getEnviarEmail() {
        return enviarEmail;
    }

    public void setEnviarEmail(Boolean enviarEmail) {
        this.enviarEmail = enviarEmail;
    }

    public String getUrlZw() {
        if (UteisValidacao.emptyString(urlZw)) {
            return "";
        }
        return urlZw;
    }

    public void setUrlZw(String urlZw) {
        this.urlZw = urlZw;
    }

    public Integer getPactoPayComunicacao() {
        if (pactoPayComunicacao == null) {
            pactoPayComunicacao = 0;
        }
        return pactoPayComunicacao;
    }

    public void setPactoPayComunicacao(Integer pactoPayComunicacao) {
        this.pactoPayComunicacao = pactoPayComunicacao;
    }

    public String getUtm_data() {
        if (UteisValidacao.emptyString(utm_data)) {
            return "";
        }
        return utm_data;
    }

    public void setUtm_data(String utm_data) {
        this.utm_data = utm_data;
    }

    public List<ClienteDependenteDTO> getClientesCadastradosComoDependentesPlanoCompartilhado() {
        return clientesCadastradosComoDependentesPlanoCompartilhado;
    }

    public void setClientesCadastradosComoDependentesPlanoCompartilhado(List<ClienteDependenteDTO> clientesCadastradosComoDependentesPlanoCompartilhado) {
        this.clientesCadastradosComoDependentesPlanoCompartilhado = clientesCadastradosComoDependentesPlanoCompartilhado;
    }

    public List<ModalidadeSelecionadaDTO> getModalidadesSelecionadas() {
        return modalidadesSelecionadas;
    }

    public void setModalidadesSelecionadas(List<ModalidadeSelecionadaDTO> modalidadesSelecionadas) {
        this.modalidadesSelecionadas = modalidadesSelecionadas;
    }

    public List<HorarioTurmaDTO> getHorariosSelecionados() {
        return horariosSelecionados;
    }

    public void setHorariosSelecionados(List<HorarioTurmaDTO> horariosSelecionados) {
        this.horariosSelecionados = horariosSelecionados;
    }

    public String getUserAgent() {
        if (UteisValidacao.emptyString(userAgent)) {
            return "";
        }
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getIp() {
        if (UteisValidacao.emptyString(ip)) {
            return "";
        }
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Boolean getTodasEmAberto() {
        return todasEmAberto;
    }

    public void setTodasEmAberto(Boolean todasEmAberto) {
        this.todasEmAberto = todasEmAberto;
    }

    public Integer getOrigemSistema() { return origemSistema;}

    public void setOrigemSistema(Integer origemSistema) {this.origemSistema = origemSistema;}

    public Date getDataUtilizacao() {
        return dataUtilizacao;
    }

    public void setDataUtilizacao(Date dataUtilizacao) {
        this.dataUtilizacao = dataUtilizacao;
    }

    public boolean isPermiteInformarDataUtilizacao() {
        return permiteInformarDataUtilizacao;
    }

    public void setPermiteInformarDataUtilizacao(boolean permiteInformarDataUtilizacao) {
        this.permiteInformarDataUtilizacao = permiteInformarDataUtilizacao;
    }

    public String getPassaporte() {
        return passaporte;
    }

    public void setPassaporte(String passaporte) {
        this.passaporte = passaporte;
    }
}
