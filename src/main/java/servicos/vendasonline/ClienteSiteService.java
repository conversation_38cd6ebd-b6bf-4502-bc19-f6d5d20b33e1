package servicos.vendasonline;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.vendas.VendasConfig;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.vendasonline.dto.ClienteDependenteDTO;
import servicos.vendasonline.dto.VendaDTO;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class ClienteSiteService {
    private Connection con;

    public ClienteSiteService(Connection con) {
        this.con = con;
    }

    public String incluirClienteSite(VendaDTO vendaDTO, UsuarioVO usuarioResponsavel, Boolean ignoraCpf, Boolean atualizarSintetico, Boolean pactoFlowVendaInternacional) throws Exception {
        String retorno = "";
        Cliente cli = new Cliente(con);
        int numeroMatricula = 0;

            validarDados(vendaDTO);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresaDao = null;

            //preencher codigoPais, codigoCidade e codigoEstado com base nos dados da Empresa
            Integer codigoPais = empresaVO.getPais().getCodigo() != 0 ? empresaVO.getPais().getCodigo() : null;
            Integer codigoCidade = empresaVO.getCidade().getCodigo() != 0 ? empresaVO.getCidade().getCodigo() : null;
            Integer codigoEstado = empresaVO.getEstado().getCodigo() != 0 ? empresaVO.getEstado().getCodigo() : null;

            //preencher codigoPais, codigoCidade e codigoEstado com base no CEP informado no ato da Venda
            if (!UteisValidacao.emptyString(vendaDTO.getCep())) {
                try {
                    //Dentro do objeto cidadeVO, já tem país e estado
                    CidadeVO cidadeVO = new Cidade(con).obterCidadeCEP(vendaDTO.getCep());
                    if (cidadeVO != null) {
                        codigoCidade = cidadeVO.getCodigo();
                        codigoEstado = cidadeVO.getEstado().getCodigo();
                        codigoPais = cidadeVO.getPais().getCodigo();
                    }
                } catch (Exception ignore) {
                }
            }

            Pessoa pessoaDao = new Pessoa(con);
            String cpfConsultar = vendaDTO.getCpf();
            if (cpfConsultar.length() == 11) {
                cpfConsultar = cpfConsultar.substring(0, 3) + "." + cpfConsultar.substring(3, 6) + "." + cpfConsultar.substring(6, 9) + "-" + cpfConsultar.substring(9);
            }

            ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
            ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            boolean validarCpfDuplicado = configuracaoSistemaVO.isValidarCpfDuplicado();
            List<PessoaVO> pessoas = new ArrayList<>();
            if (pactoFlowVendaInternacional) {
                pessoas = pessoaDao.consultarPorPassaporte(vendaDTO.getPassaporte(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                pessoaDao = null;
            } else if (validarCpfDuplicado) {
                pessoas = pessoaDao.consultarPorCfp(cpfConsultar, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                pessoaDao = null;
            }


            List emails = consultarEmailExistente(vendaDTO.getEmail());
            if (validarCpfDuplicado && !UteisValidacao.emptyList(pessoas)) {
                //Aqui o autocommit será setado como true, ou seja tudo pra cima sera commitado, cuidado ao inserir algo.
                //Para o totem gravar email do visitante e nao retornar excecao
                validarInserirEmail(emails, vendaDTO.getEmail(), (PessoaVO) pessoas.get(0));
                emails = consultarEmailExistente(vendaDTO.getEmail());
            }
            //Validar se a pessoa existente corresponde á informada pelo site
            // valida se possui contrato ativo, email , data nascimento
            ClienteVO clienteVO = validarSobreporVisitanteExistente(pessoas, emails, vendaDTO.getDataNascimento(), vendaDTO.getEmail(), false);
            boolean alterarVisitanteExistente = clienteVO != null;
            PessoaVO pessoaVO;
            if (!alterarVisitanteExistente && pessoas.size() == 1) {
                pessoaVO = pessoas.get(0);
            } else {
                pessoaVO = new PessoaVO();
            }
            if (alterarVisitanteExistente) {
                pessoaVO.setCodigo(pessoas.get(0).getCodigo());
                pessoaVO = povoarTelefoneEnderecoVisitanteExistente(pessoaVO, "", vendaDTO.getTelefone(), vendaDTO.getEndereco(), vendaDTO.getComplemento(), vendaDTO.getNumero(), vendaDTO.getBairro(), vendaDTO.getCep());
            } else {
                pessoaVO = povoarTelefoneEndereco(pessoaVO, vendaDTO.getTelefone(), "", vendaDTO.getEndereco(), vendaDTO.getComplemento(), vendaDTO.getNumero(), vendaDTO.getBairro(), vendaDTO.getCep(), configuracaoSistemaVO);
            }
            pessoaVO.setNome(vendaDTO.getNome());
            boolean temDadosDoResponsavelMae = false;
            boolean temDadosDoResponsavelPai = false;
            if (!UteisValidacao.emptyString(vendaDTO.getResponsavelPai())){
                pessoaVO.setNomePai(vendaDTO.getResponsavelPai());
                pessoaVO.setCpfPai(vendaDTO.getCpfPai());
                temDadosDoResponsavelPai = true;
            }
            if (!UteisValidacao.emptyString(vendaDTO.getResponsavelMae())){
                pessoaVO.setNomeMae(vendaDTO.getResponsavelMae());
                pessoaVO.setCpfMae(vendaDTO.getCpfMae());
                temDadosDoResponsavelMae = true;
            }
            if (codigoPais != null) {
                pessoaVO.setPais(new PaisVO());
                pessoaVO.getPais().setCodigo(codigoPais);
            }
            if (codigoCidade != null) {
                pessoaVO.setCidade(new CidadeVO());
                pessoaVO.getCidade().setCodigo(codigoCidade);
            }
            if (codigoEstado != null) {
                pessoaVO.setEstadoVO(new EstadoVO());
                pessoaVO.getEstadoVO().setCodigo(codigoEstado);
            }
            if (!alterarVisitanteExistente) {
                EmailVO emailVO = new EmailVO();
                emailVO.setEmail(vendaDTO.getEmail());
                emailVO.setEmailCorrespondencia(true);
                pessoaVO.getEmailVOs().add(emailVO);
            }
            if (!UteisValidacao.emptyString(vendaDTO.getRg())){
                pessoaVO.setRg(vendaDTO.getRg());
            }
            if(pactoFlowVendaInternacional) {
                pessoaVO.setPassaporte(vendaDTO.getPassaporte());
            }

            if ((ignoraCpf == null || !ignoraCpf) && configuracaoSistemaVO.isCpfValidar() && !configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS).isUsarSistemaInternacional() && !pactoFlowVendaInternacional) {
                boolean cpfValido = UteisValidacao.isValidCPF(vendaDTO.getCpf());
                if (cpfValido) {
                    pessoaVO.setCfp(vendaDTO.getCpf());
                } else if (!temDadosDoResponsavelPai && !temDadosDoResponsavelMae) { //pode ser que o aluno não preencha o cpf dele mas preencha do responsável quando é menor de idade.
                    throw new Exception("CPF inválido.");
                }
            } else {
                pessoaVO.setCfp(vendaDTO.getCpf());
            }
            configuracaoSistema = null;
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());

            if ((vendaDTO.getSexo() != null) && (!vendaDTO.getSexo().trim().equals(""))) {
                pessoaVO.setSexo(vendaDTO.getSexo());
            }

            if ((vendaDTO.getDataNascimento() != null) && (!vendaDTO.getDataNascimento().trim().equals("")) && (Calendario.getDate("dd/MM/yyyy", vendaDTO.getDataNascimento()).after(Calendario.hoje()))) {
                throw new Exception("Data de Nascimento superior a data atual.");
            }

            if (!UteisValidacao.emptyString(vendaDTO.getDataNascimento())) {
                pessoaVO.setDataNasc(Calendario.getDate("dd/MM/yyyy", vendaDTO.getDataNascimento()));
            }

            ColaboradorVO colaboradorSite = null;
            if (!UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel())) {
                Colaborador colaboradorDAO = new Colaborador(con);
                colaboradorSite = colaboradorDAO.consultarPorCodigoUsuario(vendaDTO.getUsuarioResponsavel(), empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                    //se não encontrou com a empresa, buscar sem a empresa
                    colaboradorSite = colaboradorDAO.consultarPorCodigoUsuario(vendaDTO.getUsuarioResponsavel(), 0, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                        colaboradorSite = null;
                    }
                }
                colaboradorDAO = null;
            }
            if (!UteisValidacao.emptyNumber(vendaDTO.getCodigoColaborador())) {
                Colaborador colaboradorDAO = new Colaborador(con);
                colaboradorSite = colaboradorDAO.consultarPorCodigo(vendaDTO.getCodigoColaborador(), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                colaboradorDAO = null;
            }
            if (colaboradorSite == null) {
                VendasConfig vendasConfigs = new VendasConfig(con);
                colaboradorSite = vendasConfigs.config(empresaVO.getCodigo()).getConsultorSite();
                vendasConfigs = null;
            }

        try {
            //con.setAutoCommit(false);
            if (UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                colaboradorSite = new ColaboradorVO();
                TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
                tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());

                colaboradorSite.getPessoa().setNome("COLABORADOR SITE");
                colaboradorSite.getPessoa().setDataNasc(Calendario.ontem());
                colaboradorSite.setEmpresa(empresaVO);
                colaboradorSite.setSituacao("AT");
                colaboradorSite.setDiaVencimento(1);
                colaboradorSite.setPorcComissao(1.0);

                colaboradorSite.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
                Colaborador colaborador = new Colaborador(con);
                colaboradorSite = colaborador.criarOuConsultarSeExistePorNome(colaboradorSite, true);
                colaborador = null;
            }

            clienteVO = clienteVO == null ? new ClienteVO() : clienteVO;
            adicionarConsultor(clienteVO, colaboradorSite);
            pessoaVO.realizarUpperCaseDados();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);
            if(!UteisValidacao.emptyNumber(vendaDTO.getCodigoCategoriaPlano())){
                clienteVO.getCategoria().setCodigo(vendaDTO.getCodigoCategoriaPlano());
            }

            AtomicBoolean clienteParqPositivo = new AtomicBoolean(false);
            if (isNotBlank(vendaDTO.getRespostaParqJson())) {
                JSONObject respostas = new JSONObject(vendaDTO.getRespostaParqJson());
                respostas.keySet().forEach(key -> {
                    if (key.toString().contains("-1") && new JSONObject(vendaDTO.getRespostaParqJson()).getBoolean(key.toString())) {
                        clienteParqPositivo.set(true);
                    }
                });
            }
            clienteVO.setParqPositivo(clienteParqPositivo.get());

            if (!alterarVisitanteExistente) {
                cli.gerarNumeroMatricula(clienteVO, empresaVO, null);
                boolean matriculaDuplicada = true;
                int contador = 1;
                while (matriculaDuplicada && contador <= 10) {
                    try {
                        cli.validarMatricula(clienteVO);
                        matriculaDuplicada = false;
                        cli.incluirClienteSimplificado(clienteVO, false);
                        contador = 10;
                    } catch (Exception e) {
                        matriculaDuplicada = true;
                        cli.atualizarMatriculaAluno(clienteVO.getCodigoMatricula());
                        clienteVO.setMatricula("");
                        cli.gerarNumeroMatricula(clienteVO, clienteVO.getEmpresa(), null);
                        contador++;
                    }
                }

                if(matriculaDuplicada){
                    numeroMatricula = clienteVO.getCodigoMatricula();

                    if(clienteVO != null && clienteVO.getPessoa() != null && !UteisValidacao.emptyList(clienteVO.getPessoa().getTelefoneVOs())) {
                        for(TelefoneVO tel : clienteVO.getPessoa().getTelefoneVOs()) {
                            if(!UteisValidacao.validaTelefone(tel.getNumero())) {
                                throw new Exception("O telefone informado " + tel.getNumero() + " é inválido. Por favor, verifique as informações e tente novamente.");
                            }
                        }
                    }else{
                        throw new Exception("Desculpe, ocorreu uma falha ao gerar o número de matrícula. Por favor, tente novamente.");
                    }
                }
            } else {
                cli.alterarClienteSimplesSite(clienteVO, configuracaoSistemaVO, false);
            }
            cli = null;

            retorno = "Cliente cadastrado com sucesso.";
            if(atualizarSintetico) {
                try {
                    ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                    zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                    zwFacade = null;
                } catch (SinteticoException se) {
                    Uteis.logar(null, "Problema ao processar sintético do cliente: " + clienteVO.getCodigo() + " - Empresa: " + empresaVO.getNome());
                }
            }

            Log logDao = new Log(con);
            logDao.incluirLogInclusaoClienteSite(clienteVO, usuarioResponsavel);
            logDao = null;

            try {
                if (empresaVO.isNotificarWebhook()) {
                    ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                    zwFacade.notificarCadastro(clienteVO, usuarioResponsavel);
                    zwFacade = null;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            //con.commit();
            return retorno + " | CodCliente " + clienteVO.getCodigo();
        } catch (Exception e) {
            gravarLogTentativaCadastroClienteVendas(vendaDTO, e);
            //con.rollback();
            //con.setAutoCommit(true);
            cli.atualizarMatriculaAluno(numeroMatricula);
            cli = null;
            return "ERRO: " + e.getMessage();
        } finally {
            //con.setAutoCommit(true);
        }
    }

    public String incluirClientesDependentesSite(VendaDTO vendaDTO, UsuarioVO usuarioResponsavel) throws Exception {
        String retorno = "";
        Cliente cli = new Cliente(con);
        int numeroMatricula = 0;
        try {
            con.setAutoCommit(false);
            validarDados(vendaDTO);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresaDao = null;

            //preencher codigoPais, codigoCidade e codigoEstado com base nos dados da Empresa
            Integer codigoPais = empresaVO.getPais().getCodigo() != 0 ? empresaVO.getPais().getCodigo() : null;
            Integer codigoCidade = empresaVO.getCidade().getCodigo() != 0 ? empresaVO.getCidade().getCodigo() : null;
            Integer codigoEstado = empresaVO.getEstado().getCodigo() != 0 ? empresaVO.getEstado().getCodigo() : null;

            //preencher codigoPais, codigoCidade e codigoEstado com base no CEP informado no ato da Venda
            if (!UteisValidacao.emptyString(vendaDTO.getCep())) {
                try {
                    //Dentro do objeto cidadeVO, j? tem pa?s e estado
                    CidadeVO cidadeVO = new Cidade(con).obterCidadeCEP(vendaDTO.getCep());
                    if (cidadeVO != null) {
                        codigoCidade = cidadeVO.getCodigo();
                        codigoEstado = cidadeVO.getEstado().getCodigo();
                        codigoPais = cidadeVO.getPais().getCodigo();
                    }
                } catch (Exception ignore) {
                }
            }

            for(ClienteDependenteDTO clienteDependenteDTO : vendaDTO.getClientesCadastradosComoDependentesPlanoCompartilhado()) {
                Pessoa pessoaDao = new Pessoa(con);
                String cpfConsultar = clienteDependenteDTO.getCpf();

                if (cpfConsultar.length() == 11) {
                    cpfConsultar = cpfConsultar.substring(0, 3) + "." + cpfConsultar.substring(3, 6) + "." + cpfConsultar.substring(6, 9) + "-" + cpfConsultar.substring(9);
                }
                ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
                ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                List<PessoaVO> pessoas = pessoaDao.consultarPorCfp(cpfConsultar, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                pessoaDao = null;

                List emails = consultarEmailExistente(clienteDependenteDTO.getEmail());

                if (!UteisValidacao.emptyList(pessoas)) {
                    //Aqui o autocommit ser? setado como true, ou seja tudo pra cima sera commitado, cuidado ao inserir algo.
                    //Para o totem gravar email do visitante e nao retornar excecao
                    validarInserirEmail(emails, clienteDependenteDTO.getEmail(), pessoas.get(0));
                    emails = consultarEmailExistente(clienteDependenteDTO.getEmail());
                }

                //Validar se a pessoa existente corresponde ? informada pelo site
                // valida se possui contrato ativo, email , data nascimento
                ClienteVO clienteVO = validarSobreporVisitanteExistente(pessoas, emails, clienteDependenteDTO.getDataNascimento(), clienteDependenteDTO.getEmail(), true);
                boolean alterarVisitanteExistente = clienteVO != null;
                PessoaVO pessoaVO;

                if (!alterarVisitanteExistente && pessoas.size() == 1) {

                    pessoaVO = pessoas.get(0);
                } else {
                    pessoaVO = new PessoaVO();
                }

                if (alterarVisitanteExistente) {
                    pessoaVO.setCodigo((pessoas.get(0)).getCodigo());
                    pessoaVO = povoarTelefoneEnderecoVisitanteExistente(
                            pessoaVO, "",
                            clienteDependenteDTO.getTelefone(),
                            clienteDependenteDTO.getEndereco(),
                            clienteDependenteDTO.getComplemento(),
                            clienteDependenteDTO.getNumero(),
                            clienteDependenteDTO.getBairro(),
                            clienteDependenteDTO.getCep()
                    );
                } else {
                    pessoaVO = povoarTelefoneEndereco(pessoaVO,
                            clienteDependenteDTO.getTelefone(),
                            "", clienteDependenteDTO.getEndereco(),
                            clienteDependenteDTO.getComplemento(), clienteDependenteDTO.getNumero(),
                            clienteDependenteDTO.getBairro(),
                            clienteDependenteDTO.getCep(),
                            configuracaoSistemaVO);
                }
                pessoaVO.setNome(clienteDependenteDTO.getNome());
                if (!UteisValidacao.emptyString(clienteDependenteDTO.getResponsavelPai())){
                    pessoaVO.setNomePai(clienteDependenteDTO.getResponsavelPai());
                    pessoaVO.setCpfPai(clienteDependenteDTO.getCpfPai());
                }
                if (!UteisValidacao.emptyString(clienteDependenteDTO.getResponsavelMae())){
                    pessoaVO.setNomeMae(clienteDependenteDTO.getResponsavelMae());
                    pessoaVO.setCpfMae(clienteDependenteDTO.getCpfMae());
                }
                if (codigoPais != null) {
                    pessoaVO.setPais(new PaisVO());
                    pessoaVO.getPais().setCodigo(codigoPais);
                }
                if (codigoCidade != null) {
                    pessoaVO.setCidade(new CidadeVO());
                    pessoaVO.getCidade().setCodigo(codigoCidade);
                }
                if (codigoEstado != null) {
                    pessoaVO.setEstadoVO(new EstadoVO());
                    pessoaVO.getEstadoVO().setCodigo(codigoEstado);
                }
                if (!alterarVisitanteExistente) {
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(clienteDependenteDTO.getEmail());
                    emailVO.setEmailCorrespondencia(true);
                    pessoaVO.getEmailVOs().add(emailVO);
                }
                if (!UteisValidacao.emptyString(vendaDTO.getRg())){
                    pessoaVO.setRg(vendaDTO.getRg());
                }
                /////
                if (configuracaoSistemaVO.isCpfValidar() && !configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS).isUsarSistemaInternacional()) {
                    if (UteisValidacao.isValidCPF(clienteDependenteDTO.getCpf())) {
                        pessoaVO.setCfp(clienteDependenteDTO.getCpf());
                    } else {
                        throw new Exception("CPF inv?lido.");
                    }
                } else {
                    pessoaVO.setCfp(clienteDependenteDTO.getCpf());
                }
                configuracaoSistema = null;
                pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());

                if ((clienteDependenteDTO.getSexo() != null) && (!clienteDependenteDTO.getSexo().trim().equals(""))) {
                    pessoaVO.setSexo(clienteDependenteDTO.getSexo());
                }

                if ((clienteDependenteDTO.getDataNascimento() != null) && (!clienteDependenteDTO.getDataNascimento().trim().equals("")) && (Calendario.getDate("dd/MM/yyyy", clienteDependenteDTO.getDataNascimento()).after(Calendario.hoje()))) {
                    throw new Exception("Data de Nascimento superior a data atual.");
                }

                if (!UteisValidacao.emptyString(clienteDependenteDTO.getDataNascimento())) {
                    pessoaVO.setDataNasc(Calendario.getDate("dd/MM/yyyy", clienteDependenteDTO.getDataNascimento()));
                }

                ColaboradorVO colaboradorSite = null;
                if (!UteisValidacao.emptyNumber(vendaDTO.getUsuarioResponsavel())) {
                    Colaborador colaboradorDAO = new Colaborador(con);
                    colaboradorSite = colaboradorDAO.consultarPorCodigoUsuario(vendaDTO.getUsuarioResponsavel(), empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                        //se n?o encontrou com a empresa, buscar sem a empresa
                        colaboradorSite = colaboradorDAO.consultarPorCodigoUsuario(vendaDTO.getUsuarioResponsavel(), 0, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        if (UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                            colaboradorSite = null;
                        }
                    }
                    colaboradorDAO = null;
                }
                if (!UteisValidacao.emptyNumber(vendaDTO.getCodigoColaborador())) {
                    Colaborador colaboradorDAO = new Colaborador(con);
                    colaboradorSite = colaboradorDAO.consultarPorCodigo(vendaDTO.getCodigoColaborador(), 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    colaboradorDAO = null;
                }
                if (colaboradorSite == null) {
                    VendasConfig vendasConfigs = new VendasConfig(con);
                    colaboradorSite = vendasConfigs.config(empresaVO.getCodigo()).getConsultorSite();
                    vendasConfigs = null;
                }
                if (UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                    colaboradorSite = new ColaboradorVO();
                    TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
                    tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());

                    colaboradorSite.getPessoa().setNome("COLABORADOR SITE");
                    colaboradorSite.getPessoa().setDataNasc(Calendario.ontem());
                    colaboradorSite.setEmpresa(empresaVO);
                    colaboradorSite.setSituacao("AT");
                    colaboradorSite.setDiaVencimento(1);
                    colaboradorSite.setPorcComissao(1.0);

                    colaboradorSite.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
                    Colaborador colaborador = new Colaborador(con);
                    colaboradorSite = colaborador.criarOuConsultarSeExistePorNome(colaboradorSite, true);
                    colaborador = null;
                }
                //////////////
                clienteVO = clienteVO == null ? new ClienteVO() : clienteVO;
                adicionarConsultor(clienteVO, colaboradorSite);
                pessoaVO.realizarUpperCaseDados();
                clienteVO.setPessoa(pessoaVO);
                clienteVO.setEmpresa(empresaVO);
                if(!UteisValidacao.emptyNumber(vendaDTO.getCodigoCategoriaPlano())){
                    clienteVO.getCategoria().setCodigo(vendaDTO.getCodigoCategoriaPlano());
                }

                AtomicBoolean clienteParqPositivo = new AtomicBoolean(false);
                if (isNotBlank(vendaDTO.getRespostaParqJson())) {
                    JSONObject respostas = new JSONObject(vendaDTO.getRespostaParqJson());
                    respostas.keySet().forEach(key -> {
                        if (key.toString().contains("-1") && new JSONObject(vendaDTO.getRespostaParqJson()).getBoolean(key.toString())) {
                            clienteParqPositivo.set(true);
                        }
                    });
                }
                clienteVO.setParqPositivo(clienteParqPositivo.get());

                if (!alterarVisitanteExistente) {
                    cli.gerarNumeroMatricula(clienteVO, empresaVO, null);
                    boolean matriculaDuplicada = true;
                    int contador = 1;
                    while (matriculaDuplicada && contador <= 10) {
                        try {
                            cli.validarMatricula(clienteVO);
                            matriculaDuplicada = false;
                            cli.incluirClienteSimplificado(clienteVO, false);
                            contador = 10;
                        } catch (Exception e) {
                            matriculaDuplicada = true;
                            cli.atualizarMatriculaAluno(clienteVO.getCodigoMatricula());
                            clienteVO.setMatricula("");
                            cli.gerarNumeroMatricula(clienteVO, clienteVO.getEmpresa(), null);
                            contador++;
                        }
                    }
                    if(matriculaDuplicada){
                        numeroMatricula = clienteVO.getCodigoMatricula();
                        throw new Exception("Desculpe, ocorreu uma falha ao gerar o n?mero de matr?cula. Por favor, tente novamente.");
                    } // linha 510: inclui dependente
                } else {
                    cli.alterarClienteSimplesSite(clienteVO, configuracaoSistemaVO, false);
                }

                retorno = "Dependente cadastrado com sucesso.";


                try {
                    ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                    zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                } catch (SinteticoException se) {
                    Uteis.logar(null, "Problema ao processar sint?tico do cliente: " + clienteVO.getCodigo() + " - Empresa: " + empresaVO.getNome());
                }

                Log logDao = new Log(con);
                logDao.incluirLogInclusaoClienteSite(clienteVO, usuarioResponsavel);

                try {
                    if (empresaVO.isNotificarWebhook()) {
                        ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                        zwFacade.notificarCadastro(clienteVO, usuarioResponsavel);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            con.commit();

            return retorno;

        } catch (Exception e) {
            gravarLogTentativaCadastroClienteVendas(vendaDTO, e);
            con.rollback();
            cli.atualizarMatriculaAluno(numeroMatricula);
            return "ERRO INCLUINDO DEPENDENTES: " + e.getMessage();
        } finally {
            con.setAutoCommit(true);
        }
    }


    public ClienteVO incluirClienteCNPJ(VendaDTO vendaDTO, UsuarioVO usuarioResponsavel, boolean pactoStore) throws Exception {
        try {
            con.setAutoCommit(false);
            validarDados(vendaDTO);

            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            empresaDao = null;

            Pessoa pessoaDao = new Pessoa(con);
            List<PessoaVO> pessoas = pessoaDao.consultarPorCNPJ(vendaDTO.getCnpj(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            PessoaVO pessoaVO;
            if (pessoas.size() == 1) {
                pessoaVO = pessoas.get(0);
            } else {
                pessoaVO = new PessoaVO();
            }

            //endereço e telefone
            if (!pactoStore) {
                if (!UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                    pessoaVO = povoarTelefoneEnderecoVisitanteExistente(pessoaVO, "", vendaDTO.getTelefone(), vendaDTO.getEndereco(), vendaDTO.getComplemento(), vendaDTO.getNumero(), vendaDTO.getBairro(), vendaDTO.getCep());
                } else {
                    ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
                    ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    pessoaVO = povoarTelefoneEndereco(pessoaVO, vendaDTO.getTelefone(), "", vendaDTO.getEndereco(), vendaDTO.getComplemento(), vendaDTO.getNumero(), vendaDTO.getBairro(), vendaDTO.getCep(), configuracaoSistemaVO);
                }
            }

            pessoaVO.setNome(vendaDTO.getNome());
            pessoaVO.setCnpj(Uteis.formatarCpfCnpj(vendaDTO.getCnpj(), false));
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setCategoriaPessoa(TipoPessoa.JURIDICA);
            pessoaVO.setNomeResponsavelEmpresa(vendaDTO.getNomeResponsavelEmpresa());
            pessoaVO.setCpfResponsavelEmpresa(vendaDTO.getCpfResponsavelEmpresa());

            //email
            if (!UteisValidacao.emptyString(vendaDTO.getEmail())) {
                EmailVO emailVO = new EmailVO();
                emailVO.setEmail(vendaDTO.getEmail());
                emailVO.setEmailCorrespondencia(true);
                pessoaVO.getEmailVOs().add(emailVO);
            }

            if (!UteisValidacao.emptyString(vendaDTO.getDataNascimento())) {
                pessoaVO.setDataNasc(Calendario.getDate("dd/MM/yyyy", vendaDTO.getDataNascimento()));
            }

            ColaboradorVO colaboradorSite = null;
            if (!UteisValidacao.emptyNumber(vendaDTO.getCodigoColaborador())) {
                Colaborador colaborador = new Colaborador(con);
                colaboradorSite = colaborador.consultarPorCodigo(vendaDTO.getCodigoColaborador(), 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                colaborador = null;

                if (vendaDTO.isVendaConsultor()) {
                    if (colaboradorSite == null) {
                        throw new ServiceException("O colaborador de código " + vendaDTO.getCodigoColaborador() + " não não existe.");
                    }
                }

                if (colaboradorSite != null && colaboradorSite.isInativo()) {
                    throw new ServiceException("O colaborador " + colaboradorSite.getPessoa().getNome() + " não pode vender planos porque seu cadastro está inativo.");
                }
            }

            if (colaboradorSite == null) {
                VendasConfig vendasConfigsDAO = new VendasConfig(con);
                colaboradorSite = vendasConfigsDAO.config(empresaVO.getCodigo()).getConsultorSite();
                vendasConfigsDAO = null;
            }

            if (UteisValidacao.emptyNumber(colaboradorSite.getCodigo())) {
                colaboradorSite = new ColaboradorVO();
                TipoColaboradorVO tipoColaboradorVO = new TipoColaboradorVO();
                tipoColaboradorVO.setDescricao(TipoColaboradorEnum.CONSULTOR.getSigla());
                colaboradorSite.getPessoa().setNome("COLABORADOR SITE");
                colaboradorSite.getPessoa().setDataNasc(Calendario.ontem());
                colaboradorSite.setEmpresa(empresaVO);
                colaboradorSite.setSituacao("AT");
                colaboradorSite.setDiaVencimento(1);
                colaboradorSite.setPorcComissao(1.0);

                colaboradorSite.getListaTipoColaboradorVOs().add(tipoColaboradorVO);
                Colaborador colaboradorDAO = new Colaborador(con);
                colaboradorSite = colaboradorDAO.criarOuConsultarSeExistePorNome(colaboradorSite, true);
                colaboradorDAO = null;
            }


            //cliente
            Cliente clienteDAO = new Cliente(con);
            ClienteVO clienteVO = new ClienteVO();
            if (!UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            adicionarConsultor(clienteVO, colaboradorSite);
            pessoaVO.realizarUpperCaseDados();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setEmpresa(empresaVO);

            //novo cliente
            if (UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                clienteDAO.gerarNumeroMatricula(clienteVO, empresaVO, null);
                clienteDAO.incluirClienteSimplificado(clienteVO, false);
            } else {
                ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
                ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                clienteDAO.alterarClienteSimplesSite(clienteVO, configuracaoSistemaVO, false);
                configuracaoSistemaDAO = null;
            }
            clienteDAO = null;
            con.setAutoCommit(false);

            try {
                ZillyonWebFacade zwFacade = new ZillyonWebFacade(con);
                zwFacade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                zwFacade = null;
            } catch (SinteticoException se) {
                Uteis.logar(null, "Problema ao processar sintético do cliente: " + clienteVO.getCodigo() + " - Empresa: " + empresaVO.getNome());
            }

            Log logDao = new Log(con);
            logDao.incluirLogInclusaoClienteSite(clienteVO, usuarioResponsavel);
            logDao = null;

            con.commit();
            return clienteVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            gravarLogTentativaCadastroClienteVendas(vendaDTO, ex);
            con.rollback();
            con.setAutoCommit(true);
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void validarDados(VendaDTO vendaDTO) throws Exception {
        if (UteisValidacao.emptyString(Uteis.getSobrenome(vendaDTO.getNome()))) {
            throw new Exception("Informe seu sobrenome");
        }
    }

    private void gravarLogTentativaCadastroClienteVendas(VendaDTO vendaDTO, Exception ex) {
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("PERSISTIR CLIENTE");
            log.setChavePrimaria("");
            log.setNomeEntidade("CLIENTE");
            log.setValorCampoAnterior("");

            String motivo = ex.getMessage();
            try {
                StringBuilder result = new StringBuilder(ex.toString() + "\n");
                StackTraceElement[] trace = ex.getStackTrace();
                for (StackTraceElement stackTraceElement : trace) {
                    result.append(stackTraceElement.toString()).append("\n");
                }
                motivo = result.toString();
            } catch (Exception ignored) {
            }

            log.setValorCampoAlterado("EMPRESA: ".concat("" + vendaDTO.getUnidade()).concat(";CLIENTE: ").concat(vendaDTO.getNome()).concat(";EMAIL: ").concat(vendaDTO.getEmail()).concat(";MOTIVO: ").concat(motivo));
            log.setOperacao("FALHA_INCLUSAO_CLIENTE_VENDAS_ONLINE");
            log.setResponsavelAlteracao("VENDAS_ONLINE");
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception e) {
            Uteis.logar(null, "FALHA AO INCLUIR LOG DA FALHA DE INCLUSÃO DE CLIENTE ATRÁVES DO VENDAS ONLINE");
        }
    }

    private List consultarEmailExistente(String email) throws Exception {
        Email emailDao = new Email(con);
        List emails = emailDao.consultarEmailExiste(email, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        emailDao = null;
        return emails;
    }

    private void validarInserirEmail(List emails, String email, PessoaVO pessoa) throws Exception {
        if (emails.size() == 0 && StringUtils.isNotBlank(email)) {
            gravarEmail(pessoa, true, email);
        }
    }

    private void gravarEmail(PessoaVO pessoa, boolean emailcorrespondencia, String email) throws Exception {
        con.setAutoCommit(true);
        Email emailDao = new Email(con);
        EmailVO emailVO = new EmailVO();
        emailVO.setPessoa(pessoa.getCodigo());
        emailVO.setEmailCorrespondencia(emailcorrespondencia);
        emailVO.setEmail(email);
        emailDao.incluir(emailVO);
        emailDao = null;
        con.setAutoCommit(false);
    }

    private ClienteVO validarSobreporVisitanteExistente(List pessoas, List emails, String dataNascimento, String email,
                                                        Boolean cadastrandoDependenteVendasOnline) throws Exception {
        if (!UteisValidacao.emptyList(pessoas)) {
            PessoaVO pessoa = (PessoaVO) pessoas.get(0);
            Cliente clienteDao = new Cliente(con);
            Contrato contratoDao = new Contrato(con);
            ClienteVO clienteVO = clienteDao.consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contrato = contratoDao.consultarContratoVigentePorPessoa(pessoa.getCodigo(),
                    false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDao = null;

            if(cadastrandoDependenteVendasOnline) {
                return clienteVO;
            }
            if (clienteVO.getSituacao().equals(SituacaoClienteEnum.VISITANTE.getCodigo()) && UteisValidacao.emptyNumber(contrato.getCodigo())) {
                if (!UteisValidacao.emptyString(dataNascimento) &&
                        Calendario.getDataComHoraZerada(pessoa.getDataNasc()).equals(Calendario.getDataComHoraZerada(Uteis.getDate(dataNascimento)))) {
                    for (Object o : emails) {
                        EmailVO emailVO = (EmailVO) o;
                        if (emailVO.getEmail().equalsIgnoreCase(email) && clienteVO != null && !UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                            clienteVO = clienteDao.consultarPorChavePrimaria(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                            clienteDao = null;
                            return clienteVO;
                        }
                    }
                }
            }

        }
        return null;
    }


    private PessoaVO povoarTelefoneEnderecoVisitanteExistente(PessoaVO pessoaExistente, String telResidencial, String telCelular,
                                                              final String endereco, final String complemento,
                                                              final String numero, final String bairro, final String cep) throws Exception {
        Pessoa pessoaDao = new Pessoa(con);
        pessoaExistente = pessoaDao.consultarPorChavePrimaria(pessoaExistente.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean telefoneComercialExistente = false;
        boolean telefoneResidencialExistente = false;
        for (TelefoneVO telefone : pessoaExistente.getTelefoneVOs()) {
            if (telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo()) && !UteisValidacao.emptyString(telCelular)) {
                telefoneComercialExistente = true;
                telefone.setNumero(telCelular);
            } else if (telefone.getTipoTelefone().equals(TipoTelefoneEnum.RESIDENCIAL.getCodigo()) && !UteisValidacao.emptyString(telResidencial)) {
                telefoneResidencialExistente = true;
                telefone.setNumero(telResidencial);
            }
        }
        ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!telefoneComercialExistente) {
            povoarTelefonePessoa(pessoaExistente.getTelefoneVOs(), telCelular, TipoTelefoneEnum.CELULAR, "", configuracaoSistemaVO);
        }
        if (!telefoneResidencialExistente) {
            povoarTelefonePessoa(pessoaExistente.getTelefoneVOs(), telResidencial, TipoTelefoneEnum.RESIDENCIAL, "", configuracaoSistemaVO);
        }
        boolean enderecoExistente = false;
        for (EnderecoVO enderecoVO : pessoaExistente.getEnderecoVOs()) {
            if (enderecoVO.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo()) && !UteisValidacao.emptyString(endereco)) {
                enderecoExistente = true;
                enderecoVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
                enderecoVO.setEndereco(endereco);
                enderecoVO.setComplemento(complemento);
                enderecoVO.setNumero(numero);
                enderecoVO.setBairro(bairro);
                enderecoVO.setCep(cep);
                break;
            }
        }
        if (!enderecoExistente) {
            povoarEndereco(pessoaExistente.getEnderecoVOs(), endereco, complemento, numero, bairro, cep);
        }
        return pessoaExistente;
    }

    private void povoarTelefonePessoa(List<TelefoneVO> telefones, String numero, TipoTelefoneEnum tipo, String descricao, ConfiguracaoSistemaVO configuracaoSistemaVO) {
        if (UteisValidacao.emptyString(numero)) {
            return;
        }
        TelefoneVO telefoneVO = new TelefoneVO();
        telefoneVO.setTipoTelefone(tipo.getCodigo());
        if (configuracaoSistemaVO.isUsarSistemaInternacional()) {
            telefoneVO.setUsarSistemaInternacional(configuracaoSistemaVO.isUsarSistemaInternacional());
            telefoneVO.setNumero(TelefoneVO.aplicarMascara(numero, configuracaoSistemaVO.getMascaraTelefone()));
        } else {
            telefoneVO.setNumero(Formatador.formataTelefoneZW(numero));
        }
        telefoneVO.setDescricao(descricao);
        telefones.add(telefoneVO);
    }

    private void povoarEndereco(List<EnderecoVO> enderecoVOS, final String endereco, final String complemento,
                                final String numero, final String bairro, final String cep) {
        if (UteisValidacao.emptyString(endereco)) {
            return;
        }
        EnderecoVO enderecoVO = new EnderecoVO();
        enderecoVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
        enderecoVO.setEndereco(endereco);
        enderecoVO.setComplemento(complemento);
        enderecoVO.setNumero(numero);
        enderecoVO.setBairro(bairro);
        enderecoVO.setCep(cep);
        enderecoVO.setEnderecoCorrespondencia(true);
        enderecoVOS.add(enderecoVO);
    }

    private PessoaVO povoarTelefoneEndereco(PessoaVO pessoa, String telCelular, String telComercial, final String endereco, final String complemento,
                                            final String numero, final String bairro, final String cep, ConfiguracaoSistemaVO configuracaoSistemaVO) throws Exception {
        povoarTelefonePessoa(pessoa.getTelefoneVOs(), telCelular, TipoTelefoneEnum.CELULAR, "", configuracaoSistemaVO);
        povoarTelefonePessoa(pessoa.getTelefoneVOs(), telComercial, TipoTelefoneEnum.RESIDENCIAL, "", configuracaoSistemaVO);
        povoarEndereco(pessoa.getEnderecoVOs(), endereco, complemento, numero, bairro, cep);
        return pessoa;
    }

    private void adicionarConsultor(ClienteVO cliente, ColaboradorVO consultor) {
        if (cliente != null && !UteisValidacao.emptyList(cliente.getVinculoVOs())) {
            for (VinculoVO vinculo : cliente.getVinculoVOs()) {
                if (vinculo.getTipoColaboradorVinculo().equals(TipoColaboradorEnum.CONSULTOR)) {
                    vinculo.setColaborador(consultor);
                    return;
                }
            }
        }
        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setColaborador(consultor);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
        cliente.getVinculoVOs().add(vinculoVO);
    }
}
