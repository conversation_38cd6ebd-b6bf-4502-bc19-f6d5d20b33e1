package servicos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoCorrigirMetasCRMNaoAtingidas;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.enumeradores.TipoClienteRestricaoEnum;
import br.com.pactosolucoes.integracao.financeiro.IntegracaoFinanceiroJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import controle.arquitetura.RoboControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.financeiro.GestaoNFCeControle;
import controle.financeiro.GestaoNotasControle;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.enums.TipoOperacaoIntegracaoMembersEnum;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoIntegracaoFogueteVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaConfigEstacionamentoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.LogIntegracoesVO;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.OperacaoColetivaVO;
import negocio.comuns.contrato.StatusOperacaoColetivaEnum;
import negocio.comuns.contrato.TipoOperacaoColetivaEnum;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.nfe.DocumentoFiscalRelatorioPeriodoTO;
import negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.acesso.IntegracaoMember;
import negocio.facade.jdbc.arquitetura.ResultadoServicos;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.AulaDesmarcada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.ClienteRestricao;
import negocio.facade.jdbc.basico.ConfiguracaoNotaFiscal;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.contrato.OperacaoColetiva;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.RedeEmpresaVO;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.acesso.IntegracaoMemberService;
import servicos.adm.IntegracaoEstacionamento;
import servicos.adm.IntegracaoF360;
import servicos.dadosgerenciaispagamento.DadosGerenciaisPagamentoService;
import servicos.impl.admCoreMs.AdmCoreMsService;
import servicos.impl.admCoreMs.ClienteRedeEmpresaDTO;
import servicos.impl.admCoreMs.ClienteRestricaoDTO;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.integracao.IntegracaoNFSeWSConsumer;
import servicos.oamd.RedeEmpresaService;
import servicos.operacoes.BonusColetivoService;
import servicos.operacoes.DelsoftService;
import servicos.operacoes.MentorWebService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import test.simulacao.ProcessoSincronizarContratosClientesIntegracaoFoguete;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.util.Objects.isNull;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

/**
 * <AUTHOR>
 */
public class AdministrativoRunner {

    private static final SimpleDateFormat FORMATADOR_DATA = new SimpleDateFormat("dd/MM/yyyy");

    private static class RetornoAtivosVencidos {
        public int ativos;
        public int vencidos;
        public int agregadores;
        public int agregadores_2acessos;
        public int agregadores_3acessos;
        public int agregadores_4acessos;
        public int agregadores_5_ou_mais_acessos;
        public int checkinsAgregadores;

        public RetornoAtivosVencidos(int ativos, int vencidos, int agregadores, int agregadores_2acessos,
                                     int agregadores_3acessos, int agregadores_4acessos, int agregadores_5_ou_mais_acessos, int checkinsAgregadores) {
            this.ativos = ativos;
            this.vencidos = vencidos;
            this.agregadores = agregadores;
            this.agregadores_2acessos = agregadores_2acessos;
            this.agregadores_3acessos = agregadores_3acessos;
            this.agregadores_4acessos = agregadores_4acessos;
            this.agregadores_5_ou_mais_acessos = agregadores_5_ou_mais_acessos;
            this.checkinsAgregadores = checkinsAgregadores;
        }
    }

    public static void main(String[] args) {
        Uteis.debug = true;
        Uteis.logar("Entrou no Administrativo Runner");
        if (args.length == 0) {
            args = new String[]{"testeaa"};
        }
        if (args.length >= 1) {
            String chave = args[0];
            Connection con = null;
            try {
                DAO dao = new DAO();
                con = dao.obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(con);
                //atualizar banco de dados, se defasado
                LoginControle control = new LoginControle();
                control.setUsername("RECOR");
                control.setSenha(PropsService.getPropertyValue(PropsService.RECORRENCIA_USER_PASSWORD));
                control.setUserOamd("adm");
                control.login();
                control.atualizarBD();

                RoboControle roboControle = new RoboControle();
                roboControle.inicializarRobo();
                RoboVO robo = roboControle.getRobo();
                if (args.length >= 2) {
                    robo.setDia(FORMATADOR_DATA.parse(args[1]));
                    Calendario.dia = FORMATADOR_DATA.parse(args[1]);
                }
                deletarDadosBI(chave, con);
                processarAtualizacaoAtivosVencidosFinanceiro(chave, con);
                processarOperacoesColetivas(robo, dao, chave, con);

                processarEstacionamento(robo, chave, con);

                processarIntegracaoF360(robo, chave, con, null);

                processarRelatoriosNotas(robo, chave, con);

                processarDadosGerenciaisPagamento(robo.getDia(), chave);

                processarIntegracaoMentorWeb(robo, con);

                processarIntegracaoSesi(robo, chave, con);

                processarIntegracaoDelsoft(robo, con);

                processarAlunosPlanosVIP(robo, chave, con);

                processarCorrigirMetasCRMNaoAtingidas(robo, chave, con);

                processarClientesRestricoes(robo, chave, con);

                processarClientesRedeEmpresa(robo, chave, con);

                processarContratosClienteIntegracaoFoguete(robo, con, null);

                try {
                    Transacao transacao = new Transacao(con);
                    transacao.gerarReciboTransacoesSemRecibo();
                } catch (Exception e) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(" Não foi possível executar o processo de geração de recibo para transações sem recibo no dia ").append(Uteis.getData(robo.getDia()));
                    sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
                    sb.append(" - ERRO: ").append(e.getMessage());
                    Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.SEVERE,sb.toString() + " - " + chave, e);
                }

                try {
                    Logger.getLogger(AdministrativoRunner.class.getName()).log(
                            Level.INFO, " Excluíndo Jobs Antigos (Jenkins) em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
                    JenkinsService.limparJobsAntigos(con, chave);
                } catch (Exception ex) {
                    Uteis.logar(null, chave + " - Não foi possível excluir os agendamentos do Jenkins");
                }

                try {
                    Logger.getLogger(AdministrativoRunner.class.getName()).log(
                            Level.INFO, " Excluíndo Jobs Antigos PactoPay (Jenkins) em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
                    JenkinsService.limparJobsAntigosPactoPayComunicacao(con, chave);
                    JenkinsService.limparJobsAntigosPactoPayEnvioEmail(con, chave);
                } catch (Exception ex) {
                    Uteis.logar(null, chave + " - Não foi possível excluir os agendamentos do Jenkins PactoPay");
                }

                try {
                    if (PropsService.isTrue(PropsService.useBounceService)) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(
                                Level.INFO, " Verificar E-mails em Bounce e excluir no ZillyonWeb em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
                        BounceService.excluirTodos(chave);
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(
                                Level.INFO, " Finalizado E-mails em Bounce e excluir no ZillyonWeb em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
                    }
                } catch (Exception ex) {
                    Uteis.logar(null, chave + " - Não foi possível Executar Exclusão de E-mails em Bounce devido ao erro: " + ex.getMessage());
                }

                deletarHistoricoLogsTransacao(con);
                processarNotas(robo, chave, con);

                Logger.getLogger(AdministrativoRunner.class.getName()).log(
                        Level.INFO, " Finalizando AdministrativoRunner em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});

            } catch (Exception ex) {
                Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.SEVERE,
                        "Erro ao obter conexao especifica com a chave " + chave, ex);
            } finally {
                Uteis.finalizarExecutor(1);
            }

            if (chave.equals("cb81628c1605eea1d25a9bf96e2ae8")) { // INEEX
                try {
                    IntegracaoEstacionamento.gerarArquivoEnviarFTP(false, "academia.parkingplus.com.br", "ineex_poa", "1neex@2017", 22, "alunoEstacionamento.txt", con);
                } catch (Exception erro) {
                    Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.SEVERE,
                            "Erro ao gerar e enviar arquivo integração com estacionamento " + chave, erro);
                }
            }
        }
    }

    private static void deletarDadosBI(String chave, Connection con) throws SQLException {
        SuperFacadeJDBC.executarUpdate("delete from filtrobi", con);
    }

    private static List<FormaPagamentoVO> getFormasPagamento(FormaPagamento formaPagamentoDAO, String formasPagamentoEmissao) throws Exception {
        if (StringUtils.isBlank(formasPagamentoEmissao)) {
            return new ArrayList<FormaPagamentoVO>();
        }

        final String[] formasPagamento = formasPagamentoEmissao.replaceAll("\\\\", "").split("\\|");
        final List<FormaPagamentoVO> listaFormasPagamento = new ArrayList<FormaPagamentoVO>();

        for (String formaPagamento : formasPagamento) {
            listaFormasPagamento.addAll(formaPagamentoDAO.consultarPorTipoFormaPagamento(
                    formaPagamento,
                    true,
                    false,
                    false,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        return listaFormasPagamento;
    }

    public static void processarNotas(RoboVO robo, String chave, Connection con) {
        Uteis.logar(null, "INICIALIZA ENVIO DE NOTAS - em: " + Uteis.getDataComHora(Calendario.hoje()));
        LocalDateTime dateTimeStart = null;
        LocalDateTime dateTimeEnd = null;
        long dateTimeDiff;

        for (EmpresaVO empresa : robo.getListaEmpresa()) {
            if (!empresa.isAtiva()) {
                continue;
            }
            try {
                if (empresa.getUsarNFSe() && empresa.isEnviarNFSeAutomatico()) {
                    ResultadoServicosVO resultadoServicosVO = new ResultadoServicosVO(ServicoEnum.NFSE, empresa.getCodigo());
                    Uteis.logar(null,"Enviando Notas da Empresa " + empresa.getNome() + " em : " + Uteis.getDataComHora(Calendario.hoje()));

                    Date dataInicio = Uteis.somarDias(robo.getDia(), -1);
                    Date dataFinal = Uteis.somarDias(robo.getDia(), -1);

                    //SE FOR O ULTIMO DIA DO MES E FOR O PROCESSO A NOITE A DATA A SER FILTRADA DEVE SER DO DIA E NÃO DO DIA ANTERIOR
                    Integer hora = Uteis.gethoraHH(Calendario.hoje());
                    if (hora >= 17 && (Uteis.obterDiaData(robo.getDia()) == Uteis.obterDiaData(Uteis.obterUltimoDiaMes(robo.getDia())))) {
                        Uteis.logar(null, "ULTIMO DIA DO MÊS - APÓS AS 17 HORAS - FILTRAR DADOS DO DIA - " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()));
                        dataInicio = robo.getDia();
                        dataFinal = robo.getDia();
                    }

                    //SE FOR O PRIMEIRO DIA DO MES FILTRAR OS DADOS DO DIA
                    if (Uteis.obterDiaData(robo.getDia()) == 1) {
                        Uteis.logar(null, "PRIMEIRO DIA DO MES - FILTRAR DADOS DO DIA - " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()));
                        dataInicio = robo.getDia();
                        dataFinal = robo.getDia();
                    }

                    if (empresa.isConsultarDiasAnterioresNFSe()) {
                        dataInicio = Uteis.obterPrimeiroDiaMes(dataInicio);
                    }

                    GestaoNotasControle controle = new GestaoNotasControle();
                    FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
                    List<FormaPagamentoVO> formasPagamento = new ArrayList<>();
                    formasPagamento.addAll(formaPagamentoDAO.consultarPorTipoFormaPagamento(TipoFormaPagto.CARTAOCREDITO, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    formasPagamento.addAll(formaPagamentoDAO.consultarPorTipoFormaPagamento(TipoFormaPagto.CARTAODEBITO, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    formasPagamento.addAll(getFormasPagamento(formaPagamentoDAO, empresa.getFormasPagamentoEmissaoNFSe()));
                    for (FormaPagamentoVO formaPag : formasPagamento) {
                        try {
                            controle.setDataInicio(dataInicio);
                            controle.setDataFim(dataFinal);

                            Uteis.logar(null, "NFSE | " + formaPag.getDescricao() + " | Dt. Início " + Uteis.getData(dataInicio) + " | Dt. Final " + Uteis.getData(dataFinal));

                            dateTimeStart = LocalDateTime.now();
                            controle.consultarItensAutomatico(empresa, formaPag.getCodigo(), dataInicio, dataFinal, true);
                            try {
                                controle.totalizar();
                            } catch (Exception ex) {
                                ex.printStackTrace();
                                resultadoServicosVO.getResultado().put("totalizar | Erro: " + ex.getMessage());
                                Uteis.logar(ex, AdministrativoRunner.class);
                            }

                            dateTimeEnd = LocalDateTime.now();
                            dateTimeDiff = ChronoUnit.MINUTES.between(dateTimeStart, dateTimeEnd);
                            Uteis.logar(null, "NFSE | " + formaPag.getDescricao() + " | GestaoNotasControle.consultarItensAutomatico | " + dateTimeDiff + " minutos");

                            Uteis.logar(null, "NFSE | " + formaPag.getDescricao() + " | " + controle.getListaItensApresentar().size() + " itens");
                            Uteis.logar(null, "NFSE | " + formaPag.getDescricao() + " | " + controle.getEmitir().getQuantidade() + " itens disponíveis para emissão");

                            if (UteisValidacao.emptyNumber(controle.getListaItensApresentar().size())) {
                                String msg1 = ("NFSE | " + formaPag.getDescricao() + " | Sem itens para emissão.");
                                Uteis.logar(null, msg1);
                                resultadoServicosVO.getResultado().put(msg1);
                                continue;
                            }

                            controle.selecionarTodosItens();
                            controle.calcularValorSelecionado();
                            Uteis.logar(null, "NFSE | " + formaPag.getDescricao() + " | " + controle.getSelecionado().getQuantidade() + " itens selecionados");

                            dateTimeStart = LocalDateTime.now();
                            controle.enviarNotasAutomatico(empresa, chave);
                            dateTimeEnd = LocalDateTime.now();
                            dateTimeDiff = ChronoUnit.MINUTES.between(dateTimeStart, dateTimeEnd);
                            Uteis.logar(null, "NFSE | " + formaPag.getDescricao() + " | GestaoNotasControle.enviarNotasAutomatico | " + dateTimeDiff + " minutos");
                            Uteis.logar(null, "NFSE | " + formaPag.getDescricao() + " | Forma de pagamento enviada ");
                        } catch (Exception ex) {
                            Uteis.logar(ex, AdministrativoRunner.class);
                            String msgErro = ("NFSE | " + formaPag.getDescricao() + " | ERRO: " + ex.getMessage());
                            Uteis.logar(msgErro);
                            resultadoServicosVO.getResultado().put(msgErro);
                        }
                    }

                    ResultadoServicos resultadoServicosDAO = new ResultadoServicos(con);
                    resultadoServicosDAO.gravarResultado(resultadoServicosVO);
                    resultadoServicosDAO = null;
                }

                //enviar notas que estão aguardando envio
                NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                dateTimeStart = LocalDateTime.now();
                notaFiscalDAO.enviarNotasAguardando();
                dateTimeEnd = LocalDateTime.now();
                dateTimeDiff = ChronoUnit.MINUTES.between(dateTimeStart, dateTimeEnd);
                notaFiscalDAO.retentativaEnvioEnotasNFSe();
                Uteis.logar(null, "NFSE | NotaFiscal.enviarNotasAguardando | "+ dateTimeDiff + " minutos");
                notaFiscalDAO = null;
            } catch (Exception ex) {
                Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                        empresa.getNome() +
                                " - Não foi possível emitir as notas do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                Uteis.logar(null, empresa.getNome() +
                        " - Não foi possível emitir as notas do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
            }

            try {
                if (empresa.isUsarNFCe() && empresa.isEnviarNFCeAutomatico()) {
                    ResultadoServicosVO resultadoServicosVO = new ResultadoServicosVO(ServicoEnum.NFCE, empresa.getCodigo());
                    Uteis.logar(null, "Enviando Notas NFCe da Empresa " + empresa.getNome() + " em : " + Uteis.getDataComHora(Calendario.hoje()));

                    Date dataInicio = Uteis.somarDias(robo.getDia(), -1);
                    Date dataFinal = Uteis.somarDias(robo.getDia(), -1);

                    //SE FOR O ULTIMO DIA DO MES E FOR O PROCESSO A NOITE A DATA A SER FILTRADA DEVE SER DO DIA E NÃO DO DIA ANTERIOR
                    Integer hora = Uteis.gethoraHH(Calendario.hoje());
                    if (hora >= 17 && (Uteis.obterDiaData(robo.getDia()) == Uteis.obterDiaData(Uteis.obterUltimoDiaMes(robo.getDia())))) {
                        Uteis.logar(null, "ULTIMO DIA DO MÊS - APÓS AS 17 HORAS - FILTRAR DADOS DO DIA - " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()));
                        dataInicio = robo.getDia();
                        dataFinal = robo.getDia();
                    }

                    //SE FOR O PRIMEIRO DIA DO MES FILTRAR OS DADOS DO DIA
                    if (Uteis.obterDiaData(robo.getDia()) == 1) {
                        Uteis.logar(null, "PRIMEIRO DIA DO MES - FILTRAR DADOS DO DIA - " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()));
                        dataInicio = robo.getDia();
                        dataFinal = robo.getDia();
                    }

                    if (empresa.isConsultarDiasAnterioresNFCe()) {
                        dataInicio = Uteis.obterPrimeiroDiaMes(dataInicio);
                    }

                    GestaoNFCeControle gestaoNFCeControle = new GestaoNFCeControle();
                    FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
                    List<FormaPagamentoVO> formasPagamento = new ArrayList<>();
                    formasPagamento.addAll(formaPagamentoDAO.consultarPorTipoFormaPagamento(TipoFormaPagto.CARTAOCREDITO, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    formasPagamento.addAll(formaPagamentoDAO.consultarPorTipoFormaPagamento(TipoFormaPagto.CARTAODEBITO, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    formasPagamento.addAll(getFormasPagamento(formaPagamentoDAO, empresa.getFormasPagamentoEmissaoNFCe()));
                    for (FormaPagamentoVO formaPag : formasPagamento) {

                        gestaoNFCeControle.setDataInicio(dataInicio);
                        gestaoNFCeControle.setDataFim(dataFinal);

                        Uteis.logar(null, "NFCE | " + formaPag.getDescricao() + " | Dt. Início " + Uteis.getData(dataInicio));
                        Uteis.logar(null, "NFCE | " + formaPag.getDescricao() + " | Dt. Final " + Uteis.getData(dataFinal));

                        gestaoNFCeControle.consultarItensAutomatico(empresa, formaPag.getCodigo(), dataInicio, dataFinal, true);
                        try {
                            gestaoNFCeControle.totalizar();
                        } catch (Exception ignored) {
                        }

                        Uteis.logar(null, "NFCE | " + formaPag.getDescricao() + " | " + gestaoNFCeControle.getListaItensApresentar().size() + " itens");
                        Uteis.logar(null, "NFCE | " + formaPag.getDescricao() + " | " + gestaoNFCeControle.getEmitir().getQuantidade() + " itens disponíveis para emissão");

                        if (UteisValidacao.emptyNumber(gestaoNFCeControle.getListaItensApresentar().size())) {
                            Uteis.logar(null, "NFCE | " + formaPag.getDescricao() + " | Sem itens para emissão.");
                            continue;
                        }

                        gestaoNFCeControle.selecionarTodosItens();
                        gestaoNFCeControle.calcularValorSelecionado();
                        Uteis.logar(null, "NFCE | " + formaPag.getDescricao() + " | " + gestaoNFCeControle.getSelecionado().getQuantidade() + " itens selecionados");

                        gestaoNFCeControle.enviarNotas(empresa, chave);
                        Uteis.logar(null, "NFCE | " + formaPag.getDescricao() + " | Forma de pagamento enviada.");

                    }

                    ResultadoServicos resultadoServicosDAO = new ResultadoServicos(con);
                    resultadoServicosDAO.gravarResultado(resultadoServicosVO);
                    resultadoServicosDAO = null;
                }

                //enviar notas que estão aguardando envio
                NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                dateTimeStart = LocalDateTime.now();
                notaFiscalDAO.enviarNotasAguardando();
                dateTimeEnd = dateTimeEnd.now();
                dateTimeDiff = ChronoUnit.MINUTES.between(dateTimeStart, dateTimeEnd);
                Uteis.logar(null, "NFCE | NotaFiscal.enviarNotasAguardando | "+dateTimeDiff+" minutos");
                notaFiscalDAO = null;
            } catch (Exception ex) {
                Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                        empresa.getNome() +
                                " - Não foi possível emitir as notas NFCe do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                Uteis.logar(null, empresa.getNome() +
                        " - Não foi possível emitir as notas NFCe do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
            }
        }
        Uteis.logar(null, "FINALIZADO ENVIO DE NOTAS - em: " + Uteis.getDataComHora(Calendario.hoje()));
    }

    public static void processarEstacionamento(RoboVO robo, String chave, Connection con) throws Exception {
        for (EmpresaVO empresa : robo.getListaEmpresa()) {
            if (!empresa.isAtiva()) {
                continue;
            }

            if (empresa.isUtilizaSistemaEstacionamento() && empresa.getConfigEstacionamento() != null) {
                ResultadoServicosVO resultadoServicosVO = new ResultadoServicosVO(ServicoEnum.ESTACIONAMENTO, empresa.getCodigo());
                EmpresaConfigEstacionamentoVO estConfig = empresa.getConfigEstacionamento();
                try {
                    IntegracaoEstacionamento.gerarArquivoEnviarFTP(false, estConfig, con, empresa.getCodigo());
                } catch (Exception erro) {
                    erro.printStackTrace();
                    Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.SEVERE,
                            "Erro ao gerar e enviar arquivo integração com estacionamento " + chave, erro);
                }
                ResultadoServicos resultadoServicosDAO = new ResultadoServicos(con);
                resultadoServicosDAO.gravarResultado(resultadoServicosVO);
                resultadoServicosDAO = null;
            }
        }
    }

    public static String processarIntegracaoF360(RoboVO robo, String chave, Connection con, Date dataProcesso) throws Exception {
        Uteis.logar("Iniciando processamento Integracao F360");
        int qtdSucesso = 0;
        int qtdErro = 0;

        StringBuilder sbEmpresasSucesso = new StringBuilder();
        StringBuilder sbEmpresasErro = new StringBuilder();

        for (EmpresaVO empresa : robo.getListaEmpresa()) {
            if (!empresa.isAtiva()) {
                continue;
            }

            if (empresa.isIntegracaoF360RelFatHabilitada()) {
                Uteis.logar("Empresa " + empresa.getNome() + " - IntegracaoF360RelFatHabilitada");
                Uteis.logar("Iniciando processamento Integracao F360");
                ResultadoServicosVO resultadoServicosVO = new ResultadoServicosVO(ServicoEnum.INTEGRACAO_F360, empresa.getCodigo());
                try {
                    Date dataInicio = Uteis.somarDias(isNull(dataProcesso) ? Calendario.hoje() : dataProcesso, -15);
                    Date dataTermino = Uteis.somarDias(isNull(dataProcesso) ? Calendario.hoje() : dataProcesso, -1);
                    Uteis.logar("dataInicio: " + dataInicio + " | " + "dataTermino: " + dataTermino);

                    IntegracaoF360.gerarArquivoEnviarFTP(empresa, dataInicio, dataTermino, con);
                    sbEmpresasSucesso.append(" | " + empresa.getNome());
                    qtdSucesso++;
                } catch (Exception erro) {
                    sbEmpresasErro.append(" | " + empresa.getNome());
                    qtdErro++;
                    erro.printStackTrace();
                    Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.SEVERE,
                            "Erro ao gerar e enviar arquivo integração F360 " + empresa.getNome() + chave, erro);
                }
                ResultadoServicos resultadoServicosDAO = new ResultadoServicos(con);
                resultadoServicosDAO.gravarResultado(resultadoServicosVO);
            }
        }

        String textoComplementarEmpresasSucesso =  qtdSucesso > 0 ? " --> " + sbEmpresasSucesso : "";
        StringBuilder sbSucesso = new StringBuilder();
        sbSucesso.append("###SUCESSO###: total: " + qtdSucesso + textoComplementarEmpresasSucesso);

        String textoComplementarEmpresasErro =  qtdErro > 0 ? " --> " + sbEmpresasErro : "";
        StringBuilder sbErro = new StringBuilder();
        sbErro.append(" | ###ERRO###: total: " + qtdErro + textoComplementarEmpresasErro);

        Uteis.logar(sbSucesso.toString().replaceFirst("\\|", "") + sbErro);
        return sbSucesso.toString().replaceFirst("\\|", "") + sbErro;
    }

    private static void processarRelatoriosNotas(RoboVO robo, String chave, Connection con) {
        EmpresaVO emp = null;
        try {
            Uteis.logar(null, "INICIALIZA PROCESSAMENTO DE RELATORIOS DE NOTAS - em: " + Uteis.getDataComHora(Calendario.hoje()));
            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                emp = empresa;
                if (!empresa.isAtiva()) {
                    continue;
                }

                if (empresa.isUsaNotasDelphi()) {
                    relatorioNotasNaoAutorizadasOuCanceladasDesdeOntemDelphi(empresa, chave);
                }
                if (empresa.isUsaEnotas()) {
                    relatorioNotasNaoAutorizadasOuCanceladasDesdeOntemEnotas(empresa, chave, con);
                    relatorioDivergenciaEnotas(empresa, con);
                }

            }
        } catch (Exception ex) {
            String msgError = new StringBuilder().append(emp != null ? emp.getNome() : "SEM NOME DA EMPRESA")
                    .append(" - Não foi possível emitir o relatório (consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem) do período: (")
                    .append(Calendario.ontem())
                    .append(" ~ ")
                    .append(Calendario.getDataComHoraZerada(Calendario.hoje()))
                    .append(") em: ")
                    .append(Calendario.hoje())
                    .append(" - ERRO: ") + ex.getMessage();

            Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, msgError, ex);
            Uteis.logar(null, msgError);
        }
    }

    private static void relatorioNotasNaoAutorizadasOuCanceladasDesdeOntemDelphi(EmpresaVO empresa, String chave) {
        try {
            List<String> modelosEnvioAutomaticoNotificacao = new ArrayList<String>();
            if (empresa.getUsarNFSe() && empresa.isEnvioNotificacaoNotasNFSe()) {
                modelosEnvioAutomaticoNotificacao.add(ModeloDocumentoFiscal.NFSe.name());
            }

            if (empresa.isUsarNFCe() && empresa.isEnvioNotificacaoNotasNFCe()) {
                modelosEnvioAutomaticoNotificacao.add(ModeloDocumentoFiscal.NFCe.name());
            }

            if (!modelosEnvioAutomaticoNotificacao.isEmpty() && StringUtils.isNotEmpty(empresa.getEmailsNotificacaoAutomaticaNotas())) {
                Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo = IntegracaoNFSeWSConsumer
                        .consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(
                                empresa.getChaveNFSe(), chave, Uteis.removerMascara(empresa.getCNPJ()), modelosEnvioAutomaticoNotificacao
                        );

                if (!mapRelatorioPorModelo.isEmpty()) {
                    enviarEmailNotasNaoAutorizadasOuCanceladasDesdeOntem(empresa, chave, mapRelatorioPorModelo, false);
                    enviarSocialMailingNotasNaoAutorizadasOuCanceladasDesdeOntem(empresa, chave, mapRelatorioPorModelo, false);
                }
            }
        } catch (Exception ex) {
            String msgError = new StringBuilder().append(empresa != null ? empresa.getNome() : "SEM NOME DA EMPRESA")
                    .append(" - Não foi possível emitir o relatório (consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem) do período: (")
                    .append(Calendario.ontem())
                    .append(" ~ ")
                    .append(Calendario.getDataComHoraZerada(Calendario.hoje()))
                    .append(") em: ")
                    .append(Calendario.hoje())
                    .append(" - ERRO: ") + ex.getMessage();

            Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, msgError, ex);
            Uteis.logar(null, msgError);
        }
    }

    private static void relatorioNotasNaoAutorizadasOuCanceladasDesdeOntemEnotas(EmpresaVO empresa, String chave, Connection con) {
        try {
            List<String> modelosEnvioAutomaticoNotificacao = new ArrayList<String>();
            if (empresa.getUsarNFSe() && empresa.isEnvioNotificacaoNotasNFSe()) {
                modelosEnvioAutomaticoNotificacao.add(ModeloDocumentoFiscal.NFSe.name());
            }

            if (empresa.isUsarNFCe() && empresa.isEnvioNotificacaoNotasNFCe()) {
                modelosEnvioAutomaticoNotificacao.add(ModeloDocumentoFiscal.NFCe.name());
            }

            if (!modelosEnvioAutomaticoNotificacao.isEmpty() && StringUtils.isNotEmpty(empresa.getEmailsNotificacaoAutomaticaNotas())) {
                Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo = getMapByModeloEnotas(empresa, con);
                if (!mapRelatorioPorModelo.isEmpty()) {
                    enviarEmailNotasNaoAutorizadasOuCanceladasDesdeOntem(empresa, chave, mapRelatorioPorModelo, true);
                    enviarSocialMailingNotasNaoAutorizadasOuCanceladasDesdeOntem(empresa, chave, mapRelatorioPorModelo, true);
                }
            }

        } catch (Exception ex) {
            String msgError = new StringBuilder().append(empresa != null ? empresa.getNome() : "SEM NOME DA EMPRESA")
                    .append(" - Não foi possível emitir o relatório (consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem) do período: (")
                    .append(Calendario.ontem())
                    .append(" ~ ")
                    .append(Calendario.getDataComHoraZerada(Calendario.hoje()))
                    .append(") em: ")
                    .append(Calendario.hoje())
                    .append(" - ERRO: ") + ex.getMessage();

            Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, msgError, ex);
            Uteis.logar(null, msgError);
        }
    }

    private static void relatorioDivergenciaEnotas(EmpresaVO empresa, Connection con) {
        try {
            if(Uteis.getUrlWebHookDiscordEnotasHabilitar().equals("false")) {
                return;
            }
            if(UteisValidacao.emptyString(Uteis.getUrlWebHookDiscordEnotas()) || Uteis.getUrlWebHookDiscordEnotas().equals("@URL_WEBHOOK_DISCORD_ENOTAS@")) {
                System.out.println("O envio de notificações de notas está habilitado mas a URL não foi definida.");
                return;
            }

            ConfiguracaoNotaFiscal configDAO = new ConfiguracaoNotaFiscal(con);
            List<ConfiguracaoNotaFiscalVO> listaConfigs = new ArrayList<ConfiguracaoNotaFiscalVO>();

            if(!UteisValidacao.emptyNumber(empresa.getConfiguracaoNotaFiscalNFSe().getCodigo())) {
                listaConfigs.add(configDAO.consultarPorChavePrimaria(empresa.getConfiguracaoNotaFiscalNFSe().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }
            if(!UteisValidacao.emptyNumber(empresa.getConfiguracaoNotaFiscalNFCe().getCodigo())) {
                listaConfigs.add(configDAO.consultarPorChavePrimaria(empresa.getConfiguracaoNotaFiscalNFCe().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            }

            for(ConfiguracaoNotaFiscalVO configZW : listaConfigs) {
                String configDiscord = "";
                if(configZW.isAtivo() && configZW.isEnotas()) {
                    JSONObject configEnotas = new JSONObject(configDAO.consultarSituacaoEmpresaEnotas(configZW).getJson());

                    if(!configZW.getIdEnotas().equals(configEnotas.getString("id")) &&
                            (!UteisValidacao.emptyString(configZW.getIdEnotas()) || !configEnotas.isNull("id"))) {
                        configDiscord += "\nID eNotas Pacto: " + configZW.getIdEnotas();
                        configDiscord += "\nID eNotas eNotas: " + configEnotas.getString("id") + "\n";
                    }
                    if(!Uteis.formatarCpfCnpj(configZW.getCnpj(), true).equals(configEnotas.get("cnpj")) &&
                            (!UteisValidacao.emptyString(configZW.getCnpj()) || !configEnotas.isNull("cnpj"))) {
                        configDiscord += "\nCNPJ Pacto: " + configZW.getCnpj();
                        configDiscord += "\nCNPJ eNotas: " + Uteis.formatarCpfCnpj(configEnotas.get("cnpj").toString(), false) + "\n";
                    }
                    if(!configZW.getInscricaoMunicipal().replaceAll("[^\\d]", "").equals(configEnotas.get("inscricaoMunicipal")) &&
                            (!UteisValidacao.emptyString(configZW.getInscricaoMunicipal()) || !configEnotas.isNull("inscricaoMunicipal"))) {
                        configDiscord += "\nInscrição Municipal Pacto: " + configZW.getInscricaoMunicipal();
                        configDiscord += "\nInscrição Municipal eNotas: " + configEnotas.get("inscricaoMunicipal") + "\n";
                    }
                    if(!configZW.getInscricaoEstadual().equals(configEnotas.get("inscricaoEstadual")) &&
                            (!UteisValidacao.emptyString(configZW.getInscricaoEstadual()) || !configEnotas.isNull("inscricaoEstadual"))) {
                        configDiscord += "\nInscrição Estadual Pacto: " + configZW.getInscricaoEstadual();
                        configDiscord += "\nInscrição Estadual eNotas: " + configEnotas.get("inscricaoEstadual") + "\n";
                    }
                    if(!configZW.getRazaoSocial().equals(configEnotas.get("razaoSocial")) &&
                            (!UteisValidacao.emptyString(configZW.getRazaoSocial()) || !configEnotas.isNull("razaoSocial"))) {
                        configDiscord += "\nRazão Social Pacto: " + configZW.getRazaoSocial();
                        configDiscord += "\nRazão Social eNotas: " + configEnotas.get("razaoSocial") + "\n";
                    }
                    if(!configZW.getNomeFantasia().equals(configEnotas.get("nomeFantasia")) &&
                            (!UteisValidacao.emptyString(configZW.getNomeFantasia()) || !configEnotas.isNull("nomeFantasia"))) {
                        configDiscord += "\nNome Fantasia Pacto: " + configZW.getNomeFantasia();
                        configDiscord += "\nNome Fantasia eNotas: " + configEnotas.get("nomeFantasia") + "\n";
                    }
                    if(configZW.isOptanteSimplesNacional() != configEnotas.getBoolean("optanteSimplesNacional")) {
                        configDiscord += "\nOptante Simples Nacional Pacto: " + configZW.isOptanteSimplesNacional();
                        configDiscord += "\nOptante Simples Nacional eNotas: " + configEnotas.getBoolean("optanteSimplesNacional") + "\n";
                    }
                    if(!Uteis.getData(configZW.getDataCadastro(), "br").equals(Uteis.getData(new SimpleDateFormat("yyyy-MM-dd")
                            .parse(configEnotas.get("dataCriacao").toString().substring(0, 10)), "br")) &&
                            (configZW.getDataCadastro() != null || !configEnotas.isNull("dataCriacao"))) {
                        configDiscord += "\nData Criação Pacto: " + Uteis.getData(configZW.getDataCadastro(), "br");
                        configDiscord += "\nData Criação eNotas: " + Uteis.getData(new SimpleDateFormat("yyyy-MM-dd").parse(configEnotas.get("dataCriacao").toString().substring(0, 10)), "br") + "\n";
                    }
                    if(!configZW.getEmail().equals(configEnotas.get("email")) &&
                            (!UteisValidacao.emptyString(configZW.getEmail()) || !configEnotas.isNull("email"))) {
                        configDiscord += "\nEmail Pacto: " + configZW.getEmail();
                        configDiscord += "\nEmail eNotas: " + configEnotas.get("email") + "\n";
                    }
                    if(!configZW.getTelefoneComercial().replaceAll("[^\\d]", "").equals(configEnotas.get("telefoneComercial")) &&
                            (!UteisValidacao.emptyString(configZW.getTelefoneComercial()) || !configEnotas.isNull("telefoneComercial"))) {
                        configDiscord += "\nTelefone Comercial Pacto: " + configZW.getTelefoneComercial();
                        configDiscord += "\nTelefone Comercial eNotas: " + configEnotas.get("telefoneComercial") + "\n";
                    }
                    if(configZW.isIncentivadorCultural() != configEnotas.getBoolean("incentivadorCultural")) {
                        configDiscord += "\nIncentivador Cultural Pacto: " + configZW.isIncentivadorCultural();
                        configDiscord += "\nIncentivador Cultural eNotas: " + configEnotas.getBoolean("incentivadorCultural") + "\n";
                    }
                    if(!configZW.getRegimeEspecialTributacao().equals(configEnotas.get("regimeEspecialTributacao")) &&
                            (!UteisValidacao.emptyString(configZW.getRegimeEspecialTributacao()) || !configEnotas.isNull("regimeEspecialTributacao"))) {
                        configDiscord += "\nRegime Especial Tributação Pacto: " + configZW.getRegimeEspecialTributacao();
                        configDiscord += "\nRegime Especial Tributação eNotas: " + configEnotas.get("regimeEspecialTributacao") + "\n";
                    }

                    // endereco
                    if(!configZW.getPaisVO().getNome().equals(configEnotas.getJSONObject("endereco").get("pais").toString().toUpperCase(Locale.ROOT)) &&
                            (!UteisValidacao.emptyString(configZW.getPaisVO().getNome()) || !configEnotas.getJSONObject("endereco").isNull("pais"))) {
                        configDiscord += "\nPaís Pacto: " + configZW.getPaisVO().getNome();
                        configDiscord += "\nPaís eNotas: " + configEnotas.getJSONObject("endereco").get("pais") + "\n";
                    }
                    if(!configZW.getEstadoVO().getSigla().equals(configEnotas.getJSONObject("endereco").get("uf")) &&
                            (!UteisValidacao.emptyString(configZW.getEstadoVO().getSigla()) || !configEnotas.getJSONObject("endereco").isNull("uf"))) {
                        configDiscord += "\nUF Pacto: " + configZW.getEstadoVO().getSigla();
                        configDiscord += "\nUF eNotas: " + configEnotas.getJSONObject("endereco").get("uf") + "\n";
                    }
                    if(!configZW.getCidadeVO().getCodigoIBGE().substring(0, 2).equals(configEnotas.getJSONObject("endereco").get("codigoIbgeUf").toString()) &&
                            (!UteisValidacao.emptyString(configZW.getCidadeVO().getCodigoIBGE().substring(0, 2)) || !configEnotas.getJSONObject("endereco").isNull("codigoIbgeUf"))) {
                        configDiscord += "\nCódigo do IBGE da UF Pacto: " + configZW.getCidadeVO().getCodigoIBGE().substring(0, 2);
                        configDiscord += "\nCódigo do IBGE da UF eNotas: " + configEnotas.getJSONObject("endereco").get("codigoIbgeUf") + "\n";
                    }
                    if(!configZW.getCidadeVO().getNome().equals(configEnotas.getJSONObject("endereco").get("cidade").toString().toUpperCase(Locale.ROOT)) &&
                            (!UteisValidacao.emptyString(configZW.getCidadeVO().getNome()) || !configEnotas.getJSONObject("endereco").isNull("cidade"))) {
                        configDiscord += "\nCidade Pacto: " + configZW.getCidadeVO().getNome();
                        configDiscord += "\nCidade eNotas: " + configEnotas.getJSONObject("endereco").get("cidade") + "\n";
                    }
                    if(!configZW.getCidadeVO().getCodigoIBGE().equals(configEnotas.getJSONObject("endereco").get("codigoIbgeCidade").toString()) &&
                            (!UteisValidacao.emptyString(configZW.getCidadeVO().getCodigoIBGE()) || !configEnotas.getJSONObject("endereco").isNull("codigoIbgeCidade"))) {
                        configDiscord += "\nCódigo do IBGE da Cidade Pacto: " + configZW.getCidadeVO().getCodigoIBGE();
                        configDiscord += "\nCódigo do IBGE da Cidade eNotas: " + configEnotas.getJSONObject("endereco").get("codigoIbgeCidade") + "\n";
                    }
                    if(!configZW.getLogradouro().equals(configEnotas.getJSONObject("endereco").get("logradouro")) &&
                            (!UteisValidacao.emptyString(configZW.getLogradouro()) || !configEnotas.getJSONObject("endereco").isNull("logradouro"))) {
                        configDiscord += "\nLogradouro Pacto: " + configZW.getLogradouro();
                        configDiscord += "\nLogradouro eNotas: " + configEnotas.getJSONObject("endereco").get("logradouro") + "\n";
                    }
                    if(!configZW.getNumero().equals(configEnotas.getJSONObject("endereco").get("numero")) &&
                            (!UteisValidacao.emptyString(configZW.getNumero()) || !configEnotas.getJSONObject("endereco").isNull("numero"))) {
                        configDiscord += "\nNúmero Pacto: " + configZW.getNumero();
                        configDiscord += "\nNúmero eNotas: " + configEnotas.getJSONObject("endereco").get("numero") + "\n";
                    }
                    if(!configZW.getComplemento().equals(configEnotas.getJSONObject("endereco").get("complemento").toString().toUpperCase(Locale.ROOT)) &&
                            (!UteisValidacao.emptyString(configZW.getComplemento()) || !configEnotas.getJSONObject("endereco").isNull("complemento"))) {
                        configDiscord += "\nComplemento Pacto: " + configZW.getComplemento();
                        configDiscord += "\nComplemento eNotas: " + configEnotas.getJSONObject("endereco").get("complemento") + "\n";
                    }
                    if(!configZW.getBairro().equals(configEnotas.getJSONObject("endereco").get("bairro")) &&
                            (!UteisValidacao.emptyString(configZW.getBairro()) || !configEnotas.getJSONObject("endereco").isNull("bairro"))) {
                        configDiscord += "\nBairro Pacto: " + configZW.getBairro();
                        configDiscord += "\nBairro eNotas: " + configEnotas.getJSONObject("endereco").get("bairro") + "\n";
                    }
                    if(!configZW.getCep().replaceAll("[^\\d]", "").equals(configEnotas.getJSONObject("endereco").get("cep")) &&
                            (!UteisValidacao.emptyString(configZW.getCep()) || !configEnotas.getJSONObject("endereco").isNull("cep"))) {
                        configDiscord += "\nCEP Pacto: " + configZW.getCep();
                        configDiscord += "\nCEP eNotas: " + configEnotas.getJSONObject("endereco").get("cep") + "\n";
                    }

                    // modulos
                    if(!configZW.getDataCertificado_Apresentar().substring(0, Math.min(10, configZW.getDataCertificado_Apresentar().length())).equals(configEnotas.getJSONObject("certificadoDigital").get("dataVencimento")) &&
                            (!UteisValidacao.emptyString(configZW.getDataCertificado_Apresentar()) || !configEnotas.getJSONObject("certificadoDigital").isNull("dataVencimento"))) {
                        configDiscord += "\nData validade certificado Pacto: " + configZW.getDataCertificado_Apresentar().substring(0, 10);
                        configDiscord += "\nData validade certificado eNotas: " + configEnotas.getJSONObject("certificadoDigital").get("dataVencimento") + "\n";
                    }

                    // NFESE
                    if(configZW.getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFSE)) {
                        if(!configZW.getCodTributacaoMunicipal().equals(configEnotas.get("codigoServicoMunicipal")) &&
                                (!UteisValidacao.emptyString(configZW.getCodTributacaoMunicipal()) || !configEnotas.isNull("codigoServicoMunicipal"))) {
                            configDiscord += "\nCódigo Tributação Município Pacto: " + configZW.getCodTributacaoMunicipal();
                            configDiscord += "\nCódigo Tributação Município eNotas: " + configEnotas.get("codigoServicoMunicipal") + "\n";
                        }
                        if(!configZW.getCodListaServico().equals(configEnotas.get("itemListaServicoLC116")) &&
                                (!UteisValidacao.emptyString(configZW.getCodListaServico()) || !configEnotas.isNull("itemListaServicoLC116"))) {
                            configDiscord += "\nCódigo Lista Serviço Pacto: " + configZW.getCodListaServico();
                            configDiscord += "\nCódigo Lista Serviço eNotas: " + configEnotas.get("itemListaServicoLC116") + "\n";
                        }
                        if(!configZW.getCnae().equals(configEnotas.get("cnae")) &&
                                (!UteisValidacao.emptyString(configZW.getCnae()) || !configEnotas.isNull("cnae"))) {
                            configDiscord += "\nCNAE Pacto: " + configZW.getCnae();
                            configDiscord += "\nCNAE eNotas: " + configEnotas.get("cnae") + "\n";
                        }
                        if(!configZW.getIss().equals(configEnotas.getDouble("aliquotaIss")) &&
                                (!UteisValidacao.emptyNumber(configZW.getIss()) || !configEnotas.isNull("aliquotaIss"))) {
                            configDiscord += "\nISS(%) Pacto: " + configZW.getIss();
                            configDiscord += "\nISS(%) eNotas: " + configEnotas.getDouble("aliquotaIss") + "\n";
                        }
                        if(!configZW.getDescricaoServico().equals(configEnotas.get("descricaoServico")) &&
                                (!UteisValidacao.emptyString(configZW.getDescricaoServico()) || !configEnotas.isNull("descricaoServico"))) {
                            configDiscord += "\nDescrição Serviço Pacto: " + configZW.getDescricaoServico();
                            configDiscord += "\nDescrição Serviço eNotas: " + configEnotas.get("descricaoServico") + "\n";
                        }
                        if(configZW.isEnviarEmailCliente() != configEnotas.getBoolean("enviarEmailCliente")) {
                            configDiscord += "\nEnviar email cliente Pacto: " + configZW.isEnviarEmailCliente();
                            configDiscord += "\nEnviar email cliente eNotas: " + configEnotas.getBoolean("enviarEmailCliente") + "\n";
                        }

                        // configuracoesNFSeProducao
                        if(!configZW.getConfigProducaoVO().getSequencialNFe().equals(configEnotas.getJSONObject("configuracoesNFSeProducao").get("sequencialNFe")) &&
                                (!UteisValidacao.emptyNumber(configZW.getConfigProducaoVO().getSequencialNFe()) || !configEnotas.getJSONObject("configuracoesNFSeProducao").isNull("sequencialNFe"))) {
                            configDiscord += "\nAmbiente Produção - Sequêncial Nota Pacto: " + configZW.getConfigProducaoVO().getSequencialNFe();
                            configDiscord += "\nAmbiente Produção - Sequêncial Nota eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("sequencialNFe") + "\n";
                        }
                        if(!configZW.getConfigProducaoVO().getSerieNFe().equals(configEnotas.getJSONObject("configuracoesNFSeProducao").get("serieNFe")) &&
                                (!UteisValidacao.emptyString(configZW.getConfigProducaoVO().getSerieNFe()) || !configEnotas.getJSONObject("configuracoesNFSeProducao").isNull("serieNFe"))) {
                            configDiscord += "\nAmbiente Produção - Série Pacto: " + configZW.getConfigProducaoVO().getSerieNFe();
                            configDiscord += "\nAmbiente Produção - Série eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("serieNFe") + "\n";
                        }
                        if(!configZW.getConfigProducaoVO().getSequencialLoteNFe().equals(configEnotas.getJSONObject("configuracoesNFSeProducao").get("sequencialLoteNFe")) &&
                                (!UteisValidacao.emptyNumber(configZW.getConfigProducaoVO().getSequencialLoteNFe()) || !configEnotas.getJSONObject("configuracoesNFSeProducao").isNull("sequencialLoteNFe"))) {
                            configDiscord += "\nAmbiente Produção - Sequêncial Lote Pacto: " + configZW.getConfigProducaoVO().getSequencialLoteNFe();
                            configDiscord += "\nAmbiente Produção - Sequêncial Lote eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("sequencialLoteNFe") + "\n";
                        }
                        if(!configZW.getConfigProducaoVO().getUsuarioAcessoProvedor().equals(configEnotas.getJSONObject("configuracoesNFSeProducao").get("usuarioAcessoProvedor")) &&
                                (!UteisValidacao.emptyString(configZW.getConfigProducaoVO().getUsuarioAcessoProvedor()) || !configEnotas.getJSONObject("configuracoesNFSeProducao").isNull("usuarioAcessoProvedor"))) {
                            configDiscord += "\nAmbiente Produção - Usuário Acesso Pacto: " + configZW.getConfigProducaoVO().getUsuarioAcessoProvedor();
                            configDiscord += "\nAmbiente Produção - Usuário Acesso eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("usuarioAcessoProvedor") + "\n";
                        }
                        if(!configZW.getConfigProducaoVO().getTokenAcessoProvedor().equals(configEnotas.getJSONObject("configuracoesNFSeProducao").get("tokenAcessoProvedor")) &&
                                (!UteisValidacao.emptyString(configZW.getConfigProducaoVO().getTokenAcessoProvedor()) || !configEnotas.getJSONObject("configuracoesNFSeProducao").isNull("tokenAcessoProvedor"))) {
                            configDiscord += "\nAmbiente Produção - Token Acesso Pacto: " + configZW.getConfigProducaoVO().getTokenAcessoProvedor();
                            configDiscord += "\nAmbiente Produção - Token Acesso eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("tokenAcessoProvedor") + "\n";
                        }

                        // configuracoesNFSeHomologacao
                        if(!configZW.getConfigHomologacaoVO().getSequencialNFe().equals(configEnotas.getJSONObject("configuracoesNFSeHomologacao").get("sequencialNFe")) &&
                                (!UteisValidacao.emptyNumber(configZW.getConfigHomologacaoVO().getSequencialNFe()) || !configEnotas.getJSONObject("configuracoesNFSeHomologacao").isNull("sequencialNFe"))) {
                            configDiscord += "\nAmbiente Homologação - Sequêncial Nota Pacto: " + configZW.getConfigHomologacaoVO().getSequencialNFe();
                            configDiscord += "\nAmbiente Homologação - Sequêncial Nota eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("sequencialNFe") + "\n";
                        }
                        if(!configZW.getConfigHomologacaoVO().getSerieNFe().equals(configEnotas.getJSONObject("configuracoesNFSeHomologacao").get("serieNFe")) &&
                                (!UteisValidacao.emptyString(configZW.getConfigHomologacaoVO().getSerieNFe()) || !configEnotas.getJSONObject("configuracoesNFSeHomologacao").isNull("serieNFe"))) {
                            configDiscord += "\nAmbiente Homologação - Série Pacto: " + configZW.getConfigHomologacaoVO().getSerieNFe();
                            configDiscord += "\nAmbiente Homologação - Série eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("serieNFe") + "\n";
                        }
                        if(!configZW.getConfigHomologacaoVO().getSequencialLoteNFe().equals(configEnotas.getJSONObject("configuracoesNFSeHomologacao").get("sequencialLoteNFe")) &&
                                (!UteisValidacao.emptyNumber(configZW.getConfigHomologacaoVO().getSequencialLoteNFe()) || !configEnotas.getJSONObject("configuracoesNFSeHomologacao").isNull("sequencialLoteNFe"))) {
                            configDiscord += "\nAmbiente Homologação - Sequêncial Lote Pacto: " + configZW.getConfigHomologacaoVO().getSequencialLoteNFe();
                            configDiscord += "\nAmbiente Homologação - Sequêncial Lote eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("sequencialLoteNFe") + "\n";
                        }
                        if(!configZW.getConfigHomologacaoVO().getUsuarioAcessoProvedor().equals(configEnotas.getJSONObject("configuracoesNFSeHomologacao").get("usuarioAcessoProvedor")) &&
                                (!UteisValidacao.emptyString(configZW.getConfigHomologacaoVO().getUsuarioAcessoProvedor()) || !configEnotas.getJSONObject("configuracoesNFSeHomologacao").isNull("usuarioAcessoProvedor"))) {
                            configDiscord += "\nAmbiente Homologação - Usuário Acesso Pacto: " + configZW.getConfigHomologacaoVO().getUsuarioAcessoProvedor();
                            configDiscord += "\nAmbiente Homologação - Usuário Acesso eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("usuarioAcessoProvedor") + "\n";
                        }
                        if(!configZW.getConfigHomologacaoVO().getTokenAcessoProvedor().equals(configEnotas.getJSONObject("configuracoesNFSeHomologacao").get("tokenAcessoProvedor")) &&
                                (!UteisValidacao.emptyString(configZW.getConfigHomologacaoVO().getTokenAcessoProvedor()) || !configEnotas.getJSONObject("configuracoesNFSeHomologacao").isNull("tokenAcessoProvedor"))) {
                            configDiscord += "\nAmbiente Homologação - Token Acesso Pacto: " + configZW.getConfigHomologacaoVO().getTokenAcessoProvedor();
                            configDiscord += "\nAmbiente Homologação - Token Acesso eNotas: " + configEnotas.getJSONObject("configuracoesNFSeProducao").get("tokenAcessoProvedor") + "\n";
                        }
                    }

                    // !Uteis.isAmbienteDesenvolvimentoTeste()
                    if(!UteisValidacao.emptyNumber(configDiscord.length())) {
                        JSONObject jsonEnvio = new JSONObject();
                        jsonEnvio.put("username", "");
                        jsonEnvio.put("avatar_url", "");
                        jsonEnvio.put("content", "");

                        JSONObject auxEmbeds = new JSONObject();
                        auxEmbeds.put("title", empresa.getNome() + " - " + configZW.getTipoNotaFiscal());
                        auxEmbeds.put("url", "https://app.pactosolucoes.com.br/apoio/apoio/" + empresa.getCodEmpresaFinanceiro());
                        auxEmbeds.put("color", 16711680);
                        auxEmbeds.put("description", configDiscord);
                        auxEmbeds.put("timestamp", "");

                        JSONObject author = new JSONObject();
                        author.put("name", Uteis.getDataAtual());
                        auxEmbeds.put("author", author);
                        auxEmbeds.put("image", new JSONObject());
                        auxEmbeds.put("thumbnail", new JSONObject());
                        auxEmbeds.put("footer", new JSONObject());
                        auxEmbeds.put("fields", new JSONObject());

                        JSONArray embeds = new JSONArray();
                        embeds.put(auxEmbeds);
                        jsonEnvio.put("embeds", embeds);
                        jsonEnvio.put("components", new JSONArray());

                        Map<String, String> params = new HashMap<>();
                        params.put("User-Agent", "WebHookDiscordEnotas");
                        ExecuteRequestHttpService.post(Uteis.getUrlWebHookDiscordEnotas(), jsonEnvio.toString(), params);
                    }
                }
            }

            configDAO = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> getMapByModeloEnotas(EmpresaVO empresa, Connection con) throws Exception {
        Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo = new HashMap<>();

        for (ModeloDocumentoFiscal modeloDocumentoFiscal : ModeloDocumentoFiscal.values()) {
            DocumentoFiscalRelatorioPeriodoTO obj = new DocumentoFiscalRelatorioPeriodoTO();

            String tipoNotaFiscal = "";
            if (modeloDocumentoFiscal.equals(ModeloDocumentoFiscal.NFCe)) {
                tipoNotaFiscal = TipoNotaFiscalEnum.NFCE.getCodigo().toString();
            } else if (modeloDocumentoFiscal.equals(ModeloDocumentoFiscal.NFSe)) {
                tipoNotaFiscal = TipoNotaFiscalEnum.NFSE.getCodigo().toString() + "," + TipoNotaFiscalEnum.NFE.getCodigo().toString();
            }

            Date dataRegistroInicial = Calendario.somarDias(Calendario.hoje(), -1);
            Date dataRegistroFinal = Calendario.hoje();

            NotaFiscal notaDAO = new NotaFiscal(con);
            Map<String, Integer> mapQuantidadePorStatus = notaDAO.obterMapa(empresa.getCodigo(), tipoNotaFiscal, dataRegistroInicial, dataRegistroFinal);
            notaDAO = null;

            if (!obj.getMapQuantidadePorStatus().isEmpty()) {
                obj.setPeriodoLimiteInferior(dataRegistroInicial);
                obj.setPeriodoLimiteSuperior(dataRegistroFinal);
                obj.setMapQuantidadePorStatus(mapQuantidadePorStatus);
                mapRelatorioPorModelo.put(modeloDocumentoFiscal, obj);
            }
        }
        return mapRelatorioPorModelo;
    }

    private static void enviarEmailNotasNaoAutorizadasOuCanceladasDesdeOntem(EmpresaVO empresa,
                                                                             String chave,
                                                                             Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo,
                                                                             boolean eNotas) throws Exception {

        final String ASSUNTO = "Notificação de notas não autorizadas e canceladas";
        final String REMETENTE = "Pacto Soluções - NF";
        final String EMAIL_REMETENTE = "<EMAIL>";

        String[] emails = UteisEmail.retornarArrayEmailStringSeparadoPorPontoVirgulaIgnorandoInvalidos(empresa.getEmailsNotificacaoAutomaticaNotas());
        String corpoEmail = DocumentoFiscalRelatorioPeriodoTO.mapByModeloDocumentoFiscalToEmailHTML(mapRelatorioPorModelo, chave, empresa.getNome(), eNotas);
        ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
        config.setEmailPadrao(EMAIL_REMETENTE);

        Uteis.logar(null, String.format("(%s) - Vou enviar o e-mail com o relatório (\"relatorioNotasNaoAutorizadasOuCanceladasDesdeOntem\") " +
                        "para a unidade-chave (\"%s\" - %s) os seguintes destinatários (%s).",
                Uteis.getDataComHora(Calendario.hoje()),
                empresa.getNome(),
                chave,
                empresa.getEmailsNotificacaoAutomaticaNotas())
        );

        new UteisEmail()
                .novo("", config)
                .enviarEmailN(emails, corpoEmail, ASSUNTO, REMETENTE);

        Uteis.logar(null, "Email do relatório (\"relatorioNotasNaoAutorizadasOuCanceladasDesdeOntem\") enviado!");
    }

    private static void enviarSocialMailingNotasNaoAutorizadasOuCanceladasDesdeOntem(EmpresaVO empresa,
                                                                                     String chave,
                                                                                     Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> mapRelatorioPorModelo,
                                                                                     boolean eNotas) throws Exception {
        String texto = DocumentoFiscalRelatorioPeriodoTO.mapByModeloDocumentoFiscalToSocialMailingHTML(mapRelatorioPorModelo, chave, empresa.getNome(), eNotas);
        List<String> emailsDestinatarios = UteisEmail.retornarListaEmailStringSeparadoPorPontoVirgulaIgnorandoInvalidos(empresa.getEmailsNotificacaoAutomaticaNotas());

        for (String emailDestinatario : emailsDestinatarios) {
            List<UsuarioVO> listaUsuario = getFacade().getUsuario().listarPorEmail(emailDestinatario);

            for (UsuarioVO usuarioDestino : listaUsuario) {
                getFacade().getSocialMailing().enviarMensagemComoRemetentePactoBR(texto, usuarioDestino.getColaboradorVO().getPessoa(), false);
            }
        }
    }

    public static void processarOperacoesColetivas(RoboVO robo, DAO dao, String chave, Connection con) throws Exception {
        Uteis.logar(null, "INICIALIZA Processamento Operação Coletiva - em: " + Uteis.getDataComHora(Calendario.hoje()));
        OperacaoColetiva operacaoColetivaDAO = new OperacaoColetiva(con);
        new BonusColetivoService().ajustarContratoFuturosOperacaoProcessada(con);
        for (EmpresaVO empresa : robo.getListaEmpresa()) {
            if (!empresa.isAtiva()) {
                continue;
            }
            List<OperacaoColetivaVO> operacoes = operacaoColetivaDAO.consultar(empresa.getCodigo(), Calendario.hoje(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (OperacaoColetivaVO operacao : operacoes) {
                try {
                    if(operacao.getTipo().equals(TipoOperacaoColetivaEnum.BLOQUEAR_ACESSO_CATRACA_POR_PERIODO)) {
                        if(operacao.getStatus() == StatusOperacaoColetivaEnum.AGUARDANDO) {
                            processarOperacaoColetivaBloqueioCatraca(con, operacao, empresa);
                        } else if (operacao.getStatus() == StatusOperacaoColetivaEnum.PROCESSANDO) {
                            removerMensagensBloqueioCatraca(con, operacao);
                        }
                    } else {
                        if (operacao.getTipo().equals(TipoOperacaoColetivaEnum.DESMARCAR_AULAS_ALUNOS_PERIODO)) {
                            if (operacao.getStatus().equals(StatusOperacaoColetivaEnum.AGUARDANDO_EXCLUSAO)){
                                new AulaDesmarcada(con).deletarAulasDermarcadasEstornoOperacaoColetiva(operacao);
                            }else {
                                new TurmasServiceImpl(con).desmarcarAulasOperacaoColetiva(operacao, chave);
                            }
                        } else {
                            if(operacao.getStatus().equals(StatusOperacaoColetivaEnum.AGUARDANDO_EXCLUSAO)){
                                new BonusColetivoService().excluirBonusColetivo(con, operacao);
                            }else {
                                new BonusColetivoService().adicionarBonusColetivo(con, operacao, null);
                            }
                        }
                    }

                } catch (Exception ex) {
                    Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, ex.getMessage(), ex);
                    Uteis.logar(null, ex.getMessage());
                }
            }
        }
        Uteis.logar(null, "Processamento Operação Coletiva - em: " + Uteis.getDataComHora(Calendario.hoje()));
    }

    private static void processarOperacaoColetivaBloqueioCatraca(Connection con, OperacaoColetivaVO operacao, EmpresaVO empresa) throws Exception {
        ClienteMensagem clienteMensagemDao = new ClienteMensagem(con);
        List<ClienteVO> clientes = null;

        if (operacao.getBloquearAcessoAniversario()) {
            clientes = new Cliente(con).consultarAniversariantesPorPeriodo(
                    empresa.getCodigo(), Calendar.getInstance().getTime(), operacao.getDataFinal(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } else {
            clientes = new Cliente(con).consultarPorEmpresa(empresa.getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        Integer qtdClientesMsgIncluida = 0;
        for (ClienteVO cliente : clientes) {
            if (operacao.getPlanoVO().getCodigo() != 0) {
                String operacaoPlano = getFacade().getPlano().consultarDescricaoPorCodigo(operacao.getPlanoVO().getCodigo());
                if (!operacaoPlano.equals(cliente.getPlanoApresentar())) {
                    continue;
                }
            }

            ClienteMensagemVO clienteMensagemVO = new ClienteMensagemVO();

            Date dataBloqueio = operacao.getBloquearAcessoAniversario() ? cliente.getDataAniversario() : Calendar.getInstance().getTime();
            if (dataBloqueio == null) {
                continue;
            }

            Date dataDesbloqueio;
            if (operacao.getBloquearAcessoAniversario()) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(cliente.getDataAniversario());
                cal.add(Calendar.DAY_OF_MONTH, 1);
                dataDesbloqueio = cal.getTime();
            } else {
                dataDesbloqueio = operacao.getDataFinal();
            }

            clienteMensagemVO.setCodigoOperacaoColetiva(operacao.getCodigo());
            clienteMensagemVO.setDataBloqueio(dataBloqueio);
            clienteMensagemVO.setDataDesbloqueio(dataDesbloqueio);
            clienteMensagemVO.setMensagem(operacao.getMensagemCatraca());
            clienteMensagemVO.setBloqueio(true);
            clienteMensagemVO.setNovoObj(true);
            clienteMensagemVO.setTipomensagem(TiposMensagensEnum.CATRACA);
            clienteMensagemVO.setCliente(cliente);

            try {
                clienteMensagemDao.incluir(clienteMensagemVO);
                qtdClientesMsgIncluida++;
            } catch (Exception e) {
                Uteis.logar("Aviso: falha ao incluir ClienteMensagem para o cliente " + cliente.getCodigo(), e);
            }
        }

        operacao.setStatus(StatusOperacaoColetivaEnum.PROCESSANDO);
        String msgResultado = "Bloqueio da catraca realizado para " + qtdClientesMsgIncluida + " clientes";
        if (operacao.getPlanoVO().getCodigo() != 0) {
            String operacaoPlano = getFacade().getPlano().consultarDescricaoPorCodigo(operacao.getPlanoVO().getCodigo());
            msgResultado += " do plano " + operacaoPlano;
        }
        if(operacao.getBloquearAcessoAniversario()) {
            msgResultado += " (no dia do aniversário)";
        }
        Uteis.logar(null,  msgResultado);
        operacao.setResultado(msgResultado);
        new OperacaoColetiva(con).alterar(operacao);
    }

    private static void removerMensagensBloqueioCatraca(Connection con, OperacaoColetivaVO operacao) throws Exception {
        ClienteMensagem clienteMensagemDao = new ClienteMensagem(con);
        List<ClienteMensagemVO> listaClienteMensagemVo = clienteMensagemDao.consultarPorCodigoOperacaoColetiva(operacao.getCodigo(), true, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (listaClienteMensagemVo != null && !listaClienteMensagemVo.isEmpty()) {
            clienteMensagemDao.excluirMultiplasClienteMensagem(listaClienteMensagemVo);
            Uteis.logar(null,  "Operacao Coletiva de bloqueio de catraca: " + listaClienteMensagemVo.size() + " mensagens de bloqueio excluidas em " + Uteis.getDataComHora(Calendario.hoje()));
        }
    }



    private static RetornoAtivosVencidos consultarAtivosVencidos(Connection con, Integer codigoEmpresa, Date data) throws Exception {
        String comandoSQL = "SELECT\n"
                + "  sum(qtdAtivos) AS ativos,\n"
                + "  sum(qtdVencidos) AS vencidos,\n"
                + "  sum(qtdAgregador) as agregadores,\n"
                + "  sum(qtdAgregador_2acessos) as agregadores_2acessos,\n"
                + "  sum(qtdAgregador_3acessos) as agregadores_3acessos,\n"
                + "  sum(qtdAgregador_4acessos) as agregadores_4acessos,\n"
                + "  sum(qtdAgregador_5_ou_mais_acessos) as agregadores_5_ou_mais_acessos,\n"
                + "  sum(qtdCheckinAgregador) as qtdCheckinsAgregador\n"
                + "FROM (SELECT\n"
                + "        'A' AS tipo,\n"
                + "        count(*) AS qtdAtivos,\n"
                + "        0 AS qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        c.codigo\n"
                + "      FROM contrato c\n"
                + "      WHERE current_date >= c.vigenciaDe\n"
                + "            AND current_date <= c.vigenciaAteAjustada\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('CA', 'DE')\n"
                + "                       AND current_date >= h.datainiciosituacao \n"
                + "                       AND current_date <= h.datafinalsituacao\n"
                + "                 )\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   o.contrato\n"
                + "                 FROM contratooperacao o\n"
                + "                 WHERE o.tipooperacao IN ('TR', 'TV')\n"
                + "                       AND current_date >= o.datainicioefetivacaooperacao\n"
                + "                       AND current_date <= o.datafimefetivacaooperacao)\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   contratobaseadorenovacao\n"
                + "                 FROM contrato ct\n"
                + "                 WHERE ct.pessoa = c.pessoa\n"
                + "                       AND current_date >= ct.vigenciaDe \n"
                + "                       AND current_date <= ct.vigenciaAteAjustada \n"
                + "                 )\n"
                + "      GROUP BY c.empresa, c.codigo\n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'V' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        count(*) AS qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        c.codigo\n"
                + "      FROM contrato c\n"
                + "      WHERE c.contratoresponsavelrenovacaomatricula = 0\n"
                + "            AND c.codigo IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('VE')\n"
                + "                       AND current_date >= h.datainiciosituacao\n"
                + "                       AND current_date <= h.datafinalsituacao\n"
                + "                 )\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('DE')\n"
                + "                       AND h.datainiciosituacao >= '" + Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(data)) + "' \n"
                + "                       AND h.datainiciosituacao <= current_date\n"
                + "                )\n"
                + "      GROUP BY c.empresa, c.codigo\n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        1 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        count(*) as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso between month_ago and current_date)\n"
                + "            AND (COALESCE(p.tokengympass, '') <> ''\n"
                + "                 OR COALESCE(p.tokengogood, '') <> ''\n"
                + "                 OR p.tipototalpass)\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 1 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G2' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        1 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 2 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G3' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        1 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 3 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G4' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        1 as qtdAgregador_4acessos,\n"
                + "        0 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) = 4 \n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        'G5' AS tipo,\n"
                + "        0 AS qtdAtivos,\n"
                + "        0 as qtdVencidos,\n"
                + "        0 as qtdAgregador,\n"
                + "        0 as qtdAgregador_2acessos,\n"
                + "        0 as qtdAgregador_3acessos,\n"
                + "        0 as qtdAgregador_4acessos,\n"
                + "        1 as qtdAgregador_5_ou_mais_acessos,\n"
                + "        0 as qtdCheckinAgregador,\n"
                + "        c.empresa,\n"
                + "        p.pessoa\n"
                + "      FROM periodoacessocliente p\n"
                + "      INNER JOIN cliente c ON p.pessoa = c.pessoa\n"
                + "      WHERE (p.datainicioacesso BETWEEN month_ago AND current_date)\n"
                + "        AND (\n"
                + "          COALESCE(p.tokengympass, '') <> ''\n"
                + "          OR COALESCE(p.tokengogood, '') <> ''\n"
                + "          OR p.tipototalpass\n"
                + "        )\n"
                + "      GROUP BY c.empresa, p.pessoa\n"
                + "      having count(*) > 4) AS foo\n"
                + "  INNER JOIN empresa emp ON foo.empresa = emp.codigo AND emp.codigo = " + codigoEmpresa.toString();

        comandoSQL = comandoSQL.replaceAll("current_date", "'" + Uteis.getDataJDBC(data) + "'");
        comandoSQL = comandoSQL.replaceAll("month_ago", "'" + Uteis.getDataJDBC(Uteis.somarMeses(data, -1)) + "'");
        try(ResultSet rs = criarConsulta(comandoSQL, con)) {
            if (rs.next()) {
                return new RetornoAtivosVencidos(rs.getInt("ativos"),
                        rs.getInt("vencidos"),
                        rs.getInt("agregadores"),
                        rs.getInt("agregadores_2acessos"),
                        rs.getInt("agregadores_3acessos"),
                        rs.getInt("agregadores_4acessos"),
                        rs.getInt("agregadores_5_ou_mais_acessos"),
                        rs.getInt("qtdCheckinsAgregador"));
            }
        }
        return null;
    }

    private static void processarAtualizacaoAtivosVencidosFinanceiro(String chave, Connection con) {
        String comandoSQL = "SELECT codigo FROM empresa";
        try(ResultSet rs = criarConsulta(comandoSQL, con)) {
            while (rs.next()) {
                processarAtualizacaoAtivosVencidosFinanceiro(chave, rs.getInt("codigo"), con);
            }
        } catch (Exception ex) {
            Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, ex.getMessage(), ex);
            Uteis.logar(null, ex.getMessage());
        }
    }

    private static void processarAtualizacaoAtivosVencidosFinanceiro(String chave, Integer codigoEmpresa, Connection con) {
        Uteis.logar(null, "INICIALIZA Processamento Ativos Vencidos Financeiro - em: " + Uteis.getDataComHora(Calendario.hoje()));
        try {
            String url = String.format("%s/prest/adm/%s/consultarEmpresaFinanceiro?codigoEmpresa=%s", PropsService.getPropertyValue(PropsService.urlOamd), chave, codigoEmpresa.toString());
            String result = ExecuteRequestHttpService.executeRequestGET(url, null);
            JSONObject jsonObject = new JSONObject(result);
            if (!jsonObject.has("return")) {
                throw new Exception(String.format("Empresa não encontrada: %s / %s / %s", chave, codigoEmpresa, result));
            }

            IntegracaoFinanceiroJSON ifp = JSONMapper.getObject(jsonObject.getJSONObject("return"), IntegracaoFinanceiroJSON.class);
            if (ifp == null || ifp.getChave() == null || ifp.getChave().isEmpty()) {
                throw new Exception(String.format("Empresa não encontrada: %s / %s / %s", chave, codigoEmpresa, result));
            }

            Date hoje = Calendario.getDataComHoraZerada(Calendario.hoje());

            // Pega a próxima data para atualização
            Date dataAtu = Calendario.getInstance(Calendario.getAno(hoje), Calendario.getMes(hoje), ifp.getDiaAtualizarAtivosVencidos()).getTime();
            // Se a data da atualização já passou, é calculada a data do mês posterior
            if (Calendario.menor(dataAtu, hoje) && ifp.getUaAtivosVencidos() != null) {
                // Próximo mês
                dataAtu = Calendario.somarMeses(dataAtu, 1);
                // Garantir que o dia do mês permaneça o mesmo
                dataAtu = Calendario.getInstance(Calendario.getAno(dataAtu), Calendario.getMes(dataAtu), ifp.getDiaAtualizarAtivosVencidos()).getTime();
            }

            // Atualiza se nenhuma atualização foi feita ainda
            boolean atualizar = ifp.getUaAtivosVencidos() == null || Calendario.getAno(ifp.getUaAtivosVencidos()) < 2000;
            if (!atualizar) {
                // Atualiza se já estiver a 5 dias ou menos, da data de atualizar
                atualizar = Calendario.diferencaEmDias(hoje, dataAtu) <= 5;
            }


            if (!atualizar) {
                // Log?
                return;
            }

            if (Calendario.menorOuIgual(hoje, dataAtu)) {
                dataAtu = hoje;
            }

            RetornoAtivosVencidos ret = consultarAtivosVencidos(con, codigoEmpresa, dataAtu);

            if (ret == null) {
                throw new Exception(String.format("Ativos/vencidos da empresa não encontrados: %s / %s / %s", chave, codigoEmpresa, result));
            }

            Uteis.logar("RetornoAtivosVencidos => %s Ativos: %s, Vencidos: %s, Agregadores %s, Check-ins dos Agregadores: %s", chave, ret.ativos, ret.vencidos, ret.agregadores, ret.checkinsAgregadores);

            url = String.format("%s/prest/adm/%s/atualizarAtivosVencidos?codigoEmpresa=%s&ativos=%s&vencidos=%s&agregadores=%s&agregadores2=%s&agregadores3=%s&agregadores4=%s&agregadores5=%s&checkinsAgregadores=%s",
                    PropsService.getPropertyValue(PropsService.urlOamd), chave, codigoEmpresa, ret.ativos, ret.vencidos, ret.agregadores, ret.agregadores_2acessos, ret.agregadores_3acessos, ret.agregadores_4acessos, ret.agregadores_5_ou_mais_acessos, ret.checkinsAgregadores);
            result = ExecuteRequestHttpService.executeRequest(url, null, false, "utf-8");
            jsonObject = new JSONObject(result);
            if (jsonObject.length() == 0 || !jsonObject.has("return") && !jsonObject.has("erro")) {
                throw new Exception(String.format("atualizarAtivosVencidos - retorno inesperado: %s", result));
            }
            if (jsonObject.has("erro")) {
                throw new Exception(String.format("atualizarAtivosVencidos - erro: %s", jsonObject.getString("erro")));
            }
        } catch (Exception ex) {
            Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO, ex.getMessage(), ex);
            Uteis.logar(null, ex.getMessage());
        }
        Uteis.logar(null, "Processamento Ativos Vencidos Financeiro - em: " + Uteis.getDataComHora(Calendario.hoje()));
    }

    private static void processarDadosGerenciaisPagamento(Date dia, String key) {
        DadosGerenciaisPagamentoService service = null;
        try {
            service = new DadosGerenciaisPagamentoService(key);
            service.processaDados(dia);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            service = null;
        }
    }

    public static void processarIntegracaoMentorWeb(RoboVO robo, Connection con) throws Exception {
        Uteis.logar(null, "INICIALIZA Processamento Integracao Mentor Web - em: " + Uteis.getDataComHora(Calendario.hoje()));

        for (EmpresaVO empresa : robo.getListaEmpresa()) {
            if (!empresa.isAtiva() || !empresa.isIntegracaoMentorWebHabilitada()) {
                continue;
            }
            new MentorWebService().inicializarMentorWebService(empresa, con);
        }

        Uteis.logar(null, "FINALIZA Processamento Integracao Mentor Web - em: " + Uteis.getDataComHora(Calendario.hoje()));
    }

    public static void processarIntegracaoDelsoft(RoboVO robo, Connection con) throws Exception {
        Uteis.logar(null, "INICIALIZA Processamento Integracao Delsoft - em: " + Uteis.getDataComHora(Calendario.hoje()));

        for (EmpresaVO empresa : robo.getListaEmpresa()) {
            if (!empresa.isAtiva() || !empresa.getUtilizaIntegracaoDelsoft()) {
                continue;
            }
            new DelsoftService().inicializarDelsolftService(empresa, con);
        }

        Uteis.logar(null, "Processamento Integracao Delsoft - em: " + Uteis.getDataComHora(Calendario.hoje()));
    }

    public static void processarIntegracaoEVO(RoboVO robo, final String chave, Connection con) throws Exception {
        try {
            boolean possuiIntegracaoConfigurada = false;
            Uteis.logar(null, "INICIALIZA Processamento Integracao EVO - em: " + Uteis.getDataComHora(Calendario.hoje()));
            RedeEmpresaService.limparMapaDeRedes();

            IntegracaoMemberService integracaoMemberService = new IntegracaoMemberService(con, chave, null, TipoOperacaoIntegracaoMembersEnum.MEMBERS_FREEPASS);
            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                IntegracaoMember integracaoMemberDAO = new IntegracaoMember(con);
                List<IntegracaoMemberVO> integracoes = integracaoMemberDAO.consultarIntegracoesPorEmpresa(empresa.getCodigo());
                if (!integracoes.isEmpty()) {
                    integracaoMemberService.consultarOuCriarProdutoFreePassIntegracao();
                    possuiIntegracaoConfigurada = true;
                }

                for (IntegracaoMemberVO integracaoMemberVO : integracoes) {
                    integracaoMemberService.sincronizar(integracaoMemberVO);
                }
            }

            if (possuiIntegracaoConfigurada) {
                integracaoMemberService.integrarMembers();
            }

            Uteis.logar(null, "Processamento Integracao EVO - em: " + Uteis.getDataComHora(Calendario.hoje()));
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarIntegracaoEVO | Erro: " + ex.getMessage());
        }
    }

    public static void processarAlunosPlanosVIP(RoboVO robo, final String chave, Connection con) {
        try {
            Uteis.logar(null, "INICIOU PROCESSO DE ALUNOS VIPS - em: " + Uteis.getDataComHora(Calendario.hoje()));

            RedeEmpresaService.limparMapaDeRedes();
            RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
            if (redeEmpresa == null || !redeEmpresa.getGestaoRedes()) {
                return;
            }

            Uteis.logar(null, "REDE DE EMPRESA " + redeEmpresa.getNome() + " - em: " + Uteis.getDataComHora(Calendario.hoje()));
            if(UteisValidacao.emptyString(redeEmpresa.getServiceMap().getAcessoSistemaMsUrl())){
                throw new Exception("REDE DE EMPRESA " + redeEmpresa.getNome() + " - URL do AcessoSistemaMS não configurada - em: " + Uteis.getDataComHora(Calendario.hoje()));
            }

            Cliente clienteDAO = new Cliente(con);
            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                List<ClienteVO> clientesVipsNaoSincronizados = clienteDAO.consultarClientesVipsNaoSincronizados(empresa.getCodigo());

                Uteis.logar(null, "Empresa: " + empresa.getNome() + " - Quantidade: " + clientesVipsNaoSincronizados.size() + " - em: " + Uteis.getDataComHora(Calendario.hoje()));

                for (ClienteVO cliente : clientesVipsNaoSincronizados) {
                    if (cliente.getSincronizadoRedeEmpresa() == null) {
                        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = AcessoSistemaMSService.findByCPF(cliente.getPessoa().getCfp(), null, redeEmpresa);
                        if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                            autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO(cliente);
                            autorizacao = AcessoSistemaMSService.insertAccessAuthorization(autorizacao, chave, cliente.getEmpresa().getCodigo(), redeEmpresa);
                        } else {
                            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoModelo = new AutorizacaoAcessoGrupoEmpresarialVO(cliente);
                            autorizacao.setCodigoPessoa(autorizacaoModelo.getCodigoPessoa());
                            autorizacao.setCodAcesso(autorizacaoModelo.getCodAcesso());
                            autorizacao.setCodigoGenerico(autorizacaoModelo.getCodigoGenerico());
                            autorizacao.setAssinaturaBiometriaDigital(autorizacaoModelo.getAssinaturaBiometriaDigital());
                            autorizacao.setAssinaturaBiometriaFacial(autorizacaoModelo.getAssinaturaBiometriaFacial());
                            autorizacao = AcessoSistemaMSService.updateAccessAuthorization(autorizacao, chave, cliente.getEmpresa().getCodigo(), redeEmpresa);
                        }
                        AcessoSistemaMSService.publish(autorizacao, false, redeEmpresa);

                        if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                            clienteDAO.alterarSincronizadoRedeEmpresa(cliente.getCodigo());
                        }
                    }
                }
            }

            Uteis.logar(null, "FINALIZOU PROCESSO DE ALUNOS VIPS - em: " + Uteis.getDataComHora(Calendario.hoje()));

        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarAlunosPlanosVIP | Erro: " + ex.getMessage());
        }
    }

    private static void processarCorrigirMetasCRMNaoAtingidas(RoboVO robo, String chave, Connection con) {
        try {
            Uteis.logar(null, "INICIOU PROCESSO CORRIGIR METAS NAO ATINGIDAS - em: " + Uteis.getDataComHora(Calendario.hoje()));
            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                Uteis.logar(null, "INICIOU PROCESSO CORRIGIR METAS NAO ATINGIDAS - empresa:" + empresa.getCodigo() + " - " + empresa.getNome());
                ProcessoCorrigirMetasCRMNaoAtingidas.corrigirMetasCRMNaoAtingidas(con, empresa.getCodigo(), Calendario.somarDias(Calendario.hoje(), -1));
            }
            Uteis.logar(null, "FINALIZOU PROCESSO CORRIGIR METAS NAO ATINGIDAS - em: " + Uteis.getDataComHora(Calendario.hoje()));
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("processoCorrigirMetasNaoAtigidas | Erro: " + e.getMessage());
        }
    }

    public static void processarClientesRestricoes(RoboVO robo, String chave, Connection con) {
        try {
            Uteis.logar(null, "INICIOU PROCESSO CLIENTES RESTRIÇÕES - em: " + Uteis.getDataComHora(Calendario.hoje()));

            RedeEmpresaService.limparMapaDeRedes();
            final RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
            final boolean temRedeEmpresa = redeEmpresa != null && redeEmpresa.getGestaoRedes();

            if (temRedeEmpresa) {
                Uteis.logar(null, "REDE DE EMPRESA " + redeEmpresa.getNome() + " - em: " + Uteis.getDataComHora(Calendario.hoje()));
            } else {
                Uteis.logar(null, "CHAVE DA EMPRESA " + chave + " - em: " + Uteis.getDataComHora(Calendario.hoje()));
            }

            Cliente clienteDAO = new Cliente(con);
            ClienteRestricao clienteRestricaoDAO = new ClienteRestricao(con);

            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                incluirRestricoesClientes(empresa, chave, clienteDAO, redeEmpresa, temRedeEmpresa, clienteRestricaoDAO);
                retirarRestricoesClientes(empresa, chave, clienteDAO, redeEmpresa, temRedeEmpresa, clienteRestricaoDAO);
            }

            Uteis.logar(null, "FINALIZOU PROCESSO CLIENTES RESTRIÇÕES - em: " + Uteis.getDataComHora(Calendario.hoje()));
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarClientesRestricoes | Erro: " + ex.getMessage());
        }
    }

    private static void incluirRestricoesClientes(EmpresaVO empresa, String chave, Cliente clienteDAO, RedeEmpresaVO redeEmpresa, boolean temRedeEmpresa, ClienteRestricao clienteRestricaoDAO) throws Exception {
        if (!empresa.isAtiva() || !empresa.isUtilizaGestaoClientesComRestricoes()) {
            return;
        }

        final List<ClienteVO> clientesInadimplentes = clienteDAO.consultarClientesInadinplentesSincronizarRestricao(empresa.getCodigo(), true);
        for (ClienteVO clienteVO : clientesInadimplentes) {
            final String cpf = Uteis.tirarCaracteres(clienteVO.getPessoa().getCfp(), true);
            if (cpf.length() != 11) {
                continue;
            }
            if (UteisValidacao.emptyString(clienteVO.getPessoa().getNome())) {
                continue;
            }

            final ClienteRestricaoDTO clienteRestricaoDTO = new ClienteRestricaoDTO();
            clienteRestricaoDTO.setCodigoMatricula(clienteVO.getCodigoMatricula());
            clienteRestricaoDTO.setCpf(cpf);
            clienteRestricaoDTO.setNome(clienteVO.getPessoa().getNome());
            clienteRestricaoDTO.setCodigoEmpresa(empresa.getCodigo());
            clienteRestricaoDTO.setNomeEmpresa(empresa.getNome());
            clienteRestricaoDTO.setChaveEmpresa(chave);
            clienteRestricaoDTO.setObservacao(TipoClienteRestricaoEnum.INADIMPLENCIA.getDescricao());
            clienteRestricaoDTO.setTipo(TipoClienteRestricaoEnum.INADIMPLENCIA.getSigla());

            if (temRedeEmpresa) {
                AdmCoreMsService.incluirClienteRestricao(redeEmpresa, clienteRestricaoDTO);
            } else {
                clienteRestricaoDAO.incluir(clienteRestricaoDTO);
            }

            clienteDAO.atualizarDataInclusaoClienteRestricao(clienteVO.getCodigo(), Calendario.hoje());
        }
    }

    private static void retirarRestricoesClientes(EmpresaVO empresa, String chave, Cliente clienteDAO, RedeEmpresaVO redeEmpresa, boolean temRedeEmpresa, ClienteRestricao clienteRestricaoDAO) throws Exception {
        final List<ClienteVO> clientesRestricoesRetirar = clienteDAO.consultarClientesInadinplentesSincronizarRestricao(empresa.getCodigo(), false);

        for (ClienteVO clienteVO : clientesRestricoesRetirar) {
            final String cpf = Uteis.tirarCaracteres(clienteVO.getPessoa().getCfp(), true);
            if (cpf.length() != 11) {
                continue;
            }

            if (temRedeEmpresa) {
                AdmCoreMsService.retirarClienteRestricoes(redeEmpresa, chave, cpf, TipoClienteRestricaoEnum.INADIMPLENCIA);
            } else {
                clienteRestricaoDAO.excluir(cpf, TipoClienteRestricaoEnum.INADIMPLENCIA);
            }

            clienteDAO.atualizarDataInclusaoClienteRestricao(clienteVO.getCodigo(), null);
        }
    }

    public static void processarClientesRedeEmpresa(RoboVO robo, String chave, Connection con) {
        try {
            Uteis.logar(null, "INICIOU PROCESSO CLIENTES REDE EMPRESA - em: " + Uteis.getDataComHora(Calendario.hoje()));

            RedeEmpresaService.limparMapaDeRedes();
            RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
            if (redeEmpresa == null || !redeEmpresa.getGestaoRedes()
                    || !redeEmpresa.isSincronizarClientesNaFranqueadora()
                    || UteisValidacao.emptyString(redeEmpresa.getChaveFranqueadora())
                    || UteisValidacao.emptyNumber(redeEmpresa.getCodigoUnidadeFranqueadora())) {
                return;
            }
            Uteis.logar(null, "REDE DE EMPRESA " + redeEmpresa.getNome() + " - em: " + Uteis.getDataComHora(Calendario.hoje()));

            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                if (!empresa.isAtiva()) {
                    continue;
                }

                Cliente clienteDAO = new Cliente(con);
                List<ClienteVO> clientes = clienteDAO.consultarClientesNaoSincronizadosRedeEmpresa(empresa.getCodigo());
                for (ClienteVO clienteVO: clientes) {
                    String cpf = Uteis.tirarCaracteres(clienteVO.getPessoa().getCfp(), true);
                    if (cpf.length() != 11) {
                        continue;
                    }
                    if (UteisValidacao.emptyString(clienteVO.getPessoa().getNome())) {
                        continue;
                    }
                    ClienteRedeEmpresaDTO clienteRedeEmpresaDTO = new ClienteRedeEmpresaDTO();
                    clienteRedeEmpresaDTO.setCodigoMatricula(clienteVO.getCodigoMatricula());
                    clienteRedeEmpresaDTO.setCpf(cpf);
                    clienteRedeEmpresaDTO.setNome(clienteVO.getPessoa().getNome());
                    clienteRedeEmpresaDTO.setCodigoEmpresa(empresa.getCodigo());
                    clienteRedeEmpresaDTO.setChaveEmpresa(chave);
                    clienteRedeEmpresaDTO.setNomeEmpresa(empresa.getNome());
                    clienteRedeEmpresaDTO.setDataSincronizacao(Calendario.hoje().getTime());

                    AdmCoreMsService.salvarClienteRedeEmpresa(redeEmpresa, clienteRedeEmpresaDTO);
                    clienteDAO.atualizarDataSincronizacaoFranqueadora(clienteVO.getCodigo(), new Date(clienteRedeEmpresaDTO.getDataSincronizacao()));
                }
            }

            Uteis.logar(null, "FINALIZOU PROCESSO CLIENTES REDE EMPRESA - em: " + Uteis.getDataComHora(Calendario.hoje()));
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarClientesRestricoes | Erro: " + ex.getMessage());
        }
    }

    private static void deletarHistoricoLogsTransacao(Connection con) {
        try {
            Uteis.logarDebug("deletarHistoricoLogsTransacao | Início...");

            SuperFacadeJDBC.executarUpdate("delete from transacaowebhook where data::date <= (current_date - interval '1 month')::date;", con);
            SuperFacadeJDBC.executarUpdate("delete from historicoretornotransacao where codigo in ( \n" +
                    "select \n" +
                    "h.codigo \n" +
                    "from historicoretornotransacao h \n" +
                    "inner join transacao t on t.codigo = h.transacao \n" +
                    "where t.dataprocessamento::date <= (current_date - interval '1 month')::date);", con);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("deletarHistoricoLogsTransacao | Erro: " + ex.getMessage());
        } finally {
            Uteis.logarDebug("deletarHistoricoLogsTransacao | Fim...");
        }
    }

    public static void processarIntegracaoSesi(RoboVO robo, String chave, Connection con) {
        ConfiguracaoSistema configuracaoSistema = null;
        try {
            configuracaoSistema = new ConfiguracaoSistema(con);


            if (configuracaoSistema.realizarEnvioSesiSC()) {
                ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
                Uteis.logar(null, "INICIALIZA PROCESSO DE INTEGRAÇÃO SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));
                LocalDateTime dateTimeStart = null;
                LocalDateTime dateTimeEnd = null;
                long dateTimeDiff;

                for (EmpresaVO empresa : robo.getListaEmpresa()) {
                    if (!empresa.isAtiva()) {
                        continue;
                    }
                    Uteis.logar(null, "INICIALIZA PROCESSO DE INTEGRAÇÃO SESI - emprea: " + empresa.getNome());

                    Date dataAtual = Uteis.somarDias(robo.getDia(), -1);
                    Date inicioDoMes = Uteis.obterPrimeiroDiaMes(dataAtual);
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    String dataFormatada = simpleDateFormat.format(dataAtual);
                    String dataInicioDoMes = simpleDateFormat.format(inicioDoMes);

                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE GERAR NOTA FISCAL SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));
                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataFormatada,"gerarNotaFiscal", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível emitir as notas Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível emitir as notas Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }
                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE INTEGRAR TESOURARIA DINHEIRO SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));
                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataFormatada,"integrarTesourariaDinheiro", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível emitir TESOURARIA  Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível emitir TESOURARIA  Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }

                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE INTEGRAR PIX SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));
                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(),null, "consultaIntegracaoPix", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível emitir PIX  Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível emitir PIX  Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }
                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE Consultar tesouraria dinheiro SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));
                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataFormatada,"consultaIntegracaoTesourariaDinheiro", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível consultar a tesouraria dinheiro Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível consultar a tesouraria dinheirodo dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }
                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE Consultar deposito dinheiro SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));
                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataFormatada,"consultaIntegracaoDeposito", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível consultar a deposito dinheiro Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível consultar a deposito dinheiro do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }
                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE Consultar Notas SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));
                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataFormatada,"consultarNotaFiscal", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível consultar Notas do Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível consultar notas do Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }

                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE ErrosCartao SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));

                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataInicioDoMes,"errosCartao", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível processar ErrosCartao do Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível processar ErrosCartao Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }
                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE ErrosPix SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));

                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataInicioDoMes,"errosPix", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível processar ErrosPix do Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível processar ErrosPix Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }
                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE ErrosDeposito SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));

                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataInicioDoMes,"errosDeposito" , false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível processar ErrosDeposito do Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível processar ErrosDeposito Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }
                    try {
                        Uteis.logar(null, "INICIALIZA PROCESSO DE ErrosTesouraria SESI - em: " + Uteis.getDataComHora(Calendario.hoje()));

                        zillyonWebFacade.startThreadIntegracaoFiesc( chave, empresa.getCodigo(), dataInicioDoMes,"errosTesouraria", false);
                    } catch (Exception ex) {
                        Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.INFO,
                                empresa.getNome() +
                                        " - Não foi possível processar ErrosTesouraria do Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage(), new Object[]{});
                        Uteis.logar(null, empresa.getNome() +
                                " - Não foi possível processar ErrosTesouraria Sesi do dia " + Uteis.getData(robo.getDia()) + " em: " + Uteis.getDataComHora(Calendario.hoje()) + " - ERRO: " + ex.getMessage());
                    }

                    Uteis.logar(null, "FINALIZANDO PROCESSO DE INTEGRAÇÃO SESI - empresa: " + empresa.getNome());

                }
                zillyonWebFacade = null;

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        configuracaoSistema = null;
    }

    public static void processarContratosClienteIntegracaoFoguete(RoboVO robo, Connection con, UsuarioVO usuarioVO) throws Exception {
        Uteis.logar(null, "INICIALIZA Processamento Integracao Foguete - em: " + Uteis.getDataComHora(Calendario.hoje()));

        Empresa empresaDAO = new Empresa(con);

        if (usuarioVO == null) {
            Usuario usuarioDAO = new Usuario(con);
            usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            usuarioDAO = null;
        }

        for (EmpresaVO empresa : robo.getListaEmpresa()) {
            if (!empresa.isAtiva()) {
                continue;
            }
            ConfiguracaoIntegracaoFogueteVO configFoguete = empresaDAO.consultarConfiguracaoIntegracaoFoguete(empresa.getCodigo());
            if (!configFoguete.isHabilitada() || UteisValidacao.emptyString(configFoguete.getTokenApi())) {
                continue;
            }

            if (UteisValidacao.emptyString(configFoguete.getUrlApi())) {
                throw new ConsistirException("URL Api não está configurada para Integração Foguete!");
            }

            try (ProcessoSincronizarContratosClientesIntegracaoFoguete processo = new ProcessoSincronizarContratosClientesIntegracaoFoguete(con, empresa.getCodigo(), false, configFoguete, usuarioVO)) {
                processo.sincronizarContratosClientes();
                processo.sincronizarContratosEstornados();
            }

            LogIntegracoesVO log = new LogIntegracoesVO();
            log.setDataLancamento(new Date());
            log.setServico("INTEGRACAO_FOGUETE");
            log.setResultado(String.format("Robo - Integração processada com sucesso para a empresa: %d %s", empresa.getCodigo(), empresa.getNome()));
            LogIntegracoes logIntegracoesDAO = new LogIntegracoes(con);
            logIntegracoesDAO.incluir(log);
            logIntegracoesDAO = null;
        }
        empresaDAO = null;

        Uteis.logar(null, "Processamento Integracao Foguete - em: " + Uteis.getDataComHora(Calendario.hoje()));
    }

}
