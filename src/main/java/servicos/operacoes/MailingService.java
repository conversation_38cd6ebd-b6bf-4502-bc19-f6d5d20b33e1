package servicos.operacoes;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.thoughtworks.xstream.XStream;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import controle.financeiro.BoletoBancarioControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.basico.BoletoEmailTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GerenteTO;
import negocio.comuns.basico.InfoClienteTO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.*;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.HistoricoPontos;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.MailingHistorico;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.crm.MalingEnviados;
import negocio.facade.jdbc.crm.SmsEnviados;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.boleto.BoletoService;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.botconversa.BotConversaController;
import servicos.integracao.gymbotpro.GymbotProController;
import servicos.integracao.sms.Balance;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;
import servicos.integracao.whatsApp.WhatsAppController;
import servicos.operacoes.MailingItensController.MailingItem;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.pix.PixEmailService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servicos.util.SFTP;

import javax.mail.Address;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

import static negocio.comuns.utilitarias.Uteis.getPaintFotoDaNuvem;

/**
 * <AUTHOR>
 */
public class MailingService implements AutoCloseable {


    private Connection con;
    private String key;
    private MalaDireta malaDiretaDAO;
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
    private MailingHistorico mailingHistoricoDAO;
    private boolean envioViaServico = true;
    private Email email;
    private Telefone telefone;
    private HistoricoContato historicoContatoDAO;
    private UsuarioMovel usuarioMovelDAO;
    private RemessaItem remessaItemDAO;
    private MovParcela movParcelaDAO;
    private MovProdutoParcela movProdutoParcelaDAO;
    private Cliente clienteDAO;
    private MalingEnviados malingEnviadosDAO ;

    private SmsEnviados smse;
    private StringBuffer log;
    private MailingItensController mailingItensController;
    private Pessoa pessoaDAO;
    private Map<Integer, EmpresaVO> mapaEmpresa;

    private HistoricoPontos historicoPontosDao;
    private static final Map<String, LocalDateTime> ULTIMO_ENVIO_MAP = new ConcurrentHashMap<>();
    public MailingService(String key) throws Exception {
        this.key = key;
        this.con = new DAO().obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(key, this.con);

        malaDiretaDAO = new MalaDireta(con);
        mailingHistoricoDAO = new MailingHistorico(con);
        historicoContatoDAO = new HistoricoContato(con);
        malingEnviadosDAO = new MalingEnviados(con);
        email = new Email(con);
        telefone = new Telefone(con);
        remessaItemDAO = new RemessaItem(con);
        movParcelaDAO = new MovParcela(con);
        usuarioMovelDAO = new UsuarioMovel(con);
        movProdutoParcelaDAO = new MovProdutoParcela(con);
        configuracaoSistemaCRMVO = new ConfiguracaoSistemaCRM(con).consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        clienteDAO = new Cliente(con);
        pessoaDAO = new Pessoa(con);
        smse = new SmsEnviados(con);
        historicoPontosDao = new HistoricoPontos(con);
        inicializarDados(con);
        inicializarEnum();
    }

    public MailingService(Connection con) throws Exception {
        try {
            this.key = DAO.resolveKeyFromConnection(con);
        } catch (Exception e) {
//            this.key = "";
        }

        malaDiretaDAO = new MalaDireta(con);
        mailingHistoricoDAO = new MailingHistorico(con);
        historicoContatoDAO = new HistoricoContato(con);
        malingEnviadosDAO = new MalingEnviados(con);
        email = new Email(con);
        telefone = new Telefone(con);
        remessaItemDAO = new RemessaItem(con);
        movParcelaDAO = new MovParcela(con);
        usuarioMovelDAO = new UsuarioMovel(con);
        movProdutoParcelaDAO = new MovProdutoParcela(con);
        configuracaoSistemaCRMVO = new ConfiguracaoSistemaCRM(con).consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        clienteDAO = new Cliente(con);
        pessoaDAO = new Pessoa(con);
        smse = new SmsEnviados(con);
        historicoPontosDao = new HistoricoPontos(con);
        inicializarDados(con);
        inicializarEnum();
    }

    @Override
    public void close() throws Exception {
        if (con != null && !con.isClosed()) {
            con.close();
        }
    }

    private void inicializarDados(Connection con) throws Exception {
        Empresa empresaDAO = null;
        try {
            empresaDAO = new Empresa(con);
            mapaEmpresa = empresaDAO.obterMapaEmpresas(Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresaDAO = null;
        }
    }

    private void inicializarEnum() {
        try {
            System.out.println("Inicializando Enumeradores...");
            TipoConvenioCobrancaEnum.values();
            TipoAutorizacaoCobrancaEnum.values();
        } catch (Exception e) {
            Uteis.logar(null, e.getMessage());
        }
    }

    private static void limparEntradasAntigas() {

        LocalDateTime agora = LocalDateTime.now();
        ULTIMO_ENVIO_MAP.entrySet().removeIf(entry ->
                entry.getValue().isBefore(agora.minusMinutes(1440))
        );
    }

    private boolean validarVigenciaAgendamento(final MalaDiretaVO md) {
        TimeZone tz = TimeZone.getTimeZone(md.getEmpresa().getTimeZoneDefault());
        if (md.getAgendamentoPrevisto() && !md.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.INSTANTANEO)) {
            if (md.getVigenteAte() != null) {
                //Verifica se não está entre o período informado pelo Usuário
                if (!Calendario.entre(Calendario.hojeCalendar(tz).getTime(), Calendario.getDataComHoraZerada(md.getDataEnvio()), md.getVigenteAte())) {
                    return false;
                }
            }
            Calendar c = Calendario.getInstance(md.getDataEnvio());
            c.set(Calendar.HOUR_OF_DAY, md.getHoraInicio());
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            c.set(Calendar.MILLISECOND, 0);

            if (!Calendario.maiorOuIgual(Calendario.hojeCalendar(tz).getTime(), c.getTime())) {
                return false;
            }

            if (md.getVigenteAte() == null) {
                return !Calendario.menor(Calendario.hojeCalendar(tz).getTime(), md.getDataEnvio());
            }
        } else {
            //return md.getAgendamento().getUltimaExecucao() == null || !Calendario.maiorOuIgual(md.getAgendamento().getUltimaExecucao(), md.getAgendamento().getDataInicial());
            return  true;
        }
        return true;
    }

    private boolean validarHorarioDeEnvio(final MalaDiretaVO md) {
        //Verificar se o horário que está enviando, está dentro do período de envio.
        if(md.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.INSTANTANEO)){
            return true;
        }
        TimeZone tz = TimeZone.getTimeZone(md.getEmpresa().getTimeZoneDefault());
        Calendar cInicio = Calendario.hojeCalendar(tz);
        cInicio.set(Calendar.HOUR_OF_DAY, md.getAgendamento().getHoraInicio());
        cInicio.set(Calendar.MINUTE, 0);
        cInicio.set(Calendar.SECOND, 0);
        cInicio.set(Calendar.MILLISECOND, 0);
        Calendar cFim = Calendario.hojeCalendar(tz);
        cFim.set(Calendar.HOUR_OF_DAY, md.getAgendamento().getHoraFim());
        cFim.set(Calendar.MINUTE, 59);
        cFim.set(Calendar.SECOND, 59);
        cFim.set(Calendar.MILLISECOND, 0);

        //Irá verificar se o 'agora' está entre a hora que pode ser enviado, uma vez que o mailing está vigente.
        return Calendario.entre(Calendario.hojeCalendar(tz).getTime(), cInicio.getTime(), cFim.getTime());
    }

    public void enviarMalaDireta(Integer codigoMalaDireta) throws Exception {
        enviarMalaDireta(codigoMalaDireta, true, null);
    }

    public void enviarMalaDireta(Integer codigoMalaDireta, boolean validarAgendamento, ClienteVO cliente) throws Exception {
        log = new StringBuffer();
        limparEntradasAntigas();
        mailingItensController = new MailingItensController(SuperControle.nrLimiteDestinatariosEmail);

        MalaDiretaVO malaDiretaVO = malaDiretaDAO.consultarPorChavePrimaria(codigoMalaDireta, Uteis.NIVELMONTARDADOS_TODOS, true);
        malaDiretaVO.setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS));
        if(!malaDiretaVO.getEmpresa().isAtiva()){
            throw new ConsistirException("Empresa não está ativa");
        }

        if(malaDiretaVO.getTipoAgendamento().getDescricao().equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getDescricao())){
            if(UteisValidacao.dataMenorDataAtualSemHora(malaDiretaVO.getDataCriacao())){
                throw new ConsistirException("Contato instatâneo já disparado");
            }
        }
        TimeZone tz = TimeZone.getTimeZone(malaDiretaVO.getEmpresa().getTimeZoneDefault());

        malaDiretaVO.setCfgEvento(malaDiretaDAO.consultarConfigEvento(codigoMalaDireta, false, new ConfigEventoMailingTO()));
        if (malaDiretaVO.getCodigo() != 0) {
            MailingHistoricoVO historico = new MailingHistoricoVO();
            historico.setMalaDireta(malaDiretaVO.getCodigo());
            historico.setDataInicio(Calendario.hojeCalendar(tz).getTime());

            if (!validarAgendamento || validarVigenciaAgendamento(malaDiretaVO)) {
                if (validarAgendamento && !validarHorarioDeEnvio(malaDiretaVO)) {
                    throw new ConsistirException("Mailing está fora do horário de envio!");
                }
                //montar histórico

                //mailings criados a partir do contato ou da fase de abertura meta devem ser tratados de forma diferente
                if(cliente != null){
                    historico.setPessoasAfetadas(1);
                }else if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                    historico.setFiltro(malaDiretaVO.getSqlClientes());
                    if (!malaDiretaVO.getSqlCount().equals("")) {
                        historico.setPessoasAfetadas(SuperFacadeJDBC.contar(malaDiretaVO.getSqlCount(), con));
                    } else {
                        historico.setPessoasAfetadas(1);
                    }
                }else{
                    historico.setPessoasAfetadas(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
                historico.setStatus(StatusEnvioMailingEnum.AGUARDANDO);

                try {
                    if (malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.EMAIL)) {
                        malaDiretaVO.setMensagemComAlteracaoTag(ModeloMensagemVO.personalizarModeloMsg(malaDiretaVO.getMensagem(), malaDiretaVO.getEmpresa(), key, true));
                        //Verificando se a mensagem contem o identificador de imagem de upload
                        if (malaDiretaVO.getMensagemComAlteracaoTag().contains("malaDiretaImagemCRM")) {
                            //Alterando o caminho da imagem
                            malaDiretaVO.setMensagemComAlteracaoTag(ModeloMensagemVO.alterarCaminhoImagemParaNomeArquivo(
                                    malaDiretaVO.getMensagemComAlteracaoTag(),
                                    malaDiretaVO.getModeloMensagem().getNomeImagem(),
                                    envioViaServico));
                        }

                        obterListaEmails(malaDiretaVO, historico, cliente);

                        if (malaDiretaVO.getCfgEvento() != null &&
                                malaDiretaVO.getCfgEvento().isModeloPadraoBoleto()) {
                            malaDiretaVO.getRemetente().setNome("");
                        }

                        if ((maladiretaContemTags(malaDiretaVO, true) || configuracaoSistemaCRMVO.isEnviarEmailIndividualmente())
                                && !configuracaoSistemaCRMVO.getIntegracaoPacto()) {
                            enviarEmailMalaDiretaIndividual(malaDiretaVO, cliente);
                        } else {
                            enviarEmailMalaDireta(malaDiretaVO);
                        }
                    } else if(malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.FTP)){

                        List<AmostraClienteTO> clientes = new ArrayList<AmostraClienteTO>();
                        if(cliente == null){
                            clientes = clienteDAO.consultarAmostra(malaDiretaVO.getSqlClientesAmostra(), null);
                        }else{
                            clientes.add(cliente.toAmostraClienteTO());
                        }

                        if (UteisValidacao.emptyList(clientes)) {

                            Uteis.logar("Sem cliente para gerar Arquivo XML | maladireta: " + malaDiretaVO.getCodigo());
                            log.append("Sem cliente para gerar Arquivo XML");

                        } else {

                            for (AmostraClienteTO amostra : clientes) {
                                Integer codigoPessoa = amostra.getCodigoPessoa();
                                if (UteisValidacao.emptyNumber(codigoPessoa)) {
                                    ClienteVO clienteVO = clienteDAO.consultarPorMatricula(amostra.getMatricula(), false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                                    if (clienteVO != null) {
                                        codigoPessoa = clienteVO.getPessoa().getCodigo();
                                    }
                                }
                                amostra.setInfoCliente(clienteDAO.obterInfoCliente(key, codigoPessoa));
                                amostra.setNomeEmpresa(amostra.getInfoCliente().getNomeEmpresa());
                                amostra.getInfoCliente().setModeloContrato(amostra.getInfoCliente().obterURLModeloContrato(key));
                            }

                            MalaDiretaTO malaDiretaTO = new MalaDiretaTO(malaDiretaVO);
                            malaDiretaTO.setClientes(clientes);

                            XStream xstream = new XStream();
                            xstream.processAnnotations(MalaDiretaTO.class);
                            xstream.processAnnotations(AmostraClienteTO.class);
                            xstream.processAnnotations(InfoClienteTO.class);
                            xstream.processAnnotations(GerenteTO.class);
                            String xmlClientes = xstream.toXML(malaDiretaTO);

                            String diretorioLocalArquivoXml = PropsService.getPropertyValue(PropsService.diretorioArquivos)+ "crm/mailing/";
                            String nomeArquivoXml = "clientes-empresa-"+
                                    malaDiretaVO.getEmpresa().getCodigo()+
                                    "-maladireta-"+malaDiretaVO.getTitulo()+
                                    Calendario.getData("-dd-MM-yyyy-HH-mm-ss")+
                                    ".xml";
                            String path = Uteis.salvarArquivo(nomeArquivoXml,
                                    xmlClientes,
                                    diretorioLocalArquivoXml);

                            if (configuracaoSistemaCRMVO.getMailingFtpType().toUpperCase().equals("FTP")) {

                                try {
                                    String retorno = enviarFTP(configuracaoSistemaCRMVO.getMailingFtpServer(),
                                            configuracaoSistemaCRMVO.getMailingFtpPort(),
                                            configuracaoSistemaCRMVO.getMailingFtpUser(),
                                            configuracaoSistemaCRMVO.getMailingFtpPass(),
                                            path, configuracaoSistemaCRMVO.getMailingFtpFolder(), nomeArquivoXml);
                                    if (!retorno.equals("ok")) {
                                        throw new Exception(retorno);
                                    }
                                } catch (Exception e) {
                                    Uteis.logar("Erro ao enviar arquivo xml de maladireta para ftp: ");
                                    Uteis.logar(e, MailingService.class);
                                }

                            } else {

                                SFTP sftp = new SFTP(configuracaoSistemaCRMVO.getMailingFtpServer(),
                                        configuracaoSistemaCRMVO.getMailingFtpUser(),
                                        configuracaoSistemaCRMVO.getMailingFtpPass(),
                                        configuracaoSistemaCRMVO.getMailingFtpPort());

                                try {
                                    sftp.putFile(path, configuracaoSistemaCRMVO.getMailingFtpFolder(), configuracaoSistemaCRMVO.getMailingFtpType());
                                } catch (Exception e) {
                                    Uteis.logar("Erro ao enviar arquivo xml de maladireta para sftp: ");
                                    Uteis.logar(e, MailingService.class);
                                }
                            }
                        }

                    } else if (malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.APP)) {
                        malaDiretaVO.setMensagemComAlteracaoTag(ModeloMensagemVO.personalizarModeloMsg(malaDiretaVO.getMensagem(), malaDiretaVO.getEmpresa(), key, false));
                        List<Integer> cods = email.obterCodigosClientes(malaDiretaVO.getSqlCodCliente());
                        for (Integer i : cods) {
                            boolean consultarClienteTem = usuarioMovelDAO.consultarClienteTem(i);
                            if(!consultarClienteTem){
                                mailingItensController.addExcluido(i, ErroEnvioMailing.NAO_ESTA_NO_TREINO.getCodigo(),0);
                                continue;
                            }
                            String nomeCliente = clienteDAO.consultarNomePorCodigoCliente(i);
                            String mensagem = ModeloMensagemVO.personalizarTagNomePessoa(malaDiretaVO.getMensagemComAlteracaoTag(), nomeCliente);
                            if(mensagem.contains("TAG_SALDOPONTOS")) {
                                Integer saldoPontos = historicoPontosDao.obterPontosTotalPorCliente(i);
                                mensagem = mensagem.replace("TAG_SALDOPONTOS", saldoPontos.toString());
                            }
                            try {
                                mensagem = resolverTagPesquisaMalaDireta(malaDiretaVO, mensagem, pessoaDAO.obterPessoaCliente(i), i,false);
                            } catch (Exception ignore) {
                            }

                            String retornoGerarNotificacoes = TreinoWSConsumer.gerarNotificacoes(key, malaDiretaVO, (i.toString()), mensagem);
                            if (retornoGerarNotificacoes.startsWith("ERRO:")) {
                                mailingItensController.addExcluido(i, retornoGerarNotificacoes, 0);
                            } else {
                                String[] itens = retornoGerarNotificacoes.split(";");
                                for (String item : itens) {
                                    mailingItensController.add(i, "",0);
                                    try {
                                        historicoContatoDAO.executarGravacaoVindoEmail(malaDiretaVO, 0, i, 0, i, false);
                                    } catch (Exception e) {
                                        Uteis.logar(e, this.getClass());
                                    }
                                }
                            }
                        }

                        for(MailingItensController.MailingItem mi : mailingItensController.getItemsAguardando()){
                            mi.setEnviado(true);
                        }

                    } else if (malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.WHATSAPP)){
                        try {
                            List<Message> enviarMessage  = obterListaTelefones(malaDiretaVO, historico, cliente);
                            WhatsAppController service = new WhatsAppController(key, malaDiretaVO, this.con);
                            String ret =  service.sendMessage(key,malaDiretaVO.getEmpresa().getCodigo() ,malaDiretaVO.getIdTemplate(),enviarMessage );
                            log.append(ret);
                            for(MailingItensController.MailingItem mi : mailingItensController.getItemsAguardando()){
                                mi.setEnviado(true);
                            }
                            historico.setLog(log.toString());
                        }
                        catch (Exception ex){
                            throw new RuntimeException(ex);
                        }

                    }else if (malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT)){
                        try {
                            List<Message> enviarMessage  = obterListaTelefones(malaDiretaVO, historico, cliente);
                            BotConversaController service = new BotConversaController(key, malaDiretaVO, this.con);
                            ConfiguracaoIntegracaoBotConversaVO fluxoBotConversa = malaDiretaDAO.buscarFluxoBotConversa(con, malaDiretaVO.getUrlwebhoobotconversa(),"0");

                            if (fluxoBotConversa != null) {
                                malaDiretaVO.setCodigoFluxoGymBotConversa(fluxoBotConversa.getCodigo());
                            }

                            String ret = service.sendMessage(key,malaDiretaVO.getEmpresa().getCodigo() ,malaDiretaVO.getUrlwebhoobotconversa(),enviarMessage );
                            log.append(ret);
                            for(MailingItensController.MailingItem mi : mailingItensController.getItemsAguardando()){
                                mi.setEnviado(true);
                            }
                            historico.setLog(log.toString());
                        }
                        catch (Exception ex){
                            for(MailingItensController.MailingItem mi : mailingItensController.getItemsAguardando()){
                                mi.setEnviado(false).setErro(String.valueOf(ErroEnvioMailing.BOTCONVERSA_INVALIDO.getCodigo()));
                            }
                            throw new RuntimeException(ex);
                        }
                    } else if (malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT_PRO)){
                        try {
                            List<Message> enviarMessage = obterListaTelefones(malaDiretaVO, historico, cliente);
                            ConfiguracaoIntegracaoGymbotProVO configuracaoIntegracaoGymbotProVO = malaDiretaVO.getConfigGymbotPro();
                            GymbotProController service = new GymbotProController(key, malaDiretaVO, this.con);
                            String ret = service.sendMessage(key,malaDiretaVO.getEmpresa().getCodigo(),configuracaoIntegracaoGymbotProVO.getToken(),configuracaoIntegracaoGymbotProVO.getIdFluxo(),enviarMessage);

                            log.append(ret);
                            for(MailingItensController.MailingItem mi : mailingItensController.getItemsAguardando()){
                                mi.setEnviado(true);
                            }
                            historico.setLog(log.toString());
                        }
                        catch (Exception ex){
                            for(MailingItensController.MailingItem mi : mailingItensController.getItemsAguardando()){
                                mi.setEnviado(false).setErro(String.valueOf(ErroEnvioMailing.BOTCONVERSA_INVALIDO.getCodigo()));
                            }
                            throw new RuntimeException(ex);
                        }
                    } else if (malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.WEBHOOK)) {
                        enviarMalaDiretaWebHook(codigoMalaDireta, validarAgendamento, key);
                    } else {
                        String tokenSMS = malaDiretaVO.getSmsMarketing() ? malaDiretaVO.getEmpresa().getTokenSMSShortCode() : malaDiretaVO.getEmpresa().getTokenSMS();
                        SmsController smsController = new SmsController(tokenSMS, key, TimeZone.getTimeZone(malaDiretaVO.getEmpresa().getTimeZoneDefault()));
                        String retorno = smsController.sendMessage(codigoMalaDireta.toString(), obterListaTelefones(malaDiretaVO, historico, cliente));

                        List<Message> enviadosNoServidor = smsController.getSent(codigoMalaDireta.toString());
                        smse.excluir(Calendario.ontem());

                        for(MailingItensController.MailingItem mi : mailingItensController.getItemsAguardando()){
                            if(enviadosNoServidor.contains(new Message(Long.parseLong(mi.getValor().replaceAll("\\D","")) %100000000000L))){
                                mi.setEnviado(true);
                                SmsEnviados sms = new SmsEnviados(con);

                            }else{
                                mi.setEnviado(false).setErro(String.valueOf(ErroEnvioMailing.INVALIDO_BLOQUEADO.getCodigo()));
                            }
                        }

                        mailingItensController.marcarTodosNaoEnviado();

                        //String retorno = enviarSMSMalaDireta(malaDiretaVO, obterListaTelefones(malaDiretaVO, historico));
                        if (retorno.contains("ERRO")){
                            throw new ConsistirException(retorno);
                        }

                        Balance saldoRemanecente = smsController.getSaldoRemanecente();
                        log.append("Retorno serviço sms: ").append("Enviados[")
                                .append(mailingItensController.getQtdeEnviados())
                                .append("] Não enviados[").append(mailingItensController.getQtdeNaoEnviados())
                                .append("] Saldo [").append(saldoRemanecente.getBalance()).append("]");

                        historico.setSaldo(saldoRemanecente.getBalance());
                    }

                    historicoContatoDAO.gravarHistoricoContatoMalaDiretaAgendado(malaDiretaVO, cliente, mailingItensController, configuracaoSistemaCRMVO.getIntegracaoPacto());
                    historico.setStatus(StatusEnvioMailingEnum.CONCLUIDO);
                } catch (Exception e) {
                    historico.setStatus(mailingItensController.getEnviados().length() > 0 ? StatusEnvioMailingEnum.CONCLUIDO_ERRO : StatusEnvioMailingEnum.ERRO);

                    //marca todos que estao pendente como nao enviados
                    mailingItensController.marcarTodosNaoEnviado();

                    if (e.getMessage() == null) {
                        log.append(NullPointerException.class.getName()).append("<br/>");
                    } else {
                        log.append(e.getMessage()).append("<br/>");
                        if (e.getMessage().contains("send command to SMTP host")) {
                            log.append("Possivelmente o seu limite de envios foi atingido<br/>");
                        }
                    }
                }
                malaDiretaDAO.preencherDataEnvio(malaDiretaVO, Calendario.hojeCalendar(tz).getTime());
                malaDiretaDAO.preencherDataUltimaExecucao(malaDiretaVO, Calendario.hojeCalendar(tz).getTime());
                historico.setLog(log.toString());
                historico.setClientesEnviados(mailingItensController.getEnviados().toString().replaceFirst(",", ""));
                historico.setClientesNaoEnviados(mailingItensController.getEnviadosExcluidos().toString().replaceFirst(",", ""));
                if(StringUtils.countMatches(historico.getClientesNaoEnviados(),"timeout") >= 3){
                    malaDiretaDAO.atualizarStatusEntregabilidade(malaDiretaVO, false);
                } else {
                    malaDiretaDAO.atualizarStatusEntregabilidade(malaDiretaVO, true);
                }
                historico.setDataFim(Calendario.hoje());
                if (malaDiretaVO.getMeioDeEnvio().equals(MeioEnvio.EMAIL.getCodigo())) {
                    historico.setSaldo(null);
                }
                if (malaDiretaVO.getMeioDeEnvio().equals(MeioEnvio.GYMBOT.getCodigo())) {
                    historico.setSaldo(null);
                }
                mailingHistoricoDAO.incluir(historico);
            } else {
                historico.setLog("Mailing não está vigente!");
                historico.setClientesEnviados("");
                historico.setClientesNaoEnviados("");
                historico.setStatus(StatusEnvioMailingEnum.ERRO);
                malaDiretaDAO.alterarSituacaoExclusao(malaDiretaVO, false);
                mailingHistoricoDAO.incluir(historico);
                throw new ConsistirException("Mailing não está vigente!");
            }
        }
        try {
            LogVO log = new LogVO();
            log.setChavePrimaria(malaDiretaVO.getCodigo().toString());


            if (mailingItensController != null && mailingItensController.getItems() != null
                    && !mailingItensController.getItems().isEmpty()) {
                MailingItensController.MailingItem primeiroItem = mailingItensController.getItems().get(0);
                if (primeiroItem != null && primeiroItem.getCodigo() > 0) {
                    ClienteVO clienteTemp = clienteDAO.consultarPorChavePrimaria(primeiroItem.getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (clienteTemp != null && clienteTemp.getPessoa() != null) {
                        log.setPessoa(clienteTemp.getPessoa().getCodigo());
                    }
                }
            }

            log.setNomeEntidade("MALADIRETA");
            log.setNomeEntidadeDescricao("MALADIRETA");
            log.setOperacao("GERAÇÃO DE BOLETO AUTOMÁTICO");
            log.setResponsavelAlteracao("RECORRENCIA");
            log.setUserOAMD("");
            log.setNomeCampo("ENVIO");
            log.setDataAlteracao(Calendario.hojeSemRequest());
            log.setValorCampoAnterior("");

            StringBuilder infoLog = new StringBuilder();

            try {
                if (malaDiretaVO.getCfgEvento() != null &&
                        malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo()) {

                    EmpresaVO empresaVO = malaDiretaVO.getEmpresa();
                    if (empresaVO != null) {

                        ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);

                        List<ConvenioCobrancaVO> conveniosBoleto = convenioCobrancaDAO.consultarPorTipoCobranca(
                                new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO, TipoCobrancaEnum.BOLETO_ONLINE},
                                empresaVO.getCodigo(),
                                SituacaoConvenioCobranca.ATIVO,
                                null,
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS
                        );

                        if (!UteisValidacao.emptyList(conveniosBoleto)) {
                            infoLog.append(" | Convênio: ").append(conveniosBoleto.get(0).getDescricao());
                        }

                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            log.setValorCampoAlterado(infoLog.toString());
            getFacade().getLog().incluir(log);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void enviarMalaDiretaWebHook(Integer codigoMalaDireta, boolean validarAgendamento, String chave) throws Exception {
        log = new StringBuffer();
        limparEntradasAntigas();
        mailingItensController = new MailingItensController(SuperControle.nrLimiteDestinatariosEmail);

        if (con == null) {
            con = new DAO().obterConexaoEspecifica(key);
        }

        MalaDiretaVO malaDiretaVO = malaDiretaDAO.consultarPorChavePrimaria(codigoMalaDireta, Uteis.NIVELMONTARDADOS_TODOS, true);
        malaDiretaVO.setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS));
        if (UteisValidacao.emptyString(malaDiretaVO.getUrlWebhook())) {
            throw new ConsistirException("URL Webhook não configurada");
        }

        if(!malaDiretaVO.getEmpresa().isAtiva()){
            throw new ConsistirException("Empresa não está ativa");
        }

        if(malaDiretaVO.getTipoAgendamento().getDescricao().equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getDescricao())){
            if(UteisValidacao.dataMenorDataAtualSemHora(malaDiretaVO.getDataCriacao())){
                throw new ConsistirException("Contato instatâneo já disparado");
            }
        }
        TimeZone tz = TimeZone.getTimeZone(malaDiretaVO.getEmpresa().getTimeZoneDefault());

        malaDiretaVO.setCfgEvento(malaDiretaDAO.consultarConfigEvento(codigoMalaDireta, false, new ConfigEventoMailingTO()));
        if (malaDiretaVO.getCodigo() != 0) {
            MailingHistoricoVO historico = new MailingHistoricoVO();
            historico.setMalaDireta(malaDiretaVO.getCodigo());
            historico.setDataInicio(Calendario.hojeCalendar(tz).getTime());

            if (!validarAgendamento || validarVigenciaAgendamento(malaDiretaVO)) {
                if (validarAgendamento && !validarHorarioDeEnvio(malaDiretaVO)) {
                    throw new ConsistirException("Mailing está fora do horário de envio!");
                }
                //montar histórico

                //mailings criados a partir do contato ou da fase de abertura meta devem ser tratados de forma diferente
                if (malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
                    historico.setFiltro(malaDiretaVO.getSqlClientes());
                    if (!malaDiretaVO.getSqlCount().equals("")) {
                        historico.setPessoasAfetadas(SuperFacadeJDBC.contar(malaDiretaVO.getSqlCount(), con));
                    } else {
                        historico.setPessoasAfetadas(1);
                    }
                }else{
                    historico.setPessoasAfetadas(malaDiretaVO.getMalaDiretaEnviadaVOs().size());
                }
                historico.setStatus(StatusEnvioMailingEnum.AGUARDANDO);

                try {
                    if (malaDiretaVO.getMeioDeEnvioEnum().equals(MeioEnvio.WEBHOOK)) {

                        if (malaDiretaVO.getSql().contains("distinct  %s FROM")) {
                            malaDiretaVO.setSql(malaDiretaVO.getSql().replaceFirst("distinct", ""));
                        }

                        List<AmostraClienteTO> amostraClienteTOS = getFacade().getCliente().consultarAmostra(malaDiretaVO.getSqlClientesAmostra()
                                + (malaDiretaVO.getCfgEvento().getIndicados() ? " ORDER BY nome " :  " ORDER BY sw.nomecliente"), null);

                        JSONObject json = new JSONObject();
                        String descEvento = "";
                        if (malaDiretaVO.getEvento() != null && TipoEventoEnum.obter(malaDiretaVO.getEvento()) != null) {
                            descEvento = TipoEventoEnum.obter(malaDiretaVO.getEvento()).getDescricao();
                        }
                        json.put("evento", descEvento);
                        if (!UteisValidacao.emptyNumber(malaDiretaVO.getEvento()) && TipoEventoEnum.obter(malaDiretaVO.getEvento()) != null) {
                            json.put("evento_enum", TipoEventoEnum.obter(malaDiretaVO.getEvento()));
                        }
                        json.put("titulo", malaDiretaVO.getTitulo());
                        JSONArray jsonArrayClientes = new JSONArray();

                        for (AmostraClienteTO amostraClienteTO : amostraClienteTOS) {
                            jsonArrayClientes.put(amostraClienteTO.toJSON());
                        }
                        json.put("clientes", jsonArrayClientes);

                        Map<String, String> headers = new HashMap<>();
                        headers.put("Content-Type", "application/json");
                        headers.put("Chave", chave);
                        RequestHttpService service = new RequestHttpService();
                        service.connectTimeout = 5000;
                        service.connectionRequestTimeout = 30000;
                        RespostaHttpDTO respostaHttpDTO = service.executeRequest(malaDiretaVO.getUrlWebhook(), headers, null, json.toString(), MetodoHttpEnum.POST);
                        service = null;
                        int status = respostaHttpDTO == null ? -1 : respostaHttpDTO.getHttpStatus();
                        if (status < 200 || status >= 300) {
                            String body = respostaHttpDTO != null ? respostaHttpDTO.getResponse() : "Sem resposta";
                            throw new Exception("Falha no WebHook. HTTP " + status + " | " + body);

                        }

                    }
                    historico.setStatus(StatusEnvioMailingEnum.CONCLUIDO);
                } catch (Exception e) {
                    historico.setStatus(mailingItensController.getEnviados().length() > 0 ? StatusEnvioMailingEnum.CONCLUIDO_ERRO : StatusEnvioMailingEnum.ERRO);

                    //marca todos que estao pendente como nao enviados
                    mailingItensController.marcarTodosNaoEnviado();

                    if (e.getMessage() == null) {
                        log.append(NullPointerException.class.getName()).append("<br/>");
                    } else {
                        log.append(e.getMessage()).append("<br/>");
                        if (e.getMessage().contains("send command to SMTP host")) {
                            log.append("Possivelmente o seu limite de envios foi atingido<br/>");
                        }
                    }
                }
                malaDiretaDAO.preencherDataEnvio(malaDiretaVO, Calendario.hojeCalendar(tz).getTime());
                malaDiretaDAO.preencherDataUltimaExecucao(malaDiretaVO, Calendario.hojeCalendar(tz).getTime());
                historico.setLog(log.toString());
                historico.setClientesEnviados(mailingItensController.getEnviados().toString().replaceFirst(",", ""));
                historico.setClientesNaoEnviados(mailingItensController.getEnviadosExcluidos().toString().replaceFirst(",", ""));
                if(StringUtils.countMatches(historico.getClientesNaoEnviados(),"timeout") >= 3){
                    malaDiretaDAO.atualizarStatusEntregabilidade(malaDiretaVO, false);
                } else {
                    malaDiretaDAO.atualizarStatusEntregabilidade(malaDiretaVO, true);
                }
                historico.setDataFim(Calendario.hoje());
                if (malaDiretaVO.getMeioDeEnvio().equals(MeioEnvio.EMAIL.getCodigo())) {
                    historico.setSaldo(null);
                }
                if (malaDiretaVO.getMeioDeEnvio().equals(MeioEnvio.GYMBOT.getCodigo())) {
                    historico.setSaldo(null);
                }
                mailingHistoricoDAO.incluir(historico);
            } else {
                historico.setLog("Mailing não está vigente!");
                historico.setClientesEnviados("");
                historico.setClientesNaoEnviados("");
                historico.setStatus(StatusEnvioMailingEnum.ERRO);
                malaDiretaDAO.alterarSituacaoExclusao(malaDiretaVO, false);
                mailingHistoricoDAO.incluir(historico);
                throw new ConsistirException("Mailing não está vigente!");
            }
        }
        try {
            LogVO log = new LogVO();
            log.setChavePrimaria(malaDiretaVO.getCodigo().toString());


            if (mailingItensController != null && mailingItensController.getItems() != null
                    && !mailingItensController.getItems().isEmpty()) {
                MailingItensController.MailingItem primeiroItem = mailingItensController.getItems().get(0);
                if (primeiroItem != null && primeiroItem.getCodigo() > 0) {
                    ClienteVO clienteTemp = clienteDAO.consultarPorChavePrimaria(primeiroItem.getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (clienteTemp != null && clienteTemp.getPessoa() != null) {
                        log.setPessoa(clienteTemp.getPessoa().getCodigo());
                    }
                }
            }

            log.setNomeEntidade("MALADIRETA");
            log.setNomeEntidadeDescricao("MALADIRETA");
            log.setOperacao("GERAÇÃO DE BOLETO AUTOMÁTICO");
            log.setResponsavelAlteracao("RECORRENCIA");
            log.setUserOAMD("");
            log.setNomeCampo("ENVIO");
            log.setDataAlteracao(Calendario.hojeSemRequest());
            log.setValorCampoAnterior("");

            StringBuilder infoLog = new StringBuilder();

            try {
                if (malaDiretaVO.getCfgEvento() != null &&
                        malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo()) {

                    EmpresaVO empresaVO = malaDiretaVO.getEmpresa();
                    if (empresaVO != null) {

                        ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);

                        List<ConvenioCobrancaVO> conveniosBoleto = convenioCobrancaDAO.consultarPorTipoCobranca(
                                new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO, TipoCobrancaEnum.BOLETO_ONLINE},
                                empresaVO.getCodigo(),
                                SituacaoConvenioCobranca.ATIVO,
                                null,
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS
                        );

                        if (!UteisValidacao.emptyList(conveniosBoleto)) {
                            infoLog.append(" | Convênio: ").append(conveniosBoleto.get(0).getDescricao());
                        }

                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            log.setValorCampoAlterado(infoLog.toString());
            getFacade().getLog().incluir(log);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean verificarMensagemEnviada(String chave, long minutos) {
        LocalDateTime agora = LocalDateTime.now();
        LocalDateTime limite = agora.minusMinutes(minutos);

        LocalDateTime ultimoEnvio = ULTIMO_ENVIO_MAP.get(chave);

        if (ultimoEnvio != null && ultimoEnvio.isAfter(limite)) {
            return true;
        }

        ULTIMO_ENVIO_MAP.put(chave, agora);
        return false;
    }
    /**
     * Verifica se a {@link MalaDiretaVO} contém uma tag dentro de sua mensagem.
     * @param mala
     * @return
     */
    private boolean maladiretaContemTags(MalaDiretaVO mala, boolean semTagsSandy){
        boolean contemTags = false;
        if(mala.getMensagem() != null){
            List<String> tags = semTagsSandy ? obterTagsNaoValidandoTagsSendy() : MalaDiretaVO.TAGS;
            for(String tag : tags){
                if(mala.getMensagem().contains(tag)){
                    contemTags = true;
                    break;
                }
            }
        }
        return contemTags;
    }

    private List<String> obterTagsNaoValidandoTagsSendy() {
        List<String> tagsTodas = MalaDiretaVO.TAGS;
        List<String> tagsValidar = new ArrayList<>();
        if (configuracaoSistemaCRMVO.getIntegracaoPacto()) {
            for (String tag : tagsTodas) {
                if (tag.equals("TAG_NOME")) {
                    continue;
                } else if (tag.equals("TAG_PNOME")) {
                    continue;
                } else if (tag.equals("TAG_CODCLIENTE")) {
                    continue;
                } else if (tag.equals("TAG_EMAIL_CLIENTE")) {
                    continue;
                } else if (tag.equals("TAG_BOLETO")) {
                    continue;
                }
                tagsValidar.add(tag);
            }
        }
        return tagsValidar;
    }

    private void obterListaEmails(MalaDiretaVO malaDiretaVO, MailingHistoricoVO historico) throws Exception {
        obterListaEmails(malaDiretaVO, historico, null);
    }

    private void obterListaEmails(MalaDiretaVO malaDiretaVO, MailingHistoricoVO historico, ClienteVO cliente) throws Exception {
        if(cliente != null &&
                cliente.getPessoa() != null &&
                cliente.getPessoa().getEmailVOs() != null
        ){
            for (EmailVO email: cliente.getPessoa().getEmailVOs()) {
                mailingItensController.add(cliente.getCodigo(),  email.getEmail(), email.getBounced());
            }
        }

        if(malaDiretaVO.getTipoAgendamento() != null){
            switch (malaDiretaVO.getTipoAgendamento()) {
                case AGENDAMENTO_INSTANTANEO:
                    for (MalaDiretaEnviadaVO malaDiretaEnviadaVO : malaDiretaVO.getMalaDiretaEnviadaVOs()) {
                        //--------------- envio para pessoa --------------- //
                        if (malaDiretaEnviadaVO.getClienteVO().getPessoa().getCodigo() != 0) {
                            for (Object obj : malaDiretaEnviadaVO.getClienteVO().getPessoa().getEmailVOs()) {
                                EmailVO em = (EmailVO) obj;

                                if (!UteisValidacao.emptyString(em.getEmail())
                                        && UteisEmail.getValidEmail(em.getEmail())) {
                                    mailingItensController.add(malaDiretaEnviadaVO.getClienteVO().getCodigo(), em.getEmail(), em.getBounced());
                                }else{
                                    mailingItensController.addExcluido(malaDiretaEnviadaVO.getClienteVO().getCodigo(), "", em.getBounced());
                                }
                            }
                        }
                        //--------------- envio para passivo --------------- //
                        if (malaDiretaEnviadaVO.getPassivoVO().getCodigo() != 0) {
                            if (!UteisValidacao.emptyString(malaDiretaEnviadaVO.getPassivoVO().getEmail())
                                    && UteisEmail.getValidEmail(malaDiretaEnviadaVO.getPassivoVO().getEmail())) {
                                mailingItensController.add(malaDiretaEnviadaVO.getPassivoVO().getCodigo(), malaDiretaEnviadaVO.getPassivoVO().getEmail(), malaDiretaEnviadaVO.getPassivoVO().getBounced() );
                            }else{
                                mailingItensController.addExcluido(malaDiretaEnviadaVO.getPassivoVO().getCodigo(), "",malaDiretaEnviadaVO.getPassivoVO().getBounced() );
                            }
                        }
                        //--------------- envio para indicado --------------- //
                        if (malaDiretaEnviadaVO.getIndicadoVO().getCodigo() != 0) {
                            if (!UteisValidacao.emptyString(malaDiretaEnviadaVO.getIndicadoVO().getEmail())
                                    && UteisEmail.getValidEmail(malaDiretaEnviadaVO.getIndicadoVO().getEmail())) {
                                mailingItensController.add(malaDiretaEnviadaVO.getIndicadoVO().getCodigo(), malaDiretaEnviadaVO.getIndicadoVO().getEmail(), malaDiretaEnviadaVO.getIndicadoVO().getBounced());
                            }else{
                                mailingItensController.addExcluido(malaDiretaEnviadaVO.getIndicadoVO().getCodigo(), "",0);
                            }
                        }
                    }
                    if (malaDiretaVO.getMalaDiretaEnviadaVOs().size() == 0) {
                        obterEmailsParaMalaDireta(malaDiretaVO, cliente);
                    }
                    break;
                case AGENDAMENTO_PREVISTO:
                    obterEmailsParaMalaDireta(malaDiretaVO, cliente);
                    break;
            }
        }
        historico.setRegistrosAfetados(mailingItensController.getRegistroAfetados());
    }

    private void obterEmailsParaMalaDireta(MalaDiretaVO malaDiretaVO) throws Exception {
        obterEmailsParaMalaDireta(malaDiretaVO, null);
    }

    private void obterEmailsParaMalaDireta(MalaDiretaVO malaDiretaVO, ClienteVO cliente) throws Exception {
        List<EmailVO> emails;
        if(cliente != null){
            emails = cliente.getPessoa().getEmailVOs();
        }else if(malaDiretaVO.getCfgEvento().getEventoCodigo() != null
                && malaDiretaVO.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.INDICADOS.getCodigo())){
            emails = email.obterEmailsIndicados(malaDiretaVO.getSqlClientes());
        }else{
            emails = email.obterEmailsEnvioAgendado(malaDiretaVO.getSqlPessoa());
        }

        for (EmailVO email : emails) {
            if (UteisEmail.getValidEmail(email.getEmail())) {
                mailingItensController.add(email.getCliente(), email.getEmail(), email.getBounced());
            } else {
                mailingItensController.addExcluido(email.getCliente(), ErroEnvioMailing.EMAIL_INVALIDO.getCodigo(), email.getBounced());
            }
        }
    }

    private void enviarEmailMalaDiretaIndividual(MalaDiretaVO malaDiretaVO, ClienteVO cliente) throws Exception {
        UteisEmail uteisEmail = new UteisEmail();
        uteisEmail.novo(malaDiretaVO.getTitulo(), configuracaoSistemaCRMVO);
        uteisEmail.setRemetente(malaDiretaVO.getRemetente());
        uteisEmail.setChave(key);
        uteisEmail.setEmpresaVO(malaDiretaVO.getEmpresa());
        uteisEmail.setMalaDiretaVO(malaDiretaVO);
        Integer ocorrencia =  malaDiretaVO.getCfgEvento().getOcorrencia();

        if (mailingItensController.getRegistroAfetados() == 0 || malaDiretaVO.getMalaDiretaEnviadaVOs().isEmpty()) {
            List<EmailVO> listaEmails = new ArrayList<EmailVO>();

            if (malaDiretaVO.getCfgEvento().getEventoCodigo() != null && malaDiretaVO.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.INDICADOS.getCodigo())) {
                listaEmails = email.obterEmailsIndicados(malaDiretaVO.getSqlClientes());
            }else if(cliente != null &&
                    cliente.getPessoa() != null &&
                    cliente.getPessoa().getEmailVOs() != null
                    && (malaDiretaVO.getSql().equals("") || (ocorrencia >= 6 && ocorrencia <= 10))
            ){
                for (EmailVO email: cliente.getPessoa().getEmailVOs()) {
                    email.setCliente(cliente.getCodigo());
                    listaEmails.add(email);
                }
            } else if (malaDiretaVO.getCfgEvento() == null || !malaDiretaVO.getCfgEvento().getItensNaoAprovados() && !malaDiretaVO.getSql().equals("")) {
                listaEmails = email.obterEmailsEnvioAgendado(malaDiretaVO.getSqlPessoa());
            }

            if (malaDiretaVO.getCfgEvento().getEventoCodigo() != null && malaDiretaVO.getCfgEvento().getItensNaoAprovados()) {

                List<AmostraClienteTO> listaAmostra = clienteDAO.consultarAmostra(malaDiretaVO.getSqlClientesAmostra(), null);

                for (AmostraClienteTO amostraClienteTO : listaAmostra) {
                    PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(amostraClienteTO.getCodigoPessoa(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                    listaEmails = email.consultarEmails(amostraClienteTO.getCodigoPessoa(), true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    List<MailingItensController.MailingItem> emails = new ArrayList<>();
                    int emailsEnviadosHoje = malaDiretaDAO.countEmails(Calendario.hoje(), true, configuracaoSistemaCRMVO.getIntegracaoPacto());
                    int emailsEnviadosMes = malaDiretaDAO.countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, configuracaoSistemaCRMVO.getIntegracaoPacto());
                    emails.addAll(uteisEmail.validarEmailsEnviarHoje(emailsEnviadosHoje, mailingItensController.getItems(), configuracaoSistemaCRMVO));
                    emails.addAll(uteisEmail.validarEmailsEnviarMensal(emailsEnviadosMes, mailingItensController.getItems(), configuracaoSistemaCRMVO));
                    for (EmailVO emailVO : listaEmails) {
                        emailVO.setPessoaVO(pessoaVO);
                        for (MailingItensController.MailingItem email : mailingItensController.getItems()) {
                            if (emailVO.getCliente().equals(email.getCodigo())) {
                                realizarEnvioEmail(uteisEmail, emailVO, malaDiretaVO, amostraClienteTO);
                            }
                        }
                    }
                    for(MailingItensController.MailingItem mail : emails){
                        mail.setEnviado(false).setErro("Limite excedido");
                    }
                    mailingItensController.getItems().addAll(emails);
                }

            } else {
                int emailsEnviadosHoje = malaDiretaDAO.countEmails(Calendario.hoje(), true, configuracaoSistemaCRMVO.getIntegracaoPacto());
                int emailsEnviadosMes = malaDiretaDAO.countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, configuracaoSistemaCRMVO.getIntegracaoPacto());
                List<MailingItensController.MailingItem> emails = new ArrayList<>();
                emails.addAll(uteisEmail.validarEmailsEnviarHoje(emailsEnviadosHoje, mailingItensController.getItems(), configuracaoSistemaCRMVO));
                emails.addAll(uteisEmail.validarEmailsEnviarMensal(emailsEnviadosMes, mailingItensController.getItems(), configuracaoSistemaCRMVO));
                for (EmailVO emailVO : listaEmails) {
                    realizarEnvioEmail(uteisEmail, emailVO, malaDiretaVO, null, cliente);
                    mailingItensController.marcarEmailEnviado(emailVO.getEmail());
                    if (UteisValidacao.emptyNumber(emailVO.getCliente())){
                        ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(emailVO.getPessoa(), Uteis.NIVELMONTARDADOS_MINIMOS);
                        emailVO.setCliente(clienteVO.getCodigo());
                    }
                    malingEnviadosDAO.incluir( new MalingEnviadosVO(
                            emailVO.getCliente(),
                            emailVO.getPessoa(),
                            Calendario.hoje(),
                            "MA"
                    ));

                    // No for da linha acima (listaEmails), o próximo e-mail está com a Lista Pix do aluno atual e dos anteriores, por isso está enviando vários e-mails de cobrança de pix.
                    // Precisa limpar a lista antes de ir para o próximo e-mail.
                    // Não identifiquei impactos ao limpar a lista no if abaixo.
                    // O comentário é para ajudar a lembrar de que se for trocar o if por outro comando, será necessário limpar a lista em outro lugar.
                    if (!UteisValidacao.emptyList(malaDiretaVO.getListaPixEnviar())) {
                        malaDiretaVO.setListaPixEnviar(new ArrayList<>());
                    }

                }
                for(MailingItensController.MailingItem mail : emails){
                    mail.setEnviado(false).setErro("Limite excedido");
                }
                malingEnviadosDAO.excluir(Calendario.ontem());
                mailingItensController.getItems().addAll(emails);

            }
        } else {

            for (MailingItensController.MailingItem mi: mailingItensController.getItemsAguardando()) {
                MalaDiretaEnviadaVO itemMalaDireta = getMalaDireta(malaDiretaVO.getMalaDiretaEnviadaVOs(), mi);
                if(null != itemMalaDireta) {

                    if (malaDiretaVO.getTipoEvento() != null && (malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) || malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS))
                            && malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo() && malaDiretaVO.getCfgEvento().isModeloPadraoBoleto()) {
                        String [] emailDestino = new String[]{""};
                        try {
                            List<BoletoEmailTO> listaBoletos = obterEmailsEnviarBoletoModeloPadrao(key, uteisEmail, malaDiretaVO, itemMalaDireta.getCodigoPessoa());
                            MalaDireta malaDiretaDAO = new MalaDireta(con);
                            for (BoletoEmailTO boletoEmailTO : listaBoletos) {
                                int emailsEnviadosHoje = malaDiretaDAO.countEmails(Calendario.hoje(), true, configuracaoSistemaCRMVO.getIntegracaoPacto());
                                int emailsEnviadosMes = malaDiretaDAO.countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, configuracaoSistemaCRMVO.getIntegracaoPacto());
                                List<MailingItensController.MailingItem> emails = new ArrayList<>();
                                emails.add(mi);
                                uteisEmail.validarEmailsEnviarHoje(emailsEnviadosHoje, emails, configuracaoSistemaCRMVO);
                                uteisEmail.validarEmailsEnviarMensal(emailsEnviadosMes, emails, configuracaoSistemaCRMVO);
                                emailDestino = new String[]{emails.size() > 0 ? emails.get(0).getValor() : ""};
                                uteisEmail.enviarEmailN(mailingItensController.toArray(emails), boletoEmailTO.getHtmlEmail(), malaDiretaVO.getTitulo(), malaDiretaVO.getEmpresa().getNome(), configuracaoSistemaCRMVO, malaDiretaVO.getCodigo(), malaDiretaVO.getEmpresa().getCodigo(), null);
                            }
                            malaDiretaDAO = null;
                            mailingItensController.marcarEmailEnviado(emailDestino[0]);
                        } catch (javax.mail.SendFailedException emailInvalido) {
                            tratarInvalidAdress(emailInvalido, mailingItensController.getItemsAguardando());
                        } catch (Exception ex) {
                            tratarExceptionBoleto(ex, emailDestino, mailingItensController.getItemsAguardando());
                        }

                    } else {
                        String mensagemFinal = obterMensagemIndividual(uteisEmail, malaDiretaVO, itemMalaDireta, null, null);
                        try {
                            MalaDireta malaDiretaDAO = new MalaDireta(this.con);
                            int emailsEnviadosHoje = malaDiretaDAO.countEmails(Calendario.hoje(), true, configuracaoSistemaCRMVO.getIntegracaoPacto());
                            int emailsEnviadosMes = malaDiretaDAO.countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, configuracaoSistemaCRMVO.getIntegracaoPacto());
                            List<MailingItensController.MailingItem> emails = new ArrayList<>();
                            emails.add(mi);
                            uteisEmail.validarEmailsEnviarHoje(emailsEnviadosHoje, emails, configuracaoSistemaCRMVO);
                            uteisEmail.validarEmailsEnviarMensal(emailsEnviadosMes, emails, configuracaoSistemaCRMVO);
                            malaDiretaDAO = null;
                            if (!UteisValidacao.emptyList(malaDiretaVO.getListaPixEnviar()) && mensagemFinal.contains("TAG_PIX")) {
                                Empresa empresaDAO = new Empresa(this.con);
                                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(malaDiretaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                empresaDAO = null;
                                for (PixVO pixVO : malaDiretaVO.getListaPixEnviar()) {
                                    mensagemFinal = PixEmailService.emailHtml(pixVO, Uteis.getUrlAplicacao(), empresaVO, key);
                                    uteisEmail.enviarEmailN(mailingItensController.toArray(emails), mensagemFinal, malaDiretaVO.getTitulo(), malaDiretaVO.getEmpresa().getNome(), configuracaoSistemaCRMVO,malaDiretaVO.getCodigo(), malaDiretaVO.getEmpresa().getCodigo(), null);
                                }
                            } else {
                                uteisEmail.enviarEmailN(mailingItensController.toArray(emails), mensagemFinal, malaDiretaVO.getTitulo(), malaDiretaVO.getEmpresa().getNome(), configuracaoSistemaCRMVO,malaDiretaVO.getCodigo(), malaDiretaVO.getEmpresa().getCodigo(), null);
                            }
                            mailingItensController.marcarEmailEnviado(emails.size() > 0 ? emails.get(0).getValor() : "");
                        } catch (javax.mail.SendFailedException emailInvalido) {
                            tratarInvalidAdress(emailInvalido, mailingItensController.getItemsAguardando());
                        } catch (Exception ex) {
                            tratarExceptionBoleto(ex, new String[]{mi.getValor()}, mailingItensController.getItemsAguardando());
                        }
                    }
                }
            }
        }
    }

    private void realizarEnvioEmail(UteisEmail uteisEmail, EmailVO emailVO, MalaDiretaVO malaDiretaVO, AmostraClienteTO amostraClienteTO ) throws Exception {
        realizarEnvioEmail(uteisEmail, emailVO, malaDiretaVO, amostraClienteTO, null);
    }

    private void realizarEnvioEmail(UteisEmail uteisEmail, EmailVO emailVO, MalaDiretaVO malaDiretaVO, AmostraClienteTO amostraClienteTO, ClienteVO clienteVO) throws Exception {
        if (UteisEmail.getValidEmail(emailVO.getEmail())) {
            String[] emailDestino = {emailVO.getEmail()};

            emailVO.setPessoaVO( pessoaDAO.consultarPorCodigo(emailVO.getPessoa(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            if (malaDiretaVO.getTipoEvento() != null && (malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) || malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS))
                    && malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo() && malaDiretaVO.getCfgEvento().isModeloPadraoBoleto()) {

                try {
                    List<BoletoEmailTO> listaBoletos = obterEmailsEnviarBoletoModeloPadrao(key, uteisEmail, malaDiretaVO, emailVO.getPessoa());
                    for (BoletoEmailTO boletoEmailTO : listaBoletos) {
                        uteisEmail.enviarEmailN(emailDestino, boletoEmailTO.getHtmlEmail(), malaDiretaVO.getTitulo(), malaDiretaVO.getEmpresa().getNome(), configuracaoSistemaCRMVO, malaDiretaVO.getCodigo(), malaDiretaVO.getEmpresa().getCodigo(), null);
                    }
                    mailingItensController.marcarEmailEnviado(emailVO.getEmail());
                } catch (javax.mail.SendFailedException emailInvalido) {
                    tratarInvalidAdress(emailInvalido, mailingItensController.getItemsAguardando());
                } catch (Exception ex) {
                    tratarExceptionBoleto(ex, emailDestino, mailingItensController.getItemsAguardando());
                }

            } else {

                String mensagemFinal = obterMensagemIndividual(uteisEmail, malaDiretaVO, null, emailVO, amostraClienteTO);
                try {
                    if (!UteisValidacao.emptyList(malaDiretaVO.getListaPixEnviar()) && mensagemFinal.contains("TAG_PIX")) {
                        Empresa empresaDAO = new Empresa(this.con);
                        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(malaDiretaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        empresaDAO = null;
                        for (PixVO pixVO : malaDiretaVO.getListaPixEnviar()) {
                            mensagemFinal = PixEmailService.emailHtml(pixVO, Uteis.getUrlAplicacao(), empresaVO, key);
                            uteisEmail.enviarEmailN(emailDestino, mensagemFinal, malaDiretaVO.getTitulo(), malaDiretaVO.getEmpresa().getNome(), configuracaoSistemaCRMVO, malaDiretaVO.getCodigo(), malaDiretaVO.getEmpresa().getCodigo(), null);
                        }
                    } else {
                        String nomeEmpresa =  clienteVO != null ? clienteVO.getEmpresa().getNome() : malaDiretaVO.getEmpresa().getNome();
                        uteisEmail.enviarEmailN(emailDestino, mensagemFinal, malaDiretaVO.getTitulo(), nomeEmpresa, configuracaoSistemaCRMVO, malaDiretaVO.getCodigo(), malaDiretaVO.getEmpresa().getCodigo(), null);
                    }
                    mailingItensController.marcarEmailEnviado(emailVO.getEmail());
                } catch (javax.mail.SendFailedException emailInvalido) {
                    tratarInvalidAdress(emailInvalido, mailingItensController.getItemsAguardando());
                } catch (Exception ex) {
                    tratarExceptionBoleto(ex, emailDestino, mailingItensController.getItemsAguardando());
                }
            }

            uteisEmail.novo(malaDiretaVO.getTitulo(), configuracaoSistemaCRMVO);
            uteisEmail.setRemetente(malaDiretaVO.getRemetente());
        }
    }

    private MalaDiretaEnviadaVO getMalaDireta(List<MalaDiretaEnviadaVO> malaDiretaEnviadaVOs, MailingItensController.MailingItem mi) {
        for (MalaDiretaEnviadaVO mde :malaDiretaEnviadaVOs) {
            if(mde.getClienteVO().getCodigo().equals(mi.getCodigo())){
                return mde;
            }

        }
        return null;
    }

    private String obterMensagemIndividual(UteisEmail uteisEmail, MalaDiretaVO malaDiretaVO, MalaDiretaEnviadaVO itemMalaDireta, EmailVO emailVO, AmostraClienteTO amostraClienteTO) throws Exception {
        VendasConfig vendasConfigDAO;
        try {
            vendasConfigDAO = new VendasConfig(this.con);

            String mensagemComAlteracaoTag = malaDiretaVO.getMensagemComAlteracaoTag();
            if (itemMalaDireta != null) {
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("NOME_EMPRESA ", malaDiretaVO.getEmpresa().getNome());
                if (emailVO != null && emailVO.getPessoaVO() != null) {
                    mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_NOME", Uteis.formatarNome(emailVO.getPessoaVO().getNome()));
                } else {
                    mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_NOME", Uteis.formatarNome(itemMalaDireta.getNome()));
                }    mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_PAGONLINE", vendasConfigDAO.tagPagamento(key, malaDiretaVO.getEmpresa().getCodigo(), itemMalaDireta.getClienteVO().getCodigo(), true));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_CADCARTAO", vendasConfigDAO.tagPagamento(key, malaDiretaVO.getEmpresa().getCodigo(), itemMalaDireta.getClienteVO().getCodigo(), false));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_PNOME", Uteis.formatarNome(Uteis.getPrimeiroNome(itemMalaDireta.getNome())));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_SALDOPONTOS", (emailVO == null ? "0" : new HistoricoPontos(con).obterPontosTotalPorCliente(emailVO.getCliente()).toString()));
                mensagemComAlteracaoTag = resolverTagParcela(malaDiretaVO, mensagemComAlteracaoTag, itemMalaDireta.getCodigoPessoa(), false);
                mensagemComAlteracaoTag = resolverTagBoleto(malaDiretaVO, uteisEmail, mensagemComAlteracaoTag, itemMalaDireta.getCodigoPessoa(), false);
                mensagemComAlteracaoTag = resolverTagPesquisaMalaDireta(malaDiretaVO, mensagemComAlteracaoTag, itemMalaDireta.getCodigoPessoa(), 0,false);
                resolverTagPix(malaDiretaVO, uteisEmail, mensagemComAlteracaoTag, itemMalaDireta.getCodigoPessoa());
            } else {
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("NOME_EMPRESA", malaDiretaVO.getEmpresa().getNome());
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_NOME", Uteis.formatarNome(emailVO.getPessoaVO().getNome()));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_PAGONLINE", vendasConfigDAO.tagPagamento(key, malaDiretaVO.getEmpresa().getCodigo(), emailVO.getCliente(), true));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_CADCARTAO", vendasConfigDAO.tagPagamento(key, malaDiretaVO.getEmpresa().getCodigo(), emailVO.getCliente(), false));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_PNOME",  Uteis.formatarNome(Uteis.getPrimeiroNome(emailVO.getPessoaVO().getNome())));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_SALDOPONTOS", (emailVO == null ? "0" : new HistoricoPontos(con).obterPontosTotalPorCliente(emailVO.getCliente()).toString()));
                mensagemComAlteracaoTag = resolverTagParcela(malaDiretaVO, mensagemComAlteracaoTag, emailVO.getPessoa(), false);
                mensagemComAlteracaoTag = resolverTagBoleto(malaDiretaVO, uteisEmail, mensagemComAlteracaoTag, emailVO.getPessoa(), false);
                mensagemComAlteracaoTag = resolverTagPesquisaMalaDireta(malaDiretaVO, mensagemComAlteracaoTag, emailVO.getPessoa(), 0,false);
                resolverTagPix(malaDiretaVO, uteisEmail, mensagemComAlteracaoTag, emailVO.getPessoa());
            }

            if (amostraClienteTO != null) {
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll(TagsEmailRemessaEnum.TAG_PARCELA_VALOR.getTag(), "R\\$ " + Formatador.formatarValorMonetarioSemMoeda(amostraClienteTO.getMovParcelaVO().getValorParcela()));
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll(TagsEmailRemessaEnum.TAG_CARTAO_MASCARADO.getTag(), amostraClienteTO.getRemessaItemVO().getValorCartaoMascaradoOuAgenciaConta());
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll(TagsEmailRemessaEnum.TAG_CODIGO_RETORNO.getTag(), amostraClienteTO.getRemessaItemVO().getCodigoStatus());
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll(TagsEmailRemessaEnum.TAG_DESCRICAO_RETORNO.getTag(), amostraClienteTO.getRemessaItemVO().getDescricaoStatus());
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll(TagsEmailRemessaEnum.TAG_ACAO_REALIZAR_RETORNO.getTag(), amostraClienteTO.getRemessaItemVO().getAcaoRealizar());
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll(TagsEmailRemessaEnum.TAG_DATA_RETORNO.getTag(), Uteis.getDataComHHMM(amostraClienteTO.getRemessaItemVO().getRemessa().getDataRetorno()));
                mensagemComAlteracaoTag = resolverTagPesquisaMalaDireta(malaDiretaVO, mensagemComAlteracaoTag, amostraClienteTO.getCodigoPessoa(), amostraClienteTO.getCodigoCliente(),false);
            }

            if (emailVO != null) {
                Contrato contratoDao = new Contrato(con);
                ContratoVO contratoVO = contratoDao.consultarUltimoContratoPorPessoa(emailVO.getPessoa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                IntegracaoCadastros integracaoCadastros = new IntegracaoCadastros();
                mensagemComAlteracaoTag = integracaoCadastros.replaceTagsContrato(mensagemComAlteracaoTag, contratoVO);
                contratoDao = null;
            }

            return mensagemComAlteracaoTag;
        } finally {
            vendasConfigDAO = null;
        }
    }

    public String resolverTagPesquisaMalaDireta(MalaDiretaVO malaDiretaVO, String mensagemComAlteracaoTag, Integer codPessoa, Integer codCliente, boolean enviandoSendy) throws Exception {
        Integer colaborador = malaDiretaVO.getRemetente().getColaboradorVO().getCodigo();
        Integer questionario = malaDiretaVO.getQuestionario();
        return resolverTagPesquisa(key, mensagemComAlteracaoTag, colaborador, questionario, codPessoa, codCliente,enviandoSendy);
    }

    public String resolverTagPesquisa(String chave, String mensagemComAlteracaoTag, Integer colaborador, Integer questionario, Integer codPessoa, Integer codCliente,boolean enviandoSendy) throws Exception {
        if (mensagemComAlteracaoTag.contains("TAG_PESQUISA") &&
                !UteisValidacao.emptyNumber(questionario) &&
                (!UteisValidacao.emptyNumber(codPessoa) || !UteisValidacao.emptyNumber(codCliente))) {

            ClienteVO clienteVO = new ClienteVO();
            if (!UteisValidacao.emptyNumber(codPessoa)) {
                clienteVO = clienteDAO.consultarPorCodigoPessoa(codPessoa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            } else {
                clienteVO.setCodigo(codCliente);
            }

            JSONObject json = new JSONObject();
            json.put("key", chave);
            json.put("questionario", questionario);
            json.put("cliente", clienteVO.getCodigo());
            json.put("empresa", 0);
            json.put("colaborador", colaborador);
            String urlPesquisa = PropsService.getPropertyValue("DISCOVERY_URL")+"/redir"+"/"+chave+"/zillyonWeb?"+"m=/faces/pesquisa.jsp&q=" + Uteis.encriptar(json.toString(), "PESQUIS@");

            if (!UteisValidacao.emptyString(urlPesquisa) && !UteisValidacao.emptyString(configuracaoSistemaCRMVO.getTokenBitly())) {
                JSONObject jsonBitLy = new JSONObject();
                jsonBitLy.put("domain", "bit.ly");
                jsonBitLy.put("long_url", urlPesquisa);
                urlPesquisa = Uteis.enviarSolicitacaoEncurtarLink(jsonBitLy, configuracaoSistemaCRMVO.getTokenBitly());
            }
            if (UteisValidacao.emptyString(urlPesquisa)) {
                throw new Exception("Erro ao gerar URL da Pesquisa! Questionario: " + questionario + " | codPessoa: " + codPessoa);
            }
            if (enviandoSendy){
                return urlPesquisa;
            }else{
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_PESQUISA", urlPesquisa);
            }
        }
        return mensagemComAlteracaoTag;
    }

    private String resolverTagParcela(MalaDiretaVO malaDiretaVO, String mensagemComAlteracaoTag, Integer codPessoa, boolean enviandoSendy) throws Exception {
        if (mensagemComAlteracaoTag.contains("TAG_PARCELAS_COBRANCA")) {
            tratarInicioFimEventoMalaDireta(malaDiretaVO);
            List<MovParcelaVO> parcelasEmAtraso = movParcelaDAO.consultarPorCodigoPessoaDiasEmAberto(codPessoa, malaDiretaVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            StringBuilder sbParcelas = new StringBuilder();
            for (MovParcelaVO movParcelaVO : parcelasEmAtraso) {
                List<MovParcelaVO> parcelasBoleto = new ArrayList<MovParcelaVO>();
                movParcelaVO.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));

                parcelasBoleto.add(movParcelaVO);

                double valorAcrescimo = movParcelaDAO.montarMultaJurosParcelaVencida(movParcelaVO.getEmpresa(), parcelasBoleto, Calendario.hoje(), false, 1.0, null);
                double valorParcela = movParcelaVO.getValorParcela();
                double valorFinal = valorAcrescimo + valorParcela;
                String valorFinal_Apresentar = Formatador.formatarValorMonetario(valorFinal);
                sbParcelas.append("Parcela de vencimento ").append(movParcelaVO.getDataVencimento_Apresentar()).append(" - Valor principal: R$ ").append(movParcelaVO.getValorParcela_Apresentar()).append(" - Valor atualizado: ").append(valorFinal_Apresentar).append(";<br/>");
            }

            if (enviandoSendy) {
                return sbParcelas.toString();
            } else {
                mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_PARCELAS_COBRANCA", sbParcelas.toString());
            }

        }
        return mensagemComAlteracaoTag;
    }

    private List<BoletoEmailTO> obterEmailsEnviarBoletoModeloPadrao(String chave, UteisEmail uteisEmail,
                                                                    MalaDiretaVO malaDiretaVO, Integer codPessoa) throws Exception {
        Empresa empresaDAO;
        Pessoa pessoaDAO;
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            empresaDAO = new Empresa(this.con);
            boletoDAO = new Boleto(this.con);
            pessoaDAO = new Pessoa(this.con);
            usuarioDAO = new Usuario(this.con);

            List<BoletoEmailTO> listaBoletos = new ArrayList<>();
            if (malaDiretaVO.getTipoEvento() != null && (malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) || malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS))
                    && malaDiretaVO.getCfgEvento().isBoletoParcelasVencendo() && malaDiretaVO.getCfgEvento().isModeloPadraoBoleto()) {

                tratarInicioFimEventoMalaDireta(malaDiretaVO);
                List<MovParcelaVO> parcelasEmAtraso = movParcelaDAO.consultarPorCodigoPessoaDiasEmAberto(codPessoa, malaDiretaVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(malaDiretaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                this.movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, TipoCobrancaEnum.BOLETO, parcelasEmAtraso, Calendario.hoje());

                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

                Map<Integer, EmpresaVO> mapaEmpresas = new HashMap<Integer, EmpresaVO>();
                for (MovParcelaVO movParcelaVO : parcelasEmAtraso) {

                    BoletoEmailTO boletoEmailTO = null;
                    RemessaItemVO boleto = obterUltimoRemessaItemBoletoPendente(movParcelaVO);
                    BoletoVO boletoVO = null;

                    //SE NÃO ENCONTROU REMESSAITEM VERIFICAR BOLETO ONLINE
                    if (boleto == null) {
                        List<BoletoVO> listaBoletoOnline = boletoDAO.consultarPorMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (!UteisValidacao.emptyList(listaBoletoOnline)) {
                            for (BoletoVO boletoVO1 : listaBoletoOnline) {
                                if (boletoVO1.isPodeEnviarEmail()) {
                                    boletoVO = boletoVO1;
                                    break;
                                }
                            }
                        }

                        //Caso não tenha boleto gerado ou tenha boleto gerado e não registrado
                        if (boletoVO == null || (boletoVO.getTipo().equals(TipoBoletoEnum.CAIXA) && boletoVO.getSituacao().equals(SituacaoBoletoEnum.GERADO))) {
                            boletoVO = processarBoletoOnline(movParcelaVO, usuarioVO);
                        }

                        if (boletoVO != null) {
                            boletoEmailTO = new BoletoEmailTO();
                            boletoEmailTO.setEmpresaVO(movParcelaVO.getEmpresa());
                            boletoEmailTO.setPessoaVO(movParcelaVO.getPessoa());
                            boletoEmailTO.setLinhaDigitavel(boletoVO.getLinhaDigitavel());
                            boletoEmailTO.setValor(boletoVO.getValor());
                            boletoEmailTO.setDataVencimento(boletoVO.getDataVencimento());
                            boletoEmailTO.setNomeArquivoBoleto("Boleto-" + boletoVO.getCodigo() + ".pdf");

                            //O Banco do Brasil o registro é online, mas a geração do PDF é nossa.
                            //Nesse trecho, só entra os boletos que a geração é do Banco.
                            //A geração para o Banco do Brasil é feita mais a frente.
                            if (!boletoVO.isApresentarImprimirBoletoBancoBrasil()) {
                                File file = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos) + File.separator + "boleto_email_temp" + File.separator + key + "-" + boletoEmailTO.getNomeArquivoBoleto());
                                byte[] arquivo = ExecuteRequestHttpService.obterByteFromUrl(boletoVO.getLinkBoleto(), null, null);
                                FileUtilities.saveToFile(arquivo, file.getPath());
                                boletoEmailTO.setUrlFileArquivoBoleto(file.getAbsolutePath());
                            }
                        }
                    }
                    if (boletoVO != null) {
                        //Gerar a impressao do boleto para o Banco do Brasil Online
                        if (boletoVO.isApresentarImprimirBoletoBancoBrasil()) {
                            BoletoBancarioControle controle = new BoletoBancarioControle();

                            mapaEmpresas.put(movParcelaVO.getEmpresa().getCodigo(), movParcelaVO.getEmpresa());

                            BoletoService boletoService = new BoletoService();
                            RemessaItemVO boletoBB = montarObjetoParaImprimirBancoBrasil(boletoVO);

                            controle.prepararBoletoParaImpressao(boletoBB);
                            Uteis.logar(null, "Ponto 1 - Antes de chamar o metodo que gera o boleto");
                            boletoEmailTO = boletoService.executarImprimirBoletoMailingModeloPadrao(boletoEmailTO, controle, mapaEmpresas.get(boletoBB.getMovParcelas().get(0).getMovParcelaVO().getEmpresa().getCodigo()), key, boletoBB.getCodigo());
                            String nomePDF = boletoEmailTO.getUrlFileArquivoBoleto();
                            Uteis.logar(null, "Nome do PDF: " + nomePDF);
                            boletoEmailTO.setNomeArquivoBoleto(nomePDF.substring(nomePDF.lastIndexOf("\\") + 1));
                            boletoService = null;

                        }
                    }

                    if (boletoEmailTO == null) {
                        BoletoBancarioControle controle = new BoletoBancarioControle();
                        movParcelaVO.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                        boolean anexarBoleto = true;
                        if (boleto == null || Calendario.menor(boleto.getDataVencimentoBoleto(), Calendario.hoje())) {
                            List<MovParcelaVO> parcelasBoleto = new ArrayList<>();
                            parcelasBoleto.add(movParcelaVO);
                            controle.setParcelaSacado(movParcelaVO);
                            controle.setParcelasBoleto(parcelasBoleto);
                            controle.calcularDataVencimento();

                            //verificar se existe autorização de cobrança boleto
                            boolean existeAutorizacao = controle.isExisteAutorizacaoBoleto(movParcelaDAO.getCon());
                            //se não existe validar se empresa permite mailing adicionar autorizacao
                            if (!existeAutorizacao) {
                                existeAutorizacao = controle.adicionarAutorizacaoBoleto(movParcelaVO, movParcelaDAO.getCon());
                            }
                            if (existeAutorizacao && permiteMaillingCriarBoleto(movParcelaVO.getEmpresa().getCodigo())) {
                                controle.validarAutorizacaoCobrancaSacado();
                                controle.calcularValorTitulo();
                                controle.calcularMultaJuros();
                                controle.verificarProcessarAgrupamentoBoletos();
                                controle.gravarBoleto(malaDiretaVO.getRemetente(), movParcelaVO.getEmpresa());
                                boleto = obterUltimoRemessaItemBoletoPendente(movParcelaVO);
                            } else {
                                anexarBoleto = false;
                                throw new Exception("Não foi possível enviar o e-mail. Aluno não tem boleto gerado ou se tem pode ser que o boleto esteja vencido.\n" +
                                        "Para gerar boleto automático e enviar por e-mail marque a opção \"Permitir Mailing criar boleto ao enviar e-mail\" nas configurações da empresa.");
                            }
                        }

                        if (anexarBoleto) {
                            boletoEmailTO = new BoletoEmailTO();
                            boletoEmailTO.setEmpresaVO(movParcelaVO.getEmpresa());
                            boletoEmailTO.setPessoaVO(movParcelaVO.getPessoa());
                            boletoEmailTO.setValor(boleto.getValorBoleto());
                            boletoEmailTO.setDataVencimento(boleto.getDataVencimentoBoleto());

                            mapaEmpresas.put(movParcelaVO.getEmpresa().getCodigo(), movParcelaVO.getEmpresa());

                            //gerar a impressao do boleto
                            BoletoService boletoService = new BoletoService();
                            controle.prepararBoletoParaImpressao(boleto);
                            Uteis.logar(null, "Ponto 1 - Antes de chamar o metodo que gera o boleto");
                            boletoEmailTO = boletoService.executarImprimirBoletoMailingModeloPadrao(boletoEmailTO, controle, mapaEmpresas.get(boleto.getMovParcelas().get(0) != null ? boleto.getMovParcelas().get(0).getMovParcelaVO().getEmpresa().getCodigo() : boleto.getRemessa().getEmpresa()), key, boleto.getCodigo());
                            String nomePDF = boletoEmailTO.getUrlFileArquivoBoleto();
                            Uteis.logar(null, "Nome do PDF: " + nomePDF);
                            boletoEmailTO.setNomeArquivoBoleto(nomePDF.substring(nomePDF.lastIndexOf("\\") + 1));
                            boletoService = null;
                        }
                    }

                    if (boletoEmailTO != null) {
                        boletoEmailTO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(boletoEmailTO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
                        boletoEmailTO.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(boletoEmailTO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                        Uteis.logar(null, "Ponto 0 EmailBoleto");
                        boletoDAO.gerarHTMLModeloPadraoBoleto(chave, boletoEmailTO);

                        Uteis.logar(null, "Ponto 1 EmailBoleto");
                        File arquivo = new File(boletoEmailTO.getUrlFileArquivoBoleto());
                        uteisEmail.addAnexo(boletoEmailTO.getNomeArquivoBoleto(), arquivo);
                        Uteis.logar(null, "Ponto 2 EmailBoleto");
                        listaBoletos.add(boletoEmailTO);
                    }
                }
            }
            return listaBoletos;
        } finally {
            empresaDAO = null;
            boletoDAO = null;
            pessoaDAO = null;
            usuarioDAO = null;
        }
    }

    private RemessaItemVO montarObjetoParaImprimirBancoBrasil(BoletoVO boletoVO) {
        BoletoVO boletoOnlineParaConverterRemessa = boletoVO;
        RemessaItemVO boletoImprimir = new RemessaItemVO();

        BoletoMovParcela boletoMovParcelaDAO;
        try {
            boletoMovParcelaDAO = new BoletoMovParcela(this.con);
            boletoImprimir.getRemessa().setConvenioCobranca(getFacade().getConvenioCobranca().consultarPorChavePrimaria(boletoOnlineParaConverterRemessa.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            boletoImprimir.getRemessa().getConvenioCobranca().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(boletoOnlineParaConverterRemessa.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            boletoImprimir.getRemessa().getConvenioCobranca().getTipoRemessa().setArquivoLayoutRemessa(ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_BRASIL);
            boletoImprimir.getRemessa().setDataRegistro(boletoOnlineParaConverterRemessa.getDataRegistro());

            boletoImprimir.setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(boletoOnlineParaConverterRemessa.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            boletoImprimir.setDataVencimentoBoleto(boletoOnlineParaConverterRemessa.getDataVencimento());

            List<RemessaItemMovParcelaVO> movParcelasLista = new ArrayList<>();
            Double valorBoleto = 0.0;

            List<BoletoMovParcelaVO> listaBoletoMovParcela = boletoMovParcelaDAO.consultarPorCodigoBoleto(boletoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            boletoOnlineParaConverterRemessa.setListaBoletoMovParcela(listaBoletoMovParcela);

            for (BoletoMovParcelaVO boletoMovParcelaVO : boletoOnlineParaConverterRemessa.getListaBoletoMovParcela()) {
                RemessaItemMovParcelaVO remessaItemMovParcelaVO = new RemessaItemMovParcelaVO();
                remessaItemMovParcelaVO.setValorOriginal(boletoMovParcelaVO.getMovParcelaVO().getValorParcela());
                remessaItemMovParcelaVO.setMovParcelaVO(boletoMovParcelaVO.getMovParcelaVO());
                movParcelasLista.add(remessaItemMovParcelaVO);
                valorBoleto += boletoMovParcelaVO.getMovParcelaVO().getValorParcela();
            }
            boletoImprimir.setMovParcelas(movParcelasLista);
            boletoImprimir.setValorItemRemessa(valorBoleto);

            String parametrosCriacaoBoleto = boletoOnlineParaConverterRemessa.getParamsEnvio();
            JsonObject jsonObjectBoleto = new JsonParser().parse(parametrosCriacaoBoleto).getAsJsonObject();
            JsonObject jsonPagador = jsonObjectBoleto.getAsJsonObject("pagador");
            HashMap<String, String> propriedades = new HashMap<>();
            propriedades.put("CpfCnpjPagador", jsonPagador.get("numeroInscricao").getAsString());
            propriedades.put("NomePagador", jsonPagador.get("nome").getAsString());
            boletoImprimir.setProps(propriedades);

            boletoImprimir.setCodigo(boletoOnlineParaConverterRemessa.getIdentificador());
            boletoImprimir.setIdentificador(boletoOnlineParaConverterRemessa.getIdentificador());

        } catch (Exception ex) {
            String msgErro = "Não foi possível imprimir o boleto!" + ex.getMessage();
            ex.printStackTrace();
        } finally {
            boletoMovParcelaDAO = null;
        }
        return boletoImprimir;
    }

    private RemessaItemVO obterUltimoRemessaItemBoletoPendente(MovParcelaVO movParcelaVO) throws Exception {
        RemessaItemVO remessaItemVO = this.remessaItemDAO.consultarUltimoBoletoParcela(movParcelaVO);
        if (remessaItemVO != null && remessaItemVO.getRemessa() != null &&
                (remessaItemVO.getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.GERADA) ||
                        remessaItemVO.getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA))) {
            return remessaItemVO;
        }
        return null;
    }

    private String resolverTagBoleto(MalaDiretaVO malaDiretaVO, UteisEmail uteisEmail, String mensagemComAlteracaoTag, Integer codPessoa, boolean enviandoSendy) throws Exception {
        Empresa empresaDAO;
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            empresaDAO = new Empresa(this.con);
            boletoDAO = new Boleto(this.con);
            usuarioDAO = new Usuario(this.con);

            if (mensagemComAlteracaoTag.contains("TAG_BOLETO") &&
                    malaDiretaVO.getTipoEvento() != null &&
                    (malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) ||
                            malaDiretaVO.getTipoEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS))) {
                String linkBoletos = "";
                BoletoBancarioControle controle = new BoletoBancarioControle();
                tratarInicioFimEventoMalaDireta(malaDiretaVO);
                List<MovParcelaVO> parcelasEmAtraso = movParcelaDAO.consultarPorCodigoPessoaDiasEmAberto(codPessoa, malaDiretaVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);


                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(malaDiretaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                this.movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, TipoCobrancaEnum.BOLETO, parcelasEmAtraso, Calendario.hoje());

                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

                List<RemessaItemVO> boletosParaAnexar = new ArrayList<>();
                Map<Integer, EmpresaVO> mapaEmpresas = new HashMap<>();
                List<String> linksBoletos = new ArrayList<>();
                for (MovParcelaVO movParcelaVO : parcelasEmAtraso) {

                    RemessaItemVO boleto = obterUltimoRemessaItemBoletoPendente(movParcelaVO);

                    //SE NÃO ENCONTROU REMESSAITEM VERIFICAR BOLETO ONLINE
                    if (boleto == null) {
                        List<BoletoVO> listaBoletoOnline = boletoDAO.consultarPorMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (!UteisValidacao.emptyList(listaBoletoOnline)) {
                            for (BoletoVO boletoVO1 : listaBoletoOnline) {
                                if (boletoVO1.isPodeEnviarEmail()) {
                                    linkBoletos = boletoVO1.getLinkBoleto();
                                    break;
                                }
                            }
                        }

                        //Caso não tenha boleto gerado
                        if (UteisValidacao.emptyString(linkBoletos)) {
                            BoletoVO boletoVO = processarBoletoOnline(movParcelaVO, usuarioVO);
                            if (boletoVO != null) {
                                linkBoletos = boletoVO.getLinkBoleto();
                            }
                        }
                    }

                    if (UteisValidacao.emptyString(linkBoletos)) {
                        movParcelaVO.setMovProdutoParcelaVOs(movProdutoParcelaDAO.consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                        boolean anexarBoleto = true;
                        if (boleto == null || Calendario.menor(boleto.getDataVencimentoBoleto(), Calendario.hoje())) {
                            List<MovParcelaVO> parcelasBoleto = new ArrayList<MovParcelaVO>();
                            parcelasBoleto.add(movParcelaVO);
                            controle.setParcelaSacado(movParcelaVO);
                            controle.setParcelasBoleto(parcelasBoleto);
                            controle.calcularDataVencimento();

                            //verificar se existe autorização de cobrança boleto
                            boolean existeAutorizacao = controle.isExisteAutorizacaoBoleto(movParcelaDAO.getCon());
                            //se não existe validar se empresa permite mailing adicionar autorizacao
                            if (!existeAutorizacao) {
                                existeAutorizacao = controle.adicionarAutorizacaoBoleto(movParcelaVO, movParcelaDAO.getCon());
                            }
                            if (existeAutorizacao && permiteMaillingCriarBoleto(movParcelaVO.getEmpresa().getCodigo())) {
                                controle.validarAutorizacaoCobrancaSacado();
                                controle.calcularValorTitulo();
                                controle.calcularMultaJuros();
                                controle.verificarProcessarAgrupamentoBoletos();
                                controle.gravarBoleto(malaDiretaVO.getRemetente(), movParcelaVO.getEmpresa());
                                boleto = obterUltimoRemessaItemBoletoPendente(movParcelaVO);
                            } else {
                                anexarBoleto = false;
                            }
                        }
                        if (anexarBoleto) {
                            boletosParaAnexar.add(boleto);
                            mapaEmpresas.put(movParcelaVO.getEmpresa().getCodigo(), movParcelaVO.getEmpresa());
                        }
                    } else {
                        linksBoletos.add(linkBoletos);
                        linkBoletos = "";
                    }
                }

                if(enviandoSendy){ // TAG_BOLETO SENDY
                    gerarSalvarBoletosNuvemObtendoUrl(linksBoletos, controle, boletosParaAnexar, mapaEmpresas);
                    return anexarBoletosLinks(linksBoletos);
                }

                mensagemComAlteracaoTag = UteisValidacao.emptyList(linksBoletos) ?
                        anexarBoletos(uteisEmail, mensagemComAlteracaoTag, controle, boletosParaAnexar, mapaEmpresas) : mensagemComAlteracaoTag.replaceAll("TAG_BOLETO", anexarBoletosLinks(linksBoletos));
            }
            return mensagemComAlteracaoTag;
        } finally {
            empresaDAO = null;
            boletoDAO = null;
            usuarioDAO = null;
        }
    }

    private BoletoVO processarBoletoOnline(MovParcelaVO movParcelaVO, UsuarioVO usuarioVO) throws Exception {
        Boleto boletoDAO;
        AutorizacaoCobrancaCliente autoCliDAO;
        try {
            boletoDAO = new Boleto(con);
            autoCliDAO = new AutorizacaoCobrancaCliente(con);

            //VERIFICAR SE TEM AUTORIZAÇÃO DE COBRANÇA BOLETO ONLINE
            List<AutorizacaoCobrancaClienteVO> listaAuto = autoCliDAO.consultarPorPessoaTipoCobranca(movParcelaVO.getPessoa().getCodigo(),
                    new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO_ONLINE}, true, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (UteisValidacao.emptyList(listaAuto)) {
                boolean gerou = autoCliDAO.gerarAutorizacaoCobrancaBoleto(movParcelaVO.getEmpresa().getCodigo(), movParcelaVO.getPessoa().getCodigo(), 0);
                if (gerou) {
                    listaAuto = autoCliDAO.consultarPorPessoaTipoCobranca(movParcelaVO.getPessoa().getCodigo(), new TipoCobrancaEnum[]{TipoCobrancaEnum.BOLETO_ONLINE}, true, Uteis.NIVELMONTARDADOS_MINIMOS);
                }
            }

            if (!UteisValidacao.emptyList(listaAuto)) {
                if (permiteMaillingCriarBoleto(movParcelaVO.getEmpresa().getCodigo())) {

                    Date dataVencimento = movParcelaVO.getDataVencimento();
                    if (UteisValidacao.dataMenorDataAtualSemHora(dataVencimento)) {
                        //caso seja informado um valor então deve ser gerado a data de vencimento a partir da data atual.
                        //by Luiz Felipe 05/04/2021
                        dataVencimento = Calendario.somarDias(Calendario.hoje(), 2);
                    }

                    List<MovParcelaVO> listaParcelas = new ArrayList<>();
                    listaParcelas.add(movParcelaVO);
                    return boletoDAO.gerarBoleto(movParcelaVO.getPessoa(), listaAuto.get(0).getConvenio(), listaParcelas, dataVencimento,
                            usuarioVO, OrigemCobrancaEnum.MAILING, true, true);
                }
            }
            return null;
        } finally {
            boletoDAO = null;
            autoCliDAO = null;
        }
    }

    private boolean permiteMaillingCriarBoleto(Integer empresa) throws Exception {
        EmpresaVO empresaVO = mapaEmpresa.get(empresa);
        if (empresaVO == null) {
            throw new Exception("Empresa não encontrada para verificar se permite mailing permite criar boleto");
        }
        return empresaVO.isPermiteMaillingCriarBoleto();
    }

    private void tratarInicioFimEventoMalaDireta(MalaDiretaVO malaDiretaVO) throws Exception {
        if (malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.DIARIAMENTE)) {
            malaDiretaVO.getCfgEvento().setInicio(Calendario.somarDias(Calendario.hoje(), malaDiretaVO.getCfgEvento().getDiaMenos()));
            malaDiretaVO.getCfgEvento().setFim(Calendario.somarDias(Calendario.hoje(), malaDiretaVO.getCfgEvento().getDiaMenos()));
        } else if (malaDiretaVO.getAgendamento().getOcorrencia().equals(OcorrenciaEnum.INSTANTANEO)) {
            //nada fazer
        } else {
            malaDiretaVO.getCfgEvento().setInicio(Calendario.hoje());
            malaDiretaVO.getCfgEvento().setFim(Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje()));
        }
    }

    private String anexarBoletosLinks(List<String> linksBoletos) {
        StringBuilder mensagem = new StringBuilder();
        int contador = 1;
        if (linksBoletos.size() > 1) {
            mensagem.append("<table><tr><br>");
            mensagem.append("Você possui mais de um boleto para pagamento:");
            mensagem.append("</tr></table>");
        }
        for (String linkBoleto : linksBoletos) {
            if (linksBoletos.size() > 1) {
                mensagem.append("<table><tr>");
                mensagem.append("Boleto " + contador + ": ");
                contador++;
            }
            mensagem.append(linkBoleto);

            if (linksBoletos.size() > 1) {
                mensagem.append("</tr></table><br>");
            }
        }
        return mensagem.toString();
    }

    private String anexarBoletos(UteisEmail uteisEmail, String mensagemComAlteracaoTag, BoletoBancarioControle controle, List<RemessaItemVO> boletosParaAnexar,  Map<Integer, EmpresaVO> mapaEmpresas) throws Exception {
        String textoSubstituir = (boletosParaAnexar.size() > 0) ? "Boletos em Anexo" : "";
        mensagemComAlteracaoTag = mensagemComAlteracaoTag.replaceAll("TAG_BOLETO", textoSubstituir + "<br/>");

        Uteis.logar(null, "Iniciando anexo de boletos: " + boletosParaAnexar.size());
        BoletoService boletoService = new BoletoService();
        for (RemessaItemVO boleto : boletosParaAnexar) {
            controle.prepararBoletoParaImpressao(boleto);
            Uteis.logar(null, "Ponto 1 - Antes de chamar o metodo que gera o boleto");
            String nomePDF = boletoService.executarImprimirBoletoMailing(controle, mapaEmpresas.get(boleto.getMovParcelas().get(0) != null ? boleto.getMovParcelas().get(0).getMovParcelaVO().getEmpresa().getCodigo() : boleto.getRemessa().getEmpresa()), key, boleto.getCodigo());
            Uteis.logar(null, "Nome do PDF: " + nomePDF);
            File arquivo = new File(nomePDF);
            uteisEmail.addAnexo(nomePDF.substring(nomePDF.lastIndexOf("\\") + 1), arquivo);
            Uteis.logar(null, "Fim da rotina de anexar boleto");
        }
        boletoService = null;
        return mensagemComAlteracaoTag;
    }

    private void gerarSalvarBoletosNuvemObtendoUrl(List<String> linksBoletos, BoletoBancarioControle controle, List<RemessaItemVO> boletosParaAnexar,  Map<Integer, EmpresaVO> mapaEmpresas) throws Exception {
        BoletoService boletoService = new BoletoService();
        for (RemessaItemVO boleto : boletosParaAnexar) {
            controle.prepararBoletoParaImpressao(boleto);
            String nomePDF = boletoService.executarImprimirBoletoMailing(controle, mapaEmpresas.get(boleto.getMovParcelas().get(0) != null ? boleto.getMovParcelas().get(0).getMovParcelaVO().getEmpresa().getCodigo() : boleto.getRemessa().getEmpresa()), key, boleto.getCodigo());
            File arquivo = new File(nomePDF);
            linksBoletos.add(getPaintFotoDaNuvem(MidiaService.getInstance().uploadObjectWithExtension(key, MidiaEntidadeEnum.ANEXO_BOLETO_ALUNO_SENDY,
                    "ANEXO_BOLETO_ALUNO_SENDY_" + boleto.getIdentificador().toString(),
                    arquivo, ".pdf")));
        }
        boletoService = null;
    }

    private void enviarEmailMalaDireta(MalaDiretaVO malaDiretaVO) throws Exception {
        enviarEmailMalaDireta(malaDiretaVO, null);
    }


    private void enviarEmailMalaDireta(MalaDiretaVO malaDiretaVO, ClienteVO cliente) throws Exception {
        UteisEmail uteisEmail = new UteisEmail();
        uteisEmail.novo(malaDiretaVO.getTitulo(), configuracaoSistemaCRMVO);
        uteisEmail.setChave(key);
        uteisEmail.setEmpresaVO(malaDiretaVO.getEmpresa());
        uteisEmail.setMalaDiretaVO(malaDiretaVO);
        uteisEmail.setRemetente(malaDiretaVO.getRemetente());

        if (configuracaoSistemaCRMVO.getUsarRemetentePadraoGeral() != null && configuracaoSistemaCRMVO.getUsarRemetentePadraoGeral()) {
            uteisEmail.setRemetentePersonalizado(configuracaoSistemaCRMVO.getRemetentePadrao());
        }

        //fracionar envio
        List<List<MailingItensController.MailingItem>> fracionarDestinatarios = new ArrayList<List<MailingItensController.MailingItem>>();
        if(cliente != null){
            List<MailingItensController.MailingItem> emails = new ArrayList<MailingItensController.MailingItem>();
            for (EmailVO email: cliente.getPessoa().getEmailVOs()) {
                emails.add(new MailingItensController.MailingItem(cliente.getCodigo(), email.getEmail(), email.getBounced()));
            }
            fracionarDestinatarios.add(emails);
        }else if(configuracaoSistemaCRMVO.getIntegracaoPacto()){ // Sendy
            fracionarDestinatarios.add(mailingItensController.getItems());
            if(maladiretaContemTags(malaDiretaVO, false)){
                if (!malaDiretaVO.getMensagemComAlteracaoTag().contains("TAG_PIX")) {
                    preencherCustomFieldsEmails(malaDiretaVO, fracionarDestinatarios);
                    malaDiretaVO.setMensagemComAlteracaoTag(substituirTagsSendy(malaDiretaVO.getMensagemComAlteracaoTag()));
                } else {
                    preencherCustomFieldsEmailsPix(malaDiretaVO, fracionarDestinatarios, uteisEmail);
                    malaDiretaVO.setMensagemComAlteracaoTag(PixEmailService.emailHtmlSendy());
                }
            }
        }else{
            fracionarDestinatarios = mailingItensController.fracionarDestinatarios();
        }
        MalaDireta malaDiretaDAO = new MalaDireta(con);
        for (List<MailingItensController.MailingItem> emails : fracionarDestinatarios) {
            boolean sair = false;
            while (!sair) {
                sair = true;
                try {
                    List<MailingItensController.MailingItem> emailsExcluidos = new ArrayList<>();
                    if (!emails.isEmpty()) {
                        int emailsEnviadosHoje = malaDiretaDAO.countEmails(Calendario.hoje(), true, configuracaoSistemaCRMVO.getIntegracaoPacto());
                        int emailsEnviadosMes = malaDiretaDAO.countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, configuracaoSistemaCRMVO.getIntegracaoPacto());
                        emailsExcluidos.addAll(uteisEmail.validarEmailsEnviarHoje(emailsEnviadosHoje, emails, configuracaoSistemaCRMVO));
                        emailsExcluidos.addAll(uteisEmail.validarEmailsEnviarMensal(emailsEnviadosMes, emails, configuracaoSistemaCRMVO));
                        uteisEmail.enviarEmailN(mailingItensController.toArray(emails), malaDiretaVO.getMensagemComAlteracaoTag(), malaDiretaVO.getTitulo(), malaDiretaVO.getEmpresa().getNome(), configuracaoSistemaCRMVO, malaDiretaVO.getCodigo(), malaDiretaVO.getEmpresa().getCodigo(), emails);
                    }
                    mailingItensController.marcarTodosEnviado(emails);
                    for(MailingItensController.MailingItem mail : emailsExcluidos){
                        mail.setEnviado(false).setErro("Limite excedido");
                    }
                    emails.addAll(emailsExcluidos);
                } catch (javax.mail.SendFailedException emailInvalido) {
                    sair = tratarInvalidAdress(emailInvalido, emails);
                }

            }
        }
        malaDiretaDAO = null;
    }

    private void preencherCustomFieldsEmails(MalaDiretaVO malaDireta, List<List<MailingItem>> fracionarDestinatarios)
            throws Exception {
        String msg = malaDireta.getMensagemComAlteracaoTag();
        List<MailingItem> emails = (fracionarDestinatarios != null && !fracionarDestinatarios.isEmpty())
                ? fracionarDestinatarios.get(0)
                : new ArrayList<>();
        String separador = "%s%";
        for (MailingItem email : emails) {
            if(email == null || email.getCodigo() == 0){
                Uteis.logarDebug("Não foi possivel preencher as tags do email, maladireta: " + malaDireta.getCodigo()+", email: " + email.getValor());
                continue;
            }
            String custom_fields = email.getCustom_fields() != null ? email.getCustom_fields() : "";
            PessoaVO pessoa = clienteDAO.consultarPorChavePrimaria(email.getCodigo(),
                    Uteis.NIVELMONTARDADOS_CONSULTA_WS).getPessoa();
            String nome = pessoa.getNome();
            if (msg.contains("TAG_NOME")) {
                custom_fields += nome;
            }
            custom_fields += separador;
            if (msg.contains("TAG_PNOME")) {
                custom_fields += Uteis.getPrimeiroNome(nome);
            }
            custom_fields += separador;
            if (msg.contains("TAG_CODCLIENTE")) {
                String codCliente = Criptografia.encrypt("&" + email.getCodigo(), SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);
                custom_fields += codCliente;
            }
            custom_fields += separador;
            if (msg.contains("TAG_EMAIL_CLIENTE")) {
                String emailCliente = Criptografia.encrypt("&" + email.getValor(), SuperControle.Crypt_KEY, AlgoritmoCriptoEnum.ALGORITMO_AES);
                custom_fields += emailCliente;
            }
            custom_fields += separador;
            if (msg.contains("TAG_BOLETO")) {
                try {
                    custom_fields += resolverTagBoleto(malaDireta, null, malaDireta.getMensagemComAlteracaoTag(), pessoa.getCodigo(), true);
                } catch (Exception ex) {
                    email.setErro(ex.getMessage());
                }
            }
            custom_fields += separador;
            if (msg.contains("TAG_PARCELAS_COBRANCA")) {
                try {
                    custom_fields += resolverTagParcela(malaDireta, malaDireta.getMensagemComAlteracaoTag(), pessoa.getCodigo(), true) + "</p>";
                } catch (Exception ex) {
                    email.setErro(ex.getMessage());
                }
            }
            custom_fields += separador;
            if (msg.contains("TAG_PAGONLINE")) {
                VendasConfig vendasConfigDAO;
                try {
                    vendasConfigDAO = new VendasConfig(this.con);
                    custom_fields += vendasConfigDAO.tagPagamento(key, malaDireta.getEmpresa().getCodigo(), email.getCodigo(), true) + "</p>";
                } catch (Exception ex) {
                    email.setErro(ex.getMessage());
                }
            }
            custom_fields += separador;
            if (msg.contains("TAG_PESQUISA")) {
                try {
                    custom_fields += resolverTagPesquisaMalaDireta(malaDireta, malaDireta.getMensagemComAlteracaoTag(), pessoa.getCodigo(),0, true) + "</p>";
                } catch (Exception ex) {
                    email.setErro(ex.getMessage());
                }
            }
            email.setCustom_fields(custom_fields);
        }
        clienteDAO = null;
    }

    private void preencherCustomFieldsEmailsPix(MalaDiretaVO malaDireta, List<List<MailingItem>> fracionarDestinatarios, UteisEmail uteisEmail)
            throws Exception {
        String msg = malaDireta.getMensagemComAlteracaoTag();
        List<MailingItem> emails = (fracionarDestinatarios != null && !fracionarDestinatarios.isEmpty())
                ? fracionarDestinatarios.get(0)
                : new ArrayList<>();
        String separador = "%s%";
        for (MailingItem email : emails) {
            if(email == null || email.getCodigo() == 0){
                Uteis.logarDebug("Não foi possivel preencher as tags do email, maladireta: " + malaDireta.getCodigo()+", email: " + email.getValor());
                continue;
            }
            String custom_fields = email.getCustom_fields() != null ? email.getCustom_fields() : "";
            PessoaVO pessoa = clienteDAO.consultarPorChavePrimaria(email.getCodigo(),
                    Uteis.NIVELMONTARDADOS_CONSULTA_WS).getPessoa();

            //Gera Pix
            try {
                resolverTagPix(malaDireta, uteisEmail, msg, pessoa.getCodigo());
            } catch (Exception ex) {
                email.setErro(ex.getMessage());
            }

            if (!UteisValidacao.emptyList(malaDireta.getListaPixEnviar())) {
                //Seta url tela padrão com o gerador QRCode.
                custom_fields += Uteis.getUrlAplicacao();
                custom_fields += separador;
                //Seta valor pix
                custom_fields += malaDireta.getListaPixEnviar().get(0).getValorFormatado();
                custom_fields += separador;
                //Seta nome empresa pix
                String nomeEmpresa = "";
                if (UteisValidacao.emptyString(malaDireta.getEmpresa().getNome())) {
                    nomeEmpresa = consultarNomeEmpresa(malaDireta, email, nomeEmpresa);
                } else {
                    nomeEmpresa = malaDireta.getEmpresa().getNome();
                }
                custom_fields += com.sun.xml.ws.util.StringUtils.capitalize(nomeEmpresa.toLowerCase());
                custom_fields += separador;
                //Seta código QRCode
                custom_fields += malaDireta.getListaPixEnviar().get(0).obterUrlCompartilharQRcode(key);
            }

            email.setCustom_fields(custom_fields);

            // Quando terminar de usar os dados da malaDireta.getListaPixEnviar(), precisa limpar a lista para o próximo email.
            if (!UteisValidacao.emptyList(malaDireta.getListaPixEnviar())) {
                malaDireta.setListaPixEnviar(null);
            }
        }
        clienteDAO = null;
    }

    private String consultarNomeEmpresa(MalaDiretaVO malaDireta, MailingItem email, String nomeEmpresa) {
        EmpresaVO empresaVO = null;
        Empresa empresaDAO = null;
        try {
            empresaDAO = new Empresa(this.con);
            empresaVO = empresaDAO.consultarPorChavePrimaria(malaDireta.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception ex) {
            email.setErro(ex.getMessage());
        } finally {
            empresaDAO = null;
        }
        if (empresaVO != null) {
            nomeEmpresa = empresaVO.getNome();
        }
        return nomeEmpresa;
    }

    private String substituirTagsSendy(String mensagemComAlteracaoTag) {
        if(mensagemComAlteracaoTag.contains("TAG_PNOME"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_PNOME", "[TAG_PNOME,fallback=]");
        if(mensagemComAlteracaoTag.contains("TAG_NOME"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_NOME", "[TAG_NOME,fallback=]");
        if(mensagemComAlteracaoTag.contains("TAG_CODCLIENTE"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_CODCLIENTE", "[TAG_CODCLIENTE,fallback=]");
        if(mensagemComAlteracaoTag.contains("TAG_EMAIL_CLIENTE"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_EMAIL_CLIENTE", "[TAG_EMAIL_CLIENTE,fallback=]");
        if(mensagemComAlteracaoTag.contains("TAG_BOLETO"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_BOLETO", "[TAG_BOLETO,fallback=]");
        if(mensagemComAlteracaoTag.contains("TAG_PARCELAS_COBRANCA"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_PARCELAS_COBRANCA", "[TAG_PARCELAS_COBRANCA,fallback=]");
        if(mensagemComAlteracaoTag.contains("TAG_PAGONLINE"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_PAGONLINE", "[TAG_PAGONLINE,fallback=]");
        if(mensagemComAlteracaoTag.contains("TAG_PESQUISA"))
            mensagemComAlteracaoTag = mensagemComAlteracaoTag.replace("TAG_PESQUISA", "[TAG_PESQUISA,fallback=]");

        return mensagemComAlteracaoTag;
    }

    private boolean tratarInvalidAdress(javax.mail.SendFailedException emailInvalido, List<MailingItensController.MailingItem> emails) {
        boolean sair = true;
        if (emailInvalido.toString().contains("Invalid Addresses")) {
            String[] split = emailInvalido.toString().split("<");
            for (String str : split) {
                if (str.contains(">")) {
                    sair = false;
                    String email = str.substring(0, str.indexOf(">"));
                    for (MailingItensController.MailingItem mi : emails){
                        if (mi.getValor().equals(email)) {
                            if(emailInvalido.toString().contains("you are sending too many mails")){
                                mi.setEnviado(false).setErro("Limite excedido");
                            }else{
                                mi.setEnviado(false).setErro("Email invalido");
                            }
                        }
                    }
                }
            }

            // https://support.google.com/a/answer/3726730?hl=pt-BR
            // Referência de erros SMTP
        }else if(emailInvalido.toString().contains("550 5.4.5")){ //  excedeu o limite de redirecionamento SMTP diário
            for (MailingItensController.MailingItem mi : emails) {
                for (Address mail : emailInvalido.getValidUnsentAddresses()) {
                    if (mi.getValor().equals(mail.toString())) {
                        if (emailInvalido.toString().contains("sending quota exceeded.")) {
                            mi.setEnviado(false).setErro("Limite excedido");
                            break;
                        }
                    }
                }
            }
        }
        return sair;
    }

    private void tratarExceptionBoleto(Exception exception, String[] emailDest, List<MailingItensController.MailingItem> emails) throws Exception {
        for (MailingItensController.MailingItem mi : emails) {
            for (String email : emailDest) {
                if (mi.getValor().equalsIgnoreCase(email)) {
                    mi.setEnviado(false).setErro(exception.getMessage());
                    break;
                }
            }
        }
    }

    private List<Message> obterListaTelefones(MalaDiretaVO malaDireta, MailingHistoricoVO historico, ClienteVO cliente) throws Exception {
        List<Message> listaBeans = new ArrayList<Message>();
         switch (malaDireta.getTipoAgendamento()) {
            case AGENDAMENTO_INSTANTANEO:
                //iterar para validar os telefones
                for (MalaDiretaEnviadaVO malaDiretaEnviadaVO : malaDireta.getMalaDiretaEnviadaVOs()) {
                    if (malaDiretaEnviadaVO.getClienteVO().getPessoa().getCodigo() != 0) {
                        if (!malaDiretaEnviadaVO.getClienteVO().getPessoa().getTelefonesCelular().isEmpty()) {
                            String[] telefones = malaDiretaEnviadaVO.getClienteVO().getPessoa().getTelefonesCelular().split(";");
                            if (telefones.length > 0) {
                                List<Message> messagesTel = montarListaBeans(
                                        malaDireta,
                                        malaDiretaEnviadaVO.getClienteVO().getPessoa().getNome(),
                                        malaDiretaEnviadaVO.getClienteVO().getPessoa().getCodigo(),
                                        malaDiretaEnviadaVO.getClienteVO().getCodigo(),
                                        telefones
                                );
                                for (Message m : messagesTel) {
                                    // Verificação de duplicidade local
                                    if (!exists(listaBeans, m)) {

                                        // Se for GYMBOT_PRO, verifica duplicidade histórica no map
                                        String chaveEnvio = m.getCodigoCliente() + ":" + m.getNumero();
                                        boolean jaEnviada = false;

                                        try {
                                            if (malaDireta.getCfgEvento() != null
                                                    && malaDireta.getCfgEvento().getEventoCodigo() != null
                                                    && malaDireta.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.VISITANTES.getCodigo())) {

                                                // Para VISITANTES, segura 24h (1440 minutos)
                                                jaEnviada = verificarMensagemEnviada(chaveEnvio, 1440);
                                            } else {
                                                // Senão, mantém 5 minutos
                                                jaEnviada = verificarMensagemEnviada(chaveEnvio, 5);
                                            }
                                        } catch (Exception ex) {
                                            Uteis.logarDebug("Falha ao verificar duplicidade hist. para cliente "
                                                    + m.getCodigoCliente() + ": " + ex.getMessage());
                                        }



                                        if (!jaEnviada) {
                                            listaBeans.add(m);
                                            mailingItensController.add(
                                                    malaDiretaEnviadaVO.getClienteVO().getCodigo(),
                                                    m.getNumero(),
                                                    0
                                            );
                                        } else {
                                            Uteis.logarDebug("Mensagem duplicada (<5min) para cliente "
                                                    + m.getCodigoCliente() + ". Ignorando.");
                                            mailingItensController.addExcluido(
                                                    malaDiretaEnviadaVO.getClienteVO().getCodigo(),
                                                    10005, // ou outro código de erro
                                                    0
                                            );
                                        }

                                    } else {
                                        // Já existia na lista (duplicidade local)
                                        if (malaDireta.getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT)) {
                                            mailingItensController.addExcluido(
                                                    malaDiretaEnviadaVO.getClienteVO().getCodigo(),
                                                    10005,
                                                    0
                                            );
                                        } else {
                                            mailingItensController.addExcluido(
                                                    malaDiretaEnviadaVO.getClienteVO().getCodigo(),
                                                    10002,
                                                    0
                                            );
                                        }
                                    }
                                }
                            } else {
                                mailingItensController.addExcluido(
                                        malaDiretaEnviadaVO.getClienteVO().getCodigo(),
                                        ErroEnvioMailing.SEM_TELEFONE.getCodigo(),
                                        0
                                );
                            }
                        } else {
                            mailingItensController.addExcluido(
                                    malaDiretaEnviadaVO.getClienteVO().getCodigo(),
                                    ErroEnvioMailing.SEM_TELEFONE.getCodigo(),
                                    0
                            );
                        }
                    }
                    //se for passivo
                    if (malaDiretaEnviadaVO.getPassivoVO().getCodigo() != 0) {
                        if (!malaDiretaEnviadaVO.getPassivoVO().getTelefones().isEmpty()) {
                            String[] telefones = malaDiretaEnviadaVO.getPassivoVO().getTelefones().split(";");
                            if (telefones.length > 0) {
                                List<Message> messagesTel = montarListaBeans(
                                        malaDireta,
                                        malaDiretaEnviadaVO.getPassivoVO().getNome(),
                                        0, // não tem codigoPessoa
                                        0, // não tem codigoCliente
                                        telefones
                                );
                                for (Message m : messagesTel) {
                                    if (!exists(listaBeans, m)) {

                                        String chaveEnvio = m.getCodigoCliente() + ":" + m.getNumero();
                                        boolean jaEnviada = false;

                                        try {
                                            if (malaDireta.getCfgEvento() != null
                                                    && malaDireta.getCfgEvento().getEventoCodigo() != null
                                                    && malaDireta.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.VISITANTES.getCodigo())) {

                                                // Para VISITANTES, segura 24h (1440 minutos)
                                                jaEnviada = verificarMensagemEnviada(chaveEnvio, 1440);
                                            } else {
                                                // Senão, mantém 5 minutos
                                                jaEnviada = verificarMensagemEnviada(chaveEnvio, 5);
                                            }
                                        } catch (Exception ex) {
                                            Uteis.logarDebug("Falha ao verificar duplicidade hist. para cliente "
                                                    + m.getCodigoCliente() + ": " + ex.getMessage());
                                        }



                                        if (!jaEnviada) {
                                            listaBeans.add(m);
                                            mailingItensController.add(
                                                    malaDiretaEnviadaVO.getPassivoVO().getCodigo(),
                                                    m.getNumero(),
                                                    0
                                            );
                                        } else {
                                            Uteis.logarDebug("Mensagem duplicada (<5min) (passivo) p/ cliente "
                                                    + m.getCodigoCliente() + ". Ignorando.");
                                            mailingItensController.addExcluido(
                                                    malaDiretaEnviadaVO.getPassivoVO().getCodigo(),
                                                    10002,
                                                    0
                                            );
                                        }

                                    } else {
                                        mailingItensController.addExcluido(
                                                malaDiretaEnviadaVO.getPassivoVO().getCodigo(),
                                                10002,
                                                0
                                        );
                                    }
                                }
                            } else {
                                mailingItensController.addExcluido(
                                        malaDiretaEnviadaVO.getPassivoVO().getCodigo(),
                                        ErroEnvioMailing.SEM_TELEFONE.getCodigo(),
                                        0
                                );
                            }
                        } else {
                            mailingItensController.addExcluido(
                                    malaDiretaEnviadaVO.getPassivoVO().getCodigo(),
                                    ErroEnvioMailing.SEM_TELEFONE.getCodigo(),
                                    0
                            );
                        }
                    }

                    //se for indicado
                    if (malaDiretaEnviadaVO.getIndicadoVO().getCodigo() != 0) {
                        if (!malaDiretaEnviadaVO.getIndicadoVO().getTelefones().isEmpty()) {
                            String[] telefones = malaDiretaEnviadaVO.getIndicadoVO().getTelefones().split(";");
                            if (telefones.length > 0) {
                                List<Message> messagesTel = montarListaBeans(
                                        malaDireta,
                                        malaDiretaEnviadaVO.getIndicadoVO().getNomeIndicado(),
                                        0,
                                        0,
                                        telefones
                                );
                                for (Message m : messagesTel) {
                                    if (!exists(listaBeans, m)) {
                                        String chaveEnvio = m.getCodigoCliente() + ":" + m.getNumero();

                                        boolean jaEnviada = false;

                                        try {
                                            if (malaDireta.getCfgEvento() != null
                                                    && malaDireta.getCfgEvento().getEventoCodigo() != null
                                                    && malaDireta.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.VISITANTES.getCodigo())) {

                                                // Para VISITANTES, segura 24h (1440 minutos)
                                                jaEnviada = verificarMensagemEnviada(chaveEnvio, 1440);
                                            } else {
                                                // Senão, mantém 5 minutos
                                                jaEnviada = verificarMensagemEnviada(chaveEnvio, 5);
                                            }
                                        } catch (Exception ex) {
                                            Uteis.logarDebug("Falha ao verificar duplicidade hist. para cliente "
                                                    + m.getCodigoCliente() + ": " + ex.getMessage());
                                        }



                                        if (!jaEnviada) {
                                            listaBeans.add(m);
                                            mailingItensController.add(
                                                    malaDiretaEnviadaVO.getIndicadoVO().getCodigo(),
                                                    m.getNumero(),
                                                    0
                                            );
                                        } else {
                                            Uteis.logarDebug("Mensagem duplicada (<5min) (indicado) p/ cliente "
                                                    + m.getCodigoCliente() + ". Ignorando.");
                                            mailingItensController.addExcluido(
                                                    malaDiretaEnviadaVO.getIndicadoVO().getCodigo(),
                                                    10002,
                                                    0
                                            );
                                        }

                                    } else {
                                        mailingItensController.addExcluido(
                                                malaDiretaEnviadaVO.getIndicadoVO().getCodigo(),
                                                10002,
                                                0
                                        );
                                    }
                                }
                            } else {
                                mailingItensController.addExcluido(
                                        malaDiretaEnviadaVO.getIndicadoVO().getCodigo(),
                                        ErroEnvioMailing.SEM_TELEFONE.getCodigo(),
                                        0
                                );
                            }
                        } else {
                            mailingItensController.addExcluido(
                                    malaDiretaEnviadaVO.getIndicadoVO().getCodigo(),
                                    ErroEnvioMailing.SEM_TELEFONE.getCodigo(),
                                    0
                            );
                        }
                    }
                }

                // Se não houve itens (malaDiretaEnviadaVOs vazia), busca de outro lugar
                if (malaDireta.getMalaDiretaEnviadaVOs().isEmpty()) {
                    obterTelefonesParaMalaDireta(malaDireta, listaBeans, null);
                }
                break;
            case AGENDAMENTO_PREVISTO:
                obterTelefonesParaMalaDireta(malaDireta, listaBeans, cliente);
                break;
        }
        historico.setRegistrosAfetados(listaBeans.size());
        return listaBeans;
    }

    private void obterTelefonesParaMalaDireta(MalaDiretaVO malaDireta, List<Message> listaBeans, ClienteVO cliente) throws Exception {
        Map<String, List<TelefoneVO>> map;
        if (malaDireta.getCfgEvento().getEventoCodigo() != null
                && malaDireta.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.INDICADOS.getCodigo())) {
            map = telefone.obterTelefonesIndicados(malaDireta.getSqlClientes());
        } else {
            if(malaDireta.getMeioDeEnvioEnum().getCodigo().equals(MeioEnvio.GYMBOT.getCodigo())
                    || malaDireta.getMeioDeEnvioEnum().getCodigo().equals(MeioEnvio.GYMBOT_PRO.getCodigo())){
                if (cliente == null) {
                    map = telefone.obterTelefonesEnvioAgendadoBotConversa(malaDireta.getSqlPessoa(), malaDireta);
                } else {
                    map = telefone.obterTelefonesEnvioAgendadoBotConversa(cliente.getPessoa().getCodigo().toString(), malaDireta);
                }
            }else {
                map = telefone.obterTelefonesEnvioAgendado(malaDireta.getSqlPessoa());
            }
        }

        // Define intervalo só se for GYMBOT_PRO (ou outro)
        LocalDateTime intervaloMinimo = null;
        if (malaDireta.getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT_PRO)) {
            intervaloMinimo = LocalDateTime.now().minusMinutes(5);
        }

        if (map != null) {
            Set<String> keySet = map.keySet();
            for (String key : keySet) {
                if (map.get(key) != null && !map.get(key).isEmpty()) {
                    for (TelefoneVO tf : map.get(key)) {

                        if (!Uteis.validarTelefoneCelular(tf.getNumero())){
                            continue;
                        }

                        Message m = montarListaBeans(malaDireta, key, tf.getPessoa(), tf.getCodigo(), tf.getNumero()).get(0);

                        // Verificação de duplicidade local
                        if (!exists(listaBeans, m)) {

                            // Se for GYMBOT_PRO, verifica duplicidade histórica
                            String chaveEnvio = m.getCodigoCliente() + ":" + tf.getNumero();
                            boolean jaEnviada = false;
                            if (intervaloMinimo != null) {
                                try {
                                    if (malaDireta.getCfgEvento() != null
                                            && malaDireta.getCfgEvento().getEventoCodigo() != null
                                            && malaDireta.getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.VISITANTES.getCodigo())) {

                                        // Para VISITANTES, segura 24h (1440 minutos)
                                        jaEnviada = verificarMensagemEnviada(chaveEnvio, 1440);
                                    } else {
                                        // Senão, mantém 5 minutos
                                        jaEnviada = verificarMensagemEnviada(chaveEnvio, 5);
                                    }
                                } catch (Exception ex) {
                                    Uteis.logarDebug("Falha ao verificar duplicidade hist. para cliente "
                                            + m.getCodigoCliente() + ": " + ex.getMessage());
                                }

                            }

                            if (!jaEnviada) {
                                // Não duplicada ? adiciona
                                listaBeans.add(m);
                                mailingItensController.add(tf.getCodigo(), m.getNumero(), 0);
                            } else {
                                // Já enviada recentemente
                                Uteis.logarDebug("Mensagem duplicada (<5min) para cliente "
                                        + m.getCodigoCliente() + ". Ignorando.");
                                // Marca como excluída
                                if (malaDireta.getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT)) {
                                    mailingItensController.addExcluido(tf.getCodigo(), 10005, 0);
                                } else {
                                    mailingItensController.addExcluido(tf.getCodigo(), 10002, 0);
                                }
                            }

                        } else {
                            // Duplicidade local
                            if (malaDireta.getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT)) {
                                mailingItensController.addExcluido(tf.getCodigo(), 10005, 0);
                            } else {
                                mailingItensController.addExcluido(tf.getCodigo(), 10002, 0);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Joao Alcides
     * 18/01/2012
     */
    private Map<Integer, List<String>> fracionarDestinatarios(List<String> emails) {
        //o index pra obter o destinatario
        int index = 0;
        int chave = 0;
        Map<Integer, List<String>> destinatarios = new HashMap<Integer, List<String>>();
        //enquanto o index for menor do que o numero de destinarios
        while (index < emails.size()) {
            List<String> lista = new ArrayList<String>();
            for (int i = 1; i <= SuperControle.nrLimiteDestinatariosEmail; i++) {
                lista.add(emails.get(index++));
                //caso o index seja igual ao numero de destinarios, para
                if (index == emails.size()) {
                    break;
                }
            }
            destinatarios.put(chave++, lista);
        }
        return destinatarios;
    }

    private List<Message> montarListaBeans(MalaDiretaVO malaDireta, String nomePessoa, Integer codPessoa, Integer codCliente, String... telefones) throws Exception {
        List<Message> listaBeans = new ArrayList<Message>();
        VendasConfig vendasConfigDAO;
        vendasConfigDAO = new VendasConfig(malaDiretaDAO.getCon());
        String mensagem="";
        String mensagemComTagsEmpresa = ModeloMensagemVO.personalizarModeloMsg(malaDireta.getMensagem(), malaDireta.getEmpresa(), key, false);
        if (telefones != null) {
            for (String tel : telefones) {
                //personalizar a tag de nome
                mensagem = mensagemComTagsEmpresa;
                if (!malaDireta.getMeioDeEnvioEnum().getDescricao().equals(MeioEnvio.WHATSAPP.getDescricao()) && !malaDireta.getMeioDeEnvioEnum().getDescricao().equals(MeioEnvio.GYMBOT.getDescricao()) ) {
                    mensagem = ModeloMensagemVO.personalizarTagNomePessoa(mensagem, nomePessoa);
                    if(mensagem.contains("TAG_PAGONLINE")) {
                        String linkPagamento = vendasConfigDAO.tagPagamentosms(key, malaDireta.getEmpresa().getCodigo(), codCliente, true);
                        mensagem = mensagem.replaceAll("TAG_PAGONLINE", linkPagamento);
                        if(mensagem.length() > 140){
                            mensagem = "Olá, segue seu link de pagamento: " + linkPagamento;
                        }
                    }
                    if (mensagem.contains("TAG_SALDOPONTOS")) {
                        Integer saldoPontos = historicoPontosDao.obterPontosTotalPorCliente(codCliente);
                        mensagem = mensagem.replace("TAG_SALDOPONTOS", saldoPontos.toString());
                    }
                    mensagem = resolverTagPesquisaMalaDireta(malaDireta, mensagem, codPessoa, codCliente,false);
                }
                malaDireta.setMensagemComAlteracaoTag(mensagem);
                Message beanMessage = new Message();
                beanMessage.setTipo((malaDireta.getMeioDeEnvioEnum().getDescricao().equals(MeioEnvio.WHATSAPP.getDescricao()) ? MeioEnvio.WHATSAPP.getDescricao() : MeioEnvio.SMS.getDescricao()));
                beanMessage.setCodigoPessoa(codPessoa);
                beanMessage.setNome(nomePessoa);
                beanMessage.setCodigoCliente(codCliente);
                beanMessage.setMsg(malaDireta.getMensagemComAlteracaoTag());
                beanMessage.setNumero(tel);
                listaBeans.add(beanMessage);
            }
        }
        return listaBeans;
    }

    public boolean isEnvioViaServico() {
        return envioViaServico;
    }

    public void setEnvioViaServico(boolean envioViaServico) {
        this.envioViaServico = envioViaServico;
    }


    public boolean exists(List<Message> listaBeans, Message m){
        for(Message m1 : listaBeans){
            if(m1.getNumero().equalsIgnoreCase(m.getNumero())){
                return true;
            }
        }
        return false;

    }

    private void enviarMalaDiretaAssincrono(List<Integer> codigosMalasDireta, final ClienteVO cliente, final OcorrenciaEnum ocorrenciaEnum) {
        for (final Integer malaDireta : codigosMalasDireta) {
            System.out.println("Enviar email assincrono | MalaDireta: " + malaDireta + " || Ocorrencia: " + ocorrenciaEnum.name());
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (ocorrenciaEnum.equals(OcorrenciaEnum.INCLUSAO_VISITANTE) ||
                                ocorrenciaEnum.equals(OcorrenciaEnum.VENDA_CONTRATO) ||
                                ocorrenciaEnum.equals(OcorrenciaEnum.APOS_MATRICULA) ||
                                ocorrenciaEnum.equals(OcorrenciaEnum.APOS_RENOVACAO) ||
                                ocorrenciaEnum.equals(OcorrenciaEnum.APOS_REMATRICULA) ||
                                ocorrenciaEnum.equals(OcorrenciaEnum.APOS_CANCELAMENTO)) {
                            //Aguardar para evitar deadlock antes que a operação seja finalizada
                            Thread.sleep(120000);
                        }
                        Connection con = new DAO().obterConexaoEspecifica(key);
                        Conexao.guardarConexaoForJ2SE(key, con);
                        if (malaDiretaDAO.clienteValidoParaEnvio(ocorrenciaEnum, cliente, malaDireta, con)) {
                            MailingService ms = new MailingService(con);
                            ms.enviarMalaDireta(malaDireta, true, cliente);
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, MailingService.class);
                    }
                }
            }).start();
        }
    }

    public void enviarMalaDiretaComOcorrencia(OcorrenciaEnum ocorrenciaEnum, Integer cliente) {
        try {
            if (!UteisValidacao.emptyNumber(cliente)) {
                ClienteVO clienteVO = getClienteDAO().consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_TODOS);
                enviarMalaDiretaAssincrono(malaDiretaDAO.consultarCodigosPorOcorrencia(ocorrenciaEnum, clienteVO.getEmpresa().getCodigo()), clienteVO, ocorrenciaEnum);
            }
        } catch (Exception ignored) {
        }
    }

    public Cliente getClienteDAO() {
        return clienteDAO;
    }

    public void setClienteDAO(Cliente clienteDAO) {
        this.clienteDAO = clienteDAO;
    }

    private String enviarFTP(String host, Integer port, String user, String pass, String filePath, String uploadPath, String nomeArquivo) {
//        String host = "localhost";
//        String user = "luiz";
//        String pass = "teste";
//        String filePath = "C:\\PactoJ\\testeFTP.txt";
//        String uploadPath = "/testeFTP_ENVIO.txt";

        try {
            String ftpUrl = "ftp://%s:%s@%s/%s;type=i";
            String username = URLEncoder.encode(user, "UTF-8");
            String password = URLEncoder.encode(pass, "UTF-8");

            ftpUrl = String.format(ftpUrl, username, password, (host + ":" + port), (uploadPath + "/" + nomeArquivo));
            System.out.println("Upload URL: " + ftpUrl);

            URL url = new URL(ftpUrl);
            URLConnection conn = url.openConnection();
            OutputStream outputStream = conn.getOutputStream();
            FileInputStream inputStream = new FileInputStream(filePath);

            byte[] buffer = new byte[4096];
            int bytesRead = -1;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            inputStream.close();
            outputStream.close();
            System.out.println("File uploaded");
            return "ok";
        } catch (IOException ex) {
            Uteis.logar(null,"Erro ao enviarFTP: " + ex.getMessage());
            return "ERRO: " + ex.getMessage();
        }
    }

    private void resolverTagPix(MalaDiretaVO malaDiretaVO, UteisEmail uteisEmail, String mensagemComAlteracaoTag, Integer codPessoa) throws Exception {
        if (mensagemComAlteracaoTag.contains("TAG_PIX")) {
            Pix pixDAO;
            ConvenioCobranca convenioCobrancaDAO;
            Empresa empresaDAO;
            Usuario usuarioDAO;
            Pessoa pessoaDAO;
            MovParcela movParcelaDAO;
            Transacao transacaoDAO = null;
            try {
                pixDAO = new Pix(this.con);
                convenioCobrancaDAO = new ConvenioCobranca(this.con);
                empresaDAO = new Empresa(this.con);
                usuarioDAO = new Usuario(this.con);
                transacaoDAO = new Transacao(this.con);
                pessoaDAO = new Pessoa(this.con);
                movParcelaDAO = new MovParcela(this.con);

                Integer convenioPixEmpresa = empresaDAO.obterConvenioCobrancaPix(malaDiretaVO.getEmpresa().getCodigo());
                ConvenioCobrancaVO convenioCobrancaVO = null;
                if (!UteisValidacao.emptyNumber(convenioPixEmpresa)) {
                    convenioCobrancaVO = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioPixEmpresa, malaDiretaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    List<ConvenioCobrancaVO> lista = convenioCobrancaDAO.consultarPorTipoCobranca(TipoCobrancaEnum.PIX, malaDiretaVO.getEmpresa().getCodigo(), SituacaoConvenioCobranca.ATIVO, null, Uteis.NIVELMONTARDADOS_TODOS);
                    if (!UteisValidacao.emptyList(lista)) {
                        convenioCobrancaVO = lista.get(0);
                    }
                }
                if (convenioCobrancaVO == null || UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
                    throw new Exception("Nenhum convnio PIX encontrado");
                }

                FormaPagamentoVO formaPagamentoVO = pixDAO.obterFormaPagamentoPix(null, convenioCobrancaVO);
                if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                    throw new Exception("Forma de Pagamento PIX encontrada");
                }

                tratarInicioFimEventoMalaDireta(malaDiretaVO);

                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                List<MovParcelaVO> parcelasEmAtraso = movParcelaDAO.consultarPorCodigoPessoaDiasEmAberto(codPessoa, malaDiretaVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                //setar valores de multa e juros nas parcelas
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(malaDiretaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, TipoCobrancaEnum.PIX, parcelasEmAtraso, Calendario.hoje());

                PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(codPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                PixVO pixVO = pixDAO.gerarObterPix(key, pessoaVO, convenioCobrancaVO, empresaVO, parcelasEmAtraso, usuarioVO, formaPagamentoVO.getCodigo(), OrigemCobrancaEnum.MAILING, true);
                malaDiretaVO.getListaPixEnviar().add(pixVO);
            } catch (Exception ex) {
                if (transacaoDAO == null) {
                    transacaoDAO = new Transacao(this.con);
                }
                throw ex;
            } finally {
                pixDAO = null;
                convenioCobrancaDAO = null;
                empresaDAO = null;
                usuarioDAO = null;
                pessoaDAO = null;
                transacaoDAO = null;
                movParcelaDAO = null;
            }
        }
    }

    public static void main(String[] args) {
        try {
            MailingService mailingService = new MailingService("key");
            mailingService.enviarMalaDireta(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

