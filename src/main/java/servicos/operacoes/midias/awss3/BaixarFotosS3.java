package servicos.operacoes.midias.awss3;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import org.apache.commons.io.IOUtils;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

public class BaixarFotosS3 {

    // Mapa para armazenar nomes de arquivos: chaveS3 -> nomeArquivo
    private static final Map<String, String> mapaNomesArquivos = new HashMap<>();

    public static void main(String[] args) throws Exception {
        if (args.length == 0)
            args = new String[]{"79b6fe8f03c95397a40490c0776f45a3"};
        String chaveacademia = args[0];
        Integer codigoEmpresa = null;

        // Verificar se foi fornecido código da empresa como segundo parâmetro
        if (args.length > 1) {
            try {
                codigoEmpresa = Integer.parseInt(args[1]);
                System.out.println("Filtrando por empresa: " + codigoEmpresa);
            } catch (NumberFormatException e) {
                System.out.println("Código da empresa inválido: " + args[1] + ". Baixando todas as mídias.");
            }
        }

        AmazonS3Client amazonS3Client = new AmazonS3Client();
        MidiaEntidadeEnum[] midias = new MidiaEntidadeEnum[]{
                MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO,
                MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO,
                MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO_PDF,
                MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO,
                MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO_PDF,
                MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO,
                MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO_PDF,
                MidiaEntidadeEnum.ANEXO1,
                MidiaEntidadeEnum.ANEXO1_PDF,
                MidiaEntidadeEnum.ANEXO2,
                MidiaEntidadeEnum.ANEXO2_PDF,
                MidiaEntidadeEnum.FOTO_PESSOA
        };

        for (MidiaEntidadeEnum m : midias) {
            if (m == MidiaEntidadeEnum.FOTO_PESSOA) {
                processarDownloadFotoPessoa(amazonS3Client, chaveacademia, codigoEmpresa);
            } else {
                // Obter lista de chaves S3 válidas para a empresa se código foi fornecido
                Map<String, Integer> chavesValidasEmpresa = null;
                if (codigoEmpresa != null) {
                    chavesValidasEmpresa = obterChavesValidasEmpresa(chaveacademia, codigoEmpresa);
                    System.out.println("Encontradas " + chavesValidasEmpresa.size() + " chaves S3 válidas para " + m.name() + " da empresa " + codigoEmpresa);
                }
                processarDownloadMidia(m, amazonS3Client, chaveacademia, chavesValidasEmpresa);
            }
        }

    }

    private static void processarDownloadMidia(MidiaEntidadeEnum m, AmazonS3Client amazonS3Client, String chaveacademia, Map<String, Integer> chavesValidasEmpresa) throws Exception {
        ObjectListing result = amazonS3Client.getS3().listObjects(new ListObjectsRequest()
                .withBucketName(amazonS3Client.getBucketName())
                .withPrefix(chaveacademia + "/" + Uteis.encriptarAWS(m.name().toLowerCase())));
        byte[] foto;
        try {
            do {
                for (Iterator<S3ObjectSummary> iterator = result.getObjectSummaries().iterator(); iterator.hasNext(); ) {
                    S3ObjectSummary summary = iterator.next();

                    // Se foi especificada uma empresa, verificar se a chave S3 está na lista de chaves válidas
                    if (chavesValidasEmpresa != null && !chavesValidasEmpresa.containsKey(summary.getKey())) {
                        continue; // Pular este arquivo
                    }

                    try (S3Object object = amazonS3Client.getS3().getObject(new GetObjectRequest(amazonS3Client.getBucketName(), summary.getKey()))) {
                        foto = IOUtils.toByteArray(object.getObjectContent());
                        try {

                            if (chavesValidasEmpresa == null) {
                                salvarArquivo(chaveacademia + "/" + m.name() + "/" + summary.getKey().replace(chaveacademia + "/", ""),
                                        foto, "/opt/ZW_ARQ");
                            } else {
                                Integer identificadorPessoa = chavesValidasEmpresa.get(summary.getKey());
                                String extension = summary.getKey().split("\\.")[1];

                                // Verificar se existe nome personalizado para este arquivo
                                String nomePersonalizado = mapaNomesArquivos.get(summary.getKey());

                                if (nomePersonalizado != null) {
                                    // Usar nome personalizado da tabela arquivo
                                    salvarArquivo(chaveacademia + "/" + m.name() + "/" + nomePersonalizado, foto, "/opt/ZW_ARQ");
                                } else {
                                    // Para outros tipos, manter nomenclatura original
                                    salvarArquivo(chaveacademia + "/" + m.name() + "/CONTRATO_" + identificadorPessoa + "." + extension,
                                            foto, "/opt/ZW_ARQ");
                                }
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } catch (IOException ex) {
                        Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
                // load more items!!!
                result = result.isTruncated() ? amazonS3Client.getS3().listNextBatchOfObjects(result) : null;
            } while (result != null);
        } catch (Exception e) {
            Uteis.logar(null, "Downloading an object FAILED: " + e.getMessage());
        }
    }

    public static String salvarArquivo(String nomeArquivo, byte[] dados, String destinationFile) throws Exception {
        String caminho = destinationFile;
        boolean windows = System.getProperty("os.name").toUpperCase().contains("WINDOWS");

        // Sanitizar nome do arquivo para Windows
        String nomeArquivoSanitizado = nomeArquivo;
        if (windows) {
            // Substituir caracteres inválidos no Windows: < > : " | ? * \ / @
            nomeArquivoSanitizado = nomeArquivo
                .replace("<", "_")
                .replace(">", "_")
                .replace(":", "_")
                .replace("\"", "_")
                .replace("|", "_")
                .replace("?", "_")
                .replace("*", "_")
                .replace("\\", "_")
                .replace("@", "_")
                .replace("=", "_");
        }

        caminho += "/" + nomeArquivoSanitizado;
        Uteis.forceDirectory(caminho.substring(0, caminho.lastIndexOf("/")));
        File arquivoDados = new File(caminho);
        arquivoDados.createNewFile();
        OutputStream os = new FileOutputStream(arquivoDados);
        os.write(dados);
        os.close();
        System.out.println("gravei DADOS -> " + caminho);
        return arquivoDados.getAbsoluteFile().toString();
    }

    /**
     * Processa o download de fotos de pessoa filtradas por empresa
     * MANTÉM a funcionalidade original da pasta FACIAL_PESSOA
     */
    private static void processarDownloadFotoPessoa(AmazonS3Client amazonS3Client, String chaveacademia, Integer codigoEmpresa) throws Exception {
        try (Connection con = new DAO().obterConexaoEspecifica(chaveacademia)) {
            Cliente clienteDAO = new Cliente(con);
            Map<String, String> mapFotokeyCodAcesso = clienteDAO.getMapCodAcessoFotokey();

            // Se foi especificada uma empresa, filtrar apenas pessoas dessa empresa
            if (codigoEmpresa != null) {
                mapFotokeyCodAcesso = filtrarFotosPorEmpresa(con, mapFotokeyCodAcesso, codigoEmpresa);
                System.out.println("Filtrando fotos de pessoa para empresa " + codigoEmpresa + ": " + mapFotokeyCodAcesso.size() + " fotos");
            }

            for (Map.Entry<String, String> itemCodAcessoFotokey : mapFotokeyCodAcesso.entrySet()) {
                byte[] foto;
                String key = itemCodAcessoFotokey.getValue().split("\\?time")[0];
                key = key.split("\\?v=")[0];
                S3Object object = null;
                try {
                    object = amazonS3Client.findS3Object(key);
                    foto = IOUtils.toByteArray(object.getObjectContent());
                    try {
                        // MANTÉM o formato original: FACIAL_PESSOA/CODACESSO_
                        salvarArquivo(chaveacademia + "/FACIAL_PESSOA/CODACESSO_" + itemCodAcessoFotokey.getKey() + ".jpg", foto, "/opt/ZW_ARQ");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } catch (IOException ex) {
                    Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                } finally {
                    if (object != null) {
                        object.close();
                    }
                }
            }
        }
    }

    /**
     * Filtra o mapa de fotos para incluir apenas pessoas da empresa especificada
     * MANTÉM a funcionalidade original baseada em códigos de acesso
     */
    private static Map<String, String> filtrarFotosPorEmpresa(Connection con, Map<String, String> mapFotokeyCodAcesso, Integer codigoEmpresa) throws Exception {
        Map<String, String> mapFiltrado = new HashMap<>();

        // Obter lista de códigos de acesso de clientes da empresa
        // Incluir tanto clientes quanto colaboradores
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT codacesso FROM (");
        sql.append("  SELECT c.codacesso FROM cliente c WHERE c.empresa = ").append(codigoEmpresa).append(" AND c.codacesso IS NOT NULL");
        sql.append("  UNION ");
        sql.append("  SELECT col.codacesso FROM pessoa p ");
        sql.append("  INNER JOIN colaborador col ON col.pessoa = p.codigo ");
        sql.append("  WHERE col.empresa = ").append(codigoEmpresa).append(" AND col.codacesso IS NOT NULL");
        sql.append(") AS codigos_acesso");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        Set<String> codigosAcessoEmpresa = new HashSet<>();
        while (rs.next()) {
            String codacesso = rs.getString("codacesso");
            if (codacesso != null && !codacesso.trim().isEmpty()) {
                codigosAcessoEmpresa.add(codacesso);
            }
        }

        // Filtrar o mapa original
        for (Map.Entry<String, String> entry : mapFotokeyCodAcesso.entrySet()) {
            if (codigosAcessoEmpresa.contains(entry.getKey())) {
                mapFiltrado.put(entry.getKey(), entry.getValue());
            }
        }

        return mapFiltrado;
    }



    /**
     * Obtém todas as chaves S3 válidas para uma empresa específica
     * Consulta diretamente as tabelas que armazenam os anexos por tipo de mídia
     */
    private static Map<String, Integer> obterChavesValidasEmpresa(String chaveacademia, Integer codigoEmpresa) throws Exception {
        Map<String, Integer> chavesValidas = new HashMap<>();

        try (Connection con = new DAO().obterConexaoEspecifica(chaveacademia)) {
            // ANEXO_ASSINATURA_CONTRATO, ANEXO_DOCUMENTOS_CONTRATO, ANEXO_ENDERECO_CONTRATO,
            // ANEXO1, ANEXO2 - todos vêm da tabela contratoassinaturadigital
            chavesValidas.putAll(obterChavesContratoAssinaturaDigital(con, codigoEmpresa));

            // ANEXO_ATESTADO_APTIDAO e ANEXO_ATESTADO_APTIDAO_PDF - vêm da tabela arquivo
            Map<String, String> chavesArquivos = obterChavesArquivosPorEmpresa(con, chaveacademia, codigoEmpresa);
            // Armazenar na variável de classe para uso posterior
            mapaNomesArquivos.putAll(chavesArquivos);
            // Adicionar ao mapa principal (valor 0 pois não é usado para arquivos)
            for (String chave : chavesArquivos.keySet()) {
                chavesValidas.put(chave, 0);
            }
        }

        return chavesValidas;
    }

    /**
     * Obtém chaves S3 da tabela contratoassinaturadigital para contratos de uma empresa específica
     */
    private static Map<String, Integer> obterChavesContratoAssinaturaDigital(Connection con, Integer codigoEmpresa) throws Exception {
        Map<String, Integer> chaves = new HashMap<>();

        String sql = "SELECT contrato, cad.documentos, cad.endereco, cad.assinatura, cad.assinatura2, " +
                     "cad.atestado, cad.anexo1, cad.anexo2, cad.anexocancelamento " +
                     "FROM contratoassinaturadigital cad " +
                     "INNER JOIN contrato c ON c.codigo = cad.contrato " +
                     "WHERE c.empresa = " + codigoEmpresa;

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("documentos"));
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("endereco"));
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("assinatura"));
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("assinatura2"));
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("atestado"));
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("anexo1"));
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("anexo2"));
            adicionarChaveSeValida(chaves, rs.getInt("contrato"), rs.getString("anexocancelamento"));
        }

        return chaves;
    }

    /**
     * Obtém chaves S3 da tabela arquivo para ANEXO_ATESTADO_APTIDAO e ANEXO_ATESTADO_APTIDAO_PDF
     * filtradas por empresa. Retorna mapa com chaveS3 -> "ARQUIVO_codigo_extensao"
     */
    private static Map<String, String> obterChavesArquivosPorEmpresa(Connection con, String chaveacademia, Integer codigoEmpresa) throws Exception {
        Map<String, String> chaves = new HashMap<>();
        AmazonS3Client amazonS3Client = new AmazonS3Client();

        // Consultar todos os arquivos filtrados por empresa (sem filtro por tipo)
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT a.codigo, a.extensao, a.pessoa ");
        sql.append("FROM arquivo a ");
        sql.append("INNER JOIN pessoa p ON p.codigo = a.pessoa ");
        sql.append("WHERE (");
        sql.append("  EXISTS (SELECT 1 FROM cliente c WHERE c.pessoa = a.pessoa AND c.empresa = ").append(codigoEmpresa).append(") ");
        sql.append("  OR ");
        sql.append("  EXISTS (SELECT 1 FROM colaborador col WHERE col.pessoa = a.pessoa AND col.empresa = ").append(codigoEmpresa).append(") ");
        sql.append(")");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            int codigoArquivo = rs.getInt("codigo");
            String extensao = rs.getString("extensao");

            // Gerar chaves S3 e nome do arquivo
            try {
                String nomeArquivo = "ARQUIVO_" + codigoArquivo + extensao;

                // Para todos arquivos - usar ANEXO_ATESTADO_APTIDAO
                String chave = amazonS3Client.genKey(chaveacademia, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, Integer.toString(codigoArquivo), extensao);
                chaves.put(chave, nomeArquivo);

                // Para arquivos PDF - usar também ANEXO_ATESTADO_APTIDAO_PDF
                if (extensao != null && extensao.toLowerCase().contains("pdf")) {
                    String chavePdf = amazonS3Client.genKey(chaveacademia, MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO_PDF, Integer.toString(codigoArquivo), extensao);
                    chaves.put(chavePdf, nomeArquivo);
                }

            } catch (Exception e) {
                System.err.println("Erro ao gerar chave S3 para arquivo " + codigoArquivo + ": " + e.getMessage());
            }
        }

        return chaves;
    }



    /**
     * Adiciona uma chave ao conjunto se ela for válida (não nula e não vazia)
     */
    private static void adicionarChaveSeValida(Map<String, Integer> chaves, Integer contrato, String chave) {
        if (chave != null && !chave.trim().isEmpty() && !chave.contains("image_icon.jpg")) {
            chaves.put(chave, contrato);
        }
    }

}
