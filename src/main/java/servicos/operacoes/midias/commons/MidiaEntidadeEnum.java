/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias.commons;

import negocio.comuns.utilitarias.Uteis;

/**
 * <AUTHOR>
 */
public enum MidiaEntidadeEnum {

    FOTO_EMPRESA(1, ".jpg", "foto", true),
    FOTO_EMPRESA_RELATORIO(2, ".jpg", "fotoRelatorio", true),
    FOTO_EMPRESA_EMAIL(3, ".jpg", "fotoEmail", true),
    FOTO_EMPRESA_REDESOCIAL(4, ".jpg", "fotoRedeSocial", true),
    FOTO_EMPRESA_HomeBackground640x551(5, ".jpg", "homeBackground640x551", true),
    FOTO_EMPRESA_HomeBackground320x276(6, ".jpg", "homeBackground320x276", true),
    FOTO_PACOTE_STUDIO(7, ".jpg", "imagem", true),
    FOTO_PESSOA(8, ".jpg", "foto", true),
    FOTO_MODELO_MENSAGEM(9, ".jpg", "imagemModelo", true),
    FOTO_LOGIN_POST_PEQUENO(10, ".jpg", "imagem", true),
    FOTO_LOGIN_SLIDER_JPG(11, ".jpg", "slider", true),
    FOTO_LOGIN_SLIDER_GIF(12, ".gif", "slider_gif", false),
    FOTO_EMPRESA_PROPAGANDA_BOLETO(13, ".jpg", "propagandaBoleto", true),
    PDF_NFE(14, ".pdf", "idRPS", false),
    XML_NFE(15, ".xml", "idRPS", false),
    FOTO_ANEXO_LANCAMENTO(16, ".jpg", "anexoLancamento", true),
    ANEXO_ASSINATURA_CONTRATO(17, ".png", "anexoAssinatura", false),
    ANEXO_DOCUMENTOS_CONTRATO(18, ".jpg", "anexoDocumentos", true),
    ANEXO_ENDERECO_CONTRATO(19, ".jpg", "anexoEndereco", true),
    ANEXO_ATESTADO_APTIDAO(20, ".jpg", "atestadoAptidao", true),
    ASSINATURA_EMPRESA(21, ".jpg", "assinaturaEmpresa", true),
    ANEXO_ATESTADO_OPERACAO_CONTRATO(22, ".jpg", "atestadoOperacaoContrato", false),
    ANEXO_CLIENTE(23, ".jpg", "anexoCliente", false),
    FOTO_AVALIACAO_FISICA(24, ".jpg", "avaliacaoFisica", false),
    FOTO_AVALIACAO_POSTURAL(25, ".jpg", "avaliacaoPostural", false),
    ASSINATURA_USUARIO(28, ".jpg", "assinaturaUsuario", true),
    FOTO_MODADLIDADE(29, ".jpg", "fotoModalidade", true),
    ANEXO1(30, ".jpg", "anexo1", true),
    ANEXO2(31, ".jpg", "anexo2", true),
    PRINT_ACOES_USUARIO(32, ".png", "print", false),
    PESQUISA(33, ".png", "pesquisa", false),
    NOTAFISCAL_CERTIFICADO(34, ".pfx", "notaFiscalCertificado", false),
    NOTAFISCAL_LOGOTIPO(35, ".png", "notaFiscalLogotipo", false),
    IMAGENS_ACADEMIA_VENDAS(36, ".jpg", "imagemAcademiaVendas", true),
    IMAGENS_PRODUTO_ACADEMIA(37, ".jpg", "imagemProdutoAcademia", false),
    ANEXO_ARQUIVO_MOV_CONTA(38, ".jpg", "arquivoMovConta", false),
    PESSOA_ANEXO1(39, ".jpg", "pessoaanexo1", true),
    PESSOA_ANEXO2(40, ".jpg", "pessoaanexo2", true),
    ANEXO_CANCELAMENTO(41, ".jpg", "anexoCancelamento", true),
    IMAGENS_MODALIDADE_CORROUSEL_VENDAS(42, ".png", "imagemModalidadeCarrouselVendas", false),
    IMAGENS_PAGINA_INICIAL_VENDAS(43, ".jpg", "imagemPaginaInicialVendas", true),
    IMAGENS_MENU_VENDAS(44, ".jpg", "imagemMenuVendas", true),
    TEMPLATE_FACIAL_FOTO_PESSOA(45, ".jpg", "templateFacialFotoPessoa", true),
    IMAGENS_BANNER_MULTI_CONFIG_VENDAS(46, ".jpg", "imagemBannerMultiConfigVendas", true),
    IMAGEM_FOTO_FACHADA_VENDAS(47, ".jpg", "imagemFotoFachadaVendas", true),
    IMAGENS_CAPTACAO_LEADS_VENDAS(48, ".jpg", "imagemCaptacaoLeadsVendas", true),
    ANEXO_ASSINATURA_CONTRATO_TERMO_RESPONSABILIDADE(49, ".png", "anexoAssinaturaTermoResponsabilidade", false),
    ANEXO_BOLETO_ALUNO_SENDY(50, ".pdf", "anexoBoletoAlunoSendy", false),
    ANEXO_ARQUIVOS_RH(51, ".jpg", "anexoArquivosRh", false),
    FOTO_AULA(52, ".jpg", "fotoAula", true),
    ANEXO_MODELO_CONTRATO(53, ".jpg", "anexoModeloContrato", true),
    ANEXO_ASSINATURA_CANCELAMENTO_CONTRATO(54, ".png", "anexoAssinaturaCancelamentoContrato", false),
    ANEXO1_PDF(55, ".pdf", "anexo1Pdf", false),
    ANEXO2_PDF(56, ".pdf", "anexo2Pdf", false),
    ANEXO_DOCUMENTOS_CONTRATO_PDF(57, ".pdf", "anexoDocumentosPdf", false),
    ANEXO_ENDERECO_CONTRATO_PDF(58, ".pdf", "anexoEnderecoPdf", false),
    ANEXO_ATESTADO_APTIDAO_PDF(59, ".pdf", "atestadoAptidaoPdf", false),
    NOTA_FISCAL_ZIP(60, ".zip", "notaFiscalZip", false),
    ANEXO_DOCUMENTO_FORNECEDOR(61, ".pdf", "documentoFornecedor", false),
    ANEXO_DOCUMENTO_COMPRA(62, ".pdf", "documentoCompra", false),
    CARTEIRINHA_CLIENTE_PACTO_PRINT(63, ".pdf", "carteirinhaClientePactoPrint", false),
    ANEXO_ASSINATURA_CONTRATO2(64, ".png", "anexoAssinatura2", false),
    ANEXO_DOCUMENTOS_CONTRATO_PRODUTO(65, ".jpg", "anexoDocumentosProduto", true),
    ANEXO_ENDERECO_CONTRATO_PRODUTO(66, ".jpg", "anexoEnderecoProduto", true),
    ANEXO_ASSINATURA_CONTRATO_PRODUTO(67, ".png", "anexoAssinaturaProduto", false),
    ANEXO_ATESTADO_APTIDAO_PRODUTO(68, ".jpg", "atestadoAptidaoProduto", true),
    ANEXO1_PRODUTO(69, ".jpg", "anexo1Produto", true),
    ANEXO2_PRODUTO(70, ".jpg", "anexo2Produto", true),

    ;

    private Integer codigo;
    private String extensao;
    private String nomeCampo;
    private Boolean droparAlphaChannel;

    MidiaEntidadeEnum(Integer codigo, final String extensao, final String nomeCampo, final boolean droparAlphaChannel) {
        this.codigo = codigo;
        this.extensao = extensao;
        this.nomeCampo = nomeCampo;
        this.droparAlphaChannel = droparAlphaChannel;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getExtensao() {
        return extensao;
    }

    public String getNomeCampo() {
        return nomeCampo;
    }

    public Boolean getDroparAlphaChannel() {
        return droparAlphaChannel;
    }

    public static MidiaEntidadeEnum fromValue(String value) {
        for (MidiaEntidadeEnum mee : values()) {
            if (mee.name().equalsIgnoreCase(value)) {
                return mee;
            }
        }
        return  MidiaEntidadeEnum.FOTO_PESSOA;
    }

    public static void main(String[] args) throws Exception {
//        MidiaEntidadeEnum[] midias = new MidiaEntidadeEnum[]{MidiaEntidadeEnum.ANEXO_DOCUMENTOS_CONTRATO,
//        MidiaEntidadeEnum.ANEXO_ENDERECO_CONTRATO,
//        MidiaEntidadeEnum.ANEXO_ASSINATURA_CONTRATO,
//        MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO,
//        MidiaEntidadeEnum.ANEXO1,
//        MidiaEntidadeEnum.ANEXO2};
        for (MidiaEntidadeEnum m : MidiaEntidadeEnum.values()) {
            System.out.println(m.name() + " - " + Uteis.encriptarAWS(m.name().toLowerCase()));
        }
    }
}
