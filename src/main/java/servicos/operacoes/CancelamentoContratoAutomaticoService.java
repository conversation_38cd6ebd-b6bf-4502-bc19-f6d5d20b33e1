package servicos.operacoes;

import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.CancelamentoContratoVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class CancelamentoContratoAutomaticoService {

    public CancelamentoContratoAutomaticoService(Connection con) {
        this.con = con;
    }
    private static final String RENOVACAO = "RN";
    private static final String CANCELADO = "CA";
    private static final String INATIVO = "IN";
    private CancelamentoContratoVO cancelamento;
    private ZillyonWebFacade zwFacade;
    private Connection con;

    /**
     * Método que executa todas as operações do cancelamento automatico este
     * método é análogo ao cancelamento normal, sendo que cada etapa (chamada a
     * um método) simula uma tela do passo a passo do cancelamento no sistema.
     *
     * <AUTHOR> 12/07/2011
     */
    public void cancelarAutomatico(ContratoRecorrenciaVO contratoRecorrencia) throws Exception {
        cancelarAutomaticamente(contratoRecorrencia.getContrato(),contratoRecorrencia.getValorMensal());
    }

    public void cancelarAutomaticamente(ContratoVO contratoVO, Double valorMensal) throws Exception {

        if (contratoVO.getSituacao().equals(CANCELADO) || contratoVO.getSituacao().equals(INATIVO)  || getZWFacade().getContratoOperacao().existeOperacaoParaEsteContrato(contratoVO.getCodigo(), "CA")) {
            Uteis.logar(null,"Não foi possível cancelar o contrato de número " + contratoVO.getCodigo() + ", pois o mesmo já se encontra cancelado.");
            return;
        }
        if(getZWFacade().getMovParcela().existeParcelaContratoEmRemessaGeradaOuAguardando(contratoVO.getCodigo(), null)){
            Uteis.logar(null,"Não foi possível cancelar o contrato de número "
                    + contratoVO.getCodigo() + ", pois existe parcela desse contrato em remessa ainda não processada.");
            return;
        }
        if (!UteisValidacao.emptyNumber(contratoVO.getContratoResponsavelRenovacaoMatricula())) {
            Uteis.logar(null,"Não foi possível cancelar o contrato de número "
                    + contratoVO.getCodigo() + ", pois o mesmo já foi renovado. É necessário estornar o contrato futuro.");
            return;
        }
        cancelarContrato(contratoVO, valorMensal,TipoParcelaCancelamento.obterEnumPorSigla(contratoVO.getEmpresa().getTipoParcelaCancelamento()),true,true, true);
    }

    private void cancelarContrato(ContratoVO contratoVO, Double valorMensal, TipoParcelaCancelamento tipoParcelaCancelamento, boolean cancelarSaldoContaCorrente, boolean cancelarParcelaAnuidade, boolean controleTransacao)throws Exception {
        inicializarCancelamento(contratoVO);
        if (!contratoVO.getRegimeRecorrencia()){
            if (contratoVO.isCancelamentoAntecipado() && !contratoVO.getEmpresa().isCancelamentoAutomaticoAntecipadoContratoForaRecorrencia()) {
                contratoVO.setCancelamentoAntecipadoEmpresa(false);
            }
            contratoVO.setCancelamentoObrigatoriedadePagamentoEmpresa(false);
        } else {
            if (tipoParcelaCancelamento.equals(TipoParcelaCancelamento.TODAS_PARCELAS) &&
                    contratoVO.getEmpresa().isAplicarMultaeJurosNoCancelamentoAutomatico() &&
                    contratoVO.getEmpresa().isCancelamentoAntecipado()) {
                contratoVO.setCancelamentoAntecipadoEmpresa(false);
                contratoVO.setCancelamentoObrigatoriedadePagamentoEmpresa(false);
            }
        }

        boolean existeParcelaEmCobranca = false;
        MovParcela movParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(this.con);
            for (MovParcelaVO movParc : contratoVO.getMovParcelaVOs()) {
                movParcelaDAO.validarMovParcelaComTransacaoConcluidaOuPendente(movParc.getCodigo());
            }
        } catch (Exception e) {
            existeParcelaEmCobranca = true;
            Uteis.logarDebug("CancelamentoContratoAutomaticoService - cancelarContrato: Não foi possível cancelar o contrato " + contratoVO.getCodigo() + ", pois o mesmo tem parcela em Transação pendente.");
        } finally {
            movParcelaDAO = null;
        }

        if (!existeParcelaEmCobranca) {
            if (contratoVO.getContratoRecorrenciaVO().isCancelamentoProporcional()) {
                getZWFacade().calcularCancelamentoProporcional(getCancelamento(), contratoVO, Calendario.hoje(), true);
                gravarCancelamento(contratoVO, controleTransacao);
            } else if (contratoVO.isCancelamentoAvaliandoParcelas()) {
                getZWFacade().calcularCancelamentoAvaliandoParcelas(getCancelamento(), contratoVO, Calendario.hoje(), true);
                gravarCancelamento(contratoVO, controleTransacao);
            } else if (contratoVO.isCancelamentoAntecipado() && contratoVO.getRegimeRecorrencia()
                    || (contratoVO.isCancelamentoAntecipado() && contratoVO.getEmpresa().isCancelamentoAutomaticoAntecipadoContratoForaRecorrencia() && getCancelamento().getCancelamentoAutomatico())) {
                getZWFacade().calcularCancelamentoAntecipado(getCancelamento(), contratoVO, false, false);
                gravarCancelamento(contratoVO, controleTransacao);
            } else {
                getCancelamento().setCancelarSaldoContaCorrente(cancelarSaldoContaCorrente
                        && !(tipoParcelaCancelamento.equals(TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL) || tipoParcelaCancelamento.equals(TipoParcelaCancelamento.MAIOR_IGUAL)));
                getCancelamento().setCancelarParcelaAnuidade(cancelarParcelaAnuidade);
                getCancelamento().setTipoParcelaCancelamento(tipoParcelaCancelamento);
                calcularValoresDoCancelamento(contratoVO, valorMensal);

                listarPagamento(contratoVO, tipoParcelaCancelamento);
                gravarCancelamento(contratoVO, controleTransacao);
                if (!(tipoParcelaCancelamento.equals(TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL)) && !contratoVO.getEmpresa().isGerarQuitacaoCancelamentoAuto()){
                    movimentarContaCliente(contratoVO);
                }
            }
        }
    }

    public void cancelarAutomatico(ContratoVO contratoVO, TipoParcelaCancelamento tipoParcelaCancelamento, boolean cancelarSaldoContaCorrente, boolean cancelarParcelaAnuidade, boolean controleTransacao) throws Exception {
        //o cancelamento de contrato não deve ter commit, pois o controle de transação é feito pelo processo externo.
        cancelarContrato(contratoVO, contratoVO.obterValorContratoReferenteMensal(), tipoParcelaCancelamento,cancelarSaldoContaCorrente,cancelarParcelaAnuidade, controleTransacao);
    }

    /**
     * Finaliza as operações de cancelamento, salvando em banco
     *
     * <AUTHOR> 12/07/2011
     */
    public void gravarCancelamento(ContratoVO contratoVO, boolean controleTransacao) throws Exception {
        if (!contratoVO.isCancelamentoAntecipado()) {
            if (getCancelamento().getValorSerRecebidoPeloCliente() > 0) {
                throw new ConsistirException("O sistema não cancelou o contrato de número "
                        + contratoVO.getCodigo() + ", pois apesar de ter uma parcela vencida que satisfaz a regra do cancelamento automático, o cálculo do cancelamento iria gerar uma devolução ao aluno, sugerindo que o aluno tenha pago mais do que utiizado. Um dos motivos dessa situação pode ser crédito na conta corrente do aluno. Verifique o cadastro do aluno e regularize essa situação ");
            } else {
                if (getCancelamento().getValorQuitacaoCancelamento() > 0 && contratoVO.getEmpresa().isGerarQuitacaoCancelamentoAuto()){
                    getCancelamento().setQuitacaoCancelamento(true);
                    ProdutoVO prodQuitacao = getZWFacade().getProduto().consultarPorTipoProduto("QU", true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    getCancelamento().gerarMovProdutoQuitacaoCancelamento(contratoVO, contratoVO.getEmpresa(), getCancelamento().getResponsavelCancelamento(), prodQuitacao);
                }
            }
        }
        getZWFacade().incluirOperacaoCancelamentoContratoDevolucao(getCancelamento(), contratoVO, getCancelamento().getResponsavelCancelamento(), null, new ArrayList<>(), null, controleTransacao);
        gerarLog(contratoVO, controleTransacao);
    }

    private void gerarLog(ContratoVO contratoVO, boolean controleTransacao) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("CONTRATO");
        log.setNomeEntidadeDescricao("Contrato Recorrente");
        log.setChavePrimaria(contratoVO.getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(getCancelamento().getResponsavelCancelamento().getNome());
        log.setPessoa(contratoVO.getPessoa().getCodigo());
        log.setOperacao("CANCELAMENTO RECORRENTE");
        Log logDAO = new Log(con);
        if (controleTransacao){
            logDAO.incluir(log);
        }else{
            logDAO.incluirSemCommit(log);
        }

    }

    /**
     * Preenche dados iniciais do cancelamento (justificativa, observacao, valor
     * de produtos, dias do contrato, dias utilizados, os dias que restam do
     * contrato e responsavel pelo cancelamento)
     *
     * <AUTHOR> 12/07/2011
     */
    public void inicializarCancelamento(ContratoVO contratoVO) throws Exception, Exception {
        setCancelamento(new CancelamentoContratoVO());
        contratoVO.setEmpresa(getZWFacade().getEmpresa().consultarPorChavePrimaria(contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        getCancelamento().getListaContratos().add(contratoVO);
        getCancelamento().setTipoJustificativaOperacao(getZWFacade().getJustificativa(contratoVO.getEmpresa()).getCodigo());
        String observacao = "CANCELAMENTO AUTOMATICO";
        if (!UteisValidacao.emptyString(contratoVO.getDescricaoRegraCancelamento())) {
            observacao += contratoVO.getDescricaoRegraCancelamento();
        }
        getCancelamento().setObservacao(observacao);
        getCancelamento().setValorTotalSomaProdutoContratos(contratoVO.getSomaProduto());
        getCancelamento().setNrDiasContrato(getZWFacade().obterNrDiasContrato(contratoVO));
        getCancelamento().setNrDiasUtilizadosPeloClienteContrato(getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(contratoVO, getCancelamento().getDataCancelamento()));
        getCancelamento().setNrDiasRestamContrato(contratoVO.obterNrDiasRestantesProFinalDoContrato(getCancelamento().getNrDiasContrato(), getCancelamento().getNrDiasUtilizadosPeloClienteContrato()));
        if (getCancelamento().getConfiguracaoSesc()) {
            getCancelamento().setNrDiasUtilizadosPeloClienteContrato(getCancelamento().getNrDiasUtilizadosPeloClienteContrato() - 1);
            getCancelamento().setNrDiasRestamContrato(getCancelamento().getNrDiasRestamContrato() + 1);
        }
        getCancelamento().setResponsavelCancelamento(getZWFacade().getUsuarioRecorrencia());
        getCancelamento().setContratoCancelar(contratoVO);
        getCancelamento().setCancelamentoAutomatico(true);
        contratoVO.setContratoRecorrenciaVO(getZWFacade().getContratoRecorrencia().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    /**
     * Adiciona a conta corrente do cliente na academia o valor resultante do
     * cancelamento (pode ser positivo ou negativo)
     *
     * <AUTHOR> 12/07/2011
     */
    public void movimentarContaCliente(ContratoVO contratoVO) throws Exception {
        MovimentoContaCorrenteClienteVO obj = new MovimentoContaCorrenteClienteVO();
        //setar valores comuns
        obj.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        double valorDevolvidoComMulta = getCancelamento().getValorSerRecebidoPeloCliente();
        obj.setValor(valorDevolvidoComMulta > 0 ? valorDevolvidoComMulta : (valorDevolvidoComMulta * -1));
        obj.setPessoa(contratoVO.getPessoa());
        obj.setResponsavelAutorizacao(getCancelamento().getResponsavelCancelamento());
        //consultar a conta do cliente
        MovimentoContaCorrenteClienteVO clienteCreditado = getZWFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (clienteCreditado != null) {
            obj.setSaldoAtual(clienteCreditado.getSaldoAtual() + valorDevolvidoComMulta);
        } else {
            obj.setSaldoAtual(valorDevolvidoComMulta);
        }

        if (valorDevolvidoComMulta > 0) {
            obj.setDescricao("Deposito do valor do Cancelamento");
            obj.setTipoMovimentacao("CR");
        } else {
            obj.setDescricao("Debito do valor do Cancelamento");
            obj.setTipoMovimentacao("DE");
        }


        //inserir a movimentação realizada na conta do cliente
        getZWFacade().getMovimentoContaCorrenteCliente().incluirSemCommit(obj, false);
    }

    /**
     * Responsável por verificar a necessidade da cobrança de multa e se preciso
     * obter o valor
     *
     * <AUTHOR> 12/07/2011
     */
    private Double percentualMulta(ContratoVO contratoVO) {
        Double multa = 0.0;
        if (!contratoVO.getSituacaoContrato().equals(RENOVACAO)) {
            multa = contratoVO.getPlano().getPercentualMultaCancelamento();
        }
        return multa;
    }

    /**
     * Calcular valores do cancelamento : valor mensal do contrato, valor por
     * dia, valor utilizado pelo cliente, valor de credito que o cliente ainda
     * tem, valor da multa,
     *
     * <AUTHOR> 12/07/2011
     */
    public void calcularValoresDoCancelamento(ContratoVO contratoVO, Double valorMensal) throws Exception {
        getCancelamento().setValorMensalContrato(valorMensal);
        getCancelamento().setValorBaseContrato(contratoVO.getValorBaseCalculo());
        getCancelamento().setValorDiaContratoValorBase(getZWFacade().obterValorDiaContratoValorBase(contratoVO));
        getCancelamento().setValorUtilizadoPeloClienteBase(Uteis.arredondarForcando2CasasDecimais(getCancelamento().getNrDiasUtilizadosPeloClienteContrato() * getCancelamento().getValorDiaContratoValorBase()));

        getCancelamento().setValorCreditoRestanteContratoValorBase(
                contratoVO.obterCreditoRestanteContrato(
                        getCancelamento().getNrDiasUtilizadosPeloClienteContrato(),
                        getCancelamento().getValorDiaContratoValorBase(), false));

        if (getCancelamento().getListaContratos().size() != 1) {
            getCancelamento().setValorCreditoRestanteContratoValorBase(getCancelamento().getValorCreditoRestanteContratoValorBase() + getCancelamento().getValorTotalBaseContratoRenovacao());
        }


        getCancelamento().setValorUtilizadoPeloClienteMensal(Uteis.arredondarForcando2CasasDecimais(getCancelamento().getNrDiasUtilizadosPeloClienteContrato() * getCancelamento().getValorDiaContratoValorBase()));
        getCancelamento().setValorCreditoRestanteContratoValorMensalSemTaxaEMulta(
                contratoVO.obterCreditoRestanteContrato(
                        getCancelamento().getNrDiasUtilizadosPeloClienteContrato(),
                        getCancelamento().getValorDiaContratoValorBase(), false));

        if (getCancelamento().getNrDiasUtilizadosPeloClienteContrato() > 7 || !contratoVO.getEmpresa().isIsentarCancelamento7Dias()) {
            //valor multa
            getCancelamento().setPercentualMultaCancelamento(percentualMulta(contratoVO));
            ProdutoVO objProduto = getZWFacade().getProduto().consultarPorChavePrimaria(contratoVO.getPlano().getProdutoTaxaCancelamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getCancelamento().setValorTaxaCancelamento(objProduto.getValorFinal());
            double valorFinalTemp = getCancelamento().getValorCreditoRestanteContratoValorMensalSemTaxaEMulta();
            getCancelamento().setValorCreditoRestanteContratoValorMensal(Uteis.arredondarForcando2CasasDecimais(valorFinalTemp));
        } else {
            getCancelamento().setValorTaxaCancelamento(0.0);
            getCancelamento().setPercentualMultaCancelamento(0.0);
        }
    }

    /**
     * Preenche os atributos do cancelamento que se referem a pagamento do
     * cliente e quanto deverá ser devolvido ao mesmo : saldo conta corrente,
     * valor pago pelo cliente, valor a ser devolvido para o cliente
     *
     * <AUTHOR> 12/07/2011
     */
    private void listarPagamento(ContratoVO contratoVO, TipoParcelaCancelamento tipoParcelaCancelamento) throws Exception {
        //limpar os valores antes de preenche-los
        getCancelamento().setValorASerDevolvido(0.0);
        getCancelamento().setValorTotalPagoPeloCliente(0.0);
        getCancelamento().setSaldoContaCorrenteCliente(0.0);
        //obter saldo conta corrente cliente
        getZWFacade().obterSaldoContaCorrenteCliente(getCancelamento(), contratoVO);
        getCancelamento().setValorTotalPagoPeloCliente(getZWFacade().obterValorPagoPeloCliente(getCancelamento(), contratoVO));
        getCancelamento().setSomaValorPagoPeloClienteComCheque(getCancelamento().getValorTotalPagoPeloCliente());
        getCancelamento().obterValorFinalASerDevolvidoComCheque(); // inclui conta corrente  no calculo
        getCancelamento().setValorSerRecebidoPeloCliente(getCancelamento().getValorASerDevolvido());
        if (tipoParcelaCancelamento.equals(TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL)){//ignora valores e não movimenta a conta corrente ou gera quitação
            getCancelamento().setValorSerRecebidoPeloCliente(0.0);
        } else if(contratoVO.getEmpresa().isGerarQuitacaoCancelamentoAuto()) {
            getCancelamento().setValorQuitacaoCancelamento(getCancelamento().getValorSerRecebidoPeloCliente() * -1);
        }
    }

    /**
     * Método necessário para corrigir parcelas de contratos antigas que ficaram
     * 'Em Aberto' depois que este foi renovado e mais tarde cancelado
     * automaticamente. O Processo colocará as parcelas como "Cancelada" e
     * insere o saldo devedor na Conta Corrente do Aluno
     *
     * <AUTHOR> Maciel
     * @date 05/11/2015
     */
    public void cancelarParcelasAntigasDeContratosRenovadosCancelados() throws Exception {
        UsuarioVO usuarioRecor = getZWFacade().getUsuarioRecorrencia();
        StringBuilder sqlHeader = new StringBuilder();
        StringBuilder sqlBody = new StringBuilder();
        StringBuilder sqlBottom = new StringBuilder();
        sqlHeader.append("select mp.pessoa, mp.contrato, sum(mpr.valorpago) as total ");
        sqlBody.append("from movparcela mp \n");
        sqlBody.append("inner join movprodutoparcela mpr on mpr.movparcela = mp.codigo \n");
        sqlBody.append("inner join movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sqlBody.append("inner join produto p on p.codigo = mprod.produto \n");
        sqlBody.append("inner join pessoa pes on pes.codigo = mp.pessoa \n");
        sqlBody.append("inner join cliente cli on cli.pessoa = pes.codigo \n");
        sqlBody.append("inner join contratorecorrencia cr on cr.contrato = mp.contrato \n");
        sqlBody.append("inner join contrato cont on cont.codigo =  mp.contrato \n");
        sqlBody.append("left join empresa emp ON emp.codigo = cont.empresa\n");
        sqlBody.append("where cr.diascancelamentoautomatico > 0 and cr.datainutilizada is null \n");
        sqlBody.append("and p.tipoproduto in ('PM') \n");
        sqlBody.append("and mp.situacao = 'EA' \n");
        sqlBody.append("and mp.valorParcela > 0 \n");
        sqlBody.append("and mp.codigo not in (select coalesce(movparcela,0) from remessaitem ri inner join remessa re on ri.remessa = re.codigo where re.situacaoremessa in (1,4)) \n");
        sqlBody.append("and (mp.datavencimento < coalesce((select max(co.datainicioefetivacaooperacao) from contrato c inner join contratooperacao co on co.contrato = c.codigo where co.tipooperacao = 'CA'  and c.contratobaseadorenovacao  = cont.codigo and responsavel =").append(usuarioRecor.getCodigo()+" ) , mp.datavencimento)) \n");
        sqlBody.append("and emp.tipoParcelaCancelamento = '").append(TipoParcelaCancelamento.TODAS_PARCELAS.getSigla()).append("'\n");
        sqlBottom.append("group by mp.pessoa,mp.contrato \n");
        sqlBottom.append("order by mp.pessoa,mp.contrato \n");

        StringBuilder agrupado = new StringBuilder(sqlHeader).append(sqlBody).append(sqlBottom);
        PreparedStatement ps = SuperFacadeJDBC.criarQuery(agrupado.toString(), con);

        ResultSet rs = ps.executeQuery();
        try {
            con.setAutoCommit(false);
            while (rs.next()) {
                Integer codPessoa = rs.getInt("pessoa");
                Double total = rs.getDouble("total");
                Integer contrato = rs.getInt("contrato");
                Uteis.logar(null, String.format("Cancelar parcelas da pessoa %s do contrato %s no total de %2.2f",
                        codPessoa, contrato, total));
                
                StringBuilder updateMovProduto = new StringBuilder();
                updateMovProduto.append("update movproduto set situacao = 'CA' \n");
                updateMovProduto.append("where situacao = 'EA' and codigo in \n");
                updateMovProduto.append("   (select movproduto from movprodutoparcela where movparcela in (");
                updateMovProduto.append("select mp.codigo ").append(sqlBody).append(")) ");
                updateMovProduto.append("and pessoa = ").append(codPessoa);
                SuperFacadeJDBC.executarConsulta(updateMovProduto.toString(), con);
                
                StringBuilder updateMovParcela = new StringBuilder();
                updateMovParcela.append("update movparcela set situacao = 'CA' where codigo in (");
                updateMovParcela.append(" select mp.codigo ");
                updateMovParcela.append(sqlBody);
                updateMovParcela.append(")");
                updateMovParcela.append(" and pessoa = ").append(codPessoa);
                PreparedStatement psUpdate = SuperFacadeJDBC.criarQuery(updateMovParcela.toString(), con);
                psUpdate.execute();

                MovimentoContaCorrenteClienteVO obj = new MovimentoContaCorrenteClienteVO();
                //setar valores comuns
                obj.setDataRegistro(Calendario.hoje());
                obj.setValor(total * -1);
                PessoaVO p = new PessoaVO();
                p.setCodigo(codPessoa);
                obj.setPessoa(p);
                obj.setResponsavelAutorizacao(usuarioRecor);
                //
                //lançar na conta corrente o saldo devedor
                MovimentoContaCorrenteClienteVO clienteCreditado = getZWFacade().
                        getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(
                        codPessoa,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (clienteCreditado != null) {
                    obj.setSaldoAtual(clienteCreditado.getSaldoAtual() + obj.getValor());
                } else {
                    obj.setSaldoAtual(obj.getValor());
                }
                obj.setDescricao("Debito do valor das parcelas em aberto do Contrato " + contrato);
                obj.setTipoMovimentacao("DE");
                //
                getZWFacade().getMovimentoContaCorrenteCliente().incluirSemCommit(obj, false);
                Uteis.logar(null, String.format("Canceladas com sucesso parcelas da pessoa %s do contrato %s no total de %2.2f",
                        codPessoa, contrato, total));

                getZWFacade().getClienteMensagem().excluirClienteMensagemPorMovParcelaContrato(contrato);
            }
            con.commit();
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao Cancelar parcelas em aberto de renovações canceladas: " + e.getMessage());
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }

    }


    /**
     * @param cancelamento the cancelamento to set
     */
    public void setCancelamento(CancelamentoContratoVO cancelamento) {
        this.cancelamento = cancelamento;
    }

    /**
     * @return the cancelamento
     */
    public CancelamentoContratoVO getCancelamento() {
        return cancelamento;
    }

    /**
     * @return the zwFacade
     * @throws Exception
     */
    public ZillyonWebFacade getZWFacade() throws Exception {
        if (zwFacade == null) {
            zwFacade = new ZillyonWebFacade(this.con);
        }
        return zwFacade;
    }
    
    public static void main(String... args) throws Exception {
        String chave = "bdzillyonselfitoficial-2021-12-17";
        Connection con1 = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(con1);
        CancelamentoContratoAutomaticoService cas = new CancelamentoContratoAutomaticoService(con1);
        ContratoRecorrenciaVO conRec = new ContratoRecorrencia(con1).consultarPorContrato(4247, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con1);
        conRec.getContrato().setEmpresa(new Empresa(con1).consultarPorChavePrimaria(conRec.getContrato().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
//        conRec.getContrato().getEmpresa().setTipoParcelaCancelamento(TipoParcelaCancelamento.TODAS_PARCELAS.getSigla());
        cas.cancelarAutomatico(conRec);
    }

    public boolean contratoPossuiParcelasParaCancelamentoAutomatico(ContratoVO contratoVO) throws SQLException {
        carregarDadosEmpresa(contratoVO);
        final int numeroSequenciaVencidas = contratoVO.getEmpresa().getQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia();
        return !UteisValidacao.emptyNumber(numeroSequenciaVencidas) && verificarParcelaVencidasSeguidas(contratoVO);
    }

    public boolean contratoPossuiParcelaVencidaAposQtdDiasUteisTolerado(ContratoVO contratoVO, Date diaAtual) throws Exception {
        final int qtdDiasUteisAposVencimentoParaCancelar = contratoVO.getEmpresa().getQuantidadeDiasUteisAposVencimentoParaCancelarContrato();
        return !UteisValidacao.emptyNumber(qtdDiasUteisAposVencimentoParaCancelar) && verificarParcelasVencidasDiasUteisTolerado(contratoVO, diaAtual);
    }

    private void carregarDadosEmpresa(ContratoVO contratoVO) throws SQLException {
        String sqlStr = "select quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia,tipoParcelaCancelamentoForaRegimeRecorrencia from empresa where codigo = " + contratoVO.getEmpresa().getCodigo();
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlStr);
        if (rs.next()) {
            contratoVO.getEmpresa().setQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia(rs.getInt("quantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia"));
            contratoVO.getEmpresa().setTipoParcelaCancelamentoForaRegimeRecorrencia(rs.getString("tipoParcelaCancelamentoForaRegimeRecorrencia"));
        }
    }

    private boolean verificarParcelaVencidasSeguidas(ContratoVO contratoVO) throws SQLException {
        int numeroSequenciaVencidas = contratoVO.getEmpresa().getQuantidadeParcelasSeguidasCancelamentoForaRegimeRecorrencia();

        Date dataVencimento = Calendario.hoje();
        StringBuilder sqlS = new StringBuilder();
        sqlS.append("SELECT DISTINCT(mpar.codigo), mpar.* FROM movparcela mpar\n");
        sqlS.append("LEFT JOIN movprodutoparcela mpp ON mpar.codigo = mpp.movparcela\n");
        sqlS.append("LEFT JOIN pagamentomovparcela pmp ON mpar.codigo = pmp.movparcela\n");
        sqlS.append("LEFT JOIN movproduto mprod ON mpp.movproduto = mprod.codigo\n");
        sqlS.append("LEFT JOIN produto prod ON prod.codigo = mprod.produto\n");
        sqlS.append("WHERE mpar.contrato =").append(contratoVO.getCodigo()).append("\n");
        sqlS.append("AND mpar.situacao = 'EA'\n");
        sqlS.append("AND mpar.valorparcela > 0\n");
        sqlS.append("AND mpar.dataVencimento < '").append(Uteis.getDataFormatoBD(dataVencimento)).append("'\n");
        sqlS.append("AND pmp.codigo is null\n");
        sqlS.append("AND prod.tipoproduto IN ('").append(TipoProduto.MES_REFERENCIA_PLANO.getCodigo()).append("','").append(TipoProduto.TAXA_RENEGOCIACAO.getCodigo()).append("')\n");
        sqlS.append("order by datavencimento asc;");

        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlS.toString());
        List<MovParcelaTO> parcelas = new ArrayList<>();
        while (rs.next()) {
            MovParcelaTO movParcelaTO = new MovParcelaTO();
            movParcelaTO.setDataVencimento(rs.getDate("dataVencimento"));
            movParcelaTO.setCodigo(rs.getInt("codigo"));
            parcelas.add(movParcelaTO);
        }
        if (parcelas.size() < numeroSequenciaVencidas) {
            return false;
        }

        Ordenacao.ordenarLista(parcelas, "dataVencimento");

        boolean sequencial = false;
        int count = 0;
        Date parcelaAnterior = null;
        Integer codigoAnterior = null;
        for (MovParcelaTO movParcelaTO : parcelas) {
            String mesAnteriorParcelaAtual = Uteis.getDataMesAnoConcatenado(Uteis.somarMeses(movParcelaTO.getDataVencimento(), -1));
            if (parcelaAnterior != null && (mesAnteriorParcelaAtual.equals(Uteis.getDataMesAnoConcatenado(parcelaAnterior)))
                    || (codigoAnterior != null && (movParcelaTO.getCodigo() - 1 == codigoAnterior))) {
                count++;
                sequencial = true;
            } else {
                count = 0;
                sequencial = false;
            }
            parcelaAnterior = movParcelaTO.getDataVencimento();
            codigoAnterior = movParcelaTO.getCodigo();
        }
        return (count > 0 ? count + 1 : 0) > numeroSequenciaVencidas && sequencial;
    }

    private String truncateDate(Date diaAtual) {
        final Instant instant = diaAtual.toInstant();
        final ZoneId zoneId = ZoneId.systemDefault();
        final LocalDate localDate = instant.atZone(zoneId).toLocalDate();

        return localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    private boolean verificarParcelasVencidasDiasUteisTolerado(ContratoVO contratoVO, Date diaAtual) throws Exception {
        final String dataFormatada = truncateDate(diaAtual);

        final StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.codigo as codigo, mp.datavencimento as dataVencimento from movparcela mp\n");
        sql.append("INNER JOIN contrato c on c.codigo = mp.contrato\n");
        sql.append("LEFT JOIN remessaitem ri on ri.movparcela = mp.codigo\n");
        sql.append("LEFT JOIN remessa re on re.codigo = ri.remessa\n");
        sql.append("LEFT JOIN contratorecorrencia cr on cr.contrato = mp.contrato\n");
        sql.append("WHERE mp.situacao = 'EA'\n");
        sql.append("AND c.situacao = 'AT'\n");
        sql.append("AND mp.datavencimento < '").append(dataFormatada).append("'\n");
        sql.append("AND c.codigo = ").append(contratoVO.getCodigo()).append("\n");
        sql.append("AND (((select max(codigo) from remessaitem where movparcela = mp.codigo) = ri.codigo)\n");
        sql.append("OR (select max(codigo) from remessaitem where movparcela = mp.codigo) IS NULL)\n");
        sql.append("AND (re.situacaoremessa IS NULL or re.situacaoremessa = 2)\n");
        sql.append("AND cr.contrato IS null;");

        final Statement stm = con.createStatement();
        final ResultSet rs = stm.executeQuery(sql.toString());
        final List<MovParcelaTO> parcelas = new ArrayList<>();
        while (rs.next()) {
            final MovParcelaTO movParcelaTO = new MovParcelaTO();
            movParcelaTO.setCodigo(rs.getInt("codigo"));
            movParcelaTO.setDataVencimento(rs.getDate("dataVencimento"));
            parcelas.add(movParcelaTO);
        }

        return parcelas.stream()
                .anyMatch(parcela -> verificaSeVencimentoParcelaMaiorDiasTolerados(parcela, diaAtual, contratoVO));
    }

    private boolean verificaSeVencimentoParcelaMaiorDiasTolerados(MovParcelaTO parcela, Date diaAtual, ContratoVO contratoVO) {
        final int qtdDiasUteisTolerado = contratoVO.getEmpresa().getQuantidadeDiasUteisAposVencimentoParaCancelarContrato();

        try {
            int qtdDiasUteisVencido = diferencaEmDiasUteis(parcela.getDataVencimento(), diaAtual, contratoVO.getEmpresa());
            return qtdDiasUteisVencido > qtdDiasUteisTolerado;
        } catch (Exception e) {
            throw new RuntimeException("Erro ao calcular diferença de dias úteis entre data vencimento da parcela e dias tolerados.", e);
        }
    }

    private int diferencaEmDiasUteis(final Date dataInicial, final Date dataFinal, EmpresaVO empresa) throws Exception {
        if (dataInicial == null || dataFinal == null)
            return 0;

        final Calendar cal = Calendar.getInstance();
        cal.setTime(dataInicial);

        int diasUteis = 0;
        Feriado feriadoDAO = new Feriado(this.con);

        while (cal.getTime().before(dataFinal)) {
            if (Calendario.isDiaUtil(cal.getTime()) && !feriadoDAO.validarFeriadoPorEmpresaParaCalculoAberturaMeta(empresa, cal.getTime())) {
                diasUteis++;
            }

            cal.add(Calendar.DAY_OF_MONTH, 1);
        }

        return diasUteis;
    }
}
