package servicos.relatorio;


import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;

import static org.apache.commons.lang3.StringUtils.isNotBlank;


public class InadimplenciaService {

    private Connection con;

    public InadimplenciaService(Connection con) throws Exception {
        this.con = con;
    }

    public JSONArray consultarParcelas(Integer empresa, Date dataInicioVencimento, Date dataTerminoVencimento,
                                        boolean gerarMultaJuros, String situacaoCliente,
                                        boolean apresentarDadosSensiveis, Integer colaborador,
                                        boolean ignorarParcelasSemRetorno, boolean canceladaAposVencimento,
                                        boolean canceladaAntesVencimento, String situacoesParcela) {
        Empresa empresaDAO;
        Colaborador colaboradorDAO;
        try {
            empresaDAO = new Empresa(con);
            EmpresaVO emp = empresaDAO.consultarPorCodigo(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Integer codColaborador = 0;
            Integer codPessoa = 0;
            if (!UteisValidacao.emptyNumber(colaborador)) {
                colaboradorDAO = new Colaborador(con);
                ColaboradorVO colab = colaboradorDAO.consultarPorCodigo(colaborador, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                codColaborador = colab.getCodigo();
                codPessoa = colab.getPessoa().getCodigo();
            }

            JSONArray parcelasAberto = consultarTodosParcelasEmAberto(empresa, dataInicioVencimento, dataTerminoVencimento,
                    situacaoCliente, apresentarDadosSensiveis, codColaborador, ignorarParcelasSemRetorno,
                    codPessoa, canceladaAposVencimento, canceladaAntesVencimento, situacoesParcela);

            if (gerarMultaJuros) {
                for (int i = 0; i < parcelasAberto.length(); i++) {
                    JSONObject e = parcelasAberto.getJSONObject(i);
                    if (e.getString("parcelaSituacao").equals("EA")) {
                        e.put("multas", calcularMulta(emp, e.getDouble("parcelaValorParcela")));
                        e.put("juros", calcularJuros(emp, e.getDouble("parcelaValorParcela"),
                                (Date) e.get("parcelaDatavencimento"), Calendario.hoje()));
                    }
                }
            }
            return parcelasAberto;
        } catch (Exception ex) {
            return new JSONArray();
        } finally {
            empresaDAO = null;
            colaboradorDAO = null;
        }
    }

    public double calcularMulta(EmpresaVO empresa, Double valorParcela) {
        if (empresa.isUtilizarMultaValorAbsoluto()) {
            return Uteis.arredondarForcando2CasasDecimais(empresa.getMultaCobrancaAutomatica());
        } else {
            return Uteis.arredondarForcando2CasasDecimais(valorParcela * (empresa.getMultaCobrancaAutomatica() / 100));
        }
    }

    public double calcularJuros(EmpresaVO empresa, Double valorParcela, Date dataVencimento, Date dataPagamento) {
        if (empresa.isUtilizarJurosValorAbsoluto()) {
            return Uteis.arredondarForcando2CasasDecimais((Uteis.nrDiasEntreDatas(dataVencimento, dataPagamento) * empresa.getJurosCobrancaAutomatica()));
        } else {
            return Uteis.arredondarForcando2CasasDecimais(valorParcela * ((Uteis.nrDiasEntreDatas(dataVencimento, dataPagamento)) * (empresa.getJurosCobrancaAutomatica() / 100)));
        }
    }

    public JSONArray consultarTodosParcelasEmAberto(Integer empresa, Date dataInicioVencimento,
                                                                     Date dataTerminoVencimento, String situacaoCliente,
                                                                     boolean apresentarDadosSensiveis, Integer colaborador,
                                                                     boolean ignorarParcelasSemRetorno, Integer pessoa,
                                                                     boolean canceladaAposVencimento,
                                                                     boolean canceladaAntesVencimento, String situacoesParcela) throws Exception {
        ResultSet rs = executarConsultaParametrizada(empresa, dataInicioVencimento, dataTerminoVencimento,
                situacaoCliente, apresentarDadosSensiveis, colaborador, ignorarParcelasSemRetorno,
                pessoa, canceladaAposVencimento, canceladaAntesVencimento, situacoesParcela);
        rs.beforeFirst();
        JSONArray parcelas = new JSONArray();
        while (rs.next()) {
            JSONObject parcela = new JSONObject();
            parcela.put("clienteMatricula", rs.getString("cliente_matricula"));
            parcela.put("pessoaNome", rs.getString("pessoa_nome"));
            parcela.put("movparcelaCodigo", rs.getInt("movparcela_codigo"));
            parcela.put("parcelaDescricao", rs.getString("parcela_descricao"));
            parcela.put("parcelaDataregistro", rs.getDate("parcela_dataregistro"));
            parcela.put("parcelaDatavencimento", rs.getDate("parcela_datavencimento"));
            parcela.put("dataPagamento", rs.getDate("datapagamento"));
            parcela.put("parcelaSituacao", rs.getString("parcela_situacao"));
            parcela.put("regimeRecorrencia", rs.getString("regime_recorrencia"));
            parcela.put("parcelaContrato", rs.getInt("parcela_contrato"));
            parcela.put("parcelaValorParcela", rs.getDouble("parcela_valorParcela"));
            parcela.put("email", rs.getString("email"));
            parcela.put("telefone", rs.getString("telefone"));
            parcela.put("nomeEmpresa", rs.getString("nomeempresa"));
            parcela.put("codigoCliente", rs.getInt("codigoCliente"));
            parcela.put("dataCancelamento", rs.getDate("dataCancelamento"));
            parcela.put("nrTentativas", rs.getInt("nrtentativas"));
            parcela.put("motivoRetorno", rs.getString("motivoretorno"));
            parcela.put("nomePlano", rs.getString("nomeplano"));
            parcela.put("convenios", rs.getString("convenios"));
            parcela.put("formasPagamento", rs.getString("formaspagamento"));
            parcela.put("modalidades", rs.getString("modalidades"));
            if (apresentarDadosSensiveis) {
                parcela.put("cpf", rs.getString("cpf"));
                parcela.put("endereco", rs.getString("endereco"));
            }
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery("select p.fotokey from pessoa p inner join cliente c on c.pessoa = p.codigo where c.codigo = " +
                        parcela.getInt("codigoCliente"))) {
                    if (tabelaResultado.next()) {
                        parcela.put("urlFotoAluno", Uteis.getPaintFotoDaNuvem(tabelaResultado.getString("fotokey")));
                    }
                }
            }
            parcelas.put(parcela);
        }
        return parcelas;
    }

    public ResultSet executarConsultaParametrizada(Integer empresa, Date dataInicioVencimento, Date dataTerminoVencimento,
                                                   String situacaoCliente, boolean apresentarDadosSensiveis, Integer colaborador,
                                                   boolean ignorarParcelasSemRetorno, Integer pessoa,
                                                   boolean canceladaAposVencimento,
                                                   boolean canceladaAntesVencimento, String situacoesParcela) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT distinct on (movparcela.codigo) movparcela.codigo as movparcela_codigo,\n");
        sql.append("oo.dataoperacao as datacancelamento,");
        sql.append("cliente.codigo as codigoCliente, \n");
        sql.append("Case WHEN necp.contrato IS NOT NULL THEN cliev.matricula when ((vendaavulsa.colaborador is not null or controletaxapersonal.codigo is not null) and colaborador.codigo is not null) then 'cl-'||colaborador.codigo ");
        sql.append(" When (cliente.matricula is null or cliente.matricula = '') then  vendaavulsa.nomecomprador || ' sem matrícula' else cliente.matricula end as cliente_matricula, \n");
        sql.append("Case When (movparcela.vendaavulsa > 0 and (pessoa.nome is null or pessoa.nome = '')) THEN vendaavulsa.nomecomprador WHEN necp.contrato IS NOT NULL THEN pesev.nome ELSE pessoa.nome END as pessoa_nome \n");
        sql.append(", movparcela.descricao as parcela_descricao, \n");
        sql.append("movparcela.dataregistro as parcela_dataregistro, \n");
        sql.append("movparcela.datavencimento as parcela_datavencimento, \n");
        sql.append("movparcela.situacao as parcela_situacao, \n");
        sql.append("movparcela.regimerecorrencia as regime_recorrencia, \n");
        sql.append("movparcela.contrato as parcela_contrato, \n");
        sql.append("movparcela.valorparcela as parcela_valorparcela, 1 AS total, \n");
        sql.append("rp.data as datapagamento, \n");
        sql.append("array_to_string(array(SELECT email FROM email WHERE email.pessoa = movparcela.pessoa), ',') AS email,  \n");
        sql.append("array_to_string(array_agg(distinct fp.descricao), ',') AS formaspagamento,  \n");
        sql.append("array_to_string(array_agg(distinct mod.nome), ',') AS modalidades,  \n");
        sql.append("array_to_string(array(SELECT numero FROM telefone WHERE telefone.pessoa = movparcela.pessoa), ',') AS telefone, \n");
        sql.append("emp.nome as nomeEmpresa, \n");
        sql.append("(select nrtentativas from movparcelaresultadocobranca mr where mr.movparcela = movparcela.codigo order by mr.codigo desc limit 1) as nrtentativas, \n");
        sql.append("(select motivoretorno from movparcelaresultadocobranca mr where mr.movparcela = movparcela.codigo order by mr.codigo desc limit 1) as motivoretorno, \n");
        sql.append("array_to_string((select array_agg(descricao) from conveniocobranca cc INNER JOIN autorizacaocobrancacliente acc ON acc.conveniocobranca = cc.codigo AND acc.ativa WHERE acc.cliente = cliente.codigo), ',') as convenios  \n");

        if (isNotBlank(situacaoCliente)) {
            sql.append(", st.situacao as situacao_cliente, st.situacaoContrato as situacao_contrato \n");
        }
        if (apresentarDadosSensiveis) {
            sql.append(", pessoa.cfp as cpf\n");
            sql.append(", (select replace((endereco||', '||numero||', '||complemento||', '||bairro||', '||bairro||', '||cep||', '||coalesce(cid.nome, '')||', '||coalesce(estado.sigla, '')), ', ,', ',') as endereco from endereco inner join pessoa pes on pes.codigo = endereco.pessoa left join cidade cid on cid.codigo = pes.cidade left join estado on estado.codigo = cid.estado where pes.codigo = movparcela.pessoa   limit 1) as endereco\n");
        }
        sql.append(", plano.descricao as nomeplano \n");
        sql.append("FROM movparcela \n");
        sql.append("LEFT JOIN observacaooperacao oo ON oo.movparcela = movparcela.codigo ");
        sql.append("                               AND oo.tipooperacao = 'PC' ");
        sql.append("left join empresa emp on emp.codigo = movparcela.empresa \n");
        sql.append("LEFT JOIN pessoa \n");
        sql.append("ON pessoa.codigo = movparcela.pessoa \n");
        sql.append("LEFT JOIN cliente \n");
        sql.append("ON cliente.pessoa = pessoa.codigo \n");
        sql.append("LEFT JOIN colaborador \n");
        sql.append("ON colaborador.pessoa = pessoa.codigo \n");
        sql.append("LEFT JOIN contrato \n");
        sql.append("ON movparcela.contrato = contrato.codigo ");
        sql.append("LEFT JOIN contratocondicaopagamento ccp on ccp.contrato = contrato.codigo ");
        sql.append("LEFT JOIN condicaopagamento cp on cp.codigo = ccp.condicaopagamento ");
        sql.append("LEFT JOIN contratomodalidade cmod on cmod.contrato = contrato.codigo ");
        sql.append("LEFT JOIN modalidade mod on cmod.modalidade = mod.codigo ");
        sql.append("LEFT JOIN aulaavulsadiaria \n");
        sql.append("ON aulaavulsadiaria.codigo = movparcela.aulaavulsadiaria  ");
        sql.append("LEFT JOIN vendaavulsa \n");
        sql.append("ON  vendaavulsa.codigo = movparcela.vendaavulsa ");
        sql.append("LEFT JOIN controletaxapersonal ");
        sql.append("ON controletaxapersonal.codigo = movparcela.personal ");
        sql.append("LEFT JOIN usuario \n");
        sql.append("ON usuario.codigo = movparcela.responsavel \n");
        if (!UteisValidacao.emptyNumber(colaborador)) {
            sql.append("INNER JOIN colaborador as colResponsavel  ON colResponsavel.codigo = usuario.colaborador ");
        }
        sql.append("LEFT JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = movparcela.codigo \n");
        sql.append("LEFT JOIN pessoa pesev ON pesev.codigo = movparcela.pessoa  \n");
        sql.append("LEFT JOIN cliente cliev ON cliev.pessoa = pesev.codigo \n");
        sql.append("LEFT JOIN movprodutoparcela mpp ON mpp.movparcela = movparcela.codigo \n");
        sql.append("LEFT JOIN recibopagamento rp ON mpp.recibopagamento = rp.codigo \n");
        sql.append("LEFT JOIN pagamentomovparcela pmp ON movparcela.codigo = pmp.movparcela \n");
        sql.append("LEFT JOIN movpagamento mpag ON pmp.movpagamento = mpag.codigo \n");
        sql.append("LEFT JOIN formapagamento fp ON mpag.formapagamento = fp.codigo \n");

        if (isNotBlank(situacaoCliente)) {
            sql.append("LEFT JOIN situacaoclientesinteticodw st ON st.codigocliente = cliev.codigo \n");
        }
        if (ignorarParcelasSemRetorno) {
            sql.append("LEFT JOIN remessaitem ri ON ri.movparcela = movparcela.codigo \n");
            sql.append("LEFT JOIN remessa re ON re.codigo = ri.remessa \n");
        }
        sql.append("left JOIN contratohorario chor ON chor.contrato = movparcela.contrato \n");
        sql.append("LEFT JOIN plano on plano.codigo= contrato.plano \n");
        sql.append(" WHERE  1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and movparcela.empresa = " + empresa + " \n");
        }

        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append(" and colResponsavel.pessoa = ").append(pessoa).append(" \n");
        }

        if (canceladaAposVencimento) {
            sql.append(" and (oo.dataoperacao >= movparcela.datavencimento or movparcela.situacao != 'CA')");
        } else if (canceladaAntesVencimento) {
            sql.append(" and (oo.dataoperacao < movparcela.datavencimento or movparcela.situacao != 'CA')");
        }
        String filtros = "";
        sql.append(montarFiltrosRelatorio(filtros, dataInicioVencimento, dataTerminoVencimento, situacoesParcela));
        sql.append("\n GROUP BY pessoa.cfp, rp.data, nrtentativas, motivoretorno, oo.dataoperacao, movparcela_codigo, cliente.codigo, movparcela.pessoa, cliente_matricula, pessoa_nome, parcela_descricao, parcela_dataregistro, ");
        sql.append("parcela_datavencimento, parcela_situacao, parcela_contrato, parcela_valorparcela, regime_recorrencia, datapagamento,emp.nome, plano.descricao  \n");
        if (isNotBlank(situacaoCliente)) {
            sql.append(", situacao_cliente ,situacao_contrato \n");
        }
        sql.append("ORDER BY movparcela.codigo, pessoa_nome \n");
        PreparedStatement sqlPS = con.prepareStatement(sql.toString(),
                ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultadoTotalizador = sqlPS.executeQuery();
        if (!resultadoTotalizador.next()) {
            throw new ConsistirException("Nenhum Registro Encontrado!");
        }
        return resultadoTotalizador;
    }

    private String montarFiltrosRelatorio(String selectStr, Date dataInicioVencimento, Date dataTerminoVencimento, String situacoesParcela) {
        String filtros = "";
        if (dataInicioVencimento != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.datavencimento >= '" + Uteis.getDataJDBC(dataInicioVencimento) + " 00:00:00' )", false);
        }

        if (dataTerminoVencimento != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.datavencimento <= '" + Uteis.getDataJDBC(dataTerminoVencimento) + " 23:59:59' )", false);
        }

        if (isNotBlank(situacoesParcela)) {
            StringBuilder situacoesAdd = new StringBuilder();
            String[] situacoes = situacoesParcela.split(";");
            for (String s : situacoes) {
                // Validar cada situação para prevenir SQL injection
                if (!Uteis.isValidStringValue(s)) {
                    throw new SecurityException("Situação da parcela contém caracteres inválidos: " + s);
                }
                if (!situacoesAdd.toString().isEmpty()) {
                    situacoesAdd.append(",");
                }
                // Escapar aspas simples para prevenir SQL injection
                String situacaoEscapada = s.replace("'", "''");
                situacoesAdd.append("'").append(situacaoEscapada).append("'");
            }
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.situacao IN (" + situacoesAdd.toString() + "))", false);
        }

        filtros += adicionarCondicionalWhere(filtros, " and movparcela.situacao != 'RG'", false);

        selectStr += filtros;

        return selectStr;
    }

    public String adicionarCondicionalWhere(String whereStr, String filtro, boolean operadorAND) {
        if (!operadorAND) {
            return filtro;
        } else {
            return whereStr + " AND " + filtro;
        }
    }

}
