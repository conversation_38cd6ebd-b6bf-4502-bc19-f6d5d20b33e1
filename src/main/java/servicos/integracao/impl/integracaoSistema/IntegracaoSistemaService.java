package servicos.integracao.impl.integracaoSistema;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoAcessoDTO;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoBvDTO;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoClienteDTO;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoContratoDTO;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoDTO;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoPagamentoDTO;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoProdutoDTO;
import br.com.pactosolucoes.integracao.CadSis.IntegracaoVendaAvulsaDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IntegracaoSistemaService extends SuperServico {

    private static final String UTF_8 = "UTF-8";
    private String key;
    private String urlWebhook = "";

    private static final String OPERACAO_CADASTRO = "CADASTRO";
    private static final String OPERACAO_PAGAMENTO = "PAGAMENTO";
    private static final String OPERACAO_CANCELAMENTO = "CANCELAMENTO";
    private static final String OPERACAO_CONTRATO = "CONTRATO";
    private static final String OPERACAO_VENDA_AVULSA = "VENDA_AVULSA";
    private static final String BOLETIM_VISITA = "BOLETIM_VISITA";
    private static final String OPERACAO_ACESSO = "ACESSO";

    public IntegracaoSistemaService(Connection con) throws Exception {
        super(con);
        this.key = DAO.resolveKeyFromConnection(con);
        if (UteisValidacao.emptyString(this.key)) {
            this.key = (String) JSFUtilities.getFromSession("key");
        }
    }

    public void notificarCadastro(ClienteVO clienteVO, EmpresaVO empresaVO, UsuarioVO usuarioResponsavel) {
        try {
            IntegracaoDTO dto = new IntegracaoDTO(this.key, OPERACAO_CADASTRO, empresaVO, usuarioResponsavel);
            dto.setCliente(new IntegracaoClienteDTO(clienteVO));
            enviarDadosAssincrono(empresaVO, dto);
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'notificarCadastro' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void notificarPagamento(ClienteVO clienteVO, ReciboPagamentoVO reciboPagamentoVO,
                                   EmpresaVO empresaVO, UsuarioVO usuarioResponsavel) {
        try {
            IntegracaoDTO dto = new IntegracaoDTO(this.key, OPERACAO_PAGAMENTO, empresaVO, usuarioResponsavel);
            dto.setCliente(new IntegracaoClienteDTO(clienteVO));
            dto.setPagamento(new IntegracaoPagamentoDTO(reciboPagamentoVO));
            enviarDadosAssincrono(empresaVO, dto);
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'notificarPagamento' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void notificarContrato(ClienteVO clienteVO, ContratoVO contratoVO, boolean cancelamento,
                                  EmpresaVO empresaVO, UsuarioVO usuarioResponsavel) {
        try {
            IntegracaoDTO dto = new IntegracaoDTO(this.key, cancelamento ? OPERACAO_CANCELAMENTO : OPERACAO_CONTRATO, empresaVO, usuarioResponsavel);
            dto.setCliente(new IntegracaoClienteDTO(clienteVO));
            dto.setContrato(new IntegracaoContratoDTO(contratoVO));
            enviarDadosAssincrono(empresaVO, dto);
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'notificarContrato' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void notificarVendaAvulsa(ClienteVO clienteVO, VendaAvulsaVO vendaAvulsaVO,
                                     EmpresaVO empresaVO, UsuarioVO usuarioResponsavel) {
        try {
            IntegracaoDTO dto = new IntegracaoDTO(this.key, OPERACAO_VENDA_AVULSA, empresaVO, usuarioResponsavel);
            if (clienteVO != null) {
                dto.setCliente(new IntegracaoClienteDTO(clienteVO));
            } else {
                dto.setCliente(new IntegracaoClienteDTO(vendaAvulsaVO));
            }
            dto.setVendaAvulsa(new IntegracaoVendaAvulsaDTO(vendaAvulsaVO));
            enviarDadosAssincrono(empresaVO, dto);
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'notificarVendaAvulsa' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void notificarBV(ClienteVO clienteVO, QuestionarioClienteVO questionarioClienteVO,
                                     EmpresaVO empresaVO, UsuarioVO usuarioResponsavel) {
        try {
            IntegracaoDTO dto = new IntegracaoDTO(this.key, BOLETIM_VISITA, empresaVO, usuarioResponsavel);
            if (clienteVO != null) {
                dto.setCliente(new IntegracaoClienteDTO(clienteVO));
            }
            dto.setIntegracaoBv(new IntegracaoBvDTO(questionarioClienteVO));
            enviarDadosAssincrono(empresaVO, dto);
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'notificarBV' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void notificarAcesso(AcessoClienteVO acessoClienteVO) {
        try {
            if (acessoClienteVO == null ||
                    UteisValidacao.emptyNumber(acessoClienteVO.getCodigo())) {
                return;
            }
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("e.codigo as empresa_codigo, \n");
            sql.append("e.nome as empresa_nome, \n");
            sql.append("e.cnpj as empresa_cnpj, \n");
            sql.append("e.idExterno as empresa_idExterno, \n");
            sql.append("u.codigo as usuario_codigo, \n");
            sql.append("u.username as usuario_username, \n");
            sql.append("u.nome as usuario_nome, \n");
            sql.append("a.situacao, \n");
            sql.append("a.sentido, \n");
            sql.append("a.dthrentrada as entrada, \n");
            sql.append("a.dthrsaida as saida, \n");
            sql.append("co.descricao as coletor_descricao, \n");
            sql.append("a.meioIdentificacaoEntrada, \n");
            sql.append("a.meioIdentificacaoSaida, \n");
            sql.append("cl.codigo as cliente_codigo, \n");
            sql.append("cl.matricula as cliente_matricula, \n");
            sql.append("cl.situacao as cliente_situacao, \n");
            sql.append("p.codigo as pessoa_codigo, \n");
            sql.append("p.nome as pessoa_nome, \n");
            sql.append("p.cfp as pessoa_cpf, \n");
            sql.append("p.sexo as pessoa_sexo, \n");
            sql.append("p.datacadastro as pessoa_dataCadastro, \n");
            sql.append("p.datanasc as pessoa_datanascimento \n");
            sql.append("from acessocliente a  \n");
            sql.append("inner join cliente cl on cl.codigo = a.cliente \n");
            sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
            sql.append("left join usuario u on u.codigo = a.usuario \n");
            sql.append("inner join localacesso la on la.codigo = a.localacesso \n");
            sql.append("left join coletor co on co.codigo = a.coletor \n");
            sql.append("inner join empresa e on e.codigo = la.empresa \n");
            sql.append("where e.notificarwebhook \n");
            sql.append("and a.codigo = ").append(acessoClienteVO.getCodigo());
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.getCon())) {
                if (rs.next()) {

                    PessoaVO pessoaVO = new PessoaVO();
                    pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
                    pessoaVO.setNome(rs.getString("pessoa_nome"));
                    pessoaVO.setCfp(rs.getString("pessoa_cpf"));
                    pessoaVO.setSexo(rs.getString("pessoa_sexo"));
                    pessoaVO.setDataCadastro(rs.getTimestamp("pessoa_dataCadastro"));
                    pessoaVO.setDataNasc(rs.getTimestamp("pessoa_datanascimento"));
                    ClienteVO clienteVO = new ClienteVO();
                    clienteVO.setPessoa(pessoaVO);
                    clienteVO.setCodigo(rs.getInt("cliente_codigo"));
                    clienteVO.setMatricula(rs.getString("cliente_matricula"));
                    clienteVO.setSituacao(rs.getString("cliente_situacao"));

                    UsuarioVO usuarioVO = new UsuarioVO();
                    usuarioVO.setCodigo(rs.getInt("usuario_codigo"));
                    usuarioVO.setUsername(rs.getString("usuario_username"));
                    usuarioVO.setNome(rs.getString("usuario_nome"));

                    EmpresaVO empresaVO = new EmpresaVO();
                    empresaVO.setCodigo(rs.getInt("empresa_codigo"));
                    empresaVO.setNome(rs.getString("empresa_nome"));
                    empresaVO.setCNPJ(rs.getString("empresa_cnpj"));
                    empresaVO.setIdExterno(rs.getString("empresa_idExterno"));

                    if (acessoClienteVO.getColetor() != null &&
                            UteisValidacao.emptyString(acessoClienteVO.getColetor().getDescricao())) {
                        acessoClienteVO.getColetor().setDescricao(rs.getString("coletor_descricao"));
                    }

                    IntegracaoDTO dto = new IntegracaoDTO(this.key, OPERACAO_ACESSO, empresaVO, usuarioVO);
                    dto.setCliente(new IntegracaoClienteDTO(clienteVO));
                    dto.setAcesso(new IntegracaoAcessoDTO(acessoClienteVO));
                    enviarDadosAssincrono(empresaVO, dto);
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'notificarAcesso' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    private void enviarDadosAssincrono(EmpresaVO empresa, IntegracaoDTO integracaoDTO) {
        if (!PropsService.isCompanyWebhookEnabled()) {
            Uteis.logarDebug("NOTIFICAR WEBHOOK | Integracao webhook desabilitada " +
                    " | Empresa: " + empresa.getNome() + " | Chave: " + this.key + "_" + empresa.getCodigo());
            return;
        } else {
            Uteis.logarDebug("NOTIFICAR WEBHOOK | Integracao webhook HABILITADA " +
                    " | Empresa: " + empresa.getNome() + " | Chave: " + this.key + "_" + empresa.getCodigo());
        }

        if (UteisValidacao.emptyString(integracaoDTO.getChave())) {
            Uteis.logarDebug("NOTIFICAR WEBHOOK | Chave não informada | Integracao do tipo " + integracaoDTO.getOperacao().toUpperCase() +
                    " | Empresa: " + empresa.getNome() + " | Chave: " + this.key + "_" + empresa.getCodigo());
            return;
        }

        if (UteisValidacao.emptyNumber(empresa.getCodigo())) {
            Uteis.logarDebug("NOTIFICAR WEBHOOK | Empresa não informada | Integracao do tipo " + integracaoDTO.getOperacao().toUpperCase() +
                    " | Empresa: " + empresa.getNome() + " | Chave: " + this.key + "_" + empresa.getCodigo());
            return;
        }

        Uteis.logarDebug("NOTIFICAR WEBHOOK | iniciando chamada no webhook: Integracao do tipo " + integracaoDTO.getOperacao().toUpperCase() +
                " | Empresa: " + empresa.getNome() + " | Chave: " + this.key + "_" + empresa.getCodigo());

        try {
            //se não tiver ainda no mapa, então consultar e logo em seguida preencher
            if (IntegracaoSistemaServiceMemory.getEmpresasEConfiguracoes().getOrDefault(this.key + "_" + empresa.getCodigo(), null) == null) {
                Empresa empresaDAO = new Empresa(this.getCon());
                this.urlWebhook = empresaDAO.obterUrlWebhookNotificar(empresa.getCodigo());
                IntegracaoSistemaServiceMemory.getEmpresasEConfiguracoes().put(this.key + "_" + empresa.getCodigo(), this.urlWebhook);
            } else {
                this.urlWebhook = IntegracaoSistemaServiceMemory.getEmpresasEConfiguracoes().getOrDefault(this.key + "_" + empresa.getCodigo(), null);
            }

            if (UteisValidacao.emptyString(urlWebhook)) {
                Uteis.logarDebug("NOTIFICAR WEBHOOK | urlWebhook vazia" + "| Empresa: " + empresa.getNome() + " | Chave: " + this.key + "_" + empresa.getCodigo());
                return;
            }

            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(10000);
                        preencherDataMatricula(integracaoDTO, getCon());
                        preencherProdutos(integracaoDTO, getCon());
                        Map<String, String> headers = new HashMap<>();
                        headers.put("Content-Type", "application/json");
                        headers.put("Chave", integracaoDTO.getChave());
                        RequestHttpService service = new RequestHttpService();
                        service.connectTimeout = 5000;
                        service.connectionRequestTimeout = 30000;
                        RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlWebhook, headers, null, new JSONObject(integracaoDTO).toString(), MetodoHttpEnum.POST);
                        service = null;
                        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                            throw new Exception(respostaHttpDTO.getResponse());
                        }
                    } catch (Exception ex) {
                        Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'RUN' :  " + integracaoDTO.getOperacao().toUpperCase() +
                                "| Empresa: " + empresa.getNome() + " | Chave: " + key + " | " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
            }).start();
        } catch (Exception e) {
            Uteis.logarDebug("ERRO IntegracaoSistemaService | Método 'enviarDadosAssincrono' :  " + integracaoDTO.getOperacao().toUpperCase() +
                    "| Empresa: " + empresa.getNome() + " | Chave: " + key + " | " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void preencherDataMatricula(IntegracaoDTO integracaoDTO, Connection con) {
        SituacaoClienteSinteticoDW dao = null;
        try {
            if (integracaoDTO.getCliente() == null ||
                    !UteisValidacao.emptyString(integracaoDTO.getCliente().getDataMatricula()) ||
                    UteisValidacao.emptyNumber(integracaoDTO.getCliente().getCodigoPessoa()) ||
                    UteisValidacao.emptyString(integracaoDTO.getCliente().getSituacao()) ||
                    integracaoDTO.getCliente().getSituacao().equalsIgnoreCase("VI")) {
                return;
            }
            dao = new SituacaoClienteSinteticoDW(con);
            integracaoDTO.getCliente().setDataMatricula(Uteis.getData(dao.obterDataMatricula(integracaoDTO.getCliente().getCodigoPessoa())));
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO preencherDataMatricula | Método 'notificarCadastro' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            dao = null;
        }
    }

    private void preencherProdutos(IntegracaoDTO integracaoDTO, Connection con) {
        try {
            if (integracaoDTO.getContrato() != null && !UteisValidacao.emptyNumber(integracaoDTO.getContrato().getCodigo())) {
                integracaoDTO.getContrato().setProdutos(obterProdutosIntegracao(integracaoDTO.getContrato().getCodigo(), null, con));
            }
            if (integracaoDTO.getPagamento() != null && !UteisValidacao.emptyNumber(integracaoDTO.getPagamento().getRecibo())) {
                integracaoDTO.getPagamento().setProdutos(obterProdutosIntegracao(null, integracaoDTO.getPagamento().getRecibo(), con));
            }
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO preencherDataMatricula | Método 'preencherProdutos' | Chave: " + key + " | " + ex.getMessage());
            ex.printStackTrace();
        }
    }

    private List<IntegracaoProdutoDTO> obterProdutosIntegracao(Integer contrato, Integer recibo, Connection con) {
        List<IntegracaoProdutoDTO> lista = new ArrayList<>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("m.codigo, \n");
            sql.append("m.totalfinal, \n");
            sql.append("m.descricao, \n");
            sql.append("m.quantidade, \n");
            sql.append("m.valordesconto, \n");
            sql.append("m.precounitario, \n");
            sql.append("m.situacao, \n");
            sql.append("p.tipoproduto as produto_tipoproduto, \n");
            sql.append("p.codigo as produto_codigo, \n");
            sql.append("p.descricao as produto_descricao \n");
            sql.append("from movproduto m \n");
            sql.append("inner join produto p on p.codigo = m.produto \n");
            if (!UteisValidacao.emptyNumber(contrato)) {
                sql.append("where m.contrato = ").append(contrato).append(" \n");
            } else if (!UteisValidacao.emptyNumber(recibo)) {
                sql.append("where m.codigo in ( \n");
                sql.append("select \n");
                sql.append("distinct (mov.movproduto) \n");
                sql.append("from movprodutoparcela mov \n");
                sql.append("where mov.recibopagamento = ").append(recibo).append(") \n");
            } else {
                return lista;
            }

            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    IntegracaoProdutoDTO mov = new IntegracaoProdutoDTO();
                    mov.setCodigo(rs.getInt("codigo"));
                    mov.setTotalFinal(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("totalfinal")));
                    mov.setDescricao(rs.getString("descricao"));
                    mov.setQuantidade(rs.getInt("quantidade"));
                    mov.setValorDesconto(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valordesconto")));
                    mov.setPrecoUnitario(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("precounitario")));
                    mov.setSituacao(rs.getString("situacao"));
                    mov.setProdutoTipo(rs.getString("produto_tipoproduto"));
                    mov.setProdutoCodigo(rs.getInt("produto_codigo"));
                    mov.setProdutoDescricao(rs.getString("produto_descricao"));
                    lista.add(mov);
                }
            }
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO preencherDataMatricula | Método 'obterProdutosIntegracao' | Chave: " + key + " | " + ex.getMessage());
        }
        return lista;
    }

    public String getUrlWebhook() {
        return urlWebhook;
    }

    public void setUrlWebhook(String urlWebhook) {
        this.urlWebhook = urlWebhook;
    }
}
