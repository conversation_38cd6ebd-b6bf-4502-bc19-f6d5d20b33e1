package servicos.integracao.rdstationmarketing;

import negocio.comuns.basico.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.financeiro.MovParcela;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.util.ExecuteRequestHttpService;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IntegracaoRdStationMarketingServiceImpl implements IntegracaoRdStationMarketingService {

    private Connection con;

    private final String CAMPO_VAZIO = "SEM_REGISTRO";
    private final String ENDPOINT_ATUALIZAR_CONTATOS = "https://api.rd.services/platform/contacts/email:";
    private final String CLIENT_ID = "e00fd90a-6267-4744-8320-dcda19573c03";
    private final String CLIENT_SECRET = "********************************";
    private final String ENDPOINT_REFRESH_TOKEN = "https://api.rd.services/auth/token"; // URL para renovar o token

    private final String SUCESSO_ENVIO = "SUCESSO ao enviar dados para RD Station";
    private final String ERRO_ENVIO = "ERRO ao enviar dados para RD Station";
    private final String SUCESSO_UPDATE_TOKEN = "SUCESSO ao atualizar tokens de acesso";
    private final String ERRO_UPDATE_TOKEN = "ERRO ao atualizar tokens de acesso";
    private final String EMAIL_NOT_FOUND = "Email nao encontrado";

    public IntegracaoRdStationMarketingServiceImpl(Connection con) {
        this.con = con;
    }

    @Override
    public void enviarDadosAtualizadosDoAluno(ClienteVO cliente, String nomePlano, Date dataMatricula, AtualizarCamposEnum campos) throws Exception {
        String emailEncontrado = encontrarEmailCliente(cliente);
        if (emailEncontrado.equals(EMAIL_NOT_FOUND)) {
            inserirLogIntegracao(EMAIL_NOT_FOUND,
                    "Rd Station: o cliente " + cliente.getPessoa().getNome() + " (cod:" + cliente.getCodigo() + ") nao possui e-mail cadastrado na Pacto",
                    "IntegracaoRdStationMarketingServiceImpl.enviarDadosAtualizadosDoAluno");
        } else {
            String urlRdStation = ENDPOINT_ATUALIZAR_CONTATOS + emailEncontrado;
            SituacaoClienteSinteticoDW sinteticoDW = new SituacaoClienteSinteticoDW(con);
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorCodigo(cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);


            JSONObject camposDoAluno = prepararCamposDoAluno(cliente, nomePlano, dataMatricula, campos, empresaVO, sinteticoDW);

            String accessToken = getTokenDeAutorizacao(empresaVO, empresaDao);
            empresaDao = null;

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + accessToken);

            enviarRequisicaoPatch(urlRdStation, camposDoAluno.toString(), headers);
        }
    }

    private void enviarRequisicaoPatch(String url, String body, Map<String, String> headers) throws Exception {
        // Monta e imprime o comando curl
        StringBuilder curl = new StringBuilder();
        try {
            curl.append("curl -X PATCH \"").append(url).append("\" \\\n");
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                curl.append("  -H \"").append(entry.getKey()).append(": ").append(entry.getValue()).append("\" \\\n");
            }
            curl.append("  -d '").append(body.replace("'", "\\'")).append("'");
            System.out.println("===== CURL GERADO =====");
            System.out.println(curl.toString());
            System.out.println("========================");

            // Criação da URI e requisição PATCH
            URIBuilder builder = new URIBuilder(url);
            URI uri = builder.build();

            HttpPatch httpPatch = new HttpPatch(uri);
            httpPatch.setHeader("Content-Type", headers.get("Content-Type"));
            httpPatch.setHeader("Authorization", headers.get("Authorization"));
            StringEntity entity = new StringEntity(body, StandardCharsets.UTF_8);
            httpPatch.setEntity(entity);

            HttpClient httpClient = ExecuteRequestHttpService.createConnector(null);
            HttpResponse httpResponse = httpClient.execute(httpPatch);

            String respostaRdStation = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
            inserirLogIntegracao(respostaRdStation, SUCESSO_ENVIO, "enviarDadosAtualizadosDoAluno.enviarRequisicaoPatch|"+curl.toString());

        } catch (Exception e) {
            inserirLogIntegracao(e.getMessage(), ERRO_ENVIO, "enviarDadosAtualizadosDoAluno.enviarRequisicaoPatch|"+curl.toString());
            Uteis.logar(e, this.getClass());
        }
    }

    private void exibirCurlNoConsole(String url, String body, Map<String, String> headers) {
        StringBuilder curl = new StringBuilder();
        curl.append("curl -X PATCH \"").append(url).append("\" \\\n");

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            curl.append("  -H \"")
                    .append(entry.getKey())
                    .append(": ")
                    .append(entry.getValue())
                    .append("\" \\\n");
        }

        curl.append("  -d '").append(body.replace("'", "\\'")).append("'");

        System.out.println("===== CURL GERADO =====");
        System.out.println(curl.toString());
        System.out.println("========================");
    }

    private JSONObject prepararCamposDoAluno(ClienteVO cliente, String nomePlano, Date dataMatricula, AtualizarCamposEnum campos, EmpresaVO empresaVO, SituacaoClienteSinteticoDW sinteticoDW) throws Exception {
        JSONObject camposDoAluno = new JSONObject();
        if (campos == AtualizarCamposEnum.TODOS) {

            camposDoAluno.put("name", validaCampo(cliente.getPessoa().getNome()));
            camposDoAluno.put("cf_nome_completo", validaCampo(cliente.getPessoa().getNome()));
            camposDoAluno.put("cf_idade_contato", validaCampo(cliente.getPessoa().getDataNasc_Apresentar()));
            camposDoAluno.put("cf_numero_idade", validaCampo(cliente.getPessoa().getIdadePessoa().equals("00") ? CAMPO_VAZIO : cliente.getPessoa().getIdadePessoa()));
            camposDoAluno.put("cf_sexo", validaCampo(cliente.getPessoa().getSexo_Apresentar()));
            camposDoAluno.put("mobile_phone", buscarTelefone(cliente));
            camposDoAluno.put("cf_inadimplente", verificarSePossuiCaixaEmAberto(cliente));
            camposDoAluno.put("cf_assinatura", buscarPlano(cliente, nomePlano));
            camposDoAluno.put("cf_status", validaSituacao(cliente, nomePlano, camposDoAluno.get("cf_inadimplente").equals("SIM")));
            camposDoAluno.put("cf_email_contato", validaCampo(encontrarEmailCliente(cliente)));
            camposDoAluno.put("cf_unidade", validaCampo(empresaVO.getNome()));
            camposDoAluno.put("cf_cidade_contato", validaCampo(buscarNomeCidade(cliente.getPessoa().getCidade().getCodigo())));

            if (cliente.getPessoa().getEnderecoVOs() != null && !cliente.getPessoa().getEnderecoVOs().isEmpty()) {
                camposDoAluno.put("cf_endereco_bairro", validaCampo(cliente.getPessoa().getEnderecoVOs().get(0).getBairro()));
            } else {
                camposDoAluno.put("cf_endereco_bairro", CAMPO_VAZIO);
            }

            // Data da matrícula (já existente)
            if (dataMatricula != null) {
                camposDoAluno.put("cf_data_matricula", validaCampo(Uteis.getDataString(dataMatricula.toString())));
            } else {
                camposDoAluno.put("cf_data_matricula", CAMPO_VAZIO);
            }

            // Parte nova da integração
            SituacaoClienteSinteticoDWVO sinteticoDWVO = sinteticoDW.consultarCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            camposDoAluno.put("cf_plano_atual", validaCampo(nomePlano != null ? nomePlano : CAMPO_VAZIO));

            // Saldo de créditos
            Integer saldoCreditos = sinteticoDW.qtdCreditoAluno(cliente.getCodigo()).getSaldoCreditoTreino();
            camposDoAluno.put("cf_saldo_creditos", saldoCreditos != null ? saldoCreditos : 0);

            // Matrícula realizada
            String dataInicioContrato = cliente.getDataInicioContrato();
            String dataInicioContratoFormatada = Uteis.getDataString(dataInicioContrato);
            camposDoAluno.put("cf_matricula_realizada", validaCampo(!UteisValidacao.emptyString(dataInicioContratoFormatada) ? dataInicioContratoFormatada : CAMPO_VAZIO));

            // Novo lead (data de cadastro)
            Date dataCadastroLead = sinteticoDWVO.getDataCadastro();
            String dataCadastroLeadFormatada = Uteis.getData(dataCadastroLead);
            camposDoAluno.put("cf_novo_lead", validaCampo(!UteisValidacao.emptyString(dataCadastroLeadFormatada) ? dataCadastroLeadFormatada : CAMPO_VAZIO));

            // Dias sem presença
            Long diasSemPresenca = sinteticoDWVO.getNrDiasUltimoAcesso();
            camposDoAluno.put("cf_dias_sem_presenca", diasSemPresenca);

            // Dias de inadimplência
            MovParcela movParcela = new MovParcela(con);
            Integer diasInadimplente = movParcela.obterDiasInadimplenteCliente(sinteticoDWVO.getCodigoPessoa(),null);
            camposDoAluno.put("cf_inadimplente_dias", diasInadimplente);

            Double valorContrato = sinteticoDWVO.getValorPagoContrato();
            camposDoAluno.put("cf_valor_plano",validaCampo(UteisValidacao.converterParaValorMonetario(valorContrato)));
            camposDoAluno.put("cf_data_compra_plano", sinteticoDWVO.getDataLancamentoContrato());


        } else if (campos == AtualizarCamposEnum.APENAS_SITUACAO_INADIMPLENCIA) {
            camposDoAluno.put("cf_inadimplente", verificarSePossuiCaixaEmAberto(cliente));
            camposDoAluno.put("cf_status", validaSituacao(cliente, nomePlano, camposDoAluno.get("cf_inadimplente").equals("SIM")));
        }

        return camposDoAluno;
    }


    private String validaCampo(String campo) {
        return !UteisValidacao.emptyString(campo) ? campo : CAMPO_VAZIO;
    }

    private String validaSituacao(ClienteVO cliente, String nomePlanoVindoDoSintetico, boolean possiuCaixaEmAbertoInadimplente) throws Exception {
        String situacaoValidada = validaCampo(cliente.getSituacao());
        if (situacaoValidada.equals("NO")) {
            return "CA";
        }
        if (situacaoValidada.equals("VI")
                && !buscarPlano(cliente, nomePlanoVindoDoSintetico).equals(CAMPO_VAZIO)
                && !possiuCaixaEmAbertoInadimplente) {
            return "AT";
        }
        return situacaoValidada;
    }

    private String encontrarEmailCliente(ClienteVO cliente) throws Exception {
        if (!UteisValidacao.emptyList(cliente.getPessoa().getEmailVOs())) {
            return cliente.getPessoa().getEmailVOs().get(0).getEmail();
        }
        Email emailDao = new Email(con);
        List<EmailVO> listaEmails = emailDao.consultarEmails(cliente.getPessoa().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        emailDao = null;
        if (!UteisValidacao.emptyList(listaEmails)) {
            return listaEmails.get(0).getEmail();
        }
        return EMAIL_NOT_FOUND;
    }

    private String getTokenDeAutorizacao(EmpresaVO empresaVO, Empresa dao) throws Exception {
        String accessToken = empresaVO.getConfiguracaoRDStation().getAccessTokenRdStationMarketing();
        String refreshToken = empresaVO.getConfiguracaoRDStation().getRefreshTokenRdStationMarketing();
        Boolean accessTokenExpirou = verificarSeAccessTokenExpirou(accessToken);

        if (accessTokenExpirou) {
            accessToken = atualizarAccessToken(refreshToken);
            dao.atualizarTokensRdStationApiMarketing(empresaVO.getCodigo(), accessToken, refreshToken);
        }

        return accessToken;
    }

    private Boolean verificarSeAccessTokenExpirou(String accessToken) {
        String urlTeste = "https://api.rd.services/platform/contacts/email:teste";

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + accessToken);

        URIBuilder builder;
        HttpResponse httpResponse = null;
        Boolean statusRdStation = null;

        try {
            HttpClient httpClient = ExecuteRequestHttpService.createConnector(null);

            builder = new URIBuilder(urlTeste);
            URI uri = builder.build();

            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader("Authorization", headers.get("Authorization"));

            httpResponse = httpClient.execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            statusRdStation = statusCode == 401; // doc rd station: deve vir status 401 sempre q token expirar

        } catch (Exception e) {
            Uteis.logar("Erro ao construir a URI para verificar o token: " + e.getMessage(), this.getClass());
        }
        return statusRdStation;
    }


    private String atualizarAccessToken(String refreshToken) throws Exception {
        JSONObject body = new JSONObject();
        body.put("client_id", CLIENT_ID);
        body.put("client_secret", CLIENT_SECRET);
        body.put("refresh_token", refreshToken);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        URIBuilder builder = new URIBuilder(ENDPOINT_REFRESH_TOKEN);
        URI uri = builder.build();

        HttpPost httpPost = new HttpPost(uri);
        httpPost.setHeader("Content-Type", headers.get("Content-Type"));
        StringEntity entity = new StringEntity(body.toString(), StandardCharsets.UTF_8);
        httpPost.setEntity(entity);

        HttpClient httpClient = ExecuteRequestHttpService.createConnector(null);
        String accessToken = null;
        String respostaRdStation = null;

        try {
            HttpResponse httpResponse = httpClient.execute(httpPost);
            respostaRdStation = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);
            JSONObject jsonResponse = new JSONObject(respostaRdStation);
            inserirLogIntegracao(respostaRdStation, SUCESSO_UPDATE_TOKEN, "getTokenDeAutorizacao.atualizarAccessToken");
            accessToken = jsonResponse.getString("access_token");
        } catch (Exception e) {
            inserirLogIntegracao(respostaRdStation, ERRO_UPDATE_TOKEN, "getTokenDeAutorizacao.atualizarAccessToken");
            Uteis.logar(ERRO_UPDATE_TOKEN + e.getMessage(), this.getClass());
        }

        return accessToken;
    }

    private String verificarSePossuiCaixaEmAberto(ClienteVO cliente) throws Exception {
        Cliente clienteDao = new Cliente(con);
        boolean inadimplencia = clienteDao.clienteEstaInadimplente(cliente.getCodigo(), cliente.getEmpresa().getCodigo());
        clienteDao = null;
        return inadimplencia ? "SIM" : "NAO";
    }

    private String buscarNomeCidade(int codigoCidade) throws Exception {
        Cidade cidadeDao = new Cidade(con);
        List<CidadeVO> cidadesEncontradas = cidadeDao.consultarPorCodigo(codigoCidade, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UteisValidacao.emptyList(cidadesEncontradas)) {
            return cidadesEncontradas.get(0).getNome();
        }
        return null;
    }

    private String buscarPlano(ClienteVO cliente, String nomePlanoVindoDoSintetico) {
        if (!UteisValidacao.emptyString(nomePlanoVindoDoSintetico)) {
            return nomePlanoVindoDoSintetico;
        }
        if (!UteisValidacao.emptyString(cliente.getPlanoApresentar())) {
            return cliente.getPlanoApresentar();
        }
        return CAMPO_VAZIO;
    }

    private String buscarTelefone(ClienteVO cliente) {
        if (!UteisValidacao.emptyList(cliente.getPessoa().getTelefoneVOs())) {
            return cliente.getPessoa().getTelefoneVOs().get(0).getNumero();
        }
        return CAMPO_VAZIO;
    }

    private void inserirLogIntegracao(String json, String resultado, String servico) {
        LogIntegracoesVO logIntegracoesVO = new LogIntegracoesVO();
        try {
            LogIntegracoes logIntegracoesDao = new LogIntegracoes(con);
            logIntegracoesVO.setDadosRecebidos(json);
            logIntegracoesVO.setDataLancamento(Calendario.hoje());
            logIntegracoesVO.setServico("IntegracaoRdStationMarketingServiceImpl." + servico);
            logIntegracoesVO.setResultado(resultado);
            logIntegracoesDao.incluir(logIntegracoesVO);
            logIntegracoesDao = null;

        } catch (Exception e) {
            Uteis.logar("Erro ao inserir log integracao: " + e.getMessage() + " - Dados recebidos: " + json);
        }

    }
}
