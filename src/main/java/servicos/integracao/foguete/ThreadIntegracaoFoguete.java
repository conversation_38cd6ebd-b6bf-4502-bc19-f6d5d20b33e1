package servicos.integracao.foguete;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoIntegracaoFogueteVO;
import negocio.comuns.basico.LogIntegracoesVO;
import negocio.comuns.contrato.ContratoPlanoProdutoSugeridoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.integracao.foguete.dto.AttributesDTO;
import servicos.integracao.foguete.dto.ClienteFogueteDTO;
import servicos.integracao.foguete.dto.CustomerFogueteDTO;
import servicos.integracao.foguete.dto.OfferFogueteDTO;
import servicos.integracao.foguete.dto.ProductFogueteDTO;
import servicos.integracao.foguete.dto.ProductPlanFogueteDTO;
import servicos.integracao.foguete.enums.ClienteFogueteStatusEnum;
import servicos.integracao.foguete.enums.FrequencyEnum;
import servicos.util.ExecuteRequestHttpService;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

public class ThreadIntegracaoFoguete implements Runnable, AutoCloseable {

    private static final String HEADER_TOKEN_API = "Api-Token";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";

    private final Connection con;
    private final LogIntegracoes logIntegracoesDAO;
    private final Contrato contratoDAO;
    private final Plano planoDAO;
    private final ConfiguracaoIntegracaoFogueteVO configuracaoIntegracaoFogueteVO;
    private final ClienteFogueteStatusEnum status;
    private final ContratoVO contratoVO;
    private final ClienteVO clienteNotificar;
    private final Log logDAO;
    private final UsuarioVO usuarioVO;
    private final boolean isSincronizacaoManual;

    public ThreadIntegracaoFoguete(Connection con, ConfiguracaoIntegracaoFogueteVO configuracaoIntegracaoFogueteVO, ClienteFogueteStatusEnum status, ContratoVO contratoVO, ClienteVO clienteNotificar, UsuarioVO usuarioVO, boolean isSincronizacaoManual) throws Exception {
        this.con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        this.logIntegracoesDAO = new LogIntegracoes(this.con);
        this.contratoDAO = new Contrato(this.con);
        this.planoDAO = new Plano(this.con);
        this.configuracaoIntegracaoFogueteVO = configuracaoIntegracaoFogueteVO;
        this.status = status;
        this.contratoVO = contratoVO;
        this.clienteNotificar = clienteNotificar;
        this.logDAO = new Log(this.con);
        this.usuarioVO = usuarioVO;
        this.isSincronizacaoManual = isSincronizacaoManual;
    }

    @Override
    public void run() {
        try {
            if (contratoVO == null || contratoVO.getCodigo() == 0) {
                throw new ConsistirException("Contrato não informado!");
            }
            if (clienteNotificar == null || clienteNotificar.getCodigo() == 0) {
                throw new ConsistirException("Cliente não informado!");
            }

            Uteis.logarDebug("Entrei na Integração Foguete - contrato: " + contratoVO.getCodigo());

            if (UteisValidacao.emptyString(configuracaoIntegracaoFogueteVO.getUrlApi())) {
                throw new ConsistirException("URL Api não está configurada para Integração Foguete!");
            }

            if (status.equals(ClienteFogueteStatusEnum.ACTIVE) && !validarProdutoContrato()) {
                if (isSincronizacaoManual) {
                    throw new ConsistirException("O contrato não possui o produto da integração foguete.");
                }
                return;
            }

            String chavePrimaria = String.format("%d_%d", contratoVO.getCodigo(), clienteNotificar.getCodigo());
            boolean foiSincronizadoQuandoAtivo = existeLogEnvioFoguete(chavePrimaria, ClienteFogueteStatusEnum.ACTIVE);

            Uteis.logarDebug(String.format("Contrato cod: %d - sincronizado: %s", contratoVO.getCodigo(), contratoVO.getDataSincronizacaoIntegracaoFoguete() != null));
            if (!status.equals(ClienteFogueteStatusEnum.ACTIVE) && !foiSincronizadoQuandoAtivo) {
                if (isSincronizacaoManual) {
                    throw new ConsistirException(String.format("Não foi possivel sicronizar o contrato com status %s pois ele não foi sincronizado anteriormente quando estava ativo.", status.name()));
                }
                return;
            }

            Uteis.logarDebug("Vou montar cliente para enviar a Integração Foguete - contrato: " + contratoVO.getCodigo());
            ClienteFogueteDTO clienteFogueteDTO = montarClienteFoguete();
            validarDadosCliente(clienteFogueteDTO);
            Uteis.logarDebug("Montei e validei o cliente Integração Foguete - contrato: " + contratoVO.getCodigo() + " - cliente: " + clienteNotificar.getCodigoMatricula());

            URIBuilder builder = new URIBuilder(configuracaoIntegracaoFogueteVO.getUrlApi());
            URI uri = builder.build();

            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPost.setHeader(HEADER_TOKEN_API, configuracaoIntegracaoFogueteVO.getTokenApi());

            JSONObject jsonBody = new JSONObject(clienteFogueteDTO);
            StringEntity entity = new StringEntity(jsonBody.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            HttpClient httpClient = ExecuteRequestHttpService.createConnector(null);

            Uteis.logarDebug("Integração Foguete - enviando requisicao para api");
            HttpResponse httpResponse = httpClient.execute(httpPost);
            String response = EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);

            Uteis.logarDebug("Integração Foguete - requisicao realizada para api, retorno: "+response);

            JSONObject jsonLog = new JSONObject();
            jsonLog.put("urlApi", configuracaoIntegracaoFogueteVO.getUrlApi());
            jsonLog.put("responseApi", response != null ? response : "");
            jsonLog.put("jsonBodyEnviado", jsonBody);
            jsonLog.put("codigoContrato", contratoVO.getCodigo());
            jsonLog.put("codigoMatricula", clienteNotificar.getCodigoMatricula());

            String msg =  String.format("Identifier: %s - Contrato cod: %d, Para Cliente Mat. %d - Status: %s",
                        clienteFogueteDTO.getIdentifier(), contratoVO.getCodigo(), clienteNotificar.getCodigoMatricula(), status.name());
            if ("ok".equals(response)) {
                contratoDAO.atualizarDataSincronizacaoIntegracaoFoguete(contratoVO.getCodigo(), new Date());
                salvarLog(msg + " - OK", jsonLog.toString(), clienteNotificar);
            } else {
                salvarLog(msg + " - FALHA", jsonLog.toString(), clienteNotificar);
            }
        } catch (Exception ex) {
            handleError(ex.getMessage());
        } finally {
            this.close();
        }
    }

    private boolean existeLogEnvioFoguete(String chavePrimaria, ClienteFogueteStatusEnum status) throws Exception {
        if (UteisValidacao.emptyString(chavePrimaria)) {
            return false;
        }
        String sql = "SELECT l.codigo FROM logintegracoes l \n" +
                "WHERE l.servico = 'INTEGRACAO_FOGUETE' \n" +
                "AND l.dadosrecebidos like '{%}' \n" +
                "AND (l.dadosrecebidos::json->>'jsonBodyEnviado')::json->>'status' = ? \n" +
                "AND trim(lower(l.dadosrecebidos::json->>'responseApi')) = 'ok' \n" +
                "AND l.chaveprimaria = ? ";
        try (PreparedStatement stmt = con.prepareStatement(sql)) {
            stmt.setString(1, status.name());
            stmt.setString(2, chavePrimaria);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        }
    }

    private void validarDadosCliente(ClienteFogueteDTO clienteFogueteDTO) throws Exception {
        if (clienteFogueteDTO.getCustomer() == null || UteisValidacao.emptyString(clienteFogueteDTO.getIdentifier())) {
            throw new ConsistirException("Cliente não encontrado");
        }
        if (UteisValidacao.emptyString(clienteFogueteDTO.getCustomer().getEmail())) {
            throw new ConsistirException("E-mail do cliente é obrigatório");
        }
        if (UteisValidacao.emptyString(clienteFogueteDTO.getCustomer().getName())) {
            throw new ConsistirException("Nome do cliente é obrigatório");
        }

        if (UteisValidacao.emptyString(clienteFogueteDTO.getStart_date())) {
            throw new ConsistirException("Data de início do contrato é obrigatório");
        }
        if (UteisValidacao.emptyString(clienteFogueteDTO.getDue_date())) {
            throw new ConsistirException("Data de fim do contrato é obrigatório");
        }
        if (ClienteFogueteStatusEnum.CANCELED.name().equals(clienteFogueteDTO.getStatus())
                && UteisValidacao.emptyString(clienteFogueteDTO.getCancellation_date())) {
            throw new ConsistirException("Data de cancelamento do contrato é obrigatório");
        }

        if (clienteFogueteDTO.getProduct() == null
                || UteisValidacao.emptyString(clienteFogueteDTO.getProduct().getId())) {
            throw new ConsistirException("Produto é obrigatorio");
        }

        if (UteisValidacao.emptyString(clienteFogueteDTO.getProduct().getName())) {
            throw new ConsistirException("Nome do produto é obrigatorio");
        }

        if (clienteFogueteDTO.getProduct_plan() == null
                || UteisValidacao.emptyString(clienteFogueteDTO.getProduct_plan().getId())) {
            throw new ConsistirException("Plano é obrigatorio");
        }
        if (UteisValidacao.emptyString(clienteFogueteDTO.getProduct_plan().getName())) {
            throw new ConsistirException("Nome do plano é obrigatorio");
        }
    }

    private boolean validarProdutoContrato() {
        if (UteisValidacao.emptyNumber(configuracaoIntegracaoFogueteVO.getProduto())) {
            return true;
        }
        if (!UteisValidacao.emptyList(contratoVO.getContratoPlanoProdutoSugeridoVOs())) {
            for (Object obj : contratoVO.getContratoPlanoProdutoSugeridoVOs()) {
                ContratoPlanoProdutoSugeridoVO cpps = (ContratoPlanoProdutoSugeridoVO) obj;
                if (cpps.getPlanoProdutoSugerido() != null
                        && cpps.getPlanoProdutoSugerido().getProduto().getCodigo().equals(configuracaoIntegracaoFogueteVO.getProduto())) {
                    return true;
                }
            }
        }
        if (!UteisValidacao.emptyList(contratoVO.getMovProdutoVOs())) {
            for (MovProdutoVO mp : contratoVO.getMovProdutoVOs()) {
                if (mp.getProduto() != null && mp.getProduto().getCodigo() != null
                        && mp.getProduto().getCodigo().equals(configuracaoIntegracaoFogueteVO.getProduto())) {
                    return true;
                }
            }
        }

        Uteis.logarDebug("Integração Foguete - contrato: " + contratoVO.getCodigo() + " não possui produto");
        return false;
    }

    private void handleError(String msgError) {
        final String msg = String.format("Contrato cod: %d, Cliente: %s, Status: %s Erro: %s", contratoVO.getCodigo(), clienteNotificar.getNome_Apresentar(), status.name(), msgError);
        Uteis.logarDebug("Falha sincronização Integração Foguete - " + msg);
        salvarLog(msg, "", clienteNotificar);
    }

    private ClienteFogueteDTO montarClienteFoguete() throws Exception {
        ClienteFogueteDTO clienteFogueteDTO = new ClienteFogueteDTO();
        clienteFogueteDTO.setStatus(status.name());

        if (UteisValidacao.emptyString(clienteNotificar.getPessoa().getCfp())) {
            throw new ConsistirException("Cliente não possui CPF");
        }

        clienteFogueteDTO.setIdentifier(String.format("%s_%s", contratoVO.getCodigo(), clienteNotificar.getPessoa().getCfp()));

        // contrato
        clienteFogueteDTO.setStart_date(Calendario.getData(contratoVO.getVigenciaDe(), "yyyy-MM-dd"));
        clienteFogueteDTO.setDue_date(Calendario.getData(contratoVO.getVigenciaAteAjustada(), "yyyy-MM-dd"));
        if (status.equals(ClienteFogueteStatusEnum.CANCELED)) {
            clienteFogueteDTO.setCancellation_date(Calendario.getData(obterDataCancelamento(contratoVO), "yyyy-MM-dd"));
        }

        if (UteisValidacao.emptyString(contratoVO.getPlano().getDescricao())) {
            contratoVO.setPlano(planoDAO.consultarPorChavePrimaria(contratoVO.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (contratoVO.getPlano() == null || UteisValidacao.emptyString(contratoVO.getPlano().getDescricao())) {
                throw new ConsistirException("Plano não encontrado: " + contratoVO.getPlano().getCodigo());
            }
        }
        String nomePlano = contratoVO.getPlanoDescricao_Apresentar() + " - PACTO";

        // product
        clienteFogueteDTO.setProduct(new ProductFogueteDTO());
        clienteFogueteDTO.getProduct().setId(contratoVO.getPlano().getCodigo().toString());
        clienteFogueteDTO.getProduct().setName(nomePlano);
        // plano
        clienteFogueteDTO.setProduct_plan(new ProductPlanFogueteDTO());
        clienteFogueteDTO.getProduct_plan().setId(contratoVO.getPlano().getCodigo().toString());
        clienteFogueteDTO.getProduct_plan().setName(nomePlano);
        clienteFogueteDTO.getProduct_plan().setPrice(contratoVO.getValorFinal());
        clienteFogueteDTO.getProduct_plan().setFrequency(FrequencyEnum.MONTHLY.name());

        // offer
        clienteFogueteDTO.setOffer(new OfferFogueteDTO());

        // customer
        clienteFogueteDTO.setCustomer(new CustomerFogueteDTO());
        clienteFogueteDTO.getCustomer().setName(clienteNotificar.getNome_Apresentar());
        if (!UteisValidacao.emptyList(clienteNotificar.getPessoa().getEmailVOs())) {
            clienteFogueteDTO.getCustomer().setEmail(clienteNotificar.getPessoa().getEmailVOs().get(0).getEmail());
        }
        clienteFogueteDTO.getCustomer().setCpf(clienteNotificar.getPessoa().getCfp());
        String telefones = clienteNotificar.getPessoa().getTelefonesCelular();
        if (!UteisValidacao.emptyString(telefones)) {
            String celularSemMascara = Formatador.removerMascara(telefones.split(";")[0]);
            if (!UteisValidacao.emptyString(celularSemMascara)) {
                clienteFogueteDTO.getCustomer().setCellphone("55" + celularSemMascara);
            }
        }

        //Attributes
        clienteFogueteDTO.setAttributes(new AttributesDTO());
        clienteFogueteDTO.getAttributes().setUnidade_name(clienteNotificar.getEmpresa().getNome());
        Integer unidadeId = !UteisValidacao.emptyString(clienteNotificar.getEmpresa().getCodigoRede())
                ? Integer.parseInt(clienteNotificar.getEmpresa().getCodigoRede()) : clienteNotificar.getEmpresa().getCodigo();
        clienteFogueteDTO.getAttributes().setUnidade_id(unidadeId);

        return clienteFogueteDTO;
    }

    private void salvarLog(String resultado, String dadosRecebidos, ClienteVO cliente) {
        try {
            final LogIntegracoesVO log = new LogIntegracoesVO();
            log.setDataLancamento(new Date());
            log.setServico("INTEGRACAO_FOGUETE");
            log.setResultado(resultado);
            log.setDadosRecebidos(dadosRecebidos);
            if (contratoVO != null && !UteisValidacao.emptyNumber(contratoVO.getCodigo())
                    && clienteNotificar != null && !UteisValidacao.emptyNumber(clienteNotificar.getCodigo())) {
                log.setChavePrimaria(String.format("%d_%d", contratoVO.getCodigo(), clienteNotificar.getCodigo()));
            }
            logIntegracoesDAO.incluir(log);

            if (cliente != null) {
                salvarLogCliente(resultado, cliente);
            }
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao salvar log de integração foguete: " + e.getMessage());
        }
    }

    private void salvarLogCliente(String resultado, ClienteVO cliente) throws Exception {
        final LogVO logCliente = new LogVO();
        if (contratoVO != null && !UteisValidacao.emptyNumber(contratoVO.getCodigo())
                && clienteNotificar != null && !UteisValidacao.emptyNumber(clienteNotificar.getCodigo())) {
            logCliente.setChavePrimaria(String.format("%d_%d", contratoVO.getCodigo(), clienteNotificar.getCodigo()));
        }
        logCliente.setDataAlteracao(new Date());
        logCliente.setPessoa(cliente.getPessoa().getCodigo());
        logCliente.setCliente(cliente.getCodigo());
        logCliente.setNomeEntidade("INTEGRACAOFOGUETE");
        logCliente.setNomeEntidadeDescricao("Integração Foguete");
        logCliente.setOperacao("INTEGRAÇÃO FOGUETE");
        logCliente.setNomeCampo("RESULTADO");
        logCliente.setValorCampoAlterado(resultado);
        if (usuarioVO != null && !UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
            logCliente.setResponsavelAlteracao(usuarioVO.getNome());
            logCliente.setUserOAMD(usuarioVO.getUserOamd());
        }
        logDAO.incluir(logCliente);
    }

    @Override
    public void close() {
        try {
            if (con != null && !con.isClosed()) {
                con.close();
            }
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao fechar conexão com banco de dados: " + e.getMessage());
        }
    }

    private Date obterDataCancelamento(ContratoVO contratoVO) throws Exception {
        if (contratoVO.getCancelamentoContratoVOOrignal() != null && contratoVO.getCancelamentoContratoVOOrignal().getDataCancelamento() != null) {
            return contratoVO.getCancelamentoContratoVOOrignal().getDataCancelamento();
        }

        String sql = "select dataoperacao from contratooperacao \n" +
                " where tipooperacao = 'CA' \n" +
                "and contrato = " + contratoVO.getCodigo();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getDate("dataoperacao");
            }
        }
        throw new Exception("Falha ao obter data cancelamento contrato!");
    }
}
