package servicos.integracao.igopass;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.vendas.VendasConfig;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class IgopassService {
    // Integração Igopass - controle de trava magnetica para armários, geladeiras e etc
    public static void destravarTranca(Connection connection, EmpresaVO empresa, List<MovParcelaVO> parcelas) {
        try {
            // Verificar se pelo menos 1 dos produtos informados tem função de destravar tranca
            Iterator i = parcelas.iterator();
            boolean produtoEstaTrancado = false;
            while (i.hasNext() && !produtoEstaTrancado) {
                MovParcelaVO parcela = (MovParcelaVO) i.next();
                Iterator j = parcela.getMovProdutoParcelaVOs().iterator();
                while (j.hasNext() && !produtoEstaTrancado) {
                    MovProdutoParcelaVO produto = (MovProdutoParcelaVO) j.next();
                    if (produto.getMovProdutoVO().getProduto().isDestravarTranca()) {
                        produtoEstaTrancado = true;
                    }
                }
            }

            // Se sim, verificar a configuração de trava da empresa e abrir trava
            if (produtoEstaTrancado) {
                VendasConfig vendasConfigDAO = new VendasConfig(connection);
                VendasConfigVO config = vendasConfigDAO.config(empresa.getCodigo());
                boolean travaHabilitada = config.getIgopassUrl() != null && !config.getIgopassUrl().isEmpty();
                if (travaHabilitada) {
                    JSONObject body = new JSONObject();
                    body.put("qrcode", config.getIgopassQrCodeUrl());
                    body.put("timestamp", "1629723095000");
                    body.put("lat", "-23.4262084");
                    body.put("long", "-51.9380495");
                    body.put("accuracy", "1600000");
                    body.put("reference", "0");
                    body.put("idade", "1600000");

                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");
                    headers.put("Accept", "application/json");
                    headers.put("estabelecimento_key", config.getIgopassEstabelecimento());
                    headers.put("user_key", config.getIgopassUsuario());

                    RequestHttpService requestHttpService = new RequestHttpService();
                    RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(
                            config.getIgopassUrl(),
                            headers,
                            null,
                            body.toString(),
                            MetodoHttpEnum.POST
                    );

                    JSONObject response = new JSONObject(respostaHttpDTO.getResponse());
                    Uteis.logarDebug("RESPOSTA IGOPASS: " + respostaHttpDTO.getResponse());
                }
            }
        } catch (Exception e) {
            // Como a venda já foi confirmada não tem o que fazer a não ser abrir a tranca manualmente se der erro
            Uteis.logarDebug("ERRO IGOPASS: Falha ao destravar tranca. Venda confirmada, abertura manual necessária. Erro: " + e.getMessage());
        }
    }
}
