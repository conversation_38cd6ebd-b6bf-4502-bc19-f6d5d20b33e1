package servicos.integracao.catraca;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoIntegracaoCatracaVO;
import negocio.comuns.basico.LogIntegracoesVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.catraca.dto.ClienteCatracaDTO;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.Date;
import java.util.List;

public class ThreadIntegracaoCatraca implements Runnable, AutoCloseable {

    private static final String HEADER_TOKEN_API = "IA-SECURITY-TOKEN";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";

    private final Connection con;
    private final LogIntegracoes logIntegracoesDAO;
    private final ConfiguracaoIntegracaoCatracaVO configuracaoIntegracaoCatracaVO;
    private final boolean liberar;
    private final List<String> cpfsNotificar;

    public ThreadIntegracaoCatraca(Connection con, ConfiguracaoIntegracaoCatracaVO configuracaoIntegracaoCatracaVO, boolean liberar, List<String> cpfsNotificar) throws Exception {
        this.con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        this.logIntegracoesDAO = new LogIntegracoes(this.con);
        this.configuracaoIntegracaoCatracaVO = configuracaoIntegracaoCatracaVO;
        this.liberar = liberar;
        this.cpfsNotificar = cpfsNotificar;
    }

    @Override
    public void run() {
        try {
            if (cpfsNotificar == null || cpfsNotificar.isEmpty()) {
                throw new ConsistirException("Nenhum Cliente informado!");
            }

            Uteis.logarDebug("Entrei na Integração Catraca, liberar: " + liberar);

            URI uri2 = new URIBuilder(configuracaoIntegracaoCatracaVO.getUrl() + "/user-access/block-many").build();

            HttpPut httpPut = new HttpPut(uri2);
            httpPut.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPut.setHeader(HEADER_TOKEN_API, configuracaoIntegracaoCatracaVO.getTokenApi());

            JSONObject dados = new JSONObject();
            dados.put("group_id", 27);
            dados.put("responsible_cpf", configuracaoIntegracaoCatracaVO.getDocumentoResponsavelLiberacao());
            JSONArray cpfsArray = new JSONArray();
            cpfsNotificar.forEach(cpf -> {
                JSONObject cpfJSON = new JSONObject();
                cpfJSON.put("cpf", Formatador.removerMascara(cpf));
                cpfJSON.put("is_blocked", !liberar);
                cpfJSON.put("block_message", liberar ? "" : "Acesso bloqueado pelo sistema pacto");
                cpfsArray.put(cpfJSON);
            });
            dados.put("users", cpfsArray);

            StringEntity entity2 = new StringEntity(dados.toString(), StandardCharsets.UTF_8);
            httpPut.setEntity(entity2);
            HttpClient httpClient2 = ExecuteRequestHttpService.createConnector(null);

            Uteis.logarDebug("Integração Catraca, liberar: " + liberar + ", enviando requisicao para api");

            HttpResponse httpResponse2 = httpClient2.execute(httpPut);
            String response = EntityUtils.toString(httpResponse2.getEntity(), StandardCharsets.UTF_8);

            Uteis.logarDebug("Integração Catraca, liberar: " + liberar + ", requisicao realizada para api, retorno: " + response);

            JSONObject jsonLog = new JSONObject();
            jsonLog.put("responseApi", response != null ? response : "");
            jsonLog.put("jsonBodyEnviado", dados);
            jsonLog.put("qtdCpfs", cpfsNotificar.size());

            if ("OK".equals(response)) {
                salvarLog("OK", jsonLog.toString());
            } else {
                salvarLog("FALHA", jsonLog.toString());
            }
        } catch (
                Exception ex) {
            handleError(ex.getMessage());
        } finally {
            this.close();
        }
    }

    private void handleError(String msgError) {
        String msg = String.format("Erro: %s", msgError);
        Uteis.logarDebug("Falha sincronização Integração Catraca - " + msg);
        salvarLog(msg, "");
    }

    private void salvarLog(String resultado, String dadosRecebidos) {
        try {
            LogIntegracoesVO log = new LogIntegracoesVO();
            log.setDataLancamento(new Date());
            log.setServico("INTEGRACAO_CATRACA");
            log.setResultado(resultado);
            log.setDadosRecebidos(dadosRecebidos);
            logIntegracoesDAO.incluir(log);
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao salvar log de integração catraca: " + e.getMessage());
        }
    }

    @Override
    public void close() {
        try {
            if (con != null && !con.isClosed()) {
                con.close();
            }
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao fechar conexão com banco de dados: " + e.getMessage());
        }
    }
}
