package servicos.bi;

import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import br.com.pactosolucoes.atualizadb.processo.VerificarRecibosPagamentosSemVinculo;
import java.util.List;
import negocio.comuns.utilitarias.UteisServlet;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 05/03/13
 * Time: 11:31
 */
public class VerificadorDeInconsitencias {

    private static final String OPERACAO_SELECT_ALL = "selectALL";
    private static final String OPERACAO_SELECT_ONE = "selectONE";
    private static final String format = "html";
    private static final String except = "bdzillyonpacto";


    public static void main(String[] args) { // p1 = hostPG, p2 = portaPG, p3 = userPG, p4 = senhaPG, p5 = urlAplicacao
        try {
            executar(args.length == 0 ? "localhost" : args[0],
                    args.length < 2 ? "5432" : args[1],
                    args.length < 3 ? "zillyonweb" :args[2],
                    args.length < 4 ? "pactodb" :args[3],
                    args.length < 5 ? "http://app.pactosolucoes.com.br/app/UpdateServlet" : args[4].trim()+"/UpdateServlet", null, null, null);
        } catch (Exception ex) {
            Logger.getLogger(VerificadorDeInconsitencias.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void executar(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD, String emailsPara, String equipe) throws IOException {
        if(UteisValidacao.emptyString(equipe)) {
            equipe = "M1";
        }
        if(UteisValidacao.emptyString(emailsPara) && equipe.equals("M1")) {
            emailsPara = "<EMAIL>";
        }

        if(equipe.equals("M1")){
            StringBuilder recibosDiferentesParcelas = new StringBuilder(consultarSomatorioDasParcelasDiferenteDoValorDoRecibo(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!recibosDiferentesParcelas.toString().equals("") && !recibosDiferentesParcelas.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(recibosDiferentesParcelas, "Somatório das Parcelas diferente do valor do Recibo", emailsPara);
            }
            StringBuilder empresasQueRodaramRobo = new StringBuilder(consultarEmpresasQueNaoRodaramRobo(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!empresasQueRodaramRobo.toString().equals("") && !empresasQueRodaramRobo.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(empresasQueRodaramRobo, "Empresas que não rodaram o robo", emailsPara);
            }
            StringBuilder contratosSemParcelas = new StringBuilder(consultarContratosSemParcelas(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!contratosSemParcelas.toString().equals("") && !contratosSemParcelas.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(contratosSemParcelas, "Contratos Sem Parcelas", emailsPara);
            }

            StringBuilder produtosSemContrato = new StringBuilder(consultarProdutosSemContrato(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!produtosSemContrato.toString().equals("") && !produtosSemContrato.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(produtosSemContrato, "Produtos Sem Contrato", emailsPara);
            }

            StringBuilder alunosSemSintetico = new StringBuilder(consultarAlunosSemSintetico(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!alunosSemSintetico.toString().equals("") && !alunosSemSintetico .toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(alunosSemSintetico , "Cliente Sem Sintetico", emailsPara);
            }

            StringBuilder sinteticoSemAlunos = new StringBuilder(consultarSinteticoSemAluno(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!sinteticoSemAlunos.toString().equals("") && !sinteticoSemAlunos .toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(sinteticoSemAlunos , "Sintético sem Vínculo com Aluno", emailsPara);
            }

            StringBuilder contratosSemPeriodoAcesso = new StringBuilder(consultarContratosSemPeriodoAcesso(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!contratosSemPeriodoAcesso.toString().equals("") && !contratosSemPeriodoAcesso.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(contratosSemPeriodoAcesso, "Contratos Sem Periodo de acesso", emailsPara);
            }

            StringBuilder contratosEmTurmasSemMatricula = new StringBuilder(consultarContratosEmTurmasSemMatriculas(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!contratosEmTurmasSemMatricula.toString().equals("") && !contratosEmTurmasSemMatricula.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(contratosEmTurmasSemMatricula, "Contratos em turmas sem matricula", emailsPara);
            }

            StringBuilder contratosComMatriculaSemTurma = new StringBuilder(consultarContratosComMatriculasSemTurma(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!contratosComMatriculaSemTurma.toString().equals("") && !contratosComMatriculaSemTurma.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(contratosComMatriculaSemTurma, "Contratos com Matricula sem turma", emailsPara);
            }

            StringBuilder empresasQueRodaramRoboParcialmente = new StringBuilder(consultarEmpresasQueRodaramRoboParcialmente(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!empresasQueRodaramRoboParcialmente.toString().equals("") && !empresasQueRodaramRoboParcialmente.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(empresasQueRodaramRoboParcialmente, "Empresas que rodaram o robo parcialmente", emailsPara);
            }

            StringBuilder clientesSemCodAcesso = new StringBuilder(consultarClientesSemCodAcesso(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!clientesSemCodAcesso.toString().equals("") && !clientesSemCodAcesso.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(clientesSemCodAcesso, "Clientes sem Código de Acesso", emailsPara);
            }

            StringBuilder consultarContratosSemInfoCruciais = new StringBuilder(consultarContratosSemInfoCruciais(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!consultarContratosSemInfoCruciais.toString().equals("") && !consultarContratosSemInfoCruciais.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(consultarContratosSemInfoCruciais, "Contratos sem informações importantes", emailsPara);
            }

            StringBuilder consultarContratosEstornadosSemLog = new StringBuilder(consultarContratosEstornadosSemLog(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!consultarContratosEstornadosSemLog.toString().equals("") && !consultarContratosEstornadosSemLog.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(consultarContratosEstornadosSemLog, "Contratos Estornados Sem Log", emailsPara);
            }

        } else {
            StringBuilder vinculosDuplicados = new StringBuilder(consultarVinculosDuplicados(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!vinculosDuplicados.toString().equals("") && !vinculosDuplicados.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(vinculosDuplicados, "Vínculos Duplicados", emailsPara);
            }

            StringBuilder remessaSemRetorno = new StringBuilder(consultarRemessasSemRetorno(TipoConvenioCobrancaEnum.DCC.getCodigo(), 1, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD, 1));
            if (!remessaSemRetorno.toString().equals("") && !remessaSemRetorno.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(remessaSemRetorno, "Remessa da Cielo que não foram enviadas", emailsPara);
            }

            remessaSemRetorno = new StringBuilder(consultarRemessasSemRetorno(TipoConvenioCobrancaEnum.DCC.getCodigo(), 4, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD, 2));
            if (!remessaSemRetorno.toString().equals("") && !remessaSemRetorno.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(remessaSemRetorno, "Remessa da Cielo que não tiveram retorno a mais de 3 dias", emailsPara);
            }

            remessaSemRetorno = new StringBuilder(consultarRemessasSemRetorno(TipoConvenioCobrancaEnum.DCC_BIN.getCodigo(), 1, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD, 1));
            if (!remessaSemRetorno.toString().equals("") && !remessaSemRetorno.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(remessaSemRetorno, "Remessa da BIN que não foram enviadas", emailsPara);
            }

            remessaSemRetorno = new StringBuilder(consultarRemessasSemRetorno(TipoConvenioCobrancaEnum.DCC_BIN.getCodigo(), 4, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD, 2));
            if (!remessaSemRetorno.toString().equals("") && !remessaSemRetorno.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(remessaSemRetorno, "Remessa da BIN que não tiveram retorno a mais de 3 dias", emailsPara);
            }

            remessaSemRetorno = new StringBuilder(consultarRemessasSemRetorno(TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(), 1, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD, 1));
            if (!remessaSemRetorno.toString().equals("") && !remessaSemRetorno.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(remessaSemRetorno, "Remessa da GETNET que não foram enviadas", emailsPara);
            }

            remessaSemRetorno = new StringBuilder(consultarRemessasSemRetorno(TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(), 4, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD, 2));
            if (!remessaSemRetorno.toString().equals("") && !remessaSemRetorno.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(remessaSemRetorno, "Remessa da GETNET que não tiveram retorno a mais de 3 dias", emailsPara);
            }

            StringBuilder locaisDeAcessoSemRegistro = new StringBuilder(consultarLocaisDeAcessoSemRegistro(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!locaisDeAcessoSemRegistro.toString().equals("") && !locaisDeAcessoSemRegistro.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(locaisDeAcessoSemRegistro, "Locais de Acesso sem Registro de acesso por mais de 3 dias", emailsPara);
            }

            StringBuilder locaisDeAcessoBaseOfflineDesatualizada = new StringBuilder(consultarLocaisDeAcessoBaseOfflineDesatualizada(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!locaisDeAcessoBaseOfflineDesatualizada.toString().equals("") && !locaisDeAcessoBaseOfflineDesatualizada.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(locaisDeAcessoBaseOfflineDesatualizada, "Locais de Acesso - Base Offline Desatualizada", emailsPara);
            }

            StringBuilder itensRemessaProblematicos = new StringBuilder(consultarItensRemessaAprovadosSemAutorizacao(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!itensRemessaProblematicos.toString().equals("") && !itensRemessaProblematicos.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(itensRemessaProblematicos, "Itens de remessa Inconsistentes", emailsPara);
            }

            StringBuilder empresasSemMeta = new StringBuilder(consultarEmpresasSemMeta(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!empresasSemMeta.toString().equals("") && !empresasSemMeta.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(empresasSemMeta, "Empresas sem Meta", emailsPara);
            }

            StringBuilder remessasRessarcimento = new StringBuilder(consultarRemessasItensRessarcimento(hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD));
            if (!remessasRessarcimento.toString().equals("") && !remessasRessarcimento.toString().contains(UteisServlet.MSG_SEMDADOS)) {
                EnviarEmailDosServicos.enviarEmail(remessasRessarcimento, "Itens de remessa com motivo para ressarcimento", emailsPara);
            }

        }

//        StringBuilder problemasRecibos = new StringBuilder(consultarInconsistenciasRecibo(hostPG, portaPG));
//        if(!problemasRecibos.toString().equals("") && !problemasRecibos.toString().contains(UteisServlet.MSG_SEMDADOS)){
//            EnviarEmailDosServicos.enviarEmail(problemasRecibos, "Inconsistências com parcelas, produtos e pagamentos", "<EMAIL>");
//        }
    }

    private static String consultarVinculosDuplicados(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT\n" +
                "  empresa.nome,\n" +
                "  max(c.pessoa) AS codPessoa,\n" +
                "  count(v.cliente)\n" +
                "FROM cliente c\n" +
                "  LEFT JOIN vinculo v\n" +
                "    ON v.cliente = c.codigo\n" +
                "  LEFT JOIN empresa\n" +
                "    ON c.empresa = empresa.codigo\n" +
                "WHERE tipovinculo = 'CO' OR tipovinculo IS null\n" +
                "GROUP BY empresa.nome, c.pessoa\n" +
                "HAVING (count(v.cliente) > 1)\n" +
                "ORDER BY count(v.cliente) ASC";

        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarSomatorioDasParcelasDiferenteDoValorDoRecibo(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT\n" +
                "  *,\n" +
                "  valortotalpagamento - valorpg AS dif\n" +
                "FROM\n" +
                "  (SELECT\n" +
                "     m.recibopagamento      AS recibo,\n" +
                "     rp.empresa                                       AS empresa,\n" +
                "     rp.nomepessoapagador                            AS pessoaPagador,\n" +
                "     sum(m.valortotal)                               AS valortotalpagamento,    \n" +
                "     (SELECT sum(valorpago)\n" +
                "      FROM pagamentomovparcela pmp\n" +
                "      WHERE pmp.recibopagamento = m.recibopagamento) AS valorpg\n" +
                "   FROM public.movpagamento m\n" +
                "   LEFT JOIN recibopagamento rp ON m.recibopagamento = rp.codigo\n" +
                "   WHERE m.recibopagamento IS NOT NULL and id_recebe = null AND id_movimento = null and rp.data > '2019-05-10'\n" +
                "   GROUP BY 1, 2, 3\n" +
                "   ORDER BY m.recibopagamento) AS t\n" +
                "WHERE abs(t.valortotalpagamento - t.valorpg) >= 0.01;";

        return consultar(sql,hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarRemessasSemRetorno(final Integer tipo, 
            final Integer situacao, final String hostPG, final String portaPG, 
            final String userPG, final String passWordPG, 
            final String urlUpdateServlet, final String nomeBD, int nrDiasMinimo) throws IOException {
        StringBuilder sql = new StringBuilder("select e.nome as empresa , r.codigo as remessa, r.dataregistro, ");
        sql.append("r.nomearquivo, con.numerocontrato as estabelecimento ");
        sql.append("from remessa r ");
        sql.append("inner join empresa e on e.codigo = r.empresa ");
        sql.append("left join conveniocobranca con ON con.codigo = r.conveniocobranca,configuracaosistema c ");
        sql.append("where situacaoremessa = ").append(situacao);
        sql.append(" and con.tipoconvenio = ").append(tipo).append(" and c.enviarremessasremotamente  = 't' ");
        sql.append("and ((to_char(dthrinicio,'HH24:MI:SS')::time < time '12:00:00' and (CURRENT_DATE - date(dthrinicio)) >= ").
                append(nrDiasMinimo).append(") or  (CURRENT_DATE - date(dthrinicio)) >= ").append(nrDiasMinimo + 1).append(")");
        return consultar(sql.toString(), hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarContratosRenovadosComValorZerado(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT\n");
        sql.append("\tpes.nome, \n");
        sql.append("\tcli.codigomatricula AS matricula,\n");
        sql.append("\tcon.codigo AS codContrato,\n");
        sql.append("\tpln.descricao AS plano,\n");
        sql.append("\tcon.vigenciade AS datainicio,\n");
        sql.append("\tcon.vigenciaateajustada AS datafim,\n");
        sql.append("\tcon.datalancamento,\n");
        sql.append("\temp.nome AS empresa\n");
        sql.append("FROM contrato con\n");
        sql.append("INNER JOIN cliente cli ON cli.pessoa = con.pessoa\n");
        sql.append("INNER JOIN plano pln ON pln.codigo = con.plano\n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = con.pessoa\n");
        sql.append("INNER JOIN empresa emp ON emp.codigo = con.empresa\n");
        sql.append("WHERE con.valorfinal = 0.0\n");
        sql.append("AND con.bolsa IS FALSE\n");
        sql.append("AND con.importacao IS FALSE\n");
        sql.append("AND con.situacaocontrato = 'RN'\n");
        sql.append("AND con.responsavelcontrato = (SELECT codigo FROM usuario WHERE username ILIKE 'RECOR' LIMIT 1)\n");
        sql.append("AND NOT EXISTS (SELECT codigo FROM contratooperacao WHERE contrato = con.codigo AND tipooperacao = 'CA')\n");
        sql.append("AND con.datalancamento > NOW () - INTERVAL '3 months'\n");
        sql.append("ORDER BY pes.nome");

        return consultar(sql.toString(), hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }


    private static String consultarContratosRecorrentesSemRegistroNaTabelaRecorrencia(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT\n");
        sql.append("\tpes.nome, \n");
        sql.append("\tcli.codigomatricula AS matricula,\n");
        sql.append("\tcon.codigo AS codContrato,\n");
        sql.append("\tpln.descricao AS plano,\n");
        sql.append("\tcon.vigenciade AS datainicio,\n");
        sql.append("\tcon.vigenciaateajustada AS datafim,\n");
        sql.append("\tcon.datalancamento,\n");
        sql.append("\temp.nome AS empresa,\n");
        sql.append("\tcon.situacaocontrato\n");
        sql.append("FROM contrato con\n");
        sql.append("INNER JOIN pessoa pes ON pes.codigo = con.pessoa\n");
        sql.append("INNER JOIN cliente cli ON cli.pessoa = pes.codigo\n");
        sql.append("INNER JOIN empresa emp ON emp.codigo = cli.empresa\n");
        sql.append("INNER JOIN plano pln ON pln.codigo = con.plano\n");
        sql.append("INNER JOIN planorecorrencia prc ON prc.plano = pln.codigo\n");
        sql.append("LEFT JOIN contratorecorrencia crc ON crc.contrato = con.codigo\n");
        sql.append("WHERE crc.codigo IS NULL\n");
        sql.append("AND NOT EXISTS (SELECT codigo FROM contratooperacao WHERE contrato = con.codigo AND tipooperacao = 'CA')\n");
        sql.append("AND con.datalancamento > NOW () - INTERVAL '3 months'\n");
        sql.append("ORDER BY pes.nome");

        return consultar(sql.toString(), hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }


    private static String consultarLocaisDeAcessoSemRegistro(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT la.codigo      AS codLocal,\n" +
                "       la.descricao        AS localAcesso,\n" +
                "       col.codigo          AS codColetor,\n" +
                "       col.descricao       AS coletor,\n" +
                "       emp.codigo          AS codempresa,\n" +
                "       emp.nome            AS empresa\n" +
                "FROM coletor col\n" +
                "         LEFT JOIN acessocliente ac on col.codigo = ac.coletor AND now() - interval '3 day' < dthrentrada\n" +
                "         INNER JOIN localacesso la ON col.localacesso = la.codigo\n" +
                "         INNER JOIN empresa emp ON la.empresa = emp.codigo\n" +
                "WHERE ac.codigo is null\n" +
                "  AND (col.modelo <> 'MODELO_COLETOR_DESCONHECIDO' OR col.padraocadastro = 'f')\n" +
                "  AND col.desativado = 'f'\n" +
                "  AND la.descricao NOT LIKE '%NOME EMPRESA%'\n" +
                "  AND la.descricao NOT LIKE '%PACTO%'\n" +
                "  AND la.descricao NOT LIKE '%GQS%'\n" +
                "  AND la.descricao NOT LIKE '%TESTE%'\n" +
                "  AND la.descricao NOT LIKE '%DESENV%'";

        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarInconsistenciasRecibo(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        return consultar(VerificarRecibosPagamentosSemVinculo.sqlVerificador(), hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarLocaisDeAcessoBaseOfflineDesatualizada(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT\n" +
                "  e.codigo    AS codEmpresa,\n" +
                "  e.nome      AS Empresa,\n" +
                "  l.codigo    AS codLocalAcesso,\n" +
                "  l.descricao AS LocalAcesso,\n" +
                "  l.databaseoffline,\n" +
                "  l.datadownloadbase\n" +
                "FROM\n" +
                "  localacesso l\n" +
                "  INNER JOIN empresa e ON e.codigo = l.empresa\n" +
                "WHERE date(l.databaseoffline) <> date(l.datadownloadbase)\n" +
                "      AND l.descricao NOT LIKE '% PACTO%'\n" +
                "      AND l.utilizarmodooffline";

        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarEmpresasQueNaoRodaramRobo(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select codigo,nome from empresa where not exists(select * from robo   where (dia::date) = CURRENT_DATE) or exists(select * from robo where (datahorafim  is null or rotinaprocessada = 'f') and (dia::date) = CURRENT_DATE);";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarEmpresasQueRodaramRoboParcialmente(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT codigo, nome FROM empresa WHERE EXISTS(SELECT * FROM robo WHERE rotinaprocessadaparcialmente AND (dia::date) = CURRENT_DATE);";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarClientesSemCodAcesso(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT emp.nome, COUNT(cli.*) AS qtdAlunos FROM cliente cli INNER JOIN empresa emp ON emp.codigo = cli.empresa  WHERE cli.codacesso IS NULL OR cli.codacesso ILIKE '' GROUP BY emp.nome HAVING COUNT(cli.*) > 0";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarContratosSemInfoCruciais(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        sql.append("\tc.responsavelcontrato AS responsavelContrato,\n");
        sql.append("\tc.codigo AS codigoContrato,\n");
        sql.append("\tc.datalancamento::date AS dataLancamentoContrato,\n");
        sql.append("\tp.nome AS nomePessoaContrato,\n");
        sql.append("\tCASE\n");
        sql.append("\t\tWHEN c.origemsistema = 1\n");
        sql.append("\t\tTHEN 'ZillyonWeb'\n");
        sql.append("\t\tWHEN c.origemsistema = 2\n");
        sql.append("\t\tTHEN 'Agenda Web'\n");
        sql.append("\t\tWHEN c.origemsistema = 3\n");
        sql.append("\t\tTHEN 'Pacto Treino'\n");
        sql.append("\t\tWHEN c.origemsistema = 4\n");
        sql.append("\t\tTHEN 'App Treino'\n");
        sql.append("\t\tWHEN c.origemsistema = 5\n");
        sql.append("\t\tTHEN 'App Professor'\n");
        sql.append("\t\tWHEN c.origemsistema = 6\n");
        sql.append("\t\tTHEN 'Autoatendimento'\n");
        sql.append("\t\tWHEN c.origemsistema = 7\n");
        sql.append("\t\tTHEN 'Site Vendas'\n");
        sql.append("\t\tWHEN c.origemsistema = 8\n");
        sql.append("\t\tTHEN 'Buzz Lead'\n");
        sql.append("\t\tWHEN c.origemsistema = 9\n");
        sql.append("\t\tTHEN 'Vendas 2.0'\n");
        sql.append("\t\tWHEN c.origemsistema = 10\n");
        sql.append("\t\tTHEN 'App do consultor'\n");
        sql.append("\t\tWHEN c.origemsistema = 11\n");
        sql.append("\t\tTHEN 'Booking Gympass'\n");
        sql.append("\t\tWHEN c.origemsistema = 12\n");
        sql.append("\t\tTHEN 'Fila de espera'\n");
        sql.append("\t\tWHEN c.origemsistema = 13\n");
        sql.append("\t\tTHEN 'Importacao API'\n");
        sql.append("\t\tWHEN c.origemsistema = 14\n");
        sql.append("\t\tTHEN 'Hubspot Lead'\n");
        sql.append("\t\tWHEN c.origemsistema = 15\n");
        sql.append("\t\tTHEN 'CRM Meta Diaria'\n");
        sql.append("\t\tWHEN c.origemsistema = 16\n");
        sql.append("\t\tTHEN 'Pacto Flow'\n");
        sql.append("\t\tWHEN c.origemsistema = 17\n");
        sql.append("\t\tTHEN 'Nova Tela de Negociacao'\n");
        sql.append("\tEND AS origemContrato,\n");
        sql.append("\tEXISTS (SELECT codigo FROM contratocondicaopagamento WHERE contrato =  c.codigo) as existecontratocondicaopagamento,\n");
        sql.append("\tEXISTS (SELECT codigo FROM contratoduracao WHERE contrato =  c.codigo) as existecontratoduracao,\n");
        sql.append("\tEXISTS (SELECT codigo FROM contratohorario WHERE contrato =  c.codigo) as existecontratohorario,\n");
        sql.append("\tEXISTS (SELECT codigo FROM contratomodalidade WHERE contrato =  c.codigo) as existecontratomodalidade,\n");
        sql.append("\tEXISTS (SELECT codigo FROM contratoplanoprodutosugerido WHERE contrato =  c.codigo) as existecontratoplanoprodutosugerido,\n");
        sql.append("\tEXISTS (SELECT codigo FROM historicocontrato WHERE contrato =  c.codigo) as existehistoricocontrato,\n");
        sql.append("\tEXISTS (SELECT codigo FROM movparcela WHERE contrato =  c.codigo) as existemovparcela,\n");
        sql.append("\tEXISTS (SELECT codigo FROM movproduto WHERE contrato =  c.codigo) as existemovproduto,\n");
        sql.append("\tEXISTS (SELECT codigo FROM movprodutomodalidade WHERE contrato =  c.codigo) as existemovprodutomodalidade,\n");
        sql.append("\tEXISTS (SELECT codigo FROM contratotextopadrao WHERE contrato =  c.codigo) as existecontratotextopadrao\n");
        sql.append("FROM contrato c\n");
        sql.append("INNER JOIN pessoa p ON p.codigo = c.pessoa\n");
        sql.append("INNER JOIN empresa e ON e.codigo = c.empresa\n");
        sql.append("WHERE (NOT EXISTS (SELECT codigo FROM contratoduracao WHERE contrato =  c.codigo)\n");
        sql.append("\tOR NOT EXISTS (SELECT codigo FROM contratocondicaopagamento WHERE contrato =  c.codigo)\n");
        sql.append("\tOR NOT EXISTS (SELECT codigo FROM contratohorario WHERE contrato =  c.codigo)\n");
        sql.append("\tOR NOT EXISTS (SELECT codigo FROM contratomodalidade WHERE contrato =  c.codigo))\n");
        sql.append("AND c.datalancamento > NOW() - INTERVAL '3 months'\n");
        sql.append("ORDER BY c.datalancamento;\n");
        return consultar(sql.toString(), hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarContratosEstornadosSemLog(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        StringBuilder sql = new StringBuilder();
        sql.append("select cl.codigomatricula,p.codigo as codpessoa, p.nome, l.chaveprimaria as contrato,\n");
        sql.append("l.dataalteracao as datalancamentocontrato,  l.responsavelalteracao from pessoa p \n");
        sql.append("inner join cliente cl on cl.pessoa = p.codigo inner join log l on l.pessoa  = p.codigo  and l.operacao = 'INCLUSÃO DE CONTRATO' \n");
        sql.append("left join contrato c on c.codigo = l.chaveprimaria::int \n");
        sql.append("left join log lest on lest.chaveprimaria = l.chaveprimaria and lest.operacao ='ESTORNO - CONTRATO'\n");
        sql.append("where c.codigo is null and lest.codigo is null and l.dataalteracao >  NOW() - INTERVAL '6 months';\n");
        return consultar(sql.toString(), hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarContratosSemParcelas(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT c.codigo AS contrato, c.situacao, p.nome AS aluno, e.nome AS empresa, c.datalancamento, c.valorfinal, c.importacao FROM contrato c LEFT JOIN movparcela mp ON mp.contrato = c.codigo INNER JOIN pessoa p ON p.codigo = c.pessoa INNER JOIN empresa e ON e.codigo = c.empresa WHERE mp.codigo IS NULL AND c.situacao = 'AT' AND c.situacaocontrato <> 'TF' AND c.datalancamento > '2025-01-01' ORDER BY c.datalancamento;";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

     private static String consultarProdutosSemContrato(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select mp.codigo as movproduto, pes.nome as aluno, e.nome as empresa,mp.descricao as descricao,mp.totalfinal,mp.datalancamento  from movproduto mp inner join produto p on p.codigo = mp.produto inner join empresa e on mp.empresa = e.codigo inner join pessoa pes on pes.codigo = mp.pessoa where mp.contrato is null and p.tipoproduto in ('MA','PM','RE','RN') and not ((coalesce(mp.multa,0) > 0) or  (coalesce(mp.juros,0) > 0)) and mp.vendaavulsa is null and e.ativa = true and mp.situacao = 'AT';";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarItensRemessaAprovadosSemAutorizacao(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select emp.nome as empresa , re.dataregistro , ri.codigo as remessaitem , ri.remessa , mp.situacao  , p.nome as pessoa from remessaitem ri inner join remessa re on ri.remessa = re.codigo inner join pessoa p on p.codigo = ri.pessoa inner join movparcela mp on mp.codigo = ri.movparcela inner join empresa emp on emp.codigo = mp.empresa where ri.props  ilike '%StatusVenda=00%' and ri.props ilike '%CodigoAutorizacao=000000%' and movpagamento is null and re.dataregistro > now()::date - interval '180' day order by situacao";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarEmpresasSemMeta(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select e.codigo, e.nome from empresa e, configuracaosistemacrm where e.ativa and e.codigo not in (select empresa from aberturameta  where dia::date = CURRENT_DATE) \n" +
                "and (extract(dow from CURRENT_DATE) in (1,2,3,4,5) or (extract(dow from CURRENT_DATE) = 0 and abertodomingo) or (extract(dow from CURRENT_DATE) = 6 and abertosabado)) \n" +
                "and not exists (select codigo from feriado where \n" +
                "(date_part('day', dia) = date_part('day', CURRENT_DATE) and date_part('month', dia) = date_part('month', CURRENT_DATE)) \n" +
                "and ((nacional and pais = e.pais and (naorecorrente = false or dia::date = CURRENT_DATE)) or (estadual and estado = e.estado and (naorecorrente = false or dia::date = CURRENT_DATE)) or ( not nacional and not estadual and cidade = e.cidade and (naorecorrente = false or dia::date = CURRENT_DATE))))\n" +
                "and (select exists(SELECT usu.codigo, usu.administrador, usu.cliente,usu.tipoUsuario, usu.senha,  \n" +
                "       usu.username, usu.nome, usu.dataAlteracaoSenha, usu.permiteAlterarPropriaSenha,  \n" +
                "       usu.serviceUsuario, usu.serviceSenha, sqlColEmp.codigo as colaborador \n" +
                "FROM usuario usu \n" +
                "inner join( \n" +
                "select us.codigo as codigoUsuario, colEmp.codigo, colEmp.pessoa, colEmp.situacao, colEmp.empresa \n" +
                "from colaborador col \n" +
                "inner join usuario us on us.colaborador = col.codigo \n" +
                "inner join usuarioperfilacesso upa on upa.usuario = us.codigo \n" +
                "inner join colaborador colEmp on colEmp.pessoa = col.pessoa AND colEmp.empresa = upa.empresa\n" +
                "where upa.empresa = e.codigo and colEmp.situacao = 'AT' \n" +
                ") as sqlColEmp on sqlColEmp.codigoUsuario = usu.codigo  \n" +
                "WHERE (SELECT \n" +
                "count(*) \n" +
                "FROM vinculo v \n" +
                "WHERE v.colaborador = sqlColEmp.codigo) > 0))";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }
    private static String consultarRemessasItensRessarcimento(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD)throws IOException {
        HashMap<Integer,List<String>> retornoConvenio = new HashMap<Integer,List<String>>();
        retornoConvenio.put(TipoRemessaEnum.EDI_CIELO.getId(), Arrays.asList(new String[]{"03","06","07","23"}));
        retornoConvenio.put(TipoRemessaEnum.GET_NET.getId(), Arrays.asList(new String[]{"03","67"}));
        retornoConvenio.put(TipoRemessaEnum.DCC_BIN.getId(), Arrays.asList(new String[]{"04","15"}));
        StringBuilder sql = new StringBuilder("select ri.remessa , ri.codigo as remessaitem, pes.nome as aluno, split_part(split_part(ri.props, 'StatusVenda=', 2), ',', 1) as codigoretoro from remessaitem ri INNER JOIN pessoa pes ON pes.codigo = ri.pessoa INNER JOIN movparcela mov ON mov.codigo = ri.movparcela AND mov.nrtentativas = 1 INNER JOIN remessa r ON r.codigo = ri.remessa AND r.dataregistro BETWEEN now() - (interval '3 day') AND now() WHERE ");
        for(Integer key : retornoConvenio.keySet()){
            sql.append(" or (ri.tipo = ").append(key).append(" and (");
            String erros = "";
            for(String codErro : retornoConvenio.get(key)){
                // Validar código de erro para prevenir SQL injection
                if (!Uteis.isValidStringValue(codErro)) {
                    throw new SecurityException("Código de erro contém caracteres inválidos: " + codErro);
                }
                // Escapar aspas simples para prevenir SQL injection
                String codErroEscapado = codErro.replace("'", "''");
                erros += " or ri.props ilike '%StatusVenda="+codErroEscapado+"%' ";
            }
            erros = erros.replaceFirst(" or "," ");
            sql.append(erros).append(") )");
        }
        sql = new StringBuilder(sql.toString().replaceFirst(" or "," "));
        return consultar(sql.toString(), hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarAlunosSemSintetico(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select c.codigomatricula, p.nome, c.situacao from cliente c inner join pessoa p on p.codigo = c.pessoa left join situacaoclientesinteticodw s on s.codigocliente = c.codigo where s.codigo is null ;";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarSinteticoSemAluno(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "SELECT s.matricula, s.nomecliente, s.situacao  FROM situacaoclientesinteticodw s LEFT JOIN cliente c ON s.codigocliente = c.codigo WHERE c.codigo IS NULL AND s.nomecliente IS NOT NULL AND s.nomecliente <> ''";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarContratosSemPeriodoAcesso(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select cl.codigomatricula, pe.nome, c.codigo as contrato,c.datalancamento, c.importacao,c.vigenciaateajustada from contrato c inner join cliente cl on cl.pessoa = c.pessoa inner join pessoa pe on pe.codigo = c.pessoa where c.situacao ='AT' and current_date between  vigenciade and vigenciaateajustada and not exists (select codigo from periodoacessocliente p where p.contrato = c.codigo and current_date between p.datainicioacesso and p.datafinalacesso) and 'RT' = coalesce ((select tipooperacao from contratooperacao cop where  cop.contrato = c.codigo and cop.tipooperacao in ('TR', 'TV', 'RT' ) order by cop.datainicioefetivacaooperacao desc limit 1), 'RT');";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarContratosEmTurmasSemMatriculas(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select  cl.codigomatricula, pe.nome, c.codigo as contrato,c.datalancamento,mo.nome, mo.utilizarturma, c.importacao,c.vigenciaateajustada, cmht.horarioturma  from contrato c inner join empresa e on e.codigo = c.empresa inner join plano p on p.codigo = c.plano inner join cliente cl on cl.pessoa = c.pessoa inner join pessoa pe on pe.codigo = c.pessoa  inner join contratomodalidade cm on cm.contrato = c.codigo inner join modalidade mo on mo.codigo = cm.modalidade inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo inner join contratomodalidadehorarioturma cmht on cmht.contratomodalidadeturma  = cmt.codigo where c.situacao = 'AT' and p.vendacreditotreino = false and not exists (select codigo from contratooperacao cop where cop.contrato = c.codigo and cop.tipooperacao = 'TR' and (cop.datafimefetivacaooperacao > now() or e.qtddiasparaliberacaodevagaemtrancamento > 0)) and not exists (select codigo from matriculaalunohorarioturma  where contrato = c.codigo and horarioturma = cmht.horarioturma and  datafim >= c.vigenciaateajustada - interval '1 day') and not exists (select codigo from alunofixoaula  where cliente = cl.codigo and horarioturma = cmht.horarioturma and  datafinal >= now() and dataremovido is null )";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultarContratosComMatriculasSemTurma(final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        String sql = "select mt.codigo as codigomatriculaturma,mt.*, (select max(dataoperacao) - interval '1 day' from contratooperacao  where  contrato = mt.contrato and tipooperacao in ('EM','AM')) as dataoperacao from matriculaalunohorarioturma  mt where not exists (\n" +
                "select  cmht.* from contrato c    inner join contratomodalidade cm on cm.contrato = c.codigo \n" +
                "inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo\n" +
                "inner join contratomodalidadehorarioturma cmht on cmht.contratomodalidadeturma  = cmt.codigo \n" +
                "where  c.codigo = mt.contrato and cmht.horarioturma = mt.horarioturma ) and datafim >= current_date order by contrato";
        return consultar(sql, hostPG, portaPG, userPG, passWordPG, urlUpdateServlet, nomeBD);
    }

    private static String consultar(final String sql, final String hostPG, final String portaPG, final String userPG, final String passWordPG, final String urlUpdateServlet, final String nomeBD) throws IOException {
        Map<String, String> mapa = new HashMap<String, String>();        
        mapa.put("op", nomeBD != null ? OPERACAO_SELECT_ONE : OPERACAO_SELECT_ALL);
        if (nomeBD != null)
            mapa.put("bd", nomeBD);            
        mapa.put("hostPG", hostPG);
        mapa.put("portaPG", portaPG);
        mapa.put("userPG", userPG);
        mapa.put("pwdPG", passWordPG);
        mapa.put("format", VerificadorDeInconsitencias.format);
        mapa.put("except", VerificadorDeInconsitencias.except);
        mapa.put("sql", sql);
        mapa.put("lgn", PropsService.getPropertyValue(PropsService.API_OAMD_KEY));
        mapa.put("appName", "VerificadorDeInconsitencias");
        return ExecuteRequestHttpService.executeRequest(urlUpdateServlet, mapa);
    }
}
