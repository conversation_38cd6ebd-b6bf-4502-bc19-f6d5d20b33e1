package servicos.discovery;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class DiscoveryMsService {

    private static final Map<String, ClientDiscoveryDataDTO> cache = new ConcurrentHashMap<>();

    private static String baseUrl() throws DiscoveryException {
        String url = PropsService.getPropertyValue(PropsService.URL_DISCOVERY_MS);
        if (url.equals("@DISCOVERY_URL@")) {
            throw new DiscoveryException("A propriedade @DISCOVERY_URL@ não está definida");
        }
        return url;
    }

    public static ClientDiscoveryDataDTO urls() throws Exception {
        String cacheKey = "urls";
        if (cache.containsKey(cacheKey)) {
            return cache.get(cacheKey);
        }

        String url = baseUrl() + "/find";
        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        ClientDiscoveryDataDTO data = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class);

        cache.put(cacheKey, data);
        return data;
    }

    public static ClientDiscoveryDataDTO urlsChave(String chave) throws Exception {
        if (cache.containsKey(chave)) {
            return cache.get(chave);
        }

        String url = baseUrl() + "/ea170e2cafb1337755c8b3d5ae4437f4";
        JSONObject data = new JSONObject();
        data.put("chave", chave);
        data.put("nonce", UUID.randomUUID().toString());
        data.put("timestamp", new Date().getTime());
        JSONObject bodyJson = new JSONObject();
        bodyJson.put("data", Uteis.encriptarZWUI(data.toString()));

        String response = ExecuteRequestHttpService.post(url, bodyJson.toString(),new HashMap<>());
        String contentCrypt = new JSONObject(response).getString("content");
        ClientDiscoveryDataDTO dataD = JSONMapper.getObject(new JSONObject(Uteis.desencriptarZWUI(contentCrypt)), ClientDiscoveryDataDTO.class);
        cache.put(chave, dataD);
        return dataD;
    }

    public static void clearnCache() {
        cache.clear();
    }
}
