package servicos.discovery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceMapDTO {

    private String alunoMsUrl;
    private String loginMsUrl;
    private String colaboradorMsUrl;
    private String graduacaoMsUrl;
    private String treinoApiUrl;
    private String treinoUrl;
    private String loginAppUrl;
    private String oamdUrl;
    private String zwUrl;
    private String personagemMsUrl;
    private String autenticacaoUrl;
    private String frontPersonal;
    private String planoMsUrl;
    private String produtoMsUrl;
    private String relatorioFull;
    private String sinteticoMsUrl;
    private String pactoPayDashUrl;
    private String cadastroAuxiliarUrl;
    private String zwFrontUrl;
    private String treinoFrontUrl;
    private String loginFrontUrl;
    private String gameUrl;
    private String vendasOnlineUrl;
    private String admCoreUrl;
    private String acessoSistemaMsUrl;
    private String urlMidiaSocialMs;
    private String pessoaMsUrl;
    private String urlMarketingMs;
    private String integracoesMsUrl;
    private String notificacaoMs;
    private String recursoMsUrl;
    private String contatoMsUrl;
    private String financeiroMsUrl;

    public String getAlunoMsUrl() {
        return alunoMsUrl;
    }

    public void setAlunoMsUrl(String alunoMsUrl) {
        this.alunoMsUrl = alunoMsUrl;
    }

    public String getLoginMsUrl() {
        return loginMsUrl;
    }

    public void setLoginMsUrl(String loginMsUrl) {
        this.loginMsUrl = loginMsUrl;
    }

    public String getColaboradorMsUrl() {
        return colaboradorMsUrl;
    }

    public void setColaboradorMsUrl(String colaboradorMsUrl) {
        this.colaboradorMsUrl = colaboradorMsUrl;
    }

    public String getGraduacaoMsUrl() {
        return graduacaoMsUrl;
    }

    public void setGraduacaoMsUrl(String graduacaoMsUrl) {
        this.graduacaoMsUrl = graduacaoMsUrl;
    }

    public String getTreinoApiUrl() {
        return treinoApiUrl;
    }

    public void setTreinoApiUrl(String treinoApiUrl) {
        this.treinoApiUrl = treinoApiUrl;
    }

    public String getTreinoUrl() {
        return treinoUrl;
    }

    public void setTreinoUrl(String treinoUrl) {
        this.treinoUrl = treinoUrl;
    }

    public String getLoginAppUrl() {
        return loginAppUrl;
    }

    public void setLoginAppUrl(String loginAppUrl) {
        this.loginAppUrl = loginAppUrl;
    }

    public String getOamdUrl() {
        return oamdUrl;
    }

    public void setOamdUrl(String oamdUrl) {
        this.oamdUrl = oamdUrl;
    }

    public String getZwUrl() {
        return zwUrl;
    }

    public void setZwUrl(String zwUrl) {
        this.zwUrl = zwUrl;
    }

    public String getPersonagemMsUrl() {
        return personagemMsUrl;
    }

    public void setPersonagemMsUrl(String personagemMsUrl) {
        this.personagemMsUrl = personagemMsUrl;
    }

    public String getAutenticacaoUrl() {
        return autenticacaoUrl;
    }

    public void setAutenticacaoUrl(String autenticacaoUrl) {
        this.autenticacaoUrl = autenticacaoUrl;
    }

    public String getFrontPersonal() {
        return frontPersonal;
    }

    public void setFrontPersonal(String frontPersonal) {
        this.frontPersonal = frontPersonal;
    }

    public String getPlanoMsUrl() {
        return planoMsUrl;
    }

    public void setPlanoMsUrl(String planoMsUrl) {
        this.planoMsUrl = planoMsUrl;
    }

    public String getProdutoMsUrl() {
        return produtoMsUrl;
    }

    public void setProdutoMsUrl(String produtoMsUrl) {
        this.produtoMsUrl = produtoMsUrl;
    }

    public String getRelatorioFull() {
        return relatorioFull;
    }

    public void setRelatorioFull(String relatorioFull) {
        this.relatorioFull = relatorioFull;
    }

    public String getSinteticoMsUrl() {
        return sinteticoMsUrl;
    }

    public void setSinteticoMsUrl(String sinteticoMsUrl) {
        this.sinteticoMsUrl = sinteticoMsUrl;
    }

    public String getPactoPayDashUrl() {
        return pactoPayDashUrl;
    }

    public void setPactoPayDashUrl(String pactoPayDashUrl) {
        this.pactoPayDashUrl = pactoPayDashUrl;
    }

    public String getCadastroAuxiliarUrl() {
        return cadastroAuxiliarUrl;
    }

    public void setCadastroAuxiliarUrl(String cadastroAuxiliarUrl) {
        this.cadastroAuxiliarUrl = cadastroAuxiliarUrl;
    }

    public String getZwFrontUrl() {
        return zwFrontUrl;
    }

    public void setZwFrontUrl(String zwFrontUrl) {
        this.zwFrontUrl = zwFrontUrl;
    }

    public String getTreinoFrontUrl() {
        return treinoFrontUrl;
    }

    public void setTreinoFrontUrl(String treinoFrontUrl) {
        this.treinoFrontUrl = treinoFrontUrl;
    }

    public String getLoginFrontUrl() {
        return loginFrontUrl;
    }

    public void setLoginFrontUrl(String loginFrontUrl) {
        this.loginFrontUrl = loginFrontUrl;
    }

    public String getGameUrl() {
        return gameUrl;
    }

    public void setGameUrl(String gameUrl) {
        this.gameUrl = gameUrl;
    }

    public String getVendasOnlineUrl() {
        return vendasOnlineUrl;
    }

    public void setVendasOnlineUrl(String vendasOnlineUrl) {
        this.vendasOnlineUrl = vendasOnlineUrl;
    }

    public String getAdmCoreUrl() {
        return admCoreUrl;
    }

    public void setAdmCoreUrl(String admCoreUrl) {
        this.admCoreUrl = admCoreUrl;
    }

    public String getAcessoSistemaMsUrl() {
        return acessoSistemaMsUrl;
    }

    public void setAcessoSistemaMsUrl(String acessoSistemaMsUrl) {
        this.acessoSistemaMsUrl = acessoSistemaMsUrl;
    }

    public String getUrlMidiaSocialMs() {
        return this.urlMidiaSocialMs;
    }

    public void setUrlMidiaSocialMs(final String urlMidiaSocialMs) {
        this.urlMidiaSocialMs = urlMidiaSocialMs;
    }

    public String getPessoaMsUrl() {
        return pessoaMsUrl;
    }

    public void setPessoaMsUrl(String pessoaMsUrl) {
        this.pessoaMsUrl = pessoaMsUrl;
    }

    public String getUrlMarketingMs() {
        return urlMarketingMs;
    }

    public void setUrlMarketingMs(String urlMarketingMs) {
        this.urlMarketingMs = urlMarketingMs;
    }

    public String getIntegracoesMsUrl() {
        return integracoesMsUrl;
    }

    public void setIntegracoesMsUrl(String integracoesMsUrl) {
        this.integracoesMsUrl = integracoesMsUrl;
    }

    public String getNotificacaoMs() {
        return notificacaoMs;
    }

    public void setNotificacaoMs(String notificacaoMs) {
        this.notificacaoMs = notificacaoMs;
    }

    public String getRecursoMsUrl() {
        return recursoMsUrl;
    }

    public void setRecursoMsUrl(String recursoMsUrl) {
        this.recursoMsUrl = recursoMsUrl;
    }

    public String getContatoMsUrl() {
        return contatoMsUrl;
    }

    public void setContatoMsUrl(String contatoMsUrl) {
        this.contatoMsUrl = contatoMsUrl;
    }

    public String getFinanceiroMsUrl() {
        return financeiroMsUrl;
    }

    public void setFinanceiroMsUrl(String financeiroMsUrl) {
        this.financeiroMsUrl = financeiroMsUrl;
    }
}
